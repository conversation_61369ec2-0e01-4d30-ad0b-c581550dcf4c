# KEEP ALPHABETICALLY SORTED TO AID HUMANS IN SEARCHING!
[versions]
apache-commons = "1.9.0"
detekt = "1.23.7"
flyway = "9.19.1"
hellofresh-libs = "0.6.2"
hellofresh-schemaregistry = "0.1.2383"
jackson = '2.18.0'
jib = "3.4.0"
junit = "5.11.1"
java = "21"
jetbrains-annotations = '23.0.0'
jooq-lib = '3.17.4'
log4j = "2.24.3"
kafka = "3.4.0"
kafka-avro-serializer = "7.6.1"
kotlin = "2.0.0"
kotlinx = "1.9.0"
ktor = "3.0.3"
micrometer = "1.10.6"
mockserver = "5.15.0"
mockk-core = "1.13.9"
prometheus = "0.16.0"
resilience4j = "1.7.1"
retrofit = "2.9.0"
testcontainers = "1.20.1"
wiremock = "3.9.1"
protobuf = '3.25.1'
caffeine = '3.1.8'
statsig = '1.17.3'
jsonwebtoken = '0.11.2'
slack-sdk = "1.44.1"
java-aws-sdk = '2.29.43'

[libraries]
apache-commons-csv = { module = "org.apache.commons:commons-csv", version.ref = "apache-commons" }
awaitility = { module = "org.awaitility:awaitility", version = "4.2.0" }
aws-s3-mock = { module = 'io.findify:s3mock_2.13', version = '0.2.6' }
aws-sdk-s3 = { module = "software.amazon.awssdk:s3", version.ref = "java-aws-sdk" }
aws-sdk-sts = { module = "software.amazon.awssdk:sts", version.ref = "java-aws-sdk" }
aws-sdk-sqs = { module = "software.amazon.awssdk:sqs", version.ref = "java-aws-sdk" }
coroutines-core = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-core', version.ref = "kotlinx" }
coroutines-jdk8 = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-jdk8', version.ref = "kotlinx" }
coroutines-test = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-test', version.ref = "kotlinx" }
detekt-gradle = { module = "io.gitlab.arturbosch.detekt:detekt-gradle-plugin", version.ref = "detekt" }
detekt-formatting = { module = "io.gitlab.arturbosch.detekt:detekt-formatting", version.ref = "detekt" }
detekt-cli = { module = "io.gitlab.arturbosch.detekt:detekt-cli", version.ref = "detekt" }
flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flyway" }
hellofresh-metrics = { module = "com.hellofresh:metrics", version.ref = "hellofresh-libs" }
hellofresh-service = { module = "com.hellofresh:service", version.ref = "hellofresh-libs" }
hellofresh-kafka-streams = { module = "com.hellofresh.kafka:streams", version.ref = "hellofresh-libs" }
hellofresh-schemaregistry = { module = "com.hellofresh:schema-registry", version.ref = "hellofresh-schemaregistry" }
hikaricp = { module = 'com.zaxxer:HikariCP', version = '5.0.1' }
jetbrains-annotations = { module = 'org.jetbrains:annotations', version.ref = 'jetbrains-annotations' }
jackson-cbor = { module = 'com.fasterxml.jackson.dataformat:jackson-dataformat-cbor', version.ref = 'jackson' }
jackson-databind = { module = 'com.fasterxml.jackson.core:jackson-databind', version.ref = 'jackson' }
jackson-jsr310 = { module = 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310', version.ref = 'jackson' }
jackson-kotlin = { module = 'com.fasterxml.jackson.module:jackson-module-kotlin', version.ref = 'jackson' }
jakarta-beanvalidation = { module = 'org.hibernate.validator:hibernate-validator', version = '7.0.4.Final' }
jooq-core = { module = 'org.jooq:jooq', version.ref = 'jooq-lib' }
jooq-coroutine = { module = 'org.jooq:jooq-kotlin-coroutines', version.ref = 'jooq-lib'}
junit-engine = { module = 'org.junit.jupiter:junit-jupiter-engine', version.ref = 'junit' }
junit-api = { module = 'org.junit.jupiter:junit-jupiter-api', version.ref = 'junit' }
junit-jupiter = { module = 'org.junit.jupiter:junit-jupiter', version.ref = 'junit' }
junit-params = { module = 'org.junit.jupiter:junit-jupiter-params', version.ref = 'junit' }
junit-bom = { module = 'org.junit:junit-bom', version.ref = 'junit' }
jgit = { module = 'org.eclipse.jgit:org.eclipse.jgit', version = '6.7.0.202309050840-r' }
kafka-clients = { module = "org.apache.kafka:kafka-clients", version.ref = "kafka" }
kafka-kafka213 = { module = "org.apache.kafka:kafka_2.13", version.ref = "kafka" }
kotlin-compiler-embeddable = { module = "org.jetbrains.kotlin:kotlin-compiler-embeddable", version.ref = "kotlin" }
krontab = { module = 'dev.inmo:krontab', version = '0.7.4' }
kafka-avro-serializer = { module = 'io.confluent:kafka-avro-serializer', version.ref = 'kafka-avro-serializer' }
kafka-schema-registry-client = { module = 'io.confluent:kafka-schema-registry-client', version.ref = 'kafka-avro-serializer' }
ktor-client-cio = { module = 'io.ktor:ktor-client-cio', version.ref = 'ktor' }
ktor-client-auth = { module = 'io.ktor:ktor-client-auth', version.ref = 'ktor' }
ktor-client-encoding = { module = 'io.ktor:ktor-client-encoding', version.ref = 'ktor' }
ktor-client-mock = { module = 'io.ktor:ktor-client-mock', version.ref = 'ktor' }
ktor-content-negotiation = { module = 'io.ktor:ktor-server-content-negotiation', version.ref = 'ktor' }
ktor-core = { module = 'io.ktor:ktor-server-core', version.ref = 'ktor' }
ktor-server-auth-jwt = { module = 'io.ktor:ktor-server-auth-jwt', version.ref = 'ktor' }
ktor-jackson = { module = 'io.ktor:ktor-serialization-jackson', version.ref = 'ktor' }
ktor-micrometer = { module = 'io.ktor:ktor-server-metrics-micrometer', version.ref = 'ktor' }
ktor-netty = { module = 'io.ktor:ktor-server-netty', version.ref = 'ktor' }
ktor-server-callid = { module = 'io.ktor:ktor-server-call-id', version.ref = 'ktor' }
ktor-server-compression = { module = 'io.ktor:ktor-server-compression-jvm', version.ref = 'ktor' }
ktor-swagger = { module = 'io.ktor:ktor-server-swagger', version.ref = 'ktor' }
ktor-test = { module = 'io.ktor:ktor-server-test-host', version.ref = 'ktor' }
micrometer-prometheus = { module = 'io.micrometer:micrometer-registry-prometheus', version.ref = 'micrometer' }
micrometer-core = { module = 'io.micrometer:micrometer-core', version.ref = 'micrometer' }
mockk = { module = 'io.mockk:mockk-jvm', version.ref = 'mockk-core' }
mockserver-junit = { module = 'org.mock-server:mockserver-junit-jupiter', version.ref = 'mockserver' }
mockserver-netty = { module = 'org.mock-server:mockserver-netty', version.ref = 'mockserver' }
okhttp-core = { module = 'com.squareup.okhttp3:okhttp', version = '4.10.0' }
postgresql-driver = { module = 'org.postgresql:postgresql', version = '42.6.0' }
prometheus-simpleclient-common = { module = 'io.prometheus:simpleclient_common', version.ref = 'prometheus' }
prometheus-pushgateway = { module = 'io.prometheus:simpleclient_pushgateway', version.ref = 'prometheus' }
protobuf-java-format = { module = 'com.googlecode.protobuf-java-format:protobuf-java-format', version = '1.4' }
resilience4j-circuitbreaker = { module = 'io.github.resilience4j:resilience4j-circuitbreaker', version.ref = 'resilience4j' }
resilience4j-retrofit = { module = 'io.github.resilience4j:resilience4j-retrofit', version.ref = 'resilience4j' }
resilience4j-retry = { module = 'io.github.resilience4j:resilience4j-retry', version.ref = 'resilience4j' }
resilience4j-kotlin = { module = 'io.github.resilience4j:resilience4j-kotlin', version.ref = 'resilience4j' }
restassured = { module = 'io.rest-assured:rest-assured', version = '5.5.0' }
restassured-validator = { module = 'io.rest-assured:json-schema-validator', version = '5.5.0' }
retrofit-core = { module = 'com.squareup.retrofit2:retrofit', version.ref = 'retrofit' }
retrofit-jackson = { module = 'com.squareup.retrofit2:converter-jackson', version.ref = 'retrofit' }
statsig = {module = 'com.statsig:serversdk', version.ref = 'statsig' }
testcontainers-core = { module = "org.testcontainers:testcontainers", version.ref = "testcontainers" }
testcontainers-kafka = { module = "org.testcontainers:kafka", version.ref = "testcontainers" }
testcontainers-postgresql = { module = "org.testcontainers:postgresql", version.ref = "testcontainers" }
testcontainers-junit = { module = 'org.testcontainers:junit-jupiter', version.ref = "testcontainers" }
log4j-api2 = { module = "org.apache.logging.log4j:log4j-api", version.ref = "log4j" }
log4j-bom = { module = "org.apache.logging.log4j:log4j-bom", version.ref = "log4j" }
log4j-core = { module = "org.apache.logging.log4j:log4j-core", version.ref = "log4j" }
log4j-json = { module = "org.apache.logging.log4j:log4j-layout-template-json", version.ref = "log4j" }
log4j-kotlin = { module = "org.apache.logging.log4j:log4j-api-kotlin", version = "1.2.0" }
log4j-slf4j = { module = "org.apache.logging.log4j:log4j-slf4j-impl" }
okhttp = "com.squareup.okhttp3:okhttp:4.9.3"
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
slack-appender = "be.olsson:slack-appender:1.3.0"
slack-api-client = { module = "com.slack.api:slack-api-client", version.ref = 'slack-sdk' }
slack-api-client-kotlin-extension = { module = "com.slack.api:slack-api-client-kotlin-extension", version.ref = 'slack-sdk' }
# buildSrc dependencies (plugins)
kotlin-allopen = { module = "org.jetbrains.kotlin:kotlin-allopen", version.ref = "kotlin" }
kotlin-noarg = { module = "org.jetbrains.kotlin:kotlin-noarg", version.ref = "kotlin" }
kotlin-gradle = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
jackson-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }
jib-gradle = { module = "com.google.cloud.tools:jib-gradle-plugin", version.ref = "jib" }
protobuf-java-util = { module = 'com.google.protobuf:protobuf-java-util', version.ref = 'protobuf' }
wiremock = { module = " org.wiremock:wiremock-standalone", version = "3.9.1" }
caffeine-core = { module = 'com.github.ben-manes.caffeine:caffeine', version.ref = 'caffeine'}
jjwtapi = { group = "io.jsonwebtoken", name = "jjwt-api", version.ref = "jsonwebtoken" }
jjwtimpl = { group = "io.jsonwebtoken", name = "jjwt-impl", version.ref = "jsonwebtoken" }
jjwtjackson = { group = "io.jsonwebtoken", name = "jjwt-jackson", version.ref = "jsonwebtoken" }

[plugins]
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
flyway = { id = "org.flywaydb.flyway", version.ref = "flyway" }
gradle-docker-compose = { id = "com.avast.gradle.docker-compose", version = "0.17.6" }
jooq = { id = "nu.studer.jooq", version = "9.0" }
openapi = { id = 'org.openapi.generator', version = '7.2.0' }
shadow = { id = "com.github.johnrengelman.shadow", version = "7.1.2" }
sonar = { id = "org.sonarqube", version = "6.0.1.5171" }
jacoco = { id = "org.barfuin.gradle.jacocolog", version = "3.1.0" }

[bundles]
main-compileOnly = ['jetbrains-annotations']
test-implementation = ['junit-jupiter', 'junit-params', 'junit-api', 'mockk']
test-runtime = ['junit-engine']
