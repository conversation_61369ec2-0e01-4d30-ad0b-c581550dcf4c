name: Gather Logs
description: Gather Logs
inputs:
  path_to_docker_compose:
    description: Path to docker compose
    required: true
  is_ui:
    description: Does run contain ui container
    required: false
    default: true
  suite_name:
    description: Test suite name (api, ui-ci, jobs)
    required: true
runs:
  using: composite
  steps:
    - name: Services Logs
      shell: bash
      run: |
        docker compose -f ./ci/docker-compose-back-ci.yml logs procurement-db
        docker compose -f ./ci/docker-compose-back-ci.yml logs cache
        docker compose -f ./ci/docker-compose-back-ci.yml logs hj
        docker compose -f ./ci/docker-compose-back-ci.yml logs kafka
        docker compose -f ./ci/docker-compose-back-ci.yml logs zookeeper

    - name: Backend Logs
      shell: bash
      run: docker compose -f ./ci/docker-compose-back-ci.yml logs back

    - name: Consumer Logs
      shell: bash
      run: docker compose -f ./ci/docker-compose-back-ci.yml logs consumer

    - name: Worker Logs
      shell: bash
      run: docker compose -f ./ci/docker-compose-back-ci.yml logs worker

    - name: Frontend Logs
      if: ${{ inputs.is_ui == 'true' }}
      shell: bash
      run: docker compose -f ./ci/docker-compose-front-ci.yml logs front

    - name: Allure report
      uses: ./ci/actions/allure-report
      with:
        suite_name: ${{ inputs.suite_name }}
