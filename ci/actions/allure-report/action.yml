name: Allure Report
description: Generate and upload allure report
inputs:
  suite_name:
    description: Test suite name (api, ui, jobs)
    required: true
runs:
  using: composite
  steps:
    - name: Setup java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '11'
    - name: Install allure
      shell: bash
      run: |
        wget -q -O allure.zip https://repo1.maven.org/maven2/io/qameta/allure/allure-commandline/2.13.6/allure-commandline-2.13.6.zip
        unzip -q allure.zip

    - name: Generate report
      shell: bash
      run: |
        allure-2.13.6/bin/allure generate --clean -o /tmp/allure_report /tmp/allure-results/

    - name: Zip Report
      shell: bash
      run: |
        zip -r /tmp/allure_report.zip /tmp/allure_report

    - name: Upload Report
      uses: actions/upload-artifact@v4
      with:
        name: allure_report_${{ inputs.suite_name }}
        path: /tmp/allure_report.zip
        retention-days: 1
