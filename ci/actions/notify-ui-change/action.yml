name: Notify UI changed
description: Notifies UI clients that code was changed

inputs:
  config:
    description: env app config
    required: true
  new_version:
    description: new version of the app
    required: true

runs:
  using: composite
  steps:
    - name: Push Redis notification
      shell: bash
      env:
        KEY: imt:notifications:v2
        VALUE: '{"event": "release", "data": {"version": "${{ inputs.new_version }}"}}'
        CONFIG: ${{ inputs.config }}
      run: ./ci/scripts/redis-publish.sh
