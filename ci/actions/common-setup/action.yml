name: Setup Common Steps
description: Setups python, secrets, etc.
runs:
  using: composite
  steps:
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.12"

    - name: Setup Microsoft ODBC
      shell: bash
      run: |
        curl -sSL -O https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/packages-microsoft-prod.deb
        sudo dpkg -i packages-microsoft-prod.deb
        rm packages-microsoft-prod.deb
        sudo apt-get update
        sudo apt-get install -y unixodbc
        sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18

    - name: Install poetry
      shell: bash
      run: |
        export POETRY_VERSION=1.8.0
        curl -sSL https://install.python-poetry.org | python3 -
        

    - name: Install tox
      shell: bash
      run: pip install tox
