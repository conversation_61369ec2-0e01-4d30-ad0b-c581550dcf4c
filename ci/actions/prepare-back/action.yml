name: Prepare backend part
description: Prepare and start docker compose for backend
inputs:
  GOO<PERSON><PERSON>_SHEETS_CONFIG:
    description: Google Sheet config
    required: true
  list_of_components_to_run:
    description: List of Docker container that required for tests
    required: false
  CONFIG:
    description: App config
    required: false
runs:
  using: composite
  steps:
    - name: Download back
      uses: actions/download-artifact@v4
      with:
        name: inventory-management-us
        path: /tmp

    - name: Load Docker image
      shell: bash
      run: |
        docker load --input /tmp/inventory-management-us.tar

    - name: Start Compose
      shell: bash
      env:
        GOOGLE_SHEETS_CONFIG: ${{ inputs.GOOGLE_SHEETS_CONFIG }}
        CONFIG: ${{ inputs.CONFIG }}
      run: docker compose -f ./ci/docker-compose-back-ci.yml up -d ${{inputs.list_of_components_to_run}}

    - name: Wait Compose
      shell: bash
      run: |
        pip install docker-compose-wait
        docker-compose-wait -f ./ci/docker-compose-back-ci.yml -w -t 7m
