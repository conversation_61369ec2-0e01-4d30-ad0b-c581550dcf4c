name: Build Docker image
description: Builds docker image
inputs:
  path:
    description: Path to context
    required: true
  name:
    description: Name of the image
    required: true
  build-args:
    description: Additional build args
    required: false
    default: ''
runs:
  using: composite
  steps:
    - name: Set up Docker Context for Buildx
      shell: bash
      run: |
        docker context create builders

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        endpoint: builders
    - name: Build ${{ inputs.name }}
      uses: docker/build-push-action@v6
      with:
        context: ${{ inputs.path }}
        tags: ${{ inputs.name }}:latest
        outputs: type=docker,dest=/tmp/${{ inputs.name }}.tar
        build-args: ${{inputs.build-args }}

    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.name }}
        path: /tmp/${{ inputs.name }}.tar
        retention-days: 1
