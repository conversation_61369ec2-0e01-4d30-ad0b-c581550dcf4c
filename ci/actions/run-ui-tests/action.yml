name: Start Compose
description: Prepare and start docker compose
inputs:
  tox_folder:
    description: Folder of tox.ini
    required: true
  tox_profile:
    description: tox profile to run
    required: true
  list_of_components_to_run:
    description: List of Docker container that required for tests
    required: false

runs:
  using: composite
  steps:

    - name: Import vault secrets
      id: vault-secrets
      uses: hellofresh/jetstream-ci-scripts/actions/vault@master
      with:
        secrets: |
          common/key-value/data/cd google_sheets_config | GOOGLE_SHEETS_CONFIG;

    - name: Common Setup
      id: common_setup
      uses: ./ci/actions/common-setup

    - name: Start backend
      with:
        GOOGLE_SHEETS_CONFIG: ${{ env.GOOGLE_SHEETS_CONFIG }}
        list_of_components_to_run: ${{ inputs.list_of_components_to_run }}
      uses: ./ci/actions/prepare-back

    - name: Start frontend
      uses: ./ci/actions/prepare-front

    - name: Run UI tests for ${{ inputs.tox_profile }}
      env:
        CI: true
        GOOGLE_SHEETS_CONFIG: ${{ env.GOOGLE_SHEETS_CONFIG }}
      shell: bash
      run: |
        sudo echo "127.0.0.1 back" | sudo tee -a /etc/hosts
        sudo echo "127.0.0.1 front" | sudo tee -a /etc/hosts
        cd ${{ inputs.tox_folder }}
        tox -e ${{ inputs.tox_profile }}

    - name: Gather logs
      if: ${{ failure() }}
      uses: ./ci/actions/gather-logs
      with:
        suite_name: ${{ inputs.tox_profile }}

    - name: Stop Compose
      if: ${{ always() }}
      shell: bash
      run: |
        docker compose -f ./ci/docker-compose-front-ci.yml -f ./ci/docker-compose-back-ci.yml down
