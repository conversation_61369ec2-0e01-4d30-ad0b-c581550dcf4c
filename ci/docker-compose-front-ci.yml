services:
  front:
    image: inventory-management-us-front:latest
    environment:
      VUE_APP_API_URL: http://back:8000
      VUE_APP_TEST_ENV: "true"
      VUE_APP_DEBUG: "true"
      VUE_APP_PO_SHIPMENT_ENABLED: "true"
      VUE_APP_WEBSOCKET_ENABLED: "true"
      VUE_APP_PACKAGING_ENABLED: "true"
      VUE_APP_PACKAGING_DEPLETION_ENABLED: "true"
      VUE_APP_PIMT_REPLENISHMENT_ENABLED: "true"
      VUE_APP_PACKAGING_SAFETY_STOCK_ENABLED: "true"
      VUE_APP_REPLENISHMENT_NEW_ENABLED: "true"
      VUE_APP_MULTI_WEEK_PO_VIEW_ENABLED: "true"
      VUE_APP_UNIFIED_PO_STATUS_ENABLED: "true"
      VUE_APP_WEEKEND_CHECKLIST_PO_STATUS_ENABLED: "true"
      VUE_APP_FORECAST_DIALOG_ENABLED: "true"
      VUE_APP_PO_STATUS_PHF_DELIVERY_PERCENT_ENABLED: "true"
      VUE_APP_NETWORK_DEPLETION_INVENTORY_DIALOG_ENABLED: "true"
      VUE_APP_ICS_TICKETS_ENABLED: "true"
      VUE_APP_NEW_INGREDIENT_DEPLETION_ENABLED: "true"
      VUE_APP_WORK_ORDER_ENABLED: "true"
      VUE_APP_HJ_DONATIONS_ENABLED: "true"
      VUE_APP_INVENTORY_COSTING_ENABLED: "true"
      VUE_APP_LIVE_ROW_NEED_NEW_POSITION_ACTIVE: "true"
      VUE_APP_ROW_NEED_RENAMING_ACTIVE: "true"
      VUE_APP_TOPO_SECTION_ENABLED: "true"
      CONFIG: >
        {
          "cache": { "host": "cache", "port": 6379 }
        }
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080" ]
      timeout: 30s
      retries: 30
      interval: 2s
    restart: on-failure
    ports:
      - "8080:8080"