services:
  procurement-db:
    image: public.ecr.aws/bitnami/postgresql:15.3.0
    environment:
      POSTGRES_USER: inventory_management_us
      POSTGRES_PASSWORD: inventory_management_us
      POSTGRES_DB: inventory_management_us
    healthcheck:
      test: ["CMD", "pg_isready", "-d", "inventory_management_us", "-U", "inventory_management_us"]
      interval: 2s
      timeout: 30s
      retries: 30
    ports:
      - "7432:5432"

  cache:
    image: public.ecr.aws/bitnami/redis:5.0.10
    environment:
      ALLOW_EMPTY_PASSWORD: "yes"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 2s
      timeout: 30s
      retries: 30
    ports:
      - "7379:6379"

  hj:
    image: mcr.microsoft.com/azure-sql-edge
    environment:
      ACCEPT_EULA: Y
      SA_PASSWORD: "My!StrongPassword"
    ports:
      - "1433:1433"

  back:
    build: ../back

    environment:
      APP_TYPE: BACK
      CI: true
      CONFIG: >
        {
          "flask": { "env": "dev"},
          "database": { "host": "procurement-db", "port": "5432" },
          "cache": { "host": "cache", "port": 6379 },
          "hj_db": {"host": "hj", "port": 1433}
        }
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/metrics" ]
      timeout: 30s
      retries: 30
      interval: 2s
    restart: on-failure
    ports:
      - "8000:8000"

  worker:
    build: ../back
    command: [ "rq", "worker", "-c", "procurement.core.worker_config", "-w", "worker.ProcurementWorker", "--with-scheduler" ]
    environment:
      APP_TYPE: WORKER
      CI: true
      CONFIG: >
        {
          "flask": { "env": "dev"},
          "database": { "host": "procurement-db", "port": "5432" },
          "cache": { "host": "cache", "port": 6379 },
          "hj_db": {"host": "hj", "port": 1433},
          "green_chef_hj_db": {"host": "hj", "port": 1433},
          "kafka":{
                "bootstrap_servers":[
                   "kafka:9092"
                ],
                "topics":{
                   "produce":{
                      "forecast_upload":"public.ordering.sku-inventory-demand-forecast.week-based.v1"
                   }
                }
             }
        }
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8001/metrics" ]
      timeout: 30s
      retries: 30
      interval: 2s
    restart: on-failure
    ports:
      - "8001:8000"

  consumer:
    depends_on:
      procurement-db:
        condition: service_started
      cache:
        condition: service_started
      kafka:
        condition: service_healthy
      back:
        condition: service_healthy
    build: ../back
    command: "python consumer.py"
    environment:
      APP_TYPE: CONSUMER
      CI: true
      CONFIG: >
        {
          "flask":{"env":"dev"},
          "database":{ "host":"procurement-db", "port":"5432" },
          "cache":{ "host":"cache","port":6379},
          "kafka": {
            "bootstrap_servers":[ "kafka:9092" ],
            "timeouts":{"consume":2},
            "topics":{
              "consume":{
                "grn":"public.distribution-center.inbound.goods-received-note.v1",
                "blujay_appointment":"public.distribution-center.third-party.blujay.inbound.blujay-appointment.v1beta1",
                "blujay_shipment": "public.distribution-center.third-party.blujay.inbound.purchase-order-shipment.v1beta1",
                "advance_shipping_notice": "public.supply.advance-shipping-notice.v2",
                "ot": "public.supply.procurement.purchase-order.v1",
                "dc": "public.scm.registry.dc.v1beta1",
                "transfer_order": "public.transfer-order.v1alpha5",
                "manufactured_sku": "public.rte.manufactured-sku.v4",
                "work_order_tool": "public.rte.work-order.v2",
                "inventory_snapshot": "public.distribution-center.inventory.snapshot.v1",
                "supplier": "public.planning.facility.v1",
                "culinary_sku": "public.planning.culinarysku.v1",
                "po_acknowledgement": "public.supply.purchase-order.acknowledgement.v2",
                "packaging_info": "rawevents.suppliersku.packaging.us.beta",
                "supplier_sku": "rawevents.suppliersku.us.beta",
                "purchase_qty_recommendation": "public.ordering.purchase-quantity-recommendation.v1alpha1"
              }
            }
          }
        }


  front:
    build: ../front
    environment:
      VUE_APP_API_URL: http://back:8000
      VUE_APP_TEST_ENV: "true"
      VUE_APP_DEBUG: "true"
      VUE_APP_PO_SHIPMENT_ENABLED: "true"
      VUE_APP_WEBSOCKET_ENABLED: "true"
      VUE_APP_PACKAGING_ENABLED: "true"
      VUE_APP_PACKAGING_DEPLETION_ENABLED: "true"
      VUE_APP_PIMT_REPLENISHMENT_ENABLED: "true"
      VUE_APP_PACKAGING_SAFETY_STOCK_ENABLED: "true"
      VUE_APP_REPLENISHMENT_NEW_ENABLED: "true"
      VUE_APP_FORECAST_DIALOG_ENABLED: "true"
      CONFIG: >
        {
          "cache": { "host": "cache", "port": 6379 }
        }
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080" ]
      timeout: 30s
      retries: 30
      interval: 2s
    restart: on-failure
    depends_on:
      - back
      - worker
    ports:
      - "8080:8080"


  zookeeper:
    image: confluentinc/cp-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - 22181:2181

  kafka:
    image: confluentinc/cp-kafka
    depends_on:
      - zookeeper
    healthcheck:
      test: [ "CMD", "kafka-topics", "--list", "--bootstrap-server", "localhost:29092" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    ports:
      - 29092:29092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      ALLOW_PLAINTEXT_LISTENER: yes
