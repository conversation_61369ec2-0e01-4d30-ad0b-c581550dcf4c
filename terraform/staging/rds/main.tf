locals {
    tribe               = "planning-and-purchasing"
    squad               = "supply-automation"
    project_name        = "sku-demand-forecast"
    environment         = "staging"
    monitoring_interval = 10
    monitoring_role_arn = data.aws_iam_role.rds-monitoring.arn
    family              = "postgres16"
    ca_cert_identifier  = "rds-ca-rsa2048-g1"
    apply_immediately   = true

    db_settings         = {
        option_group_name   = "default:postgres-16"
        name                = local.project_name
        db_name             = "sdf"
        engine              = "postgres"
        engine_version      = "16"
        instance_class      = "db.t3.small"
        port                = "5432"
    }
}

provider "vault" {
    namespace = "services/${local.project_name}"
}

data "vault_generic_secret" "vault_secrets" {
    path = format("${local.environment}/key-value/rds")
}

module "rds" {
    source                      = "github.com/hellofresh/tf-hf-rds?ref=1.9.0"
    environment                 = local.environment
    name                        = local.db_settings.name
    database_name               = local.db_settings.db_name
    username                    = data.vault_generic_secret.vault_secrets.data.DB_USERNAME
    password                    = data.vault_generic_secret.vault_secrets.data.DB_PASSWORD
    instance_class              = local.db_settings.instance_class
    engine                      = local.db_settings.engine
    engine_version              = local.db_settings.engine_version
    port                        = local.db_settings.port
    option_group_name           = local.db_settings.option_group_name
    allocated_storage           = "100"
    allow_major_version_upgrade = "true"
    monitoring_interval         = local.monitoring_interval
    monitoring_role_arn         = local.monitoring_role_arn
    parameter_group_name        = local.project_name
    family                      = local.family
    ca_cert_identifier          = local.ca_cert_identifier
    apply_immediately           = local.apply_immediately

    parameters =[{
            name  = "statement_timeout"
            value = "25000" # Set the timeout in milliseconds
          },
          {
            name         = "shared_preload_libraries"
            value        = "pg_cron"
            apply_method = "pending-reboot"
           },
           {
             name         = "cron.database_name"
             value        = "sdf"
             apply_method = "pending-reboot"
           }]

    tags                        = {
        Environment = local.environment
        Squad       = local.squad
        Tribe       = local.tribe
    }
}


