resource "aws_iam_policy" "staging-k8s-policy" {
    name = "sku-demand-forecast-staging-k8s-policy"

    policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowReadObject",
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:GetObjectAcl",
        "s3:ListBucket",
        "s3:ListMultipartUploadParts",
        "s3:ListBucketMultipartUploads"
      ],
      "Resource": [
        "arn:aws:s3:::scm-ordering-tool-uploads-staging",
        "arn:aws:s3:::scm-ordering-tool-uploads-staging/*",
        "arn:aws:s3:::hf-bi-dwh-uploader-staging",
        "arn:aws:s3:::hf-bi-dwh-uploader-staging/*"
      ]
    },
        {
            "Action": [
                "sts:AssumeRole"
            ],
            "Effect": "Allow",
            "Resource": [
                "arn:aws:iam::************:role/procurement-inventory-live-staging"
            ],
            "Sid": "AllowWebIdentityAccessMainBI"
        }
  ]
}
EOF
}


module "oidc_procurement_inventory" {
    source = "git::https://github.com/hellofresh/terraform-aws-iam-irsa-role.git"

    iam_role_name          = "sku-demand-forecast-staging-role"
    iam_trusted_roles_arns = ["arn:aws:iam::************:role/procurement-inventory-live-staging"]
    environments           = ["staging"]

    kubernetes_service_account = "*"
    kubernetes_namespace       = "scm"

    tags = {
        tribe       = "planning-and-purchasing"
        squad       = "supply-automation"
        jira_id     = "CRS-2442"
        Environment = "Staging"
    }
}

output "oidc_procurement_inventory_iam_role_arn" {
    description = "ARN of IAM role"
    value       = module.oidc_procurement_inventory.iam_role_arn
}
resource "aws_iam_role_policy_attachment" "staging-attachment" {
    role       = module.oidc_procurement_inventory.iam_role_name
    policy_arn = aws_iam_policy.staging-k8s-policy.arn
}
