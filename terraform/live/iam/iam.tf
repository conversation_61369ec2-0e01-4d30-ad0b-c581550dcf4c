resource "aws_iam_policy" "live-k8s-policy" {
    name = "sku-demand-forecast-live-k8s-policy"

    policy = <<EOF
{
  "Statement": [
        {
            "Action": [
                "s3:GetObject",
                "s3:GetObjectAcl",
                "s3:ListBucket",
                "s3:ListMultipartUploadParts",
                "s3:ListBucketMultipartUploads"
            ],
            "Effect": "Allow",
            "Resource": [
                "arn:aws:s3:::scm-ordering-tool-uploads-live",
                "arn:aws:s3:::scm-ordering-tool-uploads-live/*",
                "arn:aws:s3:::hf-bi-dwh-uploader",
                "arn:aws:s3:::hf-bi-dwh-uploader/*"
            ],
            "Sid": "AllowReadObject"
        },
        {
            "Action": [
                "sts:AssumeRole"
            ],
            "Effect": "Allow",
            "Resource": [
                "arn:aws:iam::************:role/procurement-inventory-live-staging"
            ],
            "Sid": "AllowWebIdentityAccessMainBI"
        }
    ],
    "Version": "2012-10-17"
}
EOF
}

module "oidc_procurement_inventory" {
    source = "git::https://github.com/hellofresh/terraform-aws-iam-irsa-role.git"

    iam_role_name          = "sku-demand-forecast-live-role"
    iam_trusted_roles_arns = ["arn:aws:iam::************:role/procurement-inventory-live-staging"]
    environments           = ["live"]

    kubernetes_service_account = "*"
    kubernetes_namespace       = "scm"

    tags = {
        tribe       = "planning-and-purchasing"
        squad       = "supply-automation"
        jira_id     = "CRS-2442"
        Environment = "Live"
    }
}

output "oidc_procurement_inventory_iam_role_arn" {
    description = "ARN of IAM role"
    value       = module.oidc_procurement_inventory.iam_role_arn
}
resource "aws_iam_role_policy_attachment" "live-attachment" {
    role       = module.oidc_procurement_inventory.iam_role_name
    policy_arn = aws_iam_policy.live-k8s-policy.arn
}

