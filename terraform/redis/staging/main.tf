locals {
  environment              = "staging"
  instance_name            = "inventory-management-us"
  description              = "Redis cluster for inventory-management-us (rw)"
  node_type                = "cache.t2.small"
  num_cache_nodes          = 2
  maintenance_window       = "sun:05:00-sun:09:00"
  apply_immediately        = "true"
  snapshot_retention_limit = 0
  engine_version           = "5.0.6"
  port                     = "6379"
  parameter_group_name     = "default.redis5.0"
}

data "aws_vpc" "default" {
  default = true
}

data "aws_security_group" "default" {
  id     = "sg-fde92199"
  vpc_id = data.aws_vpc.default.id
}

data "aws_route53_zone" "hellofresh_io" {
  name = "hellofresh.io"
}

resource "aws_elasticache_replication_group" "redis-instance" {
  engine                        = "redis"
  engine_version                = local.engine_version
  port                          = local.port
  automatic_failover_enabled    = false
  auto_minor_version_upgrade    = true
  replication_group_id          = "${local.instance_name}-${local.environment == "live" ? "live" : "stg"}"
  description                   = local.description
  node_type                     = local.node_type
  num_cache_clusters            = local.num_cache_nodes
  parameter_group_name          = local.parameter_group_name
  maintenance_window            = local.maintenance_window
  apply_immediately             = local.apply_immediately
  security_group_ids            = [data.aws_security_group.default.id]
}

resource "aws_route53_record" "dns_master" {
  zone_id = data.aws_route53_zone.hellofresh_io.id
  name    = "${local.instance_name}-redis.${local.environment}.hellofresh.io"
  type    = "CNAME"
  ttl     = "30"
  records = [aws_elasticache_replication_group.redis-instance.primary_endpoint_address]
}
