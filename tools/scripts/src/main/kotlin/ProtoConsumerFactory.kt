package com.hellofresh.skuDemandForecast.scripts

import com.hellofresh.kafka.serde.serde
import com.hellofresh.proto.stream.ordering.skuDemandForecast.v1beta1.SkuDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuDemandForecast.v1beta1.SkuDemandForecastVal

@Suppress("UseIfInsteadOfWhen")
class ProtoConsumerFactory private constructor() {
    companion object {
        fun getConsumer(arguments: Arguments): ProtoConsumer<*, *> = when (arguments[Arguments.TOPIC_ARG]) {
            "public.sku-demand-forecast.v1" -> ProtoConsumer(
                arguments,
                serde<SkuDemandForecastKey>().deserializer(),
                serde<SkuDemandForecastVal?>().deserializer(),
            )
            else -> error("Unknown topic ${arguments[Arguments.TOPIC_ARG]}")
        }
    }
}
