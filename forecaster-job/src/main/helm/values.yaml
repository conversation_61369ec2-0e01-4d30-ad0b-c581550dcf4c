---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  app:
    deploymentStrategy:
      type: Recreate
    replicaCount: 1
    minAvailable: 0
    containerPorts:
      http: 8080
      http-actuator: 8081
      jmx: '@jmxPort@'
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '4Gi'
        cpu: '2'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    hpa:
      enabled: false
    spotInstance:
      preferred: true
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 300
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      initialDelaySeconds: 10
      timeoutSeconds: 3
      failureThreshold: 15
      periodSeconds: 10

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'

