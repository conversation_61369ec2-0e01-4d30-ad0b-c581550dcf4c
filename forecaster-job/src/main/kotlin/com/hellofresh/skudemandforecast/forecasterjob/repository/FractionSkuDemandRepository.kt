package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.forecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.forecasterjob.model.FractionSkuDemand

interface FractionSkuDemandRepository {
    suspend fun save(
        transactionalContext: MetricsDSLContext,
        fractionSkuDemands: List<FractionSkuDemand>,
        // All the dates in a DC and Week
        dcDates: Collection<DcDate>
    )
}
