package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.Tables.DC_CONFIG
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.Tables.SKU_SPECIFICATION
import java.util.Locale
import java.util.UUID
import kotlinx.coroutines.future.await

private typealias DcCode = String
private typealias SkuCode = String

data class SkuSpec(
    val fumigationAllowed: Boolean?,
    val skuId: UUID?,
    val packagingType: String,
)

fun interface SkuRepository {
    suspend fun fetchSkuSpec(dcToCode: List<Pair<DcCode, SkuCode>>): Map<Pair<DcCode, SkuCode>, SkuSpec?>

    suspend fun fetchSkuSpec(dcCode: DcCode, skuCode: SkuCode): SkuSpec? =
        fetchSkuSpec(listOf(dcCode to skuCode)).values.firstOrNull()
}

object SkuRepositoryBuilder {
    suspend operator fun invoke(dsl: MetricsDSLContext): SkuRepository {
        val dcCodeToMarket = dsl.select(DC_CONFIG.MARKET, DC_CONFIG.DC_CODE)
            .from(DC_CONFIG)
            .fetchAsync()
            .thenApply { dcConfig ->
                dcConfig.associate { it[DC_CONFIG.DC_CODE] to it[DC_CONFIG.MARKET] }
            }
            .await()

        val skuCodeToSpec = dsl.select(
            SKU_SPECIFICATION.MARKET,
            SKU_SPECIFICATION.ID,
            SKU_SPECIFICATION.CODE,
            SKU_SPECIFICATION.PACKAGING,
            SKU_SPECIFICATION.FUMIGATION_ALLOWED
        )
            .from(SKU_SPECIFICATION)
            .fetchAsync()
            .thenApply { skuSpecification ->
                skuSpecification.associate {
                    Pair(
                        it[SKU_SPECIFICATION.MARKET].uppercase(Locale.getDefault()),
                        it[SKU_SPECIFICATION.CODE]
                    ) to SkuSpec(
                        fumigationAllowed = it[SKU_SPECIFICATION.FUMIGATION_ALLOWED],
                        skuId = it[SKU_SPECIFICATION.ID],
                        packagingType = it[SKU_SPECIFICATION.PACKAGING]
                    )
                }
            }
            .await()

        return SkuRepository { dcToSkuCode: List<Pair<DcCode, SkuCode>> ->
            dcToSkuCode.associate { (dcCode, skuCode) ->
                val skuSpec = dcCodeToMarket[dcCode]?.let { market ->
                    skuCodeToSpec[Pair(market, skuCode)]
                }
                Pair(dcCode, skuCode) to skuSpec
            }
        }
    }
}
