package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.forecasterjob.MissingRecipeDetails
import com.hellofresh.skudemandforecast.forecasterjob.model.ErrorsPerFile
import java.util.UUID

interface FileUploadsRepository {
    // PROCESSED -> PROCESSED_WITH_ERROR, CREATED -> CREATED_WITH_ERROR
    suspend fun updateStatusWithError(
        transactionalContext: MetricsDSLContext,
        processedFileErrors: List<ErrorsPerFile>,
        missingRecipeIds: Map<UUID, MissingRecipeDetails>,
    )

    //  PROCESSED_WITH_ERROR -> PROCESSED, CREATED_WITH_ERROR ->  CREATED
    suspend fun updateStatusWithNoError(
        transactionalContext: MetricsDSLContext,
        filesWithNoError: Collection<UUID>
    )
}
