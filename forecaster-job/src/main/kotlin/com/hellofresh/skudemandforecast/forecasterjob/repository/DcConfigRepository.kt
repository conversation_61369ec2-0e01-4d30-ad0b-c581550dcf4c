package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.forecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.Tables.DC_CONFIG
import java.time.DayOfWeek
import java.time.ZoneId
import kotlinx.coroutines.future.await

fun interface DcConfigRepository {
    suspend fun fetch(): List<DcConfig>
}

class DcConfigRepositoryJooqImpl(private val dsl: MetricsDSLContext) : DcConfigRepository {
    private val tagName = "fetch-dc-config"
    override suspend fun fetch(): List<DcConfig> = dsl.withTagName(tagName)
        .selectFrom(DC_CONFIG)
        .fetchAsync()
        .await()
        .map { dcConfigRecord ->
            DcConfig(
                dcCode = dcConfigRecord.dcCode,
                market = dcConfigRecord.market,
                productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart),
                zoneId = ZoneId.of(dcConfigRecord.zoneId),
                globalDc = dcConfigRecord.globalDc,
                enabled = dcConfigRecord.enabled,
            )
        }
}
