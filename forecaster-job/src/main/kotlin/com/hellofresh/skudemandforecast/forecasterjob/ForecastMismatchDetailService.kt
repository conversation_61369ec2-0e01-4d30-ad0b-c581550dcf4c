package com.hellofresh.skudemandforecast.forecasterjob

import com.hellofresh.skudemandforecast.forecasterjob.model.MismatchKey
import com.hellofresh.skudemandforecast.forecasterjob.model.MismatchedDetail
import com.hellofresh.skudemandforecast.forecasterjob.model.RecipeDemandKey

object ForecastMismatchDetailService {
    fun findMismatchDetails(
        matchedEntries: List<RecipeDemandKey>,
        unmatchedEntries: List<RecipeDemandKey>
    ): List<MismatchedDetail> {
        val keyValuesMatched = matchedEntries.groupBy { MismatchKey(it.country, it.week) }
        val keyValuesUnMatched = unmatchedEntries.groupBy { MismatchKey(it.country, it.week) }

        return keyValuesMatched.mapNotNull { (key, matchedValues) ->
            val unmatchedValues = keyValuesUnMatched[key] ?: emptyList()
            val matchedSize = matchedValues.size
            MismatchedDetail(key, matchedSize, unmatchedValues)
        }
    }
}
