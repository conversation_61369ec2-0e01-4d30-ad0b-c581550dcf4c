package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.recipelib.CountryWeek
import com.hellofresh.skudemandforecast.forecasterjob.model.UploadedDemand
import java.time.LocalDateTime

interface DemandRepository {
    suspend fun fetch(countryWeek: List<CountryWeek>): List<UploadedDemand>
    suspend fun resetDirtyBit(transactionalContext: MetricsDSLContext, timestamp: LocalDateTime)
    suspend fun fetchUpdatedCountryWeek(): List<CountryWeek>
}
