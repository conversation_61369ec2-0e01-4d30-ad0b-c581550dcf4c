package com.hellofresh.skudemandforecast.forecasterjob.model

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class FractionDemand(
    val skuId: UUID,
    val dcCode: String,
    val demandDate: LocalDate,
    val country: String,
    val locale: String,
    val productFamily: String,
    val recipeIndex: Int,
    val qty: Long,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime? = null
)
