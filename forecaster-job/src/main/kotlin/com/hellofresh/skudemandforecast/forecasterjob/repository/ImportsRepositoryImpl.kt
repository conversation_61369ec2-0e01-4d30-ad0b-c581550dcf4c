package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.forecasterjob.MissingRecipeDetails
import com.hellofresh.skudemandforecast.forecasterjob.model.ErrorsPerFile
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus.CREATED_WITH_ERROR
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus.PROCESSED
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus.PROCESSED_WITH_ERROR
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.impl.DSL

object ImportsRepositoryImpl : FileUploadsRepository, Logging {
    private const val UPDATED_PROCESSED_ERRORS = "import-processed-file-errors"

    @Suppress("SpreadOperator")
    override suspend fun updateStatusWithError(
        transactionalContext: MetricsDSLContext,
        processedFileErrors: List<ErrorsPerFile>,
        missingRecipeIds: Map<UUID, MissingRecipeDetails>,
    ) {
        logger.info(
            """
                  There are ${processedFileErrors.size} errors while processing recipe & demand records,
                  Error details = $processedFileErrors
            """.trimIndent(),
        )

        val fileErrorUpdates = processedFileErrors.map { errorDetail ->
            val missingRecipes = missingRecipeIds[errorDetail.fileSourceId]
                ?.let {
                    listOf(
                        "Following recipe indices: ${it.missingRecipes.joinToString()} " +
                            "does not exist in the ${it.sourceType} file,  for the list of weeks = ${it.weeks.joinToString()}",
                    )
                }
                ?: listOf()

            val err = errorDetail.formattedErrors
            val allErr = err + missingRecipes
            val errorArray = if (allErr.isNotEmpty()) {
                DSL.array(*allErr.toTypedArray())
            } else {
                null
            }

            // We don't want processed to become created so, since here the file
            // has errors we do the following
            // if processed -> processedWithError
            // else createdWithError
            transactionalContext.update(Tables.IMPORTS)
                .set(Tables.IMPORTS.ERRORS, errorArray)
                .set(
                    Tables.IMPORTS.STATUS,
                    DSL.`when`(
                        Tables.IMPORTS.STATUS.`in`(PROCESSED, PROCESSED_WITH_ERROR),
                        PROCESSED_WITH_ERROR,
                    ).otherwise(CREATED_WITH_ERROR),
                )
                .where(Tables.IMPORTS.ID.eq(errorDetail.fileSourceId))
        }

        transactionalContext.withTagName(UPDATED_PROCESSED_ERRORS)
            .batch(fileErrorUpdates)
            .executeAsync().await()
    }

    //  PROCESSED_WITH_ERROR -> PROCESSED, CREATED_WITH_ERROR ->  CREATED
    @Suppress("SpreadOperator")
    override suspend fun updateStatusWithNoError(
        transactionalContext: MetricsDSLContext,
        filesWithNoError: Collection<UUID>
    ) {
        logger.info(
            """
            Marking ${filesWithNoError.size} files as processed
            """.trimIndent(),
        )

        val maxUpdatedAt = transactionalContext.select(DSL.max(Tables.IMPORTS.UPDATED_AT))
            .from(Tables.IMPORTS)
            .where(Tables.IMPORTS.ID.`in`(filesWithNoError))

        transactionalContext.update(Tables.IMPORTS)
            .set(
                Tables.IMPORTS.STATUS,
                DSL.`when`(
                    Tables.IMPORTS.STATUS.`in`(PROCESSED, PROCESSED_WITH_ERROR),
                    PROCESSED,
                ).otherwise(CREATED),
            )
            .set(Tables.IMPORTS.ERRORS, DSL.array(*emptyList<String>().toTypedArray()))
            .where(Tables.IMPORTS.ID.`in`(filesWithNoError))
            .or(
                Tables.IMPORTS.STATUS.eq(FileStatus.CONSUMED)
                    .and(Tables.IMPORTS.UPDATED_AT.le(maxUpdatedAt)),
            ).executeAsync().await()
    }
}
