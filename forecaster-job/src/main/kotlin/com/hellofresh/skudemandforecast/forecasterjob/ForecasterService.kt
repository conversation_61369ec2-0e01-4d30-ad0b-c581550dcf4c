@file:Suppress("TooManyFunctions")

package com.hellofresh.skudemandforecast.forecasterjob

import com.hellofresh.service.Config.Companion.logger
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.featureflags.Context
import com.hellofresh.skuDemandForecast.featureflags.Context.COUNTRY
import com.hellofresh.skuDemandForecast.featureflags.ContextData
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfigClient
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigFeatureFlagClient
import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import com.hellofresh.skuDemandForecast.model.substitution.RecipeDetail
import com.hellofresh.skuDemandForecast.models.Country
import com.hellofresh.skuDemandForecast.models.CountryMarketMapping
import com.hellofresh.skuDemandForecast.models.DemandDetail
import com.hellofresh.skuDemandForecast.recipelib.CountryWeek
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeSnapshotRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeViewRepository
import com.hellofresh.skuDemandForecast.recipelib.UploadedRecipe
import com.hellofresh.skuDemandForecast.recipelib.merge
import com.hellofresh.skudemandforecast.forecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.forecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.forecasterjob.model.ErrorPerLine
import com.hellofresh.skudemandforecast.forecasterjob.model.ErrorsPerFile
import com.hellofresh.skudemandforecast.forecasterjob.model.FractionSkuDemand
import com.hellofresh.skudemandforecast.forecasterjob.model.ProcessedFileError.NO_DEMAND_ASSOCIATED_WITH_RECIPE
import com.hellofresh.skudemandforecast.forecasterjob.model.ProcessedFileError.NO_RECIPE_ASSOCIATED_WITH_DEMAND
import com.hellofresh.skudemandforecast.forecasterjob.model.ProcessedFileError.SKU_NOT_FOUND
import com.hellofresh.skudemandforecast.forecasterjob.model.ProcessedForecastData
import com.hellofresh.skudemandforecast.forecasterjob.model.RecipeDemandKey
import com.hellofresh.skudemandforecast.forecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.forecasterjob.model.calculateDate
import com.hellofresh.skudemandforecast.forecasterjob.model.getCurrentDcWeek
import com.hellofresh.skudemandforecast.forecasterjob.repository.DcConfigRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.DemandRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.FileUploadsRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.FractionSkuDemandRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.SkuRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.SkuSpec
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tags
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.Locale
import java.util.UUID
import kotlin.collections.Map.Entry
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.TestOnly
import org.jetbrains.annotations.VisibleForTesting

const val DELAY_PROCESSED_SECONDS = 270
const val DAYS_IN_A_WEEK = 7
const val DEMAND_FILE = "Demand"
const val RECIPE_FILE = "Recipe"

/** Only reset dirty bit for rows older than this minutes from now **/
const val DIRTY_BIT_RESET_LAG_MINS = 4L

@Suppress("LongParameterList")
class ForecasterService(
    private val readWriteDslContext: MetricsDSLContext,
    private val recipeRepository: RecipeRepository,
    private val recipeViewRepository: RecipeViewRepository,
    private val recipeSnapshotRepository: RecipeSnapshotRepository,
    private val demandRepository: DemandRepository,
    private val skuRepositoryBuilder: suspend (MetricsDSLContext) -> SkuRepository,
    private val dcConfigRepository: DcConfigRepository,
    private val fractionSkuDemandRepository: FractionSkuDemandRepository,
    private val fileUploadsRepository: FileUploadsRepository,
    private val importsRepository: FileUploadsRepository,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val meterRegistry: MeterRegistry,
    private val dynamicConfigClient: DynamicConfigClient,
    private val kittingTimePackagingTypes: Set<String>,
) : Logging {
    suspend fun process() {
        logger.debug("Starting to process demand & recipe records.")
        val dcConfigMap = dcConfigRepository.fetch().associateBy { it.dcCode }
        val processStartTime = LocalDateTime.now(ZoneOffset.UTC)
        val skuRepository = skuRepositoryBuilder(readWriteDslContext)
        val latestRecipeCountryWeek = recipeViewRepository.fetchUpdatedCountryWeek()
        val latestDemandCountryWeek = demandRepository.fetchUpdatedCountryWeek()
        val countryWeeks = latestRecipeCountryWeek.merge(latestDemandCountryWeek)

        val mpsEnabledCountryWeeks = countryWeeks
            .filter { isEnabledMpsImports(it) }.toSet()

        val recipes = recipeViewRepository.fetch(countryWeeks, mpsEnabledCountryWeeks)
        val demand = demandRepository.fetch(countryWeeks)
        val (forecastSkuDemands, errors) = prepareNewForecastDemands(
            recipes,
            demand,
            skuRepository,
            dcConfigMap,
            meterRegistry,
            kittingTimePackagingTypes,
        )
        reportRecipes(recipes)

        readWriteDslContext.withTagName("forecaster-job-save")
            .transaction { config ->
                val transactionalContext = readWriteDslContext.withMeteredConfiguration(config)
                runBlocking {
                    if (forecastSkuDemands.isNotEmpty()) {
                        logger.info("Total number of fraction sku demands created = ${forecastSkuDemands.size}")
                        val dcDates = forecastSkuDemands
                            .map { it.dcCode to it.demandDate }
                            .toSet()
                            .flatMap { (dc, date) -> dcDatesInWeek(dc, date, dcConfigMap) }
                            .toSet()

                        reportSkuUpdates(forecastSkuDemands, dcConfigMap)

                        fractionSkuDemandRepository.save(transactionalContext, forecastSkuDemands, dcDates)
                    }

                    val allFiles = (recipes.mapNotNull { it.sourceId } + demand.mapNotNull { it.sourceId }).toSet()
                    val filesWithoutErrors = allFiles - errors.map { it.fileSourceId }.toSet()
                    val missingRecipeIds = missingRecipes(recipes, demand)

                    val timeToResetDirtyBit = processStartTime.minusMinutes(DIRTY_BIT_RESET_LAG_MINS)
                    recipeRepository.resetDirtyBit(transactionalContext, timeToResetDirtyBit)
                    recipeSnapshotRepository.resetDirtyBit(transactionalContext, timeToResetDirtyBit)
                    demandRepository.resetDirtyBit(transactionalContext, timeToResetDirtyBit)

                    fileUploadsRepository.updateStatusWithError(transactionalContext, errors, missingRecipeIds)
                    fileUploadsRepository.updateStatusWithNoError(transactionalContext, filesWithoutErrors)

                    // TODO: Move the common logic outside
                    if (mpsEnabledCountryWeeks.isNotEmpty()) {
                        importsRepository.updateStatusWithError(transactionalContext, errors, missingRecipeIds)
                        importsRepository.updateStatusWithNoError(transactionalContext, filesWithoutErrors)
                    }
                }
            }
        logger.debug("Completed processing demand & recipe records.")
    }

    private fun reportSkuUpdates(fractionSkuDemands: List<FractionSkuDemand>, dcConfigMap: Map<String, DcConfig>) {
        val numSkusUpdated = fractionSkuDemands
            .filter { dcConfigMap[it.dcCode] != null }
            .groupBy {
                it.country to DcWeek(it.demandDate, dcConfigMap[it.dcCode]!!.productionStart)
            }.mapValues { (_, value) -> value.groupBy { it.skuId }.size }

        numSkusUpdated.forEach { (countryWeek, count) ->
            meterRegistry.gauge(
                "forecaster_job_sku_updates",
                tags(countryWeek.first, countryWeek.second.value),
                count,
            )
        }
    }

    private fun reportRecipes(recipes: List<UploadedRecipe>) {
        val recipesByCountryWeek = recipes.groupBy { it.country to it.week }
        recipesByCountryWeek.forEach { (country, week), recipesForCountryWeek ->
            meterRegistry.gauge(
                "forecaster_job_recipe_updates",
                tags(country, week),
                recipesForCountryWeek.size,
            )
        }
    }

    @VisibleForTesting
    fun isEnabledMpsImports(countryWeek: CountryWeek): Boolean {
        val market = CountryMarketMapping.mapping[Country(countryWeek.country)] ?: run {
            logger.error("Market not found for the country ${countryWeek.country}")
            return false
        }
        return statsigFeatureFlagClient.isEnabledFor(FeatureFlag.MpsImport(COUNTRY, countryWeek.country)) &&
            isStartWeekAllowed(market, countryWeek.week)
    }

    private fun isStartWeekAllowed(market: String, week: String): Boolean {
        val contextData = ContextData(Context.MARKET, market)
        val mpsConfig = dynamicConfigClient.getConfig(MPSConfig(setOf(contextData)))
        return mpsConfig?.parsedWeekStart?.let { weekStart -> weekStart <= week } ?: true
    }

    companion object : Logging
}

@Suppress("LongParameterList")
suspend fun prepareNewForecastDemands(
    recipesParam: List<UploadedRecipe>,
    demandsParam: List<UploadedDemand>,
    skuRepository: SkuRepository,
    dcConfigMap: Map<String, DcConfig>,
    meterRegistry: MeterRegistry,
    kittingTimePackagingTypes: Set<String>,
): Pair<List<FractionSkuDemand>, List<ErrorsPerFile>> {
    val recipeMap = recipesParam.groupBy {
        if (it.dcCode != null && it.dcCode.equals("ALL")) {
            RecipeDemandKey(it.recipeIndex, it.week, it.country, it.locale, it.brand, null)
        } else {
            RecipeDemandKey(it.recipeIndex, it.week, it.country, it.locale, it.brand, it.dcCode)
        }
    }
    val demandMap = demandsParam.groupBy {
        if (it.country == "AU" || it.country == "NZ") {
            RecipeDemandKey(it.recipeIndex, it.week, it.country, it.locale, it.brand, it.dcCode)
        } else {
            RecipeDemandKey(it.recipeIndex, it.week, it.country, it.locale, it.brand, null)
        }
    }

    val (matchedEntries, unmatched) = outerJoin(recipeMap, demandMap)
        .entries
        .partition { (_, pair) -> pair.first != null && pair.second != null }

    val processed = matchedEntries
        .flatMap { (key, pair) ->
            val (recipes, demands) = pair
            checkNotNull(recipes)
            checkNotNull(demands)

            match(key, recipes, demands, skuRepository, dcConfigMap, kittingTimePackagingTypes)
        }

    val unmatchedErrors = unmatched.flatMap { (_, pair) ->

        val (recipes, demands) = pair
        recipes?.map {
            logger.warn("Unable to match recipe: $it")
            ErrorPerLine(it.sourceId, it.recipeIndex, it.week, NO_DEMAND_ASSOCIATED_WITH_RECIPE)
        } ?: demands!!.map {
            logger.warn("Unable to match demand: $it")
            ErrorPerLine(it.sourceId, it.recipeIndex, it.week, NO_RECIPE_ASSOCIATED_WITH_DEMAND)
        }
    }

    logMismatches(matchedEntries, unmatched, meterRegistry)

    return processed
        .aggregateBrandsAndProductionPlan() to
        ErrorsPerFile.createList(
            processed
                .filter { it.errors.error != null && it.errors.fileSourceId != null }
                .map { it.errors } + unmatchedErrors,
        )
}

private fun logMismatches(
    matchedEntries: List<Entry<RecipeDemandKey, Pair<List<UploadedRecipe>?, List<UploadedDemand>?>>>,
    unmatched: List<Entry<RecipeDemandKey, Pair<List<UploadedRecipe>?, List<UploadedDemand>?>>>,
    meterRegistry: MeterRegistry
) {
    val mismatchedDetails = ForecastMismatchDetailService.findMismatchDetails(
        matchedEntries.map { it.key },
        unmatched.map { it.key },
    )
    if (mismatchedDetails.isNotEmpty()) {
        mismatchedDetails.forEach { mismatchedDetail ->
            meterRegistry.gauge(
                "recipe_demand_match",
                tags(mismatchedDetail.key.country, mismatchedDetail.key.week),
                mismatchedDetail.matchedSize,
            )
            meterRegistry.gauge(
                "recipe_demand_mismatch",
                tags(mismatchedDetail.key.country, mismatchedDetail.key.week),
                mismatchedDetail.mismatchedRecipeDemandKeys.size,
            )

            logger.warn(
                "There are recipe-demand mismatches for the country and week: ${mismatchedDetail.key}, " +
                    "total matched entries: ${mismatchedDetail.matchedSize}, total unmatched entries: ${mismatchedDetail.mismatchedRecipeDemandKeys.size}," +
                    " mismatched forecast key - ${mismatchedDetail.mismatchedRecipeDemandKeys}",
            )
        }
    }
}

// Aggregates demand for the primary key between different brands
private fun List<ProcessedForecastData>.aggregateBrandsAndProductionPlan(): List<FractionSkuDemand> =
    this.filter { it.fractionSkuDemand != null }
        .groupBy { it.fractionSkuDemand!!.toPrimaryKey() }
        .map { (key, processedDemands) ->
            // Group processedDemands by recipeDetails key
            val mergedRecipeDetails = processedDemands
                .flatMap { it.fractionSkuDemand?.recipeDetails.orEmpty() }
                .groupBy { detail ->
                    RecipeDetailKey(
                        brand = detail.brand,
                        peopleCount = detail.peopleCount,
                        picks = detail.picks,
                        demandType = detail.demandType
                    )
                }
                .map { (recipeDetailKey, recipeDetails) ->
                    RecipeDetail(
                        brand = recipeDetailKey.brand,
                        peopleCount = recipeDetailKey.peopleCount,
                        picks = recipeDetailKey.picks,
                        qty = recipeDetails.sumOf { it.qty },
                        demandType = recipeDetailKey.demandType
                    )
                }

            val mergedSourceIds = processedDemands
                .flatMap { it.fractionSkuDemand?.sourceIds.orEmpty() }
                .toSet()

            FractionSkuDemand(
                skuId = key.skuId,
                dcCode = key.dcCode,
                demandDate = key.demandDate,
                country = key.country,
                locale = key.locale,
                productFamily = key.productFamily,
                recipeIndex = key.recipeIndex,
                recipeDetails = mergedRecipeDetails,
                sourceIds = mergedSourceIds,
            )
        }

@Suppress("LongMethod", "LongParameterList")
private suspend fun match(
    key: RecipeDemandKey,
    recipes: List<UploadedRecipe>,
    demands: List<UploadedDemand>,
    skuRepository: SkuRepository,
    dcConfigMap: Map<String, DcConfig>,
    kittingTimePackagingTypes: Set<String>,
): List<ProcessedForecastData> {
    val skuToPickList = recipes.flatMap { recipe ->
        recipe.skus.map {
            it.skuCode to RecipePickDetails(it.picks, recipe.sourceId, recipe.family)
        }
    }

    return demands.groupBy { it.day to it.dcCode }
        .flatMap { (pair, demandList) ->
            val (day, dcCode) = pair

            val demandSourceIds = demandList.mapNotNull { it.sourceId }.toSet()

            skuToPickList.flatMap skuPicKList@{ (skuCode, picks) ->
                val fetchedSkuSpec = skuRepository.fetchSkuSpec(dcCode, skuCode)
                val demandsByDate = demandList.flatMap {
                    it.demandDetails?.demandDetails?.map { detail ->
                        DemandDetailWithPeopleCount(
                            detail.qty,
                            detail.type,
                            getDemandDate(
                                fetchedSkuSpec?.packagingType,
                                detail,
                                kittingTimePackagingTypes,
                            ) ?: convertDayToDate(
                                day,
                                dcCode,
                                key.week,
                                dcConfigMap,
                            )!!,
                            it.peopleCount,
                        )
                    } ?: listOf(
                        DemandDetailWithPeopleCount(
                            it.mealsToDeliver,
                            null,
                            convertDayToDate(day, dcCode, key.week, dcConfigMap)!!,
                            it.peopleCount,
                        ),
                    )
                }.groupBy { it.date }

                val (pickList, recipeFileId, family) = picks

                if (fetchedSkuSpec?.skuId == null) {
                    logger.error(
                        "Sku code from uploaded file does not exist, dcCode = $dcCode, recipeFileId = $recipeFileId, skucode = $skuCode",
                    )
                    return@skuPicKList listOf(
                        ProcessedForecastData(
                            key = key,
                            errors = ErrorPerLine(recipeFileId, error = SKU_NOT_FOUND, skuCode = skuCode),
                        ),
                    )
                }
                demandsByDate.map { (date, demandDetailWithPeopleCounts) ->
                    val peopleCountToDemandTypeQty: Map<Int, Map<String?, Int>> =
                        demandDetailWithPeopleCounts.groupBy { it.peopleCount }
                            .map { (peopleCount, demands) -> peopleCount to getDemandTypeToMealsToDeliver(demands) }
                            .toMap()

                    ProcessedForecastData(
                        key = key,
                        fractionSkuDemand = FractionSkuDemand(
                            skuId = fetchedSkuSpec.skuId,
                            dcCode = dcCode,
                            demandDate = date,
                            country = key.country,
                            locale = key.locale ?: "",
                            productFamily = family,
                            recipeIndex = key.recipeIndex,
                            recipeDetails = getRecipeDetails(
                                pickList,
                                peopleCountToDemandTypeQty,
                                key,
                                fetchedSkuSpec,
                            ),
                            sourceIds = setOfNotNull(recipeFileId) + demandSourceIds,
                        ),
                        errors = ErrorPerLine(fileSourceId = recipeFileId, skuCode = skuCode),
                    )
                }
            }
        }
}

private fun getRecipeDetails(
    pickList: Map<Int, Int>,
    peopleCountToDemandTypeQty: Map<Int, Map<String?, Int>>,
    key: RecipeDemandKey,
    skuSpec: SkuSpec,
) = pickList.flatMap { (peopleCount, qtyToPick) ->
    val demandTypeToQty = peopleCountToDemandTypeQty[peopleCount] ?: emptyMap()
    if (demandTypeToQty.isEmpty()) {
        listOf(
            RecipeDetail(
                brand = key.brand,
                peopleCount = peopleCount,
                picks = qtyToPick,
                qty = 0,
                demandType = null,
            ),
        ).filter { it.picks != 0 }
    } else {
        demandTypeToQty.map { (demandType, mealsToDeliver) ->
            RecipeDetail(
                brand = key.brand,
                peopleCount = peopleCount,
                picks = qtyToPick,
                qty = qtyToPick * mealsToDeliver.toLong(),
                demandType = getDemandType(demandType, skuSpec),
            )
        }.filter { it.picks != 0 }
            .groupBy { Triple(it.brand, it.peopleCount, it.picks) }
            .flatMap { (key, details) ->
                details.groupBy { it.demandType }
                    .map { (demandType, groupedDetails) ->
                        RecipeDetail(
                            brand = key.first,
                            peopleCount = key.second,
                            picks = key.third,
                            qty = groupedDetails.sumOf { it.qty },
                            demandType = demandType,
                        )
                    }
            }
    }
}

private fun getDemandType(demandType: String?, skuSpec: SkuSpec): DemandType? {
    var type = demandType?.let { DemandType.valueOf(it.uppercase(Locale.getDefault())) }
    if (type == DemandType.FUMIGATED && (skuSpec.fumigationAllowed == null || skuSpec.fumigationAllowed == false)) {
        type = DemandType.REGULAR
        logger.warn("Fumigated demand type is not allowed for skuId: ${skuSpec.skuId}")
    }
    return type
}

private fun getDemandTypeToMealsToDeliver(demands: List<DemandDetailWithPeopleCount>): Map<String?, Int> =
    demands.groupBy { it.type }
        .mapValues { (_, demands) -> demands.sumOf { it.qty } }

@VisibleForTesting
fun dcDatesInWeek(dcCode: String, date: LocalDate, dcConfigMap: Map<String, DcConfig>): List<DcDate> {
    val productionStart: DayOfWeek = dcConfigMap[dcCode.uppercase()]?.productionStart!!
    val week = DcWeek(date, productionStart)
    val firstDayInWeek = week.getStartDateInDcWeek(productionStart, dcConfigMap[dcCode.uppercase()]?.zoneId!!)

    return (0 until DAYS_IN_A_WEEK).map {
        DcDate(dcCode, firstDayInWeek.plusDays(it.toLong()))
    }
}

private fun getDemandDate(
    packagingType: String?,
    demandDetail: DemandDetail?,
    kittingTimePackagingTypes: Set<String>,
): LocalDate? =
    demandDetail?.let {
        if (kittingTimePackagingTypes.contains(packagingType)) {
            it.kittingTime?.toLocalDate()
        } else {
            it.assemblyTime?.toLocalDate()
        }
    }

private fun convertDayToDate(
    day: DayOfWeek,
    dcCode: String,
    week: String,
    dcConfigMap: Map<String, DcConfig>
): LocalDate? {
    val productionStart: DayOfWeek = dcConfigMap[dcCode.uppercase()]?.productionStart!!
    val currentWeekProduction: Boolean = getCurrentDcWeek(productionStart).equals(week)
    val localDate = calculateDate(
        week,
        productionStart,
        day,
        currentWeekProduction,
    )
    if (localDate == null) {
        logger.error("Couldn't calculate date for [$week], [$day], [$dcCode]")
    }
    return localDate
}

private fun <K, V1, V2> outerJoin(m1: Map<K, V1>, m2: Map<K, V2>): Map<K, Pair<V1?, V2?>> =
    buildMap {
        m1.forEach { (k, v1) ->
            this[k] = v1 to m2[k]
        }
        (m2.keys - m1.keys).forEach { k ->
            this[k] = null to m2[k]
        }
    }

private fun tags(
    country: String,
    week: String
) = Tags.of("country", country)
    .and("week", week)

@Suppress("UnnecessaryParentheses")
@TestOnly
fun missingRecipes(
    recipesParam: List<UploadedRecipe>,
    demandsParam: List<UploadedDemand>,
): Map<UUID, MissingRecipeDetails> {
    val recipeIndicesInRecipe = recipesParam.groupBy { it.recipeIndex }
        .mapValues { (_, list) -> list.filter { it.sourceId != null }.map { it.sourceId to it.week }.toSet() }
    val recipeIndicesInDemand = demandsParam.groupBy { it.recipeIndex }
        .mapValues { (_, list) -> list.filter { it.sourceId != null }.map { it.sourceId to it.week }.toSet() }

    val missingRecipeIdsInDemand = (recipeIndicesInRecipe - recipeIndicesInDemand.keys)
    val missingRecipeIdsInRecipe = (recipeIndicesInDemand - recipeIndicesInRecipe.keys)

    return buildMap {
        prepareMissingRecipeIds(missingRecipeIdsInDemand, DEMAND_FILE)
        prepareMissingRecipeIds(missingRecipeIdsInRecipe, RECIPE_FILE)
    }
}

@Suppress("NestedBlockDepth")
private fun MutableMap<UUID, MissingRecipeDetails>.prepareMissingRecipeIds(
    missingRecipeIds: Map<Int, Set<Pair<UUID?, String>>>,
    sourceType: String
) {
    missingRecipeIds.forEach { (recipeIndex, sourceList) ->
        sourceList.forEach { (fileId, week) ->
            fileId?.let {
                val existingDetails = get(fileId)
                if (existingDetails != null) {
                    val updatedMissingRecipes = existingDetails.missingRecipes + recipeIndex
                    val updatedWeeks = existingDetails.weeks + week
                    put(
                        fileId,
                        existingDetails.copy(
                            missingRecipes = updatedMissingRecipes,
                            weeks = updatedWeeks,
                        ),
                    )
                } else {
                    put(
                        fileId,
                        MissingRecipeDetails(
                            missingRecipes = setOf(recipeIndex),
                            sourceType = sourceType,
                            weeks = setOf(week),
                        ),
                    )
                }
            }
        }
    }
}

data class RecipePickDetails(
    val picks: Map<Int, Int>,
    val sourceId: UUID?,
    val family: String,
)

data class MissingRecipeDetails(
    val missingRecipes: Set<Int>,
    val sourceType: String,
    val weeks: Set<String>,
)

data class DemandDetailWithPeopleCount(
    val qty: Int,
    val type: String?,
    val date: LocalDate,
    val peopleCount: Int,
)

data class RecipeDetailKey(
    val brand: String,
    val peopleCount: Int,
    val picks: Int,
    val demandType: DemandType? = null,
)
