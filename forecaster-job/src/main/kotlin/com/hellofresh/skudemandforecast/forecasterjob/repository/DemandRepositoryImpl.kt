package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.DemandDetails
import com.hellofresh.skuDemandForecast.recipelib.CountryWeek
import com.hellofresh.skuDemandForecast.recipelib.buildCountryWeekCondition
import com.hellofresh.skudemandforecast.forecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.Demand.DEMAND
import java.time.DayOfWeek
import java.time.LocalDateTime
import kotlinx.coroutines.future.await

class DemandRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : DemandRepository {
    private val fetchModifiedDemands = "fetch-modified-demands"
    private val fetchDemands = "fetch-demands"
    private val resetDemandDirtyBit = "reset-demand-dirty-bit"

    override suspend fun fetch(countryWeek: List<CountryWeek>): List<UploadedDemand> =
        if (countryWeek.isNotEmpty()) {
            dslContext.withTagName(fetchDemands)
                .selectFrom(DEMAND)
                .where(
                    buildCountryWeekCondition(
                        DEMAND.COUNTRY,
                        DEMAND.WEEK,
                        countryWeek,
                    ),
                )
                .fetchAsync()
                .await()
                .map {
                    with(it) {
                        UploadedDemand(
                            recipeIndex = recipeIndex,
                            week = week,
                            family = productFamily,
                            locale = locale,
                            country = country,
                            day = DayOfWeek.valueOf(day.uppercase()),
                            dcCode = dcCode,
                            peopleCount = peopleCount,
                            mealsToDeliver = mealsToDeliver,
                            sourceId = sourceId,
                            brand = brand,
                            demandDetails = demandDetails?.let { demandDetails ->
                                objectMapper.readValue<DemandDetails>(demandDetails.data())
                            },
                        )
                    }
                }
        } else {
            emptyList()
        }

    override suspend fun resetDirtyBit(
        transactionalContext: MetricsDSLContext,
        timestamp: LocalDateTime,
    ) {
        transactionalContext.withTagName(resetDemandDirtyBit)
            .update(DEMAND)
            .set(DEMAND.DIRTY_BIT, false)
            .where(
                DEMAND.DIRTY_BIT.isTrue
                    .and(DEMAND.UPDATED_AT.lt(timestamp)),
            )
            .execute()
    }

    override suspend fun fetchUpdatedCountryWeek(): List<CountryWeek> =
        dslContext.withTagName(fetchModifiedDemands)
            .select(
                DEMAND.COUNTRY,
                DEMAND.WEEK,
            ).distinctOn(DEMAND.COUNTRY, DEMAND.WEEK)
            .from(DEMAND)
            .where(DEMAND.DIRTY_BIT.isTrue)
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    CountryWeek(
                        country = record.get(DEMAND.COUNTRY),
                        week = record.get(DEMAND.WEEK),
                    )
                }
            }.await()

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
