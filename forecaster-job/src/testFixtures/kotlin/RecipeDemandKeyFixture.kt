@file:Suppress("MagicNumber")

import com.hellofresh.skudemandforecast.forecasterjob.model.RecipeDemandKey
import kotlin.random.Random

fun RecipeDemandKey.Companion.default() = RecipeDemandKey(
    recipeIndex = 1,
    week = "2023-W46",
    country = "DE",
    locale = "DE",
    brand = "HELLOFRESH",
    dcCode = null
)

fun RecipeDemandKey.Companion.random() = with(Random(System.nanoTime())) {
    RecipeDemandKey(
        recipeIndex = nextInt(1000),
        week = "2023-W${nextInt(10, 52)}",
        locale = randomString(2),
        country = randomString(2),
        brand = randomString(6).uppercase(),
        dcCode = null
    )
}
