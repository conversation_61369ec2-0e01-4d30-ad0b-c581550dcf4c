import com.hellofresh.skuDemandForecast.model.substitution.RecipeDetail
import com.hellofresh.skudemandforecast.forecasterjob.model.FractionSkuDemand
import java.time.LocalDate
import java.util.UUID

@Suppress("LongParameterList", "MagicNumber")
fun FractionSkuDemand.Companion.newFractionSkuDemand(
    skuIdParam: UUID = UUID.randomUUID(),
    demandDateParam: LocalDate = LocalDate.now(),
    dcCodeParam: String = "VE",
    countryParam: String = "DE",
    localeParam: String = "DE",
    recipeIndexParam: Int = 1,
    productFamilyParam: String = "test-product-family",
    qtyParam: Long = 10,
    sourceId: UUID = UUID.randomUUID()
) = FractionSkuDemand(
    skuId = skuIdParam,
    dcCode = dcCodeParam,
    demandDate = demandDateParam,
    country = countryParam,
    locale = localeParam,
    productFamily = productFamilyParam,
    recipeIndex = recipeIndexParam,
    recipeDetails = listOf(
        RecipeDetail(
            brand = "HF",
            peopleCount = 1,
            picks = 1,
            qty = qtyParam,
        ),
        RecipeDetail(brand = "HF", peopleCount = 2, picks = 2, qty = qtyParam),
        RecipeDetail(brand = "HF", peopleCount = 3, picks = 2, qty = qtyParam),
        RecipeDetail(brand = "HF", peopleCount = 4, picks = 2, qty = qtyParam),
    ),
    sourceIds = setOf(sourceId)
)
