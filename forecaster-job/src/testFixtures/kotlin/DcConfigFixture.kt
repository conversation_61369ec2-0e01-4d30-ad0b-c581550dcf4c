@file:Suppress("MagicNumber")

import com.hellofresh.skudemandforecast.forecasterjob.model.DcConfig
import java.time.DayOfWeek.FRIDAY
import java.time.ZoneId
import kotlin.random.Random

fun DcConfig.Companion.default() = DcConfig(
    dcCode = "VE",
    market = "DACH",
    productionStart = FRIDAY,
    zoneId = ZoneId.systemDefault(),
    globalDc = "GL",
    enabled = true,
)

fun DcConfig.Companion.random() = with(Random(System.nanoTime())) {
    DcConfig(
        dcCode = randomString(2),
        market = "DACH",
        productionStart = FRIDAY,
        zoneId = ZoneId.systemDefault(),
        globalDc = randomString(2),
        enabled = true,
    )
}
