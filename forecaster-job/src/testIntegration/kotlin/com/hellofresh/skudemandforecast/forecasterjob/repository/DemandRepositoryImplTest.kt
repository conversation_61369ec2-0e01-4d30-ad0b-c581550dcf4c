package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skuDemandForecast.recipelib.CountryWeek
import com.hellofresh.skudemandforecast.forecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.Demand.DEMAND
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import random
import toDemandRecord

internal class DemandRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should fetch dirty demands with country and week (records with non-dirty available)`() {
        val fileUploadsRecord = fileUploadsRecord()
        val demandRecordOne = demandRecord(sourceIdParam = fileUploadsRecord.id)
        val demandRecordTwo = demandRecord(false, sourceIdParam = fileUploadsRecord.id)
        runBlocking {
            dsl.batchInsert(fileUploadsRecord, demandRecordOne, demandRecordTwo).execute()
            val result = demandRepository.fetchUpdatedCountryWeek()
            assertEquals(1, result.size)
            assertEquals(demandRecordOne.country, result.first().country)
            assertEquals(demandRecordOne.week, result.first().week)
        }
    }

    @Test
    fun `should fetch dirty demands with distinct country and week`() {
        val fileUploadsRecord = fileUploadsRecord()
        val demandRecordOne = demandRecord(sourceIdParam = fileUploadsRecord.id)
        val demandRecordTwo = demandRecord(sourceIdParam = fileUploadsRecord.id)
        val demandRecordThree = demandRecord(sourceIdParam = fileUploadsRecord.id)
        val demandRecordFour = demandRecord(sourceIdParam = fileUploadsRecord.id)
        runBlocking {
            dsl.batchInsert(
                fileUploadsRecord,
                demandRecordOne,
                demandRecordTwo,
                demandRecordThree,
                demandRecordFour,
            ).execute()
            val result = demandRepository.fetchUpdatedCountryWeek()
            assertEquals(1, result.size)
            assertEquals(demandRecordOne.country, result.first().country)
            assertEquals(demandRecordOne.week, result.first().week)
        }
    }

    @Test fun `should fetch demand with given country and week mapping`() {
        val countryweeks = buildSet { repeat(100) { add(CountryWeek.random()) } }
        val matchingCountryWeeks = buildSet { repeat(5) { add(countryweeks.random()) } }
        val nonMatchingCountryWeeks = countryweeks - matchingCountryWeeks

        val fileUploadsRecord = fileUploadsRecord()
        // first 5 recipes are a match
        val demands = buildList {
            repeat(10) { index ->
                val r = UploadedDemand.random().copy(sourceId = fileUploadsRecord.id)
                val countryWeek = if (index < 5) matchingCountryWeeks.random() else nonMatchingCountryWeeks.random()
                add(countryWeek.run { r.copy(country = this.country, week = this.week) })
            }
        }

        // insert the recipes
        dsl.executeInsert(fileUploadsRecord)
        dsl.batchInsert(demands.map { it.toDemandRecord() }).execute()
        val expected = demands.take(5).toSet()
        val actual = runBlocking { demandRepository.fetch(matchingCountryWeeks.toList()).toSet() }
        assertEquals(expected, actual)
    }

    @Test
    fun `should update the dirty bit for the given demand`() {
        val fileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(sourceIdParam = fileUploadsRecord.id)
        runBlocking {
            dsl.batchInsert(fileUploadsRecord, demandRecord).execute()
            demandRepository.resetDirtyBit(dsl, LocalDateTime.now())
            val updatedDemand = dsl.selectFrom(DEMAND).fetch().first()
            Assertions.assertFalse(updatedDemand.dirtyBit)
        }
    }

    @Test
    fun `should not update the dirty bit for the latest demand`() {
        val fileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(
            sourceIdParam = fileUploadsRecord.id,
            updatedAtParam = LocalDateTime.now().plusHours(
                1,
            ),
        )
        runBlocking {
            dsl.batchInsert(fileUploadsRecord, demandRecord).execute()
            demandRepository.resetDirtyBit(dsl, LocalDateTime.now())
            val updatedDemand = dsl.selectFrom(DEMAND).fetch().first()
            Assertions.assertTrue(updatedDemand.dirtyBit)
        }
    }
}
