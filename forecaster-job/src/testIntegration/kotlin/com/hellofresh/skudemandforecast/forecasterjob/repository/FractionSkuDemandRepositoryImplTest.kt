package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skudemandforecast.forecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.forecasterjob.model.FractionSkuDemand
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.FractionSkuDemand.FRACTION_SKU_DEMAND
import java.time.LocalDate
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import newFractionSkuDemand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

internal class FractionSkuDemandRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should be able to save fraction sku demand`() {
        runBlocking {
            val fractionSkuDemand = FractionSkuDemand.Companion.newFractionSkuDemand()
            fractionSkuDemandRepository.save(dsl, listOf(fractionSkuDemand), listOf())
            val result = dsl.selectFrom(FRACTION_SKU_DEMAND).fetch().first()
            assertEquals(fractionSkuDemand.recipeDetails.sumOf { it.qty }, result.qty)
            assertEquals(fractionSkuDemand.sourceIds, result.sourceIds.toSet())
        }
    }

    @Test
    fun `should be able to update existing fraction sku demand`() {
        runBlocking {
            val fractionSkuDemand = FractionSkuDemand.Companion.newFractionSkuDemand()
            fractionSkuDemandRepository.save(dsl, listOf(fractionSkuDemand), listOf())

            val newFractionSkuDemand = fractionSkuDemand.copy(
                recipeDetails = fractionSkuDemand.recipeDetails.map { it.copy(qty = 100) },
                sourceIds = setOf(UUID.randomUUID(), UUID.randomUUID()),
            )

            fractionSkuDemandRepository.save(dsl, listOf(newFractionSkuDemand), listOf())
            val result = dsl.selectFrom(FRACTION_SKU_DEMAND).fetch().first()
            assertEquals(newFractionSkuDemand.recipeDetails.sumOf { it.qty }, result.qty)
            assertEquals(newFractionSkuDemand.sourceIds, result.sourceIds.toSet())
        }
    }

    @Test
    fun `should be able to save multiple fraction sku demand`() {
        runBlocking {
            val fractionSkuDemandOne = FractionSkuDemand.Companion.newFractionSkuDemand(
                qtyParam = 100,
            )
            val fractionSkuDemandTwo = FractionSkuDemand.Companion.newFractionSkuDemand(
                qtyParam = 200,
            )
            val fractionSkuDemandThree = FractionSkuDemand.Companion.newFractionSkuDemand(
                qtyParam = 400,
            )
            fractionSkuDemandRepository.save(
                dsl,
                listOf(
                    fractionSkuDemandOne,
                    fractionSkuDemandTwo,
                    fractionSkuDemandThree,
                ),
                listOf(),
            )
            val createdRecords = dsl.selectFrom(FRACTION_SKU_DEMAND)
                .fetch()
            assertEquals(3, createdRecords.size)
            assertEquals(fractionSkuDemandOne.skuId, createdRecords.first { it.qty == 400L }.skuId)
            assertEquals(fractionSkuDemandTwo.skuId, createdRecords.first { it.qty == 800L }.skuId)
            assertEquals(fractionSkuDemandThree.skuId, createdRecords.first { it.qty == 1600L }.skuId)
        }
    }

    @Test
    fun `should make demand 0 for the dates in a dc, but not other dcs`() {
        runBlocking {
            // Create demand for dcs in 2 different date but same country.
            val date1 = LocalDate.now()
            val date2 = LocalDate.now().minusDays(1)
            val date3 = LocalDate.now().plusDays(1)

            val country = "SE"
            val anyDC = "ANY-DC"
            val dc = "MO"
            val random = Random(System.nanoTime())

            // demand for a random dc in the country and for MO dc
            fractionSkuDemandRepository.save(
                dsl,
                listOf(
                    FractionSkuDemand.Companion.newFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date1,
                        dcCodeParam = anyDC,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.newFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date1,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.newFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date2,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.newFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date3,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                ),

                listOf(),
            )

            val fractionSkuDemandMO = FractionSkuDemand.Companion.newFractionSkuDemand(
                countryParam = country,
                qtyParam = 100,
                dcCodeParam = dc,
                demandDateParam = date1,
            )

            fractionSkuDemandRepository.save(
                dsl,
                listOf(fractionSkuDemandMO),
                listOf(date1, date2, date3).map { DcDate(dc, it) },
            )

            val createdRecords = dsl.selectFrom(FRACTION_SKU_DEMAND)
                .fetch()

            assertEquals(
                400L,
                createdRecords.first {
                    it.dcCode == dc && it.demandDate == date1 && it.recipeIndex == fractionSkuDemandMO.recipeIndex
                }.qty,
            )

            with(createdRecords.first { it.dcCode == dc && it.demandDate != date1 }) {
                assertEquals(0L, qty)
                assertNull(recipeDetails)
                assertNull(sourceIds)
            }

            // Demand for other DCs are not 0 in the same country
            createdRecords
                .filter { it.dcCode != dc }
                .forEach {
                    assertTrue { it.qty != 0L }
                    assertNotNull(it.sourceIds)
                }
        }
    }
}
