package com.hellofresh.skudemandforecast.forecasterjob.repository

import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.SkuSpecificationRecord
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import randomString

class SkuSpecificationRepositoryTest : FunctionalTest() {

    @Test fun `returns skuId or null for the requested dc x sku code`() {
        val dcCodes = buildSet { repeat(5) { add(randomString(2)) } }
        val markets = buildSet { repeat(2) { add(randomString(4)) } }
        val skuCodes = buildSet { repeat(10) { add(randomString(8)) } }
        val matching = skuCodes.take(3).toSet()

        val dcRecords = dcCodes.map {
            DcConfigRecord().apply {
                dcCode = it
                market = markets.random()
                productionStart = "MONDAY"
                zoneId = "UTC"
                enabled = true
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
            }
        }

        val dcSkus = matching.map { code ->
            val dc = dcRecords.random()
            dc.dcCode to SkuSpecificationRecord().apply {
                id = UUID.randomUUID()
                parentId = null
                this.code = code
                market = dc.market
                packaging = ""
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
                fumigationAllowed = true
            }
        }

        dsl.batchInsert(dcRecords).execute()
        dsl.batchInsert(dcSkus.map { (_, sku) -> sku }).execute()

        val matchingArgument = dcSkus.map { (dc, sku) -> dc to sku.code }
        val nonMatchingArgument = skuCodes.map { randomString(3) to it }
        val output = runBlocking {
            SkuRepositoryBuilder(dsl).fetchSkuSpec(matchingArgument + nonMatchingArgument)
        }
        val actualMatching = output.filter { it.value != null }.map { it.key }.toSet()
        val actualNonMatching = output.filter { it.value == null }.map { it.key }.toSet()

        assertEquals(matchingArgument.toSet(), actualMatching)
        assertEquals(nonMatchingArgument.toSet(), actualNonMatching)
    }
}
