@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skudemandforecast.forecasterjob.repository

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.models.DemandDetails
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepositoryImpl
import com.hellofresh.skuDemandForecast.recipelib.RecipeSnapshotRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeViewRepository
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileSource.INVENTORY
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileSource.MPS
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileType.DEMAND
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.enums.FileType.RECIPE
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.ImportsRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.RecipeRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.RecipeSnapshotRecord
import com.hellofresh.skudemandforecast.forecasterjob.schema.public_.tables.records.SkuSpecificationRecord
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.random.Random.Default.nextInt
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    private val jsonObjectMapper = jacksonObjectMapper().findAndRegisterModules()

    fun fileUploadsRecord() = FileUploadsRecord().apply {
        id = UUID.randomUUID()
        dcs = setOf(UUID.randomUUID().toString()).toTypedArray()
        weeks = setOf(UUID.randomUUID().toString()).toTypedArray()
        fileName = UUID.randomUUID().toString()
        content = UUID.randomUUID().toString().toByteArray()
        errors = listOf(UUID.randomUUID().toString()).toTypedArray()
        uploadedFileSource = INVENTORY
        uploadedFileType = DEMAND
        uploadedFileStatus = CREATED
        authorName = "author"
        authorEmail = "email"
        createdAt = OffsetDateTime.now(UTC)
        market = "DACH"
    }

    fun importsRecord() = ImportsRecord().apply {
        id = UUID.randomUUID()
        dcs = setOf(UUID.randomUUID().toString()).toTypedArray()
        weeks = setOf(UUID.randomUUID().toString()).toTypedArray()
        name = UUID.randomUUID().toString()
        errors = listOf(UUID.randomUUID().toString()).toTypedArray()
        source = MPS
        type = RECIPE
        status = CREATED
        createdAt = OffsetDateTime.now(UTC)
        market = "market"
    }

    @Suppress("LongParameterList")
    fun demandRecord(
        dirtyBitParam: Boolean = true,
        sourceIdParam: UUID,
        updatedAtParam: LocalDateTime = LocalDateTime.now(UTC).minusMinutes(5),
        mealNumberParam: Int = 1,
        productFamilyParam: String = "test-product-family" + nextInt(),
        dayParam: String = "MONDAY",
        peopleCountParam: Int = 1,
        mealsToDeliverParam: Int = 1,
        countryParam: String = "DE",
        brandParam: String = "UNKNOWN",
        weekParam: String = "2023-W47",
        demandDetailsParam: DemandDetails? = null,
        dcCodeParam: String = "VE",
        localeParam: String = "DE",
    ) = DemandRecord().apply {
        recipeIndex = mealNumberParam
        week = weekParam
        productFamily = productFamilyParam
        locale = localeParam
        country = countryParam
        day = dayParam
        dcCode = dcCodeParam
        peopleCount = peopleCountParam
        mealsToDeliver = mealsToDeliverParam
        dirtyBit = dirtyBitParam
        sourceId = sourceIdParam
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = updatedAtParam
        brand = brandParam
        demandDetails = JSONB.valueOf(jsonObjectMapper.writeValueAsString(demandDetailsParam))
    }

    @Suppress("MagicNumber", "LongParameterList")
    fun recipeRecord(
        dirtyBitParam: Boolean = true,
        sourceIdParam: UUID,
        updatedAtParam: LocalDateTime = LocalDateTime.now(UTC).minusMinutes(5),
        indexParam: Int = 1,
        familyParam: String = "test-product-family" + nextInt(),
        skuCode1Param: String = "SPI-1234",
        skuCode2Param: String? = null,
        weekParam: String = "2023-W47",
        countryParam: String = "DE",
        brandParam: String = "HF",
        localeParam: String? = "DE",
    ) = RecipeRecord().apply {
        val sku2 = skuCode2Param?.let {
            listOf(
                SkuPicks(
                    skuCode2Param,
                    listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                ),
            )
        } ?: emptyList()

        recipeIndex = indexParam
        week = weekParam
        productFamily = familyParam
        locale = localeParam
        country = countryParam
        value = JSONB.jsonb(
            objectMapper.writeValueAsString(
                RecipeValue(
                    listOf(
                        SkuPicks(
                            skuCode1Param,
                            listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                        ),
                    ) + sku2,
                ),
            ),
        )
        dirtyBit = dirtyBitParam
        sourceId = sourceIdParam
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = updatedAtParam
        brand = brandParam
    }

    @Suppress("MagicNumber", "LongParameterList")
    fun recipeSnapshotRecord(
        dirtyBitParam: Boolean = true,
        sourceIdParam: UUID? = null,
        updatedAtParam: LocalDateTime = LocalDateTime.now(UTC).minusMinutes(5),
        indexParam: Int = 1,
        familyParam: String = "test-product-family" + nextInt(),
        skuCode1Param: String = "SPI-1234",
        skuCode2Param: String? = null,
        weekParam: String = "2023-W47",
        countryParam: String = "DE",
        brandParam: String = "Hello Fresh",
        localeParam: String? = "DE",
        dcCodeParam: String? = "SY"
    ) = RecipeSnapshotRecord().apply {
        val sku2 = skuCode2Param?.let {
            listOf(
                SkuPicks(
                    skuCode2Param,
                    listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                ),
            )
        } ?: emptyList()

        recipeIndex = indexParam
        week = weekParam
        productFamily = familyParam
        locale = localeParam
        country = countryParam
        value = JSONB.jsonb(
            objectMapper.writeValueAsString(
                RecipeValue(
                    listOf(
                        SkuPicks(
                            skuCode1Param,
                            listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                        ),
                    ) + sku2,
                ),
            ),
        )
        dirtyBit = dirtyBitParam
        sourceId = sourceIdParam
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = updatedAtParam
        brand = brandParam
        dcCode = dcCodeParam
    }

    fun mockMeterRegistry(): MeterRegistry {
        val mockMeterRegistry = mockk<MeterRegistry>()
        every { mockMeterRegistry.gauge(any<String>(), any<Iterable<Tag>>(), any<Number>()) } returns 1
        return mockMeterRegistry
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.RECIPE).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dsl.deleteFrom(Tables.FRACTION_SKU_DEMAND).execute()
    }

    companion object {
        lateinit var dcRecord: DcConfigRecord
        lateinit var dcRecord1: DcConfigRecord
        lateinit var skuSpecificationRecord: SkuSpecificationRecord
        internal val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        lateinit var dsl: MetricsDSLContext
        lateinit var demandRepository: DemandRepository
        lateinit var recipeRepository: RecipeRepository
        lateinit var recipeViewRepository: RecipeViewRepository
        lateinit var recipeSnapshotRepository: RecipeSnapshotRepository

        lateinit var fractionSkuDemandRepository: FractionSkuDemandRepository
        lateinit var skuRepositoryBuilder: SkuRepository
        lateinit var dcConfigRepository: DcConfigRepository
        lateinit var fileUploadsRepository: FileUploadsRepository
        lateinit var meterRegistry: SimpleMeterRegistry
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            meterRegistry = SimpleMeterRegistry()
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcRecord = createDcConfigRecord()
            dcRecord1 = createAuDcConfigRecord()
            skuSpecificationRecord = createSkuSpecificationRecord()
            dsl.batchInsert(
                dcRecord,
                dcRecord1,
                skuSpecificationRecord,
                createSkuSpecificationRecord(marketParam = "au"),
                createSkuSpecificationRecord("SPI-1235")
            ).execute()
            demandRepository = DemandRepositoryImpl(dsl)
            recipeRepository = RecipeRepositoryImpl(dsl)
            fractionSkuDemandRepository = FractionSkuDemandRepositoryImpl()
            skuRepositoryBuilder = runBlocking { SkuRepositoryBuilder(dsl) }
            dcConfigRepository = DcConfigRepositoryJooqImpl(dsl)
            fileUploadsRepository = FileUploadsRepositoryImpl()
            recipeViewRepository = RecipeViewRepository(dsl)
            recipeSnapshotRepository = RecipeSnapshotRepository()
        }

        private fun createDcConfigRecord() =
            DcConfigRecord().apply {
                dcCode = "VE"
                market = "DACH"
                productionStart = "MONDAY"
                zoneId = "UTC"
                enabled = true
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
            }
        private fun createAuDcConfigRecord() =
            DcConfigRecord().apply {
                dcCode = "SY"
                market = "AU"
                productionStart = "MONDAY"
                zoneId = "UTC"
                enabled = true
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
            }

        private fun createSkuSpecificationRecord(code: String = "SPI-1234", marketParam: String = "dach") =
            SkuSpecificationRecord().apply {
                id = UUID.randomUUID()
                parentId = null
                this.code = code
                market = marketParam
                packaging = ""
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
                fumigationAllowed = true
            }

        @AfterAll
        @JvmStatic
        fun cleanUp() {
            dsl.deleteFrom(Tables.DC_CONFIG).execute()
            dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        }
    }
}
