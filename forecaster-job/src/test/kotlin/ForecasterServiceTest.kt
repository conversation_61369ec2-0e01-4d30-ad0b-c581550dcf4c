package com.hellofresh.skudemandforecast.forecasterjob

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.featureflags.Context.COUNTRY
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig.MpsConfigValue
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestDynamicConfigClient
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.FUMIGATED
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.REGULAR
import com.hellofresh.skuDemandForecast.models.DemandDetail
import com.hellofresh.skuDemandForecast.models.DemandDetails
import com.hellofresh.skuDemandForecast.recipelib.CountryWeek
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeSnapshotRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeViewRepository
import com.hellofresh.skuDemandForecast.recipelib.UploadedRecipe
import com.hellofresh.skudemandforecast.forecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.forecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.forecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.forecasterjob.repository.DcConfigRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.DemandRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.FileUploadsRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.FractionSkuDemandRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.ImportsRepositoryImpl
import com.hellofresh.skudemandforecast.forecasterjob.repository.SkuRepository
import com.hellofresh.skudemandforecast.forecasterjob.repository.SkuRepositoryBuilder
import com.hellofresh.skudemandforecast.forecasterjob.repository.SkuSpec
import default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.THURSDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import java.util.stream.Stream
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import random

internal class ForecasterServiceTest {
    private val mockSkuRepository = mockk<SkuRepository>()
    private val mockDcConfigRepository = mockk<DcConfigRepository>()
    private val meterRegistry = SimpleMeterRegistry()
    private val mockDslContext = mockk<MetricsDSLContext>()
    private val recipeRepository = mockk<RecipeRepository>()
    private val demandRepository = mockk<DemandRepository>()
    private val skuRepositoryBuilder = mockk<SkuRepositoryBuilder>()
    private val fractionSkuDemandRepository = mockk<FractionSkuDemandRepository>()
    private val fileUploadsRepository = mockk<FileUploadsRepository>()
    private val recipeViewRepository = mockk<RecipeViewRepository>()
    private val recipeSnapshotRepository = mockk<RecipeSnapshotRepository>()
    private val importsRepository = mockk<ImportsRepositoryImpl>()

    private val dcConfig = DcConfig(
        dcCode = "VE",
        market = "DACH",
        productionStart = DayOfWeek.MONDAY,
        zoneId = ZoneId.of("Europe/Berlin"),
        globalDc = null,
        enabled = true,
    )

    @Test
    fun `should return new fraction demands for matched uploaded recipe & demand`() {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default()
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(
            recipe.recipeIndex,
            newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex,
        )
        assertEquals(
            demand.recipeIndex,
            newForecastDemands.first
                { it.recipeIndex == 1 }.recipeIndex,
        )
    }

    @Test
    fun `should return new fraction demands with correct demand date`() {
        val recipe = UploadedRecipe.Companion.default()
        val assemblyTime = LocalDateTime.now()
        val kittingTime = LocalDateTime.now().plusDays(1)
        val demand = UploadedDemand.Companion.default(
            DemandDetails(
                listOf(
                    DemandDetail(10, null, assemblyTime, kittingTime),
                ),
            ),
        )
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "Mealkit bag")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                setOf("Mealkit bag"),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(kittingTime.toLocalDate(), newForecastDemands.first().demandDate)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "Non Kitting")
        val (newForecastDemands2, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                setOf("Mealkit bag"),
            )
        }
        assertEquals(1, newForecastDemands2.size)
        assertEquals(assemblyTime.toLocalDate(), newForecastDemands2.first().demandDate)
    }

    @Test
    fun `should return new fraction demands with correct demand date considering different assemblyDate , kittingDate`() {
        val recipe = UploadedRecipe.Companion.default()
        val assemblyTimeOne = LocalDateTime.now()
        val kittingTimeOne = LocalDateTime.now().plusDays(1)
        val assemblyTimeTwo = LocalDateTime.now().plusDays(1)
        val kittingTimeTwo = LocalDateTime.now().plusDays(2)
        val assemblyTimeThree = LocalDateTime.now().plusDays(2)
        val kittingTimeThree = LocalDateTime.now().plusDays(3)
        val demand = UploadedDemand.Companion.default(
            DemandDetails(
                listOf(
                    DemandDetail(1000, null, assemblyTimeOne, kittingTimeOne),
                    DemandDetail(2000, null, assemblyTimeTwo, kittingTimeTwo),
                    DemandDetail(3000, null, assemblyTimeThree, kittingTimeThree),
                ),
            ),
        )

        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "Mealkit bag")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                setOf("Mealkit bag"),
            )
        }
        assertEquals(3, newForecastDemands.size)
        assertEquals(kittingTimeOne.toLocalDate(), newForecastDemands.first { it.qty == 1000L }.demandDate)
        assertEquals(kittingTimeTwo.toLocalDate(), newForecastDemands.first { it.qty == 2000L }.demandDate)
        assertEquals(kittingTimeThree.toLocalDate(), newForecastDemands.first { it.qty == 3000L }.demandDate)
    }

    @ParameterizedTest
    @MethodSource("inputsForDemandTypesVerification")
    @Suppress("LongParameterList")
    fun `should return new fraction demands for matched uploaded recipe & demand using the demand type fumigated, regular`(
        fumigatedDemandType: String? = null,
        regularDemandType: String? = null,
        fumigationAllowed: Boolean? = null,
        expectedFumigated: Long? = null,
        expectedRegular: Long? = null,
        expectedNull: Long? = null
    ) {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default(
            demandDetails = DemandDetails(
                listOf(
                    DemandDetail(10, fumigatedDemandType, null, null),
                    DemandDetail(10, regularDemandType, null, null),
                ),
            ),
        )
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(fumigationAllowed, UUID.randomUUID(), "type")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(
            recipe.recipeIndex,
            newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex,
        )
        assertEquals(
            demand.recipeIndex,
            newForecastDemands.first
                { it.recipeIndex == 1 }.recipeIndex,
        )
        val expectedDemandTypes = listOfNotNull(
            if (expectedFumigated != null) FUMIGATED to expectedFumigated else null,
            if (expectedRegular != null) REGULAR to expectedRegular else null,
        )

        if (expectedDemandTypes.isEmpty()) {
            assertEquals(
                expectedNull,
                newForecastDemands.first().recipeDetails.first { it.peopleCount == 1 && it.demandType == null }.qty,
            )
        } else {
            expectedDemandTypes.forEach { (demandType, expectedQty) ->
                assertEquals(
                    expectedQty,
                    newForecastDemands.first().recipeDetails.first { it.demandType == demandType }.qty,
                )
            }
        }
    }

    @Test
    fun `should return new fraction demands with all source ids from recipe & demand`() {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default()

        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")
        coEvery { mockDcConfigRepository.fetch() } returns listOf(dcConfig)

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                listOf(recipe),
                listOf(demand),
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(
            setOf(recipe.sourceId, demand.sourceId),
            newForecastDemands.first().sourceIds,
        )
    }

    @Test
    fun `should return empty list of new fraction demands when the uploaded recipe and demand are not matching`() {
        val recipe = UploadedRecipe.Companion.default().copy(
            recipeIndex = 10,
        )
        val demand = UploadedDemand.Companion.default().copy(
            recipeIndex = 20,
        )
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )
        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertTrue(newForecastDemands.isEmpty())
    }

    @Test
    fun `should returns multiple new fraction demands when the uploaded recipe and demand are matching`() {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default()
        val recipeOne = UploadedRecipe.Companion.default().copy(
            recipeIndex = 10,
        )
        val recipeNotMatching = UploadedRecipe.Companion.default().copy(
            recipeIndex = 100,
        )
        val demandOne = UploadedDemand.Companion.default().copy(
            recipeIndex = 10,
        )
        val demandNotMatching = UploadedDemand.Companion.default().copy(
            recipeIndex = 200,
        )
        val uploadedRecipe = listOf(recipe, recipeOne, recipeNotMatching)
        val uploadedDemand = listOf(demand, demandOne, demandNotMatching)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )
        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(2, newForecastDemands.size)
        assertEquals(recipe.recipeIndex, newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex)
        assertEquals(demand.recipeIndex, newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex)
        assertEquals(recipeOne.recipeIndex, newForecastDemands.first { it.recipeIndex == 10 }.recipeIndex)
        assertEquals(demandOne.recipeIndex, newForecastDemands.first { it.recipeIndex == 10 }.recipeIndex)
    }

    @Test
    fun `missing recipeIds are collected`() {
        val recipes = buildList { repeat(5) { add(UploadedRecipe.random()) } }
        val demand = buildList { add(UploadedDemand.random()) }
        val output = missingRecipes(recipes, demand)
        assertTrue { output.isNotEmpty() }

        val expected = recipes.mapNotNull { it.sourceId } + demand.mapNotNull { it.sourceId }
        assertEquals(expected.sorted(), output.keys.sorted())
    }

    @ParameterizedTest
    @MethodSource("provideMissingRecipeInputs")
    fun `should prepare the missing recipe ids for the given recipe and demand records`(
        recipesParam: List<UploadedRecipe>,
        demandsParam: List<UploadedDemand>,
        expectedResult: Map<UUID, MissingRecipeDetails>
    ) {
        val result = missingRecipes(recipesParam, demandsParam)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `dcDatesInWeek gets all the days in a production week`() {
        val dc = "VE"
        val date = LocalDate.now()
        val dcConfig = DcConfig.random().copy(
            dcCode = dc,
            productionStart = THURSDAY,
        )

        val dcDates = dcDatesInWeek(dc, date, dcConfigMap = mapOf(dc to dcConfig))
        assertEquals(7, dcDates.size)
        assertTrue { dcDates.contains(DcDate(dc, date)) }
        assertEquals(7, dcDates.map { it.date.dayOfWeek }.toSet().size)
        assertEquals(THURSDAY, dcDates.minOf { it.date }.dayOfWeek)
    }

    @Test
    fun `recipe and demand for different brands shouldn't be matched`() {
        val recipe = UploadedRecipe.default().copy(brand = "HF")
        val demand = UploadedDemand.default().copy(brand = "EP")
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(0, newForecastDemands.size)
    }

    @Test
    fun `fraction demand of different brands are aggregated`() {
        val demandHF = UploadedDemand.default().copy(brand = "HF")
        val recipeHF = UploadedRecipe.default().copy(brand = "HF")

        val demandEP = UploadedDemand.default().copy(
            brand = "EP",
            mealsToDeliver = 100,
        )
        val recipeEP = UploadedRecipe.default().copy(brand = "EP")

        val uploadedRecipe = listOf(recipeEP, recipeHF)
        val uploadedDemand = listOf(demandEP, demandHF)
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(110, newForecastDemands.first().qty)
        assertEquals(
            setOf(demandHF.sourceId, recipeHF.sourceId, demandEP.sourceId, recipeEP.sourceId),
            newForecastDemands.first().sourceIds,
        )
    }

    @Test
    fun `errors in null source ids are ignored`() {
        val recipes = buildList { repeat(5) { add(UploadedRecipe.random().copy(sourceId = null)) } }
        val demand = buildList { add(UploadedDemand.random().copy(sourceId = null)) }
        coEvery { mockSkuRepository.fetchSkuSpec(any(), any()) } returns SkuSpec(null, UUID.randomUUID(), "type")
        // Recipes will mismatch as they
        val (newForecastDemands, errors) = runBlocking {
            prepareNewForecastDemands(
                recipes,
                demand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
                meterRegistry,
                emptySet(),
            )
        }
        assertTrue { newForecastDemands.isEmpty() }

        // errors with null fileIds are ignored
        assertTrue { errors.isEmpty() }
    }

    @ParameterizedTest
    @CsvSource(
        "DE,2024-W50,DACH,DE,2024-W50,true", // Recipe countryWeek and mps flag week matches - equal
        "DE,2025-W11,DACH,DE,2025-W10,true", // Recipe countryWeek and mps flag week matches - greater
        "DE,2025-W11,'DACH','DE','',true", // Recipe countryWeek matches - no start week
        "DE,2024-W50,DACH,DE,2024-W52,false", // Recipe countryWeek and mps flag week does not matches
        "IT,2024-W50,DACH,DE,2024-W50,false", // Recipe countryWeek and mps flag week does not matches
    )
    @Suppress("LongParameterList")
    fun `should verify the enabled mps imports country and week`(
        country: String,
        week: String,
        mpsEnabledMarket: String,
        mpsEnabledCountry: String,
        mpsStartWeek: String,
        isEnabledExpected: Boolean
    ) {
        runBlocking {
            val forecasterService = ForecasterService(
                readWriteDslContext = mockDslContext,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { dsl -> SkuRepositoryBuilder(dsl) },
                dcConfigRepository = mockDcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
                fileUploadsRepository = fileUploadsRepository,
                meterRegistry = meterRegistry,
                recipeViewRepository = recipeViewRepository,
                recipeSnapshotRepository = recipeSnapshotRepository,
                importsRepository = ImportsRepositoryImpl,
                statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                    setOf(
                        FeatureFlag.MpsImport(
                            COUNTRY,
                            mpsEnabledCountry,
                        ),
                    ),
                ),

                dynamicConfigClient = StatsigTestDynamicConfigClient(
                    mapOf(
                        DynamicConfig.MPSConfig(MARKET, mpsEnabledMarket)
                            to MpsConfigValue(mpsStartWeek),
                    ),
                ),
                kittingTimePackagingTypes = emptySet(),
            )
            assertEquals(isEnabledExpected, forecasterService.isEnabledMpsImports(CountryWeek(country, week)))
        }
    }

    companion object {
        @JvmStatic
        fun provideMissingRecipeInputs(): Stream<Arguments> {
            val fileId1 = UUID.randomUUID()
            val fileId2 = UUID.randomUUID()

            // Case 1: Recipe exists but no corresponding demand
            val recipesParam1 = listOf(
                UploadedRecipe.random().copy(
                    recipeIndex = 1,
                    sourceId = fileId1,
                    week = "2024-W01",
                ),
                UploadedRecipe.random().copy(
                    recipeIndex = 3,
                    sourceId = fileId1,
                    week = "2024-W01",
                ),
                UploadedRecipe.random().copy(
                    recipeIndex = 4,
                    sourceId = fileId1,
                    week = "2024-W01",
                ),
            )
            val demandsParam1 = listOf(
                UploadedDemand.random().copy(
                    recipeIndex = 2,
                    sourceId = fileId2,
                    week = "2024-W01",
                ),
            )
            val expectedResult1 = mapOf(
                fileId1 to MissingRecipeDetails(
                    missingRecipes = setOf(1, 3, 4),
                    sourceType = "Demand",
                    weeks = setOf("2024-W01"),
                ),
                fileId2 to MissingRecipeDetails(
                    missingRecipes = setOf(2),
                    sourceType = "Recipe",
                    weeks = setOf("2024-W01"),
                ),
            )

            // Case 2: Demand exists but no corresponding recipe
            val recipesParam2 = listOf(
                UploadedRecipe.random().copy(
                    recipeIndex = 2,
                    sourceId = fileId1,
                    week = "2024-W01",
                ),
            )
            val demandsParam2 = listOf(
                UploadedDemand.random().copy(
                    recipeIndex = 1,
                    sourceId = fileId2,
                    week = "2024-W01",
                ),
                UploadedDemand.random().copy(
                    recipeIndex = 3,
                    sourceId = fileId2,
                    week = "2024-W01",
                ),
                UploadedDemand.random().copy(
                    recipeIndex = 4,
                    sourceId = fileId2,
                    week = "2024-W01",
                ),
            )
            val expectedResult2 = mapOf(
                fileId1 to MissingRecipeDetails(
                    missingRecipes = setOf(2),
                    sourceType = "Demand",
                    weeks = setOf("2024-W01"),
                ),
                fileId2 to MissingRecipeDetails(
                    missingRecipes = setOf(1, 3, 4),
                    sourceType = "Recipe",
                    weeks = setOf("2024-W01"),
                ),
            )

            // Case 3: No missing recipes or demands (recipe and demand match)
            val recipesParam3 = listOf(
                UploadedRecipe(
                    recipeIndex = 1,
                    sourceId = fileId1,
                    week = "2024-W01",
                    family = "family",
                    locale = "de",
                    country = "DE",
                    skus = emptyList(),
                    brand = "UNKNOWN",
                ),
            )
            val demandsParam3 = listOf(
                UploadedDemand(
                    recipeIndex = 1, sourceId = fileId1, week = "2024-W01",
                    family = "family", locale = "de", country = "DE", brand = "UNKNOWN", day = MONDAY,
                    dcCode = "VE", mealsToDeliver = 10, peopleCount = 4,
                ),
            )
            val expectedResult3 = emptyMap<UUID, MissingRecipeDetails>()

            return Stream.of(
                Arguments.of(recipesParam1, demandsParam1, expectedResult1),
                Arguments.of(recipesParam2, demandsParam2, expectedResult2),
                Arguments.of(recipesParam3, demandsParam3, expectedResult3),
            )
        }

        @JvmStatic fun inputsForDemandTypesVerification(): Stream<Arguments> =
            Stream.of(
                Arguments.of(null, null, null, null, null, 20L),
                Arguments.of(FUMIGATED.value, null, null, null, null, 10L),
                Arguments.of(null, REGULAR.value, null, null, 10L, null),
                Arguments.of(FUMIGATED.value, REGULAR.value, true, 10L, 10L, null),
                Arguments.of(FUMIGATED.value, REGULAR.value, false, null, 20L, null),
            )
    }
}
