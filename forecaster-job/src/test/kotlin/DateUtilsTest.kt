package com.hellofresh.skudemandforecast.forecasterjob

import com.hellofresh.skudemandforecast.forecasterjob.model.calculateDate
import java.time.DayOfWeek
import java.time.LocalDate
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class DateUtilsTest {

    @ParameterizedTest
    @CsvSource(
        "2021-W27,FRIDAY,FRIDAY,false,2021-07-02",
        "2021-W27,SATURDAY,FRIDAY,false,2021-07-03",
        "2021-W27,SUNDAY,FRIDAY,false,2021-07-04",
        "2021-W27,MONDAY,FRIDAY,false,2021-07-05",
        "2021-W27,TUESDAY,FRIDAY,false,2021-07-06",
        "2021-W27,WEDNESDAY,FRIDAY,false,2021-07-07",
        "2021-W27,THURSDAY,FRIDAY,false,2021-07-08",
        "2021-W24,WEDNESDAY,SUNDAY,false,2021-06-16",
        "2021-W24,WEDNESDAY,WEDNESDAY,false,2021-06-09",
        "2021-W24,SUNDAY,MONDAY,true,2021-06-20",
        "2021-W24,SATURDAY,MONDAY,true,2021-06-19",
        "2021-W24,FRIDAY,MONDAY,true,2021-06-18",
        "2021-W24,THURSDAY,MONDAY,true,2021-06-17",
        "2021-W24,WEDNESDAY,MONDAY,true,2021-06-16",
        "2021-W24,TUESDAY,MONDAY,true,2021-06-15",
        "2021-W24,MONDAY,MONDAY,true,2021-06-14",
        "2021-W01,WEDNESDAY,WEDNESDAY,false,2020-12-30",
        "2021-W29,SATURDAY,SATURDAY,false,2021-07-17",
        "2021-W29,SUNDAY,SATURDAY,false,2021-07-18",
        "2021-W29,MONDAY,SATURDAY,false,2021-07-19",
        "2021-W29,TUESDAY,SATURDAY,false,2021-07-20",
        "2021-W29,WEDNESDAY,SATURDAY,false,2021-07-21",
        "2021-W29,THURSDAY,SATURDAY,false,2021-07-22",
        "2021-W29,FRIDAY,SATURDAY,false,2021-07-23",
    )
    fun `demand date is calculated correctly for each dc base on production week`(
        productionWeek: String,
        dayOfWeek: DayOfWeek,
        productionStart: DayOfWeek,
        currentWeekProduction: Boolean,
        expectedDate: LocalDate,
    ) = assertEquals(expectedDate, calculateDate(productionWeek, productionStart, dayOfWeek, currentWeekProduction))
}
