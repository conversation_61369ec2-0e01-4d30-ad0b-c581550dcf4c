package com.hellofresh.skudemandforecast.forecasterjob

import com.hellofresh.skudemandforecast.forecasterjob.model.RecipeDemandKey
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class ForecastMismatchDetailServiceTest {
    @ParameterizedTest
    @MethodSource("mismatchInput")
    @Suppress("LongParameterList")
    fun `should find the mismatched details`(
        country: String,
        week: String,
        matchedSize: Int,
        unmatchedSize: Int,
    ) {
        val matchedEntries = (1..matchedSize).map {
            RecipeDemandKey(1, week, country, "Locale", "Brand", null)
        }

        val unmatchedEntries = (0..unmatchedSize).map {
            RecipeDemandKey(2, week, country, "Locale", "Brand", null)
        }

        val result = ForecastMismatchDetailService.findMismatchDetails(matchedEntries, unmatchedEntries)
        assertEquals(1, result.size)
        val mismatchDetail = result.first()
        assertEquals(country, mismatchDetail.key.country)
        assertEquals(week, mismatchDetail.key.week)
        assertEquals(matchedSize, mismatchDetail.matchedSize)
    }

    companion object {
        @JvmStatic
        fun mismatchInput(): Stream<Arguments> = Stream.of(
            Arguments.of("GB", "2024-W10", 1, 10), // "Mismatch crossed 80% - Error"
            Arguments.of("IT", "2024-W10", 20, 1000), // "Mismatch crossed 90% - Error"
            Arguments.of("IT", "2024-W10", 100, 100), // "Mismatch crossed 50%"
            Arguments.of("DE", "2024-W10", 10, 1), // "Mismatch crossed 15%"
        )
    }
}
