ownership:
  tribe: global-ai
  squad: ml-solutions

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        - louis.<PERSON><PERSON><PERSON>@hellofresh.com
        - evgenii.k<PERSON><PERSON><PERSON>@hellofresh.com
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_github_hellofresh_global_ai_mls
    kubernetes:
      enabled: true
      namespaces: ["freshbot", "global-ai*"]
      service_accounts: ["freshbot-sa"]
    aws:
      iam_principal_arns:
      - arn:aws:iam::************:role/databricks-managed-*
