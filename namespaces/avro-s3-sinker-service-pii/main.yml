ownership:
  tribe: data-platform
  squad: data-core

environments:
  staging: {}
  live: {}

vault:
  root_namespace: services
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_data_infrastructure_and_operations
        - css_iam_tech_squad_core_data
    kubernetes:
      namespaces: ["proto-s3-sinker-service-pii"]
      service_accounts:
        - kafka-connect-avro-s3-sinker-service-pii-connect*
