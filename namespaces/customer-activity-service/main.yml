ownership:
  tribe: week-management
  squad: agent-experience

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["week-management", "scm"]
      service_accounts: ["customer-activity-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - b<PERSON><PERSON><PERSON><PERSON>.<EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_agent_experience
