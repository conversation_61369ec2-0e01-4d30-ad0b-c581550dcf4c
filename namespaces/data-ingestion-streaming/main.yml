ownership:
  tribe: data-platform
  squad: data-ingestion

environments:
  staging: {}
  live: {}


vault:
  root_namespace: infrastructure
  auth_methods:
    github_actions_jwt:
      additional_repositories:
        dsi-infra: {}
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      az_additional_groups:
      - css_iam_squad_data_ingestion
      - css_iam_tech_squad_core_data
      - css_iam_squad_data_infrastructure_and_operations
    kubernetes:
      namespaces: ["data-ingestion"]
      service_accounts:
        - kafka-connect-data-ingestion-connect
        - kafka-connect-data-ingestion-connect-build
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/data-platform-role
