ownership:
 tribe: global-ai
 squad: ml-solutions

environments:
  dwh: {}
  dwh-staging: {}

vault:
 auth_methods:
  kubernetes:
    namespaces: ["global-ai*","dsp-*","beneluxfr*"]
    service_accounts: ["ds-global-ai-role","dsp-main-role-service-account","beneluxfr*"]
  azure_oidc:
    az_group_owners:
      - louis.<PERSON><PERSON><EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    az_additional_groups:
      - acl_github_hellofresh_global_ai_mls
  aws:
    iam_principal_arns:
      # legacy naming
      - arn:aws:iam::************:role/ds-ce-role
      - arn:aws:iam::************:role/gp-role
      - arn:aws:iam::************:role/scm-role
      - arn:aws:iam::************:role/us-ops-role
      - arn:aws:iam::************:role/pa-role
      - arn:aws:iam::************:role/factor75-ds-ce-role
      - arn:aws:iam::************:role/nordics-ds-ce-role
      - arn:aws:iam::************:role/ca-diat-role
      - arn:aws:iam::************:role/fpa-ds-ce-role
      - arn:aws:iam::************:role/data-platform-role
      - arn:aws:iam::************:role/uk-ds-ce-role
      - arn:aws:iam::************:role/anz-ds-role
      - arn:aws:iam::************:role/us-ma-role
      - arn:aws:iam::************:role/ma-role
      - arn:aws:iam::************:role/default-dummy-role
      - arn:aws:iam::************:role/global-ai-role
      # managed DB roles naming
      - arn:aws:iam::************:role/databricks-managed-*
