ownership:
  tribe: habit-building
  squad: loyalty-engine

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["habit-building"]
      service_accounts: ["customer-onboarding-service", "customer-onboarding-service-*", "kafka-connect-early-retention-rabbitmq-connect", "kafka-connect-early-retention-rabbitmq-connect-build"]
    azure_oidc:
      az_group_owners:
        - aleks<PERSON>.<EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-habit-building
        - css_iam_oncall_tribe-habit-building
