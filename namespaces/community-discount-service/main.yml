ownership:
  tribe: rte-expansion
  squad: rte-platform
  partition:
    - tam-expansion

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces:
        - "conversions"
      service_accounts:
        - "community-discount-service-*"
        - "cmds-outbox-worker-amqp"
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_github_hellofresh_squad-rte-systems
        - css_iam_oncall_privesc_vault_rte-systems-squad
        - css_iam_oncall_privesc_vault_new-brands-web
        - css_iam_oncall_privesc_vault_tribe-conversions-journey
        - css_iam_oncall_privesc_vault_tribe-conversions-us
        - acl_github_hellofresh_rte-platform
        - css_iam_squad_rte_platform
