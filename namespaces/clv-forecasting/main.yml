ownership:
 tribe: global-ai
 squad: audience-targeting

environments:
  dwh: {}
  dwh-staging: {}

vault:
 auth_methods:
   kubernetes:
     namespaces: ["global-ai*"]
     service_accounts: ["ds-global-ai-role", "prefect-k8s-agent-live", "prefect-k8s-agent-staging"]
   azure_oidc:
     az_group_owners:
       - <EMAIL>
       - <EMAIL>
       - <EMAIL>
       - <EMAIL>
       - <EMAIL>
       - <EMAIL>
     az_additional_groups:
       - acl_vault_audience_targeting_engineers
   aws:
     iam_principal_arns:
       - arn:aws:iam::************:role/global-ai-role
       - arn:aws:iam::************:role/prefect-k8s-agent-staging
       - arn:aws:iam::************:role/prefect-k8s-agent-live
