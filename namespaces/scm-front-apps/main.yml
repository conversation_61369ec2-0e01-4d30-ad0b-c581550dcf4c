ownership:
  tribe: scm 
  squad: shared-frontend
  partition:
    - food-systems

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  root_namespace: services 
  auth_methods:
    jwt_kubernetes:
      kubernetes_identities:
        namespace-name:
          - scm-ftcp-migration-dummy
    kubernetes:
      namespaces: ["scm"]
      service_accounts: ["*-fragment"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_monolith_scm_front_apps
