ownership:
  tribe: virality
  squad: network

environments:
  staging: {}
  live: {}

vault:
  root_namespace: infrastructure
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_network
    kubernetes:
      enabled: false
    aws:
      iam_principal_arns:
        - arn:aws:iam::985437859871:role/EMR_EC2_DefaultRole
        - arn:aws:iam::985437859871:role/adtech-ddi-pipelines-pii-staging
        - arn:aws:iam::985437859871:role/adtech-ddi-pipelines-pii-live
