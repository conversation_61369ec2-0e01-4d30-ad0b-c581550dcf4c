ownership:
  squad: shared
  tribe: product-analytics

environments:
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["product-analytics"]
      service_accounts: ["kafka-connect-pa-shared-rabbitmq-connect", "kafka-connect-pa-shared-rabbitmq-connect-build"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_pa_data_engineering
