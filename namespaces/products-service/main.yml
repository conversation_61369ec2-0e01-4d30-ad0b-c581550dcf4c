ownership:
  tribe: checkout-experience
  squad: cart-foundation

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["menu"]
      service_accounts: ["products-service-*", "products-db-dump", "products-db-restore"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_food-alliance-checkout-experience-tribe-rotation
        - css_iam_oncall_privesc_vault_food-alliance-shopping-experience-tribe-rotation
