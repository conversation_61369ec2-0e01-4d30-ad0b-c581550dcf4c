ownership:
  tribe: reliability-platform
  squad: cloud-runtime

environments:
  ahoy: {}
  staging: {}
  live: {}

vault:
  root_namespace: infrastructure
  auth_methods:
    kubernetes:
      namespaces: [ "platform", "edge-stack-public", "edge-stack-private", "vault-infra" ]
      service_accounts: [ "edge-stack-public", "edge-stack-private", "ambassador-clientsecretinjector-filter", "vault-secrets-webhook" ]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - usr_tribe_platform
        - css_iam_oncall_privesc_vault_tribe-platform
        - css_iam_oncall_privesc_vault_frontend-infrastructure

