ownership:
  tribe: us-planning-technology
  squad: engineering
vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["us-fpt-dashboards", "us-fpt-riptide", "us-fpt-development"]
      service_accounts: ["us-fpt-planning-suite-*", "us-fpt-riptide*", "kafka-connect-us-fpt-riptide*"]
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
environments:
  dwh-staging: {}
  dwh: {}
  live: {}
  staging: {}
