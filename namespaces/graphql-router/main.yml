ownership:
  tribe: shopping-foundation
  squad: shopping-platform
  partition:
    - active-journey

vault:
  auth_methods:
    kubernetes: # This refers to the legacy k8s configuration
      enabled: false
    jwt_kubernetes:
      kubernetes_identities:
        graphql-router:
          - graphql-router-vso
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_food-alliance-checkout-experience-tribe-rotation
        - css_iam_oncall_privesc_vault_food-alliance-shopping-experience-tribe-rotation
