ownership:
  tribe: global_ai
  squad: marketing-customer-profile

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  dwh:
    auth_methods:
      kubernetes:
        namespaces: ["mcpro"]
  dwh-staging:
    auth_methods:
      kubernetes:
        namespaces: ["mcpro"]

vault:
  auth_methods:
    kubernetes:
      max_lease_ttl: 168h
      namespaces: [ "mcpro" ]
      service_accounts: [ "adtech-cdp-orchestration-*" ]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_data_infrastructure_and_operations
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/EMR_EC2_DefaultRole
