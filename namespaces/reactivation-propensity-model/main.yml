ownership:
  tribe: global_ai
  squad: pricing-optimization

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["global-ai"]
      service_accounts: ["reactivation-propensity-model-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - jayasaik<PERSON><EMAIL>
