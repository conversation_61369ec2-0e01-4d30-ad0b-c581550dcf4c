ownership:
  tribe: payments
  squad: payments-platform

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["payments"]
      service_accounts: ["batch-payments-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - oleksii.<PERSON><PERSON><PERSON><PERSON><PERSON>@hellofresh.com
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-payments
        - css_iam_oncall_tribe-payments
        - css_iam_oncall_tribe-payments-2
