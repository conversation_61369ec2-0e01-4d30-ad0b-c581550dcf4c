ownership:
  tribe: miso-na
  squad: shared
vault:
  root_namespace: infrastructure
  auth_methods:
    kubernetes:
      enabled: false
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      - <EMAIL>
      az_additional_groups:
      - css_iam_tribe_data_platform
    aws:
      iam_principal_arns:
      - arn:aws:iam::794016200254:role/us-ma-role
      - arn:aws:iam::794016200254:role/databricks-managed-*
environments:
  tools: {}
  live: {}
  staging: {}
  ahoy: {}
