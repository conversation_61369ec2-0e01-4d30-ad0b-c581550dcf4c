ownership:
  tribe: payments
  squad: payments-platform

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["payments"]
      service_accounts: ["intfood-sinkers-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_customers
        - css_iam_squad_payments_platform
        - css_iam_squad_plans
        - css_iam_squad_personalization
        - css_iam_squad_payments_risk_and_revenue_management
        - css_iam_squad_payments_acceptance
        - css_iam_squad_delivery_network
        - css_iam_oncall_privesc_vault_tribe-consumer-core
        - css_iam_oncall_privesc_vault_tribe-payments
        - css_iam_oncall_tribe-payments
        - css_iam_oncall_tribe-payments-2
        - css_iam_tribe_staffengineer_customer_benefits
        - css_iam_oncall_privesc_vault_tribe-customer-benefits
        - css_iam_tech_squad_vouchers
