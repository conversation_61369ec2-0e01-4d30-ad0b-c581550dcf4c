ownership:
  tribe: consumer-core
  squad: plans

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["consumer-core", "platform"]
      service_accounts: ["api-v2-*", "cdps-api-v2-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_consumer_core
        - css_iam_oncall_privesc_vault_tribe-consumer-core
