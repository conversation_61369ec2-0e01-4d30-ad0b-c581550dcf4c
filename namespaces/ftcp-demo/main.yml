ownership:
  tribe: reliability-platform
  squad: cloud-runtime
  partition:
  - .*

environments:
  staging: {}
  live: {}

# This is the same as:
#
#   ftcp_environments: {}
#     stage: {}
#     prod: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      enabled: false
    azure_oidc:
      az_group_owners:
        - <EMAIL>
    jwt_kubernetes:
      kubernetes_identities:
        ftcp-demo:
          - app-a
          - app-b
        default:
          - outbox-worker-amqp-vault-auth
          - outbox-worker-client-amqp-vault-auth
          - outbox-worker-kafka-vault-auth
          - outbox-worker-client-kafka-vault-auth
