ownership:
  tribe: virality
  squad: network

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["conversions"]
      service_accounts:
        - referral-service-*
        - kafka-connect-referral-service-rabbitmq-connect
        - kafka-connect-referral-service-rabbitmq-connect-build
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
