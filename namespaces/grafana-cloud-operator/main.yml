ownership:
  tribe: platform
  squad: site-reliability

environments:
  ahoy: {}
  staging: {}
  au-staging: {}
  live: {}
  au-live: {}
  dwh: {}
  dwh-staging: {}
  tools: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["grafana-cloud-operator"]
      service_accounts: ["grafana-cloud-operator", "grafana-cloud-operator-api"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - usr_tribe_platform
