ownership:
  tribe: advanced-strategic-procurement
  squad: sourcing-data-management

environments:
  staging: {}
  live: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["scm"]
      service_accounts: ["kafka-connect-scm-procurement-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_sourcing_data_management
        - css_iam_squad_supply_planning_integrations
