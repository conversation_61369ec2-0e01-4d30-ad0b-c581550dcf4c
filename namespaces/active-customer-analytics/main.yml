ownership:
  tribe: global-actives
  squad: active-customer-analytics

environments:
  dwh: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_global_actives
    kubernetes:
      enabled: false
    aws:
      iam_principal_arns:
        - arn:aws:iam::794016200254:role/databricks-managed-actives_analytics-ds-ce
        - arn:aws:iam::794016200254:role/databricks-managed-actives_analytics_sensitive-ds-ce
