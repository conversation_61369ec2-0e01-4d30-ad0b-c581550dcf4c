ownership:
  tribe: planning-and-purchasing
  squad: purchase-order-lifecycle
  partition:
    - food-systems

environments:
  ahoy: {}
  staging: {}
  live: {}

vault:
  root_namespace: services 
  auth_methods:
    jwt_kubernetes:
      kubernetes_identities:
        oms-kafka-processor:
          - oms-kafka-processor-vso
        oms-order-management-http:
          - oms-order-management-http-vso
    kubernetes:
      namespaces: ["scm", "oms-*"]
      service_accounts: ["oms-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_order_planning
        - ext_Intellias_Ordering
        - css_iam_tribe_staffengineer_procurement_tech
