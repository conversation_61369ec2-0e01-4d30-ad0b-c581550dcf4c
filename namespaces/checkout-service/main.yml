ownership:
  tribe: payments
  squad: conversions

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["payments"]
      service_accounts: ["checkout-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - oleksii.s<PERSON><PERSON><PERSON><PERSON>@hellofresh.com
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_payments_conversions
        - css_iam_oncall_privesc_vault_tribe-payments
        - acl_github_hellofresh_payments-backend
