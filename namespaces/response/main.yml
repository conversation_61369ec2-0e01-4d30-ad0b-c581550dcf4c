ownership:
  tribe: reliability-platform
  squad: observability
  partition:
    - foundations

environments:
  live: {}
  staging: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["monitoring", "platform"]
      service_accounts: ["response-api", "response-cron"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - usr_tribe_platform
        - css_iam_squad_observability
