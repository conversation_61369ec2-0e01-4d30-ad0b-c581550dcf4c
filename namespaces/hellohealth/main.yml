ownership:
  tribe: no-tribe
  squad: shared-squads

environments:                        # List of environments that the application runs on.
  tools: {}                          # For each environment will be created default KV secrets engine
  live: {}                           # and kubernetes auth method. It is possible to override common
  staging: {}                        # kuberentes auth methods parameters like allowed namespaces and
  ahoy:                              # service_accounts here
    auth_methods:
      kubernetes:
        namespaces: ["*"]            # This is a special use case for ahoy to use * wildcard for namespace since ahoy spins up dynamic namespaces

vault:
  auth_methods:
    kubernetes:
      namespaces: ["*"]
      service_accounts: ["default"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>