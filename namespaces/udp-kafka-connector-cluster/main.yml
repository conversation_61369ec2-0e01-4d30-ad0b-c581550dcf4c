ownership:
  tribe: data-platform
  squad: data-fusion

environments:
  live: {}
  staging: {}

vault:
  root_namespace: services
  auth_methods:
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      - <EMAIL>
      az_additional_groups:
      - acl_vault_tardis
      - css_iam_squad_data_infrastructure_and_operations
    kubernetes:
      namespaces: ["udp-kafka-cluster-connector","udp-kafka-connect-cluster"]
      service_accounts: [ "kafka-connect-unified-data-platform-*","kafka-connect-tardis-cdc-*" ]
