ownership:
  tribe: data-platform
  squad: data-streaming-and-operations
  partition:
    - foundations

environments:
  dwh-staging: {}
  dwh: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["airflow-cookbook", "airflow-example", "data-platform", "airflow-cookbook-poc"]
      service_accounts: [ "airflow-*", "ftcp-*" ]
      max_lease_ttl: 24h
    aws:
      role: airflow-cookbook-staging-irsa-role
      iam_principal_arns:
        - arn:aws:iam::************:role/airflow-cookbook-staging-irsa-role
    jwt_kubernetes:
      kubernetes_identities:
        airflow-cookbook-poc:
          - ftcp-airflow-cookbook-vso
          - ftcp-dsop-airflow-cookbook-poc-cleanup
          - ftcp-dsop-airflow-cookbook-poc-dag-processor
          - ftcp-dsop-airflow-cookbook-poc-migrate-database-job
          - ftcp-dsop-airflow-cookbook-poc-scheduler
          - ftcp-dsop-airflow-cookbook-poc-statsd
          - ftcp-dsop-airflow-cookbook-poc-triggerer
          - ftcp-dsop-airflow-cookbook-poc-webserver
          - ftcp-dsop-airflow-cookbook-poc-worker
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>

