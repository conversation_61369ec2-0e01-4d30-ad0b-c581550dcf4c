ownership:
  tribe: global-core-data-products
  squad: pa-data-engineering

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    github_actions_jwt:
      additional_repositories:
        dsi-infra: {}
    kubernetes:
      namespaces: ["pa*"]
      service_accounts: ["ds-pa-role"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_pa_data_engineering
        - acl_github_hellofresh_global_ai_mls
        - css_iam_squad_foundations_squad
    aws:
      iam_principal_arns:
        # databricks AWS role
        - arn:aws:iam::************:role/pa-role
        - arn:aws:iam::************:role/global-ai-role
