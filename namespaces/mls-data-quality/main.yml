ownership:
  tribe: global-ai
  squad: ml-solutions

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        - louis.<PERSON><EMAIL>
        - evgenii.k<PERSON><PERSON><PERSON>@hellofresh.com
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_github_hellofresh_global_ai_mls
    kubernetes:
        enabled: false
    aws:
      iam_principal_arns:
        # legacy naming
        - arn:aws:iam::794016200254:role/ds-ce-role
        - arn:aws:iam::794016200254:role/gp-role
        - arn:aws:iam::794016200254:role/scm-role
        - arn:aws:iam::794016200254:role/us-ops-role
        - arn:aws:iam::794016200254:role/pa-role
        - arn:aws:iam::794016200254:role/factor75-ds-ce-role
        - arn:aws:iam::794016200254:role/nordics-ds-ce-role
        - arn:aws:iam::794016200254:role/ca-diat-role
        - arn:aws:iam::794016200254:role/fpa-ds-ce-role
        - arn:aws:iam::794016200254:role/data-platform-role
        - arn:aws:iam::794016200254:role/uk-ds-ce-role
        - arn:aws:iam::794016200254:role/anz-ds-role
        - arn:aws:iam::794016200254:role/us-ma-role
        - arn:aws:iam::794016200254:role/ma-role
        - arn:aws:iam::794016200254:role/default-dummy-role
        - arn:aws:iam::794016200254:role/global-ai-role
        # managed DB roles naming
        - arn:aws:iam::794016200254:role/databricks-managed-*
