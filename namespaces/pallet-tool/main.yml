ownership:                           # not used at the moment by any terraform
  tribe: local-tech-and-data                       # just for the reference
  squad: nordic-ops-tech
environments:
  staging: {}
  live: {}

vault:                               # Vault configuration section
  auth_methods:                      # should be created.
    kubernetes:                      # Auth methods definition for the namespace: (kubernetes/github_actions/azure_oidc)
      namespaces: ["nordics"]
      service_accounts: ["pallet-tool*", "kafka-connect-nordics*"]
    azure_oidc:                      # - azure_oidc
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/pallet-tool-staging-role
        - arn:aws:iam::************:role/pallet-tool-live-role
