ownership:
 tribe: scm-analytics
 squad: scm-analytics-engineers

environments:
  dwh: {}
  dwh-staging: {}

vault:
 auth_methods:
    github_actions_jwt:
      additional_repositories:
        dsi-infra: {}
    kubernetes:
       namespaces: [ "scm*" ]
       service_accounts: [ "ds-scm-role" ]
    azure_oidc:
     az_group_owners:
     - mahesh.ka<PERSON>@hellofresh.com
     - <EMAIL>
     - <EMAIL>
     - <EMAIL>
     az_additional_groups:
     - acl_vault_scm_analytics_engineers
     - acl_github_hellofresh_global_ai_mls
     - acl_vault_scm_data_scientists
    aws:
      iam_principal_arns:
      - arn:aws:iam::************:role/scm-role
      - arn:aws:iam::************:role/global-ai-role
