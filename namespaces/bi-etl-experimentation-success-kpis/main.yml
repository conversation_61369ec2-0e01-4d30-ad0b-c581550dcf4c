ownership:
  tribe: adtech
  squad: experimentation-core

environments:
  dwh-spark-staging: {}
  dwh-spark-live: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_github_hellofresh_exp-squad
    kubernetes:
      namespaces: ["experimentation-core"]
      service_accounts: ["emr-containers-sa-spark-*"]
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/EMR_EC2_DefaultRole
        - arn:aws:iam::************:role/databricks-managed-experimentation-core-ds-ce
        - arn:aws:iam::************:role/hf-experimentation-core-emr-eks-staging
        - arn:aws:iam::************:role/hf-experimentation-core-emr-eks-live
