ownership:
  tribe: data-solutions
  squad: enablement

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        # The Azure group that was created here is tf-acc-vault-data-recommendation-adoption-admins
        # https://myaccount.microsoft.com/groups/79840a6c-8c73-42f9-90c6-6e5a3dc58410
        # I'm adding this comment because I get a little bit lost in both the myriad of azure groups
        # that I'm a member of (177 currently) and how access is granted to vault.
        # Hoping this a comment helps explain it a little to others and future me.
        - <EMAIL>
        - <EMAIL>
    kubernetes:
      namespaces: [ "data-solutions" ]
      service_accounts: [ "data-solutions-*" ]
