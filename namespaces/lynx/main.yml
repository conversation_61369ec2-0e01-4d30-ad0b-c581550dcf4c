ownership:
  tribe: anz-data
  squad: anz-marketing-automation

environments:
  au-staging: {}
  au-live: {}
  ahoy: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["au-marketing"]
      service_accounts: ["lynx-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_anz_data_marketing_automation
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/anz-ds-role
        - arn:aws:iam::************:role/databricks-managed-*
