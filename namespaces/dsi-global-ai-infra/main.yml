ownership:
 tribe: global-ai
 squad: ml-solutions

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    github_actions_jwt:
      additional_repositories:
        dsi-infra: {}
    kubernetes:
      namespaces: ["global-ai*"]
      service_accounts: ["global-ai*"]
    azure_oidc:
      az_group_owners:
        - louis.<PERSON><PERSON><EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_github_hellofresh_global_ai_mls
    aws:
     iam_principal_arns:
     - arn:aws:iam::************:role/global-ai-role
