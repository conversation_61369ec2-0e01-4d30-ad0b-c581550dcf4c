ownership:
  tribe: conversions-journey
  squad: customer-clarity

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["conversions"]
      service_accounts: ["reactivation-bff-api"]

    azure_oidc:
      az_group_owners:
        - css_iam_squad_customer_clarity
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-conversions-journey
        - css_iam_oncall_privesc_vault_tribe-conversions-us
        - css_iam_squad_dynamic-journey
