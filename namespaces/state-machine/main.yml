ownership:
  tribe: payments
  squad: acceptance

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["payments"]
      service_accounts: ["state-machine-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_payments_acceptance
        - css_iam_oncall_privesc_vault_tribe-payments
        - css_iam_oncall_tribe-payments
        - css_iam_oncall_tribe-payments-2
        - acl_github_hellofresh_payments-backend
