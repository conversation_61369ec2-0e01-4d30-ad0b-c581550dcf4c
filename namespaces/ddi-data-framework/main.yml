ownership:
  tribe: adtech
  squad: commsdata

environments:
  live: {}
  staging: {}

vault:
  root_namespace: infrastructure
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-conversions-squad-adtech
    kubernetes:
      enabled: false
    aws:
      iam_principal_arns:
        - arn:aws:iam::985437859871:role/EMR_EC2_DefaultRole
        - arn:aws:iam::985437859871:role/adtech-ddi-pipelines-pii-staging
        - arn:aws:iam::985437859871:role/adtech-ddi-pipelines-pii-live
        - arn:aws:iam::985437859871:role/adtech-commsdata-pipelines-live
        - arn:aws:iam::985437859871:role/adtech-commsdata-pipelines-staging
        - arn:aws:iam::985437859871:role/adtech-commstech-pipelines-live
