ownership:
  tribe: conversions
  squad: vouchers

environments:
  staging: {}
  live: {}
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"] # This is a special use case for ahoy to use * wildcard for namespace since ahoy spins up dynamic namespaces

vault:
  auth_methods:
    kubernetes:
      namespaces: ["conversions", "cust-benefits"]
      service_accounts:
        - vsc-*
        - voucher-*
        - vouchers-*
        - kafka-connect-voucher-service-connect
        - kafka-connect-voucher-service-connect-build
        - kafka-connect-vouchers-connect
        - kafka-connect-vouchers-connect-build
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_staffengineer_customer_benefits
        - css_iam_oncall_privesc_vault_tribe-customer-benefits
        - css_iam_tech_squad_pricing_experience
