ownership:
  tribe: data-platform
  squad: data-infrastructure-and-operations
  partition:
    - foundations

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: [ "streamlit-onboarding" ]
      service_accounts: [ "streamlit-onboarding-*" ]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
    github_actions_jwt:
      additional_repositories:
        streamlit-iam-automation:
          allow_write: true  # To allow copy Azure Vault secrets for service namespace
    jwt_kubernetes:
      kubernetes_identities:
        streamlit-onboarding:
          - streamlit-onboarding-vso
        proc-sku-catalog:
          - proc-sku-catalog-vso
        streamlit-helloworld:
          - streamlit-helloworld-vso          
