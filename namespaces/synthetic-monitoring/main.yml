ownership:
  tribe: platform
  squad: site-reliability

environments:
  ahoy: {}
  staging: {}
  au-staging: {}
  live: {}
  au-live: {}
  dwh: {}
  dwh-staging: {}
  dwh-spark-live: {}
  dwh-spark-staging: {}
  tools: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces:
      - "synthetic-monitoring"
      - "vault-infra"
      service_accounts:
      - "synthetic-monitoring"
      - "canary-checker-sa"
      - "canary-checker-ui"
      - "vault-secrets-webhook"
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - usr_tribe_platform
