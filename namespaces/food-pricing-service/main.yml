ownership:
  tribe: consumer-core
  squad: squad-pricing

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["checkout-experience", "consumer-core"]
      service_accounts: ["food-pricing-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_staffengineer_customer_benefits
        - css_iam_oncall_privesc_vault_food-alliance-checkout-experience-tribe-rotation
        - css_iam_oncall_privesc_vault_food-alliance-shopping-experience-tribe-rotation
