ownership:
  tribe: checkout-experience
  squad: cart-foundation

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["market", "keda"]
      service_accounts:
        - mas-selections-streamer-*
        - kafka-connect-mas-selections-streamer-connect
        - kafka-connect-mas-selections-streamer-connect-build
        - keda-operator
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_food-alliance-checkout-experience-tribe-rotation
        - css_iam_oncall_privesc_vault_food-alliance-shopping-experience-tribe-rotation
