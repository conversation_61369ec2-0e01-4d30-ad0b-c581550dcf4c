ownership:
  tribe: global-bi
  squad: core-analytics

environments:
  dwh-staging: {}
  dwh: {}
  staging: {}
  live: {}

vault:
  root_namespace: services
  auth_methods:
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      - <EMAIL>
      az_additional_groups:
      - acl_vault_gbi
      - css_iam_squad_data_infrastructure_and_operations
    github_actions_jwt:
      additional_repositories:
        hf-analytics: {}
        gbi-oracle-ledger: {}
        gbi-pc2-reliance: {}
        gbi-oracle-bicc: {}
    kubernetes:
      namespaces: [ "gbi", "global-bi" ]
      service_accounts:
      - gbi-*
      - airflow-*
      - kafka-connect-global-bi-rabbitmq-connect
      - kafka-connect-global-bi-rabbitmq-connect-build
      max_lease_ttl: 24h
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/EMR_EC2_DefaultRole
        - arn:aws:iam::************:role/airflow-global-bi-staging-pod-role # TO DEPRECATE
        - arn:aws:iam::************:role/airflow-global-bi-live-pod-role # TO DEPRECATE
        - arn:aws:iam::************:role/airflow-global-bi-staging-pod-irsa-role
        - arn:aws:iam::************:role/airflow-global-bi-live-pod-irsa-role
        - arn:aws:iam::************:role/global-bi-ec2-emr-role-staging
        - arn:aws:iam::************:role/global-bi-ec2-emr-role-live
        - arn:aws:iam::************:role/hf-ffdp-shared-emr-eks-staging
        - arn:aws:iam::************:role/hf-ffdp-shared-emr-eks-live
        - arn:aws:iam::************:role/hf-ffdp-finance-emr-eks-staging
        - arn:aws:iam::************:role/hf-ffdp-finance-emr-eks-live
        - arn:aws:iam::************:role/hf-ffdp-core-emr-eks-staging
        - arn:aws:iam::************:role/hf-ffdp-core-emr-eks-live
        - arn:aws:iam::************:role/hf-ffdp-assortment-emr-eks-staging
        - arn:aws:iam::************:role/hf-ffdp-assortment-emr-eks-live
        
