ownership:
  tribe: local-tech-and-data
  squad: bnl

environments:
  dwh: {}
  dwh-staging: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: [ "bnl-logistics-analytics" ]
      service_accounts: [ "bnl-logistics-analytics-*" ]
    azure_oidc:
      az_group_owners:
        - audrya.kere<PERSON><PERSON><PERSON><EMAIL>
        - <EMAIL>
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/databricks-managed-bnl_logistics_analytics-ds-ce
