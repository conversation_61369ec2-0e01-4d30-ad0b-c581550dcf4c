ownership:
  tribe: customer-care-na
  squad: shared

environments: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_customer_care_us

    kubernetes:
      enabled: false
    aws:
      iam_principal_arns:
        - arn:aws:iam::794016200254:role/databricks-managed-us-customer-care-ds-ce
        - arn:aws:iam::794016200254:role/databricks-managed-us-customer-care-sensitive-ds-ce
        - arn:aws:iam::794016200254:role/databricks-managed-actives_analytics-ds-ce
