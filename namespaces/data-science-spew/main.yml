# Sample manifest for onboarding repository to vault enterprise
ownership:
  tribe: marketing-analytics-us
  squad: data-science-us
vault:
  root_namespace: infrastructure
  auth_methods:
    kubernetes:
      enabled: false
    azure_oidc:
      az_group_owners:
      - ram<PERSON>.ghan<PERSON><PERSON>@hellofresh.com
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      az_additional_groups:
      - css_iam_tribe_data_platform
    aws:
      iam_principal_arns:
      - arn:aws:iam::794016200254:role/us-ma-role
      - arn:aws:iam::794016200254:role/databricks-managed-*
      - arn:aws:iam::794016200254:role/databricks-managed-pii_access-us-ma
environments: 
  live: {}
  staging: {}
