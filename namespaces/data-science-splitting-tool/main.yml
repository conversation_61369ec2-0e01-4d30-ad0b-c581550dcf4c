ownership:
 tribe: scm-analytics
 squad: scm-analytics-engineers

environments:
  live: {}
  staging: {}
  ahoy: {}
  dwh-staging: {}
  dwh: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["data-science","isa","isa-live"]
      service_accounts: ["data-science-*","isa-*"]
    azure_oidc:
      az_group_owners:
        - mahesh.ka<PERSON>@hellofresh.com
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_vault_scm_analytics_engineers
        - acl_vault_scm_data_scientists
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/isa-airflow-live-workers-irsa-role
        - arn:aws:iam::************:role/isa-airflow-staging-workers-irsa-role
        - arn:aws:iam::************:role/scm-role
        - arn:aws:iam::************:user/d4-apps
