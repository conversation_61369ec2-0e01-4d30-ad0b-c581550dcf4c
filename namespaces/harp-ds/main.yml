ownership:
  tribe: checkout-experience
  squad: personalization

environments:
  tools:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
        service_accounts: ["*"]
  dwh: {}
  dwh-staging: {}
  live: {}
  staging: {}

vault:
  root_namespace: infrastructure
  auth_methods:
    kubernetes:
      namespaces: ["product-analytics", "pa*"]
      service_accounts: ["ds-pa-role"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_food_science
        - css_iam_oncall_privesc_vault_food-alliance-customer-facing-systems-rotation
        - css_iam_oncall_privesc_vault_food-alliance-internal-systems-rotation
    aws:
      iam_principal_arns:
        # databricks AWS role
        - arn:aws:iam::************:role/pa-role
        # github runner AWS role to allow unit tests to connect to vault
        - "arn:aws:iam::************:role/github-actions-*" # tools
        # github runner AWS role to allow deployment to Prefect
        - "arn:aws:iam::************:role/github-actions-runner" # tools
