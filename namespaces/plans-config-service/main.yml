ownership:
  tribe: conversions-journey
  squad: checkout

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["conversions"]
      service_accounts: ["plans-config-service-api"]

    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - css_iam_squad_checkout
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-conversions-journey
        - css_iam_oncall_privesc_vault_tribe-conversions-us
        - css_iam_oncall_privesc_vault_frontend-infrastructure
