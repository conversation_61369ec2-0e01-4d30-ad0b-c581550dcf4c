ownership:                           # not used at the moment by any terraform
  tribe: local-tech-and-data                       # just for the reference
  squad: nordic-ops-tech

environments:
  ahoy: {}
  dwh: {}
  dwh-staging: {}

vault:                               # Vault configuration section
  auth_methods:                      # should be created.
    kubernetes:                      # Auth methods definition for the namespace:
      namespaces: ["nordics-opstech"]
      service_accounts: ["nordics-ops-app-*"]
    azure_oidc:                      # - azure_oidc
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/nordics-ds-ce-role
        - arn:aws:iam::************:role/databricks-managed-nordics-analytics-ds-ce
