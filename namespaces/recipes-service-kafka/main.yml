ownership:
  tribe: shopping-experience
  squad: shopping-platform

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["menu", "shopping-experience"]
      service_accounts: ["recipes-service-kafka-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_supply_planning_integrations
        - css_iam_sourcing_data_management
        - css_iam_squad_product_catalogue
        - css_iam_oncall_privesc_vault_food-alliance-checkout-experience-tribe-rotation
        - css_iam_oncall_privesc_vault_food-alliance-shopping-experience-tribe-rotation
