ownership:
  tribe: customer-benefits
  squad: vouchers

environments:
  staging: {}
  live: {}
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"] # This is a special use case for ahoy to use * wildcard for namespace since ahoy spins up dynamic namespaces

vault:
  auth_methods:
    kubernetes:
      namespaces: ["cust-benefits"]
      service_accounts: ["benefit-distribution*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_staffengineer_customer_benefits
        - css_iam_oncall_privesc_vault_tribe-customer-benefits
