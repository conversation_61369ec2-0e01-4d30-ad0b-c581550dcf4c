ownership:
  tribe: labor-management
  squad: labor-tracking-and-planning

environments:
  staging: {}
  live: {}

vault:
  auth_methods:
    azure_oidc:
      enabled: true
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
          - scm-na-lion-vault
    kubernetes:
      enabled: true
      namespaces: ["scm-scan2job"]
      service_accounts: ["kafka-connect-scm-scan2job-connect", "kafka-connect-scm-scan2job-connect-build"]
