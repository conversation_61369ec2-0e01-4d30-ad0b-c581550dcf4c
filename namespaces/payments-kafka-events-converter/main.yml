ownership:
  tribe: payments
  squad: risk_and_revenue_management

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["payments"]
      service_accounts: ["payments-kafka-events-converter-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_payments_risk_and_revenue_management
        - css_iam_oncall_privesc_vault_tribe-payments
        - css_iam_oncall_tribe-payments
        - css_iam_oncall_tribe-payments-2
        - acl_github_hellofresh_payments-backend
