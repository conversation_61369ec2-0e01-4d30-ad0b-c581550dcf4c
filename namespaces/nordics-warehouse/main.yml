ownership:                           # not used at the moment by any terraform
  tribe: local-tech-and-data                       # just for the reference
  squad: nordic-ops-tech

environments:
  staging: {}
  live: {}

vault:                               # Vault configuration section
  auth_methods:                      # should be created.
    kubernetes:                      # Auth methods definition for the namespace:
      enabled: false
    azure_oidc:                      # - azure_oidc
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
    aws:
      iam_principal_arns:
        - arn:aws:iam::794016200254:role/nordics-ds-ce-role
        - arn:aws:iam::794016200254:role/databricks-managed-nordics-analytics-ds-ce
