ownership:
  tribe: productivity-platform
  squad: mobile-foundations

environments: {}

vault:
  auth_methods:
    kubernetes:
      enabled: false
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - mejdeddine.ben<PERSON><PERSON>@hellofresh.com
      az_additional_groups:
        - acl_github_hellofresh_squad_ios_foundation
        - acl_github_hellofresh_squad-android-foundation
        - css_iam_oncall_privesc_vault_tribe-conversions-journey
        - css_iam_oncall_privesc_vault_tribe-conversions-us
        - css_iam_oncall_privesc_vault_frontend-infrastructure
        - acl_github_helofresh_squad-delivery-satisfaction-android
        - acl_github_hellofresh_payments-android
        - acl_github_hellofresh_squad-cart-experience-android
        - acl_github_hellofresh_squad-early-retention
        - acl_github_hellofresh_squad-loyalty-program-android
        - Squad-loyalty-journey-android
        - acl_github_hellofresh_squad-reactivations-eng-android
        - acl_github_hellofresh_squad-welcome-experience
        - acl_github_hellofresh_tribe-conversions-journey-android
        - acl_github_hellofresh_tribe_customer_benefits_android
        - acl_github_hellofresh_tribe-habit-building-android
        - acl_github_hellofresh_food-android
        - acl_github_hellofresh_virality-android
        - acl_github_hellofresh_zest-android
        - acl_github_hellofresh_mobile_devops
        - acl_github_hellofresh_commstech
        - acl_github_hellofresh_crm_engine
        - css_iam_squad_shared_mobile_modules
    github_actions_jwt:
      additional_repositories:
        ios: {}
        mobile-automation: {}
