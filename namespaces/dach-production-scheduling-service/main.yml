ownership:
  tribe: local-tech-and-data
  squad: ops-tech-dach

environments:
  staging: {}
  live: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["dach-tech"]
      service_accounts: ["dach-production-scheduling-service-*"]
    azure_oidc:
      az_group_owners:
        - mayko<PERSON>.sa<PERSON><PERSON>@hellofresh.de
        - rahul.sasi<PERSON><EMAIL>
        - kseniia.k<PERSON><PERSON><PERSON><PERSON>@hellofresh.de
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - acl_vault_dach_tech

