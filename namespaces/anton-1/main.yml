ownership:
  tribe: reliability-platform
  squad: cloud-runtime

environments:
  staging: {}

vault:
  auth_methods:
    azure_oidc:
      az_group_owners:
      - <EMAIL>
    github_actions_jwt:
      additional_repositories:
        vault-namespace-automation: {}
    kubernetes:
      namespaces: ["default"]
      service_accounts: ["default"]
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/vault-*
