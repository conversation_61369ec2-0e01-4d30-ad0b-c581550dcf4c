ownership:
  tribe: order-management
  squad: global-order-management

environments:
  ahoy: {}
  au-staging: {}
  staging: {}
  live: {}
  dwh: {}
  dwh-staging: {}

vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["au-ops", "scm", "data-platform"]
      service_accounts: ["katana-data-ingestion-*", "kafka-connect-*", "kdi-precutoff-*", "kdi-experiment-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_global_order_management
        - css_iam_squad_anz_ops_excellence
