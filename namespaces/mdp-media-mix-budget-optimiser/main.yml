ownership:
  tribe: marketing-data-product
  squad: marketing-science

environments:
  live:
    auth_methods:
      kubernetes:
        namespaces: ["marketing-analytics", "marketing-data-product-live"]
        service_accounts: ["marketing-*", "kafka-connect-*"]

  staging:
    auth_methods:
      kubernetes:
        namespaces: ["marketing-analytics", "marketing-data-product"]
        service_accounts: ["marketing-*", "kafka-connect-*"]

  dwh:
    auth_methods:
      kubernetes:
        namespaces: ["marketing-analytics-live", "marketing-analytics-staging", "marketing-data-product"]
        service_accounts: ["airflow-*", "mdp-airflow-*"]

  dwh-staging:
    auth_methods:
      kubernetes:
        namespaces: ["marketing-data-product"]
        service_accounts: ["airflow-*", "mdp-airflow-*"]

vault:
  auth_methods:
    kubernetes: {}
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_tribe_marketing_data_product
        - css_iam_squad_marketing_processes_and_interfaces
