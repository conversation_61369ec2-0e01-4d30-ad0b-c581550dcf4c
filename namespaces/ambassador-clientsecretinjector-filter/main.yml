ownership:
  tribe: reliability-platform
  squad: cloud-runtime

environments:
  ahoy: {}
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: [ "platform" ]
      service_accounts: [ "ambassador-clientsecretinjector-filter" ]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - usr_tribe_platform
        - css_iam_oncall_privesc_vault_tribe-platform

