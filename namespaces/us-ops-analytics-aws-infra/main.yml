ownership:
  tribe: us-ops-analytics
  squad: data-management-and-bi
vault:
  root_namespace: services
  auth_methods:
    kubernetes:
      namespaces: ["us-ops-analytics"]
      service_accounts: [ "airflow-*", "us-*"]
    azure_oidc:
      az_group_owners:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>

      az_additional_groups:
      - css_iam_tribe_data_platform
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/usoa_lambda_test_role
        - arn:aws:iam::************:role/us-ops-blujay-lambda-execution-role
environments:  
  tools: {}
  live: {}
  staging: {}
  ahoy: {}
  dwh-staging: {}
  dwh: {}

