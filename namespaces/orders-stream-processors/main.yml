ownership:
  tribe: consumer-core
  squad: customer-orders

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["consumer-core", "keda"]
      service_accounts: ["osp-*", "kafka-connect-orders-stream-processors-*", "keda-operator"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:
        - css_iam_oncall_privesc_vault_tribe-consumer-core

