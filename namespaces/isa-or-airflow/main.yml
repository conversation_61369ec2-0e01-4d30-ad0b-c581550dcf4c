ownership:
 tribe: scm-analytics
 squad: scm-analytics-engineers

environments:
  dwh-staging: {}
  dwh: {}

vault:
  root_namespace: services
  auth_methods:
    github_actions_jwt:
      additional_repositories:
        isa-or-orchestration: {}
    azure_oidc:
      az_group_owners:
      - mahesh.ka<PERSON>@hellofresh.com
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      az_additional_groups:
      - acl_vault_scm_analytics_engineers
      - acl_vault_scm_data_scientists
      - acl_vault_scm_or
      - css_iam_squad_data_infrastructure_and_operations
      - acl_vault_dach_tech
    kubernetes:
      namespaces: [ "isa-or","isa-or"]
      service_accounts: [ "isa-*" ]
      max_lease_ttl: 730h
    aws:
      iam_principal_arns:
        - arn:aws:iam::************:role/EMR_EC2_DefaultRole
        - arn:aws:iam::************:role/isa-or-airflow-live-workers-irsa-role
        - arn:aws:iam::************:role/isa-or-airflow-staging-workers-irsa-role
