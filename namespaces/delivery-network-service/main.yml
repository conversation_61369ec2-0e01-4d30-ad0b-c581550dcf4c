ownership:
  tribe: logistics
  squad: delivery-network

environments:
  ahoy:
    auth_methods:
      kubernetes:
        namespaces: ["*"]
  staging: {}
  live: {}

vault:
  auth_methods:
    kubernetes:
      namespaces: ["scm"]
      service_accounts: ["delivery-network-service-*"]
    azure_oidc:
      az_group_owners:
        - <EMAIL>
      az_additional_groups:
        - css_iam_squad_delivery_network
        - css_iam_tribe_staffengineer_logistics
        - css_iam_oncall_privesc_vault_tribe-scm-squad-delivery-network
