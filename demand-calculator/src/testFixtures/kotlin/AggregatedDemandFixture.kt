@file:Suppress("MagicNumber")

package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand

import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import java.time.LocalDate
import java.util.UUID
import kotlin.random.Random

fun AggregatedDemand.Companion.random() =
    with(Random(System.nanoTime())) {
        AggregatedDemand(
            sku = Sku(id = UUID.randomUUID(), code = UUID.randomUUID().toString()),
            parentSku = null,
            dcCode = "VE",
            productionDate = LocalDate.now().plusDays(nextLong(100)),
            recipeBreakdowns = listOf(RecipeBreakdownLine.random(this)),
        )
    }

fun AggregatedDemand.Companion.default() =
    AggregatedDemand(
        sku = Sku(id = UUID(0, 0), code = "sku_code"),
        parentSku = null,
        dcCode = "VE",
        productionDate = LocalDate.now(),
        recipeBreakdowns = listOf(RecipeBreakdownLine.default()),
    )

fun RecipeBreakdownLine.Companion.default(recipeIndex: Int = 100, qty: Long = 1000, demandType: DemandType? = null) =
    RecipeBreakdownLine(recipeIndex, "country", "locale", "prodFamily", "brand", 2, 1, qty, demandType = demandType)

fun RecipeBreakdownLine.Companion.random(random: Random = Random(System.nanoTime())) =
    default().copy(
        recipeIndex = random.nextInt(100),
        qty = random.nextLong(1000),
    )
