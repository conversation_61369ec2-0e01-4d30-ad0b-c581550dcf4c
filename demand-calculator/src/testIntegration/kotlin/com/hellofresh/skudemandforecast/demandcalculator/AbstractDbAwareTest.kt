package com.hellofresh.skudemandforecast.demandcalculator

import KafkaInfraPreparation
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.kafka.serde.deserializer
import com.hellofresh.kafka.serde.serializer
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.model.substitution.DemandByDate
import com.hellofresh.skuDemandForecast.model.substitution.RecipeDetail
import com.hellofresh.skuDemandForecast.model.substitution.RecipeDetailsDbValue
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSkuInDemand
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSkuOutDemand
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSubstitutionDetail
import com.hellofresh.skuDemandForecast.model.substitution.SkuDemand
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku
import com.hellofresh.skudemandforecast.demandcalculator.message.SKU_DEMAND_FORECAST_TOPIC_NAME
import com.hellofresh.skudemandforecast.demandcalculator.schema.outbox.Tables
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.FractionSkuDemandRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.RecipeSkuSubstitutionRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSubstitutionRecord
import com.hellofresh.skudemandforecast.demandcalculator.substitution.Substitution
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.distributioncenter.default
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import javax.sql.DataSource
import org.apache.kafka.clients.admin.NewTopic
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.KafkaProducer
import org.flywaydb.core.Flyway
import org.jooq.JSONB
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeAll
import org.postgresql.Driver
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName

private const val ARTIFACTORY_PROXY = "repo.tools-k8s.hellofresh.io"
private const val POSTGRES_VERSION = "15.2-alpine"

@SuppressWarnings("UnnecessaryAbstractClass")
@Testcontainers
abstract class AbstractDbAwareTest internal constructor() {

    val defaultDistributionCenter = DistributionCenter.Companion.default()
    val defaultDcCode = defaultDistributionCenter.dcCode
    val defaultMarket = defaultDistributionCenter.market

    @Suppress("LongParameterList")
    fun persistDemand(
        sku: UUID,
        recipeIndex: Int,
        qty: Long,
        date: LocalDate? = LocalDate.now().plusDays(7),
        dcCodeParam: String = defaultDcCode,
        countryParam: String = "DE",
        recipeDetailsParam: List<RecipeDetail>? = null,
    ): FractionSkuDemandRecord = persistNewFractionDemand(
        sku,
        recipeIndex,
        qty,
        date,
        dcCodeParam,
        countryParam,
        recipeDetailsParam,
    )

    fun persistDemand(
        skuId: UUID?,
        dcCode: String,
        productionDate: LocalDate,
        recipeBreakdownLines: List<RecipeBreakdownLine>
    ): List<FractionSkuDemandRecord> = persistFractionSkuDemandRecord(
        skuId!!,
        productionDate,
        dcCode,
        recipeBreakdownLines = recipeBreakdownLines,
        qtyParam = null,
    )

    fun persistSkuSpecificationRecord(
        skus: List<Sku>,
        parentSku: Sku? = null,
        market: String = defaultMarket
    ): List<SkuSpecificationRecord> {
        val records = skus
            .map { (id, code) ->
                SkuSpecificationRecord(
                    id,
                    parentSku?.id,
                    code,
                    LocalDateTime.now(),
                    null,
                    market,
                    null,
                    "packaging"
                )
            }
            .toMutableList()

        if (parentSku != null) {
            records.add(
                SkuSpecificationRecord(
                    parentSku.id,
                    null,
                    parentSku.code,
                    LocalDateTime.now(),
                    null,
                    market,
                    null,
                    "packaging"
                ),
            )
        }

        dsl.batchMerge(records).execute()
        return records
    }

    fun persistDistributionCenter(code: String = defaultDcCode, globalDc: String? = null, market: String? = defaultMarket) =
        DcConfigRecord()
            .apply {
                dcCode = code
                productionStart = MONDAY.name
                this.market = market
                zoneId = ZoneOffset.UTC.id
                enabled = true
                this.globalDc = globalDc
            }.also {
                dsl.batchInsert(it).execute()
            }

    @Suppress("LongParameterList")
    fun persistNewFractionDemand(
        skuId: UUID,
        recipeIndex: Int,
        qty: Long,
        date: LocalDate? = LocalDate.now().plusDays(7),
        dcCode: String = defaultDcCode,
        country: String = "DE",
        recipeDetailsParam: List<RecipeDetail>? = null,
    ) =
        FractionSkuDemandRecord(
            skuId,
            dcCode,
            date,
            country,
            "",
            "pfam",
            recipeIndex,
            qty,
            recipeDetailsParam?.let {
                JSONB.valueOf(
                    objectMapper.writeValueAsString(RecipeDetailsDbValue(recipeDetailsParam)),
                )
            },
            listOf(UUID.randomUUID()).toTypedArray(),
            LocalDateTime.now(),
            LocalDateTime.now(),
        ).also {
            dsl.batchInsert(it).execute()
        }

    @Suppress("LongParameterList")
    fun persistFractionSkuDemandRecord(
        skuIdParam: UUID = UUID.randomUUID(),
        demandDateParam: LocalDate = LocalDate.now(),
        dcCodeParam: String = "VE",
        qtyParam: Long? = 10,
        recipeBreakdownLines: List<RecipeBreakdownLine>,
    ): List<FractionSkuDemandRecord> {
        val newFractionDemands = recipeBreakdownLines
            .groupBy { it.recipeIndex to it.country to it.locale to it.productFamily }
            .map { (_, values) ->
                FractionSkuDemandRecord().apply {
                    skuId = skuIdParam
                    demandDate = demandDateParam
                    dcCode = dcCodeParam
                    country = values.first().country
                    locale = values.first().locale
                    recipeIndex = values.first().recipeIndex
                    productFamily = values.first().productFamily
                    qty = qtyParam ?: values.sumOf { it.qty }
                    sourceIds = listOf(UUID.randomUUID()).toTypedArray()
                    recipeDetails =
                        values.mapNotNull {
                            if (it.brand != null && it.peopleCount != null && it.picks != null) {
                                RecipeDetail(it.brand!!, it.peopleCount!!, it.picks!!, it.qty, it.demandType)
                            } else {
                                null
                            }
                        }.let {
                            if (it.isNotEmpty()) {
                                JSONB.valueOf(objectMapper.writeValueAsString(RecipeDetailsDbValue(it)))
                            } else {
                                JSONB.valueOf(null)
                            }
                        }
                    createdAt = LocalDateTime.now()
                    updatedAt = LocalDateTime.now()
                }
            }
        dsl.batchInsert(newFractionDemands).execute()
        return newFractionDemands
    }

    @Suppress("LongParameterList")
    fun checkDbResult(
        skuId: UUID,
        dcCode: String,
        demandDate: LocalDate,
        skuSpecRecord: SkuSpecificationRecord,
        recipes: List<RecipeBreakdownLine>,
        aggregateDemandResult: AggregatedDemand,
    ) {
        Assertions.assertEquals(skuId, aggregateDemandResult.sku.id)
        Assertions.assertEquals(dcCode, aggregateDemandResult.dcCode)
        Assertions.assertEquals(demandDate, aggregateDemandResult.productionDate)
        if (aggregateDemandResult.parentSku != null) {
            Assertions.assertEquals(skuSpecRecord.parentId, aggregateDemandResult.parentSku!!.id)
        }
        Assertions.assertEquals(skuSpecRecord.code, aggregateDemandResult.sku.code)
        Assertions.assertEquals(recipes.toSet(), aggregateDemandResult.recipeBreakdowns.toSet())
        Assertions.assertEquals(
            aggregateDemandResult.totalRecipeBreakDownQty(),
            aggregateDemandResult.recipeBreakdowns.sumOf { it.qty },
        )
    }

    fun persistSubstitutionRecord(substitution: Substitution) {
        SkuSubstitutionRecord().apply {
            id = substitution.id
            version = 0
            dcCode = substitution.dcCode
            fromDate = substitution.date.minusDays(2)
            toDate = substitution.date.plusDays(2)
            skuIdOut = substitution.skuOut.id
            authorName = "test"
            authorEmail = "test"
            value = JSONB.valueOf(
                objectMapper.writeValueAsString(
                    SubstitutionDetail(
                        skuOut = SkuDemand(
                            skuId = substitution.skuOut.id,
                            demand = listOf(DemandByDate(substitution.date, substitution.qtyOut?.toInt(), fullDay = substitution.fullDayOut)),
                            picks = substitution.picksOut,
                        ),
                        skuIn = substitution.skuIn.map {
                            SkuDemand(
                                skuId = it.key.id,
                                demand = listOf(DemandByDate(substitution.date, it.value.qty?.toInt(), it.value.fullDay)),
                                picks = it.value.picks,
                            )
                        },
                    ),
                ),
            )
            reference = "test"
            disabled = false
            sourceId = substitution.sourceId
        }.let { dsl.batchInsert(it).execute() }
    }

    @Suppress("LongParameterList")
    fun recipeSkuSubstitution(
        recipeSkuIndex: Int = 1000,
        numberOfSkuIns: Int = 1,
        fromDateParam: LocalDate = LocalDate.now(),
        brandParam: String = "HF",
        countryParam: String = "AU",
        productionDateParam: LocalDate = LocalDate.now(),
        picksOut: Int = 1,
        picksIn: Int = 1,
    ) = RecipeSkuSubstitutionRecord().apply {
        recipeIndex = recipeSkuIndex
        week = "W13"
        country = countryParam
        brand = brandParam
        dcCode = "SY"
        peopleCount = 2
        fromDate = fromDateParam
        toDate = LocalDate.now()
        value = JSONB.valueOf(
            objectMapper.writeValueAsString(
                listOf(
                    RecipeSubstitutionDetail(
                        skuOut = RecipeSkuOutDemand(
                            skuId = UUID.randomUUID(),
                            skuCode = "PHF-10-00707-5",
                            parentSkuId = UUID.randomUUID(),
                            parentSkuCode = "PHF-10-00707-15",
                            fullDay = true,
                            picks = picksOut,
                        ),
                        skuIn = (0..numberOfSkuIns).map { numberOfSkuIn ->
                            RecipeSkuInDemand(
                                skuId = UUID.randomUUID(),
                                skuCode = "PHF-10-00707-$numberOfSkuIn",
                                parentSkuId = UUID.randomUUID(),
                                parentSkuCode = "PHF-10-00709-25",
                                date = productionDateParam,
                                fullDay = true,
                                picks = picksIn,
                            )
                        },
                    ),
                ),
            ),
        )
    }

    fun createAggregatedDemand(
        recipeSkuOut: RecipeSkuOutDemand,
        dcCode: String = "SY",
        productionDate: LocalDate = LocalDate.now(),
        recipeIndexParam: Int = 1000,
    ): AggregatedDemand {
        val recipeBreakdownLine = RecipeBreakdownLine(recipeIndexParam, "AU", "", "PRODUCT_FAMILY", "HF", 2, 1, 10)
        val aggregatedDemand = AggregatedDemand(
            sku = Sku(id = recipeSkuOut.skuId, code = recipeSkuOut.skuCode),
            parentSku = null,
            dcCode = dcCode,
            productionDate = productionDate,
            recipeBreakdowns = listOf(recipeBreakdownLine),
        )
        return aggregatedDemand
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var dataSource: DataSource
        const val DEFAULT_DC_CODE = "BV"
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
        private var postgresImage = DockerImageName
            .parse("$ARTIFACTORY_PROXY/postgres:$POSTGRES_VERSION")
            .asCompatibleSubstituteFor("postgres")

        @Container
        private val postgres = PostgreSQLContainer(
            postgresImage,
        ).withDatabaseName("outbox")

        lateinit var kafkaProducer:
            KafkaProducer<SkuDemandForecastKey, SkuDemandForecastVal>
        lateinit var expectedSkuDemandForecastTopicConsumer:
            KafkaConsumer<SkuDemandForecastKey, SkuDemandForecastVal>

        @BeforeAll
        @JvmStatic
        fun initDb() {
            createDataSource()
            migrateDatabase()
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                },
            ).withMetrics(SimpleMeterRegistry())
            dsl.ddl(Tables.OUTBOX_MESSAGE_KAFKA).find { it.sql.startsWith("create table") }?.execute()

            initKafka()
        }

        private fun createDataSource() {
            val config =
                HikariConfig()
                    .also {
                        it.jdbcUrl = postgres.jdbcUrl
                        it.username = postgres.username
                        it.password = postgres.password
                        it.driverClassName = Driver::class.java.name
                    }
            dataSource = HikariDataSource(config)
        }

        private fun migrateDatabase() {
            val flyway = Flyway.configure()
                .locations("classpath:/db/migration/schema")
                .dataSource(dataSource)
                .placeholders(
                    mapOf(
                        "DB_USERNAME" to "sdf",
                        "DB_OUTBOX_USERNAME" to "outbox",
                        "DB_OUTBOX_PASSWORD" to "'123456'",
                    ),
                )
                .load()

            val result = flyway.migrate()
            Assertions.assertTrue(result.success, "fail to migrate!")
        }

        private fun initKafka() {
            KafkaInfraPreparation.startKafkaAndCreateTopics(
                listOf(NewTopic(SKU_DEMAND_FORECAST_TOPIC_NAME, 6, 1)),
            )

            expectedSkuDemandForecastTopicConsumer = KafkaInfraPreparation.createKafkaConsumer(
                "randomSQRJobTest",
                SKU_DEMAND_FORECAST_TOPIC_NAME,
                deserializer<SkuDemandForecastKey>(),
                deserializer<SkuDemandForecastVal>(),
            )

            kafkaProducer = KafkaInfraPreparation.createKafkaProducer(
                serializer<SkuDemandForecastKey>(),
                serializer<SkuDemandForecastVal>(),
            )
        }
    }
}
