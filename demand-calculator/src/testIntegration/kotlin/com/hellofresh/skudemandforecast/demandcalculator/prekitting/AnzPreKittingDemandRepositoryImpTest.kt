package com.hellofresh.skudemandforecast.demandcalculator.prekitting

import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.AnzPrekittingDemandRecord
import com.hellofresh.skudemandforecast.demandcalculator.AbstractDbAwareTest
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.ANZ_PREKITTING_DEMAND
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class AnzPreKittingDemandRepositoryImpTest : AbstractDbAwareTest() {
    private var repository = AnzPreKittingDemandRepositoryImp(dsl)
    private val week1 = "2024-W10"

    @AfterEach
    fun cleanupDb() {
        dsl.deleteFrom(ANZ_PREKITTING_DEMAND).execute()
    }

    @Test
    fun `get preKitting demand should return a list with preKettingDemandDto`() {
        val date = LocalDate.now()
        runBlocking {
            val anzPrekittingDemandRecord1 = AnzPrekittingDemandRecord().apply {
                dcCode = "VE"
                skuId = UUID.randomUUID()
                week = week1
                demandQuantity = 10
                demandDate = date
            }
            val anzPrekittingDemandRecord2 = AnzPrekittingDemandRecord().apply {
                dcCode = anzPrekittingDemandRecord1.dcCode
                skuId = UUID.randomUUID()
                week = week1
                demandQuantity = 10
                demandDate = date.plusWeeks(1)
            }

            dsl.batchInsert(anzPrekittingDemandRecord1, anzPrekittingDemandRecord2).execute()

            val expected = setOf(
                AnzPreKittingDemand(
                    "VE",
                    anzPrekittingDemandRecord1.skuId,
                    anzPrekittingDemandRecord1.demandQuantity,
                    anzPrekittingDemandRecord1.demandDate
                ),
                AnzPreKittingDemand(
                    "VE",
                    anzPrekittingDemandRecord2.skuId,
                    anzPrekittingDemandRecord2.demandQuantity,
                    anzPrekittingDemandRecord2.demandDate
                ),
            )

            val result = repository.getPreKittingDemand(setOf(anzPrekittingDemandRecord1.dcCode), date)

            assertEquals(expected, result.toSet())
        }
    }

    @Test
    fun `get preKitting demand should return an empty list when there's no matching records`() {
        val date = LocalDate.now()
        runBlocking {
            AnzPrekittingDemandRecord().apply {
                dcCode = "VE"
                skuId = UUID.randomUUID()
                week = week1
                demandQuantity = 10
                demandDate = date
            }.also {
                dsl.batchInsert(it).execute()
            }

            val result = repository.getPreKittingDemand(setOf("HH"), date)

            assertTrue { result.isEmpty() }
        }
    }
}
