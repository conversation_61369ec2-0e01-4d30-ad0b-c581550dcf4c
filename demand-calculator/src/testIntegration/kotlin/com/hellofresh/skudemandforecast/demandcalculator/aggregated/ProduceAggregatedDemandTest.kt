package com.hellofresh.skudemandforecast.demandcalculator.aggregated

import KafkaInfraPreparation
import com.hellofresh.kafka.serde.deserializer
import com.hellofresh.kafka.serde.serializer
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.FUMIGATED
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.REGULAR
import com.hellofresh.skuDemandForecast.models.protoDate
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemandRepository
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemandRepositoryImpl
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.default
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDocking
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.DUPLICATE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.REPLACE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekitting
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekittings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.UNKNOWN_BRAND
import com.hellofresh.skudemandforecast.demandcalculator.message.AggregatedDemandPublishJob
import com.hellofresh.skudemandforecast.demandcalculator.message.AggregatedDemandPublishRepository
import com.hellofresh.skudemandforecast.demandcalculator.message.SKU_DEMAND_FORECAST_TOPIC_NAME
import com.hellofresh.skudemandforecast.demandcalculator.message.toProtoDecimal
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.AGGREGATED_DEMAND
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.DC_CONFIG
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.SKU_SPECIFICATION
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.distributioncenter.default
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.admin.AdminClient
import org.apache.kafka.clients.admin.AdminClientConfig
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.logging.log4j.kotlin.Logging
import org.flywaydb.core.Flyway
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName

private const val ARTIFACTORY_PROXY = "repo.tools-k8s.hellofresh.io"
private const val KAFKA_BROKER_VERSION = "7.3.1"
private const val POSTGRES_VERSION = "15.2-alpine"
private const val OUTBOX_DB_USERNAME = "outbox"
private const val OUTBOX_DB_PASSWORD = "123456"

const val WEEK_IN_KAFKA_HEADER_KEY = "week"

class ProduceAggregatedDemandTest : Logging {
    private val repository: AggregatedDemandRepository
    private val aggregatedDemandPublishJob: AggregatedDemandPublishJob

    private val defaultDistributionCenter = DistributionCenter.Companion.default()
    private val defaultDcCode = defaultDistributionCenter.dcCode
    private val defaultProductionStart = MONDAY
    private var dsl: MetricsDSLContext
    private lateinit var kafkaAdminClient: AdminClient

    init {
        initKafka()
        initPostgres()
        val dataSource = HikariDataSource(
            HikariConfig()
                .also {
                    it.driverClassName = org.postgresql.Driver::class.qualifiedName
                    it.jdbcUrl = postgres.jdbcUrl
                    it.username = postgres.username
                    it.password = postgres.password
                },
        )
        dsl = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())
        repository = AggregatedDemandRepositoryImpl(dsl, dsl)
        aggregatedDemandPublishJob = AggregatedDemandPublishJob(
            dsl,
            AggregatedDemandPublishRepository(),
            KafkaInfraPreparation.createKafkaProducer(
                serializer<com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey>(),
                serializer<com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal>(),
                kafka,
            ),
            DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl)),
        )
    }

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        dsl.deleteFrom(AGGREGATED_DEMAND).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
    }

    @Test
    fun `demand is sent to output topics for selected markets with fumigated , regular demand type`() {
        // given
        val dcWeek = DcWeek(LocalDate.now(), MONDAY)
        val aggregatedDemand = AggregatedDemand(
            sku = Sku(
                id = UUID.fromString("6eb0ddcc-b249-4674-b853-2642c0dc7600"),
                code = "PHF-11-22222-3",
            ),
            parentSku = null,
            dcCode = defaultDcCode,
            productionDate = dcWeek.getStartDateInDcWeek(
                defaultDistributionCenter.productionStart,
                defaultDistributionCenter.zoneId,
            ),
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.Companion.default(0, 1L, demandType = FUMIGATED),
                RecipeBreakdownLine.Companion.default(0, 1L, demandType = REGULAR).copy(
                    brand = "B2",
                ),
                RecipeBreakdownLine.Companion.default(1, 2L).copy(
                    brand = null,
                ),
            ),
            substitutions = Substitutions(
                subOut = listOf(Substitution(10, demandType = FUMIGATED)),
                subIn = listOf(Substitution(20, demandType = REGULAR)),
            ),
            prekittings = Prekittings(
                prekittingOut = listOf(Prekitting(1, demandType = FUMIGATED)),
                prekittingIn = listOf(Prekitting(2, demandType = REGULAR)),
            ),
            crossDockings = CrossDockings(
                crossDockingsOut = listOf(CrossDocking("DC1", REPLACE, 8)),
                crossDockingsIn = listOf(CrossDocking("DC2", DUPLICATE, 9)),
            ),
            sourceIds = setOf(UUID.randomUUID()),
        )

        val dcConfigRecord1 = DcConfigRecord().apply {
            dcCode = defaultDcCode
            productionStart = defaultProductionStart.name
            market = "M1"
            zoneId = defaultDistributionCenter.zoneId.id
            enabled = true
        }
        val dcConfigRecord2 = DcConfigRecord().apply {
            dcCode = "D2"
            productionStart = defaultProductionStart.name
            market = "M2"
            zoneId = defaultDistributionCenter.zoneId.id
            enabled = true
        }
        dsl.batchInsert(dcConfigRecord1, dcConfigRecord2).execute()

        // when
        runBlocking {
            repository.upsert(listOf(aggregatedDemand, aggregatedDemand.copy(dcCode = dcConfigRecord2.dcCode)))
        }

        runBlocking { aggregatedDemandPublishJob.publish(setOf(dcConfigRecord1.market)) }

        val aggDemandRecords = dsl.fetch(AGGREGATED_DEMAND)
        assertEquals(1, aggDemandRecords.count { !it.published })
        assertEquals(1, aggDemandRecords.count { it.published })
        assertEquals(dcConfigRecord1.dcCode, aggDemandRecords.first { it.published }.dcCode)

        // and assert the message content in the topic
        val (expectedKeyV2, expectedValueV2) = buildExpectedV2(aggregatedDemand)

        KafkaInfraPreparation.assertTopicRecords(
            listOf(expectedKeyV2 to expectedValueV2),
            KafkaInfraPreparation.createKafkaConsumer(
                "ProduceAggregatedDemandTest",
                SKU_DEMAND_FORECAST_TOPIC_NAME,
                deserializer<com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey>(),
                deserializer<com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal>(),
                kafka,
            ),
        ) { expected, actualRecord -> assertWeekHeader(expected, actualRecord) }
    }

    private fun buildExpectedV2(aggregatedDemand: AggregatedDemand): Pair<
        com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey,
        com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal,
        > {
        val expectedKeyV2 = com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey.newBuilder()
            .setSkuId(aggregatedDemand.sku.id.toString())
            .setDate(aggregatedDemand.productionDate.protoDate())
            .setDistributionCenterBobCode(aggregatedDemand.dcCode).build()

        val expectedValueV2 = com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.newBuilder()
            .setSkuCode(aggregatedDemand.sku.code)
            .setTotalQty(aggregatedDemand.totalQty.toProtoDecimal())
            .addAllRecipesBreakdown(
                aggregatedDemand.recipeBreakdowns.groupBy { Triple(it.recipeIndex, it.brand, it.demandType) }
                    .map { (key, values) ->
                        val (recipeIndex, brand, demandType) = key
                        RecipesBreakdown.newBuilder()
                            .setQty(values.sumOf { it.qty }.toProtoDecimal())
                            .setRecipeIndex(recipeIndex.toString())
                            .setBrand(brand ?: UNKNOWN_BRAND)
                            .setDemandType(getDemandType(demandType))
                            .build()
                    },
            ).setSubstitutions(
                com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitutions.newBuilder()
                    .addAllSubOut(
                        aggregatedDemand.substitutions?.subOut?.map {
                            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitution.newBuilder()
                                .setQty(it.qty.toProtoDecimal())
                                .setBrand(UNKNOWN_BRAND)
                                .setDemandType(getDemandType(it.demandType))
                                .build()
                        },
                    )
                    .addAllSubIn(
                        aggregatedDemand.substitutions?.subIn?.map {
                            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitution.newBuilder()
                                .setQty(it.qty.toProtoDecimal())
                                .setBrand(UNKNOWN_BRAND)
                                .setDemandType(getDemandType(it.demandType))
                                .build()
                        },
                    ),
            ).setPreKittings(
                com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekittings.newBuilder()
                    .addAllPrekittingOut(
                        aggregatedDemand.prekittings?.prekittingOut?.map {
                            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekitting.newBuilder()
                                .setQty(it.qty.toProtoDecimal())
                                .setDemandType(getDemandType(it.demandType))
                                .build()
                        },
                    )
                    .addAllPrekittingIn(
                        aggregatedDemand.prekittings?.prekittingIn?.map {
                            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekitting.newBuilder()
                                .setQty(it.qty.toProtoDecimal())
                                .setDemandType(getDemandType(it.demandType))
                                .build()
                        },
                    ),

            ).setCrossDockings(
                com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockings.newBuilder()
                    .addAllCrossdockingOut(buildExpectedCrossDocking(aggregatedDemand.crossDockings?.crossDockingsOut))
                    .addAllCrossdockingIn(buildExpectedCrossDocking(aggregatedDemand.crossDockings?.crossDockingsIn)),
            )
            .build()

        return expectedKeyV2 to expectedValueV2
    }

    private fun getDemandType(demandType: DemandType?): SkuDemandForecastVal.DemandType =
        when (demandType) {
            FUMIGATED -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_FUMIGATED
            REGULAR -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_REGULAR
            else -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_UNSPECIFIED
        }

    private fun buildExpectedCrossDocking(crossDockings: List<CrossDocking>?) =
        crossDockings?.map {
            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDocking.newBuilder()
                .setDistributionCenterCode(it.dcCode)
                .setActionValue(it.action.ordinal + 1)
                .setQty(it.quantity.toProtoDecimal()).build()
        }

    private fun assertWeekHeader(
        expected:
        Pair<
            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey,
            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal,
            >,
        actualRecord:
        ConsumerRecord<
            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey,
            com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal,
            >
    ) {
        assertEquals(
            DcWeek(
                LocalDate.of(expected.first.date.year, expected.first.date.month, expected.first.date.day),
                defaultProductionStart,
            ).value,

            actualRecord.headers().lastHeader(
                WEEK_IN_KAFKA_HEADER_KEY,
            )?.value()?.decodeToString(),
        )
    }

    private fun initPostgres() {
        postgres.start()
        Flyway.configure()
            .dataSource(postgres.jdbcUrl, postgres.username, postgres.password)
            .locations("filesystem:../sku-demand-forecast-db/src/main/resources/db/migration/schema")
            .placeholders(
                mapOf(
                    "DB_USERNAME" to "sdf",
                    "DB_OUTBOX_USERNAME" to OUTBOX_DB_USERNAME,
                    "DB_OUTBOX_PASSWORD" to "'$OUTBOX_DB_PASSWORD'",
                ),
            )
            .load()
            .migrate()
    }

    private fun initKafka() {
        kafka.start()
        kafkaAdminClient = AdminClient.create(
            mapOf(
                AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
            ),
        )
    }

    companion object {
        private val network = Network.newNetwork()
        private var postgresImage = DockerImageName
            .parse("$ARTIFACTORY_PROXY/postgres:$POSTGRES_VERSION")
            .asCompatibleSubstituteFor("postgres")
        private val postgres = PostgreSQLContainer(postgresImage)
            .withNetwork(network)
            .withDatabaseName("sdf")
            .withUsername("sdf")
            .withPassword("123456")

        private var kafkaImage = DockerImageName
            .parse("$ARTIFACTORY_PROXY/confluentinc/cp-kafka:$KAFKA_BROKER_VERSION")
            .asCompatibleSubstituteFor("confluentinc/cp-kafka")
        private val kafka = KafkaContainer(kafkaImage)
            .withNetwork(network)
            .waitingFor(
                Wait.forLogMessage(
                    ".*INFO\\s+\\[KafkaServer\\s+id=\\d+\\]" +
                        "\\s+started\\s+\\(kafka.server.KafkaServer\\).*",
                    1,
                ),
            )
    }
}
