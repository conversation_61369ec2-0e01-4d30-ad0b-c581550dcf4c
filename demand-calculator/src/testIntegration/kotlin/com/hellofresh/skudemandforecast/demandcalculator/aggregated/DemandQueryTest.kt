package com.hellofresh.skudemandforecast.demandcalculator.aggregated

import com.hellofresh.skudemandforecast.demandcalculator.AbstractDbAwareTest
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.DemandQueryImpl
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.default
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DemandQueryTest : AbstractDbAwareTest() {
    private val skuCode = "SPI-000-0000"
    private val demandQuery = DemandQueryImpl(dsl)

    @BeforeEach
    fun cleanupDb() {
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.FRACTION_SKU_DEMAND).execute()
        dsl.deleteFrom(Tables.FRACTION_SKU_DEMAND).execute()
    }

    @Test
    fun `should aggregate a single fraction sku demand correctly`() {
        val skuId = UUID.randomUUID()
        val dcCode = "VE"
        val productionDate = LocalDate.now()
        persistDistributionCenter(dcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]
        val recipeBreakdownLine = RecipeBreakdownLine.Companion.default(1, 2L)
        persistDemand(skuId, dcCode, productionDate, listOf(recipeBreakdownLine))

        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(dcCode)) }

        assertEquals(1, aggregateDemands.size)
        checkDbResult(
            skuId,
            dcCode,
            productionDate,
            skuSpecRecord,
            listOf(recipeBreakdownLine),
            aggregateDemands.first(),
        )
    }

    @Test
    fun `should aggregate two fraction sku demands for the same skuId correctly`() {
        val skuId1 = UUID.randomUUID()
        val dcCode = "VE"
        val productionDate = LocalDate.now()
        persistDistributionCenter(dcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId1, skuCode)))[0]
        val recipeBreakdownLines = listOf(
            RecipeBreakdownLine.Companion.default(1, 1L),
            RecipeBreakdownLine.Companion.default(2, 2L),
        )
        persistDemand(skuId1, dcCode, productionDate, recipeBreakdownLines)

        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(dcCode)) }

        assertEquals(1, aggregateDemands.size)
        checkDbResult(skuId1, dcCode, productionDate, skuSpecRecord, recipeBreakdownLines, aggregateDemands.first())
    }

    @Test
    fun `should aggregate two fraction sku demands for the same skuId correctly without recipe breakdown`() {
        val skuId1 = UUID.randomUUID()
        val productionDate = LocalDate.now()
        persistDistributionCenter(defaultDcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId1, skuCode)))[0]
        val demand1 = persistDemand(skuId1, 1, 10, productionDate)
        val demand2 = persistDemand(skuId1, 2, 50, productionDate)

        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(defaultDcCode)) }

        assertEquals(1, aggregateDemands.size)
        checkDbResult(
            skuId1,
            defaultDcCode,
            productionDate,
            skuSpecRecord,
            listOf(
                RecipeBreakdownLine(
                    demand1.recipeIndex,
                    demand1.country,
                    demand1.locale,
                    demand1.productFamily,
                    null,
                    null,
                    null,
                    demand1.qty,
                ),
                RecipeBreakdownLine(
                    demand2.recipeIndex,
                    demand2.country,
                    demand2.locale,
                    demand2.productFamily,
                    null,
                    null,
                    null,
                    demand2.qty,
                ),
            ),
            aggregateDemands.first(),
        )

        assertEquals((demand1.sourceIds + demand2.sourceIds).toSet(), aggregateDemands.first().sourceIds.toSet())
    }

    @Test
    fun `should return the aggregate demands using the new fraction sku demand data- feature flag enabled for dc`() {
        val skuId = UUID.randomUUID()
        val dcCode = "VE"
        val productionDate = LocalDate.now()
        persistDistributionCenter(dcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]
        val recipeBreakdownLine = RecipeBreakdownLine.Companion.default(1, 10L)
        persistFractionSkuDemandRecord(
            skuIdParam = skuId,
            dcCodeParam = dcCode,
            demandDateParam = productionDate,
            recipeBreakdownLines = listOf(recipeBreakdownLine),
        )
        val demandQuery = DemandQueryImpl(dsl)
        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(dcCode)) }
        assertEquals(1, aggregateDemands.size)
        checkDbResult(
            skuId,
            dcCode,
            productionDate,
            skuSpecRecord,
            listOf(recipeBreakdownLine),
            aggregateDemands.first(),
        )
    }

    @Test
    fun `should return the aggregate demands using the new fraction sku demand data when recipe_details is empty`() {
        val skuId = UUID.randomUUID()
        val productionDate = LocalDate.now()
        val demand1 = persistNewFractionDemand(skuId = skuId, recipeIndex = 1, qty = 10, date = productionDate)
        val demand2 = persistNewFractionDemand(skuId = skuId, recipeIndex = 2, qty = 50, date = productionDate)
        persistDistributionCenter(demand1.dcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]

        val demandQuery = DemandQueryImpl(dsl)
        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(demand1.dcCode)) }
        assertEquals(1, aggregateDemands.size)
        checkDbResult(
            skuId,
            demand1.dcCode,
            productionDate,
            skuSpecRecord,
            listOf(
                RecipeBreakdownLine(
                    demand1.recipeIndex,
                    demand1.country,
                    demand1.locale,
                    demand1.productFamily,
                    null,
                    null,
                    null,
                    demand1.qty,
                ),
                RecipeBreakdownLine(
                    demand2.recipeIndex,
                    demand2.country,
                    demand2.locale,
                    demand2.productFamily,
                    null,
                    null,
                    null,
                    demand2.qty,
                ),
            ),
            aggregateDemands.first(),
        )
    }

    @Test
    fun `should return the aggregate demands for given dcs`() {
        val skuId = UUID.randomUUID()
        val productionDate = LocalDate.now()
        val demand1 = persistNewFractionDemand(skuId = skuId, recipeIndex = 1, qty = 10, date = productionDate)
        val demand2 = persistNewFractionDemand(
            skuId = skuId,
            recipeIndex = 2,
            qty = 50,
            date = productionDate,
            dcCode = "D2"
        )
        val demand3 = persistNewFractionDemand(
            skuId = skuId,
            recipeIndex = 2,
            qty = 50,
            date = productionDate,
            dcCode = "D3"
        )
        persistDistributionCenter(demand1.dcCode)
        persistDistributionCenter(demand2.dcCode)
        persistDistributionCenter(demand3.dcCode)
        persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]

        val demandQuery = DemandQueryImpl(dsl)
        val aggregateDemands = runBlocking {
            demandQuery.aggregateFractionDemands(
                setOf(demand1.dcCode, demand2.dcCode)
            )
        }
        assertEquals(2, aggregateDemands.size)
        assertEquals(demand1.qty, aggregateDemands.first { it.dcCode == demand1.dcCode }.totalQty)
        assertEquals(demand2.qty, aggregateDemands.first { it.dcCode == demand2.dcCode }.totalQty)

        val aggregateDemandsDc3 = runBlocking { demandQuery.aggregateFractionDemands(setOf(demand3.dcCode)) }
        assertEquals(1, aggregateDemandsDc3.size)
        assertEquals(demand3.qty, aggregateDemandsDc3.first { it.dcCode == demand3.dcCode }.totalQty)
    }

    @Test
    fun `should return the aggregate demands using the new fraction sku demand data - feature flag enabled for market`() {
        val skuId = UUID.randomUUID()
        val dcCode = "VE"
        val productionDate = LocalDate.now()
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]
        val recipeBreakdownLine1 = RecipeBreakdownLine.Companion.default(1, 10L)
        val recipeBreakdownLine2 = recipeBreakdownLine1.copy(
            peopleCount = recipeBreakdownLine1.peopleCount!! + 1,
            qty = recipeBreakdownLine1.qty + 100,
        )
        val recipeBreakdownLines = listOf(recipeBreakdownLine1, recipeBreakdownLine2)
        persistFractionSkuDemandRecord(
            skuIdParam = skuId,
            dcCodeParam = dcCode,
            demandDateParam = productionDate,
            recipeBreakdownLines = recipeBreakdownLines,
        )
        val demandQuery = DemandQueryImpl(dsl)
        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(dcCode)) }
        assertEquals(1, aggregateDemands.size)
        checkDbResult(
            skuId,
            dcCode,
            productionDate,
            skuSpecRecord,
            recipeBreakdownLines,
            aggregateDemands.first(),
        )
    }

    @Test
    fun `should return the aggregate demands using the old & new fraction sku demand data - feature flag enabled for dc`() {
        val skuId = UUID.randomUUID()
        val skuCode = "SPI-000-0001"
        val dcCode = "DC"
        val productionDate = LocalDate.now()
        persistDistributionCenter(dcCode)
        persistDistributionCenter(defaultDcCode)
        val skuSpecRecord = persistSkuSpecificationRecord(listOf(Sku(skuId, skuCode)))[0]
        val recipeBreakdownLine1 = RecipeBreakdownLine.Companion.default(1, 10L)
        val recipeBreakdownLine2 = recipeBreakdownLine1.copy(
            peopleCount = recipeBreakdownLine1.peopleCount!! + 1,
            qty = recipeBreakdownLine1.qty + 100,
        )
        val recipeBreakdownLines = listOf(recipeBreakdownLine1, recipeBreakdownLine2)
        persistDemand(skuId, defaultDcCode, productionDate, recipeBreakdownLines)
        persistFractionSkuDemandRecord(
            skuIdParam = skuId,
            dcCodeParam = dcCode,
            demandDateParam = productionDate,
            recipeBreakdownLines = recipeBreakdownLines,
        )
        val demandQuery = DemandQueryImpl(dsl)
        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(dcCode, defaultDcCode)) }

        assertEquals(2, aggregateDemands.size)
        checkDbResult(
            skuId,
            dcCode,
            productionDate,
            skuSpecRecord,
            recipeBreakdownLines,
            aggregateDemands.first { it.dcCode == dcCode },
        )
    }

    @Test
    fun `should aggregate demand for a sku with a parent sku that doesn't exist - CIF-1707`() {
        val skuId = UUID.randomUUID()
        val nonExistentParentSku = UUID(0, 0)
        persistDistributionCenter(defaultDcCode)
        val skuSpecRecord = SkuSpecificationRecord(
            skuId,
            nonExistentParentSku,
            "SPI-1234",
            LocalDateTime.now(),
            null,
            "VE",
            null,
            "packaging"
        )
        dsl.batchInsert(skuSpecRecord).execute()
        persistDemand(skuId, 1, 101)

        val aggregateDemands = runBlocking { demandQuery.aggregateFractionDemands(setOf(defaultDcCode)) }

        assertEquals(1, aggregateDemands.size)
        assertNull(aggregateDemands.first().parentSku)
    }
}
