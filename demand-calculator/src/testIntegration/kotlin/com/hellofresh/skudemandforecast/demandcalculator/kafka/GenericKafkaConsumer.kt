package com.hellofresh.skudemandforecast.demandcalculator.kafka

import com.hellofresh.logging.withContext
import java.time.Duration
import java.util.Properties
import java.util.UUID
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.serialization.Deserializer
import org.apache.logging.log4j.kotlin.Logging

class GenericKafkaConsumer<K, V>(
    topicName: String,
    bootstrapServers: String,
    keyDeserializer: Deserializer<K>,
    valDeserializer: Deserializer<V>
) : Logging {
    private val timeout = Duration.ofMillis(100)
    private val consumer: KafkaConsumer<K, V>

    init {
        val groupId = "GenericKafkaConsumer-${UUID.randomUUID()}"
        val props = Properties()
            .apply {
                setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers)
                setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId)
                setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true")
                setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest")
                setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, keyDeserializer.javaClass.name)
                setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, valDeserializer.javaClass.name)
            }
        consumer = KafkaConsumer(props)
        consumer.subscribe(listOf(topicName))
        withContext("groupId" to groupId, "topic" to topicName) {
            logger.info("The consumer has subscribed to the topic.")
        }
        consumer.seekToBeginning(consumer.assignment())
    }

    fun consume(): Flow<ConsumerRecords<K, V>> =
        flow {
            while (true) {
                val records = consumer.poll(timeout)
                if (!records.isEmpty) {
                    emit(records)
                }
            }
        }
}
