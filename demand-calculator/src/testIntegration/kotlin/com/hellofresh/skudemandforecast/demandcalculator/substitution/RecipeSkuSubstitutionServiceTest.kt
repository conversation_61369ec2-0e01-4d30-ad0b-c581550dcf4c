package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSkuInDemand
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSkuOutDemand
import com.hellofresh.skuDemandForecast.model.substitution.RecipeSubstitutionDetail
import com.hellofresh.skudemandforecast.demandcalculator.AbstractDbAwareTest
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

class RecipeSkuSubstitutionServiceTest : AbstractDbAwareTest() {
    private val recipeSkuSubstitutionService = RecipeSkuSubstitutionService(RecipeSkuSubstitutionRepositoryImpl(dsl))

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.RECIPE_SKU_SUBSTITUTION).execute()
    }

    @Test
    fun `should prepare the substitutions using recipe sku substitutions and demands`() {
        val recipeSkuSubstitutionRecord = recipeSkuSubstitution()
        val recipeSubstitutionDetail = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord.value.toString()
        ).first()

        val recipeSkuOut = recipeSubstitutionDetail.skuOut
        val recipeSkuIn = recipeSubstitutionDetail.skuIn.first()

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord).execute()
            val aggregatedDemand = createAggregatedDemand(recipeSkuOut)
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand),
                mapOf()
            )
            assertEquals(1, aggregatedDemandWithSubstitutions.size)
            aggregatedDemandWithSubstitutions.first().apply {
                assertEquals(LocalDate.now(), date)
                assertEquals("SY", dcCode)
                assertEquals(10, qtyOut)
                assertFalse(fullDayOut)
                assertEquals(1, picksOut)
                assertEquals(recipeSkuOut.skuCode, skuOut.code)
                assertEquals(recipeSkuIn.skuCode, skuIn.keys.first().code)
            }
            val skuIn = aggregatedDemandWithSubstitutions.first().skuIn[
                Sku(
                    recipeSkuIn.skuId,
                    recipeSkuIn.skuCode,
                    recipeSkuIn.parentSkuId,
                    recipeSkuIn.parentSkuCode,
                ),
            ]
            assertEquals(10, skuIn?.qty)
            assertEquals(1, skuIn?.picks)
            assertFalse(skuIn?.fullDay!!)
        }
    }

    @Test
    fun `should prepare correct substitutions with different picks using recipe sku substitutions and demands`() {
        val recipeSkuSubstitutionRecord = recipeSkuSubstitution(picksIn = 1, picksOut = 2)
        val recipeSubstitutionDetail = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord.value.toString()
        ).first()

        val recipeSkuOut = recipeSubstitutionDetail.skuOut
        val recipeSkuIn = recipeSubstitutionDetail.skuIn.first()

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord).execute()
            val aggregatedDemand = createAggregatedDemand(recipeSkuOut)
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand),
                mapOf()
            )
            assertEquals(1, aggregatedDemandWithSubstitutions.size)
            aggregatedDemandWithSubstitutions.first().apply {
                assertEquals(LocalDate.now(), date)
                assertEquals("SY", dcCode)
                assertEquals(10, qtyOut)
                assertFalse(fullDayOut)
                assertEquals(1, picksOut)
                assertEquals(recipeSkuOut.skuCode, skuOut.code)
                assertEquals(recipeSkuIn.skuCode, skuIn.keys.first().code)
            }
            val skuIn = aggregatedDemandWithSubstitutions.first().skuIn[
                Sku(
                    recipeSkuIn.skuId,
                    recipeSkuIn.skuCode,
                    recipeSkuIn.parentSkuId,
                    recipeSkuIn.parentSkuCode,
                ),
            ]
            assertEquals(5, skuIn?.qty)
            assertEquals(1, skuIn?.picks)
            assertFalse(skuIn?.fullDay!!)
        }
    }

    @Test
    fun `should not create the substitutions when the dc code does not matches`() {
        val recipeSkuSubstitutionRecord = recipeSkuSubstitution()
        val recipeSubstitutionDetail = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord.value.toString()
        ).first()

        val recipeSkuOut = recipeSubstitutionDetail.skuOut

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord).execute()
            val aggregatedDemand = createAggregatedDemand(recipeSkuOut, "ML")
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand),
                mapOf()
            )
            assertEquals(0, aggregatedDemandWithSubstitutions.size)
        }
    }

    @Test
    fun `should not create the substitutions when the production date does not matches`() {
        val notMatchingFutureDate = LocalDate.now().plusDays(20)
        val recipeSkuSubstitutionRecord =
            recipeSkuSubstitution(fromDateParam = notMatchingFutureDate, productionDateParam = notMatchingFutureDate)
        val recipeSubstitutionDetail = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord.value.toString()
        ).first()

        val recipeSkuOut = recipeSubstitutionDetail.skuOut

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord).execute()
            val aggregatedDemand = createAggregatedDemand(
                recipeSkuOut = recipeSkuOut,
                dcCode = "SY",
                productionDate = LocalDate.now().plusDays(10),
            )
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand),
                mapOf()
            )
            assertEquals(0, aggregatedDemandWithSubstitutions.size)
        }
    }

    @Test
    fun `should prepare the substitutions using recipe sku substitutions and demands - 3 skuIns`() {
        val recipeSkuSubstitutionRecord = recipeSkuSubstitution(numberOfSkuIns = 2)
        val recipeSubstitutionDetail = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord.value.toString()
        ).first()

        val recipeSkuOut = recipeSubstitutionDetail.skuOut
        val recipeSkuIn1 = recipeSubstitutionDetail.skuIn[0]
        val recipeSkuIn2 = recipeSubstitutionDetail.skuIn[1]
        val recipeSkuIn3 = recipeSubstitutionDetail.skuIn[2]

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord).execute()
            val aggregatedDemand = createAggregatedDemand(recipeSkuOut)
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand),
                mapOf()
            )
            assertEquals(1, aggregatedDemandWithSubstitutions.size)
            assertRecipeSkuOut(aggregatedDemandWithSubstitutions[0], recipeSkuOut)
            val aggregatedDemandWithSubstitution = aggregatedDemandWithSubstitutions.first()
            assertAggregatedDemandSubstitution(
                aggregatedDemandWithSubstitution,
                recipeSkuIn1,
                recipeSkuIn2,
                recipeSkuIn3
            )
        }
    }

    @Test
    fun `should prepare the substitutions using recipe sku substitutions and demands - 3 skuIns - multiple recipe sku substitutions`() {
        val recipeSkuSubstitutionRecord1 = recipeSkuSubstitution(numberOfSkuIns = 2)
        val recipeSkuSubstitutionRecord2 = recipeSkuSubstitution(recipeSkuIndex = 1001, numberOfSkuIns = 2)
        val recipeSubstitutionDetail1 = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord1.value.toString()
        ).first()
        val recipeSubstitutionDetail2 = objectMapper.readValue<List<RecipeSubstitutionDetail>>(
            recipeSkuSubstitutionRecord2.value.toString()
        ).first()

        val skuSpecifications = listOf(recipeSubstitutionDetail1, recipeSubstitutionDetail2)
            .flatMap { it.skuIn }
            .associate { sku ->
                sku.skuId to SkuSpecificationRecord().apply {
                    id = sku.skuId
                    code = sku.skuCode
                    parentId = sku.parentSkuId
                }
            } + listOf(recipeSubstitutionDetail1, recipeSubstitutionDetail2)
            .flatMap { listOf(it.skuOut) }
            .associate { sku ->
                sku.skuId to SkuSpecificationRecord().apply {
                    id = sku.skuId
                    code = sku.skuCode
                    parentId = sku.parentSkuId
                }
            }

        val firstRecipeSkuOut = recipeSubstitutionDetail1.skuOut
        val firstRecipeSkuIn1 = recipeSubstitutionDetail1.skuIn[0]
        val firstRecipeSkuIn2 = recipeSubstitutionDetail1.skuIn[1]
        val firstRecipeSkuIn3 = recipeSubstitutionDetail1.skuIn[2]

        val secondRecipeSkuOut = recipeSubstitutionDetail2.skuOut
        val secondRecipeSkuIn1 = recipeSubstitutionDetail2.skuIn[0]
        val secondRecipeSkuIn2 = recipeSubstitutionDetail2.skuIn[1]
        val secondRecipeSkuIn3 = recipeSubstitutionDetail2.skuIn[2]

        runBlocking {
            dsl.batchInsert(recipeSkuSubstitutionRecord1, recipeSkuSubstitutionRecord2).execute()
            val aggregatedDemand1 = createAggregatedDemand(firstRecipeSkuOut)
            val aggregatedDemand2 = createAggregatedDemand(secondRecipeSkuOut, recipeIndexParam = 1001)
            val aggregatedDemandWithSubstitutions = recipeSkuSubstitutionService.prepareSubstitutions(
                listOf(aggregatedDemand1, aggregatedDemand2),
                skuSpecifications
            )
            assertEquals(2, aggregatedDemandWithSubstitutions.size)
            assertRecipeSkuOut(aggregatedDemandWithSubstitutions[0], firstRecipeSkuOut)
            assertRecipeSkuOut(aggregatedDemandWithSubstitutions[1], secondRecipeSkuOut)
            val aggregatedDemandWithSubstitution1 = aggregatedDemandWithSubstitutions[0]
            val aggregatedDemandWithSubstitution2 = aggregatedDemandWithSubstitutions[1]
            assertAggregatedDemandSubstitution(
                aggregatedDemandWithSubstitution1,
                firstRecipeSkuIn1,
                firstRecipeSkuIn2,
                firstRecipeSkuIn3
            )
            assertAggregatedDemandSubstitution(
                aggregatedDemandWithSubstitution2,
                secondRecipeSkuIn1,
                secondRecipeSkuIn2,
                secondRecipeSkuIn3
            )
        }
    }
    private fun assertRecipeSkuOut(
        aggregatedDemandWithSubstitution: Substitution,
        firstRecipeSkuOut: RecipeSkuOutDemand
    ) {
        aggregatedDemandWithSubstitution.apply {
            assertEquals(LocalDate.now(), date)
            assertEquals("SY", dcCode)
            assertEquals(10, qtyOut)
            assertFalse(fullDayOut)
            assertEquals(1, picksOut)
            assertEquals(firstRecipeSkuOut.skuCode, skuOut.code)
        }
    }
    private fun assertAggregatedDemandSubstitution(
        aggregatedDemandWithSubstitution: Substitution,
        firstRecipeSkuIn1: RecipeSkuInDemand,
        firstRecipeSkuIn2: RecipeSkuInDemand,
        firstRecipeSkuIn3: RecipeSkuInDemand
    ) {
        assertEquals(
            firstRecipeSkuIn1.skuCode,
            aggregatedDemandWithSubstitution.skuIn.keys.find { it.code == firstRecipeSkuIn1.skuCode }?.code,
        )
        assertEquals(
            firstRecipeSkuIn2.skuCode,
            aggregatedDemandWithSubstitution.skuIn.keys.find { it.code == firstRecipeSkuIn2.skuCode }?.code,
        )
        assertEquals(
            firstRecipeSkuIn3.skuCode,
            aggregatedDemandWithSubstitution.skuIn.keys.find { it.code == firstRecipeSkuIn3.skuCode }?.code,
        )
        val skuIn1 = aggregatedDemandWithSubstitution.skuIn[
            Sku(
                firstRecipeSkuIn1.skuId,
                firstRecipeSkuIn1.skuCode,
                firstRecipeSkuIn1.parentSkuId,
                firstRecipeSkuIn1.parentSkuCode,
            ),
        ]
        val skuIn2 = aggregatedDemandWithSubstitution.skuIn[
            Sku(
                firstRecipeSkuIn2.skuId,
                firstRecipeSkuIn2.skuCode,
                firstRecipeSkuIn2.parentSkuId,
                firstRecipeSkuIn2.parentSkuCode,
            ),
        ]
        val skuIn3 = aggregatedDemandWithSubstitution.skuIn[
            Sku(
                firstRecipeSkuIn3.skuId,
                firstRecipeSkuIn3.skuCode,
                firstRecipeSkuIn3.parentSkuId,
                firstRecipeSkuIn3.parentSkuCode,
            ),
        ]
        assertEquals(10, skuIn1?.qty)
        assertEquals(1, skuIn1?.picks)
        assertFalse(skuIn1?.fullDay!!)

        assertEquals(10, skuIn2?.qty)
        assertEquals(1, skuIn2?.picks)
        assertFalse(skuIn2?.fullDay!!)

        assertEquals(10, skuIn3?.qty)
        assertEquals(1, skuIn3?.picks)
        assertFalse(skuIn3?.fullDay!!)
    }
}
