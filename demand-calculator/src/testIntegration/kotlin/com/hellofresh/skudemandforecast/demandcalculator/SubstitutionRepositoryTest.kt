package com.hellofresh.skudemandforecast.demandcalculator

import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skuDemandForecast.model.substitution.DemandByDate
import com.hellofresh.skuDemandForecast.model.substitution.SkuDemand
import com.hellofresh.skuDemandForecast.model.substitution.Substitution
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSubstitutionRecord
import com.hellofresh.skudemandforecast.demandcalculator.substitution.DemandSubstitutions
import com.hellofresh.skudemandforecast.demandcalculator.substitution.SubstitutionRepositoryImpl
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.random.Random
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

private const val MAX_INT_RANGE = 1000

class SubstitutionRepositoryTest : AbstractDbAwareTest() {

    private val substitutionsRepository = SubstitutionRepositoryImpl(dsl)

    @AfterEach
    fun cleanupDb() {
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.SKU_SUBSTITUTION).execute()
    }

    @Test fun `skips the substitution with no matching sku in the sku_specification`() {
        val sub1 = substitution()
        val sub2 = substitution()
        val sub3 = substitution()
        val subs = listOf(sub1, sub2, sub3)

        val records = subs.map { sub ->

            SkuSubstitutionRecord().apply {
                id = UUID.randomUUID()
                version = 0
                dcCode = sub.dcCode
                fromDate = sub.fromDate
                toDate = sub.toDate
                skuIdOut = sub.substitutionDetail.skuOut.skuId
                authorName = sub.authorName
                authorEmail = sub.authorEmail
                reference = sub.reference
                value = JSONB.valueOf(objectMapper.writeValueAsString(sub.substitutionDetail))
                disabled = false
            }
        }

        dsl.batchInsert(records)

        runBlocking {
            val skuSpecificationId = UUID.randomUUID()
            val demand = DemandSubstitutions(
                substitutionsRepository.fetchActive(
                    subs.map { it.dcCode }.toSet(),
                    mapOf(
                        skuSpecificationId to SkuSpecificationRecord().apply {
                            id = skuSpecificationId
                            parentId = null
                            code = "code"
                            market = "VE"
                        },
                    ),
                ),
                StatsigTestFeatureFlagClient(emptySet()),
            )
            assertEquals(0, demand.substitutions.size)
        }
    }

    @Test fun `Returns the substitutions from the DB`() {
        val sub1 = substitution()
        val sub2 = substitution()
        val sub3 = substitution()
        val subs = listOf(sub1, sub2, sub3)

        val records = subs.map { toSubstitutionRecord(it) }

        insertSkusFromSubstitutions(subs)
        dsl.batchInsert(records).execute()

        runBlocking {
            val skuSpecificationRecords = prepareSkuSpecificationRecords(subs)
            val substitutions = substitutionsRepository.fetchActive(
                subs.map { it.dcCode }.toSet(),
                skuSpecificationRecords,
            )
            val demand = DemandSubstitutions(
                substitutions,
                StatsigTestFeatureFlagClient(emptySet()),
            )
            assertEquals(subs.sumOf { it.substitutionDetail.skuOut.demand.size }, demand.substitutions.size)

            val skuOutToInExpected = subs.map { sub ->
                sub.substitutionDetail.skuOut.skuId to sub.substitutionDetail.skuIn.map { it.skuId }.sorted()
            }.sortedBy { it.first }

            val skuOutToInActual = demand.substitutions
                .groupBy { it.skuOut }
                .map { (skuOut, subs) ->
                    skuOut.id to subs.flatMap { it.skuIn.map { (skuIn, _) -> skuIn.id } }
                        .toSet().toList().sorted()
                }
                .sortedBy { it.first }
            assertEquals(skuOutToInExpected, skuOutToInActual)
        }
    }

    @Test fun `Returns the substitutions for given dcs`() {
        val sub1 = substitution()
        val sub2 = substitution()
        val sub3 = substitution().copy(dcCode = "DC")
        val subs = listOf(sub1, sub2, sub3)

        val records = subs.map { toSubstitutionRecord(it) }

        insertSkusFromSubstitutions(subs)
        dsl.batchInsert(records).execute()

        runBlocking {
            val skuSpecificationRecords = prepareSkuSpecificationRecords(subs)
            val substitutions = substitutionsRepository.fetchActive(
                setOf(sub1.dcCode, sub2.dcCode),
                skuSpecificationRecords,
            )

            assertTrue(
                substitutions.all {
                    it.dcCode in setOf(sub1.dcCode, sub2.dcCode) &&
                        it.skuOut.id in setOf(sub1.skuIdOut, sub2.skuIdOut)
                },
            )

            val substitutionsSub3 = substitutionsRepository.fetchActive(setOf(sub3.dcCode), skuSpecificationRecords)
            assertTrue(
                substitutionsSub3.all {
                    it.skuOut.id == sub3.skuIdOut &&
                        it.dcCode == sub3.dcCode
                },
            )
        }
    }

    @Test
    fun `returns sku specifications for given markets`() {
        val record1 = SkuSpecificationRecord().apply {
            this.id = UUID.randomUUID()
            this.code = "c1"
            this.market = "D1"
            this.packaging = ""
        }
        val record2 = SkuSpecificationRecord().apply {
            this.id = UUID.randomUUID()
            this.code = "c2"
            this.market = "D2"
            this.packaging = ""
        }
        val record3 = SkuSpecificationRecord().apply {
            this.id = UUID.randomUUID()
            this.code = "c3"
            this.market = "D3"
            this.packaging = ""
        }

        dsl.batchInsert(record1, record2, record3).execute()

        runBlocking {
            val skus = substitutionsRepository.fetchSkuSpecificationRecords(
                setOf(record1.market, record2.market),
            )
            assertEquals(2, skus.size)
            assertEquals(record1.market, skus[record1.id]?.market)
            assertEquals(record2.market, skus[record2.id]?.market)

            val skuRecord3 = substitutionsRepository.fetchSkuSpecificationRecords(
                setOf(record3.market),
            )
            assertEquals(1, skuRecord3.size)
            assertEquals(record3.market, skuRecord3[record3.id]?.market)
        }
    }

    @Test fun `Returns last substitution version when active from DB otherwise 0`() {
        val sub1V0 = substitution()
        val sub1V1 = sub1V0.copy(substitutionDetail = substitution().substitutionDetail)
        val sub2Disabled = substitution()
        val subs = listOf(sub1V0, sub1V1, sub2Disabled)

        val sub1V0Record = toSubstitutionRecord(sub1V0)
        val sub1V1Record = toSubstitutionRecord(sub1V1, sub1V0Record.id, sub1V0Record.version + 1)

        val sub2DisabledRecord = toSubstitutionRecord(substitution = sub2Disabled, disabledParam = true)

        val skuSpecificationRecords = insertSkusFromSubstitutions(subs)
        dsl.batchInsert(sub1V0Record, sub1V1Record, sub2DisabledRecord).execute()

        runBlocking {
            val substitutions = substitutionsRepository.fetchActive(
                subs.map { it.dcCode }.toSet(),
                skuSpecificationRecords.associateBy {
                    it.id
                },
            )

            assertEquals(
                sub1V1.substitutionDetail.skuOut.demand.size +
                    sub2Disabled.substitutionDetail.skuOut.demand.size,
                substitutions.size,
            )

            substitutions.filter { it.id == sub1V1Record.id }.forEach {
                assertEquals(sub1V1Record.id, it.id)
                assertEquals(sub1V1.substitutionDetail.skuOut.skuId, it.skuOut.id)
                assertEquals(sub1V1.substitutionDetail.skuIn.size, it.skuIn.size)
                assertEquals(sub1V1.substitutionDetail.skuIn.first().skuId, it.skuIn.keys.first().id)
            }

            substitutions.filter { it.id == sub2DisabledRecord.id }.forEach {
                assertEquals(sub2DisabledRecord.id, it.id)
                assertEquals(sub2Disabled.substitutionDetail.skuOut.skuId, it.skuOut.id)
                assertEquals(sub2Disabled.substitutionDetail.skuIn.size, it.skuIn.size)
                assertEquals(sub2Disabled.substitutionDetail.skuIn.first().skuId, it.skuIn.keys.first().id)
                assertTrue(
                    it.skuIn.all { (_, skuIn) ->
                        skuIn.qty == 0L && !skuIn.fullDay
                    },
                )
                assertTrue(it.qtyOut == 0L && !it.fullDayOut)
            }
        }
    }

    private fun insertSkusFromSubstitutions(substitutions: List<Substitution>): List<SkuSpecificationRecord> {
        val skuRecords = substitutions.flatMap { sub ->
            sub.substitutionDetail.skuIn.map {
                SkuSpecificationRecord().apply {
                    id = it.skuId
                    parentId = null
                    code = UUID.randomUUID().toString()
                    market = "VE"
                    this.packaging = ""
                }
            } + SkuSpecificationRecord().apply {
                id = sub.substitutionDetail.skuOut.skuId
                parentId = null
                code = UUID.randomUUID().toString()
                market = "VE"
                this.packaging = ""
            }
        }
        dsl.batchInsert(skuRecords).execute()
        return skuRecords
    }

    private fun substitution() = with(Random(System.nanoTime())) {
        val skuIdOut = UUID.randomUUID()
        Substitution(
            subId = UUID.randomUUID(),
            skuIdOut = skuIdOut,
            fromDate = LocalDate.now(),
            toDate = LocalDate.now().plusDays(6),
            dcCode = "VE",
            substitutionDetail = SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = skuIdOut,
                    demand = (0..6).map { DemandByDate(LocalDate.now().plusDays(it.toLong()), qty = it + 1) },
                ),
                skuIn = listOf(
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = (0..6).map { DemandByDate(LocalDate.now().plusDays(it.toLong()), qty = it + 1) },
                    ),
                ),
            ),
            reference = "Reference-${nextInt(MAX_INT_RANGE)}",
            authorName = "Author-${nextInt(MAX_INT_RANGE)}",
            authorEmail = "<EMAIL>",
            lastEdited = OffsetDateTime.now(),
            sourceId = null,
            version = 0,
        )
    }

    private fun toSubstitutionRecord(
        substitution: Substitution,
        idParam: UUID = UUID.randomUUID(),
        versionParam: Int = 0,
        disabledParam: Boolean = false
    ) =
        SkuSubstitutionRecord().apply {
            id = idParam
            version = versionParam
            disabled = disabledParam
            dcCode = substitution.dcCode
            fromDate = substitution.fromDate
            toDate = substitution.toDate
            skuIdOut = substitution.substitutionDetail.skuOut.skuId
            authorName = substitution.authorName
            authorEmail = substitution.authorEmail
            reference = substitution.reference
            value = JSONB.valueOf(objectMapper.writeValueAsString(substitution.substitutionDetail))
        }

    private fun prepareSkuSpecificationRecords(subs: List<Substitution>): Map<UUID, SkuSpecificationRecord> =
        subs.flatMap { listOf(it.skuIdOut) + it.substitutionDetail.skuIn.map { skuIn -> skuIn.skuId } }
            .distinct()
            .associateWith { id ->
                SkuSpecificationRecord().apply {
                    this.id = id
                    this.parentId = null
                    this.code = "code"
                    this.market = "VE"
                    this.packaging = ""
                }
            }
}
