package com.hellofresh.skudemandforecast.demandcalculator.agregatedemand

import com.hellofresh.skuDemandForecast.featureflags.Context.DC
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag.SkuSubstitution
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.FUMIGATED
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.REGULAR
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregateDemandService
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemandRepository
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.DemandQuery
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.PrekittingService
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.default
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.random
import com.hellofresh.skudemandforecast.demandcalculator.crossdocking.CrossDockingService
import com.hellofresh.skudemandforecast.demandcalculator.prekitting.AnzPreKittingDemand
import com.hellofresh.skudemandforecast.demandcalculator.prekitting.AnzPreKittingDemandRepository
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import com.hellofresh.skudemandforecast.demandcalculator.substitution.RecipeSkuSubstitutionRepository
import com.hellofresh.skudemandforecast.demandcalculator.substitution.RecipeSkuSubstitutionService
import com.hellofresh.skudemandforecast.demandcalculator.substitution.Sku as SubSKU
import com.hellofresh.skudemandforecast.demandcalculator.substitution.SkuIn
import com.hellofresh.skudemandforecast.demandcalculator.substitution.Substitution
import com.hellofresh.skudemandforecast.demandcalculator.substitution.SubstitutionRepository
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepository
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking

class AggregateDemandServiceTest {

    private val demandQueryMock: DemandQuery = mockk()
    private val substitutionRepositoryMock: SubstitutionRepository = mockk(relaxed = true)
    private val recipeSkuSubstitutionRepositoryMock: RecipeSkuSubstitutionRepository = mockk(relaxed = true)

    private val recipeSkuSubstitutionService: RecipeSkuSubstitutionService = mockk()

    private val aggregatedDemandRepoMock: AggregatedDemandRepository = mockk()
    private val dcRepository: DcRepository = mockk()

    private val anzPreKittingDemandRepository: AnzPreKittingDemandRepository = mockk(relaxed = true)
    private val crossDemandService: CrossDockingService = mockk()
    private val anzDcs = setOf("WA", "PH", "ML", "VT", "SY", "NZ")
    private val anzDc = anzDcs.random()
    private val prekittingService = PrekittingService(anzDcs = anzDcs, anzPreKittingDemandRepository)
    private val distributionCenter = DistributionCenter(
        dcCode = "VE",
        productionStart = FRIDAY,
        market = "DACH",
        zoneId = ZoneId.systemDefault(),
        enabled = true,
        globalDc = null,
    )
    private val markets = setOf(distributionCenter.market)
    private val dcCode = distributionCenter.dcCode
    private val dcCodes = setOf(distributionCenter.dcCode)

    private lateinit var dcConfigService: DcConfigService
    private lateinit var service: AggregateDemandService

    @BeforeTest
    fun init() {
        val slot = slot<List<AggregatedDemand>>()
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(distributionCenter)
        coEvery {
            aggregatedDemandRepoMock.upsert(capture(slot))
        } answers { slot.captured }
        coEvery {
            aggregatedDemandRepoMock.fetchExistingKeys(any(), any())
        } answers { setOf() }
        coEvery {
            aggregatedDemandRepoMock.fetchAggregatedDemand(any(), any())
        } answers { emptyList() }

        coEvery {
            crossDemandService.processCrossDocking(any())
        } returnsArgument 0

        dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)

        service = AggregateDemandService(
            demandQueryMock,
            aggregatedDemandRepoMock,
            substitutionRepositoryMock,
            dcConfigService,
            StatsigTestFeatureFlagClient(emptySet()),
            RecipeSkuSubstitutionService(recipeSkuSubstitutionRepositoryMock),
            crossDemandService,
            anzDcs,
            prekittingService,
        )
    }

    @Test
    fun `should aggregate two SKUs in the same DC with different parent ids`() {
        // given
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val parent1 = Sku(UUID.randomUUID(), "parent1")
        val aggregatedDemand1 = AggregatedDemand(
            sku = sku1,
            parentSku = parent1,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
        )

        val sku2 = Sku(UUID.randomUUID(), "code2")
        val parent2 = Sku(UUID.randomUUID(), "parent2")
        val aggregatedDemand2 = AggregatedDemand(
            sku = sku2,
            parentSku = parent2,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemand1, aggregatedDemand2)
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = "EU"),
        )
        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(4, aggregatedDemands.size)
        assertEquals(aggregatedDemand1, aggregatedDemands.first { aggregatedDemand1.sku == it.sku })
        assertEquals(aggregatedDemand2, aggregatedDemands.first { aggregatedDemand2.sku == it.sku })
        assertEquals(
            aggregatedDemand1.copy(sku = parent1, parentSku = null, dcCode = "EU"),
            aggregatedDemands.first { parent1 == it.sku },
        )
        assertEquals(
            aggregatedDemand2.copy(sku = parent2, parentSku = null, dcCode = "EU"),
            aggregatedDemands.first { parent2 == it.sku },
        )
    }

    @Test
    fun `should aggregate two unrelated SKUs in the same DC with null parentIds`() {
        // given

        val sku1 = Sku(UUID.randomUUID(), "code1")

        val aggregatedDemand1 = AggregatedDemand(
            sku = sku1,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
        )

        val sku2 = Sku(UUID.randomUUID(), "code2")
        val aggregatedDemand2 = AggregatedDemand(
            sku = sku2,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemand1, aggregatedDemand2)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(2, aggregatedDemands.size)
        assertEquals(aggregatedDemand1, aggregatedDemands.first { aggregatedDemand1.sku == it.sku })
        assertEquals(aggregatedDemand2, aggregatedDemands.first { aggregatedDemand2.sku == it.sku })
    }

    @Test
    fun `should aggregate two unrelated SKUs in the same DC, one with parentId and the other with null parentId`() {
        // given
        val parent1 = Sku(UUID.randomUUID(), "parent1")

        val sku1 = Sku(UUID.randomUUID(), "code1")
        val aggregatedDemand1 = AggregatedDemand(
            sku = sku1,
            parentSku = parent1,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
        )

        val sku2 = Sku(UUID.randomUUID(), "code2")
        val aggregatedDemand2 = AggregatedDemand(
            sku = sku2,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemand1, aggregatedDemand2)
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = "EU"),
        )

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(3, aggregatedDemands.size)
        assertEquals(aggregatedDemand1, aggregatedDemands.first { aggregatedDemand1.sku == it.sku })
        assertEquals(aggregatedDemand2, aggregatedDemands.first { aggregatedDemand2.sku == it.sku })
        assertEquals(
            aggregatedDemand1.copy(sku = parent1, parentSku = null, dcCode = "EU"),
            aggregatedDemands.first { parent1 == it.sku },
        )
    }

    @Test
    fun `should remove demand if the substitution was deleted`() {
        // given
        val aggregateDemandServiceTemp = AggregateDemandService(
            demandQueryMock,
            aggregatedDemandRepoMock,
            substitutionRepositoryMock,
            DcConfigService(SimpleMeterRegistry(), dcRepository),
            StatsigTestFeatureFlagClient(setOf(SkuSubstitution(DC, dcCode))),
            recipeSkuSubstitutionService,
            crossDemandService,
            anzDcs,
            prekittingService,
        )
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val skuOutSub = SubSKU(id = sku1.id, code = sku1.code, null, null)

        val aggregatedDemandSubOut = AggregatedDemand(
            sku = sku1,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.Companion.default(
                    recipeIndex = 1,
                    qty = 100L,
                ),
            ),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
        )

        val skuIn = Sku(UUID.randomUUID(), "code2")
        val skuInSub = SubSKU(skuIn.id, skuIn.code, null, null)

        val sub = Substitution(
            UUID.randomUUID(),
            date = LocalDate.now(),
            dcCode,
            skuOutSub,
            aggregatedDemandSubOut.totalQty,
            skuIn = mapOf(skuInSub to SkuIn(200, null, false)),
            fullDayOut = false,
            picksOut = null,
        )

        val expectedAggregatedDemandSubIn = AggregatedDemand(
            sku = skuIn,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
        )

        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemandSubOut)
        coEvery { substitutionRepositoryMock.fetchActive(dcCodes, any()) } returns listOf(sub)
        coEvery { substitutionRepositoryMock.fetchSkuSpecificationRecords(markets) } returns
            mapOf(
                skuIn.id to SkuSpecificationRecord().apply {
                    id = skuIn.id
                    code = skuIn.code
                },
                sku1.id to SkuSpecificationRecord().apply {
                    id = sku1.id
                    code = sku1.code
                },
            )

        // when
        val aggregatedDemands = runBlocking { aggregateDemandServiceTemp.aggregate(markets) }

        // then
        assertEquals(2, aggregatedDemands.size)

        assertEquals(
            aggregatedDemandSubOut.copy(
                substitutions = Substitutions(
                    subOut = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = sub.qtyOut!!,
                        ),
                    ),
                ),
                recipeBreakdowns = listOf(
                    RecipeBreakdownLine.Companion.default(
                        recipeIndex = 1,
                        qty = 100L,
                    ),
                ),
            ),
            aggregatedDemands.first { aggregatedDemandSubOut.sku == it.sku },
        )
        assertEquals(
            expectedAggregatedDemandSubIn.copy(
                recipeBreakdowns = aggregatedDemandSubOut.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                substitutions = Substitutions(
                    subIn = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = 200,
                        ),
                    ),
                ),
            ),
            aggregatedDemands.first { expectedAggregatedDemandSubIn.sku == it.sku },
        )

        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemandSubOut)
        coEvery {
            substitutionRepositoryMock.fetchActive(dcCodes, any())
        } returns listOf(sub.copy(qtyOut = 0, skuIn = mapOf(skuInSub to SkuIn(0, null, false))))

        val secondAggregatedDemand = runBlocking { aggregateDemandServiceTemp.aggregate(markets) }

        assertEquals(2, secondAggregatedDemand.size)

        assertEquals(aggregatedDemandSubOut, secondAggregatedDemand.first { aggregatedDemandSubOut.sku == it.sku })
        assertEquals(
            expectedAggregatedDemandSubIn.copy(
                recipeBreakdowns = aggregatedDemandSubOut.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                substitutions = Substitutions(
                    subIn = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = 0L,
                        ),
                    ),
                ),
            ),
            secondAggregatedDemand.first { expectedAggregatedDemandSubIn.sku == it.sku },
        )
    }

    @Test
    fun `should aggregate two SKUs in different DCs and having the same parentId`() {
        // given
        val commonParentId = Sku(UUID.randomUUID(), "parent1")

        val sku1 = Sku(UUID.randomUUID(), "code1")
        val recipeBreakdowns1 = listOf(
            RecipeBreakdownLine.Companion.default(
                recipeIndex = 1,
                qty = 100L,
            ),
        )
        val aggregatedDemand1 =
            AggregatedDemand(
                sku = sku1,
                parentSku = commonParentId,
                dcCode = dcCode,
                productionDate = LocalDate.now(),
                recipeBreakdowns = recipeBreakdowns1,
            )

        val dcCode2 = "CH"
        val sku2 = Sku(UUID.randomUUID(), "code2")
        val recipeBreakdowns2 = listOf(
            RecipeBreakdownLine.Companion.default(
                recipeIndex = 2,
                qty = 200L,
            ),
        )
        val aggregatedDemand2 =
            AggregatedDemand(
                sku = sku2,
                parentSku = commonParentId,
                dcCode = dcCode2,
                productionDate = LocalDate.now(),
                recipeBreakdowns = recipeBreakdowns2,
            )

        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = "EU"),
            distributionCenter.copy(dcCode = dcCode2, globalDc = "EU"),
        )
        coEvery {
            demandQueryMock.aggregateFractionDemands(setOf(dcCode, dcCode2))
        } returns listOf(aggregatedDemand1, aggregatedDemand2)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(3, aggregatedDemands.size)
        assertEquals(aggregatedDemand1, aggregatedDemands.first { aggregatedDemand1.sku == it.sku })
        assertEquals(aggregatedDemand2, aggregatedDemands.first { aggregatedDemand2.sku == it.sku })

        val aggregatedDemand = aggregatedDemands.find { it.sku.id == commonParentId.id }
        assertNotNull(aggregatedDemand)
        assertEquals((recipeBreakdowns1 + recipeBreakdowns2).toSet(), aggregatedDemand.recipeBreakdowns.toSet())
        assertEquals(
            recipeBreakdowns1.sumOf { it.qty } + recipeBreakdowns2.sumOf { it.qty },
            aggregatedDemand.totalRecipeBreakDownQty(),
        )
        assertEquals("EU", aggregatedDemand.dcCode)
    }

    @Test
    fun `should aggregate two SKUs in global dc with some dc not part of current calculation`() {
        // given
        val commonParentId = Sku(UUID.randomUUID(), "parent1")

        val sku1 = Sku(UUID.randomUUID(), "code1")
        val recipeBreakdowns1 = listOf(
            RecipeBreakdownLine.Companion.default(
                recipeIndex = 1,
                qty = 100L,
            ),
        )
        val aggregatedDemand1 =
            AggregatedDemand(
                sku = sku1,
                parentSku = commonParentId,
                dcCode = dcCode,
                productionDate = LocalDate.now(),
                recipeBreakdowns = recipeBreakdowns1,
            )

        val dcCode2 = "CH"
        val sku2 = Sku(UUID.randomUUID(), "code2")
        val recipeBreakdowns2 = listOf(
            RecipeBreakdownLine.Companion.default(
                recipeIndex = 2,
                qty = 200L,
            ),
        )
        val aggregatedDemand2 =
            AggregatedDemand(
                sku = sku2,
                parentSku = commonParentId,
                dcCode = dcCode2,
                productionDate = LocalDate.now(),
                recipeBreakdowns = recipeBreakdowns2,
            )
        val dcCode3 = "D3"
        val aggregatedDemand3 =
            AggregatedDemand(
                sku = Sku(UUID.randomUUID(), "code3"),
                parentSku = Sku(UUID.randomUUID(), "parent3"),
                dcCode = dcCode3,
                productionDate = LocalDate.now(),
                recipeBreakdowns = emptyList(),
            )

        coEvery {
            aggregatedDemandRepoMock.fetchAggregatedDemand(
                setOf(aggregatedDemand2.dcCode),
                match { it <= aggregatedDemand2.productionDate },
            )
        } returns listOf(aggregatedDemand2)

        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = dcCode3, globalDc = "G2"),
            distributionCenter.copy(dcCode = "EU"),
            distributionCenter.copy(dcCode = "G2", market = "M2"),
            distributionCenter.copy(dcCode = dcCode2, market = "M2", globalDc = "EU"),
        )
        coEvery {
            demandQueryMock.aggregateFractionDemands(match { it.containsAll(setOf(dcCode, dcCode3)) })
        } returns listOf(aggregatedDemand1, aggregatedDemand3)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(3, aggregatedDemands.size)
        assertEquals(aggregatedDemand1, aggregatedDemands.first { aggregatedDemand1.sku == it.sku })
        assertEquals(aggregatedDemand3, aggregatedDemands.first { aggregatedDemand3.sku == it.sku })

        val aggregatedDemand = aggregatedDemands.first { it.sku.id == commonParentId.id }
        assertEquals((recipeBreakdowns1 + recipeBreakdowns2).toSet(), aggregatedDemand.recipeBreakdowns.toSet())
        assertEquals(
            recipeBreakdowns1.sumOf { it.qty } + recipeBreakdowns2.sumOf { it.qty },
            aggregatedDemand.totalRecipeBreakDownQty(),
        )
        assertEquals("EU", aggregatedDemand.dcCode)
    }

    @Test
    fun `should sum demand for two skus with a common parentId each belonging to the same recipe index in two different markets`() {
        // given
        val commonParentId = Sku(UUID.randomUUID(), "parent1")

        val sku1 = Sku(UUID.randomUUID(), "code1")
        val recipeIndex = 1
        val qty1 = 100L
        val recipeBreakdowns1 = listOf(RecipeBreakdownLine.Companion.default(recipeIndex = recipeIndex, qty = qty1))
        val aggregatedDemandDach =
            AggregatedDemand(sku1, commonParentId, dcCode, LocalDate.now(), recipeBreakdowns1)

        val dcCodeDH = "DH"
        val qty2 = 200L
        val recipeBreakdowns2 = listOf(RecipeBreakdownLine.Companion.default(recipeIndex = recipeIndex, qty = qty2))
        val aggregatedDemandBenelux = AggregatedDemand(
            sku1,
            commonParentId,
            dcCodeDH,
            LocalDate.now(),
            recipeBreakdowns2,
        )

        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = "EU"),
            distributionCenter.copy(dcCode = dcCodeDH, globalDc = "EU"),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(setOf(dcCode, dcCodeDH)) } returns listOf(
            aggregatedDemandDach,
            aggregatedDemandBenelux,
        )

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        val aggregatedDemand = aggregatedDemands.find { it.sku.id == commonParentId.id }
        assertEquals(recipeIndex, aggregatedDemand?.recipeBreakdowns?.size)
        val demandResult = aggregatedDemand?.recipeBreakdowns?.first()
        assertEquals(1, demandResult?.recipeIndex)
        assertEquals(qty1 + qty2, demandResult?.qty)
    }

    @Test fun `ignore if a dc has no globalDc for the aggregation`() {
        val parentSku = Sku(UUID.randomUUID(), "parent1")

        val aggregatedDemand = AggregatedDemand.random().copy(parentSku = parentSku)
        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemand)
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "GD"),
            distributionCenter.copy(dcCode = "GD"),
        )
        // when
        var aggregatedDemands = runBlocking { service.aggregate(markets) }
        assertNotNull(aggregatedDemands)
        assertTrue { aggregatedDemands.isNotEmpty() }
        assertTrue { aggregatedDemands.any { it.sku == parentSku } }

        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(distributionCenter.copy(globalDc = null))
        dcConfigService.fetchOnDemand()

        aggregatedDemands = runBlocking { service.aggregate(markets) }
        assertTrue { aggregatedDemands.none { it.sku == parentSku } }
    }

    @Test
    fun `should process aggregated demands for ANZ`() {
        // given
        val dcCode = "SY"
        val aggregateDemandServiceTemp = AggregateDemandService(
            demandQueryMock,
            aggregatedDemandRepoMock,
            substitutionRepositoryMock,
            DcConfigService(SimpleMeterRegistry(), dcRepository),
            StatsigTestFeatureFlagClient(setOf(SkuSubstitution(DC, dcCode))),
            recipeSkuSubstitutionService,
            crossDemandService,
            anzDcs,
            prekittingService,
        )
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val aggregatedDemand1 = AggregatedDemand(
            sku = sku1,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = listOf(RecipeBreakdownLine.Companion.default(recipeIndex = 1000, qty = 50)),
            substitutions = null,
            prekittings = null,
        )

        val sku2 = Sku(UUID.randomUUID(), "code2")
        val aggregatedDemand2 = AggregatedDemand(
            sku = sku2,
            parentSku = null,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
            substitutions = null,
            prekittings = null,
        )
        val skuInSkuId = UUID.randomUUID()
        val substitution = Substitution(
            id = sku1.id,
            date = LocalDate.now(),
            dcCode = dcCode,
            skuOut = SubSKU(
                id = sku1.id,
                code = sku1.code,
                parentId = null,
                parentCode = null,
            ),
            qtyOut = 50,
            skuIn = mapOf(
                SubSKU(
                    id = skuInSkuId,
                    code = "code10",
                    parentId = null,
                    parentCode = null,
                ) to SkuIn(50, 1, true),
            ),
            fullDayOut = true,
            picksOut = 1,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(dcCode = dcCode, globalDc = null),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(setOf(dcCode)) } returns listOf(aggregatedDemand1, aggregatedDemand2)
        coEvery { recipeSkuSubstitutionService.prepareSubstitutions(any(), any()) } returns listOf(substitution)

        // when
        val aggregatedDemands = runBlocking { aggregateDemandServiceTemp.aggregate(markets) }

        // then
        assertEquals(3, aggregatedDemands.size)
        assertEquals(
            50,
            aggregatedDemands.first { aggregatedDemand1.sku == it.sku }.substitutions!!.subOut.sumOf { it.qty },
        )
        assertEquals(50, aggregatedDemands.first { skuInSkuId == it.sku.id }.substitutions!!.subIn.sumOf { it.qty })
        assertEquals(aggregatedDemand2, aggregatedDemands.first { aggregatedDemand2.sku == it.sku })
        coVerify(exactly = 1) { recipeSkuSubstitutionService.prepareSubstitutions(any(), any()) }
    }

    @Test fun `aggregate sourceIds when aggregating for the parent sku`() {
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val parent = Sku(UUID.randomUUID(), "parent1")
        val aggregatedDemand1 = AggregatedDemand(
            sku = sku1,
            parentSku = parent,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
            sourceIds = setOf(UUID.randomUUID(), UUID.randomUUID()),
        )

        val sku2 = Sku(UUID.randomUUID(), "code2")
        val aggregatedDemand2 = AggregatedDemand(
            sku = sku2,
            parentSku = parent,
            dcCode = dcCode,
            productionDate = LocalDate.now(),
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
            sourceIds = setOf(UUID.randomUUID(), UUID.randomUUID()),
        )
        coEvery { demandQueryMock.aggregateFractionDemands(dcCodes) } returns listOf(aggregatedDemand1, aggregatedDemand2)
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(globalDc = "EU"),
            distributionCenter.copy(dcCode = "EU"),
        )
        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(
            aggregatedDemand1.sourceIds + aggregatedDemand2.sourceIds,
            aggregatedDemands.first { it.sku == parent }.sourceIds,
        )
    }

    @Test
    fun `should return aggregated demand with total quantity with anz pre kitting demand, fumigated, regular`() {
        // given
        val sku = Sku(UUID.randomUUID(), "code1")
        val dcCode = anzDc
        val yesterday = LocalDate.now().minusDays(1)
        val today = LocalDate.now()
        val tomorrow = LocalDate.now().plusDays(1)
        val nextDay = LocalDate.now().plusDays(2)

        val aggregatedDemandYesterday = createAggregatedDemand(sku, dcCode, yesterday)
        val aggregatedDemandToday = createAggregatedDemand(sku, dcCode, today)
        val aggregatedDemandTomorrow = createAggregatedDemand(sku, dcCode, tomorrow)
        val aggregatedDemandNextDay = createAggregatedDemand(sku, dcCode, nextDay)

        val preKittingDemands = listOf(
            AnzPreKittingDemand(dcCode, sku.id, 750, yesterday),
            AnzPreKittingDemand(dcCode, sku.id, 750, today),
            AnzPreKittingDemand(dcCode, sku.id, 750, tomorrow),
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(dcCode = dcCode),
        )
        coEvery {
            anzPreKittingDemandRepository.getPreKittingDemand(setOf(dcCode), yesterday)
        } returns preKittingDemands
        coEvery {
            demandQueryMock.aggregateFractionDemands(setOf(dcCode))
        } returns
            listOf(aggregatedDemandYesterday, aggregatedDemandToday, aggregatedDemandTomorrow, aggregatedDemandNextDay)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(4, aggregatedDemands.size)
        assertTotalQty(2250, aggregatedDemands, yesterday)
        assertTotalQty(1500, aggregatedDemands, today)
        assertTotalQty(1500, aggregatedDemands, tomorrow)
        assertTotalQty(750, aggregatedDemands, nextDay)
        assertPreKittingValues(aggregatedDemands, yesterday, 500, 250, 0, 0)
        assertPreKittingValues(aggregatedDemands, today, 500, 250, 500, 250)
        assertPreKittingValues(aggregatedDemands, tomorrow, 500, 250, 500, 250)
        assertPreKittingValues(aggregatedDemands, nextDay, 0, 0, 500, 250)
    }

    @Test
    fun `should return aggregated demand with total quantity with anz - considering the prekitting WITHOUT fumigated, regular`() {
        // given
        val sku = Sku(UUID.randomUUID(), "code1")
        val dcCode = anzDc
        val yesterday = LocalDate.now().minusDays(1)
        val today = LocalDate.now()
        val tomorrow = LocalDate.now().plusDays(1)
        val nextDay = LocalDate.now().plusDays(2)

        val aggregatedDemandYesterday = createAggregatedDemandWithoutDemandType(sku, dcCode, yesterday)
        val aggregatedDemandToday = createAggregatedDemandWithoutDemandType(sku, dcCode, today)
        val aggregatedDemandTomorrow = createAggregatedDemandWithoutDemandType(sku, dcCode, tomorrow)
        val aggregatedDemandNextDay = createAggregatedDemandWithoutDemandType(sku, dcCode, nextDay)

        val preKittingDemands = listOf(
            AnzPreKittingDemand(dcCode, sku.id, 750, yesterday),
            AnzPreKittingDemand(dcCode, sku.id, 750, today),
            AnzPreKittingDemand(dcCode, sku.id, 750, tomorrow),
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(dcCode = dcCode),
        )
        coEvery {
            anzPreKittingDemandRepository.getPreKittingDemand(setOf(dcCode), yesterday)
        } returns preKittingDemands
        coEvery {
            demandQueryMock.aggregateFractionDemands(setOf(dcCode))
        } returns
            listOf(aggregatedDemandYesterday, aggregatedDemandToday, aggregatedDemandTomorrow, aggregatedDemandNextDay)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(4, aggregatedDemands.size)
        assertTotalQty(2250, aggregatedDemands, yesterday)
        assertTotalQty(1500, aggregatedDemands, today)
        assertTotalQty(1500, aggregatedDemands, tomorrow)
        assertTotalQty(750, aggregatedDemands, nextDay)
        assertPreKittingValues(aggregatedDemands, yesterday, 750, 0)
        assertPreKittingValues(aggregatedDemands, today, 750, 750)
        assertPreKittingValues(aggregatedDemands, tomorrow, 750, 750)
        assertPreKittingValues(aggregatedDemands, nextDay, 0, 750)
    }

    @Suppress("LongParameterList")
    private fun assertPreKittingValues(
        aggregatedDemands: List<AggregatedDemand>,
        date: LocalDate,
        expectedInFumigated: Long,
        expectedInRegular: Long,
        expectedOutFumigated: Long,
        expectedOutRegular: Long
    ) {
        val demand = aggregatedDemands.find { it.productionDate == date }
        assertEquals(
            expectedInFumigated,
            demand?.prekittings?.prekittingIn?.filter { it.demandType == FUMIGATED }?.sumOf {
                it.qty
            },
        )
        assertEquals(
            expectedInRegular,
            demand?.prekittings?.prekittingIn?.filter { it.demandType == REGULAR }?.sumOf {
                it.qty
            },
        )
        assertEquals(
            expectedOutFumigated,
            demand?.prekittings?.prekittingOut?.filter {
                it.demandType == FUMIGATED
            }?.sumOf { it.qty },
        )
        assertEquals(
            expectedOutRegular,
            demand?.prekittings?.prekittingOut?.filter {
                it.demandType == REGULAR
            }?.sumOf { it.qty },
        )
    }

    private fun assertPreKittingValues(
        aggregatedDemands: List<AggregatedDemand>,
        date: LocalDate,
        expectedIn: Long,
        expectedOut: Long,
    ) {
        val demand = aggregatedDemands.find { it.productionDate == date }
        assertEquals(
            expectedIn,
            demand?.prekittings?.prekittingIn?.sumOf {
                it.qty
            },
        )
        assertEquals(
            expectedIn,
            demand?.prekittings?.prekittingIn?.sumOf {
                it.qty
            },
        )
        assertEquals(
            expectedOut,
            demand?.prekittings?.prekittingOut?.sumOf { it.qty },
        )
        assertEquals(
            expectedOut,
            demand?.prekittings?.prekittingOut?.sumOf { it.qty },
        )
    }

    private fun assertTotalQty(qty: Long, aggregatedDemands: List<AggregatedDemand>, date: LocalDate) = assertEquals(
        qty,
        aggregatedDemands.find { it.productionDate == date }?.totalQty,
    )

    private fun createAggregatedDemandWithoutDemandType(sku: Sku, dcCode: String, date: LocalDate): AggregatedDemand =
        AggregatedDemand(
            sku = sku,
            parentSku = null,
            dcCode = dcCode,
            productionDate = date,
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.Companion.default(recipeIndex = 1, qty = 500L),
                RecipeBreakdownLine.Companion.default(recipeIndex = 1, qty = 1000L),
            ),
        )

    private fun createAggregatedDemand(sku: Sku, dcCode: String, date: LocalDate): AggregatedDemand =
        AggregatedDemand(
            sku = sku,
            parentSku = null,
            dcCode = dcCode,
            productionDate = date,
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.Companion.default(recipeIndex = 1, qty = 500L, demandType = FUMIGATED),
                RecipeBreakdownLine.Companion.default(recipeIndex = 1, qty = 1000L, demandType = REGULAR),
            ),
        )

    @Test
    fun `should return aggregated demand with totalQty with zero preKittingQty if there's no day before demand`() {
        // given
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val dcCode = anzDc
        val today = LocalDate.now()
        val yesterday = LocalDate.now().minusDays(1)
        val aggregatedDemand = AggregatedDemand(
            sku = sku1,
            parentSku = null,
            dcCode = dcCode,
            productionDate = today,
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
        )

        val preKittingDemands = listOf(
            AnzPreKittingDemand(dcCode, sku1.id, 5, today),
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(dcCode = anzDc),
        )
        coEvery {
            anzPreKittingDemandRepository.getPreKittingDemand(setOf(dcCode), yesterday)
        } returns preKittingDemands
        coEvery {
            demandQueryMock.aggregateFractionDemands(setOf(anzDc))
        } returns listOf(aggregatedDemand, aggregatedDemand.copy(productionDate = yesterday))

        val expectedTotalQtyWithPreKitting =
            aggregatedDemand.totalRecipeBreakDownQty() + preKittingDemands[0].demandQty - 0L // no demand for the day before
        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(2, aggregatedDemands.size)
        assertEquals(
            expectedTotalQtyWithPreKitting,
            aggregatedDemands.first { aggregatedDemand.sku == it.sku && it.productionDate == today }.totalQty,
        )
    }

    @Test
    fun `should return aggregated demand for days with no demand but prekitting`() {
        // given
        val sku = Sku(UUID.randomUUID(), "code1")
        val dcCode = anzDc
        val date1 = LocalDate.now()
        val date2 = LocalDate.now().minusDays(1)
        val aggregatedDemand = AggregatedDemand(
            sku = sku,
            parentSku = null,
            dcCode = dcCode,
            productionDate = date2,
            recipeBreakdowns = emptyList(),
            substitutions = Substitutions(
                subOut = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
                subIn = listOf(
                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(qty = 0L),
                ),
            ),
        )

        val preKittingDemands = listOf(
            AnzPreKittingDemand(dcCode, sku.id, 5, date1),
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(
            distributionCenter.copy(dcCode = anzDc),
        )
        coEvery {
            anzPreKittingDemandRepository.getPreKittingDemand(setOf(dcCode), date2)
        } returns preKittingDemands
        coEvery {
            demandQueryMock.aggregateFractionDemands(setOf(anzDc))
        } returns listOf(aggregatedDemand)

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(2, aggregatedDemands.size)
        assertEquals(
            aggregatedDemands[1].prekittings!!.prekittingIn[0].qty,
            5L,
        )
        assertEquals(aggregatedDemands[1].sku.id, sku.id)
    }

    @Test
    fun `should return aggregated demand with null totalQty if there's no matching anz demand`() {
        // given
        val sku1 = Sku(UUID.randomUUID(), "code1")
        val parent1 = Sku(UUID.randomUUID(), "parent1")
        val dcCode = anzDc
        val today = LocalDate.now()
        val yesterday = LocalDate.now().minusDays(1)
        val aggregatedDemand = AggregatedDemand(
            sku = sku1,
            parentSku = parent1,
            dcCode = dcCode,
            productionDate = today,
            recipeBreakdowns = emptyList(),
            substitutions = null,
            prekittings = null,
        )

        coEvery {
            anzPreKittingDemandRepository.getPreKittingDemand(setOf(dcCode), yesterday)
        } returns listOf()
        coEvery {
            demandQueryMock.aggregateFractionDemands(dcCodes)
        } returns
            listOf(aggregatedDemand, aggregatedDemand.copy(productionDate = yesterday))

        // when
        val aggregatedDemands = runBlocking { service.aggregate(markets) }

        // then
        assertEquals(2, aggregatedDemands.size)
        assertEquals(aggregatedDemand, aggregatedDemands.first { it.productionDate == today })
        assertEquals(aggregatedDemand.totalQty, 0)
    }
}
