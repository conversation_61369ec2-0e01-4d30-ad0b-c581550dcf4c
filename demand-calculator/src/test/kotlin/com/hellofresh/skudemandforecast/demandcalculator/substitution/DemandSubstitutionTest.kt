@file:Suppress("MaximumLineLength", "LongMethod")

package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.hellofresh.skuDemandForecast.featureflags.Context.DC
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku as AggregatedDemandSku
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.default
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.getRecipeBreakDownWithSubs
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.random
import com.hellofresh.skudemandforecast.demandcalculator.substitution.DemandSubstitutions.GroupingKey
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class DemandSubstitutionTest {

    // Parameterized test
    @ParameterizedTest
    @MethodSource("mergeTestArguments")
    fun `merge function`(
        name: String,
        originalDemand: MutableList<AggregatedDemand>,
        newDemand: AggregatedDemand,
        expected: MutableList<AggregatedDemand>
    ) {
        val origin = originalDemand.associateBy { GroupingKey.from(it) }.toMutableMap()
        val expectedMap = expected.associateBy { GroupingKey.from(it) }.toMutableMap()
        val actual = origin.merge(newDemand)
        assertEquals(expectedMap, actual, name)
    }

    @ParameterizedTest
    @MethodSource("substitutionTestArguments")
    fun `substitution function`(
        name: String,
        demand: AggregatedDemand,
        sub: Substitution,
        expected: List<AggregatedDemand>,
    ) {
        val actual = substitute(demand, listOf(sub)).sortedBy { it.sku.id }
        assertEquals(expected.sortedBy { it.sku.id }, actual, name)
    }

    @ParameterizedTest
    @MethodSource("fullDaySubstitutionTestArguments")
    fun testFullDaySubstitution(
        name: String,
        demand: AggregatedDemand,
        sub: Substitution,
        expected: List<AggregatedDemand>,
        errorExpected: Throwable?,
    ) {
        if (errorExpected != null) {
            assertThrows<Exception> {
                substitute(demand, listOf(sub)).sortedBy { it.sku.id }
            }
        } else {
            val actual = substitute(demand, listOf(sub)).sortedBy { it.sku.id }
            assertEquals(expected.sortedBy { it.sku.id }, actual, name)
        }
    }

    @Test
    fun `perform function doesn't substitute when substitution key does not match`() {
        val substitutions = DemandSubstitutions(
            substitutions = listOf(Substitution.random(), Substitution.random()),
            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet()),
        )
        val originalDemand = listOf(AggregatedDemand.random(), AggregatedDemand.random())
        val newDemand = substitutions.perform(originalDemand)
        assertEquals(originalDemand, newDemand)
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `perform function adds the new demand to the subbed in sku and removes the demand from subbed out`(
        enableSkuSubstitutionFlagResult: Boolean
    ) {
        val skuOut = Sku.random().copy(parentId = null, parentCode = null)
        val skuIn = Sku.random().copy(parentId = null, parentCode = null)
        val sub = Substitution.random()
        val date = LocalDate.now()
        val demand = AggregatedDemand.random()
        val skuOutDemand = demand.copy(sku = AggregatedDemandSku(skuOut.id, skuOut.code), productionDate = date)
        val skuInDemand = demand.copy(sku = AggregatedDemandSku(skuIn.id, skuIn.code), productionDate = date)
        val skuInDemandQty = skuInDemand.totalRecipeBreakDownQty()
        val skuOutDemandQty = skuOutDemand.totalRecipeBreakDownQty()

        val substitutions = DemandSubstitutions(
            substitutions = listOf(
                // half of the sku1 demand is subbed
                sub.copy(
                    skuOut = skuOut,
                    date = date,
                    skuIn = mapOf(skuIn to SkuIn(skuInDemandQty, null)),
                    qtyOut = skuOutDemandQty,
                ),
            ),
            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                setOf(FeatureFlag.SkuSubstitution(DC, sub.dcCode)),
            ),
        )
        val originalDemand = listOf(skuInDemand, skuOutDemand)
        val newDemand = substitutions.perform(
            originalDemand,
        )
        assertEquals(originalDemand.size, newDemand.size)
        if (enableSkuSubstitutionFlagResult) {
            assertEquals(
                skuOutDemandQty,
                newDemand.find { it.sku == AggregatedDemandSku(skuOut.id, skuOut.code) }!!.substitutions!!.subOut.sumOf { it.qty },
            )
            assertEquals(
                skuOutDemandQty + skuInDemandQty,
                newDemand.find { it.sku == AggregatedDemandSku(skuIn.id, skuIn.code) }!!.getRecipeBreakDownWithSubs().values.sum(),
            )

            // originalQty is unchanged
            assertEquals(
                skuOutDemand.totalRecipeBreakDownQty(),
                newDemand.find { it.sku == AggregatedDemandSku(skuIn.id, skuIn.code) }!!.totalRecipeBreakDownQty(),
            )
        }
    }

    @Test
    fun `perform function merges demand when 2 different skus are subbed in with same sku`() {
        val skuOut1 = Sku.random().copy(parentId = null, parentCode = null)
        val skuOut2 = Sku.random().copy(parentId = null, parentCode = null)
        val skuIn = Sku.random().copy(parentId = null, parentCode = null)
        val sub = Substitution.random()
        val date = LocalDate.now()
        val skuOut2Demand = AggregatedDemand.random().copy(
            sku = AggregatedDemandSku(skuOut2.id, skuOut2.code),
            productionDate = date,
        )
        val skuOut1Demand = AggregatedDemand.random().copy(
            sku = AggregatedDemandSku(skuOut1.id, skuOut1.code),
            productionDate = date,
        )
        val skuInDemand = AggregatedDemand.random().copy(
            sku = AggregatedDemandSku(skuIn.id, skuIn.code),
            productionDate = date,
        )
        val skuOut1DemandQty = skuOut1Demand.totalRecipeBreakDownQty()
        val skuOut2DemandQty = skuOut2Demand.totalRecipeBreakDownQty()
        val skuInDemandQty = skuInDemand.totalRecipeBreakDownQty()

        val substitutions = DemandSubstitutions(
            substitutions = listOf(
                // half of the sku1 demand is subbed
                sub.copy(
                    skuOut = skuOut1,
                    date = date,
                    skuIn = mapOf(skuIn to SkuIn(skuOut1DemandQty, null)),
                    qtyOut = skuOut1DemandQty,
                ),
                sub.copy(
                    skuOut = skuOut2,
                    date = date,
                    skuIn = mapOf(skuIn to SkuIn(skuOut2DemandQty, null)),
                    qtyOut = skuOut2DemandQty,
                ),

            ),
            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                setOf(FeatureFlag.SkuSubstitution(DC, sub.dcCode)),
            ),
        )
        val originalDemands = listOf(skuOut1Demand, skuOut2Demand, skuInDemand)
        val newDemands = substitutions.perform(
            originalDemands,
        )
        assertEquals(originalDemands.size, newDemands.size)

        assertEquals(
            skuOut1DemandQty,
            newDemands.find { it.sku == AggregatedDemandSku(skuOut1.id, skuOut1.code) }!!.totalRecipeBreakDownQty(),
        )
        assertEquals(
            skuOut2DemandQty,
            newDemands.find { it.sku == AggregatedDemandSku(skuOut2.id, skuOut2.code) }!!.totalRecipeBreakDownQty(),
        )

        assertEquals(
            skuOut2DemandQty + skuOut1DemandQty + skuInDemandQty,
            newDemands.find {
                it.sku == AggregatedDemandSku(skuIn.id, skuIn.code)
            }!!.getRecipeBreakDownWithSubs().values.sumOf { it },
        )

        assertEquals(
            skuInDemand.totalRecipeBreakDownQty(),
            newDemands.find { it.sku == AggregatedDemandSku(skuIn.id, skuIn.code) }!!.totalRecipeBreakDownQty(),
        )
    }

    @Test
    fun `perform function removes demand from subbed out when there is no subs in`() {
        val skuOut = Sku.random().copy(parentId = null, parentCode = null)
        val date = LocalDate.now()
        val skuOutDemand = AggregatedDemand.random().copy(
            sku = AggregatedDemandSku(skuOut.id, skuOut.code),
            productionDate = date,
        )
        val skuOutDemandQty = skuOutDemand.totalRecipeBreakDownQty()
        val sub = Substitution.random().copy(skuOut = skuOut, date = date, skuIn = emptyMap(), qtyOut = skuOutDemandQty)

        val substitutions = DemandSubstitutions(
            substitutions = listOf(
                sub.copy(skuOut = skuOut, date = date, skuIn = emptyMap(), qtyOut = skuOutDemandQty),
            ),
            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                setOf(FeatureFlag.SkuSubstitution(DC, sub.dcCode)),
            ),
        )
        val newDemands = substitutions.perform(
            listOf(skuOutDemand),
        )
        assertEquals(1, newDemands.size)
        assertEquals(
            skuOutDemandQty,
            newDemands.first().substitutions!!.subOut.sumOf { it.qty },
        )
    }

    @Suppress("LongParameterList")
    @ParameterizedTest
    @MethodSource("validateSubstitutionArguments")
    fun `substitution creation is subjected to constraints`(
        name: String,
        picksOut: Int?,
        fullDayOut: Boolean,
        qtyOut: Long?,
        picksIn: Int?,
        fullDayIn: Boolean,
        qtyIn: Long?,
    ) {
        assertThrows<IllegalStateException>(name) {
            Substitution.random().copy(
                qtyOut = qtyOut,
                picksOut = picksOut,
                fullDayOut = fullDayOut,
                skuIn = mapOf(Sku.random() to SkuIn(qty = qtyIn, picks = picksIn, fullDay = fullDayIn)),
            )
        }
    }

    @Test
    fun `perform function applies multiple substitutions for sku sub out`() {
        val skuOut = Sku.random().copy(parentId = null, parentCode = null)

        val skuIn1 = Sku.random().copy(parentId = null, parentCode = null)
        val skuIn2 = Sku.random().copy(parentId = null, parentCode = null)

        val skuOutDemand = AggregatedDemand.random().copy(
            sku = AggregatedDemandSku(skuOut.id, skuOut.code),
            productionDate = LocalDate.now(UTC),
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.default().copy(qty = 200),
            ),
        )

        val skuIn1SubInQty = 200L
        val sub1 = Substitution.random().copy(
            qtyOut = 100,
            skuOut = skuOut,
            skuIn = mapOf(skuIn1 to SkuIn(qty = skuIn1SubInQty)),
            date = skuOutDemand.productionDate,
        )

        val skuIn2SubInQty = 300L
        val sub2 = Substitution.random().copy(
            qtyOut = 150,
            skuOut = skuOut,
            skuIn = mapOf(skuIn2 to SkuIn(qty = skuIn2SubInQty)),
            date = skuOutDemand.productionDate,
        )

        val substitutions = DemandSubstitutions(
            substitutions = listOf(sub1, sub2),
            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                setOf(FeatureFlag.SkuSubstitution(DC, sub1.dcCode)),
            ),
        )

        val newDemand = substitutions.perform(
            listOf(skuOutDemand),
        )
        assertEquals(3, newDemand.size)

        assertEquals(
            skuOutDemand.totalRecipeBreakDownQty(),
            newDemand.find { it.sku.id == skuOut.id }?.substitutions!!.subOut.sumOf { it.qty },
        )
        assertEquals(
            skuIn1SubInQty,
            newDemand.find { it.sku.id == skuIn1.id }?.substitutions!!.subIn.sumOf { it.qty },
        )
        assertEquals(
            skuIn1SubInQty,
            newDemand.find { it.sku.id == skuIn1.id }?.substitutions!!.subIn.sumOf { it.qty },
        )

        assertEquals(
            skuIn2SubInQty,
            newDemand.find { it.sku.id == skuIn2.id }?.substitutions!!.subIn.sumOf { it.qty },
        )
        assertEquals(
            skuIn2SubInQty,
            newDemand.find { it.sku.id == skuIn2.id }?.substitutions!!.subIn.sumOf { it.qty },
        )
    }

    @Suppress("LongMethod")
    companion object {

        @JvmStatic
        fun mergeTestArguments(): List<Arguments> {
            val randomRecipe = RecipeBreakdownLine.Companion.random()
            val defaultDemand = AggregatedDemand.default()
            val defaultRecipe = RecipeBreakdownLine.default()
            return listOf<Arguments>(
                Arguments.of(
                    "New Aggregated Demand is added",
                    mutableListOf<AggregatedDemand>(),
                    defaultDemand,
                    mutableListOf(defaultDemand),
                ),

                Arguments.of(
                    "Existing recipe qty is merged",
                    mutableListOf(defaultDemand),
                    defaultDemand.copy(
                        recipeBreakdowns = listOf(defaultRecipe.copy(qty = 100L), randomRecipe),
                        substitutions = null,
                    ),
                    mutableListOf(
                        defaultDemand.copy(
                            recipeBreakdowns = listOf(defaultRecipe.copy(qty = 100 + defaultRecipe.qty), randomRecipe),
                            substitutions = null,
                        ),
                    ),

                ),
                Arguments.of(
                    "sourceIds are aggregated",
                    mutableListOf(
                        defaultDemand.copy(
                            sourceIds = setOf(UUID(0, 0)),
                            substitutions = null,
                        ),
                    ),
                    defaultDemand.copy(
                        recipeBreakdowns = listOf(defaultRecipe.copy(qty = 100L), randomRecipe),
                        sourceIds = setOf(UUID(0, 1)),
                        substitutions = null,
                    ),
                    mutableListOf(
                        defaultDemand.copy(
                            recipeBreakdowns = listOf(defaultRecipe.copy(qty = 100 + defaultRecipe.qty), randomRecipe),
                            sourceIds = setOf(UUID(0, 1), UUID(0, 0)),
                            substitutions = null,
                        ),
                    ),
                ),
            )
        }

        @JvmStatic
        fun fullDaySubstitutionTestArguments(): List<Arguments> {
            val defaultRecipe = RecipeBreakdownLine.default()
            val skuIn1 = Sku.random()
            val skuIn2 = Sku.random()
            val skuOut = Sku.random()
            val qtyOut = 100L
            val sub = Substitution.random().copy(
                qtyOut = qtyOut,
                skuOut = skuOut,
                skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty), skuIn2 to SkuIn(qty = defaultRecipe.qty)),
            )
            val demand = AggregatedDemand(
                sku = AggregatedDemandSku(id = sub.skuOut.id, code = sub.skuOut.code),
                parentSku = AggregatedDemandSku(id = sub.skuOut.parentId!!, code = sub.skuOut.parentCode!!),
                dcCode = sub.dcCode,
                productionDate = sub.date,
                recipeBreakdowns = listOf(defaultRecipe),
                substitutions = Substitutions(
                    subOut = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = defaultRecipe.qty,
                        ),
                    ),
                    subIn = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = 0L
                        ),
                    ),
                ),
            )

            return listOf(
                Arguments.of(
                    "SkuOut must have picks as non 0 if full day is enabled",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty / 2,
                        picksOut = 0,
                        fullDayOut = true,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty / 2, picks = 1, fullDay = true)),
                    ),
                    listOf<AggregatedDemand>(),
                    ArithmeticException(),
                ),

                Arguments.of(
                    "If fullDay enabled, we sub out all the demand",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty / 2,
                        picksOut = 1,
                        fullDayOut = true,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty / 2, picks = 1, fullDay = true)),
                    ),
                    listOf(
                        // all demand is gone, qty is not used. Similarly same amount of demand is added, qty is not used
                        demand.copy(
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                    ),
                    null,
                ),

                Arguments.of(
                    "If fullDay enabled, we sub in the demand ratio of skuIn and skuOut picks",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty / 2,
                        picksOut = 1,
                        fullDayOut = true,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty / 2, picks = 2, fullDay = true)),
                    ),
                    listOf(
                        demand.copy(
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty * 2,
                                    ),
                                ),
                            ),
                        ),
                    ),
                    null,
                ),

                Arguments.of(
                    "Full day is enabled for subOut and not subIn",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty / 2,
                        picksOut = 1,
                        fullDayOut = true,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty / 2, picks = 1, fullDay = false)),
                    ),
                    listOf(
                        demand.copy(
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty / 2,
                                    ),
                                ),
                            ),
                        ),
                    ),
                    null,
                ),
            )
        }

        @JvmStatic
        fun substitutionTestArguments(): List<Arguments> {
            val defaultRecipe = RecipeBreakdownLine.default()
            val skuIn1 = Sku.random()
            val skuIn2 = Sku.random()
            val skuOut = Sku.random()
            val qtyOut = 100L
            val sub = Substitution.random().copy(
                qtyOut = qtyOut,
                skuOut = skuOut,
                skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty), skuIn2 to SkuIn(qty = defaultRecipe.qty)),
            )
            val demand = AggregatedDemand(
                sku = AggregatedDemandSku(id = sub.skuOut.id, code = sub.skuOut.code),
                parentSku = AggregatedDemandSku(id = sub.skuOut.parentId!!, code = sub.skuOut.parentCode!!),
                dcCode = sub.dcCode,
                productionDate = sub.date,
                recipeBreakdowns = listOf(defaultRecipe),
                substitutions = Substitutions(
                    subIn = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty = 0L
                        ),
                    ),
                ),
            )

            return listOf<Arguments>(
                Arguments.of(
                    "substitute nothing if recipeList is empty",
                    demand.copy(recipeBreakdowns = listOf()),
                    sub,
                    listOf(demand.copy(recipeBreakdowns = listOf())),
                ),

                Arguments.of(
                    "substitute sku_out even if skuIn is empty",
                    demand,
                    sub.copy(qtyOut = 100, skuIn = mapOf()),

                    //
                    listOf(
                        demand.copy(
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = 100L,
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),

                Arguments.of(
                    "subIn is bigger than qtyOut",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty + defaultRecipe.qty,
                        skuIn = mapOf(
                            skuIn1 to SkuIn(qty = defaultRecipe.qty),
                            skuIn2 to SkuIn(qty = defaultRecipe.qty),
                        ),
                    ),
                    listOf(
                        // 1. Demand for sku1 is added
                        // 2. No demand is added for skuIn2
                        // 3. The original demand is all gone
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn2.id, code = skuIn2.code),
                            parentSku = AggregatedDemandSku(id = skuIn2.parentId!!, code = skuIn2.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuOut.id, code = skuOut.code),
                            parentSku = AggregatedDemandSku(id = skuOut.parentId!!, code = skuOut.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = listOf(defaultRecipe),
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty,
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),

                Arguments.of(
                    "partial recipe can be subbed out",
                    demand,
                    sub.copy(
                        qtyOut = defaultRecipe.qty / 2,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty / 2)),
                    ),
                    listOf(

                        // new demand subbed in
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty / 2,
                                    ),
                                ),
                            ),
                        ),

                        // old demand not subbed out
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = sub.skuOut.id, code = sub.skuOut.code),
                            parentSku = AggregatedDemandSku(id = sub.skuOut.parentId!!, code = sub.skuOut.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = listOf(defaultRecipe),
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = defaultRecipe.qty / 2,
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
                Arguments.of(
                    "qtyOut is 0, then we only sub-in and don't sub-out",
                    demand,
                    sub.copy(
                        qtyOut = 0,
                        skuIn = mapOf(skuIn1 to SkuIn(qty = defaultRecipe.qty)),
                    ),
                    listOf(
                        AggregatedDemand(
                            sku = AggregatedDemandSku(id = skuIn1.id, code = skuIn1.code),
                            parentSku = AggregatedDemandSku(id = skuIn1.parentId!!, code = skuIn1.parentCode!!),
                            dcCode = sub.dcCode,
                            productionDate = sub.date,
                            recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                            substitutions = Substitutions(
                                subIn = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = 1000L,
                                    ),
                                ),
                            ),
                        ),
                        demand.copy(
                            substitutions = Substitutions(
                                subOut = listOf(
                                    com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                                        qty = 0L,
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            )
        }

        @JvmStatic
        fun validateSubstitutionArguments(): List<Arguments> = listOf(

            // picksOut, fullDayOut, qtyOut, picksIn, fullDayIn, qtyIn
            Arguments.of(
                "sku out is full day but skuOut picks are null",
                null,
                true,
                1L,
                2,
                false,
                null,
            ),
            Arguments.of(
                "sku in is full day but skuIn picks are null",
                null,
                false,
                20L,
                null,
                true,
                null,
            ),
            Arguments.of(
                "sku out is not full day but skuOut qty is null",
                2,
                false,
                null,
                1,
                false,
                null,
            ),
            Arguments.of(
                "sku in is not full day but skuIn qty is null",
                null,
                false,
                20L,
                3,
                false,
                null,
            ),
        )
    }
}
