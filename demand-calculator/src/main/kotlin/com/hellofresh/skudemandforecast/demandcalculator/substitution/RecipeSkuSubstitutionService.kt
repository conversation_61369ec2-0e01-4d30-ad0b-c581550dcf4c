package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.hellofresh.skuDemandForecast.model.substitution.RecipeSkuInDemand
import com.hellofresh.skuDemandForecast.model.substitution.RecipeWithSkuSubstitutionDetail
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

class RecipeSkuSubstitutionService(private val recipeSkuSubstitutionRepository: RecipeSkuSubstitutionRepository) {

    suspend fun prepareSubstitutions(
        aggregatedFractionDemands: List<AggregatedDemand>,
        skuSpecifications: Map<UUID, SkuSpecificationRecord>
    ): List<Substitution> {
        if (aggregatedFractionDemands.isEmpty()) return emptyList()

        logger.info("Preparing the substitutions, total aggregatedFractionDemands = ${aggregatedFractionDemands.size}")
        val minimumProductionDate = aggregatedFractionDemands.minOf { it.productionDate }
        val dcs = aggregatedFractionDemands.map { it.dcCode }.toSet()
        val recipeSkuSubstitutions = recipeSkuSubstitutionRepository.fetch(
            dcs,
            skuSpecifications,
            minimumProductionDate,
        )
        logger.info("Total number of existing recipe sku substitutions = ${recipeSkuSubstitutions.size}")
        val aggregatedDemandsAtRecipeLevel = prepareAggregatedDemandAtRecipeLevel(aggregatedFractionDemands)
        val recipeSkuSubstitutionByKeys = recipeSkuSubstitutions
            .groupBy {
                RecipeBreakdownKeyWithBrand(
                    it.recipeIndex,
                    it.dcCode,
                    it.country,
                    it.brand,
                    it.peopleCount,
                )
            }

        val substitutions = mutableListOf<Substitution>()
        aggregatedDemandsAtRecipeLevel.forEach { aggregatedDemandWithRecipeLevel ->
            val recipeLevelSkuSubstitutions = recipeSkuSubstitutionByKeys[
                RecipeBreakdownKeyWithBrand(
                    aggregatedDemandWithRecipeLevel.recipeIndex,
                    aggregatedDemandWithRecipeLevel.dcCode,
                    aggregatedDemandWithRecipeLevel.country,
                    aggregatedDemandWithRecipeLevel.brand,
                    aggregatedDemandWithRecipeLevel.peopleCount,
                ),
            ] ?: emptyList()

            recipeLevelSkuSubstitutions.forEach { recipeSkuSubstitution ->
                val skuInMapByDate = recipeSkuSubstitution.skuSubstitutions
                    .filter { it.skuOut.skuId == aggregatedDemandWithRecipeLevel.sku.id }
                    .flatMap { it.skuIn.map { skuIn -> RecipeSubstitutionPerDay(skuIn, it.skuOut.picks) } }
                    .groupBy { it.subIn.date }

                val substitutionsByDate = skuInMapByDate[aggregatedDemandWithRecipeLevel.productionDate]

                if (substitutionsByDate != null) {
                    val skuInMap = substitutionsByDate.associate { sub ->
                        val sku =
                            Sku(sub.subIn.skuId, sub.subIn.skuCode, sub.subIn.parentSkuId, sub.subIn.parentSkuCode)
                        sku to SkuIn(aggregatedDemandWithRecipeLevel.qty * sub.subIn.picks / sub.subOutPicks, sub.subIn.picks, false)
                        // full-day is false because the SKU is substituted for this recipe not for the whole day.
                    }
                    substitutions.add(
                        createSubstitution(aggregatedDemandWithRecipeLevel, recipeSkuSubstitution, skuInMap),
                    )
                }
            }
        }
        return substitutions
    }

    private fun createSubstitution(
        aggregatedDemandWithRecipeLevel: AggregatedDemandWithRecipeBreakdown,
        recipeSkuSubstitution: RecipeWithSkuSubstitutionDetail,
        skuInMap: Map<Sku, SkuIn>
    ) = Substitution(
        id = UUID.randomUUID(),
        date = aggregatedDemandWithRecipeLevel.productionDate,
        dcCode = recipeSkuSubstitution.dcCode,
        skuOut = Sku(
            aggregatedDemandWithRecipeLevel.sku.id,
            aggregatedDemandWithRecipeLevel.sku.code,
            aggregatedDemandWithRecipeLevel.parentSku?.id,
            aggregatedDemandWithRecipeLevel.parentSku?.code,
        ),
        qtyOut = aggregatedDemandWithRecipeLevel.qty,
        skuIn = skuInMap,
        fullDayOut = false,
        picksOut = aggregatedDemandWithRecipeLevel.picks,
        demandType = aggregatedDemandWithRecipeLevel.demandType
    )

    private fun prepareAggregatedDemandAtRecipeLevel(
        aggregatedDemands: List<AggregatedDemand>
    ): List<AggregatedDemandWithRecipeBreakdown> =
        aggregatedDemands.flatMap { demand ->
            demand.recipeBreakdowns.map { breakdown ->
                AggregatedDemandWithRecipeBreakdown(
                    demand.sku,
                    demand.parentSku,
                    demand.dcCode,
                    demand.productionDate,
                    breakdown.recipeIndex,
                    breakdown.country,
                    breakdown.brand,
                    breakdown.peopleCount,
                    breakdown.picks,
                    breakdown.qty,
                    breakdown.demandType
                )
            }
        }

    companion object : Logging

    data class RecipeSubstitutionPerDay(val subIn: RecipeSkuInDemand, val subOutPicks: Int)
}
