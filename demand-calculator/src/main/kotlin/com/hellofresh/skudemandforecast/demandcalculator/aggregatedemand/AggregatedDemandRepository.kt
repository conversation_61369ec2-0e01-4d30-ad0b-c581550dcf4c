package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand

import java.time.LocalDate

interface AggregatedDemandRepository {

    suspend fun fetchAggregatedDemand(dcs: Set<String>, dateFrom: LocalDate): List<AggregatedDemand>

    suspend fun fetchExistingKeys(dcs: Set<String>, laterThan: LocalDate): Set<AggregatedDemandKey>

    fun upsert(aggregatedDemands: List<AggregatedDemand>): List<AggregatedDemand>
}
