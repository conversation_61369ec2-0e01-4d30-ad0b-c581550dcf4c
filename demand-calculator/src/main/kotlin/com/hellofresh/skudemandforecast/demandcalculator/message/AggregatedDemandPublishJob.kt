package com.hellofresh.skudemandforecast.demandcalculator.message

import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.LocalDate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.toList
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.kotlin.coroutines.transactionCoroutine

const val SKU_DEMAND_FORECAST_TOPIC_NAME = "public.demand.sku-demand-forecast.v2"
const val WEEK_KAFKA_HEADER_KEY = "week"

class AggregatedDemandPublishJob(
    private val dslContext: MetricsDSLContext,
    private val aggregatedDemandPublishRepository: AggregatedDemandPublishRepository,
    private val producer: Producer<SkuDemandForecastKey, SkuDemandForecastVal>,
    private val dcConfigService: DcConfigService,
) {
    suspend fun publish(markets: Set<String>) {
        logger.info("Starting publish aggregated demand job for markets: $markets")
        dslContext.withTagName("publish-aggregated-demand")
            .transactionCoroutine { conf ->

                val txContext = dslContext.withMeteredConfiguration(conf)

                val aggregatedDemandPublished = aggregatedDemandPublishRepository
                    .fetchNonPublishedDemand(
                        dcConfigService.dcConfigurations.filter { it.value.market in markets }.keys,
                        txContext
                    )
                    .flowOn(Dispatchers.IO)
                    .onEach {
                        producer.send(toProducerRecord(it, buildHeaders(it.productionDate, it.dcCode)))
                    }.toList()
                producer.flush()
                if (aggregatedDemandPublished.isNotEmpty()) {
                    aggregatedDemandPublishRepository.updatePublished(txContext, aggregatedDemandPublished)
                    logger.info("Published ${aggregatedDemandPublished.size} aggregated demands")
                }
            }
        logger.info("Ending publish aggregated demand job")
    }

    private fun toProducerRecord(
        aggregatedDemand: AggregatedDemand,
        headers: List<KHeader>,
    ): ProducerRecord<SkuDemandForecastKey, SkuDemandForecastVal> {
        val (key, value) = SkuDemandForecastTopicMapper.toTopicRecord(aggregatedDemand)
        val record = ProducerRecord(SKU_DEMAND_FORECAST_TOPIC_NAME, null, key, value)
        headers.forEach { header ->
            record.headers().add(header.key, header.value)
        }
        return record
    }

    private fun buildHeaders(productionDate: LocalDate, dcCode: String) =
        dcConfigService.dcConfigurations[dcCode]
            ?.let { distributionCenter ->
                listOf(
                    KHeader(
                        WEEK_KAFKA_HEADER_KEY,
                        DcWeek(
                            productionDate,
                            distributionCenter.productionStart,
                        ).value.toByteArray(),
                    ),
                )
            } ?: emptyList()

    companion object : Logging
}

data class KHeader(val key: String, val value: ByteArray)
