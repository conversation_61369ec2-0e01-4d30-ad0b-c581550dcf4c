@file:Suppress("LongMethod")

package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.model.substitution.RecipeDetailsDbValue
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.FRACTION_SKU_DEMAND
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.SKU_SPECIFICATION
import java.time.LocalDate
import java.time.ZoneOffset
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging

class DemandQueryImpl(private val dslContext: MetricsDSLContext) : DemandQuery {

    private val fetchFractionDemands = "fetch-fraction-demands"

    override suspend fun aggregateFractionDemands(dcCodes: Set<String>): List<AggregatedDemand> {
        val skuSpecificationWithParent = SKU_SPECIFICATION.`as`("withParent")
        return dslContext.withTagName(fetchFractionDemands)
            .select(
                SKU_SPECIFICATION.ID,
                SKU_SPECIFICATION.CODE,
                SKU_SPECIFICATION.PARENT_ID,
                skuSpecificationWithParent.CODE,
                FRACTION_SKU_DEMAND.DC_CODE,
                FRACTION_SKU_DEMAND.DEMAND_DATE,
                FRACTION_SKU_DEMAND.RECIPE_INDEX,
                FRACTION_SKU_DEMAND.COUNTRY,
                FRACTION_SKU_DEMAND.LOCALE,
                FRACTION_SKU_DEMAND.PRODUCT_FAMILY,
                FRACTION_SKU_DEMAND.RECIPE_DETAILS,
                FRACTION_SKU_DEMAND.QTY,
                FRACTION_SKU_DEMAND.SOURCE_IDS,
            )
            .from(FRACTION_SKU_DEMAND)
            .join(SKU_SPECIFICATION)
            .on(FRACTION_SKU_DEMAND.SKU_ID?.eq(SKU_SPECIFICATION.ID))
            .leftJoin(skuSpecificationWithParent)
            .on(SKU_SPECIFICATION.PARENT_ID.eq(skuSpecificationWithParent.ID))
            .where(
                FRACTION_SKU_DEMAND.DEMAND_DATE.ge(demandCalculationFrom())
                    .and(
                        FRACTION_SKU_DEMAND.DC_CODE.`in`(dcCodes),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.groupBy {
                    val skuSpec = it.into(SKU_SPECIFICATION)
                    val parentSkuSpec = it.into(skuSpecificationWithParent)
                    AggregatedDemandKeyWithParentId(
                        sku = Sku(skuSpec.id, skuSpec.code),
                        parentSku = skuSpec.parentId?.let {
                            if (parentSkuSpec.code == null) {
                                logger.error(
                                    "Parent sku code is null for skuId ${skuSpec.id} and parentId ${skuSpec.parentId}. Setting parentSku to null",
                                )
                                null
                            } else {
                                Sku(skuSpec.parentId, parentSkuSpec.code)
                            }
                        },
                        dcCode = it[FRACTION_SKU_DEMAND.DC_CODE],
                        date = it[FRACTION_SKU_DEMAND.DEMAND_DATE],
                    )
                }.map { (key, records) ->
                    val sourceIds = records.mapNotNull {
                        it[FRACTION_SKU_DEMAND.SOURCE_IDS]?.toList()
                    }.flatten()
                    val recipeBreakdowns = records.flatMap { record ->
                        val recipeIndex = record[FRACTION_SKU_DEMAND.RECIPE_INDEX]
                        val country = record[FRACTION_SKU_DEMAND.COUNTRY]
                        val locale = record[FRACTION_SKU_DEMAND.LOCALE]
                        val productFamily = record[FRACTION_SKU_DEMAND.PRODUCT_FAMILY]

                        record[FRACTION_SKU_DEMAND.RECIPE_DETAILS]
                            ?.let {
                                objectMapper.readValue<RecipeDetailsDbValue>(it.data())
                                    .recipeDetails.map {
                                        RecipeBreakdownLine(
                                            recipeIndex = recipeIndex,
                                            country = country,
                                            locale = locale,
                                            productFamily = productFamily,
                                            brand = it.brand,
                                            peopleCount = it.peopleCount,
                                            picks = it.picks,
                                            qty = it.qty,
                                            demandType = it.demandType,
                                        )
                                    }
                            } ?: // Backwards compatibility
                            listOf(
                                RecipeBreakdownLine(
                                    recipeIndex = recipeIndex,
                                    country = country,
                                    locale = locale,
                                    productFamily = productFamily,
                                    brand = null,
                                    peopleCount = null,
                                    picks = null,
                                    qty = record[FRACTION_SKU_DEMAND.QTY],
                                ),
                            )
                    }
                    AggregatedDemand(
                        sku = Sku(key.sku.id, key.sku.code),
                        parentSku = key.parentSku,
                        dcCode = key.dcCode,
                        productionDate = key.date,
                        recipeBreakdowns = recipeBreakdowns,
                        sourceIds = sourceIds.toSet(),
                    )
                }
            }.await()
    }

    companion object : Logging {
        const val DEMAND_DAYS_BEFORE = 7L

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        fun demandCalculationFrom() = LocalDate.now(ZoneOffset.UTC).minusDays(DEMAND_DAYS_BEFORE)
    }
}

private data class AggregatedDemandKeyWithParentId(
    val sku: Sku,
    val parentSku: Sku?,
    val dcCode: String,
    val date: LocalDate,
)
