package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.hellofresh.skuDemandForecast.featureflags.Context.DC
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigFeatureFlagClient
import com.hellofresh.skuDemandForecast.lib.memoize
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.merge
import com.hellofresh.skudemandforecast.demandcalculator.substitution.DemandSubstitutions.GroupingKey
import com.hellofresh.skudemandforecast.demandcalculator.substitution.Sku as SubstitutionSku
import java.time.LocalDate
import kotlin.math.min
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.TestOnly

/**
 *    Algorithm
 *    1. For each date and skuOut, we get demand per recipe
 *    2. deduct qtyOut from recipes.
 *    3. For each skuIn create a new demand and attribute it to the first recipe in the recipe list
 *
 *    Substitute Step
 *    ==============
 *    Original Demand
 *       [SkuA,d1,ve] -> [{r1-> 40, r2-> 50, r3 -> 20}]
 *       [SkuB,d1,ve] -> [{r1-> 30, r2-> 20}]
 *    Substitute
 *      [SkuA,d1,ve] and qtyOut=100 subbed in =  [{skuB -> 100, skuC -> 200}]
 *
 *     Returns Demand
 *       [SkuA,d1,ve] -> [{r1-> 0, r2-> 0, r3 -> 10}]
 *       [SkuB,d1,ve] = [{r1=130, r2->20}]
 *       [SkuC,d1,ve] = [{r1=200}]
 *
 *    Merge Step
 *    ==========
 *    ([skuInB,d1,ve] -> [{r1->50}]) + ([SkuB,d1,ve] -> [{r4->100}]) = [SkuB,d1,ve] -> [{r1->50,r4->100}]
 *    ([skuInB,d1,ve] -> [{r1->50}]) + ([SkuB,d1,ve] -> [{r1->100}]) = [SkuB,d1,ve] -> [{r1->150}]
 *
 *    OriginalDemand
 *    =============
 *    Original Demand is the demand before substitution. Therefore
 *
 *    1.After subbing out, the original demand of the SkuOut shall remain same.
 *    2. If a new demand is added for SkuIn, the original demand is 0.
 *    3. Merge step adds the original demand.
 *    4. Parent has original demand which is sum of the children's original demand.
 *
 */
class DemandSubstitutions(
    val substitutions: List<Substitution>,
    val statsigFeatureFlagClient: StatsigFeatureFlagClient
) {

    private val substitutionsGrouped = substitutions.groupBy { GroupingKey(it.skuOut, it.date, it.dcCode) }

    /** Key to uniquely identify a demand in the service
     * */
    @TestOnly
    data class GroupingKey(
        val sku: SubstitutionSku,
        val date: LocalDate,
        val dcCode: String,
    ) {
        companion object {
            fun from(demand: AggregatedDemand) = with(demand) {
                GroupingKey(SubstitutionSku(sku.id, sku.code, parentSku?.id, parentSku?.code), productionDate, dcCode)
            }
        }
    }

    // Each demand has a unique key called [GroupingKey], we see this key only
    // once in each iteration. This means in merge, we get skuOut only once,
    // demand for the skuIn many times for each time its inserted.

    fun perform(demandList: List<AggregatedDemand>): List<AggregatedDemand> {
        val output = mutableMapOf<GroupingKey, AggregatedDemand>()
        val dcEnabled = isDcEnabledForSubstitution()
        demandList.forEach { demand ->
            var sub = emptyList<Substitution>()
            if (dcEnabled(demand.dcCode)) {
                sub = substitutionsGrouped[GroupingKey.from(demand)] ?: emptyList()
            }
            substitute(demand, sub).forEach { output.merge(it) }
        }
        return output.values.toList()
    }

    private fun isDcEnabledForSubstitution(): (String) -> Boolean = { dcCode: String ->
        isDcEnabledForSubstitution(dcCode)
    }.memoize()

    private fun isDcEnabledForSubstitution(dcCode: String) =
        statsigFeatureFlagClient.isEnabledFor(FeatureFlag.SkuSubstitution(DC, dcCode))

    companion object : Logging
}

/**
 * Adds [newDemand] to the list of AggregatedDemand.
 *
 * If no demand exist for the [GroupingKey], then simply appends the new demand
 * otherwise add the demand for the already existing demands. Note that we need
 * to add demand for the recipes which were subbed out.
 *
 *
 * */
@TestOnly
fun MutableMap<GroupingKey, AggregatedDemand>.merge(
    newDemand: AggregatedDemand
): MutableMap<GroupingKey, AggregatedDemand> {
    val key = GroupingKey.from(newDemand)

    val existingDemandForSku = this[key]

    if (existingDemandForSku == null) {
        this[key] = newDemand
        return this
    }
    this[key] = existingDemandForSku.merge(newDemand)
    return this
}

/* Remove the demand from each recipe, returns the updated demand which is
*  the demand from the skus subbed in, plus the subbed out demand
*
*  Algorithm
*  1. All skuIn should be added as a new demand irrespective of the qtyOut.
*  2. Deduct as much qtyOut. Make demand  0 if qtyOut is bigger.
*  4. Prepare the final effective demand - reduced demand for the subOut + new demand from the subbed in skus
* */
@TestOnly
fun substitute(demand: AggregatedDemand, substitutions: List<Substitution>): List<AggregatedDemand> {
    if (substitutions.isEmpty() || demand.recipeBreakdowns.isEmpty()) {
        if (substitutions.isNotEmpty()) {
            DemandSubstitutions.logger.error(
                "No demand to substitute for subs id: ${substitutions.map { it.id }}",
            )
        }
        return listOf(demand)
    }
    val totalSkuOutDemand = demand.totalRecipeBreakDownQty()

    val subInDemands = substitutions.flatMap { sub ->
        // all the sku_in is added as  new demand
        sub.skuIn.map { (sku, subIn) ->
            val qty = if (subIn.fullDay) {
                totalSkuOutDemand * subIn.picks!! / sub.picksOut!!
            } else {
                subIn.qty!!
            }
            AggregatedDemand(
                sku = Sku(id = sku.id, code = sku.code),
                parentSku = sku.parentId?.let { Sku(sku.parentId, sku.parentCode!!) },
                dcCode = sub.dcCode,
                productionDate = sub.date,
                recipeBreakdowns = demand.recipeBreakdowns.take(1).map { it.copy(qty = 0L) },
                substitutions = Substitutions(
                    subIn = listOf(
                        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
                            qty,
                            demandType = sub.demandType,
                        ),
                    ),
                ),
                sourceIds = sub.sourceId?.let { setOf(it) } ?: emptySet(),
            )
        }
    }

    var remainingTotalQty: Long = totalSkuOutDemand
    val subOuts = substitutions.map { sub ->
        val totalSubSkuOut = if (sub.fullDayOut) totalSkuOutDemand else sub.qtyOut!!
        val substitutionsOut = min(remainingTotalQty, totalSubSkuOut)
        remainingTotalQty -= substitutionsOut
        com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution(
            substitutionsOut,
            demandType = sub.demandType,
        )
    }

    return listOf(
        demand.copy(
            substitutions = Substitutions(
                subOut = subOuts,
            ),
            sourceIds = demand.sourceIds + substitutions.mapNotNull { it.sourceId }.toSet(),
        ),
    ) + subInDemands
}
