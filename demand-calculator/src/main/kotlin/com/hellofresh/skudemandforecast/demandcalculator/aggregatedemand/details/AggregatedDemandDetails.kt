package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine

const val UNKNOWN_BRAND = "UNKNOWN"

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class AggregatedDemandDetails(
    val recipeBreakdowns: List<RecipeBreakdownLine>,
    val substitutions: Substitutions?,
    val prekittings: Prekittings?,
    val crossDockings: CrossDockings?,
)
