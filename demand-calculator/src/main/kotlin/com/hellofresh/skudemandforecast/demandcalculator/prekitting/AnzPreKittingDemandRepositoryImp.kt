package com.hellofresh.skudemandforecast.demandcalculator.prekitting

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.ANZ_PREKITTING_DEMAND
import java.time.LocalDate

class AnzPreKittingDemandRepositoryImp(private val dslContext: MetricsDSLContext) : AnzPreKittingDemandRepository {

    private val selectPreKittingDemand = "select-pre-kitting-demand"

    override fun getPreKittingDemand(
        dcCodes: Set<String>,
        fromDate: LocalDate
    ): List<AnzPreKittingDemand> {
        if (dcCodes.isEmpty()) {
            return emptyList()
        }
        val query = dslContext.withTagName(selectPreKittingDemand)
            .selectFrom(ANZ_PREKITTING_DEMAND)
            .where(ANZ_PREKITTING_DEMAND.DC_CODE.`in`(dcCodes))
            .and(ANZ_PREKITTING_DEMAND.DEMAND_DATE.ge(fromDate))

        val results = query.fetch()
        return results.map { record ->
            AnzPreKittingDemand(
                record.dcCode,
                record.skuId,
                record.demandQuantity,
                record.demandDate,
            )
        }
    }
}
