package com.hellofresh.skudemandforecast.demandcalculator.message

import com.google.type.Date
import com.google.type.Decimal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.FUMIGATED
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.REGULAR
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDocking
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.DUPLICATE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.REPLACE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekitting
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekittings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.UNKNOWN_BRAND

object SkuDemandForecastTopicMapper {
    fun toTopicRecord(aggregatedDemand: AggregatedDemand): Pair<SkuDemandForecastKey, SkuDemandForecastVal> =
        with(aggregatedDemand) {
            val key = SkuDemandForecastKey.newBuilder()
                .setDistributionCenterBobCode(dcCode)
                .setDate(
                    Date.newBuilder()
                        .setYear(productionDate.year)
                        .setMonth(productionDate.monthValue)
                        .setDay(productionDate.dayOfMonth),
                ).setSkuId(sku.id.toString())

            val value = SkuDemandForecastVal.newBuilder()
                .setSkuCode(sku.code)
                .setTotalQty(totalQty.toProtoDecimal())
                .addAllRecipesBreakdown(
                    toRecipeBreakdowns(recipeBreakdowns),
                ).setSubstitutions(
                    toSubstitutions(substitutions),
                ).setPreKittings(
                    toPrekitting(prekittings),
                ).setCrossDockings(
                    toCrossDocking(crossDockings),
                )
            key.build() to value.build()
        }

    private fun toCrossDocking(crossDockings: CrossDockings?): SkuDemandForecastVal.CrossDockings.Builder? =
        SkuDemandForecastVal.CrossDockings.newBuilder().apply {
            crossDockings?.also {
                addAllCrossdockingOut(
                    toCrossDockings(crossDockings.crossDockingsOut),
                ).addAllCrossdockingIn(
                    toCrossDockings(crossDockings.crossDockingsIn),
                )
            }
        }

    private fun toCrossDockings(crossDockings: List<CrossDocking>): List<SkuDemandForecastVal.CrossDocking> =
        crossDockings.map {
            SkuDemandForecastVal.CrossDocking.newBuilder()
                .setDistributionCenterCode(it.dcCode)
                .setAction(
                    when (it.action) {
                        REPLACE -> SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_REPLACE
                        DUPLICATE -> SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_DUPLICATE
                    },
                )
                .setQty(it.quantity.toProtoDecimal())
                .build()
        }

    private fun toPrekitting(prekittings: Prekittings?): SkuDemandForecastVal.Prekittings.Builder? =
        SkuDemandForecastVal.Prekittings.newBuilder().apply {
            prekittings?.also {
                addAllPrekittingOut(
                    toPrekittings(prekittings.prekittingOut),
                ).addAllPrekittingIn(
                    toPrekittings(prekittings.prekittingIn),
                )
            }
        }

    private fun toPrekittings(prekittings: List<Prekitting>): List<SkuDemandForecastVal.Prekitting> =
        prekittings.map {
            SkuDemandForecastVal.Prekitting.newBuilder()
                .setQty(it.qty.toProtoDecimal())
                .setDemandType(getDemandType(it.demandType))
                .build()
        }

    private fun toSubstitutions(substitutions: Substitutions?): SkuDemandForecastVal.Substitutions.Builder? =
        SkuDemandForecastVal.Substitutions.newBuilder()
            .apply {
                substitutions?.also {
                    addAllSubOut(
                        toSubstitutions(substitutions.subOut),
                    ).addAllSubIn(
                        toSubstitutions(substitutions.subIn),
                    )
                }
            }
}

private fun toSubstitutions(substitutions: List<Substitution>) =
    substitutions.map {
        SkuDemandForecastVal.Substitution.newBuilder()
            .setQty(it.qty.toProtoDecimal())
            .setBrand(it.brand ?: UNKNOWN_BRAND)
            .setDemandType(getDemandType(it.demandType))
            .apply {
                if (it.recipeIndex != null) recipeIndex = it.recipeIndex.toString()
            }
            .build()
    }

private fun toRecipeBreakdowns(recipeBreakdowns: List<RecipeBreakdownLine>): List<RecipesBreakdown> =
    recipeBreakdowns.groupBy {
        Triple(it.recipeIndex, it.brand, it.demandType)
    }.map { (key, values) ->
        val (recipeIndex, recipeBrand: String?, demandType: DemandType?) = key
        RecipesBreakdown.newBuilder()
            .setRecipeIndex(recipeIndex.toString())
            .setQty(values.sumOf { it.qty }.toProtoDecimal())
            .setBrand(recipeBrand ?: UNKNOWN_BRAND)
            .setDemandType(getDemandType(demandType))
            .build()
    }

private fun getDemandType(demandType: DemandType?): SkuDemandForecastVal.DemandType =
    when (demandType) {
        FUMIGATED -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_FUMIGATED
        REGULAR -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_REGULAR
        else -> SkuDemandForecastVal.DemandType.DEMAND_TYPE_UNSPECIFIED
    }

fun Long.toProtoDecimal() = Decimal.newBuilder().setValue(this.toString()).build()
