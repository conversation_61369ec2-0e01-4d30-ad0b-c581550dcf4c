package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import java.time.LocalDate

data class AggregatedDemandWithRecipeBreakdown(
    val sku: com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku,
    val parentSku: com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.Sku?,
    val dcCode: String,
    val productionDate: LocalDate,
    val recipeIndex: Int,
    val country: String,
    val brand: String?,
    val peopleCount: Int?,
    val picks: Int?,
    val qty: Long,
    val demandType: DemandType? = null,
)
