package com.hellofresh.skudemandforecast.demandcalculator.substitution

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.DemandQueryImpl
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.SKU_SPECIFICATION
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.Tables.SKU_SUBSTITUTION
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSubstitutionRecord
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.impl.DSL.lower

private val objectMapper = ObjectMapper().findAndRegisterModules()

interface SubstitutionRepository {
    suspend fun fetchActive(dcs: Set<String>, skuSpecifications: Map<UUID, SkuSpecificationRecord>): List<Substitution>
    suspend fun fetchSkuSpecificationRecords(markets: Set<String>): Map<UUID, SkuSpecificationRecord>
}

class SubstitutionRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : SubstitutionRepository, Logging {

    private val fetchSubstitutions = "fetch-substitutions"
    private val fetchSubsSkus = "fetch-subs-skus"

    override suspend fun fetchActive(dcs: Set<String>, skuSpecifications: Map<UUID, SkuSpecificationRecord>): List<Substitution> =
        fetchActiveSubstitutionRecords(dcs)
            .flatMap { substitutionRecord ->
                val subs = objectMapper.readValue<SubstitutionDetail>(substitutionRecord.value.data())
                val skuOut = skuSpecifications.sku(subs.skuOut.skuId) ?: return@flatMap listOf()
                val skuOutParent = skuOut.parentId?.let { skuSpecifications.sku(skuOut.parentId) ?: return@flatMap listOf() }
                runCatching {
                    subs.skuOut.demand.map { skuOutDemand ->
                        Substitution(
                            id = substitutionRecord.id,
                            date = skuOutDemand.date,
                            dcCode = substitutionRecord.dcCode,
                            skuOut = Sku(skuOut.id, skuOut.code, skuOut.parentId, skuOutParent?.code),
                            qtyOut = if (substitutionRecord.disabled) 0L else skuOutDemand.qty?.toLong() ?: 0L,
                            fullDayOut = if (substitutionRecord.disabled) false else skuOutDemand.fullDay,
                            picksOut = subs.skuOut.picks,
                            skuIn = subs.skuIn.mapNotNull innerLoop@{ (id, demand, picks) ->
                                val sku = skuSpecifications.sku(id) ?: return@innerLoop null
                                val skuParent = sku.parentId?.let { skuSpecifications.sku(sku.parentId) ?: return@innerLoop null }
                                val qty = if (substitutionRecord.disabled) {
                                    0L
                                } else {
                                    demand.firstOrNull { skuOutDemand.date == it.date }?.qty
                                }
                                val fullDay = if (substitutionRecord.disabled) {
                                    false
                                } else {
                                    demand.firstOrNull {
                                        skuOutDemand.date == it.date
                                    }?.fullDay ?: return@innerLoop null
                                }
                                Sku(sku.id, sku.code, sku.parentId, skuParent?.code) to SkuIn(qty?.toLong(), picks, fullDay)
                            }.toMap(),
                            sourceId = substitutionRecord.sourceId,
                        )
                    }
                }.getOrElse {
                    logger.error("Can not fetch some substitutions.", it)
                    listOf()
                }
            }

    override suspend fun fetchSkuSpecificationRecords(markets: Set<String>): Map<UUID, SkuSpecificationRecord> =
        dslContext.withTagName(fetchSubsSkus)
            .selectFrom(SKU_SPECIFICATION)
            .where(lower(SKU_SPECIFICATION.MARKET).`in`(markets.map { it.lowercase() }))
            .fetchAsync()
            .await()
            .associateBy { it.id }

    private suspend fun fetchActiveSubstitutionRecords(dcs: Set<String>): List<SkuSubstitutionRecord> =
        dslContext.withTagName(fetchSubstitutions)
            .selectFrom(SKU_SUBSTITUTION)
            .where(SKU_SUBSTITUTION.TO_DATE.ge(DemandQueryImpl.demandCalculationFrom()))
            .and(SKU_SUBSTITUTION.DC_CODE.`in`(dcs))
            .fetchAsync()
            .thenApply { result ->
                result.groupBy { it.id }
                    .map { (_, subs) -> subs.sortedByDescending { it.version }.first() }
            }
            .await()

    private fun Map<UUID, SkuSpecificationRecord?>.sku(skuId: UUID) =
        if (this[skuId] == null) {
            logger.warn("sku: $skuId not found")
            null
        } else {
            this[skuId]
        }
}
