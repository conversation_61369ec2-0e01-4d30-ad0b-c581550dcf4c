package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand

import com.hellofresh.skuDemandForecast.model.substitution.DemandType
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.FUMIGATED
import com.hellofresh.skuDemandForecast.model.substitution.DemandType.REGULAR
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekitting
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekittings
import com.hellofresh.skudemandforecast.demandcalculator.prekitting.AnzPreKittingDemandRepository
import com.hellofresh.skudemandforecast.demandcalculator.schema.public_.tables.records.SkuSpecificationRecord
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

class PrekittingService(
    private val anzDcs: Set<String> = emptySet(),
    private val anzPreKittingDemandRepository: AnzPreKittingDemandRepository,
) {

    fun preparePreKittings(aggregatedDemands: List<AggregatedDemand>, skuSpecifications: Map<UUID, SkuSpecificationRecord>): List<AggregatedDemand> {
        if (aggregatedDemands.isEmpty()) return aggregatedDemands

        val preKittingDemandsByAggregatedDemand = anzPreKittingDemandRepository.getPreKittingDemand(
            aggregatedDemands.asSequence().map { it.dcCode }.filter { it in anzDcs }.toSet(),
            aggregatedDemands.minOf { it.productionDate },
        ).associateBy {
            AggregatedDemandKey(
                it.skuId,
                it.dcCode,
                it.demandDate,
            )
        }.mapValues { it.value.demandQty }

        val demandByKey = aggregatedDemands.associateBy { it.key() }
        val allKeys = demandByKey.keys union preKittingDemandsByAggregatedDemand.keys
        val (anzKeys, nonAnzKeys) = allKeys.partition { it.dcCode in anzDcs } // partitioning keys based on dcCode
        val preparedPrekittings = mutableMapOf<AggregatedDemandKey, Prekittings>()
        return nonAnzKeys.mapNotNull { demandByKey[it] } + anzKeys.sortedBy { it.productionDate }.map { key ->
            val aggregatedDemand = demandByKey[key]
            val preKittingDemand = preKittingDemandsByAggregatedDemand[key]
            val preKittingDemandDayBefore = preKittingDemandsByAggregatedDemand[
                key.copy(
                    productionDate = key.productionDate.minusDays(1),
                ),
            ]
            val preKittingFumigatedDemandDayBefore = getPreviousDayPreKittingDemand(preparedPrekittings, key, FUMIGATED)
            val preKittingRegularDemandDayBefore = getPreviousDayPreKittingDemand(preparedPrekittings, key, REGULAR)

            val prekittings = createPrekittings(
                preKittingDemand, preKittingFumigatedDemandDayBefore,
                preKittingRegularDemandDayBefore, demandByKey, key,
            )?.also { preparedPrekittings[key] = it }
                ?: createPrekittings(preKittingDemand, preKittingDemandDayBefore)

            aggregatedDemand
                ?.let { aggregatedDemand.copy(prekittings = prekittings) }
                ?: AggregatedDemand(
                    sku = Sku(id = key.skuId, code = skuSpecifications[key.skuId]?.code ?: ""),
                    parentSku = skuSpecifications[key.skuId]?.parentId?.let { Sku(id = it, code = "") },
                    dcCode = key.dcCode,
                    productionDate = key.productionDate,
                    recipeBreakdowns = emptyList(),
                    sourceIds = emptySet(),
                    prekittings = prekittings,
                )
        }
    }

    private fun getPreviousDayPreKittingDemand(
        preparedPrekittings: Map<AggregatedDemandKey, Prekittings>,
        key: AggregatedDemandKey,
        demandType: DemandType
    ): Long? =
        preparedPrekittings[key.copy(productionDate = key.productionDate.minusDays(1))]
            ?.prekittingIn
            ?.filter { it.demandType == demandType }
            ?.sumOf { it.qty }

    // prekitting support for demand type null
    private fun createPrekittings(preKittingDemand: Long?, preKittingDemandDayBefore: Long?): Prekittings? =
        if (preKittingDemand != null || preKittingDemandDayBefore != null) {
            Prekittings(
                prekittingIn =
                listOfNotNull(preKittingDemand?.let { Prekitting(qty = preKittingDemand) }),
                prekittingOut =
                listOfNotNull(
                    preKittingDemandDayBefore?.let { Prekitting(qty = preKittingDemandDayBefore) },
                ),
            )
        } else {
            null
        }

    // prekitting support for demand type fumigated, regular
    private fun createPrekittings(
        preKittingDemand: Long?,
        preKittingFumigatedDemandDayBefore: Long?,
        preKittingRegularDemandDayBefore: Long?,
        demandsByKey: Map<AggregatedDemandKey, AggregatedDemand>,
        key: AggregatedDemandKey
    ): Prekittings? {
        if (preKittingDemand == null && preKittingFumigatedDemandDayBefore == null &&
            preKittingRegularDemandDayBefore == null
        ) {
            return null
        }

        val tomorrowAggregatedDemand = demandsByKey[key.copy(productionDate = key.productionDate.plusDays(1))]
        var remainingDemand = preKittingDemand ?: 0L

        // The order CANNOT be changed - first Fumigated then Regular
        val processedFumigatedPrekitting = processPrekittingDemand(
            remainingDemand,
            tomorrowAggregatedDemand,
            FUMIGATED,
        ).also { remainingDemand -= it?.qty ?: 0L }

        val processedRegularPrekitting = processPrekittingDemand(
            remainingDemand,
            tomorrowAggregatedDemand,
            REGULAR,
        )

        val prekittingsIn = listOfNotNull(processedFumigatedPrekitting, processedRegularPrekitting)
        val prekittingsOut = listOfNotNull(
            preKittingFumigatedDemandDayBefore?.let { Prekitting(it, FUMIGATED) },
            preKittingRegularDemandDayBefore?.let { Prekitting(it, REGULAR) },
        )

        return if (prekittingsIn.isEmpty() && prekittingsOut.isEmpty()) {
            null
        } else {
            Prekittings(prekittingIn = prekittingsIn, prekittingOut = prekittingsOut)
        }
    }

    private fun processPrekittingDemand(
        remainingDemand: Long,
        aggregatedDemand: AggregatedDemand?,
        demandType: DemandType
    ): Prekitting? =
        aggregatedDemand?.recipeBreakdowns
            ?.filter { it.demandType == demandType }
            ?.sumOf { it.qty }
            ?.takeIf { it > 0 }
            ?.let { demandQty ->
                val processedQty = minOf(remainingDemand, demandQty)
                Prekitting(qty = processedQty, demandType = demandType)
            }

    companion object : Logging
}
