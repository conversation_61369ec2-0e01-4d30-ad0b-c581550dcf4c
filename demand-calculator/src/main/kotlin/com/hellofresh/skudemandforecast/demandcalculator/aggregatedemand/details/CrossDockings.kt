package com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class CrossDockings(
    val crossDockingsIn: List<CrossDocking> = emptyList(),
    val crossDockingsOut: List<CrossDocking> = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class CrossDocking(
    val dcCode: String,
    val action: CrossDockingAction,
    val quantity: Long
)

enum class CrossDockingAction {
    REPLACE,
    DUPLICATE
}
