---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace:  services/@projectName@

deployments:
  app:
    resources:
      requests:
        memory: '500Mi'
        cpu: '400m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec /benthos -c /etc/benthos/benthos.yaml -r "/etc/benthos/resources-*.yaml" streams /etc/benthos/streams
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      DB_NAME: sdf
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      SKU_SPECIFICATION_PROCESSOR_GROUP_ID: sku-demand-forecast.kafka-db-sink-sku-specification.v4
      KAFKA_CLIENT_ID: sku-demand-forecast.kafka-db-sink
      KAFKA_SASL_MECHANISM: PLAIN
      SKU_SPECIFICATION_TOPIC: 'csku-inventory-forecast.intermediate.sku-specification.v1'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
    livenessProbe:
      httpGet:
        path: /ping
        port: http
      initialDelaySeconds: 15
      periodSeconds: 60
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: http

services:
  app:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

configMap:
  BENTHOS_PORT: '8080'

alerts:
  systemRules:
    - name: '@<EMAIL>'
      rules:
        - alert: '@projectName.upperCamelCase@SkuSpecificationConsumerLag'
          expr: 'max(kafka_consumer_group_rep_lag{name=~"sku-demand-forecast.kafka-db-sink-sku-specification.*"}) by (name) > 500'
          for: '2m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Sku Specification Consumer group lag'
            description: 'Sku Specification Consumer group {{ $labels.name }} has lag of {{ humanize $value }}.'
