@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

group = "$group.${project.name}".replace("-", "")
description = "Demand matcher Job"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib"))
    implementation(project(":lib:db"))
    implementation(project(":lib:job"))
    implementation(project(":lib:models"))
    implementation(project(":distribution-center-lib"))
    implementation(project(":lib:featureflags"))
    implementation(libs.hellofresh.service)
    implementation(libs.prometheus.pushgateway)
    implementation(libs.coroutines.jdk8)

    testImplementation(libs.mockk)
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "dc_config|fraction_sku_demand|mps_fraction_sku_demand|recipe|recipe_snapshot|" +
                            "file_uploads|file_type|demand|aggregated_demand|demand_imported"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
