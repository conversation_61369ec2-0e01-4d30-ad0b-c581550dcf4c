package com.hellofresh.skudemandforecast.demandmatcher

import com.hellofresh.skudemandforecast.demandmatcher.repository.DemandMatcherRepository
import com.hellofresh.skudemandforecast.demandmatcher.repository.RecipeDemand
import com.hellofresh.skudemandforecast.demandmatcher.repository.RecipeDemandKey
import io.micrometer.core.instrument.Gauge
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RecipeDemandMatcherTest {

    private lateinit var demandMatcherRepository: DemandMatcherRepository
    private lateinit var meterRegistry: MeterRegistry
    private lateinit var recipeDemandMatcher: RecipeDemandMatcher

    @BeforeEach
    fun setup() {
        demandMatcherRepository = mockk()
        meterRegistry = SimpleMeterRegistry()
        recipeDemandMatcher = RecipeDemandMatcher(demandMatcherRepository, meterRegistry)
    }

    @Test
    fun `check dach Demand differences between missing demand data and imported demand data`() {
        val demandData =
            RecipeDemandKey(
                1,
                "week1",
                "day1",
                "country1",
                "dcCode1",
                "locale1",
                1,
            ) to 1
        val demandImportedData1 = demandData.first to 1
        val demandImportedData2 = RecipeDemandKey(
            2,
            "week2",
            "day2",
            "country2",
            "dcCode2",
            "locale2",
            2,
        ) to 1

        val recipeDemand = RecipeDemand(
            mapOf(
                demandData,
            ),
            mapOf(
                demandImportedData1,
                demandImportedData2,
            ),
        )

        coEvery { demandMatcherRepository.dachDemandDataComparison() } returns recipeDemand

        runBlocking {
            recipeDemandMatcher.checkDachDemand()
        }

        val metricValue = meterRegistry.meters
            .firstOrNull {
                it.id.name == "dach_missing_in_demand_table" &&
                    it.id.tags.any { tag -> tag.key == "week" && tag.value == demandImportedData2.first.week } &&
                    it.id.tags.any { tag -> tag.key == "dc" && tag.value == demandImportedData2.first.dcCode }
            }
            ?.let { (it as Gauge).value() }

        assertEquals(1.0, metricValue)
    }

    @Test
    fun `check dach Demand differences between missing demand imported data and demand data`() {
        val demandData1 =
            RecipeDemandKey(
                1,
                "week1",
                "day1",
                "country1",
                "dcCode1",
                "locale1",
                1,
            ) to 1
        val demandData2 = RecipeDemandKey(
            2,
            "week2",
            "day2",
            "country2",
            "dcCode2",
            "locale2",
            2,
        ) to 1
        val demandImportedData = demandData1.first to 1
        val recipeDemand = RecipeDemand(
            mapOf(
                demandData1,
                demandData2,
            ),
            mapOf(
                demandImportedData,
            ),
        )

        coEvery { demandMatcherRepository.dachDemandDataComparison() } returns recipeDemand

        runBlocking {
            recipeDemandMatcher.checkDachDemand()
        }

        val metricValue = meterRegistry.meters
            .firstOrNull {
                it.id.name == "dach_missing_in_demand_imported_table" &&
                    it.id.tags.any { tag -> tag.key == "week" && tag.value == demandData2.first.week } &&
                    it.id.tags.any { tag -> tag.key == "dc" && tag.value == demandData2.first.dcCode }
            }
            ?.let { (it as Gauge).value() }

        assertEquals(1.0, metricValue)
    }

    @Test
    fun `check dach Demand mealsToDeliver mismatch`() {
        val demandData1 =
            RecipeDemandKey(
                1,
                "week1",
                "day1",
                "country1",
                "dcCode1",
                "locale1",
                1,
            ) to 10
        val demandData2 = demandData1.first.copy(recipeIndex = 2) to 12

        val demandImportedData1 = demandData1.first to 11
        val demandImportedData2 = demandData2.first to 13
        val recipeDemand = RecipeDemand(
            mapOf(
                demandData1,
                demandData2,
            ),
            mapOf(
                demandImportedData1,
                demandImportedData2,
            ),
        )

        coEvery { demandMatcherRepository.dachDemandDataComparison() } returns recipeDemand

        runBlocking {
            recipeDemandMatcher.checkDachDemand()
        }
        val totalCounter = meterRegistry.meters
            .firstOrNull {
                it.id.name == "dach_different_meals_to_deliver" &&
                    it.id.tags.any { tag -> tag.key == "week" && tag.value == demandData1.first.week } &&
                    it.id.tags.any { tag -> tag.key == "dc" && tag.value == demandData1.first.dcCode }
            }
            ?.let { (it as Gauge).value() }

        assertEquals(2.0, totalCounter)
    }
}
