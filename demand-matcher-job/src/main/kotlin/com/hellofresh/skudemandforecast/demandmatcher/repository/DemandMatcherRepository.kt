package com.hellofresh.skudemandforecast.demandmatcher.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.demandmatcherjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandmatcherjob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandmatcherjob.schema.public_.Tables.DEMAND_IMPORTED
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Table

private const val FUTURE_WEEKS = 16L
private const val DACH_MARKET = "DACH"

class DemandMatcherRepository(private val dslContext: MetricsDSLContext) {

    suspend fun dachDemandDataComparison(): RecipeDemand {
        val futureWeeks = calculateFutureWeeks()
        val dcCodes = fetchDachDcs().map { it.dcCode }.toSet()
        val demandData = fetchRecipeDemandDataFromTable(DEMAND, futureWeeks, dcCodes)
        val demandImportedData = fetchRecipeDemandDataFromTable(DEMAND_IMPORTED, futureWeeks, dcCodes)

        return RecipeDemand(
            demand = demandData,
            demandImported = demandImportedData,
        )
    }

    private suspend fun fetchRecipeDemandDataFromTable(
        table: Table<*>,
        futureWeeks: Set<String>,
        dcCodes: Set<String>
    ): Map<RecipeDemandKey, Int> =
        dslContext.withTagName("fetch-dach-demand-checker")
            .selectFrom(table)
            .where(
                table.field(DEMAND.DC_CODE)?.`in`(dcCodes)
                    ?.and(table.field(DEMAND.WEEK)?.`in`(futureWeeks)),

            )
            .fetchAsync()
            .thenApply { records ->
                records.map {
                    RecipeDemandKey(
                        it.get(DEMAND.RECIPE_INDEX),
                        it.get(DEMAND.WEEK),
                        it.get(DEMAND.DAY),
                        it.get(DEMAND.COUNTRY),
                        it.get(DEMAND.DC_CODE),
                        it.get(DEMAND.LOCALE),
                        it.get(DEMAND.PEOPLE_COUNT),
                    ) to it.get(DEMAND.MEALS_TO_DELIVER)
                }.toMap()
            }
            .await()

    private fun fetchDachDcs() = dslContext.selectFrom(Tables.DC_CONFIG)
        .where(Tables.DC_CONFIG.MARKET.eq(DACH_MARKET))
        .fetch()

    private fun calculateFutureWeeks() =
        fetchDachDcs().flatMap { dc ->
            (0..FUTURE_WEEKS).map { index ->
                DcWeek(
                    LocalDate.now(ZoneId.of(dc.zoneId)).plusWeeks(index),
                    DayOfWeek.valueOf(dc.productionStart),
                ).toString()
            }
        }.toSet()

    companion object : Logging
}

data class RecipeDemand(
    val demand: Map<RecipeDemandKey, Int>,
    val demandImported: Map<RecipeDemandKey, Int>
)

data class RecipeDemandKey(
    val recipeIndex: Int,
    val week: String,
    val day: String,
    val country: String,
    val dcCode: String,
    val locale: String,
    val peopleCount: Int,
)
