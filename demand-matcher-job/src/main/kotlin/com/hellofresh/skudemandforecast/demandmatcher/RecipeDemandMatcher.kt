package com.hellofresh.skudemandforecast.demandmatcher

import com.hellofresh.skudemandforecast.demandmatcher.repository.DemandMatcherRepository
import com.hellofresh.skudemandforecast.demandmatcher.repository.RecipeDemandKey
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Tags
import org.apache.logging.log4j.kotlin.Logging

class RecipeDemandMatcher(
    private val demandMatcherRepository: DemandMatcherRepository,
    private val meterRegistry: MeterRegistry
) {

    suspend fun checkDachDemand() {
        logger.info("dach Demand vs Demand imported comparison started")
        val dachDemandData = demandMatcherRepository.dachDemandDataComparison()

        val demandData = dachDemandData.demand.keys
        val demandImportedData = dachDemandData.demandImported.keys

        val allDemandData = demandData.groupBy {
            DachDemandMetricKey(it.week, it.dcCode)
        }

        val allDemandImportedData = demandImportedData.groupBy {
            DachDemandMetricKey(it.week, it.dcCode)
        }

        val allDachDemandMetricKey = allDemandData.keys + allDemandImportedData.keys

        val missingInDemandImportedData = demandData.subtract(demandImportedData).groupBy {
            DachDemandMetricKey(it.week, it.dcCode)
        }
        val missingInDemandData = demandImportedData.subtract(demandData).groupBy {
            DachDemandMetricKey(it.week, it.dcCode)
        }
        val mismatchData = calculateMealsToDeliverMismatch(dachDemandData.demand, dachDemandData.demandImported)

        addMetrics(allDachDemandMetricKey, missingInDemandData, missingInDemandImportedData, mismatchData)
        logger.info("dach Demand vs Demand imported comparison ended")
    }

    private fun calculateMealsToDeliverMismatch(demandData: Map<RecipeDemandKey, Int>, demandImportedData: Map<RecipeDemandKey, Int>) =
        demandData.filter { (key, _) ->
            demandImportedData.containsKey(key)
        }.keys.mapNotNull { key ->
            if (demandData[key] != demandImportedData[key]) {
                key
            } else {
                null
            }
        }.groupBy {
            DachDemandMetricKey(it.week, it.dcCode)
        }

    private fun addMetrics(
        allDachDemandMetricKey: Set<DachDemandMetricKey>,
        missingDemandData: Map<DachDemandMetricKey, List<RecipeDemandKey>>,
        missingImportedDemandData: Map<DachDemandMetricKey, List<RecipeDemandKey>>,
        mismatchKeysData: Map<DachDemandMetricKey, List<RecipeDemandKey>>
    ) {
        logMissingDemands("Missing demands in the existing demand table", missingDemandData)
        logMissingDemands("Missing demands in the new demand imported table", missingImportedDemandData)

        allDachDemandMetricKey.forEach { metricKey ->
            addGauge("dach_missing_in_demand_table", metricKey, missingDemandData[metricKey]?.size)
            addGauge("dach_missing_in_demand_imported_table", metricKey, missingImportedDemandData[metricKey]?.size)
            addGauge("dach_different_meals_to_deliver", metricKey, mismatchKeysData[metricKey]?.size)
        }
    }

    private fun logMissingDemands(message: String, demandData: Map<*, List<RecipeDemandKey>>) {
        demandData.values.flatten().forEach { recipeDemandKey ->
            logger.info(
                message + """
            dcCode        = ${recipeDemandKey.dcCode}
            week          = ${recipeDemandKey.week}
            recipeIndex   = ${recipeDemandKey.recipeIndex}
            day           = ${recipeDemandKey.day}
            locale        = ${recipeDemandKey.locale}
            country       = ${recipeDemandKey.country}
            peopleCount   = ${recipeDemandKey.peopleCount}
                """.trimIndent(),
            )
        }
    }

    private fun addGauge(metricName: String, dachDemandMetricKey: DachDemandMetricKey, value: Int?) {
        if (value != null && value > 0) {
            logger.warn("Issue found for metric $metricName , key: $dachDemandMetricKey, value: $value")
        }
        meterRegistry.addGauge(
            metricName,
            value?.toDouble() ?: 0.0,
            Tags.of(Tag.of("week", dachDemandMetricKey.week), Tag.of("dc", dachDemandMetricKey.dcCode)),
        )
    }

    data class DachDemandMetricKey(val week: String, val dcCode: String)

    companion object : Logging
}
