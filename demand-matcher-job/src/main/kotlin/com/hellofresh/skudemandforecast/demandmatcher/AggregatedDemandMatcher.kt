package com.hellofresh.skudemandforecast.demandmatcher

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.demandmatcherjob.schema.public_.Tables.AGGREGATED_DEMAND
import com.hellofresh.skudemandforecast.demandmatcherjob.schema.public_.Tables.FRACTION_SKU_DEMAND
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tags
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DatePart.MINUTE
import org.jooq.impl.DSL

/**
Compares original demand from aggregate demand table with aggregated fraction demand values
 **/

const val DEMAND_DAYS_BEFORE = 7L
const val DC_CHUNK_SIZE = 5
const val DEMAND_LAST_UPDATE_BUFFER_MINUTES = 5

class AggregatedDemandMatcher(
    private val dslContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) {

    suspend fun matchRecipeAggregatedDemand(meterRegistry: MeterRegistry) {
        dcConfigService.dcConfigurations.values
            .filter { it.enabled }
            .chunked(DC_CHUNK_SIZE)
            .forEach { dcs ->
                val dcsByCode = dcs.associateBy { it.dcCode }
                val fractionAggregated = fetchFractionDemandAggregatedCandidates(dcs)

                if (fractionAggregated.isNotEmpty()) {
                    val aggregatedDemand = fetchAggregatedDemand(
                        dcsByCode.keys.toList(),
                        fractionAggregated.minOf { it.key.date },
                    )

                    compare(meterRegistry, dcsByCode, fractionAggregated, aggregatedDemand)
                }
            }
    }

    private fun compare(
        meterRegistry: MeterRegistry,
        dcsByCode: Map<String, DistributionCenter>,
        fractionAggregated: Map<AggregatedDemandKey, BigDecimal>,
        aggregatedDemand: Map<AggregatedDemandKey, BigDecimal>
    ) {
        logger.info("Comparing Recipe Aggregated Demand for dcs: ${dcsByCode.keys}")

        val (existingKeys, missingKeysAggDemand) = fractionAggregated.keys.partition {
            aggregatedDemand.containsKey(
                it,
            )
        }

        meterRegistry.addMetricKeys(
            "recipe_agg_demand_missing",
            fractionAggregated.keys,
            missingKeysAggDemand,
            dcsByCode,
        )
        logger.info("Missing Aggregated Demand Keys: ${objectMapper.writeValueAsString(missingKeysAggDemand)}")

        val (matching, mismatching) = existingKeys.partition { fractionAggregated[it] == aggregatedDemand[it] }

        meterRegistry.addMetricKeys("recipe_agg_demand_match", fractionAggregated.keys, matching, dcsByCode)

        meterRegistry.addMetricKeys("recipe_agg_demand_mismatch", fractionAggregated.keys, mismatching, dcsByCode)
        logger.info("Mismatching Aggregated Demand Keys: ${objectMapper.writeValueAsString(missingKeysAggDemand)}")
    }

    private fun MeterRegistry.addMetricKeys(
        metricName: String,
        allKeys: Set<AggregatedDemandKey>,
        metricKeys: List<AggregatedDemandKey>,
        dcsByCode: Map<String, DistributionCenter>
    ) {
        val metricTagKey = metricKeys
            .groupBy { createMetricTagKey(it, dcsByCode[it.dcCode]!!) }

        allKeys
            .map { createMetricTagKey(it, dcsByCode[it.dcCode]!!) }
            .distinct()
            .forEach {
                val (dc, week) = it
                addGauge(
                    metricName,
                    metricTagKey[it]?.size?.toDouble() ?: 0.0,
                    Tags.of("dc", dc).and("week", week),
                )
            }
    }

    private fun createMetricTagKey(aggregatedDemandKey: AggregatedDemandKey, dcConfig: DistributionCenter) =
        aggregatedDemandKey.dcCode to DcWeek(aggregatedDemandKey.date, dcConfig.productionStart).value

    private suspend fun fetchFractionDemandAggregatedCandidates(dcs: List<DistributionCenter>): Map<AggregatedDemandKey, BigDecimal> {
        val aggQty = DSL.sum(FRACTION_SKU_DEMAND.QTY)
        val maxUpdateAt = DSL.max(FRACTION_SKU_DEMAND.UPDATED_AT)
        val selectGroupKeys = dslContext.select(
            FRACTION_SKU_DEMAND.DC_CODE,
            FRACTION_SKU_DEMAND.DEMAND_DATE,
            FRACTION_SKU_DEMAND.SKU_ID,
            aggQty,
            maxUpdateAt,
        )
            .from(FRACTION_SKU_DEMAND)
            .where(
                FRACTION_SKU_DEMAND.DEMAND_DATE?.ge(LocalDate.now(ZoneOffset.UTC).minusDays(DEMAND_DAYS_BEFORE)),
                FRACTION_SKU_DEMAND.DC_CODE?.`in`(dcs),
            ).groupBy(
                FRACTION_SKU_DEMAND.DC_CODE,
                FRACTION_SKU_DEMAND.DEMAND_DATE,
                FRACTION_SKU_DEMAND.SKU_ID,
            )

        return dslContext.withTagName("fetch-fraction-demand-candidates")
            .selectFrom(selectGroupKeys)
            .where(
                selectGroupKeys.field(maxUpdateAt)?.le(
                    DSL.localDateTimeSub(
                        DSL.currentLocalDateTime(),
                        DEMAND_LAST_UPDATE_BUFFER_MINUTES,
                        MINUTE,
                    ),
                ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.map {
                    AggregatedDemandKey(
                        skuId = it[FRACTION_SKU_DEMAND.SKU_ID],
                        dcCode = it[FRACTION_SKU_DEMAND.DC_CODE],
                        date = it[FRACTION_SKU_DEMAND.DEMAND_DATE],
                    ) to it[aggQty]
                }.toMap()
            }.await()
    }

    private suspend fun fetchAggregatedDemand(
        dcs: List<String>,
        date: LocalDate
    ): Map<AggregatedDemandKey, BigDecimal> =
        dslContext.withTagName("fetch-aggregated-demand")
            .select(
                AGGREGATED_DEMAND.DC_CODE,
                AGGREGATED_DEMAND.PRODUCTION_DATE,
                AGGREGATED_DEMAND.SKU_ID,
                AGGREGATED_DEMAND.ORIGINAL_DEMAND,
            )
            .from(AGGREGATED_DEMAND)
            .where(
                AGGREGATED_DEMAND.PRODUCTION_DATE.ge(
                    date,
                ).and(AGGREGATED_DEMAND.DC_CODE.`in`(dcs)),
            )
            .fetchAsync()
            .thenApply { results ->
                results.map {
                    AggregatedDemandKey(
                        skuId = it[AGGREGATED_DEMAND.SKU_ID],
                        dcCode = it[AGGREGATED_DEMAND.DC_CODE],
                        date = it[AGGREGATED_DEMAND.PRODUCTION_DATE],
                    ) to BigDecimal(it[AGGREGATED_DEMAND.ORIGINAL_DEMAND])
                }.toMap()
            }.await()

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}

private data class AggregatedDemandKey(
    val dcCode: String,
    val skuId: UUID,
    val date: LocalDate,
)
