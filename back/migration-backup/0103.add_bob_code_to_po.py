from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE ordering.purchase_order
            ADD COLUMN IF NOT EXISTS bob_code text,
            ALTER COLUMN dc DROP NOT NULL,
            ALTER COLUMN supplier DROP NOT NULL,
            ALTER COLUMN supplier_code TYPE INTEGER USING supplier_code::integer
        ;
        UPDATE "ordering".purchase_order SET bob_code = substring(po_number, 5, 2);
        ALTER TABLE "ordering".purchase_order ALTER bob_code SET NOT NULL;
        CREATE INDEX i_ordering_purchase_order_bob_code ON ordering.purchase_order (bob_code);
        """
    ),
    step(
        """
        DELETE FROM ordering.purchase_order_sku WHERE (po_uuid, sku_uuid, id) IN (
            SELECT
                pos.po_uuid, pos.sku_uuid, pos.id
            FROM "ordering".purchase_order_sku pos
            JOIN (
                SELECT
                po_uuid, sku_uuid, max(id) AS max_id
                FROM "ordering".purchase_order_sku pos
                GROUP BY po_uuid, sku_uuid
                HAVING count(*) > 1
            ) AS t
                ON pos.po_uuid = t.po_uuid AND pos.sku_uuid = t.sku_uuid AND pos.id <> t.max_id
        );
        """
    ),
    step(
        """
        ALTER TABLE ordering.purchase_order_sku ADD CONSTRAINT u_po_sku_uuids UNIQUE (po_uuid, sku_uuid);
        """
    ),
]
