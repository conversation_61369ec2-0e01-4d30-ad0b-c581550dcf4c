from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE highjump.latest_hj_pallet_snapshots (
            sku_code        VARCHAR(255),
            pallet_quantity REAL,
            wh_id           VARCHAR(255),
            sync_date       DATE,
            expiration_date DATE,
            brand           VARCHAR(255)
        );
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS latest_hj_pallet_snapshots_brand_wh_id
        ON highjump.latest_hj_pallet_snapshots (brand, wh_id);
    """
    ),
]
