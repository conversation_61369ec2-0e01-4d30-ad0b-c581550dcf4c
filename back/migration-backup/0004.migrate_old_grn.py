from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO pimt.goods_receipt_note
            SELECT
                grn_line.sku_code,
                grn_line.order_number,
                max(grn_line.sku_id) AS sku_id,
                max(grn_line.receive_date) AS delivery_time,
                max(grn.bob_code) AS bob_code,
                sum(grn_line.case_qty * pos.case_size) AS units
            FROM pimt.goods_receipt_notification AS grn
            JOIN pimt.grn_line_item AS grn_line
                USING (receipt_id)
            JOIN "ordering".purchase_order AS po
                ON grn_line.order_number = po.order_number AND NOT po.deleted
            JOIN "ordering".purchase_order_sku pos
                USING(po_uuid)
            JOIN "ordering".sku po_sku
                ON po_sku.sku_uuid = pos.sku_uuid AND po_sku.sku_code = grn_line.sku_code
            WHERE pos.case_size <> 0
            GROUP BY grn_line.sku_code, grn_line.order_number
        ;
        """,
    ),
]
