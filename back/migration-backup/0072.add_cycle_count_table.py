from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.cycle_counts (
            sku_code character varying(40) NOT NULL,
            site character varying(4) NOT NULL,
            cycle_count_day date NOT NULL,
            units integer,
            brand character varying(4) NOT NULL,
            scm_week integer NOT NULL,
            PRIMARY KEY (scm_week, brand, site, sku_code, cycle_count_day)
        );
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta ("name",doc_code,weekly,"order",title_template,brand,required,project)
            VALUES
            (
                'GreenChef Cycle Counts',
                'cycle_count_gc',
                false,
                34,
                'GreenChef Cycle Counts',
                'GC',
                false,
                'imt'
            );
        """
    ),
]
