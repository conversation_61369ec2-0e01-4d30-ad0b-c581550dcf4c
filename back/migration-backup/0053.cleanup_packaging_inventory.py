from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DROP TABLE IF EXISTS inventory.packaging_liner_inventory;
        DROP TABLE IF EXISTS inventory.packaging_inventory;
        """
    ),
    step(
        """
        DELETE FROM procurement.gsheet_admin
            WHERE meta_id IN (SELECT "id" from procurement.gsheet_meta WHERE doc_code = 'in_week_pit');
        DELETE FROM procurement.gsheet_meta WHERE doc_code = 'in_week_pit';
        """
    ),
]
