from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.organic_sku (
            conventional_sku varchar(20) NOT NULL,
            organic_sku varchar(20) NOT NULL,

            CONSTRAINT organic_sku_pkey PRIMARY KEY (conventional_sku, organic_sku)
        );
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta (name, doc_code, weekly, "order", title_template, brand, required, project)
        VALUES ('Organic SKU Mapping', 'org_sku', FALSE, 6, 'GC Produce ORG + CV', 'GC', FALSE, 'imt')
        ;

        UPDATE procurement.gsheet_meta SET title_template = 'GC OSCAR' WHERE doc_code = 'gc_oscar' AND brand = 'GC';
        UPDATE procurement.gsheet_meta SET title_template = 'GC Production Kit Guide - {scm_week}'
            WHERE doc_code = 'pkg' AND brand = 'GC';
        UPDATE procurement.gsheet_meta SET title_template = 'GC Hybrid Needs - Week {week_number} - {year}'
            WHERE doc_code = 'hybrid_needs' AND brand = 'GC';
        """
    ),
]
