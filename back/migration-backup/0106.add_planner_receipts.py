from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.inventory_planner_receipt (
            bob_code TEXT NOT NULL,
            sku_uuid TEXT NOT NULL,
            week INTEGER NOT NULL,
            receipts TEXT [] NOT NULL,
            PRIMARY KEY (bob_code, week, sku_uuid)
        );
        """
    ),
]
