from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta ("name",doc_code,weekly,"order",title_template,brand,required,project)
            VALUES
            (
                'Master Supplier Portal List',
                'supplier_portal',
                false,
                33,
                'Master Supplier Portal List',
                'HF',
                false,
                'imt'
            );
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.supplier_portal(
            supplier_name varchar(255) NOT NULL,
            supplier_code integer PRIMARY KEY,
            UNIQUE (supplier_name)
        );
        """
    ),
]
