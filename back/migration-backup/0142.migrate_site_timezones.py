from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        UPDATE inventory.site s
        SET timezone = dc.tz
        FROM
            inventory.weekly_config wc
            JOIN procurement.distribution_center dc
                ON wc.properties->>'bob_code' = dc.bob_code
        WHERE wc.site = s.id AND wc.brand = s.brand AND wc.market = s.market AND wc.properties->'bob_code' IS NOT NULL;

        -- fix for old sites with missing bob code
        UPDATE inventory.site SET timezone = 'America/Chicago' WHERE timezone = '-05:00';

        -- fix dc registry invalid tz
        UPDATE inventory.site SET timezone = 'America/Phoenix' WHERE timezone = 'US/Arizona';
        """
    )
]
