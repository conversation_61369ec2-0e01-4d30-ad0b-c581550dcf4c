from migrations.scripts.permissions import create_permissions
from yoyo import step

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

permission = Permissions.HJ_PCK_SNAPSHOT_JOB
role_mapping = {"admin": [permission.read, permission.write]}


steps = [
    *create_permissions(role_mapping),
    step(
        """
        INSERT INTO procurement.gsheet_meta ("name",doc_code,weekly,"order",title_template,brand,required,project)
            VALUES
            (
                'In - Week Packaging Demand',
                'packaging_demand',
                true,
                32,
                'In - Week Packaging Demand {scm_week}',
                'HF',
                false,
                'imt'
            );
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_demand(
            week integer NOT NULL,
            site varchar(4) NOT NULL,
            brand varchar(4) NOT NULL,
            sku_code varchar(20) NOT NULL,
            demand_by_day integer[],

            PRIMARY KEY (week, site, brand, sku_code)
        );
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_liner_demand(
            week integer NOT NULL,
            site varchar(4) NOT NULL,
            brand varchar(4) NOT NULL,
            liner_group varchar(40) NOT NULL,
            demand_by_day integer[],

            PRIMARY KEY (week, site, brand, liner_group)
        );
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS highjump.packaging_pallet_snapshot(
            snapshot_date date NOT NULL,
            wh_id varchar(8) NOT NULL,
            sku_code varchar(20) NOT NULL,
            pallet_quantity integer NOT NULL,

            PRIMARY KEY (snapshot_date, wh_id, sku_code)
        );
        """
    ),
]
