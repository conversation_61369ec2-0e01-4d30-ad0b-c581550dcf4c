from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.work_order (
            wo_uuid TEXT PRIMARY KEY,
            menu_week TEXT NOT NULL,
            high_jump_warehouse_id TEXT NOT NULL,
            recipe_code TEXT NOT NULL,
            sub_recipe_code TEXT NOT NULL,
            sub_recipe_name TEXT NOT NULL,
            target_amount INTEGER NOT NULL,
            scheduled_date DATE NOT NULL,
            scheduled_shift INTEGER NOT NULL
        );
        """
    ),
]
