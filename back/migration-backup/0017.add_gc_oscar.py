from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE forecast.oscar
        ALTER COLUMN forecast TYPE DECIMAL USING forecast::DECIMAL,
        ADD COLUMN forecast_by_day DECIMAL [],
        ADD COLUMN units VARCHAR(20) NOT NULL DEFAULT 'unit';
        """,
    ),
    step(
        """
        ALTER TABLE inventory.compiled ALTER COLUMN weights TYPE DECIMAL[] USING weights::DECIMAL[];
        ALTER TABLE inventory.compiled RENAME TO mock_plan;
        ALTER TABLE inventory.mock_plan SET SCHEMA forecast;
        """
    ),
]
