from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.inventory_snapshot DROP CONSTRAINT inventory_snapshot_pkey;
        ALTER TABLE inventory.inventory_snapshot ADD CONSTRAINT inventory_snapshot_pkey PRIMARY KEY
        (wh_code, snapshot_ts, state, expiration_date, location_type, location_id, sku_code, inventory_type);
        CREATE INDEX iinventory_snapshot_snapshot_ts ON inventory.inventory_snapshot (snapshot_ts);
        """
    ),
]
