from migrations.scripts.permissions import create_permissions
from yoyo.migrations import step

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.buffer_analysis_comment (
            market TEXT,
            week INTEGER,
            brand TEXT,
            site TEXT,
            sku_code TEXT,
            comment TEXT,
            last_edited_by TEXT,
            UNIQUE (market, week, brand, site, sku_code)
        );
        """
    ),
    create_permissions(
        {
            role: [
                Permissions.IMT_ANALYTICS_BUFFER_ANALYSIS_V1.read,
                Permissions.IMT_ANALYTICS_BUFFER_ANALYSIS_V1.write,
            ]
            for role in [
                "admin",
                "procurementmanager",
                "procurementuser",
                "procurementleadership",
                "scrubber",
                "dcuniversaluser",
                "dcmanagementuser",
                "dcuser",
            ]
        }
    ),
]
