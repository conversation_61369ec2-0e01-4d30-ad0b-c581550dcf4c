from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (name, doc_code, weekly, "order", title_template,
        required, project, market)
        VALUES
        ('Master Ambient Safety Stock Data', 'ambient_safety_stock', false, 24,
         'Master Ambient Safety Stock Data', true, 'imt', 'US'
        );
        """,
    ),
    step(
        """
        INSERT INTO procurement.gsheet_admin (meta_id, gsheet_id, created_tmst, updated_tmst)
            SELECT id, '1_rFyBsBTQ_Q5ei_CEOfXfpyfKyBvs_Se1je_6arx2Ss', now(), now()
            FROM procurement.gsheet_meta WHERE doc_code = 'ambient_safety_stock';
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_combined_report (
            region TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            market TEXT NOT NULL,
            supplier_name TEXT NOT NULL,
            units NUMERIC,
            pallets NUMERIC,
            is_hfo bool,
            PRIMARY KEY (sku_code, region, supplier_name, market)
        );
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_units_per_truck (
            bob_code TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            demand_pipeline TEXT NOT NULL,
            units_per_truck_load NUMERIC,
            PRIMARY KEY (bob_code, sku_code, demand_pipeline)
        );
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS forecast.packaging_long_term_forecast_mapping (
            sku_code TEXT NOT NULL,
            sku_type TEXT NOT NULL,
            market TEXT NOT NULL,
            demand_pipeline TEXT NOT NULL,
            PRIMARY KEY (sku_code, demand_pipeline, market)
        );
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS forecast.packaging_long_term_forecast (
            week TEXT NOT NULL,
            brand TEXT NOT NULL,
            site TEXT NOT NULL,
            market TEXT NOT NULL,
            forecast NUMERIC,
            demand_pipeline TEXT NOT NULL,
            sku_type TEXT NOT NULL,
            PRIMARY KEY (week, brand, site, market, sku_type, demand_pipeline)
        );
        """,
    ),
]
