from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS procurement.inventory_module_in_house_adjustment(
            sku_code text not null,
            brand text not null,
            site text not null,
            in_house_adjustment integer,
            adjustment_date date,
            PRIMARY KEY (sku_code, site, brand)
        );
        """
    ),
]
