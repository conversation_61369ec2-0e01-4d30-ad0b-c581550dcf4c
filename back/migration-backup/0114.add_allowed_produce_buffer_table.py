from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.allowed_produce_buffer (
            brand TEXT NOT NULL,
            site TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            week INTEGER NOT NULL,
            total_buffer decimal NOT NULL,
            PRIMARY KEY (brand, site, week, sku_code)
        );
        """
    ),
]
