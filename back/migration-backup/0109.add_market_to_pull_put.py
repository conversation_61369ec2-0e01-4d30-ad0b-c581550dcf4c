from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.pull_put ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.pull_put ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_pull_put ON inventory.pull_put (market);
        """
    )
]
