from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta
            (name, doc_code, weekly, "order", title_template, brand, required, project)
        VALUES
        (
            'New Production Kit Guide', 'pkg_v2', true, 1051,
            'EveryPlate Production Kit Guide - {scm_week}', 'EP', false, 'imt'
        );

        ALTER TABLE inventory.mealkit_ingredient ADD COLUMN picks_6p INTEGER NOT NULL DEFAULT 0;
        ALTER TABLE inventory.mealkit_ingredient ALTER COLUMN picks_6p DROP DEFAULT;

        ALTER TABLE inventory.remake_tool ADD COLUMN picks_6p INTEGER NOT NULL DEFAULT 0,
        ALTER COLUMN picks_2p SET NOT NULL , ALTER COLUMN picks_4p SET NOT NULL ;
        ALTER TABLE inventory.remake_tool ALTER COLUMN picks_6p DROP DEFAULT;
        """
    ),
]
