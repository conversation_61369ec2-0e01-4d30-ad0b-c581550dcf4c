from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.goods_receipt_note (
            bob_code varchar(4) NOT NULL,
            po_number varchar(20) NOT NULL,
            order_number varchar(20) NOT NULL,
            sku_code varchar(20) NOT NULL,
            sku_id int,
            status smallint NOT NULL,
            week int NOT NULL,
            units_received decimal,
            receipt_time_est timestamp,
            unit varchar(10),
            update_ts timestamp NOT NULL DEFAULT current_timestamp,
            source varchar(10) NOT NULL
        );
        """
    ),
    # migrate old data
    step(
        """
        WITH t AS (
            SELECT
            po_number,
            sku_code,
            scm_week_raw::int AS week,
            sum(quantity_received) OVER(PARTITION BY po_number, sku_code, status, week) AS units_received,
            receipt_time_est,
            CASE
                WHEN status = 'Closed' THEN 5
                WHEN status = 'Rejected' THEN 4
                WHEN status = 'Cancelled' THEN 4  -- rejected
                WHEN status = 'Printing Labels' THEN 3  -- received
                WHEN status = 'Ready to Print Labels' THEN 3  -- received
                WHEN status = 'In QC Check' THEN 2  -- open
                WHEN status = 'QC Supervisor Needed' THEN 2  -- open
                WHEN status = 'Unloading' THEN 3  -- received
                ELSE 0  -- unspecified
            END AS status,
            unit,
            row_number() OVER(
                PARTITION BY
                po_number,
                sku_code,
                CASE
                    WHEN status = 'Closed' THEN 5
                    WHEN status = 'Rejected' THEN 4
                    WHEN status = 'Cancelled' THEN 4  -- rejected
                    WHEN status = 'Printing Labels' THEN 3  -- received
                    WHEN status = 'Ready to Print Labels' THEN 3  -- received
                    WHEN status = 'In QC Check' THEN 2  -- open
                    WHEN status = 'QC Supervisor Needed' THEN 2  -- open
                    WHEN status = 'Unloading' THEN 3  -- received
                    ELSE 0  -- unspecified
                END
                ORDER BY week DESC
            ) AS rn
            FROM highjump.v_procurement_export_receipts
        )
        INSERT INTO inventory.goods_receipt_note
            SELECT
                substring(po_number, 5, 2) AS bob_code,
                po_number,
                substring(po_number, 1, 12) order_number,
                sku_code,
                ((regexp_split_to_array((sku_code)::text, '-'::text))[3])::integer AS sku_id,
                status,
                "week",
                units_received,
                receipt_time_est,
                unit,
                current_timestamp AS update_ts,
                'HJ' AS "source"
            FROM t
            WHERE rn = 1
        ;
        """
    ),
    step(
        """
        ALTER TABLE inventory.goods_receipt_note
        ADD CONSTRAINT goods_receipt_note_pkey PRIMARY KEY (po_number, sku_code, status);
        """
    ),
]
