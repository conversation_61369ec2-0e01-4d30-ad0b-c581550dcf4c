from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.brand_config ADD market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.brand_config ALTER market DROP DEFAULT;
        ALTER TABLE inventory.brand_config RENAME brand_id TO brand;
        ALTER TABLE inventory.brand_config
            DROP CONSTRAINT brand_config_pkey,
            ADD CONSTRAINT brand_config_pkey PRIMARY KEY (brand, market, "week");
        ALTER TABLE inventory.brand_config DROP CONSTRAINT brand_config_brand_id_fkey;
        """
    ),
    step(
        """
        ALTER TABLE inventory.weekly_config ADD market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.weekly_config ALTER market DROP DEFAULT;
        ALTER TABLE inventory.weekly_config RENAME brand_id TO brand;
        ALTER TABLE inventory.weekly_config RENAME site_id TO site;
        ALTER TABLE inventory.weekly_config
            DROP CONSTRAINT IF EXISTS u_week_site_brand,
            ADD CONSTRAINT u_weekly_config_site_brand_market_week UNIQUE (site, brand, market, "week");
        DROP INDEX IF EXISTS inv_week_config_week_brand_site;
        CREATE INDEX i_weekly_config_brand_market_week ON inventory.weekly_config (brand, market, "week");
        ALTER TABLE inventory.weekly_config
            DROP CONSTRAINT weekly_config_brand_id_fkey,
            DROP CONSTRAINT weekly_config_site_id_fkey;
        """
    ),
    step(
        """
        ALTER TABLE inventory.brand ADD market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.brand ALTER market DROP DEFAULT;
        ALTER TABLE inventory.brand
            DROP CONSTRAINT brand_pkey,
            ADD CONSTRAINT brand_pkey PRIMARY KEY (id, market);
        """
    ),
    step(
        """
        ALTER TABLE inventory.site ADD brand TEXT, ADD market TEXT;
        ALTER TABLE inventory.site ALTER market DROP DEFAULT;
        ALTER TABLE inventory.site DROP CONSTRAINT site_pkey;

        INSERT INTO inventory.site (
            id,
            sequence_number,
            name,
            created_tmst,
            updated_tmst,
            user_id,
            dc_azure_role,
            timezone,
            brand,
            market
        )
        SELECT
            s.id,
            s.sequence_number,
            s.name,
            s.created_tmst,
            s.updated_tmst,
            s.user_id,
            s.dc_azure_role,
            s.timezone,
            t.brand,
            'US'
        FROM inventory.site AS s
        JOIN (
            SELECT DISTINCT
                site, brand
            FROM inventory.weekly_config
        ) AS t
        ON s.id = t.site
        ;

        DELETE FROM inventory.site WHERE market IS NULL;  -- delete old records

        ALTER TABLE inventory.site ALTER brand SET NOT NULL, ALTER market SET NOT NULL;
        ALTER TABLE inventory.site ADD CONSTRAINT site_pkey PRIMARY KEY (id, brand, market);
        """
    ),
    step(
        """
        ALTER TABLE procurement.parallel_sync_log ADD market TEXT;
        UPDATE procurement.parallel_sync_log SET market = 'US' WHERE global_parent_id IS NULL;
        DROP INDEX IF EXISTS procurement.global_parent_id_parallel_sync_log;  -- index is duplicated
        DROP INDEX procurement.job_name_parallel_sync_log;
        CREATE INDEX i_parallel_sync_log_job_name_market_sync_time
            ON procurement.parallel_sync_log (job_name, market, sync_time);
        """
    ),
    step(
        """
        CREATE TABLE inventory.market (
            code TEXT PRIMARY KEY,
            "name" TEXT NOT NULL
        );

        INSERT INTO inventory.market
        VALUES
            ('US', 'United States'),
            ('CA', 'Canada')
        ;
        """
    ),
    step(
        """
        ALTER TABLE procurement.gsheet_meta ADD market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE procurement.gsheet_meta ALTER market DROP DEFAULT;
        ALTER TABLE procurement.gsheet_meta
            DROP CONSTRAINT gsheet_meta_doc_code_brand_project_key,
            ADD CONSTRAINT gsheet_meta_doc_code_brand_project_key UNIQUE (doc_code, brand, market, project);
        """
    ),
]
