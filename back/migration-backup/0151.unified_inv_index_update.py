from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step("ALTER TABLE inventory.unified_inventory ADD location_id TEXT;"),
    step(
        """
        CREATE UNIQUE INDEX IF NOT EXISTS i_unified_inventory_key ON inventory.unified_inventory (
            order_number, sku_code, wh_code, inventory_type, lot_code, location_id,
            inventory_status, expiration_date, snapshot_timestamp
        ) NULLS NOT DISTINCT;
        """
    ),
    step("ALTER TABLE inventory.unified_inventory DROP CONSTRAINT IF EXISTS unified_inventory_uniq;"),
]
