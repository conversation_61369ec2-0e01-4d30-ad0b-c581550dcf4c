from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS forecast.tpl_mock_plan (
            site varchar(5) NOT NULL,
            production_type varchar(50) NOT NULL,
            weights DECIMAL[] NULL,
            PRIMARY KEY (site, production_type)
        );
        """,
    ),
    step(
        """
        DROP TABLE IF EXISTS forecast.forecast_3pl;
        DROP TABLE IF EXISTS forecast.packaging_3pl;
        DROP TABLE IF EXISTS forecast.planing_3pl;
        """,
    ),
]
