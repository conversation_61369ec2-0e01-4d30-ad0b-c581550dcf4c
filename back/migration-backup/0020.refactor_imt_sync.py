from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("ALTER TABLE inventory.isp DROP CONSTRAINT isp_sku_code_fkey;"),
    step("UPDATE procurement.gsheet_meta SET project = 'imt' WHERE  project = '3pl';"),
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id IN
            (SELECT id FROM procurement.gsheet_meta WHERE doc_code = 'imt_data_dumps');
        DELETE FROM procurement.gsheet_meta WHERE doc_code = 'imt_data_dumps';
    """
    ),
]
