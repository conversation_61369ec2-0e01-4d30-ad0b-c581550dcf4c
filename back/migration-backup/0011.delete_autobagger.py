from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DROP TABLE IF EXISTS highjump.v_procurement_export_autobagger, inventory.autobagger_skus;
        DELETE  FROM procurement.permission_role WHERE permission_id IN (
        SELECT id from procurement.permission WHERE name in ('imt-autobagger-sku-v1:r', 'imt-autobagger-sku-v1:w'));
        DELETE FROM procurement.permission where name IN ('imt-autobagger-sku-v1:r', 'imt-autobagger-sku-v1:w');
        """
    ),
]
