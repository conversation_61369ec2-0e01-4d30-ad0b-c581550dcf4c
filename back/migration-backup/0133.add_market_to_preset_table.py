from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE procurement.network_depletion_preset ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE procurement.network_depletion_preset ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE procurement.network_depletion_preset
            DROP CONSTRAINT IF EXISTS network_depletion_preset_pkey,
            ADD CONSTRAINT network_depletion_preset_pkey PRIMARY KEY (user_id, id, market);
        """
    ),
]
