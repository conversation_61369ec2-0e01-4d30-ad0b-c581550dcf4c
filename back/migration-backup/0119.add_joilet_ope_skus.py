from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (
        name, doc_code, weekly, "order", title_template, brand, project, required, market)
        VALUES ('Joilet OPE SKUs', 'joilet_ope_skus', <PERSON>als<PERSON>, 21, 'Joliet OPE SKUs', 'FJ', 'imt', False, 'US');
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.joilet_ope_skus (sku_code TEXT PRIMARY KEY);
        """
    ),
]
