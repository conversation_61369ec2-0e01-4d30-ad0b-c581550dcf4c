from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.hj_override ADD COLUMN IF NOT EXISTS deleted_by VARCHAR(255);
        ALTER TABLE inventory.hj_override ADD COLUMN IF NOT EXISTS deleted_ts TIMESTAMP without time zone;
        """,
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS i_inventory_hj_override_deleted_ts ON inventory.hj_override (deleted_ts);
        """
    ),
]
