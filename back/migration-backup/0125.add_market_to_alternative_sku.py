from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.organic_sku_mapping ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;

        ALTER TABLE inventory.organic_sku_mapping ALTER COLUMN market DROP DEFAULT;

        ALTER TABLE inventory.organic_sku_mapping
            DROP CONSTRAINT IF EXISTS organic_sku_pkey,
            ADD CONSTRAINT organic_sku_mapping_pkey PRIMARY KEY (interchangable_sku_code, original_sku_code, market);

        ALTER TABLE inventory.packaging_sku_mapping ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;

        ALTER TABLE inventory.packaging_sku_mapping ALTER COLUMN market DROP DEFAULT;

        ALTER TABLE inventory.packaging_sku_mapping
            DROP CONSTRAINT IF EXISTS packaging_sku_mapping_pkey,
            ADD CONSTRAINT packaging_sku_mapping_pkey PRIMARY KEY (interchangable_sku_code, original_sku_code, market);
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta (
            name, doc_code, weekly, "order", title_template, brand, required, project, market
        )
        VALUES ('RWA SKU mapping', 'rwa_sku_mapping', False, 6, 'Canada RWA + CV', 'HF', False, 'imt', 'CA');
        """
    ),
]
