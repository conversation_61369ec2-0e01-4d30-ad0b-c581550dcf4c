from migrations.scripts import permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    # Drop old migrations history
    step(
        """
        DELETE FROM _yoyo_log;
        DELETE FROM _yoyo_migration;
        """
    ),
    # Create Schemas
    step(
        """
        CREATE SCHEMA IF NOT EXISTS forecast;
        CREATE SCHEMA IF NOT EXISTS highjump;
        CREATE SCHEMA IF NOT EXISTS inventory;
        CREATE SCHEMA IF NOT EXISTS ordering;
        CREATE SCHEMA IF NOT EXISTS pimt;
        CREATE SCHEMA IF NOT EXISTS procurement;
        """
    ),
    step(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'checklist_status_enum') THEN
            CREATE TYPE procurement.checklist_status_enum AS ENUM
            (
                'Completed',
                'In Progress',
                'Delayed',
                'No Response',
                'Other'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'comment_type_enum') THEN
            CREATE TYPE procurement.comment_type_enum AS ENUM
            (
                'sku',
                'po',
                'tpw_sku'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'day_of_week_enum') THEN
            CREATE TYPE procurement.day_of_week_enum AS ENUM
            (
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
                'Sunday'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'receiving_type_enum') THEN
            CREATE TYPE procurement.receiving_type_enum AS ENUM
            (
                'HighJump',
                'Manual'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sync_status_enum') THEN
            CREATE TYPE procurement.sync_status_enum AS ENUM
            (
                'in_progress',
                'success',
                'failed'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'week_site_type_enum') THEN
            CREATE TYPE procurement.week_site_type_enum AS ENUM
            (
                'Primary',
                'Satellite'
            );
            END IF;
        END$$;
        """
    ),
    # Create tables
    step(
        """
        CREATE TABLE IF NOT EXISTS procurement."user" (
            `id serial PRIMARY KEY,
            first_name character varying(255),
            last_name character varying(255),
            email character varying(255) NOT NULL,
            picture text,
            last_login timestamp without time zone,
            forced_out boolean NOT NULL`
        );

        CREATE TABLE IF NOT EXISTS procurement.comment (
            id serial PRIMARY KEY,
            brand character varying(2) NOT NULL,
            site character varying(30),
            resource_id character varying(255),
            comment text,
            week integer,
            last_updated timestamp without time zone,
            updated_by character varying(255),
            resource_type procurement.comment_type_enum NOT NULL,
            domain character varying(20) NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.gsheet_meta (
            id serial PRIMARY KEY,
            name character varying(255) NOT NULL,
            doc_code character varying(255) NOT NULL,
            weekly boolean NOT NULL,
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            "order" integer NOT NULL,
            title_template character varying(255) NOT NULL,
            brand character varying(255),
            required boolean DEFAULT true,
            project character varying(30),
            UNIQUE (doc_code, brand, project)
        );

        CREATE TABLE IF NOT EXISTS procurement.gsheet_admin (
            id serial PRIMARY KEY,
            meta_id integer NOT NULL,
            scm_week character varying(255),
            gsheet_id character varying(255) NOT NULL,
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            valid boolean NOT NULL,
            error_message text,
            user_id integer,
            CONSTRAINT gsheet_admin_meta_id_fkey FOREIGN KEY (meta_id) REFERENCES procurement.gsheet_meta(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.ordering_tool_gsheet (
            quarter character varying(8) NOT NULL PRIMARY KEY,
            gsheet_id character varying(255) NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.parallel_sync_log (
            id serial PRIMARY KEY,
            job_name character varying(255) NOT NULL,
            parent_job_ids character varying(255)[],
            sync_status character varying(11),
            warnings text,
            sync_time timestamp with time zone NOT NULL,
            global_parent_id bigint,
            CONSTRAINT parallel_sync_log_global_parent_id_fkey FOREIGN KEY (global_parent_id) REFERENCES
            procurement.parallel_sync_log(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.permission (
            id serial PRIMARY KEY,
            name character varying(255),
            UNIQUE (name)
        );

        CREATE TABLE IF NOT EXISTS procurement.role (
            id serial PRIMARY KEY,
            full_name character varying(255) NOT NULL,
            short_name character varying(255) NOT NULL,
            priority integer DEFAULT 100 NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.permission_role (
            permission_id integer NOT NULL,
            role_id integer NOT NULL,
            PRIMARY KEY (permission_id, role_id),
            CONSTRAINT permission_role_permission_id_fkey FOREIGN KEY (permission_id)
            REFERENCES procurement.permission(id),
            CONSTRAINT permission_role_role_id_fkey FOREIGN KEY (role_id) REFERENCES procurement.role(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.scrubbing_comment (
            id serial PRIMARY KEY,
            dc character varying(255) NOT NULL,
            week integer NOT NULL,
            po_number character varying(20) NOT NULL,
            sku_code character varying(20) NOT NULL,
            comment text NOT NULL,
            last_updated timestamp without time zone NOT NULL,
            updated_by character varying(255) NOT NULL,
            UNIQUE (po_number, sku_code)
        );

        CREATE TABLE IF NOT EXISTS procurement.sync_log (
            id serial PRIMARY KEY,
            "time" timestamp with time zone NOT NULL,
            job_name character varying,
            sync_status procurement.sync_status_enum NOT NULL,
            error_message text
        );

        CREATE TABLE IF NOT EXISTS procurement.user_role (
            id serial PRIMARY KEY,
            user_id integer,
            role_id integer,
            CONSTRAINT user_role_role_id_fkey FOREIGN KEY (role_id) REFERENCES procurement.role(id),
            CONSTRAINT user_role_user_id_fkey FOREIGN KEY (user_id) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.user_view_state (
            user_id integer NOT NULL,
            resource character varying(255) NOT NULL,
            value text,
            id serial PRIMARY KEY,
            name character varying(255) NOT NULL,
            last_usage timestamp without time zone DEFAULT now() NOT NULL,
            CONSTRAINT user_view_state_user_id_fkey FOREIGN KEY (user_id) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS forecast.forecast_3pl (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            prod_type character varying(255) NOT NULL,
            recipe_num character varying(8) NOT NULL,
            kit_size integer NOT NULL,
            volume_by_day integer[],
            PRIMARY KEY(week, site, prod_type, recipe_num, kit_size)
        );

        CREATE TABLE IF NOT EXISTS forecast.oscar (
            dc character varying(2) NOT NULL,
            scm_week character varying(8) NOT NULL,
            sku_id integer NOT NULL,
            forecast integer NOT NULL,
            brand character varying(100) NOT NULL,
            PRIMARY KEY (dc, scm_week, sku_id, brand)
        );

        CREATE TABLE IF NOT EXISTS forecast.packaging_3pl (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            weight_by_day double precision[],
            PRIMARY KEY (week, site)
        );

        CREATE TABLE IF NOT EXISTS forecast.packaging_forecast (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            brand character varying(3) NOT NULL,
            sku_code character varying(255) NOT NULL,
            sku_name character varying(255),
            forecast integer,
            PRIMARY KEY (week, brand, site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS forecast.planing_3pl (
            site character varying(5) NOT NULL,
            prod_type character varying(255) NOT NULL,
            weights_by_day jsonb,
            PRIMARY KEY (site, prod_type)
        );

        CREATE TABLE IF NOT EXISTS highjump.v_highjump_discard (
            sku_code character varying(255),
            wh_id character varying(255),
            discard_date timestamp without time zone,
            quantity integer
        );

        CREATE TABLE IF NOT EXISTS highjump.v_highjump_wip (
            sku_code character varying(255),
            wh_id character varying(255),
            brand character varying(255),
            quantity integer
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_autobagger (
            sku_code character varying(255),
            pallet_quantity real,
            po_number character varying(255),
            scm_week character varying(255),
            wh_id character varying(255),
            pallet_create_date timestamp without time zone,
            week character varying(255) NOT NULL,
            brand character varying(255)
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_inv (
            id serial PRIMARY KEY,
            wh_id character varying(10),
            quantity integer,
            sku_code character varying(30),
            brand character varying(2),
            updated_ts timestamp without time zone,
            date date NOT NULL
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_lps (
            sku_code character varying(255),
            pallet_quantity real,
            po_number character varying(255),
            scm_week character varying(255),
            wh_id character varying(255),
            week character varying(255) NOT NULL,
            brand character varying(255)
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_receipts (
            wh_id character varying(255),
            supplier_name character varying(255),
            po_number character varying(255),
            cases_received real,
            quantity_received real,
            sku_code character varying(255),
            sku_name character varying(255),
            scm_week_raw character varying(255),
            status character varying(255),
            receipt_time_est timestamp without time zone,
            week character varying(255) NOT NULL,
            brand character varying(255)
        );

        CREATE TABLE IF NOT EXISTS inventory.autobagger_skus (
            sku_code character varying(255) NOT NULL,
            brand character varying(100) NOT NULL,
            sites character varying[] NOT NULL,
            PRIMARY KEY (sku_code, brand)
        );

        CREATE TABLE IF NOT EXISTS inventory.bid_tool_input (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            supplier_name character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            price real NOT NULL,
            brand character varying(5) DEFAULT 'HF'::character varying NOT NULL,
            PRIMARY KEY (week, brand, site, sku_code, supplier_name)
        );

        CREATE TABLE IF NOT EXISTS inventory.bidding_tool_comparison (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            sku_code character varying(255) NOT NULL,
            price real NOT NULL,
            brand character varying(5) DEFAULT 'HF'::character varying NOT NULL,
            PRIMARY KEY (week, brand, site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.brand (
            id character varying(2) NOT NULL PRIMARY KEY,
            name character varying(255)
        );

        CREATE TABLE IF NOT EXISTS inventory.buyer_sku (
            user_id integer NOT NULL,
            sku_code character varying(20) NOT NULL,
            site character varying(2) NOT NULL,
            brand character varying(2) NOT NULL,
            PRIMARY KEY (sku_code, site, brand),
            CONSTRAINT buyer_sku_user_id_fkey FOREIGN KEY (user_id) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.commodity_group (
            id serial PRIMARY KEY,
            group_name character varying(255) NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.compiled (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            brand character varying(3) NOT NULL,
            production_type text NOT NULL,
            weights double precision[],
            PRIMARY KEY (week, brand, site, production_type)
        );

        CREATE TABLE IF NOT EXISTS inventory.discard (
            id serial PRIMARY KEY,
            "user" character varying(255),
            dc character varying(255) NOT NULL,
            sku character varying(255) NOT NULL,
            sku_name character varying(255) NOT NULL,
            quantity integer NOT NULL,
            quality_instructions character varying(255),
            reason character varying(255),
            source character varying(255),
            week character varying(255),
            "timestamp" timestamp without time zone,
            discarded_datetime timestamp without time zone,
            brand character varying(100) NOT NULL,
            comment character varying(4000),
            deleted_by character varying(100),
            deleted_ts timestamp without time zone,
            updated_by character varying(100)
        );

        CREATE TABLE IF NOT EXISTS inventory.hj_override (
            "user" character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            brand character varying(255) NOT NULL,
            week character varying(255) NOT NULL,
            source character varying(255) NOT NULL,
            po_number character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            sku_name character varying(255) NOT NULL,
            qty integer NOT NULL,
            upd_tmst timestamp without time zone NOT NULL,
            cre_tmst timestamp without time zone NOT NULL,
            PRIMARY KEY (week, brand, dc, po_number, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients (
            sku_code character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            scm_week character varying(255) NOT NULL,
            date date NOT NULL,
            value integer NOT NULL,
            brand character varying(100) NOT NULL,
            PRIMARY KEY (sku_code, dc, scm_week, date, brand)
        );

        CREATE TABLE IF NOT EXISTS inventory.ingredient (
            sku_id integer NOT NULL,
            sku_code character varying(255) NOT NULL PRIMARY KEY,
            sku_name character varying(255) NOT NULL,
            pack_size_amount real,
            pack_size_unit character varying(255),
            brand character varying(255) NOT NULL,
            weight_amount real,
            weight_unit character varying(255),
            storage_location character varying(255),
            allergens character varying(255),
            is_imt boolean,
            is_receipt_bp_drop boolean DEFAULT false NOT NULL,
            is_type_media boolean DEFAULT false NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.purchasing_category (
            id serial PRIMARY KEY,
            name character varying(255) NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.ingredient_category (
            sku_code character varying(20) NOT NULL PRIMARY KEY,
            category_id integer,
            CONSTRAINT ingredient_category_category_id_fkey FOREIGN KEY (category_id)
            REFERENCES inventory.purchasing_category(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.ingredient_site_subcategory (
            sku_code character varying(20) NOT NULL,
            site character varying(2) NOT NULL,
            commodity_group_id integer,
            PRIMARY KEY (site, sku_code),
            CONSTRAINT ingredient_site_commodity_group_commodity_group_id_fkey FOREIGN KEY (commodity_group_id)
            REFERENCES inventory.commodity_group(id) ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS inventory.isp (
            id serial PRIMARY KEY,
            sku_code character varying(255) NOT NULL,
            dc_id character varying(2),
            cost numeric NOT NULL,
            week character varying(255) NOT NULL,
            brand character varying(100) NOT NULL,
            CONSTRAINT isp_sku_code_fkey FOREIGN KEY (sku_code) REFERENCES inventory.ingredient(sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.mealkit (
            id serial PRIMARY KEY,
            code character varying(255) NOT NULL,
            slot character varying(20) NOT NULL,
            meal_name character varying(255) NOT NULL,
            scm_week character varying(255) NOT NULL,
            brand character varying(225)
        );

        CREATE TABLE IF NOT EXISTS inventory.mealkit_ingredient (
            id serial PRIMARY KEY,
            mealkit_id integer NOT NULL,
            sku_code character varying(255) NOT NULL,
            picks_2p integer NOT NULL,
            picks_4p integer NOT NULL,
            dc_list character varying[] NOT NULL,
            is_in_kitbox boolean DEFAULT false NOT NULL,
            CONSTRAINT mealkit_ingredient_mealkit_id_fkey FOREIGN KEY (mealkit_id)
            REFERENCES inventory.mealkit(id) ON DELETE CASCADE,
            CONSTRAINT mealkit_ingredient_sku_code_fkey FOREIGN KEY (sku_code) REFERENCES inventory.ingredient(sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_inventory (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            brand character varying(3) NOT NULL,
            sku_code character varying(255) NOT NULL,
            sku_name character varying(255),
            units_needed_by_day integer[],
            units_on_hand_by_day integer[],
            PRIMARY KEY (week, brand, site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_isp (
            brand character(2) NOT NULL,
            site character varying(5) NOT NULL,
            supplier_name character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            price real NOT NULL,
            PRIMARY KEY (brand, site, sku_code, supplier_name)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_liner_inventory (
            week integer NOT NULL,
            site character varying(5) NOT NULL,
            brand character varying(3) NOT NULL,
            liner_group character varying(255) NOT NULL,
            units_needed_by_day integer[],
            units_on_hand_by_day integer[],
            PRIMARY KEY (week, site, brand, liner_group)
        );

        CREATE TABLE IF NOT EXISTS inventory.po_void (
            id serial PRIMARY KEY,
            "user" character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            week character varying(255) NOT NULL,
            brand character varying(255) NOT NULL,
            po_number character varying(255) NOT NULL,
            source character varying(255) NOT NULL,
            comment text,
            cre_tmst timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
            sku_code character varying(255),
            supplier_name character varying(255),
            deleted_by character varying(255),
            deleted_ts timestamp without time zone,
            last_updated timestamp without time zone,
            UNIQUE (week, brand, dc, po_number, sku_code, deleted_ts)
        );

        CREATE TABLE IF NOT EXISTS inventory.pull_put (
            id serial PRIMARY KEY,
            user_email character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            brand character varying(255) NOT NULL,
            week character varying(255) NOT NULL,
            source character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            qty integer NOT NULL,
            comment text,
            upd_tmst timestamp without time zone NOT NULL,
            cre_tmst timestamp without time zone NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.receiving (
            id serial PRIMARY KEY,
            supplier character varying(255) NOT NULL,
            po character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            arrival_timestamp timestamp without time zone NOT NULL,
            receive_timestamp timestamp without time zone NOT NULL,
            delivery_status character varying(255) NOT NULL,
            total_pallets_received integer,
            total_cases_received integer,
            case_count_one_total_units integer,
            case_count_two_total_units integer,
            case_count_three_total_units integer,
            receiver_comments character varying(255),
            ticket_number character varying(255),
            week character varying(255) NOT NULL,
            username character varying(255) NOT NULL,
            sku_ingredient character varying(255) DEFAULT ''::character varying NOT NULL,
            supplier_id character varying(255) DEFAULT ''::character varying NOT NULL,
            source character varying(255) DEFAULT 'APP'::character varying NOT NULL,
            brand character varying(255) NOT NULL,
            case_size_one integer,
            case_size_two integer,
            case_size_three integer
        );

        CREATE TABLE IF NOT EXISTS inventory.remake_tool (
            meal character varying(255) NOT NULL,
            picks_2p integer,
            picks_4p integer,
            week character varying(255) NOT NULL,
            dc character varying(255) NOT NULL,
            PRIMARY KEY (week, dc, meal)
        );

        CREATE TABLE IF NOT EXISTS inventory.scrubbing (
            po_number character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            date_received timestamp without time zone,
            order_size integer,
            case_size integer,
            cases_received integer,
            quantity_received integer,
            case_price numeric,
            quantity_ordered integer,
            submitted boolean DEFAULT false NOT NULL,
            last_updated timestamp without time zone,
            updated_by integer,
            week integer NOT NULL,
            PRIMARY KEY (po_number, sku_code),
            CONSTRAINT scrubbing_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.site (
            id character varying(3) NOT NULL PRIMARY KEY,
            sequence_number integer,
            name character varying(255),
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            user_id integer,
            site_properties jsonb DEFAULT '{"timezone": "UTC"}'::jsonb
        );

        CREATE TABLE IF NOT EXISTS inventory.supplier_sku (
            supplier_sku_uuid character varying(255) NOT NULL PRIMARY KEY,
            culinary_sku_uuid character varying(255) NOT NULL,
            last_updated timestamp without time zone NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.weekend_coverage_checklist (
            id serial PRIMARY KEY,
            week integer,
            brand character varying(2),
            site character varying(2),
            po_number character varying(15),
            sku_code character varying(20),
            production_day_affected procurement.day_of_week_enum,
            to_check text,
            contact_name_vendor_carrier character varying(255),
            email_phone character varying(255),
            back_up_vendor character varying(255),
            status procurement.checklist_status_enum,
            comment text,
            updated_by_id integer,
            last_updated timestamp without time zone,
            po_landing_day procurement.day_of_week_enum,
            CONSTRAINT weekend_coverage_checklist_updated_by_id_fkey FOREIGN KEY (updated_by_id)
            REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.weekly_config (
            id serial PRIMARY KEY,
            site_id character varying(2),
            brand_id character varying(2),
            week integer,
            type procurement.week_site_type_enum,
            enabled boolean,
            properties jsonb,
            user_id integer,
            CONSTRAINT u_week_site_brand UNIQUE (week, site_id, brand_id),
            CONSTRAINT weekly_config_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES inventory.brand(id),
            CONSTRAINT weekly_config_site_id_fkey FOREIGN KEY (site_id) REFERENCES inventory.site(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.yield (
            id serial PRIMARY KEY,
            "user" character varying(255),
            yielding_tmst timestamp without time zone,
            create_tmst timestamp without time zone,
            dc character varying(255),
            week character varying(255),
            activity_type character varying(255),
            sku_code character varying(255),
            po character varying(255),
            lot_batch_code character varying(255),
            expiration_date character varying(255),
            brand_farm character varying(255),
            comments character varying(255),
            source character varying(255),
            current_good_quality_units integer,
            current_bad_quality_units integer,
            current_total_no_of_units_yielded integer,
            current_average_good_quality_units double precision,
            current_average_bad_quality_units double precision,
            quality_instructions character varying(255) NOT NULL,
            total_quantity_ordered integer,
            total_quantity_received integer,
            total_quantity_yielded integer,
            total_bad_quality_units_reported integer,
            total_quantity_yielded_to_received double precision,
            total_yield_discard_rate double precision,
            yielding_rate_threshold double precision,
            implied_total_quantity_yielded integer,
            implied_total_bad_quality_units_reported integer,
            yield_discard_rate double precision,
            brand character varying(225)
        );

        CREATE TABLE IF NOT EXISTS ordering.culinary_sku (
            sku_uuid character varying(255) NOT NULL PRIMARY KEY,
            sku_id integer GENERATED ALWAYS AS
            (((regexp_split_to_array((sku_code)::text, '-'::text))[3])::integer) STORED,
            sku_code character varying(255),
            sku_name character varying(255),
            last_updated timestamp without time zone,
            status character varying(255),
            storage_location character varying(255)
        );

        CREATE TABLE IF NOT EXISTS ordering.ordering_tool_dashboard (
            brand character varying(2) NOT NULL,
            week character varying(8) NOT NULL,
            gsheet_id character varying(255) NOT NULL,
            PRIMARY KEY (brand, week)
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order (
            po_uuid character varying(255) NOT NULL PRIMARY KEY,
            po_number character varying(255) NOT NULL,
            order_number character varying(255) GENERATED ALWAYS AS (substr((po_number)::text, 1, 12)) STORED,
            dc character varying(255) NOT NULL,
            brand character varying(255) NOT NULL,
            supplier_code character varying(255) NOT NULL,
            supplier character varying(255) NOT NULL,
            ot_po_status character varying(255) NOT NULL,
            is_sent boolean NOT NULL,
            week character varying(255) NOT NULL,
            order_date timestamp without time zone NOT NULL,
            delivery_time_start timestamp without time zone NOT NULL,
            emergency_reason character varying(255),
            ordered_by character varying(255),
            ot_last_updated timestamp without time zone,
            deleted boolean DEFAULT false NOT NULL,
            internal_last_updated timestamp without time zone DEFAULT now(),
            shipping_method character varying(255)
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order_shipment (
            po_number character varying(255) NOT NULL PRIMARY KEY,
            load_number character varying(255),
            pallet_count integer,
            carrier_name character varying(255),
            origin_location_reference character varying(255),
            region_code character varying(255),
            postal_code character varying(255),
            administrative_area character varying(255),
            locality character varying(255),
            address_lines character varying[],
            organization character varying(255),
            pickup_time timestamp without time zone,
            appointment_time timestamp without time zone,
            estimated_arrival_time timestamp without time zone,
            appointment_state integer,
            batch_sequence integer,
            execution_event character varying(255)
        );

        CREATE TABLE IF NOT EXISTS ordering.sku (
            sku_uuid character varying(255) NOT NULL PRIMARY KEY,
            sku_code character varying(20) NOT NULL,
            sku_name character varying(255) NOT NULL,
            sku_id integer GENERATED ALWAYS
            AS (((regexp_split_to_array((sku_code)::text, '-'::text))[3])::integer) STORED
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order_sku (
            po_uuid character varying(255) NOT NULL,
            sku_uuid character varying(255) NOT NULL,
            order_size integer NOT NULL,
            order_unit character varying(255) NOT NULL,
            unit_price real NOT NULL,
            case_size integer NOT NULL,
            case_unit character varying(255) NOT NULL,
            quantity integer NOT NULL,
            buffer integer,
            total_price real NOT NULL,
            id serial PRIMARY KEY,
            CONSTRAINT purchase_order_sku_po_uuid_fkey FOREIGN KEY (po_uuid)
            REFERENCES ordering.purchase_order(po_uuid) ON DELETE CASCADE,
            CONSTRAINT purchase_order_sku_sku_uuid_fkey FOREIGN KEY (sku_uuid) REFERENCES ordering.sku(sku_uuid)
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order_acknowledgement (
            po_uuid character varying(255) PRIMARY KEY,
            po_number character varying(255),
            order_number character varying(255),
            create_time timestamp without time zone,
            status INTEGER
        );

        CREATE TABLE IF NOT EXISTS pimt.exceptions_metrics (
            date date NOT NULL PRIMARY KEY,
            stats jsonb
        );

        CREATE TABLE IF NOT EXISTS pimt.goods_receipt_notification (
            receipt_id character varying(255) NOT NULL PRIMARY KEY,
            customer_id character varying(255),
            supplier_id integer,
            receipt_creation_date timestamp without time zone,
            receipt_receive_date timestamp without time zone,
            bob_code character varying(255),
            receipt_status integer
        );

        CREATE TABLE IF NOT EXISTS pimt.grn_line_item (
            receipt_id character varying(255) NOT NULL,
            receipt_line_id character varying(255) NOT NULL,
            sku_id integer NOT NULL,
            description character varying(255),
            receive_date timestamp without time zone,
            case_qty integer,
            order_number character varying(255),
            po_index character varying(255),
            receipt_line_status integer,
            lot_code character varying(255),
            pallet_count character varying(255),
            expiration_date timestamp without time zone,
            sku_code character varying(20) NOT NULL,
            PRIMARY KEY (receipt_id, receipt_line_id),
            UNIQUE (order_number, sku_id, lot_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory (
            id serial PRIMARY KEY,
            sku_id integer NOT NULL,
            po_number character varying(255) NOT NULL,
            qty integer,
            lot character varying(255),
            partner character varying(255) NOT NULL,
            order_number character varying(255) GENERATED ALWAYS AS (substr((po_number)::text, 1, 12)) STORED,
            sku_code character varying(255),
            qty_available integer,
            expiration_date timestamp without time zone
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory_notification (
            po_number character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            customer_id character varying(255),
            supplier_code character varying(255),
            bob_code character varying(255),
            quantity integer,
            expiration_date timestamp without time zone,
            case_size integer,
            pallet_count integer,
            lot_code character varying(255) NOT NULL,
            last_update_time timestamp without time zone,
            state integer,
            grn_id character varying(255),
            order_number character varying(255),
            sku_id integer,
            PRIMARY KEY (po_number, sku_code, lot_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory_update (
            id serial PRIMARY KEY,
            update_date timestamp without time zone NOT NULL
        );

        CREATE TABLE IF NOT EXISTS pimt.master_replenishment (
            site character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            category character varying(255),
            status character varying(255),
            replenishment_type character varying(255),
            shelf_life integer,
            max_supplier_lead_time integer,
            unit_flag integer,
            future_week_projections integer,
            PRIMARY KEY (site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.packaging_info (
            supplier_sku_uuid character varying(255) NOT NULL PRIMARY KEY,
            cases_per_pallet integer,
            units_per_pallet integer
        );

        CREATE TABLE IF NOT EXISTS pimt.partner (
            code character varying(255) NOT NULL PRIMARY KEY,
            partner_name character varying(255) NOT NULL,
            ot_dc character varying[] NOT NULL,
            ot_suppliers character varying[] NOT NULL,
            code_order integer NOT NULL,
            e_2_open_grn boolean DEFAULT false NOT NULL,
            region character varying(255),
            regional_dcs character varying(255)[],
            e_2_open_inventory boolean DEFAULT false NOT NULL,
            bob_code character varying(255),
            UNIQUE (bob_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.po_dummy_record (
            id serial PRIMARY KEY,
            dc character varying(255) NOT NULL,
            po_number character varying(255),
            supplier_code integer,
            supplier character varying(255) NOT NULL,
            sku_name character varying(255) NOT NULL,
            sku_code character varying(255) NOT NULL,
            order_size integer,
            case_size integer,
            total_price real,
            quantity integer,
            week character varying(255),
            delivery_time_start timestamp without time zone,
            estimated_palets integer,
            storage_asigment character varying(255),
            unit_price real,
            order_number character varying(255) GENERATED ALWAYS AS (substr((po_number)::text, 1, 12)) STORED,
            sku_id integer GENERATED ALWAYS
            AS (((regexp_split_to_array((sku_code)::text, '-'::text))[3])::integer) STORED
        );

        CREATE TABLE IF NOT EXISTS pimt.replenishment (
            sku_code character varying(255) NOT NULL PRIMARY KEY,
            replenishment_type character varying(255)
        );

        CREATE TABLE IF NOT EXISTS pimt.unit_flags (
            sku_id integer NOT NULL,
            region character varying(2) NOT NULL,
            unit_flag integer NOT NULL,
            PRIMARY KEY (sku_id, region)
        );
        """
    ),
    # Create Indexes
    step(
        """
        CREATE INDEX IF NOT EXISTS iforecast_oscar_dc ON forecast.oscar USING btree (dc);

        CREATE INDEX IF NOT EXISTS iforecast_oscar_scm_week ON forecast.oscar USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS iforecast_oscar_sku_id ON forecast.oscar USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS hjautobaggerexport_week ON highjump.v_procurement_export_autobagger
        USING btree (week);

        CREATE INDEX IF NOT EXISTS hjautobaggerexport_wh_id ON highjump.v_procurement_export_autobagger
        USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS hjpalletsnapshot_week ON highjump.v_procurement_export_lps USING btree (week);

        CREATE INDEX IF NOT EXISTS hjpalletsnapshot_wh_id ON highjump.v_procurement_export_lps USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS hjreceipts_scm_week_raw ON highjump.v_procurement_export_receipts
        USING btree (scm_week_raw);

        CREATE INDEX IF NOT EXISTS hjreceipts_week ON highjump.v_procurement_export_receipts USING btree (week);

        CREATE INDEX IF NOT EXISTS hjreceipts_wh_id ON highjump.v_procurement_export_receipts USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS iv_hj_discard_discard_date_wh_id ON highjump.v_highjump_discard
        USING btree (discard_date, wh_id);

        CREATE INDEX IF NOT EXISTS iv_hj_wip_wh_id_brand_sku_code ON highjump.v_highjump_wip
        USING btree (wh_id, brand, sku_code);

        CREATE INDEX IF NOT EXISTS iv_procurement_export_receipts_receipt_time_est
        ON highjump.v_procurement_export_receipts USING btree (receipt_time_est, wh_id, brand);

        CREATE INDEX IF NOT EXISTS v_procurement_export_autobagger_brand ON highjump.v_procurement_export_autobagger
        USING btree (brand);

        CREATE INDEX IF NOT EXISTS v_procurement_export_inv_brand_index ON highjump.v_procurement_export_inv
        USING btree (brand);

        CREATE INDEX IF NOT EXISTS v_procurement_export_inv_day_index ON highjump.v_procurement_export_inv
        USING btree (updated_ts);

        CREATE INDEX IF NOT EXISTS v_procurement_export_inv_wh_index ON highjump.v_procurement_export_inv
        USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS v_procurement_export_lps_brand ON highjump.v_procurement_export_lps
        USING btree (brand);

        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_brand ON highjump.v_procurement_export_receipts
        USING btree (brand);;

        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_po_number ON highjump.v_procurement_export_receipts
        USING btree (po_number);

        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_sku_code ON highjump.v_procurement_export_receipts
        USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS buyer_sku_brand_site_index ON inventory.buyer_sku USING btree (brand, site);

        CREATE UNIQUE INDEX IF NOT EXISTS commodity_group_group_name_uindex ON inventory.commodity_group
        USING btree (group_name);

        CREATE INDEX IF NOT EXISTS discard_brand ON inventory.discard USING btree (brand);

        CREATE INDEX IF NOT EXISTS hj_override_po_number_idx ON inventory.hj_override USING btree (po_number);

        CREATE INDEX IF NOT EXISTS hybrid_needs_brand ON inventory.hybrid_needs_ingredients USING btree (brand);

        CREATE INDEX IF NOT EXISTS iinventory_hj_override_sku_code ON inventory.hj_override USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_hybrid_needs_date ON inventory.hybrid_needs_ingredients
        USING btree (date);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_is_receipt_bp_drop ON inventory.ingredient
        USING btree (is_receipt_bp_drop) WHERE (is_receipt_bp_drop = false);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_is_type_media ON inventory.ingredient
        USING btree (is_type_media) WHERE (is_type_media = false);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_site_commodity_group_sku_id
        ON inventory.ingredient_site_subcategory USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_po_void_deleted_ts ON inventory.po_void USING btree (deleted_ts);

        CREATE INDEX IF NOT EXISTS iinventory_po_void_sku_code ON inventory.po_void USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_receiving_receive_timestamp ON inventory.receiving
        USING btree (receive_timestamp);

        CREATE INDEX IF NOT EXISTS iinventory_scrubbing_last_updated ON inventory.scrubbing USING btree (last_updated);

        CREATE INDEX IF NOT EXISTS iinventory_scrubbing_week ON inventory.scrubbing USING btree (week);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_brand ON inventory.weekend_coverage_checklist
        USING btree (brand);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_po_sku ON inventory.weekend_coverage_checklist
        USING btree (po_number, sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_site ON inventory.weekend_coverage_checklist
        USING btree (site);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_week ON inventory.weekend_coverage_checklist
        USING hash (week);

        CREATE INDEX IF NOT EXISTS ingredient_is_imt ON inventory.ingredient USING btree (is_imt);

        CREATE INDEX IF NOT EXISTS ingredient_sku_name ON inventory.ingredient USING btree (sku_name);

        CREATE INDEX IF NOT EXISTS inv_week_config_week_brand_site ON inventory.weekly_config
        USING btree (week, brand_id, site_id);

        CREATE INDEX IF NOT EXISTS inventorydiscard_dc ON inventory.discard USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorydiscard_source ON inventory.discard USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorydiscard_week ON inventory.discard USING btree (week);

        CREATE INDEX IF NOT EXISTS inventoryhj_override_brand ON inventory.hj_override USING btree (brand);

        CREATE INDEX IF NOT EXISTS inventoryhj_override_cre_tmst ON inventory.hj_override USING btree (cre_tmst);

        CREATE INDEX IF NOT EXISTS inventoryhj_override_dc ON inventory.hj_override USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventoryhj_override_source ON inventory.hj_override USING btree (source);

        CREATE INDEX IF NOT EXISTS inventoryhj_override_week ON inventory.hj_override USING btree (week);

        CREATE INDEX IF NOT EXISTS inventoryhybrid_needs_ingredients_dc ON inventory.hybrid_needs_ingredients
        USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventoryhybrid_needs_ingredients_scm_week ON inventory.hybrid_needs_ingredients
        USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS inventoryingredient_sku_id ON inventory.ingredient USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS inventorypo_void_dc ON inventory.po_void USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypo_void_source ON inventory.po_void USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorypo_void_week ON inventory.po_void USING btree (week);

        CREATE INDEX IF NOT EXISTS inventorypull_put_brand ON inventory.pull_put USING btree (brand);

        CREATE INDEX IF NOT EXISTS inventorypull_put_dc ON inventory.pull_put USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypull_put_sku_code ON inventory.pull_put USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS inventorypull_put_source ON inventory.pull_put USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorypull_put_state_code ON inventory.pull_put USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypull_put_week ON inventory.pull_put USING btree (week);

        CREATE INDEX IF NOT EXISTS inventorypullput_user_email ON inventory.pull_put USING btree (user_email);

        CREATE UNIQUE INDEX IF NOT EXISTS inventorypurchasing_category_name ON inventory.purchasing_category
        USING btree (name);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_dc ON inventory.receiving USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_sku_code ON inventory.receiving USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_source ON inventory.receiving USING btree (source);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_week ON inventory.receiving USING btree (week);

        CREATE INDEX IF NOT EXISTS inventoryyield_dc ON inventory.yield USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventoryyield_source ON inventory.yield USING btree (source);

        CREATE INDEX IF NOT EXISTS inventoryyield_week ON inventory.yield USING btree (week);

        CREATE INDEX IF NOT EXISTS isp_brand ON inventory.isp USING btree (brand);

        CREATE INDEX IF NOT EXISTS isp_sku_code ON inventory.isp USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS isp_week_brand_dc_id_sku_code_uindex ON inventory.isp
        USING btree (week DESC, brand, dc_id, sku_code);

        CREATE INDEX IF NOT EXISTS mealkit_brand ON inventory.mealkit USING btree (brand);

        CREATE INDEX IF NOT EXISTS mealkit_scm_week ON inventory.mealkit USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS mealkitingredient_dc_list ON inventory.mealkit_ingredient USING gin (dc_list);

        CREATE INDEX IF NOT EXISTS mealkitingredient_mealkit_id ON inventory.mealkit_ingredient
        USING btree (mealkit_id);

        CREATE INDEX IF NOT EXISTS mealkitingredient_sku_code ON inventory.mealkit_ingredient USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS po_void_brand ON inventory.po_void USING btree (brand);

        CREATE INDEX IF NOT EXISTS po_void_po_number_idx ON inventory.po_void USING btree (po_number);

        CREATE INDEX IF NOT EXISTS receiving_brand ON inventory.receiving USING btree (brand);

        CREATE INDEX IF NOT EXISTS scrubbing_date_received ON inventory.scrubbing USING btree (date_received);

        CREATE INDEX IF NOT EXISTS scrubbing_submitted ON inventory.scrubbing USING btree (submitted);

        CREATE INDEX IF NOT EXISTS week_inventory_isp ON inventory.isp USING btree (week);

        CREATE INDEX IF NOT EXISTS yield_brand ON inventory.yield USING btree (brand);

        CREATE INDEX IF NOT EXISTS culinary_sku_status ON ordering.culinary_sku USING btree (status);

        CREATE INDEX IF NOT EXISTS i_culinary_sku_code ON ordering.culinary_sku USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_culinary_sku_id ON ordering.culinary_sku USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_brand ON ordering.purchase_order USING btree (brand);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_dc ON ordering.purchase_order USING btree (dc);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_deleted ON ordering.purchase_order USING btree (deleted);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_delivery_time_start ON ordering.purchase_order
        USING btree (delivery_time_start);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_emergency_reason ON ordering.purchase_order
        USING btree (emergency_reason);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_order_number ON ordering.purchase_order
        USING btree (order_number);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_ot_last_updated ON ordering.purchase_order
        USING btree (internal_last_updated);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_number ON ordering.purchase_order
        USING btree (po_number);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_number_comp ON ordering.purchase_order
        USING btree (substr((po_number)::text, 1, 14));

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_uuid ON ordering.purchase_order_sku
        USING btree (po_uuid);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_sku_uuid ON ordering.purchase_order_sku
        USING btree (sku_uuid);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_supplier ON ordering.purchase_order USING btree (supplier);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_week ON ordering.purchase_order USING btree (week);

        CREATE INDEX IF NOT EXISTS iordering_sku_sku_code ON ordering.sku USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS grn_sku_code ON pimt.grn_line_item USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iordering_sku_sku_code ON pimt.inventory USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_good_receipt_notification ON pimt.goods_receipt_notification
        USING btree (receipt_status);

        CREATE INDEX IF NOT EXISTS ipimt_grn_line_item_po_number ON pimt.grn_line_item USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_grn_line_item_sku_id ON pimt.grn_line_item USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_notification_bob_code ON pimt.inventory_notification
        USING btree (bob_code);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_notification_sku_id ON pimt.inventory_notification
        USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_order_number ON pimt.inventory USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_partner ON pimt.inventory USING btree (partner);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_po_data_id ON pimt.inventory
        USING btree (((substr((po_number)::text, 1, 12) || (sku_id)::text)));

        CREATE INDEX IF NOT EXISTS ipimt_inventory_po_number ON pimt.inventory USING btree (po_number);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_sku_id ON pimt.inventory USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS ipimt_partner_code ON pimt.partner USING btree (code);

        CREATE INDEX IF NOT EXISTS ipimt_partner_dc ON pimt.partner USING gin (ot_dc);

        CREATE INDEX IF NOT EXISTS ipimt_partner_supplier ON pimt.partner USING gin (ot_suppliers);

        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_dc ON pimt.po_dummy_record USING btree (dc);

        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_order_number ON pimt.po_dummy_record
        USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_po_data_id ON pimt.po_dummy_record
        USING btree (((substr((po_number)::text, 1, 12) || substr((sku_code)::text, 8, 5))));

        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_sku_code ON pimt.po_dummy_record USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_supplier_name ON pimt.po_dummy_record USING btree (supplier);

        CREATE UNIQUE INDEX IF NOT EXISTS comment_uniq ON procurement.comment
        USING btree (domain, resource_type, week, site, resource_id, brand);

        CREATE INDEX IF NOT EXISTS global_parent_id_parallel_sync_log ON procurement.parallel_sync_log
        USING btree (global_parent_id);

        CREATE INDEX IF NOT EXISTS gsheet_meta_brand_index ON procurement.gsheet_meta USING btree (brand);

        CREATE INDEX IF NOT EXISTS gsheet_meta_doc_code_index ON procurement.gsheet_meta USING btree (doc_code);

        CREATE INDEX IF NOT EXISTS gsheet_meta_required_index ON procurement.gsheet_meta USING btree (required);

        CREATE INDEX IF NOT EXISTS gsheetadmin_meta_id ON procurement.gsheet_admin USING btree (meta_id);

        CREATE UNIQUE INDEX IF NOT EXISTS gsheetadmin_meta_id_scm_week ON procurement.gsheet_admin
        USING btree (meta_id, scm_week);

        CREATE INDEX IF NOT EXISTS gsheetadmin_scm_week ON procurement.gsheet_admin USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS iglobal_parent_id_parallel_sync_log ON procurement.parallel_sync_log
        USING btree (global_parent_id);

        CREATE INDEX IF NOT EXISTS iprocurement_scrubbing_comment_dc ON procurement.scrubbing_comment USING btree (dc);

        CREATE INDEX IF NOT EXISTS iprocurement_scrubbing_comment_week ON procurement.scrubbing_comment
        USING btree (week);

        CREATE INDEX IF NOT EXISTS job_name_parallel_sync_log ON procurement.parallel_sync_log USING btree (job_name);

        CREATE INDEX IF NOT EXISTS role_full_name ON procurement.role USING btree (full_name);

        CREATE INDEX IF NOT EXISTS role_index ON procurement.permission_role USING btree (role_id);

        CREATE INDEX IF NOT EXISTS role_short_name ON procurement.role USING btree (short_name);

        CREATE INDEX IF NOT EXISTS sync_log_job_name_index ON procurement.sync_log USING btree (job_name);

        CREATE UNIQUE INDEX IF NOT EXISTS user_email ON procurement."user" USING btree (email);

        CREATE INDEX IF NOT EXISTS user_role_user_id ON procurement.user_role USING btree (user_id);

        CREATE INDEX IF NOT EXISTS user_view_state_user_id ON procurement.user_view_state USING btree (user_id);

        CREATE INDEX IF NOT EXISTS user_view_state_user_id_resource ON procurement.user_view_state
        USING btree (user_id, resource);
        """
    ),
    # Insert Gsheet Meta
    step(
        """
        INSERT INTO procurement.gsheet_meta (id, name, doc_code, weekly, "order", title_template, brand,
        required, project)
        VALUES
        (4, 'Production Kit Guide', 'pkg', true, 4, 'Production Kit Guide - {scm_week}', 'HF', true, 'imt'),
        (17, 'Production Kit Guide', 'pkg', true, 1050,
         'EveryPlate Production Kit Guide - {scm_week}', 'EP', true, 'imt'),
        (2, 'Hybrid Needs', 'hybrid_needs', true, 2, 'Hybrid Needs - Week {week_number}', 'HF', false, 'imt'),
        (16, 'Hybrid Needs', 'hybrid_needs', true, 1030, 'EP Hybrid Needs - Week {week_number}', 'EP', false, 'imt'),
        (103, 'PIMT - ALL 3PWs - Grid Dynamics Data Feed', 'pimt', false, 2000,
         'PIMT - ALL 3PWs - Grid Dynamics Data Feed', null, true, 'pimt'),
        (102, 'Total 3PL', 'total_3pl', false, 2003, 'Total 3PL POs', null, true, 'pimt'),
        (3, 'IMT - Data Dumps', 'imt_data_dumps', true, 3, 'Week Data Dumps - Test', 'HF', true, 'imt'),
        (18, 'IMT - Data Dumps', 'imt_data_dumps', true, 1040, 'Week Data Dumps - Test', 'EP', true, 'imt'),
        (22, '3PL Hybrid Needs', 'hybrid_needs_3pl', true, 22,
         '3PL Hybrid Needs - Week {week_number}', 'HF', false, '3pl'),
        (23, '3PL IMT - Data Dumps', 'imt_data_dumps_3pl', true, 21,
         '3PL Data Dump - W{week_number}', 'HF', false, '3pl'),
        (106, 'Packaging ISP', 'packaging_isp', false, 13, 'Packaging Expected Cost Per Unit ', 'HF', true, 'imt'),
        (107, 'Bidding Tool', 'bidding_tool', true, 23, '{year} Bidding Tool - W{week_number}', 'HF', false, 'imt'),
        (108, 'EP Bidding Tool', 'bidding_tool', true, 1070, 'EP Bidding Tool - W{week_number}', 'EP', false, 'imt'),
        (109, 'Master Pallet', 'master_pallet', false, 13, 'Master Pallet Conversions ', null, true, 'imt'),
        (111, 'Mock Plan Calculation', 'mock_plan_calculation', true, 30,
         'Mock Plan Calculation - {year}-W{week_number}', 'HF', false, 'imt'),
        (112, '3PL Recipe Data by Ship Day', 'recipe_data_by_ship_day_3pl', false,
         15, '3PL Recipe Data by Ship Day', 'HF', false, '3pl'),
        (113, 'PO Status Export', 'po_status_export', false, 17, 'PO Status Export', 'HF', false, 'imt'),
        (1, 'Gil', 'gil_two', false, 1, 'Gil 2', 'HF', true, 'imt'),
        (19, 'Gil', 'gil_two', false, 1010, 'Gil 2', 'EP', true, 'imt'),
        (12, 'Total Procurement Purchasing', 'commodity_group', false, 12,
         'Total Procurement Purchasing', 'HF', true, 'imt'),
        (21, 'Total Procurement Purchasing', 'commodity_group', false, 1020,
         'Total Procurement Purchasing', 'EP', true, 'imt'),
        (114, 'In-week Packaging Inventory Tool', 'in_week_pit', true, 31,
         'Packaging Inventory Tool - W{week_number}', 'HF', false, 'imt'),
        (115, 'Packaging Forecast', 'packaging_forecast', false, 18, '{year} Packaging Forecast', 'HF', false, 'imt'),
        (116, 'PIMT Export', 'pimt-export', false, 18, 'pIMT Export', null, false, 'pimt'),
        (117, 'Daily Exceptions Export', 'imt-daily-export', false, 32,
         'IMT App - Daily Exceptions Export', null, true, 'imt'),
        (118, 'PIMT Master Replenishment', 'pimt_master_replenishment', false, 23,
         'MASTER Ingredient Replenishment Data', null, false, 'pimt')
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.gsheet_meta_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.gsheet_meta), false);
        """
    ),
    # Insert Permissions
    step(
        """
        INSERT INTO procurement.permission (id, name)
        VALUES
        (1, 'general:r'),
        (2, 'imt-gsheet-v1:r'),
        (3, 'imt-gsheet-v1:w'),
        (4, 'imt-region-v1:r'),
        (5, 'imt-region-v1:w'),
        (6, 'imt-remake-tool-v1:r'),
        (7, 'imt-remake-tool-v1:w'),
        (8, 'imt-ing-depl-v1:r'),
        (9, 'imt-dashboard-ing-v1:r'),
        (10, 'imt-po-status-v1:r'),
        (11, 'imt-cost-check-v1:r'),
        (12, 'imt-prod-need-ing-v1:r'),
        (13, 'imt-pkg-v1:r'),
        (14, 'imt-buyer-v1:r'),
        (15, 'imt-pull-put-v1:r'),
        (16, 'imt-pull-put-v1:w'),
        (17, 'imt-po-void-v1:r'),
        (18, 'imt-po-void-v1:w'),
        (19, 'imt-hj-override-v1:r'),
        (20, 'imt-hj-override-v1:w'),
        (21, 'imt-discard-v1:r'),
        (22, 'imt-discard-v1:w'),
        (23, 'imt-scrubbing-v1:r'),
        (24, 'imt-scrubbing-v1:w'),
        (25, 'imt-wcc-v1:r'),
        (26, 'imt-wcc-v1:w'),
        (27, 'imt-comment-log-v2:r'),
        (28, 'pimt-ait-v1:r'),
        (29, 'pimt-ing-depl-v1:r'),
        (30, 'pimt-buying-tool-v1:r'),
        (31, 'pimt-ops-dash-v1:r'),
        (32, 'pimt-partners-v1:r'),
        (33, 'pimt-partners-v1:w'),
        (34, 'imt-ing-depl-v2:r'),
        (37, 'ot-gsheet-v1:w'),
        (38, 'ot-gsheet-v2:w'),
        (39, 'imt-cache-v1:r'),
        (40, 'imt-cache-v1:w'),
        (42, 'imt-sync-v1:w'),
        (43, 'imt-po-sync-v1:w'),
        (44, 'pimt-sync-v1:w'),
        (35, 'imt-comment-v2:r'),
        (36, 'imt-comment-v2:w'),
        (41, 'imt-metrics-v1:w'),
        (45, 'ot-gsheet-v1:r'),
        (46, 'ot-gsheet-v2:r'),
        (47, 'imt-metrics-v1:r'),
        (48, 'imt-sync-v1:r'),
        (49, 'imt-po-sync-v1:r'),
        (52, 'pimt-sync-v1:r'),
        (53, 'general:w'),
        (54, 'shipwell-sync-v1:r'),
        (55, 'imt-forecast-upload-sync-v1:r'),
        (56, 'imt-forecast-upload-sync-v1:w'),
        (57, 'imt-jobs-v1:r'),
        (58, 'imt-jobs-v1:w'),
        (59, 'shipwell-sync-v1:w'),
        (60, 'pimt-po-status-v1:r'),
        (61, 'pimt-po-status-v1:w'),
        (62, 'pimt-comment-v2:r'),
        (63, 'pimt-comment-v2:w'),
        (64, 'imt-week-availability-sync-v1:r'),
        (65, 'imt-week-availability-sync-v1:w'),
        (66, 'imt-scrubbing-rescrub-v1:w'),
        (67, 'pimt-sync-job-v1:r'),
        (68, 'pimt-sync-job-v1:w'),
        (69, 'imt-po-sync-job-v1:r'),
        (70, 'imt-po-sync-job-v1:w'),
        (71, 'imt-sync-job-v1:r'),
        (72, 'imt-sync-job-v1:w'),
        (73, 'future-pkg-sync-job-v1:r'),
        (74, 'future-pkg-sync-job-v1:w'),
        (75, 'imt-scrubbing-export-job-v1:r'),
        (76, 'imt-scrubbing-export-job-v1:w'),
        (77, 'cleanup-job-v1:r'),
        (78, 'cleanup-job-v1:w'),
        (79, 'hj-inv-sync-job-v1:r'),
        (80, 'hj-inv-sync-job-v1:w'),
        (81, 'ot-gsheet-job-v1:r'),
        (82, 'ot-gsheet-job-v1:w'),
        (83, 'imt-forecast-upload-sync-job-v1:r'),
        (84, 'imt-forecast-upload-sync-job-v1:w'),
        (85, 'imt-week-availability-sync-job-v1:r'),
        (86, 'imt-week-availability-sync-job-v1:w'),
        (87, 'imt-modify-old-form-v1:r'),
        (88, 'imt-modify-old-form-v1:w'),
        (89, 'pimt-export-v1:r'),
        (90, 'pimt-export-v1:w'),
        (91, 'imt-daily-export-v1:r'),
        (92, 'imt-daily-export-v1:w'),
        (93, 'imt-autobagger-sku-v1:r'),
        (94, 'imt-autobagger-sku-v1:w'),
        (95, 'imt-pck-depl-v1:r'),
        (96, 'pimt-replenishment-v1:r'),
        (97, 'pimt-daily-report-v1:r'),
        (98, 'pimt-daily-report-v1:w'),
        (99, 'pimt-exception-metrics-v1:r'),
        (100, 'pimt-exception-metrics-v1:w'),
        (101, 'pimt-monthly-financial-report-v1:r'),
        (102, 'pimt-monthly-financial-report-v1:w'),
        (103, 'dc-inventory-inventory-module-v1:r')
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.permission_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.permission), false);
        """
    ),
    # Insert roles
    step(
        """
        INSERT INTO procurement.role (id, full_name, short_name, priority)
        VALUES
        (1, 'acl_inventorymanagementus_sso_adminuser', 'admin', 0),
        (2, 'acl_inventorymanagementus_sso_procurementleadership', 'procurementleadership', 20),
        (3, 'acl_inventorymanagementus_sso_procurementmanager', 'procurementmanager', 10),
        (4, 'acl_inventorymanagementus_sso_procurementuser', 'procurementuser', 30),
        (5, 'acl_inventorymanagementus_sso_dcuser', 'dcuser', 50),
        (6, 'acl_inventorymanagementus_sso_scrubber', 'scrubber', 40),
        (7, 'acl_inventorymanagementus_sso_dcuniversaluser', 'dcuniversaluser', 100),
        (8, 'dcmanagementuser', 'dcmanagementuser', 100)
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.role_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.role), false);
        """
    ),
    # Insert Permission-Role
    permissions.grant_permissions(
        {
            "admin": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-gsheet-v1:w",
                "imt-region-v1:r",
                "imt-region-v1:w",
                "imt-remake-tool-v1:r",
                "imt-remake-tool-v1:w",
                "imt-ing-depl-v1:r",
                "imt-dashboard-ing-v1:r",
                "imt-po-status-v1:r",
                "imt-cost-check-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-hj-override-v1:r",
                "imt-hj-override-v1:w",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-scrubbing-v1:r",
                "imt-scrubbing-v1:w",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "imt-comment-log-v2:r",
                "pimt-ait-v1:r",
                "pimt-ing-depl-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "ot-gsheet-v1:w",
                "ot-gsheet-v2:w",
                "imt-cache-v1:r",
                "imt-cache-v1:w",
                "imt-metrics-v1:w",
                "imt-sync-v1:w",
                "imt-po-sync-v1:w",
                "pimt-sync-v1:w",
                "ot-gsheet-v1:r",
                "ot-gsheet-v2:r",
                "imt-sync-v1:r",
                "imt-metrics-v1:r",
                "imt-po-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "shipwell-sync-v1:r",
                "imt-forecast-upload-sync-v1:r",
                "imt-forecast-upload-sync-v1:w",
                "imt-jobs-v1:r",
                "imt-jobs-v1:w",
                "shipwell-sync-v1:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-week-availability-sync-v1:r",
                "imt-week-availability-sync-v1:w",
                "imt-scrubbing-rescrub-v1:w",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-po-sync-job-v1:r",
                "imt-po-sync-job-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "future-pkg-sync-job-v1:r",
                "future-pkg-sync-job-v1:w",
                "imt-scrubbing-export-job-v1:r",
                "imt-scrubbing-export-job-v1:w",
                "cleanup-job-v1:r",
                "cleanup-job-v1:w",
                "hj-inv-sync-job-v1:r",
                "hj-inv-sync-job-v1:w",
                "ot-gsheet-job-v1:r",
                "ot-gsheet-job-v1:w",
                "imt-forecast-upload-sync-job-v1:r",
                "imt-forecast-upload-sync-job-v1:w",
                "imt-week-availability-sync-job-v1:r",
                "imt-week-availability-sync-job-v1:w",
                "imt-modify-old-form-v1:r",
                "imt-modify-old-form-v1:w",
                "pimt-export-v1:r",
                "pimt-export-v1:w",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "imt-autobagger-sku-v1:r",
                "imt-autobagger-sku-v1:w",
                "imt-pck-depl-v1:r",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-exception-metrics-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
            ],
            "procurementleadership": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "imt-dashboard-ing-v1:r",
                "imt-po-status-v1:r",
                "imt-cost-check-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-hj-override-v1:r",
                "imt-hj-override-v1:w",
                "imt-discard-v1:r",
                "imt-scrubbing-v1:r",
                "imt-scrubbing-v1:w",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "imt-comment-log-v2:r",
                "pimt-ait-v1:r",
                "pimt-ing-depl-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "imt-remake-tool-v1:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "pimt-export-v1:r",
                "pimt-export-v1:w",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "imt-pck-depl-v1:r",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-exception-metrics-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
            ],
            "procurementmanager": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "imt-dashboard-ing-v1:r",
                "imt-po-status-v1:r",
                "imt-cost-check-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-hj-override-v1:r",
                "imt-hj-override-v1:w",
                "imt-discard-v1:r",
                "imt-scrubbing-v1:r",
                "imt-scrubbing-v1:w",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "imt-comment-log-v2:r",
                "pimt-ait-v1:r",
                "pimt-ing-depl-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-jobs-v1:r",
                "imt-forecast-upload-sync-v1:r",
                "imt-scrubbing-rescrub-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-forecast-upload-sync-job-v1:r",
                "pimt-export-v1:r",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "imt-autobagger-sku-v1:r",
                "imt-autobagger-sku-v1:w",
                "imt-pck-depl-v1:r",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
            ],
            "procurementuser": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "imt-dashboard-ing-v1:r",
                "imt-po-status-v1:r",
                "imt-cost-check-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-hj-override-v1:r",
                "imt-discard-v1:r",
                "imt-scrubbing-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "imt-comment-log-v2:r",
                "pimt-ait-v1:r",
                "pimt-ing-depl-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "imt-hj-override-v1:w",
                "imt-pull-put-v1:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-jobs-v1:r",
                "imt-forecast-upload-sync-v1:r",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-forecast-upload-sync-job-v1:r",
                "pimt-export-v1:r",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "imt-pck-depl-v1:r",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "imt-scrubbing-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
            ],
            "dcuser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "imt-pck-depl-v1:r",
            ],
            "scrubber": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "imt-dashboard-ing-v1:r",
                "imt-po-status-v1:r",
                "imt-cost-check-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-po-void-v1:r",
                "imt-hj-override-v1:r",
                "imt-discard-v1:r",
                "imt-scrubbing-v1:r",
                "imt-scrubbing-v1:w",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "imt-comment-log-v2:r",
                "pimt-ait-v1:r",
                "pimt-ing-depl-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-pck-depl-v1:r",
                "pimt-replenishment-v1:r",
                "dc-inventory-inventory-module-v1:r",
            ],
            "dcuniversaluser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "imt-pck-depl-v1:r",
            ],
            "dcmanagementuser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-sync-v1:r",
                "pimt-sync-v1:r",
                "general:w",
                "imt-pck-depl-v1:r",
            ],
        }
    ),
]
