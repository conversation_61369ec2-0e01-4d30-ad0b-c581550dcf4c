from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_sku_profile (
            sku_code character varying(20) NOT NULL PRIMARY KEY,
            size character varying(50),
            type character varying(50),
            profile character varying(50)
        );
        """
    ),
]
