from migrations.scripts import permissions
from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

users_rp_mapping = {
    role: ["imt-release-log:r"]
    for role in [
        "admin",
        "procurementmanager",
        "procurementleadership",
        "procurementuser",
        "dcuser",
        "dcuniversaluser",
        "dcmanagementuser",
        "scrubber",
    ]
}
users_rp_mapping["admin"].append("imt-release-log:w")

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS procurement.release_log (
            feature_key int NOT NULL,
            feature_type text NOT NULL,
            release_date date NOT NULL,
            dc_users_available boolean NOT NULL,
            description text NOT NULL,
            PRIMARY KEY (release_date, feature_key)
        );
        """
    ),
    *permissions.create_permissions(users_rp_mapping),
]
