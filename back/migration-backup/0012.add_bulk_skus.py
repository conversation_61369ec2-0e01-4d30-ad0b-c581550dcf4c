from migrations.scripts.permissions import create_permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

new_permissions = ["imt-bulk-sku-v1:r", "imt-bulk-sku-v1:w"]
role_mapping = {
    "admin": new_permissions,
    "procurementmanager": new_permissions,
    "procurementleadership": new_permissions,
}


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.bulk_skus (
            bulk_sku_code character varying(20) NOT NULL,
            packaged_sku_code character varying(20) NOT NULL,
            brands character varying[] NOT NULL,
            PRIMARY KEY (packaged_sku_code, bulk_sku_code)
        );
        """,
        """
        DROP TABLE IF EXISTS inventory.bulk_skus;
        """,
    ),
    create_permissions(role_mapping),
]
