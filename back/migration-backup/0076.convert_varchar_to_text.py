from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE forecast.mock_plan ALTER COLUMN site TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE forecast.oscar ALTER COLUMN dc TYPE TEXT, ALTER COLUMN scm_week TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT, ALTER COLUMN units TYPE TEXT, ALTER COLUMN units SET DEFAULT 'unit':: TEXT,
            ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE forecast.packaging_forecast ALTER COLUMN site TYPE TEXT, ALTER COLUMN brand TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN sku_name TYPE TEXT;
        ALTER TABLE forecast.tpl_mock_plan ALTER COLUMN site TYPE TEXT, ALTER COLUMN production_type TYPE TEXT;
        """
    ),
    step(
        """
        ALTER TABLE highjump.latest_hj_pallet_snapshots ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN wh_id TYPE TEXT,
            ALTER COLUMN license_plate TYPE TEXT;
        ALTER TABLE highjump.packaging_pallet_snapshot ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE highjump.v_highjump_discard ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN tran_type TYPE TEXT;
        ALTER TABLE highjump.v_highjump_wip ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE highjump.v_procurement_export_inv ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE highjump.packaging_pallet_snapshot ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE highjump.v_procurement_export_lps ALTER COLUMN wh_id TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN week TYPE TEXT;
        ALTER TABLE highjump.v_procurement_export_receipts ALTER COLUMN wh_id TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN supplier_name TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN sku_name TYPE TEXT, ALTER COLUMN scm_week_raw TYPE TEXT, ALTER COLUMN status TYPE TEXT,
            ALTER COLUMN week TYPE TEXT, ALTER COLUMN unit TYPE TEXT;
        """
    ),
    step(
        """
        ALTER TABLE inventory.bid_tool_input ALTER COLUMN site TYPE TEXT, ALTER COLUMN supplier_name TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN brand TYPE TEXT, ALTER COLUMN brand SET DEFAULT 'HF'::TEXT;
        ALTER TABLE inventory.bidding_tool_comparison ALTER COLUMN site TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT, ALTER COLUMN brand SET DEFAULT 'HF'::TEXT;
        ALTER TABLE inventory.brand ALTER COLUMN "id" TYPE TEXT, ALTER COLUMN name TYPE TEXT;
        ALTER TABLE inventory.brand_config ALTER COLUMN brand_id TYPE TEXT;
        ALTER TABLE inventory.bulk_skus ALTER COLUMN bulk_sku_code TYPE TEXT, ALTER COLUMN packaged_sku_code TYPE TEXT,
            ALTER COLUMN brands TYPE TEXT[];
        ALTER TABLE inventory.buyer_sku ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.commodity_group ALTER COLUMN group_name TYPE TEXT;
        ALTER TABLE inventory.cycle_counts ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.discard ALTER COLUMN "user" TYPE TEXT, ALTER COLUMN dc TYPE TEXT,
            ALTER COLUMN sku TYPE TEXT, ALTER COLUMN quality_instructions TYPE TEXT, ALTER COLUMN reason TYPE TEXT,
            ALTER COLUMN source TYPE TEXT, ALTER COLUMN week TYPE TEXT, ALTER COLUMN brand TYPE TEXT,
            ALTER COLUMN comment TYPE TEXT, ALTER COLUMN deleted_by TYPE TEXT, ALTER COLUMN updated_by TYPE TEXT;
        ALTER TABLE inventory.goods_receipt_note ALTER COLUMN bob_code TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN order_number TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN unit TYPE TEXT,
            ALTER COLUMN source TYPE TEXT;
        ALTER TABLE inventory.hj_override ALTER COLUMN "user" TYPE TEXT, ALTER COLUMN dc TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT, ALTER COLUMN week TYPE TEXT, ALTER COLUMN source TYPE TEXT,
            ALTER COLUMN po_number TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN deleted_by TYPE TEXT;
        ALTER TABLE inventory.hybrid_needs_ingredients ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN dc TYPE TEXT,
            ALTER COLUMN scm_week TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.hybrid_needs_ingredients_shift_level ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN dc TYPE TEXT, ALTER COLUMN scm_week TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.hybrid_needs_ingredients_status ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN scm_week TYPE TEXT, ALTER COLUMN brand TYPE TEXT, ALTER COLUMN status TYPE TEXT;
        ALTER TABLE inventory.ingredient ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN pack_size_unit TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT, ALTER COLUMN weight_unit TYPE TEXT, ALTER COLUMN storage_location TYPE TEXT,
            ALTER COLUMN allergens TYPE TEXT;
        ALTER TABLE inventory.ingredient_category ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE inventory.ingredient_site_subcategory ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN site TYPE TEXT;
        ALTER TABLE inventory.isp ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN dc_id TYPE TEXT,
            ALTER COLUMN week TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.legacy_format_goods_receipt_note ALTER COLUMN wh_id TYPE TEXT,
            ALTER COLUMN bob_code TYPE TEXT, ALTER COLUMN supplier_name TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN sku_name TYPE TEXT, ALTER COLUMN scm_week_raw TYPE TEXT,
            ALTER COLUMN status TYPE TEXT, ALTER COLUMN week TYPE TEXT, ALTER COLUMN unit TYPE TEXT;
        ALTER TABLE inventory.mealkit ALTER COLUMN code TYPE TEXT, ALTER COLUMN slot TYPE TEXT,
            ALTER COLUMN meal_name TYPE TEXT, ALTER COLUMN scm_week TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.mealkit_ingredient ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN dc_list TYPE TEXT[];
        ALTER TABLE inventory.organic_sku_mapping ALTER COLUMN original_sku_code TYPE TEXT,
            ALTER COLUMN interchangable_sku_code TYPE TEXT;
        ALTER TABLE inventory.packaging_demand ALTER COLUMN site TYPE TEXT, ALTER COLUMN brand TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE inventory.packaging_isp ALTER COLUMN brand TYPE TEXT, ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN supplier_name TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE inventory.packaging_override ALTER COLUMN site TYPE TEXT, ALTER COLUMN brand TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE inventory.packaging_sku_profile ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN size TYPE TEXT,
            ALTER COLUMN type TYPE TEXT, ALTER COLUMN profile TYPE TEXT;
        ALTER TABLE inventory.po_void ALTER COLUMN "user" TYPE TEXT, ALTER COLUMN dc TYPE TEXT,
            ALTER COLUMN week TYPE TEXT, ALTER COLUMN brand TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN source TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN supplier_name TYPE TEXT,
            ALTER COLUMN deleted_by TYPE TEXT;
        ALTER TABLE inventory.pull_put ALTER COLUMN user_email TYPE TEXT, ALTER COLUMN dc TYPE TEXT,
            ALTER COLUMN brand TYPE TEXT, ALTER COLUMN week TYPE TEXT, ALTER COLUMN source TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN deleted_by TYPE TEXT;
        ALTER TABLE inventory.purchasing_category ALTER COLUMN name TYPE TEXT;
    """
    ),
    step(
        """
        ALTER TABLE inventory.receiving ALTER COLUMN sku_ingredient DROP DEFAULT,
            ALTER COLUMN supplier_id DROP DEFAULT, ALTER COLUMN source DROP DEFAULT;
        ALTER TABLE inventory.receiving ALTER COLUMN supplier TYPE TEXT, ALTER COLUMN po TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN dc TYPE TEXT, ALTER COLUMN delivery_status TYPE TEXT,
            ALTER COLUMN receiver_comments TYPE TEXT, ALTER COLUMN ticket_number TYPE TEXT, ALTER COLUMN week TYPE TEXT,
            ALTER COLUMN username TYPE TEXT, ALTER COLUMN sku_ingredient TYPE TEXT,
            ALTER COLUMN supplier_id TYPE TEXT, ALTER COLUMN source TYPE TEXT, ALTER COLUMN brand TYPE TEXT;
        ALTER TABLE inventory.receiving ALTER COLUMN sku_ingredient SET DEFAULT '':: TEXT,
            ALTER COLUMN supplier_id SET DEFAULT '':: TEXT, ALTER COLUMN source SET DEFAULT 'APP':: TEXT;

        ALTER TABLE inventory.remake_tool ALTER COLUMN meal TYPE TEXT, ALTER COLUMN week TYPE TEXT,
            ALTER COLUMN dc TYPE TEXT;
        ALTER TABLE inventory.scrubbing_hj_in_progress_log ALTER COLUMN po_number TYPE TEXT;
        ALTER TABLE inventory.site ALTER COLUMN "id" TYPE TEXT, ALTER COLUMN name TYPE TEXT,
            ALTER COLUMN dc_azure_role TYPE TEXT, ALTER COLUMN timezone TYPE TEXT;
        ALTER TABLE inventory.supplier_portal ALTER COLUMN supplier_name TYPE TEXT;
        ALTER TABLE inventory.supplier_sku ALTER COLUMN supplier_sku_uuid TYPE TEXT,
            ALTER COLUMN culinary_sku_uuid TYPE TEXT;
        ALTER TABLE inventory.weekend_coverage_checklist ALTER COLUMN brand TYPE TEXT, ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN po_number TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN contact_name_vendor_carrier TYPE TEXT, ALTER COLUMN email_phone TYPE TEXT,
            ALTER COLUMN back_up_vendor TYPE TEXT;
        ALTER TABLE inventory.weekly_config ALTER COLUMN site_id TYPE TEXT, ALTER COLUMN brand_id TYPE TEXT;
        ALTER TABLE inventory.packaging_sku_mapping ALTER COLUMN original_sku_code TYPE TEXT,
            ALTER COLUMN interchangable_sku_code TYPE TEXT;
        """
    ),
    step(
        """
        ALTER TABLE ordering.advance_shipping_notice ALTER COLUMN po_uuid TYPE TEXT,
            ALTER COLUMN order_number TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE ordering.culinary_sku ALTER COLUMN sku_uuid TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN sku_name TYPE TEXT, ALTER COLUMN status TYPE TEXT, ALTER COLUMN storage_location TYPE TEXT,
            ALTER COLUMN unit TYPE TEXT, ALTER COLUMN brands TYPE TEXT[], ALTER COLUMN sku_type TYPE TEXT;
        ALTER TABLE ordering.ordering_tool_dashboard ALTER COLUMN brand TYPE TEXT, ALTER COLUMN week TYPE TEXT,
            ALTER COLUMN gsheet_id TYPE TEXT;
        ALTER TABLE ordering.po_acknowledgement_line_items ALTER COLUMN po_uuid TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN unit_of_measure TYPE TEXT,
            ALTER COLUMN order_number TYPE TEXT;

        ALTER TABLE ordering.purchase_order DROP COLUMN order_number;
        ALTER TABLE ordering.purchase_order ALTER COLUMN po_uuid TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN dc TYPE TEXT, ALTER COLUMN brand TYPE TEXT, ALTER COLUMN supplier_code TYPE TEXT,
            ALTER COLUMN supplier TYPE TEXT, ALTER COLUMN ot_po_status TYPE TEXT, ALTER COLUMN week TYPE TEXT,
            ALTER COLUMN emergency_reason TYPE TEXT, ALTER COLUMN ordered_by TYPE TEXT,
            ALTER COLUMN shipping_method TYPE TEXT;
        ALTER TABLE ordering.purchase_order ADD COLUMN order_number
            TEXT GENERATED ALWAYS AS (substr((po_number), 1, 12)) STORED;

        ALTER TABLE ordering.purchase_order_shipment ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN load_number TYPE TEXT, ALTER COLUMN carrier_name TYPE TEXT,
            ALTER COLUMN origin_location_reference TYPE TEXT, ALTER COLUMN region_code TYPE TEXT,
            ALTER COLUMN postal_code TYPE TEXT, ALTER COLUMN administrative_area TYPE TEXT,
            ALTER COLUMN locality TYPE TEXT, ALTER COLUMN address_lines TYPE TEXT[],
            ALTER COLUMN organization TYPE TEXT, ALTER COLUMN execution_event TYPE TEXT;
        ALTER TABLE ordering.purchase_order_sku ALTER COLUMN po_uuid TYPE TEXT, ALTER COLUMN sku_uuid TYPE TEXT,
            ALTER COLUMN order_unit TYPE TEXT, ALTER COLUMN case_unit TYPE TEXT;

        ALTER TABLE ordering.sku DROP COLUMN sku_id;
        ALTER TABLE ordering.sku ALTER COLUMN sku_uuid TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN sku_name TYPE TEXT;
        ALTER TABLE ordering.sku ADD COLUMN sku_id
            INTEGER GENERATED ALWAYS AS (((regexp_split_to_array((sku_code), '-'::TEXT))[3])::INTEGER) STORED;
        """
    ),
    step(
        """
        ALTER TABLE pimt.goods_receipt_note ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN order_number TYPE TEXT,
            ALTER COLUMN bob_code TYPE TEXT;

        ALTER TABLE pimt.inventory DROP COLUMN order_number;
        ALTER TABLE pimt.inventory ALTER COLUMN po_number TYPE TEXT, ALTER COLUMN lot TYPE TEXT,
            ALTER COLUMN partner TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT;
        ALTER TABLE pimt.inventory ADD COLUMN order_number TEXT GENERATED ALWAYS AS (substr((po_number), 1, 12)) STORED;

        ALTER TABLE pimt.inventory_notification ALTER COLUMN po_number TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN customer_id TYPE TEXT, ALTER COLUMN supplier_code TYPE TEXT, ALTER COLUMN bob_code TYPE TEXT,
            ALTER COLUMN lot_code TYPE TEXT, ALTER COLUMN grn_id TYPE TEXT, ALTER COLUMN order_number TYPE TEXT;
        ALTER TABLE pimt.master_replenishment ALTER COLUMN site TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN status TYPE TEXT, ALTER COLUMN replenishment_type TYPE TEXT,
            ALTER COLUMN replenishment_buyer TYPE TEXT;
        ALTER TABLE pimt.packaging_info ALTER COLUMN supplier_sku_uuid TYPE TEXT;
        ALTER TABLE pimt.warehouse ALTER COLUMN code TYPE TEXT, ALTER COLUMN "name" TYPE TEXT,
            ALTER COLUMN ot_dcs TYPE TEXT[], ALTER COLUMN ot_suppliers TYPE TEXT[], ALTER COLUMN region TYPE TEXT,
            ALTER COLUMN regional_dcs TYPE TEXT[], ALTER COLUMN bob_code TYPE TEXT,
            ALTER COLUMN receiving_type TYPE TEXT, ALTER COLUMN hj_name TYPE TEXT,
            ALTER COLUMN inventory_type TYPE TEXT;

        ALTER TABLE pimt.po_dummy_record DROP COLUMN sku_id, DROP COLUMN order_number;
        ALTER TABLE pimt.po_dummy_record ALTER COLUMN dc TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN supplier TYPE TEXT, ALTER COLUMN sku_name TYPE TEXT, ALTER COLUMN sku_code TYPE TEXT,
            ALTER COLUMN week TYPE TEXT, ALTER COLUMN storage_asigment TYPE TEXT;
        ALTER TABLE pimt.po_dummy_record
            ADD COLUMN sku_id INTEGER GENERATED ALWAYS
                AS (((regexp_split_to_array((sku_code), '-'::TEXT))[3])::INTEGER) STORED,
            ADD COLUMN order_number TEXT GENERATED ALWAYS AS (substr((po_number), 1, 12)) STORED;
        """
    ),
    step(
        """
        ALTER TABLE procurement.comment ALTER COLUMN brand TYPE TEXT, ALTER COLUMN site TYPE TEXT,
            ALTER COLUMN resource_id TYPE TEXT, ALTER COLUMN updated_by TYPE TEXT, ALTER COLUMN domain TYPE TEXT;
        ALTER TABLE procurement.gsheet_admin ALTER COLUMN scm_week TYPE TEXT, ALTER COLUMN gsheet_id TYPE TEXT;
        ALTER TABLE procurement.gsheet_meta ALTER COLUMN name TYPE TEXT, ALTER COLUMN doc_code TYPE TEXT,
            ALTER COLUMN title_template TYPE TEXT, ALTER COLUMN brand TYPE TEXT, ALTER COLUMN project TYPE TEXT;
        ALTER TABLE procurement.ordering_tool_gsheet ALTER COLUMN quarter TYPE TEXT, ALTER COLUMN gsheet_id TYPE TEXT;
        ALTER TABLE procurement.parallel_sync_log ALTER COLUMN job_name TYPE TEXT,
            ALTER COLUMN parent_job_ids TYPE TEXT[], ALTER COLUMN sync_status TYPE TEXT;
        ALTER TABLE procurement.permission ALTER COLUMN name TYPE TEXT;
        ALTER TABLE procurement.role ALTER COLUMN full_name TYPE TEXT, ALTER COLUMN short_name TYPE TEXT;
        ALTER TABLE procurement.scrubbing_comment ALTER COLUMN dc TYPE TEXT, ALTER COLUMN po_number TYPE TEXT,
            ALTER COLUMN sku_code TYPE TEXT, ALTER COLUMN updated_by TYPE TEXT;
        ALTER TABLE procurement."user" ALTER COLUMN first_name TYPE TEXT, ALTER COLUMN last_name TYPE TEXT,
            ALTER COLUMN email TYPE TEXT;
        ALTER TABLE procurement.user_view_state ALTER COLUMN resource TYPE TEXT, ALTER COLUMN name TYPE TEXT;
        """
    ),
    step(
        """
        DROP TABLE IF EXISTS procurement.sync_log;
        DROP TABLE IF EXISTS public.business_description;
        """
    ),
]
