from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.pull_put ADD COLUMN IF NOT EXISTS deleted_by VARCHAR(255);
        ALTER TABLE inventory.pull_put ADD COLUMN IF NOT EXISTS deleted_ts TIMESTAMP without time zone;
        """,
        """
        ALTER TABLE inventory.pull_put DROP COLUMN IF EXISTS deleted_by;
        ALTER TABLE inventory.pull_put DROP COLUMN IF EXISTS deleted_ts;
        """,
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS i_inventory_pull_put_deleted_ts ON inventory.pull_put (deleted_ts);
        """
    ),
]
