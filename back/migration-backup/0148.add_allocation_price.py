from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.allocation_price (
            scm_week INTEGER NOT NULL,
            brand TEXT NOT NULL,
            site TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            price NUMERIC NOT NULL,
            PRIMARY KEY (scm_week, brand, site, sku_code)
        );
        """,
    ),
]
