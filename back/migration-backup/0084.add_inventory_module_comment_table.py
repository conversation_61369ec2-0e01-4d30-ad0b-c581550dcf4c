from migrations.scripts import permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

users_rp_mapping = {
    role: ["dc-inventory-inventory-module-v1:w"]
    for role in ["admin", "procurementmanager", "procurementleadership", "procurementuser", "scrubber"]
}

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS procurement.inventory_module_comment(
            sku_code text not null,
            brand text not null,
            site text not null,
            "text" text,
            last_edited_by int not null,
            PRIMARY KEY (sku_code, site, brand)
        );
        """
    ),
    permissions.create_permissions(users_rp_mapping),
]
