from migrations.scripts import permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

role_mapping = {"admin": ["snapshot-start-of-day-inv:r", "snapshot-start-of-day-inv:w"]}

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.weekly_snapshot (
            market TEXT NOT NULL,
            wh_code TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            week INTEGER NOT NULL,
            quantity NUMERIC NOT NULL,
            expiration_date DATE,
            UNIQUE NULLS NOT DISTINCT (market, wh_code, sku_code, week, expiration_date)
        );
        """
    ),
    *permissions.create_permissions(role_mapping),
]
