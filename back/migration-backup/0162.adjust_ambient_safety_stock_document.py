from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        TRUNCATE forecast.packaging_long_term_forecast;
        ALTER TABLE forecast.packaging_long_term_forecast DROP CONSTRAINT IF EXISTS packaging_long_term_forecast_pkey,
            ADD PRIMARY KEY ("week", "brand", "site", "market", "demand_pipeline"),
            DROP COLUMN sku_type;
        """
    ),
    step(
        """
        TRUNCATE inventory.packaging_units_per_truck;
        ALTER TABLE inventory.packaging_units_per_truck
            DROP CONSTRAINT IF EXISTS packaging_units_per_truck_pkey,
            ADD PRIMARY KEY ("bob_code", "sku_code"),
            DROP COLUMN demand_pipeline;
        """
    ),
    step(
        """
        DELETE FROM inventory.packaging_combined_report WHERE is_hfo = true;
        ALTER TABLE inventory.packaging_combined_report DROP COLUMN is_hfo;
        ALTER TABLE inventory.packaging_combined_report RENAME TO vendor_managed_inventory;
        """
    ),
    step(
        """
        TRUNCATE forecast.demand_pipeline_sku_mapping;
        ALTER TABLE forecast.demand_pipeline_sku_mapping
            DROP CONSTRAINT packaging_long_term_forecast_mapping_pkey,
            ADD PRIMARY KEY (sku_code, market);
        """
    ),
]
