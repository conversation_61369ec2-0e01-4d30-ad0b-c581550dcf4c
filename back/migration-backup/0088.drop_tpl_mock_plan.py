from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DROP TABLE IF EXISTS forecast.tpl_mock_plan;
        """
    ),
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id in (
            select id from procurement.gsheet_meta where doc_code='recipe_data_by_ship_day_3pl'
        );
        DELETE FROM  procurement.gsheet_meta WHERE doc_code='recipe_data_by_ship_day_3pl';
        """
    ),
]
