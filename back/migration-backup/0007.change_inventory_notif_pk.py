from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE pimt.inventory_notification DROP CONSTRAINT inventory_notification_pkey;
        ALTER TABLE pimt.inventory_notification ALTER COLUMN state SET NOT NULL;
        ALTER TABLE pimt.inventory_notification ADD CONSTRAINT inventory_notification_pkey
        PRIMARY KEY (po_number, sku_code, lot_code, state);
        """
    ),
]
