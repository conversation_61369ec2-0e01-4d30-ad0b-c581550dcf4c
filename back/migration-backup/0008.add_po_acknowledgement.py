from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.po_acknowledgement_line_items (
            po_uuid VARCHAR(255),
            sku_code VARCHAR(20),
            order_number VARCHAR(20),
            sku_id INTEGER,
            state INTEGER,
            number_of_pallets INTEGER,
            unit_of_measure VARCHAR(20),
            size INTEGER,
            packing_size INTEGER,
            promised_date TIMESTAMP WITHOUT TIME ZONE,
            PRIMARY KEY (po_uuid, sku_code)
        );
        """,
        """
        DROP TABLE IF EXISTS ordering.po_acknowledgement_line_items;
        """,
    ),
]
