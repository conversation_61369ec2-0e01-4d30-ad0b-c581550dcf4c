from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DROP TABLE IF EXISTS forecast.packaging_forecast;
        """
    ),
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id IN
            (SELECT id FROM procurement.gsheet_meta WHERE doc_code = 'packaging_forecast');
        DELETE FROM procurement.gsheet_meta WHERE doc_code = 'packaging_forecast';
        """
    ),
    step(
        """
        ALTER TABLE forecast.packaging_long_term_forecast
        ALTER COLUMN week TYPE INTEGER USING replace(packaging_long_term_forecast.week, '-W', '')::INTEGER;
        """
    ),
]
