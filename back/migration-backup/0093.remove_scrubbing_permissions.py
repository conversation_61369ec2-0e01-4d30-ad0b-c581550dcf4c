from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DELETE  FROM procurement.permission_role WHERE permission_id IN (
        SELECT id from procurement.permission WHERE name in
            (
            'imt-scrubbing-v1:r', 'imt-scrubbing-v1:w',
            'imt-scrubbing-export-job-v1:r', 'imt-scrubbing-export-job-v1:w',
            'imt-week-availability-sync-v1:r', 'imt-week-availability-sync-v1:w',
            'imt-week-availability-sync-job-v1:r', 'imt-week-availability-sync-job-v1:w',
            'imt-pck-depl-v1:r', 'pimt-ing-depl-v1:r'
            )
        );
        DELETE FROM procurement.permission where name IN
            (
            'imt-scrubbing-v1:r', 'imt-scrubbing-v1:w',
            'imt-scrubbing-export-job-v1:r', 'imt-scrubbing-export-job-v1:w',
            'imt-week-availability-sync-v1:r', 'imt-week-availability-sync-v1:w',
            'imt-week-availability-sync-job-v1:r', 'imt-week-availability-sync-job-v1:w',
            'imt-pck-depl-v1:r', 'pimt-ing-depl-v1:r'
        );
        """
    ),
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id IN (
            SELECT id from procurement.gsheet_meta where doc_code = 'packaging_isp'
        );
        DELETE FROM procurement.gsheet_meta where doc_code = 'packaging_isp';
        """
    ),
    step(
        """
        DROP TABLE IF EXISTS inventory.scrubbing_hj_in_progress_log;
        DROP TABLE IF EXISTS inventory.bidding_tool_comparison;
        DROP TABLE IF EXISTS inventory.bid_tool_input;
        DROP TABLE IF EXISTS inventory.packaging_isp;
        """
    ),
]
