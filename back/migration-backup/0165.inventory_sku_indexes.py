from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE INDEX IF NOT EXISTS i_cycle_counts_sku ON inventory.cycle_counts (sku_code);
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS i_inventory_snapshot_sku ON inventory.inventory_snapshot (sku_code);
        """
    ),
]
