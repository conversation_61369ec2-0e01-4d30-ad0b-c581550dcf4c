from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        UPDATE inventory.weekly_config SET properties = CASE
            WHEN properties->>'inventory_type' = 'Manual' THEN properties || '{"inventory_type": "GSHEET"}'
            WHEN properties->>'inventory_type' = 'HighJump' THEN properties || '{"inventory_type": "HJ"}'
            WHEN properties->>'inventory_type' = 'E2Open' THEN properties || '{"inventory_type": "E2OPEN"}'
            WHEN properties->>'inventory_type' = 'CycleCount' THEN properties || '{"inventory_type": "CYCLE_COUNT"}'
            ELSE properties
        END;
        """
    ),
]
