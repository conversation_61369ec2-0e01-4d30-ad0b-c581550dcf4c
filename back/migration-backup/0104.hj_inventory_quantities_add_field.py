from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE highjump.v_procurement_export_lps ADD COLUMN IF NOT EXISTS expiration_date DATE;
        ALTER TABLE highjump.v_procurement_export_lps DROP CONSTRAINT v_procurement_export_lps_pkey;
        CREATE UNIQUE INDEX ui_v_procurement_export_lps_key
            ON highjump.v_procurement_export_lps (week, wh_id, sku_code, expiration_date) NULLS NOT DISTINCT;
        """
    )
]
