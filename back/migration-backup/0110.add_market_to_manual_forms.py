from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.hj_override ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.hj_override ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_hj_override_market ON inventory.hj_override (market);
        """
    ),
    step(
        """
        ALTER TABLE highjump.v_procurement_export_receipts ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE highjump.v_procurement_export_receipts ALTER COLUMN market DROP DEFAULT;
        """
    ),
    step(
        """
        ALTER TABLE inventory.legacy_format_goods_receipt_note ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.legacy_format_goods_receipt_note ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_legacy_format_goods_receipt_note_market
          ON inventory.legacy_format_goods_receipt_note (market);
        """
    ),
    step(
        """
        ALTER TABLE inventory.po_void ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.po_void ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_lpo_void_market ON inventory.po_void (market);
        """
    ),
    step(
        """
        ALTER TABLE inventory.discard ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.discard ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_discard_market ON inventory.discard (market);
        """
    ),
    step(
        """
        ALTER TABLE inventory.weekend_coverage_checklist ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.weekend_coverage_checklist ALTER COLUMN market DROP DEFAULT;
        CREATE INDEX IF NOT EXISTS i_inventory_weekend_coverage_checklist_market
          ON inventory.weekend_coverage_checklist (market);
        """
    ),
]
