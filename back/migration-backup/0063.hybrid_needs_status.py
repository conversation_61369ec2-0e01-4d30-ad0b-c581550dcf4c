from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients_status (
            site character varying(4) NOT NULL,
            scm_week character varying(10) NOT NULL,
            day date NOT NULL,
            brand character varying(4) NOT NULL,
            status character varying(20) NOT NULL,
            PRIMARY KEY (scm_week, day, site, brand)
        );
        """
    ),
]
