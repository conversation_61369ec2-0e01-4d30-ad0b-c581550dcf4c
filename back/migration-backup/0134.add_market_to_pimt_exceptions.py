from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        ALTER TABLE pimt.exceptions_metrics ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;
        ALTER TABLE pimt.exceptions_metrics ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE pimt.exceptions_metrics
            DROP CONSTRAINT IF EXISTS exceptions_metrics_pkey,
            ADD CONSTRAINT exceptions_metrics_pkey PRIMARY KEY (exception_date, market);
        """
    )
]
