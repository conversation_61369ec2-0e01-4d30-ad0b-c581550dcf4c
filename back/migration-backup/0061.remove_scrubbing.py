from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("DROP TABLE IF EXISTS inventory.scrubbing;"),
    step(
        """
        DELETE FROM procurement.permission_role WHERE
        permission_id in (
            SELECT "id" from procurement.permission WHERE "name" in (
            'imt-scrubbing-rescrub-v1:r', 'imt-scrubbing-rescrub-v1:w')
        );
        DELETE FROM procurement.permission WHERE "name" in ('imt-scrubbing-rescrub-v1:r', 'imt-scrubbing-rescrub-v1:w');
    """
    ),
]
