from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_po_number_sku_code
        ON highjump.v_procurement_export_receipts (po_number, sku_code);

        DROP INDEX IF EXISTS v_procurement_export_receipts_po_number;
        """,
        """
        CREATE_INDEX IF NOT EXISTS v_procurement_export_receipts_po_number
        ON highjump.v_procurement_export_receipts (po_number);

        DROP INDEX IF EXISTS v_procurement_export_receipts_po_number_sku_code;
        """,
    )
]
