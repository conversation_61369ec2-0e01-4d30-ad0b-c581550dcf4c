from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.hj_override RENAME TO receipt_override;
        ALTER TABLE inventory.receipt_override RENAME CONSTRAINT hj_override_pkey TO receipt_override_pkey;
        ALTER INDEX IF EXISTS inventory.hj_override_po_number_idx RENAME TO i_receipt_override_po_number;
        ALTER INDEX IF EXISTS inventory.i_hj_override_upd_tmst RENAME TO i_receipt_override_upd_tmst;
        ALTER INDEX IF EXISTS inventory.i_inventory_hj_override_deleted_ts RENAME TO i_receipt_override_deleted_ts;
        ALTER INDEX IF EXISTS inventory.i_inventory_hj_override_market RENAME TO i_receipt_override_market;
        ALTER INDEX IF EXISTS inventory.iinventory_hj_override_sku_code RENAME TO i_receipt_override_sku_code;
        ALTER INDEX IF EXISTS inventory.inventoryhj_override_brand RENAME TO i_receipt_override_brand;
        ALTER INDEX IF EXISTS inventory.inventoryhj_override_dc RENAME TO i_receipt_override_dc;
        ALTER INDEX IF EXISTS inventory.inventoryhj_override_source RENAME TO i_receipt_override_source;
        ALTER INDEX IF EXISTS inventory.inventoryhj_override_week RENAME TO i_receipt_override_week;
        """
    ),
    step(
        """
        UPDATE procurement.permission SET name = 'imt-receipt-override-v1:r' WHERE  name = 'imt-hj-override-v1:r';
        UPDATE procurement.permission SET name = 'imt-receipt-override-v1:w' WHERE  name = 'imt-hj-override-v1:w';
        """
    ),
]
