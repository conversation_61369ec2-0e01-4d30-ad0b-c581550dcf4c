from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE forecast.oscar DROP CONSTRAINT IF EXISTS oscar_pkey;
        DELETE FROM forecast.oscar WHERE sku_code IS NULL;
        ALTER TABLE forecast.oscar ADD CONSTRAINT oscar_pkey PRIMARY KEY (dc, scm_week, sku_code, brand);
        ALTER TABLE forecast.oscar DROP COLUMN IF EXISTS sku_id;
        CREATE INDEX IF NOT EXISTS iforecast_oscar_sku_code ON forecast.oscar(sku_code);
        """
    )
]
