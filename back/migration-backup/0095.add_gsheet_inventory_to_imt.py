from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE INDEX IF NOT EXISTS i_inventory_code_type_snapshot_timestamp
        ON pimt.unified_inventory (wh_code, inventory_type, snapshot_timestamp);
        DROP INDEX IF EXISTS pimt.ipimt_unified_inventory_wh_code_snapshot_timestamp;
        DROP INDEX IF EXISTS pimt.ipimt_unified_inventory;
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta
            (name, doc_code, weekly, "order", title_template, brand, required, project, market)
        VALUES
        ('IMT Data Feeds', 'imt_inventory', false, 24,
        'IMT - ALL 3PLs - Grid Dynamics Data Feed', 'HF', false, 'imt', 'US');
        """,
    ),
    step("ALTER TABLE pimt.unified_inventory SET SCHEMA inventory;"),
]
