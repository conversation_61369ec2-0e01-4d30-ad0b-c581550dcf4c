from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.legacy_format_goods_receipt_note (
            wh_id varchar(4),
            bob_code varchar(4),
            supplier_name varchar(100),
            po_number varchar(20) NOT NULL,
            cases_received NUMERIC,
            quantity_received NUMERIC,
            sku_code varchar(20) NOT NULL,
            sku_name varchar(255),
            scm_week_raw varchar(10),
            status varchar(40) NOT NULL,
            receipt_time_est timestamp,
            week varchar(10),
            unit varchar(20),
            update_ts timestamp NOT NULL DEFAULT current_timestamp
        );
        """
    ),
    # migrate old data
    step(
        """
        WITH t AS (
            SELECT
            wh_id,
            supplier_name,
            po_number,
            sum(cases_received) OVER(PARTITION BY po_number, sku_code, status, week) AS cases_received,
            sum(quantity_received) OVER(PARTITION BY po_number, sku_code, status, week) AS quantity_received,
            sku_code,
            sku_name,
            scm_week_raw,
            status,
            receipt_time_est,
            unit,
            row_number() OVER(
                PARTITION BY po_number, sku_code, status
                ORDER BY week DESC
            ) AS rn
            FROM highjump.v_procurement_export_receipts
        )
        INSERT INTO inventory.legacy_format_goods_receipt_note
            SELECT
                wh_id,
                NULL AS bob_code,
                supplier_name,
                po_number,
                cases_received,
                quantity_received,
                sku_code,
                sku_name,
                scm_week_raw,
                status,
                receipt_time_est,
                substring(scm_week_raw, 1, 4) || '-W' || substring(scm_week_raw, 5, 2) AS week,
                unit
            FROM t
            WHERE rn = 1
        ;
        """
    ),
    step(
        """
        ALTER TABLE inventory.legacy_format_goods_receipt_note
        ADD CONSTRAINT legacy_format_goods_receipt_note_pkey PRIMARY KEY (po_number, sku_code, status);

        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_week
        ON inventory.legacy_format_goods_receipt_note (week);
        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_week_raw
        ON inventory.legacy_format_goods_receipt_note (scm_week_raw);
        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_wh_id
        ON inventory.legacy_format_goods_receipt_note (wh_id);
        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_sku_code
        ON inventory.legacy_format_goods_receipt_note (sku_code);
        """
    ),
]
