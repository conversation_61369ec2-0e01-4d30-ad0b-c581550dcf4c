from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
            ALTER TABLE ordering.culinary_sku ADD COLUMN IF NOT EXISTS brands varchar[];
        """,
    ),
    step(
        """
            ALTER TABLE inventory.discard DROP COLUMN sku_name;
            ALTER TABLE inventory.hj_override DROP COLUMN sku_name;
        """
    ),
]
