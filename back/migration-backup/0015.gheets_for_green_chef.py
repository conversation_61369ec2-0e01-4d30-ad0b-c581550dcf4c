from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (name, doc_code, weekly, "order", title_template, brand,
        required, project)
        VALUES
        ('GC Oscar', 'gc_oscar', false, 5, 'Green Chef Oscar', 'GC', true, 'imt'),
        ('Production Kit Guide', 'pkg', true, 26, 'Production Kit Guide - {scm_week}', 'GC', true, 'imt'),
        ('Hybrid Needs', 'hybrid_needs', true, 27, 'Hybrid Needs - Week {week_number}', 'GC', true, 'imt');
        """
    )
]
