from migrations.scripts.permissions import grant_permissions

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    grant_permissions(
        {
            role: [Permissions.PIMT_EXCEPTION_METRICS_V1.read]
            for role in ["procurementmanager", "procurementuser", "scrubber"]
        }
    )
]
