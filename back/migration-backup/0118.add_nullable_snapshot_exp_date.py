from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.inventory_snapshot DROP CONSTRAINT IF EXISTS inventory_snapshot_pkey;
        ALTER TABLE inventory.inventory_snapshot ALTER expiration_date DROP NOT NULL;
        UPDATE inventory.inventory_snapshot SET expiration_date = NULL WHERE expiration_date = '0001-01-01';
        CREATE UNIQUE INDEX IF NOT EXISTS ui_inventory_snapshot
            ON inventory.inventory_snapshot (
                wh_code, snapshot_ts, state, expiration_date, location_type, location_id, sku_code, inventory_type
            )
            NULLS NOT DISTINCT
        ;
        """
    ),
    step("UPDATE inventory.weekly_snapshot SET expiration_date = NULL WHERE expiration_date = '0001-01-01';"),
]
