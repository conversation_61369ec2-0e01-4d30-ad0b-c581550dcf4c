from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE forecast.oscar ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE forecast.oscar ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE forecast.oscar
            DROP CONSTRAINT IF EXISTS oscar_pkey,
            ADD CONSTRAINT oscar_pkey PRIMARY KEY (dc, scm_week, sku_code, brand, market);
        """
    ),
]
