from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE ordering.culinary_sku ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;

        ALTER TABLE ordering.culinary_sku ALTER COLUMN market DROP DEFAULT, DROP COLUMN quantity, DROP COLUMN size;

        CREATE INDEX IF NOT EXISTS iordering_culinary_sku_market ON ordering.culinary_sku (market);
        """
    ),
]
