from migrations.scripts import permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

role_permissions_mapping = {
    role: ["dc-inventory-network-depletion-v1:r", "dc-inventory-network-depletion-v1:w"]
    for role in ["admin", "procurementleadership", "procurementmanager", "procurementuser", "scrubber"]
}

steps = [
    step(
        """
    CREATE TABLE IF NOT EXISTS procurement.network_depletion_preset(
        id integer NOT NULL,
        "name" text NOT NULL,
        user_id integer NOT NULL,
        "configuration" jsonb NOT NULL,
        PRIMARY KEY (user_id, id)
    );
    """
    ),
    *permissions.create_permissions(role_permissions_mapping),
]
