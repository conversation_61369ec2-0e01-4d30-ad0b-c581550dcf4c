from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DELETE FROM procurement.permission_role WHERE permission_id IN (
        SELECT id FROM procurement.permission WHERE name = 'imt-release-log:r'
        ) AND role_id IN (SELECT id FROM procurement.role WHERE short_name <> 'admin');
        """
    )
]
