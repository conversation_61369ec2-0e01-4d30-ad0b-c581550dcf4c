from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS pimt.goods_receipt_note (
            sku_code VARCHAR(20),
            order_number VARCHAR(20),
            sku_id INTEGER,
            delivery_time TIMESTAMP WITHOUT TIME ZONE,
            bob_code VARCHAR(2),
            units INTEGER,
            PRIMARY KEY (order_number, sku_code)
        );
        """,
        """
        DROP TABLE IF EXISTS pimt.goods_receipt_note;
        """,
    )
]
