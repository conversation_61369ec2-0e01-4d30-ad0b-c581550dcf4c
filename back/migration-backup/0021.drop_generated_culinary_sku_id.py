from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE ordering.culinary_sku ADD new_sku_id int;
        UPDATE ordering.culinary_sku SET new_sku_id = sku_id;
        ALTER TABLE ordering.culinary_sku DROP sku_id;
        ALTER TABLE ordering.culinary_sku RENAME new_sku_id TO sku_id;
        CREATE INDEX i_culinary_sku_id ON ordering.culinary_sku (sku_id);
        """
    ),
]
