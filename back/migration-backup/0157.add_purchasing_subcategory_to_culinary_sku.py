from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE inventory.purchasing_subcategory (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL
        );
        CREATE UNIQUE INDEX inventorypurchasing_subcategory_name ON inventory.purchasing_subcategory (name);
        """
    ),
    step(
        """
        ALTER TABLE ordering.culinary_sku ADD COLUMN IF NOT EXISTS purchasing_subcategory_id INTEGER;
        """
    ),
]
