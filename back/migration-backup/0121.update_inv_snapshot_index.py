from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DROP INDEX IF EXISTS inventory.ui_inventory_snapshot;
        CREATE UNIQUE INDEX ui_inventory_snapshot
            ON inventory.inventory_snapshot (
                wh_code, inventory_type, snapshot_ts DESC, location_id, state, expiration_date, location_type, sku_code
            )
            NULLS NOT DISTINCT;
        """
    ),
]
