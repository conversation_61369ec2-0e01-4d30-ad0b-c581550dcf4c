from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
      ALTER TABLE pimt.unified_inventory DROP CONSTRAINT unified_inventory_pkey;
      ALTER TABLE pimt.unified_inventory ADD CONSTRAINT
          unified_inventory_pkey PRIMARY KEY (
              order_number, sku_code, wh_code, inventory_type, lot_code, inventory_status, snapshot_timestamp
      );
      """
    )
]
