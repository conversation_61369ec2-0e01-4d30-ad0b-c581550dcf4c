from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("ALTER TABLE inventory.brand ADD week_calendar_mon_shift integer, ADD week_length integer;"),
    step(
        """
        UPDATE inventory.brand SET week_calendar_mon_shift = -5, week_length = 9 WHERE id <> 'FJ';
        UPDATE inventory.brand SET week_calendar_mon_shift = -10, week_length = 12 WHERE id = 'FJ';
        """
    ),
    step("ALTER TABLE inventory.brand ALTER week_calendar_mon_shift SET NOT NULL, ALTER week_length SET NOT NULL;"),
]
