from yoyo import step

from procurement.constants.protobuf import GrnDeliveryLineState
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("ALTER TABLE inventory.goods_receipt_note DROP sku_id;"),
    step(
        """
        DELETE FROM inventory.goods_receipt_note
        WHERE (po_number, sku_code, status, update_ts) IN (
            SELECT po_number, sku_code, status, update_ts
            FROM (
                SELECT
                po_number, sku_code, status, update_ts,
                DENSE_RANK() OVER (PARTITION BY po_number, sku_code ORDER BY update_ts DESC) AS rnk
                FROM inventory.goods_receipt_note grn
            ) AS t
            WHERE rnk > 1
        );
        """
    ),
    step(
        """
        INSERT INTO inventory.goods_receipt_note (
            bob_code,
            po_number,
            order_number,
            sku_code,
            "week",
            units_received,
            receipt_time_est,
            status,
            unit,
            update_ts,
            "source"
        )
        SELECT
            bob_code,
            order_number AS po_number,
            order_number,
            sku_code,
            CAST(concat('20', substring(order_number, 1, 4)) AS int) AS "week",
            units AS units_received,
            delivery_time AS receipt_time_est,
            5 AS status,
            'unit' AS unit,
            COALESCE(record_date, current_timestamp),
            'E2OPEN' AS "source"
        FROM pimt.goods_receipt_note
        WHERE substring(order_number, 1, 4) ~ '^\\d+$'
        ON CONFLICT DO NOTHING
        ;
        """
    ),
    step(
        f"""
        DELETE FROM inventory.goods_receipt_note
        WHERE status < {GrnDeliveryLineState.RECEIVED} AND units_received = 0;
        """
    ),
]
