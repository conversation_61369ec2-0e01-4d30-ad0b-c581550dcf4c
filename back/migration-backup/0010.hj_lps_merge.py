from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE highjump.v_procurement_export_lps_new (
            week varchar(8) NOT NULL,
            wh_id varchar(10) NOT NULL,
            sku_code varchar(20) NOT NULL,
            pallet_quantity float4 NOT NULL
        );
        """
    ),
    step(
        """
        INSERT INTO highjump.v_procurement_export_lps_new
            SELECT
                week, wh_id, sku_code, SUM(pallet_quantity) AS pallet_quantity
            FROM highjump.v_procurement_export_lps
            WHERE brand IS NOT NULL
            GROUP BY week, wh_id, sku_code
        ;
        """
    ),
    step(
        """
        ALTER TABLE highjump.v_procurement_export_lps_new
            ADD CONSTRAINT v_procurement_export_lps_pkey PRIMARY KEY (week, wh_id, sku_code)
        ;
        """
    ),
    step(
        """
        DROP TABLE highjump.v_procurement_export_lps;
        ALTER TABLE highjump.v_procurement_export_lps_new RENAME TO v_procurement_export_lps;
        """
    ),
]
