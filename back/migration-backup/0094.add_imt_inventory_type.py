from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        UPDATE inventory.weekly_config SET properties = properties || jsonb '{"inventory_type": "HighJump"}'
        where properties->'receiving_type' ? 'HighJump';

        UPDATE inventory.weekly_config SET properties = properties || jsonb '{"inventory_type": "CycleCount"}'
        where NOT properties->'receiving_type' ? 'HighJump';
        """
    ),
]
