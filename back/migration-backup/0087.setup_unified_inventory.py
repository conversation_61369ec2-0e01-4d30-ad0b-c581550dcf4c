from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS pimt.unified_inventory(
            wh_code TEXT NOT NULL,
            po_number TEXT NOT NULL,
            order_number TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            lot_code TEXT NOT NULL,
            case_quantity INTEGER,
            unit_quantity INTEGER,
            expiration_date DATE,
            inventory_status TEXT NOT NULL,
            snapshot_timestamp TIMESTAMP NOT NULL,
            inventory_type TEXT NOT NULL,
            PRIMARY KEY (order_number, sku_code, wh_code, lot_code, inventory_status, snapshot_timestamp)
        );
        """
    ),
    step(
        """
        INSERT INTO pimt.unified_inventory (
            wh_code, po_number, order_number, sku_code, lot_code, case_quantity,
            expiration_date, inventory_status, inventory_type, snapshot_timestamp
        )
        SELECT
            partner, po_number, substr(po_number, 1, 12), sku_code, lot, sum(qty),
            min(expiration_date), 'AVAILABLE', 'GSHEET', inventory_update.update_date
        FROM pimt.inventory inventory
        JOIN pimt.inventory_update inventory_update ON (inventory_update.wh_code = 'GSHEET')
        WHERE lot IS NOT NULL
        GROUP BY po_number, sku_code, lot, partner, update_date
        ON CONFLICT DO NOTHING;
        """
    ),
    step(
        """
        INSERT INTO pimt.unified_inventory (
            wh_code, po_number, order_number, sku_code, lot_code, case_quantity,
            expiration_date, inventory_status, inventory_type, snapshot_timestamp
        )
        SELECT
            wh.code, po_number, substr(po_number, 1, 12), sku_code, lot_code, sum(quantity), min(expiration_date),
            CASE
                WHEN state = 0 THEN 'UNSPECIFIED'
                WHEN state = 1 THEN 'AVAILABLE'
                WHEN state = 2 THEN 'TOTAL'
                WHEN state = 3 THEN 'BOOKED_FOR_OUTBOUND'
                WHEN state = 4 THEN 'ON_HOLD'
            END,
            'E2OPEN', last_update_time
        FROM pimt.inventory_notification inventory
        JOIN pimt.warehouse wh on (wh.bob_code = inventory.bob_code)
        GROUP BY po_number, sku_code, lot_code, wh.code, last_update_time, state
        ON CONFLICT DO NOTHING;
        """
    ),
    step(
        """
        INSERT INTO pimt.unified_inventory (
            wh_code, po_number, order_number, sku_code, lot_code, unit_quantity,
            expiration_date, inventory_status, inventory_type, snapshot_timestamp
        )
        SELECT
            inventory.wh_code, po_number, substr(po_number, 1, 12), sku_code,
            lot_code, sum(pallet_qty), min(expiration_date),
            CASE
                WHEN status = 'A' THEN 'AVAILABLE'
                WHEN status = 'H' THEN 'ON_HOLD'
                ELSE 'UNSPECIFIED'
            END,
            'HJ', update_date
        FROM pimt.hj_inventory inventory
        JOIN pimt.inventory_update inventory_update ON (inventory_update.wh_code = inventory.wh_code)
        GROUP BY po_number, sku_code, lot_code, inventory.wh_code, status, update_date
        ON CONFLICT DO NOTHING;
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS ipimt_unified_inventory_snapshot_timestamp
        ON pimt.unified_inventory (snapshot_timestamp);
        CREATE INDEX IF NOT EXISTS ipimt_unified_inventory_wh_code_snapshot_timestamp
        ON pimt.unified_inventory (wh_code, snapshot_timestamp);
        """
    ),
]
