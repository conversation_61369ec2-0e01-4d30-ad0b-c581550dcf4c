from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS highjump.wip_consumption (
            sku_code TEXT,
            week INTEGER,
            wh_id TEXT,
            destination_location TEXT,
            tran_type TEXT,
            tran_date DATE,
            quantity NUMERIC,
            PRIMARY KEY (week, wh_id, sku_code, destination_location, tran_type, tran_date)
        );
        """,
    ),
]
