from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.site ADD COLUMN IF NOT EXISTS dc_azure_role VARCHAR(255);
        ALTER TABLE inventory.site ADD COLUMN IF NOT EXISTS "timezone" VARCHAR(255);
        """
    ),
    step(
        """
        UPDATE inventory.site
        SET dc_azure_role = subq.dc_azure_role
        FROM (
        SELECT DISTINCT (properties->>'dc_azure_role') AS dc_azure_role, site_id
        FROM inventory.weekly_config WHERE properties->>'dc_azure_role' IS NOT NULL) as subq
        WHERE id = subq.site_id;
        """
    ),
    step(
        """
        UPDATE inventory.weekly_config SET properties = properties - 'dc_azure_role' where properties ? 'dc_azure_role';
        """
    ),
    step(
        """
        UPDATE inventory.site
        SET "timezone" = site_properties->>'timezone';
        """
    ),
    step("""ALTER TABLE inventory.site DROP COLUMN site_properties;"""),
]
