from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DROP TABLE IF EXISTS inventory.supplier_portal;
        """
    ),
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id IN (
            SELECT id FROM procurement.gsheet_meta WHERE doc_code = 'supplier_portal'
        );
        DELETE FROM procurement.gsheet_meta WHERE doc_code = 'supplier_portal';
        """
    ),
]
