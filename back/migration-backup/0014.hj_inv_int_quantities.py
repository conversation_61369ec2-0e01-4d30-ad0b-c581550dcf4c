from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("ALTER TABLE highjump.v_procurement_export_inv ALTER quantity TYPE BIGINT;"),
    step("ALTER TABLE highjump.v_procurement_export_lps ALTER pallet_quantity TYPE BIGINT;"),
    step("ALTER TABLE highjump.latest_hj_pallet_snapshots ALTER pallet_quantity TYPE BIGINT;"),
    step(
        """
        ALTER TABLE highjump.v_procurement_export_receipts
            ALTER cases_received TYPE BIGINT,
            ALTER quantity_received TYPE BIGINT
        ;
        """
    ),
]
