from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.advance_shipping_notice (
            po_uuid character varying(50),
            order_number character varying(20),
            shipment_time TIMESTAMP without time zone,
            planned_delivery_time TIMESTAMP without time zone,
            sku_code character varying(20),
            shipping_state integer,
            size integer,
            packing_size integer,
            unit_measure integer,
            PRIMARY KEY (po_uuid, sku_code)
        );
        """,
    ),
]
