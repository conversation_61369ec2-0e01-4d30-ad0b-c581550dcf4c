from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.brand ADD "order" smallserial NOT NULL;

        CREATE TABLE inventory.brand_config (
            brand_id varchar(2) NOT NULL REFERENCES inventory.brand(id) ON DELETE CASCADE,
            "week" integer NOT NULL,
            enabled bool NOT NULL DEFAULT TRUE,
            consolidated bool NOT NULL DEFAULT TRUE,
            PRIMARY KEY (brand_id, "week")
        );

        INSERT INTO inventory.brand_config
            SELECT id, 202001
            FROM inventory.brand
        ;
        """
    ),
]
