from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_sku_mapping (
            original_sku_code varchar(20) NOT NULL,
            interchangable_sku_code varchar(20) NOT NULL,
            PRIMARY KEY (interchangable_sku_code, original_sku_code)
        );
        """
    ),
    step("""ALTER TABLE inventory.organic_sku RENAME TO organic_sku_mapping;"""),
    step(
        """
            UPDATE procurement.gsheet_meta
            SET doc_code = 'alternative_sku_mapping'
            WHERE doc_code = 'org_sku';
        """
    ),
    step(
        """
           ALTER TABLE inventory.organic_sku_mapping
           RENAME COLUMN conventional_sku TO interchangable_sku_code;
           ALTER TABLE inventory.organic_sku_mapping
           RENAME COLUMN organic_sku TO original_sku_code;
    """
    ),
]
