from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (
        name, doc_code, weekly, "order", title_template, brand, project, required, market)
        VALUES (
            'Consolidated Packaging Demand', 'consolidated_packaging_demand', False, 15,
            'Consolidated Packaging Demand (CA)', 'HF', 'imt', True, 'CA'
        );
        """,
    ),
    step(
        """
        ALTER TABLE inventory.packaging_sku_profile ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.packaging_sku_profile ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE inventory.packaging_sku_profile DROP CONSTRAINT packaging_sku_profile_pkey;
        ALTER TABLE inventory.packaging_sku_profile ADD CONSTRAINT
            packaging_sku_profile_pkey PRIMARY KEY (sku_code, market);
        """
    ),
    step(
        """
        ALTER TABLE inventory.packaging_demand ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE inventory.packaging_demand ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE inventory.packaging_demand DROP CONSTRAINT packaging_demand_pkey;
        ALTER TABLE inventory.packaging_demand ADD CONSTRAINT
            packaging_demand_pkey PRIMARY KEY (week, site, brand, sku_code, market);
        """
    ),
    step(
        """
        ALTER TABLE forecast.packaging_forecast ADD COLUMN market TEXT NOT NULL DEFAULT 'US';
        ALTER TABLE forecast.packaging_forecast ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE forecast.packaging_forecast DROP CONSTRAINT packaging_forecast_pkey;
        ALTER TABLE forecast.packaging_forecast ADD CONSTRAINT
            packaging_demand_pkey PRIMARY KEY (week, brand, site, sku_code, market);
        """
    ),
]
