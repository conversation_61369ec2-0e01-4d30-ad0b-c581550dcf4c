from migrations.scripts.permissions import create_permissions
from yoyo import step

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

permission = Permissions.IMT_PCK_DEPL_V2
role_mapping = {
    role: [permission.read, permission.write] for role in ["admin", "procurementleadership", "procurementmanager"]
}
role_mapping.update({role: [permission.read] for role in ["dcuser", "procurementuser"]})


steps = [
    *create_permissions(role_mapping),
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.packaging_override (
            "week" int NOT NULL,
            site varchar(4) NOT NULL,
            brand varchar(4) NOT NULL,
            sku_code varchar(20) NOT NULL,
            "day" date NOT NULL,
            on_hand_override int,
            incoming_override int,
            last_updated timestamp NOT NULL,
            updated_by int NOT NULL,

            PRIMARY KEY ("week", site, brand, sku_code, "day")
        );
        """
    ),
]
