from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DELETE  FROM procurement.permission_role WHERE permission_id IN (
        SELECT id from procurement.permission WHERE name in ('imt-cost-check-v1:r', 'imt-dashboard-ing-v1:r'));
        DELETE FROM procurement.permission where name IN ('imt-cost-check-v1:r', 'imt-dashboard-ing-v1:r');
        """,
    ),
]
