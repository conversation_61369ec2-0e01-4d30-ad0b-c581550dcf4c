from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE INDEX IF NOT EXISTS iordering_purchase_order_order_number ON ordering.purchase_order (order_number);
        CREATE INDEX IF NOT EXISTS ipimt_inventory_order_number ON pimt.inventory (order_number);
        CREATE INDEX IF NOT EXISTS ipimt_po_dummy_record_order_number ON pimt.po_dummy_record (order_number);
        """
    )
]
