from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.inventory_planner (
            sku_uuid TEXT,
            bob_code TEXT,
            day DATE,
            quantity INTEGER,
            PRIMARY KEY (bob_code, day, sku_uuid)
        );
        """
    ),
]
