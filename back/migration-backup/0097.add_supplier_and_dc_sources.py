from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS "ordering".supplier (
            id TEXT PRIMARY KEY,
            code int NOT NULL,
            market TEXT NOT NULL,
            name TEXT NOT NULL,
            legal_name TEXT NOT NULL,
            last_updated timestamp NOT NULL
        );
        CREATE UNIQUE INDEX IF NOT EXISTS iu_supplier_code_market ON "ordering".supplier (code, market);
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS procurement.distribution_center (
            id TEXT PRIMARY KEY,
            bob_code TEXT NOT NULL,
            name TEXT NOT NULL,
            enabled bool NOT NULL,
            tz TEXT NOT NULL,
            market TEXT NOT NULL,
            is_third_party bool NOT NULL,
            wms_type TEXT,
            parent_id TEXT,
            organization TEXT
        );
        CREATE UNIQUE INDEX IF NOT EXISTS iu_procurement_distibution_center_bob_code
            ON procurement.distribution_center (bob_code);
        """
    ),
]
