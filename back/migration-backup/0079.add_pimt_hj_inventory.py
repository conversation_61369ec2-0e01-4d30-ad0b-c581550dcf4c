from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE pimt.hj_inventory (
            wh_code text NOT NULL,
            po_number text NOT NULL,
            sku_code text NOT NULL,
            lot_code text NOT NULL,
            pallet_qty int NOT NULL,
            order_number text,
            expiration_date date,
            status text NOT NULL,
            CONSTRAINT pimt_hj_inventory_pkey PRIMARY KEY (wh_code, status, po_number, sku_code, lot_code)
        );

        CREATE INDEX ipimt_hj_inventory_order_number ON pimt.hj_inventory (order_number);
        CREATE INDEX ipimt_hj_inventory_sku_code ON pimt.hj_inventory (sku_code);
        CREATE INDEX ipimt_hj_inventory_status ON pimt.hj_inventory (status);

        CREATE INDEX iinventory_goods_receipt_note_order_number_sku
            ON inventory.goods_receipt_note (order_number, sku_code);

        ALTER TABLE pimt.inventory_update DROP id;
        ALTER TABLE pimt.inventory_update ADD wh_code text;
        UPDATE pimt.inventory_update SET wh_code = 'GSHEET';
        ALTER TABLE pimt.inventory_update ALTER wh_code SET NOT NULL;
        ALTER TABLE pimt.inventory_update ADD CONSTRAINT inventory_update_pkey PRIMARY KEY (wh_code);
        ALTER TABLE pimt.inventory_update ALTER update_date TYPE date;
        """
    ),
]
