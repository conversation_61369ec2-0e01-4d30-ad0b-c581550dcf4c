from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DELETE FROM highjump.v_procurement_export_receipts
        WHERE (po_number, week)
        IN (
            SELECT DISTINCT
            po_number, "week"
            FROM (
                SELECT po_number, "week", rank() OVER(PARTITION BY po_number ORDER BY "week" DESC) rnk
                FROM highjump.v_procurement_export_receipts vper
            ) t
            WHERE rnk > 1
        );
        """
    ),
    step(
        """
        ALTER TABLE highjump.v_procurement_export_receipts DROP "week";
        ALTER TABLE inventory.legacy_format_goods_receipt_note DROP "week";
        """
    ),
]
