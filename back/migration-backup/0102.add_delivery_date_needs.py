from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.delivery_date_needs (
            sku_code TEXT    NOT NULL,
            dc       TEXT    NOT NULL,
            scm_week TEXT    NOT NULL,
            date     date    NOT NULL,
            value    NUMERIC NOT NULL,
            brand    TEXT    NOT NULL,
            PRIMARY KEY (sku_code, dc, scm_week, date, brand)
        );

        CREATE INDEX IF NOT EXISTS delivery_date_needs_brand
            ON inventory.delivery_date_needs (brand);

        CREATE INDEX IF NOT EXISTS  iinventory_delivery_date_needs_date
            ON inventory.delivery_date_needs (date);

        CREATE INDEX IF NOT EXISTS  inventorydelivery_date_needs_dc
            ON inventory.delivery_date_needs (dc);

        CREATE INDEX IF NOT EXISTS  inventorydelivery_date_needs_scm_week
            ON inventory.delivery_date_needs (scm_week);
        """
    ),
]
