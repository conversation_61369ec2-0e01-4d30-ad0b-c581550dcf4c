from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        DROP TABLE IF EXISTS procurement.ordering_tool_gsheet;
        """
    ),
    step(
        """
        DELETE FROM procurement.permission_role WHERE permission_id IN (
            SELECT id FROM procurement.permission WHERE name IN (
            'ot-gsheet-v1:r', 'ot-gsheet-v1:w', 'ot-gsheet-v2:r', 'ot-gsheet-v2:w',
            'ot-gsheet-job-v1:r', 'ot-gsheet-job-v1:w'
            )
        );
        DELETE FROM procurement.permission WHERE name IN (
            'ot-gsheet-v1:r', 'ot-gsheet-v1:w', 'ot-gsheet-v2:r', 'ot-gsheet-v2:w',
            'ot-gsheet-job-v1:r', 'ot-gsheet-job-v1:w'
        );
    """
    ),
]
