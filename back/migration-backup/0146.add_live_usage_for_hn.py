from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_live_usage (
            sku_code TEXT NOT NULL,
            site TEXT NOT NULL,
            brand TEXT NOT NULL,
            scm_week INTEGER NOT NULL,
            day DATE NOT NULL,
            value NUMERIC NOT NULL,
            PRIMARY KEY (brand, scm_week, site, sku_code, day)
        );
        """,
    ),
]
