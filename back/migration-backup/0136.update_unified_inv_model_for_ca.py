from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        ALTER TABLE inventory.unified_inventory
        ADD COLUMN supplier text,
        ADD COLUMN case_size int,
        ALTER COLUMN po_number DROP NOT NULL,
        ALTER COLUMN order_number DROP NOT NULL;
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta (name, doc_code, weekly, "order", title_template, brand, project,
                                             market, required)
        VALUES
            ('PIMT - ALL 3PWs - Grid Dynamics Data Feed', 'pimt', False, 2000,
            'CAN PIMT - ALL 3PWs - Grid Dynamics Data Feed', NULL, 'pimt', 'CA', False);
        """
    ),
]
