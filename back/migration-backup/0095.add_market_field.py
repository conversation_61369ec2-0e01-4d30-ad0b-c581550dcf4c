from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.buyer_sku ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;
        ALTER TABLE inventory.buyer_sku ALTER COLUMN market DROP DEFAULT;
        DROP INDEX IF EXISTS buyer_sku_brand_site_index;
        ALTER TABLE inventory.buyer_sku
            DROP CONSTRAINT IF EXISTS buyer_sku_pkey,
            ADD CONSTRAINT buyer_sku_pkey PRIMARY KEY (market, site, brand, sku_code);
        CREATE INDEX IF NOT EXISTS buyer_sku_brand_site_index ON inventory.buyer_sku USING btree (brand, site, market);
        """
    ),
    step("""DROP TABLE inventory.ingredient_category;"""),
    step(
        """
        ALTER TABLE inventory.ingredient_site_subcategory ADD COLUMN market TEXT DEFAULT 'US' NOT NULL;
        ALTER TABLE inventory.ingredient_site_subcategory ALTER COLUMN market DROP DEFAULT;
        ALTER TABLE inventory.ingredient_site_subcategory
            DROP CONSTRAINT IF EXISTS ingredient_site_commodity_group_pkey,
            DROP CONSTRAINT IF EXISTS ingredient_site_subcategory_pkey,
            ADD CONSTRAINT ingredient_site_subcategory_pkey PRIMARY KEY (market, site, sku_code);
        """
    ),
]
