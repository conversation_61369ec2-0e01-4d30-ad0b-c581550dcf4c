from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients_shift_level (
            sku_code character varying(40) NOT NULL,
            dc character varying(4) NOT NULL,
            scm_week character varying(10) NOT NULL,
            date date NOT NULL,
            value_day integer NOT NULL,
            value_night integer NOT NULL,
            value_third integer NOT NULL,
            brand character varying(4) NOT NULL,
            PRIMARY KEY (date, scm_week, dc, sku_code, brand)
        );
        """
    ),
]
