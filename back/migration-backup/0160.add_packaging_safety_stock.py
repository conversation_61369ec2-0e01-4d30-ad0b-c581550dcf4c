from migrations.scripts.permissions import create_permissions
from yoyo import step

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        ALTER TABLE forecast.packaging_long_term_forecast_mapping RENAME TO demand_pipeline_sku_mapping;
        """
    ),
    create_permissions(
        {
            role: [Permissions.PIMT_PACKAGING_SAFETY_STOCK.read]
            for role in ["admin", "procurementleadership", "procurementmanager", "procurementuser"]
        }
    ),
]
