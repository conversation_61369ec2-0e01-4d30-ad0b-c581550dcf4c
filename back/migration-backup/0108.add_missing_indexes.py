from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(  # 0
        """
        ALTER TABLE procurement.inventory_module_comment
            DROP CONSTRAINT inventory_module_comment_pkey,
            ADD CONSTRAINT inventory_module_comment_pkey PRIMARY KEY (site, brand, sku_code);
        """
    ),
    step(  # 1
        """
        ALTER TABLE procurement.inventory_module_in_house_adjustment
            DROP CONSTRAINT IF EXISTS inventory_module_in_house_adjustment_pkey,
            ADD CONSTRAINT inventory_module_in_house_adjustment_pkey PRIMARY KEY (site, brand, sku_code);
        """
    ),
    step(  # 2
        """
        DROP INDEX IF EXISTS highjump.v_procurement_export_receipts_po_number;
        DROP INDEX IF EXISTS highjump.hjreceipts_wh_id;
        CREATE INDEX IF NOT EXISTS i_hj_receipts_wh_id_receipt_ts
            ON highjump.v_procurement_export_receipts (wh_id, receipt_time_est);
        """
    ),
    step(  # 3
        """
        DROP INDEX IF EXISTS highjump.v_procurement_export_inv_day_index;
        DROP INDEX IF EXISTS highjump.v_procurement_export_inv_wh_index;
        CREATE INDEX IF NOT EXISTS i_v_procurement_export_inv_wh_id_date
            ON highjump.v_procurement_export_inv (wh_id, "date");
        """
    ),
    step(  # 4
        """
        CREATE INDEX IF NOT EXISTS i_buyer_sku_buyer_id ON inventory.buyer_sku (user_id);
        """
    ),
    step(  # 5
        """
        CREATE INDEX IF NOT EXISTS i_cycle_counts_site_brand_date
            ON inventory.cycle_counts (site, brand, cycle_count_day);
        """
    ),
    step(  # 6
        """
        CREATE INDEX IF NOT EXISTS i_discards_week_dc_brand ON inventory."discard" ("week", dc, brand);
        CREATE INDEX IF NOT EXISTS i_discards_deleted_ts ON inventory."discard" (deleted_ts);
        DROP INDEX IF EXISTS inventory.inventorydiscard_week;
        DROP INDEX IF EXISTS inventory.inventorydiscard_dc;
        DROP INDEX IF EXISTS inventory.discard_brand;
        """
    ),
    step(  # 7
        """
        DROP INDEX IF EXISTS inventory.inventoryhj_override_cre_tmst;
        CREATE INDEX IF NOT EXISTS i_hj_override_upd_tmst ON inventory.hj_override (upd_tmst);
        """
    ),
    step(  # 8
        """
        ALTER TABLE inventory.hybrid_needs_ingredients
            DROP CONSTRAINT IF EXISTS hybrid_needs_ingredients_pkey,
            ADD CONSTRAINT hybrid_needs_ingredients_pkey PRIMARY KEY (dc, brand, scm_week, sku_code, "date");
        DROP INDEX IF EXISTS inventory.hybrid_needs_brand;
        DROP INDEX IF EXISTS inventory.inventoryhybrid_needs_ingredients_dc;
        DROP INDEX IF EXISTS inventory.inventoryhybrid_needs_ingredients_scm_week;
        """
    ),
    step(  # 9
        """
        ALTER TABLE inventory.hybrid_needs_ingredients_shift_level
            DROP CONSTRAINT IF EXISTS hybrid_needs_ingredients_shift_level_pkey,
            ADD CONSTRAINT hybrid_needs_ingredients_shift_level_pkey PRIMARY KEY
                (dc, brand, scm_week, sku_code, "date");
        CREATE INDEX IF NOT EXISTS i_hn_shift_level_date ON inventory.hybrid_needs_ingredients_shift_level ("date");
        """
    ),
    step(  # 10
        """
        ALTER TABLE inventory.hybrid_needs_ingredients_status
            DROP CONSTRAINT IF EXISTS hybrid_needs_ingredients_status_pkey,
            ADD CONSTRAINT hybrid_needs_ingredients_status_pkey PRIMARY KEY (scm_week, site, brand, "day");
        """
    ),
    step(  # 11
        """
        ALTER TABLE inventory.weekly_snapshot
            DROP CONSTRAINT IF EXISTS weekly_snapshot_market_wh_code_sku_code_week_expiration_dat_key,
            ADD CONSTRAINT weekly_snapshot_market_wh_code_sku_code_week_expiration_dat_key UNIQUE NULLS NOT DISTINCT
                ("week", market, wh_code, sku_code, expiration_date);
        """
    ),
    step(  # 12
        """
        CREATE INDEX IF NOT EXISTS i_inventory_grn_update_ts ON inventory.goods_receipt_note (update_ts);
        """
    ),
    step(  # 13
        """
        CREATE INDEX IF NOT EXISTS i_highjump_latest_pallet_snapshot_wh_id
            ON highjump.latest_hj_pallet_snapshots (wh_id);
        """
    ),
]
