from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE inventory.inventory_snapshot (
            wh_code TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            snapshot_ts timestamp NOT NULL,
            expiration_date date NOT NULL,
            location_type TEXT NOT NULL,
            location_id TEXT NOT NULL,
            state TEXT NOT NULL,
            inventory_type TEXT NOT NULL,
            unit_quantity NUMERIC NOT NULL,

            PRIMARY KEY (
                snapshot_ts, wh_code, state, expiration_date, location_type, location_id, sku_code, inventory_type
            )
        );
        """
    ),
]
