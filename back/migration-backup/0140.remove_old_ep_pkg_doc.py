from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DELETE FROM procurement.gsheet_admin WHERE meta_id IN
        (SELECT id FROM procurement.gsheet_meta WHERE brand = 'EP' AND doc_code = 'pkg');
        DELETE FROM procurement.gsheet_meta WHERE brand = 'EP' AND doc_code = 'pkg';
        UPDATE procurement.gsheet_meta SET name = 'Production Kit Guide' where doc_code = 'pkg_v2';
        """
    )
]
