from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (
        name, doc_code, weekly, "order", title_template, brand, project, required, market)
        VALUES (
        'Production Kit Guide', 'pkg', True, 3000, '{scm_week} Factor IMT Remake Input',
        'FJ', 'imt', FALSE, 'US');
        """,
    ),
    step("""ALTER TABLE inventory.ingredient DROP COLUMN is_imt;"""),
    step("""ALTER TABLE inventory.mealkit ADD COLUMN sub_recipe text;"""),
]
