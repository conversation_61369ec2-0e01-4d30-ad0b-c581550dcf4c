from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.brand ADD COLUMN IF NOT EXISTS country_code text;
        """
    ),
    step(
        """
        UPDATE inventory.brand SET country_code = 'CG' WHERE id = 'GC';
        UPDATE inventory.brand SET country_code = 'US' WHERE id = 'HF';
        UPDATE inventory.brand SET country_code = 'ER' WHERE id = 'EP';
        UPDATE inventory.brand SET country_code = 'FJ' WHERE id = 'FJ';
        ALTER TABLE inventory.brand ALTER country_code SET NOT NULL;
        """
    ),
]
