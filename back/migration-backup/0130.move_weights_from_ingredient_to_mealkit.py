from yoyo.migrations import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE inventory.mealkit_ingredient ADD COLUMN weight_amount REAL DEFAULT NULL,
        ADD COLUMN weight_unit TEXT DEFAULT NULL;
        """
    ),
    step(
        """
        UPDATE inventory.mealkit_ingredient
        SET weight_amount=subquery.weight_amount,
            weight_unit=subquery.weight_unit
        FROM (SELECT weight_amount, weight_unit, sku_code
              FROM  inventory.ingredient) AS subquery
        WHERE inventory.mealkit_ingredient.sku_code = subquery.sku_code;
        """
    ),
]
