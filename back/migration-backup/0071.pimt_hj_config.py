from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        ALTER TABLE pimt.partner
            ADD receiving_type varchar(20),
            ADD inventory_type varchar(20),
            ADD hj_name varchar(20),
            ADD is_3rd_party BOOLEAN;

        UPDATE pimt.partner SET
            receiving_type = CASE WHEN e_2_open_grn THEN 'E2OPEN_GRN' ELSE 'N/A' END,
            inventory_type = CASE WHEN e_2_open_inventory THEN 'E2OPEN' ELSE 'GSHEET' END,
            is_3rd_party = TRUE;

        ALTER TABLE pimt.partner
            DROP e_2_open_grn,
            DROP e_2_open_inventory;

        ALTER TABLE pimt.partner
            ALTER receiving_type SET NOT NULL,
            ALTER inventory_type SET NOT NULL,
            ALTER is_3rd_party SET NOT NULL;
        """
    ),
    step(
        """
        ALTER TABLE pimt.partner RENAME TO warehouse;

        ALTER TABLE pimt.warehouse RENAME COLUMN ot_dc TO ot_dcs;
        ALTER TABLE pimt.warehouse RENAME COLUMN code_order TO "order";
        ALTER TABLE pimt.warehouse RENAME COLUMN partner_name TO "name";

        CREATE INDEX iwarehouse_receiving_type ON pimt.warehouse (receiving_type);
        CREATE INDEX iwarehouse_inventory_type ON pimt.warehouse (inventory_type);
        """
    ),
]
