import logging

from init.services import kafka as kafka_service
from procurement import listeners_registration
from procurement.core import config_utils, initialize
from procurement.core.kafka.listener import kafka_listener

logger = logging.getLogger(__name__)


if __name__ == "__main__":
    if config_utils.is_ci:
        kafka_service.init_kafka()
    initialize.init_all()
    logger.info("Starting Kafka consumer...")
    registry = listeners_registration.register_kafka_message_handlers()
    if not registry.is_empty():
        listeners_registration.register_redis_message_handlers()
        kafka_listener.register_registry(registry)
        logger.info("Listening to [%s]", ", ".join(registry.all_keys()))
        try:
            kafka_listener.run(parallel=False)
        except KeyboardInterrupt:
            kafka_listener.close()
    else:
        logger.info("Registry is empty, nothing to listen")
    logger.info("Exiting Kafka consumer...")
