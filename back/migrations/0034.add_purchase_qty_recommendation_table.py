from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.purchase_qty_recommendation (
            quantity NUMERIC NOT NULL,
            week INTEGER NOT NULL,
            site TEXT NOT NULL,
            sku_uuid TEXT NOT NULL,
            supplier_uuid TEXT NOT NULL,
            PRIMARY KEY (week, site, sku_uuid, supplier_uuid)
        );
        """
    ),
]
