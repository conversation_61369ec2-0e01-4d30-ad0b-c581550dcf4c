from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS pimt.replenishment_override (
        sku_code TEXT,
        region TEXT,
        value numeric,
        market TEXT,
        last_updated timestamp,
        updated_by TEXT,
        PRIMARY KEY (market, region, sku_code)
        );
        """
    ),
]
