from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        TRUNCATE TABLE "ordering".transfer_order_sku;
        CREATE UNIQUE INDEX IF NOT EXISTS ui_transfer_order_sku_po_sku_supplier
            ON "ordering".transfer_order_sku (po_uuid, sku_uuid, original_supplier_id)
            NULLS NOT DISTINCT;
        """
    ),
]
