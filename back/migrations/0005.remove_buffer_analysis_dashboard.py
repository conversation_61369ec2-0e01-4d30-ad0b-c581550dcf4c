from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
            DELETE FROM procurement.permission_role
                WHERE permission_id in (
                    SELECT id FROM procurement.permission
                    WHERE name in ('imt-analytics-buffer-analysis-v1:r', 'imt-analytics-buffer-analysis-v1:w')
            );
            DELETE FROM procurement.permission
                WHERE name in ('imt-analytics-buffer-analysis-v1:r', 'imt-analytics-buffer-analysis-v1:w');
        """
    ),
]
