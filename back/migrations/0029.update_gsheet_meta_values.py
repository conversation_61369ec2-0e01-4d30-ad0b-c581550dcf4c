from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step("""ALTER TABLE procurement.gsheet_meta ADD COLUMN IF NOT EXISTS visible BOOLEAN NOT NULL DEFAULT TRUE;"""),
    step("""UPDATE procurement.gsheet_meta SET visible = FALSE WHERE doc_code = 'pkg' AND brand = 'HF';"""),
    step(
        """UPDATE procurement.gsheet_meta SET name = 'Production Kit Guide' WHERE doc_code = 'pkg_v3' AND brand = 'HF';
"""
    ),
]
