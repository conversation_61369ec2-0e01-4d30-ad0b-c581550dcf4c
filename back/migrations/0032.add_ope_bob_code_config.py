from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        UPDATE inventory.weekly_config
        SET properties = jsonb_set(properties, '{ope_bob_code}', to_jsonb('AZ'::text))
        WHERE brand = 'EP' AND site = 'AZ' AND week >= 202430;

        UPDATE inventory.weekly_config
        SET properties = jsonb_set(properties, '{ope_bob_code}', to_jsonb('GL'::text))
        WHERE brand = 'EP' AND site = 'GJ' AND week >= 202501;
        """
    ),
]
