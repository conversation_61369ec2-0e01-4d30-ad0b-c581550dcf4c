from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE forecast.canada_forecast_recipe
            (sku_code TEXT,
            week INTEGER,
            site TEXT,
            brand TEXT,
            recipe TEXT,
            recipe_name TEXT,
            picks <PERSON><PERSON><PERSON><PERSON>,
            PRIMARY KEY ("week", "site", "brand", "sku_code", "recipe"));
        """
    ),
    step(
        """
        INSERT INTO forecast.canada_forecast_recipe (sku_code, week, site, brand, recipe)
        SELECT sku_code, week, site, brand, unnest(receipts) as recipe FROM forecast.canada_forecast_receipt;
        """
    ),
    step("""DROP TABLE forecast.canada_forecast_receipt;"""),
    step("ALTER TABLE inventory.legacy_format_goods_receipt_note ADD supplier_code TEXT;"),
]
