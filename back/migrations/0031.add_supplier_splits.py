from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.supplier_splits (
            dc text not null,
            sku_code text not null,
            lead_time integer not null,
            min_order_quantity integer not null,
            incremental_order_quantity integer not null,
            start_date date not null,
            end_date date not null,
            start_week integer not null,
            end_week integer not null,
            market text not null,
            primary key (dc, sku_code, start_week, end_week, market)
        );
        """
    ),
]
