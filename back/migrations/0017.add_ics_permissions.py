from migrations.scripts.permissions import create_permissions

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

new_permissions = ["ics-tickets-v1:r"]
roles = ["admin", "procurementuser", "procurementmanager", "procurementleadership", "helloconnect"]
role_mapping = {role: new_permissions for role in roles}

steps = [
    create_permissions(role_mapping),
]
