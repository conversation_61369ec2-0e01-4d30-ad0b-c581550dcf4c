from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        ALTER TABLE "ordering".purchase_order ADD source_bob_code TEXT;
        ALTER TABLE "ordering".purchase_order ALTER supplier_code DROP NOT NULL;
        ALTER TABLE "ordering".purchase_order ALTER order_number DROP EXPRESSION;
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS "ordering".transfer_order_sku (
            id serial PRIMARY KEY,
            po_uuid TEXT NOT NULL,
            sku_uuid TEXT NOT NULL,
            order_size INTEGER NOT NULL,
            order_unit TEXT NOT NULL,
            case_price NUMERIC NOT NULL,
            case_size NUMERIC NOT NULL,
            case_unit TEXT NOT NULL,
            quantity NUMERIC NOT NULL,
            total_price NUMERIC NOT NULL,
            original_po_number TEXT,
            original_supplier_id TEXT,
            original_lot_code TEXT,
            CONSTRAINT transfer_order_sku_po_uuid_fkey
                FOREIGN KEY (po_uuid) REFERENCES "ordering".purchase_order(po_uuid) ON DELETE CASCADE
        );
        CREATE UNIQUE INDEX IF NOT EXISTS ui_transfer_order_sku_unique_key
            ON "ordering".transfer_order_sku (
                po_uuid, sku_uuid, original_po_number, original_supplier_id, original_lot_code
            )
            NULLS NOT DISTINCT;
        """
    ),
]
