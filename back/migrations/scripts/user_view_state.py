import json
from typing import Any, Iterable, List

from procurement.core.migration import base


class UserViewState(base.SqlData):
    def __init__(self, resource_to_map: dict, _id: int = None, resource: str = None, value: dict = None):
        self.resource_to_map = resource_to_map
        self.id = _id
        self.resource = resource
        self.value = value

    def _get_select_query(self, **kwargs) -> str:
        return (
            f"SELECT id, resource, value FROM procurement.user_view_state "
            f"where resource IN {tuple(self.resource_to_map.keys())};"
        )

    def _parse_sql(self, row: Iterable[Any], **kwargs) -> "UserViewState":
        _id, resource, value = row
        return UserViewState(_id=_id, resource=resource, value=json.loads(value), resource_to_map=self.resource_to_map)

    def to_sql(self, **kwargs) -> str:
        return f"UPDATE procurement.user_view_state SET value='{json.dumps(self.value)}' where id={self.id};\n"

    def transform(self, **kwargs) -> "UserViewState":
        mapping = self.resource_to_map[self.resource]
        if isinstance(self.value, str):
            self.value = json.loads(self.value)
        self.value["colState"] = self._map_cols(cols=self.value["colState"], mapping=mapping)
        self.value["filterState"] = self._map_filters(cols=self.value["filterState"], mapping=mapping)
        return self

    @staticmethod
    def _map_cols(cols: List[dict], mapping: dict) -> List[dict]:
        new_value = []

        for item in cols:
            if (col_id := item["colId"]) in mapping:
                new_key = mapping[col_id]
            else:
                new_key = col_id
            item["colId"] = new_key
            new_value.append(item)
        return new_value

    @staticmethod
    def _map_filters(cols: dict, mapping: dict) -> dict:
        new_value = {}
        for key, value in cols.items():
            if key in mapping:
                new_key = mapping[key]
            else:
                new_key = key
            new_value[new_key] = value
        return new_value
