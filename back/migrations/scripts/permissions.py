from typing import Any, Dict, List

from yoyo import step

Step = Any


def create_permissions(role_mapping: Dict[str, List[str]]) -> List[Step]:
    """
    Parameters
    ----------
    role_mapping : Dict
        key : str
        The key of this dictionary is the role to which you want to bind permissions
        value: List
            Each item in the list is a string or the name of the permission that you want to add to a specific role
    Examples of parameters
    --------
    role_mapping = {
        "admin": [
            "permission_1",
            "permission_2",
        ],
        "scrubber": [
            "permission_1",
            "permission_2",
        ]
    }
    """
    queries = []

    for role, permissions in role_mapping.items():
        for permission in permissions:
            queries.append(
                f"((SELECT id from procurement.role where short_name='{role}'),"
                f"(SELECT id from procurement.permission where name='{permission}'))"
            )

    query = ",".join(queries)

    permissions = [f"('{permission}')" for permission in role_mapping["admin"]]

    permissions_query = ",".join(permissions)

    delete_permission_string = [
        f"DELETE FROM procurement.permission WHERE name='{permission}';" for permission in role_mapping["admin"]
    ]

    delete_permission_string_query = " ".join(delete_permission_string)

    delete_string = [
        f"DELETE FROM procurement.permission_role "
        f"WHERE permission_id=(SELECT id from procurement.permission where name='{permission}');"
        for permission in role_mapping["admin"]
    ]

    delete_string_query = " ".join(delete_string)

    return [
        step(
            f"""
            INSERT INTO procurement.permission ("name")
            VALUES {permissions_query} ON CONFLICT DO NOTHING
            """,
            delete_permission_string_query,
        ),
        step(
            f"""
                INSERT INTO procurement.permission_role (role_id, permission_id)
                VALUES
                   {query} ON CONFLICT DO NOTHING;
            """,
            delete_string_query,
        ),
    ]


def grant_permissions(role_mapping: Dict[str, List[str]]) -> Step:
    apply_pairs, rollback_queries = [], []
    for role, permissions in role_mapping.items():
        for permission in permissions:
            apply_pairs.append(
                f"((SELECT id from procurement.role where short_name = '{role}'),"
                f"(SELECT id from procurement.permission where name = '{permission}'))"
            )
            rollback_queries.append(
                f"DELETE FROM procurement.permission_role "
                f"WHERE permission_id = (SELECT id from procurement.permission where name = '{permission}') "
                f"AND role_id = (SELECT id from procurement.role where short_name = '{role}');"
            )

    apply_data = ",".join(apply_pairs)
    rollback_queries = " ".join(rollback_queries)

    return step(
        f"""
            INSERT INTO procurement.permission_role (role_id, permission_id)
            VALUES
               {apply_data} ON CONFLICT DO NOTHING;
        """,
        rollback_queries,
    )
