from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS inventory.network_depletion_comment (
            sku_code text,
            resource_id text,
            week INT,
            market text,
            "text" text,
            last_updated timestamp,
            updated_by text,
            PRIMARY KEY (market, week, resource_id, sku_code)
        );
        """
    )
]
