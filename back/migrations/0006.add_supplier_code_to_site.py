from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        UPDATE inventory.weekly_config
        SET properties = jsonb_set(properties, '{supplier_codes}', '[]'::jsonb, true)
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS idx_supplier_name_market ON ordering.supplier (name, market);
        """
    ),
]
