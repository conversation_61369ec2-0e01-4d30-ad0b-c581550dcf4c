from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DROP INDEX IF EXISTS "ordering".ui_transfer_order_sku_unique_key;
        -- data is not live yet so it's ok to truncate it
        TRUNCATE TABLE "ordering".transfer_order_sku;
        ALTER TABLE "ordering".transfer_order_sku DROP id;
        ALTER TABLE "ordering".transfer_order_sku ADD line_item_uuid TEXT NOT NULL;
        ALTER TABLE "ordering".transfer_order_sku ADD PRIMARY KEY (po_uuid, line_item_uuid);
        """
    ),
]
