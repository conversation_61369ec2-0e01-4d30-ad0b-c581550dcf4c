from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.manufactured_sku_week (
            manufactured_uuid TEXT NOT NULL,
            week INTEGER NOT NULL,
            PRIMARY KEY (manufactured_uuid, week)
        );
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS i_manufactured_sku_week__week
        ON ordering.manufactured_sku_week (week);
        """
    ),
]
