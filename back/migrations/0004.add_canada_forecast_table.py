from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS forecast.canada_forecast(
            sku_code text,
            site text,
            scm_week integer,
            day date,
            value numeric,
            brand text,
            PRIMARY KEY ("site", "brand", "scm_week", "sku_code", "day")
        );
        """
    ),
    step(
        """
        DELETE FROM forecast.oscar where market = 'CA';
        ALTER TABLE forecast.oscar
            DROP CONSTRAINT IF EXISTS oscar_pkey,
            ADD CONSTRAINT oscar_pkey PRIMARY KEY (dc, scm_week, sku_code, brand);
        ALTER TABLE forecast.oscar DROP COLUMN IF EXISTS market;
        """
    ),
]
