from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.staged_inventory (
            sub_recipe_id TEXT,
            week int,
            site TEXT,
            quantity int,
            PRIMARY KEY (week, site, sub_recipe_id)
        );
        """
    ),
    step(
        """INSERT INTO procurement.gsheet_meta
            (name, doc_code, weekly, "order", title_template, brand, required, project, market)
        VALUES ('Staged Inventory', 'staged_inventory', FALSE, 20, 'Factor Staged Inventory',
            'FJ', FALSE, 'imt', 'US')"""
    ),
]
