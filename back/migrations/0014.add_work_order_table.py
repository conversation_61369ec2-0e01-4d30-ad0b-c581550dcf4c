from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step("""DROP TABLE IF EXISTS inventory.work_order;"""),
    step(
        """
        CREATE TABLE inventory.work_order (
            wo_uuid text,
            menu_week int,
            high_jump_warehouse_id text,
            recipe_sku text,
            sku_code text,
            quantity numeric,
            PRIMARY KEY (wo_uuid, sku_code)
        );
        """
    ),
]
