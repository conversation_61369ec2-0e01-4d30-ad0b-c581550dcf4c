from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE "ordering".topo_sku (
            site TEXT NOT NULL,
            sku_code TEXT NOT NULL,
            PRIMARY KEY (site, sku_code)
        );
        """
    ),
    step(
        """
        INSERT INTO procurement.gsheet_meta
        (name, doc_code, weekly, "order", title_template, brand, required, project, market)
        VALUES ('US TOPO SKUs', 'topo_sku', FALSE, 33, 'US TOPO SKUs', 'HF', FALSE, 'imt', 'US')"""
    ),
]
