from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.manufactured_sku (
            manufactured_uuid TEXT,
            manufactured_code TEXT NOT NULL,
            manufactured_name TEXT NOT NULL,
            market TEXT NOT NULL,
            PRIMARY KEY (manufactured_uuid)
        );
        """
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS ordering.manufactured_sku_part (
            root_uuid TEXT,
            part_uuid TEXT,
            unit_of_measure TEXT NOT NULL ,
            quantity NUMERIC,
            is_manufactured BOOLEAN,
            PRIMARY KEY (root_uuid, part_uuid)
        );
        """
    ),
]
