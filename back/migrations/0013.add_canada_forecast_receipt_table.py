from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS forecast.canada_forecast_receipt (
            sku_code text,
            week int,
            site text,
            brand text,
            receipts text[],
            PRIMARY KEY (brand, week, site, sku_code )
        );
        """
    ),
    step(
        """
        INSERT INTO forecast.canada_forecast_receipt (sku_code, week, site, brand, receipts)
        SELECT culinary_sku.sku_code, week, subquery.site, subquery.brand, receipts
        FROM inventory.inventory_planner_receipt
        JOIN ordering.culinary_sku ON inventory_planner_receipt.sku_uuid = culinary_sku.sku_uuid
        JOIN (
            SELECT MAX(site) AS site, MAX(brand) AS brand, properties ->>'bob_code' AS bob_code
            FROM inventory.weekly_config
            GROUP BY bob_code) subquery
        ON inventory_planner_receipt.bob_code = subquery.bob_code;
        """
    ),
    step(
        """
        DROP TABLE IF EXISTS inventory.inventory_planner_receipt;
        DROP TABLE IF EXISTS inventory.inventory_planner;
        """
    ),
]
