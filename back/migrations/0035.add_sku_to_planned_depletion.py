from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        DROP TABLE IF EXISTS pimt.planned_depletion;
        CREATE TABLE pimt.planned_depletion (
            quantity NUMERIC NOT NULL,
            sku_code TEXT NOT NULL,
            week INTEGER NOT NULL,
            demand_pipeline TEXT NOT NULL,
            region TEXT NOT NULL,
            owner TEXT NOT NULL,
            market TEXT NOT NULL,
            PRIMARY KEY (week, region, market, demand_pipeline, sku_code, owner)
        );
        """
    ),
]
