from migrations.scripts.permissions import create_permissions
from yoyo import step

from procurement.auth.permissions import Permissions
from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS pimt.replenishment_comment (
            sku_code text,
            region text,
            market text,
            "text" text,
            last_updated timestamp,
            updated_by text,
            PRIMARY KEY (market, region, sku_code )
        );
        """
    ),
    create_permissions(
        {
            role: [Permissions.PIMT_REPLENISHMENT_V1.write]
            for role in ["admin", "procurementmanager", "procurementleadership", "procurementuser", "scrubber"]
        }
    ),
]
