from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        ALTER TABLE inventory.unified_inventory ADD snapshot_id uuid;
        ALTER TABLE inventory.unified_inventory ADD imt_update_ts timestamp;
        """
    ),
    step(
        """
        CREATE INDEX IF NOT EXISTS i_unified_inventory_snapshot_id ON inventory.unified_inventory (snapshot_id);
        """
    ),
]
