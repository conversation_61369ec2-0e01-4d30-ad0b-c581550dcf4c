from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS "inventory".ics_tickets (
            market TEXT NOT NULL,
            week INTEGER NOT NULL,
            bob_code TEXT NOT NULL,
            sku_code TEXT,
            po_number TEXT,
            subject TEXT,
            ticket_link TEXT,
            priority INTEGER,
            request_type TEXT,
            ticket_id INTEGER,
            status INTEGER,
            updated_at TIMESTAMP,

            PRIMARY KEY ("market", "week", "bob_code", "ticket_id")
        )
        """
    ),
]
