from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS pimt.planned_depletion (
            quantity NUMERIC NOT NULL,
            week INTEGER NOT NULL,
            demand_pipeline TEXT NOT NULL,
            region TEXT NOT NULL,
            is_hf_managed BOOLEAN NOT NULL,
            market TEXT NOT NULL,
            PRIMARY KEY (week, region, demand_pipeline, market, is_hf_managed)
        );
        """
    ),
]
