from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)

steps = [
    step(
        """
        INSERT INTO procurement.gsheet_meta (name, doc_code, weekly, "order", title_template, brand,
        required, project, market)
        VALUES
        ('New Production Kit Guide', 'pkg_v3', true, 3,
         'Production Kit Guide - {scm_week}', 'HF', false, 'imt', 'US'
        );
        """,
    ),
    step(
        """
        ALTER TABLE inventory.mealkit_ingredient ADD COLUMN picks_3p INTEGER NOT NULL DEFAULT 0;
        """
    ),
]
