from migrations.scripts import permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    # Drop old migrations history
    step(
        """
        DELETE FROM _yoyo_log;
        DELETE FROM _yoyo_migration;
        """
    ),
    # Create Schemas
    step(
        """
        CREATE SCHEMA IF NOT EXISTS forecast;
        CREATE SCHEMA IF NOT EXISTS highjump;
        CREATE SCHEMA IF NOT EXISTS inventory;
        CREATE SCHEMA IF NOT EXISTS ordering;
        CREATE SCHEMA IF NOT EXISTS pimt;
        CREATE SCHEMA IF NOT EXISTS procurement;
        """
    ),
    step(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'checklist_status_enum') THEN
            CREATE TYPE procurement.checklist_status_enum AS ENUM
            (
                'Completed',
                'In Progress',
                'Delayed',
                'No Response',
                'Other'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'comment_type_enum') THEN
            CREATE TYPE procurement.comment_type_enum AS ENUM
            (
                'sku',
                'po',
                'wh_sku'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'day_of_week_enum') THEN
            CREATE TYPE procurement.day_of_week_enum AS ENUM
            (
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
                'Sunday'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'receiving_type_enum') THEN
            CREATE TYPE procurement.receiving_type_enum AS ENUM
            (
                'HighJump',
                'Manual'
            );
            END IF;
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sync_status_enum') THEN
            CREATE TYPE procurement.sync_status_enum AS ENUM
            (
                'in_progress',
                'success',
                'failed'
            );
            END IF;
        END$$;
        """
    ),
    step(
        """

        CREATE TABLE IF NOT EXISTS procurement.comment (
            id serial NOT NULL PRIMARY KEY,
            brand text NOT NULL,
            site text,
            resource_id text,
            comment text,
            week integer,
            last_updated timestamp without time zone,
            updated_by text,
            resource_type procurement.comment_type_enum NOT NULL,
            domain text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.distribution_center (
            id text NOT NULL PRIMARY KEY,
            bob_code text NOT NULL,
            name text NOT NULL,
            enabled boolean NOT NULL,
            tz text NOT NULL,
            market text NOT NULL,
            is_third_party boolean NOT NULL,
            wms_type text,
            parent_id text,
            organization text
        );

        CREATE TABLE IF NOT EXISTS procurement.gsheet_meta (
            id serial PRIMARY KEY,
            name text NOT NULL,
            doc_code text NOT NULL,
            weekly boolean NOT NULL,
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            "order" integer NOT NULL,
            title_template text NOT NULL,
            brand text,
            required boolean DEFAULT true,
            project text,
            market text NOT NULL,
            UNIQUE (doc_code, brand, market, project)
        );

        CREATE TABLE IF NOT EXISTS procurement.gsheet_admin (
            id serial PRIMARY KEY,
            meta_id integer NOT NULL,
            scm_week text,
            gsheet_id text NOT NULL,
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            user_id integer,
            CONSTRAINT gsheet_admin_meta_id_fkey FOREIGN KEY (meta_id) REFERENCES procurement.gsheet_meta(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.inventory_module_comment (
            sku_code text NOT NULL,
            brand text NOT NULL,
            site text NOT NULL,
            text text,
            last_edited_by integer NOT NULL,
            PRIMARY KEY (site, brand, sku_code)
        );

        CREATE TABLE IF NOT EXISTS procurement.inventory_module_in_house_adjustment (
            sku_code text NOT NULL,
            brand text NOT NULL,
            site text NOT NULL,
            in_house_adjustment integer,
            adjustment_date date,
            PRIMARY KEY (site, brand, sku_code)
        );

        CREATE TABLE IF NOT EXISTS procurement.network_depletion_preset (
            id integer NOT NULL,
            name text NOT NULL,
            user_id integer NOT NULL,
            configuration jsonb NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (user_id, id, market)
        );

        CREATE TABLE IF NOT EXISTS procurement.parallel_sync_log (
            id serial PRIMARY KEY,
            job_name text NOT NULL,
            parent_job_ids text[],
            sync_status text,
            warnings text,
            sync_time timestamp with time zone NOT NULL,
            global_parent_id bigint,
            market text,
            CONSTRAINT parallel_sync_log_global_parent_id_fkey FOREIGN KEY (global_parent_id)
            REFERENCES procurement.parallel_sync_log(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.role (
            id serial PRIMARY KEY,
            full_name text NOT NULL,
            short_name text NOT NULL,
            priority integer DEFAULT 100 NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.permission (
            id serial PRIMARY KEY,
            name text,
            UNIQUE (name)
        );


        CREATE TABLE IF NOT EXISTS procurement.permission_role (
            permission_id integer NOT NULL,
            role_id integer NOT NULL,
            PRIMARY KEY (permission_id, role_id),
            CONSTRAINT permission_role_permission_id_fkey FOREIGN KEY (permission_id)
            REFERENCES procurement.permission(id),
            CONSTRAINT permission_role_role_id_fkey FOREIGN KEY (role_id) REFERENCES procurement.role(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.release_log (
            feature_key integer NOT NULL,
            feature_type text NOT NULL,
            release_date date NOT NULL,
            dc_users_available boolean NOT NULL,
            description text NOT NULL,
            PRIMARY KEY (release_date, feature_key)
        );

        CREATE TABLE IF NOT EXISTS procurement.scrubbing_comment (
            id serial PRIMARY KEY,
            dc text NOT NULL,
            week integer NOT NULL,
            po_number text NOT NULL,
            sku_code text NOT NULL,
            comment text NOT NULL,
            last_updated timestamp without time zone NOT NULL,
            updated_by text NOT NULL,
            UNIQUE (po_number, sku_code)
        );

        CREATE TABLE IF NOT EXISTS procurement."user" (
            id serial PRIMARY KEY,
            first_name text,
            last_name text,
            email text NOT NULL,
            picture text,
            last_login timestamp without time zone,
            forced_out boolean NOT NULL
        );

        CREATE TABLE IF NOT EXISTS procurement.user_role (
            id serial PRIMARY KEY,
            user_id integer,
            role_id integer,
            market text NOT NULL,
            CONSTRAINT user_role_role_id_fkey FOREIGN KEY (role_id) REFERENCES procurement.role(id),
            CONSTRAINT user_role_user_id_fkey FOREIGN KEY (user_id) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS procurement.user_view_state (
            user_id integer NOT NULL,
            resource text NOT NULL,
            state text,
            id serial PRIMARY KEY,
            name text NOT NULL,
            last_usage timestamp without time zone DEFAULT now() NOT NULL
        );

        CREATE TABLE IF NOT EXISTS forecast.demand_pipeline_sku_mapping (
            sku_code text NOT NULL,
            sku_type text NOT NULL,
            market text NOT NULL,
            demand_pipeline text NOT NULL,
            PRIMARY KEY (sku_code, market)
        );

        CREATE TABLE IF NOT EXISTS forecast.mock_plan (
            week integer NOT NULL,
            site text NOT NULL,
            brand text NOT NULL,
            production_type text NOT NULL,
            weights numeric[],
            PRIMARY KEY (week, brand, site, production_type)
        );

        CREATE TABLE IF NOT EXISTS forecast.oscar (
            dc text NOT NULL,
            scm_week text NOT NULL,
            forecast numeric NOT NULL,
            brand text NOT NULL,
            forecast_by_day numeric[],
            units text DEFAULT 'unit'::text NOT NULL,
            sku_code text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (dc, scm_week, sku_code, brand, market)
        );

        CREATE TABLE IF NOT EXISTS forecast.packaging_forecast_bckp (
            week integer,
            site text,
            brand text,
            sku_code text,
            forecast integer,
            market text
        );

        CREATE TABLE IF NOT EXISTS forecast.packaging_long_term_forecast (
            week integer NOT NULL,
            brand text NOT NULL,
            site text NOT NULL,
            market text NOT NULL,
            forecast numeric,
            demand_pipeline text NOT NULL,
            PRIMARY KEY (week, brand, site, market, demand_pipeline)
        );

        CREATE TABLE IF NOT EXISTS highjump.latest_hj_pallet_snapshots (
            sku_code text,
            pallet_quantity bigint,
            wh_id text,
            sync_date date,
            expiration_date date,
            license_plate text
        );

        CREATE TABLE IF NOT EXISTS highjump.packaging_pallet_snapshot (
            snapshot_date date NOT NULL,
            wh_id text NOT NULL,
            sku_code text NOT NULL,
            pallet_quantity integer NOT NULL,
            PRIMARY KEY (snapshot_date, wh_id, sku_code)
        );

        CREATE TABLE IF NOT EXISTS highjump.v_highjump_discard (
            sku_code text,
            wh_id text,
            discard_date timestamp without time zone,
            quantity integer,
            tran_type text
        );

        CREATE TABLE IF NOT EXISTS highjump.v_highjump_wip (
            sku_code text NOT NULL,
            wh_id text NOT NULL,
            quantity integer,
            PRIMARY KEY (wh_id, sku_code)
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_inv (
            id serial PRIMARY KEY,
            wh_id text,
            quantity bigint,
            sku_code text,
            updated_ts timestamp without time zone,
            date date NOT NULL,
            expiration_date timestamp without time zone
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_lps (
            week text NOT NULL,
            wh_id text NOT NULL,
            sku_code text NOT NULL,
            pallet_quantity bigint NOT NULL,
            expiration_date date,
            unique_license_plate_count integer
        );

        CREATE TABLE IF NOT EXISTS highjump.v_procurement_export_receipts (
            wh_id text,
            supplier_name text,
            po_number text,
            cases_received bigint,
            quantity_received numeric,
            sku_code text,
            sku_name text,
            scm_week_raw text,
            status text,
            receipt_time_est timestamp without time zone,
            unit text,
            market text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS highjump.wip_consumption (
            sku_code text NOT NULL,
            week integer NOT NULL,
            wh_id text NOT NULL,
            destination_location text NOT NULL,
            tran_type text NOT NULL,
            tran_date date NOT NULL,
            quantity numeric,
            PRIMARY KEY (week, wh_id, sku_code, destination_location, tran_type, tran_date)
        );

        CREATE TABLE IF NOT EXISTS inventory.allocation_price (
            scm_week integer NOT NULL,
            brand text NOT NULL,
            site text NOT NULL,
            sku_code text NOT NULL,
            price numeric NOT NULL,
            PRIMARY KEY (scm_week, brand, site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.brand (
            id text NOT NULL,
            name text,
            "order" smallserial NOT NULL,
            consolidated boolean DEFAULT true NOT NULL,
            week_calendar_mon_shift integer NOT NULL,
            week_length integer NOT NULL,
            is_gil_source boolean DEFAULT true,
            country_code text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (id, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.brand_config (
            brand text NOT NULL,
            week integer NOT NULL,
            enabled boolean DEFAULT true NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (brand, market, week)
        );

        CREATE TABLE IF NOT EXISTS inventory.buffer_analysis_comment (
            market text,
            week integer,
            brand text,
            site text,
            sku_code text,
            comment text,
            last_edited_by text,
            UNIQUE (market, week, brand, site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.bulk_skus (
            bulk_sku_code text NOT NULL,
            packaged_sku_code text NOT NULL,
            brands text[] NOT NULL,
            pick_conversion integer,
            PRIMARY KEY (packaged_sku_code, bulk_sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.buyer_sku (
            user_id integer NOT NULL,
            sku_code text NOT NULL,
            site text NOT NULL,
            brand text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (market, site, brand, sku_code),
            CONSTRAINT buyer_sku_user_id_fkey FOREIGN KEY (user_id) REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.commodity_group (
            id serial PRIMARY KEY,
            group_name text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.cycle_counts (
            sku_code text NOT NULL,
            site text NOT NULL,
            cycle_count_day date NOT NULL,
            units integer,
            brand text NOT NULL,
            scm_week integer NOT NULL,
            date_of_count timestamp without time zone,
            PRIMARY KEY (scm_week, brand, site, sku_code, cycle_count_day)
        );

        CREATE TABLE IF NOT EXISTS inventory.delivery_date_needs (
            sku_code text NOT NULL,
            dc text NOT NULL,
            scm_week text NOT NULL,
            date date NOT NULL,
            value numeric NOT NULL,
            brand text NOT NULL,
            PRIMARY KEY (sku_code, dc, scm_week, date, brand)
        );

        CREATE TABLE IF NOT EXISTS inventory.discard (
            id serial PRIMARY KEY,
            "user" text,
            dc text NOT NULL,
            sku text NOT NULL,
            quantity integer NOT NULL,
            quality_instructions text,
            reason text,
            source text,
            week text,
            "timestamp" timestamp without time zone,
            discarded_datetime timestamp without time zone,
            brand text NOT NULL,
            comment text,
            deleted_by text,
            deleted_ts timestamp without time zone,
            updated_by text,
            market text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.goods_receipt_note (
            bob_code text NOT NULL,
            po_number text NOT NULL,
            order_number text NOT NULL,
            sku_code text NOT NULL,
            status smallint NOT NULL,
            week integer NOT NULL,
            units_received numeric,
            receipt_time_est timestamp with time zone,
            unit text,
            update_ts timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
            source text NOT NULL,
            cases_received numeric,
            PRIMARY KEY (po_number, sku_code, status)
        );

        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients (
            sku_code text NOT NULL,
            dc text NOT NULL,
            scm_week text NOT NULL,
            date date NOT NULL,
            value numeric NOT NULL,
            brand text NOT NULL,
            PRIMARY KEY (dc, brand, scm_week, sku_code, date)
        );

        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients_shift_level (
            sku_code text NOT NULL,
            dc text NOT NULL,
            scm_week text NOT NULL,
            date date NOT NULL,
            value_day numeric NOT NULL,
            value_night numeric NOT NULL,
            value_third numeric NOT NULL,
            brand text NOT NULL,
            PRIMARY KEY (dc, brand, scm_week, sku_code, date)
        );

        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_ingredients_status (
            site text NOT NULL,
            scm_week text NOT NULL,
            day date NOT NULL,
            brand text NOT NULL,
            status text NOT NULL,
            PRIMARY KEY (scm_week, site, brand, day)
        );

        CREATE TABLE IF NOT EXISTS inventory.hybrid_needs_live_usage (
            sku_code text NOT NULL,
            site text NOT NULL,
            brand text NOT NULL,
            scm_week integer NOT NULL,
            day date NOT NULL,
            value numeric NOT NULL,
            PRIMARY KEY (brand, scm_week, site, sku_code, day)
        );

        CREATE TABLE IF NOT EXISTS inventory.ingredient (
            sku_code text NOT NULL PRIMARY KEY,
            pack_size_amount real,
            pack_size_unit text,
            brand text NOT NULL,
            weight_amount real,
            weight_unit text,
            storage_location text,
            allergens text,
            is_receipt_bp_drop boolean DEFAULT false NOT NULL,
            is_type_media boolean DEFAULT false NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.ingredient_site_commodity_group (
            sku_code text NOT NULL,
            site text NOT NULL,
            commodity_group_id integer,
            market text NOT NULL,
            PRIMARY KEY (market, site, sku_code),
            CONSTRAINT ingredient_site_commodity_group_commodity_group_id_fkey FOREIGN KEY (commodity_group_id)
            REFERENCES inventory.commodity_group(id) ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS inventory.inventory_planner (
            sku_uuid text NOT NULL,
            bob_code text NOT NULL,
            day date NOT NULL,
            quantity integer,
            PRIMARY KEY (bob_code, day, sku_uuid)
        );

        CREATE TABLE IF NOT EXISTS inventory.inventory_planner_receipt (
            bob_code text NOT NULL,
            sku_uuid text NOT NULL,
            week integer NOT NULL,
            receipts text[] NOT NULL,
            PRIMARY KEY (bob_code, week, sku_uuid)
        );

        CREATE TABLE IF NOT EXISTS inventory.inventory_snapshot (
            wh_code text NOT NULL,
            sku_code text NOT NULL,
            snapshot_ts timestamp without time zone NOT NULL,
            expiration_date date,
            location_type text NOT NULL,
            location_id text NOT NULL,
            state text NOT NULL,
            inventory_type text NOT NULL,
            unit_quantity numeric NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.isp_bckp (
            id integer,
            sku_code text,
            dc_id text,
            cost numeric,
            week text,
            brand text
        );

        CREATE TABLE IF NOT EXISTS inventory.joliet_ope_skus (
            sku_code text NOT NULL PRIMARY KEY
        );

        CREATE TABLE IF NOT EXISTS inventory.legacy_format_goods_receipt_note (
            wh_id text,
            bob_code text,
            supplier_name text,
            po_number text NOT NULL,
            cases_received numeric,
            quantity_received numeric,
            sku_code text NOT NULL,
            sku_name text,
            scm_week_raw text,
            status text NOT NULL,
            receipt_time_est timestamp without time zone,
            unit text,
            update_ts timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (po_number, sku_code, status)
        );

        CREATE TABLE IF NOT EXISTS inventory.market (
            code text NOT NULL PRIMARY KEY,
            name text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.master_bulk_sku (
            bulk_sku_code text NOT NULL PRIMARY KEY
        );

        CREATE TABLE IF NOT EXISTS inventory.mealkit (
            id serial PRIMARY KEY,
            code text NOT NULL,
            slot text NOT NULL,
            meal_name text NOT NULL,
            scm_week text NOT NULL,
            brand text,
            sub_recipe text
        );

        CREATE TABLE IF NOT EXISTS inventory.mealkit_ingredient (
            id serial PRIMARY KEY,
            mealkit_id integer NOT NULL,
            sku_code text NOT NULL,
            picks_2p integer NOT NULL,
            picks_4p integer NOT NULL,
            dc_list text[] NOT NULL,
            is_in_kitbox boolean DEFAULT false NOT NULL,
            picks_6p integer NOT NULL,
            weight_amount real,
            weight_unit text,
            CONSTRAINT mealkit_ingredient_mealkit_id_fkey FOREIGN KEY (mealkit_id) REFERENCES inventory.mealkit(id)
            ON DELETE CASCADE,
            CONSTRAINT mealkit_ingredient_sku_code_fkey FOREIGN KEY (sku_code) REFERENCES inventory.ingredient(sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.organic_sku_mapping (
            interchangable_sku_code text NOT NULL,
            original_sku_code text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (interchangable_sku_code, original_sku_code, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_demand (
            week integer NOT NULL,
            site text NOT NULL,
            brand text NOT NULL,
            sku_code text NOT NULL,
            demand_by_day integer[],
            market text NOT NULL,
            PRIMARY KEY (week, site, brand, sku_code, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_override (
            week integer NOT NULL,
            site text NOT NULL,
            brand text NOT NULL,
            sku_code text NOT NULL,
            day date NOT NULL,
            on_hand_override integer,
            incoming_override integer,
            last_updated timestamp without time zone NOT NULL,
            updated_by integer NOT NULL,
            PRIMARY KEY (week, site, brand, sku_code, day)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_sku_import (
            bob_code text NOT NULL,
            sku_code text NOT NULL,
            units_per_truck_load numeric,
            PRIMARY KEY (bob_code, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_sku_mapping (
            original_sku_code text NOT NULL,
            interchangable_sku_code text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (interchangable_sku_code, original_sku_code, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.packaging_sku_profile (
            sku_code text NOT NULL,
            size text,
            type text,
            profile text,
            market text NOT NULL,
            PRIMARY KEY (sku_code, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.po_void (
            id serial PRIMARY KEY,
            "user" text NOT NULL,
            dc text NOT NULL,
            week text NOT NULL,
            brand text NOT NULL,
            po_number text NOT NULL,
            source text NOT NULL,
            comment text,
            cre_tmst timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
            sku_code text,
            supplier_name text,
            deleted_by text,
            deleted_ts timestamp without time zone,
            last_updated timestamp without time zone,
            market text NOT NULL,
            UNIQUE (week, brand, dc, po_number, sku_code, deleted_ts)
        );

        CREATE TABLE IF NOT EXISTS inventory.pull_put (
            id serial PRIMARY KEY,
            user_email text NOT NULL,
            dc text NOT NULL,
            brand text NOT NULL,
            week text NOT NULL,
            source text NOT NULL,
            sku_code text NOT NULL,
            qty integer NOT NULL,
            comment text,
            upd_tmst timestamp without time zone NOT NULL,
            cre_tmst timestamp without time zone NOT NULL,
            deleted_by text,
            deleted_ts timestamp without time zone,
            market text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.purchasing_category (
            id serial PRIMARY KEY,
            name text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.purchasing_subcategory (
            id serial PRIMARY KEY,
            name text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.receipt_override (
            "user" text NOT NULL,
            dc text NOT NULL,
            brand text NOT NULL,
            week text NOT NULL,
            source text NOT NULL,
            po_number text NOT NULL,
            sku_code text NOT NULL,
            qty integer NOT NULL,
            upd_tmst timestamp without time zone NOT NULL,
            cre_tmst timestamp without time zone NOT NULL,
            deleted_by text,
            deleted_ts timestamp without time zone,
            market text NOT NULL,
            cases integer,
            comment text,
            receiving_date date,
            PRIMARY KEY (week, brand, dc, po_number, sku_code)
        );

        CREATE TABLE IF NOT EXISTS inventory.receiving (
            id serial PRIMARY KEY,
            supplier text NOT NULL,
            po text NOT NULL,
            sku_code text NOT NULL,
            dc text NOT NULL,
            arrival_timestamp timestamp without time zone NOT NULL,
            receive_timestamp timestamp without time zone NOT NULL,
            delivery_status text NOT NULL,
            total_pallets_received integer,
            total_cases_received integer,
            case_count_one_total_units integer,
            case_count_two_total_units integer,
            case_count_three_total_units integer,
            receiver_comments text,
            ticket_number text,
            week text NOT NULL,
            username text NOT NULL,
            sku_ingredient text DEFAULT ''::text NOT NULL,
            supplier_id text DEFAULT ''::text NOT NULL,
            source text DEFAULT 'APP'::text NOT NULL,
            brand text NOT NULL,
            case_size_one numeric,
            case_size_two numeric,
            case_size_three numeric
        );

        CREATE TABLE IF NOT EXISTS inventory.remake_tool (
            meal text NOT NULL,
            picks_2p integer NOT NULL,
            picks_4p integer NOT NULL,
            week text NOT NULL,
            dc text NOT NULL,
            picks_6p integer NOT NULL,
            PRIMARY KEY (week, dc, meal)
        );

        CREATE TABLE IF NOT EXISTS inventory.site (
            id text NOT NULL,
            sequence_number integer,
            name text,
            created_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_tmst timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
            user_id integer,
            dc_azure_role text,
            timezone text,
            brand text NOT NULL,
            market text NOT NULL,
            PRIMARY KEY (id, brand, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.supplier_sku (
            supplier_sku_uuid text NOT NULL PRIMARY KEY,
            culinary_sku_uuid text NOT NULL,
            last_updated timestamp without time zone NOT NULL
        );

        CREATE TABLE IF NOT EXISTS inventory.top_variance_comment (
            market text,
            week integer,
            brand text,
            site text,
            sku_code text,
            po_number text,
            comment text,
            last_edited_by text,
            UNIQUE (market, week, brand, site, sku_code, po_number)
        );

        CREATE TABLE IF NOT EXISTS inventory.unified_inventory (
            wh_code text NOT NULL,
            po_number text,
            order_number text,
            sku_code text NOT NULL,
            lot_code text NOT NULL,
            case_quantity integer,
            unit_quantity integer,
            expiration_date date,
            inventory_status text NOT NULL,
            snapshot_timestamp timestamp without time zone NOT NULL,
            inventory_type text NOT NULL,
            supplier text,
            case_size integer,
            location_id text
        );

        CREATE TABLE IF NOT EXISTS inventory.vendor_managed_inventory (
            region text NOT NULL,
            sku_code text NOT NULL,
            market text NOT NULL,
            supplier_name text NOT NULL,
            units numeric,
            pallets numeric,
            PRIMARY KEY (sku_code, region, supplier_name, market)
        );

        CREATE TABLE IF NOT EXISTS inventory.weekend_coverage_checklist (
            id serial PRIMARY KEY,
            week integer,
            brand text,
            site text,
            po_number text,
            sku_code text,
            production_day_affected text,
            to_check text,
            contact_name_vendor_carrier text,
            email_phone text,
            back_up_vendor text,
            status text,
            comment text,
            updated_by_id integer,
            last_updated timestamp without time zone,
            po_landing_day text,
            fob_pick_up_date date,
            market text NOT NULL,
            CONSTRAINT weekend_coverage_checklist_updated_by_id_fkey FOREIGN KEY (updated_by_id)
            REFERENCES procurement."user"(id)
        );

        CREATE TABLE IF NOT EXISTS inventory.weekly_config (
            id serial PRIMARY KEY,
            site text,
            brand text,
            week integer,
            enabled boolean,
            properties jsonb,
            user_id integer,
            market text NOT NULL,
            UNIQUE (site, brand, market, week)
        );

        CREATE TABLE IF NOT EXISTS inventory.weekly_snapshot (
            market text NOT NULL,
            wh_code text NOT NULL,
            sku_code text NOT NULL,
            week integer NOT NULL,
            quantity numeric NOT NULL,
            expiration_date date,
            UNIQUE NULLS NOT DISTINCT (week, market, wh_code, sku_code, expiration_date)
        );

        CREATE TABLE IF NOT EXISTS inventory.work_order (
            wo_uuid text NOT NULL PRIMARY KEY,
            menu_week text NOT NULL,
            high_jump_warehouse_id text NOT NULL,
            recipe_code text NOT NULL,
            sub_recipe_code text NOT NULL,
            sub_recipe_name text NOT NULL,
            target_amount integer NOT NULL,
            scheduled_date date NOT NULL,
            scheduled_shift integer NOT NULL
        );

        CREATE TABLE IF NOT EXISTS ordering.advance_shipping_notice (
            po_uuid text NOT NULL,
            order_number text,
            shipment_time timestamp without time zone,
            planned_delivery_time timestamp without time zone,
            sku_code text NOT NULL,
            shipping_state integer,
            size integer,
            packing_size numeric,
            unit_measure integer,
            PRIMARY KEY (po_uuid, sku_code)
        );

        CREATE TABLE IF NOT EXISTS ordering.allowed_produce_buffer (
            brand text NOT NULL,
            site text NOT NULL,
            sku_code text NOT NULL,
            week integer NOT NULL,
            total_buffer numeric NOT NULL,
            PRIMARY KEY (brand, site, week, sku_code)
        );

        CREATE TABLE IF NOT EXISTS ordering.culinary_sku (
            sku_uuid text NOT NULL PRIMARY KEY,
            sku_code text,
            sku_name text,
            last_updated timestamp without time zone,
            status text,
            storage_location text,
            unit text,
            sku_id integer,
            brands text[],
            sku_type text,
            purchasing_category_id integer,
            market text NOT NULL,
            purchasing_subcategory_id integer
        );

        CREATE TABLE IF NOT EXISTS ordering.ordering_tool_dashboard (
            brand text NOT NULL,
            week text NOT NULL,
            gsheet_id text NOT NULL,
            PRIMARY KEY (brand, week)
        );

        CREATE TABLE IF NOT EXISTS ordering.po_acknowledgement_line_items (
            po_uuid text NOT NULL,
            sku_code text NOT NULL,
            order_number text,
            sku_id integer,
            state integer,
            number_of_pallets integer,
            unit_of_measure text,
            size integer,
            packing_size integer,
            promised_date timestamp without time zone,
            PRIMARY KEY (po_uuid, sku_code)
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order (
            po_uuid text NOT NULL PRIMARY KEY,
            po_number text NOT NULL,
            dc text,
            brand text NOT NULL,
            supplier_code integer NOT NULL,
            supplier text,
            ot_po_status text NOT NULL,
            is_sent boolean NOT NULL,
            week text NOT NULL,
            order_date timestamp without time zone NOT NULL,
            delivery_time_start timestamp without time zone NOT NULL,
            emergency_reason text,
            ordered_by text,
            ot_last_updated timestamp without time zone,
            deleted boolean DEFAULT false NOT NULL,
            internal_last_updated timestamp without time zone DEFAULT now(),
            shipping_method text,
            order_number text GENERATED ALWAYS AS (substr(po_number, 1, 12)) STORED,
            bob_code text NOT NULL
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order_shipment (
            po_number text NOT NULL PRIMARY KEY,
            load_number text,
            pallet_count integer,
            carrier_name text,
            region_code text,
            postal_code text,
            administrative_area text,
            locality text,
            address_lines text[],
            organization text,
            appointment_time timestamp without time zone,
            appointment_state integer,
            batch_sequence integer,
            execution_event text
        );

        CREATE TABLE IF NOT EXISTS ordering.purchase_order_sku (
            po_uuid text NOT NULL,
            sku_uuid text NOT NULL,
            order_size integer NOT NULL,
            order_unit text NOT NULL,
            case_price numeric NOT NULL,
            case_size numeric NOT NULL,
            case_unit text NOT NULL,
            quantity numeric NOT NULL,
            buffer numeric,
            total_price numeric NOT NULL,
            id serial PRIMARY KEY,
            UNIQUE (po_uuid, sku_uuid),
            CONSTRAINT purchase_order_sku_po_uuid_fkey FOREIGN KEY (po_uuid) REFERENCES ordering.purchase_order(po_uuid)
            ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS ordering.supplier (
            id text NOT NULL PRIMARY KEY,
            code integer NOT NULL,
            market text NOT NULL,
            name text NOT NULL,
            legal_name text NOT NULL,
            last_updated timestamp without time zone NOT NULL
        );

        CREATE TABLE IF NOT EXISTS pimt.exceptions_metrics (
            exception_date date NOT NULL,
            stats jsonb,
            market text NOT NULL,
            PRIMARY KEY (exception_date, market)
        );

        CREATE TABLE IF NOT EXISTS pimt.goods_receipt_note (
            sku_code text NOT NULL,
            order_number text NOT NULL,
            sku_id integer,
            delivery_time timestamp without time zone,
            bob_code text,
            units numeric,
            record_date date,
            PRIMARY KEY (order_number, sku_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.hj_inventory (
            wh_code text NOT NULL,
            po_number text NOT NULL,
            sku_code text NOT NULL,
            lot_code text NOT NULL,
            pallet_qty integer NOT NULL,
            order_number text,
            expiration_date date,
            status text NOT NULL,
            PRIMARY KEY (wh_code, status, po_number, sku_code, lot_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory (
            id serial PRIMARY KEY,
            sku_id integer NOT NULL,
            po_number text NOT NULL,
            qty integer,
            lot text,
            partner text NOT NULL,
            sku_code text,
            qty_available integer,
            expiration_date timestamp without time zone,
            order_number text GENERATED ALWAYS AS (substr(po_number, 1, 12)) STORED
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory_notification (
            po_number text NOT NULL,
            sku_code text NOT NULL,
            customer_id text,
            supplier_code text,
            bob_code text,
            quantity integer,
            expiration_date timestamp without time zone,
            case_size integer,
            pallet_count integer,
            lot_code text NOT NULL,
            last_update_time timestamp without time zone,
            state integer NOT NULL,
            grn_id text,
            order_number text,
            sku_id integer,
            PRIMARY KEY (po_number, sku_code, lot_code, state)
        );

        CREATE TABLE IF NOT EXISTS pimt.inventory_update (
            update_date date NOT NULL,
            wh_code text NOT NULL PRIMARY KEY
        );

        CREATE TABLE IF NOT EXISTS pimt.master_replenishment (
            site text NOT NULL,
            sku_code text NOT NULL,
            status text,
            replenishment_type text,
            shelf_life integer,
            max_supplier_lead_time integer,
            future_week_projections integer,
            market boolean,
            min_order_qty numeric,
            request_for_proposal numeric,
            market_factor numeric,
            replenishment_buyer text,
            PRIMARY KEY (site, sku_code)
        );

        CREATE TABLE IF NOT EXISTS pimt.packaging_info (
            supplier_sku_uuid text NOT NULL PRIMARY KEY,
            cases_per_pallet integer,
            units_per_pallet integer
        );

        CREATE TABLE IF NOT EXISTS pimt.warehouse (
            code text NOT NULL PRIMARY KEY,
            name text NOT NULL,
            ot_dcs text[] NOT NULL,
            ot_suppliers text[] NOT NULL,
            "order" integer NOT NULL,
            region text,
            regional_dcs text[],
            bob_code text,
            receiving_type text NOT NULL,
            inventory_type text NOT NULL,
            hj_name text,
            is_3rd_party boolean NOT NULL,
            market text NOT NULL,
            packaging_regions character varying[],
            UNIQUE (bob_code)
        );
        """
    ),
    # Create Indexes
    step(
        """
        CREATE INDEX IF NOT EXISTS iforecast_oscar_dc ON forecast.oscar USING btree (dc);

        CREATE INDEX IF NOT EXISTS iforecast_oscar_scm_week ON forecast.oscar USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS iforecast_oscar_sku_code ON forecast.oscar USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS hjreceipts_scm_week_raw ON highjump.v_procurement_export_receipts
        USING btree (scm_week_raw);

        CREATE INDEX IF NOT EXISTS i_highjump_latest_pallet_snapshot_wh_id ON highjump.latest_hj_pallet_snapshots
        USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS i_hj_receipts_wh_id_receipt_ts ON highjump.v_procurement_export_receipts
        USING btree (wh_id, receipt_time_est);

        CREATE INDEX IF NOT EXISTS i_v_procurement_export_inv_wh_id_date ON highjump.v_procurement_export_inv
        USING btree (wh_id, date);

        CREATE INDEX IF NOT EXISTS iv_hj_discard_discard_date_wh_id ON highjump.v_highjump_discard
        USING btree (discard_date, wh_id);

        CREATE UNIQUE INDEX IF NOT EXISTS ui_v_procurement_export_lps_key ON highjump.v_procurement_export_lps
        USING btree (week, wh_id, sku_code, expiration_date) NULLS NOT DISTINCT;

        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_po_number_sku_code
        ON highjump.v_procurement_export_receipts USING btree (po_number, sku_code);

        CREATE INDEX IF NOT EXISTS v_procurement_export_receipts_sku_code ON highjump.v_procurement_export_receipts
        USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS buyer_sku_brand_site_index ON inventory.buyer_sku USING btree (brand, site);

        CREATE UNIQUE INDEX IF NOT EXISTS commodity_group_group_name_uindex ON inventory.commodity_group
        USING btree (group_name);

        CREATE INDEX IF NOT EXISTS delivery_date_needs_brand ON inventory.delivery_date_needs USING btree (brand);

        CREATE INDEX IF NOT EXISTS i_buyer_sku_buyer_id ON inventory.buyer_sku USING btree (user_id);

        CREATE INDEX IF NOT EXISTS i_cycle_counts_date_of_count ON inventory.cycle_counts USING btree (date_of_count);

        CREATE INDEX IF NOT EXISTS i_cycle_counts_site_brand_date ON inventory.cycle_counts
        USING btree (site, brand, cycle_count_day);

        CREATE INDEX IF NOT EXISTS i_cycle_counts_sku ON inventory.cycle_counts USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_discards_deleted_ts ON inventory.discard USING btree (deleted_ts);

        CREATE INDEX IF NOT EXISTS i_discards_week_dc_brand ON inventory.discard USING btree (week, dc, brand);

        CREATE INDEX IF NOT EXISTS i_grn_receipt_time ON inventory.goods_receipt_note USING btree (receipt_time_est);

        CREATE INDEX IF NOT EXISTS i_hn_shift_level_date ON inventory.hybrid_needs_ingredients_shift_level
        USING btree (date);

        CREATE INDEX IF NOT EXISTS i_inventory_code_type_snapshot_timestamp ON inventory.unified_inventory
        USING btree (wh_code, inventory_type, snapshot_timestamp);

        CREATE INDEX IF NOT EXISTS i_inventory_discard_market ON inventory.discard USING btree (market);

        CREATE INDEX IF NOT EXISTS i_inventory_grn_update_ts ON inventory.goods_receipt_note USING btree (update_ts);

        CREATE INDEX IF NOT EXISTS i_inventory_legacy_format_goods_receipt_note_market
        ON inventory.legacy_format_goods_receipt_note USING btree (market);

        CREATE INDEX IF NOT EXISTS i_inventory_lpo_void_market ON inventory.po_void USING btree (market);

        CREATE INDEX IF NOT EXISTS i_inventory_pull_put ON inventory.pull_put USING btree (market);

        CREATE INDEX IF NOT EXISTS i_inventory_pull_put_deleted_ts ON inventory.pull_put USING btree (deleted_ts);

        CREATE INDEX IF NOT EXISTS i_inventory_pull_put_upd_tmst ON inventory.pull_put USING btree (upd_tmst);

        CREATE INDEX IF NOT EXISTS i_inventory_snapshot_sku ON inventory.inventory_snapshot USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_inventory_weekend_coverage_checklist_market ON inventory.weekend_coverage_checklist
        USING btree (market);

        CREATE INDEX IF NOT EXISTS i_pimt_unified_inventory_sku_code ON inventory.unified_inventory
        USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_receipt_override_brand ON inventory.receipt_override USING btree (brand);

        CREATE INDEX IF NOT EXISTS i_receipt_override_dc ON inventory.receipt_override USING btree (dc);

        CREATE INDEX IF NOT EXISTS i_receipt_override_deleted_ts ON inventory.receipt_override USING btree (deleted_ts);

        CREATE INDEX IF NOT EXISTS i_receipt_override_market ON inventory.receipt_override USING btree (market);

        CREATE INDEX IF NOT EXISTS i_receipt_override_po_number ON inventory.receipt_override USING btree (po_number);

        CREATE INDEX IF NOT EXISTS i_receipt_override_sku_code ON inventory.receipt_override USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_receipt_override_source ON inventory.receipt_override USING btree (source);

        CREATE INDEX IF NOT EXISTS i_receipt_override_upd_tmst ON inventory.receipt_override USING btree (upd_tmst);

        CREATE INDEX IF NOT EXISTS i_receipt_override_week ON inventory.receipt_override USING btree (week);

        CREATE UNIQUE INDEX IF NOT EXISTS i_unified_inventory_key ON inventory.unified_inventory
        USING btree (order_number, sku_code, wh_code, inventory_type, lot_code, location_id, inventory_status,
        expiration_date, snapshot_timestamp) NULLS NOT DISTINCT;

        CREATE INDEX IF NOT EXISTS i_weekly_config_brand_market_week ON inventory.weekly_config
        USING btree (brand, market, week);

        CREATE INDEX IF NOT EXISTS iinventory_delivery_date_needs_date ON inventory.delivery_date_needs
        USING btree (date);

        CREATE INDEX IF NOT EXISTS iinventory_goods_receipt_note_order_number_sku ON inventory.goods_receipt_note
        USING btree (order_number, sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_hybrid_needs_date ON inventory.hybrid_needs_ingredients
        USING btree (date);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_is_receipt_bp_drop ON inventory.ingredient
        USING btree (is_receipt_bp_drop) WHERE (is_receipt_bp_drop = false);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_is_type_media ON inventory.ingredient
        USING btree (is_type_media) WHERE (is_type_media = false);

        CREATE INDEX IF NOT EXISTS iinventory_ingredient_site_commodity_group_sku_id
        ON inventory.ingredient_site_commodity_group USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_po_void_deleted_ts ON inventory.po_void USING btree (deleted_ts);

        CREATE INDEX IF NOT EXISTS iinventory_po_void_sku_code ON inventory.po_void USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_receiving_receive_timestamp ON inventory.receiving
        USING btree (receive_timestamp);

        CREATE INDEX IF NOT EXISTS iinventory_snapshot_snapshot_ts ON inventory.inventory_snapshot
        USING btree (snapshot_ts);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_brand ON inventory.weekend_coverage_checklist
        USING btree (brand);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_po_sku ON inventory.weekend_coverage_checklist
        USING btree (po_number, sku_code);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_site ON inventory.weekend_coverage_checklist
        USING btree (site);

        CREATE INDEX IF NOT EXISTS iinventory_weekend_coverage_checklist_week ON inventory.weekend_coverage_checklist
        USING hash (week);

        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_sku_code
        ON inventory.legacy_format_goods_receipt_note USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_week_raw
        ON inventory.legacy_format_goods_receipt_note USING btree (scm_week_raw);

        CREATE INDEX IF NOT EXISTS ilegacy_format_goods_receipt_note_wh_id ON inventory.legacy_format_goods_receipt_note
        USING btree (wh_id);

        CREATE INDEX IF NOT EXISTS inv_week_config_week_brand_site ON inventory.weekly_config
        USING btree (week, brand, site);

        CREATE INDEX IF NOT EXISTS inventory_discard_timestamp ON inventory.discard USING btree ("timestamp");

        CREATE INDEX IF NOT EXISTS inventorydelivery_date_needs_dc ON inventory.delivery_date_needs USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorydelivery_date_needs_scm_week ON inventory.delivery_date_needs
        USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS inventorydiscard_source ON inventory.discard USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorypo_void_dc ON inventory.po_void USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypo_void_source ON inventory.po_void USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorypo_void_week ON inventory.po_void USING btree (week);

        CREATE INDEX IF NOT EXISTS inventorypull_put_brand ON inventory.pull_put USING btree (brand);

        CREATE INDEX IF NOT EXISTS inventorypull_put_dc ON inventory.pull_put USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypull_put_sku_code ON inventory.pull_put USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS inventorypull_put_source ON inventory.pull_put USING btree (source);

        CREATE INDEX IF NOT EXISTS inventorypull_put_state_code ON inventory.pull_put USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventorypull_put_week ON inventory.pull_put USING btree (week);

        CREATE INDEX IF NOT EXISTS inventorypullput_user_email ON inventory.pull_put USING btree (user_email);

        CREATE UNIQUE INDEX IF NOT EXISTS inventorypurchasing_category_name ON inventory.purchasing_category
        USING btree (name);

        CREATE UNIQUE INDEX IF NOT EXISTS inventorypurchasing_subcategory_name ON inventory.purchasing_subcategory
        USING btree (name);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_dc ON inventory.receiving USING btree (dc);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_sku_code ON inventory.receiving USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_source ON inventory.receiving USING btree (source);

        CREATE INDEX IF NOT EXISTS inventoryreceiving_week ON inventory.receiving USING btree (week);

        CREATE INDEX IF NOT EXISTS ipimt_unified_inventory_snapshot_timestamp ON inventory.unified_inventory
        USING btree (snapshot_timestamp);

        CREATE INDEX IF NOT EXISTS mealkit_brand ON inventory.mealkit USING btree (brand);

        CREATE INDEX IF NOT EXISTS mealkit_scm_week ON inventory.mealkit USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS mealkitingredient_dc_list ON inventory.mealkit_ingredient USING gin (dc_list);

        CREATE INDEX IF NOT EXISTS mealkitingredient_mealkit_id ON inventory.mealkit_ingredient
        USING btree (mealkit_id);

        CREATE INDEX IF NOT EXISTS mealkitingredient_sku_code ON inventory.mealkit_ingredient USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS po_void_brand ON inventory.po_void USING btree (brand);

        CREATE INDEX IF NOT EXISTS po_void_po_number_idx ON inventory.po_void USING btree (po_number);

        CREATE INDEX IF NOT EXISTS receiving_brand ON inventory.receiving USING btree (brand);

        CREATE UNIQUE INDEX IF NOT EXISTS ui_inventory_snapshot ON inventory.inventory_snapshot
        USING btree (wh_code, inventory_type, snapshot_ts DESC, location_id, state, expiration_date, location_type,
        sku_code) NULLS NOT DISTINCT;

        CREATE INDEX IF NOT EXISTS culinary_sku_status ON ordering.culinary_sku USING btree (status);

        CREATE INDEX IF NOT EXISTS i_culinary_sku_code ON ordering.culinary_sku USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS i_culinary_sku_id ON ordering.culinary_sku USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS i_ordering_purchase_order_bob_code ON ordering.purchase_order USING btree (bob_code);

        CREATE INDEX IF NOT EXISTS iordering_culinary_sku_market ON ordering.culinary_sku USING btree (market);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_brand ON ordering.purchase_order USING btree (brand);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_dc ON ordering.purchase_order USING btree (dc);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_deleted ON ordering.purchase_order USING btree (deleted);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_delivery_time_start ON ordering.purchase_order
        USING btree (delivery_time_start);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_emergency_reason ON ordering.purchase_order
        USING btree (emergency_reason);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_order_number ON ordering.purchase_order
        USING btree (order_number);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_ot_last_updated ON ordering.purchase_order
        USING btree (internal_last_updated);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_number ON ordering.purchase_order
        USING btree (po_number);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_number_comp ON ordering.purchase_order
        USING btree (substr(po_number, 1, 14));

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_po_uuid ON ordering.purchase_order_sku
        USING btree (po_uuid);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_sku_uuid ON ordering.purchase_order_sku
        USING btree (sku_uuid);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_supplier ON ordering.purchase_order USING btree (supplier);

        CREATE INDEX IF NOT EXISTS iordering_purchase_order_week ON ordering.purchase_order USING btree (week);

        CREATE UNIQUE INDEX IF NOT EXISTS iu_supplier_code_market ON ordering.supplier USING btree (code, market);

        CREATE INDEX IF NOT EXISTS goods_receipt_note_record_date ON pimt.goods_receipt_note USING btree (record_date);

        CREATE INDEX IF NOT EXISTS i_pimt_warehouse_code_inv_type ON pimt.warehouse USING btree (code, inventory_type);

        CREATE INDEX IF NOT EXISTS ipimt_hj_inventory_order_number ON pimt.hj_inventory USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_hj_inventory_sku_code ON pimt.hj_inventory USING btree (sku_code);

        CREATE INDEX IF NOT EXISTS ipimt_hj_inventory_status ON pimt.hj_inventory USING btree (status);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_notification_bob_code ON pimt.inventory_notification
        USING btree (bob_code);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_notification_sku_id ON pimt.inventory_notification
        USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_order_number ON pimt.inventory USING btree (order_number);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_partner ON pimt.inventory USING btree (partner);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_po_data_id ON pimt.inventory
        USING btree (((substr(po_number, 1, 12) || (sku_id)::text)));

        CREATE INDEX IF NOT EXISTS ipimt_inventory_po_number ON pimt.inventory USING btree (po_number);

        CREATE INDEX IF NOT EXISTS ipimt_inventory_sku_id ON pimt.inventory USING btree (sku_id);

        CREATE INDEX IF NOT EXISTS ipimt_partner_code ON pimt.warehouse USING btree (code);

        CREATE INDEX IF NOT EXISTS ipimt_partner_dc ON pimt.warehouse USING gin (ot_dcs);

        CREATE INDEX IF NOT EXISTS ipimt_partner_supplier ON pimt.warehouse USING gin (ot_suppliers);

        CREATE INDEX IF NOT EXISTS iwarehouse_inventory_type ON pimt.warehouse USING btree (inventory_type);

        CREATE INDEX IF NOT EXISTS iwarehouse_receiving_type ON pimt.warehouse USING btree (receiving_type);

        CREATE UNIQUE INDEX IF NOT EXISTS comment_uniq ON procurement.comment
        USING btree (domain, resource_type, week, site, resource_id, brand);

        CREATE INDEX IF NOT EXISTS gsheet_meta_brand_index ON procurement.gsheet_meta USING btree (brand);

        CREATE INDEX IF NOT EXISTS gsheet_meta_doc_code_index ON procurement.gsheet_meta USING btree (doc_code);

        CREATE INDEX IF NOT EXISTS gsheet_meta_required_index ON procurement.gsheet_meta USING btree (required);

        CREATE INDEX IF NOT EXISTS gsheetadmin_meta_id ON procurement.gsheet_admin USING btree (meta_id);

        CREATE UNIQUE INDEX IF NOT EXISTS gsheetadmin_meta_id_scm_week ON procurement.gsheet_admin
        USING btree (meta_id, scm_week);

        CREATE INDEX IF NOT EXISTS gsheetadmin_scm_week ON procurement.gsheet_admin USING btree (scm_week);

        CREATE INDEX IF NOT EXISTS i_parallel_sync_log_job_name_market_sync_time ON procurement.parallel_sync_log
        USING btree (job_name, market, sync_time);

        CREATE INDEX IF NOT EXISTS iglobal_parent_id_parallel_sync_log ON procurement.parallel_sync_log
        USING btree (global_parent_id);

        CREATE INDEX IF NOT EXISTS iprocurement_scrubbing_comment_dc ON procurement.scrubbing_comment USING btree (dc);

        CREATE INDEX IF NOT EXISTS iprocurement_scrubbing_comment_week ON procurement.scrubbing_comment
        USING btree (week);

        CREATE UNIQUE INDEX IF NOT EXISTS iu_procurement_distibution_center_bob_code ON procurement.distribution_center
        USING btree (bob_code);

        CREATE INDEX IF NOT EXISTS role_full_name ON procurement.role USING btree (full_name);

        CREATE INDEX IF NOT EXISTS role_index ON procurement.permission_role USING btree (role_id);

        CREATE INDEX IF NOT EXISTS role_short_name ON procurement.role USING btree (short_name);

        CREATE UNIQUE INDEX IF NOT EXISTS user_email ON procurement."user" USING btree (email);

        CREATE INDEX IF NOT EXISTS user_role_user_id ON procurement.user_role USING btree (user_id);

        CREATE INDEX IF NOT EXISTS user_view_state_user_id ON procurement.user_view_state USING btree (user_id);

        CREATE INDEX IF NOT EXISTS user_view_state_user_id_resource ON procurement.user_view_state USING btree
        (user_id, resource);
        """
    ),
    # Insert Gsheet Meta
    step(
        """
        INSERT INTO procurement.gsheet_meta (id, name, doc_code, weekly, created_tmst, "order", title_template, brand,
        required, project, market)
        VALUES
        (129, 'IMT Data Feeds', 'imt_inventory', false, '2023-08-17 16:08:41.271255+00', 24,
        'IMT - ALL 3PLs - Grid Dynamics Data Feed', 'HF', false, 'imt', 'US'),
        (130, 'Total Procurement Purchasing', 'commodity_group', false, '2023-10-24 12:04:53.016041+00', 12,
        'CA Total Procurement Purchasing', 'HF', true, 'imt', 'CA'),
        (122, 'Organic SKU Mapping', 'alternative_sku_mapping', false, '2022-11-03 15:56:04.255501+00', 6,
        'GC Produce ORG + CV', 'GC', false, 'imt', 'US'),
        (131, 'Joliet OPE SKUs', 'joliet_ope_skus', false, '2023-11-03 16:04:53.045713+00', 21, 'Joliet OPE SKUs',
        'FJ', false, 'imt', 'US'),
        (132, 'Production Kit Guide', 'pkg', true, '2023-12-07 19:43:38.077196+00', 3000,
        '{scm_week} Factor IMT Remake Input', 'FJ', false, 'imt', 'US'),
        (4, 'Production Kit Guide', 'pkg', true, '2020-08-11 18:55:20.948233+00', 4,
        'Production Kit Guide - {scm_week}', 'HF', true, 'imt', 'US'),
        (133, 'RWA SKU mapping', 'rwa_sku_mapping', false, '2023-12-07 19:43:38.111816+00', 6, 'Canada RWA + CV', 'HF',
        false, 'imt', 'CA'),
        (134, 'PIMT - ALL 3PWs - Grid Dynamics Data Feed', 'pimt', false, '2024-01-26 12:05:32.496067+00', 2000,
        'CAN PIMT - ALL 3PWs - Grid Dynamics Data Feed', NULL, false, 'pimt', 'CA'),
        (128, 'Production Kit Guide', 'pkg_v2', true, '2023-07-27 21:02:22.455216+00', 1051,
        'EveryPlate Production Kit Guide - {scm_week}', 'EP', false, 'imt', 'US'),
        (135, 'Consolidated Packaging Demand', 'consolidated_packaging_demand', false, '2024-02-23 13:11:06.947372+00',
        15, 'Consolidated Packaging Demand (CA)', 'HF', true, 'imt', 'CA'),
        (2, 'Hybrid Needs', 'hybrid_needs', true, '2020-08-11 18:55:20.948233+00', 2,
        'Hybrid Needs - Week {week_number}', 'HF', false, 'imt', 'US'),
        (16, 'Hybrid Needs', 'hybrid_needs', true, '2020-09-16 16:09:31.383137+00', 1030,
        'EP Hybrid Needs - Week {week_number}', 'EP', false, 'imt', 'US'),
        (136, 'Master Ambient Safety Stock Data', 'ambient_safety_stock', false, '2024-03-29 19:54:25.585848+00', 24,
        'Master Ambient Safety Stock Data', 'HF', true, 'imt', 'US'),
        (109, 'Master Pallet', 'master_pallet', false, '2021-06-08 13:32:01.430911+00', 13,
        'Master Pallet Conversions ', NULL, true, 'imt', 'US'),
        (111, 'Mock Plan Calculation', 'mock_plan_calculation', true, '2021-06-23 17:51:08.772678+00', 30,
        'Mock Plan Calculation - {year}-W{week_number}', 'HF', false, 'imt', 'US'),
        (113, 'PO Status Export', 'po_status_export', false, '2021-08-13 17:27:12.394187+00', 17, 'PO Status Export',
        'HF', false, 'imt', 'US'),
        (1, 'Gil', 'gil_two', false, '2020-08-11 18:55:20.948233+00', 1, 'Gil 2', 'HF', true, 'imt', 'US'),
        (19, 'Gil', 'gil_two', false, '2020-09-16 16:09:31.383137+00', 1010, 'Gil 2', 'EP', true, 'imt', 'US'),
        (12, 'Total Procurement Purchasing', 'commodity_group', false, '2020-09-04 19:51:38.834355+00', 12,
        'Total Procurement Purchasing', 'HF', true, 'imt', 'US'),
        (21, 'Total Procurement Purchasing', 'commodity_group', false, '2020-09-30 13:11:22.261117+00', 1020,
        'Total Procurement Purchasing', 'EP', true, 'imt', 'US'),
        (116, 'PIMT Export', 'pimt-export', false, '2022-01-14 12:50:37.181942+00', 18, 'pIMT Export', NULL, false,
        'pimt', 'US'),
        (117, 'Daily Exceptions Export', 'imt-daily-export', false, '2022-01-28 14:49:51.303476+00', 32,
        'IMT App - Daily Exceptions Export', NULL, true, 'imt', 'US'),
        (118, 'PIMT Master Replenishment', 'pimt_master_replenishment', false, '2022-03-18 19:42:25.500478+00', 23,
        'MASTER Ingredient Replenishment Data', NULL, false, 'pimt', 'US'),
        (103, 'PIMT - ALL 3PWs - Grid Dynamics Data Feed', 'pimt', false, '2020-12-03 12:33:44.048233+00', 2000,
        'PIMT - ALL 3PWs - Grid Dynamics Data Feed', NULL, true, 'pimt', 'US'),
        (24, '3PL Hybrid Needs', 'hybrid_needs_3pl', true, '2021-02-04 10:45:34.031876+00', 22,
        '3PL Hybrid Needs - Week {week_number}', 'HF', false, 'imt', 'US'),
        (25, '3PL IMT - Data Dumps', 'imt_data_dumps_3pl', true, '2021-02-04 10:45:34.061818+00', 21,
        '3PL Data Dump - W{week_number}', 'HF', false, 'imt', 'US'),
        (119, 'GC Oscar', 'gc_oscar', false, '2022-09-30 15:27:33.321197+00', 5, 'GC OSCAR', 'GC', true, 'imt', 'US'),
        (120, 'Production Kit Guide', 'pkg', true, '2022-09-30 15:27:33.321197+00', 26,
        'GC Production Kit Guide - {scm_week}', 'GC', true, 'imt', 'US'),
        (121, 'Hybrid Needs', 'hybrid_needs', true, '2022-09-30 15:27:33.321197+00', 27,
        'GC Hybrid Needs - Week {week_number} - {year}', 'GC', true, 'imt', 'US'),
        (123, 'In - Week Packaging Demand', 'packaging_demand', true, '2023-01-04 15:59:33.64577+00', 32,
        'In - Week Packaging Demand {scm_week}', 'HF', false, 'imt', 'US'),
        (125, 'Gil', 'gil_two', false, '2023-02-06 16:11:38.781132+00', 1, 'Gil 2', 'FJ', true, 'imt', 'US'),
        (126, 'Hybrid Needs', 'hybrid_needs', true, '2023-02-13 19:10:18.085037+00', 32,
        'FJ Hybrid Needs - Week {week_number}', 'FJ', false, 'imt', 'US'),
        (127, 'GreenChef Cycle Counts', 'cycle_count_gc', false, '2023-04-14 18:38:09.728646+00', 34,
        'GreenChef Cycle Counts', 'GC', false, 'imt', 'US')
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.gsheet_meta_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.gsheet_meta), false);
        """
    ),
    # Insert Permissions
    step(
        """
        INSERT INTO procurement.permission (id, name)
        VALUES
        (1, 'general:r'),
        (2, 'imt-gsheet-v1:r'),
        (3, 'imt-gsheet-v1:w'),
        (4, 'imt-region-v1:r'),
        (5, 'imt-region-v1:w'),
        (6, 'imt-remake-tool-v1:r'),
        (7, 'imt-remake-tool-v1:w'),
        (8, 'imt-ing-depl-v1:r'),
        (114, 'dc-inventory-inventory-module-v1:w'),
        (10, 'imt-po-status-v1:r'),
        (115, 'dc-inventory-network-depletion-v1:r'),
        (12, 'imt-prod-need-ing-v1:r'),
        (13, 'imt-pkg-v1:r'),
        (14, 'imt-buyer-v1:r'),
        (15, 'imt-pull-put-v1:r'),
        (16, 'imt-pull-put-v1:w'),
        (17, 'imt-po-void-v1:r'),
        (18, 'imt-po-void-v1:w'),
        (21, 'imt-discard-v1:r'),
        (22, 'imt-discard-v1:w'),
        (119, 'imt-release-log:r'),
        (120, 'imt-release-log:w'),
        (25, 'imt-wcc-v1:r'),
        (26, 'imt-wcc-v1:w'),
        (116, 'dc-inventory-network-depletion-v1:w'),
        (28, 'pimt-ait-v1:r'),
        (19, 'imt-receipt-override-v1:r'),
        (30, 'pimt-buying-tool-v1:r'),
        (31, 'pimt-ops-dash-v1:r'),
        (32, 'pimt-partners-v1:r'),
        (33, 'pimt-partners-v1:w'),
        (34, 'imt-ing-depl-v2:r'),
        (117, 'snapshot-start-of-day-inv:r'),
        (118, 'snapshot-start-of-day-inv:w'),
        (39, 'imt-cache-v1:r'),
        (40, 'imt-cache-v1:w'),
        (20, 'imt-receipt-override-v1:w'),
        (42, 'imt-sync-v1:w'),
        (43, 'imt-po-sync-v1:w'),
        (44, 'pimt-sync-v1:w'),
        (27, 'imt-comment-log-v2:r'),
        (35, 'imt-comment-v2:r'),
        (36, 'imt-comment-v2:w'),
        (41, 'imt-metrics-v1:w'),
        (47, 'imt-metrics-v1:r'),
        (48, 'imt-sync-v1:r'),
        (49, 'imt-po-sync-v1:r'),
        (121, 'imt-analytics-top-variants-v1:r'),
        (122, 'imt-analytics-top-variants-v1:w'),
        (52, 'pimt-sync-v1:r'),
        (53, 'general:w'),
        (54, 'shipwell-sync-v1:r'),
        (55, 'imt-forecast-upload-sync-v1:r'),
        (56, 'imt-forecast-upload-sync-v1:w'),
        (57, 'imt-jobs-v1:r'),
        (58, 'imt-jobs-v1:w'),
        (59, 'shipwell-sync-v1:w'),
        (60, 'pimt-po-status-v1:r'),
        (61, 'pimt-po-status-v1:w'),
        (62, 'pimt-comment-v2:r'),
        (63, 'pimt-comment-v2:w'),
        (123, 'imt-analytics-buffer-analysis-v1:r'),
        (124, 'imt-analytics-buffer-analysis-v1:w'),
        (125, 'dc-inventory-unified-inventory-v1:r'),
        (67, 'pimt-sync-job-v1:r'),
        (68, 'pimt-sync-job-v1:w'),
        (69, 'imt-po-sync-job-v1:r'),
        (70, 'imt-po-sync-job-v1:w'),
        (71, 'imt-sync-job-v1:r'),
        (72, 'imt-sync-job-v1:w'),
        (73, 'future-pkg-sync-job-v1:r'),
        (74, 'future-pkg-sync-job-v1:w'),
        (126, 'forecast-push-data:r'),
        (127, 'pimt-packaging-safety-stock:r'),
        (77, 'cleanup-job-v1:r'),
        (78, 'cleanup-job-v1:w'),
        (79, 'hj-inv-sync-job-v1:r'),
        (80, 'hj-inv-sync-job-v1:w'),
        (83, 'imt-forecast-upload-sync-job-v1:r'),
        (84, 'imt-forecast-upload-sync-job-v1:w'),
        (87, 'imt-modify-old-form-v1:r'),
        (88, 'imt-modify-old-form-v1:w'),
        (89, 'pimt-export-v1:r'),
        (90, 'pimt-export-v1:w'),
        (91, 'imt-daily-export-v1:r'),
        (92, 'imt-daily-export-v1:w'),
        (96, 'pimt-replenishment-v1:r'),
        (97, 'pimt-daily-report-v1:r'),
        (98, 'pimt-daily-report-v1:w'),
        (99, 'pimt-exception-metrics-v1:r'),
        (100, 'pimt-exception-metrics-v1:w'),
        (101, 'pimt-monthly-financial-report-v1:r'),
        (102, 'pimt-monthly-financial-report-v1:w'),
        (103, 'dc-inventory-inventory-module-v1:r'),
        (104, 'imt-bulk-sku-v1:r'),
        (105, 'imt-bulk-sku-v1:w'),
        (106, 'manual-forms-export-to-s3-v1:r'),
        (107, 'manual-forms-export-to-s3-v1:w'),
        (108, 'hj-pck-snapshot-job:r'),
        (109, 'hj-pck-snapshot-job:w'),
        (110, 'imt-pck-depl-v2:r'),
        (111, 'imt-pck-depl-v2:w'),
        (112, 'financial-export-email-v1:r'),
        (113, 'financial-export-email-v1:w')
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.permission_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.permission), false);
        """
    ),
    # Insert roles
    step(
        """
        INSERT INTO procurement.role (id, full_name, short_name, priority)
        VALUES
        (7, 'acl_inventorymanagementus_sso_dcuniversaluser', 'dcuniversaluser', 100),
        (23, 'fj-gd-dcuser', 'fj-gd-dcuser', 100),
        (24, 'fj-ag-dcuser', 'fj-ag-dcuser', 100),
        (10, 'hf-nj-dcuser', 'hf-nj-dcuser', 100),
        (25, 'hf-oe-dcuser', 'hf-oe-dcuser', 100),
        (26, 'hf-oh-dcuser', 'hf-oh-dcuser', 100),
        (11, '	acl_inventorymanagementus_sso_hf-nj-dcuser', '	acl_inventorymanagementus_sso_hf-nj-dcuser', 100),
        (12, '	hf-nj-dcuser', '	hf-nj-dcuser', 100),
        (27, 'hf-ae-dcuser', 'hf-ae-dcuser', 100),
        (8, 'dcmanagementuser', 'dcmanagementuser', 100),
        (13, 'ep-nj-dcuser', 'ep-nj-dcuser', 100),
        (14, 'ep-tx-dcuser', 'ep-tx-dcuser', 100),
        (15, 'hf-ga-dcuser', 'hf-ga-dcuser', 100),
        (16, 'hf-t3-dcuser', 'hf-t3-dcuser', 100),
        (17, 'hf-az-dcuser', 'hf-az-dcuser', 100),
        (1, 'acl_inventorymanagementus_sso_adminuser', 'admin', 0),
        (3, 'acl_inventorymanagementus_sso_procurementmanager', 'procurementmanager', 10),
        (2, 'acl_inventorymanagementus_sso_procurementleadership', 'procurementleadership', 20),
        (4, 'acl_inventorymanagementus_sso_procurementuser', 'procurementuser', 30),
        (6, 'acl_inventorymanagementus_sso_scrubber', 'scrubber', 40),
        (5, 'acl_inventorymanagementus_sso_dcuser', 'dcuser', 50),
        (18, 'gc-co-dcuser', 'gc-co-dcuser', 100),
        (19, 'gc-sw-dcuser', 'gc-sw-dcuser', 100),
        (20, 'fj-au-dcuser', 'fj-au-dcuser', 100),
        (21, 'fj-br-dcuser', 'fj-br-dcuser', 100),
        (22, 'fj-jo-dcuser', 'fj-jo-dcuser', 100),
        (28, 'hf-bl-dcuser', 'hf-bl-dcuser', 100),
        (29, 'fj-lz-dcuser', 'fj-lz-dcuser', 100)
        ON CONFLICT DO NOTHING;

        SELECT pg_catalog.setval('procurement.role_id_seq',
        (SELECT MAX(id) + 1 FROM procurement.role), false);
        """
    ),
    # Insert Permission-Role
    permissions.grant_permissions(
        {
            "dcuniversaluser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
            ],
            "dcmanagementuser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
            ],
            "admin": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-gsheet-v1:w",
                "imt-region-v1:r",
                "imt-region-v1:w",
                "imt-remake-tool-v1:r",
                "imt-remake-tool-v1:w",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-release-log:r",
                "imt-release-log:w",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "snapshot-start-of-day-inv:r",
                "snapshot-start-of-day-inv:w",
                "imt-cache-v1:r",
                "imt-cache-v1:w",
                "imt-receipt-override-v1:w",
                "imt-sync-v1:w",
                "imt-po-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-metrics-v1:w",
                "imt-metrics-v1:r",
                "imt-sync-v1:r",
                "imt-po-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "shipwell-sync-v1:r",
                "imt-forecast-upload-sync-v1:r",
                "imt-forecast-upload-sync-v1:w",
                "imt-jobs-v1:r",
                "imt-jobs-v1:w",
                "shipwell-sync-v1:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-po-sync-job-v1:r",
                "imt-po-sync-job-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "future-pkg-sync-job-v1:r",
                "future-pkg-sync-job-v1:w",
                "forecast-push-data:r",
                "pimt-packaging-safety-stock:r",
                "cleanup-job-v1:r",
                "cleanup-job-v1:w",
                "hj-inv-sync-job-v1:r",
                "hj-inv-sync-job-v1:w",
                "imt-forecast-upload-sync-job-v1:r",
                "imt-forecast-upload-sync-job-v1:w",
                "imt-modify-old-form-v1:r",
                "imt-modify-old-form-v1:w",
                "pimt-export-v1:r",
                "pimt-export-v1:w",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-exception-metrics-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
                "imt-bulk-sku-v1:r",
                "imt-bulk-sku-v1:w",
                "manual-forms-export-to-s3-v1:r",
                "manual-forms-export-to-s3-v1:w",
                "hj-pck-snapshot-job:r",
                "hj-pck-snapshot-job:w",
                "imt-pck-depl-v2:r",
                "imt-pck-depl-v2:w",
                "financial-export-email-v1:r",
                "financial-export-email-v1:w",
            ],
            "procurementmanager": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-remake-tool-v1:w",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-discard-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-receipt-override-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-forecast-upload-sync-v1:r",
                "imt-jobs-v1:r",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "pimt-packaging-safety-stock:r",
                "imt-forecast-upload-sync-job-v1:r",
                "pimt-export-v1:r",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
                "imt-bulk-sku-v1:r",
                "imt-bulk-sku-v1:w",
                "imt-pck-depl-v2:r",
                "imt-pck-depl-v2:w",
            ],
            "procurementleadership": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-remake-tool-v1:w",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-discard-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-receipt-override-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
                "pimt-packaging-safety-stock:r",
                "pimt-export-v1:r",
                "pimt-export-v1:w",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-exception-metrics-v1:w",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
                "imt-bulk-sku-v1:r",
                "imt-bulk-sku-v1:w",
                "imt-pck-depl-v2:r",
                "imt-pck-depl-v2:w",
            ],
            "procurementuser": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-discard-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-receipt-override-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-forecast-upload-sync-v1:r",
                "imt-jobs-v1:r",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "pimt-packaging-safety-stock:r",
                "imt-forecast-upload-sync-job-v1:r",
                "pimt-export-v1:r",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
                "imt-pck-depl-v2:r",
                "imt-pck-depl-v2:w",
            ],
            "scrubber": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-po-void-v1:r",
                "imt-discard-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-exception-metrics-v1:r",
                "dc-inventory-inventory-module-v1:r",
            ],
            "dcuser": [
                "general:r",
                "imt-ing-depl-v1:r",
                "imt-po-status-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-discard-v1:r",
                "imt-discard-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-analytics-buffer-analysis-v1:r",
                "imt-analytics-buffer-analysis-v1:w",
                "dc-inventory-unified-inventory-v1:r",
                "imt-pck-depl-v2:r",
            ],
        }
    ),
]
