from migrations.scripts.permissions import grant_permissions
from yoyo import step

from procurement.core.migration import utils as migration_utils

__depends__ = migration_utils.get_depends(__name__)


steps = [
    step(
        """
            INSERT INTO procurement.role (full_name, short_name, priority)
            VALUES
            ('acl_inventorymanagementus_sso_helloconnect', 'helloconnect', 29);
            """
    ),
    grant_permissions(
        {
            "helloconnect": [
                "general:r",
                "imt-gsheet-v1:r",
                "imt-region-v1:r",
                "imt-remake-tool-v1:r",
                "imt-ing-depl-v1:r",
                "dc-inventory-inventory-module-v1:w",
                "imt-po-status-v1:r",
                "dc-inventory-network-depletion-v1:r",
                "imt-prod-need-ing-v1:r",
                "imt-pkg-v1:r",
                "imt-buyer-v1:r",
                "imt-pull-put-v1:r",
                "imt-pull-put-v1:w",
                "imt-po-void-v1:r",
                "imt-po-void-v1:w",
                "imt-discard-v1:r",
                "imt-wcc-v1:r",
                "imt-wcc-v1:w",
                "dc-inventory-network-depletion-v1:w",
                "pimt-ait-v1:r",
                "imt-receipt-override-v1:r",
                "pimt-buying-tool-v1:r",
                "pimt-ops-dash-v1:r",
                "pimt-partners-v1:r",
                "pimt-partners-v1:w",
                "imt-ing-depl-v2:r",
                "imt-receipt-override-v1:w",
                "imt-sync-v1:w",
                "pimt-sync-v1:w",
                "imt-comment-log-v2:r",
                "imt-comment-v2:r",
                "imt-comment-v2:w",
                "imt-sync-v1:r",
                "imt-analytics-top-variants-v1:r",
                "imt-analytics-top-variants-v1:w",
                "pimt-sync-v1:r",
                "general:w",
                "imt-forecast-upload-sync-v1:r",
                "imt-jobs-v1:r",
                "pimt-po-status-v1:r",
                "pimt-po-status-v1:w",
                "pimt-comment-v2:r",
                "pimt-comment-v2:w",
                "dc-inventory-unified-inventory-v1:r",
                "pimt-sync-job-v1:r",
                "pimt-sync-job-v1:w",
                "imt-sync-job-v1:r",
                "imt-sync-job-v1:w",
                "pimt-packaging-safety-stock:r",
                "imt-forecast-upload-sync-job-v1:r",
                "pimt-replenishment-v1:w",
                "pimt-export-v1:r",
                "imt-daily-export-v1:r",
                "imt-daily-export-v1:w",
                "pimt-replenishment-v1:r",
                "pimt-daily-report-v1:r",
                "pimt-daily-report-v1:w",
                "pimt-exception-metrics-v1:r",
                "pimt-monthly-financial-report-v1:r",
                "pimt-monthly-financial-report-v1:w",
                "dc-inventory-inventory-module-v1:r",
                "imt-pck-depl-v2:r",
                "imt-pck-depl-v2:w",
                "imt-discard-v1:w",
            ]
        }
    ),
]
