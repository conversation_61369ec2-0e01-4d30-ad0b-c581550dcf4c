from typing import Callable, Optional

from procurement.core import prepare_metrics

mark_process_dead: Optional[Callable] = None


def on_starting(server):  # pylint: disable=unused-argument
    print("Starting application Gunicorn")  # Point for monitoring redeploy app in logs
    prepare_metrics.prepare_metrics_env(cleanup=True)


def child_exit(server, worker):  # pylint: disable=unused-argument
    # First access to prometheus modules should happen after core.init()
    global mark_process_dead
    if not mark_process_dead:
        from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics

        mark_process_dead = GunicornPrometheusMetrics.mark_process_dead_on_child_exit
    mark_process_dead(worker.pid)
