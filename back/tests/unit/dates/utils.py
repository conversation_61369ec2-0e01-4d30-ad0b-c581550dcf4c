from typing import Iterable, List

from procurement.core.dates import ScmWeek


def check_list_of_scm_weeks(actual_scm_weeks: Iterable[ScmWeek], expected_scm_weeks: Iterable[ScmWeek]):
    for actual, expected in zip(actual_scm_weeks, expected_scm_weeks):
        assert actual.year == expected.year
        assert actual.week == expected.week


def assert_year_weeks(actual_list_of_weeks: List[ScmWeek], expected_len_weeks: int, expected_last_week: ScmWeek):
    assert (
        len(actual_list_of_weeks) == expected_len_weeks
        and expected_last_week.year == actual_list_of_weeks[-1].year
        and expected_last_week.week == actual_list_of_weeks[-1].week
    )
