from typing import List

import pytest

from procurement.core.dates import ScmQuarter, ScmWeek
from tests.unit.dates.utils import check_list_of_scm_weeks


def test_for_fail():
    with pytest.raises(ValueError):
        ScmQuarter(2020, 7)

    with pytest.raises(ValueError):
        ScmQuarter(2020, 0)

    with pytest.raises(ValueError):
        ScmQuarter(2020, -7)


def _check_list_of_scm_quarters(actual_scm_quarters: List[ScmQuarter], expected_scm_quarters: List[ScmQuarter]):
    for actual, expected in zip(actual_scm_quarters, expected_scm_quarters):
        assert actual.year == expected.year
        assert actual.quarter == expected.quarter


def test_get_by_year():
    _check_list_of_scm_quarters(ScmQuarter.get_by_year(2021), _get_scm_quarters_by_year(2021))
    _check_list_of_scm_quarters(ScmQuarter.get_by_year(2022), _get_scm_quarters_by_year(2022))
    _check_list_of_scm_quarters(ScmQuarter.get_by_year(2023), _get_scm_quarters_by_year(2023))


def _get_scm_quarters_by_year(year):
    return [ScmQuarter(year=year, quarter=quarter) for quarter in range(1, 5)]


def test_from_scm_week():
    actual_quarter_from_scm_week_2020_17 = ScmQuarter.from_scm_week(ScmWeek(2020, 17))
    actual_quarter_from_scm_week_2022_1 = ScmQuarter.from_scm_week(ScmWeek(2022, 1))
    actual_quarter_from_scm_week_2019_52 = ScmQuarter.from_scm_week(ScmWeek(2019, 52))
    actual_quarter_from_scm_week_2020_33 = ScmQuarter.from_scm_week(ScmWeek(2020, 33))
    actual_quarter_from_scm_week_2020_53 = ScmQuarter.from_scm_week(ScmWeek(2020, 53))

    assert actual_quarter_from_scm_week_2020_17.year == 2020 and actual_quarter_from_scm_week_2020_17.quarter == 2
    assert actual_quarter_from_scm_week_2022_1.year == 2022 and actual_quarter_from_scm_week_2022_1.quarter == 1
    assert actual_quarter_from_scm_week_2019_52.year == 2019 and actual_quarter_from_scm_week_2019_52.quarter == 4
    assert actual_quarter_from_scm_week_2020_33.year == 2020 and actual_quarter_from_scm_week_2020_33.quarter == 3
    assert actual_quarter_from_scm_week_2020_53.year == 2020 and actual_quarter_from_scm_week_2020_53.quarter == 4


def test_from_str():
    actual_quarter_from_str_2022_4 = ScmQuarter.from_str("2022-Q4")
    actual_quarter_from_str_2021_2 = ScmQuarter.from_str("2021-Q2")
    actual_quarter_from_str_2023_1 = ScmQuarter.from_str("2023-Q1")

    assert actual_quarter_from_str_2022_4.year == 2022 and actual_quarter_from_str_2022_4.quarter == 4
    assert actual_quarter_from_str_2021_2.year == 2021 and actual_quarter_from_str_2021_2.quarter == 2
    assert actual_quarter_from_str_2023_1.year == 2023 and actual_quarter_from_str_2023_1.quarter == 1


def test_get_first_and_last_week():
    actual_first_week_from_quarter_2023_3 = ScmQuarter(2023, 3).get_first_week()
    actual_first_week_from_quarter_2019_2 = ScmQuarter(2019, 2).get_first_week()

    actual_last_week_from_quarter_2023_3 = ScmQuarter(2023, 3).get_last_week()
    actual_last_week_from_quarter_2019_2 = ScmQuarter(2019, 2).get_last_week()

    assert actual_first_week_from_quarter_2023_3.year == 2023 and actual_first_week_from_quarter_2023_3.week == 27
    assert actual_first_week_from_quarter_2019_2.year == 2019 and actual_first_week_from_quarter_2019_2.week == 14

    assert actual_last_week_from_quarter_2023_3.year == 2023 and actual_last_week_from_quarter_2023_3.week == 39
    assert actual_last_week_from_quarter_2019_2.year == 2019 and actual_last_week_from_quarter_2019_2.week == 26


def test_get_weeks():
    expected_data_2019_2 = list(ScmWeek.range(ScmWeek(2019, 14), ScmWeek(2019, 26)))
    expected_data_2023_4 = list(ScmWeek.range(ScmWeek(2023, 40), ScmWeek(2023, 52)))
    expected_data_2020_3 = list(ScmWeek.range(ScmWeek(2020, 27), ScmWeek(2020, 39)))
    expected_data_2022_1 = list(ScmWeek.range(ScmWeek(2022, 1), ScmWeek(2022, 13)))
    check_list_of_scm_weeks(ScmQuarter(2019, 2).get_weeks(), expected_data_2019_2)
    check_list_of_scm_weeks(ScmQuarter(2023, 4).get_weeks(), expected_data_2023_4)
    check_list_of_scm_weeks(ScmQuarter(2020, 3).get_weeks(), expected_data_2020_3)
    check_list_of_scm_weeks(ScmQuarter(2022, 1).get_weeks(), expected_data_2022_1)


def test_str_repr():
    assert repr(ScmQuarter(2022, 4)) == "2022-Q4"
    assert str(ScmQuarter(2019, 1)) == "2019-Q1"


def test_get_quarter_range():
    assert ScmQuarter.get_quarter_range(ScmQuarter(2021, 1), ScmQuarter(2022, 3)) == [
        ScmQuarter(2021, 1),
        ScmQuarter(2021, 2),
        ScmQuarter(2021, 3),
        ScmQuarter(2021, 4),
        ScmQuarter(2022, 1),
        ScmQuarter(2022, 2),
        ScmQuarter(2022, 3),
    ]
    assert ScmQuarter.get_quarter_range(ScmQuarter(2022, 1), ScmQuarter(2022, 1)) == [ScmQuarter(2022, 1)]
    assert ScmQuarter.get_quarter_range(ScmQuarter(2023, 1), ScmQuarter(2022, 1)) == []


def test_quarters_add():
    assert ScmQuarter(2021, 1) + 2 == ScmQuarter(2021, 3)
    assert ScmQuarter(2021, 1) + 4 == ScmQuarter(2022, 1)
    assert ScmQuarter(2021, 1) + 0 == ScmQuarter(2021, 1)
    assert ScmQuarter(2021, 1) + 9 == ScmQuarter(2023, 2)


def test_quarters_sub():
    assert ScmQuarter(2021, 4) - 2 == ScmQuarter(2021, 2)
    assert ScmQuarter(2021, 4) - 4 == ScmQuarter(2020, 4)
    assert ScmQuarter(2021, 1) - 0 == ScmQuarter(2021, 1)
    assert ScmQuarter(2021, 1) - 9 == ScmQuarter(2018, 4)


def test_quarter_eq():
    q_1 = ScmQuarter(2020, 4)
    q_2 = ScmQuarter(2020, 4)
    assert q_1 == q_2 and q_1 is not q_2


def test_quarters_hash():
    assert hash(ScmQuarter(2021, 3)) != hash(ScmQuarter(2021, 4))
    assert hash(ScmQuarter(2020, 1)) == hash(ScmQuarter(2020, 1))


def test_quarter_comparison():
    assert ScmQuarter(2021, 3) > ScmQuarter(2021, 2)
    assert ScmQuarter(2023, 3) > ScmQuarter(2021, 2)
    assert not (ScmQuarter(2021, 1) > ScmQuarter(2021, 2))
    assert not (ScmQuarter(2021, 1) > ScmQuarter(2021, 1))
    assert ScmQuarter(2021, 3) >= ScmQuarter(2021, 3)
    assert ScmQuarter(2021, 3) >= ScmQuarter(2021, 2)
    assert ScmQuarter(2023, 3) >= ScmQuarter(2021, 2)
    assert not (ScmQuarter(2021, 1) >= ScmQuarter(2021, 2))
    assert ScmQuarter(2021, 1) < ScmQuarter(2021, 2)
    assert ScmQuarter(2021, 1) < ScmQuarter(2023, 2)
    assert not (ScmQuarter(2021, 3) < ScmQuarter(2021, 2))
    assert not (ScmQuarter(2021, 3) < ScmQuarter(2021, 3))
    assert ScmQuarter(2021, 1) <= ScmQuarter(2021, 1)
    assert ScmQuarter(2021, 1) <= ScmQuarter(2023, 1)
    assert ScmQuarter(2021, 1) <= ScmQuarter(2021, 2)
    assert not (ScmQuarter(2021, 3) <= ScmQuarter(2021, 2))
