from datetime import date, datetime, timedelta

import pytest
from freezegun import freeze_time

from procurement.core.dates import ScmWeek, ScmWeekConfig, Weekday
from tests.unit.dates.utils import check_list_of_scm_weeks


def test_from_date():
    actual_scm_week_from_date_2020_1_1 = ScmWeek.from_date(datetime(2020, 1, 1))
    actual_scm_week_from_date_2020_12_23 = ScmWeek.from_date(datetime(2020, 12, 23))
    actual_scm_week_from_date_2020_12_24 = ScmWeek.from_date(datetime(2020, 12, 24))
    actual_scm_week_from_date_2020_12_31 = ScmWeek.from_date(datetime(2020, 12, 31))
    actual_scm_week_from_date_2020_8_19 = ScmWeek.from_date(datetime(2020, 8, 19))
    actual_scm_week_from_date_2020_8_18 = ScmWeek.from_date(datetime(2020, 8, 18))

    assert actual_scm_week_from_date_2020_1_1.year == 2020 and actual_scm_week_from_date_2020_1_1.week == 2
    assert actual_scm_week_from_date_2020_12_23.year == 2020 and actual_scm_week_from_date_2020_12_23.week == 53
    assert actual_scm_week_from_date_2020_12_24.year == 2020 and actual_scm_week_from_date_2020_12_24.week == 53
    assert actual_scm_week_from_date_2020_12_31.year == 2021 and actual_scm_week_from_date_2020_12_31.week == 1
    assert actual_scm_week_from_date_2020_8_19.year == 2020 and actual_scm_week_from_date_2020_8_19.week == 35
    assert actual_scm_week_from_date_2020_8_18.year == 2020 and actual_scm_week_from_date_2020_8_18.week == 34

    with freeze_time(datetime(2022, 7, 22)):
        actual_scm_week_from_current_date = ScmWeek.from_date(_date=None)
        assert actual_scm_week_from_current_date.year == 2022 and actual_scm_week_from_current_date.week == 30


def test_add_week():
    actual_scm_week_from_date_2020_8_18 = ScmWeek.from_date(datetime(2020, 8, 18)) + 1
    actual_scm_week_from_date_2020_12_24 = ScmWeek.from_date(datetime(2020, 12, 24)) + 1
    actual_scm_week_from_date_2021_12_27 = ScmWeek.from_date(datetime(2021, 12, 27)) + 3
    actual_scm_week_from_date_2022_7_29 = ScmWeek.from_date(datetime(2022, 7, 29)) + 5

    assert actual_scm_week_from_date_2020_8_18.year == 2020 and actual_scm_week_from_date_2020_8_18.week == 35
    assert actual_scm_week_from_date_2020_12_24.year == 2021 and actual_scm_week_from_date_2020_12_24.week == 1
    assert actual_scm_week_from_date_2021_12_27.year == 2022 and actual_scm_week_from_date_2021_12_27.week == 3
    assert actual_scm_week_from_date_2022_7_29.year == 2022 and actual_scm_week_from_date_2022_7_29.week == 36

    with freeze_time(datetime(2022, 7, 22)):
        actual_scm_week_from_current_date = ScmWeek.from_date(_date=None) + 4
        assert actual_scm_week_from_current_date.year == 2022 and actual_scm_week_from_current_date.week == 34


def test_minus_week():
    actual_scm_week_from_date_2022_7_29 = ScmWeek.from_date(datetime(2022, 7, 29)) - 6
    actual_scm_week_from_date_2021_1_3 = ScmWeek.from_date(datetime(2021, 1, 3)) - 3
    actual_scm_week_from_date_2020_7_29 = ScmWeek.from_date(datetime(2020, 7, 29)) - 2
    actual_scm_week_from_date_2020_1_1 = ScmWeek.from_date(datetime(2020, 1, 1)) - 1

    assert actual_scm_week_from_date_2022_7_29.year == 2022 and actual_scm_week_from_date_2022_7_29.week == 25
    assert actual_scm_week_from_date_2021_1_3.year == 2020 and actual_scm_week_from_date_2021_1_3.week == 51
    assert actual_scm_week_from_date_2020_7_29.year == 2020 and actual_scm_week_from_date_2020_7_29.week == 30
    assert actual_scm_week_from_date_2020_1_1.year == 2020 and actual_scm_week_from_date_2020_1_1.week == 1

    with freeze_time(datetime(2022, 7, 22)):
        actual_scm_week_from_date_2022_7_22 = ScmWeek.from_date(_date=None) - 4
        assert actual_scm_week_from_date_2022_7_22.year == 2022 and actual_scm_week_from_date_2022_7_22.week == 26


def test_weeks():
    with pytest.raises(ValueError):
        ScmWeek(0, 0)

    with pytest.raises(ValueError):
        ScmWeek(2022, 53)

    ScmWeek(2026, 53)

    with pytest.raises(ValueError):
        ScmWeek(2022, 54)

    with pytest.raises(ValueError):
        ScmWeek(10000, 0)


def test_from_number():
    actual_scm_week_from_number_202216 = ScmWeek.from_number(202216)
    actual_scm_week_from_number_202101 = ScmWeek.from_number(202101)
    actual_scm_week_from_number_202010 = ScmWeek.from_number(202010)
    actual_scm_week_from_number_202317 = ScmWeek.from_number(202317)

    assert actual_scm_week_from_number_202216.year == 2022 and actual_scm_week_from_number_202216.week == 16
    assert actual_scm_week_from_number_202101.year == 2021 and actual_scm_week_from_number_202101.week == 1
    assert actual_scm_week_from_number_202010.year == 2020 and actual_scm_week_from_number_202010.week == 10
    assert actual_scm_week_from_number_202317.year == 2023 and actual_scm_week_from_number_202317.week == 17


def test_is_valid_number():
    assert ScmWeek.is_valid_number("202216")
    assert ScmWeek.is_valid_number(202101)
    assert ScmWeek.is_valid_number("202010")
    assert ScmWeek.is_valid_number(202317)


def test_is_not_valid_number():
    assert not ScmWeek.is_valid_number("Scm_week")
    assert not ScmWeek.is_valid_number(3)
    assert not ScmWeek.is_valid_number(datetime(2023, 7, 10))
    assert not ScmWeek.is_valid_number([202117, 202011])


def test_week_range():
    expected_list_of_scm_weeks_2022_1_2022_4 = [ScmWeek(2022, 1), ScmWeek(2022, 2), ScmWeek(2022, 3), ScmWeek(2022, 4)]
    expected_list_of_scm_weeks_2022_50_2022_3 = [
        ScmWeek(2021, 50),
        ScmWeek(2021, 51),
        ScmWeek(2021, 52),
        ScmWeek(2022, 1),
        ScmWeek(2022, 2),
        ScmWeek(2022, 3),
    ]
    expected_list_of_scm_weeks_2020_14_2020_15 = [ScmWeek(2020, 14), ScmWeek(2020, 15)]

    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2022_1_2022_4, list(ScmWeek.range(ScmWeek(2022, 1), ScmWeek(2022, 4)))
    )
    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2022_50_2022_3, list(ScmWeek.range(ScmWeek(2021, 50), ScmWeek(2022, 3)))
    )
    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2020_14_2020_15, list(ScmWeek.range(ScmWeek(2020, 14), ScmWeek(2020, 15)))
    )


def test_range_from_dates():
    expected_list_of_scm_weeks_2021_31_2021_35 = [
        ScmWeek(2021, 31),
        ScmWeek(2021, 32),
        ScmWeek(2021, 33),
        ScmWeek(2021, 34),
        ScmWeek(2021, 35),
    ]
    expected_list_of_scm_weeks_2021_31 = [ScmWeek(2021, 31)]
    expected_list_of_scm_weeks_2022_51_2023_2 = [
        ScmWeek(2022, 51),
        ScmWeek(2022, 52),
        ScmWeek(2023, 1),
        ScmWeek(2023, 2),
    ]

    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2021_31_2021_35,
        list(ScmWeek.range_from_dates(datetime(2021, 7, 29), datetime(2021, 8, 30))),
    )
    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2021_31, list(ScmWeek.range_from_dates(datetime(2021, 7, 29), datetime(2021, 7, 30)))
    )
    check_list_of_scm_weeks(
        expected_list_of_scm_weeks_2022_51_2023_2,
        list(ScmWeek.range_from_dates(datetime(2022, 12, 15), datetime(2023, 1, 5))),
    )


def test_from_str():
    expected_scm_week_2021_4 = ScmWeek(2021, 4)
    expected_scm_week_2020_15 = ScmWeek(2020, 15)
    expected_scm_week_2023_52 = ScmWeek(2023, 52)

    actual_scm_week_from_str_2021_4 = ScmWeek.from_str("2021-W04")
    actual_scm_week_from_str_2020_15 = ScmWeek.from_str("2020-W15")
    actual_scm_week_from_str_2023_52 = ScmWeek.from_str("2023-W52")

    assert (
        actual_scm_week_from_str_2021_4.year == expected_scm_week_2021_4.year
        and actual_scm_week_from_str_2021_4.week == expected_scm_week_2021_4.week
    )
    assert (
        actual_scm_week_from_str_2020_15.year == expected_scm_week_2020_15.year
        and actual_scm_week_from_str_2020_15.week == expected_scm_week_2020_15.week
    )
    assert (
        actual_scm_week_from_str_2023_52.year == expected_scm_week_2023_52.year
        and actual_scm_week_from_str_2023_52.week == expected_scm_week_2023_52.week
    )


def test_to_date_range():
    assert ScmWeek(2021, 15).to_date_range() == (date(2021, 4, 7), date(2021, 4, 13))
    assert ScmWeek(2022, 1).to_date_range() == (date(2021, 12, 29), date(2022, 1, 4))
    assert ScmWeek(2023, 15).to_date_range() == (date(2023, 4, 5), date(2023, 4, 11))


def test_current_week():
    with freeze_time(datetime(2022, 8, 5)):
        current_week = ScmWeek.current_week()
        assert current_week.year == 2022 and current_week.week == 32

    with freeze_time(datetime(2021, 12, 12)):
        current_week = ScmWeek.current_week()
        assert current_week.year == 2021 and current_week.week == 50

    with freeze_time(datetime(2020, 3, 27)):
        current_week = ScmWeek.current_week()
        assert current_week.year == 2020 and current_week.week == 14


def test_min_max():
    max_scm_week = ScmWeek.max()
    min_scm_week = ScmWeek.min()

    assert max_scm_week.year == 9999 and max_scm_week.week == 1
    assert min_scm_week.year == 2 and min_scm_week.week == 2


def test_hash():
    assert hash(ScmWeek(2021, 3)) != hash(ScmWeek(2021, 4))
    assert hash(ScmWeek(2020, 1)) == hash(ScmWeek(2020, 1))


def test_str_repr():
    assert str(ScmWeek(2022, 7)) == "2022-W07"
    assert repr(ScmWeek(2022, 7)) == "2022-W07"


def test_ge():
    assert ScmWeek(2022, 7) >= "2022-W02"
    assert ScmWeek(2022, 1) >= "2022-W01"
    assert ScmWeek(2022, 7) >= ScmWeek(2022, 2)
    assert ScmWeek(2022, 1) >= ScmWeek(2022, 1)


def test_negative_ge():
    assert not ScmWeek(2022, 7) >= "2022-W10"
    assert not ScmWeek(2020, 11) >= "2021-W09"
    assert not ScmWeek(2021, 7) >= ScmWeek(2021, 17)
    assert not ScmWeek(2019, 34) >= ScmWeek(2019, 52)


def test_eq():
    assert ScmWeek(2023, 8) == "2023-W08"
    assert not (ScmWeek(2024, 7) == 202407)
    assert not (ScmWeek(2024, 7) == ScmWeek(2023, 8))
    assert ScmWeek(2023, 8) == ScmWeek(2023, 8)


def test_ne():
    assert ScmWeek(2021, 2) != "2019-W02"
    assert not (ScmWeek(2022, 4) != "2022-W04")
    assert not (ScmWeek(2021, 2) != ScmWeek(2021, 2))
    assert ScmWeek(2021, 2) != ScmWeek(2019, 2)


def test_le():
    assert ScmWeek(2021, 2) <= "2021-W02"
    assert ScmWeek(2020, 5) <= "2022-W14"
    assert ScmWeek(2024, 2) <= "2024-W03"
    assert ScmWeek(2021, 2) <= ScmWeek(2021, 2)
    assert ScmWeek(2020, 5) <= ScmWeek(2022, 14)
    assert ScmWeek(2024, 2) <= ScmWeek(2024, 3)


def test_negative_le():
    assert not ScmWeek(2020, 16) <= "2020-W15"
    assert not ScmWeek(2021, 8) <= "2020-W07"
    assert not ScmWeek(2019, 1) <= "2018-W52"
    assert not ScmWeek(2024, 16) <= "2019-W16"


def test_lt():
    assert ScmWeek(2021, 3) < "2021-W09"
    assert ScmWeek(2020, 5) < "2021-W14"
    assert ScmWeek(2021, 3) < ScmWeek(2021, 9)
    assert ScmWeek(2020, 5) < ScmWeek(2021, 14)


def test_negative_lt():
    assert not ScmWeek(2020, 16) < "2020-W15"
    assert not ScmWeek(2021, 8) < "2021-W08"
    assert not ScmWeek(2019, 1) < "2018-W52"
    assert not ScmWeek(2024, 16) < "2019-W16"


def test_gt():
    assert ScmWeek(2020, 3) > "2019-W04"
    assert ScmWeek(2020, 5) > "2020-W04"
    assert ScmWeek(2020, 3) > ScmWeek(2019, 4)
    assert ScmWeek(2020, 5) > ScmWeek(2020, 4)


def test_negative_gt():
    assert not ScmWeek(2021, 18) > "2021-W19"
    assert not ScmWeek(2020, 1) > "2021-W01"
    assert not ScmWeek(2022, 25) > "2022-W25"
    assert not ScmWeek(2021, 44) > "2022-W44"


def test_int_week():
    assert 202215 == int(ScmWeek(2022, 15))
    assert 202001 == int(ScmWeek(2020, 1))
    assert 201917 == int(ScmWeek(2019, 17))


def _get_list_of_dates(date_):
    return [date_ + timedelta(day) for day in range(7)]


def test_week_days():
    assert ScmWeek(2022, 18).week_days() == _get_list_of_dates(date(2022, 4, 27))
    assert ScmWeek(2024, 18).week_days() == _get_list_of_dates(date(2024, 4, 24))
    assert ScmWeek(2019, 1).week_days() == _get_list_of_dates(date(2018, 12, 26))


def test_weekday_titles():
    config = ScmWeekConfig(0, 9)
    expected = [
        "Monday 1",
        "Tuesday 1",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
        "Monday 2",
        "Tuesday 2",
    ]
    assert ScmWeek.get_weekday_titles(config) == tuple(expected)

    config_mon_plus_1 = ScmWeekConfig(1, 9)
    expected_mon_plus_1 = [
        "Tuesday 1",
        "Wednesday 1",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
        "Monday",
        "Tuesday 2",
        "Wednesday 2",
    ]
    assert ScmWeek.get_weekday_titles(config_mon_plus_1) == tuple(expected_mon_plus_1)

    config_mon_minus_1 = ScmWeekConfig(-1, 9)
    expected_mon_minus_1 = [
        "Sunday 1",
        "Monday 1",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday 2",
        "Monday 2",
    ]
    assert ScmWeek.get_weekday_titles(config_mon_minus_1) == tuple(expected_mon_minus_1)


def test_weekday_titles_extends():
    config = ScmWeekConfig(-1, 8)
    expected = [
        "Friday 1",
        "Saturday 1",
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday 2",
        "Saturday 2",
    ]
    assert ScmWeek.get_weekday_titles(config, extend_left=2, extend_right=-1) == tuple(expected)

    expected = [
        "Tuesday 1",
        "Wednesday 1",
        "Thursday 1",
        "Friday 1",
        "Saturday 1",
        "Sunday 1",
        "Monday",
        "Tuesday 2",
        "Wednesday 2",
        "Thursday 2",
        "Friday 2",
        "Saturday 2",
        "Sunday 2",
    ]
    assert ScmWeek.get_weekday_titles(config, extend_left=-2, extend_right=7) == tuple(expected)

    expected = [
        "Friday 1",
        "Saturday 1",
        "Sunday 1",
        "Monday 1",
        "Tuesday 1",
        "Wednesday 1",
        "Thursday 1",
        "Friday 2",
        "Saturday 2",
        "Sunday 2",
        "Monday 2",
        "Tuesday 2",
        "Wednesday 2",
        "Thursday 2",
        "Friday 3",
        "Saturday 3",
        "Sunday 3",
    ]
    assert ScmWeek.get_weekday_titles(config, extend_left=2, extend_right=7) == tuple(expected)


def test_last_production_day():
    config: ScmWeekConfig = ScmWeekConfig(-10, 8)
    week = ScmWeek(2023, 9)
    assert week.get_last_production_day(config) == date(2023, 2, 24)


def test_week_config_validation():
    with pytest.raises(ValueError):
        ScmWeekConfig(-10, 6)


def test_weekdays_parser():
    assert Weekday.from_index(-1) == Weekday.SUN
    assert Weekday.from_index(-5) == Weekday.WED
    assert Weekday.from_index(-10) == Weekday.FRI

    assert Weekday.from_index(0) == Weekday.MON
    assert Weekday.from_index(5) == Weekday.SAT
    assert Weekday.from_index(9) == Weekday.WED

    assert Weekday.from_index(7) == Weekday.MON
    assert Weekday.from_index(-7) == Weekday.MON
    assert Weekday.from_index(-8) == Weekday.SUN
