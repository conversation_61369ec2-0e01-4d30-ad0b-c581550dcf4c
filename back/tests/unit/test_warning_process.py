import unittest
from unittest.mock import call, patch

from procurement.managers.datasync.framework import warnings


class NotifyTest(unittest.TestCase):
    @patch("procurement.managers.datasync.framework.warnings._add_warning")
    def test_notify(self, warning_listener):
        warnings.notify_warning("test run")
        self.assertEqual(warning_listener.call_args, call({"message": "test run"}, job=None))
