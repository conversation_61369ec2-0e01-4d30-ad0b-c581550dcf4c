import json

from requests import Response


class FakeResponse(Response):
    def __init__(self, url, params, **kwargs):
        super().__init__()

        self.url = url
        self.params = params
        self.kwargs = kwargs

    @property
    def text(self):
        return self._content

    @text.setter
    def text(self, value):
        self._content = value

    def json(self, **kwargs):
        return json.loads(self._content)


def mock_requests(text="", status=200, method="GET", middleware=None):
    def request(url, params=None, **kwargs):
        response = FakeResponse(url, params, **kwargs)
        response.text = text
        response.status_code = status
        response.method = method
        if middleware:
            return middleware(response)
        return response

    return request
