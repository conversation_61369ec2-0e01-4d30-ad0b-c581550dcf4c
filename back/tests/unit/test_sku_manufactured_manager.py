from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.data.dto.ordering.manufactured_skus import ManufacturedSku, ManufacturedSkuPart
from procurement.data.dto.sku import SkuDetail
from procurement.managers.ordering.manufactured_sku import ManufacturedSkuBreakdown


def common_setup(mocker, manufactured_sku_uuid_by_code, manufactured_sku_parts, sku_details_by_uuid, manufactured_skus):
    mocker.patch(
        "procurement.managers.ordering.manufactured_sku.ManufacturedSkuBreakdown.manufactured_sku_uuid_by_code",
        new_callable=mocker.PropertyMock(return_value=manufactured_sku_uuid_by_code),
    )
    mocker.patch(
        "procurement.managers.ordering.manufactured_sku.ManufacturedSkuBreakdown.manufactured_sku_parts",
        new_callable=mocker.PropertyMock(return_value=manufactured_sku_parts),
    )
    mocker.patch(
        "procurement.managers.ordering.manufactured_sku.ManufacturedSkuBreakdown.sku_details_by_uuid",
        new_callable=mocker.PropertyMock(return_value=sku_details_by_uuid),
    )
    mocker.patch(
        "procurement.repository.ordering.manufactured_sku.get_manufactured_skus",
        new_callable=mocker.PropertyMock(return_value=lambda: manufactured_skus),
    )
    return ManufacturedSkuBreakdown()


def test_breakdown_one_sku(mocker):
    manufactured_sku_uuid_by_code = {"code1": "uuid1"}
    manufactured_skus = [
        ManufacturedSku(manufactured_uuid="uuid1", manufactured_code="code1", manufactured_name="name1")
    ]
    manufactured_sku_parts = {
        "uuid1": [
            ManufacturedSkuPart(
                root_uuid="uuid1",
                part_uuid="part_uuid1",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(1000),
                is_manufactured=False,
            )
        ]
    }
    sku_details_by_uuid = {
        "part_uuid1": SkuDetail(
            sku_code="sku_code1",
            sku_uuid="part_uuid1",
            sku_name="sku_name1",
            sku_id=1,
            purchasing_category="purchasing_category",
        )
    }

    manufactured_sku_manager = common_setup(
        mocker, manufactured_sku_uuid_by_code, manufactured_sku_parts, sku_details_by_uuid, manufactured_skus
    )

    expected_result = {"sku_code1": Decimal(1000)}
    assert manufactured_sku_manager.get_breakdown("code1") == expected_result


def test_breakdown_receipt_with_subreceipt(mocker):
    manufactured_sku_uuid_by_code = {"code1": "uuid1", "code2": "part_uuid1"}
    manufactured_skus = [
        ManufacturedSku(manufactured_uuid="uuid1", manufactured_code="code1", manufactured_name="name1"),
        ManufacturedSku(manufactured_uuid="part_uuid1", manufactured_code="code2", manufactured_name="name2"),
    ]
    manufactured_sku_parts = {
        "uuid1": [
            ManufacturedSkuPart(
                root_uuid="uuid1",
                part_uuid="part_uuid1",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(10),
                is_manufactured=True,
            )
        ],
        "part_uuid1": [
            ManufacturedSkuPart(
                root_uuid="part_uuid1",
                part_uuid="part_uuid2",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(10),
                is_manufactured=False,
            )
        ],
    }
    sku_details_by_uuid = {
        "part_uuid2": SkuDetail(
            sku_code="sku_code1",
            sku_uuid="part_uuid2",
            sku_name="sku_name1",
            sku_id=1,
            purchasing_category="purchasing_category",
        )
    }
    manufactured_sku_manager = common_setup(
        mocker, manufactured_sku_uuid_by_code, manufactured_sku_parts, sku_details_by_uuid, manufactured_skus
    )
    expected_result = {"sku_code1": Decimal(100)}
    assert manufactured_sku_manager.get_breakdown("code1") == expected_result


def test_breakdown_receipt_with_sku_adding(mocker):
    manufactured_sku_uuid_by_code = {"code1": "uuid1", "code2": "part_uuid1"}
    manufactured_skus = [
        ManufacturedSku(manufactured_uuid="uuid1", manufactured_code="code1", manufactured_name="name1"),
        ManufacturedSku(manufactured_uuid="part_uuid1", manufactured_code="code2", manufactured_name="name2"),
    ]
    manufactured_sku_parts = {
        "uuid1": [
            ManufacturedSkuPart(
                root_uuid="uuid1",
                part_uuid="part_uuid1",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(10),
                is_manufactured=True,
            ),
            ManufacturedSkuPart(
                root_uuid="uuid1",
                part_uuid="part_uuid2",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(20),
                is_manufactured=False,
            ),
        ],
        "part_uuid1": [
            ManufacturedSkuPart(
                root_uuid="part_uuid1",
                part_uuid="part_uuid2",
                unit_of_measure=UnitOfMeasure.POUND,
                quantity=Decimal(10),
                is_manufactured=False,
            )
        ],
    }
    sku_details_by_uuid = {
        "part_uuid2": SkuDetail(
            sku_code="sku_code1",
            sku_uuid="part_uuid2",
            sku_name="sku_name1",
            sku_id=1,
            purchasing_category="purchasing_category",
        )
    }
    manufactured_sku_manager = common_setup(
        mocker, manufactured_sku_uuid_by_code, manufactured_sku_parts, sku_details_by_uuid, manufactured_skus
    )
    expected_result = {"sku_code1": Decimal(120)}
    assert manufactured_sku_manager.get_breakdown("code1") == expected_result
