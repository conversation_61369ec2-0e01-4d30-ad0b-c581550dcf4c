from datetime import date
from unittest.mock import patch

import pytest

from procurement.core.dates import ScmWeek, ScmWeekConfig
from procurement.managers.datasync.forecast_upload import get_updates, reduce_factor_forecast_days

FACTOR_WEEK_CONFIG = ScmWeekConfig(-10, 12)
SKU_1 = "sku1"
SKU_2 = "sku2"

# fetcher data, cached data with difference
FETCHER_DATA = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_2, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 0},
}

# different set of data in cached and updates
NORMALIZED_DATA = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_2, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 0},
}

# /--- Case 1: data updated compared to cached values
CACHE_DATA = {
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 11)): {ScmWeek.from_str("2021-W51"): 100},
    (SKU_1, date(2020, 12, 12)): {ScmWeek.from_str("2021-W51"): 125},
}

NEW_DATA_WITH_CHANGES = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
    (SKU_2, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 0},
    (SKU_2, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
    (SKU_2, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
}

UPDATES_WITH_DIFF = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {},
    (SKU_1, date(2020, 12, 11)): {ScmWeek.from_str("2021-W51"): 0},
    (SKU_1, date(2020, 12, 12)): {ScmWeek.from_str("2021-W51"): 0},
    (SKU_2, date(2020, 12, 8)): {},
    (SKU_2, date(2020, 12, 9)): {},
    (SKU_2, date(2020, 12, 10)): {},
}
# ---/


# /--- Case 2,3: no updates in new data
NORMALIZED_DATA_WITHOUT_DIFF = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 11)): {ScmWeek.from_str("2020-W51"): 0},
}

CACHE_DATA_WITHOUT_DIFF = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 12)): {},
}

# /--- Case 3: no updates in new data and cache contains old days
OLD_DAYS_CACHE_DATA_WITHOUT_DIFF = {
    (SKU_1, date(2020, 12, 6)): {ScmWeek.from_str("2020-W50"): 90},
    (SKU_1, date(2020, 12, 7)): {ScmWeek.from_str("2020-W50"): 80},
    # ^^^ old days should be ignored if they are still in cache
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 12)): {},
}

WITHOUT_DIFF_UPDATES = {
    (SKU_1, date(2020, 12, 8)): {},
    (SKU_1, date(2020, 12, 9)): {},
    (SKU_1, date(2020, 12, 10)): {},
    (SKU_1, date(2020, 12, 11)): {},
    (SKU_1, date(2020, 12, 12)): {},
}
# ---/


# /--- Case 4: new data and no cached values
NO_CACHE_DATA = {}

NORMALIZED_DATA_NO_CACHE = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
}


NO_CACHE_UPDATES = {
    (SKU_1, date(2020, 12, 8)): {ScmWeek.from_str("2020-W50"): 100},
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {},
}
# ---/


# /--- Case 5: force push is True and previously existing forecast disappeared

FORECE_CACHE_DATA = {
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 11)): {ScmWeek.from_str("2021-W51"): 100},
    (SKU_1, date(2020, 12, 12)): {ScmWeek.from_str("2021-W51"): 125},
}

FORCE_MISSING_SKU_NEW_DATA = {
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W51"): 50},
    # missing (SKU_1, date(2020, 12, 11))
    (SKU_1, date(2020, 12, 12)): {ScmWeek.from_str("2021-W51"): 125},
    (SKU_1, date(2020, 12, 13)): {ScmWeek.from_str("2021-W51"): 0},
}

FORCE_MISSING_SKU_EXPECTED_UPDATES = {
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W51"): 0},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W51"): 50},
    (SKU_1, date(2020, 12, 11)): {ScmWeek.from_str("2021-W51"): 0},
    (SKU_1, date(2020, 12, 12)): {ScmWeek.from_str("2021-W51"): 125},
    (SKU_1, date(2020, 12, 13)): {ScmWeek.from_str("2021-W51"): 0},
}

# ---/

# /--- Case 6: force push is True and previously existing forecast disappeared with overlapping weeks

FORCE_MISSING_SKU_OVERLAP_WEEKS_CACHE_DATA = {
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 90, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
}

FORCE_MISSING_SKU_OVERLAP_WEEKS_NEW_DATA = {
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W51"): 120},
}

FORCE_MISSING_SKU_OVERLAP_WEEKS_EXPECTED_UPDATES = {
    (SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 120},
    (SKU_1, date(2020, 12, 10)): {ScmWeek.from_str("2020-W50"): 0, ScmWeek.from_str("2020-W51"): 0},
}

# ---/


DAY_FROM = date(2020, 12, 8)
DAY_TO = date(2020, 12, 10)


@pytest.mark.parametrize(
    "normalized_data,cache_data,updates",
    [
        (NEW_DATA_WITH_CHANGES, CACHE_DATA, UPDATES_WITH_DIFF),
        (NORMALIZED_DATA_WITHOUT_DIFF, CACHE_DATA_WITHOUT_DIFF, WITHOUT_DIFF_UPDATES),
        (NORMALIZED_DATA_WITHOUT_DIFF, OLD_DAYS_CACHE_DATA_WITHOUT_DIFF, WITHOUT_DIFF_UPDATES),
        (NORMALIZED_DATA_NO_CACHE, NO_CACHE_DATA, NO_CACHE_UPDATES),
    ],
)
def test_get_updates(normalized_data, cache_data, updates):
    updates_data = get_updates(normalized_data, cache_data, DAY_FROM)
    assert updates_data == updates


@pytest.mark.parametrize(
    "cached_data,new_data,expected_updates",
    [
        (FORECE_CACHE_DATA, FORCE_MISSING_SKU_NEW_DATA, FORCE_MISSING_SKU_EXPECTED_UPDATES),
        (
            FORCE_MISSING_SKU_OVERLAP_WEEKS_CACHE_DATA,
            FORCE_MISSING_SKU_OVERLAP_WEEKS_EXPECTED_UPDATES,
            FORCE_MISSING_SKU_OVERLAP_WEEKS_EXPECTED_UPDATES,
        ),
    ],
)
def test_get_updates_with_forced(cached_data, new_data, expected_updates):
    updates = get_updates(normalized_data=new_data, cached_data=cached_data, upload_start_date=DAY_FROM, force=True)
    assert expected_updates == updates


@patch("procurement.managers.admin.brand_admin.get_week_config", return_value=FACTOR_WEEK_CONFIG)
def test_reduce_factor_days(config_patch):
    actual_result = reduce_factor_forecast_days(
        normalized_data={(SKU_1, date(2020, 12, 9)): {ScmWeek.from_str("2020-W50"): 10}}
    )
    expected_result = {(SKU_1, date(2020, 12, 3)): {ScmWeek.from_str("2020-W50"): 10}}
    assert actual_result == expected_result
