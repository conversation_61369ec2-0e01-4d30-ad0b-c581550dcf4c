from procurement.client.googlesheets import DataRange


def test_data_range():
    assert DataRange(last_column="A").last_column_formatted == "A"
    assert DataRange(last_column="AA").last_column_formatted == "AA"
    assert DataRange(last_column="AQ").last_column_formatted == "AQ"
    assert DataRange(last_column="BA").last_column_formatted == "BA"
    assert DataRange(last_column="CI").last_column_formatted == "CI"
    assert DataRange(last_column="ABC").last_column_formatted == "ABC"
