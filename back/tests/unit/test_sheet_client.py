import unittest
from collections import defaultdict
from unittest.mock import Mock, patch

from googleapiclient.errors import HttpError

from procurement.client.googlesheets.client import GoogleSheetClient
from procurement.client.googlesheets.errors import GSheetsQuotaExceed
from procurement.client.googlesheets.model import DataRange
from procurement.core import cache_utils, metrics

with patch.object(metrics, "PROMETHEUS_REGISTRY"), patch.object(cache_utils, "config", defaultdict(lambda: {})):

    class FakeSheetResource:
        def __init__(self):
            self.actions = []

        def values(self):
            self.actions.append("get values")
            return self

        def spreadsheets(self):
            self.actions.append("get spreadsheets")
            return self

        def get(self, *args, **kwargs):
            self.actions.append(["get get", args, kwargs])
            return self

        def update(self, *args, **kwargs):
            self.actions.append(["update", args, kwargs])
            return self

        def clear(self, *args, **kwargs):
            self.actions.append(["get clear", args, kwargs])
            return self

        def append(self, *args, **kwargs):
            self.actions.append(["get clear", args, kwargs])
            return self

        def execute(self):
            self.actions.append("execute")
            return {"values": ["a", "b", "c"]}

    def fake_google_service_init(self):
        self.sheets_resource = FakeSheetResource()

    class Test(unittest.TestCase):
        def setUp(self):
            with patch.object(GoogleSheetClient, "__init__", fake_google_service_init):
                self.service = GoogleSheetClient()

        def test_read(self):
            self.assertEqual(["a", "b", "c"], self.service.read("sheet_id", "sheet_name"))
            expected_actions = [
                "get spreadsheets",
                "get values",
                [
                    "get get",
                    (),
                    {"spreadsheetId": "sheet_id", "range": "sheet_name!A2:A", "valueRenderOption": "FORMATTED_VALUE"},
                ],
                "execute",
            ]
            self.assertListEqual(self.service.sheets_resource.actions, expected_actions)

        def test_update(self):
            self.service.update(
                "sheet_id",
                "sheet_name",
                data=[["a", "b", "c"]],
                data_range=DataRange(
                    first_column="A",
                    last_column="C",
                    first_row=1,
                    last_row=1,
                ),
            )

            expected_actions = [
                "get spreadsheets",
                "get values",
                [
                    "update",
                    (),
                    {
                        "body": {"values": [["a", "b", "c"]]},
                        "range": "sheet_name!A1:C1",
                        "spreadsheetId": "sheet_id",
                        "valueInputOption": "USER_ENTERED",
                    },
                ],
                "execute",
            ]
            self.assertListEqual(self.service.sheets_resource.actions, expected_actions)

        def test_replace(self):
            self.service.update(
                "sheet_id",
                "sheet_name",
                data=[["a", "b", "c", None]],
                data_range=DataRange(
                    first_column="A",
                    last_column="D",
                    first_row=1,
                    last_row=3,
                ),
            )
            expected_actions = [
                "get spreadsheets",
                "get values",
                [
                    "update",
                    (),
                    {
                        "body": {"values": [["a", "b", "c", ""], ["", "", "", ""], ["", "", "", ""]]},
                        "range": "sheet_name!A1:D3",
                        "spreadsheetId": "sheet_id",
                        "valueInputOption": "USER_ENTERED",
                    },
                ],
                "execute",
            ]
            self.assertListEqual(self.service.sheets_resource.actions, expected_actions)

        def test_replace_without_headers(self):
            self.service.update(
                "sheet_id",
                "sheet_name",
                data=[["a", "b", "c"]],
                data_range=DataRange(
                    first_column="A",
                    last_column="D",
                    first_row=2,
                    last_row=4,
                ),
            )
            expected_actions = [
                "get spreadsheets",
                "get values",
                [
                    "update",
                    (),
                    {
                        "body": {"values": [["a", "b", "c", ""], ["", "", "", ""], ["", "", "", ""]]},
                        "range": "sheet_name!A2:D4",
                        "spreadsheetId": "sheet_id",
                        "valueInputOption": "USER_ENTERED",
                    },
                ],
                "execute",
            ]
            self.assertListEqual(self.service.sheets_resource.actions, expected_actions)

        def test_replace_auto_size(self):
            self.service.update(
                "sheet_id",
                "sheet_name",
                data=[["a", "b", "c"]],
                data_range=DataRange(first_column="A", first_row=1),
            )

            expected_actions = [
                "get spreadsheets",
                "get values",
                [
                    "update",
                    (),
                    {
                        "body": {"values": [["a", "b", "c"]]},
                        "range": "sheet_name!A1:C1",
                        "spreadsheetId": "sheet_id",
                        "valueInputOption": "USER_ENTERED",
                    },
                ],
                "execute",
            ]
            self.assertListEqual(self.service.sheets_resource.actions, expected_actions)

    class Test2(unittest.TestCase):
        def setUp(self):
            with patch.object(GoogleSheetClient, "__init__", fake_google_service_init):
                self.service = GoogleSheetClient()

        def test_retry_decorator(self):
            self.retry_count = 1

            def retry(resource_self):
                if not self.retry_count:
                    return resource_self

                self.retry_count -= 1
                raise GSheetsQuotaExceed(HttpError(Mock(), b""))

            FakeSheetResource.spreadsheets = retry

            self.service.update("sheet_id", "sheet_name", data=[("a", "b", "c")])
            self.assertEqual(self.retry_count, 0)
            self.retry_count = 1
            self.service.read("sheet_id", "sheet_name")
            self.assertEqual(self.retry_count, 0)
