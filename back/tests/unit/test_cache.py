import functools
from typing import FrozenSet
from unittest import TestCase

import pytest

from procurement.core.cache_utils import caching as cache_utils


def simple_decorator(fn):
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        return fn(*args, **kwargs)

    return wrapper


@simple_decorator
def foo(x: int, y: int, a: int = 5, b: int = 42, c: bool = False, d: str = None):
    ab = a
    return ab


@simple_decorator
def simple_foo():
    pass


@simple_decorator
def arbitrary_foo(a: int, b: int = 5, *args):
    pass


@simple_decorator
def another_arbitrary_foo(a: int, b: int = 5, **kwargs):
    pass


@simple_decorator
def decorated_typed_fun(a: str, b: FrozenSet, c: int = 5):
    return a


@simple_decorator
def decorated_untyped_fun(a, b, c):
    return a


@simple_decorator
def decorated_unhashable_arg_func(a: list):
    return a


class TestHashFuncCall(TestCase):
    def test_hash_equality(self):
        self.assertEqual(hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, y=2, x=1)))
        self.assertEqual(hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, 1, y=2)))
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, 1, 2, a=5))
        )
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2)),
            hash(cache_utils._hash_func_call(foo, 1, 2, d=None, c=False, b=42, a=5)),
        )
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, a=1, b=2, y=4, x=1)),
            hash(cache_utils._hash_func_call(foo, 1, 4, a=1, b=2)),
        )
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2, 8, 42)),
            hash(cache_utils._hash_func_call(foo, b=42, a=8, x=1, y=2)),
        )
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, 1, None, None, None)),
            hash(cache_utils._hash_func_call(foo, b=None, a=None, x=1, y=None)),
        )
        self.assertEqual(
            hash(cache_utils._hash_func_call(foo, 1, None, None, None, True)),
            hash(cache_utils._hash_func_call(foo, c=True, b=None, a=None, x=1, y=None)),
        )

        self.assertEqual(hash(cache_utils._hash_func_call(simple_foo)), hash(cache_utils._hash_func_call(simple_foo)))

    def test_hash_non_equality(self):
        self.assertNotEqual(hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, 1, 5)))
        self.assertNotEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, y=5, x=1))
        )
        self.assertNotEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, 1, 2, None, None))
        )
        self.assertNotEqual(
            hash(cache_utils._hash_func_call(foo, 1, 2)), hash(cache_utils._hash_func_call(foo, 1, 2, a=None, b=None))
        )


class TestHashableSignatureCheck(TestCase):
    def test_hashable_signature(self):
        cache_utils.verify_hashable_signature(foo)
        cache_utils.verify_hashable_signature(simple_foo)
        cache_utils.verify_hashable_signature(decorated_typed_fun)

    def test_unhashable_signature(self):
        non_function = "I'm a callable!"

        with pytest.raises(RuntimeError):
            cache_utils.verify_hashable_signature(arbitrary_foo)
        with pytest.raises(RuntimeError):
            cache_utils.verify_hashable_signature(another_arbitrary_foo)
        with pytest.raises(RuntimeError):
            cache_utils.verify_hashable_signature(decorated_untyped_fun)
        with pytest.raises(RuntimeError):
            cache_utils.verify_hashable_signature(decorated_unhashable_arg_func)
        with pytest.raises(RuntimeError):
            cache_utils.verify_hashable_signature(non_function)


class TestNoArgumentsCheck(TestCase):
    def test_no_args(self):
        cache_utils._verify_no_args(simple_foo)

    def test_with_args(self):
        non_function = "I'm a callable!"

        with pytest.raises(RuntimeError):
            cache_utils._verify_no_args(non_function)
        with pytest.raises(RuntimeError):
            cache_utils._verify_no_args(arbitrary_foo)
        with pytest.raises(RuntimeError):
            cache_utils._verify_no_args(foo)
