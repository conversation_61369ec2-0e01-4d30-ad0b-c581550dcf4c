import unittest
from datetime import datetime
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import GSHEET_DATE_FORMAT
from procurement.data.googlesheet_model.core import Cell, DateCell, SheetModel, SheetRow

raw_date = datetime(2021, 1, 1)


class FakeSheetRow(SheetRow):
    cell_b: Annotated[str, Cell("B")]
    cell_a: Annotated[str, Cell("A")]
    cell_d: Annotated[str, Cell("D")]
    cell_date_a: Annotated[datetime, DateCell("F", GSHEET_DATE_FORMAT, print_format="%m-%d-%Y")]


class FakeSheetModel(SheetModel[FakeSheetRow]):
    first_row = 2
    parent_doc = None


class Test(unittest.TestCase):
    def test_from_dict(self):
        model = FakeSheetModel()
        rows = model.from_records([{"cell_a": "a", "cell_b": "b", "cell_d": "d", "cell_date_a": raw_date}])

        first_record = rows[0]

        self.assertEqual(first_record.cell_a, "a")
        self.assertEqual(first_record.cell_b, "b")
        self.assertEqual(first_record.cell_d, "d")
        self.assertEqual(first_record.cell_date_a, raw_date)
