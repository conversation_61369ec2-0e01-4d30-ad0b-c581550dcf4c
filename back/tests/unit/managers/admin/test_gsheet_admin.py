import unittest
from dataclasses import dataclass
from unittest.mock import Mock, patch

from procurement.core.dates import ScmWeek
from procurement.managers.admin import gsheet_admin


@dataclass
class TestGsheetMeta:
    id: int
    doc_code: str
    brand: str


@dataclass
class TestGsheet:
    meta_id: int
    scm_week: str


DOC_1_HF = TestGsheetMeta(1, "doc_1", "HF")
DOC_1_EP = TestGsheetMeta(2, "doc_1", "EP")
DOC_2_HF = TestGsheetMeta(3, "doc_2", "HF")

GSHEET_META = (
    DOC_1_HF,
    DOC_1_EP,
    DOC_2_HF,
)

VALIDATION_CONFIG = {
    ("doc_1", "HF"): [ScmWeek(2021, 45), ScmWeek(2021, 46)],
    ("doc_1", "EP"): [ScmWeek(2021, 45), ScmWeek(2021, 46)],
    ("doc_2", "HF"): [ScmWeek(2021, 45), ScmWeek(2021, 46), ScmWeek(2021, 47)],
}


class GsheetAdmitTest(unittest.TestCase):
    def _test_week_doc_availability(self, test_meta, available_gsheets, validation_config, expected):
        meta_mock = Mock(return_value=test_meta)
        gsheet_mock = Mock(return_value=available_gsheets)

        with (
            patch("procurement.repository.inventory.gsheet_admin.get_meta", meta_mock),
            patch("procurement.repository.inventory.gsheet_admin.get_docs", gsheet_mock),
        ):
            actual = gsheet_admin.check_weekly_docs_availability(validation_config)
            self.assertEqual(expected, actual)

    def test_all_docs_available(self):
        self._test_week_doc_availability(
            test_meta=GSHEET_META,
            available_gsheets=(
                TestGsheet(DOC_1_HF.id, "2021-W45"),
                TestGsheet(DOC_1_HF.id, "2021-W46"),
                TestGsheet(DOC_1_EP.id, "2021-W45"),
                TestGsheet(DOC_1_EP.id, "2021-W46"),
                TestGsheet(DOC_2_HF.id, "2021-W45"),
                TestGsheet(DOC_2_HF.id, "2021-W46"),
                TestGsheet(DOC_2_HF.id, "2021-W47"),
            ),
            validation_config=VALIDATION_CONFIG,
            expected=[],
        )

    def test_only_ep_doc_missing(self):
        self._test_week_doc_availability(
            test_meta=GSHEET_META,
            available_gsheets=(
                TestGsheet(DOC_1_HF.id, "2021-W45"),
                TestGsheet(DOC_1_HF.id, "2021-W46"),
                TestGsheet(DOC_1_EP.id, "2021-W45"),
                TestGsheet(DOC_2_HF.id, "2021-W45"),
                TestGsheet(DOC_2_HF.id, "2021-W46"),
                TestGsheet(DOC_2_HF.id, "2021-W47"),
            ),
            validation_config=VALIDATION_CONFIG,
            expected=[(DOC_1_EP, [ScmWeek(2021, 46)])],
        )

    def test_all_hf_docs_missing(self):
        self._test_week_doc_availability(
            test_meta=GSHEET_META,
            available_gsheets=(
                TestGsheet(DOC_1_EP.id, "2021-W45"),
                TestGsheet(DOC_1_EP.id, "2021-W46"),
            ),
            validation_config=VALIDATION_CONFIG,
            expected=[
                (DOC_1_HF, [ScmWeek(2021, 45), ScmWeek(2021, 46)]),
                (DOC_2_HF, [ScmWeek(2021, 45), ScmWeek(2021, 46), ScmWeek(2021, 47)]),
            ],
        )

    def test_same_doc_missing_for_diff_week(self):
        self._test_week_doc_availability(
            test_meta=GSHEET_META,
            available_gsheets=(
                TestGsheet(DOC_1_HF.id, "2021-W46"),
                TestGsheet(DOC_1_EP.id, "2021-W45"),
                TestGsheet(DOC_2_HF.id, "2021-W45"),
                TestGsheet(DOC_2_HF.id, "2021-W46"),
                TestGsheet(DOC_2_HF.id, "2021-W47"),
            ),
            validation_config=VALIDATION_CONFIG,
            expected=[
                (DOC_1_HF, [ScmWeek(2021, 45)]),
                (DOC_1_EP, [ScmWeek(2021, 46)]),
            ],
        )

    def test_all_missing(self):
        self._test_week_doc_availability(
            test_meta=GSHEET_META,
            available_gsheets=[],
            validation_config=VALIDATION_CONFIG,
            expected=[
                (DOC_1_HF, [ScmWeek(2021, 45), ScmWeek(2021, 46)]),
                (DOC_1_EP, [ScmWeek(2021, 45), ScmWeek(2021, 46)]),
                (DOC_2_HF, [ScmWeek(2021, 45), ScmWeek(2021, 46), ScmWeek(2021, 47)]),
            ],
        )
