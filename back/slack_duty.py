import json
import os
from datetime import date, datetime

import google_auth_httplib2
import googleapiclient
import httplib2
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from slack_webhook import Slack

SLACK_URL = os.environ.get("SLACK_URL", "")
SCHEDULE_TAB = "Schedule"
NOTIFICATIONS = "Notifications"
CONTACTS_TAB = "Contacts"
CHANNEL = os.environ.get("CHANNEL", "ext-imt-gd-channnel")
SCHEDULE_GSHEET_ID = "1JoPbT_ti2bsXNcC-zQYvzegck6utK45-aRAxuEO7uGY"


def _load_config():
    try:
        return json.loads(os.environ.get("GSHEET_CONFIG"))
    except Exception:
        config_file_path = os.environ.get("GSHEET_CONFIG", "config/googlesheets.config.json")
        with open(config_file_path, encoding="utf-8") as config_file:
            return json.load(config_file)


googlesheet_sa = _load_config()

credentials = Credentials.from_service_account_info(
    googlesheet_sa, scopes=["https://www.googleapis.com/auth/spreadsheets"]
)


def build_request(http, *args, **kwargs):  # pylint: disable=unused-argument
    new_http = google_auth_httplib2.AuthorizedHttp(credentials, http=httplib2.Http())
    return googleapiclient.http.HttpRequest(new_http, *args, **kwargs)


resource = build(
    "sheets",
    "v4",
    credentials=credentials,
    requestBuilder=build_request,
    cache=False,
)


def read_gsheet(spreadsheet_id, data_range, is_formatted):
    kwargs = dict(
        spreadsheetId=spreadsheet_id,
        range=data_range,
        valueRenderOption="FORMATTED_VALUE" if is_formatted else "UNFORMATTED_VALUE",
    )
    return resource.spreadsheets().values().get(**kwargs).execute().get("values", [])


def append_gsheet(spreadsheet_id, data_range, data):
    resource.spreadsheets().values().append(
        spreadsheetId=spreadsheet_id, range=data_range, body={"values": data}, valueInputOption="RAW"
    ).execute()


def send_message(message):
    slack = Slack(url=SLACK_URL)
    slack.post(
        channel=CHANNEL,
        username="Duty bot",
        icon_url="https://www.shareicon.net/data/512x512/2015/11/19/674721_support_512x512.png",
        text=message,
        pins="add",
    )


def build_message(user):
    data = read_gsheet(
        spreadsheet_id=SCHEDULE_GSHEET_ID,
        data_range=f"{CONTACTS_TAB}!A2:E",
        is_formatted=True,
    )
    contacts = {
        line[0]: dict(name=line[0], slack_id=line[2], phone=line[3], email=line[4]) for line in data if len(line) >= 5
    }
    user_data = contacts.get(user)
    if user_data is None:
        raise ValueError(f"User {user} not found in '{CONTACTS_TAB}' tab")
    message = (
        f"Today on support {user_data['name']}, Slack: <@{user_data['slack_id']}>, "
        f"Email: {user_data['email']}, Phone Number: {user_data['phone']} "
    )
    return message


def notify_todays_shift():
    schedule = read_gsheet(
        SCHEDULE_GSHEET_ID,
        f"{SCHEDULE_TAB}!A1:D",
        is_formatted=True,
    )
    participants = schedule.pop(0)[1:]
    today = date.today()
    for line in schedule[2:]:
        date_str = line.pop(0)
        day = datetime.strptime(date_str, "%A, %B %d, %Y").date() if date_str else None
        if today == day:
            user_index = next((i for i, v in enumerate(line) if v), None)
            if user_index is None:
                raise ValueError(f"No shifts added for {today}")
            message = build_message(participants[user_index])
            send_message(message)
            return
    raise ValueError(f"No shifts added for {today}")


if __name__ == "__main__":
    notify_todays_shift()
