import logging
import os
import socket

import sqlalchemy as sqla

from procurement.core import initialize
from procurement.core.config_utils import config

from .services import kafka as kafka_service


def init_logger():
    _logger = logging.getLogger()
    formatter = logging.Formatter("%(process)d %(asctime)s %(name)-12s %(levelname)-8s %(message)s")
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    _logger.addHandler(stream_handler)
    _logger.setLevel(os.environ.get("LOG_LEVEL", logging.DEBUG))
    return _logger


logger = init_logger()


def get_hj_config():
    return config["hj_db"]


def init():
    initialize.init_all()
    try:
        logger.info("Start preparing MSSQL DB")
        recreate_dbs()
        insert_data()
    except Exception:
        logger.exception("Can not init MSSQL DB")
    else:
        logger.info("MSSQL DB initialization done")

    try:
        logger.info("Start preparing Kafka service")
        kafka_service.init_kafka()
    except Exception:
        logger.exception("Can not init Kafka")
    else:
        logger.info("Kafka initialization done")


def recreate_dbs(hj_config=None):
    commands = get_files("init/command")
    for command in commands:
        run_command(command, hj_config=hj_config)


def insert_data():
    inserts = get_files("init/data")
    for inserts_path in inserts:
        load_data(inserts_path)


def get_engine(hj_config=None):
    hj_config = hj_config or get_hj_config()
    host = hj_config["host"]
    host = socket.gethostbyname(host)
    engine = sqla.create_engine(
        url=sqla.URL.create(
            drivername="mssql+pyodbc",
            username=hj_config["user"],
            password=hj_config["password"],
            host=host,
            port=hj_config["port"],
            database=hj_config["database"],
            query={"driver": "ODBC Driver 18 for SQL Server", "TrustServerCertificate": "yes"},
        ),
    )
    return engine


def load_data(inserts_path):
    logger.info("LOAD %s", inserts_path)
    with open(inserts_path, encoding="utf-8") as inserts_file:
        queries = inserts_file.readlines()
    engine = get_engine()
    with engine.connect() as conn:
        for query in queries:
            conn.execute(sqla.text(query))
        conn.commit()


def get_files(path):
    logger.info("GET %s", path)
    return map(lambda file: os.path.join(path, file), os.listdir(path))


def run_command(command_path, hj_config):
    logger.info("RUN %s", command_path)
    with open(command_path, encoding="utf-8") as commands_file:
        command = commands_file.read()
    engine = get_engine(hj_config=hj_config)
    with engine.connect() as conn:
        conn.execute(sqla.text(command))
        conn.commit()
