import logging
import time

from confluent_kafka import KafkaException, admin

from procurement.core.config_utils import config

logger = logging.getLogger(__name__)


def init_kafka():
    kafka_config = config["kafka"]
    client = admin.AdminClient(
        {
            "bootstrap.servers": ",".join(kafka_config["bootstrap_servers"]),
            "client.id": kafka_config["client_id"],
            "group.id": kafka_config["group_id"],
        }
    )
    topics = []
    for topic_type in ("produce", "consume"):
        topics.extend(
            admin.NewTopic(topic=topic, num_partitions=1) for topic in kafka_config["topics"][topic_type].values()
        )
    futures = client.create_topics(topics)

    # Wait for all futures to complete
    while any(fut.running() for fut in futures.values()):
        time.sleep(0.5)

    failed = []
    for topic, fut in futures.items():
        try:
            fut.result()
        except KafkaException as e:
            if e.args and e.args[0].code() == admin.KafkaError.TOPIC_ALREADY_EXISTS:
                logger.debug("Topic already exists: %s", topic)
            else:
                logger.warning("Failed to create topic %s", topic, exc_info=True)
                failed.append(topic)

    if failed:
        raise RuntimeError(f"Failed to create topics: {failed}")
