insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00269765', N'PHF-10-50243-4', N'EP, Carrot - 3 Ounce (oz)', N'10/13/19', 800, 800, N'H', N'1941EN372666_E1', N'QCHOLD', N'10/10/19', 201941, N'Produce', N'HF01', N'10/03/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00271384', N'PHF-10-10973-4', N'Carrot - 6 Ounce (oz)', N'10/20/19', 800, 800, N'H', null, N'C-06-42-1', null, null, N'Produce', N'HF01', N'10/10/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00272975', N'PHF-10-50243-4', N'EP, Carrot - 3 Ounce (oz)', N'10/13/19', 800, 800, N'H', N'1941EN372666_E1', N'QCHOLD', N'10/12/19', 201941, N'Produce', N'HF01', N'10/03/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00273544', N'PHF-10-10973-4', N'Carrot - 6 Ounce (oz)', N'10/24/19', 400, 400, N'H', null, N'C-06-53-1', null, null, N'Produce', N'HF01', N'10/10/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00274087', N'PHF-10-10973-4', N'Carrot - 6 Ounce (oz)', N'10/23/19', 800, 800, N'H', null, N'C-05-22-1', null, null, N'Produce', N'HF01', N'10/13/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00274109', N'PHF-10-10973-4', N'Carrot - 6 Ounce (oz)', N'10/24/19', 400, 400, N'H', null, N'C-05-20-1', null, null, N'Produce', N'HF01', N'10/10/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00274464', N'PHF-10-50244-2', N'EP, Cucumber, Persian - 1 Piece (pc)', N'10/23/19', 3000, 3000, N'H', N'1942EN413379_O2', N'N-02-31-1', N'10/14/19', 201942, N'Produce', N'HF01', N'10/13/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00274466', N'PHF-10-50244-2', N'EP, Cucumber, Persian - 1 Piece (pc)', N'10/23/19', 2100, 2100, N'H', N'1942EN413379_O2', N'AB OUT', N'10/14/19', 201942, N'Produce', N'HF01', N'10/13/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00276226', N'PHF-10-10365-4', N'Scallions - 2 Piece (pc)', N'10/16/19', 1800, 1800, N'H', null, N'STGDR-22', null, null, N'Produce', N'HF01', N'10/03/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00280447', N'PHF-10-10411-4', N'Thyme, Fresh - 0.25 Ounce (oz)', N'10/25/19', 2800, 2800, N'H', null, N'AB OUT', null, null, N'Produce', N'HF01', N'10/15/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00280824', N'PHF-10-50294-4', N'EP, Carrot - 6 Ounce (oz)', N'10/20/19', 320, 320, N'H', null, N'EC01', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282245', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/28/19', 800, 800, N'H', N'1943EN181953_O1', N'C-02-48-4', N'10/19/19', 201943, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282247', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/28/19', 800, 800, N'H', N'1943EN181953_O1', N'C-02-45-4', N'10/19/19', 201943, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282248', N'PHF-10-10365-4', N'Scallions - 2 Piece (pc)', N'10/27/19', 4000, 4000, N'H', null, N'C-06-33-1', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282270', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-40-4', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282295', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/29/19', 800, 800, N'H', N'1943EN738618_O1', N'C-02-38-4', N'10/19/19', 201943, N'Produce', N'HF01', N'10/19/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282296', N'PHF-10-50243-4', N'EP, Carrot - 3 Ounce (oz)', N'10/27/19', 800, 0, N'H', null, N'C-02-36-4', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282298', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-33-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282399', N'PHF-10-50243-4', N'EP, Carrot - 3 Ounce (oz)', N'10/27/20', 800, 0, N'A', null, N'C-02-28-1', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282457', N'PHF-10-10365-4', N'Scallions - 2 Piece (pc)', N'10/27/19', 4000, 4000, N'H', null, N'C-06-41-2', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282468', N'PHF-10-50220-4', N'EP, Squash, Zucchini - 8 Ounce (oz)', N'10/29/19', 280, 280, N'H', N'1943EN738618_O1', N'AB OUT', N'10/19/19', 201943, N'Produce', N'HF01', N'10/19/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00282640', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-46-1', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00283186', N'PHF-10-10365-4', N'Scallions - 2 Piece (pc)', N'10/27/19', 4000, 4000, N'H', null, N'C-06-43-1', null, null, N'Produce', N'HF01', N'10/17/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00283290', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-45-2', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00283345', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-37-2', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00283347', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-34-2', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284027', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-47-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284088', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-40-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284141', N'PHF-10-10341-4', N'Radishes - 3 Piece (pc)', N'10/24/19', 3000, 3000, N'H', null, N'C-06-14-2', null, null, N'Produce', N'HF01', N'10/14/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284199', N'PHF-10-10341-4', N'Radishes - 3 Piece (pc)', N'10/24/19', 450, 450, N'H', null, N'LOST', null, null, N'Produce', N'HF01', N'10/14/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284223', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-37-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284225', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-31-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284320', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 800, 800, N'H', null, N'C-02-30-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00284321', N'PHF-10-50296-4', N'EP, Carrot - 12 Ounce (oz)', N'10/27/19', 600, 600, N'H', null, N'C-02-21-1', null, null, N'Produce', N'HF01', N'10/18/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285186', N'PHF-10-10051-4', N'Broccoli, Florets - 8 Ounce (oz)', N'10/25/19', 200, 200, N'H', null, N'AB OUT', null, null, N'Produce', N'HF01', N'10/15/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285188', N'PHF-10-10411-4', N'Thyme, Fresh - 0.25 Ounce (oz)', N'11/30/19', 4000, 4000, N'H', null, N'C-01-09-3', null, null, N'Produce', N'HF02', N'10/16/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285267', N'PHF-10-10387-2', N'Squash, Zucchini - 8 Ounce (oz)', N'05/29/21', 40, 0, N'A', N'1942NJ622095_O1', N'AB OUT', N'01/27/20', 201942, N'Produce', N'HF01', N'10/09/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285286', N'PHF-10-10065-4', N'Carrot - 3 Ounce (oz)', N'05/29/21', 800, 0, N'A', null, N'AB OUT', null, null, N'Produce', N'HF01', N'10/03/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285384', N'PHF-10-10387-2', N'Squash, Zucchini - 8 Ounce (oz)', N'05/29/21', 40, 0, N'A', N'1942NJ622095_O1', N'AB OUT', N'07/10/20', 201942, N'Produce', N'HF01', N'10/09/19');
insert into dbo.v_procurement_export_autobagger (License_Plate, SKU, Item_Name, expiration_date, Pallet_Quantity, Quantity_Unavailable, Pallet_Status, po_number, location_id, Receipt_Date, SCM_Week, SKU_Category, wh_id, Pallet_Create_Date) values (N'LPAB00285385', N'PHF-10-10387-2', N'Squash, Zucchini - 8 Ounce (oz)', N'05/29/21', 60, 0, N'A', N'1942NJ622095_O1', N'AB OUT', N'07/10/20', 201942, N'Produce', N'HF01', N'10/09/19');
