{"killswitch": {"export_enabled": false, "forecast_upload_enabled": true, "blujay_shipment_sync_enabled": true, "culinary_sku_sync_enabled": true, "blujay_appointment_sync_enabled": true, "packaging_info_sync_enabled": true, "supplier_sku_sync_enabled": true, "pimt_3pw_monthly_report": false, "po_acknowledgement_sync_enabled": true, "grn_sync_enabled": true, "advance_shipping_notice_sync_enabled": true, "hj_grn_enabled": true, "limit_preventative_alert_time": true, "inventory_planner_sync_enabled": true, "kafka_inv_snapshot_enabled": true, "kafka_supplier_enabled": true, "kafka_dc_enabled": true, "wip_consumption_import_enabled": true, "show_bulk_on_ingredient_depletion": true, "combined_ep_gc_forecast_enabled": true, "use_oscar_for_ca_forecast": true, "kafka_work_order_tool_enabled": true, "kafka_transfer_order_enabled": true, "kafka_manufactured_sku_enabled": true, "show_aggregated_depletion_on_ingredient_depletion": true, "disable_discard_form_for_hj": true, "transfer_orders_enabled": true, "ing_depl_consider_prev_week_inv": true, "use_hj_for_grn_hj_warehouses": true, "display_hj_discards_in_discard_form": true, "inventory_export_to_s3_enabled": true, "top_variance_aggregation_enabled": true, "kafka_factor_pkg_consumption_enabled": true, "kafka_supplier_splits_consumption_enabled": true, "kafka_purchase_qty_recommendation_consumption_enabled": true, "kafka_ope_bulk_skus_enabled": true, "ope_use_10_wo_mock_plans": true, "blujay_filter_out_carrier": true, "kafka_shelf_life_consumption_enabled": true}, "cache": {"timeout": {"rq_job": 600, "ordering_tool": 31536000, "hj_autostore": 300}}, "flask": {"debug": false, "__jwt_access_token_expiration_seconds": "1 hour (this is just a comment)", "jwt_access_token_expiration_seconds": 3600, "__jwt_refresh_token_expiration_seconds": "30 days", "jwt_refresh_token_expiration_seconds": 2592000}, "oauth": {"client_id": "acdd6d4f-9046-413e-9b7e-0316b5dd0755", "enabled": true, "scope": ["User.Read"], "authority": "https://login.microsoftonline.com/74590b7d-28d8-4cbc-aa47-c81b1e969ae8", "callback": "https://inventory-management-us-api.staging-k8s.hellofresh.io/auth/authorize"}, "auth_dev_user": {"id": 192, "email": "<EMAIL>"}, "kafka": {"client_id": "imt-consumer-id", "group_id": "imt-consumer-group", "sasl_mechanism": "PLAIN", "sasl_plain_username": "imt", "security_protocol": "SASL_SSL", "ssl_cafile": "./config/staging/kafka_ca_staging.pem", "enable_auto_commit": true, "auto_commit_interval_ms": 5000, "topics": {"consume": {"purchase_orders": "rawevents.purchase-order", "blujay_shipment": "public.distribution-center.third-party.blujay.inbound.purchase-order-shipment.v1beta1", "grn": "public.distribution-center.inbound.goods-received-note.v1", "culinary_sku": "public.planning.culinarysku.v1", "blujay_appointment": "public.distribution-center.third-party.blujay.inbound.blujay-appointment.v1beta1", "packaging_info": "rawevents.suppliersku.packaging.us.beta", "supplier_sku": "rawevents.suppliersku.us.beta", "pimt_inventory": "public.distribution-center.third-party.e2open.inventory.v1beta1", "po_acknowledgement": "public.supply.purchase-order.acknowledgement.v2", "advance_shipping_notice": "public.supply.advance-shipping-notice.v2", "inventory_planner": "public.demand.sku-demand-forecast.v1", "inventory_snapshot": "public.distribution-center.inventory.snapshot.v1", "supplier": "public.planning.facility.v1", "dc": "public.scm.registry.dc.v1beta1", "ot": "public.supply.procurement.purchase-order.v1", "work_order_tool": "public.rte.work-order.v2", "manufactured_sku": "public.rte.manufactured-sku.v4", "transfer_order": "public.transfer-order.v1", "supplier_splits": "public.planning.culinarysku.suppliersplits.v3", "purchase_qty_recommendation": "public.ordering.purchase-quantity-recommendation.v1alpha1", "shelf_life": "public.planning.suppliersku.nutrition.v1"}, "produce": {"forecast_upload": "public.ordering.sku-inventory-demand-forecast.week-based.v1"}}, "timeouts": {"consume": 10}}, "hj_db": {"timeout": 30}, "green_chef_hj_db": {"timeout": 30}, "database": {"stale_timeout": 30}, "logging": {"handlers": {"debug_handler": {"level": "DEBUG", "class": "logging.StreamHandler", "formatter": "json"}, "stderr": {"level": "INFO", "formatter": "json"}}, "root": {"level": "INFO"}, "loggers": {"procurement.core.kafka": {"handlers": ["debug_handler"], "level": "DEBUG"}}}, "mailing": {"pimt": {"exceptions-report": {"from": "<EMAIL>", "to": "<EMAIL>"}, "monthly-3pw-report": {"from": "<EMAIL>", "to": "<EMAIL>"}}}, "slack": {"default_username": "Staging Slack Bot"}, "ics": {"ca": {"url": "https://chefsplate.freshservice.com/api/"}, "us": {"url": "https://hellofreshus.freshservice.com/api"}}}