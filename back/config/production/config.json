{"killswitch": {"export_enabled": true, "po_export_enabled": true, "blujay_shipment_sync_enabled": true, "culinary_sku_sync_enabled": true, "forecast_upload_enabled": true, "blujay_appointment_sync_enabled": true, "pimt_export_enabled": true, "imt_daily_export": true, "pimt_daily_exception_report": true, "packaging_info_sync_enabled": true, "supplier_sku_sync_enabled": true, "po_acknowledgement_sync_enabled": true, "pimt_3pw_monthly_report": true, "grn_sync_enabled": true, "advance_shipping_notice_sync_enabled": true, "limit_preventative_alert_time": true, "inventory_planner_sync_enabled": true, "kafka_inv_snapshot_enabled": true, "kafka_supplier_enabled": true, "kafka_dc_enabled": true, "hj_grn_enabled": true, "wip_consumption_import_enabled": true, "kafka_work_order_tool_enabled": true, "combined_ep_gc_forecast_enabled": true, "use_oscar_for_ca_forecast": false, "use_hj_for_grn_hj_warehouses": true, "show_bulk_on_ingredient_depletion": true, "aggregated_forecast_upload_enabled": false, "kafka_manufactured_sku_enabled": true, "inventory_export_to_s3_enabled": false, "kafka_supplier_splits_consumption_enabled": true, "blujay_filter_out_carrier": true, "consume_new_packaging_demand": true, "canada_pck_depl_on_oh_aggregation_enabled": true, "kafka_purchase_qty_recommendation_consumption_enabled": true, "kafka_shelf_life_consumption_enabled": true}, "cache": {"timeout": {"rq_job": 900, "ordering_tool": 300, "hj_autostore": 300}}, "oauth": {"enabled": true, "client_id": "9c680d61-e819-4685-89af-1ed160d1d8e8", "authority": "https://login.microsoftonline.com/74590b7d-28d8-4cbc-aa47-c81b1e969ae8", "callback": "https://imt-api.hellofresh.com/auth/authorize", "scope": ["User.Read"]}, "flask": {"debug": false, "__jwt_access_token_expiration_seconds": "1 hour (this is just a comment)", "jwt_access_token_expiration_seconds": 3600, "__jwt_refresh_token_expiration_seconds": "30 days", "jwt_refresh_token_expiration_seconds": 2592000}, "kafka": {"client_id": "imt-consumer-id", "group_id": "imt-consumer-group", "sasl_mechanism": "PLAIN", "sasl_plain_username": "imt", "security_protocol": "SASL_SSL", "ssl_cafile": "./config/production/kafka_ca_live.pem", "enable_auto_commit": true, "auto_commit_interval_ms": 5000, "topics": {"consume": {"blujay_shipment": "public.distribution-center.third-party.blujay.inbound.purchase-order-shipment.v1beta1", "grn": "public.distribution-center.inbound.goods-received-note.v1", "culinary_sku": "public.planning.culinarysku.v1", "blujay_appointment": "public.distribution-center.third-party.blujay.inbound.blujay-appointment.v1beta1", "packaging_info": "rawevents.suppliersku.packaging.us.beta", "supplier_sku": "rawevents.suppliersku.us.beta", "pimt_inventory": "public.distribution-center.third-party.e2open.inventory.v1beta1", "po_acknowledgement": "public.supply.purchase-order.acknowledgement.v2", "advance_shipping_notice": "public.supply.advance-shipping-notice.v2", "inventory_planner": "public.demand.sku-demand-forecast.v1", "inventory_snapshot": "public.distribution-center.inventory.snapshot.v1", "supplier": "public.planning.facility.v1", "dc": "public.scm.registry.dc.v1beta1", "ot": "public.supply.procurement.purchase-order.v1", "work_order_tool": "public.rte.work-order.v2", "manufactured_sku": "public.rte.manufactured-sku.v4", "supplier_splits": "public.planning.culinarysku.suppliersplits.v3", "purchase_qty_recommendation": "public.ordering.purchase-quantity-recommendation.v1alpha1", "shelf_life": "public.planning.suppliersku.nutrition.v1"}, "produce": {"forecast_upload": "public.ordering.sku-inventory-demand-forecast.week-based.v1"}}}, "database": {"stale_timeout": 30}, "hj_db": {"timeout": 200}, "green_chef_hj_db": {"timeout": 200}, "logging": {"handlers": {"stderr": {"formatter": "json"}}}, "mailing": {"finance": {"factor": {"from": "<EMAIL>", "to": "<EMAIL>"}, "core": {"from": "<EMAIL>", "to": "<EMAIL>"}}, "pimt": {"exceptions-report": {"from": "<EMAIL>", "to": "<EMAIL>"}, "monthly-3pw-report": {"from": "<EMAIL>", "to": "<EMAIL>"}}}, "slack": {"default_username": "Production Slack Bot", "channels": {"default": {"name": "imt-app-notifications"}, "forecast": {"name": "imt-ot-forecast"}, "imt_preventative": {"name": "imt-preventative-checks"}}}, "ics": {"ca": {"url": "https://chefsplate.freshservice.com/api/"}, "us": {"url": "https://hellofreshus.freshservice.com/api"}}}