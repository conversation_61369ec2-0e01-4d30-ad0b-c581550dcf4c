[tool.black]
line-length = 120
target-version = ['py312']
include = '\.pyi?$'
exclude = '/(.tox|venv|.venv|__pycache__|ui)/'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120
src_paths = ["procurement", "back", "tests", "init", "automated_tests"]

[tool.pylint.main]
init-hook='import sys; sys.path.append(".")'
py-version = "3.12"
ignore = ["slack_duty.py"]
ignore-paths = ["tests/*"]
max-args = 6
max-locals = 18
load-plugins = [
    "pylint.extensions.check_elif",
    "pylint.extensions.private_import",
    "pylint.extensions.redefined_variable_type",
    "pylint.extensions.redefined_loop_name",
    "pylint.extensions.empty_comment",
    "pylint.extensions.set_membership"
]
good-names = ["dc", "po", "db", "wh"]
max-line-length = 120
disable = [
    "too-few-public-methods",
    "broad-except",
    "not-an-iterable",
    "no-value-for-parameter",
    "too-many-instance-attributes",
    "too-many-public-methods",
    "unsubscriptable-object",
    "superfluous-parens",
    "not-callable",
    "wrong-import-order",
    "ungrouped-imports",
    "wrong-import-position",
    "C",
    "fixme",
    "redefined-loop-name",
    "duplicate-code",
    "global-statement",
    "no-name-in-module",
    "unsupported-membership-test",  # does not work properly
]

[tool.poetry]
name = "inventory-management-us"
version = "0.1.0"
description = ""
authors = []

[[tool.poetry.source]]
name = "artifactory-tools-hellofresh"
url = "https://artifactory.tools-k8s.hellofresh.io/artifactory/api/pypi/py/simple/"
priority = "default"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[tool.poetry.dependencies]
python = "^3.10"
attrs = "^22.2.0"
attribute-mapping = "^1.3.0"
cachetools = "^5.3.0"
certifi = "^2022.12.7"
Cython = "^0.29.33"
Flask = "^2.3.3"
Flask-Cors = "^3.0.10"
Flask-JWT-Extended = "^4.4.4"
Flask-Compress = "^1.13"
google-api-python-client = "^2.80.0"
gunicorn = "^20.1.0"
immutabledict = "^2.2.3"
isoweek = "^1.3.3"
jsonschema = "^4.17.3"
msal = "^1.21.0"
openpyxl = "^3.1.1"
parse = "^1.19.0"
psycopg2-binary = "^2.9.5"
PyJWT = { extras = ["crypto"], version = "^2.6.0" }
python-dateutil = "^2.8.2"
python-json-logger = "^2.0.7"
pytz = "^2024.0"
redis = "^4.5.1"
requests = "^2.28.2"
tenacity = "^8.2.2"
python-redis-lock = "^4.0.0"
rq = "^1.15.0"
schedule = "^1.1.0"
sendgrid = "^6.9.7"
simplejson = "^3.18.3"
yoyo-migrations = "^8.2.0"
confluent-kafka = { version = "2.3.0", extras = ["avro"] }
event-bus = "^1.0.2"
s3transfer = "^0.6.0"
boto3 = "^1.26.81"
prometheus-client = "^0.16.0"
rq-exporter = "^2.0.0"
prometheus-flask-exporter = "^0.22.3"
schema-registry = "0.1.2464"
snowflake-connector-python = "^3.5.0"
sqlalchemy = "^2.0.3"
pyodbc = "^5.0.0"


pytest = { version = "^6.2.4", optional = true }
tox-docker = { version = "^4.1.0", optional = true }
docker = { extras = ["tls"], version = "^6.1.0", optional = true }
allure-pytest = { version = "^2.9.43", optional = true }
PyHamcrest = { version = "^2.0.2", optional = true }
playwright =  { version = "1.43.0", optional = true }
pytest-playwright = { version = "0.4.4", optional = true }
busypie = { version = "^0.4.5", optional = true }
freezegun = { version = "^1.1.0", optional = true }
pytest-rerunfailures = { version = "^10.0", optional = true }
pytest-timeout = { version = "^1.4.2", optional = true }
pytest-mock = { version = "^3.6.1", optional = true }
coverage = { version = "^6.0.2", optional = true }
faker = { version = "^13.15.1", optional = true }
delayed-assert = { version = "^0.3.6", optional = true }

tox = { version = "^3.23.1", optional = true }
pre-commit = { version = ">=3.1.1", optional = true }
black = { version = ">=24.1.1", optional = true }
prospector = { extras = ["with-everything"], version = "^1.10.0", optional = true }


[tool.poetry.extras]
tests-dependencies = [
    "pytest",
    "playwright",
    "pytest-playwright",
    "docker",
    "tox-docker",
    "allure-pytest",
    "PyHamcrest",
    "requests",
    "tenacity",
    "busypie",
    "pytest-rerunfailures",
    "pytest-timeout",
    "pytest-mock",
    "coverage",
    "freezegun",
    "faker",
    "delayed-assert",
    "confluent-kafka",
]

dev = ["tox", "pre-commit", "black", "prospector"]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
