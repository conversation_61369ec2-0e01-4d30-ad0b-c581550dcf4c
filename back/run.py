import logging

from procurement import app, listeners_registration
from procurement.core import initialize

logger = logging.getLogger(__name__)


def init():
    initialize.init_all()
    flask_app = app.create_app()
    listeners_registration.register_redis_message_handlers()

    return flask_app


if __name__ == "__main__":
    logger.info("Starting Procurement API ...")
    init().run(host="0.0.0.0", port=8000)  # nosec B104
    logger.info("End of Procurement")
