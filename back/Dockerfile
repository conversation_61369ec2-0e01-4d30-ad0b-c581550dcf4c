ARG PYTHON_BASE_IMAGE=public.ecr.aws/docker/library/python:3.12.6-slim-bookworm
FROM $PYTHON_BASE_IMAGE
#FROM 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us:prod-image-back

RUN mkdir -p /app/config
WORKDIR /app

# Needed for apt-key add
RUN apt-get update --allow-releaseinfo-change && apt-get -y install p7zip-full
RUN apt-get -y install curl

# Install Microsoft ODBC driver
RUN apt-get install -y gnupg2 lsb-release
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
RUN curl https://packages.microsoft.com/config/debian/$(lsb_release -rs)/prod.list | tee /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update
RUN apt-get install -y unixodbc
RUN ACCEPT_EULA=Y apt-get install -y msodbcsql18

ADD poetry.lock .
ADD pyproject.toml .
RUN pip install --upgrade pip
RUN \
 export POETRY_VERSION=1.8.0 && \
 curl -sSL https://install.python-poetry.org | python - && \
 export PATH="/root/.local/bin:$PATH" && \
 poetry config virtualenvs.create false && \
 poetry install

#ENV CONFIG_FILE /app/config/config.json

ADD run.py /app/
ADD worker.py /app/
ADD clock.py /app/
ADD consumer.py /app/
ADD gunicorn_config.py /app/

ADD procurement /app/procurement
ADD init /app/init
ADD config /app/config
ADD migrations /app/migrations

#EXPOSE 8000
CMD ["gunicorn","-w","2","-c","gunicorn_config.py", "--threads", "4", "--bind","0.0.0.0:8000","run:init()", "--timeout", "90"]
