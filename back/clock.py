import logging
import time as timings
from datetime import datetime, time
from enum import StrEnum
from functools import wraps
from typing import AbstractSet, Any, Callable, FrozenSet, Iterable

import schedule

from procurement.constants.hellofresh_constant import BRAND_HF, MARKET_CA, MARKET_US, PartOfDay
from procurement.core import initialize
from procurement.core.config_utils import ENV, EnvType, killswitch
from procurement.core.metrics import ApplicationMetrics
from procurement.managers.datasync import (
    allowed_buffers_upload,
    documents,
    hj_datasync,
    imt_daily_exception_export,
    imt_datasync,
    inventory_sync,
    pimt_datasync,
    po_export_datasync,
    snapshot_sync,
)
from procurement.managers.datasync.maintenance import submit_cleanup_job
from procurement.managers.imt.export import manual_forms_export

logger = logging.getLogger(__name__)

initialize.init_all()

TIMING_FORMAT = "{:02}:{:02}"

MIDNIGHT = TIMING_FORMAT.format(0, 0)
WORK_DAY_START = TIMING_FORMAT.format(7, 0)
MIDDAY = TIMING_FORMAT.format(12, 0)


class Day(StrEnum):
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"

    @staticmethod
    def working_days() -> FrozenSet[str]:
        return frozenset((Day.MONDAY, Day.TUESDAY, Day.WEDNESDAY, Day.THURSDAY, Day.FRIDAY))

    @staticmethod
    def weekend_days() -> FrozenSet[str]:
        return frozenset((Day.SATURDAY, Day.SUNDAY))

    @staticmethod
    def all_days() -> FrozenSet[str]:
        return frozenset((*Day,))


def safe(func) -> Callable:
    @wraps(func)
    def safe_func(*args, **kwargs):
        try:
            func(*args, **kwargs)
        except Exception as exception:
            logger.exception("Error on call %s", func.__name__, stack_info=True)
            ApplicationMetrics.critical_exceptions("Scheduler", exception)

    return safe_func


def schedule_every_day(timing: str, action: Callable, except_: AbstractSet[str] = frozenset(), **kwargs) -> None:
    available_days = Day.all_days().difference(except_)
    _schedule_every_day(timing, action, available_days, **kwargs)


def schedule_every_working_day(
    timing: str, action: Callable, except_: AbstractSet[str] = frozenset(), **kwargs
) -> None:
    available_days = Day.working_days().difference(except_)
    _schedule_every_day(timing, action, available_days, **kwargs)


def schedule_every_weekend(timing: str, action: Callable, **kwargs):
    _schedule_every_day(timing, action, Day.weekend_days(), **kwargs)


def schedule_for_specified_days(timing: str, action: Callable, days: AbstractSet[str], **kwargs) -> None:
    _schedule_every_day(timing, action, days, **kwargs)


def _schedule_every_day(timing: str, action: Callable, days: AbstractSet[str], **kwargs) -> None:
    for day in days:
        schedule_day = getattr(schedule.every(), day)
        schedule_day.at(timing).do(action, **kwargs)


def schedule_every_nth_day(func: Callable[[], Any], day_number: int) -> Callable[[], None]:
    def _inner_func():
        if datetime.today().day == day_number:
            func()

    return _inner_func


imt_sync = safe(imt_datasync.launch_sync_with_shift_from_current_week)

imt_sync_future_pkg = safe(imt_datasync.launch_future_pkg_sync)

pimt_inventory_outdated_alert = safe(pimt_datasync.launch_pimt_update_date_alert)

pimt_sync = safe(pimt_datasync.launch_pimt_sync)
pimt_hj_sync = safe(pimt_datasync.launch_pimt_hj_sync)

clean_up_parallel_sync_log = safe(submit_cleanup_job)

schedule_hj_inv_sync = safe(hj_datasync.submit_hj_inv_schedule_job_us)
schedule_hj_wip_consumption_sync = safe(hj_datasync.submit_wip_consumption)

schedule_hj_packaging_pallet_snapshot = safe(hj_datasync.schedule_packaging_inventory_update)

schedule_po_export = safe(po_export_datasync.launch_po_export)

schedule_csac_export = safe(pimt_datasync.launch_csac_export)
schedule_ops_main_protein_export = safe(pimt_datasync.launch_ops_main_protein_export)
schedule_ops_main_gdp_export = safe(pimt_datasync.launch_ops_main_gdp_export)
schedule_strategy_export = safe(pimt_datasync.launch_strategy_export)
schedule_replenishment_export = safe(pimt_datasync.launch_replenishment_export)

schedule_pimt_3pw_report = safe(pimt_datasync.launch_pimt_monthly_3pw_report)

schedule_pimt_daily_exception_report = safe(pimt_datasync.launch_pimt_daily_exception_report)

doc_availability_check = safe(documents.submit_doc_availability_check)

schedule_packaging_depletion_daily_export = safe(imt_daily_exception_export.launch_packaging_depletion_daily_export)
schedule_weekly_liner_guidance_daily_export = safe(imt_daily_exception_export.launch_weekly_liner_guidance_daily_export)
schedule_packaging_po_status_daily_export = safe(imt_daily_exception_export.launch_packaging_po_status_daily_export)
schedule_factor_po_status_daily_export = safe(imt_daily_exception_export.launch_factor_po_status_daily_export)
schedule_factor_network_depletion_daily_export = safe(
    imt_daily_exception_export.launch_factor_network_depletion_daily_export
)
schedule_factor_finance_export_weekly = safe(po_export_datasync.launch_factor_weekly_financial_export)
schedule_core_finance_export_weekly = safe(po_export_datasync.launch_core_weekly_financial_export)
schedule_factor_finance_export_monthly = safe(po_export_datasync.launch_factor_monthly_financial_export)
schedule_core_finance_export_monthly = safe(po_export_datasync.launch_core_monthly_financial_export)
schedule_core_finance_export_daily = safe(po_export_datasync.launch_core_daily_financial_export)

send_manual_form_export = safe(manual_forms_export.submit_manual_forms_export_to_s3)

snapshot_start_of_day_inventory = safe(snapshot_sync.schedule_snapshot_start_of_day_inventory)
outdated_snapshot_check = safe(snapshot_sync.launch_snapshot_date_outdated)

submit_import_allowed_buffers_job = safe(allowed_buffers_upload.submit_import_allowed_buffers_job)

schedule_inventory_export_to_s3 = safe(inventory_sync.schedule_inventory_export_to_s3)


def _schedule_imt_sync(sch_time: str, market: str) -> None:
    schedule.every().day.at(sch_time).do(imt_sync, market=market)


def _schedule_prev_week_imt_sync(sch_time: str, market: str) -> None:
    schedule.every().day.at(sch_time).do(imt_sync, shift_from_current=-2, market=market)
    schedule.every().day.at(sch_time).do(imt_sync, shift_from_current=-1, market=market)


def _schedule_next_week_imt_sync(sch_time: str, market: str) -> None:
    schedule.every().day.at(sch_time).do(imt_sync, shift_from_current=1, market=market)
    schedule.every().day.at(sch_time).do(imt_sync, shift_from_current=2, market=market)


def setup_production_sync() -> Iterable[int]:
    sync_hours = list(range(7, 24))

    _schedule_prod_hourly_sync(sync_hours)

    schedule.every().day.at(TIMING_FORMAT.format(10, 0)).do(submit_import_allowed_buffers_job)
    schedule.every().day.at(TIMING_FORMAT.format(16, 0)).do(submit_import_allowed_buffers_job)

    schedule.every().day.at(MIDDAY).do(pimt_inventory_outdated_alert)

    schedule.every().day.at(WORK_DAY_START).do(imt_sync_future_pkg)

    schedule.every().thursday.at(MIDNIGHT).do(snapshot_start_of_day_inventory, brands=[BRAND_HF], market=MARKET_CA)
    schedule.every().thursday.at(TIMING_FORMAT.format(4, 0)).do(schedule_hj_wip_consumption_sync, market=MARKET_CA)

    schedule.every().wednesday.at(TIMING_FORMAT.format(8, 0)).do(outdated_snapshot_check, market=MARKET_CA)
    schedule.every().thursday.at(TIMING_FORMAT.format(8, 0)).do(outdated_snapshot_check, market=MARKET_CA)

    _schedule_prod_finance_exports()

    if killswitch.imt_daily_export:
        _schedule_prod_imt_daily_export()

    if killswitch.pimt_daily_exception_report:
        schedule_every_working_day(timing=MIDDAY, action=schedule_pimt_daily_exception_report)

    _schedule_prod_3pw_monthly_report()

    schedule.every().day.at(TIMING_FORMAT.format(11, 59)).do(send_manual_form_export)

    for schedule_day in [schedule.every().monday, schedule.every().tuesday, schedule.every().wednesday]:
        schedule_day.at(TIMING_FORMAT.format(18, 0)).do(doc_availability_check)

    return sync_hours


def _schedule_prod_hourly_sync(sync_hours: Iterable[int]) -> None:
    if killswitch.pimt_export_enabled:
        schedule.every().day.at(TIMING_FORMAT.format(8, 0)).do(schedule_replenishment_export)
    for hour in sync_hours:
        hourly_sch_time = TIMING_FORMAT.format(hour, 0)
        _schedule_prev_week_imt_sync(hourly_sch_time, MARKET_US)
        _schedule_prev_week_imt_sync(hourly_sch_time, MARKET_CA)
        for minutes in range(9, 60, 15):
            sch_time = TIMING_FORMAT.format(hour, minutes)
            _schedule_next_week_imt_sync(sch_time, MARKET_US)
            _schedule_next_week_imt_sync(sch_time, MARKET_CA)
        for minutes in range(0, 59, 15):
            sch_time = TIMING_FORMAT.format(hour, minutes)

            _schedule_imt_sync(sch_time, MARKET_US)
            _schedule_imt_sync(sch_time, MARKET_CA)

            if killswitch.po_export_enabled:
                schedule.every().day.at(sch_time).do(schedule_po_export)

        if killswitch.pimt_export_enabled:
            pimt_daily_exports = (
                schedule_csac_export,
                schedule_ops_main_protein_export,
                schedule_ops_main_gdp_export,
                schedule_strategy_export,
            )
            for export in pimt_daily_exports:
                schedule.every().day.at(TIMING_FORMAT.format(hour, 0)).do(export)


def _schedule_prod_finance_exports() -> None:
    for export in (schedule_factor_finance_export_monthly, schedule_core_finance_export_monthly):
        schedule.every().day.at(MIDNIGHT).do(schedule_every_nth_day(export, day_number=1))

    schedule.every().tuesday.at(MIDNIGHT).do(schedule_factor_finance_export_weekly, market=MARKET_US)
    schedule.every().thursday.at(MIDNIGHT).do(schedule_core_finance_export_weekly)
    schedule.every().day.at(MIDNIGHT).do(schedule_core_finance_export_daily)


def _schedule_prod_imt_daily_export() -> None:
    start_hour = 8
    end_hour = 15
    morning_export_time = TIMING_FORMAT.format(start_hour, 30)
    evening_export_time = TIMING_FORMAT.format(end_hour, 0)

    schedule_every_day(timing=TIMING_FORMAT.format(start_hour, 20), action=schedule_weekly_liner_guidance_daily_export)

    schedule_every_working_day(timing=morning_export_time, action=schedule_packaging_depletion_daily_export)
    schedule_every_weekend(timing=TIMING_FORMAT.format(9, 20), action=schedule_packaging_depletion_daily_export)

    exports = (schedule_packaging_po_status_daily_export,)

    for export in exports:
        schedule_every_working_day(timing=morning_export_time, action=export, part_of_day=PartOfDay.AM)
        schedule_every_weekend(timing=TIMING_FORMAT.format(9, 20), action=export, part_of_day=PartOfDay.AM)

        schedule_every_working_day(
            timing=evening_export_time, action=export, except_={Day.FRIDAY}, part_of_day=PartOfDay.PM
        )
        schedule_for_specified_days(
            timing=evening_export_time,
            action=export,
            days={Day.FRIDAY, Day.SATURDAY, Day.SUNDAY},
            part_of_day=PartOfDay.PM,
        )

    factor_exports = (
        schedule_factor_po_status_daily_export,
        schedule_factor_network_depletion_daily_export,
    )

    for export in factor_exports:
        schedule.every().hour.at(":00").do(export, part_of_day=PartOfDay.AM)
        schedule_for_specified_days(
            timing=evening_export_time, action=export, days={Day.FRIDAY}, part_of_day=PartOfDay.PM
        )


def _schedule_prod_3pw_monthly_report() -> None:
    if killswitch.pimt_3pw_monthly_report:
        schedule.every().day.at(WORK_DAY_START).do(schedule_every_nth_day(schedule_pimt_3pw_report, day_number=1))


def setup_staging_sync() -> Iterable[int]:
    _schedule_imt_sync(TIMING_FORMAT.format(16, 0), MARKET_CA)
    _schedule_imt_sync(TIMING_FORMAT.format(16, 0), MARKET_US)

    return range(3, 14)  # 9:00-20:00 EEST (dev team working hours)


def setup_common_sync(sync_hours: Iterable[int]) -> None:
    for hour in sync_hours:
        schedule.every().day.at(TIMING_FORMAT.format(hour, 0)).do(pimt_hj_sync)
    schedule.every().day.at(TIMING_FORMAT.format(11, 0)).do(pimt_sync, market=MARKET_US)
    schedule.every().day.at(TIMING_FORMAT.format(11, 0)).do(pimt_sync, market=MARKET_CA)
    schedule.every().saturday.at(TIMING_FORMAT.format(0, 30)).do(clean_up_parallel_sync_log)
    schedule.every().day.at(MIDNIGHT).do(schedule_hj_inv_sync)
    schedule.every().day.at(MIDNIGHT).do(schedule_hj_packaging_pallet_snapshot, market=MARKET_US, at_time=time(5, 30))
    schedule.every().thursday.at(MIDNIGHT).do(
        schedule_hj_packaging_pallet_snapshot, market=MARKET_CA, at_time=time(3, 0)
    )
    if killswitch.inventory_export_to_s3_enabled:
        schedule.every().hour.at(":00").do(schedule_inventory_export_to_s3)


def run_main_loop():
    while True:
        schedule.run_pending()
        timings.sleep(1)


def build_sync():
    if ENV == EnvType.PRODUCTION:
        sync_hours = setup_production_sync()
    else:
        sync_hours = setup_staging_sync()

    setup_common_sync(sync_hours)


if __name__ == "__main__":
    build_sync()
    run_main_loop()
