from enum import IntEnum

from .hellofresh_constant import InventoryState as UnifiedInvState
from .hellofresh_constant import UnitOfMeasure


class GrnUnitMeasure(IntEnum):
    UNSPECIFIED = 0
    OTHER = 1
    UNIT = 2
    CASE = 3
    KG = 4
    LBS = 5
    OZ = 6
    LITRE = 7
    GAL = 8


GRN_IMT_UOM_MAP = {
    GrnUnitMeasure.UNSPECIFIED: None,
    GrnUnitMeasure.OTHER: None,
    GrnUnitMeasure.UNIT: UnitOfMeasure.UNIT,
    GrnUnitMeasure.CASE: UnitOfMeasure.CASE,
    GrnUnitMeasure.KG: UnitOfMeasure.KILOGRAM,
    GrnUnitMeasure.LBS: UnitOfMeasure.POUND,
    GrnUnitMeasure.OZ: UnitOfMeasure.OUNCE,
    GrnUnitMeasure.LITRE: UnitOfMeasure.LITER,
    GrnUnitMeasure.GAL: UnitOfMeasure.GALLON,
}


class OtUnitMeasure(IntEnum):
    UNSPECIFIED = 0
    UNIT = 1
    KG = 2
    LBS = 3
    GAL = 4
    LITRE = 5
    OZ = 6


OT_UOM_MAP = {
    OtUnitMeasure.UNIT: UnitOfMeasure.UNIT,
    OtUnitMeasure.KG: UnitOfMeasure.KILOGRAM,
    OtUnitMeasure.LBS: UnitOfMeasure.POUND,
    OtUnitMeasure.GAL: UnitOfMeasure.GALLON,
    OtUnitMeasure.LITRE: UnitOfMeasure.LITER,
    OtUnitMeasure.OZ: UnitOfMeasure.OUNCE,
}


class DemandUnitOfMeasure(IntEnum):
    UNSPECIFIED = 0
    UNIT = 1
    KG = 2
    LBS = 3
    GAL = 4
    LITRE = 5
    OZ = 6


IMT_DEMAND_UOM_MAP = {
    UnitOfMeasure.UNIT: DemandUnitOfMeasure.UNIT,
    UnitOfMeasure.EACH: DemandUnitOfMeasure.UNIT,
    UnitOfMeasure.PIECE: DemandUnitOfMeasure.UNIT,
    UnitOfMeasure.KILOGRAM: DemandUnitOfMeasure.KG,
    UnitOfMeasure.POUND: DemandUnitOfMeasure.LBS,
    UnitOfMeasure.GALLON: DemandUnitOfMeasure.GAL,
    UnitOfMeasure.LITER: DemandUnitOfMeasure.LITRE,
    UnitOfMeasure.OUNCE: DemandUnitOfMeasure.OZ,
}


class GrnDeliveryLineState(IntEnum):
    UNSPECIFIED = 0
    EXPECTED = 1
    OPEN = 2
    RECEIVED = 3
    REJECTED = 4
    CLOSED = 5
    CANCELLED = 6


class InventoryLocationType(IntEnum):
    UNSPECIFIED = 0
    STAGING = 1
    PRODUCTION = 2
    STORAGE = 3
    QUARANTINE = 4
    DONATIONS = 5
    WASTE = 6
    INBOUND = 7


class InventoryState(IntEnum):
    UNSPECIFIED = 0
    ACTIVE = 1
    ON_HOLD = 2
    INCOMING = 3
    LOST = 4
    OUTSIDE = 5
    GONE = 6
    BOOKED_FOR_OUTBOUND = 7
    TOTAL = 8

    def to_unified_state(self) -> UnifiedInvState:
        if self == self.ACTIVE:
            return UnifiedInvState.AVAILABLE
        return UnifiedInvState[self.name]


class E2OpenInventoryState(IntEnum):
    UNSPECIFIED = 0
    AVAILABLE = 1
    TOTAL = 2
    BOOKED_FOR_OUTBOUND = 3
    ON_HOLD = 4


class WmsType(IntEnum):
    UNSPECIFIED = 0
    FCMS = 1
    WMSL = 2
    HJ = 3


class OrderingToolPoStatus(IntEnum):
    UNSPECIFIED = 0
    INITIATED = 1
    APPROVED = 2
    REJECTED = 3
    DELETED = 4


class TransferOrderStatus(IntEnum):
    UNSPECIFIED = 0
    OPEN = 1
    DELETED = 3
    CANCELLED = 4
    ORDERED = 5
    ACCEPTED = 6
    REJECTED = 7
    RESERVED = 8
    IN_TRANSIT = 9
    DELIVERED = 10


class ShippingMethod(IntEnum):
    UNSPECIFIED = 0
    VENDOR_DELIVERED = 1
    CROSSDOCK = 2
    FREIGHT_ON_BOARD = 3
    OTHER = 4
