from __future__ import annotations

from enum import Enum, IntEnum, StrEnum
from typing import Self

import pytz

from procurement.core.config_utils import killswitch

MARKET_HEADER = "IMT-Market"

MARKET_US = "US"
MARKET_CA = "CA"

BRAND_HF = "HF"
BRAND_EP = "EP"
BRAND_GC = "GC"
BRAND_FJ = "FJ"
NO_BRAND = "NO BRAND"
ALL_BRANDS = "All Brands"
ALL_DCS = "All DCs"
BRAND_HF_CP_FULL = "HelloFresh, Chef’s Plate"
BRAND_HF_FULL = "HelloFresh"
BRAND_CP_FULL = "Chef’s Plate"
BRAND_FJ_FULL = "Factor"
MANY_SUPPLIERS = "[Many Suppliers]"

SITE_AU = "AU"
SITE_BR = "BR"
SITE_AG = "AG"
SITE_JO = "JO"
SITE_AC = "AC"
SITE_UB = "UB"
SITE_BO = "BO"
SITE_SW = "SW"
SITE_GJ = "GJ"
SITE_NW = "NW"
WAREHOUSE_JOLIET = "Joliet"
WAREHOUSE_FIDELITY = "Fidelity"
BOB_CODE_GL = "GL"

YES = "Yes"
NO = "No"

FUTURE_WEEK_PULL_REASONS = ("Future Week Product Pull", "Future Week Product Pull Replacement")
CHARITY_EMERGENCY_REASON = "Charity"

PRODUCTION_TYPE_KITTING = "Kitting"
PRODUCTION_TYPE_ASSEMBLY = "Assembly"

MAX_SUPPLIER_LEAD_TIME_WEEKS = 2

GENERAL_SUPPLIER = "General Emergency Pick-Up Supplier"

BACKHAUL = "Backhaul"

HF_INVENTORY = "HelloFresh Inventory"

TIMEZONE_NAMES = tuple(
    ["UTC"]
    + sorted(pytz.country_timezones["US"])
    + sorted(pytz.country_timezones["CA"])
    + sorted(pytz.country_timezones["DE"])
)

APP_TIMEZONE = pytz.timezone("America/New_York")

FREIGHT_ON_BOARD_SHIP_METHOD = "freight on board"
CROSSDOCK_SHIP_METHOD = "crossdock"
PACKAGING_OTHER_CODE = "OTH-"

TPW_SOURCE = "From 3PW"
NOT_TPW_SOURCE = "Assumed"

RECEIVE_EXPIRATION_PROTEIN_RANGE_MONTHS = 5

DEPLETION_WEEKS_AHEAD = 9
FORECASTED_WEEKS_QUANTITY = DEPLETION_WEEKS_AHEAD + 1

PKG_WEEKS_AHEAD = 5
MOCK_PLAN_WEEKS_AHEAD = DEPLETION_WEEKS_AHEAD if killswitch.ope_use_10_wo_mock_plans else PKG_WEEKS_AHEAD
MOCK_PLAN_ERROR_WEEKS_OUT = 6

CA_DEMAND_WEEK_RANGE = 10

HARDCODED_SUPPLIER_NAMES = [
    "Factor Joliet",
    "Freeport Logistics, Inc.",
    "GreenLeaf 3PL Inventory",
    "Lineage Henderson",
    "Romark Logistics LLC",
    "Castellini 3PL Inventory",
    "TX3 HelloFresh Inventory",
    "CO Green Chef Inventory",
    "GA HelloFresh Inventory",
    "ECF 3PL Inventory",
    "Hermann Warehouse",
    "Castellini Company 3PW",
    "SW Green Chef Inventory",
    "Woods Distribution Solutions, LLC",
    "Dreisbach Enterprises",
    "AZ HelloFresh Inventory",
    "TX HelloFresh Inventory",
    "Factor Goodyear",
    "First Choice Freezer",
    "TX EveryPlate Inventory",
    "Castellini Kentucky 3PL Inventory",
    "NT EveryPlate Inventory",
    "Factor Aurora",
    "Lineage - Elizabeth NJ",
    "Lineage McDonough",
    "SW EveryPlate Inventory",
    "Factor - General Mapping",
    "NJ HelloFresh Inventory",
    "Dairyland Produce, LLC",
    "Ryder Distribution",
    "Shippers Warehouse, Inc.",
    "Factor Lake Zurich",
    "Reformulation Supplier 1",
    "Reformulation Supplier 2",
    "Lineage - Gold Spike",
    "Factor Brett Anthony Foods",
    "Factor Fresco Foods",
]


class UnitOfMeasure(Enum):
    BOTTLE = ("bottle",)
    BULK = ("bulk",)
    CLOVE = ("clove",)
    COUNT = ("count",)
    CASE = ("case",)
    ROLL = ("roll",)
    UNIT = ("unit",)
    FLUID_OUNCE = ("fl oz", "fluid ounce")
    GALLON = ("gal", "gallon")
    TABLESPOON = ("tbsp", "tablespoon")
    TEASPOON = ("tsp", "teaspoon")
    EACH = ("ea", "each")
    MILLILITER = ("ml", "milliliter")
    KILOGRAM = ("kg", "kilogram")
    OUNCE = ("oz", "ounce")
    PIECE = ("pc", "piece")
    POUND = ("lb", "pound", "{short_name}s")
    LITER = ("l", "liter")
    GRAM = ("g", "gram")
    CUP = ("c", "cup")

    def __init__(self, short_name: str, full_name: str = None, display_format: str = "{short_name}"):
        self.short_name = short_name
        self.full_name = full_name or short_name
        self.__display_name = display_format.format(short_name=short_name, full_name=full_name)

    def __str__(self):
        return self.__display_name

    @classmethod
    def __short_name_mapping(cls) -> dict[str, UnitOfMeasure]:
        if not hasattr(cls, "__UNIT_MAP"):
            cls.__UNIT_MAP = {unit.short_name: unit for unit in cls}
        return cls.__UNIT_MAP

    @classmethod
    def value_of(cls, unit_short_name: str) -> UnitOfMeasure | None:
        return cls.__short_name_mapping().get(unit_short_name)

    @classmethod
    def inexact_value_of(cls, str_unit: str) -> UnitOfMeasure | None:
        if not str_unit:
            return None
        str_unit = str_unit.strip().lower()
        for unit in cls:
            # trying to match as good as we can
            if str_unit.startswith(unit.short_name) or str_unit.startswith(unit.full_name):
                return unit
        return None


class PoAckUnitOfMeasure(StrEnum):
    UNIT = "UNIT"
    CASE = "CASE"


class Domain(StrEnum):
    IMT = "imt"
    PIMT = "pimt"


class PartOfDay(StrEnum):
    AM = "AM"
    PM = "PM"


class GrnSource(StrEnum):
    E2OPEN = "E2OPEN"
    HJ = "HJ"
    WMSL = "WMSL"


class ReceiveInputType(StrEnum):
    HIGH_JUMP = "HighJump"
    MANUAL = "Manual"
    GRN = "E2OpenGrn"
    WMSL = "WMSL"

    @property
    def is_high_jump(self) -> bool:
        return self == self.HIGH_JUMP

    @property
    def is_manual(self) -> bool:
        return self == self.MANUAL

    @property
    def is_grn(self) -> bool:
        return self == self.GRN

    @property
    def is_wmsl(self) -> bool:
        return self == self.WMSL

    @property
    def is_in_grn(self) -> bool:
        return self in (self.WMSL, self.GRN)

    @property
    def grn_source_name(self) -> GrnSource | None:
        if self.is_grn:
            return GrnSource.E2OPEN
        if self.is_high_jump:
            return GrnSource.HJ
        if self.is_wmsl:
            return GrnSource.WMSL
        return None


class InventoryInputType(StrEnum):
    HJ = "HighJump"
    GSHEET = "Gsheet"
    E2OPEN = "E2Open"
    CYCLE_COUNT = "CycleCount"
    WMSL = "WMSL"

    @property
    def is_high_jump(self) -> bool:
        return self == self.HJ

    @property
    def is_gsheet(self) -> bool:
        return self == self.GSHEET

    @property
    def is_e2open(self) -> bool:
        return self == self.E2OPEN

    @property
    def is_cycle_count(self) -> bool:
        return self == self.CYCLE_COUNT

    @property
    def is_wmsl(self) -> bool:
        return self == self.WMSL

    @property
    def is_in_unified_inventory(self) -> bool:
        return self in (self.GSHEET, self.E2OPEN)


class HybridNeedsDataSourceType(StrEnum):
    SNOWFLAKE = "Snowflake"
    GSHEET = "Gsheet"

    def is_snowflake(self) -> bool:
        return self == self.SNOWFLAKE

    def is_gsheet(self) -> bool:
        return self == self.GSHEET


class WhReceivingType(StrEnum):
    NA = "N/A"
    # later will be merged to just one GRN source
    E2OPEN_GRN = "E2OPEN_GRN"
    HJ_GRN = "HJ_GRN"

    @property
    def is_na(self) -> bool:
        return self == self.NA

    @property
    def is_hj(self) -> bool:
        return self == self.HJ_GRN

    @property
    def is_grn(self) -> bool:
        return self in self.__grn_types()

    @property
    def grn_source_name(self) -> GrnSource | None:
        if self == self.E2OPEN_GRN:
            return GrnSource.E2OPEN
        if self == self.HJ_GRN:
            return GrnSource.HJ
        return None

    @classmethod
    def __grn_types(cls) -> tuple[WhReceivingType, ...]:
        if not hasattr(cls, "__GRN_TYPES_CACHED"):
            cls.__GRN_TYPES_CACHED = (
                (WhReceivingType.E2OPEN_GRN,)
                if killswitch.use_hj_for_grn_hj_warehouses
                else (WhReceivingType.E2OPEN_GRN, WhReceivingType.HJ_GRN)
            )
        return cls.__GRN_TYPES_CACHED


class DayOfWeek(StrEnum):
    MONDAY = "Monday"
    TUESDAY = "Tuesday"
    WEDNESDAY = "Wednesday"
    THURSDAY = "Thursday"
    FRIDAY = "Friday"
    SATURDAY = "Saturday"
    SUNDAY = "Sunday"


class IngredientCategory(StrEnum):
    PROTEIN = "Protein"
    GROCERY = "Grocery"
    DAIRY = "Dairy"
    PACKAGING = "Packaging"
    PRODUCE = "Produce"


class ReplenishmentType(StrEnum):
    DETERMINISTIC = "Deterministic"
    DYNAMIC = "Dynamic"
    REORDER_POINT = "Reorder Point"
    REPLENISHMENT = "Replenishment"
    MODIFIED_REPLENISHMENT = "Modified Replenishment"


class GsheetProductionPlanType(StrEnum):
    FORECAST = "Forecast"
    PLAN = "Plan"


class SnowflakeProductionPlanType(StrEnum):
    COMPLETE = "Complete"
    PROJECTED = "Projected"


class ProductionPlanType(StrEnum):
    ACTUAL = "Actual"
    PROJECTED = "Projected"


class WmslLocationType(StrEnum):
    QUARANTINE = "QUARANTINE"
    PRODUCTION = "PRODUCTION"
    STORAGE = "STORAGE"


class InventoryState(StrEnum):
    UNSPECIFIED = "Unspecified"
    AVAILABLE = "Available"
    TOTAL = "Total"
    BOOKED_FOR_OUTBOUND = "Booked for Outbound"
    ON_HOLD = "On Hold"
    UNAVAILABLE = "Unavailable"
    INCOMING = "Incoming"
    # LOST, OUTSIDE and GONE might be consolidated to just UNAVAILABLE
    LOST = "Lost"
    OUTSIDE = "Outside"
    GONE = "Gone"
    WIP = "WIP"


class HjInventoryState(StrEnum):
    AVAILABLE = "A"
    ON_HOLD = "H"
    UNAVAILABLE = "U"

    def display_name(self) -> str:
        return self.to_unified_state(self)

    @classmethod
    def to_unified_state(cls, status: str | Self) -> InventoryState:
        if status == cls.AVAILABLE:
            return InventoryState.AVAILABLE
        if status == cls.ON_HOLD:
            return InventoryState.ON_HOLD
        if status == cls.UNAVAILABLE:
            return InventoryState.UNAVAILABLE
        return InventoryState.UNSPECIFIED


class SkuStatus(StrEnum):
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    ARCHIVED = "Archived"
    LAUNCH = "Launch"
    LIMITED = "Limited"
    ONBOARDING = "Onboarding"
    DEPLETION_TRACK = "Depletion Track"


class POAcknowledgementState(IntEnum):
    UNSPECIFIED = 0
    OPEN = 1
    ACCEPTED = 2
    ACCEPTED_WITH_CHANGES = 3
    REJECTED = 4


class HjWipConsumptionType(IntEnum):
    CARRYOVER = 0
    INITIAL_PULL = 1
    PUTAWAY_TO_PRODUCTION = 2


class PoType(StrEnum):
    INBOUND = "Inbound"
    OUTBOUND = "Outbound"


class PimtExceptionType(StrEnum):
    INVALID_PO_NUMBER = "Invalid PO Number"
    INVALID_PO_SKU_COMBINATION = "Invalid PO and SKU Combination"
    INVALID_SKU = "Invalid SKU"
    MISSING_PO = "Missing PO"
    MISSING_EXPIRATION_DATE = "Missing Expiration Date"
    INVALID_EXPIRATION_DATE = "Invalid Expiration Date"
    INVALID_PROTEIN_EXPIRATION_DATE = "Invalid Expiration Date - shelf life under 5 months"
    EXPIRATION_DATE_TOO_HIGH = "Invalid Expiration Date - expiration date > Receipt Date + Shelf Life"
    EARLIER_THEN_RECEIVE_EXPIRATION_DATE = "Invalid Expiration Date - Expires on or before receipt date"
    INCORRECT_FORMAT_OF_EXPIRATION_DATE = "Invalid Expiration Date - Incorrect format"
    INVALID_OR_MISSING_EXPIRATION_DATE = "Invalid/Missing Expiration Date"
    INVALID_DELIVERY_TIME = "Invalid Delivery Time"

    @staticmethod
    def get_ui_exceptions():
        return [
            exception
            for exception in PimtExceptionType.__members__.values()
            if exception
            not in (
                PimtExceptionType.INCORRECT_FORMAT_OF_EXPIRATION_DATE,
                PimtExceptionType.EARLIER_THEN_RECEIVE_EXPIRATION_DATE,
                PimtExceptionType.INVALID_EXPIRATION_DATE,
                PimtExceptionType.MISSING_EXPIRATION_DATE,
                PimtExceptionType.INVALID_PROTEIN_EXPIRATION_DATE,
                PimtExceptionType.EXPIRATION_DATE_TOO_HIGH,
            )
        ]


class ExpirationStatusEnum(StrEnum):
    EXPIRED = "Expired"
    LESS_THAN_30 = "1-30 Days"
    LESS_THAN_60 = "30-60 Days"
    MORE_THAN_60 = "+60 Days"
