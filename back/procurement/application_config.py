from datetime import timedelta  # pylint: skip-file

from procurement.core import utils
from procurement.core.cache_utils.constants import REDIS_CONFIG
from procurement.core.config_utils import config


class FlaskConfig:
    PROPAGATE_EXCEPTIONS = True
    JSONIFY_PRETTYPRINT_REGULAR = False
    JSON_SORT_KEYS = False
    COMPRESS_ALGORITHM = "gzip"
    COMPRESS_LEVEL = 4

    def __init__(self):
        env = config["flask"].get("env", "dev")
        self.ENV = "development" if env == "dev" else env
        self.DEBUG = config["flask"].get("debug", False)

        self.JWT_SECRET_KEY = config["flask"].get("jwt_secret", False)
        self.JWT_ACCESS_TOKEN_EXPIRES = timedelta(
            seconds=config["flask"].get("jwt_access_token_expiration_seconds", utils.ttl_string_to_sec("1h"))
        )
        self.JWT_REFRESH_TOKEN_EXPIRES = timedelta(
            seconds=config["flask"].get("jwt_refresh_token_expiration_seconds", utils.ttl_string_to_sec("30d"))
        )

        self.RQ_DASHBOARD_REDIS_URL = REDIS_CONFIG.get_redis_url()

        self.USER_DEV_CREDS = config.get("auth_dev_user", {"id": 1, "email": "<EMAIL>"})

        self.CLIENT_ID = config["oauth"]["client_id"]  # Application (client) ID of app registration
        self.CLIENT_SECRET = config["oauth"]["client_secret"]
        self.AUTHORITY = config["oauth"]["authority"]  # did use tenant id here
        self.AUTH_STATE_TIMEOUT_SECONDS = config["oauth"]["state_timeout_seconds"]
        self.SCOPE = config["oauth"]["scope"]
        self.AUTH_CALLBACK_URI = config["oauth"]["callback"]


flask_config = FlaskConfig()
