import base64
import logging

import sendgrid

from procurement.core.config_utils import config, killswitch

from . import file_export

logger = logging.getLogger(__name__)


class SendGridException(Exception):
    def __init__(self, message: str):
        super().__init__(message)


class MailService:
    def __init__(self):
        self.mailing_service = sendgrid.SendGridAPIClient(api_key=config.get("mailing", {}).get("api_key"))

    def send_mail(self, mailing_config: dict, file: bytes, formatters: dict) -> None:
        file_name = mailing_config["attachment_template"].format(**formatters)

        if killswitch.export_to_local_files_enabled:
            file_export.dump_to_file(file_name=file_name, file_bytes=file)
            return

        title = mailing_config["title_template"].format(**formatters)
        body = mailing_config["body_template"].format(**formatters)

        mail_from = mailing_config.get("from")
        mail_to = mailing_config.get("to")
        if not (mail_from and mail_to):
            logger.warning("mailing addresses are not configured, unable to send report")
            return

        mail = sendgrid.Mail(
            from_email=mail_from,
            to_emails=mail_to,
            subject=title,
            plain_text_content=body,
        )

        mail.add_attachment(self.create_file_attachment(file, file_name))

        try:
            self.mailing_service.send(mail)
        except Exception as err:
            raise ValueError(f"Email was not sent because of SendGrid's bad response: {err}") from err
        logger.info("Email '%s' sent to %s", title, mail_to)

    @staticmethod
    def create_file_attachment(file: bytes, file_name: str) -> sendgrid.Attachment:
        attachment_bytes = base64.encodebytes(file)
        attachment_b64_str = attachment_bytes.decode("ascii").replace("\n", "")
        return sendgrid.Attachment(file_content=attachment_b64_str, file_name=file_name)


mail_service = MailService()
