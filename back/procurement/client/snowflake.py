from collections.abc import Iterable

from snowflake import connector

from procurement.core.config_utils import config, is_ci

DEFAULT_PAGE_SIZE = 5000


class SnowflakeService:
    def __init__(self, snowflake_config: dict):
        self.snowflake_config = snowflake_config

    def get_connection(self):
        return connector.connect(
            user=self.snowflake_config["user"],
            password=self.snowflake_config["password"],
            account=self.snowflake_config["account"],
            role=self.snowflake_config["role"],
            region=self.snowflake_config["region"],
        )

    def select_all(self, query: str, params: dict = None) -> Iterable[tuple | dict]:
        if is_ci:
            return
        with self.get_connection() as connection:
            cursor = connection.cursor()
            try:
                cursor.execute(query, params)
                while batch := cursor.fetchmany(DEFAULT_PAGE_SIZE):
                    # using generator here due to possibly large results from Snowflake
                    yield from batch
            finally:
                cursor.close()


snowflake_service = SnowflakeService(config.get("snowflake"))
