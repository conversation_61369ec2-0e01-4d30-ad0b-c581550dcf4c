import json
import logging
from enum import StrEnum

import requests

from procurement.core.config_utils import config
from procurement.core.metrics import ApplicationMetrics

logger = logging.getLogger(__name__)


class SlackChannel(StrEnum):
    DEFAULT = "default"
    FORECAST = "forecast"
    PREVENTATIVE_CHECK = "imt_preventative"


class SlackClient:
    def __init__(self):
        self.slack_config = config.get("slack", {})
        self._session = requests.Session()
        self._session.headers.update(
            {"Content-Type": "application/json;charset=utf-8", "User-Agent": self.slack_config["app_name"]}
        )
        self._url = self.slack_config.get("webhook_url")  # TODO: legacy url
        self._default_icon = self.slack_config.get("default_icon")
        self._default_username = self.slack_config.get("default_username")
        self._channels = self.slack_config.get("channels")

    def post(self, text: str, *, channel: SlackChannel = SlackChannel.DEFAULT):
        """
        :param text: Message that should be posted
        Keyword-only arguments:
        :param channel: Name of the channel in the config. Otherwise, default_channel will be used
        """
        channel_url = self._channels[channel].get("url")
        if not channel_url and not self._url:
            return
        data = {"text": text}
        if not channel_url:  # for legacy hook
            data["channel"] = self._channels[channel]["name"]
            data["username"] = self._default_username
            data["icon_url"] = self._default_icon
        try:
            response = self._session.post(url=channel_url or self._url, data=json.dumps(data).encode("utf-8"))
            if response.status_code == 200:
                return

            ApplicationMetrics.critical_exceptions(
                service="slack_client", message=f"Slack service returned {response.status_code} and {response.text}"
            )
        except Exception as exc:
            logger.exception("Failed to send slack message")
            ApplicationMetrics.critical_exceptions(service="slack_client", message=f"Slack error {str(exc)}")

    def post_to_forecast(self, text: str):
        self.post(text, channel=SlackChannel.FORECAST)

    def post_preventative_check(self, text: str):
        self.post(text, channel=SlackChannel.PREVENTATIVE_CHECK)


slack_client = SlackClient()
