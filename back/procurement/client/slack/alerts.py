import logging
from datetime import datetime, time
from typing import Any

from procurement.core.cache_utils.factory import CACHES
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.typing import BOB_CODE, WH_CODE

from . import slack_client
from .formats import SlackFormat

logger = logging.getLogger(__name__)


class BaseAlert:
    def __init__(self, key: str = None, value: Any = None, message: str = None, enabled: bool = True):
        self.key = key
        self.value = value
        self.message = message
        self.enabled = enabled

    def _should_send(self):
        return self.enabled


class BaseSyncAlert(BaseAlert):
    def __init__(self, key: str = None, value: Any = None, message: str = None, enabled: bool = False):
        super().__init__(key, value, message, enabled)

    def _should_send(self) -> bool:
        current_time = datetime.now().time()
        is_in_time = not (
            killswitch.limit_preventative_alert_time
            and (current_time < time(hour=8, minute=30) or current_time > time(hour=17, minute=30))
        )
        return super()._should_send() and is_in_time


class OscarPackagingMissingForecastAlert(BaseSyncAlert):
    def __init__(self, sites: set[str], brand: str, week: ScmWeek):
        value = ",".join(sorted(sites))
        super().__init__(
            key=f"op_mf:{brand}:{week}",
            value=value,
            message=f"Please address the following: " f"Missing forecasts found for [{brand}] in [{value}].",
        )


class HjConnectionErrorAlert(BaseAlert):
    def __init__(self, db_name: str, exc: str | None):
        super().__init__(
            key=f"hj_con:{db_name}",
            value=exc,
            message=f"Please address the following: " f"HighJump connection failed for [{db_name}].\nWith {exc}",
        )


class JobFailedAlert(BaseSyncAlert):
    def __init__(self, job_name: str, job_failed: bool):
        super().__init__(
            key=f"job_fail:{job_name}",
            value=job_name if job_failed else None,
            message=f"Please address the following: " f"The following job failed to run: {job_name}",
        )


class PimtInventoryOutdated(BaseAlert):
    def __init__(self, whs: list[WH_CODE]):
        super().__init__(
            key=f"wh_outdated:{whs}",
            value=whs,
            message=f"{SlackFormat.channel()} Please address the following: "
            f"Following warehouses have not been updated in more than 1 day: {whs}",
        )


class KafkaInventorySnapshotOutdated(BaseAlert):
    def __init__(self, market: str, bob_codes: set[BOB_CODE]):
        super().__init__(
            key=f"inventory_snapshot_outdated:{market}",
            value=bob_codes,
            message=f"{SlackFormat.channel()} Please address the following: "
            f"Following sites have not been updated in more than 1 day: {bob_codes}",
        )


def preventative_alert(alert: BaseAlert) -> None:
    cache_value = CACHES.imt_slack_alerts_states.get_and_delete(alert.key)
    if not alert.value:
        return
    if not cache_value or cache_value != alert.value:
        slack_client.post_preventative_check(alert.message)
    CACHES.imt_slack_alerts_states.put(key=alert.key, data=alert.value)
