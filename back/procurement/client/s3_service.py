from enum import Str<PERSON><PERSON>
from io import BytesIO
from pathlib import Path

import boto3

from procurement.core.config_utils import config


class S3BucketName(StrEnum):
    IMT_FORMS = "manual_form_export"


class _S3Service:
    def __init__(self):
        self._buckets: dict = {}

    def upload_bytes(self, file: BytesIO, s3_file_name: str, bucket: S3BucketName, folder: str = None) -> None:
        if folder:
            s3_file_name = Path(folder) / s3_file_name

        self._get_bucket(bucket).put_object(Body=file, Key=str(s3_file_name))

    def _get_bucket(self, config_name: S3BucketName):
        """Configure and return Bucket object"""
        if config_name not in self._buckets:
            client = self._get_client()

            bucket_name = config["s3_export_configs"].get(config_name, {}).get("bucket")
            if not bucket_name:
                raise AttributeError(f"Bucket is not set for {config_name}")

            self._buckets[config_name] = client.Bucket(bucket_name)

        return self._buckets[config_name]

    @staticmethod
    def _get_client():
        credentials = config["s3"]

        if not credentials:
            raise AttributeError("Credentials are not set for S3")

        return boto3.resource("s3", **credentials)


S3Service = _S3Service()
