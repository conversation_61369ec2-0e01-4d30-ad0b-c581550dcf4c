import logging
import tempfile
from pathlib import Path

from procurement.core.config_utils import config

logger = logging.getLogger(__name__)


def dump_to_file(file_name: str, file_bytes: bytes):
    file_name = file_name.replace("/", "-")
    local_dir = config["export"].get("local_export_dir", "imt_export")
    path = Path(tempfile.gettempdir()) / local_dir
    path.mkdir(parents=True, exist_ok=True)
    with open(path / file_name, "wb") as file:
        file.write(file_bytes)
        logger.info("File exported to %s", file.name)
