import logging

import requests
from requests.auth import HTTP<PERSON>asicAuth

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.config_utils import config
from procurement.core.exceptions.client import IcsError
from procurement.core.metrics import ApplicationMetrics

logger = logging.getLogger(__name__)

REQUEST_TIMEOUT = 2 * 60  # 2 minutes


class IcsClient:
    def __init__(self, market: str):
        self.ics_config = config.get("ics", {}).get(market, {})
        self._auth = HTTPBasicAuth(self.ics_config.get("api_token"), "")
        self._url = self.ics_config.get("url")

    def get(self, endpoint: str, params: dict | None = None) -> list[dict] | dict | None:
        try:
            response = requests.get(
                f"{self._url}/{endpoint}",
                auth=self._auth,
                headers={"Content-Type": "application/json;charset=utf-8"},
                timeout=REQUEST_TIMEOUT,
                params=params,
            )
            if response.status_code == 200:
                return response.json()

            ApplicationMetrics.critical_exceptions(
                service="ics_client", message=f"ICS service returned code {response.status_code} - {response.text}"
            )
            raise IcsError(response.text)
        except Exception as exc:
            logger.exception("Failed to get ICS data on %s", endpoint)
            ApplicationMetrics.critical_exceptions(service="ics_client", message=f"ICS error {str(exc)}")
            return None

    def get_tickets(self, workspace_id: int | None = None) -> list[dict]:
        params = {"workspace_id": workspace_id} if workspace_id else None
        res = self.get("v2/tickets/", params=params)
        return res["tickets"] if "tickets" in res else []


ca_ics_client = IcsClient("ca")
us_ics_client = IcsClient("us")


def get_client(market: str) -> IcsClient:
    return us_ics_client if market == MARKET_US else ca_ics_client
