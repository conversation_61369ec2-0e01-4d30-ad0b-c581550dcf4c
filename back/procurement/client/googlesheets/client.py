import logging
from socket import timeout

from google.auth.exceptions import RefreshError
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from tenacity import after_log, retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from procurement.core.config_utils import config
from procurement.core.exceptions.gsheets import GSheetServiceError
from procurement.core.metrics import ApplicationMetrics

from .errors import GSheetsApiInternalError, GSheetsInvalidSheetError, GSheetsQuotaExceed
from .model import DataRange, SpreadsheetDetails

logger = logging.getLogger(__name__)


def group_funcs(*funcs):
    def group(retry_state):
        for func in funcs:
            func(retry_state)

    return group


def _process_http_error(spreadsheet_id, http_error):
    logger.warning(http_error)
    if http_error.resp.status == 400:
        error = GSheetsInvalidSheetError(http_error)
    elif http_error.resp.status == 429:
        ApplicationMetrics.gsheet_call(metric_type="quota_exceeded", status="429")
        error = GSheetsQuotaExceed(http_error)
    elif http_error.resp.status >= 500:
        ApplicationMetrics.gsheet_call(metric_type="api_internal_error", status=str(http_error.resp.status))
        error = GSheetsApiInternalError(http_error)
    else:
        ApplicationMetrics.gsheet_call(metric_type="failed", status="403/404")
        error = GSheetServiceError(
            {
                403: f"Service account have no permission to the document with ID {spreadsheet_id}",
                404: f"Document with ID {spreadsheet_id} not found or does not exist",
            }.get(http_error.resp.status, "Unknown error")
        )
    raise error from http_error


def _process_refresh_error(service, auth_error):
    logger.warning(auth_error)
    ApplicationMetrics.gsheet_call(metric_type="auth_error")
    try:
        service.build_resource()
    except Exception:  # nosec B110
        pass
    raise GSheetsApiInternalError(auth_error)


def _gsheet_retry_wrapper(func):
    post_functions = group_funcs(
        after_log(logger, logging.INFO),
        lambda retry_state: ApplicationMetrics.gsheet_call(call_type="gsheet_call_retry"),
    )

    internal_error_retry = retry(
        retry=retry_if_exception_type(GSheetsApiInternalError),
        stop=stop_after_attempt(2),
        wait=wait_exponential(2),
        after=post_functions,
    )
    timeout_retry = retry(
        retry=retry_if_exception_type(timeout),
        stop=stop_after_attempt(5),
        after=post_functions,
    )
    quota_retry = retry(
        retry=retry_if_exception_type(GSheetsQuotaExceed),
        stop=stop_after_attempt(8),
        wait=wait_exponential(2),
        after=post_functions,
    )

    @ApplicationMetrics.gsheet_call_time()
    @internal_error_retry
    @timeout_retry
    @quota_retry
    def _invoke_wrapper(service, spreadsheet_id, *args, **kwargs):
        try:
            res = func(service, spreadsheet_id, *args, **kwargs)
            ApplicationMetrics.gsheet_call(metric_type="success", status="200")
            return res
        except HttpError as http_error:
            _process_http_error(spreadsheet_id, http_error)
        except RefreshError as auth_error:
            _process_refresh_error(service, auth_error)
        return None

    return _invoke_wrapper


class GoogleSheetClient:
    scopes = ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive"]

    def __init__(self):
        self.sheets_resource = None
        self.drive_resource = None
        self.build_resource()

    def build_resource(self):
        googlesheet_sa = config["googlesheet_sa"]["config"]
        credentials = Credentials.from_service_account_info(googlesheet_sa, scopes=self.scopes)

        self.sheets_resource = build("sheets", "v4", credentials=credentials, cache=False)
        self.drive_resource = build("drive", "v3", credentials=credentials, cache=False)

    def get_doc_details(self, spreadsheet_id) -> SpreadsheetDetails:
        return SpreadsheetDetails(self._get_doc_details(spreadsheet_id))

    def read(self, spreadsheet_id, sheet_name, data_range: DataRange = None, is_formatted=True) -> list[list]:
        """
        Read spreadsheet

        :param spreadsheet_id: document ID
        :param sheet_name: document tab name
        :param data_range: the range of data
        :param is_formatted: if True then reads data from a cell after applied in-sheet formatting, otherwise reads
        raw value. For example raw date value '2022-07-29' might be shown as '07/29/2022' after in-sheet formatting.

        return list of rows
        """
        data_range = data_range or DataRange()
        return self._read_spreadsheets(spreadsheet_id, data_range.get_range(sheet_name), is_formatted)

    def update(  # pylint: disable=too-many-arguments
        self, spreadsheet_id, sheet_name, data, data_range: DataRange = None, force_update=True
    ):
        """
        Update spreadsheet content using one api call

        spreadsheet_id: document ID
        sheet_name: document tab name
        data: data, list of list values.
        first_row: first line index, number or 'auto'
        last_row: last write line index, number or 'auto'
        first_column: first column index, char or number or 'auto'
        last_column: last column index, char or number or 'auto'

        Example:
            # clear old rows, do not clear headers
            data = ["123-123", "week-38", "blabla"],  ["123-122", "week-38", "blabla"]
            service.update(
                spreadsheet_id = "spreadsheet id",
                sheet_name= "sheet name",
                data=data,
                first_row=2,
                first_column='A',
                last_row=len(data) + 100 # 100 old rows after current values will be removing,
                last_column='auto'
            )

            # custom headers, do not clear document
            service.update(
                spreadsheet_id = "spreadsheet id",
                sheet_name= "sheet name",
                data = [["sku", "week", "some value"], ["123-123", "week-38", "blabla"],
                        ["123-122", "week-38", "blabla"]],
                first_row=1,
                last_row='auto',
                first_column='A',
                last_column='auto'
            )

        return: dict with updated data information
        """
        data_range = data_range or DataRange()
        # Clean spreadsheet if data is empty
        if not data:
            return self.clean(spreadsheet_id, sheet_name, data_range)

        # Replace None values with empty strings, because None value not updates cell value

        if force_update:
            for row in data:
                for index, value in enumerate(row):
                    if value is None:
                        row[index] = ""

        self._calculate_range_limits(data, data_range)

        self._add_missing_data(data, data_range)

        return self._update(spreadsheet_id, data_range.get_range(sheet_name), {"values": data})

    @staticmethod
    def _calculate_range_limits(data, data_range: DataRange):
        auto_limits = frozenset({None, "auto"})

        if data_range.last_row in auto_limits:
            data_range.last_row = data_range.first_row + len(data) - 1

        if data_range.last_column in auto_limits:
            data_range.last_column = len(data[0]) + data_range.first_column - 1

    @staticmethod
    def _add_missing_data(data, data_range: DataRange):
        width = data_range.last_column - data_range.first_column + 1
        height = data_range.last_row - data_range.first_row + 1

        lacks_cols = width - len(data[0])
        if lacks_cols > 0:
            for row in data:
                row.extend("" for _ in range(lacks_cols))

        lacks_rows = height - len(data)
        if lacks_rows > 0:
            empty_row = [["" for _ in range(width)]]
            for _ in range(lacks_rows):
                data += empty_row

    @_gsheet_retry_wrapper
    def create_sheet(self, spreadsheet_id, sheet_name):
        body = {"requests": [{"addSheet": {"properties": {"title": sheet_name}}}]}
        # pylint: disable=no-member
        self.sheets_resource.spreadsheets().batchUpdate(spreadsheetId=spreadsheet_id, body=body).execute()

    def append(self, spreadsheet_id, sheet_name, data, data_range: DataRange = None):
        """
        Append rows, does not clear existing records

        spreadsheet_id: document ID
        sheet_name: document tab name
        data: data, list of list values

        return: dict with updated data information
        """
        data_range = data_range or DataRange()
        return self._append(spreadsheet_id, data_range.get_range(sheet_name), data)

    def clean(self, spreadsheet_id, sheet_name, data_range: DataRange = None) -> dict:
        """
        Clear spreadsheet content

        spreadsheet_id: document ID
        sheet_name: document tab name
        first_row: first line index, number
        last_row: last write line index, number
        first_column: first column index, char or number
        last_column: last column index, char or number

        return: dict with updated data information
        """
        data_range = data_range or DataRange()
        return self._clean(spreadsheet_id, data_range.get_range(sheet_name))

    @_gsheet_retry_wrapper
    def create_spreadsheet(self, title: str) -> str:
        spreadsheet = {"properties": {"title": title}}
        return (
            # pylint: disable=no-member
            self.sheets_resource.spreadsheets()
            .create(body=spreadsheet, fields="spreadsheetId")
            .execute()
            .get("spreadsheetId")
        )

    @_gsheet_retry_wrapper
    def delete_spreadsheet(self, spreadsheet_id: str):
        # pylint: disable=no-member
        return self.drive_resource.files().delete(fileId=spreadsheet_id).execute()

    @_gsheet_retry_wrapper
    def _append(self, spreadsheet_id, data_range, data):
        return (
            # pylint: disable=no-member
            self.sheets_resource.spreadsheets()
            .values()
            .append(spreadsheetId=spreadsheet_id, range=data_range, body=data, valueInputOption="RAW")
            .execute()
        )

    @_gsheet_retry_wrapper
    def _clean(self, spreadsheet_id, data_range):
        return (
            # pylint: disable=no-member
            self.sheets_resource.spreadsheets()
            .values()
            .clear(spreadsheetId=spreadsheet_id, range=data_range)
            .execute()
        )

    @_gsheet_retry_wrapper
    def _update(self, spreadsheet_id, data_range, data):
        return (
            # pylint: disable=no-member
            self.sheets_resource.spreadsheets()
            .values()
            .update(spreadsheetId=spreadsheet_id, range=data_range, body=data, valueInputOption="USER_ENTERED")
            .execute()
        )

    @_gsheet_retry_wrapper
    def _read_spreadsheets(self, spreadsheet_id, data_range, is_formatted):
        kwargs = {
            "spreadsheetId": spreadsheet_id,
            "range": data_range,
            "valueRenderOption": "FORMATTED_VALUE" if is_formatted else "UNFORMATTED_VALUE",
        }
        # pylint: disable=no-member
        return self.sheets_resource.spreadsheets().values().get(**kwargs).execute().get("values", [])

    @_gsheet_retry_wrapper
    def _get_doc_details(self, spreadsheet_id):
        # pylint: disable=no-member
        return self.sheets_resource.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()


gsheet_client = GoogleSheetClient()
