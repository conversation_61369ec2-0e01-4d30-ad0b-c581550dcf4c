class SpreadsheetDetails:
    def __init__(self, json_data):
        self.title = json_data["properties"]["title"]
        self.spreadsheet_id = json_data["spreadsheetId"]
        self.tabs = {}
        for tab_json in json_data["sheets"]:
            tab = SheetDetails(tab_json)
            self.tabs[tab.title] = tab

    def get_tab(self, tab_name):
        return self.tabs.get(tab_name)


class SheetDetails:
    def __init__(self, json_data):
        properties = json_data["properties"]
        self.title = properties["title"]
        self.row_count = properties["gridProperties"]["rowCount"]
        self.column_count = properties["gridProperties"]["columnCount"]


class DataRange:
    LETTERS_COUNT = ord("Z") - ord("A") + 1  # 26
    A_INDEX = ord("A")  # 65

    def __init__(self, first_row: int = 2, last_row: int = None, first_column: str = "A", last_column: str = None):
        self.first_row = first_row
        self.last_row = last_row
        self.first_column = self.convert_to_int(first_column)
        self.last_column = self.convert_to_int(last_column)

    @property
    def first_column_formatted(self) -> str:
        return self.convert_to_letter(self.first_column)

    @property
    def last_column_formatted(self) -> str:
        return self.convert_to_letter(self.last_column)

    def convert_to_letter(self, index: str | int) -> str | None:
        """Convert index to internal expected format"""
        if index is None:
            return None
        if isinstance(index, str):
            return index.upper()
        if isinstance(index, int):
            return self._to_letter(index)
        raise ValueError(f"Invalid column index {index}")

    def _to_letter(self, column: int) -> str:
        """
        Convert number index to string
        """
        character = chr(ord("A") + column % self.LETTERS_COUNT)
        remainder = column // self.LETTERS_COUNT
        if column >= self.LETTERS_COUNT:
            return self._to_letter(remainder - 1) + character
        return character

    def convert_to_int(self, column: int | str) -> int | None:
        """
        Convert string index to number
        """
        if isinstance(column, int):
            return column
        if column is None:
            return None

        character = column.upper()
        result = 0
        position = 1
        for char in reversed(character):
            result += (ord(char) - self.A_INDEX + 1) * position
            position *= self.LETTERS_COUNT
        return result - 1

    def get_range(self, sheet_name: str) -> str:
        search = f"{sheet_name}!{self.first_column_formatted}{self.first_row}:"
        search += self.last_column_formatted if self.last_column else self.first_column_formatted

        if self.last_row:
            search += str(self.last_row)
        return search
