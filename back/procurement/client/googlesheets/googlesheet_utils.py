import logging
import re
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Any

FRONT_FORMAT = "%m/%d/%Y"
DATE_FORMAT = "%Y-%m-%d"
MONTH_FORMAT = "%Y-%m"
MONTH_FORMAT_2 = "%b-%Y"
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
FRONT_DATETIME_FORMAT = "%Y-%m-%d %H:%M"
GSHEET_TIME_FORMAT_LOCALE = "%I:%M %p"
GSHEET_DATE_FORMAT = "%m/%d/%Y"
GSHEET_DATETIME_FORMAT_24H_1 = "%m/%d/%Y %H:%M"
GSHEET_DATETIME_FORMAT_24H_2 = "%m/%d/%Y %H-%M"
GSHEET_DATETIME_FORMAT_24H_3 = "%m/%d/%Y %H"
GSHEET_DATETIME_FORMAT_24H_4 = "%m/%d/%Y %H;%M"
GSHEET_DATETIME_FORMAT_12H_1 = "%m/%d/%Y %I:%M %p"
GSHEET_DATETIME_FORMAT_12H_1_SEC = "%m/%d/%Y %I:%M:%S %p"
GSHEET_DATETIME_FORMAT_12H_2 = "%m/%d/%Y %I-%M %p"
GSHEET_DATETIME_FORMAT_12H_3 = "%m/%d/%Y %I %p"
GSHEET_DATETIME_FORMAT_12H_4 = "%m/%d/%Y %I;%M %p"
GSHEET_DATETIME_FORMAT = GSHEET_DATETIME_FORMAT_24H_1
HJ_TIME_EST = "%b %d %Y  %I:%M%p"

logger = logging.getLogger(__name__)


def to_float(value) -> float | None:
    try:
        return float(value)
    except (ValueError, TypeError):
        return None


def to_decimal(value: str) -> Decimal | None:
    try:
        return Decimal(value.replace(",", "")) if value else None
    except (ValueError, TypeError, InvalidOperation):
        return None


def _is_int_string(value: Any) -> bool:
    if isinstance(value, str):
        return value[1:].isdigit() if value and value[0] in {"-", "+"} else value.isdigit()
    return False


def to_int(value: Any, nullable=True, round_up=False, default_value=None):
    if value is None:
        return default_value
    if isinstance(value, int):
        return value
    if _is_int_string(value):
        return int(value)
    replaced = value.replace(",", "") if isinstance(value, str) else value
    if _is_int_string(replaced):
        return int(replaced)
    if round_up and (float_val := to_float(replaced)) is not None:
        return round(float_val)
    # Can not parse to int or float
    if nullable:
        return default_value
    raise ValueError(f"Cannot parse value {value} to int, nullable={nullable}, is_round={round_up}")


def parse_date(raw_date, fmt=GSHEET_DATE_FORMAT):
    try:
        return datetime.strptime(raw_date, fmt) if raw_date else None
    except ValueError:
        return datetime.strptime(f"{raw_date}/{datetime.now().year}", fmt)


def parse_datetime(_datetime, fmt=GSHEET_DATETIME_FORMAT) -> datetime:
    try:
        return datetime.strptime(_datetime, fmt) if _datetime != "" else None
    except ValueError:
        return datetime.strptime(change_year_to_current_year(_datetime), fmt)


def change_year_to_current_year(_datetime):
    return re.sub(r"/\d+ ", f"/{datetime.now().year} ", _datetime)


def parse_datetime_str_12h_or_24h(dte, time_part, am_pm) -> datetime:
    timestamp_str_24h = f"{dte} {time_part}"
    timestamp_str_12h = f"{dte} {time_part} {am_pm}"
    formats_12h = [
        GSHEET_DATETIME_FORMAT_12H_1,
        GSHEET_DATETIME_FORMAT_12H_2,
        GSHEET_DATETIME_FORMAT_12H_3,
        GSHEET_DATETIME_FORMAT_12H_4,
    ]
    formats_24h = [
        GSHEET_DATETIME_FORMAT_24H_1,
        GSHEET_DATETIME_FORMAT_24H_2,
        GSHEET_DATETIME_FORMAT_24H_3,
        GSHEET_DATETIME_FORMAT_24H_4,
    ]

    for _format in formats_12h:
        try:
            return parse_datetime(timestamp_str_12h, _format)
        except ValueError:
            continue

    for _format in formats_24h:
        try:
            return parse_datetime(timestamp_str_24h, _format)
        except ValueError:
            continue

    try:
        logger.warning("Can not parse time - %s %s", time_part, am_pm)
        return parse_datetime(f"{dte} 12:00 AM", GSHEET_DATETIME_FORMAT_12H_1)
    except ValueError as exc:
        raise ValueError(f"Can not parse datetime - {dte} {time_part} {am_pm}") from exc


def percents_to_decimal(value):
    if value is None:
        return None
    if not value.endswith("%"):
        raise ValueError(f"Cannot parse percentage value {value} to decimal, % is absent")
    return Decimal(value[:-1]) / 100


def safe_sum(values):
    return sum(filter(None, values)) if values else 0


def col_to_num(col_str: str, shift=0):
    """Convert base26 column string to number."""
    expn = 0
    col_num = 0
    for char in reversed(col_str):
        col_num += (ord(char) - ord("A") + 1) * (26**expn)
        expn += 1

    return col_num - shift - 1


def num_to_col(column):
    """Convert number to base26 column string."""
    temp, letter = "", ""
    column += 1

    while column > 0:
        temp = (column - 1) % 26
        letter = chr(temp + 65) + letter
        column = (column - temp - 1) // 26

    return letter


def validate_required_field(value: Any, field_name: str):
    if value is None or value == "":
        raise ValueError(f'The field "{field_name}" is empty OR has invalid content.')


def validate_required_fields(row, fields: list[str]):
    for field in fields:
        validate_required_field(getattr(row, field), field)
