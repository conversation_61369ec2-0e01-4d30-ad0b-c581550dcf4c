from typing import Named<PERSON><PERSON><PERSON>

from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS


class Forecast(NamedTuple):
    site: str
    scm_week: ScmWeek
    sku_code: str
    forecast: UNITS
    brand: str


class NextExistingForecastDto(NamedTuple):
    site: str
    scm_week: ScmWeek
    sku_code: str
    forecast: UNITS


class SkuForecast(NamedTuple):
    sku_code: str
    forecast: UNITS


class PackagingForecast(NamedTuple):
    demand_pipeline: str
    week: ScmWeek
    brand: str
    site: str
    forecast: UNITS


class CanadaForecastRecipes(NamedTuple):
    sku_code: str
    recipes: list[str]
