from datetime import datetime
from typing import NamedTuple

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.typing import SKU_CODE


class SkuMeta(NamedTuple):
    sku_code: str
    sku_id: int
    sku_name: str
    unit: UnitOfMeasure | None
    category: str | None
    subcategory: str | None
    commodity_group: str | None
    brands: set[str]
    is_manufactured_sku: bool


class BasicSkuMeta(NamedTuple):
    sku_code: str
    sku_uuid: str
    unit: UnitOfMeasure | None


class SkuMetaWithBrands(NamedTuple):
    sku_code: str
    sku_uuid: str
    sku_name: str
    unit: UnitOfMeasure | None
    brands: list[str]


class SkuCodeName(NamedTuple):
    sku_code: str
    sku_name: str


class SkuCodeNameStatusCategory(NamedTuple):
    sku_code: str
    sku_name: str
    sku_status: str
    category: str | None


class AlternativeSkuMapping(NamedTuple):
    original_sku_code: SKU_CODE
    interchangable_sku_code: SKU_CODE


class SupplierSkuRecord(NamedTuple):
    supplier_sku_uuid: str
    culinary_sku_uuid: str
    last_updated: datetime


class SkuProfile(NamedTuple):
    size: str
    type: str
    profile: str


class SkuDetail(NamedTuple):
    sku_code: str
    sku_uuid: str
    sku_name: str
    sku_id: int
    purchasing_category: str
