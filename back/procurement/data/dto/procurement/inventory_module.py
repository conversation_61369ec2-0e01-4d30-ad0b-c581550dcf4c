from typing import NamedTuple


class InventoryModuleCommentInput(NamedTuple):
    sku_code: str
    text: str
    last_edited_by_id: int
    brand: str
    site: str


class InventoryModuleComment(NamedTuple):
    sku_code: str
    text: str
    last_edited_by_email: str


class InventoryModuleInHouseAdjustmentInput(NamedTuple):
    sku_code: str
    in_house_adjustment: int
    brand: str
    site: str


class InventoryModuleInHouseAdjustment(NamedTuple):
    sku_code: str
    value: int
