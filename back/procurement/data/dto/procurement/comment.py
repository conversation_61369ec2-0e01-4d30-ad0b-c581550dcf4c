from dataclasses import dataclass
from datetime import datetime
from typing import <PERSON><PERSON><PERSON><PERSON>

from procurement.constants.hellofresh_constant import Domain
from procurement.core.dates import ScmWeek
from procurement.data.models.constants import CommentType


class CommentInput(NamedTuple):
    domain: Domain
    week: ScmWeek
    site: str
    brand: str
    comment: str
    resource_id: str
    resource_type: CommentType


class CommentResource(NamedTuple):
    resource_id: str
    resource_type: CommentType
    domain: Domain


@dataclass
class Comment:
    comment_id: int
    domain: Domain
    brand: str
    site: str | None
    region: str | None
    sku_code: str
    sku_name: str
    resource_type: CommentType
    comment: str
    week: int
    last_updated: datetime
    updated_by: str
    po_number: str = None
    commodity_group_name: str = None
    purchasing_category_name: str = None


class CommentView(NamedTuple):
    site: str
    comment: str
    last_updated: datetime
    updated_by: str


class CommentPreView(NamedTuple):
    brand: str
    site: str
    resource_id: str
    week: ScmWeek
