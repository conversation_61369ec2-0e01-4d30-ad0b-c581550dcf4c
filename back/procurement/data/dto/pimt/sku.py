from typing import NamedTuple

from procurement.constants.hellofresh_constant import IngredientCategory


class IngredientDetails(NamedTuple):
    sku_code: str
    sku_name: str
    status: str
    purchasing_category: str
    commodity_groups: list[str]
    brands: list[str] | None = None

    @property
    def is_packaging(self) -> bool:
        return any(IngredientCategory.PACKAGING in name for name in (self.commodity_groups or []))

    @property
    def is_unlabeled(self) -> bool:
        return self.sku_name and "unlabeled" in self.sku_name.lower()
