from decimal import Decimal
from typing import Named<PERSON>up<PERSON>

from procurement.constants.hellofresh_constant import MAX_SUPPLIER_LEAD_TIME_WEEKS


class MasterReplenishmentItem(NamedTuple):
    site: str
    sku_code: str
    market: bool
    market_factor: Decimal
    min_order_qty: Decimal
    buyers: set[str]
    category: str = None
    status: str = None
    replenishment_type: str = None
    shelf_life: int = 0
    request_for_proposal: Decimal = Decimal()
    max_supplier_lead_time: int = MAX_SUPPLIER_LEAD_TIME_WEEKS


class MasterReplenishmentSku(NamedTuple):
    sku_code: str
    site: str
