from dataclasses import dataclass

from procurement.constants.hellofresh_constant import InventoryInputType, WhReceivingType


@dataclass
class Warehouse:
    code: str
    name: str
    bob_code: str
    ot_dcs: list[str]
    ot_suppliers: list[str]
    order: int
    region: str
    regional_dcs: list[str]
    receiving_type: WhReceivingType
    inventory_type: InventoryInputType
    hj_name: str | None
    is_3rd_party: bool
    market: str
    packaging_regions: tuple[str, ...] | None

    @property
    def is_e2open_grn(self) -> bool:
        return self.receiving_type == WhReceivingType.E2OPEN_GRN

    @property
    def is_e2open_inventory(self) -> bool:
        return self.inventory_type == InventoryInputType.E2OPEN
