from dataclasses import dataclass
from datetime import date, datetime

from procurement.core.dates import ScmWeek
from procurement.core.typing import BRAND, SIT<PERSON>, SKU_CODE


@dataclass
class CycleCountInput:
    sku_code: SKU_CODE
    site: SITE
    cycle_count_day: date
    units: int
    brand: BRAND
    scm_week: ScmWeek
    date_for_comparison: datetime


@dataclass
class CycleCount:
    sku_code: SKU_CODE
    cycle_count_day: date
    units: int
