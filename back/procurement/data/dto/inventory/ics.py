import dataclasses
from datetime import datetime
from enum import StrEnum
from typing import <PERSON><PERSON><PERSON><PERSON>

from procurement.core.dates import ScmWeek


class IcsTicketPreview(NamedTuple):
    week: ScmWeek
    sku_code: str | None
    po_number: str | None
    order_number: str | None
    bob_code: str


@dataclasses.dataclass
class IcsTicket:
    ticket_id: int
    ticket_link: str
    po_number: str | None
    order_number: str | None
    sku_code: str | None
    bob_code: str
    subject: str
    week: ScmWeek
    production_impact: str
    status: str | None
    request_type: str
    updated_at: datetime


@dataclasses.dataclass
class IcsTicketWithSiteBrand(IcsTicket):
    brand: str
    site: str


class IcsStatus(StrEnum):
    OPEN = "Open"
    CLOSED = "Closed"
    IN_PROGRESS = "In Progress"


ics_status_mapping = {
    2: IcsStatus.OPEN,
    5: IcsStatus.CLOSED,
    22: IcsStatus.IN_PROGRESS,
}
