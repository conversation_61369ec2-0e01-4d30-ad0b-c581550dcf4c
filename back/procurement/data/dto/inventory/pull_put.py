import dataclasses
from datetime import datetime
from enum import StrEnum
from typing import <PERSON><PERSON><PERSON><PERSON>

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek


class InventoryType(StrEnum):
    PULL = "Pull"
    INVENTORY = "Inventory"


@dataclasses.dataclass
class PullPutDto:
    inventory_id: int
    dc: str
    brand: str
    user_email: str
    sku_code: str
    sku_name: str | None
    qty: int
    updated_at: datetime
    comment: str | None
    week: ScmWeek
    unit_of_measure: UnitOfMeasure | None = None
    pull_put_type: InventoryType | str | None = dataclasses.field(init=False)

    def __post_init__(self):
        self.pull_put_type = InventoryType.INVENTORY.value if self.qty >= 0 else InventoryType.PULL.value


class ExportPullPut(NamedTuple):
    sku_name: str
    qty: int
