import dataclasses
from datetime import date, datetime
from typing import NamedTuple

from procurement.core.dates import ScmWeek


@dataclasses.dataclass(slots=True)
class ReceiptOverrideValidationItem:
    brand: str
    site: str
    po_number: str
    sku_code: str


@dataclasses.dataclass(slots=True)
class ReceiptOverride:
    po_number: str
    sku_code: str
    qty: int
    cases: int | None
    receiving_date: date | None


@dataclasses.dataclass(slots=True)
class ReceiptOverrideExtended(ReceiptOverride):
    brand: str
    site: str
    sku_name: str
    user: str
    upd_tmst: datetime
    comment: str | None
    week: ScmWeek = None


class ReceiptSku(NamedTuple):
    sku_code: str
    sku_name: str


class ReceiptData(NamedTuple):
    bob_code: str
    sku_code: str
    sku_name: str
    po_number: str
    supplier_name: str
    quantity_received: int
    cases_received: int
