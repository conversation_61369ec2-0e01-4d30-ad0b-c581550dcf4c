from datetime import date
from typing import Named<PERSON>up<PERSON>

from procurement.constants.hellofresh_constant import ProductionPlanType
from procurement.core.dates import ScmWeek
from procurement.core.typing import SITE, SKU_CODE, UNITS


class HybridNeedsDto(NamedTuple):
    sku_code: SKU_CODE
    day: date
    scm_week: ScmWeek
    value: UNITS


class ShiftNeedsDto(NamedTuple):
    sku_code: SKU_CODE
    day: date
    scm_week: ScmWeek
    value_day: UNITS
    value_night: UNITS
    value_third: UNITS


class HybridNeedsStatusDto(NamedTuple):
    day: date
    status: ProductionPlanType
    site: SITE


class HybridNeedsLiveUsage(NamedTuple):
    sku_code: SKU_CODE
    day: date
    value: UNITS
