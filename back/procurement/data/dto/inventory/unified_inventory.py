from datetime import date, datetime
from decimal import Decimal
from typing import <PERSON><PERSON><PERSON><PERSON>

from procurement.constants.hellofresh_constant import InventoryInputType, InventoryState
from procurement.core.typing import BOB_CODE, UNITS


class UnifiedInventoryUnits(NamedTuple):
    sku_code: str
    wh_code: str
    total: int


class UnifiedInventoryDto(NamedTuple):
    source: InventoryInputType
    bob_code: BOB_CODE
    po_number: str | None
    sku_code: str
    units: UNITS
    status: InventoryState
    snapshot_timestamp: datetime
    lot_code: str | None
    location_id: str | None
    expiration_date: date | None
    supplier: str | None


class UnifiedExpiredInventory(NamedTuple):
    sku_code: str
    lot_code: str
    wh_code: str
    cases: int
    inventory_expiration_date: date | None
    total: UNITS
    state: InventoryState
    po_number: str
    supplier: str
    supplier_code: str
    po_delivery_time_start: datetime
    case_size: UNITS
    case_price: Decimal
    grn_delivery_time_start: datetime


class UnifiedInventoryWhLastUpdate(NamedTuple):
    wh_code: str
    last_updated: date


class UnifiedInventoryE2OpenExport(NamedTuple):
    sku_code: str
    order_number: str
    po_number: str
    lot_code: str
    expiration_date: date
