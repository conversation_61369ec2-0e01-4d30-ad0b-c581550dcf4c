from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.constants.protobuf import GrnDeliveryLineState
from procurement.core.dates import ScmWeek


@dataclass
class GrnInput:
    bob_code: str
    po_number: str
    order_number: str | None
    sku_code: str
    week: ScmWeek
    units_received: Decimal
    cases_received: Decimal
    receipt_time_est: datetime
    status: GrnDeliveryLineState
    unit: UnitOfMeasure | None
    update_ts: datetime
    source: str


@dataclass
class GrnPo:
    order_number: str
    sku_code: str
    receipt_time_est: datetime
    units_received: Decimal
    cases_received: Decimal | None
    po_number: str | None = None
    sku_name: str | None = None
