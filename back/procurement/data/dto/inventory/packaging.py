from dataclasses import dataclass
from datetime import date
from typing import NamedTup<PERSON>

from procurement.core.dates import ScmWeek


@dataclass
class PackagingOverrideKey:
    week: ScmWeek
    site: str
    brand: str
    sku_code: str
    day: date


@dataclass(slots=True)
class PackagingOverride:
    on_hand_override: int | None
    incoming_override: int | None


@dataclass(slots=True)
class PackagingOverrideExtended(PackagingOverride):
    week: ScmWeek
    sku_code: str
    day: date


class PackagingDemand(NamedTuple):
    week: int
    sku_code: str
    demand_by_day: list[int]
