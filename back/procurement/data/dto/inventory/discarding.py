from datetime import datetime
from typing import NamedTuple


class Discard(NamedTuple):
    id: int
    user: str | None
    timestamp: datetime | None
    dc: str
    sku: str
    discarded_datetime: datetime | None
    quantity: int
    quality_instructions: str | None
    reason: str | None
    source: str | None
    week: str | None
    brand: str
    comment: str | None
    updated_by: str | None
    deleted_by: str | None
    deleted_ts: datetime | None
    market: str
