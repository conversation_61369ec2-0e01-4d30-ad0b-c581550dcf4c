from datetime import datetime
from typing import NamedTuple


class ReceivingItem(NamedTuple):
    po_number: str
    sku_code: str
    receive_timestamp: datetime
    case_count_one_total_units: int
    case_count_two_total_units: int | None
    case_count_three_total_units: int | None
    case_size_one: int | None
    case_size_two: int | None
    case_size_three: int | None
    total_cases_received: int | None
