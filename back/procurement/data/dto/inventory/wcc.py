from datetime import date
from typing import <PERSON><PERSON><PERSON><PERSON>

from procurement.constants.hellofresh_constant import Day<PERSON><PERSON><PERSON>eek
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory.weekend_coverage_checklist import ChecklistStatus


class WeekendCoverageChecklistItem(NamedTuple):
    brand: str
    site: str
    week: ScmWeek
    po_number: str
    sku_code: str


class WeekendCoverageChecklistFullItem(NamedTuple):
    id: int
    brand: str
    site: str
    week: ScmWeek
    po_number: str
    sku_code: str
    updated_by: str
    sku_name: str
    shipping_method: str
    carrier_name: str
    po_landing_day: DayOfWeek | None
    production_day_affected: DayOfWeek | None
    to_check: str
    contact_name_vendor_carrier: str
    email_phone: str
    back_up_vendor: str
    status: ChecklistStatus | None
    comment: str
    fob_pick_up_date: date


class ChecklistCount(NamedTuple):
    brand: str
    site: str
    po_number: str
    sku_code: str
    count: int
