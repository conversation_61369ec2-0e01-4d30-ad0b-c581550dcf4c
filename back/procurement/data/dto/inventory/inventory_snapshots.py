import dataclasses
from datetime import date

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.core.typing import UNITS


@dataclasses.dataclass(slots=True)
class InventorySnapshot:
    sku_code: str
    snapshot_date: date
    units: UNITS | None
    expiration_date: date
    location_type: str


@dataclasses.dataclass(slots=True)
class InventorySnapshotExtended(InventorySnapshot):
    bob_code: str
    state: str
    location_id: str


@dataclasses.dataclass(slots=True)
class LatestSnapshotByWh:
    bob_code: str
    inventory_type: InventoryInputType
    snapshot_date: date
