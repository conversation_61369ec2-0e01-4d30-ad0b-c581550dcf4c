from datetime import date
from decimal import Decimal
from typing import NamedTuple

from procurement.core.typing import UNITS


class Inventory(NamedTuple):
    sku_code: str
    quantity: UNITS
    snapshot_timestamp: date | None = None
    expiration_date: date | None = None
    license_count: int | None = None


class InventoryUnitPrice(NamedTuple):
    po_number: str
    sku_code: str
    cost: Decimal
