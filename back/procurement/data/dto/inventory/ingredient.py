from decimal import Decimal
from typing import NamedTuple

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek


class KittingFlag(NamedTuple):
    week: ScmWeek
    sku_code: str
    is_in_kitbox: str


class CommodityGroup(NamedTuple):
    id: int
    group_name: str


class PkgData(NamedTuple):
    sku_code: str
    weight_amount: float | Decimal | None
    weight_unit: str | None
    storage_location: str | None
    allergens: str | None
    sku_id: int
    sku_name: str
    code: str
    slot: str
    meal_name: str
    picks_2p: int | None
    picks_3p: int | None
    picks_4p: int | None
    picks_6p: int | None
    purcasing_category: str
    sub_recipe: str | None


class Meal(NamedTuple):
    slot: str
    sku_code: str
    picks_2p: int
    picks_4p: int
    picks_6p: int
    weight_amount: Decimal
    unit_of_measure: UnitOfMeasure


class SkuMeals(NamedTuple):
    sku_code: str
    meal_numbers: list[str]


class Mealkit(NamedTuple):
    id: int
    code: str
    slot: str
