from datetime import datetime
from typing import NamedTuple


class PostalAddress(NamedTuple):
    region_code: str
    postal_code: str
    administrative_area: str
    locality: str
    address_lines: list[str]
    organization: str


class Shipment(NamedTuple):
    po_number: str
    load_number: str
    pallet_count: int
    carrier_name: str
    origin_location: PostalAddress
    appointment_time: datetime | None
    execution_event: str | None
