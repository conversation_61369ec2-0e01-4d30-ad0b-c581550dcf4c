from decimal import Decimal
from typing import NamedTuple

from procurement.constants.hellofresh_constant import UnitOfMeasure


class ManufacturedSku(NamedTuple):
    manufactured_uuid: str
    manufactured_code: str
    manufactured_name: str


class ManufacturedSkuNameCode(NamedTuple):
    manufactured_name: str
    manufactured_code: str


class ManufacturedSkuPart(NamedTuple):
    root_uuid: str
    part_uuid: str
    unit_of_measure: UnitOfMeasure
    quantity: Decimal
    is_manufactured: bool
