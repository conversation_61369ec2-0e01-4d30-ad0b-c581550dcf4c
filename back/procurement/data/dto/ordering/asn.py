import dataclasses
from datetime import datetime
from decimal import Decimal

from procurement.constants.protobuf import GrnUnitMeasure
from procurement.core.typing import PO_NUMBER, SKU_CODE
from procurement.data.models.ordering.advance_shipping_notice import AdvanceShippingNoticeState


@dataclasses.dataclass(frozen=True)
class BaseAdvanceShippingNoticeDto:
    shipment_time: datetime
    planned_delivery_time: datetime
    sku_code: SKU_CODE
    shipping_state: AdvanceShippingNoticeState
    size: int
    packing_size: Decimal
    unit_measure: GrnUnitMeasure


@dataclasses.dataclass(frozen=True)
class AdvanceShippingNoticeDto(BaseAdvanceShippingNoticeDto):
    po_number: PO_NUMBER
