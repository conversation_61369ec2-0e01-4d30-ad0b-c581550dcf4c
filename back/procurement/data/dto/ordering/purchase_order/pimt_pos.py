import dataclasses
from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

from procurement.core.dates import ScmWeek


@dataclasses.dataclass(frozen=True)
class PimtPoItemBase:
    po_number: str
    week: str
    sku_code: str
    quantity: Decimal
    delivery_time_start: datetime
    case_size: Decimal
    cases_received: Decimal | None
    supplier: str
    sku_name: str
    order_size: int
    case_price: Decimal
    shipping_method: str
    emergency_reason: str
    grn_units: Decimal | None
    has_grn: bool
    supplier_code: int
    grn_receive_date: datetime
    ordered_by: str
    transfer_source_bob_code: str | None
    has_multiple_transfer_items: bool


@dataclasses.dataclass(frozen=True)
class PimtPoItem(PimtPoItemBase):
    total_price: Decimal
    appointment_time: datetime


class PimtInboundOrder(NamedTuple):
    bob_code: str
    grn_quantity: Decimal | None
    total_quantity: Decimal
    sku_code: str
    week: str | None = None


class PimtOutboundOrder(NamedTuple):
    total_quantity: Decimal
    sku_code: str
    supplier: str
    bob_code: str | None = None
    source_bob_code: str | None = None
    week: str | None = None


class SupplierOutboundPo(NamedTuple):
    supplier: str
    bob_code: str
    week: ScmWeek
    sku_code: str
    quantity: Decimal


class InventoryPo(NamedTuple):
    order_number: str
    sku_code: str
    delivery_time_start: datetime
    supplier: str
