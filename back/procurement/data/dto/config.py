from typing import NamedTuple

from procurement.core.dates import ScmWeek, ScmWeekConfig


class Market(NamedTuple):
    code: str
    name: str


class Brand(NamedTuple):
    # config keys
    code: str
    config_week: ScmWeek

    # brand config
    name: str
    market: str
    consolidated: bool
    scm_week_config: ScmWeekConfig
    order: int
    country_code: str

    # weekly config
    enabled: bool
