from datetime import datetime
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ReceivingModel(BaseModel):
    __tablename__ = "receiving"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]

    # General Delivery Information
    supplier: Mapped[str]
    supplier_id: Mapped[str]
    po: Mapped[str]
    sku_code: Mapped[str]
    sku_ingredient: Mapped[str]
    dc: Mapped[str]

    arrival_timestamp: Mapped[datetime]
    receive_timestamp: Mapped[datetime]

    # Delivery Acceptance
    delivery_status: Mapped[str]

    # Receiving Information
    total_pallets_received: Mapped[int | None]
    total_cases_received: Mapped[int | None]

    case_count_one_total_units: Mapped[int | None]
    case_count_two_total_units: Mapped[int | None]
    case_count_three_total_units: Mapped[int | None]

    case_size_one: Mapped[Decimal | None]
    case_size_two: Mapped[Decimal | None]
    case_size_three: Mapped[Decimal | None]

    # Additional Comments
    receiver_comments: Mapped[str | None]
    ticket_number: Mapped[str | None]

    week: Mapped[str]
    username: Mapped[str]
    source: Mapped[str]
    brand: Mapped[str]
