from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel, datetime_with_tz


class SiteModel(BaseModel):
    __tablename__ = "site"
    __table_args__ = (PrimaryKeyConstraint("id", "brand", "market"), {"schema": "inventory"})

    id: Mapped[str]
    brand: Mapped[str]
    market: Mapped[str]
    sequence_number: Mapped[int]
    name: Mapped[str]
    created_tmst: Mapped[datetime_with_tz]
    updated_tmst: Mapped[datetime_with_tz]
    timezone: Mapped[str]
    dc_azure_role: Mapped[str]
    user_id: Mapped[int]
