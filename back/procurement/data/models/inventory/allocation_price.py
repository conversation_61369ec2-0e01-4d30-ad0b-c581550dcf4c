from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class AllocationPriceModel(BaseModel):
    __tablename__ = "allocation_price"
    __table_args__ = (PrimaryKeyConstraint("scm_week", "brand", "site", "sku_code"), {"schema": "inventory"})

    scm_week: Mapped[int]
    brand: Mapped[str]
    site: Mapped[str]
    sku_code: Mapped[str]
    price: Mapped[Decimal]
