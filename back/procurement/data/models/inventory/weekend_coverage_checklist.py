from datetime import date, datetime
from enum import StrEnum

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ChecklistStatus(StrEnum):
    COMPLETED = "Completed"
    IN_PROGRESS = "In Progress"
    DELAYED = "Delayed"
    NO_RESPONSE = "No Response"
    OTHER = "Other"


class WeekendCoverageChecklistModel(BaseModel):
    __tablename__ = "weekend_coverage_checklist"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    week: Mapped[int]
    brand: Mapped[str]
    site: Mapped[str]
    po_number: Mapped[str]
    sku_code: Mapped[str]
    po_landing_day: Mapped[str | None]
    production_day_affected: Mapped[str | None]
    to_check: Mapped[str | None]
    contact_name_vendor_carrier: Mapped[str | None]
    email_phone: Mapped[str | None]
    back_up_vendor: Mapped[str | None]
    status: Mapped[str | None]
    comment: Mapped[str | None]
    updated_by_id: Mapped[int]
    last_updated: Mapped[datetime]
    fob_pick_up_date: Mapped[date | None]
    market: Mapped[str]
