from datetime import datetime
from decimal import Decimal

import sqlalchemy as sqla
from sqlalchemy import PrimaryKeyConstraint, <PERSON>ple
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel, datetime_with_tz


class GoodsReceiptNoteModel(BaseModel):
    __tablename__ = "goods_receipt_note"
    __table_args__ = (PrimaryKeyConstraint("po_number", "sku_code", "status"), {"schema": "inventory"})

    bob_code: Mapped[str]
    po_number: Mapped[str]
    order_number: Mapped[str]
    sku_code: Mapped[str]
    week: Mapped[int]
    units_received: Mapped[Decimal | None]
    cases_received: Mapped[Decimal | None]
    receipt_time_est: Mapped[datetime_with_tz | None]
    status: Mapped[int]
    unit: Mapped[str | None]
    update_ts: Mapped[datetime]
    source: Mapped[str]

    @classmethod
    def bob_code_source_tuple(cls) -> Tuple:
        return sqla.tuple_(cls.bob_code, cls.source)


# TODO: this table is a temporary replace during the killswitch.hj_grn_enabled transition
class LegacyHjGoodsReceiptNoteModel(BaseModel):
    __tablename__ = "legacy_format_goods_receipt_note"
    __table_args__ = (PrimaryKeyConstraint("po_number", "sku_code", "status"), {"schema": "inventory"})

    wh_id: Mapped[str | None]
    bob_code: Mapped[str | None]
    supplier_name: Mapped[str | None]
    po_number: Mapped[str]
    cases_received: Mapped[int | None]
    quantity_received: Mapped[Decimal | None]
    sku_code: Mapped[str]
    sku_name: Mapped[str | None]
    scm_week_raw: Mapped[str | None]
    status: Mapped[str]
    receipt_time_est: Mapped[datetime | None]
    unit: Mapped[str | None]
    update_ts: Mapped[datetime]
    market: Mapped[str]
    supplier_code: Mapped[str | None]
