from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class BulkSkusModel(BaseModel):
    __tablename__ = "bulk_skus"
    __table_args__ = (PrimaryKeyConstraint("packaged_sku_code", "bulk_sku_code"), {"schema": "inventory"})

    bulk_sku_code: Mapped[str]
    packaged_sku_code: Mapped[str]
    pick_conversion: Mapped[int]
    brands: Mapped[list[str]]


class MasterBulkSkuModel(BaseModel):
    __tablename__ = "master_bulk_sku"
    __table_args__ = (PrimaryKeyConstraint("bulk_sku_code"), {"schema": "inventory"})

    bulk_sku_code: Mapped[str]
