from datetime import date
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class WeeklySnapshotModel(BaseModel):
    __tablename__ = "weekly_snapshot"
    __table_args__ = (
        PrimaryKeyConstraint("market", "wh_code", "week", "sku_code", "expiration_date"),  # fake pk
        UniqueConstraint("market", "wh_code", "week", "sku_code", "expiration_date"),
        {"schema": "inventory"},
    )

    market: Mapped[str]
    wh_code: Mapped[str]
    sku_code: Mapped[str]
    week: Mapped[int]
    quantity: Mapped[Decimal]
    expiration_date: Mapped[date | None]
