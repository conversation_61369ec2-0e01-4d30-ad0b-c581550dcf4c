from datetime import date, datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PackagingOverrideModel(BaseModel):
    __tablename__ = "packaging_override"
    __table_args__ = PrimaryKeyConstraint("week", "site", "brand", "sku_code", "day"), {"schema": "inventory"}

    week: Mapped[int]
    site: Mapped[str]
    brand: Mapped[str]
    sku_code: Mapped[str]
    day: Mapped[date]
    on_hand_override: Mapped[int | None]
    incoming_override: Mapped[int | None]
    last_updated: Mapped[datetime]
    updated_by: Mapped[int]
