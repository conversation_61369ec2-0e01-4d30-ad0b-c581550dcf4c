from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class BrandModel(BaseModel):
    __tablename__ = "brand"
    __table_args__ = (PrimaryKeyConstraint("id", "market"), {"schema": "inventory"})

    id: Mapped[str]
    market: Mapped[str]
    name: Mapped[str]
    order: Mapped[int]
    consolidated: Mapped[bool]
    week_calendar_mon_shift: Mapped[int]
    week_length: Mapped[int]
    country_code: Mapped[str]


class BrandConfigModel(BaseModel):
    __tablename__ = "brand_config"
    __table_args__ = (PrimaryKeyConstraint("brand", "market", "week"), {"schema": "inventory"})

    brand: Mapped[str]
    market: Mapped[str]
    week: Mapped[int]
    enabled: Mapped[bool]
