from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class TopVarianceCommentModel(BaseModel):
    __tablename__ = "top_variance_comment"
    __table_args__ = (
        PrimaryKeyConstraint("site"),  # fake pk
        UniqueConstraint("market", "week", "brand", "site", "sku_code", "po_number"),
        {"schema": "inventory"},
    )

    market: Mapped[str]
    week: Mapped[int]
    brand: Mapped[str]
    site: Mapped[str]
    sku_code: Mapped[str]
    po_number: Mapped[str]
    comment: Mapped[str]
    last_edited_by: Mapped[str]
