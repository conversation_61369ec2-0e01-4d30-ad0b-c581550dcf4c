from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class BuyerSkuModel(BaseModel):
    __tablename__ = "buyer_sku"
    __table_args__ = (PrimaryKeyConstraint("brand", "site", "sku_code", "market"), {"schema": "inventory"})

    user_id: Mapped[int]
    sku_code: Mapped[str]
    site: Mapped[str]
    brand: Mapped[str]
    market: Mapped[str]
