from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class SupplierSkuModel(BaseModel):
    __tablename__ = "supplier_sku"
    __table_args__ = (PrimaryKeyConstraint("supplier_sku_uuid"), {"schema": "inventory"})

    supplier_sku_uuid: Mapped[str]
    culinary_sku_uuid: Mapped[str]
    last_updated: Mapped[datetime]
