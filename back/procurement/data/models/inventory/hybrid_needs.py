from datetime import date
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class HybridNeedsIngredientsModel(BaseModel):
    __tablename__ = "hybrid_needs_ingredients"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "dc", "scm_week", "date", "brand"), {"schema": "inventory"})

    sku_code: Mapped[str]
    dc: Mapped[str]
    scm_week: Mapped[str]
    date: Mapped[date]
    value: Mapped[Decimal]
    brand: Mapped[str]


class HybridNeedsIngredientsStatusModel(BaseModel):
    __tablename__ = "hybrid_needs_ingredients_status"
    __table_args__ = (PrimaryKeyConstraint("scm_week", "day", "site", "brand"), {"schema": "inventory"})

    site: Mapped[str]
    scm_week: Mapped[str]
    day: Mapped[date]
    brand: Mapped[str]
    status: Mapped[str]


class HybridNeedsLiveUsageModel(BaseModel):
    __tablename__ = "hybrid_needs_live_usage"
    __table_args__ = (PrimaryKeyConstraint("brand", "scm_week", "site", "sku_code", "day"), {"schema": "inventory"})

    site: Mapped[str]
    sku_code: Mapped[str]
    scm_week: Mapped[int]
    day: Mapped[date]
    brand: Mapped[str]
    value: Mapped[str]


class HybridNeedsIngredientsShiftLevelModel(BaseModel):
    __tablename__ = "hybrid_needs_ingredients_shift_level"
    __table_args__ = (PrimaryKeyConstraint("date", "scm_week", "dc", "sku_code", "brand"), {"schema": "inventory"})

    sku_code: Mapped[str]
    dc: Mapped[str]
    scm_week: Mapped[str]
    date: Mapped[date]
    value_day: Mapped[Decimal]
    value_night: Mapped[Decimal]
    value_third: Mapped[Decimal]
    brand: Mapped[str]


class DeliveryDateNeedsModel(BaseModel):
    __tablename__ = "delivery_date_needs"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "dc", "scm_week", "date", "brand"), {"schema": "inventory"})

    sku_code: Mapped[str]
    dc: Mapped[str]
    scm_week: Mapped[str]
    date: Mapped[date]
    value: Mapped[Decimal]
    brand: Mapped[str]
