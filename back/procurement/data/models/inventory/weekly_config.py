from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class WeeklyConfigModel(BaseModel):
    __tablename__ = "weekly_config"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    site: Mapped[str]
    brand: Mapped[str]
    market: Mapped[str]
    week: Mapped[int]
    enabled: Mapped[bool]
    properties: Mapped[dict]
    user_id: Mapped[int]
