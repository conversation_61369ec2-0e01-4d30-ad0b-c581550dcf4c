from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PackagingSkuProfileModel(BaseModel):
    __tablename__ = "packaging_sku_profile"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "market"), {"schema": "inventory"})

    sku_code: Mapped[str]
    size: Mapped[str | None]
    type: Mapped[str | None]
    profile: Mapped[str | None]
    market: Mapped[str]
