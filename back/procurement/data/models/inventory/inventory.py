from datetime import date, datetime
from uuid import UUID

from sqlalchemy import Index, PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.constants.hellofresh_constant import InventoryInputType, InventoryState
from procurement.data.models.base import BaseModel


class UnifiedInventoryModel(BaseModel):
    __tablename__ = "unified_inventory"
    __table_args__ = (
        Index(
            "i_unified_inventory_key",
            "order_number",
            "sku_code",
            "wh_code",
            "inventory_type",
            "lot_code",
            "location_id",
            "inventory_status",
            "expiration_date",
            "snapshot_timestamp",
            unique=True,
        ),
        PrimaryKeyConstraint(
            "order_number",
            "sku_code",
            "wh_code",
            "inventory_type",
            "lot_code",
            "location_id",
            "inventory_status",
            "expiration_date",
            "snapshot_timestamp",
        ),
        {"schema": "inventory"},
    )

    wh_code: Mapped[str]
    po_number: Mapped[str | None]
    order_number: Mapped[str | None]
    sku_code: Mapped[str]
    lot_code: Mapped[str]
    # Either case or unit quantity should be set
    case_quantity: Mapped[int | None]
    unit_quantity: Mapped[int | None]
    expiration_date: Mapped[date | None]
    inventory_status: Mapped[InventoryState]
    inventory_type: Mapped[InventoryInputType]
    snapshot_timestamp: Mapped[datetime]
    snapshot_id: Mapped[UUID]
    supplier: Mapped[str | None]
    case_size: Mapped[int | None]
    location_id: Mapped[str | None]
    imt_update_ts: Mapped[datetime | None]
