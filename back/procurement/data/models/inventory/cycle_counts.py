from datetime import date, datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class CycleCountsModel(BaseModel):
    __tablename__ = "cycle_counts"
    __table_args__ = (
        PrimaryKeyConstraint("scm_week", "brand", "site", "sku_code", "cycle_count_day"),
        {"schema": "inventory"},
    )

    site: Mapped[str]
    sku_code: Mapped[str]
    cycle_count_day: Mapped[date]
    units: Mapped[int]
    brand: Mapped[str]
    scm_week: Mapped[str]
    date_of_count: Mapped[datetime]
