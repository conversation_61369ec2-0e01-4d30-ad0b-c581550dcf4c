from datetime import date, datetime
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class InventorySnapshotModel(BaseModel):
    __tablename__ = "inventory_snapshot"
    __table_args__ = (
        PrimaryKeyConstraint(
            "wh_code",
            "snapshot_ts",
            "state",
            "expiration_date",
            "location_type",
            "location_id",
            "sku_code",
            "inventory_type",
        ),
        {"schema": "inventory"},
    )

    wh_code: Mapped[str]
    sku_code: Mapped[str]
    snapshot_ts: Mapped[datetime]
    expiration_date: Mapped[date | None]
    location_type: Mapped[str]
    location_id: Mapped[str]
    state: Mapped[str]
    inventory_type: Mapped[str]
    unit_quantity: Mapped[Decimal]
