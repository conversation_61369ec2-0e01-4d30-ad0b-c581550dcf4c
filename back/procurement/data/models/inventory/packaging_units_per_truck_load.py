from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PackagingSkuImportModel(BaseModel):
    __tablename__ = "packaging_sku_import"
    __table_args__ = (PrimaryKeyConstraint("bob_code", "sku_code"), {"schema": "inventory"})

    bob_code: Mapped[str]
    sku_code: Mapped[str]
    units_per_truck_load: Mapped[int | None]
