from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class DiscardModel(BaseModel):
    __tablename__ = "discard"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    user: Mapped[str | None]
    timestamp: Mapped[datetime | None]
    dc: Mapped[str]
    sku: Mapped[str]
    discarded_datetime: Mapped[datetime | None]
    quantity: Mapped[int]
    quality_instructions: Mapped[str | None]
    reason: Mapped[str | None]
    source: Mapped[str | None]
    week: Mapped[str | None]
    brand: Mapped[str]
    comment: Mapped[str | None]
    updated_by: Mapped[str | None]
    deleted_by: Mapped[str | None]
    deleted_ts: Mapped[datetime | None]
    market: Mapped[str]
