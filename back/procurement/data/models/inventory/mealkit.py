from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class MealkitModel(BaseModel):
    __tablename__ = "mealkit"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    code: Mapped[str]
    slot: Mapped[str]
    meal_name: Mapped[str]
    scm_week: Mapped[str]
    brand: Mapped[str]
    sub_recipe: Mapped[str]


class MealkitIngredientModel(BaseModel):
    __tablename__ = "mealkit_ingredient"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]

    mealkit_id: Mapped[int]
    sku_code: Mapped[str]
    picks_2p: Mapped[int]
    picks_3p: Mapped[int]
    picks_4p: Mapped[int]
    picks_6p: Mapped[int]
    is_in_kitbox: Mapped[bool]
    dc_list: Mapped[list[str]]
    weight_amount: Mapped[float | None]
    weight_unit: Mapped[str | None]
