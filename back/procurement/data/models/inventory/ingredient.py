from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PurchasingCategoryModel(BaseModel):
    __tablename__ = "purchasing_category"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    name: Mapped[str]


class PurchasingSubcategoryModel(BaseModel):
    __tablename__ = "purchasing_subcategory"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    name: Mapped[str]


class CommodityGroupModel(BaseModel):
    __tablename__ = "commodity_group"
    __table_args__ = (PrimaryKeyConstraint("id"), UniqueConstraint("group_name"), {"schema": "inventory"})

    id: Mapped[int]
    group_name: Mapped[str]


class IngredientSiteCommodityGroupModel(BaseModel):
    __tablename__ = "ingredient_site_commodity_group"
    __table_args__ = (PrimaryKeyConstraint("site", "sku_code", "market"), {"schema": "inventory"})

    sku_code: Mapped[str]
    site: Mapped[str]
    commodity_group_id: Mapped[int]
    market: Mapped[str]


class IngredientModel(BaseModel):
    __tablename__ = "ingredient"
    __table_args__ = (PrimaryKeyConstraint("sku_code"), {"schema": "inventory"})

    sku_code: Mapped[str]
    pack_size_amount: Mapped[float | None]
    pack_size_unit: Mapped[str | None]
    storage_location: Mapped[str | None]
    brand: Mapped[str]
    allergens: Mapped[str | None]
    is_receipt_bp_drop: Mapped[bool]
    is_type_media: Mapped[bool]
