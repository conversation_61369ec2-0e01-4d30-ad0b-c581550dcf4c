from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint, orm
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PullPutModel(BaseModel):
    __tablename__ = "pull_put"
    __table_args__ = (
        PrimaryKeyConstraint("id"),
        {"schema": "inventory"},
    )

    id: Mapped[int]
    user_email: Mapped[str]
    dc: Mapped[str]
    brand: Mapped[str]
    week: Mapped[str]
    market: Mapped[str]
    source: Mapped[str]
    sku_code: Mapped[str]
    qty: Mapped[int]
    comment: Mapped[str | None]
    upd_tmst: Mapped[datetime]
    cre_tmst: Mapped[datetime] = orm.mapped_column(default=datetime.now)
    deleted_by: Mapped[str]
    deleted_ts: Mapped[datetime]
