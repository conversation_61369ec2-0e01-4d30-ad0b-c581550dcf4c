from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class RemakeToolModel(BaseModel):
    __tablename__ = "remake_tool"
    __table_args__ = (PrimaryKeyConstraint("week", "dc", "meal"), {"schema": "inventory"})

    meal: Mapped[str]
    picks_2p: Mapped[int | None]
    picks_4p: Mapped[int | None]
    picks_6p: Mapped[int | None]
    week: Mapped[str]
    dc: Mapped[str]
