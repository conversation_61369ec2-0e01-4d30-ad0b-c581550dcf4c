from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class IcsTicketsModel(BaseModel):
    __tablename__ = "ics_tickets"
    __table_args__ = (PrimaryKeyConstraint("market", "week", "bob_code", "ticket_id"), {"schema": "inventory"})

    market: Mapped[str]
    week: Mapped[int]
    bob_code: Mapped[str]
    ticket_id: Mapped[int]

    sku_code: Mapped[str | None]
    po_number: Mapped[str | None]
    order_number: Mapped[str | None]
    subject: Mapped[str]
    ticket_link: Mapped[str]
    production_impact: Mapped[str]
    request_type: Mapped[str]
    status: Mapped[int]
    updated_at: Mapped[datetime]
