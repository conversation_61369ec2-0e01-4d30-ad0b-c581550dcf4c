from datetime import date, datetime

from sqlalchemy import ColumnElement, PrimaryKeyConstraint, Select
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ReceiptOverrideModel(BaseModel):
    __tablename__ = "receipt_override"
    __table_args__ = (PrimaryKeyConstraint("brand", "dc", "po_number", "sku_code", "week"), {"schema": "inventory"})

    user: Mapped[str]
    dc: Mapped[str]
    brand: Mapped[str]
    week: Mapped[str]
    source: Mapped[str]
    po_number: Mapped[str]
    sku_code: Mapped[str]
    qty: Mapped[int]
    cases: Mapped[int | None]
    receiving_date: Mapped[date | None]
    upd_tmst: Mapped[datetime]
    cre_tmst: Mapped[datetime]
    deleted_by: Mapped[str]
    deleted_ts: Mapped[datetime]
    market: Mapped[str]
    comment: Mapped[str]

    @classmethod
    def select_with_deleted(cls, *fields) -> Select:
        return super().base_select(*fields)

    @classmethod
    def base_select(cls, *fields: ColumnElement) -> Select:
        return super().base_select(*fields).where(ReceiptOverrideModel.deleted_ts.is_(None))
