from datetime import datetime

from sqlalchemy import ColumnElement, PrimaryKeyConstraint, Select
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PoVoidModel(BaseModel):
    __tablename__ = "po_void"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "inventory"})

    id: Mapped[int]
    user: Mapped[str]
    dc: Mapped[str]
    week: Mapped[str]
    brand: Mapped[str]
    po_number: Mapped[str]
    supplier_name: Mapped[str | None]
    sku_code: Mapped[str | None]
    comment: Mapped[str | None]
    source: Mapped[str | None]
    cre_tmst: Mapped[datetime]
    deleted_by: Mapped[str | None]
    deleted_ts: Mapped[datetime | None]
    last_updated: Mapped[datetime | None]
    market: Mapped[str]

    @classmethod
    def select_with_deleted(cls, *fields) -> Select:
        return super().base_select(*fields)

    @classmethod
    def base_select(cls, *fields: ColumnElement) -> Select:
        return super().base_select(*fields).where(PoVoidModel.deleted_ts.is_(None))
