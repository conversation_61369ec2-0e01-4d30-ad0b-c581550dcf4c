from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class VendorManagedInventoryModel(BaseModel):
    __tablename__ = "vendor_managed_inventory"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "region", "supplier_name", "market"), {"schema": "inventory"})

    region: Mapped[str]
    sku_code: Mapped[str]
    market: Mapped[str]
    supplier_name: Mapped[str]
    units: Mapped[int | None]
    inbound: Mapped[int | None]
    outbound: Mapped[int | None]
