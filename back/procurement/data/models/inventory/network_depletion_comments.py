from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class NetworkDepletionCommentModel(BaseModel):
    __tablename__ = "network_depletion_comment"
    __table_args__ = (PrimaryKeyConstraint("market", "week", "resource_id", "sku_code"), {"schema": "inventory"})

    market: Mapped[str]
    week: Mapped[int]
    resource_id: Mapped[str]
    sku_code: Mapped[str]
    text: Mapped[str]
    updated_by: Mapped[str]
    last_updated: Mapped[datetime]
