from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PackagingDemandModel(BaseModel):
    __tablename__ = "packaging_demand"
    __table_args__ = (PrimaryKeyConstraint("week", "site", "brand", "sku_code", "market"), {"schema": "inventory"})

    week: Mapped[int]
    site: Mapped[str]
    brand: Mapped[str]
    sku_code: Mapped[str]
    demand_by_day: Mapped[list[int]]
    market: Mapped[str]
