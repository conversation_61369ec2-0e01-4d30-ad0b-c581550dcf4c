from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class WorkOrderModel(BaseModel):
    __tablename__ = "work_order"
    __table_args__ = (PrimaryKeyConstraint("wo_uuid", "sku_code"), {"schema": "inventory"})

    wo_uuid: Mapped[str]
    menu_week: Mapped[int]
    high_jump_warehouse_id: Mapped[str]
    recipe_sku: Mapped[str]
    sku_code: Mapped[str]
    quantity: Mapped[Decimal]
