from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class OrganicSkuModel(BaseModel):
    __tablename__ = "organic_sku_mapping"
    __table_args__ = (
        PrimaryKeyConstraint("interchangable_sku_code", "original_sku_code", "market"),
        {"schema": "inventory"},
    )

    interchangable_sku_code: Mapped[str]
    original_sku_code: Mapped[str]
    market: Mapped[str]


class PackagingSkuModel(BaseModel):
    __tablename__ = "packaging_sku_mapping"
    __table_args__ = (
        PrimaryKeyConstraint("interchangable_sku_code", "original_sku_code", "market"),
        {"schema": "inventory"},
    )

    original_sku_code: Mapped[str]
    interchangable_sku_code: Mapped[str]
    market: Mapped[str]
