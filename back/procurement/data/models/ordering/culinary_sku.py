from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class CulinarySkuModel(BaseModel):
    __tablename__ = "culinary_sku"
    __table_args__ = (PrimaryKeyConstraint("sku_uuid"), {"schema": "ordering"})

    sku_uuid: Mapped[str]
    sku_id: Mapped[int | None]
    sku_code: Mapped[str | None]
    sku_name: Mapped[str | None]
    market: Mapped[str]
    last_updated: Mapped[datetime]
    status: Mapped[str | None]
    storage_location: Mapped[str | None]
    unit: Mapped[str | None]
    brands: Mapped[list[str] | None]
    sku_type: Mapped[str | None]
    purchasing_category_id: Mapped[int | None]
    purchasing_subcategory_id: Mapped[int | None]
    is_manufactured_sku: Mapped[bool]
