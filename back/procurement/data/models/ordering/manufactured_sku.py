from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ManufacturedSkuModel(BaseModel):
    __tablename__ = "manufactured_sku"
    __table_args__ = (PrimaryKeyConstraint("manufactured_uuid"), {"schema": "ordering"})

    manufactured_uuid: Mapped[str]
    manufactured_code: Mapped[str]
    manufactured_name: Mapped[str]
    market: Mapped[str]


class ManufacturedSkuWeekModel(BaseModel):
    __tablename__ = "manufactured_sku_week"
    __table_args__ = (PrimaryKeyConstraint("manufactured_uuid", "week"), {"schema": "ordering"})

    manufactured_uuid: Mapped[str]
    week: Mapped[int]


class ManufacturedSkuPartModel(BaseModel):
    __tablename__ = "manufactured_sku_part"
    __table_args__ = (PrimaryKeyConstraint("root_uuid", "part_uuid"), {"schema": "ordering"})

    root_uuid: Mapped[str]
    part_uuid: Mapped[str]
    unit_of_measure: Mapped[str]
    quantity: Mapped[Decimal]
    is_manufactured: Mapped[bool]
