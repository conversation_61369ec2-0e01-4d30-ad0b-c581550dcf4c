from datetime import date

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class SupplierSplitsModel(BaseModel):
    __tablename__ = "supplier_splits"
    __table_args__ = (
        PrimaryKeyConstraint("dc", "sku_code", "start_week", "end_week", "market"),
        {"schema": "ordering"},
    )

    dc: Mapped[str]
    sku_code: Mapped[str]
    lead_time: Mapped[int]
    min_order_quantity: Mapped[int]
    incremental_order_quantity: Mapped[int]
    start_date: Mapped[date]
    end_date: Mapped[date]
    start_week: Mapped[int]
    end_week: Mapped[int]
    market: Mapped[str]
