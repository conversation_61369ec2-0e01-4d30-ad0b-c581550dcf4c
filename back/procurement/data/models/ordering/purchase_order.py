from datetime import datetime
from decimal import Decimal

from sqlalchemy import ColumnElement, ForeignKey, PrimaryKeyConstraint, Select
from sqlalchemy import orm as sqla_orm
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PurchaseOrderModel(BaseModel):
    __tablename__ = "purchase_order"
    __table_args__ = (PrimaryKeyConstraint("po_uuid"), {"schema": "ordering"})

    po_uuid: Mapped[str]
    po_number: Mapped[str]
    order_number: Mapped[str]
    dc: Mapped[str]
    brand: Mapped[str]
    supplier_code: Mapped[int | None]
    supplier: Mapped[str | None]
    ot_po_status: Mapped[str]
    is_sent: Mapped[bool]
    week: Mapped[str]
    order_date: Mapped[datetime]
    delivery_time_start: Mapped[datetime]
    emergency_reason: Mapped[str | None]
    ordered_by: Mapped[str | None]
    ot_last_updated: Mapped[datetime | None]
    deleted: Mapped[bool] = sqla_orm.mapped_column(default=False)
    internal_last_updated: Mapped[datetime] = sqla_orm.mapped_column(default=datetime.now)
    shipping_method: Mapped[str | None]
    bob_code: Mapped[str]
    source_bob_code: Mapped[str | None]

    @classmethod
    def select_all_pos(cls, *fields) -> Select:
        return super().base_select(*fields)

    @classmethod
    def base_select(cls, *fields: ColumnElement) -> Select:
        return super().base_select(*fields).where(~PurchaseOrderModel.deleted, PurchaseOrderModel.is_sent)


class PurchaseOrderSkuModel(BaseModel):
    __tablename__ = "purchase_order_sku"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "ordering"})

    id: Mapped[int]
    po_uuid: Mapped[str] = sqla_orm.mapped_column(ForeignKey("ordering.purchase_order.po_uuid"))
    sku_uuid: Mapped[str]
    order_size: Mapped[int]
    order_unit: Mapped[str]
    case_price: Mapped[Decimal]
    case_size: Mapped[Decimal]
    case_unit: Mapped[str]
    quantity: Mapped[Decimal]
    buffer: Mapped[Decimal | None]
    total_price: Mapped[Decimal]


class TransferOrderSkuModel(BaseModel):
    __tablename__ = "transfer_order_sku"
    __table_args__ = (PrimaryKeyConstraint("po_uuid", "line_item_uuid"), {"schema": "ordering"})

    line_item_uuid: Mapped[str]
    po_uuid: Mapped[str] = sqla_orm.mapped_column(ForeignKey("ordering.purchase_order.po_uuid"))
    sku_uuid: Mapped[str]
    order_size: Mapped[int]
    order_unit: Mapped[str]
    case_price: Mapped[Decimal]
    case_size: Mapped[Decimal]
    case_unit: Mapped[str]
    quantity: Mapped[Decimal]
    total_price: Mapped[Decimal]
    original_po_number: Mapped[str | None]
    original_supplier_id: Mapped[str | None]
    original_supplier_code: Mapped[str | None]
    original_lot_code: Mapped[str | None]
