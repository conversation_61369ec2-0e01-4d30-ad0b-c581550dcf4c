from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class AllowedProduceBufferModel(BaseModel):
    __tablename__ = "allowed_produce_buffer"
    __table_args__ = (PrimaryKeyConstraint("brand", "site", "week", "sku_code"), {"schema": "ordering"})

    brand: Mapped[str]
    site: Mapped[str]
    sku_code: Mapped[str]
    week: Mapped[int]
    total_buffer: Mapped[Decimal]
