from datetime import datetime
from decimal import Decimal
from enum import IntEnum

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class AdvanceShippingNoticeState(IntEnum):
    SHIPPING_STATE_UNSPECIFIED = 0
    SHIPPING_STATE_SHIPPED = 1
    SHIPPING_STATE_PARTIALLY_SHIPPED = 2
    SHIPPING_STATE_ON_HOLD = 3
    SHIPPING_STATE_CANCELLED = 4


class AdvanceShippingNoticeModel(BaseModel):
    __tablename__ = "advance_shipping_notice"
    __table_args__ = (PrimaryKeyConstraint("po_uuid", "sku_code"), {"schema": "ordering"})

    po_uuid: Mapped[str]
    order_number: Mapped[str]
    shipment_time: Mapped[datetime]
    planned_delivery_time: Mapped[datetime]
    sku_code: Mapped[str]
    shipping_state: Mapped[int]
    size: Mapped[int]
    packing_size: Mapped[Decimal]
    unit_measure: Mapped[int]
