from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PurchaseQtyRecommendationModel(BaseModel):
    __tablename__ = "purchase_qty_recommendation"
    __table_args__ = (PrimaryKeyConstraint("week", "site", "sku_uuid", "supplier_uuid"), {"schema": "ordering"})

    week: Mapped[int]
    site: Mapped[str]
    sku_uuid: Mapped[str]
    supplier_uuid: Mapped[str]
    quantity: Mapped[Decimal]
