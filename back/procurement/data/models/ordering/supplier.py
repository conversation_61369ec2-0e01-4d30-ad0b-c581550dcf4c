from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class SupplierModel(BaseModel):
    __tablename__ = "supplier"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "ordering"})

    id: Mapped[str]
    code: Mapped[int]
    name: Mapped[str]
    legal_name: Mapped[str]
    market: Mapped[str]
    last_updated: Mapped[datetime]
