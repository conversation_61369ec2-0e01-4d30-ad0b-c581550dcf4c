from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class POAcknowledgementLineItemsModel(BaseModel):
    __tablename__ = "po_acknowledgement_line_items"
    __table_args__ = (PrimaryKeyConstraint("po_uuid", "sku_code"), {"schema": "ordering"})

    po_uuid: Mapped[str]
    order_number: Mapped[str]
    sku_code: Mapped[str]
    sku_id: Mapped[int]
    state: Mapped[int]
    number_of_pallets: Mapped[int]
    unit_of_measure: Mapped[str]
    size: Mapped[int]
    packing_size: Mapped[int]
    promised_date: Mapped[datetime]
