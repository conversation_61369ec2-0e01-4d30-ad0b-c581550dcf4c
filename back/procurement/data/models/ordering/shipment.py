from datetime import datetime
from enum import IntEnum

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class AppointmentStateEnum(IntEnum):
    UNSPECIFIED = 0
    REQUESTED = 1
    CONFIRMED = 2
    VOIDED = 3
    MANUAL = 4


class POShipmentModel(BaseModel):
    __tablename__ = "purchase_order_shipment"
    __table_args__ = (PrimaryKeyConstraint("po_number"), {"schema": "ordering"})

    po_number: Mapped[str]
    load_number: Mapped[str]
    pallet_count: Mapped[int]
    carrier_name: Mapped[str]
    execution_event: Mapped[str | None]
    region_code: Mapped[str]
    postal_code: Mapped[str]
    administrative_area: Mapped[str]
    locality: Mapped[str]
    address_lines: Mapped[list[str]]
    organization: Mapped[str]
    appointment_time: Mapped[datetime]
    appointment_state: Mapped[int | None]
    batch_sequence: Mapped[int | None]
