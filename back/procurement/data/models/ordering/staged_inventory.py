from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class StagedInventoryModel(BaseModel):
    __tablename__ = "staged_inventory"
    __table_args__ = (PrimaryKeyConstraint("week", "site", "sub_recipe_id"), {"schema": "ordering"})

    sub_recipe_id: Mapped[str]
    week: Mapped[int]
    site: Mapped[str]
    quantity: Mapped[int]
