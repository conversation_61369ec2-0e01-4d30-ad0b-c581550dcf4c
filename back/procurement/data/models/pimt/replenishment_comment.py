from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ReplenishmentCommentModel(BaseModel):
    __tablename__ = "replenishment_comment"
    __table_args__ = (PrimaryKeyConstraint("market", "region", "sku_code"), {"schema": "pimt"})

    market: Mapped[str]
    region: Mapped[str]
    sku_code: Mapped[str]
    text: Mapped[str]
    updated_by: Mapped[str]
    last_updated: Mapped[datetime]
