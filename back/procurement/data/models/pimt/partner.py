from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.data.models.base import BaseModel


class WarehouseModel(BaseModel):
    __tablename__ = "warehouse"
    __table_args__ = (PrimaryKeyConstraint("code"), {"schema": "pimt"})

    code: Mapped[str]
    name: Mapped[str]
    bob_code: Mapped[str]
    ot_dcs: Mapped[list[str]]
    ot_suppliers: Mapped[list[str]]
    order: Mapped[int]
    region: Mapped[str]
    regional_dcs: Mapped[list[str]]
    receiving_type: Mapped[str]
    inventory_type: Mapped[InventoryInputType]
    hj_name: Mapped[str | None]
    is_3rd_party: Mapped[bool]
    market: Mapped[str]
    packaging_regions: Mapped[tuple[str, ...] | None]
