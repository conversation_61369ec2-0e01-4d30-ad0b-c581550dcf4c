from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PlannedDepletionModel(BaseModel):
    __tablename__ = "planned_depletion"
    __table_args__ = (
        PrimaryKeyConstraint("week", "region", "market", "demand_pipeline", "sku_code", "owner"),
        {"schema": "pimt"},
    )

    week: Mapped[int]
    sku_code: Mapped[str]
    quantity: Mapped[Decimal]
    demand_pipeline: Mapped[str]
    region: Mapped[str]
    owner: Mapped[str]
    market: Mapped[str]
