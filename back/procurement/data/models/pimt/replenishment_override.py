from datetime import datetime
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ReplenishmentOverrideModel(BaseModel):
    __tablename__ = "replenishment_override"
    __table_args__ = (PrimaryKeyConstraint("market", "region", "sku_code"), {"schema": "pimt"})

    market: Mapped[str]
    region: Mapped[str]
    sku_code: Mapped[str]
    value: Mapped[Decimal]
    updated_by: Mapped[str]
    last_updated: Mapped[datetime]
