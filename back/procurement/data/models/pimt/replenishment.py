from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class MasterReplenishmentModel(BaseModel):
    __tablename__ = "master_replenishment"
    __table_args__ = (PrimaryKeyConstraint("site", "sku_code"), {"schema": "pimt"})

    site: Mapped[str]
    sku_code: Mapped[str]
    status: Mapped[str | None]
    market: Mapped[bool | None]
    replenishment_type: Mapped[str | None]
    shelf_life: Mapped[int | None]
    max_supplier_lead_time: Mapped[int | None]
    min_order_qty: Mapped[Decimal | None]
    request_for_proposal: Mapped[Decimal | None]
    market_factor: Mapped[Decimal | None]
    replenishment_buyer: Mapped[str | None]
