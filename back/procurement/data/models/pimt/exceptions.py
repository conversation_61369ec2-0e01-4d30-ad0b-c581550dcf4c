from datetime import date

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ExceptionModel(BaseModel):
    __tablename__ = "exceptions_metrics"
    __table_args__ = (PrimaryKeyConstraint("exception_date", "market"), {"schema": "pimt"})

    exception_date: Mapped[date]
    stats: Mapped[dict]
    market: Mapped[str]
