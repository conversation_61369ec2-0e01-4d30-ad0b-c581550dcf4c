from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel, datetime_with_tz


class GsheetMetaModel(BaseModel):
    __tablename__ = "gsheet_meta"
    __table_args__ = (
        PrimaryKeyConstraint("id"),
        UniqueConstraint("doc_code", "brand", "market", "project"),
        {"schema": "procurement"},
    )

    id: Mapped[int]
    name: Mapped[str]
    doc_code: Mapped[str]
    market: Mapped[str]
    weekly: Mapped[bool]
    created_tmst: Mapped[datetime_with_tz]
    order: Mapped[int]
    title_template: Mapped[str]
    brand: Mapped[str | None]
    required: Mapped[bool]
    project: Mapped[str | None]
    visible: Mapped[bool]


class GsheetAdminModel(BaseModel):
    __tablename__ = "gsheet_admin"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})

    id: Mapped[int]
    meta_id: Mapped[int]
    scm_week: Mapped[str | None]
    gsheet_id: Mapped[str]
    created_tmst: Mapped[datetime_with_tz]
    updated_tmst: Mapped[datetime_with_tz]
    user_id: Mapped[int]
