from datetime import date

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class ReleaseLogModel(BaseModel):
    __tablename__ = "release_log"
    __table_args__ = (PrimaryKeyConstraint("release_date", "feature_key"), {"schema": "procurement"})

    feature_key: Mapped[int]
    feature_type: Mapped[str]
    release_date: Mapped[date]
    dc_users_available: Mapped[bool]
    description: Mapped[str]
