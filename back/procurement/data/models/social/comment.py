from datetime import datetime

from sqlalchemy import Enum
from sqlalchemy import orm as sqla_orm
from sqlalchemy.orm import Mapped

from procurement.constants.hellofresh_constant import Domain
from procurement.data.models.base import BaseModel as BModel
from procurement.data.models.constants import CommentType


class CommentModel(BModel):
    __tablename__ = "comment"
    __table_args__ = {"schema": "procurement"}

    id: Mapped[int] = sqla_orm.mapped_column(primary_key=True, autoincrement=True)
    domain: Mapped[Domain] = sqla_orm.mapped_column(Enum(*Domain))
    resource_type: Mapped[CommentType] = sqla_orm.mapped_column(Enum(*CommentType))
    brand: Mapped[str]
    site: Mapped[str]
    resource_id: Mapped[str]
    comment: Mapped[str]
    week: Mapped[int]
    last_updated: Mapped[datetime]
    updated_by: Mapped[str]
