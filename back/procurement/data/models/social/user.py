from datetime import datetime

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class UserModel(BaseModel):
    __tablename__ = "user"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})

    id: Mapped[int]
    first_name: Mapped[str]
    last_name: Mapped[str]
    email: Mapped[str]
    picture: Mapped[str]
    last_login: Mapped[datetime]
    forced_out: Mapped[bool]


class RoleModel(BaseModel):
    __tablename__ = "role"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})

    id: Mapped[int]
    full_name: Mapped[str | None]
    short_name: Mapped[str | None]
    priority: Mapped[int]


class UserRoleModel(BaseModel):
    __tablename__ = "user_role"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})

    id: Mapped[int]
    user_id: Mapped[int]
    role_id: Mapped[int]
    market: Mapped[str]


class PermissionModel(BaseModel):
    __tablename__ = "permission"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})
    id: Mapped[int]
    name: Mapped[str]


class PermissionRoleModel(BaseModel):
    __tablename__ = "permission_role"
    __table_args__ = (PrimaryKeyConstraint("permission_id", "role_id"), {"schema": "procurement"})

    permission_id: Mapped[int]
    role_id: Mapped[int]
