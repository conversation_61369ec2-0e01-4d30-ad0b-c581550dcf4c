from datetime import datetime

import sqlalchemy as sqla
from sqlalchemy import orm as sqla_orm
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class UserViewStateModel(BaseModel):
    __tablename__ = "user_view_state"
    __table_args__ = {"schema": "procurement"}

    id: Mapped[int] = sqla_orm.mapped_column(primary_key=True, autoincrement=True)
    user_id: Mapped[int]
    resource: Mapped[str]
    name: Mapped[str]
    state: Mapped[str | None]
    last_usage: Mapped[datetime] = sqla_orm.mapped_column(server_default=sqla.text("NOW()"))
