from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class NetworkDepletionPresetModel(BaseModel):
    __tablename__ = "network_depletion_preset"
    __table_args__ = (PrimaryKeyConstraint("user_id", "id", "market"), {"schema": "procurement"})

    id: Mapped[int]
    name: Mapped[str]
    user_id: Mapped[int]
    configuration: Mapped[dict]
    market: Mapped[str]
