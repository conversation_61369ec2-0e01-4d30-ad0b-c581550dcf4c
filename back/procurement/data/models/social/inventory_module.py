from datetime import date

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class InventoryModuleCommentModel(BaseModel):
    __tablename__ = "inventory_module_comment"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "site", "brand"), {"schema": "procurement"})

    sku_code: Mapped[str]
    site: Mapped[str]
    brand: Mapped[str]
    text: Mapped[str]
    last_edited_by: Mapped[int]


class InventoryModuleInHouseAdjustmentModel(BaseModel):
    __tablename__ = "inventory_module_in_house_adjustment"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "site", "brand"), {"schema": "procurement"})

    sku_code: Mapped[str]
    site: Mapped[str]
    brand: Mapped[str]
    in_house_adjustment: Mapped[int]
    adjustment_date: Mapped[date]
