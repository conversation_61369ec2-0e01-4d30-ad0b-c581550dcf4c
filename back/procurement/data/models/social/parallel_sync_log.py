from enum import StrEnum

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy import orm as sqla_orm
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel, datetime_with_tz


class SyncStatus(StrEnum):
    """
    Available job statuses
    """

    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    QUEUED = "queued"


class ParallelSyncLogModel(BaseModel):
    __tablename__ = "parallel_sync_log"
    __table_args__ = {"schema": "procurement"}

    id: Mapped[int] = sqla_orm.mapped_column(primary_key=True, autoincrement=True)
    market: Mapped[str | None]
    job_name: Mapped[str]
    parent_job_ids: Mapped[list[str] | None]
    sync_status: Mapped[str | None]
    warnings: Mapped[str | None]
    sync_time: Mapped[datetime_with_tz]
    global_parent_id: Mapped[int | None] = sqla_orm.mapped_column(ForeignKey("parallel_sync_log.id"))
