from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class DcModel(BaseModel):
    __tablename__ = "distribution_center"
    __table_args__ = (PrimaryKeyConstraint("id"), {"schema": "procurement"})

    id: Mapped[str]
    bob_code: Mapped[str]
    name: Mapped[str]
    enabled: Mapped[bool]
    tz: Mapped[str]
    is_third_party: Mapped[bool]
    market: Mapped[str]
    wms_type: Mapped[str | None]
    parent_id: Mapped[str | None]
    organization: Mapped[str | None]
