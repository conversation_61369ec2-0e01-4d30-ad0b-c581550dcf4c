from collections.abc import Iterable
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Annotated

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy import Enum as SqlaEnum
from sqlalchemy import Integer, Numeric, Select, Text
from sqlalchemy import orm as sqla_orm
from sqlalchemy.dialects.postgresql import ARRAY, JSONB, TIMESTAMP, Insert
from sqlalchemy.orm import DeclarativeBase, InstrumentedAttribute
from sqlalchemy.sql import ColumnCollection

datetime_with_tz = Annotated[datetime, sqla_orm.mapped_column(TIMESTAMP(timezone=True))]  # pylint: disable=invalid-name


class BaseModel(DeclarativeBase):
    type_annotation_map = {
        Enum: SqlaEnum(Enum, native_enum=False),
        str: Text,
        list[int]: ARRAY(Integer),
        list[str]: ARRAY(Text),
        tuple[str, ...]: ARRAY(Text, as_tuple=True),
        list[Decimal]: ARRAY(Numeric),
        dict: JSON<PERSON>,
    }

    @classmethod
    def get_primary_key_columns(cls) -> ColumnCollection:
        return cls.__table__.primary_key.columns

    @classmethod
    def get_excluded_columns(
        cls, insert_query: Insert, exclude_columns: Iterable[InstrumentedAttribute] | None = None
    ) -> ColumnCollection:
        """
        Returns all insert query's excluded columns except primary keys or :param exclude_columns: if supplied.
        """
        excluded_columns = (
            {k.name for k in exclude_columns} if exclude_columns else {c.name for c in cls.get_primary_key_columns()}
        )
        return ColumnCollection((c.key, c) for c in insert_query.excluded if c.key not in excluded_columns)

    @classmethod
    def get_non_key_columns(cls) -> ColumnCollection:
        return ColumnCollection((n, c) for n, c in cls.__table__.columns.items() if not c.primary_key)

    @classmethod
    def base_select(cls, *fields: ColumnElement) -> Select:
        fields = fields or [cls]
        return sqla.select(*fields).select_from(cls)
