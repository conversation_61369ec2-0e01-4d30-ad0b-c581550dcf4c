from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class PackagingLongTermForecastModel(BaseModel):
    __tablename__ = "packaging_long_term_forecast"
    __table_args__ = (
        PrimaryKeyConstraint("week", "brand", "site", "market", "demand_pipeline"),
        {"schema": "forecast"},
    )

    week: Mapped[int]
    brand: Mapped[str]
    site: Mapped[str]
    market: Mapped[str]
    demand_pipeline: Mapped[str]
    forecast: Mapped[int | None]
