from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class OscarModel(BaseModel):
    __tablename__ = "oscar"
    __table_args__ = (PrimaryKeyConstraint("dc", "scm_week", "sku_code", "brand"), {"schema": "forecast"})

    dc: Mapped[str]
    scm_week: Mapped[str]
    sku_code: Mapped[str]
    forecast: Mapped[Decimal]
    brand: Mapped[str]
    forecast_by_day: Mapped[list[Decimal] | None]
    units: Mapped[str]
