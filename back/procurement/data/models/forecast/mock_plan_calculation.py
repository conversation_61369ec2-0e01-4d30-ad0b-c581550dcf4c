from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class MockPlanModel(BaseModel):
    __tablename__ = "mock_plan"
    __table_args__ = (PrimaryKeyConstraint("week", "brand", "site", "production_type"), {"schema": "forecast"})

    week: Mapped[int]
    site: Mapped[str]
    brand: Mapped[str]
    production_type: Mapped[str]
    # includes wednesday_1 - thursday_2 (9 days) for HF, EP, GC
    # includes friday_1 - tuesday_2 (12 days) for FJ
    weights: Mapped[list[Decimal] | None]
