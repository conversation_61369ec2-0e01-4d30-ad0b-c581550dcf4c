from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class DemandPipelineSkuMappingModel(BaseModel):
    __tablename__ = "demand_pipeline_sku_mapping"
    __table_args__ = (PrimaryKeyConstraint("sku_code", "market"), {"schema": "forecast"})

    sku_code: Mapped[str]
    sku_type: Mapped[str]
    demand_pipeline: Mapped[str]
    market: Mapped[str]
