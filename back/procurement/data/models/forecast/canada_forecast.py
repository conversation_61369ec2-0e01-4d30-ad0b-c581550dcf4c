from datetime import date
from decimal import Decimal

from sqlalchemy import PrimaryKeyConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class CanadaForecastModel(BaseModel):
    __tablename__ = "canada_forecast"
    __table_args__ = (PrimaryKeyConstraint("site", "brand", "scm_week", "sku_code", "day"), {"schema": "forecast"})

    sku_code: Mapped[str]
    site: Mapped[str]
    brand: Mapped[str]
    scm_week: Mapped[int]
    day: Mapped[date]
    value: Mapped[Decimal]


class CanadaForecastRecipeModel(BaseModel):
    __tablename__ = "canada_forecast_recipe"
    __table_args__ = (PrimaryKeyConstraint("week", "site", "brand", "sku_code", "recipe"), {"schema": "forecast"})

    sku_code: Mapped[str]
    week: Mapped[int]
    site: Mapped[str]
    brand: Mapped[str]
    recipe: Mapped[str]
    recipe_name: Mapped[str]
    picks: Mapped[dict]
