from datetime import date, datetime
from decimal import Decimal
from enum import StrEnum

from sqlalchemy import PrimaryKeyConstraint, UniqueConstraint
from sqlalchemy.orm import Mapped

from procurement.data.models.base import BaseModel


class HjPalletSnapshotModel(BaseModel):
    __tablename__ = "v_procurement_export_lps"
    __table_args__ = (
        UniqueConstraint("week", "wh_id", "sku_code", "expiration_date"),
        PrimaryKeyConstraint("week", "wh_id", "sku_code", "expiration_date"),  # fake pk
        {"schema": "highjump"},
    )

    week: Mapped[str]
    wh_id: Mapped[str]
    sku_code: Mapped[str]
    pallet_quantity: Mapped[int]
    expiration_date: Mapped[date]
    unique_license_plate_count: Mapped[int]


class HjPackagingPalletSnapshotModel(BaseModel):
    __tablename__ = "packaging_pallet_snapshot"
    __table_args__ = (PrimaryKeyConstraint("snapshot_date", "wh_id", "sku_code"), {"schema": "highjump"})

    snapshot_date: Mapped[date]
    wh_id: Mapped[str]
    sku_code: Mapped[str]
    pallet_quantity: Mapped[int]


class LatestHjPalletSnapshotsModel(BaseModel):
    __tablename__ = "latest_hj_pallet_snapshots"
    __table_args__ = (PrimaryKeyConstraint("wh_id"), {"schema": "highjump"})  # fake pk

    sku_code: Mapped[str | None]
    license_plate: Mapped[str | None]
    pallet_quantity: Mapped[int | None]
    wh_id: Mapped[str | None]
    sync_date: Mapped[date]
    expiration_date: Mapped[date]


class HJReceiptsModel(BaseModel):
    __tablename__ = "v_procurement_export_receipts"
    __table_args__ = (PrimaryKeyConstraint("wh_id"), {"schema": "highjump"})

    wh_id: Mapped[str | None]
    supplier_name: Mapped[str | None]
    po_number: Mapped[str | None]
    cases_received: Mapped[int | None]
    quantity_received: Mapped[Decimal | None]
    case_size: Mapped[Decimal | None]
    sku_code: Mapped[str | None]
    sku_name: Mapped[str | None]
    scm_week_raw: Mapped[str | None]
    status: Mapped[str | None]
    receipt_time_est: Mapped[datetime | None]
    unit: Mapped[str | None]
    market: Mapped[str]
    supplier_code: Mapped[str | None]


class HJInvSnapshotModel(BaseModel):
    __tablename__ = "v_procurement_export_inv"
    __table_args__ = (PrimaryKeyConstraint("wh_id"), {"schema": "highjump"})  # fake pk

    wh_id: Mapped[str]
    sku_code: Mapped[str]
    quantity: Mapped[int | None]
    date: Mapped[date | None]
    updated_ts: Mapped[datetime]
    expiration_date: Mapped[datetime]


class HjWipModel(BaseModel):
    __tablename__ = "v_highjump_wip"
    __table_args__ = (PrimaryKeyConstraint("wh_id", "sku_code"), {"schema": "highjump"})  # fake pk

    sku_code: Mapped[str]
    wh_id: Mapped[str]
    quantity: Mapped[int]


class HjDiscardModel(BaseModel):
    __tablename__ = "v_highjump_discard"
    __table_args__ = (PrimaryKeyConstraint("wh_id"), {"schema": "highjump"})  # fake pk

    sku_code: Mapped[str]
    wh_id: Mapped[str]
    tran_type: Mapped[str]
    discard_date: Mapped[datetime]
    quantity: Mapped[int]


class HjWipConsumptionModel(BaseModel):
    __tablename__ = "wip_consumption"
    __table_args__ = (
        PrimaryKeyConstraint("week", "wh_id", "sku_code", "destination_location", "tran_type", "tran_date"),
        {"schema": "highjump"},
    )

    sku_code: Mapped[str]
    week: Mapped[int]
    wh_id: Mapped[str]
    destination_location: Mapped[str]
    tran_type: Mapped[str]
    tran_date: Mapped[date]
    quantity: Mapped[Decimal]


class DiscardType(StrEnum):
    WIP_DISCARD = "WIP Discard"
    DISCARD = "Discard"
    BULK_DISCARD = "Bulk Item Discard"
    AUTOBUGGER_DISCARD = "Autobagger Discard"
    DONATION = "Donation"
