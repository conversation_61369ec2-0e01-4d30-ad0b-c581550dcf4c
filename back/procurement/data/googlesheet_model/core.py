from __future__ import annotations

import abc
import inspect
import logging
import typing
from abc import ABC
from datetime import datetime
from decimal import Decimal
from typing import Any, Callable, ClassVar, Generic, TypeVar

from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE

from procurement.client.googlesheets import DataRange, googlesheet_utils, gsheet_client

logger = logging.getLogger(__name__)


INVALID_CELL_VALUES = ("#N/A", "#VALUE!", "#REF!")
FAST_UPDATE_ROWS_OVERLAP = 100

CELL_T = TypeVar("CELL_T")  # pylint: disable=invalid-name


class AbstractCell(ABC, Generic[CELL_T]):
    """Base class representing a googlesheet cell"""

    def __init__(self, cell_index: str, *, default_value: CELL_T | None = None, header: str = ""):
        """
        :param cell_index: column's literal at original sheet
        :param header: name of the cell's column (str)
        """
        self.cell_index = cell_index
        self.num_index = googlesheet_utils.col_to_num(cell_index)
        self.num_end_index = self.num_index  # to better handle cases when the value spans over more than 1 column
        self.default_value = default_value
        self.header = header
        self.name = None

    def write_header(self, headers: list[str], offset: int = 0) -> None:
        index = self.num_index - offset
        headers[index] = self.header

    def _find_content(self, data: list[str], offset: int) -> str:
        """
        Find cell's value in given data
        """
        index = self.num_index - offset
        return data[index] if index < len(data) else ""

    def read_value(self, data: list[str], offset: int = 0) -> CELL_T:
        """
        Return the cell's value as string
        """
        value = self._find_content(data, offset)
        value = str(self._check(value))
        return self._get_typed_value(self._post_process(value))

    def write_value(self, value: CELL_T | None, row: list[str], offset: int = 0) -> None:
        index = self.num_index - offset
        row[index] = self._get_write_format_value(value)

    def _get_write_format_value(self, value: CELL_T | None) -> str:
        if value is None:
            return ""
        return str(value)

    @abc.abstractmethod
    def _get_typed_value(self, value: str) -> CELL_T | None: ...

    @staticmethod
    def _post_process(value: str) -> str:
        """
        Strip the value

        :param value: cell's value
        """
        if value is not None and isinstance(value, str):
            value = value.strip()
        return value

    @staticmethod
    def _check(value: str) -> str:
        """
        Check if the value is valid

        :param value: cell's value
        """
        if isinstance(value, str) and any(value.startswith(invalid_value) for invalid_value in INVALID_CELL_VALUES):
            raise ValueError(f"Invalid value {value}")
        return value


class Cell(AbstractCell[str]):
    def _get_typed_value(self, value: str) -> str | None:
        return value


class IntCell(AbstractCell[int]):
    """Represents cell with an integer value"""

    def __init__(
        self,
        cell_index: str,
        *,
        default_value: int | None = None,
        nullable: bool = True,
        round_up: bool = False,
        header: str = "",
    ):
        """
        :param nullable: True if column can have None value. It is recommended to use nullable=False only
        for testing and debugging
        :param round_up: if True then round up float values to int
        """
        super().__init__(cell_index, default_value=default_value, header=header)
        self.nullable = nullable
        self.round_up = round_up

    def _get_typed_value(self, value: str) -> int | None:
        return googlesheet_utils.to_int(value, self.nullable, self.round_up, self.default_value)


class FloatCell(AbstractCell[float]):
    def _get_typed_value(self, value: str) -> float | None:
        return googlesheet_utils.to_float(value)


class DecimalCell(AbstractCell[Decimal]):
    def _get_typed_value(self, value: str) -> Decimal | None:
        return googlesheet_utils.to_decimal(value)


class DateCell(AbstractCell[datetime]):
    def __init__(self, cell_index: str, *date_formats: str, header: str = "", print_format: str = None):
        """
        :param date_formats: date formats
        :param print_format:
        """
        super().__init__(cell_index, header=header)
        if not date_formats:
            date_formats = [googlesheet_utils.GSHEET_DATE_FORMAT]
        self.date_formats = date_formats
        self.print_format = print_format or date_formats[0]

    def _get_typed_value(self, value: str) -> datetime | None:
        if not value:
            return None
        for date_format in self.date_formats:
            try:
                return googlesheet_utils.parse_date(value, date_format)
            except ValueError:
                continue
        raise ValueError(f"Cannot parse value {value} to date")

    def _get_write_format_value(self, value: datetime) -> str:
        """
        Return the cell's value as a str obj
        """
        return value.strftime(self.print_format) if value is not None else ""


class BooleanCell(AbstractCell[bool]):
    def __init__(self, cell_index: str, *, default_value: bool = False, true_value: str = "TRUE", header: str = ""):
        """
        :param true_value: value that parsed as True
        """
        super().__init__(cell_index, default_value=default_value, header=header)
        self._true_value = true_value

    def _get_typed_value(self, value: str) -> bool:
        return value == self._true_value


class MoneyCell(AbstractCell[Decimal]):
    def __init__(self, cell_index: str, *, header: str | None = None, currency: str = "$"):
        super().__init__(cell_index, header=header)
        self.currency = currency

    def _get_typed_value(self, value: str) -> Decimal | None:
        if value:
            return Decimal(value.replace(self.currency, "").strip())
        return None

    def _get_write_format_value(self, value: Decimal) -> str:
        value = super()._get_write_format_value(value)
        return self.currency + value if value else value


class ListCell(Generic[CELL_T], AbstractCell[list[CELL_T]]):
    """Represents values of the multiple cells"""

    def __init__(
        self,
        cell_index: str,
        end_cell_index: str,
        *,
        step: int = 1,
        headers: list[str] | None = None,
        type_parser: Callable[[str], CELL_T] | None = None,
    ):
        """
        :param cell_index: index of the starting value
        :param end_cell_index: index of the ending value (inclusive)
        :param step: iterate between the cells by the number of steps
        :param type_parser: callable for the value parsing
        """
        super().__init__(cell_index)
        self.end_cell_index = end_cell_index
        self.num_end_index = googlesheet_utils.col_to_num(end_cell_index)
        self.step = step
        self.type_parser = type_parser
        self._headers = headers
        self._cells_count = (self.num_end_index - self.num_index + 1) // self.step
        if headers is not None and self._cells_count != len(headers):
            raise ValueError(
                "Headers amount should match amount of columns: "
                f"expected {self._cells_count}, but {len(headers)} was given"
            )

    @staticmethod
    def _default_parser(value: str) -> str:
        return value

    def _get_typed_value(self, value: str) -> CELL_T:
        if self.type_parser:
            return self.type_parser(value)
        return self._default_parser(value)

    def write_header(self, headers: list[str], offset: int = 0) -> None:
        if not self._headers:
            return
        start_index = self._start_num_index(offset)
        end_index = self._end_num_index_exclusive(offset)
        if end_index > len(headers):
            raise ValueError(
                f"Target header row does not have columns to fit range [{self.cell_index}, {self.end_cell_index}]"
            )
        for index, header in zip(range(start_index, end_index, self.step), self._headers):
            headers[index] = header

    def _start_num_index(self, offset: int) -> int:
        return self.num_index - offset

    def _end_num_index_exclusive(self, offset: int) -> int:
        return self.num_end_index - offset + 1

    def read_value(self, data: list[str], offset: int = 0) -> list[CELL_T]:
        """
        Return the values of the cells
        """
        start_index = self._start_num_index(offset)
        end_index = self._end_num_index_exclusive(offset)
        row = data
        if end_index > len(data):
            row = row + [""] * (end_index - len(data))
        return [self._typed(row[i]) for i in range(start_index, end_index, self.step)]

    def write_value(self, value: list[CELL_T] | None, row: list[str], offset: int = 0) -> None:
        if not value:
            # don't need to refill row with empty values
            return
        if self._cells_count != len(value):
            raise ValueError(f"Values count missmatch: expected {self._cells_count} values, but {len(value)} was given")
        start_index = self._start_num_index(offset)
        end_index = self._end_num_index_exclusive(offset)
        if end_index > len(row):
            raise ValueError(
                f"Target row does not have columns to fit range [{self.cell_index}, {self.end_cell_index}]"
            )
        for index, val in zip(range(start_index, end_index, self.step), value):
            # TODO: add separate lambda for parsing to write format if needed
            row[index] = self._get_write_format_value(val)

    def _typed(self, raw_value: str | None) -> CELL_T | None:
        """
        Parse the value if the type_parser is provided
        """
        try:
            value = str(self._check(raw_value)) if raw_value is not None else None
            value = self._post_process(value)
        except (ValueError, TypeError):
            value = ""
        return self._get_typed_value(value)


class SourceSheetRow:
    """Contains raw sheet row data, sources and metadata"""

    def __init__(
        self,
        line_number: int,
        document: SheetModel,
        source: list[str] | dict | None = None,
        first_col_shift: int = 0,
    ):
        """
        :param line_number: index of the line
        :param document: SheetModel object
        :param source: the values of the row
        """
        self.errors = []
        self.source = source
        self.line_number = line_number
        self.document = document
        self.first_col_shift = first_col_shift

    @property
    def tab(self) -> str:
        """Return the name of the tab"""
        return self.document.sheet_name

    @property
    def sheet(self) -> str:
        """Return the name of the document"""
        return self.document.parent_doc.doc_code if self.document else ""

    def __str__(self) -> str:
        return f"SheetRowMeta: errors={self.errors}, line={self.line_number}, source=[{','.join(self.source)}]"


class SheetRowMeta(type):
    def __new__(mcs, name, bases, methods):
        cls_inst = type.__new__(mcs, name, bases, methods)
        cells = {cell.name: cell for base in bases for cell in getattr(base, "cells", [])}

        cells.update(
            {
                cell_name: cell_specs.__metadata__[0]
                for cell_name, cell_specs in inspect.get_annotations(cls_inst, eval_str=True).items()
                if hasattr(cell_specs, "__metadata__")
            }
        )

        for cell_name, cell in cells.items():
            cell.name = cell_name

        cls_inst.cells = tuple(cells.values())
        cls_inst.min_index = min((c.num_index for c in cls_inst.cells), default=0)
        cls_inst.max_index = max((c.num_end_index for c in cls_inst.cells), default=0)
        return cls_inst


class SheetRow(metaclass=SheetRowMeta):
    """Represents a googlesheet row"""

    cells: tuple[Cell]
    min_index: int
    max_index: int

    def __init__(self, sheet: SheetModel):
        self.sheet = sheet
        self.meta = None

    @classmethod
    def row_from_record(cls, raw_row: SourceSheetRow) -> SheetRow:
        row = cls(raw_row.document)
        row.meta = raw_row
        for cell in cls.cells:
            try:
                value = cell.read_value(raw_row.source, raw_row.first_col_shift)
            except Exception as exc:  # validation or extraction value exceptions
                value = cell.default_value
                row.meta.errors.append({"field": cell.name, "error": exc})
            setattr(row, cell.name, value)
        return row

    @classmethod
    def row_from_db(cls, values: dict, line_number: int, sheet: SheetModel | None = None) -> SheetRow:
        row = cls(sheet)
        row.meta = SourceSheetRow(line_number=line_number, source=values, document=sheet)
        for cell in cls.cells:
            setattr(row, cell.name, values.get(cell.name, cell.default_value))
        return row

    def to_list(self) -> list[str | None]:
        result: list[str] = [""] * (self.max_index - self.min_index + 1)
        for cell in self.cells:
            cell.write_value(getattr(self, cell.name), result, self.min_index)
        return result


class SheetHeader:
    def __init__(self, spec: typing.Type[SheetRow]):
        self.headers = [""] * (spec.max_index - spec.min_index + 1)
        for cell in spec.cells:
            cell.write_header(self.headers, spec.min_index)

    def format_headers(self, formatters: dict[str, Any]) -> SheetHeader:
        self.headers = [header.format(**formatters) for header in self.headers]
        return self


class GsheetDocModel:
    doc_code: ClassVar[str] = None


SheetRowT = TypeVar("SheetRowT", bound=SheetRow)


class SheetModelMeta(abc.ABCMeta):
    def __new__(mcs, name, bases, namespace, **kwargs):
        cls_inst = super().__new__(mcs, name, bases, namespace, **kwargs)
        cls_inst.row_spec = next(
            (b.__args__[0] for b in cls_inst.__orig_bases__ if isinstance(b.__args__[0], SheetRowMeta)), None
        )
        if cls_inst.row_spec:
            cls_inst.first_column = googlesheet_utils.num_to_col(cls_inst.row_spec.min_index)
            cls_inst.last_column = googlesheet_utils.num_to_col(cls_inst.row_spec.max_index)
        return cls_inst


class SheetModel(Generic[SheetRowT], metaclass=SheetModelMeta):
    """Base class for the Sheet model"""

    parent_doc: ClassVar[GsheetDocModel]
    first_row: ClassVar[int]
    last_row: ClassVar[int | None] = None
    sheet_name: str

    # automatically derived from actual row type
    first_column: ClassVar[str]
    last_column: ClassVar[str]

    def __init__(self):
        self.sheet_header = SheetHeader(self.row_spec)  # pylint: disable=no-member

    def read(self, spreadsheet_id: str, limit: int | None = None, is_formatted: bool = True) -> list[SheetRowT]:
        """
        Read the sheet

        :param spreadsheet_id: document ID
        :param limit: max count of lines to read
        :param is_formatted: if there are formatted cells
        """
        data_grid = gsheet_client.read(
            spreadsheet_id,
            sheet_name=self.sheet_name,
            data_range=DataRange(
                first_row=self.first_row,
                first_column=self.first_column,
                last_column=self.last_column,
                last_row=self._get_last_row(self.first_row, self.last_row, limit),
            ),
            is_formatted=is_formatted,
        )
        result = []
        first_col_shift = googlesheet_utils.col_to_num(self.first_column)
        for line, source_row in enumerate(data_grid):
            row = self._clean_row(source_row)
            if self._is_row_empty(row):
                continue
            raw_row = SourceSheetRow(
                line_number=line + self.first_row, document=self, source=row, first_col_shift=first_col_shift
            )
            result.append(self.row_spec.row_from_record(raw_row))  # pylint: disable=no-member

        return result

    def write(self, spreadsheet_id: str, data: list, fast_update: bool = True, update_rows_overlap=0):
        """
        Write to the sheet

        :param spreadsheet_id: document ID
        :param data: list of data to write in the sheet
        :param fast_update:
        :param update_rows_overlap:
        """
        if fast_update:
            last_row = len(data) + (update_rows_overlap or FAST_UPDATE_ROWS_OVERLAP)
        else:
            last_row = max(len(data) + self.first_row, self._get_row_count(spreadsheet_id))
        return gsheet_client.update(
            spreadsheet_id=spreadsheet_id,
            sheet_name=self.sheet_name,
            data=data,
            data_range=DataRange(
                first_column=self.first_column,
                last_column=self.last_column,
                first_row=self.first_row,
                last_row=last_row,
            ),
        )

    def _get_row_count(self, spreadsheet_id: str) -> int:
        """
        Return the number of rows in the sheet

        :param spreadsheet_id: document ID
        """
        data_grid = gsheet_client.read(
            spreadsheet_id=spreadsheet_id,
            sheet_name=self.sheet_name,
            data_range=DataRange(
                first_row=self.first_row,
                first_column=self.first_column,
                last_column=self.first_column,
            ),
        )
        return len(data_grid) + self.first_row

    def from_records(self, data: list[dict]) -> list[SheetRowT]:
        """
        Convert db response to sheet model

        :param data: list of data to convert
        """
        return [
            # pylint: disable=no-member
            self.row_spec.row_from_db(values=row, line_number=i + self.first_row, sheet=self)
            for i, row in enumerate(data)
        ]

    @staticmethod
    def _is_row_empty(row: list[str]) -> bool:
        return all(cell == "" for cell in row)

    @staticmethod
    def _clean_row(row: list[str]) -> list[str]:
        return [ILLEGAL_CHARACTERS_RE.sub("", cell).strip() if isinstance(cell, str) else cell for cell in row]

    @staticmethod
    def _get_last_row(first_row: int, last_row: int, limit: int) -> int:
        """
        Return the last row in the sheet

        :param first_row: index of the first row
        :param last_row: index of the last row
        :param limit: max number of rows
        """
        if limit:
            return first_row + limit - 1
        return last_row

    def create_sheet_if_not_exist(self, spreadsheet_id: str) -> bool:
        """
        Creating sheet if not exists

        :param spreadsheet_id: document ID
        :return: True if sheet has been created and False if this sheet already exist
        """
        actual_doc_details = gsheet_client.get_doc_details(spreadsheet_id)
        sheet = actual_doc_details.get_tab(self.sheet_name)
        if sheet is None:
            gsheet_client.create_sheet(spreadsheet_id, self.sheet_name)
            return True
        return False

    def populate_header(self, spreadsheet_id: str, first_row=1, first_column="A") -> None:
        """
        Write the headers in the sheet

        :param spreadsheet_id: document ID
        :param first_row: index of the first row
        :param first_column: index of the first column (char)
        """
        gsheet_client.update(
            spreadsheet_id=spreadsheet_id,
            sheet_name=self.sheet_name,
            data=(self.sheet_header.headers,),
            data_range=DataRange(first_column=first_column, last_column=self.last_column, first_row=first_row),
        )


#  TODO use notify_warning() instead log.warnings (GD-2040)
def write_to_sheet(model: SheetModel, data: list[dict], gsheet_id) -> None:
    """
    Function for writing to a google sheets
    :param model: Subtype SheetModel
    :param data: list of dictionaries that has the corresponding keys to the attributes
    :param gsheet_id: Google sheet's id
    """
    try:
        rows = [row.to_list() for row in model.from_records(data)]

        model.create_sheet_if_not_exist(gsheet_id)
        model.populate_header(gsheet_id)
        model.write(gsheet_id, rows, fast_update=False)
    except Exception as exc:
        logger.warning("An error occurred while writing data to gsheet - %s", exc)
        raise
