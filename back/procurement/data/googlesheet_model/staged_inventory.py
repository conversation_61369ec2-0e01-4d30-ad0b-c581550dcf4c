from typing import Annotated

from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, IntCell, SheetModel, SheetRow


class StagedInventory(GsheetDocModel):
    doc_code = "staged_inventory"


class StagedInventoryRow(SheetRow):
    site: Annotated[str, Cell("A")]
    week: Annotated[str, Cell("B")]
    sub_recipe_id: Annotated[str, Cell("F")]
    quantity: Annotated[int, IntCell("H")]


class StagedInventorySheet(SheetModel[StagedInventoryRow]):
    first_row = 2
    parent_doc = StagedInventory()
    sheet_name = "DATA"
