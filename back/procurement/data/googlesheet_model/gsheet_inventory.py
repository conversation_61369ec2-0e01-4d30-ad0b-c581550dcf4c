from datetime import datetime
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT, GSHEET_DATE_FORMAT
from procurement.data.googlesheet_model.core import Cell, DateCell, GsheetDocModel, IntCell, SheetModel, SheetRow


class PimtInventoryDocModel(GsheetDocModel):
    doc_code = "pimt"


class ImtInventoryDocModel(GsheetDocModel):
    doc_code = "imt_inventory"


class UpdateDateRow(SheetRow):
    update_date: Annotated[datetime, DateCell("B", GSHEET_DATE_FORMAT)]


class InventoryDateSheet(SheetModel[UpdateDateRow]):
    sheet_name = "Last_Update"
    first_row = 1
    last_row = 1

    parent_doc = PimtInventoryDocModel()


class PimtInventoryDateSheet(InventoryDateSheet):
    parent_doc = PimtInventoryDocModel()


class ImtInventoryDateSheet(InventoryDateSheet):
    parent_doc = ImtInventoryDocModel()


class InventoryRow(SheetRow):
    po_number: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("B")]
    cases: Annotated[int, IntCell("C")]
    cases_available: Annotated[int, IntCell("D")]
    lot: Annotated[str, Cell("E")]
    expiration_date: Annotated[datetime, DateCell("F", GSHEET_DATE_FORMAT, DATE_FORMAT)]


class CanadaInventoryRow(SheetRow):
    sku_code: Annotated[str, Cell("A")]
    supplier: Annotated[str, Cell("B")]
    cases: Annotated[int, IntCell("C")]
    case_size: Annotated[int, IntCell("D")]
    units: Annotated[int, IntCell("E")]
    lot: Annotated[str, Cell("F")]
    expiration_date: Annotated[datetime, DateCell("G", GSHEET_DATE_FORMAT, DATE_FORMAT)]


class FidelityInventoryRow(InventoryRow):
    bob_code: Annotated[str, Cell("G")]


class TopoSkuRow(SheetRow):
    dc: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("B")]


class InventoryDataModel(SheetModel[InventoryRow]):
    first_row = 2

    def __init__(self, wh_name):
        super().__init__()
        self.sheet_name = f"{wh_name}_FeedData"


class CanadaInventoryDataModel(SheetModel[CanadaInventoryRow]):
    first_row = 2

    def __init__(self, wh_name):
        super().__init__()
        self.sheet_name = f"{wh_name}_FeedData"


class FidelityInventoryDataModel(SheetModel[FidelityInventoryRow]):
    first_row = 2
    sheet_name = "Fidelity_FeedData"
    parent_doc = PimtInventoryDocModel()


class PimtInventoryDataModel(InventoryDataModel):
    parent_doc = PimtInventoryDocModel()


class ImtInventoryDataModel(InventoryDataModel):
    parent_doc = ImtInventoryDocModel()


class CanadaPimtInventoryDataModel(CanadaInventoryDataModel):
    parent_doc = PimtInventoryDocModel()


class TopoSkuDocModel(GsheetDocModel):
    doc_code = "topo_sku"


class TopoSkuDataModel(SheetModel[TopoSkuRow]):
    first_row = 2
    sheet_name = "IMT Feed"
    parent_doc = TopoSkuDocModel()


IMT_INVENTORY_MODELS = (ImtInventoryDataModel, ImtInventoryDateSheet)
PIMT_INVENTORY_MODELS = (PimtInventoryDataModel, PimtInventoryDateSheet)
CANADA_PIMT_INVENTORY_MODELS = (CanadaPimtInventoryDataModel, PimtInventoryDateSheet)
