from typing import Annotated

from procurement.data.googlesheet_model.core import BooleanCell, Cell, GsheetDocModel, SheetModel, SheetRow


class JolietOpeSkus(GsheetDocModel):
    doc_code = "joliet_ope_skus"


class JolietOpeSkuRow(SheetRow):
    should_include: Annotated[bool, BooleanCell("A")]
    sku_code: Annotated[str, Cell("B")]


class JolietOpeSkuSheet(SheetModel[JolietOpeSkuRow]):
    first_row = 3
    parent_doc = JolietOpeSkus()
    sheet_name = "JO SKU List"
