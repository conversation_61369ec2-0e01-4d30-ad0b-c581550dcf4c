from typing import Annotated

from procurement.data.googlesheet_model.core import Cell, DecimalCell, GsheetDocModel, IntCell, SheetModel, SheetRow


class ImtDataDump3PLDocModel(GsheetDocModel):
    doc_code = "imt_data_dumps_3pl"


class PullPutRow(SheetRow):
    entry_type: Annotated[str, Cell("A")]
    po_number: Annotated[str, Cell("B")]
    sku_name: Annotated[str, Cell("C")]
    quantity: Annotated[int, IntCell("D")]


class ProcurementInputs3PL(SheetModel[PullPutRow]):
    first_row = 2
    parent_doc = ImtDataDump3PLDocModel()

    def __init__(self, state):
        super().__init__()
        self.sheet_name = state + "_Procurement-Inputs"


class DiscardRow(SheetRow):
    timestamp: Annotated[str, Cell("A")]
    username: Annotated[str, Cell("B")]
    date_of_discard: Annotated[str, Cell("C")]
    time_of_discard: Annotated[str, Cell("D")]
    am_or_pm: Annotated[str, Cell("E")]
    number_of_units_discarded: Annotated[str, Cell("F")]
    sku: Annotated[str, Cell("G")]
    sku_name: Annotated[str, Cell("H")]
    week: Annotated[str, Cell("I")]
    dc: Annotated[str, Cell("J")]


class DiscardData3PL(SheetModel[DiscardRow]):
    sheet_name = ""
    first_row = 2

    def __init__(self, state):
        super().__init__()
        self.sheet_name = state + "_Discard"

    parent_doc = ImtDataDump3PLDocModel()


class ReceiveRow(SheetRow):
    receipt_tmst: Annotated[str, Cell("A")]
    date: Annotated[str, Cell("B")]
    user: Annotated[str, Cell("C")]
    supplier: Annotated[str, Cell("D")]
    po: Annotated[str, Cell("E")]
    truck_arrival_date: Annotated[str, Cell("F")]
    ingredient_name: Annotated[str, Cell("G")]
    sku_code: Annotated[str, Cell("H")]
    cases_received: Annotated[int, IntCell("L")]
    case_count_1_units: Annotated[int, IntCell("N")]
    cases_size_1: Annotated[int, DecimalCell("O")]
    case_count_2_units: Annotated[int, IntCell("P")]
    cases_size_2: Annotated[int, DecimalCell("Q")]
    case_count_3_units: Annotated[int, IntCell("R")]
    cases_size_3: Annotated[int, DecimalCell("S")]


class Receive3PL(SheetModel[ReceiveRow]):
    first_row = 2
    parent_doc = ImtDataDump3PLDocModel()

    def __init__(self, dc_name):
        super().__init__()
        self.sheet_name = dc_name.upper() + "_Receiving"


class CycleCount3plRow(SheetRow):
    sku_code: Annotated[str, Cell("A")]
    units: Annotated[int, IntCell("C")]
    date_of_count: Annotated[str, Cell("E")]
    production_boh: Annotated[str, Cell("G")]


class CycleCount3pl(SheetModel[CycleCount3plRow]):
    first_row = 2
    parent_doc = ImtDataDump3PLDocModel()

    def __init__(self, dc_name):
        super().__init__()
        self.sheet_name = dc_name.upper() + "_CycleCounts"
