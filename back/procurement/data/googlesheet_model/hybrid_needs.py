from decimal import Decimal
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import to_decimal
from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, ListCell, SheetModel, SheetRow, SheetRowT


class HybridNeedsDocModel(GsheetDocModel):
    doc_code = "hybrid_needs"


class HybridNeedsDocModel3PL(GsheetDocModel):
    doc_code = "hybrid_needs_3pl"


class HybridNeedsSheetModel(SheetModel[SheetRowT]):
    sheet_name_pattern = "Hybrid Needs Ingredients ({site})"
    parent_doc = HybridNeedsDocModel()

    def __init__(self, site):
        super().__init__()
        self.sheet_name = self.sheet_name_pattern.format(site=site)


class HybridNeeds3plSheetModel(HybridNeedsSheetModel[SheetRowT]):
    parent_doc = HybridNeedsDocModel3PL()


class HnRow(SheetRow):
    sku_code: Annotated[str, Cell("B")]
    days: Annotated[list[Decimal], ListCell("D", "L", type_parser=to_decimal)]


class HnStatusRow(SheetRow):
    daily_statuses: Annotated[list[str], ListCell("D", "L")]


class HnFactorStatusRow(SheetRow):
    daily_statuses: Annotated[list[str], ListCell("D", "O")]


class HnFactorRow(SheetRow):
    sku_code: Annotated[str, Cell("B")]
    days: Annotated[list[Decimal], ListCell("D", "O", type_parser=to_decimal)]


class HnShiftLevelRow(SheetRow):
    sku_code: Annotated[str, Cell("B")]
    sku_name: Annotated[str, Cell("C")]
    days_shifts: Annotated[list[Decimal], ListCell("D", "AB", type_parser=to_decimal)]


class HybridNeedsIngredients(HybridNeedsSheetModel[HnRow]):
    first_row = 7


class HybridNeedsIngredients3PL(HybridNeeds3plSheetModel[HnRow]):
    first_row = 5


class HybridNeedsIngredientsFactor(HybridNeedsSheetModel[HnFactorRow]):
    first_row = 7
    sheet_name_pattern = "Hybrid Needs Ingredients - {site}"


class HybridNeedsIngredientsFactorStatus(HybridNeedsSheetModel[HnFactorStatusRow]):
    first_row = last_row = 4
    sheet_name_pattern = "Hybrid Needs Ingredients - {site}"


class DeliveryDateNeeds(HybridNeedsSheetModel[HnFactorRow]):
    first_row = 7
    sheet_name_pattern = "Delivery Date Needs - {site}"


class HybridNeedsIngredientsStatus(HybridNeedsSheetModel[HnStatusRow]):
    first_row = last_row = 2


class HybridNeedsIngredients3plStatus(HybridNeeds3plSheetModel[HnStatusRow]):
    first_row = last_row = 1


class HybridNeedsIngredientsShiftLevel(SheetModel[HnShiftLevelRow]):
    first_row = 8

    parent_doc = HybridNeedsDocModel()

    def __init__(self, dc):
        super().__init__()
        self.sheet_name = f"Shift-Level Hybrid Needs Ingredients ({dc})"
