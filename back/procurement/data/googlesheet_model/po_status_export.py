from datetime import date
from decimal import Decimal
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT, DATETIME_FORMAT
from procurement.data.googlesheet_model.core import (
    Cell,
    DateCell,
    DecimalCell,
    GsheetDocModel,
    IntCell,
    SheetModel,
    SheetRow,
)


class PoStatusGsheetDocModel(GsheetDocModel):
    doc_code = "po_status_export"


class PoStatusExportRow(SheetRow):
    supplier: Annotated[str, Cell("A", header="Supplier")]
    po_sku: Annotated[str, Cell("B", header="Unique PO-SKU Combination")]
    po_num: Annotated[str, Cell("C", header="PO Number")]
    sku_name: Annotated[str, Cell("D", header="SKU Name")]
    sku: Annotated[str, Cell("E", header="SKU")]
    delivery_date: Annotated[date, DateCell("F", DATETIME_FORMAT, DATE_FORMAT, header="Scheduled Delivery Date")]
    status: Annotated[str, Cell("G", header="PO Status")]
    order_size: Annotated[int, IntCell("H", header="Order Size")]
    order_unit: Annotated[str, Cell("I", header="Order Unit")]
    case_size: Annotated[Decimal, DecimalCell("J", header="Case Size")]
    case_unit: Annotated[str, Cell("K", header="Case Unit")]
    ordered: Annotated[Decimal, DecimalCell("L", header="Quantity Ordered")]
    received: Annotated[Decimal, DecimalCell("M", header="Quantity Received")]
    cases_received: Annotated[int, IntCell("N", header="Cases Received")]
    emergency_reason: Annotated[str, Cell("O", header="Emergency Reason")]
    received_date: Annotated[date, DateCell("P", DATETIME_FORMAT, DATE_FORMAT, header="Date Received")]
    week: Annotated[str, Cell("Q", header="Week")]
    shipping_method: Annotated[str, Cell("R", header="Ship Method")]
    buyer: Annotated[str, Cell("S", header="Buyer")]


class PoStatusSheet(SheetModel[PoStatusExportRow]):
    first_row = 2
    parent_doc = PoStatusGsheetDocModel()

    def __init__(self, brand, site):
        super().__init__()
        self.sheet_name = f"{brand}_{site}"
