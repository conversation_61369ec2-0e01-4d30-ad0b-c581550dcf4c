from decimal import Decimal
from typing import Annotated

from procurement.data.googlesheet_model.core import (
    BooleanCell,
    Cell,
    DecimalCell,
    GsheetDocModel,
    IntCell,
    MoneyCell,
    SheetModel,
    SheetRow,
)


class BasePkgRow(SheetRow):
    site: Annotated[str, Cell("B")]
    recipe_code: Annotated[str, Cell("C")]
    meal_number: Annotated[str, Cell("D")]
    recipe_type: Annotated[str, Cell("E")]
    meal_name: Annotated[str, Cell("F")]
    sku_type: Annotated[str, Cell("H")]
    picks_2p: Annotated[int, IntCell("I")]
    picks_4p: Annotated[int, IntCell("J")]


class PkgRow(BasePkgRow):
    allergens: Annotated[str, Cell("L")]
    is_in_kitbox: Annotated[bool, BooleanCell("O", true_value="Yes")]
    sku_code: Annotated[str, Cell("Q")]
    purchasing_category: Annotated[str, Cell("R")]
    pack_size_amount: Annotated[Decimal, DecimalCell("S")]
    pack_size_unit: Annotated[str, Cell("T")]
    weight_amount: Annotated[Decimal, DecimalCell("U")]
    weight_unit: Annotated[str, Cell("V")]
    unit_price: Annotated[Decimal, MoneyCell("W")]
    storage_location: Annotated[str, Cell("AF")]


class PkgDocModel(GsheetDocModel):
    doc_code = "pkg"


class AllDCsOffering(SheetModel[PkgRow]):
    first_row = 8
    parent_doc = PkgDocModel()


class CoreAllDCsOffering(AllDCsOffering):
    sheet_name = "All DCs Offering"


class TplAllDCsOffering(AllDCsOffering):
    sheet_name = "All 3PLs Offering"


class GcAllDCsOffering(AllDCsOffering):
    sheet_name = "All DCs Offering IMT View"


class NewPkgRow(BasePkgRow):
    picks_6p: Annotated[int, IntCell("K")]
    allergens: Annotated[str, Cell("M")]
    is_in_kitbox: Annotated[bool, BooleanCell("P", true_value="Yes")]
    sku_code: Annotated[str, Cell("R")]
    purchasing_category: Annotated[str, Cell("S")]
    pack_size_amount: Annotated[Decimal, DecimalCell("T")]
    pack_size_unit: Annotated[str, Cell("U")]
    weight_amount: Annotated[Decimal, DecimalCell("V")]
    weight_unit: Annotated[str, Cell("W")]
    unit_price: Annotated[Decimal, MoneyCell("X")]
    storage_location: Annotated[str, Cell("AH")]


class NewPkgDocModel(GsheetDocModel):
    doc_code = "pkg_v2"


class NewPkgDocModelV3(GsheetDocModel):
    doc_code = "pkg_v3"


class NewAllDCsOffering(SheetModel[NewPkgRow]):
    first_row = 8
    parent_doc = NewPkgDocModel()


class NewCoreAllDCsOffering(NewAllDCsOffering):
    sheet_name = "All DCs Offering"


class NewTplAllDCsOffering(NewAllDCsOffering):
    sheet_name = "All 3PLs Offering"


class FactorPkgRow(SheetRow):
    # NOTE: For easier calculations, we've decided to map existing columns to other values:
    # For core brands (HF, GC, EP) columns meal number and meal name contain meal number and meal name
    # For Factor meal number contains recipe_code and meal name recipe name
    # For core brands column recipe_code contains real recipe code, while for FJ it contains sub recipe id

    site: Annotated[str, Cell("B")]
    meal_number: Annotated[str, Cell("C")]
    recipe_type: Annotated[str, Cell("E")]
    meal_name: Annotated[str, Cell("F")]
    sku_code: Annotated[str, Cell("H")]

    sku_type: Annotated[str, Cell("L")]
    allergens: Annotated[str, Cell("U")]
    is_in_kitbox: Annotated[bool, BooleanCell("N", true_value="Yes")]

    pack_size_amount: Annotated[Decimal, DecimalCell("P")]
    pack_size_unit: Annotated[str, Cell("Q")]
    weight_amount: Annotated[Decimal, DecimalCell("S")]
    weight_unit: Annotated[str, Cell("T")]
    allergens: Annotated[str, Cell("U")]
    unit_price: Annotated[Decimal, MoneyCell("V")]
    storage_location: Annotated[str, Cell("AB")]
    recipe_name: Annotated[str, Cell("AG")]
    recipe_code: Annotated[str, Cell("AH")]


class FactorAllDCsOffering(SheetModel[FactorPkgRow]):
    first_row = 2
    parent_doc = PkgDocModel()
    sheet_name = "All DCs Offering"


class NewPkg3pRow(BasePkgRow):
    picks_3p: Annotated[int, IntCell("J")]
    picks_4p: Annotated[int, IntCell("K")]
    allergens: Annotated[str, Cell("M")]
    is_in_kitbox: Annotated[bool, BooleanCell("Q", true_value="Yes")]
    sku_code: Annotated[str, Cell("S")]
    purchasing_category: Annotated[str, Cell("T")]
    pack_size_amount: Annotated[Decimal, DecimalCell("U")]
    pack_size_unit: Annotated[str, Cell("V")]
    weight_amount: Annotated[Decimal, DecimalCell("W")]
    weight_unit: Annotated[str, Cell("X")]
    unit_price: Annotated[Decimal, MoneyCell("Y")]
    storage_location: Annotated[str, Cell("AI")]


class AllDcsOfferingV3(SheetModel[NewPkg3pRow]):
    first_row = 8
    parent_doc = NewPkgDocModelV3()


class CoreAllDCsOfferingV3(AllDcsOfferingV3):
    sheet_name = "All DCs Offering"


class TplAllDCsOfferingV3(AllDcsOfferingV3):
    first_row = 8
    sheet_name = "All 3PLs Offering"
