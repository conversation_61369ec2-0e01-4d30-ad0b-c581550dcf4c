from typing import Annotated

from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, SheetModel, SheetRow


class AlternativeSkuRow(SheetRow):
    original_sku_code: Annotated[str, Cell("A")]
    interchangable_sku_code: Annotated[str, Cell("C")]


class AlternativeSkuDocModel(GsheetDocModel):
    doc_code = "alternative_sku_mapping"


class RwaSkuDocModel(GsheetDocModel):
    doc_code = "rwa_sku_mapping"


class AlternativeSkuSheetModel(SheetModel[AlternativeSkuRow]):
    first_row = 2
    parent_doc = AlternativeSkuDocModel()


class OrganicSkuSheetModel(AlternativeSkuSheetModel):
    sheet_name = "Mapping"


class PackagingSkuSheetModel(AlternativeSkuSheetModel):
    sheet_name = "PCK_mapping"


class RwaSkuSheetModel(SheetModel[AlternativeSkuRow]):
    first_row = 2
    parent_doc = RwaSkuDocModel()
    sheet_name = "Mapping"
