from typing import Annotated

from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, IntCell, SheetModel, SheetRow


class MasterReplenishmentDocModel(GsheetDocModel):
    doc_code = "pimt_master_replenishment"


class MasterReplenishmentRow(SheetRow):
    site: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("B")]
    status: Annotated[str, Cell("F")]
    market: Annotated[str, Cell("G")]
    replenishment_type: Annotated[str, Cell("H")]
    shelf_life: Annotated[int, IntCell("I")]
    max_supplier_lead_time: Annotated[int, IntCell("J")]
    min_order_qty: Annotated[str, Cell("K")]
    request_for_proposal: Annotated[str, Cell("L")]
    market_factor: Annotated[str, Cell("M")]
    replenishment_buyer: Annotated[str, Cell("N")]


class MasterReplenishmentSheet(SheetModel[MasterReplenishmentRow]):
    parent_doc = MasterReplenishmentDocModel()
    sheet_name = "Master_v2"
    first_row = 2
