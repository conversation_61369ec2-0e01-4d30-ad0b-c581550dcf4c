from typing import Annotated

from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, IntCell, SheetModel, SheetRow


class CycleCountGcDocModel(GsheetDocModel):
    doc_code = "cycle_count_gc"


class CycleCountGcRow(SheetRow):
    scm_week: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("C")]
    submitted_time: Annotated[str, Cell("D")]
    units: Annotated[int, IntCell("E")]


class CycleCountGc(SheetModel[CycleCountGcRow]):
    first_row = 2
    parent_doc = CycleCountGcDocModel()

    def __init__(self, dc_name):
        super().__init__()
        self.sheet_name = dc_name.upper() + "_CycleCounts"
