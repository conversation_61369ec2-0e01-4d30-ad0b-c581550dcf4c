from decimal import Decimal
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import to_decimal
from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, ListCell, SheetModel, SheetRow


class MockPlanCalculationDocModel(GsheetDocModel):
    doc_code = "mock_plan_calculation"


class MockPlanRow(SheetRow):
    site: Annotated[str, Cell("A")]
    brand: Annotated[str, Cell("B")]
    production_type: Annotated[str, Cell("C")]
    weight: list[Decimal]


class CoreMockPlanRow(MockPlanRow):
    weight: Annotated[list[Decimal], ListCell("D", "L", type_parser=to_decimal)]


class Compiled(SheetModel[CoreMockPlanRow]):
    parent_doc = MockPlanCalculationDocModel()
    sheet_name = "Compiled"
    first_row = 2


class FactorMockPlanRow(MockPlanRow):
    site: Annotated[str, Cell("A")]
    brand: Annotated[str, Cell("B")]
    weight: Annotated[list[Decimal], ListCell("D", "O", type_parser=to_decimal)]


class CompiledFactor(SheetModel[FactorMockPlanRow]):
    parent_doc = MockPlanCalculationDocModel()
    sheet_name = "FJ Compiled"
    first_row = 2
