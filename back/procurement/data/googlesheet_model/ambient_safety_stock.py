from decimal import Decimal
from typing import Annotated

from procurement.data.googlesheet_model.core import (
    BooleanCell,
    Cell,
    DecimalCell,
    GsheetDocModel,
    IntCell,
    SheetModel,
    SheetRow,
)


class AmbientSafetyStockDataModel(GsheetDocModel):
    doc_code = "ambient_safety_stock"


class SafetyCombinedReportRow(SheetRow):
    supplier: Annotated[str, Cell("B")]
    sku_code: Annotated[str, Cell("E")]
    units_on_hand: Annotated[int, IntCell("H")]
    inbound: Annotated[int, IntCell("I")]
    outbound: Annotated[int, IntCell("J")]
    pallets_on_hand: Annotated[int, IntCell("L")]
    is_hfo: Annotated[bool, BooleanCell("Q", true_value="Y")]
    region: Annotated[str, Cell("Y")]


class SafetyCombinedReport(SheetModel[SafetyCombinedReportRow]):
    parent_doc = AmbientSafetyStockDataModel()
    sheet_name = "Combined Reports"
    first_row = 3


class SafetySkuMappingImportRow(SheetRow):
    sku_type: Annotated[str, Cell("B")]
    bob_code: Annotated[str, Cell("C")]
    demand_pipeline: Annotated[str, Cell("E")]
    sku_code: Annotated[str, Cell("G")]
    units_per_truck_load: Annotated[int, IntCell("H")]


class SafetySkuMappingImport(SheetModel[SafetySkuMappingImportRow]):
    parent_doc = AmbientSafetyStockDataModel()
    sheet_name = "SKU Mapping Import"
    first_row = 2


class LongTermForecastImportRow(SheetRow):
    week: Annotated[str, Cell("A")]
    site: Annotated[str, Cell("B")]
    brand: Annotated[str, Cell("C")]
    forecast: Annotated[int, IntCell("F")]
    sku_type: Annotated[str, Cell("D")]
    demand_pipeline: Annotated[str, Cell("E")]


class LongTermForecastImport(SheetModel[LongTermForecastImportRow]):
    parent_doc = AmbientSafetyStockDataModel()
    sheet_name = "Long-Term Forecast Import"
    first_row = 2


class PlannedDepletionRow(SheetRow):
    week: Annotated[str, Cell("B")]
    quantity: Annotated[Decimal, DecimalCell("D")]
    demand_pipeline: Annotated[str, Cell("E")]
    sku_code: Annotated[str, Cell("F")]
    region: Annotated[str, Cell("G")]
    owner: Annotated[str, Cell("H")]


class PlannedDepletion(SheetModel[PlannedDepletionRow]):
    parent_doc = AmbientSafetyStockDataModel()
    sheet_name = "Planned Depletion Import"
    first_row = 2
