from decimal import Decimal
from typing import Annotated

from procurement.data.googlesheet_model.core import (
    Cell,
    DecimalCell,
    GsheetDocModel,
    IntCell,
    ListCell,
    MoneyCell,
    SheetModel,
    SheetRow,
    SheetRowT,
)


class PimtGsheetDocModel(GsheetDocModel):
    doc_code = "pimt-export"


class PimtExport(SheetModel[SheetRowT]):
    parent_doc = PimtGsheetDocModel()
    first_row = 2


class OpsMainDashboardRow(SheetRow):
    site: Annotated[str, Cell("A", header="Site (3PW Name)")]
    sku_code: Annotated[str, Cell("B", header="SKU")]
    sku_name: Annotated[str, Cell("C", header="SKU Name")]
    category: Annotated[str, Cell("D", header="Category")]
    commodity_group: Annotated[str, Cell("E", header="Commodity Group")]
    in_house: Annotated[Decimal, DecimalCell("F", header="In-House")]
    inbound: Annotated[Decimal, DecimalCell("G", header="Inbound")]
    outbound: Annotated[Decimal, DecimalCell("H", header="Outbound")]


class OpsMainDashboardSheet(PimtExport[OpsMainDashboardRow]):
    pass


class OpsMainDashboardProteinSheet(OpsMainDashboardSheet):
    sheet_name = "Ops Main Dashboard Protein"


class OpsMainDashboardGroceryDairySheet(OpsMainDashboardSheet):
    sheet_name = "Ops Main Dashboard G/D/P"


class StrategyMainDashboardRow(SheetRow):
    partner: Annotated[str, Cell("A", header="Site")]
    sku_name: Annotated[str, Cell("B", header="SKU Name")]
    sku_code: Annotated[str, Cell("C", header="SKU")]
    category: Annotated[str, Cell("D", header="Category")]
    supplier: Annotated[str, Cell("E", header="Supplier")]
    supplier_code: Annotated[str, Cell("F", header="Supplier Code")]
    receive_date: Annotated[str, Cell("G", header="Received Date")]
    lot: Annotated[str, Cell("H", header="Lot Code")]
    po_number: Annotated[str, Cell("I", header="PO #")]
    cases: Annotated[int, IntCell("J", header="Cases")]
    total_units: Annotated[Decimal, DecimalCell("K", header="Total Units")]
    case_size: Annotated[Decimal, DecimalCell("L", header="Units per Case")]
    case_price: Annotated[Decimal, MoneyCell("M", header="Case Price")]
    unit_price: Annotated[Decimal, MoneyCell("N", header="Unit Price")]
    expiration_date: Annotated[str, Cell("O", header="Expiration Date")]
    days_until_expiration: Annotated[int, IntCell("P", header="Days until Expiration")]
    source: Annotated[str, Cell("Q", header="Source of Expiration Date")]


class StrategyMainDashboardSheet(PimtExport[StrategyMainDashboardRow]):
    sheet_name = "Strategy Main Dashboard"


class CsacDataExportRow(SheetRow):
    sku_code: Annotated[str, Cell("A", header="SKU Number")]
    sku_name: Annotated[str, Cell("B", header="SKU Name")]
    site: Annotated[str, Cell("C", header="DC")]
    average_cost: Annotated[Decimal, DecimalCell("D", header="Avg Cost")]
    cold_storage_facility: Annotated[str, Cell("E", header="Cold Storage Facility")]


class CsacSheet(PimtExport[CsacDataExportRow]):
    sheet_name = "CSAC Data"


class ReplenishmentExportRow(SheetRow):
    region: Annotated[str, Cell("A", header="Region")]
    sku: Annotated[str, Cell("B", header="SKU")]
    sku_name: Annotated[str, Cell("C", header="SKU Name")]
    replenishment_type: Annotated[str, Cell("D", header="3PW Replenishment Type")]
    buyer: Annotated[str, Cell("E", header="Buyer")]
    replenishment_buyer: Annotated[str, Cell("F", header="Replenishment Buyer")]
    category: Annotated[str, Cell("G", header="Category")]
    subcategory: Annotated[str, Cell("H", header="Sub-Category")]
    brand: Annotated[str, Cell("I", header="Brand")]

    lead_time: Annotated[int, IntCell("J", header="Max PO Lead Time (Weeks)")]
    row_needs: Annotated[int, IntCell("K", header="{week_0} ROWN")]
    forecasts: Annotated[
        list[int],
        ListCell(
            cell_index="L",
            end_cell_index="U",
            headers=[
                "{week_0} Forecast",
                "{week_1} Forecast",
                "{week_2} Forecast",
                "{week_3} Forecast",
                "{week_4} Forecast",
                "{week_5} Forecast",
                "{week_6} Forecast",
                "{week_7} Forecast",
                "{week_8} Forecast",
                "{week_9} Forecast",
            ],
        ),
    ]
    total_forecast: Annotated[int, IntCell("V", header="10 Wk Forecast")]
    average_weekly_demand: Annotated[int, IntCell("W", header="Avg Weekly Demand")]
    total_core_inventory: Annotated[Decimal, DecimalCell("X", header="TOTAL DC On-Hand")]
    total_tpl_inventory: Annotated[Decimal, DecimalCell("Y", header="TOTAL 3PL On-Hand")]
    total_wh_inventory: Annotated[Decimal, DecimalCell("Z", header="TOTAL 3PW On-Hand")]
    total_inventory_wos: Annotated[Decimal, DecimalCell("AA", header="Inv WOS")]
    expire_soon_inventory: Annotated[int, DecimalCell("AB", header="Soon to Expire Inventory (<60 Days)")]
    expired_inventory: Annotated[Decimal, DecimalCell("AC", header="Expired Inventory")]
    inbounds: Annotated[
        list[Decimal],
        ListCell(
            cell_index="AD",
            end_cell_index="AM",
            headers=[
                "{week_0} On-Order",
                "{week_1} On-Order",
                "{week_2} On-Order",
                "{week_3} On-Order",
                "{week_4} On-Order",
                "{week_5} On-Order",
                "{week_6} On-Order",
                "{week_7} On-Order",
                "{week_8} On-Order",
                "{week_9} On-Order",
            ],
        ),
    ]
    total_inbound: Annotated[int, IntCell("AN", header="Total On Order")]
    total_undelivered: Annotated[int, IntCell("AO", header="Total Undelivered")]
    end_of_weeks: Annotated[
        list[Decimal],
        ListCell(
            cell_index="AP",
            end_cell_index="AY",
            headers=[
                "{week_0} EOW Net Inventory",
                "{week_1} EOW Net Inventory",
                "{week_2} EOW Net Inventory",
                "{week_3} EOW Net Inventory",
                "{week_4} EOW Net Inventory",
                "{week_5} EOW Net Inventory",
                "{week_6} EOW Net Inventory",
                "{week_7} EOW Net Inventory",
                "{week_8} EOW Net Inventory",
                "{week_9} EOW Net Inventory",
            ],
        ),
    ]
    end_of_weeks_wos: Annotated[
        list[Decimal],
        ListCell(
            cell_index="AZ",
            end_cell_index="BI",
            headers=[
                "{week_0} EOW WOS",
                "{week_1} EOW WOS",
                "{week_2} EOW WOS",
                "{week_3} EOW WOS",
                "{week_4} EOW WOS",
                "{week_5} EOW WOS",
                "{week_6} EOW WOS",
                "{week_7} EOW WOS",
                "{week_8} EOW WOS",
                "{week_9} EOW WOS",
            ],
        ),
    ]
    total_inventory_cost: Annotated[Decimal, DecimalCell("BJ", header="Total Inventory Cost")]
    dc_inventory_cost: Annotated[Decimal, DecimalCell("BK", header="DC Cost")]
    tpl_inventory_cost: Annotated[Decimal, DecimalCell("BL", header="3PL Cost")]
    tpw_inventory_cost: Annotated[Decimal, DecimalCell("BM", header="3PW Cost")]
    vendor_managed_inventory: Annotated[Decimal, DecimalCell("BN", header="Vendor Managed Inventory")]


class ReplenishmentSheet(PimtExport[ReplenishmentExportRow]):
    sheet_name = "Replenishment Export"


class ReplenishmentSheetTotal(PimtExport[ReplenishmentExportRow]):
    sheet_name = "Replenishment Export Total"
