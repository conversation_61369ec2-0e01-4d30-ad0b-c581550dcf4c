from decimal import Decimal
from typing import Annotated

from procurement.data.googlesheet_model.core import (
    Cell,
    DecimalCell,
    GsheetDocModel,
    IntCell,
    SheetModel,
    SheetRow,
    SheetRowT,
)


class ImtDailyExportDocModel(GsheetDocModel):
    doc_code = "imt-daily-export"


class IngredientDepletionRow(SheetRow):
    brand: Annotated[str, Cell("A", header="Brand")]
    site: Annotated[str, Cell("B", header="DC")]
    sku_code: Annotated[str, Cell("C", header="SKU")]
    sku_name: Annotated[str, Cell("D", header="SKU Name")]
    impacted_recipes: Annotated[str, Cell("I", header="Impacted Recipes")]
    plan: Annotated[int, IntCell("J", header="Plan")]
    forecast_oscar: Annotated[Decimal, DecimalCell("K", header="Forecast - OSCAR")]
    delta: Annotated[Decimal, DecimalCell("L", header="Delta")]
    supplement_need: Annotated[str, Cell("M", header="Supplement Need")]
    critical_delivery: Annotated[str, Cell("N", header="Critical Delivery")]
    units_needed: Annotated[Decimal, DecimalCell("O", header="Units Needed")]
    units_ordered: Annotated[Decimal, DecimalCell("P", header="Units Ordered")]
    units_received: Annotated[Decimal, DecimalCell("Q", header="Units Received")]
    units_in_house_hj: Annotated[Decimal, DecimalCell("R", header="Units in House (HJ)")]
    row_need: Annotated[int, IntCell("S", header="ROW Need")]
    units_in_house_minus_row_need: Annotated[Decimal, DecimalCell("T", header="Units in House - ROW Need")]
    next_week_forecast: Annotated[Decimal, DecimalCell("U", header="Next Week Forecast")]
    units_in_house_min_row_need_min_forecast: Annotated[
        Decimal, DecimalCell("V", header="Units in House - ROW Need - Next Week Forecast")
    ]
    units_scheduled_to_be_produced_by_autobagger: Annotated[
        int, IntCell("W", header="Units Scheduled to be Produced by Auto-Bagger")
    ]
    inventory: Annotated[Decimal, DecimalCell("X", header="Inventory")]
    discards: Annotated[Decimal, DecimalCell("Y", header="Discards")]
    pulls: Annotated[Decimal, DecimalCell("Z", header="Pulls")]
    total_on_hand: Annotated[Decimal, DecimalCell("AA", header="Total On Hand")]
    total_on_hand_minus_production_needs: Annotated[
        Decimal, DecimalCell("AB", header="Total On Hand - Production Need")
    ]
    in_progress_hj: Annotated[Decimal, DecimalCell("AC", header="In-Progress (HJ)")]
    awaiting_delivery: Annotated[Decimal, DecimalCell("AD", header="Awaiting Delivery")]
    not_delivered: Annotated[Decimal, DecimalCell("AE", header="Not Delivered")]
    buffer_quantity: Annotated[Decimal, DecimalCell("AF", header="Buffer Quantity (if all is delivered)")]
    buffer_percent: Annotated[Decimal, DecimalCell("AG", header="Buffer % (if all is delivered)")]


class ImtDailyExport(SheetModel[SheetRowT]):
    parent_doc = ImtDailyExportDocModel()
    first_row = 2


class CoreIngredientDepletionRow(IngredientDepletionRow):
    category: Annotated[str, Cell("E", header="Category")]
    commodity_group: Annotated[str, Cell("F", header="Commodity Group")]
    buyer: Annotated[str, Cell("G", header="Buyer")]
    unit_of_measure: Annotated[str, Cell("H", header="UOM")]
    week: Annotated[str, Cell("AH", header="SCM Week")]
    bulk_units_ordered: Annotated[Decimal, DecimalCell("AI", header="Bulk Units Ordered")]
    bulk_units_received: Annotated[Decimal, DecimalCell("AJ", header="Bulk Units Received")]
    bulk_delta: Annotated[Decimal, DecimalCell("AK", header="Bulk's Delta")]
    bulk_units_in_hj: Annotated[Decimal, DecimalCell("AL", header="Bulk Units in HJ")]


class UndeliveredExportRow(SheetRow):
    brand: Annotated[str, Cell("A", header="Brand")]
    site: Annotated[str, Cell("B", header="DC")]
    sku_code: Annotated[str, Cell("C", header="SKU")]
    buyer: Annotated[str, Cell("D", header="Buyer")]
    supplier: Annotated[str, Cell("E", header="Supplier")]
    po: Annotated[str, Cell("F", header="PO")]
    sku_name: Annotated[str, Cell("G", header="SKU Name")]
    category: Annotated[str, Cell("H", header="Category")]


class PackagingUndeliveredRow(UndeliveredExportRow):
    quantity_ordered: Annotated[Decimal, DecimalCell("I", header="Quantity Ordered")]
    scheduled_delivery_date: Annotated[str, Cell("J", header="Scheduled Delivery Date")]
    status: Annotated[str, Cell("K", header="Status")]
    week: Annotated[str, Cell("L", header="SCM Week")]
    appointment_time: Annotated[str, Cell("M", header="BluJay Appointment Time (Local Time)")]


class PackagingUndeliveredExport(ImtDailyExport[PackagingUndeliveredRow]):
    sheet_name = "PKG IMT App Undelivered"


class FactorUndeliveredRow(SheetRow):
    brand: Annotated[str, Cell("A", header="Brand")]
    site: Annotated[str, Cell("B", header="DC")]
    sku_code: Annotated[str, Cell("C", header="SKU")]
    purchasing_uom: Annotated[str, Cell("D", header="Purchasing UOM")]
    buyer: Annotated[str, Cell("E", header="Buyer")]
    supplier: Annotated[str, Cell("F", header="Supplier")]
    po: Annotated[str, Cell("G", header="PO")]
    sku_name: Annotated[str, Cell("H", header="SKU Name")]
    scheduled_delivery_date: Annotated[str, Cell("I", header="Scheduled Delivery Date")]
    status: Annotated[str, Cell("J", header="Status")]


class FactorUndeliveredExport(ImtDailyExport[FactorUndeliveredRow]):
    sheet_name = "F_ IMT App Undelivereds"


class PackagingDepletionRow(SheetRow):
    brand: Annotated[str, Cell("A", header="Brand")]
    site: Annotated[str, Cell("B", header="DC")]
    sku_name: Annotated[str, Cell("C", header="SKU Name")]
    sku_code: Annotated[str, Cell("D", header="Full SKU")]
    buyer: Annotated[str, Cell("E", header="Buyer")]
    commodity_group: Annotated[str, Cell("F", header="Commodity Group")]
    beginning_on_hand_today: Annotated[int, IntCell("G", header="Beginning on Hand Today")]
    demand_today: Annotated[int, IntCell("H", header="Demand Today")]
    incoming_pos_today: Annotated[int, IntCell("I", header="Incoming POs Today")]
    beginning_on_hand_tomorrow: Annotated[int, IntCell("J", header="Beginning On Hand Tomorrow")]
    demand_tomorrow: Annotated[int, IntCell("K", header="Demand Tomorrow")]
    units_short: Annotated[int, IntCell("L", header="Units Short")]
    critical_deliveries: Annotated[int, IntCell("M", header="Critical Deliveries")]


class PackagingDepletionExportSheet(ImtDailyExport[PackagingDepletionRow]):
    sheet_name = "PKG AM Queried IMT App"


class WeeklyLinerGuidanceRow(SheetRow):
    brand: Annotated[str, Cell("A", header="Brand")]
    site: Annotated[str, Cell("B", header="DC")]
    commodity_group: Annotated[str, Cell("C", header="Commodity Group")]
    sku_name: Annotated[str, Cell("D", header="SKU Name")]
    sku_code: Annotated[str, Cell("E", header="Full SKU")]
    buyer: Annotated[str, Cell("F", header="Buyer")]
    eow_inventory: Annotated[int, IntCell("G", header="EOW Inventory")]
    overconsumption: Annotated[int, IntCell("H", header="Overconsumption")]


class WeeklyLinerGuidance(ImtDailyExport[WeeklyLinerGuidanceRow]):
    sheet_name = "PKG EOW Queried IMT App"


class NetworkDeplExportRow(CoreIngredientDepletionRow):
    is_consolidated: Annotated[bool, Cell("AM", header="Consolidated")]


class FactorNetworkDeplSheet(ImtDailyExport[NetworkDeplExportRow]):
    pass


class FactorAmNetworkQueried(FactorNetworkDeplSheet):
    sheet_name = "F_ AM Queried NDM"


class FactorPmNetworkQueried(FactorNetworkDeplSheet):
    sheet_name = "F_ PM Queried NDM"
