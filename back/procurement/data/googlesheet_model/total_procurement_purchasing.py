from typing import Annotated

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.request_utils import context
from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, SheetModel, SheetRow


class TotalProcurementPurchasing(GsheetDocModel):
    doc_code = "commodity_group"


class TppRow(SheetRow):
    sku_code: Annotated[str, Cell("B")]
    purchasing_category: Annotated[str, Cell("C")]
    commodity_group: Annotated[str, Cell("D")]
    email: Annotated[str, Cell("F")]
    dc: Annotated[str, Cell("G")]
    brand: Annotated[str, Cell("H")]


class AllSkus(SheetModel[TppRow]):
    first_row = 2
    parent_doc = TotalProcurementPurchasing()

    def __init__(self):
        self.sheet_name = "All SKUs v4" if context.get_request_context().market == MARKET_US else "Final Assignments"
        super().__init__()
