from decimal import Decimal
from typing import Annotated

from .core import Cell, DecimalCell, GsheetDocModel, IntCell, SheetModel, SheetRow


class OscarGsheetRow(SheetRow):
    site: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("D")]
    forecast: Annotated[int, IntCell("F", nullable=True, round_up=True)]
    scm_week: Annotated[str, Cell("H")]


class GilStaticForecastDocModel(GsheetDocModel):
    doc_code = "gil_two"


class MostRecentForecast(SheetModel[OscarGsheetRow]):
    first_row = 2
    parent_doc = GilStaticForecastDocModel()

    def __init__(self, brand):
        super().__init__()
        self.sheet_name = f"{brand} Most Recent Forecast/Wk"


class HfMostRecentForecast(MostRecentForecast):
    first_row = 4


class GreenChefOscarForecastDocModel(GsheetDocModel):
    doc_code = "gc_oscar"


class GreenChefOscarRow(SheetRow):
    site: Annotated[str, Cell("A")]
    sku_code: Annotated[str, Cell("F")]
    units: Annotated[str, Cell("H")]
    forecast: Annotated[Decimal, DecimalCell("I")]
    scm_week: Annotated[str, Cell("K")]
    pack_day: Annotated[str, Cell("L")]


class GreenChefMostRecentForecast(SheetModel[GreenChefOscarRow]):
    sheet_name = "[PURCHASE] Most Recent Forecast/Wk"
    first_row = 2
    parent_doc = GreenChefOscarForecastDocModel()
