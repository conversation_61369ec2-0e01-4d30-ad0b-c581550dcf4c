import functools
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import to_int
from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, ListCell, SheetModel, SheetRow


class InWeekPackagingDemand(GsheetDocModel):
    doc_code = "packaging_demand"


class PackagingDemandRow(SheetRow):
    full_brand: Annotated[str, Cell("B")]
    site: Annotated[str, Cell("C")]
    sku_code: Annotated[str, Cell("H")]
    size: Annotated[str, Cell("E")]
    sku_type: Annotated[str, Cell("D")]
    demand_profile: Annotated[str, Cell("I")]
    demand_by_day: Annotated[list[int], ListCell("K", "R", type_parser=functools.partial(to_int, default_value=0))]


class PackagingSkuDemand(SheetModel[PackagingDemandRow]):
    parent_doc = InWeekPackagingDemand()
    sheet_name = "In-Week Demand by SKU"
    first_row = 4
