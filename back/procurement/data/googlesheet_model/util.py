import logging
from collections.abc import Iterable

from procurement.data.googlesheet_model.core import SheetModel, SheetRow, SourceSheetRow

logger = logging.getLogger(__name__)


def build_row_errors(data: Iterable[SheetRow], message=None, **kwargs) -> list[dict]:
    errors = []
    for item in filter(lambda x: x.meta.errors, data):
        for exc in item.meta.errors:
            error = build_sheet_error(item, exc, message, **kwargs)
            errors.append(error)
    return errors


def build_sheet_error(row: SheetRow | None, error, message=None, model: SheetModel = None, **kwargs):
    meta = row.meta if row else SourceSheetRow(line_number=(model.first_row if model else None), document=model)
    source_string = ",".join(map(str, meta.source)) if meta.source else None
    log_message = f"Error={error}, line number={meta.line_number}, value={source_string}, sheet={meta.sheet}-{meta.tab}"
    if kwargs:
        log_message = f"{log_message}, kwargs={kwargs}"

    logger.warning(log_message)
    warning = {
        "message": message,
        "error": str(error),
        "line": meta.line_number,
        "value": source_string,
        "sheet": meta.sheet,
        "tab": meta.tab,
    }
    if kwargs:
        warning.update(kwargs)
    return warning
