import functools
from typing import Annotated

from procurement.client.googlesheets.googlesheet_utils import to_int
from procurement.data.googlesheet_model.core import Cell, GsheetDocModel, ListCell, SheetModel, SheetRow


class ConsolidatedPackagingDemandModel(GsheetDocModel):
    doc_code = "consolidated_packaging_demand"


class InWeekConsolidatedDemandRow(SheetRow):
    brand: Annotated[str, Cell("A")]
    site: Annotated[str, Cell("B")]
    sku_code: Annotated[str, Cell("D")]
    size: Annotated[str, Cell("F")]
    demand_by_day_w1: Annotated[list[int], ListCell("G", "M", type_parser=functools.partial(to_int, default_value=0))]
    demand_by_day_w2: Annotated[list[int], ListCell("N", "T", type_parser=functools.partial(to_int, default_value=0))]


class InWeekConsolidatedDemand(SheetModel[InWeekConsolidatedDemandRow]):
    parent_doc = ConsolidatedPackagingDemandModel()
    sheet_name = "In Week Consolidated Demand"
    first_row = 3


class WeeklyConsolidatedDemandRow(SheetRow):
    brand: Annotated[str, Cell("A")]
    site: Annotated[str, Cell("B")]
    sku_code: Annotated[str, Cell("D")]
    forecasts_by_week: Annotated[list[int], ListCell("G", "P", type_parser=functools.partial(to_int, default_value=0))]


class WeeklyConsolidatedDemand(SheetModel[WeeklyConsolidatedDemandRow]):
    parent_doc = ConsolidatedPackagingDemandModel()
    sheet_name = "Weekly Consolidated Demand"
    first_row = 3


class ConsolidatedDemandWeekDataRow(SheetRow):
    week: Annotated[str, Cell("G")]


class ConsolidatedDemandWeekData(SheetModel[ConsolidatedDemandWeekDataRow]):
    sheet_name = "In Week Consolidated Demand"
    parent_doc = ConsolidatedPackagingDemandModel()
    first_row = 1
    last_row = 1
