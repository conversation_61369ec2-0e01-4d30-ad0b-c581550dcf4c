import base64
import functools
import logging
import uuid
from datetime import datetime

import flask
import msal
import requests
from flask import Blueprint, jsonify, make_response, redirect, request
from flask_jwt_extended import create_access_token, create_refresh_token, get_jwt_identity, jwt_required
from requests import ReadTimeout

from procurement.application_config import flask_config
from procurement.core.cache_utils.factory import CACHES
from procurement.core.config_utils import ENV, EnvType
from procurement.core.exceptions.api_errors import UsersError
from procurement.data.dto.procurement.user import User
from procurement.data.models.social.user import UserModel
from procurement.managers.social import users

from .common import add_url_params, jwt

logger = logging.getLogger(__name__)

USER_PIC_TIMEOUT = 10

auth_bp = Blueprint("oauth_auth", __name__)


@functools.cache
def _get_msal_app() -> msal.ConfidentialClientApplication:
    return msal.ConfidentialClientApplication(
        client_id=flask_config.CLIENT_ID, authority=flask_config.AUTHORITY, client_credential=flask_config.CLIENT_SECRET
    )


def handle_invalid_usage(error: UsersError) -> flask.Response:
    response = jsonify(error.to_dict())
    response.status_code = error.status_code
    return response


def login():
    if "url" not in request.args:
        return jsonify({"msg": "Invalid parameter. 'url' must be present."}), 400
    logger.info("[Login] Called Login ")
    authorization_url = create_auth_flow(request.args.get("url"))
    logger.debug("[Login] redirect url returned %s", authorization_url)
    return redirect(authorization_url, 302)


def create_auth_flow(client_url: str) -> str:
    state = str(uuid.uuid4())
    msal_app = _get_msal_app()
    auth_code = msal_app.initiate_auth_code_flow(
        flask_config.SCOPE, state=state, redirect_uri=flask_config.AUTH_CALLBACK_URI
    )
    CACHES.auth_states.put(state, (auth_code, client_url), flask_config.AUTH_STATE_TIMEOUT_SECONDS)
    return auth_code["auth_uri"]


def login_with_test_user():
    return jsonify(access_token=create_access_token(identity=flask_config.USER_DEV_CREDS)), 200


def logout():
    logger.info("[Logout] Called Logout ")
    try:
        user_id = get_jwt_identity()
        if "uuid" in user_id:
            del CACHES.active_auth_tokens[user_id["uuid"]]
    except Exception:
        logger.warning("Attempt to logout without or with broken JWT token", exc_info=True)

    return jsonify({"url": flask_config.AUTHORITY + "/oauth2/v2.0/logout"})


def authorize():
    logger.debug("[Authorize] Called Authorize ")
    state = request.args.get("state")
    if not state:
        return jsonify({"msg": "'state' parameter must be present. Please use '/login' resource."}), 400
    auth_code, client_url = CACHES.auth_states.get_and_delete(state) or ({}, "")  # state can be used only once

    if not client_url:
        return jsonify({"msg": "User state has been expired."}), 400

    # Authentication/Authorization failure
    if "error" in request.args:
        logger.error("Authentication/Authorization failed %s", request.args.get("error"))
        client_url = add_url_params(client_url, {"auth_error": str(request.args)})
        return make_response(redirect(client_url))

    # Auth processing
    azure_resp_dict = _get_msal_app().acquire_token_by_auth_code_flow(auth_code, dict(request.args))

    if "error" in azure_resp_dict:
        logger.error(
            "Authentication/Authorization processing failed with Error: %s Error Code: %s. Error Description: %s",
            azure_resp_dict["error"],
            azure_resp_dict.get("error_codes"),
            azure_resp_dict.get("error_description"),
        )
        client_url = add_url_params(client_url, {"auth_error": str(azure_resp_dict)})
        return make_response(redirect(client_url))

    id_token_claims: dict = azure_resp_dict.get("id_token_claims")

    logger.debug("IDP response --> %s", id_token_claims)

    picture = _get_picture(
        azure_resp_dict["token_type"], azure_resp_dict["access_token"], id_token_claims["preferred_username"]
    )

    user = _resolve_user(id_token_claims, picture)

    user_identity = {"id": user.id, "email": user.email, "uuid": state}
    logger.debug("Composed user --> %s", user_identity)

    access_token = create_access_token(identity=user_identity)
    refresh_token = create_refresh_token(identity={"uuid": state, "id": user.id})
    CACHES.active_auth_tokens.put(state, user.email, flask_config.JWT_REFRESH_TOKEN_EXPIRES)

    client_url = add_url_params(client_url, {"at": access_token, "rt": refresh_token})
    return make_response(redirect(client_url))


# We are using the `refresh=True` options in jwt_required to only allow
# refresh tokens to access this route.
@jwt_required(refresh=True)  # This method handle incorrect or expired token
def refresh():
    user_jwt_id = get_jwt_identity()
    user_id = user_jwt_id.get("id")
    user = users.get_user_info_by_id(user_id)
    if not user:
        logger.warning(
            "Suspicious behavior. Request for refresh token for user_id %s which is not exist in a system.", user_jwt_id
        )
        return jsonify({"message": "Invalid refresh token."}), 401
    old_state = user_jwt_id["uuid"]
    exp = CACHES.active_auth_tokens.get_ttl(old_state)
    if exp <= 0:
        return jsonify({"message": "Refresh token is not found or expired."}), 404
    new_state = str(uuid.uuid4())
    del CACHES.active_auth_tokens[old_state]
    # using previous expiration time to make sure that user re-logins
    # at least once per JWT_REFRESH_TOKEN_EXPIRES (default is 30 days)
    CACHES.active_auth_tokens.put(new_state, user.email, exp)

    return jsonify(
        access_token=create_access_token(identity={"uuid": new_state, "id": user_id, "email": user.email}),
        refresh_token=create_refresh_token(identity={"uuid": new_state, "id": user_id}),
    )


def _resolve_user(id_token_claims: dict, picture: str) -> User:
    email = id_token_claims.get("email", id_token_claims.get("preferred_username"))
    name = id_token_claims.get("name", "")
    name_arr = name.split(" ")
    first_name_concr, last_name_concr = (name_arr[0], name_arr[1]) if len(name_arr) == 2 else (name, None)

    user_data = {
        UserModel.email: email,
        UserModel.forced_out: False,
        UserModel.first_name: id_token_claims.get("given_name", first_name_concr),
        UserModel.last_name: id_token_claims.get("family_name", last_name_concr),
        UserModel.last_login: datetime.now(),
        UserModel.picture: picture,
    }
    users.upsert_user(user_data)

    user = users.get_by_mail(email)
    users.update_user_roles(user, id_token_claims.get("roles", []))
    return user


@jwt.token_in_blocklist_loader
def check_if_token_is_revoked(jwt_header: dict, jwt_payload: dict) -> bool:  # pylint: disable=unused-argument
    sub = jwt_payload["sub"]
    if "uuid" not in sub:
        return True  # Old type of token, without uuid
    # For readability, if user in the cache - the token is not revoked
    if sub["uuid"] in CACHES.active_auth_tokens:
        return False
    return True


def _get_picture(token_type: str, access_token: str, username: str) -> bytes | None:
    headers = {"Authorization": f"{token_type} {access_token}"}
    try:
        picture = requests.get(
            "https://graph.microsoft.com/v1.0/me/photos/48x48/$value", headers=headers, timeout=USER_PIC_TIMEOUT
        )
        if picture.status_code == 200:
            return base64.encodebytes(picture.content)
    except ReadTimeout:
        pass
    logger.debug("The photo cannot be read for user: %s", username)
    return None


def register_oauth_endpoints() -> Blueprint:
    auth_bp.register_error_handler(UsersError, handle_invalid_usage)
    auth_bp.add_url_rule("login/", view_func=login)
    auth_bp.add_url_rule("logout/", view_func=logout)
    auth_bp.add_url_rule("authorize/", view_func=authorize)
    auth_bp.add_url_rule("refresh/", view_func=refresh, methods=["POST"])

    if ENV == EnvType.STAGING:
        auth_bp.add_url_rule("login_test_user/", view_func=login_with_test_user)

    return auth_bp
