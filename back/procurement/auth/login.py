import logging

from flask import Blueprint

from procurement.core.config_utils import config

from . import common, oauth, simple

logger = logging.getLogger(__name__)


def create_auth() -> Blueprint:
    if config["oauth"]["enabled"]:
        auth_bp = oauth.register_oauth_endpoints()
    else:
        auth_bp = simple.register_simple_endpoints()

    common.register_common_routes(auth_bp)
    return auth_bp
