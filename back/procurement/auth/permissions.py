from enum import Enum, StrEnum


class PermissionSpec:
    def __init__(self, name: str, read_only: bool = False):
        self.name = name
        self.read_only = read_only


class Permissions(Enum):
    GENERAL = PermissionSpec("general")

    # IMT gsheet admin
    IMT_GHEET_V1 = PermissionSpec("imt-gsheet-v1")

    # IMT Region admin
    IMT_REGION_V1 = PermissionSpec("imt-region-v1")

    # IMT Bulk SKUs
    IMT_BULK_SKU_V1 = PermissionSpec("imt-bulk-sku-v1")

    # IMT Remake Tool
    IMT_REMAKE_TOOL_V1 = PermissionSpec("imt-remake-tool-v1")

    # IMT Dashboards
    IMT_ING_DEPL_V1 = PermissionSpec("imt-ing-depl-v1", read_only=True)
    IMT_PO_STATUS_V1 = PermissionSpec("imt-po-status-v1", read_only=True)
    IMT_PROD_NEED_ING_V1 = PermissionSpec("imt-prod-need-ing-v1", read_only=True)
    IMT_PCK_V1 = PermissionSpec("imt-pkg-v1", read_only=True)
    IMT_BUYER_V1 = PermissionSpec("imt-buyer-v1", read_only=True)
    IMT_ING_DEPL_V2 = PermissionSpec("imt-ing-depl-v2", read_only=True)
    IMT_PCK_DEPL_V2 = PermissionSpec("imt-pck-depl-v2")

    FORECAST_PUSH_DATA = PermissionSpec("forecast-push-data", read_only=True)

    # IMT Analytics
    IMT_ANALYTICS_TOP_VARIANCES_V1 = PermissionSpec("imt-analytics-top-variants-v1")

    # IMT Inventory Pull/Put form
    IMT_PULL_PUT_V1 = PermissionSpec("imt-pull-put-v1")

    # IMT PO Void form
    IMT_PO_VOID_V1 = PermissionSpec("imt-po-void-v1")

    # IMT Receipt Override form
    IMT_RECEIPT_OVERRIDE_V1 = PermissionSpec("imt-receipt-override-v1")

    # IMT Discard form
    IMT_DISCARD_V1 = PermissionSpec("imt-discard-v1")

    # IMT Weekend coverage checklist form
    IMT_WCC_V1 = PermissionSpec("imt-wcc-v1")

    # IMT Comment log
    IMT_COMMENT_LOG_V2 = PermissionSpec("imt-comment-log-v2", read_only=True)
    IMT_COMMENT_V2 = PermissionSpec("imt-comment-v2")
    PIMT_COMMENT_V2 = PermissionSpec("pimt-comment-v2")

    # ICS Tickets
    ICS_TICKETS_V1 = PermissionSpec("ics-tickets-v1", read_only=True)

    # PIMT dashboards
    PIMT_AIT_V1 = PermissionSpec("pimt-ait-v1", read_only=True)
    PIMT_BUYING_TOOL_V1 = PermissionSpec("pimt-buying-tool-v1", read_only=True)
    PIMT_OPS_DASH_V1 = PermissionSpec("pimt-ops-dash-v1", read_only=True)
    PIMT_REPLENISHMENT_V1 = PermissionSpec("pimt-replenishment-v1")
    PIMT_PACKAGING_SAFETY_STOCK = PermissionSpec("pimt-packaging-safety-stock", read_only=True)

    # PIMT partners admin
    PIMT_PARTNERS_V1 = PermissionSpec("pimt-partners-v1")

    # PIMT po-status
    PIMT_PO_STATUS_V1 = PermissionSpec("pimt-po-status-v1")

    # IMT Release Log
    IMT_RELEASE_LOG = PermissionSpec("imt-release-log")

    # Admin Utils
    IMT_CACHE_V1 = PermissionSpec("imt-cache-v1")
    IMT_METRICS_V1 = PermissionSpec("imt-metrics-v1")

    # Imt Sync
    IMT_SYNC_V1 = PermissionSpec("imt-sync-v1")

    # IMT App - Daily Exceptions Export
    IMT_DAILY_EXPORT_V1 = PermissionSpec("imt-daily-export-v1")

    # PO sync
    IMT_PO_SYNC_V1 = PermissionSpec("imt-po-sync-v1")

    # PIMT Sync
    PIMT_SYNC_V1 = PermissionSpec("pimt-sync-v1")

    # PIMT Export
    PIMT_EXPORT_V1 = PermissionSpec("pimt-export-v1")

    # PIMT Daily Exception Report
    PIMT_DAILY_REPORT_V1 = PermissionSpec("pimt-daily-report-v1")

    # PIMT Monthly Financial Report
    PIMT_MONTHLY_FINANCIAL_REPORT_V1 = PermissionSpec("pimt-monthly-financial-report-v1")

    # PIMT Metrics
    PIMT_EXCEPTION_METRICS_V1 = PermissionSpec("pimt-exception-metrics-v1", read_only=True)

    # Forecast Sync
    IMT_FORECAST_UPLOAD_SYNC_V1 = PermissionSpec("imt-forecast-upload-sync-v1")

    # Jobs Dashboard
    IMT_JOBS_DASHBOARD_V1 = PermissionSpec("imt-jobs-v1")

    # Job items on Jobs page
    PIMT_SYNC_JOB_V1 = PermissionSpec("pimt-sync-job-v1")
    IMT_PO_SYNC_JOB_V1 = PermissionSpec("imt-po-sync-job-v1")
    IMT_SYNC_JOB_V1 = PermissionSpec("imt-sync-job-v1")
    FUTURE_PKG_SYNC_JOB_V1 = PermissionSpec("future-pkg-sync-job-v1")
    CLEANUP_JOB_V1 = PermissionSpec("cleanup-job-v1")
    HJ_INV_SYNC_JOB_V1 = PermissionSpec("hj-inv-sync-job-v1")
    HJ_PCK_SNAPSHOT_JOB = PermissionSpec("hj-pck-snapshot-job")
    IMT_FORECASTS_UPLOAD_SYNC_JOB_V1 = PermissionSpec("imt-forecast-upload-sync-job-v1")
    MANUAL_FORMS_EXPORT_TO_S3_V1 = PermissionSpec("manual-forms-export-to-s3-v1")
    FINANCIAL_EXPORT_EMAIL = PermissionSpec("financial-export-email-v1")
    SNAPSHOT_START_OF_DAY_INVENTORY = PermissionSpec("snapshot-start-of-day-inv")

    # Modify old forms
    IMT_MODIFY_OLD_FORM_V1 = PermissionSpec("imt-modify-old-form-v1")

    # DC Inventory
    DC_INVENTORY_INVENTORY_MODULE_V1 = PermissionSpec("dc-inventory-inventory-module-v1")
    DC_INVENTORY_NETWORK_DEPLETION_V1 = PermissionSpec("dc-inventory-network-depletion-v1")
    DC_INVENTORY_UNIFIED_INVENTORY_V1 = PermissionSpec("dc-inventory-unified-inventory-v1", read_only=True)

    def __init__(self, permission_spec: PermissionSpec):
        self.permission_name = permission_spec.name
        self.read_only = permission_spec.read_only

    @property
    def read(self) -> str:
        return self.permission_name + ":r"

    @property
    def write(self) -> str:
        if self.read_only:
            raise ValueError(f"Permissions {self.permission_name} is read only")
        return self.permission_name + ":w"

    @property
    def is_read_only(self) -> bool:
        return self.read_only

    @staticmethod
    def get_all_permissions() -> set[str]:
        permissions = set()
        for permission in Permissions.__members__.values():
            permissions.add(permission.read)
            if not permission.is_read_only:
                permissions.add(permission.write)
        return permissions


class AvailableUserRoles(StrEnum):
    ADMIN = "admin"
    PROCUREMENT_MANAGER = "procurementleadership"
    PROCUREMENT_USER = "procurementmanager"
    BUYER = "procurementuser"
    DC_USER = "dcuser"
    DC_UNIVERSAL_USER = "dcuniversaluser"
    DC_MANAGEMENT_USER = "dcmanagementuser"
    HELLO_CONNECT_USER = "helloconnect"


DC_USER_PERMISSIONS = (
    Permissions.GENERAL,
    Permissions.IMT_ING_DEPL_V1,
    Permissions.IMT_PO_STATUS_V1,
    Permissions.IMT_PROD_NEED_ING_V1,
    Permissions.IMT_PCK_V1,
    Permissions.IMT_BUYER_V1,
    Permissions.IMT_DISCARD_V1,
    Permissions.IMT_COMMENT_V2,
    Permissions.IMT_SYNC_V1,
    Permissions.PIMT_SYNC_V1,
    Permissions.IMT_PCK_DEPL_V2,
)
