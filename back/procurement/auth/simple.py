import logging

from flask import Blueprint, jsonify, make_response, redirect, request
from flask_jwt_extended import create_access_token, create_refresh_token, get_jwt_identity, jwt_required

from procurement.application_config import flask_config
from procurement.core.exceptions.api_errors import UserNotExisting
from procurement.managers.social import users

from .common import add_url_params, jwt

logger = logging.getLogger(__name__)

auth_bp = Blueprint("simple_auth", __name__)


def login():
    redirect_url = request.args.get("url")
    logger.debug("GET /login [DEV] with redirect_url: %s", redirect_url)
    if not redirect_url:
        return jsonify({"msg": "Invalid parameter. 'url' must be present."}), 400
    if request.args.get("error"):
        redirect_url = add_url_params(redirect_url, {"auth_error": "This is the local test cookie error"})
        return make_response(redirect(redirect_url))
    if "email" in request.args:
        user = users.get_by_mail(request.args["email"])
        if not user:
            raise UserNotExisting()
        identity = {"id": user.id, "email": user.email}
    else:
        identity = flask_config.USER_DEV_CREDS
    access_token = create_access_token(identity=identity)
    refresh_token = create_refresh_token(identity=identity)

    client_url = add_url_params(redirect_url, {"at": access_token, "rt": refresh_token})
    logger.debug("GET /login [DEV] redirct user to: %s", client_url)
    return make_response(redirect(client_url))


@jwt_required(refresh=True)  # This method handle incorrect or expired token
def refresh():
    logger.debug("GET /refresh [DEV]")
    try:
        identity = get_jwt_identity()
    except Exception as exception:
        logger.debug("GET /refresh [DEV] error. Incorrect JWT identities.")
        raise exception
    access_token = create_access_token(identity=identity)
    refresh_token = create_refresh_token(identity=identity)
    return jsonify(access_token=access_token, refresh_token=refresh_token)


@jwt_required()
def logout():
    logger.info("[Logout] Called Logout ")
    return {}


@jwt.token_in_blocklist_loader
def check_if_token_is_revoked(jwt_header: dict, jwt_payload: dict) -> bool:  # pylint: disable=unused-argument
    return False


def register_simple_endpoints() -> Blueprint:
    auth_bp.add_url_rule("/login/", view_func=login)
    auth_bp.add_url_rule("/refresh/", view_func=refresh, methods=["POST"])
    auth_bp.add_url_rule("/logout/", view_func=logout)

    return auth_bp
