from urllib.parse import urlencode

from flask import Blueprint, jsonify, redirect, request
from flask_jwt_extended import JWTManager

jwt = JWTManager()


def add_url_params(url: str, params: dict):
    url += ("&" if "?" in url else "?") + urlencode(params)
    url = url[:2048]
    return url


def captcha_handler_redirect():
    if "url" not in request.args:
        return jsonify({"msg": "Invalid parameter. 'url' must be present."}), 400
    return redirect(request.args.get("url"), 302)


def register_common_routes(auth_bp: Blueprint):
    auth_bp.add_url_rule("redirect/", view_func=captcha_handler_redirect)
