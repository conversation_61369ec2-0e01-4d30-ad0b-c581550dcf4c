import logging
import pickle  # nosec B403
import struct
import time
from datetime import datetime, timedelta
from threading import Thread
from typing import Callable, Generic, TypeVar

from redis_lock import Lock

from procurement.core.cache_utils.constants import cache

from .metrics import ApplicationMetrics
from .runtime import SHUTDOWN_REQUESTED

T = TypeVar("T")

logger = logging.getLogger(__name__)


class WriteBuffer(Generic[T]):
    def __init__(
        self,
        key: str,
        sink: Callable[[list[T]], None],
        size: int = 500,
        flush_period: timedelta | None = timedelta(seconds=30),
    ):
        """Redis write buffer for performing batch event-based operations
        :param key: name of the buffer
        :param sink: function to consume all the records. Ideally should not throw any exceptions and discard only items
            that didn't pass validation, otherwise the whole batch will be discarded from the buffer.
        :param size: buffer size
        :param flush_period: starts separate thread that periodically flush all records in the buffer
            (for the cases on infrequent writes or flushing the last batch). Min = 1 sec
        """
        self.key = key
        self._rkey = f"write_buffer:{self.key}"
        self._flush_key = f"{self._rkey}:flusht"
        self._sink = sink
        self._size = size
        self._flush_period = flush_period
        if flush_period:
            self._set_flush_time()
            flusher = Thread(target=self._auto_flusher)
            flusher.start()

    def write(self, item: T) -> None:
        """Writes ``item`` to the buffer and flushes it if size is exceeded"""
        buffer_size = cache.rpush(self._rkey, pickle.dumps(item))
        if buffer_size >= self._size:
            self.flush()

    def flush(self, empty: bool = False) -> None:
        """Flushes buffer to the ``sink`` if buffer size is more than its size
        :param empty: if ``True``, flushes the whole buffer regardless of current size
        """
        with self._lock():
            size = cache.llen(self._rkey)
            if not size or size < self._size and not empty:
                self._set_flush_time()
                return
            # TODO: upgrade to Redis 6+ and use ``lpop`` with count instead
            items = cache.lrange(self._rkey, 0, -1)
            if items:
                cache.ltrim(self._rkey, len(items), -1)
            self._set_flush_time()
            try:
                if items:
                    self._sink([pickle.loads(i) for i in items])  # nosec B301
                    logger.info("Buffer flushed -- name='%s', items=%s", self._rkey, len(items))
            except Exception as exc:
                err_msg = f"Error while flushing buffer -- name='{self._rkey}', items_lost={len(items)}"
                logger.error(err_msg, exc_info=exc)
                ApplicationMetrics.critical_exceptions(service=self._rkey, exception=exc, message=err_msg)

    def _lock(self) -> Lock:
        return Lock(
            redis_client=cache,
            name=self._rkey,
            expire=3,
            auto_renewal=True,
        )

    def _set_flush_time(self) -> None:
        now = datetime.now().timestamp()
        cache.set(self._flush_key, struct.pack("d", now))

    def _get_last_flush(self) -> datetime:
        flush_time = cache.get(self._flush_key)
        return datetime.fromtimestamp(struct.unpack("d", flush_time)[0]) if flush_time is not None else datetime.now()

    def _auto_flusher(self) -> None:
        wait_sec = max(min(10, round(self._flush_period.total_seconds() / 2)), 1)
        while True:
            if datetime.now() - self._get_last_flush() > self._flush_period:
                logger.info("Auto flushing buffer... -- name='%s'", self._rkey)
                self.flush(True)
            time.sleep(wait_sec)
            if SHUTDOWN_REQUESTED.is_set():
                break
