import logging
import typing
from functools import wraps
from types import FunctionType
from typing import Callable, ParamSpec, TypeVar

from procurement.core.cache_utils import caching

from . import context

logger = logging.getLogger(__name__)

P = ParamSpec("P")
R = TypeVar("R")
_NONE_RESULT = object()


def request_cache(func: Callable[P, R]) -> Callable[P, R]:
    """
    Caches function's calls on per-request or per-job basis

    :param func: Function that should be cached
    """
    wrapped = typing.cast(FunctionType, func)
    prefix = caching.get_path(wrapped)
    caching.verify_hashable_signature(wrapped)

    @wraps(func)
    def decorator(*args: P.args, **kwargs: P.kwargs) -> R:
        request_context = context.get_request_context()
        if request_context.is_cached:
            return caching.cached_call(wrapped, prefix, request_context.cache_object, *args, **kwargs)
        return func(*args, **kwargs)

    return decorator
