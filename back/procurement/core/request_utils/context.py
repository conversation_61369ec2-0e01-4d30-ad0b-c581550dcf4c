import dataclasses
import logging
import traceback
from collections import defaultdict
from datetime import date
from typing import Any, TypeVar

import flask
import rq

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.dates import ScmWeek
from procurement.data.dto.config import Brand
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.social.models import NO_PERMISSIONS, UserInfo, UserPermissions

logger = logging.getLogger(__name__)
CONTEXT_KEY = "request_context"
R = TypeVar("R")


@dataclasses.dataclass
class RequestContext:
    user_info: UserInfo | None = None

    _market: str | None = None
    _current_week: ScmWeek | None = None
    _current_site: DcConfig | None = None

    def __post_init__(self):
        self._warnings: dict[str, list[Any]] = defaultdict(list)
        self._cache_object: dict[str, dict[tuple, R]] = defaultdict(dict)
        self.today = date.today()

    @property
    def user_permissions(self) -> UserPermissions:
        return self.user_info.user_permissions.get(self.market, NO_PERMISSIONS)

    @property
    def cache_object(self) -> dict[str, dict[tuple, R]]:
        return self._cache_object

    @property
    def warnings(self) -> dict[str, list[Any]]:
        return self._warnings

    @property
    def market(self) -> str:
        if not self._market:
            logger.warning(
                "Market context is used without setting it. Usage location:\n%s", "".join(traceback.format_stack()[:-1])
            )
            return MARKET_US
        return self._market

    @market.setter
    def market(self, market: str) -> None:
        self._market = market

    @property
    def current_week(self) -> ScmWeek | None:
        return self._current_week

    @property
    def current_site(self) -> DcConfig | None:
        return self._current_site

    @property
    def is_cached(self) -> bool:
        return True

    def set_site(self, site: DcConfig) -> "RequestContext":
        self._current_site = site
        return self


@dataclasses.dataclass
class _NonCachedRequestContext(RequestContext):
    @property
    def is_cached(self) -> bool:
        return False

    def set_brand(self, brand: Brand) -> "RequestContext":
        raise NotImplementedError("set_brand is not available in NonCachedRequestContext")

    def set_site(self, site: DcConfig) -> "RequestContext":
        raise NotImplementedError("set_site is not available in NonCachedRequestContext")


def get_request_context() -> RequestContext:
    if flask.has_app_context() and flask.has_request_context():
        context = flask.g
    elif current_job := rq.get_current_job():
        context = current_job
    else:
        return _NonCachedRequestContext()
    if not hasattr(context, CONTEXT_KEY):
        setattr(context, CONTEXT_KEY, RequestContext())
    return getattr(context, CONTEXT_KEY)
