import logging
import time
from logging import config as logger_config
from typing import Callable, ParamSpec, TypeVar

from procurement.core.config_utils import config


def init_logger():
    logger_config.dictConfig(config["logging"])


P = ParamSpec("P")
R = TypeVar("R")


def log_wrapper(func: Callable[P, R]) -> Callable[P, R]:
    logger = logging.getLogger(func.__module__)

    def inner_wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
        start_time = time.time()
        logger.debug("%s function called with args %s and kwargs %s", func.__name__, str(args), str(kwargs))
        result = func(*args, **kwargs)
        run_time = time.time() - start_time
        logger.debug("%s function finished execution in %s seconds", func.__name__, str(run_time))
        return result

    return inner_wrapper
