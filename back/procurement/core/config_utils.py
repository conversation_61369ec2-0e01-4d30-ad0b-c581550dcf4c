import json
import logging
import os
import subprocess  # nosec B404
from enum import StrEnum
from json.decoder import JSONDecodeError
from pathlib import Path

logger = logging.getLogger(__name__)


class EnvType(StrEnum):
    DEV = "dev"
    STAGING = "staging"
    PRODUCTION = "production"


class AppType(StrEnum):
    WORKER = "WORKER"
    BACK = "BACK"
    CONSUMER = "CONSUMER"
    CLOCK = "CLOCK"
    TESTS = "TESTS"


class KillSwitch:
    def __init__(self, general_config):  # pylint: disable=too-many-statements
        # Default values
        self.export_enabled: bool = False
        self.forecast_upload_enabled = False
        self.po_export_enabled = False
        self.blujay_shipment_sync_enabled = False
        self.culinary_sku_sync_enabled = False
        self.blujay_appointment_sync_enabled = False
        self.pimt_export_enabled = False
        self.imt_daily_export = False
        self.pimt_daily_exception_report = False
        self.packaging_info_sync_enabled = False
        self.supplier_sku_sync_enabled = False
        self.po_acknowledgement_sync_enabled = False
        self.pimt_3pw_monthly_report = False
        self.grn_sync_enabled = False
        self.advance_shipping_notice_sync_enabled = False
        self.export_to_local_files_enabled = False
        self.hj_grn_enabled = False
        self.limit_preventative_alert_time = False
        self.kafka_inv_snapshot_enabled = False
        self.kafka_supplier_enabled = False
        self.kafka_dc_enabled = False
        self.wip_consumption_import_enabled = False
        self.kafka_ot_enabled = True
        self.kafka_transfer_order_enabled = False
        self.show_bulk_on_ingredient_depletion = False
        self.use_oscar_for_ca_forecast = False
        self.combined_ep_gc_forecast_enabled = False
        self.use_hj_for_grn_hj_warehouses = False
        self.kafka_manufactured_sku_enabled = False
        self.kafka_work_order_tool_enabled = False
        self.aggregated_forecast_upload_enabled = True
        self.show_aggregated_depletion_on_ingredient_depletion = False
        self.disable_discard_form_for_hj = False
        self.display_hj_discards_in_discard_form = False
        self.transfer_orders_enabled = False
        self.transfer_orders_receipt_supplier_filtering_enabled = False
        self.ing_depl_consider_prev_week_inv = False  # GD-5700
        self.inventory_export_to_s3_enabled = False
        self.kafka_factor_pkg_consumption_enabled = False
        self.consume_rejected_orders = True
        self.top_variance_aggregation_enabled = False
        self.kafka_supplier_splits_consumption_enabled = False
        self.kafka_purchase_qty_recommendation_consumption_enabled = False
        self.kafka_ope_bulk_skus_enabled = False
        self.blujay_filter_out_carrier = False
        self.consume_new_packaging_demand = False
        self.canada_pck_depl_on_oh_aggregation_enabled = True
        self.use_topo_for_all_skus = False
        self.ope_use_10_wo_mock_plans = False
        self.kafka_shelf_life_consumption_enabled = False
        self.enable_sql_stack_traced_metric = False

        killswitch_config = general_config.get("killswitch", {})

        # Override properties
        for key, value in self.__dict__.items():
            setattr(self, key, killswitch_config.get(key, value))


def load_config() -> dict:
    """
    TODO sequence load\\overwrite:
    load:
    1. ./config/config_base.json
    2. os.environ.get("CONFIG")
    GET ENV ->
    3. ./config/config_{ENV}.json

    Override:

    config_base
        << override with config_{ENV}
            << override with  os.environ.get("CONFIG")

    :return config:
    """
    base_config_path = Path(__file__).parent.parent.parent.resolve() / "config"
    # 1. Load base_config
    with open(base_config_path / "config_base.json", encoding="utf8") as config_file:
        base_config = json.load(config_file)

    # 2. Load env variable config
    env_config = {}
    try:
        env_config = json.loads(os.environ.get("CONFIG"))
    except (TypeError, JSONDecodeError):
        try:
            with open(os.environ.get("CONFIG"), encoding="utf8") as config_file:
                env_config = json.load(config_file)
        except (TypeError, FileNotFoundError):
            try:
                with open(base_config_path / "config.json", encoding="utf8") as config_file:
                    env_config = json.load(config_file)
            except FileNotFoundError:
                logger.warning("Environment config is not set")

    env = EnvType(env_config.get("flask", {}).get("env", "dev").lower())
    logger.info("Starting application as on %s environment", env.value)

    # 3. Load local environment config
    local_env_json_file = base_config_path / f"{env.value}/config.json"
    try:
        with open(local_env_json_file, encoding="utf8") as config_file:
            local_env_config = json.load(config_file)
    except FileNotFoundError:
        logger.warning("Environment config file %s not found or parse problem", local_env_json_file, exc_info=True)
        prepared_local_config = base_config
    else:
        prepared_local_config = merge(local_env_config, base_config)

    result_config = merge(env_config, prepared_local_config)

    result_config["googlesheet_sa"]["config"] = __load_gsheet_sa(result_config["googlesheet_sa"])
    return result_config


def merge(source: dict, destination: dict) -> dict:
    """
    >>> a = { 'first' : { 'all_rows' : { 'pass' : 'dog', 'number' : '1' } } }
    >>> b = { 'first' : { 'all_rows' : { 'fail' : 'cat', 'number' : '5' } } }
    >>> merge(b, a) == { 'first' : { 'all_rows' : { 'pass' : 'dog', 'fail' : 'cat', 'number' : '5' } } }
    True
    """
    for key, value in source.items():
        if isinstance(value, dict):
            # get node or create one
            node = destination.setdefault(key, {})
            merge(value, node)
        else:
            destination[key] = value

    return destination


def __load_gsheet_sa(gsheet_sa_config: dict):
    if gsheet_config_json := os.environ.get("GOOGLE_SHEETS_CONFIG"):
        return json.loads(gsheet_config_json)
    if gsheet_config := __load_gsheet_sa_zip(gsheet_sa_config):
        return gsheet_config
    config_file_path = os.environ.get("GOOGLE_SHEETS_CONFIG_PATH", "./config/googlesheets.config.json")
    with open(config_file_path, encoding="utf8") as config_file:
        return json.load(config_file)


def __load_gsheet_sa_zip(gsheet_sa_config: dict):
    if not (zip_pass := gsheet_sa_config.get("password")):
        return None
    config_dir = "./config/googlesheets"
    config_zip_file_name = gsheet_sa_config.get("zip")
    pid = os.getpid()
    config_pid_dir = f"{config_dir}/{pid}"
    try:
        subprocess.check_call(  # nosec B607, B603
            ["7z", "e", f"{config_dir}/{config_zip_file_name}", f"-o{config_pid_dir}", f"-p{zip_pass}", "-y"],
            stdout=subprocess.DEVNULL,
        )
    except subprocess.CalledProcessError:
        logger.warning("Unable to unzip gsheet config")
        return None
    file_names = os.listdir(config_pid_dir)
    if len(file_names) != 1:
        raise ValueError(f"{config_dir}/{config_zip_file_name} must contain exactly one file")
    config_file_path = f"{config_pid_dir}/{file_names[0]}"
    with open(config_file_path, encoding="utf8") as config_file:
        gsheet_config = json.load(config_file)
    os.remove(config_file_path)
    os.rmdir(config_pid_dir)
    return gsheet_config


config = load_config()
ENV = EnvType(config.get("flask", {}).get("env", "dev").lower())
killswitch = KillSwitch(config)

app_type = AppType(os.environ.get("APP_TYPE", AppType.BACK).upper())
is_ci = os.getenv("CI") is not None
MIGRATION_PATH = "migrations"
