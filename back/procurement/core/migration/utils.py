import logging
import pathlib
import re

from tenacity import Retrying, retry, retry_if_exception_type, stop_after_attempt, wait_fixed
from yoyo import get_backend, read_migrations
from yoyo.exceptions import LockTimeout

from procurement.core.config_utils import MIGRATION_PATH, config


@retry(retry=retry_if_exception_type(LockTimeout), stop=stop_after_attempt(3), wait=wait_fixed(10))
def apply_migration():
    db_config = config["database"]
    main_db = Retrying(stop=stop_after_attempt(20), wait=wait_fixed(1))(
        get_backend,
        f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:"
        f"{db_config['port']}/{db_config['name']}",
    )
    migrations = read_migrations(MIGRATION_PATH)

    try:
        with main_db.lock():
            # Apply any outstanding migrations
            to_apply = main_db.to_apply(migrations)
            main_db.apply_migrations(to_apply)
    except Exception as exc:
        logging.getLogger(__name__).exception("Applying rollback. Can not apply migrations: %s", exc)
        raise


MIGRATION_REGEXP = re.compile(r"\d{4}\..*\.py")

MigrationFileName = MigrationName = str


def _get_migration_name(file_name: pathlib.Path) -> MigrationName:
    return file_name.name[:-3]


def get_depends(file_name: MigrationFileName) -> set[MigrationName]:
    migrations_path = pathlib.Path(MIGRATION_PATH)

    migrations: list[MigrationName] = sorted(
        [
            _get_migration_name(migration)
            for migration in migrations_path.iterdir()
            if re.match(MIGRATION_REGEXP, migration.name)
        ]
    )

    cur_migration = _get_migration_name(pathlib.Path(file_name))

    try:
        prev_migration = migrations[: migrations.index(cur_migration)]
    except ValueError as exc:
        raise ValueError(
            "Please specify name as '<xxxx>.<name>.py', where xxxx is 4 digts number of migration"
        ) from exc

    if prev_migration:
        # Get last dependency
        return set(prev_migration[-1:])

    # No dependencies
    return set()
