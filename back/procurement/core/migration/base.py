from __future__ import annotations

import dataclasses
from abc import ABC, abstractmethod
from typing import Any, Self

from psycopg2.extensions import connection as psql_conn
from psycopg2.extensions import cursor as psql_cursor


@dataclasses.dataclass
class SqlData(ABC):
    """Class that defines database record that you want to modify

    To make it work, you should implement following methods:
        _get_select_query: method should return sql query that will be used to query data from db.
        _parse_sql: method accepts one row from database, and should return object of this class.
        to_sql: method should return sql query string build from object.
        transform: method should be used to perform modification of data.
            (important!) transform should return SqlData object. Also, you can change data in place and return self

        Example usage can be found in migration 0025.update_user_view.py

    """

    @staticmethod
    @abstractmethod
    def _get_select_query(**kwargs) -> str:
        """Method should return sql query that will be used to query data from db"""
        raise NotImplementedError()

    def _from_sql(self, cursor: psql_cursor, **kwargs) -> list[Self]:
        cursor.execute(self._get_select_query(**kwargs))
        return [self._parse_sql(row, **kwargs) for row in cursor.fetchall()]

    @staticmethod
    @abstractmethod
    def _parse_sql(row: tuple[Any, ...], **kwargs) -> SqlData:
        """Should return object parsed from sql row"""
        raise NotImplementedError()

    @abstractmethod
    def to_sql(self, **kwargs) -> str:
        """Should return sql query that must be applied to db"""
        raise NotImplementedError()

    @abstractmethod
    def transform(self, **kwargs) -> Self:
        """Should apply modifications on SqlData object"""
        raise NotImplementedError()

    def apply(self, connection: psql_conn, **kwargs) -> None:
        """This is a main entry point for migration.
        Example call:
            from yoyo import step
            steps = [step(UserViewState(kwargs).apply)]
        """
        cursor = connection.cursor()
        items = self._from_sql(cursor, **kwargs)
        if items:
            query = "".join([item.transform(**kwargs).to_sql(**kwargs) for item in items])
            cursor.execute(query)
