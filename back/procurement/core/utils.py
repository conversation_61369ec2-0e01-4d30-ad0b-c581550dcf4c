import itertools
import re
from collections import defaultdict
from collections.abc import Collection, Iterable
from datetime import date
from decimal import Decimal
from itertools import islice
from typing import Callable, Generator, Iterator

from dateutil import parser as date_parser

from procurement.core.dates import ScmWeek
from procurement.core.typing import BOB_CODE, ORDER_NUMBER, PO_NUMBER, SKU_CODE, SKU_ID, UNITS

SKU_CODE_PATTERN = re.compile(r"^[A-Z]{3}-(\d+-?)+$")


def get_order_number_from_po_number(po_number: PO_NUMBER) -> ORDER_NUMBER | None:
    return po_number.split("_")[0] if po_number is not None else None


def get_bob_code_from_po_number(po_number: PO_NUMBER) -> BOB_CODE | None:
    bob_code = po_number[4:6] if po_number and len(po_number) > 5 else None
    return bob_code if bob_code and bob_code.isalpha() else None


def get_week_from_po_number(po_number: PO_NUMBER) -> ScmWeek | None:
    return ScmWeek.from_short_number(int(po_number[:4])) if po_number is not None and len(po_number) > 4 else None


def get_sku_id_from_sku_code(sku_code: SKU_CODE | None) -> SKU_ID | None:
    return next((int(part) for part in (sku_code or "").split("-") if len(part) > 2 and part.isdecimal()), None)


def is_valid_sku_code(sku_code: SKU_CODE | None) -> bool:
    return (
        get_sku_id_from_sku_code(sku_code) is not None and len(sku_code) < 40 and re.match(SKU_CODE_PATTERN, sku_code)
    )


def is_green_chef_sku(sku_code: SKU_CODE) -> bool:
    return sku_code and sku_code.startswith("G")


def is_valid_date(date_):
    try:
        date_parser.parse(date_)
    except ValueError:
        return False
    return True


def is_bulk_sku(sku_name: str) -> bool:
    return sku_name and "bulk" in sku_name.lower()


def float_to_decimal(a_float: float) -> Decimal:
    if not a_float:
        return Decimal()
    return round(Decimal(a_float), 6).normalize()


def is_in_int4_range(number: int) -> bool:
    return isinstance(number, int) and -2147483648 <= number <= 2147483647


def chunked[T](sequence: Iterable[T], batch_size: int) -> Generator[list[T], None, None]:  # noqa: E999
    marker = object()
    for group in (list(g) for g in itertools.zip_longest(*[iter(sequence)] * batch_size, fillvalue=marker)):
        if group[-1] is marker:
            del group[group.index(marker) :]
        yield group


def ceil_subone_integer(value: float | int | Decimal) -> int:
    if 0 < value < 1:
        return 1
    return round(value)


def ceil_subone_decimal(value: UNITS) -> UNITS:
    if 0 < value < 1:
        return Decimal(1)
    return value


def min_of_two[T](first: T | None, second: T | None) -> T | None:
    return min(filter(None, (first, second)), default=None)


def max_of_two[T](first: T | None, second: T | None) -> T | None:
    return max(filter(None, (first, second)), default=None)


def iskip[T](collection: Iterable[T], skip_next: int) -> Iterator[T]:
    it = iter(collection)
    next(islice(it, skip_next, skip_next), None)
    return it


def group_by[K, T](items: Iterable[T], key: Callable[[T], K]) -> dict[K, list[T]]:
    res = defaultdict(list)
    for it in items:
        res[key(it)].append(it)
    res.default_factory = None
    return res


def split_comma_string(string: str) -> list[str]:
    if not string:
        return []
    return list(map(str.strip, string.split(",")))


def is_any_collection_empty(*collections: Collection | None) -> bool:
    return any(c is not None and not c for c in collections)


def reduce_day_to_range(day: date, first_day: date, last_day: date) -> date:
    if day < first_day:
        return first_day
    if day > last_day:
        return last_day
    return day


def ttl_string_to_sec(td: str | int) -> int:
    """
    Parses time strings like "20s", "5m", "1h", "1d" to amount of seconds.
    If td value is int or has no time unit specified then it's parsed as seconds.
    """
    if isinstance(td, int):
        return td
    time_unit = td[-1]
    if time_unit.isalpha():
        time_value = int(td[:-1])
        if time_unit == "s":
            return time_value
        if time_unit == "m":
            return time_value * 60
        if time_unit == "h":
            return time_value * 60 * 60
        if time_unit == "d":
            return time_value * 60 * 60 * 24
        raise ValueError(f"Unknown time unit '{time_unit}' in ttl string '{td}'")
    return int(td)
