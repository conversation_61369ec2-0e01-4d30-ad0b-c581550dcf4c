import dataclasses
import functools
import inspect
import itertools
import logging
import os
import threading
import time
import typing
from collections.abc import Iterable
from types import FunctionType, TracebackType
from typing import Any, Callable, ParamSpec, Type, TypeVar

from flask import Flask, request
from prometheus_client import metrics, multiprocess
from prometheus_client.registry import CollectorRegistry
from prometheus_client.values import MultiProcessValue
from prometheus_flask_exporter import PrometheusMetrics
from prometheus_flask_exporter.multiprocess import GunicornInternalPrometheusMetrics

from .config_utils import AppType, app_type
from .dates import ScmWeek

P = ParamSpec("P")
R = TypeVar("R")

MULTIPROC_DIR = "prometheus_multiproc_dir"

APP_LABEL = {"app": "InventoryManagement", "app_type": None}

PROMETHEUS_REGISTRY = CollectorRegistry(target_info=APP_LABEL)


class Summary(metrics.Summary):
    def __init__(self, name: str, documentation: str, labelnames: Iterable[str] = (), **kwargs):
        super().__init__(
            name=name, documentation=documentation, labelnames=itertools.chain(labelnames, APP_LABEL.keys()), **kwargs
        )

    def labels(self, *labelvalues: Any, **labelkwargs: Any) -> "Summary":
        return super().labels(*labelvalues, **{**labelkwargs, **APP_LABEL})


class Counter(metrics.Counter):
    def __init__(self, name: str, documentation: str, labelnames: Iterable[str] = (), **kwargs):
        super().__init__(
            name=name, documentation=documentation, labelnames=itertools.chain(labelnames, APP_LABEL.keys()), **kwargs
        )

    def labels(self, *labelvalues: Any, **labelkwargs: Any) -> "Counter":
        return super().labels(*labelvalues, **{**labelkwargs, **APP_LABEL})


def init_registry() -> None:
    if app_type:
        APP_LABEL["app_type"] = app_type.value
    if MULTIPROC_DIR in os.environ:
        multiprocess.MultiProcessCollector(PROMETHEUS_REGISTRY)


def override_value_class() -> None:
    if app_type == AppType.WORKER:
        metrics.values.ValueClass = MultiProcessValue(lambda: 9999)
    else:
        metrics.values.ValueClass = metrics.values.get_value_class()


@dataclasses.dataclass
class LabelsConfig:
    """Structure that stores labels information"""

    labels: dict
    labeled_params: list
    mapping: dict
    only_mapped: bool


class SummaryMetric:
    """Helper class that encapsulates Summary metric and provides ability to use it as decorator or context manager"""

    def __init__(self, metric: Summary, labels_config: LabelsConfig):
        self.metric = metric
        self.labels_config = labels_config
        self.local_values = threading.local()

    def _set_labeled_params(self, func: Callable[P, R], args: P.args, kwargs: P.kwargs) -> None:
        """Pulls labeled param values from func signature, maps them if it's necessary and saves them into labels"""
        if self.labels_config.labeled_params:
            params_values = _get_params_dict(func, args, kwargs)

            if self.labels_config.mapping:
                if self.labels_config.only_mapped:
                    new_param_values = {}
                    for key, value in self.labels_config.mapping.items():
                        new_param_values[value] = params_values[key]
                    params_values = new_param_values
                else:
                    for key, value in self.labels_config.mapping.items():
                        params_values[value] = params_values[key]

            for param in self.labels_config.labeled_params:
                self.local_values.labels[param] = params_values.get(param, None)

    def __call__(self, func: Callable[P, R]) -> Callable[P, R]:
        """Returns function decorated with Summery metric"""

        @functools.wraps(func)
        def wrapped(*args: P.args, **kwargs: P.kwargs) -> R:
            with self:
                self.local_values.labels["method"] = f"{typing.cast(func, FunctionType).__module__}.{func.__name__}"
                self._set_labeled_params(func, args, kwargs)
                return func(*args, **kwargs)

        return wrapped

    def __enter__(self) -> None:
        """Saves labels into threading.local"""
        self.local_values.labels = self.labels_config.labels.copy()
        self.local_values.start_time = time.time()
        for param in self.labels_config.labeled_params:
            self.local_values.labels[param] = None

    def __exit__(self, exc_type: Type[Exception], exc_val: Exception, exc_tb: TracebackType) -> None:
        """Calculates time of execution"""
        current_time = time.time()
        seconds = current_time - self.local_values.start_time

        if seconds < 0:
            logging.warning(
                "Unbelievable, negative time: %s, current: %s, start_time: %s, labels: %s",
                seconds,
                current_time,
                self.local_values.start_time,
                self.local_values.labels,
            )
        if exc_type:
            self.local_values.labels["result"] = f"Error! Type: {exc_type}, Message: {exc_val}"

        self.metric.labels(**self.local_values.labels).observe(abs(time.time() - self.local_values.start_time))


class _ApplicationMetrics:
    """
    This class contains different functions that returns different kinds of metrics.
    """

    _exceptions: Counter | None = None
    _gsheet_call: Counter | None = None
    _functional_methods: Summary | None = None
    _count_ot_hit_metric: Counter | None = None
    _count_gsheet_validation_measurement: Counter | None = None
    _count_gsheet_validation_measurement_all: Counter | None = None
    _datasync_measurement: Summary | None = None
    _gsheet_call_time: Summary | None = None
    _ordering_tool_measuring: Summary | None = None
    _sql_call: Counter | None = None
    _export_count: Counter | None = None
    _kafka_message: Counter | None = None

    @staticmethod
    def check_mapping(labeled_params, params_mapping):
        if not all(value in labeled_params for value in params_mapping.values()):
            raise ValueError("Not identical keys in params_mapping and labeled_params that came outside.")

    def functional_methods(
        self, request_type=None, method="method_name", params_mapping=None, only_mapped=False
    ) -> Callable:
        """
        This function returns functional metric that has name, description, labels and params.
        """
        params_mapping = params_mapping or {}
        labels = {"type": None, "method": method, "result": "success", "request_type": request_type}
        labeled_params = ["brand", "week"]
        self.check_mapping(labeled_params, params_mapping)

        labels_config = LabelsConfig(
            labels=labels, labeled_params=labeled_params, mapping=params_mapping, only_mapped=only_mapped
        )
        if not self._functional_methods:
            self._functional_methods = Summary(
                name="functional_methods",
                documentation="Function Method time",
                labelnames=list(labels.keys()) + labeled_params,
            )
        return SummaryMetric(self._functional_methods, labels_config)

    def datasync_measurement(self, metric_type=None, params_mapping=None, only_mapped=False) -> Callable:
        """
        This function returns datasync_measurement metric that has name, description, labels and params.
        """
        params_mapping = params_mapping or {}
        labels = {"type": metric_type, "method": "method_name", "result": "success"}
        labeled_params = []
        self.check_mapping(labeled_params, params_mapping)

        labels_config = LabelsConfig(
            labels=labels, labeled_params=labeled_params, mapping=params_mapping, only_mapped=only_mapped
        )
        if not self._datasync_measurement:
            self._datasync_measurement = Summary(
                name="datasync_measurement",
                documentation="Sync jobs measures",
                labelnames=list(labels.keys()) + labeled_params,
            )
        return SummaryMetric(self._datasync_measurement, labels_config)

    def gsheet_call_time(self, params_mapping=None, only_mapped=False) -> Callable:
        """
        This function returns gsheet_call_time metric that has name, description, labels and params.
        """
        params_mapping = params_mapping or {}
        labels = {"method": "method_name", "result": "success"}
        labeled_params = []
        self.check_mapping(labeled_params, params_mapping)

        labels_config = LabelsConfig(
            labels=labels, labeled_params=labeled_params, mapping=params_mapping, only_mapped=only_mapped
        )
        if not self._gsheet_call_time:
            self._gsheet_call_time = Summary(
                name="gsheet_call_time",
                documentation="Measuring request",
                labelnames=list(labels.keys()) + labeled_params,
            )
        return SummaryMetric(self._gsheet_call_time, labels_config)

    def ordering_tool_measuring(self, params_mapping=None, only_mapped=False) -> Callable:
        """
        This function returns ordering_tool_measuring metric that has name, description, labels and params.
        """
        params_mapping = params_mapping or {}
        labels = {"method": "method_name", "result": "success"}
        labeled_params = []
        self.check_mapping(labeled_params, params_mapping)

        labels_config = LabelsConfig(
            labels=labels, labeled_params=labeled_params, mapping=params_mapping, only_mapped=only_mapped
        )
        if not self._ordering_tool_measuring:
            self._ordering_tool_measuring = Summary(
                name="ordering_tool_measuring",
                documentation="Measuring request",
                labelnames=list(labels.keys()) + labeled_params,
            )

        return SummaryMetric(self._ordering_tool_measuring, labels_config)

    def count_ot_hit_metric(self) -> None:
        """This function presents count_ot_hit_metric that has type Counter"""
        if not self._count_ot_hit_metric:
            self._count_ot_hit_metric = Counter(
                name="ot_cache_hit", documentation="Ordering Tool cache hit", registry=PROMETHEUS_REGISTRY
            )

        self._count_ot_hit_metric.labels().inc()

    def count_gsheet_validation_measurement(
        self, metric_type: str = None, doc_model: str = None, scm_week: ScmWeek = None, available_dcs: list[str] = None
    ) -> None:
        """This function presents count_gsheet_validation_measurement metric that has type Counter"""
        labels = {
            "type": metric_type,
            "doc_model": doc_model,
            "scm_week": str(scm_week) if scm_week else None,
            "available_dcs": available_dcs,
        }

        if not self._count_gsheet_validation_measurement:
            self._count_gsheet_validation_measurement = Counter(
                name="gsheet_validation_measurement",
                documentation="Measuring of gsheet validation",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )

        self._count_gsheet_validation_measurement.labels(**labels).inc()

    @staticmethod
    def _get_exception_labels(severity, service, exception, message):
        """Set labels values"""
        return {
            "severity": severity,
            "service_type": service,
            "exc_type": type(exception).__name__ if exception else None,
            "message": str(exception) if not message else message,
        }

    def critical_exceptions(self, service: str = None, exception: Exception = None, message: str = None) -> None:
        """This function presents critical_exceptions metric that has type Counter"""
        labels = self._get_exception_labels("critical", service, exception, message)

        if not self._exceptions:
            self._exceptions = Counter(
                name="exceptions",
                documentation="Count critical exceptions",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )

        self._exceptions.labels(**labels).inc()

    def non_critical_exceptions(self, service: str = None, exception: Exception = None, message=None) -> None:
        """This function presents non_critical_exceptions metric that has type Counter"""
        labels = self._get_exception_labels("non-critical", service, exception, message)

        if not self._exceptions:
            self._exceptions = Counter(
                name="exceptions",
                documentation="Count non critical exceptions",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )

        self._exceptions.labels(**labels).inc()

    def gsheet_call(self, metric_type=None, status=None, call_type=None) -> None:
        """This function presents gsheet_call metric that has type Counter"""
        labels = {"type": metric_type, "status": status, "call_type": call_type}

        if not self._gsheet_call:
            self._gsheet_call = Counter(
                name="gsheet_call",
                documentation="Call to gsheet service",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )

        self._gsheet_call.labels(**labels).inc()

    def db_metrics(self, request_type: str, func_name: str):
        labels = {"type": request_type, "func": func_name}
        if not self._sql_call:
            self._sql_call = Counter(
                name="db_request",
                documentation="SQL request to DB",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )

        self._sql_call.labels(**labels).inc()

    def count_export_metrics(self, export_type: str):
        labels = {"export_type": export_type}
        if not self._export_count:
            self._export_count = Counter(
                name="export_count",
                documentation="Count of exported documents",
                labelnames=labels.keys(),
                registry=PROMETHEUS_REGISTRY,
            )
        self._export_count.labels(**labels).inc()

    def count_kafka_metrics(self, message_type: str, topic: str):
        name = "kafka_message_count"
        description = "Count of kafka messages"
        labels = {"message_type": message_type, "topic": topic}
        if not self._kafka_message:
            self._kafka_message = Counter(
                name=name, documentation=description, labelnames=labels.keys(), registry=PROMETHEUS_REGISTRY
            )
        self._kafka_message.labels(**labels).inc()


ApplicationMetrics = _ApplicationMetrics()


def init_flask_api(app: Flask):
    def exclude_options_methods():
        if request.method == "OPTIONS":
            setattr(request, "prom_do_not_track", True)

    init_registry()

    app.before_request(exclude_options_methods)
    prometheus_config = {
        "default_labels": APP_LABEL,
        "registry": PROMETHEUS_REGISTRY,
        "group_by": "endpoint",
        "buckets": (0.05, 0.1, 0.2, 0.5, 1, 2, 3, 5, 8, 10, 15, 20, float("inf")),
    }

    if MULTIPROC_DIR in os.environ:
        GunicornInternalPrometheusMetrics(app, **prometheus_config)
    else:
        PrometheusMetrics(app, **prometheus_config)


def _get_params_dict(func: Callable[P, R], args: P.args, kwargs: P.kwargs) -> dict:
    signature_args = list(inspect.signature(func).parameters.keys())
    res = _name_positional_args(args, signature_args)
    left_params = signature_args[len(args) :]
    for key in left_params:
        if key in kwargs:
            res[key] = kwargs[key]
    return res


def _name_positional_args(args: P.args, signature_args: list[str]) -> dict:
    if len(args) and not isinstance(args, tuple):
        args = (args,)
    return {signature_args[index]: value for index, value in enumerate(args)}
