import logging

from tenacity import Retrying, stop_after_attempt, wait_fixed

from .constants import cache

logger = logging.getLogger(__name__)


def init_redis() -> None:
    """
    Initialize redis queues and client
    """
    retrying = Retrying(stop=stop_after_attempt(20), wait=wait_fixed(1))
    redis_version = retrying(cache.execute_command, "INFO")["redis_version"]
    logger.info("Redis version: %s", redis_version)
