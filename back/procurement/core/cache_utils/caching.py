import inspect
import typing
from datetime import datetime, timedelta
from functools import wraps
from types import FunctionType
from typing import Callable, ParamSpec, TypeVar

from cachetools import keys

from procurement.core import config_utils

P = ParamSpec("P")
R = TypeVar("R")
_NONE_RESULT = object()


def no_args_temp_cache(ttl: timedelta):
    _cached = None
    _cache_ts = None

    def decorator(func: Callable[[], R]) -> Callable[[], R]:
        wrapped = typing.cast(FunctionType, func)
        _verify_no_args(wrapped)

        @wraps(func)
        def noop_wrapper() -> R:
            return wrapped()

        @wraps(func)
        def wrapper() -> R:
            now = datetime.now()
            nonlocal _cache_ts
            nonlocal _cached
            if not _cache_ts or now - _cache_ts >= ttl:
                _cached = wrapped()
                _cache_ts = now
            return _cached

        return noop_wrapper if config_utils.is_ci else wrapper

    return decorator


def get_path(func: FunctionType) -> str:
    """
    Join function name and it's module name

    :param func: Function for which path is built
    :return: function's module and function's name
    """
    if callable(func):
        return func.__module__ + ":" + func.__name__
    raise RuntimeError("'func' must be a function")


def cached_call(func: FunctionType, prefix: str, cache_obj: dict[str, R], *args: P.args, **kwargs: P.kwargs) -> R:
    args_key = _hash_func_call(func, *args, **kwargs)

    if (value := cache_obj[prefix].get(args_key)) is not None:
        return None if value is _NONE_RESULT else value

    value = func(*args, **kwargs)
    cache_obj[prefix][args_key] = _NONE_RESULT if value is None else value
    return value


def verify_hashable_signature(func: FunctionType) -> None:
    """
    Verify that function call can be hashed

    :param func: Function that should be checked
    :raises ValueError
    """
    prefix = get_path(func)
    function = _undecorate_func(func)
    if not function:
        raise RuntimeError(f"{prefix} is not a function")
    spec = inspect.getfullargspec(function)
    if spec.varargs:
        raise RuntimeError(f"Result of function {prefix} with vararg '{spec.varargs}' can not be cached")
    if spec.varkw:
        raise RuntimeError(f"Result of function {prefix} with kwargs '{spec.varkw}' can not be cached")
    for arg_name in spec.args:
        arg_type = spec.annotations.get(arg_name)
        if not arg_type:
            raise RuntimeError(f"Result of function {prefix} with untyped argument '{arg_name}' can not be cached")
        if not (arg_type.__hash__ and (arg_type.__eq__ or arg_type.__cmp__)):
            raise RuntimeError(
                f"Result of function {prefix} with unhashable argument '{arg_name}': {arg_type} can not be cached"
            )


def _verify_no_args(func: FunctionType):
    prefix = get_path(func)
    function = _undecorate_func(func)
    if not function:
        raise RuntimeError(f"{prefix} is not a function")
    spec = inspect.getfullargspec(function)
    if spec.varargs or spec.varkw or spec.args:
        raise RuntimeError(f"Function {prefix} has to have no arguments")


def _hash_func_call(func: FunctionType, *args: P.args, **kwargs: P.kwargs) -> tuple:
    """
    Builds a hash key for a function call

    :param func: Function that should be hashed
    :return: cache key
    """
    func = _undecorate_func(func)
    arg_names = func.__code__.co_varnames[: func.__code__.co_argcount]
    if not arg_names:
        return keys.hashkey(tuple())
    sorted_args = {}
    if func.__defaults__:
        for arg_name, arg_val in zip(reversed(arg_names), reversed(func.__defaults__)):
            sorted_args[arg_name] = arg_val
    for arg_name, arg_val in zip(arg_names, args):
        sorted_args[arg_name] = arg_val
    for arg_name, arg_val in kwargs.items():
        sorted_args[arg_name] = arg_val
    return keys.hashkey(*sorted(sorted_args.items()))


def _undecorate_func(func: FunctionType) -> FunctionType:
    """
    Return original function without applied decorators

    :param func: Function to undecorate
    :return: Original function
    """
    if not func.__closure__:
        return func
    closure = (c.cell_contents for c in func.__closure__)
    return next((f for f in closure if isinstance(f, FunctionType)), None)
