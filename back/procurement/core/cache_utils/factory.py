import abc
import json
import logging
import pickle  # nosec B403
from collections.abc import Iterable
from functools import cached_property
from typing import Any, Callable, TypeVar

from procurement.core import utils
from procurement.core.config_utils import config

from .constants import cache, local_cache

logger = logging.getLogger(__name__)

T = TypeVar("T")

_TTL = config["cache"]["ttl"]


def _get_ttl(key: str, default: str | int | None = None) -> int:
    return utils.ttl_string_to_sec(_TTL.get(key, default))


class _CacheBase:
    """Defines interface of processing cache misses"""

    @abc.abstractmethod
    def put(self, *args, **kwargs) -> None:
        """
        Interface for saving values in cache
        """

    def _get_from_provider(self, provider: Callable[[], T] = None, **kwargs) -> T | None:
        """
        Used for situation where there is no cache hit.
        Tries to calculate data by calling provider function.

        :param provider: Callable that should return new value to be cached
        :param kwargs: Arguments, passed to
        :return: Result of provider's call(if present)
        """
        if not provider:
            return None

        res = provider()
        self.put(data=res, **kwargs)

        return res


class _CacheObject(_CacheBase):
    """Stores Value by Key"""

    def __init__(self, key: str):
        self.key = key

    def put(self, data: Any, expires: int = None) -> None:  # pylint: disable=arguments-differ
        """
        Save data in Redis.

        :param data: Data to be stored
        :param expires: (optional) TTL of the key
        """
        cache.set(self.key, pickle.dumps(data), ex=expires)

    def get_ttl(self) -> int:
        """
        Time To Live of the key.

        :return: seconds until the key will expire
        """
        return cache.ttl(self.key)

    def get(self, provider: Callable[[], T] = None, expires: int = None) -> T | None:
        """
        This method gets data by the key from the cache if it exists
        otherwise saves the result of the provider to the cache.

        :param provider: (optional) Callable that should return new value to be cached
        :param expires: (optional) TTL of the key
        :return: Data from cache (if cache hit), or from provider (if present)
        """
        data = cache.get(self.key)

        if data:
            try:
                return pickle.loads(data)  # nosec B301
            except Exception:
                cache.delete(self.key)
                logger.exception("Bad value in cache")

        return self._get_from_provider(provider, expires=expires)


class LocalCacheObject(_CacheBase):
    """Store value by key in builtin cache object"""

    def __init__(self, key: str):
        self.key = key

    def _build_path(self, suffix: tuple) -> tuple:
        """
        Builds value's full key by appending suffix to common prefix.

        :param suffix: Immutable list of arguments to identify cached record
        :return: Cache key with suffix
        """
        if suffix:
            return self.key, suffix
        return (self.key,)

    def put(self, data, suffix: tuple = None) -> None:  # pylint: disable=arguments-differ
        """
        Saves data into the local cache

        :param data: data to be stored
        :param suffix: Optional key's suffix for the data, if none then data is stored by a common prefix of this cache.
        Each entry of the tuple has to be hashable.
        """
        local_cache[self._build_path(suffix)] = data

    def get(self, suffix: tuple = None, provider: Callable[[], T] = None) -> T | None:
        """
        This method gets data by the key from the cache if it exists
        otherwise saves the result of the provider to the cache.

        :param provider: (optional) Callable that should return new value to be cached
        :param suffix: (optional) Immutable list of arguments to identify cached record
        :return: Data from cache (if cache hit), or from provider (if present)
        """
        data = local_cache.get(self._build_path(suffix))
        if data:
            return data

        return self._get_from_provider(provider, suffix=suffix)

    def delete(self, suffix: tuple = None) -> None:
        """
        Delete data from local cache

        :param suffix: (optional) Immutable list of arguments to identify cached record
        """
        key = self._build_path(suffix)
        if key in local_cache:
            del local_cache[key]


class _CacheMap(_CacheBase):
    """Stores Values in Redis by keys with common prefix"""

    def __init__(self, prefix: str, default_ttl_sec: int | None = None):
        self.prefix = prefix
        self._default_ttl = default_ttl_sec

    def _build_path(self, key: str) -> str:
        """
        Builds key as "prefix:key"

        :param key: Variable part of the key
        :return: Full key
        """
        return self.prefix + ":" + key

    def put(self, key: str, data, expires: int = None) -> None:  # pylint: disable=arguments-differ
        """
        Save data in Redis.

        :param key: Variable part of the key
        :param data: data to be stored
        :param expires: (optional) TTL of the key. If not specified then default cache's ttl is used.
        """
        expires = self._default_ttl if expires is None else expires
        cache.set(self._build_path(key), pickle.dumps(data), ex=expires)

    def get(self, key: str, provider: Callable[[], T] = None, expires: int = None) -> T | None:
        """
        This method gets data by the key from the cache if it exists
        otherwise saves the result of the provider to the cache.

        :param key: Variable part of the key
        :param provider: (optional) Callable that should return new value to be cached
        :param expires: (optional) TTL of the key
        :return: Data from cache (if cache hit), or from provider (if present)
        """
        full_key = self._build_path(key)

        data = cache.get(full_key)
        if data:
            try:
                return pickle.loads(data)  # nosec B301
            except Exception:
                cache.delete(full_key)
                logger.exception("Bad value in cache")

        expires = self._default_ttl if expires is None else expires
        return self._get_from_provider(provider, expires=expires, key=key)

    def get_ttl(self, key: str) -> int:
        """
        Time To Live of the key.

        :return: seconds until the key will expire
        """
        return cache.ttl(self._build_path(key))

    def get_and_delete(self, key: str) -> Any:
        """
        Pop data from cache

        :param key: Variable part of the key
        :return: Data from cache (if cache hit), or from provider (if present)
        """
        data = self.get(key)
        if data:
            self.delete(key)
        return data

    def delete(self, key: str) -> None:
        """
        Delete data by key

        :param key: Variable part of the key
        """
        cache.delete(self._build_path(key))

    def __delitem__(self, key) -> None:
        """
        Delete data by key

        :param key: Variable part of the key
        """
        self.delete(key)

    def __contains__(self, key) -> bool:
        """
        Checks if data exists

        :param key: Variable part of the key
        :return: Whether the key exists
        """
        return bool(cache.exists(self._build_path(key)))


class _DictCacheObject(_CacheBase):
    """Stores values as hash map by key in Redis"""

    def __init__(self, prefix: str, default_ttl_sec: int | None = None):
        self.prefix = prefix
        self._default_ttl = default_ttl_sec

    def get(self, key: str, provider: Callable[[], T] = None, expires: int = None) -> T | None:
        """
        This method gets data by the key from the cache if it exists
        otherwise saves the result of the provider to the cache.

        :param key: Inner key of hash map
        :param provider: (optional) Callable that should return new value to be cached
        :param expires: (optional) TTL of the key
        :return: Data from cache (if cache hit), or from provider (if present)
        """
        data = cache.hget(self.prefix, key)
        if data:
            try:
                return pickle.loads(data)  # nosec B301
            except Exception:
                self.delete(key)
                logger.exception("Bad value in cache")

        expires = self._default_ttl if expires is None else expires
        return self._get_from_provider(provider, expires=expires, key=key)

    def put(self, key: str, data, expires: int = None) -> None:  # pylint: disable=arguments-differ
        """
        Save data in Redis.

        :param key: Inner key of hash map
        :param data: data to be stored
        :param expires: (optional) TTL of the key
        """

        cache.hset(self.prefix, key, pickle.dumps(data))
        expires = self._default_ttl if expires is None else expires
        if expires:
            cache.expire(self.prefix, expires)

    def delete(self, keys: Iterable[str] = None) -> None:
        """
        Deletes hash map or it's inner keys

        :param keys: (optional) Specify which inner keys to delete
        """

        if keys:
            cache.hdel(self.prefix, *keys)
        else:
            cache.delete(self.prefix)


class _Queue:
    """Used in PUB/SUB"""

    def __init__(self, channel: str):
        self.channel = channel

    def push(self, data: Any) -> None:
        """
        Publish data into the channel

        :param data: Data to be published
        """
        cache.publish(self.channel, json.dumps(data))


class _CacheFactory:
    """All available application cache objects"""

    @cached_property
    def auth_states(self) -> _CacheMap:
        """
        Used for storing state for the period of redirection
        user to Authorization provider (Azure) and back.
        """
        return _CacheMap("imt:auth:user_login_state:v2")

    @cached_property
    def active_auth_tokens(self) -> _CacheMap:
        """
        Used for storing active sessions
        """
        return _CacheMap("imt:auth:v4")

    @cached_property
    def user_info(self) -> _CacheMap:
        """
        User info and permissions
        """
        return _CacheMap("imt:user_info:v1", default_ttl_sec=_get_ttl("user_info", "15m"))

    @cached_property
    def sites_configs(self) -> _DictCacheObject:
        """
        IMT weekly configs (site descriptions) by names
        """
        return _DictCacheObject("imt:sites:v7", default_ttl_sec=_get_ttl("sites", "1d"))

    @cached_property
    def forecast_upload(self) -> _CacheMap:
        """
        Stores the latest forecast by sites
        """
        # 30 days, to not lose the cached values in case if (for some reason) the upload was not started in 30 days.
        # Without the cached values we won't be able to correctly send tombstones (aka zero values)
        return _CacheMap("imt:forecast_upload:v2", default_ttl_sec=_get_ttl("forecast_upload", "30d"))

    @cached_property
    def hj_autostore(self) -> _CacheMap:
        """
        HJ autostores by site
        """
        return _CacheMap("imt:hj_autostore:v1", default_ttl_sec=_get_ttl("hj_autostore", "5m"))

    @cached_property
    def invalid_pimt_inventory_data(self) -> _CacheObject:
        """
        Stores invalid pimt inventory data
        """
        return _CacheObject("pimt:invalid_inventory_data:v1")

    @cached_property
    def invalid_pimt_grn_data(self) -> _CacheObject:
        """
        Stores invalid pimt grn data
        """
        return _CacheObject("pimt:invalid_grn_data:v1")

    @cached_property
    def imt_slack_alerts_states(self) -> _CacheMap:
        """
        Stores posted slack alerts to avoid spamming
        """
        return _CacheMap("imt:slack_alerts", default_ttl_sec=_get_ttl("slack_alerts", "1d"))

    @cached_property
    def job_info(self) -> _CacheMap:
        """
        Stores responses for GET jobs/<name> endpoint
        """
        return _CacheMap("imt:job_info", default_ttl_sec=_get_ttl("job_info", "12s"))

    @cached_property
    def job_step_info(self) -> _CacheMap:
        """
        Stores individual step info. It's used when querying all jobs statuses for the sync page
        """
        return _CacheMap("imt:job_step_info", default_ttl_sec=_get_ttl("job_step_info", "10s"))


class _QueueFactory:
    """All available queues for PUB/SUB"""

    @cached_property
    def user_notifications(self) -> _Queue:
        return _Queue("imt:notifications:v2")


class _LocalCacheFactory:
    """All available local caches"""

    @cached_property
    def brands(self) -> LocalCacheObject:
        """
        Brand names by id's
        """
        return LocalCacheObject("brands")


CACHES = _CacheFactory()
QUEUES = _QueueFactory()
LOCAL_CACHES = _LocalCacheFactory()
