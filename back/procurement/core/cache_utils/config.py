from typing import Any


class RedisConfig:
    def __init__(self, config: dict[str, Any]):
        self.host = config.get("host", "localhost")
        self.port = config.get("port", 6379)
        self.password = config.get("password")
        self.db = config.get("db", 1)

    def get_redis_url(self) -> str:
        """
        Build connection url from config object

        :return: Url for redis client
        """
        return f"redis://{self.host}:{self.port}/{self.db}" + (f"?password={self.password}" if self.password else "")
