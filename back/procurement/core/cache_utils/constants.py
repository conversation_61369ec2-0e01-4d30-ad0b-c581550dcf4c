import logging
from typing import <PERSON><PERSON><PERSON><PERSON>

from cachetools import Cache as LocalCache
from redis import ConnectionPool, Redis
from rq import Queue

from procurement.core.config_utils import config

from .config import RedisConfig

logger = logging.getLogger(__name__)


class WorkerQueues(NamedTuple):
    main_queue: Queue


cache_config = config["cache"]
REDIS_CONFIG = RedisConfig(cache_config)

cache_timeouts = cache_config.get("timeout", {})
pool = ConnectionPool(
    host=REDIS_CONFIG.host, port=REDIS_CONFIG.port, db=REDIS_CONFIG.db, password=REDIS_CONFIG.password
)

cache = Redis(connection_pool=pool, decode_responses=True)
broker = Redis(connection_pool=pool)
worker_queues = WorkerQueues(main_queue=Queue(connection=broker, default_timeout=cache_timeouts.get("rq_job", 600)))

local_cache = LocalCache(maxsize=10000)
