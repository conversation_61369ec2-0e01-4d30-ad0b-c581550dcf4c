import abc
import logging
import threading
from collections import defaultdict
from collections.abc import Iterable
from typing import Any, Generic, TypeVar, final

from procurement.core.exceptions.listener_errors import ConsumerClosedException, MessageUnprocessableException
from procurement.core.exceptions.worker_exceptions_handlers import worker_custom_exception_handler

from .runtime import SHUTDOWN_REQUESTED

logger = logging.getLogger(__name__)

__all__ = ("BaseListener", "BaseHandler", "BaseHandlerRegistry")

MessageT = TypeVar("MessageT")

KeyT = TypeVar("KeyT")


class BaseHandler(Generic[MessageT]):
    @abc.abstractmethod
    def handle(self, msg: MessageT) -> None:
        pass

    def close(self) -> None:
        pass


Handler = TypeVar("Handler", bound=BaseHandler)


class BaseHandlerRegistry(Generic[KeyT, Handler]):
    def __init__(self):
        self._mapping: dict[KeyT, set[BaseHandler]] = defaultdict(set)

    def register_handler(self, key: KeyT, handler: <PERSON><PERSON><PERSON><PERSON>) -> None:
        self._mapping[key].add(handler)

    def get_handler(self, key: KeyT) -> set[BaseHandler]:
        return self._mapping[key]

    def is_empty(self) -> bool:
        return len(self._mapping) == 0

    def all_keys(self) -> Iterable:
        return self._mapping.keys()

    def close(self):
        for handlers in self._mapping.values():
            for handler in handlers:
                handler.close()


class BaseListener(Generic[MessageT]):
    def __init__(self):
        self.exceptions_handler = worker_custom_exception_handler
        self.handler_registry: BaseHandlerRegistry | None = None
        self._parallel = False

    @final
    def run(self, parallel: bool = True):
        logger.info("The %s application is listening", self.__class__.__name__)
        self._parallel = parallel
        if parallel:
            threading.Thread(target=self._start_listen).start()
        else:
            self._start_listen()

    def _start_listen(self):
        class_name = self.__class__.__name__
        self._subscribe()
        while True:
            try:
                msg = self._get_message()
                if msg is not None:
                    logger.debug("Message got %s", class_name)
                    self._process_message(msg)
            except ConsumerClosedException:
                logger.info("Listener closed, stopping listener thread %s", class_name)
                return
            except Exception as exc:
                self.exceptions_handler(exc)
                logger.critical(str(exc), exc_info=True)
            if SHUTDOWN_REQUESTED.is_set():
                logger.info("Closing %s listener", self.__class__.__name__)
                self.close()
                break

    def register_registry(self, registry: BaseHandlerRegistry) -> None:
        self.handler_registry = registry

    @abc.abstractmethod
    def close(self) -> None:
        pass

    @abc.abstractmethod
    def _subscribe(self) -> None:
        pass

    def _process_message(self, msg: MessageT) -> None:
        msg = self._pre_handle(msg)
        handlers = self.handler_registry.get_handler(self._get_key(msg))
        for handler in handlers:
            try:
                handler.handle(msg)
            except MessageUnprocessableException:
                continue
        self._post_handle()

    @abc.abstractmethod
    def _get_message(self) -> MessageT | None:
        pass

    @abc.abstractmethod
    def _get_key(self, msg: MessageT) -> Any:
        pass

    def _pre_handle(self, msg: MessageT) -> MessageT:
        return msg

    def _post_handle(self) -> None:
        pass
