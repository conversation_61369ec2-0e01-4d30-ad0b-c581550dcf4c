import abc
import logging
import pickle  # nosec B03

from procurement.core.listener import BaseHandler

from .events import BaseEvent

logger = logging.getLogger(__name__)


class RedisHandler(BaseHandler[BaseEvent]):
    def handle(self, msg: dict) -> None:
        deserialized_msg = pickle.loads(msg["data"])  # nosec B301
        logger.info("Got Redis message: %s", deserialized_msg)
        self._process(deserialized_msg)

    @abc.abstractmethod
    def _process(self, msg: BaseEvent):
        pass
