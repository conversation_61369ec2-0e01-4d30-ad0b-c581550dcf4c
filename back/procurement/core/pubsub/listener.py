import logging
import pickle  # nosec B403
from typing import Type

from redis import Redis
from redis.exceptions import ConnectionError as RedisConnectionError

from procurement.core import listener
from procurement.core.config_utils import config
from procurement.core.exceptions.listener_errors import ConsumerClosedException

from .events import BaseEvent
from .handlers import RedisHandler

logger = logging.getLogger(__name__)


class RedisEventListener(listener.BaseListener[dict]):
    def __init__(self, client: Redis):
        super().__init__()
        self.pubsub_config = config["pubsub"]
        self.client = client
        self.channel = None

    def close(self) -> None:
        self.channel.close()

    def _subscribe(self) -> None:
        self.channel = self.client.pubsub()
        self.channel.subscribe(self.pubsub_config["channel"])

    def _get_message(self) -> dict | None:
        try:
            return self.channel.get_message(ignore_subscribe_messages=True, timeout=self.pubsub_config["timeout"])
        except (<PERSON><PERSON><PERSON><PERSON>, ValueError, RedisConnectionError, RuntimeError) as exc:
            raise ConsumerClosedException() from exc

    def _get_key(self, msg: dict) -> BaseEvent:
        return type(pickle.loads(msg["data"]))  # nosec B301


class RedisHandlerRegistry(listener.BaseHandlerRegistry[Type[BaseEvent], RedisHandler]):
    pass
