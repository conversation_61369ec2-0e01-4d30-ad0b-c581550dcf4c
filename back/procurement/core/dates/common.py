from isoweek import Week

__YEAR_LAST_WEEK_CACHE = {}


def last_week_of_year(year: int) -> int:
    """
    Get last week of the year and cache the results of calculations.

    :param year: Year to search
    :return: Number of week
    """
    if not (last_week := __YEAR_LAST_WEEK_CACHE.get(year)):
        last_week = Week.last_week_of_year(year).week
        __YEAR_LAST_WEEK_CACHE[year] = last_week
    return last_week
