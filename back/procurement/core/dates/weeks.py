from collections.abc import Iterable
from datetime import date, datetime, timedelta
from functools import lru_cache
from typing import Any

from isoweek import Week

from .constants import Weekday

WEEK_FORMAT_TEMPLATE = "{}-W{:02}"
_YEAR_LAST_WEEK_CACHE = {}


def _is_valid_week(year: int, week: int) -> bool:
    """
    Check if week specification is valid (in range of year's weeks).
    """
    last_week = ScmWeek.last_week_of_year(year)
    return 0 < week <= last_week


class ScmWeekConfig:
    __slots__ = ("calendar_mon_shift", "length")

    def __init__(self, calendar_mon_shift: int, length: int):
        if length < 7:
            raise ValueError("Production week can't be shorter than calendar week")
        self.calendar_mon_shift = calendar_mon_shift
        self.length = length

    @property
    def overlap(self) -> int:
        return max(self.length - 7, 0)

    @property
    def start_weekday(self) -> Weekday:
        return Weekday.from_index(self.calendar_mon_shift)

    def __eq__(self, other):
        if not isinstance(other, ScmWeekConfig):
            return False
        return (self.calendar_mon_shift, self.length) == (other.calendar_mon_shift, other.length)

    def __hash__(self):
        return hash((self.calendar_mon_shift, self.length))


DEFAULT_WEEK_CONFIG = ScmWeekConfig(-5, 9)


@lru_cache(maxsize=10)
def _get_weekday_titles(
    week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG, *, extend_left: int = 0, extend_right: int = 0
) -> tuple[str, ...]:
    first_day = Weekday.MON + (week_config.calendar_mon_shift - extend_left)
    days_count = week_config.length + extend_right + extend_left
    titles = []
    for day_i in range(days_count):
        if day_i > 7 - 1:
            titles.append(str(first_day) + f" {day_i // 7 + 1}")
        elif day_i < days_count - 7:
            titles.append(str(first_day) + " 1")
        else:
            titles.append(str(first_day))
        first_day += 1
    return tuple(titles)


class ScmWeek:
    """Contains common week manipulation methods"""

    __slots__ = ("year", "week", "__hash")

    def __init__(self, year: int, week: int):
        if not _is_valid_week(year, week):
            raise ValueError(
                f"Incorrect SCM week. Attempted to instantiate {week} for year {year}, "
                f"when last week in this year - {Week.last_week_of_year(year).week}"
            )
        self.year = year
        self.week = week
        self.__hash = hash((self.year, self.week))

    @staticmethod
    @lru_cache(maxsize=54)
    def from_str(scm_week: str) -> "ScmWeek":
        """
        Generate ScmWeek from str representation ("2022-W14").
        """
        try:
            year, week = (int(x) for x in scm_week.split("-W"))
            return ScmWeek(year, week)
        except Exception as exc:
            raise ValueError(f"Invalid week format '{scm_week}'. Valid example '2022-W01'.") from exc

    @staticmethod
    @lru_cache(maxsize=54)
    def from_number(scm_week: int) -> "ScmWeek":
        """
        Generate ScmWeek from number representation (202214).
        """
        return ScmWeek(scm_week // 100, scm_week % 100)

    @staticmethod
    @lru_cache(maxsize=54)
    def from_short_number(scm_week: int) -> "ScmWeek":
        """
        Generate ScmWeek from number representation (2214).
        """
        return ScmWeek(2000 + scm_week // 100, scm_week % 100)

    def to_short_number(self) -> int:
        return (self.year - 2000) * 100 + self.week

    @staticmethod
    def is_valid_number(scm_week: Any) -> bool:
        """
        Check that week in correct number format (in range of weeks of the year).
        """
        try:
            int_week = int(scm_week)
            return _is_valid_week(int_week // 100, int_week % 100)
        except (TypeError, ValueError):
            return False

    @staticmethod
    def get_weekday_titles(
        week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG, *, extend_left: int = 0, extend_right: int = 0
    ) -> tuple[str, ...]:
        return _get_weekday_titles(week_config, extend_left=extend_left, extend_right=extend_right)

    @staticmethod
    def range(from_week: "ScmWeek", to_week: "ScmWeek") -> Iterable["ScmWeek"]:
        """
        Returns generator of weeks starting from `from_week` to `to_week`. Inclusive from both sides.
        """
        while from_week <= to_week:
            yield from_week
            from_week += 1

    @staticmethod
    def range_from_dates(
        date_from: date, date_to: date, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG
    ) -> Iterable["ScmWeek"]:
        """
        Generate list of weeks from dates starting from `from_week` to `to_week`.

        Note: Inclusive from both sides.
        """
        return ScmWeek.range(ScmWeek.from_date(date_from, week_config), ScmWeek.from_date(date_to, week_config))

    def to_date_range(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> tuple[date, date]:
        """
        Gets first and last non overlapping days of week.
        """
        return ScmWeek.get_first_day(self, week_config), ScmWeek.get_last_day(self, week_config)

    def to_production_date_range(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> tuple[date, date]:
        """
        Gets first and last days of production week.
        """
        return ScmWeek.get_first_day(self, week_config), ScmWeek.get_last_production_day(self, week_config)

    @staticmethod
    def from_date(_date: date | datetime | None, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> "ScmWeek":
        """
        Get ScmWeek for specific date.
        """
        if _date is None:
            _date = date.today()
        week = Week.withdate(_date - timedelta(days=week_config.calendar_mon_shift))
        return ScmWeek(week.year, week.week)

    @staticmethod
    def current_week(week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> "ScmWeek":
        """
        Get current week as ScmWeek.
        """
        return ScmWeek.from_date(datetime.now(), week_config)

    @staticmethod
    def max() -> "ScmWeek":
        """
        Get week from maximal date.
        """
        return ScmWeek.from_date(datetime.max - timedelta(days=366))

    @staticmethod
    def min() -> "ScmWeek":
        """
        Get week from minimal date.
        """
        return ScmWeek.from_date(datetime.min + timedelta(days=366))

    def next(self) -> "ScmWeek":
        """
        Get next week.
        """
        last_week = self.last_week_of_year(self.year)
        if self.week == last_week:
            return ScmWeek(year=self.year + 1, week=1)
        return ScmWeek(year=self.year, week=self.week + 1)

    def prev(self) -> "ScmWeek":
        """
        Get previous week.
        """
        if self.week == 1:
            last_week = self.last_week_of_year(self.year - 1)
            return ScmWeek(year=self.year - 1, week=last_week)
        return ScmWeek(year=self.year, week=self.week - 1)

    def __add__(self, other: int) -> "ScmWeek":
        return self._add_delta(other)

    def __sub__(self, other: int) -> "ScmWeek":
        return self._add_delta(-other)

    def _add_delta(self, delta) -> "ScmWeek":
        """
        Safely get week ahead or behind this week.

        :param delta: weeks ahead or weeks behind
        :return: ScmWeek behind or ahead of this week
        """
        is_increment = delta > 0
        delta_abs = abs(delta)
        last = self
        while delta_abs > 0:
            last = last.next() if is_increment else last.prev()
            delta_abs -= 1
        return last

    def __str__(self) -> str:
        return WEEK_FORMAT_TEMPLATE.format(self.year, self.week)

    def __int__(self) -> int:
        return self.to_number_format()

    def __repr__(self) -> str:
        return str(self)

    def __lt__(self, other) -> bool:
        if isinstance(other, str):
            other = ScmWeek.from_str(other)
        return self.year < other.year or self.year == other.year and self.week < other.week

    def __le__(self, other) -> bool:
        if isinstance(other, str):
            other = ScmWeek.from_str(other)
        return self.year < other.year or self.year == other.year and self.week <= other.week

    def __gt__(self, other) -> bool:
        if isinstance(other, str):
            other = ScmWeek.from_str(other)
        return self.year > other.year or self.year == other.year and self.week > other.week

    def __ge__(self, other) -> bool:
        if isinstance(other, str):
            other = ScmWeek.from_str(other)
        return self.year > other.year or self.year == other.year and self.week >= other.week

    def __eq__(self, other) -> bool:
        # TODO: remove auto casting from comparing methods
        if isinstance(other, str):
            other = ScmWeek.from_str(other)
        if not isinstance(other, ScmWeek):
            return False
        return self.year == other.year and self.week == other.week

    def __hash__(self):
        return self.__hash

    def to_number_format(self) -> int:
        """
        Convert to number format, e.g. 2020-W14 = 202014.

        :return: Number representation of ScmWeek
        """
        return self.year * 100 + self.week

    def get_first_day(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> date:
        return Week(self.year, self.week).monday() + timedelta(days=week_config.calendar_mon_shift)

    def get_last_day(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> date:
        """
        Returns last non overlapping day of the week
        """
        return self.get_first_day(week_config) + timedelta(days=6)

    def get_last_production_day(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> date:
        return self.get_first_day(week_config) + timedelta(days=week_config.length - 1)

    def week_days(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> list[date]:
        """
        Returns list of non overlapping days of this week
        """
        return self._date_range(self.get_first_day(week_config), self.get_last_day(week_config))

    def is_overlapped_by_previous_week(self, day: date, week_config: ScmWeekConfig) -> bool:
        return 0 <= (day - self.get_first_day(week_config)).days < week_config.overlap

    def production_days(self, week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG) -> list[date]:
        return self._date_range(self.get_first_day(week_config), self.get_last_production_day(week_config))

    @staticmethod
    def _date_range(first_day: date, last_day: date) -> list[date]:
        return [first_day + timedelta(days=i) for i in range((last_day - first_day).days + 1)]

    @staticmethod
    def last_week_of_year(year: int) -> int:
        """
        Get last week of the year and cache the results of calculations.

        :param year: Year to search
        :return: Number of week
        """
        if not (last_week := _YEAR_LAST_WEEK_CACHE.get(year)):
            last_week = Week.last_week_of_year(year).week
            _YEAR_LAST_WEEK_CACHE[year] = last_week
        return last_week
