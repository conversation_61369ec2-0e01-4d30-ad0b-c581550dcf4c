import math
from datetime import datetime

from .weeks import ScmWeek

QUARTER_FORMAT_TEMPLATE = "{}-Q{:01}"
QUARTERS_IN_YEAR = 4


class ScmQuarter:
    """Contains common quarter manipulation methods"""

    __slots__ = ("year", "quarter")

    def __init__(self, year: int, quarter: int):
        if quarter > QUARTERS_IN_YEAR or quarter < 1:
            raise ValueError(
                f"Incorrect SCM quarter. Attempted to instantiate {quarter} for year {year}, "
                f"when maximum quarters - 4"
            )
        self.year = year
        self.quarter = quarter

    def __str__(self) -> str:
        return QUARTER_FORMAT_TEMPLATE.format(self.year, self.quarter)

    def __repr__(self) -> str:
        return str(self)

    def __add__(self, other: int) -> "ScmQuarter":
        new_quarter = self.year * QUARTERS_IN_YEAR + self.quarter - 1 + other
        return ScmQuarter(new_quarter // QUARTERS_IN_YEAR, new_quarter % QUARTERS_IN_YEAR + 1)

    def __sub__(self, other: int) -> "ScmQuarter":
        return self.__add__(-other)

    def __eq__(self, other: "ScmQuarter") -> bool:
        if not isinstance(other, ScmQuarter):
            return False
        return self.year == other.year and self.quarter == other.quarter

    def __hash__(self):
        return hash((self.year, self.quarter))

    def __ge__(self, other) -> bool:
        return (self.year, self.quarter) >= (other.year, other.quarter)

    def __gt__(self, other) -> bool:
        return (self.year, self.quarter) > (other.year, other.quarter)

    @staticmethod
    def get_by_year(year: int) -> list["ScmQuarter"]:
        """
        Get quarters for specific year.

        :param year: Year to generate quarters
        :return: List of year's quarters
        """
        return [ScmQuarter(year=year, quarter=quarter) for quarter in range(1, 5)]

    @staticmethod
    def from_scm_week(scm_week: ScmWeek) -> "ScmQuarter":
        if scm_week.week == 53:
            quarter = QUARTERS_IN_YEAR
        else:
            quarter = math.ceil(scm_week.week / 13)
        return ScmQuarter(scm_week.year, quarter)

    @staticmethod
    def from_str(scm_quarter: str) -> "ScmQuarter":
        """
        Build quarter from str representation (2020-Q4)

        :param scm_quarter: String representation of the quarter
        :return: ScmQuarter
        """
        year, quarter = (int(x) for x in scm_quarter.split("-Q"))
        return ScmQuarter(year, quarter)

    def get_first_week(self) -> ScmWeek:
        """
        Get first week of this quarter.

        :return: First week
        """
        return ScmWeek(year=self.year, week=int((self.quarter - 1) * 13 + 1))

    def get_last_week(self) -> ScmWeek:
        """
        Get last week of this quarter.

        :return: Last week
        """
        last_week = ScmWeek.last_week_of_year(self.year)
        extra_week = self.quarter == QUARTERS_IN_YEAR and last_week == 53
        return ScmWeek(year=self.year, week=int(self.quarter * 13 + extra_week))

    def get_weeks(self) -> list[ScmWeek]:
        """
        Get weeks in this quarter.

        :return: List of quarter's weeks
        """
        return list(ScmWeek.range(self.get_first_week(), self.get_last_week()))

    @staticmethod
    def get_quarter_range(from_quarter: "ScmQuarter", to_quarter: "ScmQuarter") -> list["ScmQuarter"]:
        """
        Generate list of quarters from ScmQuarter starting from `from_quarter` to `to_quarter`.

        Note: Inclusive from both sides.
        """
        result = []
        while from_quarter <= to_quarter:
            result.append(from_quarter)
            from_quarter += 1
        return result


def get_weeks_for_quarters() -> dict[ScmQuarter, list[ScmWeek]]:
    quarters = ScmQuarter.get_quarter_range(
        from_quarter=ScmQuarter(year=2020, quarter=1), to_quarter=ScmQuarter(year=datetime.today().year + 1, quarter=4)
    )
    return {quarter: quarter.get_weeks() for quarter in quarters}
