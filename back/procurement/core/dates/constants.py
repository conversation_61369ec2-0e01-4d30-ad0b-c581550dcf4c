from __future__ import annotations

from enum import IntEnum

_WEEKDAYS = ("Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday")


class Weekday(IntEnum):
    MON, TUE, WED, THU, FRI, SAT, SUN = range(7)

    def __str__(self):
        return _WEEKDAYS[self]

    def __add__(self, other):
        if not isinstance(other, int):
            raise ValueError()
        return Weekday((self._value_ + other) % 7)  # pylint: disable=no-member

    def __sub__(self, other):
        return self.__add__(-other)

    def numbered_title(self, number: int = 0) -> str:
        return f"{self} {number}" if number else str(self)

    @classmethod
    def from_index(cls, index: int) -> Weekday:
        if index < 0:
            index -= 7 * (index // 7)
        return cls(index % 7)
