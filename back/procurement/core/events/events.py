import dataclasses
from enum import Enum, auto
from typing import Any

from procurement.constants.hellofresh_constant import Domain
from procurement.core.dates import ScmWeek


@dataclasses.dataclass(frozen=True)
class BaseEvent:
    def to_dict(self) -> dict[str, Any]:
        return dataclasses.asdict(self)

    @classmethod
    def type(cls):
        return cls.__name__


@dataclasses.dataclass(frozen=True)
class SiteChangedEvent(BaseEvent):
    pass


@dataclasses.dataclass(frozen=True)
class NewDCAzureRoleEvent(BaseEvent):
    name: str


class ActionType(Enum):
    ADDED = auto()
    UPDATED = auto()
    DELETED = auto()


@dataclasses.dataclass(frozen=True)
class CommentEvent(BaseEvent):
    domain: Domain
    week: ScmWeek
    brand: str | None
    site: str
    item_key: str
    action_type: ActionType
    payload: str | None = None


@dataclasses.dataclass(frozen=True)
class SkuCommentEvent(CommentEvent):
    pass


@dataclasses.dataclass(frozen=True)
class WhSkuCommentEvent(CommentEvent):
    pass


@dataclasses.dataclass(frozen=True)
class PoCommentEvent(CommentEvent):
    pass


@dataclasses.dataclass(frozen=True)
class WCCEvent(BaseEvent):
    action_type: ActionType
    sku_code: str
    po_number: str
    brand: str
    site: str
    week: ScmWeek
    count_by_sku: int = None
    is_last_po: bool = False


@dataclasses.dataclass(frozen=True)
class ManualFormScheduleExportEvent(BaseEvent):
    week: ScmWeek


@dataclasses.dataclass(frozen=True)
class ReplenishmentCommentEvent(BaseEvent):
    action_type: ActionType
    region: str
    sku_code: str
    text: str | None = None


@dataclasses.dataclass(frozen=True)
class NetworkDepletionCommentEvent(BaseEvent):
    action_type: ActionType
    week: ScmWeek


@dataclasses.dataclass(frozen=True)
class BulkSkusChangedEvent(BaseEvent):
    pass
