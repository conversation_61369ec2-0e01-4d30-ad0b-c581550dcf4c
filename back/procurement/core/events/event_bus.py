from typing import Any, Callable, Type

import event_bus as bus_lib

from .events import Base<PERSON><PERSON>
from .exceptions import NoListenerException

_event_bus: bus_lib.EventBus = bus_lib.EventBus()


class _Listener:
    def __init__(self, event_type: str):
        self.__event_type = event_type

    def subscribe(self) -> Callable[[Callable], Any]:
        return _event_bus.on(self.__event_type)

    def publish(self, event: BaseEvent) -> Any:
        return _event_bus.emit(self.__event_type, event)


class _EventBus:
    def __init__(self):
        self.__mapping: dict[Type[BaseEvent], _Listener] = {}

    def publish(self, event: BaseEvent) -> Any:
        event_type = type(event)

        if event_type not in self.__mapping:
            raise NoListenerException(event)

        return self.__mapping[type(event)].publish(event)

    def subscribe(self, event_type: Type[BaseEvent]) -> Callable[[Callable], Any]:
        if event_type not in self.__mapping:
            self.__mapping[event_type] = _Listener(event_type.type())

        return self.__mapping[event_type].subscribe()


EVENT_BUS = _EventBus()
