from decimal import Decimal
from typing import NamedTuple, Type<PERSON>lia<PERSON>, TypeVar

# pylint: disable=invalid-name
# TODO: replace usage of basic types
SKU_ID: TypeAlias = int
SKU_CODE: TypeAlias = str
SKU_NAME: TypeAlias = str
SKU_UUID: TypeAlias = str
PO_UUID: TypeAlias = str
PO_NUMBER: TypeAlias = str
SUPPLIER: TypeAlias = str
ORDER_NUMBER: TypeAlias = str
PURCHASING_CATEGORY_NAME: TypeAlias = str
COMMODITY_GROUP_NAME: TypeAlias = str
LOT_CODE: TypeAlias = str
CATEGORY_NAME: TypeAlias = str
BRAND: TypeAlias = str
MARKET: TypeAlias = str
SITE: TypeAlias = str
EMERGENCY_REASON: TypeAlias = str
DAY_INDEX: TypeAlias = int
PROD_TYPE: TypeAlias = str
PO_SKU_KEY: TypeAlias = tuple[PO_NUMBER, SKU_CODE]
WH_CODE: TypeAlias = str
HJ_NAME: TypeAlias = str
BOB_CODE: TypeAlias = str
PIMT_REGION: TypeAlias = str
PACKAGED_SKU_CODE: TypeAlias = str
BULK_SKU_CODE: TypeAlias = str
UNITS = TypeVar("UNITS", int, Decimal)
RECIPE_CODE: TypeAlias = str
MEAL_NUMBER: TypeAlias = str
OT_DC: TypeAlias = str
MULTI_BRAND_DCS: TypeAlias = str
CONSOLIDATED_SITE: TypeAlias = str
ROLE: TypeAlias = str
DEMAND_PIPELINE: TypeAlias = str
PCK_REGION: TypeAlias = str
MANUFACTURED_CODE: TypeAlias = str
MANUFACTURED_UUID: TypeAlias = str


class PoSku(NamedTuple):
    po_number: PO_NUMBER
    sku_code: SKU_CODE
