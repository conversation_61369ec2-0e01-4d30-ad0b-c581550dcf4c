import os
import time

from procurement.constants.hellofresh_constant import APP_TIMEZONE

from . import log, metrics, prepare_metrics, runtime
from .cache_utils import initialize as redis_init
from .config_utils import AppType, app_type
from .migration import utils as migration_utils


def init_all():
    init_timezone()

    runtime.set_signals()

    log.init_logger()
    init_metrics()

    init_db()

    redis_init.init_redis()


def init_db():
    if app_type in {AppType.BACK, AppType.WORKER, AppType.CONSUMER}:
        migration_utils.apply_migration()


def init_timezone():
    if hasattr(time, "tzset"):
        os.environ["TZ"] = str(APP_TIMEZONE)
        time.tzset()


def init_metrics():
    metrics.override_value_class()

    prepare_metrics.prepare_metrics_env(cleanup=False)
    metrics.init_registry()
