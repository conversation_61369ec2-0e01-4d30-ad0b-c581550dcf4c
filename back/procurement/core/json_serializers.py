import datetime
from typing import I<PERSON>, Any, AnyStr

import simplejson
from flask.app import <PERSON><PERSON><PERSON>rovider


def default(obj: Any):
    if isinstance(obj, datetime.datetime):
        return obj.replace(microsecond=0).isoformat()
    if isinstance(obj, datetime.date):
        return obj.isoformat()
    raise TypeError()


class SimpleJsonProvider(JSONProvider):
    def dumps(self, obj: Any, **kwargs: Any) -> str:
        return simplejson.dumps(obj, default=default)

    def loads(self, s: str | bytes, **kwargs: Any) -> Any:
        return simplejson.loads(s)

    def dump(self, obj: Any, fp: IO[str], **kwargs: Any) -> None:
        return simplejson.dump(obj, fp, default=default)

    def load(self, fp: IO[AnyStr], **kwargs: Any) -> Any:
        return simplejson.load(fp)
