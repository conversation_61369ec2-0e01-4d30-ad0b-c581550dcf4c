import logging
import os
import shutil

from .config_utils import app_type

MULTIPROC_DIR = "prometheus_multiproc_dir"
logger = logging.getLogger(__name__)


def prepare_metrics_env(cleanup: bool = False):
    metrics_dir = "./metrics" + f"-{app_type}" if app_type else ""
    metrics_dir = os.path.abspath(metrics_dir)
    if MULTIPROC_DIR not in os.environ:
        # TODO maybe tempdir
        os.environ[MULTIPROC_DIR] = metrics_dir
    if not os.path.isdir(metrics_dir):
        os.mkdir(metrics_dir)

    # Cleanup metrics folder.
    # It should happen only 1 time before the app starts and not happen on the worker starts.
    if cleanup:
        metrics_dir = os.environ[MULTIPROC_DIR]
        logger.info("Cleanup metrics directory %s", metrics_dir)
        print(f"Cleanup metrics directory {metrics_dir}")  # Print for gunicorn
        for filename in os.listdir(metrics_dir):
            file_path = os.path.join(metrics_dir, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as exc:
                print("Failed to delete %s. Reason: %s", file_path, exc)
