from procurement.core.exceptions.base_errors import ApiError


class ConnectionException(ApiError):
    def __init__(self, msg: str):
        super().__init__("Connection", msg, 404)


class UnauthorizedException(ApiError):
    def __init__(self):
        super().__init__("Unauthorized", 401)


class DownstreamServiceException(ApiError):
    def __init__(self, msg: str):
        super().__init__("Downstream Service", msg, 500)


class OTTokenError(ApiError):
    def __init__(self, msg: str):
        super().__init__("OT token", msg, 404)
