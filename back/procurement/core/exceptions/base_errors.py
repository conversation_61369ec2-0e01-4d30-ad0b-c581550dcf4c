import logging

logger = logging.getLogger(__name__)


class ApiError(Exception):
    status_code = 500

    def __init__(self, prefix, message, status_code=None, data=None):
        Exception.__init__(self, message)
        self.data = data
        if status_code is not None:
            self.status_code = status_code
        logger.warning("%s error : %s", prefix, message)

    def to_dict(self):
        result = {"error": str(self)}
        if self.data:
            result["data"] = self.data
        return result


class IllegalStateError(ApiError):
    def __init__(self, message="Illegal state", status_code=500):
        super().__init__("Code", message, status_code)


class NonCriticalWrapperException(Exception):
    def __init__(self, origin_exception):
        self.origin_exception = origin_exception


class NestedException(Exception):
    def __init__(self, uuid: str, from_exc: Exception):
        super().__init__(f"Incident {uuid}. \n Exception {from_exc}")
