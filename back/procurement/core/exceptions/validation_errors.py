from procurement.core.exceptions.base_errors import ApiError


class InputParamsValidationException(ApiError):
    def __init__(self, message):
        super().__init__("Input validator", message, 400)


class BatchValidationException(ApiError):
    def __init__(self, message, data):
        super().__init__("Input validator", message, 400, data)

    @staticmethod
    def build_error(index: int, columns: dict):
        """
        :param index: UI row index
        :param columns: dict of colName -> message
        """
        return {"row": index, "cols": columns}


class InvalidDataException(ValueError):
    pass


class JobArgumentValidationException(ApiError):
    def __init__(self, message):
        super().__init__("Job Argument Validation", message, 400)


class ScmWeekConfig(ApiError):
    def __init__(self, message):
        super().__init__("Job Argument Validation", message, 400)
