from procurement.core.exceptions.base_errors import ApiError


class DbBusinessError(ApiError):
    def __init__(self, message, status_code=None):
        super().__init__("Resource", message, status_code)


class ResourceNotFound(DbBusinessError):
    def __init__(self, msg: str):
        super().__init__(msg, 404)


class ForbidResource(DbBusinessError):
    def __init__(self, msg: str):
        super().__init__(msg, 403)


class HjOperationalError(Exception):
    pass
