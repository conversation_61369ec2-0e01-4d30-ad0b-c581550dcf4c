import inspect
from typing import Named<PERSON><PERSON>le, Type

from confluent_kafka.cimpl import KafkaException
from redis import RedisError
from rq.timeouts import JobTimeoutException

from procurement.client.mailing import SendGridException
from procurement.core.exceptions.base_errors import NestedException, NonCriticalWrapperException
from procurement.core.exceptions.db_errors import HjOperationalError
from procurement.core.exceptions.gsheets import GSheetValidationError
from procurement.core.metrics import ApplicationMetrics


class ExceptionType(NamedTuple):
    exc_type: Type
    service_name: str


EXCEPTION_HANDLERS = (
    ExceptionType(exc_type=RedisError, service_name="redis"),
    ExceptionType(exc_type=HjOperationalError, service_name="hj"),
    ExceptionType(exc_type=KafkaException, service_name="kafka"),
    ExceptionType(exc_type=GSheetValidationError, service_name="sheet"),
    ExceptionType(exc_type=JobTimeoutException, service_name="job"),
    ExceptionType(exc_type=SendGridException, service_name="sendgrid"),
)


def worker_custom_exception_handler(exc_value):
    frame = inspect.trace()[-1]
    mod = inspect.getmodule(frame[0])
    modname = mod.__name__ if mod else frame[1]

    if isinstance(exc_value, NestedException):
        exc_value = exc_value.__cause__

    if isinstance(exc_value, NonCriticalWrapperException):
        exc_value = exc_value.origin_exception
        exc_handler = ApplicationMetrics.non_critical_exceptions
    else:
        exc_handler = ApplicationMetrics.critical_exceptions

    service = next((item.service_name for item in EXCEPTION_HANDLERS if isinstance(exc_value, item.exc_type)), modname)
    exc_handler(service=service, exception=exc_value)
