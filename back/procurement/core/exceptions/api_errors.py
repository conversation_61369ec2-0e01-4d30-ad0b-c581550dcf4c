from procurement.core.exceptions.base_errors import ApiError
from procurement.core.request_utils import context


class InvalidRequestException(ApiError):
    def __init__(self, message):
        super().__init__("Invalid request", message, 400)


class UsersError(ApiError):
    def __init__(self, message, status_code=None):
        super().__init__("Users", message, status_code)


class UserNotExisting(UsersError):
    def __init__(self):
        super().__init__("This user does not exist.", 400)


class InvalidPermission(UsersError):
    def __init__(self, permission: str, brand: str = None, site: str = None):
        user_email = context.get_request_context().user_info.email
        brand_part = f" brand {brand}"
        site_part = f" site {site}" if site else ""
        for_part = " for" if brand_part or site_part else ""

        super().__init__(
            f"User {user_email} do not have {permission} permission{for_part}{brand_part}{site_part}.", 403
        )


class InvalidWarehouse(ApiError):
    def __init__(self, wh_code):
        super().__init__("PIMT", f"{wh_code} is invalid Warehouse name", 400)
