import logging
import signal
import threading

logger = logging.getLogger(__name__)

SHUTDOWN_REQUESTED = threading.Event()

_default_sig_handlers = {}


def _request_shutdown_handler(sig, frame):
    logger.info("Requested shutdown...")
    SHUTDOWN_REQUESTED.set()
    _default_sig_handlers[sig](sig, frame)


def set_signals():
    for interrupt_signal in [signal.SIGTERM, signal.SIGINT, signal.SIGQUIT, signal.SIGHUP]:
        _default_sig_handlers[interrupt_signal] = signal.getsignal(interrupt_signal)
        signal.signal(interrupt_signal, _request_shutdown_handler)
