import contextlib

from confluent_kafka import Producer

from procurement.core.config_utils import config
from procurement.core.metrics import ApplicationMetrics

from . import config as kafka_config


class CustomProtobufProducer:
    """The class represents a Kafka protobuf producer"""

    def __init__(self, topic_cfg_name: str):
        self.plain_producer_config = kafka_config.build_lib_config(config["kafka"])
        # removing avro schema related params since it's using protobuf instead
        self.plain_producer_config.pop("schema.registry.url", None)
        self.plain_producer_config.pop("basic.auth.user.info", None)
        self.plain_producer_config.pop("basic.auth.credentials.source", None)
        self._producer = None
        self._topic = config["kafka"]["topics"]["produce"].get(topic_cfg_name)

    @property
    def producer(self) -> Producer:
        if not self._producer:
            self._producer = Producer(self.plain_producer_config)
        return self._producer

    def produce_raw(self, key, value) -> None:
        """Produces (sends) key-value pair to Kafka"""
        if not self._topic:
            return
        ApplicationMetrics.count_kafka_metrics("produce", self._topic)
        self.producer.produce(topic=self._topic, key=key, value=value)

    def flush(self) -> None:
        if self._topic:
            self.producer.flush()

    @contextlib.contextmanager
    def producing(self) -> "CustomProtobufProducer":
        try:
            yield self
        finally:
            self.flush()


inv_demand_forecast = CustomProtobufProducer("forecast_upload")
