import dataclasses
from typing import Any, Callable


def default_converter(value: Any) -> str:
    return value


@dataclasses.dataclass
class ConfigProperty:
    """
    Kafka config properties name mapper
    """

    new_name: str
    converter: Callable[[Any], str] = default_converter


CONFIG_MAPPER: dict[str, ConfigProperty] = {
    "auto_offset_reset": ConfigProperty(new_name="auto.offset.reset"),
    "bootstrap_servers": ConfigProperty(new_name="bootstrap.servers", converter=",".join),
    "client_id": ConfigProperty(new_name="client.id"),
    "group_id": ConfigProperty(new_name="group.id"),
    "sasl_mechanism": ConfigProperty(new_name="sasl.mechanism"),
    "sasl_plain_username": ConfigProperty(new_name="sasl.username"),
    "sasl_plain_password": ConfigProperty(new_name="sasl.password"),
    "security_protocol": ConfigProperty(new_name="security.protocol"),
    "ssl_cafile": ConfigProperty(new_name="ssl.ca.location"),
    "enable_auto_commit": ConfigProperty(new_name="enable.auto.commit"),
    "auto_commit_interval_ms": ConfigProperty(new_name="auto.commit.interval.ms"),
}


def build_lib_config(app_config: dict) -> dict:
    """Map config properties to kafka.conf names
    :param app_config: Kafka initialization config
    :return: config in kafka.conf format
    """
    kafka_config = {}
    for key, value in app_config.items():
        if key in CONFIG_MAPPER:
            mapper = CONFIG_MAPPER[key]
            kafka_config[mapper.new_name] = mapper.converter(value)
    return kafka_config
