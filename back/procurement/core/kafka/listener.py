import logging

from confluent_kafka import Consumer, Message

from procurement.core import listener
from procurement.core.config_utils import config
from procurement.core.exceptions import listener_errors
from procurement.core.metrics import ApplicationMetrics

from . import config as kafka_config

logger = logging.getLogger(__name__)


class _KafkaListener(listener.BaseListener[Message]):
    """The class represents a Kafka listener"""

    def __init__(self, consumer_kwargs: dict):
        super().__init__()
        self.consumer = None
        self.consumer_kwargs = consumer_kwargs
        self.consume_timeout = config["kafka"]["timeouts"]["consume"]

    def close(self):
        """Closes kafka consumer"""
        self.consumer.close()
        self.handler_registry.close()

    def reset_offset(self, topic_to_reset: str, read_last: int = None):
        """Resets the offset for the specific topic

        :param topic_to_reset: Name of the topic
        :param read_last: offset from the current position (reread N last messages)
        """
        for topic_partition in self.consumer.assignment():
            if topic_partition.topic == topic_to_reset:
                if offsets := self.consumer.get_watermark_offsets(topic_partition, timeout=self.consume_timeout):
                    topic_partition.offset = (
                        max(offsets[0], offsets[1] - read_last) if read_last is not None else offsets[0]
                    )
                    self.consumer.seek(topic_partition)

    def _subscribe(self) -> None:
        """Subscribes to registered topics"""
        self.consumer = Consumer(self.consumer_kwargs)
        self.consumer.subscribe(topics=list(self.handler_registry.all_keys()))

    def _get_message(self) -> Message:
        """
        Poll the message and return if there is one

        :return: new Message object
        """
        try:
            return self.consumer.poll(self.consume_timeout)
        except RuntimeError as exc:
            raise listener_errors.ConsumerClosedException() from exc

    def _pre_handle(self, msg: Message) -> Message:
        """
        Check whether there were errors during the polling or connection

        :param msg: Message
        :return: Message
        """
        if msg.error():
            logger.error("Consumer error: %s", msg.error())
            raise listener_errors.ErrorMessageException()

        ApplicationMetrics.count_kafka_metrics("receive", msg.topic())
        return msg

    def _get_key(self, msg: Message) -> str:
        """
        Returns the name of the message's topic

        :return: Topic's name
        """
        return msg.topic()


kafka_listener = _KafkaListener(kafka_config.build_lib_config(config["kafka"]))
