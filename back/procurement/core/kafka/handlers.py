import abc
import logging
from functools import cache

from confluent_kafka import Message
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.serialization import MessageField, SerializationContext, StringDeserializer
from google.protobuf.message import DecodeError

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core import listener
from procurement.core.config_utils import config
from procurement.core.exceptions.listener_errors import MessageUnprocessableException

logger = logging.getLogger(__name__)


@cache
def get_schema_registry_client() -> SchemaRegistryClient:
    """
    Returns or initializes the SchemaRegistryClient object

    :return: SchemaRegistryClient object
    """
    return SchemaRegistryClient({"url": config["kafka"].get("schema_registry_url")})


def parse_protobuf(value: bytes, protobuf_class, value_type: str):
    """
    Deserialize protobuf string to object

    :param value: Serialized protobuf object
    :param protobuf_class: Class to use in parsing
    :param value_type: Message field (key or value)
    """
    obj = protobuf_class()
    try:
        obj.ParseFromString(value)
    except DecodeError as exc:
        logger.warning("Cant parse %s, value %s", value_type, value)
        raise MessageUnprocessableException() from exc
    return obj


class KafkaHandler(listener.BaseHandler[Message]):
    """Base class for Kafka handlers"""

    _ignore_empty_messages = True

    def __init__(self, market: str = MARKET_US):
        self.market = market

    def handle(self, msg: Message) -> None:
        """
        Handles the income message

        :param msg: Income message
        """
        if not self._is_header_accepted(msg):
            return
        msg.set_key(self.deserialize_key(msg.key(), SerializationContext(msg.topic(), MessageField.KEY)))
        if not self._is_key_accepted(msg):
            return
        if not msg.value() and self._ignore_empty_messages:
            return
        msg.set_value(self.deserialize_value(msg.value(), SerializationContext(msg.topic(), MessageField.VALUE)))
        logger.debug("Got Kafka message:topic=%s, key=%s, value=%s", msg.topic(), msg.key(), msg.value())
        self._process(msg)

    def _is_header_accepted(self, msg: Message) -> bool:  # pylint: disable=unused-argument
        return True

    def _is_key_accepted(self, msg: Message) -> bool:  # pylint: disable=unused-argument
        return True

    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        """
        Abstract method for message's key deserialization

        :param key: Message key to be deserialized
        :param ctx: Serialization context
        """
        return StringDeserializer()(key, ctx)

    @abc.abstractmethod
    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        """
        Abstract method for message value deserialization

        :param value: Message value to be deserialized
        :param ctx: Serialization context
        """

    @abc.abstractmethod
    def _process(self, msg: Message) -> None:
        """
        Abstract method for processing the message after deserialization

        :param msg: Deserialized message to be processed
        """


class KafkaHandlerRegistry(listener.BaseHandlerRegistry[str, KafkaHandler]):
    """Used as registry of Kafka handlers"""
