import logging

from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.request_utils import context

logger = logging.getLogger(__name__)


def is_week_readonly(week: ScmWeek) -> bool:
    if Permissions.IMT_MODIFY_OLD_FORM_V1.write in context.get_request_context().user_permissions.permissions:
        return False
    return not week or (week < ScmWeek.current_week() - 1)


def validate_week_for_edit(week: ScmWeek):
    if is_week_readonly(week):
        raise InputParamsValidationException("Only last previous week is available for edit")


def validate_id_for_edit(identifier: int):
    if identifier == -1:
        raise InputParamsValidationException("HJ Discards can't be edited")
