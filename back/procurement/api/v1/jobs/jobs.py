from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.exceptions.api_errors import InvalidPermission
from procurement.core.exceptions.validation_errors import JobArgumentValidationException
from procurement.managers.datasync import job_constants, management
from procurement.managers.datasync.framework.job_spec import JobSpec

from .schema.jobs import post_job


class JobsBase(base_api.BaseApi):
    permission = Permissions.GENERAL

    @staticmethod
    def _get_job_spec(key: str) -> JobSpec:
        job_spec: JobSpec = job_constants.JobsEnum[key].value
        if not job_spec:
            raise JobArgumentValidationException(f"There is no job named {key}")
        return job_spec


class Jobs(JobsBase):
    permission = Permissions.IMT_JOBS_DASHBOARD_V1

    def _verify_permission(self) -> None:
        user_info = self.request_context.user_permissions

        if (
            self.current_permission not in user_info.permissions
            and self.current_permission != Permissions.IMT_JOBS_DASHBOARD_V1.write
        ):
            raise InvalidPermission(self.current_permission)

    def get(self):
        return [
            self._build_response(key, job) for key, job in management.get_filtered_jobs().items() if not job.is_hidden
        ]

    @staticmethod
    def _build_response(key: str, item: JobSpec):
        runs = [
            {
                "lastUpdated": run["last_updated"],
                "status": run["status"],
                "jobName": run["name"],
                "arguments": run["arguments"],
                "inProgress": run["in_progress"],
            }
            for run in item.runs
        ]

        args = [
            {
                "key": arg.key,
                "label": arg.label,
                "description": arg.description,
                "type": arg.ui_type,
                "required": arg.required,
            }
            for arg in item.arguments
            if not arg.is_hidden
        ]

        return {
            "key": key,
            "name": item.name,
            "arguments": args,
            "runs": runs,
            "permissions": {"read": item.permission.read, "write": item.permission.write},
            "isSingle": not args,
            "dbNameTemplate": item.name_template,
        }

    def post(self):
        data = request.json
        validate(data, post_job)
        job_spec = self._get_job_spec(key=data["key"])

        user_info = {
            "email": self.request_context.user_info.email,
            "user_id": self.request_context.user_info.user_id,
        }
        data["arguments"].update(user_info)
        cleaned_arguments = job_spec.clean_arguments(data["arguments"])

        job_spec.execute_job(cleaned_arguments)


class JobsInfo(JobsBase):
    def get(self, key):
        job_spec = self._get_job_spec(key)

        cleaned_arguments = job_spec.clean_arguments(request.args)

        return management.get_info(job_spec, cleaned_arguments)
