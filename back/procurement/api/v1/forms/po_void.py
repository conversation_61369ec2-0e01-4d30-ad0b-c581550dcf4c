import datetime

from flask import request
from jsonschema import validate

from procurement.api import base_api, input_forms
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.managers.imt import po_void

from .schema import po_void_schema


class BasePOVoid(base_api.BaseApi):
    permission = Permissions.IMT_PO_VOID_V1

    @staticmethod
    def _parse_request(item: dict) -> po_void.PoVoidInput:
        return po_void.PoVoidInput(
            po_number=item["poNumber"],
            sku_name=item["skuName"],
            comment=item.get("comment"),
            dc=item["dc"],
            brand=item["brand"],
            ui_row_id=item["uiRowId"],
            last_updated=datetime.datetime.now(),
        )


class POVoid(BasePOVoid):
    @staticmethod
    def _build_response(item: PoVoidDto) -> dict:
        return {
            "id": item.id,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "poNumber": item.po_number,
            "dc": item.dc,
            "brand": item.brand,
            "comment": item.comment,
            "supplier": item.supplier,
            "userName": item.user,
            "timestamp": item.timestamp.isoformat(),
        }

    def delete(self):
        data = request.get_json()
        validate(data, po_void_schema.po_void_delete)
        item_id = data["id"]
        week = po_void.get_po_void_week_by_id(item_id)
        input_forms.validate_week_for_edit(week)
        po_void.delete_po_void(item_id)
        return None, 204

    def get(self):
        arguments = request.args
        validate(arguments, po_void_schema.po_void_get)
        week = ScmWeek.from_str(arguments["week"])
        voids = po_void.get_po_void_response(week)
        return {
            "data": [self._build_response(item) for item in voids],
            "readonlyWeek": input_forms.is_week_readonly(week),
        }

    def post(self):
        body = request.get_json()
        validate(body, po_void_schema.po_void_post)
        week = ScmWeek.from_str(body["week"])
        input_forms.validate_week_for_edit(week)
        po_void.add_po_void(items=[self._parse_request(item) for item in body["data"]], week=week)
        return None, 201

    def patch(self):
        data = request.get_json()
        validate(data, po_void_schema.po_void_patch)
        po_void.edit_po_void_comment(po_void_id=data["id"], comment=data["comment"])


class ValidatePOVoid(BasePOVoid):
    def post(self):
        data = request.get_json()
        validate(data, po_void_schema.po_void_post)
        week = ScmWeek.from_str(data["week"])
        input_forms.validate_week_for_edit(week)

        po_void.validate_void_pos(items=[self._parse_request(item) for item in data["data"]], week=week)
        return {"msg": "Items are validated"}, 200


class POVoidPOs(BasePOVoid):
    def get(self):
        validate(request.args, po_void_schema.po_void_pos)
        week = ScmWeek.from_str(request.args["week"])
        brand = request.args["brand"]
        site = request.args["dc"]

        return po_void.get_po_void_pos(week, brand, site)


class POVoidSkus(BasePOVoid):
    def get(self):
        validate(request.args, po_void_schema.po_void_skus)
        week = ScmWeek.from_str(request.args["week"])
        brand = request.args["brand"]
        site = request.args["dc"]
        po_number = request.args["po_number"]

        return po_void.get_po_void_skus(week, brand, site, po_number)
