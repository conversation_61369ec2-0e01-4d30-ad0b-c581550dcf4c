from datetime import datetime

from flask import request
from jsonschema import validate

from procurement.api import base_api, input_forms
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import FRONT_DATETIME_FORMAT
from procurement.core.dates import ScmWeek
from procurement.managers.forms import discarding

from .schema import discard_form as discard_schema


class BaseDiscard(base_api.BaseApi):
    permission = Permissions.IMT_DISCARD_V1

    @staticmethod
    def _parse_request(data: list[dict]) -> list[discarding.DiscardInput]:
        discards = []
        for item in data:
            discards.append(
                discarding.DiscardInput(
                    brand=item["brand"],
                    site=item["site"],
                    discard_time=datetime.strptime(item["discardedDateTime"], FRONT_DATETIME_FORMAT),
                    sku_name=item["skuName"],
                    num_units=int(item["numUnits"]),
                    comment=item.get("comment"),
                    ui_row_id=int(item["rowId"]),
                )
            )
        return discards


class Discard(BaseDiscard):
    def get(self):
        arguments = request.args
        validate(arguments, discard_schema.discard_get)
        week = ScmWeek.from_str(arguments["week"])
        result = discarding.get_discards_by_week(week)

        return {
            "data": [self.build_response_item(item) for item in result],
            "readonlyWeek": input_forms.is_week_readonly(week),
        }

    def post(self):
        body = request.get_json()
        validate(body, discard_schema.discard_post)
        input_forms.validate_week_for_edit(body["week"])
        discarding.add_discards(self._parse_request(body["data"]), ScmWeek.from_str(body["week"]))
        return None, 201

    def patch(self):
        data = request.get_json()
        validate(data, discard_schema.discard_update)
        discard_id = data.pop("id")
        input_forms.validate_id_for_edit(discard_id)
        week = discarding.get_discard_week_by_id(discard_id)
        input_forms.validate_week_for_edit(week)
        discarding.update_discard_item(discard_id, data)

    def delete(self):
        data = request.get_json()
        validate(data, discard_schema.discard_delete)
        discard_id = data["id"]
        input_forms.validate_id_for_edit(discard_id)
        week = discarding.get_discard_week_by_id(discard_id)
        input_forms.validate_week_for_edit(week)
        discarding.delete_discard(discard_id)

    @staticmethod
    def build_response_item(item: discarding.DiscardData):
        return {
            "id": item.discard_id,
            "user": item.user,
            "site": item.site,
            "skuName": item.sku_name,
            "category": item.category,
            "discardedTime": item.discarded_time,
            "quantity": item.quantity,
            "week": item.week,
            "brand": item.brand,
            "timestamp": item.timestamp,
            "comment": item.comment,
            "discardRate": item.discard_rate,
            "unitOfMeasure": str(item.unit_of_measure),
        }


class DiscardValidate(BaseDiscard):
    def post(self):
        """
        Validate Discard form request.
        :return: status of validation and list of errors if have
        """
        body = request.get_json()
        validate(body, discard_schema.discard_post)

        week = body["week"]
        input_forms.validate_week_for_edit(week)
        discarding.validate_data(
            week=ScmWeek.from_str(week),
            items=self._parse_request(body["data"]),
        )


class DiscardSkus(BaseDiscard):
    def get(self):
        validate(request.args, discard_schema.discard_skus_get)
        brand = request.args["brand"]
        search_key = request.args["searchKey"]
        return discarding.get_skus_for_autocomplete(brand, search_key)
