from flask import request
from jsonschema import validate

from procurement.api import input_forms
from procurement.api.base_api import BaseApi
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.pull_put import PullPutDto
from procurement.managers.imt import inventory

from .schema.pull_put_schema import (
    inventory_pull_put_args,
    inventory_pull_put_delete,
    inventory_pull_put_patch,
    inventory_pull_put_post,
    inventory_pull_put_skus_get,
)


class BasePullPut(BaseApi):
    permission = Permissions.IMT_PULL_PUT_V1

    def _parse_request(self, item: dict) -> inventory.PullPutInput:
        return inventory.PullPutInput(
            brand=item["brand"],
            dc=item["dc"],
            sku_name=item["skuName"],
            qty=item["qty"],
            comment=item.get("comment"),
            user_email=self.request_context.user_info.email,
            row=item.get("uiRowId"),
        )


class InventoryPullPut(BasePullPut):
    @staticmethod
    def _build_response(item: PullPutDto) -> dict:
        return {
            "id": item.inventory_id,
            "brand": item.brand,
            "dc": item.dc,
            "userEmail": item.user_email,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "qty": item.qty,
            "comment": item.comment,
            "updatedAt": item.updated_at.isoformat(),
            "unitOfMeasure": str(item.unit_of_measure),
        }

    def get(self):
        validate(request.args, inventory_pull_put_args)
        week = ScmWeek.from_str(request.args["week"])
        items = inventory.get_pull_put_items(week=week, fetch_sku=True)
        return {
            "data": [self._build_response(item) for item in items],
            "readonlyWeek": input_forms.is_week_readonly(week),
        }

    def post(self):
        body = request.get_json()
        validate(body, inventory_pull_put_post)
        week = ScmWeek.from_str(body["week"])
        input_forms.validate_week_for_edit(week)
        inventory.add_pull_put(items=[self._parse_request(item) for item in body["data"]], week=week)

        return None, 201

    def patch(self):
        body = request.get_json()
        validate(body, inventory_pull_put_patch)
        item_id = body["id"]
        week = inventory.get_pull_put_week_by_id(item_id)
        input_forms.validate_week_for_edit(week)

        item = inventory.edit_pull_put(item_id, new_qty=body["qty"], new_comment=body["comment"])
        return self._build_response(item)

    def delete(self):
        body = request.get_json()
        validate(body, inventory_pull_put_delete)
        item_id = body["id"]
        week = inventory.get_pull_put_week_by_id(item_id)

        input_forms.validate_week_for_edit(week)
        inventory.delete_pull_put(item_id, week)

        return None, 204


class ValidatePullPut(BasePullPut):
    def post(self):
        body = request.get_json()
        validate(body, inventory_pull_put_post)
        week = ScmWeek.from_str(body["week"])
        input_forms.validate_week_for_edit(week)
        info = inventory.validate_batch_pull_put([self._parse_request(item) for item in body["data"]], week)
        return info


class PullPutSkus(BasePullPut):
    def get(self):
        validate(request.args, inventory_pull_put_skus_get)
        brand = request.args["brand"]
        search_key = request.args["searchKey"]

        return inventory.get_skus(brand, search_key)
