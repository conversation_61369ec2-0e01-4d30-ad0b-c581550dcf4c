from datetime import date

from flask import request
from jsonschema import validate

from procurement.api import base_api, input_forms
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import FRONT_FORMAT
from procurement.core.dates import ScmWeek
from procurement.core.typing import PoSku
from procurement.managers.forms import receipt_override
from procurement.repository.inventory.receipt_override import ReceiptOverrideKey

from .schema.receipt_override_form import (
    receipt_override_delete,
    receipt_override_get,
    receipt_override_patch,
    receipt_override_pos,
    receipt_override_post_body,
    receipt_override_skus,
)


class BaseReceiptOverride(base_api.BaseApi):
    permission = Permissions.IMT_RECEIPT_OVERRIDE_V1

    @staticmethod
    def _parse_request(item: dict) -> receipt_override.ReceiptOverrideInput:
        return receipt_override.ReceiptOverrideInput(
            week=item.get("week"),
            brand=item["brand"],
            dc=item["dc"],
            po_number=item["poNumber"],
            sku_code=item["skuCode"],
            sku_name=item.get("skuName"),
            adjusted_received_volume=item.get("adjustedReceivedVolume"),
            adjusted_received_cases=item.get("adjustedReceivedCases"),
            adjusted_receiving_date=(
                date.fromisoformat(item.get("adjustedReceivingDate")) if item.get("adjustedReceivingDate") else None
            ),
            ui_row_id=item.get("uiRowId"),
            comment=item.get("comment"),
        )


class ReceiptOverride(BaseReceiptOverride):
    @staticmethod
    def _build_response(item: receipt_override.ReceiptOverrideData):
        return {
            "entryType": item.entry_type,
            "brand": item.brand,
            "dc": item.site,
            "poNumber": item.po_number,
            "supplierName": item.supplier_name,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "quantityReceived": item.quantity_received,
            "adjustedReceivedVolume": item.adjusted_received_volume,
            "differenceReceiptVsAdj": item.difference_receipt_vs_adj,
            "casesReceived": item.cases_received,
            "adjustedReceivedCases": item.adjusted_received_cases,
            "caseDifferenceReceiptVsAdj": item.case_difference_receipt_vs_adj,
            "adjustedReceivingDate": (
                item.adjusted_receiving_date.strftime(FRONT_FORMAT) if item.adjusted_receiving_date else None
            ),
            "submittedBy": item.submitted_by,
            "timestamp": item.timestamp.isoformat(),
            "comment": item.comment,
        }

    def get(self):
        """
        Get Receipt Override information
        :return: Receipt Override info
        """
        arguments = request.args
        validate(arguments, receipt_override_get)
        week = ScmWeek.from_str(arguments["week"])

        return {
            "data": [self._build_response(item) for item in receipt_override.get_data(week)],
            "readonlyWeek": input_forms.is_week_readonly(week),
        }

    def post(self):
        """
        Submit Receipt Override form request. Raises APIError if validation fails or smth goes wrong
        :return: message of error or success and status code
        """
        body = request.get_json()
        validate(body, receipt_override_post_body)
        week = ScmWeek.from_str(body["week"])
        input_forms.validate_week_for_edit(week)
        receipt_override.add_items(week=week, items=[self._parse_request(item) for item in body["data"]])
        return None, 201

    def patch(self):
        """
        Edit Receipt Override Adjusted Received Volume. Raises APIError if validation fails or smth goes wrong
        :return: message of error or success and status code
        """
        body = request.get_json()
        validate(body, receipt_override_patch)

        data_input = self._parse_request(body)
        week = ScmWeek.from_str(data_input.week)
        input_forms.validate_week_for_edit(week)

        receipt_override.edit_item(
            override_key=ReceiptOverrideKey(
                week=week,
                brand=data_input.brand,
                site=data_input.dc,
                po_number=data_input.po_number,
            ),
            sku_code=data_input.sku_code,
            qty=data_input.adjusted_received_volume,
            cases=data_input.adjusted_received_cases,
            receiving_date=data_input.adjusted_receiving_date,
            comment=data_input.comment,
        )

    def delete(self):
        """
        Delete Receipt Override row by (week, brand, dc, po number, sku name)
        :return: message of error or success and status code
        """
        body = request.get_json()
        validate(body, receipt_override_delete)
        data_input = self._parse_request(body)
        week = ScmWeek.from_str(data_input.week)
        input_forms.validate_week_for_edit(week)

        receipt_override.delete_item(
            week=week,
            brand=data_input.brand,
            dc=data_input.dc,
            po_sku=PoSku(sku_code=data_input.sku_code, po_number=data_input.po_number),
        )
        return None, 204


class ReceiptOverrideValidate(BaseReceiptOverride):
    def post(self):
        """
        Validate Receipt Override form request.
        :return: status of validation and list of errors if have
        """
        body = request.get_json()
        validate(body, receipt_override_post_body)

        week = ScmWeek.from_str(body["week"])
        input_forms.validate_week_for_edit(week)
        receipt_override.validate_data(week=week, items=[self._parse_request(item) for item in body["data"]])


class ReceiptOverridePOs(BaseReceiptOverride):
    def get(self):
        validate(request.args, receipt_override_pos)
        week = ScmWeek.from_str(request.args["week"])
        brand = request.args["brand"]
        site = request.args["dc"]

        return receipt_override.get_pos(week, brand, site)


class ReceiptOverrideSkus(BaseReceiptOverride):
    def get(self):
        validate(request.args, receipt_override_skus)
        week = ScmWeek.from_str(request.args["week"])
        brand = request.args["brand"]
        site = request.args["dc"]
        # TODO: fix API field names
        po_number = request.args["po_number"]
        return [
            {"sku_code": s.sku_code, "sku_name": s.sku_name}
            for s in receipt_override.get_skus(week, brand, site, po_number)
        ]
