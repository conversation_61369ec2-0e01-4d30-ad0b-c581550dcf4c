from flask import Blueprint

from .discard import Discard, DiscardSkus, DiscardValidate
from .inventory import InventoryPullPut, PullPutSkus, ValidatePullPut
from .po_void import POVoid, POVoidPOs, POVoidSkus, ValidatePOVoid
from .receipt_override import ReceiptOverride, ReceiptOverridePOs, ReceiptOverrideSkus, ReceiptOverrideValidate
from .weekend_coverage_checklist import (
    PoNumbersByWeek,
    PosSkuNames,
    WeekendCoverageChecklist,
    WeekendCoverageChecklistByPoSku,
    WeekendCoverageChecklistItem,
    WeekendCoverageChecklistPoStatus,
    WeekendCoverageChecklistPreview,
    WeekendCoverageChecklistValidation,
)


def register_api() -> Blueprint:
    api_bp = Blueprint("forms", __name__, url_prefix="forms/")
    for path, resource in {
        "pull-put/": InventoryPullPut.as_view("pull-put"),
        "pull-put/skus/": PullPutSkus.as_view("pull-put-skus"),
        "pull-put/validate/": ValidatePullPut.as_view("pull-put-validate"),
        "po-void/": POVoid.as_view("po-void"),
        "po-void/pos/": POVoidPOs.as_view("po-void-pos"),
        "po-void/skus/": POVoidSkus.as_view("po-void-skus"),
        "po-void/validate/": ValidatePOVoid.as_view("po-void-validate"),
        "receipt-override/": ReceiptOverride.as_view("receipt-override"),
        "receipt-override/validate/": ReceiptOverrideValidate.as_view("receipt-override-validate"),
        "receipt-override/pos/": ReceiptOverridePOs.as_view("receipt-override-pos"),
        "receipt-override/skus/": ReceiptOverrideSkus.as_view("receipt-override-skus"),
        "weekend-coverage-checklist/": WeekendCoverageChecklist.as_view("wcc"),
        "weekend-coverage-checklist/validate/": WeekendCoverageChecklistValidation.as_view("wcc-validate"),
        "weekend-coverage-checklist/preview/": WeekendCoverageChecklistPreview.as_view("wcc-preview"),
        "weekend-coverage-checklist/preview/<po_number>/<sku_code>/": WeekendCoverageChecklistByPoSku.as_view(
            "wcc-preview-by-po-sku"
        ),
        "weekend-coverage-checklist/<int:wcc_id>/": WeekendCoverageChecklistItem.as_view("wcc-info"),
        "weekend-coverage-checklist/autocomplete/<week>/pos/": PoNumbersByWeek.as_view("wcc-autocomplete-pos"),
        "weekend-coverage-checklist/autocomplete/<week>/pos/<po_number>/sku-names/": PosSkuNames.as_view(
            "wcc-autocomplete-skus"
        ),
        "weekend-coverage-checklist/po-status/": WeekendCoverageChecklistPoStatus.as_view("wcc-po-status"),
        "discard/": Discard.as_view("discard"),
        "discard/validate/": DiscardValidate.as_view("discard-validate"),
        "discard/skus/": DiscardSkus.as_view("discard-skus"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
