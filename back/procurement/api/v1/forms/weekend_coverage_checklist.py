from collections.abc import Iterable
from datetime import datetime

from flask import request
from jsonschema import validate

from procurement.api import input_forms
from procurement.api.base_api import BaseApi
from procurement.api.v1.imt import purchase_order
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.constants.hellofresh_constant import DayOfWeek
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.managers.imt import weekend_coverage_checklist as checklist_manager
from procurement.managers.imt import weekend_coverage_checklist_po_status as wcc_po_status
from procurement.managers.imt.models import ChecklistModel, ChecklistSubmissionModel

from .schema.weekend_coverage_checklist import (
    get_checklist_preview_schema,
    patch_week_checklist_item_schema,
    post_week_checklist_schema,
    week_checklist_args_schema,
    week_checklist_po_status_args_schema,
)


class BaseWCC(BaseApi):
    permission = Permissions.IMT_WCC_V1

    @staticmethod
    def _build_response(data: Iterable[ChecklistModel]) -> list:
        return [
            {
                "id": item.checklist_id,
                "brand": item.brand,
                "site": item.site,
                "poNumber": item.po_number,
                "skuCode": item.sku_code,
                "skuName": item.sku_name,
                "poLandingDay": item.po_landing_day.value if item.po_landing_day else None,
                "productionDayAffected": item.production_day_affected.value,
                "toCheck": item.to_check,
                "contactNameVendorCarrier": item.contact_name_vendor_carrier,
                "emailPhone": item.email_phone,
                "backUpVendor": item.back_up_vendor,
                "updatedBy": item.updated_by,
                "status": item.status.value if item.status else None,
                "comment": item.comment,
                "shipMethod": item.ship_method,
                "carrierName": item.carrier_name,
                "poStatus": item.po_status,
                "fobPickUpDate": item.fob_pick_up_date.strftime(DATE_FORMAT) if item.fob_pick_up_date else None,
            }
            for item in data
        ]

    @staticmethod
    def _parse_submit(items: Iterable[dict]):
        return [
            ChecklistSubmissionModel(
                ui_row_id=item["rowId"],
                po_number=item["poNumber"],
                sku_name=item["skuName"],
                po_landing_day=DayOfWeek(item["poLandingDay"]) if item["poLandingDay"] else None,
                production_day_affected=(
                    DayOfWeek(item["productionDayAffected"]) if item["productionDayAffected"] else None
                ),
                to_check=item.get("toCheck"),
                contact_name_vendor_carrier=item.get("contactNameVendorCarrier"),
                email_phone=item.get("emailPhone"),
                back_up_vendor=item.get("backUpVendor"),
                fob_pick_up_date=(
                    datetime.strptime(item["fobPickUpDate"], DATE_FORMAT) if item.get("fobPickUpDate") else None
                ),
            )
            for item in items
        ]


class WeekendCoverageChecklist(BaseWCC):
    def get(self):
        validate(request.args, week_checklist_args_schema)
        week = ScmWeek.from_str(request.args["week"])
        return {
            "data": self._build_response(checklist_manager.get_checklist_by_week(week=week)),
            "readonlyWeek": input_forms.is_week_readonly(week),
        }

    def post(self):
        data = request.json
        validate(data, post_week_checklist_schema)
        args = request.args
        week = ScmWeek.from_str(request.args["week"])
        validate(args, week_checklist_args_schema)
        input_forms.validate_week_for_edit(week)
        items = self._parse_submit(data)
        result = checklist_manager.submit_checklist(week=week, items=items)
        return self._build_response(result)


class WeekendCoverageChecklistByPoSku(BaseWCC):
    def get(self, po_number: str, sku_code: str):
        return self._build_response(checklist_manager.get_checklist_by_po_sku(po_number=po_number, sku_code=sku_code))


class WeekendCoverageChecklistValidation(BaseWCC):
    def post(self):
        data = request.json
        validate(data, post_week_checklist_schema)
        args = request.args
        week = ScmWeek.from_str(args["week"])
        validate(args, week_checklist_args_schema)
        input_forms.validate_week_for_edit(week)
        items = self._parse_submit(data)
        return checklist_manager.validate_checklist_submission(week=week, items=items)


class WeekendCoverageChecklistPreview(BaseWCC):
    def get(self):
        args = request.args
        validate(args, get_checklist_preview_schema)
        brand = args.get("brand")
        sites = utils.split_comma_string(args["sites"]) if "sites" in args else None
        weeks = tuple(ScmWeek.from_str(week.strip()) for week in request.args["week"].split(","))
        return checklist_manager.get_checklist_preview(weeks=weeks, brand=brand, sites=sites)


class PoNumbersByWeek(BaseWCC):
    def get(self, week):
        week = ScmWeek.from_str(week)
        return [
            {
                "poNumber": po_ship_method.po_number,
                "shipMethod": po_ship_method.ship_method,
            }
            for po_ship_method in checklist_manager.get_po_numbers_data_by_week(week=week)
        ]


class PosSkuNames(BaseWCC):
    def get(self, week, po_number):
        week = ScmWeek.from_str(week)
        return checklist_manager.get_pos_sku_names(week=week, po_number=po_number)


class WeekendCoverageChecklistItem(BaseWCC):
    def patch(self, wcc_id: int):
        parameter_map = {
            "status": "status",
            "comment": "comment",
            "poLandingDay": "po_landing_day",
            "productionDayAffected": "production_day_affected",
            "toCheck": "to_check",
            "contactNameVendorCarrier": "contact_name_vendor_carrier",
            "emailPhone": "email_phone",
            "backUpVendor": "back_up_vendor",
            "fobPickUpDate": "fob_pick_up_date",
        }
        args = request.args
        validate(args, week_checklist_args_schema)
        week = ScmWeek.from_str(args["week"])
        input_forms.validate_week_for_edit(week)
        params_original = request.get_json()
        validate(params_original, patch_week_checklist_item_schema)

        params = {parameter_map[key]: params_original[key] for key in params_original}
        checklist_manager.update_checklist_item(wcc_id, params)

    def delete(self, wcc_id: int):
        args = request.args
        validate(args, week_checklist_args_schema)
        input_forms.validate_week_for_edit(ScmWeek.from_str(args["week"]))
        checklist_manager.delete_checklist_item(wcc_id)


class WeekendCoverageChecklistPoStatus(BaseApi):
    permission = Permissions.IMT_WCC_V1

    def get(self):
        args = request.args
        validate(args, week_checklist_po_status_args_schema)
        po_number = request.args["poNumber"]
        sku_code = request.args["skuCode"]
        if po_number and sku_code:
            res = wcc_po_status.get_wcc_po_status_by_po_number_sku(po_number, sku_code)
            return [purchase_order.build_base_response(item) for item in res]
        raise InvalidRequestException("'poNumber' and 'skuCode' params must be specified")
