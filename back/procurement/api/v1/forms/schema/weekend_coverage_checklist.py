from procurement.constants.hellofresh_constant import DayOfWeek
from procurement.data.models.inventory.weekend_coverage_checklist import ChecklistStatus

production_day_affected_values = [
    DayOfWeek.FRIDAY,
    DayOfWeek.SATURDAY,
    DayOfWeek.SUNDAY,
    DayOfWeek.MONDAY,
    DayOfWeek.TUESDAY,
    "",
]

po_landing_day_values = [
    DayOfWeek.MONDAY,
    DayOfWeek.TUESDAY,
    DayOfWeek.FRIDAY,
    DayOfWeek.SATURDAY,
    DayOfWeek.SUNDAY,
    "",
]

status_values = [
    ChecklistStatus.COMPLETED,
    ChecklistStatus.DELAYED,
    ChecklistStatus.IN_PROGRESS,
    ChecklistStatus.NO_RESPONSE,
    ChecklistStatus.OTHER,
    None,
]

get_checklist_preview_schema = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "sites": {"type": "string"},
    },
    "required": ["week"],
}

week_checklist_args_schema = {
    "type": "object",
    "properties": {"week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"}},
    "required": ["week"],
}

patch_week_checklist_item_schema = {
    "type": "object",
    "maxProperties": 1,
    "properties": {
        "status": {"enum": status_values},
        "comment": {"type": ["string", "null"]},
        "poLandingDay": {"enum": po_landing_day_values},
        "productionDayAffected": {"enum": production_day_affected_values},
        "toCheck": {"type": ["string", "null"]},
        "contactNameVendorCarrier": {"type": ["string", "null"]},
        "emailPhone": {"type": ["string", "null"]},
        "backUpVendor": {"type": ["string", "null"]},
        "fobPickUpDate": {"type": ["string", "null"]},
    },
    "additionalProperties": False,
}

post_week_checklist_schema = {
    "type": "array",
    "minItems": 1,
    "items": {
        "type": "object",
        "properties": {
            "rowId": {"type": "integer"},
            "poNumber": {"type": "string"},
            "skuName": {"type": "string"},
            "poLandingDay": {"type": "string", "enum": po_landing_day_values},
            "productionDayAffected": {"type": "string", "enum": production_day_affected_values},
            "toCheck": {"type": ["string", "null"]},
            "contactNameVendorCarrier": {"type": ["string", "null"]},
            "emailPhone": {"type": ["string", "null"]},
            "backUpVendor": {"type": ["string", "null"]},
            "fobPickUpDate": {"type": ["string", "null"]},
        },
        "required": [
            "rowId",
            "poNumber",
            "skuName",
            "productionDayAffected",
        ],
    },
}

week_checklist_po_status_args_schema = {
    "type": "object",
    "properties": {"poNumber": {"type": "string"}, "skuCode": {"type": "string"}},
    "required": ["poNumber", "skuCode"],
}
