po_void_get = {"type": "object", "properties": {"week": {"type": "string"}}, "required": ["week"]}

po_void_patch = {
    "type": "object",
    "properties": {
        "id": {"type": "number"},
        "comment:": {"type": "string"},
    },
    "required": ["id", "comment"],
}

po_void_post = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "data": {
            "type": "array",
            "minItems": 1,
            "items": {
                "type": "object",
                "properties": {
                    "poNumber": {"type": "string"},
                    "skuName": {"type": "string"},
                    "comment": {"type": "string"},
                    "dc": {"type": "string"},
                    "brand": {"type": "string"},
                    "uiRowId": {"type": "number"},
                },
                "required": ["poNumber", "skuName", "dc", "brand", "uiRowId"],
            },
        },
    },
    "required": ["week", "data"],
}

po_void_pos = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "dc": {"type": "string"},
    },
    "required": ["week", "brand", "dc"],
}

po_void_skus = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "dc": {"type": "string"},
        "po_number": {"type": "string"},
    },
    "required": ["week", "brand", "dc", "po_number"],
}

po_void_delete = {
    "type": "object",
    "properties": {
        "id": {"type": "number"},
    },
    "required": ["id"],
}
