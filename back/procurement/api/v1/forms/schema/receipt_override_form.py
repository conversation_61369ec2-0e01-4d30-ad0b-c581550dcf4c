receipt_override_get = {"type": "object", "properties": {"week": {"type": "string"}}, "required": ["week"]}


receipt_override_post_body = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "data": {
            "type": "array",
            "items": {
                "type": "object",
                "minItems": 1,
                "properties": {
                    "brand": {"type": "string"},
                    "dc": {"type": "string"},
                    "poNumber": {"type": "string"},
                    "adjustedReceivedVolume": {"type": "integer"},
                    "adjustedReceivedCases": {"anyOf": [{"type": "integer"}, {"type": "null"}]},
                    "adjustedReceivingDate": {"anyOf": [{"type": "string"}, {"type": "null"}]},
                    "skuCode": {"type": "string"},
                    "skuName": {"type": "string"},
                    "uiRowId": {"type": "integer"},
                    "comment": {"type": "string"},
                },
                "required": [
                    "brand",
                    "dc",
                    "poNumber",
                    "adjustedReceivedVolume",
                    "adjustedReceivedCases",
                    "skuCode",
                    "skuName",
                    "uiRowId",
                ],
            },
        },
    },
    "required": ["week", "data"],
}

receipt_override_patch = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "dc": {"type": "string"},
        "poNumber": {"type": "string"},
        "skuCode": {"type": "string"},
        "adjustedReceivedVolume": {"type": "integer"},
        "adjustedReceivedCases": {"anyOf": [{"type": "integer"}, {"type": "null"}]},
        "adjustedReceivingDate": {"anyOf": [{"type": "string"}, {"type": "null"}]},
        "comment": {"type": "string"},
    },
    "required": ["week", "brand", "dc", "poNumber", "skuCode"],
}

receipt_override_delete = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "dc": {"type": "string"},
        "poNumber": {"type": "string"},
        "skuCode": {"type": "string"},
    },
    "required": ["week", "brand", "dc", "poNumber", "skuCode"],
}

receipt_override_pos = {
    "type": "object",
    "properties": {"week": {"type": "string"}, "brand": {"type": "string"}, "dc": {"type": "string"}},
    "required": ["week", "brand", "dc"],
}

receipt_override_skus = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "brand": {"type": "string"},
        "dc": {"type": "string"},
        "po_number": {"type": "string"},
    },
    "required": ["week", "brand", "dc", "po_number"],
}
