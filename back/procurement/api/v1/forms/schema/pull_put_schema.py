inventory_pull_put_post = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "data": {
            "type": "array",
            "items": {
                "type": "object",
                "minItems": 1,
                "properties": {
                    "brand": {"type": "string"},
                    "dc": {"type": "string"},
                    "skuName": {"type": "string"},
                    "qty": {"type": "integer"},
                    "comment": {"type": "string"},
                    "uiRowId": {"type": "integer"},
                },
                "required": ["brand", "dc", "skuName", "qty", "uiRowId"],
            },
        },
    },
    "required": ["week", "data"],
}

inventory_pull_put_patch = {
    "type": "object",
    "properties": {
        "qty": {"type": "integer"},
        "comment": {"type": "string"},
        "id": {"type": "integer"},
    },
    "required": ["id", "qty"],
}

inventory_pull_put_args = {
    "type": "object",
    "properties": {"week": {"type": "string"}},
    "required": ["week"],
}

inventory_pull_put_skus_get = {
    "type": "object",
    "properties": {"brand": {"type": "string"}, "searchKey": {"type": "string", "minLength": 4}},
    "required": ["brand", "searchKey"],
}

inventory_pull_put_delete = {
    "type": "object",
    "properties": {
        "id": {"type": "integer"},
    },
    "required": ["id"],
}
