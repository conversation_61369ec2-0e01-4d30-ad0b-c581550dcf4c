discard_get = {"type": "object", "properties": {"week": {"type": "string"}}, "required": ["week"]}

discard_update = {
    "type": "object",
    "properties": {
        "numberOfUnits": {"type": "number"},
        "comment": {"type": "string", "maxLength": 4000},
        "discardedDateTime": {"type": "string"},
        "id": {"type": "number"},
    },
    "required": ["id"],
    "additionalProperties": False,
}

discard_post = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
        "data": {
            "type": "array",
            "minItems": 1,
            "items": {
                "type": "object",
                "properties": {
                    "brand": {"type": "string"},
                    "site": {"type": "string"},
                    "discardedDateTime": {"type": "string"},
                    "skuName": {"type": "string"},
                    "numUnits": {"type": "number"},
                    "comment": {"type": ["string", "null"]},
                    "rowId": {"type": "number"},
                },
                "required": ["brand", "site", "discardedDateTime", "skuName", "numUnits", "rowId"],
            },
        },
    },
    "required": ["week", "data"],
}

discard_skus_get = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "searchKey": {"type": "string", "minLength": 4},
    },
    "required": ["searchKey", "brand"],
}

discard_delete = {
    "type": "object",
    "properties": {
        "id": {"type": "number"},
    },
    "required": ["id"],
}
