comment_post = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "site": {"type": "string"},
        "skuCode": {"type": "string"},
        "comment": {"type": "string"},
    },
    "required": ["brand", "site", "skuCode", "comment"],
}

_week_inventory_schema = {
    "inbound": {"type": "integer"},
    "unitsExpiring": {"type": "integer"},
    "remainingInventory": {"type": "integer"},
}

_current_week_inventory_schema = {
    "unitsInHouseAdjustment": {"type": ["integer", "null"]},
    "unitsInHouseHJYesterday": {"type": "integer"},
    "startOfDayUnitsInHouseHJ": {"type": "integer"},
    "productionNeedsRestOfWeekForCurrentWeek": {"type": "integer"},
    "received": {"type": "integer"},
    "unitsExpiring": {"type": "integer"},
    "undeliveredPastDuePos": {"type": "integer"},
    **_week_inventory_schema,
}

_next_week_inventory_schema = {
    "startOfWeekInventory": {"type": "integer"},
    "productionNeedsFullWeek": {"type": "integer"},
    **_week_inventory_schema,
}

_dc_inventory_schema = {
    "weekly": {
        "type": "object",
        "patternProperties": {
            r"^\d{4}-W\d{2}$": {
                "type": "object",
                "oneOf": [
                    {
                        "properties": {**_current_week_inventory_schema},
                        "required": list(_current_week_inventory_schema),
                    },
                    {
                        "properties": {**_next_week_inventory_schema},
                        "required": list(_next_week_inventory_schema),
                    },
                ],
            }
        },
    },
    "sku": {"type": "string"},
    "skuName": {"type": "string"},
    "category": {"type": "string"},
    "storageLocation": {"type": "string"},
    "buyer": {"type": ["string", "null"]},
    "commodityGroup": {"type": ["string", "null"]},
    "isReplenishmentSku": {"type": "boolean"},
    "weeksOnHand": {"type": "integer"},
    "palletsOnHand": {"type": ["integer", "null"]},
    "unitsInHouseHj": {"type": "integer"},
    "dcExpiringInventory": {"type": "integer"},
    "supplementNeed": {"type": ["string", "null"]},
    "supplementNeedQuantity": {"type": ["integer", "null"]},
    "unitsInHouse": {"type": "integer"},
    "threePWWeeksOnHand": {"type": "integer"},
    "unitsInHouseNetwork": {"type": "integer"},
    "threePWExpiringInventory": {"type": "integer"},
    "notes": {"type": ["string", "null"]},
    "lastEditedBy": {"type": ["string", "null"]},
    "threePWOutbound": {"type": "integer"},
    "tenDaysFromExpiration": {"type": ["string", "null"]},
    "threePWTenDaysFromExpiration": {"type": ["string", "null"]},
}

in_house_adjustment_schema = {
    "type": "object",
    "properties": {
        "data": {
            "type": "object",
            "properties": _dc_inventory_schema,
            "required": list(_dc_inventory_schema),
        },
        "brand": {"type": "string"},
        "site": {"type": "string"},
    },
    "required": ["data", "site", "brand"],
}
