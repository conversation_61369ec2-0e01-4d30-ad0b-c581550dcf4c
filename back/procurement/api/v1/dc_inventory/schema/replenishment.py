replenishment_get = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "topoRecommendation": {"type": "string"}},
}

replenishment_po_status_get = {
    "type": "object",
    "properties": {
        "skuCode": {"type": "string"},
        "region": {"type": "string"},
    },
    "required": ["skuCode"],
}


replenishment_comment_preview_get = {
    "type": "object",
    "properties": {"region": {"type": "string"}},
    "required": ["region"],
}

replenishment_comment_get = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "skuCode": {"type": "string"}},
    "required": ["region", "skuCode"],
}

replenishment_comment_delete = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "skuCode": {"type": "string"}},
    "required": ["region", "skuCode"],
}

replenishment_comment_post = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "skuCode": {"type": "string"}, "comment": {"type": "string"}},
    "required": ["region", "skuCode", "comment"],
}

replenishment_override_post = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "skuCode": {"type": "string"}, "value": {"type": "number"}},
    "required": ["region", "skuCode", "value"],
}

replenishment_override_delete = {
    "type": "object",
    "properties": {"region": {"type": "string"}, "skuCode": {"type": "string"}},
    "required": ["region", "skuCode"],
}
