from procurement.api.schema.types import WEEK

get_preset_form = {"type": "object", "properties": {"week": {"type": "string"}}, "required": ["week"]}

add_preset = {
    "type": "array",
    "minItems": 1,
    "maxItems": 3,
    "items": {
        "type": "object",
        "properties": {
            "presetId": {"type": "number", "minimum": 1, "maximum": 3},
            "presetName": {"type": "string"},
            "brands": {"type": "object", "patternProperties": {"^.+$": {"type": "array", "items": {"type": "string"}}}},
            "warehouses": {"type": "array", "items": {"type": "string"}},
        },
        "required": ["presetId", "presetName", "brands", "warehouses"],
    },
}

get_network_depletion = {
    "type": "object",
    "properties": {"week": WEEK, "presetId": {"type": "string"}},
    "required": ["week", "presetId"],
}

network_depletion_po_status_schema = {
    "type": "object",
    "properties": {
        "presetId": {"type": "string", "pattern": "^[1-3]$"},
        "skuCode": {"type": "string"},
        "week": WEEK,
    },
    "required": ["presetId", "week"],
}


network_depletion_comment_preview_get = {
    "type": "object",
    "properties": {"presetId": {"type": "string"}, "week": WEEK},
    "required": ["presetId", "week"],
}

network_depletion_comment_get = {
    "type": "object",
    "properties": {
        "presetId": {"type": "string"},
        "week": WEEK,
        "skuCode": {"type": "string"},
    },
    "required": ["presetId", "week", "skuCode"],
}

network_depletion_comment_delete = {
    "type": "object",
    "properties": {
        "presetId": {"type": "string"},
        "week": WEEK,
        "skuCode": {"type": "string"},
    },
    "required": ["presetId", "week", "skuCode"],
}

network_depletion_comment_post = {
    "type": "object",
    "properties": {
        "presetId": {"type": "number", "minimum": 1, "maximum": 3},
        "week": WEEK,
        "skuCode": {"type": "string"},
        "comment": {"type": "string"},
    },
    "required": ["presetId", "week", "skuCode", "comment"],
}

network_depletion_ics_tickets_preview_get = {
    "type": "object",
    "properties": {
        "presetId": {"type": "string"},
        "week": WEEK,
    },
    "required": ["presetId", "week"],
}
