from collections import defaultdict
from collections.abc import Iterable

from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi
from procurement.api.v1 import depletion_common
from procurement.api.v1.imt import Donation, purchase_order
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.core.typing import BRAND, ORDER_NUMBER, PO_NUMBER, SITE, SKU_CODE
from procurement.data.dto.inventory.network_depletion_comment import NetworkDepletionCommentItem
from procurement.data.dto.procurement.network_depletion import UserPreset
from procurement.managers import ics as ics_manager
from procurement.managers.dc_inventory.network_depletion import comment, core, dashboard, po_status
from procurement.managers.dc_inventory.network_depletion import preset_configuration as preset_service
from procurement.managers.dc_inventory.network_depletion.preset_configuration import PresetForm, PresetFormBrand

from .schema import network_depletion as network_depletion_schema


class BaseNetworkDepletion(BaseApi):
    permission = Permissions.DC_INVENTORY_NETWORK_DEPLETION_V1


class PresetFormResource(BaseNetworkDepletion):
    def get(self):
        arguments = request.args
        validate(arguments, network_depletion_schema.get_preset_form)
        scm_week = ScmWeek.from_str(arguments["week"])
        preset_form = preset_service.get_preset_form(scm_week)
        return self._build_response(preset_form)

    def _build_response(self, preset_form: PresetForm):
        return {"brands": self._build_brand_info(preset_form.brands), "warehouses": preset_form.warehouses}

    @staticmethod
    def _build_brand_info(brands_info: list[PresetFormBrand]):
        return {
            brand.brand_code: {
                "name": brand.brand_name,
                "weekConfig": f"{brand.scm_week_config.length}:{brand.scm_week_config.overlap}",
                "sites": brand.sites,
            }
            for brand in brands_info
        }


class Presets(BaseNetworkDepletion):
    def get(self):
        return {
            "presets": [
                self._build_response(preset)
                for preset in sorted(preset_service.get_user_presets(), key=lambda x: x.preset_id)
            ]
        }

    def post(self):
        data = request.json
        validate(data, network_depletion_schema.add_preset)
        preset_service.update_presets(self._parse_request(data))
        return None, 201

    @staticmethod
    def _build_response(preset: UserPreset):
        return {
            "presetId": preset.preset_id,
            "presetName": preset.name,
            "brands": preset.brands,
            "warehouses": preset.warehouses,
        }

    @staticmethod
    def _parse_request(data: Iterable[dict]):
        return [
            UserPreset(
                preset_id=item["presetId"],
                name=item["presetName"],
                brands=item["brands"],
                warehouses=item["warehouses"],
            )
            for item in data
        ]


class NetworkDepletion(BaseNetworkDepletion):
    def get(self):
        arguments = request.args
        validate(arguments, network_depletion_schema.get_network_depletion)
        scm_week = ScmWeek.from_str(arguments["week"])
        preset_id = int(arguments["presetId"])
        return [
            depletion_common.build_aggregated_response(item)
            for item in dashboard.get_network_depletion(scm_week, preset_id)
        ]


class NetworkDepletionWeekdays(BaseNetworkDepletion):
    def get(self):
        arguments = request.args
        validate(arguments, network_depletion_schema.get_network_depletion)
        scm_week = ScmWeek.from_str(arguments["week"])
        preset_id = int(arguments["presetId"])
        days = core.get_weekdays(preset_id, scm_week)
        return {day.day.isoformat(): day.weekday_title for day in days}


class NetworkDepletionPoStatus(BaseNetworkDepletion):
    def get(self):
        args = request.args
        validate(args, network_depletion_schema.network_depletion_po_status_schema)
        preset_id = int(args["presetId"])
        sku_code = args["skuCode"]
        scm_week = ScmWeek.from_str(args["week"])
        res = po_status.get_po_status(preset_id=preset_id, week=scm_week, sku_code=sku_code)
        return [purchase_order.build_base_response(item) for item in res]


class NetworkDepletionCommentPreview(BaseNetworkDepletion):
    def get(self):
        validate(request.args, network_depletion_schema.network_depletion_comment_preview_get)
        return comment.get_network_depletion_comments_preview(
            preset_id=int(request.args["presetId"]), week=ScmWeek.from_str(request.args["week"])
        )


class NetworkDepletionComment(BaseNetworkDepletion):
    def get(self):
        validate(request.args, network_depletion_schema.network_depletion_comment_get)
        return self._build_response(
            comment.get_network_depletion_comment(
                preset_id=int(request.args["presetId"]),
                week=ScmWeek.from_str(request.args["week"]),
                sku_code=request.args["skuCode"],
            )
        )

    @staticmethod
    def _build_response(item: NetworkDepletionCommentItem) -> dict:
        return {"value": item.text, "lastUpdated": item.last_updated.isoformat(), "lastUpdatedBy": item.updated_by}

    def post(self):
        data = request.json
        validate(data, network_depletion_schema.network_depletion_comment_post)
        comment.upsert_network_depletion_comments(
            preset_id=data["presetId"],
            week=ScmWeek.from_str(data["week"]),
            sku_code=data["skuCode"],
            text=data["comment"],
        )
        return None, 201

    def delete(self):
        validate(request.args, network_depletion_schema.network_depletion_comment_delete)
        comment.delete_network_depletion_comments(
            preset_id=int(request.args["presetId"]),
            week=ScmWeek.from_str(request.args["week"]),
            sku_code=request.args["skuCode"],
        )
        return None, 204


class NetworkDepletionIcsTicketPreview(BaseApi):
    permission = Permissions.ICS_TICKETS_V1

    def get(self):
        validate(request.args, network_depletion_schema.network_depletion_ics_tickets_preview_get)
        week = ScmWeek.from_str(request.args["week"])
        preset_id = int(request.args["presetId"])
        return self._build_response(ics_manager.get_network_depletion_ics_tickets_preview(preset_id, week))

    @staticmethod
    def _build_response(
        ics_preview_data: dict[
            BRAND, dict[ScmWeek, dict[SITE, list[tuple[SKU_CODE | None, PO_NUMBER | None, ORDER_NUMBER | None]]]]
        ],
    ):
        res = defaultdict(dict)
        for brand, sku_codes_po_numbers_by_week_site in ics_preview_data.items():
            for week, sku_codes_po_numbers_by_site in sku_codes_po_numbers_by_week_site.items():
                res[brand][str(week)] = {
                    site: [
                        {"skuCode": sku_code, "poNumber": po_number, "orderNumber": order_number}
                        for sku_code, po_number, order_number in sku_codes_po_numbers
                    ]
                    for site, sku_codes_po_numbers in sku_codes_po_numbers_by_site.items()
                }
        return res


class NetworkDepletionDonation(Donation):
    permission = Permissions.DC_INVENTORY_NETWORK_DEPLETION_V1
