import itertools
from collections.abc import Iterable

from flask import request

from procurement.api.base_api import BaseApi
from procurement.auth.permissions import Permissions
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.typing import BOB_CODE
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.managers.admin import dc_admin
from procurement.managers.dc_inventory.network_depletion import preset_configuration
from procurement.managers.inventory.unified_inventory import UnifiedInventoryItem, UnifiedInventoryManager
from procurement.managers.pimt import partners
from procurement.managers.sku import alternative_sku_mapping


class BaseUnifiedInventoryApi(BaseApi):
    permission = Permissions.DC_INVENTORY_UNIFIED_INVENTORY_V1

    @staticmethod
    def _build_response(item: UnifiedInventoryItem) -> dict:
        return {
            "source": item.source,
            "brands": ", ".join(item.brands or []),
            "site": item.bob_code,
            "siteName": item.site_name,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "category": item.category,
            "expirationDate": item.expiration_date,
            "quantity": item.units,
            "status": item.status,
            "lot": item.lot_code,
            "locationId": item.location_id,
            "poNumber": item.po_number,
            "supplier": item.supplier,
            "snapshotTimestamp": item.snapshot_timestamp,
        }

    def _get(self, bob_codes: Iterable[BOB_CODE] | None):
        requested_sku = request.args.get("sku")
        sku_codes = [requested_sku] if requested_sku else None
        include_org_cv_skus = request.args.get("includeOrgCvSkus", "false").lower() == "true"
        if sku_codes and include_org_cv_skus:
            org_cv_mapping = alternative_sku_mapping.get_all_alternative_sku(OrganicSkuModel)
            if alt_sku := org_cv_mapping.get(requested_sku):
                sku_codes.append(alt_sku)
        return [
            self._build_response(item)
            for item in UnifiedInventoryManager(sku_codes=sku_codes, bob_codes=bob_codes).get_rich_live_inventory(
                wip_inventory_included=not sku_codes
            )
        ]


class UnifiedInventory(BaseUnifiedInventoryApi):
    def get(self):
        region = request.args.get("pimtRegion")
        bob_codes = None
        if region:
            site_map = utils.group_by(dc_admin.get_all_market_dcs(), lambda s: s.sheet_name)
            whs = [wh for wh in partners.get_all_partners() if wh.region == region]
            regional_dcs = set(itertools.chain(*(w.regional_dcs for w in whs)))
            bob_codes = itertools.chain(
                (w.bob_code for w in whs),
                (s.bob_code for dc in regional_dcs for s in site_map.get(dc, [])),
            )
        return self._get(bob_codes)


class SiteUnifiedInventory(BaseUnifiedInventoryApi):
    def get(self):
        brand = request.args.get("brand")
        site = request.args.get("site")
        week = ScmWeek.from_str(request.args.get("week"))
        site_obj = dc_admin.get_site(brand, site, week)
        if not site_obj:
            raise InvalidRequestException(f"Site {site} is invalid for brand {brand} and week {week}")
        bob_codes = [site_obj.bob_code]
        if request.args.get("includeWarehouseInventory", "").lower() == "true":
            bob_codes.extend(wh.bob_code for wh in partners.get_all_partners() if site in wh.regional_dcs)
        return self._get(bob_codes=bob_codes)


class NetworkDeplUnifiedInventory(BaseUnifiedInventoryApi):
    def get(self):
        preset_id = int(request.args["presetId"])
        bob_codes = preset_configuration.get_bob_codes_for_preset(preset_id)
        return self._get(bob_codes=bob_codes)
