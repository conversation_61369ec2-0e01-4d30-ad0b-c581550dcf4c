from collections.abc import Iterable
from datetime import datetime

from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi, BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.core.dates import ScmWeek
from procurement.core.typing import SITE
from procurement.managers.dc_inventory import inventory_module as inventory_service
from procurement.managers.dc_inventory.inventory_module import (
    AvailableInventorySummary,
    Comment,
    CurrentWeekInventory,
    IngredientSummary,
    InventoryModuleDashboard,
    SkuInventoryModule,
    SupplementNeed,
    TpwInventory,
    WeekInventory,
)

from .schema import inventory_module as inventory_module_schema


def build_sku_response(sku_inventory: SkuInventoryModule):
    ingredient_summary = sku_inventory.ingredient_summary
    available_inventory = sku_inventory.available_inventory_summary
    supplement_need = available_inventory.supplement_need
    tpw_inventory = sku_inventory.tpw_inventory
    return {
        "weekly": build_weekly_section(sku_inventory.current_week_inventory, sku_inventory.upcoming_weeks_inventories),
        "sku": ingredient_summary.sku,
        "skuName": ingredient_summary.sku_name,
        "category": ingredient_summary.category,
        "storageLocation": ingredient_summary.storage_location,
        "buyer": ingredient_summary.buyer,
        "commodityGroup": ingredient_summary.commodity_group,
        "isReplenishmentSku": ingredient_summary.is_replenishment_sku,
        "weeksOnHand": available_inventory.weeks_on_hand,
        "palletsOnHand": available_inventory.pallets_on_hand if available_inventory.pallets_on_hand else None,
        "unitsInHouseHj": available_inventory.units_in_house_hj,
        "dcExpiringInventory": available_inventory.dc_expiring_inventory,
        "tenDaysFromExpiration": (
            available_inventory.less_than_ten_days_expiration.isoformat()
            if available_inventory.less_than_ten_days_expiration
            else None
        ),
        "supplementNeed": str(supplement_need.week) if supplement_need else None,
        "supplementNeedQuantity": supplement_need.qty if supplement_need else None,
        "unitsInHouse": tpw_inventory.units_in_house_today if tpw_inventory else 0,
        "threePWWeeksOnHand": tpw_inventory.tpw_weeks_on_hand if tpw_inventory else 0,
        "unitsInHouseNetwork": tpw_inventory.units_in_house_today_network if tpw_inventory else 0,
        "threePWExpiringInventory": tpw_inventory.tpw_expiring_inventory if tpw_inventory else 0,
        "notes": sku_inventory.comment.text if sku_inventory.comment else None,
        "lastEditedBy": sku_inventory.comment.last_edited_by if sku_inventory.comment else None,
        "threePWOutbound": tpw_inventory.tpw_outbound if tpw_inventory else 0,
        "threePWTenDaysFromExpiration": (
            tpw_inventory.less_than_ten_days_expiration.isoformat()
            if tpw_inventory and tpw_inventory.less_than_ten_days_expiration
            else None
        ),
    }


def build_weekly_section(current_week_inventory: CurrentWeekInventory, upcoming_weeks_inventories: list[WeekInventory]):
    weekly = {str(current_week_inventory.week): build_current_week(current_week_inventory)}
    weekly.update({str(inventory.week): build_upcoming_week(inventory) for inventory in upcoming_weeks_inventories})
    return weekly


def build_base_week(weekly_item: WeekInventory):
    return {
        "inbound": weekly_item.inbound,
        "unitsExpiring": weekly_item.units_expiring,
        "remainingInventory": weekly_item.remaining_inventory,
    }


def build_current_week(current_week_inventory: CurrentWeekInventory):
    return {
        "unitsInHouseHJYesterday": current_week_inventory.units_in_house_yesterday,
        "startOfDayUnitsInHouseHJ": current_week_inventory.start_of_week_inventory,
        "productionNeedsRestOfWeekForCurrentWeek": current_week_inventory.row_need,
        "received": current_week_inventory.received,
        "rowNeedsPreviousWeek": current_week_inventory.previous_week_row_need,
        "unitsInHouseAdjustment": current_week_inventory.units_in_house_adjustment,
        "undeliveredPastDuePos": current_week_inventory.undelivered_past_due_pos,
        **build_base_week(current_week_inventory),
    }


def build_upcoming_week(upcoming_week_inventory: WeekInventory):
    return {
        "startOfWeekInventory": upcoming_week_inventory.start_of_week_inventory,
        "productionNeedsFullWeek": upcoming_week_inventory.row_need,
        **build_base_week(upcoming_week_inventory),
    }


class InventoryModule(BaseApiWithSites):
    permission = Permissions.DC_INVENTORY_INVENTORY_MODULE_V1

    def _get_sites(self):
        return inventory_service.get_inventory_sites(self.weeks[0], self.brand)[self.brand]

    def get(self):
        return self._build_response(InventoryModuleDashboard(self.brand, self.sites).get_inventory_by_site())

    def _build_response(self, inventory_module: dict[SITE, list[SkuInventoryModule]]):
        return {site: self._build_site_response(site_inventory) for site, site_inventory in inventory_module.items()}

    @staticmethod
    def _build_site_response(site_inventory: list[SkuInventoryModule]):
        return [build_sku_response(sku_inventory) for sku_inventory in site_inventory]


class InventoryModuleComment(BaseApi):
    permission = Permissions.DC_INVENTORY_INVENTORY_MODULE_V1

    def post(self):
        body = request.get_json()
        validate(body, inventory_module_schema.comment_post)
        comment, user_email = inventory_service.add_comment(
            body["skuCode"], body["brand"], body["site"], body["comment"]
        )
        return {"comment": comment.text, "lastEditedBy": user_email}


class InventoryModuleInHouseAdjustment(BaseApi):
    permission = Permissions.DC_INVENTORY_INVENTORY_MODULE_V1

    def post(self):
        body = request.get_json()
        validate(body, inventory_module_schema.in_house_adjustment_schema)
        inventory_data = body["data"]
        inventory = self._build_model_from_adjustment_request(inventory_data)
        return build_sku_response(
            inventory_service.process_inventory_by_in_house_adjustment(inventory, body["brand"], body["site"])
        )

    @staticmethod
    def _build_model_from_adjustment_request(body: dict) -> SkuInventoryModule:
        weekly = body["weekly"]
        if supplement_need := body.get("supplementNeed"):
            supplement_need = SupplementNeed(
                week=ScmWeek.from_str(supplement_need),
                qty=body.get("supplementNeedQuantity", 0),
            )
        current_week_inventory, week_inventory = InventoryModuleInHouseAdjustment._build_weekly_from_request(weekly)
        return SkuInventoryModule(
            ingredient_summary=IngredientSummary(
                sku=body["sku"],
                sku_name=body["skuName"],
                category=body["category"],
                storage_location=body["storageLocation"],
                buyer=body["buyer"],
                commodity_group=body["commodityGroup"],
                is_replenishment_sku=body["isReplenishmentSku"],
            ),
            available_inventory_summary=AvailableInventorySummary(
                weeks_on_hand=body["weeksOnHand"],
                pallets_on_hand=body["palletsOnHand"],
                supplement_need=supplement_need,
                dc_expiring_inventory=body["dcExpiringInventory"],
                units_in_house_hj=body["unitsInHouseHj"],
                less_than_ten_days_expiration=(
                    datetime.strptime(body["tenDaysFromExpiration"], DATE_FORMAT)
                    if body["tenDaysFromExpiration"]
                    else None
                ),
            ),
            tpw_inventory=TpwInventory(
                units_in_house_today=body["unitsInHouse"],
                units_in_house_today_network=body["unitsInHouseNetwork"],
                tpw_expiring_inventory=body["threePWExpiringInventory"],
                tpw_outbound=body["threePWOutbound"],
                less_than_ten_days_expiration=(
                    datetime.strptime(body["threePWTenDaysFromExpiration"], DATE_FORMAT)
                    if body["threePWTenDaysFromExpiration"]
                    else None
                ),
                tpw_weeks_on_hand=body["threePWWeeksOnHand"],
            ),
            current_week_inventory=current_week_inventory,
            upcoming_weeks_inventories=week_inventory,
            comment=Comment(text=body.get("notes"), last_edited_by=body.get("lastEditedBy")),
        )

    @staticmethod
    def _build_weekly_from_request(weekly: dict) -> tuple[CurrentWeekInventory, list[WeekInventory]]:
        weeks = sorted(weekly.items(), key=lambda x: ScmWeek.from_str(x[0]))
        return (
            InventoryModuleInHouseAdjustment._build_current_week_from_request(*weeks[0]),
            InventoryModuleInHouseAdjustment._build_weeks_from_request(weeks[1:]),
        )

    @staticmethod
    def _build_current_week_from_request(week: str, data: dict) -> CurrentWeekInventory:
        return CurrentWeekInventory(
            inbound=data["inbound"],
            units_expiring=data["unitsExpiring"],
            remaining_inventory=data["remainingInventory"],
            start_of_week_inventory=data["startOfDayUnitsInHouseHJ"],
            row_need=data["productionNeedsRestOfWeekForCurrentWeek"],
            week=ScmWeek.from_str(week),
            units_in_house_yesterday=data["unitsInHouseHJYesterday"],
            received=data["received"],
            previous_week_row_need=data["rowNeedsPreviousWeek"],
            units_in_house_adjustment=data["unitsInHouseAdjustment"],
            undelivered_past_due_pos=data.get("undeliveredPastDuePos"),
        )

    @staticmethod
    def _build_weeks_from_request(weeks: Iterable[tuple[str, dict]]) -> list[WeekInventory]:
        return [
            WeekInventory(
                inbound=data["inbound"],
                units_expiring=data["unitsExpiring"],
                remaining_inventory=data["remainingInventory"],
                start_of_week_inventory=data["startOfWeekInventory"],
                row_need=data["productionNeedsFullWeek"],
                week=ScmWeek.from_str(week),
            )
            for week, data in weeks
        ]
