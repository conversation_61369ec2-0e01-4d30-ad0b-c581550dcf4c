from flask import request
from jsonschema import validate

from procurement.api import base_api, formatting
from procurement.api.v1.dc_inventory.schema import replenishment as schema
from procurement.api.v1.imt import purchase_order
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.typing import PIMT_REGION, SKU_NAME
from procurement.data.dto.pimt.replenishment_comment import ReplenishmentCommentItem
from procurement.managers.dc_inventory.replenishment import forecasts
from procurement.managers.dc_inventory.replenishment import po_status as replenishment_po_status_service
from procurement.managers.dc_inventory.replenishment import replenishment as replenishment_service
from procurement.managers.dc_inventory.replenishment import replenishment_comment, replenishment_override
from procurement.managers.dc_inventory.replenishment.forecasts import Forecast
from procurement.managers.dc_inventory.replenishment.region_level import ReplenishmentItem


class Replenishment(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1
    max_week = ScmWeek.max()

    def get(self):
        validate(request.args, schema.replenishment_get)

        region = request.args.get("region")
        use_topo_recommendation = request.args.get("topoRecommendation", "").lower() == "true"
        sorted_replenishment = sorted(
            replenishment_service.get_replenishment_view(region, use_topo_recommendation), key=self._sort_key
        )
        return [self._build_replenishment(item) for item in sorted_replenishment]

    @staticmethod
    def _sort_key(item: ReplenishmentItem) -> tuple[SKU_NAME, PIMT_REGION]:
        return item.sku_overview.sku_name, item.region

    @staticmethod
    def _build_replenishment(item: ReplenishmentItem) -> dict:
        return {
            "brand": ",".join(item.sku_overview.brands),
            "sku": item.sku_overview.sku_code,
            "skuName": item.sku_overview.sku_name,
            "category": item.sku_overview.category,
            "subCategory": item.sku_overview.subcategory,
            "buyer": ",".join(item.buyers),
            "strategyManager": ",".join(item.strategy_manager),
            "replenishmentType": item.replenishment_type,
            "replenishmentBuyer": ",".join(item.replenishment_buyers),
            "requiredUnitsQuantity": item.required_order_quantity_units,
            "requiredCasesQuantity": item.required_order_quantity_cases,
            "requiredPalletsQuantity": item.required_order_quantity_pallets,
            "leadTime": item.lead_time_weeks,
            "inWeekRowNeed": item.row_needs,
            "forecasts": {str(week): forecast for week, forecast in item.forecasts.items()},
            "highForecastWeeks": {
                str(week): is_high_forecast_week for week, is_high_forecast_week in item.high_forecast_weeks.items()
            },
            "totalForecast": item.total_forecast,
            "averageWeeklyDemand": item.avg_weekly_usage,
            "totalInventoryCore": item.total_core_inventory,
            "totalInventoryTpl": item.total_tpl_inventory,
            "vendorManagedInventory": item.total_vendor_managed_inventory,
            "totalOnHand": item.total_on_hand,
            "totalInventoryWarehouse": item.current_wh_inventory,
            "expiringSoonInventory": item.expiring_in_sixty_days,
            "expiredInventory": item.expired_inventory,
            "totalInventoryWos": item.total_wos_inventory,
            "inbounds": {str(week): inbounds for week, inbounds in item.inbounds.items()},
            "totalInbounds": item.total_inbounds,
            "totalUndelivered": item.undelivered,
            "endOfWeek": {str(week): eow for week, eow in item.end_of_weeks.items()},
            "endOfWeekWos": {str(week): eow_wos for week, eow_wos in item.end_of_weeks_wos.items()},
            "endOfWeekBufferFlags": {str(week): flag for week, flag in item.end_of_weeks_buffer_flags.items()},
            "totalCost": formatting.round_if_available(item.total_inventory_cost, 2),
            "dcCost": formatting.round_if_available(item.dc_inventory_cost, 2),
            "tplCost": formatting.round_if_available(item.tpl_inventory_cost, 2),
            "tpwCost": formatting.round_if_available(item.tpw_inventory_cost, 2),
            "totalOnHandOverride": item.total_on_hand_override,
            # TOPO section
            "purchaseQtyRecommendation": item.pqr_total,
            "orderingWeek": str(item.ordering_week) if item.ordering_week else None,
            "deliveryWeek": [str(week) for week in item.delivery_weeks] if item.delivery_weeks else None,
            "projectedShortageWeek": str(item.projected_shortage_week) if item.projected_shortage_week else None,
            "projectedShortageQty": item.projected_shortage_qty,
            # Inventory Targets section
            "maxMoq": item.min_order_quantity,
            "maxIoq": item.incremental_order_quantity,
            "shelfLife": item.shelf_life,
        }


class ReplenishmentPoStatus(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1

    def get(self):
        validate(request.args, schema.replenishment_po_status_get)
        sku_code = request.args["skuCode"]
        region = request.args.get("region")
        if sku_code:
            res = replenishment_po_status_service.get_replenishment_po_status(region, sku_code)
            return [purchase_order.build_base_response(item) for item in res]
        raise InvalidRequestException("'skuCode' param must be specified")


class ReplenishmentUndeliveredPoStatus(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1

    def get(self):
        validate(request.args, schema.replenishment_po_status_get)
        sku_code = request.args["skuCode"]
        region = request.args.get("region")
        if sku_code:
            res = replenishment_po_status_service.get_replenishment_undelivered_po_status(region, sku_code)
            return [purchase_order.build_base_response(item) for item in res]
        raise InvalidRequestException("'skuCode' param must be specified")


class BaseReplenishmentComment(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1


class ReplenishmentCommentPreview(BaseReplenishmentComment):

    def get(self):
        validate(request.args, schema.replenishment_comment_preview_get)
        return replenishment_comment.get_replenishment_comments_preview(region=request.args["region"])


class ReplenishmentComment(BaseReplenishmentComment):
    def get(self):
        validate(request.args, schema.replenishment_comment_get)
        return self._build_response(
            replenishment_comment.get_replenishment_comment(
                region=request.args["region"], sku_code=request.args["skuCode"]
            )
        )

    @staticmethod
    def _build_response(item: ReplenishmentCommentItem) -> dict:
        return {"value": item.text, "lastUpdated": item.last_updated.isoformat(), "lastUpdatedBy": item.updated_by}

    def post(self):
        data = request.json
        validate(data, schema.replenishment_comment_post)
        replenishment_comment.upsert_replenishment_comments(
            region=data["region"], sku_code=data["skuCode"], text=data["comment"]
        )
        return None, 201

    def delete(self):
        validate(request.args, schema.replenishment_comment_delete)
        replenishment_comment.delete_replenishment_comments(
            region=request.args["region"], sku_code=request.args["skuCode"]
        )
        return None, 204


class ReplenishmentForecast(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1

    def get(self):
        validate(request.args, schema.replenishment_po_status_get)
        sorted_forecasts = sorted(
            forecasts.get_forecasts(region=request.args.get("region"), sku_code=request.args["skuCode"]),
            key=self._sort_key,
        )
        return [self._build_response(item) for item in sorted_forecasts]

    @staticmethod
    def _sort_key(item: Forecast):
        return item.scm_week, item.site

    @staticmethod
    def _build_response(item: Forecast) -> dict:
        return {"skuCode": item.sku_code, "site": item.site, "week": str(item.scm_week), "forecast": item.forecast}


class ReplenishmentOverride(base_api.BaseApi):
    permission = Permissions.PIMT_REPLENISHMENT_V1

    def post(self):
        data = request.json
        validate(data, schema.replenishment_override_post)
        region = data["region"]
        sku_code = data["skuCode"]
        replenishment_override.upsert_replenishment_override(region=region, sku_code=sku_code, value=data["value"])
        replen_item = replenishment_service.get_replenishment_item(region=region, sku_code=sku_code)
        return self._build_response(replen_item), 201

    def delete(self):
        validate(request.args, schema.replenishment_override_delete)
        region = request.args["region"]
        sku_code = request.args["skuCode"]
        replenishment_override.delete_replenishment_override(region=region, sku_code=sku_code)
        replen_item = replenishment_service.get_replenishment_item(region=region, sku_code=sku_code)
        return self._build_response(replen_item)

    def _build_response(self, item: ReplenishmentItem) -> dict:
        return {
            "endOfWeek": {str(week): eow for week, eow in item.end_of_weeks.items()},
            "endOfWeekFlags": {str(week): flag for week, flag in item.end_of_weeks_buffer_flags.items()},
        }
