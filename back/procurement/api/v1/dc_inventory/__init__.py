from flask import Blueprint

from .inventory_module import InventoryModule, InventoryModuleComment, InventoryModuleInHouseAdjustment
from .network_depletion_module import (
    NetworkDepletion,
    NetworkDepletionComment,
    NetworkDepletionCommentPreview,
    NetworkDepletionDonation,
    NetworkDepletionIcsTicketPreview,
    NetworkDepletionPoStatus,
    NetworkDepletionWeekdays,
    PresetFormResource,
    Presets,
)
from .replenishment import (
    Replenishment,
    ReplenishmentComment,
    ReplenishmentCommentPreview,
    ReplenishmentForecast,
    ReplenishmentOverride,
    ReplenishmentPoStatus,
    ReplenishmentUndeliveredPoStatus,
)
from .unified_inventory import NetworkDeplUnifiedInventory, SiteUnifiedInventory, UnifiedInventory


def register_api() -> Blueprint:
    api_bp = Blueprint("dc-inventory", __name__, url_prefix="dc-inventory/")
    for path, resource in {
        "inventory-module/": InventoryModule.as_view("inventory-module"),
        "inventory-module/comment/": InventoryModuleComment.as_view("inventory-module-comment"),
        "inventory-module/in-house-adjustment/": InventoryModuleInHouseAdjustment.as_view(
            "inventory-module-in-house-adjustment"
        ),
        "network-depletion/": NetworkDepletion.as_view("network-depletion"),
        "network-depletion/preset-form/": PresetFormResource.as_view("preset-form"),
        "network-depletion/presets/": Presets.as_view("presets"),
        "network-depletion/weekdays/": NetworkDepletionWeekdays.as_view("network-depletion-weekdays"),
        "network-depletion/po-status/": NetworkDepletionPoStatus.as_view("network-depletion-po-status"),
        "network-depletion/comments/preview/": NetworkDepletionCommentPreview.as_view(
            "network-depletion-comment-preview"
        ),
        "network-depletion/comments/": NetworkDepletionComment.as_view("network-depletion-comment"),
        "network-depletion/inventory/": NetworkDeplUnifiedInventory.as_view("network-depletion-inventory"),
        "network-depletion/ics-tickets/preview/": NetworkDepletionIcsTicketPreview.as_view(
            "network-depletion-ics-tickets-preview"
        ),
        "network-depletion/donations/": NetworkDepletionDonation.as_view("network-depletion-donations"),
        "unified-inventory/": UnifiedInventory.as_view("unified-inventory"),
        "site-unified-inventory/": SiteUnifiedInventory.as_view("site-unified-inventory"),
        "replenishment/": Replenishment.as_view("replenishment"),
        "replenishment/po-status/": ReplenishmentPoStatus.as_view("replenishment-po-status"),
        "replenishment/undelivered-po-status/": ReplenishmentUndeliveredPoStatus.as_view(
            "replenishment-undelivered-po-status"
        ),
        "replenishment/comments/preview/": ReplenishmentCommentPreview.as_view("replenishment-comments-preview"),
        "replenishment/comments/": ReplenishmentComment.as_view("replenishment-comments"),
        "replenishment/forecasts/": ReplenishmentForecast.as_view("replenishment-forecasts"),
        "replenishment/override/": ReplenishmentOverride.as_view("replenishment-override"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
