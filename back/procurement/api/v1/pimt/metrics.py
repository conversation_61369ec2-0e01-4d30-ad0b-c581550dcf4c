from flask import request

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.managers.pimt.pimt_daily_exception_report import ExceptionMetricsData, get_daily_exception_metrics


class PIMTMetrics(base_api.BaseApi):
    permission = Permissions.PIMT_EXCEPTION_METRICS_V1

    @staticmethod
    def _build_response(items: list[ExceptionMetricsData], week_from: ScmWeek, week_to: ScmWeek) -> dict:
        return {
            "weekFrom": str(week_from),
            "weekTo": str(week_to),
            "data": [
                {
                    "totalExceptions": {
                        "count": item.invalid_rows.count,
                        "pct": item.invalid_rows.per_cent,
                        "total_count": item.invalid_rows.total_count,
                    },
                    "date": item.export_date.isoformat(),
                    "week": str(item.week),
                    "byPartner": {
                        partner_item.partner: {
                            "totalExceptions": {
                                "count": partner_item.invalid_rows.count,
                                "pct": partner_item.invalid_rows.per_cent,
                                "total_count": partner_item.invalid_rows.total_count,
                            },
                            "byType": {
                                exception: {
                                    "count": value.count,
                                    "pct": value.per_cent,
                                }
                                for exception, value in partner_item.by_type.items()
                            },
                        }
                        for partner_item in item.by_partner
                    },
                }
                for item in items
            ],
        }

    def get(self):
        week_to = request.args.get("weekTo")
        week_to = ScmWeek.from_str(week_to) if week_to else ScmWeek.current_week()
        week_from = request.args.get("weekFrom")
        week_from = ScmWeek.from_str(week_from) if week_from else week_to - 16
        return self._build_response(get_daily_exception_metrics(week_from, week_to), week_from, week_to)
