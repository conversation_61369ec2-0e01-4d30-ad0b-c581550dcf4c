from procurement.api.schema.types import WEEK
from procurement.managers.exports.po_filter import PoFilter

pimt_export_args_schema = {
    "type": "object",
    "properties": {
        "warehouses": {"type": "string"},
        "date": {"type": "string", "format": "date"},
        "month": {"type": "string", "format": r"^\d{4}-\d{2}$"},
        "week": WEEK,
        "dateFrom": {"type": "string", "format": "date"},
        "dateTo": {"type": "string", "format": "date"},
        "poFilter": {"type": "string", "enum": [e.value for e in PoFilter]},
    },
}
