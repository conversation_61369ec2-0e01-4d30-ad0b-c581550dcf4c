from procurement.constants.hellofresh_constant import InventoryInputType, WhReceivingType

_wh_inventory_types = [InventoryInputType.E2OPEN, InventoryInputType.HJ, InventoryInputType.GSHEET]
_wh_fields = {
    "name": {"type": "string"},
    "otDcs": {"type": "array", "items": {"type": "string"}},
    "otSuppliers": {"type": "array", "items": {"type": "string"}},
    "receivingType": {"type": "string", "enum": list(WhReceivingType)},
    "inventoryType": {"type": "string", "enum": _wh_inventory_types},
    "region": {"type": "string"},
    "regionalDCs": {"type": "array", "items": {"type": "string"}},
    "bobCode": {"type": "string"},
    "is3rdParty": {"type": "boolean"},
    "hjName": {"type": ["string", "null"]},
    "packagingRegions": {"type": "array", "items": {"type": "string"}},
}
_optional_wh_fields = {"hjName", "packagingRegions"}

partners_post = {
    "type": "object",
    "properties": {
        "partnerCode": {"type": "string"},
        **_wh_fields,
    },
    "required": ["partnerCode", *(set(_wh_fields) - _optional_wh_fields)],
}


partners_patch = {
    "type": "object",
    "properties": _wh_fields,
    "required": list((set(_wh_fields) - _optional_wh_fields)),
}


partners_order = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "partnerCode": {"type": "string"},
            "sequenceNumber": {"type": "number"},
        },
        "required": ["partnerCode", "sequenceNumber"],
    },
}
