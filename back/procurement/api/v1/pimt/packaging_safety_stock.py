from flask import request
from jsonschema import validate

from procurement.api import formatting
from procurement.api.base_api import BaseApi
from procurement.api.v1.imt import purchase_order
from procurement.auth.permissions import Permissions
from procurement.managers.imt.purchase_order.po_status import PoStatusResult
from procurement.managers.pimt.packaging_safety_stock import dashboard, po_status
from procurement.managers.pimt.packaging_safety_stock.base import SafetyStockItem
from procurement.managers.pimt.packaging_safety_stock.demand_level import DemandLevelSafetyStockItem

from .schema import safety_stock as schema


class PackagingSafetyStock(BaseApi):
    permission = Permissions.PIMT_PACKAGING_SAFETY_STOCK

    def get(self):
        validate(request.args, schema.safety_stock_get)
        items = dashboard.get_safety_stock_dashboard(pimt_region=request.args["pimtRegion"])

        items.sort(key=self.sorting_key)
        return [self._build_demand_level_item(item) for item in items]

    @staticmethod
    def sorting_key(_item: DemandLevelSafetyStockItem) -> tuple:
        return formatting.get_packaging_ordering_key(_item.item_name, _item.category, _item.liner_group)

    def _build_demand_level_item(self, item: DemandLevelSafetyStockItem) -> dict:
        return {
            "items": [self._build_base_item(sku_item) for sku_item in item.get_child_items()],
            **self._build_base_item(item),
        }

    @staticmethod
    def _build_base_item(item: SafetyStockItem) -> dict:
        return {
            "packagingRegion": item.packaging_region,
            "category": item.category,
            "ownership": item.ownership,
            "itemName": item.item_name,
            "skuCode": ", ".join(item.sku_codes),
            "unitsPerTruckLoad": round(item.units_per_truck_load, 2) if item.units_per_truck_load else None,
            "weekly": {
                str(weekly_item.week): {
                    "hfOwnedBoh": weekly_item.hf_owned_boh,
                    "vendorOwnedBoh": weekly_item.vendor_owned_boh,
                    "hfOwnedPlannedDepletion": round(weekly_item.hf_planned_depletion),
                    "vendorManagedPlannedDepletion": round(weekly_item.vendor_planned_depletion),
                    "liveDemand": weekly_item.live_demand,
                    "incomingPos": weekly_item.incoming_pos,
                    "outboundPos": weekly_item.outbound_pos,
                    "truckLoads": weekly_item.truck_loads,
                    "weeksOnHand": weekly_item.weeks_on_hand,
                    "vendorPosToDc": weekly_item.vendor_pos_to_dc,
                }
                for weekly_item in item.weekly_items
            },
            "linerGroup": formatting.to_string(item.liner_group),
        }


class PackagingSafetyStockPoStatus(BaseApi):
    permission = Permissions.PIMT_PACKAGING_SAFETY_STOCK

    @staticmethod
    def _build_response(item: PoStatusResult) -> dict:
        base_response = purchase_order.build_base_response(item)
        base_response["poType"] = item.po_type

        return base_response

    def get(self):
        validate(request.args, schema.safety_po_status_get)

        items = po_status.get_packaging_safety_stock_po_status(
            packaging_region=request.args["packagingRegion"],
            demand_pipeline=request.args["demandPipeline"],
            pimt_region=request.args["pimtRegion"],
        )
        return [self._build_response(item) for item in items]
