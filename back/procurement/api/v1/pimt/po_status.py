from collections.abc import Iterable
from datetime import date, datetime

from flask import request
from jsonschema import validate

from procurement.api.base_api import FileExportBaseApi
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import MONTH_FORMAT, MONTH_FORMAT_2
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.pimt import partners
from procurement.managers.pimt.export import pimt_po_status_export

from .schema import po_status_schema as schemas


class PimtPoFinancialExport(FileExportBaseApi):
    permission = Permissions.PIMT_PO_STATUS_V1

    _file_suffix = "pimt_financial_export.xlsx"

    def get(self):
        args = request.args
        validate(args, schemas.pimt_export_args_schema)
        warehouses = request.args.get("warehouses")
        whs = partners.get_all_partners(e2open_grn=True)
        if warehouses:
            whs = tuple(filter(lambda w: w.name in warehouses, whs))
        return self._generate_period_export(whs, args)

    @staticmethod
    def _generate_period_export(warehouses: Iterable[Warehouse], args: dict) -> tuple[bytes, str]:
        warehouses_string = "_".join(wh.bob_code.lower() for wh in warehouses)
        po_filter = args["poFilter"]
        if "date" in args:
            day = date.fromisoformat(args["date"])
            return (
                pimt_po_status_export.generate_daily_pimt_financial_excel(
                    day=day, warehouses=warehouses, po_filter=po_filter
                ),
                f"{args['date']}_{warehouses_string}_{PimtPoFinancialExport._file_suffix}",
            )
        if "week" in args:
            week = ScmWeek.from_str(args["week"])
            return (
                pimt_po_status_export.generate_weekly_pimt_financial_excel(
                    week=week, warehouses=warehouses, po_filter=po_filter
                ),
                f"{args['week']}_{warehouses_string}_{PimtPoFinancialExport._file_suffix}",
            )
        if "weekFrom" in args and "weekTo" in args:
            week_from = ScmWeek.from_str(args["weekFrom"])
            week_to = ScmWeek.from_str(args["weekTo"])
            return (
                pimt_po_status_export.generate_weekly_range_pimt_financial_excel(
                    week_from=week_from, week_to=week_to, warehouses=warehouses, po_filter=po_filter
                ),
                f"{args['weekFrom']}_{args['weekTo']}_{warehouses_string}_{PimtPoFinancialExport._file_suffix}",
            )
        if "month" in args:
            month_day = datetime.strptime(args["month"], MONTH_FORMAT).date()
            return (
                pimt_po_status_export.generate_monthly_pimt_financial_excel(
                    day=month_day, warehouses=warehouses, po_filter=po_filter
                ),
                f"{month_day.strftime(MONTH_FORMAT_2)}_{warehouses_string}_{PimtPoFinancialExport._file_suffix}",
            )
        if "dateFrom" in args and "dateTo" in args:
            date_from = date.fromisoformat(args["dateFrom"])
            date_to = date.fromisoformat(args["dateTo"])
            return (
                pimt_po_status_export.generate_custom_pimt_financial_excel(
                    date_from=date_from, date_to=date_to, warehouses=warehouses, po_filter=po_filter
                ),
                f"{args['dateFrom']}_{args['dateTo']}_{warehouses_string}_{PimtPoFinancialExport._file_suffix}",
            )
        raise InvalidRequestException(
            "Either one of 'date', 'week', 'month', 'dateFrom' and 'dateTo', 'weekFrom' and 'weekTo' "
            "or 'poFilter' params should be specified"
        )
