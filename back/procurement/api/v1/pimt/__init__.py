from flask import Blueprint

from .expiring_inventory_report import ExpiringInventoryReport
from .job_sync import PIMTSync
from .metrics import PIMTMetrics
from .ops_main_dashboard import BuyingTool, OpsMainDashboard
from .packaging_safety_stock import PackagingSafetyStock, PackagingSafetyStockPoStatus
from .partners import PackagingRegions, PimtPartnerItem, PimtPartnerOrder, PimtPartners, Regions
from .po_status import PimtPoFinancialExport


def register_api() -> Blueprint:
    api_bp = Blueprint("pimt", __name__, url_prefix="pimt/")
    for path, resource in {
        "sync/": PIMTSync.as_view("pimt-sync"),
        "main-ops/": OpsMainDashboard.as_view("ops-main"),
        "main-ops/<string:sku_code>/buying-tool/": BuyingTool.as_view("buying-tool"),
        "expiring-inventory-report/": ExpiringInventoryReport.as_view("expiring-inventory-report"),
        "partners/": PimtPartners.as_view("partners"),
        "partners/<code>/": PimtPartnerItem.as_view("partner-info"),
        "partner-order/": PimtPartnerOrder.as_view("partner-order"),
        "partner-regions/": Regions.as_view("regions"),
        "partner-packaging-regions/": PackagingRegions.as_view("packaging-regions"),
        "po-financial-export/": PimtPoFinancialExport.as_view("pimt-po-financial-export"),
        "inventory-metrics/": PIMTMetrics.as_view("inventory-metrics"),
        "safety-stock/": PackagingSafetyStock.as_view("safety-stock"),
        "safety-stock-po-status/": PackagingSafetyStockPoStatus.as_view("safety-stock-po-status"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
