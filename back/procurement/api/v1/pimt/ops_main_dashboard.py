from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.pimt import inventory as inventory_service
from procurement.managers.pimt.inventory import InventoryDashboard
from procurement.managers.pimt.ops_main_dashboard import (
    AggregatedInventory,
    OpsMainDashboardModel,
    SkuInventory,
    SkuPartnerInventory,
    TotalDetails,
    TotalItem,
    build_ops_main_dashboard,
)


class OpsMainDashboard(base_api.BaseApi):
    permission = Permissions.PIMT_OPS_DASH_V1

    @staticmethod
    def _build_aggr(item: AggregatedInventory) -> dict:
        updated_date = item.updated_date
        return {
            "site": item.site,
            "inventoryUpdated": updated_date.isoformat() if updated_date else None,
            "isOutdatedDate": item.is_outdated_date,
            "inHouseCost": round(item.in_house_cost, 2),
            "totalOnHandUnits": item.total_on_hand_units,
        }

    @staticmethod
    def _build_ops(item: SkuPartnerInventory) -> dict:
        return {
            "site": item.site,
            "totalOnHand": item.total_on_hand,
            "available": item.available,
            "unitsOnHold": item.units_on_hold,
            "unitsBookedForOutbound": item.units_booked_for_outbound,
            "inBound": item.inbound,
            "outBound": item.outbound,
            "expiredInventory": item.expired_inventory,
        }

    def _build_sku_inventory(self, item: SkuInventory) -> dict:
        return {
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "brands": item.brands,
            "category": item.category,
            "commodityGroup": item.commodity_group,
            "ops": {key: self._build_ops(value) for key, value in item.ops.items()},
            "total": {
                "totalOnHand": item.total.total_on_hand,
                "available": item.total.available,
                "unitsOnHold": item.total.units_on_hold,
                "unitsBookedForOutbound": item.total.units_booked_for_outbound,
                "inBound": item.total.inbound,
                "outBound": item.total.outbound,
                "expiredInventory": item.total.expired_inventory,
            },
        }

    @staticmethod
    def _build_total_item(item: TotalItem) -> dict:
        return {
            "cost": round(item.cost, 2),
            "unitsCount": item.units_count,
        }

    def _build_total(self, item: TotalDetails) -> dict:
        updated_date = item.inventory_updated
        return {
            "inBound": item.inbound,
            "totalOnHand": self._build_total_item(item.total_on_hand),
            "available": item.available,
            "unitsOnHold": item.units_on_hold,
            "unitsBookedForOutbound": item.units_booked_for_outbound,
            "inventoryUpdated": updated_date.isoformat() if updated_date else None,
            "isOutdatedDate": item.is_outdated_date,
            "outBound": item.outbound,
            "expiredInventory": item.expired_inventory,
            "value": item.value,
        }

    def _build_response(self, ops_main_dashboard: OpsMainDashboardModel) -> dict:
        return {
            "items": [self._build_sku_inventory(item) for item in ops_main_dashboard.items],
            "aggr": [self._build_aggr(item) for item in ops_main_dashboard.aggr],
            "total": self._build_total(ops_main_dashboard.total),
        }

    def get(self):
        return self._build_response(build_ops_main_dashboard())


class BuyingTool(base_api.BaseApi):
    permission = Permissions.PIMT_BUYING_TOOL_V1

    def get(self, sku_code: str):
        return [
            self._build_response(item)
            for item in inventory_service.get_expiring_inventory_report(sku_code, available_inv_only=False)
        ]

    @staticmethod
    def _build_response(item: InventoryDashboard) -> dict:
        return {
            "supplierName": item.supplier,
            "receivedDate": item.receive_date.isoformat() if item.receive_date else None,
            "poNumber": item.po_number,
            "casePrice": round(item.case_price, 2) if item.case_price else 0,
            "totalCasesCount": item.cases,
            "unitsPerCaseCount": item.case_size,
            "totalUnitCount": item.total_units,
            "lot": item.lot,
            "shelfLifeCountInDays": item.shelf_life,
            "expirationDate": item.expiration_date.isoformat() if item.expiration_date else None,
            "daysUntilCount": item.days_until_expiration,
            "site": item.partner,
            "isExpired": item.is_expired,
            "palletQuantity": item.pallet_quantity,
            "inventoryStatus": item.state,
        }
