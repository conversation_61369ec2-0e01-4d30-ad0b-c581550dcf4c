from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.pimt import inventory


class ExpiringInventoryReport(base_api.BaseApi):
    permission = Permissions.PIMT_AIT_V1

    def get(self):
        expiring_inventory_data = inventory.get_expiring_inventory_report(available_inv_only=False)
        return [self._build_response(item) for item in expiring_inventory_data]

    @staticmethod
    def _build_response(item: inventory.InventoryDashboard) -> dict:
        return {
            "buyer": item.buyer,
            "skuName": item.sku_name,
            "skuCode": item.sku_code,
            "supplierName": item.supplier,
            "receivedDate": item.receive_date.isoformat() if item.receive_date else None,
            "poNumber": item.po_number,
            "casePrice": round(item.case_price, 2) if item.case_price is not None else None,
            "casesCount": item.cases,
            "unitsPerCaseCount": item.case_size,
            "total": item.total_units,
            "lot": item.lot,
            "commodityGroup": item.commodity_group,
            "shelfLifeInDaysCount": item.shelf_life,
            "expirationDate": item.expiration_date.isoformat() if item.expiration_date else None,
            "daysUntilCount": item.days_until_expiration,
            "unitPrice": round(item.unit_price, 2),
            "nextWeekUsed": str(item.next_week_used),
            "forecast": item.forecast,
            "site": item.partner,
            "source": item.source,
            "costIfDiscarded": round(item.cost_if_discarded, 2),
            "distinction": item.distinction,
            "inventoryStatus": item.state,
        }
