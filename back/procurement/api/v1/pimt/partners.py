from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import InventoryInputType, WhReceivingType
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.pimt import partners
from procurement.managers.pimt.partners import WarehouseOrder

from .schema import partners as partner_schema


class PimtPartnerBase(base_api.BaseApi):
    permission = Permissions.PIMT_PARTNERS_V1


# TODO: full renaming
class PimtPartners(PimtPartnerBase):
    def get(self):
        e2open_grn = request.args.get("filterE2Open") == "true"
        return [
            self._build_response(wh)
            for wh in partners.get_all_partners()
            if not e2open_grn or wh.receiving_type.is_grn or wh.receiving_type.is_hj
        ]

    def post(self):
        data = request.json
        validate(data, partner_schema.partners_post)
        partners.add_warehouse(self._parse_request(data))

    @staticmethod
    def _parse_request(data: dict) -> Warehouse:
        return Warehouse(
            code=data["partnerCode"],
            name=data["name"],
            ot_dcs=data["otDcs"],
            ot_suppliers=data["otSuppliers"],
            region=data["region"],
            regional_dcs=data["regionalDCs"],
            bob_code=data["bobCode"],
            receiving_type=WhReceivingType(data["receivingType"]),
            inventory_type=InventoryInputType(data["inventoryType"]),
            hj_name=data.get("hjName"),
            is_3rd_party=data["is3rdParty"],
            market=context.get_request_context().market,
            order=0,  # ignored
            packaging_regions=data.get("packagingRegions"),
        )

    @staticmethod
    def _build_response(item: Warehouse):
        return {
            "partnerCode": item.code,
            "name": item.name,
            "otDcs": item.ot_dcs,
            "otSuppliers": item.ot_suppliers,
            "order": item.order,
            "receivingType": item.receiving_type,
            "inventoryType": item.inventory_type,
            "region": item.region,
            "regionalDCs": item.regional_dcs,
            "bobCode": item.bob_code if item.bob_code is not None else "",
            "is3rdParty": item.is_3rd_party,
            "hjName": item.hj_name,
            "packagingRegions": item.packaging_regions,
        }


class PimtPartnerItem(PimtPartnerBase):
    @staticmethod
    def _parse_request(code: str, item: dict) -> Warehouse:
        return Warehouse(
            code=code,
            name=item["name"],
            ot_dcs=item["otDcs"],
            ot_suppliers=item["otSuppliers"],
            region=item["region"],
            regional_dcs=item["regionalDCs"],
            bob_code=item["bobCode"],
            receiving_type=WhReceivingType(item["receivingType"]),
            inventory_type=InventoryInputType(item["inventoryType"]),
            hj_name=item.get("hjName"),
            is_3rd_party=item["is3rdParty"],
            market=context.get_request_context().market,
            order=0,  # ignored during update
            packaging_regions=item.get("packagingRegions"),
        )

    def patch(self, code: str):
        data = request.json
        validate(data, partner_schema.partners_patch)
        partners.update_warehouse(self._parse_request(code, data))

    def delete(self, code: str):
        partners.delete_warehouse(code)
        return None, 204


class PimtPartnerOrder(PimtPartnerBase):
    def post(self):
        data = request.json
        validate(data, partner_schema.partners_order)
        partners.update_order(self._parse_request(data))

    @staticmethod
    def _parse_request(data: list[dict]):
        return [WarehouseOrder(wh_code=order["partnerCode"], order=order["sequenceNumber"]) for order in data]

    def get(self):
        return [self._build_response(item) for item in partners.get_order()]

    @staticmethod
    def _build_response(order: WarehouseOrder):
        return {
            "partnerCode": order.wh_code,
            "sequenceNumber": order.order,
        }


class Regions(PimtPartnerBase):
    def get(self):
        week = ScmWeek.current_week()
        return list(
            {
                site: dc
                for brand in brand_admin.get_brand_ids(week)
                for site, dc in dc_admin.get_all_dcs(week=week, brand=brand, only_enabled=False).items()
            }
        )


class PackagingRegions(PimtPartnerBase):
    def get(self):
        week = ScmWeek.current_week()
        return list(
            {
                dc.bob_code
                for brand in brand_admin.get_brand_ids(week)
                for dc in dc_admin.get_all_dcs(week=week, brand=brand, only_enabled=False).values()
                if dc.bob_code
            }
        )
