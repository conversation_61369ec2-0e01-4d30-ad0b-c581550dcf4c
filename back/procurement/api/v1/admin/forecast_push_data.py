from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.forecasts import forecast_upload_data
from procurement.managers.forecasts.forecast_upload_data import ForecastUploadItem


class ForecastPushData(base_api.BaseApi):
    permission = Permissions.FORECAST_PUSH_DATA

    def get(self):
        return [self._build_response(item) for item in forecast_upload_data.get_forecast_upload_data()]

    @staticmethod
    def _build_response(item: ForecastUploadItem) -> dict:
        return {
            "skuCode": item.sku_code,
            "week": str(item.week),
            "day": item.day,
            "site": item.site,
            "brand": item.brand,
            "quantity": item.quantity,
        }
