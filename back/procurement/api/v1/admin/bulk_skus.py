import logging
from collections.abc import Iterable
from typing import Any

from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi
from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import ALL_BRANDS
from procurement.data.dto.inventory.bulk_skus import BulkSkuWithNames
from procurement.managers.imt import bulk_sku as bulk_sku_service
from procurement.managers.imt.bulk_sku import BulkSkusData

from .schema import bulk_skus_schema as validation_schema

logger = logging.getLogger(__name__)


class BulkSkus(BaseApi):
    permission = Permissions.IMT_BULK_SKU_V1

    def get(self):
        return self._build_response(bulk_sku_service.get_bulk_skus_with_names())

    def post(self):
        self._upsert(is_edit=False)

    def patch(self):
        self._upsert(is_edit=True)

    def _upsert(self, is_edit: bool):
        request_data = request.get_json()
        validate(request_data, validation_schema.bulk_skus_schema)

        bulk_sku = self._parse_request(request_data)
        BulkSkus.validate_sku_codes(bulk_sku)
        bulk_sku_service.upsert_bulk_sku(bulk_sku, is_edit=is_edit)

    def delete(self):
        request_data = request.args
        validate(request_data, validation_schema.bulk_skus_schema_delete)

        bulk_sku = self._parse_request(request_data)
        BulkSkus.validate_sku_codes(bulk_sku)
        bulk_sku_service.delete_bulk_sku(bulk_sku)

    @staticmethod
    def validate_sku_codes(bulk_sku: BulkSkusData):
        code_name_dict = bulk_sku_service.get_sku_name_by_sku(bulk_sku)
        for sku_code in (bulk_sku.bulk_sku_code, bulk_sku.packaged_sku_code):
            if not code_name_dict.get(sku_code):
                raise ValueError(f"SKU Name not found for SKU Code '{sku_code}'")

    @staticmethod
    def _build_response(skus_data: Iterable[BulkSkuWithNames]) -> list[dict[str, str]]:
        return [
            {
                "brands": list(sku.brands),
                "bulkSkuCode": sku.bulk_sku_code,
                "packagedSkuCode": sku.packaged_sku_code,
                "pickConversion": sku.pick_conversion,
                "isMasterSku": sku.is_master_sku,
                "bulkSkuName": sku.bulk_sku_name,
                "packagedSkuName": sku.packaged_sku_name,
            }
            for sku in skus_data
        ]

    @staticmethod
    def _parse_request(sku_data: dict[str, Any]) -> BulkSkusData:
        brands = sku_data.get("brands", [])
        if ALL_BRANDS in brands:
            brands = [ALL_BRANDS]
        return BulkSkusData(
            brands=frozenset(brands),
            bulk_sku_code=sku_data["bulkSkuCode"],
            packaged_sku_code=sku_data["packagedSkuCode"],
            pick_conversion=sku_data.get("pickConversion", 1),
            is_master_sku=sku_data.get("isMasterSku", False),
        )
