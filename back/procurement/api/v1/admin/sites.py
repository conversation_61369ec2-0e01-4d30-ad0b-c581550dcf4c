from typing import Any

from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.data.dto.inventory.site import SiteSequence
from procurement.managers.admin import brand_admin
from procurement.managers.admin.dc_admin import commands as dc_commands
from procurement.managers.admin.dc_admin import queries as dc_queries
from procurement.managers.admin.dc_admin.commands import WeeklyConfigInput
from procurement.managers.admin.dc_admin.constants import DcProperty
from procurement.managers.admin.dc_admin.dto import DcConfig
from procurement.repository.ordering import supplier as supplier_repo

from .schema import site_schema as schemas


class SiteAdminBase(base_api.BaseApi):
    permission = Permissions.IMT_REGION_V1


class SiteConfigAdmin(SiteAdminBase):
    def get(self):
        request_data = request.args
        validate(request_data, schemas.config_get)
        week = ScmWeek.from_str(request_data["week"])
        brand = request_data.get("brand")
        archived_threshold = ScmWeek.current_week() - 5
        supplier_map = supplier_repo.get_supplier_name_by_code()
        required_fields = {it.name for it in DcProperty.get_required()}
        return [
            self._build_response(item, supplier_map, archived_threshold, required_fields)
            for item in self._order_admin_site_configs(dc_queries.get_configs(week, brand))
        ]

    @staticmethod
    def _order_admin_site_configs(admin_site_configs: list[DcConfig]) -> list[DcConfig]:
        brands = brand_admin.get_brands(with_disabled=True)
        return sorted(admin_site_configs, key=lambda c: (brands.get(c.brand).order, c.sequence_number))

    @staticmethod
    def _build_response(
        item: DcConfig, supplier_map: dict[int, str], archived_threshold: ScmWeek, required_fields: set[DcProperty]
    ) -> dict[str, Any]:
        return {
            "brand": item.brand,
            "week": str(item.week),
            "receivingType": item.receiving_type,
            "inventoryType": item.inventory_type,
            "source": item.source.name,
            "enabled": item.enabled,
            "isArchived": not item.enabled and item.week < archived_threshold,
            "orderingToolName": item.ordering_tool_name,
            "id": item.id,
            "hjName": item.high_jump_name,
            "siteShort": item.sheet_name,
            "siteFull": item.site_name,
            "is3pl": item.is_3pl,
            "isFulfilmentOnly": item.is_fulfilment_only,
            "isHjAutostore": item.is_hj_autostore,
            "optional": {key: value for (key, value) in item.properties.items() if key not in required_fields},
            "bobCode": item.bob_code,
            "timezone": str(item.timezone),
            "dcAzureRole": item.dc_azure_role,
            "hnDataSource": item.hybrid_needs_data_source,
            "isWipConsumptionEnabled": item.is_wip_consumption_enabled,
            "supplierNames": list(map(supplier_map.get, item.supplier_codes)),
        }

    def post(self):
        validate(request.json, schemas.config_post)
        self._trim_request(request.json)
        self._validate_week(request.json["week"])
        self._validate_supplier_names(request.json["supplierNames"])

        dc_commands.upsert_weekly_config(
            WeeklyConfigInput(
                brand=request.json["brand"],
                week=ScmWeek.from_str(request.json["week"]),
                receiving_type=request.json["receivingType"],
                inventory_type=InventoryInputType(request.json["inventoryType"]),
                source=request.json["source"],
                enabled=request.json["enabled"],
                ordering_tool_name=request.json["orderingToolName"],
                hj_name=request.json["hjName"],
                site_short=request.json["siteShort"],
                site_full=request.json["siteFull"],
                is_3pl=request.json["is3pl"],
                is_hj_autostore=request.json["isHjAutostore"],
                is_fulfillment_only=request.json["isFulfilmentOnly"],
                optional=request.json["optional"],
                timezone=request.json["timezone"],
                bob_code=request.json["bobCode"],
                dc_azure_role=request.json["dcAzureRole"],
                hybrid_needs_data_source=request.json["hnDataSource"],
                is_wip_consumption_enabled=request.json.get("isWipConsumptionEnabled", False),
                supplier_names=request.json.get("supplierNames", []),
            ),
        )
        return None, 201

    @staticmethod
    def _validate_week(week: str) -> None:
        try:
            ScmWeek.from_str(week)
        except ValueError as value_error:
            raise InputParamsValidationException("Week is invalid") from value_error

    @staticmethod
    def _validate_supplier_names(supplier_names: list[str]) -> None:
        valid_supplier_names = supplier_repo.get_all_supplier_names()
        for supplier_name in supplier_names:
            if supplier_name not in valid_supplier_names:
                raise InputParamsValidationException(f"Supplier name '{supplier_name}' is invalid")

    def delete(self):
        request_data = request.args
        validate(request_data, schemas.config_delete)
        brand = request_data["brand"]
        site = request_data["site"]
        dc_commands.delete_configs(brand, site)
        return {"msg": f"All configs with brand {brand} and dc {site} successfully deleted"}, 200


class Sites(SiteAdminBase):
    def post(self):
        new_order = request.json
        validate(new_order, schemas.site_reorder)
        dc_commands.update_sequence({site["siteShort"]: site["sequenceNumber"] for site in new_order})
        return {"msg": "Items successfully reordered"}, 200

    def get(self):
        return [self._build_response(item) for item in dc_queries.get_market_sites_sequence()]

    @staticmethod
    def _build_response(site: SiteSequence) -> dict[str, Any]:
        return {"siteShort": site.id, "siteFull": site.name, "sequenceNumber": site.sequence_number}


class SiteNextConfigs(SiteAdminBase):
    def get(self):
        request_data = request.args
        validate(request_data, schemas.config_get_next_weeks)
        week = ScmWeek.from_str(request_data["week"])
        brand = request_data["brand"]
        site = request_data["site"]
        return [str(config_week) for config_week in dc_queries.get_next_configs(week, brand, site).values()]


class SiteConfigPropertiesFields(SiteAdminBase):
    def get(self):
        return {
            "optional": [self._build_response(it) for it in DcProperty.get_optional() if it.display],
            "required": [self._build_response(it) for it in DcProperty.get_required() if it.display],
        }

    @staticmethod
    def _build_response(item: DcProperty):
        res = {"key": item.name, "label": item.label, "description": item.description, "type": item.type.name}
        if item.values:
            res["values"] = item.values()
        return res
