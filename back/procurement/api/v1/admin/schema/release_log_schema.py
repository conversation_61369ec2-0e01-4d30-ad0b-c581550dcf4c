release_log_schema = {
    "type": "object",
    "properties": {
        "releaseDate": {"type": "string"},
        "features": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "dcUsersAvailable": {"type": "boolean"},
                    "description": {"type": "string"},
                    "type": {"type": "string", "enum": ["NEW", "FIX", "ONGOING"]},
                },
                "required": ["dcUsersAvailable", "description", "type"],
            },
        },
    },
    "required": ["features", "releaseDate"],
}
