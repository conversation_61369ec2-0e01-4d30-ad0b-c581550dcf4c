post_brand_order = {
    "type": "array",
    "items": {"type": "string", "maxLength": 2},
    "minItems": 1,
    "uniqueItems": True,
}

post_brand_config = {
    "type": "object",
    "properties": {
        "code": {"type": "string", "maxLength": 2},
        "name": {"type": "string"},
        "enabled": {"type": "boolean"},
        "consolidated": {"type": "boolean"},
        "weekStart": {"type": "string"},
        "weekLength": {"type": "integer", "minimum": 1, "maximum": 14},
        "countryCode": {"type": "string"},
    },
    "required": ["code", "name", "enabled", "consolidated", "weekStart", "weekLength", "countryCode"],
}
