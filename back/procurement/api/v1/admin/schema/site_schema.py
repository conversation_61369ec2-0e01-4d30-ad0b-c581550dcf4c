from procurement.constants.hellofresh_constant import (
    TIMEZONE_NAMES,
    HybridNeedsDataSourceType,
    InventoryInputType,
    ReceiveInputType,
)

config_object = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "receivingType": {"type": "string", "enum": list(ReceiveInputType)},
        "inventoryType": {"type": "string", "enum": list(InventoryInputType)},
        "hnDataSource": {"type": "string", "enum": list(HybridNeedsDataSourceType)},
        "supplierNames": {"type": "array", "items": {"type": "string"}},
        "source": {"type": "string", "enum": ["GSHEET", "APP", "HIGHJUMP"]},
        "enabled": {"type": "boolean"},
        "is3pl": {"type": "boolean"},
        "isFulfilmentOnly": {"type": "boolean"},
        "isHjAutostore": {"type": "boolean"},
        "orderingToolName": {"type": "string"},
        "hjName": {"type": "string"},
        "siteShort": {"type": "string"},
        "siteFull": {"type": "string"},
        "timezone": {"type": "string", "enum": list(TIMEZONE_NAMES)},
        "dcAzureRole": {"type": ["string", "null"]},
        "bobCode": {"type": ["string"]},
        "optional": {"type": "object"},
        "isWipConsumptionEnabled": {"type": "boolean"},
    },
    "required": [
        "brand",
        "week",
        "receivingType",
        "inventoryType",
        "enabled",
        "orderingToolName",
        "supplierNames",
        "hjName",
        "siteShort",
        "siteFull",
        "is3pl",
        "isHjAutostore",
        "source",
        "timezone",
        "dcAzureRole",
        "bobCode",
    ],
    "additionalProperties": False,
}

config_post = config_object


config_get = {
    "type": "object",
    "properties": {
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "brand": {"type": "string"},
    },
    "required": ["week"],
}

config_get_next_weeks = {
    "type": "object",
    "properties": {
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "brand": {"type": "string"},
        "site": {"type": "string"},
    },
    "required": ["week", "brand", "site"],
}

site_reorder = {
    "type": "array",
    "minItems": 1,
    "items": {
        "type": "object",
        "properties": {"siteShort": {"type": "string"}, "sequenceNumber": {"type": "integer"}},
        "required": ["siteShort", "sequenceNumber"],
    },
}

config_delete = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "site": {"type": "string"},
    },
    "required": ["brand", "site"],
}
