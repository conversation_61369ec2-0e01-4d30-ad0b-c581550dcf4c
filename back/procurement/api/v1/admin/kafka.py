import logging

from flask import request

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.pubsub.events import ResetKafkaTopicEvent
from procurement.core.pubsub.publisher import publish_event

from .superuser_utils import superuser_pass

logger = logging.getLogger(__name__)


class KafkaTopicAdmin(base_api.BaseApi):
    permission = Permissions.IMT_CACHE_V1

    @superuser_pass
    def delete(self, topic_name):
        logger.info("User '%s' requested to reset offset on '%s'", self.request_context.user_info.email, topic_name)
        if not topic_name:
            return {"msg": "Empty topic name"}, 400
        read_last = request.args.get("readLast")
        if read_last is not None:
            read_last = int(read_last)

        publish_event(ResetKafkaTopicEvent(topic=topic_name, read_last=read_last))

        return {"msg": "Done"}, 200
