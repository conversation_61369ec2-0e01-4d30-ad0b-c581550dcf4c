from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.api.v1.admin.schema import brand_schema as schemas
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek, ScmWeekConfig, Weekday
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.request_utils import context
from procurement.data.dto.config import Brand
from procurement.managers.admin import brand_admin


class BrandAdminBase(base_api.BaseApi):
    permission = Permissions.IMT_REGION_V1


_SHIFT_TITLES = tuple(
    f"Calendar Week {f'{week_shift:+} ' if week_shift else ''}{weekday}"
    for week_shift in range(-2, 2)
    for weekday in Weekday
)
_SHIFT_TITLES_INDEX = {title: i for i, title in enumerate(_SHIFT_TITLES)}


def _config_week_shift_to_title(shift: int) -> str:
    return _SHIFT_TITLES[shift + len(Weekday) * 2]


def _shift_title_to_config_shift(title: str) -> int:
    return _SHIFT_TITLES_INDEX[title] - len(Weekday) * 2


class Brands(BrandAdminBase):
    def get(self):
        return list(brand_admin.get_all_brand_ids_ordered())

    def post(self):
        new_order = request.json
        validate(new_order, schemas.post_brand_order)
        brand_admin.update_order(new_order)


class BrandConfigs(BrandAdminBase):
    def get(self, week: str):
        week = ScmWeek.from_str(week)
        return [
            {
                "code": brand.code,
                "name": brand.name,
                "consolidated": brand.consolidated,
                "enabled": brand.enabled,
                "weekStart": _config_week_shift_to_title(brand.scm_week_config.calendar_mon_shift),
                "weekLength": brand.scm_week_config.length,
                "configWeek": str(brand.config_week),
                "countryCode": brand.country_code,
            }
            for brand in brand_admin.get_brands(week, with_disabled=True).values()
        ]

    def post(self, week: str):
        brand_config = request.json
        week = ScmWeek.from_str(week)
        self._validate_brand_fields(brand_config)
        week_config = ScmWeekConfig(
            calendar_mon_shift=_shift_title_to_config_shift(brand_config["weekStart"]),
            length=brand_config["weekLength"],
        )
        brand = Brand(
            code=brand_config["code"],
            name=brand_config["name"],
            market=context.get_request_context().market,
            enabled=brand_config["enabled"],
            consolidated=brand_config["consolidated"],
            scm_week_config=week_config,
            config_week=week,
            order=0,  # not updated here
            country_code=brand_config["countryCode"],
        )
        brand_admin.edit_brand_config(brand)

    @staticmethod
    def _validate_brand_fields(brand_config: dict):
        validate(brand_config, schemas.post_brand_config)
        if brand_config["weekStart"] not in _SHIFT_TITLES_INDEX:
            raise InvalidRequestException(f"Invalid Week Start, the value must one of {_SHIFT_TITLES}")


class WeekShiftTitles(BrandAdminBase):
    def get(self):
        return list(_SHIFT_TITLES)
