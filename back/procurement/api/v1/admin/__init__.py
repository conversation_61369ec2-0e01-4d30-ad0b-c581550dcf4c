from flask import Blueprint

from .brands import BrandConfigs, Brands, WeekShiftTitles
from .bulk_skus import BulkSkus
from .cache import CacheAdmin, CacheKeysAdmin
from .forecast_push_data import ForecastPushData
from .gsheet_admin import GSheetDocs
from .jobs import JobsInfoByStatus
from .kafka import KafkaTopicAdmin
from .markets import Markets
from .metrics_check import CheckMetrics
from .release_log import ReleaseLogAdmin
from .sites import SiteConfigAdmin, SiteConfigPropertiesFields, SiteNextConfigs, Sites
from .supplier import SupplierNamesAdmin


def register_api() -> Blueprint:
    api_bp = Blueprint("admin", __name__, url_prefix="admin/")
    for path, resource in {
        "gsheet/": GSheetDocs.as_view("gsheets"),
        "site/config/": SiteConfigAdmin.as_view("site-configs"),
        "site/config/next_configs/": SiteNextConfigs.as_view("site-next-configs"),
        "site/properties/": SiteConfigPropertiesFields.as_view("site-properties"),
        "site/": Sites.as_view("sites"),
        "market/": Markets.as_view("market"),
        "brand/": Brands.as_view("brands"),
        "brand/config/<week>/": BrandConfigs.as_view("brand-config"),
        "brand/config/week-start-shifts/": WeekShiftTitles.as_view("week-shifts"),
        "cache/<key_pattern>/": CacheAdmin.as_view("cache-admin"),
        "cache/keys/<key_pattern>/": CacheKeysAdmin.as_view("cache-keys"),
        "metrics-test/": CheckMetrics.as_view("metrics-test"),
        "kafka-topics/<topic_name>/": KafkaTopicAdmin.as_view("kafka-admin"),
        "jobs-info/": JobsInfoByStatus.as_view("job-info"),
        "bulk-skus/": BulkSkus.as_view("bulk-skus"),
        "release-log/": ReleaseLogAdmin.as_view("release-log"),
        "forecast-push-data/": ForecastPushData.as_view("forecast-push-data"),
        "supplier-names/": SupplierNamesAdmin.as_view("supplier-names"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
