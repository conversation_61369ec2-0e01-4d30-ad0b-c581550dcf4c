from flask import request

from procurement.api import base_api
from procurement.api.v1.admin.superuser_utils import superuser_pass
from procurement.auth.permissions import Permissions
from procurement.managers.datasync.framework import queries


class JobsInfoByStatus(base_api.BaseApi):
    permission = Permissions.IMT_CACHE_V1

    @superuser_pass
    def get(self):
        job_statuses = request.args.get("job_statuses")
        is_detailed: bool = request.args.get("details", default=False, type=lambda v: v.lower() == "true")
        queue_name: str = request.args.get("queue")
        if job_statuses:
            job_statuses = job_statuses.split(",")
        return queries.get_job_info_by_status(is_detailed, queue_name, job_statuses)
