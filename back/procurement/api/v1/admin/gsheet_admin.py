from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.api.v1.admin.schema import gsheet_admin_schema
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.managers.admin import gsheet_admin as gsheet_service


class GSheetDocs(base_api.BaseApi):
    permission = Permissions.IMT_GHEET_V1

    def get(self):
        args = request.args
        validate(args, gsheet_admin_schema.gsheet_get)
        project = args.get("project")
        brand = args.get("brand")
        week = ScmWeek.from_str(args["week"]) if args.get("week") else None
        return gsheet_service.get_docs(project=project, scm_week=week, brand=brand)

    def post(self):
        body = request.get_json()
        validate(body, gsheet_admin_schema.gsheet_admin_post_body)
        scm_week = ScmWeek.from_str(body.get("week")) if body.get("week") else None

        gsheet_id = gsheet_service.save_gsheet_admin(scm_week=scm_week, meta_id=body["meta_id"], gsheet_url=body["url"])
        return {"id": gsheet_id}

    def delete(self):
        body = request.get_json()
        validate(body, gsheet_admin_schema.gsheet_admin_delete_body)
        scm_week = ScmWeek.from_str(body.get("week")) if body.get("week") else None
        gsheet_service.delete_gsheet_url(scm_week=scm_week, meta_id=body["meta_id"])
