from flask import request
from jsonschema.validators import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.admin import supplier_codes

from .schema import supplier_names_schema


class SupplierNamesAdmin(base_api.BaseApi):
    permission = Permissions.IMT_REGION_V1

    def get(self):
        validate(request.args, supplier_names_schema.supplier_names_get)
        search_key = request.args["searchKey"]
        return supplier_codes.get_supplier_names_for_autocomplete(search_key)
