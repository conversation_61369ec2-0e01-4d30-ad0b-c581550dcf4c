import logging

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.cache_utils.constants import cache

from .superuser_utils import superuser_pass

logger = logging.getLogger(__name__)


class CacheAdmin(base_api.BaseApi):
    permission = Permissions.IMT_CACHE_V1

    @superuser_pass
    def delete(self, key_pattern):
        logger.info(
            "User '%s' requested to remove data from cache by key '%s'",
            self.request_context.user_info.email,
            key_pattern,
        )
        if not key_pattern:
            return {"msg": "Empty key pattern"}, 400
        keys_to_remove = [key_pattern]
        if key_pattern.endswith("*"):
            keys_to_remove = cache.keys(pattern=key_pattern)
        for key in keys_to_remove:
            cache.delete(key)
        return {"msg": f"Done. Removed {len(keys_to_remove)} keys"}, 200

    @superuser_pass
    def get(self, key_pattern):
        logger.info(
            "User '%s' requested to get data from cache by key '%s'", self.request_context.user_info.email, key_pattern
        )
        if not key_pattern:
            return {"msg": "Empty key pattern"}, 400
        keys_to_get = [key_pattern]
        if key_pattern.endswith("*"):
            keys_to_get = cache.keys(pattern=key_pattern)
        vales = {key: str(get_value_from_cache(key)) for key in keys_to_get}
        return vales


class CacheKeysAdmin(base_api.BaseApi):
    permission = Permissions.IMT_CACHE_V1

    @superuser_pass
    def get(self, key_pattern):
        logger.info(
            "User '%s' requested to remove data from cache by key '%s'",
            self.request_context.user_info.email,
            key_pattern,
        )
        if not key_pattern:
            return {"msg": "Empty key pattern"}, 400
        if key_pattern.endswith("*"):
            keys = cache.keys(pattern=key_pattern)
            return {key: True for key in keys}
        return {key_pattern: cache.exists(key_pattern) > 0}


def get_value_from_cache(key):
    key_type = cache.type(key)
    if key_type == b"hash":
        return cache.hgetall(key)
    if key_type == b"set":
        return cache.mget(key)
    if key_type == b"list":
        return cache.lrange(key, 0, -1)
    return cache.get(key)
