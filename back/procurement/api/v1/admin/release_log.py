from datetime import date
from typing import Any

from flask import request
from jsonschema.validators import validate

from procurement.api.base_api import BaseApi
from procurement.api.v1.admin.schema import release_log_schema
from procurement.auth.permissions import AvailableUserRoles, Permissions
from procurement.data.dto.procurement.release_log import ReleaseLog, ReleaseLogFeature
from procurement.managers.admin import release_log


class ReleaseLogBase(BaseApi):
    @staticmethod
    def _build_response(release_logs: list[ReleaseLog]):
        return [
            {
                "releaseDate": item.release_date,
                "features": [
                    {
                        "type": feature.feature_type,
                        "description": feature.description,
                        "dcUsersAvailable": feature.dc_users_available,
                    }
                    for feature in item.features
                ],
            }
            for item in release_logs
        ]

    def _is_dc_user(self) -> bool:
        return AvailableUserRoles.DC_USER in self.request_context.user_info.roles


class ReleaseLogAdmin(ReleaseLogBase):
    permission = Permissions.IMT_RELEASE_LOG

    def _build_response_with_page_keys(self, release_logs: list[ReleaseLog], page_keys: list[date]) -> dict[str, Any]:
        return {
            "msg": "success",
            "data": self._build_response(release_logs),
            "pageKeys": page_keys,
        }

    @staticmethod
    def _parse_request(data: dict) -> ReleaseLog:
        return ReleaseLog(
            release_date=date.fromisoformat(data["releaseDate"]),
            features=[
                ReleaseLogFeature(
                    dc_users_available=feature["dcUsersAvailable"],
                    description=feature["description"],
                    feature_type=feature["type"],
                )
                for feature in data["features"]
            ],
        )

    def get(self):
        date_from_str = request.args.get("dateFrom")
        date_from = date.fromisoformat(date_from_str) if date_from_str else None
        release_logs, page_keys = release_log.get_release_logs_page(
            for_dc_user=self._is_dc_user(),
            date_from=date_from,
        )
        return self._build_response_with_page_keys(release_logs, page_keys)

    def post(self):
        body = request.get_json()
        validate(body, release_log_schema.release_log_schema)
        release_log.create_release_log(self._parse_request(body))

        return None, 201

    def put(self):
        body = request.get_json()
        validate(body, release_log_schema.release_log_schema)
        release_log.update_release_log(self._parse_request(body))
