import logging
from functools import wraps

from flask import request

from procurement.core.config_utils import config

logger = logging.getLogger(__name__)


def superuser_pass(func):
    @wraps(func)
    def decorator(*args, **kwargs):
        super_pass = request.args.get("superuser_pass")
        if not config.get("superuser_pass"):
            logger.warning("Please define 'superuser_pass' parameter in config")
            return {"msg": "Superuser key not configured"}, 405  # Method Not Allowed
        if not super_pass or super_pass != config.get("superuser_pass"):
            return {"msg": "Empty or not matched superuser key"}, 401
        return func(*args, **kwargs)

    return decorator
