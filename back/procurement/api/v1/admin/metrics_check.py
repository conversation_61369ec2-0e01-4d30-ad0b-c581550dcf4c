import logging

from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.metrics import ApplicationMetrics

from .schema import metrics as schema
from .superuser_utils import superuser_pass

logger = logging.getLogger(__name__)


class CheckMetrics(base_api.BaseApi):
    permission = Permissions.IMT_METRICS_V1

    @superuser_pass
    def post(self):
        args = request.args
        validate(args, schema.metrics_get)
        severity = args["severity"]
        logger.info(
            "User %s initiate to generate metrics with severity: %s", self.request_context.user_info.email, severity
        )
        if severity == "critical":
            ApplicationMetrics.critical_exceptions(service="test", message="Test message")
        elif severity == "non-critical":
            ApplicationMetrics.non_critical_exceptions(service="test", message="Test message")
        return {"msg": "Done"}, 200
