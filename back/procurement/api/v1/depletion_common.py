from procurement.api import formatting
from procurement.client.googlesheets.googlesheet_utils import FRONT_FORMAT
from procurement.managers.depletion.result import AggregatedIngredientDepletionResult, IngredientDepletionResult
from procurement.managers.depletion.weekly import AggregatedDepletionWeeklyItem, AllocationPriceRange
from procurement.managers.imt.ingredients_depletion.buffer_analysis import BufferAnalysis
from procurement.managers.imt.ingredients_depletion.bulk import BulkValues
from procurement.managers.imt.ingredients_depletion.daily import DailyDepletion
from procurement.managers.imt.ingredients_depletion.weekly import DepletionWeeklyItem
from procurement.managers.imt.ingredients_depletion.work_order import SiteDWorkOrder


def build_response(depletion_item: IngredientDepletionResult) -> dict:
    return {
        "brand": ", ".join(sorted(depletion_item.brands)),
        "site": ", ".join(sorted(depletion_item.sites)),
        "sku": depletion_item.sku_code,
        "skuName": depletion_item.sku_name,
        "unitOfMeasure": formatting.to_string(depletion_item.unit_of_measure),
        "category": depletion_item.category,
        "commodityGroup": depletion_item.commodity_group,
        "buyer": depletion_item.buyer,
        "impactedRecipes": ", ".join(sorted(depletion_item.impacted_recipes)),
        "plan": depletion_item.plan,
        "liveRowNeed": depletion_item.live_row_need,
        "forecastOscar": depletion_item.forecast,
        "plannedProduction": depletion_item.planned_production,
        "rowForecast": depletion_item.row_forecast,
        "delta": depletion_item.delta,
        "sn": depletion_item.supplement_need,
        "supplementNeedHj": depletion_item.supplement_need_hj,
        "weekly": build_weekly_overview(depletion_item.weekly_overview),
        "bulkValues": _build_bulk_values(depletion_item.bulk_values),
        "bufferAnalysis": _build_buffer_analysis(depletion_item.buffer_analysis),
        "daily": _build_daily_values(depletion_item.daily),
        "endOfWeekInventory": depletion_item.end_of_week_inventory,
        "criticalDelivery": depletion_item.critical_delivery_status,
        "hjUnit": formatting.to_string(depletion_item.hj_unit),
        "workOrder": _build_work_order(depletion_item.work_order_section),
    }


def _build_buffer_analysis(buffer_analysis: BufferAnalysis) -> dict:
    if not buffer_analysis:
        return {
            "poBuffer": None,
            "bufferQuantityNeeded": None,
            "supplier": None,
            "caseTotal": None,
            "caseYield": None,
            "caseCost": None,
            "totalCost": None,
            "allowedBuffer": None,
        }

    return {
        "poBuffer": formatting.round_if_available(buffer_analysis.po_buffer, 4),
        "bufferQuantityNeeded": formatting.round_if_available(buffer_analysis.quantity_needed),
        "supplier": buffer_analysis.supplier,
        "caseTotal": buffer_analysis.case_total,
        "caseYield": buffer_analysis.case_yield,
        "caseCost": formatting.round_if_available(buffer_analysis.case_cost, 2),
        "totalCost": formatting.round_if_available(buffer_analysis.total_cost, 2),
        "allowedBuffer": buffer_analysis.allowed_buffer,
    }


def _build_bulk_values(bulk_values: BulkValues) -> dict:
    return {
        "bulkUnitsOrdered": bulk_values.bulk_units_ordered,
        "bulkUnitsReceived": bulk_values.bulk_units_received,
        "delta": bulk_values.delta,
        "bulkUnitsInHj": bulk_values.bulk_units_in_hj,
        "bulkDiscards": bulk_values.bulk_discards,
    }


def _build_daily_values(daily_values: DailyDepletion | None) -> dict:
    return {
        day.strftime(FRONT_FORMAT): {
            "inventory": val.inventory,
            "unitsDelivered": val.units_delivered,
            "discards": val.discards,
            "onHand": val.on_hand,
            "productionNeeds": val.production_needs,
            "eodInventoryProduction": val.eod_inventory_production,
            "unitsOnOrder": val.units_on_order,
            "eodInventoryOrder": val.eod_inventory_order,
            "statusInWeek": val.status_in_week,
            "totalOnHand": val.total_on_hand,
            "totalProductionNeed": val.total_production_need,
            "status": val.supplemented_need_status,
            "totalOnHandMinusTotalProductionNeed": val.total_on_hand_minus_total_production_need,
        }
        for day, val in (daily_values.by_day.items() if daily_values else {})
    }


def _build_work_order(work_order: SiteDWorkOrder | None) -> dict:
    if not work_order:
        return {
            "requiredLbs": None,
            "stagedLbs": None,
            "bufferedForecast": None,
            "woRowNeed": None,
            "stagedPlusHjMinusReceived": None,
            "hjMinusWoRowNeed": None,
            "hjPlusToBeDeliveredMinusWoRowNeed": None,
        }

    return {
        "requiredLbs": formatting.round_if_available(work_order.required_lbs, 2),
        "stagedLbs": formatting.round_if_available(work_order.staged_lbs, 2),
        "bufferedForecast": formatting.round_if_available(work_order.buffered_forecast, 2),
        "woRowNeed": formatting.round_if_available(work_order.wo_row_need, 2),
        "stagedPlusHjMinusReceived": formatting.round_if_available(work_order.staged_plus_hj_minus_received, 2),
        "hjMinusWoRowNeed": formatting.round_if_available(work_order.hj_minus_wo_row_need, 2),
        "hjPlusToBeDeliveredMinusWoRowNeed": formatting.round_if_available(
            work_order.hj_plus_to_be_delivered_minus_wo_row_need, 2
        ),
    }


def build_aggregated_response(depletion_item: AggregatedIngredientDepletionResult) -> dict:
    child_depletion = depletion_item.child_depletion
    return {
        **build_response(depletion_item),
        "childItems": ([build_response(item) for item in child_depletion] if len(child_depletion) > 1 else []),
    }


def build_allocation_price(
    allocation_price_range: AllocationPriceRange | None, is_aggr_depl_weekly_item: bool
) -> str | None | dict[str, str]:
    if not allocation_price_range:
        return None
    return (
        {"min": str(allocation_price_range.min_price), "max": str(allocation_price_range.max_price)}
        if is_aggr_depl_weekly_item
        else str(allocation_price_range.min_price)
    )


def build_weekly_overview(weekly: DepletionWeeklyItem) -> dict:
    return {
        "allocationPrice": build_allocation_price(
            weekly.allocation_price_range, isinstance(weekly, AggregatedDepletionWeeklyItem)
        ),
        "unitsNeeded": weekly.units_needed,
        "unitsOrdered": weekly.units_ordered,
        "unitsReceived": weekly.units_received,
        "hjDiscards": round(weekly.hj_discards, 2) if weekly.hj_discards is not None else None,
        "hjAutostoreInventory": weekly.hj_autostore_inventory,
        "hjAutostoreInventoryPlusHjInHouse": weekly.hj_autostore_inv_plus_in_house,
        "unitsInHouseHj": weekly.hj_units_in_house,
        "rowNeed": weekly.row_need,
        "unitsInHouseMinusRowNeed": weekly.units_in_house_minus_row_need,
        "nextWeekForecast": weekly.next_week_forecast,
        "unitsInHouseMinRowNeedMinForecast": weekly.units_in_house_minus_row_need_minus_forecast,
        "unitsToProduceByAutobagger": weekly.units_to_produce_by_autobagger,
        "startOfTheDayInventory": weekly.start_of_day_inventory,
        "startOfTheDayInventoryMinusUnitsInHouseHj": weekly.start_of_day_inv_minus_hj_units_in_house,
        "hjSnapshot": weekly.hj_snapshot,
        "inventory": weekly.inventory,
        "discards": weekly.discards,
        "donations": weekly.donations,
        "pulls": weekly.pulls,
        "wipConsumptionCarryover": weekly.wip_consumption_carryover if weekly.wip_consumption_carryover else None,
        "wipConsumptionInitialPull": (
            weekly.wip_consumption_initial_pull if weekly.wip_consumption_initial_pull else None
        ),
        "wipConsumptionPutawayToProduction": (
            weekly.wip_consumption_putaway_to_production if weekly.wip_consumption_putaway_to_production else None
        ),
        "totalOnHand": weekly.total_on_hand,
        "onHandMinProductionNeeds": weekly.total_on_hand_minus_prod_needs,
        "inProgressHj": weekly.in_progress_hj,
        "awaitingDelivery": weekly.awaiting_delivery,
        "notDelivered": weekly.not_delivered,
        "bufferQuantity": weekly.buffer_quantity,
        "bufferPercent": weekly.buffer_percent,
        "previousWeekRowNeed": weekly.prev_week_row_need,
        "unitsOnProductionFloor": weekly.units_on_prod_floor,
    }
