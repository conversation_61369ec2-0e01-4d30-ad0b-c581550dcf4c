from procurement.api.base_api import BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.managers.imt import production_need
from procurement.managers.imt.production_need import DailyValue, ExtendedProductionNeedResult


class ProductionNeedIngredients(BaseApiWithSites):
    permission = Permissions.IMT_PROD_NEED_ING_V1

    def get(self):
        return {
            site: self._build_response(prod_needs)
            for site, prod_needs in production_need.get_extended_production_need(
                self.sites, self.weeks[0], self.brand
            ).items()
        }

    def _build_response(self, prod_needs: list[ExtendedProductionNeedResult]):
        items = [
            {
                "sku": prod_need_item.sku_code,
                "daily": {day.isoformat(): self._build_daily(value) for day, value in prod_need_item.daily.items()},
                "total": prod_need_item.total,
                "rowNeed": prod_need_item.row_need,
                "skuName": prod_need_item.sku_name,
                "liveConsumption": prod_need_item.live_consumption,
                "liveRowNeed": prod_need_item.live_row_need,
                "delta": prod_need_item.delta,
                "buyers": prod_need_item.buyers,
            }
            for prod_need_item in prod_needs
        ]
        items.sort(key=lambda i: i["skuName"] or "")
        return items

    @staticmethod
    def _build_daily(value: DailyValue):
        return {
            "value": value.value,
            "valueDay": value.value_day,
            "valueNight": value.value_night,
            "valueThird": value.value_third,
        }


class ProductionNeedIngredientsHeader(BaseApiWithSites):
    permission = Permissions.IMT_PROD_NEED_ING_V1

    def get(self):
        if not self.sites:
            return {}
        site_day_headers = production_need.get_production_need_headers(self.sites[0], self.weeks[0], self.brand)
        return {
            day.isoformat(): {"planType": header.plan_type, "weekday": header.weekday_title}
            for day, header in site_day_headers.items()
        }
