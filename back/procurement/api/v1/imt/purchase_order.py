from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime

from flask import request
from jsonschema import validate

from procurement.api import base_api, formatting
from procurement.api.base_api import BaseApi, BaseApiWithSites, FileExportBaseApi
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import FRONT_FORMAT, MONTH_FORMAT, MONTH_FORMAT_2
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.managers import po_status_common
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.datasync import po_export_datasync
from procurement.managers.imt.export import po_status_finance
from procurement.managers.imt.purchase_order import po_status as imt_po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusIncludeFlags, PoStatusResult
from procurement.managers.imt.v2.depletion import packaging
from procurement.managers.pimt import partners as partner_service
from procurement.managers.pimt import purchase_order as pimt_po_status
from procurement.repository.ordering.purchase_order import utils as po_utils

from .schema import po_status_schema as schemas

_WAREHOUSES_BRAND = "WAREHOUSES"


def build_base_response(po_res: PoStatusResult) -> dict:
    res = {
        "poNumber": po_res.po_number,
        "poStatus": po_res.po_status,
        "orderNumber": utils.get_order_number_from_po_number(po_res.po_number),
        "receiveVariance": po_res.receive_variance,
        "supplier": po_res.supplier,
        "skuName": po_res.sku_name,
        "sku": po_res.sku_code,
        "scheduledDeliveryDate": (
            po_res.scheduled_delivery_date.strftime(FRONT_FORMAT) if po_res.scheduled_delivery_date else None
        ),
        "orderSize": po_res.order_size,
        "caseSize": po_res.case_size,
        "quantityOrdered": po_res.quantity_ordered,
        "quantityReceived": po_res.quantity_received,
        "casesReceived": po_res.cases_received,
        "caseSizeReceived": po_res.case_size_received,
        "emergencyReason": po_res.emergency_reason,
        "casePrice": po_res.case_price,
        "totalPrice": po_res.total_price,
        "shipMethod": po_res.ship_method,
        "poBuyer": po_res.po_buyer,
        "proposedQuantityCases": po_res.proposed_quantity_cases,
        "proposedUnitsPerCase": po_res.proposed_units_per_case,
        "proposedQuantityUnits": po_res.proposed_quantity_units,
        "proposedDeliveryDate": po_res.proposed_delivery_date,
        "totalPriceReceived": po_res.total_price_received,
        "asnShipmentDate": po_res.asn_shipment_date.isoformat() if po_res.asn_shipment_date else None,
        "asnPlannedDeliveryTime": (
            po_res.asn_planned_delivery_time.isoformat() if po_res.asn_planned_delivery_time else None
        ),
        "asnShippedQuantityCases": po_res.asn_shipped_quantity_cases,
        "asnUnitsOfMeasure": formatting.to_string(po_res.asn_units_of_measure),
        "asnCaseCount": po_res.asn_case_count,
        "asnShippedQuantityUnits": po_res.asn_shipped_quantity_units,
        "asnRequiresHighAttention": po_res.asn_requires_high_attention,
        "asnRequiresAttention": po_res.asn_requires_attention,
        "site": po_res.site,
        "dateReceived": po_res.datetime_received.replace(tzinfo=None).isoformat() if po_res.datetime_received else None,
        "category": po_res.category,
        "buyers": po_res.buyers,
        "purchasingUnit": formatting.to_string(po_res.purchasing_unit),
        "orderUnit": formatting.to_string(po_res.order_unit),
        "week": formatting.to_string(po_res.week),
        "forecastDeliveryPercent": (
            round(po_res.percentage_of_the_forecasted, 4) if po_res.percentage_of_the_forecasted else None
        ),
        "transferSourceBobCode": po_res.transfer_source_bob_code,
        "hasTransferDropdown": po_res.has_multiple_transfer_items,
    }
    if po_res.shipment_data:
        res.update(
            {
                "loadNumber": po_res.shipment_data.load_number,
                "palletCount": po_res.shipment_data.pallet_count,
                "carrierName": po_res.shipment_data.carrier_name,
                "originLocation": po_res.shipment_data.origin_location,
                "appointmentTime": (
                    po_res.shipment_data.appointment_time.isoformat() if po_res.shipment_data.appointment_time else None
                ),
            }
        )
    return res


def _build_response(data: dict[str, list[PoStatusResult]]) -> dict[str, list[dict]]:
    return {site: list(map(_build_po_response_item, po_res_list)) for site, po_res_list in data.items()}


def _build_po_response_item(po_res: PoStatusResult) -> dict:
    res = build_base_response(po_res)
    res.update({"casePriceFlag": po_res.case_price_flag, "allocationPrice": po_res.allocation_price})
    return res


class PoStatus(BaseApiWithSites):
    permission = Permissions.IMT_PO_STATUS_V1

    def get(self):
        validate(request.args, schemas.po_status_get)

        if self.is_wh_request():
            result = defaultdict(list)
            request_wh = request.args.get("dc")
            whs = [
                p for p in partner_service.get_all_partners(e2open_grn=True) if not request_wh or p.code == request_wh
            ]
            for wh in whs:
                result[wh.code].extend(
                    po_status_common.convert_po_status_data_to_result(
                        pimt_po_status.get_po_data_by_week(weeks=list(self.weeks), wh=wh)
                    )
                )
        else:
            if not brand_admin.get_brands(week=max(self.weeks)).get(self.brand):
                raise InvalidRequestException(f"Selected brand is invalid: {self.brand}")
            sku_code = request.args.get("skuCode")
            if sku_code:
                include_bulk_skus = request.args.get("includeBulkSkus", "false").lower() == "true"
                include_org_cv_skus = request.args.get("includeOrgCvSkus", "false").lower() == "true"
                include_packaged_skus = request.args.get("includePackagedSkus", "false").lower() == "true"
                result = {
                    self.sites[0]: imt_po_status.get_po_status_by_sku_code(
                        sku_code=sku_code,
                        weeks=self.weeks,
                        site=self.sites[0],
                        brand=self.brand,
                        include_flags=PoStatusIncludeFlags(
                            include_bulk_skus, include_org_cv_skus, include_packaged_skus, include_consolidated_dcs=True
                        ),
                    )
                }
            else:
                result = imt_po_status.get_po_status(weeks=self.weeks, sites=self.sites, brand=self.brand)
        return _build_response(result)

    def _get_sites(self) -> Iterable[str]:
        if self.is_wh_request():
            return set()
        return super()._get_sites()

    def is_wh_request(self) -> bool:
        return self.brand.upper() == _WAREHOUSES_BRAND


class MultiBrandPoStatus(BaseApi):
    permission = Permissions.IMT_PO_STATUS_V1

    def get(self):
        validate(request.args, schemas.po_status_get_for_multi_brand_dc)
        sku_code = request.args.get("skuCode")
        week = ScmWeek.from_str(request.args.get("week"))
        multi_brand_dc = request.args.get("dc")
        result = {multi_brand_dc: imt_po_status.get_po_status_for_multi_brand_dc(multi_brand_dc, sku_code, week)}
        return _build_response(result)


class TransferOrderStatus(BaseApi):
    permission = Permissions.IMT_PO_STATUS_V1

    def get(self):
        validate(request.args, schemas.to_status_get)
        po_number = request.args["poNumber"]
        sku_code = request.args["skuCode"]
        sites = {s.bob_code for s in dc_admin.get_all_market_dcs()}
        whs = {w.bob_code for w in partner_service.get_all_partners()}
        bob_code = po_utils.get_po_bob_code(po_number)
        if not bob_code:
            raise InvalidRequestException(f"Invalid PO number {po_number}. Failed to get DC code.")
        res = None
        if bob_code in sites:
            res = imt_po_status.get_to_status(po_number=po_number, sku_code=sku_code)
        if bob_code in whs:
            res = po_status_common.convert_po_status_data_to_result(
                pimt_po_status.get_to_data(bob_code=bob_code, po_number=po_number, sku_code=sku_code)
            )
        if res:
            return list(map(_build_po_response_item, res))
        raise InvalidRequestException(f"Couldn't find a DC with bob code {bob_code}")


class PackagingDepletionPoStatus(BaseApiWithSites):
    permission = Permissions.IMT_PO_STATUS_V1

    def get(self):
        validate(request.args, schemas.pck_depl_po_status_get)
        sku_code = request.args["skuCode"]
        res = imt_po_status.to_extended_po_status(
            brand=self.brand,
            site=self.sites[0],
            sku_po_master_view_list=packaging.get_packaging_po_status(
                week=self.weeks[0], site=self.sites[0], brand=self.brand, sku_code=sku_code
            ),
        )
        return {self.sites[0]: [build_base_response(po_res) for po_res in res]}


class PoExport(base_api.BaseApi):
    permission = Permissions.IMT_PO_SYNC_V1

    def post(self):
        po_export_datasync.launch_po_export()


class PoFinancialExport(FileExportBaseApi):
    permission = Permissions.IMT_PO_STATUS_V1

    _file_suffix = "financial_export.xlsx"

    def get(self):
        args = request.args
        validate(args, schemas.export_args_schema)
        brands = (
            set(map(str.upper, utils.split_comma_string(args["brands"])))
            if "brands" in args
            else set(brand_admin.get_brand_ids()).union([_WAREHOUSES_BRAND])
        )
        return self._generate_period_export(brands, args)

    @staticmethod
    def _generate_period_export(brands: set[str], args: dict) -> tuple[bytes, str]:
        include_warehouses = _WAREHOUSES_BRAND in brands
        brands_string = "_".join(b.lower() for b in brands)
        brands.discard(_WAREHOUSES_BRAND)
        common_args = {
            "brands": brands,
            "po_filter": args["poFilter"],
            "include_warehouses": include_warehouses,
        }
        if "date" in args:
            day = date.fromisoformat(args["date"])
            return (
                po_status_finance.generate_daily_financial_excel(day=day, **common_args),
                f"{args['date']}_{brands_string}_{PoFinancialExport._file_suffix}",
            )
        if "week" in args:
            week = ScmWeek.from_str(args["week"])
            return (
                po_status_finance.generate_weekly_financial_excel(week=week, **common_args),
                f"{args['week']}_{brands_string}_{PoFinancialExport._file_suffix}",
            )
        if "weekFrom" in args and "weekTo" in args:
            week_from = ScmWeek.from_str(args["weekFrom"])
            week_to = ScmWeek.from_str(args["weekTo"])
            return (
                po_status_finance.generate_weekly_range_financial_excel(
                    week_from=week_from, week_to=week_to, **common_args
                ),
                f"{args['weekFrom']}_{args['weekTo']}_{brands_string}_{PoFinancialExport._file_suffix}",
            )
        if "month" in args:
            month_day = datetime.strptime(args["month"], MONTH_FORMAT).date()
            return (
                po_status_finance.generate_monthly_financial_excel(day=month_day, **common_args),
                f"{month_day.strftime(MONTH_FORMAT_2)}_{brands_string}_{PoFinancialExport._file_suffix}",
            )
        if "dateFrom" in args and "dateTo" in args:
            date_from = date.fromisoformat(args["dateFrom"])
            date_to = date.fromisoformat(args["dateTo"])
            return (
                po_status_finance.generate_custom_financial_excel(date_from=date_from, date_to=date_to, **common_args),
                f"{args['dateFrom']}_{args['dateTo']}_{brands_string}_{PoFinancialExport._file_suffix}",
            )
        raise InvalidRequestException(
            "Either one of 'date', 'week', 'month', 'dateFrom' and 'dateTo', 'weekFrom' and 'weekTo' "
            "or 'poFilter' params should be specified"
        )
