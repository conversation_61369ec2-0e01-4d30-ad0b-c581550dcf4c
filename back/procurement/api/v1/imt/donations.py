from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.managers.imt import donations
from procurement.managers.imt.discards import DiscardItem

from .schema import discards as schema


class Donation(BaseApi):
    permission = Permissions.IMT_ING_DEPL_V1

    def get(self):
        validate(request.args, schema.discards_schema_get)
        sku_code = request.args["skuCode"]
        brands = request.args["brand"].split(", ")
        sites = request.args["site"].split(", ")
        week = ScmWeek.from_str(request.args["week"])
        return [self._build_response(item) for item in donations.get_donations(sku_code, brands, sites, week)]

    @staticmethod
    def _build_response(item: DiscardItem) -> dict:
        return {
            "site": item.site,
            "skuCode": item.sku_code,
            "discardDate": item.discard_date.isoformat(),
            "quantity": item.quantity,
            "tranType": item.tran_type,
        }
