from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi, BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.typing import ORDER_NUMBER, PO_NUMBER, SITE, SKU_CODE
from procurement.data.dto.inventory.ics import IcsTicketWithSiteBrand as IcsTicketDto
from procurement.managers import ics as ics_manager

from .schema import ics_schema


class IcsTicketBase(BaseApiWithSites):
    permission = Permissions.ICS_TICKETS_V1

    def _get_sites(self) -> list[str]:
        return utils.split_comma_string(request.args["sites"])


class IcsTicketPreview(IcsTicketBase):
    def get(self):
        validate(request.args, ics_schema.ics_ticket_preview_get)
        return self._build_response(ics_manager.get_ics_tickets_preview(self.brand, self.sites, self.weeks))

    @staticmethod
    def _build_response(
        ics_preview_data: dict[
            ScmWeek, dict[SITE, list[tuple[SKU_CODE | None, PO_NUMBER | None, ORDER_NUMBER | None]]]
        ],
    ):
        res = {}
        for week, sku_codes_po_numbers_by_site in ics_preview_data.items():
            res[str(week)] = {
                site: [
                    {"skuCode": sku_code, "poNumber": po_number, "orderNumber": order_number}
                    for sku_code, po_number, order_number in sku_codes_po_numbers
                ]
                for site, sku_codes_po_numbers in sku_codes_po_numbers_by_site.items()
            }
        return res


class IcsTicket(BaseApi):
    permission = Permissions.ICS_TICKETS_V1

    def get(self):
        validate(request.args, ics_schema.ics_ticket_get)
        sku_code = request.args.get("skuCode")
        po_number = request.args.get("poNumber")
        week = ScmWeek.from_str(request.args.get("week"))
        brands = utils.split_comma_string(request.args["brand"])
        sites = utils.split_comma_string(request.args["sites"])
        if sku_code or po_number:
            return self._build_response(ics_manager.get_ics_tickets(brands, sites, sku_code, po_number, week))
        raise InvalidRequestException("Either one of 'skuCode' or 'poNumber' params must be specified")

    @staticmethod
    def _build_response(items: list[IcsTicketDto]):
        return [
            {
                "ticketId": item.ticket_id,
                "ticketLink": item.ticket_link,
                "poNumber": item.po_number,
                "skuCode": item.sku_code,
                "bobCode": item.bob_code,
                "subject": item.subject,
                "week": str(item.week),
                "productionImpact": item.production_impact,
                "status": item.status,
                "requestType": item.request_type,
                "updatedAt": item.updated_at,
                "brand": item.brand,
                "site": item.site,
            }
            for item in items
        ]
