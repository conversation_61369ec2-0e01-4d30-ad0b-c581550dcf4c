from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.managers.datasync import imt_datasync

from .schema import ingredient_depletion as schemas


class IMTSync(base_api.BaseApi):
    permission = Permissions.IMT_SYNC_V1

    def post(self):
        data = request.json
        validate(data, schemas.ingredient_depletion_sync_post)
        imt_datasync.sync_imt_pipeline(
            week=ScmWeek.from_str(data["week"]), upload_forecast=False
        ).submit_if_not_in_progress()
