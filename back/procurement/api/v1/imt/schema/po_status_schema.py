from procurement.api.schema.types import WEEK
from procurement.managers.exports.po_filter import PoFilter

po_status_get = {
    "type": "object",
    "properties": {
        "skuCode": {"type": "string"},
    },
}

to_status_get = {
    "type": "object",
    "properties": {
        "poNumber": {"type": "string"},
        "skuCode": {"type": "string"},
    },
    "required": ["poNumber", "skuCode"],
}

po_status_get_for_multi_brand_dc = {
    "type": "object",
    "properties": {
        "skuCode": {"type": "string"},
        "dc": {"type": "string"},
        "week": WEEK,
    },
    "required": ["skuCode", "week", "dc"],
}

pck_depl_po_status_get = {
    "type": "object",
    "properties": {
        "skuCode": {"type": "string"},
    },
    "required": ["skuCode"],
}

export_args_schema = {
    "type": "object",
    "properties": {
        "brands": {"type": "string"},
        "date": {"type": "string", "format": "date"},
        "month": {"type": "string", "format": r"^\d{4}-\d{2}$"},
        "weekFrom": WEEK,
        "weekTo": WEEK,
        "dateFrom": {"type": "string", "format": "date"},
        "dateTo": {"type": "string", "format": "date"},
        "poFilter": {"type": "string", "enum": [e.value for e in PoFilter]},
    },
}
