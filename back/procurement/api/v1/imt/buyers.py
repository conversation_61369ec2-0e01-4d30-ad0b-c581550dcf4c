from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi
from procurement.api.v1 import depletion_common
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import FRONT_FORMAT
from procurement.core.dates import ScmWeek
from procurement.managers.imt import buyer_dashboard
from procurement.managers.imt import buyers as buyers_service
from procurement.managers.imt.ingredients_depletion.result import SiteIngredientDepletionResult

from .schema import buyers_schema


class Buyers(BaseApi):
    permission = Permissions.IMT_BUYER_V1

    def get(self):
        buyers = [{"id": _id, "name": name} for _id, name in buyers_service.get_buyers().items()]
        return buyers


class BuyersDashboard(BaseApi):
    permission = Permissions.IMT_BUYER_V1

    def get(self, buyer_id: int):
        validate(request.args, buyers_schema.buyers_dashboard_schema_get)
        week = ScmWeek.from_str(request.args["week"])
        [current_week_data, next_week_data] = buyer_dashboard.get_buyer_dashboard(buyer_id, week)
        today_po_status = buyer_dashboard.get_today_po_status_dashboard(buyer_id)
        dashboards = {
            str(current_week_data.week): {
                "depletion": _build_depletion_dashboard(current_week_data.depletion),
                "poStatus": _build_po_status_dashboard(current_week_data.po_status),
                "poStatusToday": _build_po_status_dashboard(today_po_status),
            },
            str(next_week_data.week): {
                "depletion": _build_depletion_dashboard(next_week_data.depletion),
            },
        }
        return dashboards


class BuyersCriticalItemsDashboard(BaseApi):
    permission = Permissions.IMT_BUYER_V1

    def get(self):
        validate(request.args, buyers_schema.buyers_dashboard_schema_get)
        week = ScmWeek.from_str(request.args["week"])
        dashboard_data = buyer_dashboard.get_buyer_critical_dashboard(week=week)
        return {str(week): _build_depletion_dashboard(dashboard_data.depletion)}


def _build_depletion_dashboard(depletion_data: dict[str, dict[str, list[SiteIngredientDepletionResult]]]) -> list[dict]:
    return [
        {
            "brand": brand,
            "site": site,
            "sku": depletion.sku_code,
            "skuName": depletion.sku_name,
            "category": depletion.category,
            "commodityGroup": depletion.commodity_group,
            "impactedRecipes": ", ".join(sorted(depletion.impacted_recipes)),
            "plan": depletion.plan,
            "forecastOscar": depletion.forecast,
            "delta": depletion.delta,
            "sn": depletion.supplement_need,
            "weekly": depletion_common.build_weekly_overview(depletion.weekly_overview),
        }
        for brand, brand_data in depletion_data.items()
        for site, site_data in brand_data.items()
        for depletion in site_data
    ]


def _build_po_status_dashboard(po_status_data: dict[str, dict[str, list]]) -> list[dict]:
    return [
        {
            "brand": brand,
            "site": site,
            "week": str(po.week),
            "sku": po.sku_code,
            "category": po.category,
            "skuName": po.sku_name,
            "poNumber": po.po_number,
            "poStatus": po.po_status,
            "receiveVariance": po.receive_variance,
            "supplier": po.supplier,
            "scheduledDeliveryDate": po.scheduled_delivery_date.strftime(FRONT_FORMAT),
            "orderSize": po.order_size,
            "caseSize": po.case_size,
            "caseSizeReceived": po.case_size_received,
            "quantityOrdered": po.quantity_ordered,
            "quantityReceived": po.quantity_received,
            "casesReceived": po.cases_received,
            "emergencyReason": po.emergency_reason,
            "casePrice": po.case_price,
            "totalPrice": po.total_price,
            "totalPriceReceived": po.total_price_received,
            "forecastDeliveryPercent": (
                round(po.percentage_of_the_forecasted, 4) if po.percentage_of_the_forecasted else None
            ),
        }
        for brand, brand_data in po_status_data.items()
        for site, site_data in brand_data.items()
        for po in site_data
    ]
