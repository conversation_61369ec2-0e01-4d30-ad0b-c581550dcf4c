from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.imt.ingredients_depletion.remake_tool import remake_tool
from procurement.managers.imt.ingredients_depletion.remake_tool.remake_ingredient_depletion import (
    RemakeToolIngredientDepletionResult,
    RemakeToolWeeklyDepletionItem,
)
from procurement.managers.imt.ingredients_depletion.remake_tool.remake_tool import RemakeToolSlot

from .schema.remake_schema import remake_schema, remake_schema_post_json


class RemakeTool(base_api.BaseApiWithSites):
    permission = Permissions.IMT_REMAKE_TOOL_V1

    def get(self):
        validate(request.args, remake_schema)
        day = request.args["day"]
        site = self.sites[0]
        week = self.weeks[0]
        remake_tool_data = remake_tool.get_remake_data(week, site, self.brand)
        ingredients_depletion = remake_tool.get_remake_depletion(week, site, self.brand, remake_tool_data, day)
        return self._prepare_response(site, remake_tool_data, ingredients_depletion)

    def post(self):
        validate(request.args, remake_schema)
        validate(list(request.get_json().values()), remake_schema_post_json)
        day = request.args["day"]
        site = self.sites[0]
        week = self.weeks[0]
        remake_tool_data = remake_tool.insert_data(request.get_json(), week, site)
        ingredients_depletion = remake_tool.get_remake_depletion(week, site, self.brand, remake_tool_data, day)
        return self._prepare_response(site, remake_tool_data, ingredients_depletion)

    def _prepare_response(
        self,
        site: str,
        remake_tool_data: dict[str, RemakeToolSlot],
        ingredient_depletion: list[RemakeToolIngredientDepletionResult],
    ) -> dict:
        return {
            site: {
                "remake": {sku_code: self._build_remake_response(item) for sku_code, item in remake_tool_data.items()},
                "ingredientDepletion": [self._build_depletion_response(item) for item in ingredient_depletion],
            }
        }

    @staticmethod
    def _build_remake_response(item: RemakeToolSlot) -> dict:
        return {"picks_2p": item.picks_2p, "picks_4p": item.picks_4p, "picks_6p": item.picks_6p}

    def _build_depletion_response(self, ingredient: RemakeToolIngredientDepletionResult) -> dict:
        return {
            "sku": ingredient.sku_code,
            "skuName": ingredient.sku_name,
            "category": ingredient.category,
            "commodityGroup": ingredient.commodity_group,
            "impactedRecipes": ", ".join(sorted(ingredient.impacted_recipes)),
            "plan": ingredient.plan,
            "forecastOscar": ingredient.forecast,
            "delta": ingredient.delta,
            "supplementNeed": ingredient.supplement_need,
            "weekly": self._build_weekly(ingredient.weekly_overview),
            "finalEndOfWeekBuffer": ingredient.daily.end_of_week_inventory,
            "buyers": ingredient.buyer,
        }

    @staticmethod
    def _build_weekly(weekly: RemakeToolWeeklyDepletionItem) -> dict:
        return {
            "remakeUnits": weekly.remake_units,
            "unitsNeeded": weekly.units_needed,
            "unitsNeededPlusRemake": weekly.units_needed_plus_remake,
            "unitsOrdered": weekly.units_ordered,
            "unitsReceived": weekly.units_received,
            "unitsInHouseHJ": weekly.hj_units_in_house,
            "rowNeed": weekly.row_need,
            "unitsInHouseMinusRowNeed": weekly.units_in_house_minus_row_need,
            "nextWeekForecast": weekly.next_week_forecast,
            "hjMinusRowNeedMinusForecast": weekly.units_in_house_minus_row_need_minus_forecast,
            "bufferQuantity": weekly.buffer_quantity,
            "bufferPercent": weekly.buffer_percent,
            "unitsToProduceByAutobagger": weekly.units_to_produce_by_autobagger,
            "inventory": weekly.inventory,
            "discards": weekly.discards,
            "pulls": weekly.pulls,
            "totalOnHand": weekly.total_on_hand,
            "onHandMinProductionNeeds": weekly.total_on_hand_minus_prod_needs,
            "inProgressHj": weekly.in_progress_hj,
            "awaitingDelivery": weekly.awaiting_delivery,
            "notDelivered": weekly.not_delivered,
        }
