from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApi, BaseApiWithSites
from procurement.api.v1 import depletion_common
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.metrics import ApplicationMetrics
from procurement.managers.admin import brand_admin
from procurement.managers.depletion import core as depl_core
from procurement.managers.depletion.result import AggregatedIngredientDepletionResult, IngredientDepletionResult
from procurement.managers.imt.ingredients_depletion import aggregated, dashboard

from .schema import ingredient_depletion as schema


def _validate_config(resource: BaseApiWithSites):
    if resource.brand not in brand_admin.get_brands(resource.weeks[0]):
        raise InvalidRequestException(f"Unknown Brand '{resource.brand}'")


class IngredientDepletion(BaseApiWithSites):
    permission = Permissions.IMT_ING_DEPL_V1

    @ApplicationMetrics.functional_methods(request_type="api")
    def get(self):
        _validate_config(self)
        result = dashboard.get_dashboard_depletion(self.weeks[0], self.sites, self.brand)
        return {site: [self._build_response(depl_item) for depl_item in data] for site, data in result.items()}

    @staticmethod
    def _build_response(depletion_item: IngredientDepletionResult):
        res = {
            **depletion_common.build_response(depletion_item),
            "packagedItems": ([depletion_common.build_response(item) for item in depletion_item.packaged_depl_items]),
            "childItems": ([depletion_common.build_response(item) for item in depletion_item.child_depletion]),
        }
        return res


class AggregatedIngredientDepletion(BaseApi):
    permission = Permissions.IMT_ING_DEPL_V1

    @ApplicationMetrics.functional_methods(request_type="api")
    def get(self):
        validate(request.args, schema.ingredient_depletion_aggregated_get)
        week = ScmWeek.from_str(request.args["week"])
        multi_brand_dc = request.args["multiBrandDc"]
        result = aggregated.get_aggregated_ingredient_depletion(week=week, aggregated_site=multi_brand_dc)
        return [
            depletion_common.build_aggregated_response(depletion_item)
            for depletion_item in sorted(result, key=self._sort_key)
        ]

    @staticmethod
    def _sort_key(depletion_item: AggregatedIngredientDepletionResult) -> tuple[str, int]:
        sku_name = depletion_item.sku_name
        if sku_name.startswith("EP,"):
            return sku_name[3:].strip(), 1
        if sku_name.startswith("GC,"):
            return sku_name[3:].strip(), 2
        if sku_name.startswith("Factor,"):
            return sku_name[7:].strip(), 3
        return sku_name, 0


class IngredientDepletionWeekdays(BaseApiWithSites):
    permission = Permissions.IMT_ING_DEPL_V1

    def get(self):
        _validate_config(self)
        brand = brand_admin.get_brands(self.weeks[0])[self.brand]
        days = depl_core.get_weekdays(week=self.weeks[0], week_config=brand.scm_week_config)
        return {day.day.isoformat(): day.weekday_title for day in days}

    def _get_sites(self) -> list[str]:
        return []
