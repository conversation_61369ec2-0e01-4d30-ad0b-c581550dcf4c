from flask import Blueprint

from .analytics import TopVariances
from .buyers import Buyers, BuyersCriticalItemsDashboard, BuyersDashboard
from .discards import Discards
from .donations import Donation
from .ics import IcsTicket, IcsTicketPreview
from .ingredient_depletion import AggregatedIngredientDepletion, IngredientDepletion, IngredientDepletionWeekdays
from .job_sync import IMTSync
from .production_kit_guide import PKG
from .production_need import ProductionNeedIngredients, ProductionNeedIngredientsHeader
from .purchase_order import (
    MultiBrandPoStatus,
    PackagingDepletionPoStatus,
    PoExport,
    PoFinancialExport,
    PoStatus,
    TransferOrderStatus,
)
from .remake_tool import RemakeTool


def register_api() -> Blueprint:
    api_bp = Blueprint("imt", __name__, url_prefix="imt/")
    for path, resource in {
        "ingredient-depletion/": IngredientDepletion.as_view("ing-depl"),
        "ingredient-depletion/aggregated/": AggregatedIngredientDepletion.as_view("agg-ing-depl"),
        "ingredient-depletion-weekdays/": IngredientDepletionWeekdays.as_view("ingredient-depletion-weeks"),
        "packaging-depletion-po-status/": PackagingDepletionPoStatus.as_view("ing-depl-po-status"),
        "sync/": IMTSync.as_view("imt-sync"),
        "remake-tool/": RemakeTool.as_view("remake-tool"),
        "po-status/": PoStatus.as_view("po-status"),
        "to-status/": TransferOrderStatus.as_view("to-status"),
        "po-export/": PoExport.as_view("po-export"),
        "po-financial-export/": PoFinancialExport.as_view("po-financial-export"),
        "production-kit-guide/": PKG.as_view("pkg"),
        "production-need/": ProductionNeedIngredients.as_view("prod-need"),
        "production-need/daily-header/": ProductionNeedIngredientsHeader.as_view("prod-needs-header"),
        "buyers/": Buyers.as_view("buyers"),
        "buyers/<int:buyer_id>/dashboard/": BuyersDashboard.as_view("buyer-dashboard"),
        "buyers/critical-items/": BuyersCriticalItemsDashboard.as_view("buyer-critical-items"),
        "aggregated-po-status/": MultiBrandPoStatus.as_view("aggregated-po-status"),
        "analytics/top-variances/": TopVariances.as_view("analytics-top-variances"),
        "discards/": Discards.as_view("discards"),
        "donations/": Donation.as_view("donations"),
        "ics-ticket/": IcsTicket.as_view("ics-ticket"),
        "ics-ticket/preview/": IcsTicketPreview.as_view("ics-ticket-preview"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
