from flask import request
from jsonschema import validate

from procurement.api.base_api import BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.managers.imt import discards
from procurement.managers.imt.discards import DiscardItem

from .schema import discards as schema


class Discards(BaseApiWithSites):
    permission = Permissions.IMT_ING_DEPL_V1

    def get(self):
        validate(request.args, schema.discards_schema_get)
        sku_code = request.args["skuCode"]
        return [
            self._build_response(item)
            for item in discards.get_discards(self.sites[0], self.brand, self.weeks[0], sku_code)
        ]

    @staticmethod
    def _build_response(item: DiscardItem) -> dict:
        return {
            "site": item.site,
            "skuCode": item.sku_code,
            "discardDate": item.discard_date.isoformat(),
            "quantity": item.quantity,
            "tranType": item.tran_type,
        }
