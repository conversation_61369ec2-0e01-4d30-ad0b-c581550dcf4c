import jsonschema
from flask import request

from procurement.api.base_api import BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.core import utils
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.data.dto.inventory.top_variance_comments import TopVarianceCommentInput
from procurement.managers.admin import dc_admin
from procurement.managers.imt.analytics import top_variances
from procurement.managers.imt.analytics.top_variances import AbstractTopVarianceItem, SkuTopVarianceItem

from .schema import analytics as schema


class TopVariances(BaseApiWithSites):
    permission = Permissions.IMT_ANALYTICS_TOP_VARIANCES_V1

    def get(self):
        return [
            self._build_aggregated_item(item)
            for item in sorted(
                top_variances.get_top_variances(self.brand, self.sites, self.weeks[0]),
                key=lambda item: (item.price_variance, item.brand, item.site),
                reverse=True,
            )
        ]

    def _build_aggregated_item(self, item: SkuTopVarianceItem) -> dict:
        return {
            "pos": [self._build_base_item(po) for po in item.po_sku_items.raw],
            "poFlag": item.is_forecast_exists_but_no_po,
            "budgetedCostFlag": item.pos_for_not_running_sku,
            "priceVariance": item.price_variance,
            "varianceStatus": item.variance_status,
            "quantityVariance": item.quantity_variance,
            **self._build_base_item(item),
        }

    @staticmethod
    def _build_base_item(item: AbstractTopVarianceItem) -> dict:
        return {
            "poNumber": item.po_number,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "category": item.category,
            "buyer": item.buyer,
            "allocationPrice": item.allocation_price or None,
            "poUnitPrice": round(item.unit_price, 2) if item.unit_price is not None else None,
            "poUnitPriceFlag": item.unit_price_flag,
            "budgetedCost": item.budgeted_cost,
            "receivedValue": item.received_price,
            "incomingValue": item.incoming_value,
            "comment": item.comment.comment if item.comment else None,
            "lastEditedBy": item.comment.last_edited_by if item.comment else None,
            "site": item.site,
            "brand": item.brand,
            "supplier": item.supplier,
            "emergencyReason": item.emergency_reason,
        }

    def post(self):
        if len(self.weeks) > 1:
            raise InvalidRequestException("You can only post for one week and site at a time")
        week = self.weeks[0]
        args = request.args
        brands = utils.split_comma_string(args["brand"])
        sites = utils.split_comma_string(args["dc"])
        dcs = dc_admin.get_all_brand_site_combinations(brands, sites, week).values()
        main_dc = dc_admin.get_main_consolidated_site(dcs)

        body = request.json
        jsonschema.validate(body, schema.top_variance_post_schema)
        last_edited_by = self.request_context.user_info.email
        for dc in dcs:
            top_variances.upsert_top_variance_comment(
                TopVarianceCommentInput(
                    week=week,
                    brand=dc.brand,
                    site=dc.sheet_name,
                    # delete comments for other non-main DCs to avoid comment text duplication during aggregation
                    comment=body["comment"] if dc is main_dc else "",
                    po_number=body["poNumber"],
                    sku_code=body["skuCode"],
                    last_edited_by=last_edited_by,
                )
            )
        return {"comment": body["comment"], "lastEditedBy": last_edited_by}, 201
