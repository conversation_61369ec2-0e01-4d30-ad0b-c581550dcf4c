from procurement.api import formatting
from procurement.api.base_api import BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.data.dto.inventory.ingredient import PkgData
from procurement.managers.pkg import production_kit_guide


class PKG(BaseApiWithSites):
    permission = Permissions.IMT_PCK_V1

    def get(self):
        return {
            site: self._build_response(production_kit_guide.get_pkg_data(self.weeks[0], site, self.brand))
            for site in self.sites
        }

    @staticmethod
    def _build_response(items: list[PkgData]) -> list[dict]:
        return [
            {
                "code": item.code,
                "mealNumber": item.slot,
                "mealName": item.meal_name,
                "skuId": item.sku_id,
                "picks2p": item.picks_2p,
                "picks3p": item.picks_3p,
                "picks4p": item.picks_4p,
                "picks6p": item.picks_6p,
                "sku": item.sku_code,
                "skuName": item.sku_name,
                "purchasingCategory": item.purcasing_category,
                "fullSkuPlusSkuName": item.sku_code + "-" + item.sku_name,
                "weightAmount": formatting.round_if_available(item.weight_amount, 5),
                "weightUnit": item.weight_unit,
                "storageLocation": item.storage_location,
                "allergens": item.allergens,
                "subRecipe": item.sub_recipe,
            }
            for item in items
        ]
