from decimal import Decimal
from typing import Any, TypeVar

from procurement.constants.hellofresh_constant import BRAND_EP, BRAND_FJ, BRAND_GC, BRAND_HF
from procurement.managers.imt.packaging_inventory import LinerGroupEnum

_Number = TypeVar("_Number", int, float, Decimal)


_PACKAGING_CATEGORY_ORDER = [
    "BOX",
    "KIT BAG",
    "SEPARATOR",
    "SLEEVE",
    "TRAY",
    "PRIMARY PACKAGING",
    "ICE",
    "MEDIA",
]

_PACKAGING_SIZE_ORDER = [
    " XSMALL ",
    " XS ",
    " SMALL ",
    " MEDIUM ",
    " LARGE ",
    " LARGE,",
    " XLARGE ",
    " XL ",
]

_BRAND_ORDER = [
    BRAND_HF,
    BRAND_EP,
    BRAND_GC,
    BRAND_FJ,
    "FACTOR",
]

_LONGEST_BRAND_PART = max(map(len, _BRAND_ORDER)) + 1


def round_if_available(value: _Number | None, precision: int = 0) -> _Number | None:
    return round(value, precision) if value is not None else None


def get_packaging_ordering_key(sku_name: str | None, category: str | None, liner_group: LinerGroupEnum | None) -> tuple:
    normalized_name = (sku_name or "").upper().replace(",", "").replace(" NEW ", " ")
    try:
        brand_part = normalized_name[: normalized_name.index(" ")]
    except ValueError:
        brand_part = ""
    brand_order = get_value_order(brand_part, _BRAND_ORDER, default_order=-1)
    if brand_order != -1:
        normalized_name = normalized_name.replace(_BRAND_ORDER[brand_order], "")
    size_order = next(
        (i for i, val in enumerate(_PACKAGING_SIZE_ORDER) if val in normalized_name), len(_PACKAGING_SIZE_ORDER)
    )
    if "BOX" in normalized_name:
        normalized_name = ""
    is_sized = size_order < len(_PACKAGING_SIZE_ORDER)
    liner_order = liner_group.order if liner_group else -1
    category_order = get_value_order(category, _PACKAGING_CATEGORY_ORDER)
    ordering_name = normalized_name.replace(_PACKAGING_SIZE_ORDER[size_order], " ") if is_sized else normalized_name
    return liner_order, category_order, brand_order, ordering_name, size_order


def get_value_order(value: str | None, order: list[str], default_order: int | None = None) -> int:
    value_ = (value or "").upper()
    return next(
        (i for i, val in enumerate(order) if val == value_), len(order) + 1 if default_order is None else default_order
    )


def to_string(item: Any) -> str | None:
    return str(item) if item else None
