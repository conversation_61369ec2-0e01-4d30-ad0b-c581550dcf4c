import inspect
import logging

from flask import jsonify, request
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from jsonschema import exceptions
from redis import RedisError
from werkzeug.exceptions import HTTPException

from procurement.core.exceptions.api_errors import ApiError
from procurement.core.exceptions.db_errors import DbBusinessError, HjOperationalError
from procurement.core.exceptions.gsheets import GSheetValidationError
from procurement.core.exceptions.validation_errors import InvalidDataException
from procurement.core.metrics import ApplicationMetrics

logger = logging.getLogger(__name__)


def generic_handler(exception):
    if isinstance(exception, HTTPException):
        return _http_exception_handler(exception)
    try:
        raise exception
    except Exception:
        logger.error("Generic error handler, unexpected exception", exc_info=True)

    ApplicationMetrics.non_critical_exceptions(exception=exception, service="unknown")

    return {"msg": f"Request has been failed: due to {str(exception)}"}, 500


def _http_exception_handler(exception: HTTPException):
    try:
        verify_jwt_in_request(optional=True, skip_revocation_check=True)
        user = get_jwt_identity()
    except Exception:
        user = None
    logger.error(
        "[HTTP Exception] Path: %s %s - %s, remote_addr=%s, agent=%s, user_id=%s",
        request.method,
        request.path,
        exception.description,
        request.remote_addr,
        request.user_agent,
        user,
    )
    if not user:
        return "Unauthorized or expired token", 401
    return exception


def register_errors(api):
    _register_api_handlers(api)

    _register_prometheus_handlers(api)


def handle_invalid_data_exception(error):
    logger.exception("Wrong value, %s", error)
    response = jsonify({"msg": "Request is invalid. Details: " + str(error)})
    response.status_code = 400
    return response


def handle_api_error(error):
    response = jsonify(error.to_dict())
    response.status_code = error.status_code
    return response


def handle_validation_error(error):
    logger.exception("ERROR: Wrong request schema, %s", repr(error))
    response = jsonify({"msg": "Request is invalid", "details": str(error)})
    response.status_code = 400
    return response


def handle_db_business_error(error: DbBusinessError):
    response = jsonify({"msg": f"{error} "})
    response.status_code = error.status_code
    return response


def handle_gsheet_validation_error(error):
    response = jsonify({"msg": f"{error} "})
    response.status_code = 400
    return response


def handle_peewee_client_error(error):
    msg_error = _handle_module_error(ApplicationMetrics.critical_exceptions, error, "peewee", "peewee_query")
    return msg_error


def handle_peewee_connection_error(error):
    msg_error = _handle_module_error(
        ApplicationMetrics.critical_exceptions, error, "playhouse.pool", "peewee_connection"
    )
    return msg_error


def _handle_module_error(metric, error, module_name, ex_service_name):
    logger.exception(error)
    frame = inspect.trace()[-1]
    mod = inspect.getmodule(frame[0])
    modname = mod.__name__ if mod else frame[1]

    if modname == module_name:
        service_name = ex_service_name
        metric(exception=error, service=service_name)
        return {"msg": f"Error when interacting with {service_name}: {str(error)}"}, 500
    return {"msg": f"Unexpected internal error: {str(error)}"}, 500


def _register_api_handlers(api):
    api.register_error_handler(InvalidDataException, handle_invalid_data_exception)
    api.register_error_handler(ApiError, handle_api_error)
    api.register_error_handler(exceptions.ValidationError, handle_validation_error)
    api.register_error_handler(DbBusinessError, handle_db_business_error)
    api.register_error_handler(GSheetValidationError, handle_gsheet_validation_error)
    api.register_error_handler(ValueError, handle_peewee_client_error)
    api.register_error_handler(ImportError, handle_peewee_connection_error)


def _register_prometheus_handlers(api):
    api.register_error_handler(
        HjOperationalError, lambda e: _create_prometheus_handler(ApplicationMetrics.critical_exceptions, e, "hj")
    )
    api.register_error_handler(
        RedisError, lambda e: _create_prometheus_handler(ApplicationMetrics.critical_exceptions, e, "redis")
    )


def _create_prometheus_handler(metric, exception, service_name, exit_code=500):
    logger.exception(exception)

    metric(exception=exception, service=service_name)

    error_message = str(exception)

    return {"msg": f"Service has been failed: {service_name}, due to {error_message}"}, exit_code
