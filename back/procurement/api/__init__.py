import logging
import time

from flask import Blueprint, Response, g, request

from procurement.api import error_handling
from procurement.core.metrics import ApplicationMetrics

from . import v1, v2

logger = logging.getLogger(__name__)


api_bp = Blueprint("api", __name__, url_prefix="/api/")


@api_bp.before_app_request
def before_request():
    g.start = time.time()


@api_bp.after_app_request
def after_request(response: Response) -> Response:
    diff = time.time() - g.start
    if diff > 15:
        ApplicationMetrics.critical_exceptions(
            service="api", message=f"Request {request.method} {request.full_path} took {diff} seconds."
        )
    logger.info(
        "[Request time] Path : %s %s | Time : %ss | Status : %s",
        request.method,
        request.full_path,
        diff,
        response.status,
    )
    return response


def register_api() -> Blueprint:
    for package in (v1, v2):
        api_bp.register_blueprint(package.register_api())

    error_handling.register_errors(api_bp)

    logger.debug("Blueprints successfully registered.")
    return api_bp
