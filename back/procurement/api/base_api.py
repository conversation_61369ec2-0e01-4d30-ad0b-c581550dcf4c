import logging
from collections.abc import Iterable
from functools import wraps

from flask import make_response, request
from flask.views import MethodView
from flask_cors.core import ACL_EXPOSE_HEADERS
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from jsonschema import validate

from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import MARKET_HEADER, MARKET_US
from procurement.core import request_utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidPermission, InvalidRequestException
from procurement.core.request_utils import context, warnings
from procurement.managers.admin import dc_admin
from procurement.managers.social import users

from .schema import base_schema

logger = logging.getLogger(__name__)


def authenticated(function):
    @wraps(function)
    def wrapper(*args, **kwargs):
        verify_jwt_in_request()
        me_info = get_jwt_identity()
        user = me_info.get("email")
        if user is None:
            user = me_info.get("id")
        logger.info("[Request] Path : %s %s | User : %s", request.method, request.full_path, user)
        return function(*args, **kwargs)

    return wrapper


def format_response(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        response = func(*args, **kwargs)
        msg = "success"
        if isinstance(response, tuple) and not hasattr(response, "_fields"):
            data, status = response
        else:
            data, status = response, 200

        available_warnings = warnings.get_warnings()

        if isinstance(data, dict) and "msg" in data:
            response_data = data
            response_data.update(available_warnings)
        else:
            response_data = {"msg": msg, "data": data, **available_warnings}

        return make_response(response_data, status)

    return wrapper


class BaseApi(MethodView):
    decorators = [format_response, authenticated]

    permission: Permissions = None

    # Replace with another values in child class to override permission requirements

    permission_mapping = {
        "GET": lambda permission: permission.read,
        "POST": lambda permission: permission.write,
        "PUT": lambda permission: permission.write,
        "PATCH": lambda permission: permission.write,
        "DELETE": lambda permission: permission.write,
    }

    # Set False in child class, to not verify permissions
    should_verify_permission = True

    def __init__(self):
        verify_jwt_in_request()

        self.request_context = context.get_request_context()
        markets = dc_admin.get_all_market_codes()
        market = request.headers.get(MARKET_HEADER)
        if not market:
            default_market = MARKET_US if MARKET_US in markets else next(iter(markets))
            request_utils.warnings.add_message(
                f"'{MARKET_HEADER}' header is not set, using '{default_market}' as default market"
            )
            market = default_market
        if market not in dc_admin.get_all_market_codes():
            raise InvalidRequestException(f"Market '{market}' is not available.")
        self.request_context.market = market
        self.request_context.user_info = users.get_user_info_by_id(user_id=get_jwt_identity()["id"])

        if self.should_verify_permission:
            self._verify_permission()

    def _verify_permission(self) -> None:
        if self.current_permission not in self.request_context.user_permissions.permissions:
            raise InvalidPermission(self.current_permission)

    @property
    def current_permission(self) -> str:
        return self.permission_mapping[request.method](self.permission)

    def _trim_request(self, request_payload: dict | list) -> None:
        if not request_payload:
            return
        pairs = request_payload.items() if isinstance(request_payload, dict) else enumerate(request_payload)
        for key, value in pairs:
            if isinstance(value, str):
                request_payload[key] = value.strip()
            elif isinstance(value, (dict, list)):
                self._trim_request(value)


class BaseApiWithSites(BaseApi):
    def __init__(self):
        super().__init__()
        validate(request.args, base_schema.base_with_sites_schema)
        self.brand = request.args["brand"]
        self.weeks = tuple(ScmWeek.from_str(week.strip()) for week in request.args["week"].split(","))

        if request.args.get("dc"):
            self.sites = [request.args.get("dc")]
        elif request.args.get("site"):
            self.sites = [request.args.get("site")]
        else:
            self.sites = self._get_sites()

        available_sites = self.request_context.user_permissions.available_sites

        self.sites = [site for site in self.sites if site in available_sites.get(self.brand, [])]

    def _get_sites(self) -> Iterable[str]:
        sites = set()
        for week in self.weeks:
            sites.update(dc_admin.get_enabled_sites(week, self.brand))
        return sites


def format_file_response(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        file, filename = func(*args, **kwargs)
        headers = {
            ACL_EXPOSE_HEADERS: ["Content-Disposition"],
            "Content-Disposition": f"attachment; filename={filename}",
            "Content-type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        }
        return make_response(file, headers)

    return wrapper


class FileExportBaseApi(BaseApi):
    decorators = [format_file_response, authenticated]
