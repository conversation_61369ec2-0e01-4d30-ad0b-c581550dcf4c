from procurement.core.dates import Weekday
from procurement.managers.depletion.constants import DAY_PASSED, NO_IMPACT, SUPPLEMENT_NEEDED
from procurement.managers.imt.v2.depletion.packaging.core import WEEKDAYS_TITLES_US

_weekly_overview_schema = {
    "demand": {"type": ["integer", "null"]},
    "startWeekInventory": {"type": ["integer", "null"]},
    "unitsOrdered": {"type": ["integer", "null"]},
    "unitsReceived": {"type": ["integer", "null"]},
    "eowInventory": {"type": ["integer", "null"]},
    "overconsumption": {"type": ["integer", "null"]},
}

_daily_needs_schema = {
    "date": {"type": "string", "format": "date"},
    "weekDay": {"enum": [*WEEKDAYS_TITLES_US, str(Weekday.WED)]},
    "onHand": {"type": ["integer", "null"]},
    "onHandOverride": {"type": ["integer", "null"]},
    "onHandProjected": {"type": "integer"},
    "onHandMinOnHandProjected": {"type": ["integer", "null"]},
    "demand": {"type": "integer"},
    "incoming": {"type": "integer"},
    "incomingOverride": {"type": ["integer", "null"]},
    "eodInventory": {"type": "integer"},
    "status": {"enum": [DAY_PASSED, NO_IMPACT, SUPPLEMENT_NEEDED]},
}

_depletion_item_schema = {
    "sku": {"type": ["string", "null"]},
    "skuName": {"type": "string"},
    "category": {"type": ["string", "null"]},
    "commodityGroup": {"type": ["string", "null"]},
    "buyer": {"type": ["string", "null"]},
    "supplementNeed": {"enum": [*WEEKDAYS_TITLES_US, NO_IMPACT, str(Weekday.WED)]},
    "unitsShort": {"type": ["integer", "null"]},
    "weeklyOverview": {
        "type": "object",
        "properties": _weekly_overview_schema,
        "required": list(_weekly_overview_schema),
    },
    "dailyNeeds": {
        "type": "array",
        "minItems": 7,
        "maxItems": 8,
        "items": {
            "type": "object",
            "properties": _daily_needs_schema,
            "required": list(_daily_needs_schema),
        },
    },
    "linerGroup": {"type": ["string", "null"]},
    "liners": {"type": "null"},
}

depletion_override_schema = {
    "type": "object",
    "properties": {
        "sku": {"type": "string"},
        "overrideDate": {"type": "string", "format": "date"},
        "data": {
            "type": "object",
            "properties": {
                **_depletion_item_schema,
                "liners": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": _depletion_item_schema,
                        "required": list(_depletion_item_schema),
                    },
                },
            },
            "required": list(_depletion_item_schema),
        },
    },
    "required": ["sku", "overrideDate", "data"],
}
