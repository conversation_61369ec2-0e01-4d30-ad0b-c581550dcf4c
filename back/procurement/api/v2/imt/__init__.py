from flask import Blueprint

from . import inventory, packaging_depletion, perpetrual_gross_needs


def register_api() -> Blueprint:
    api_bp = Blueprint("imt-v2", __name__, url_prefix="imt/")
    for path, resource in {
        "inventory/calculation/": inventory.Calculation.as_view("inventory-calculation"),
        "inventory/pgn/": perpetrual_gross_needs.PerpetualGrossNeed.as_view("inventory-pgn"),
        "packaging-depletion/": packaging_depletion.PackagingDepletionV2.as_view("packaging-depletion"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
