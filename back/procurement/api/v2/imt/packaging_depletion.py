from collections.abc import Iterable
from datetime import date

import jsonschema
from flask import request

from procurement.api import formatting
from procurement.api.base_api import BaseApiWithSites
from procurement.auth.permissions import Permissions
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.data.dto.inventory.packaging import PackagingOverrideKey
from procurement.managers.imt.packaging_inventory import LinerGroupEnum
from procurement.managers.imt.v2.depletion import packaging
from procurement.managers.imt.v2.depletion.packaging import PackagingDepletionDashboard
from procurement.managers.imt.v2.depletion.packaging.core import (
    DailyNeeds,
    DepletionWeeklyItem,
    PackagingDepletionModel,
)

from .schema.packaging_depletion import depletion_override_schema

_EMPTY_WEEKLY = {
    "demand": None,
    "startWeekInventory": None,
    "unitsOrdered": None,
    "unitsReceived": None,
    "eowInventory": None,
    "overconsumption": None,
}

_LINER_GROUP_BY_NAME = {str(g): g for g in LinerGroupEnum}


class PackagingDepletionV2(BaseApiWithSites):
    permission = Permissions.IMT_PCK_DEPL_V2

    @ApplicationMetrics.functional_methods(request_type="api")
    def get(self):
        res = {}
        if len(self.weeks) > 1:
            raise InvalidRequestException("You can only get the dashboard for one week at a time")

        def sorting_key(_item: PackagingDepletionModel) -> tuple:
            return formatting.get_packaging_ordering_key(_item.sku_name, _item.sku_type, _item.liner_group)

        for site in self.sites:
            dashboard = PackagingDepletionDashboard(week=self.weeks[0], site=site, brand=self.brand)
            data = dashboard.get_result()
            data.sort(key=sorting_key)
            for item in data:
                if item.is_liner_group:
                    item.liners.sort(key=lambda i: i.sku_name)
            res[site] = [self._build_response_row(r) for r in data]
        return res

    def post(self):
        if len(self.weeks) > 1 or len(self.sites) > 1:
            raise InvalidRequestException("You can only post for one week and site at a time")
        body = request.json
        jsonschema.validate(body, depletion_override_schema)
        model = self._build_model_from_override_request(body["data"])
        if model.liner_group and not model.liners:
            raise InvalidRequestException("For liners you have to send the whole liner group with its liners")
        override_key = PackagingOverrideKey(
            week=self.weeks[0],
            site=self.sites[0],
            brand=self.brand,
            sku_code=body["sku"],
            day=date.fromisoformat(body["overrideDate"]),
        )
        result_model = packaging.override_depletion(key=override_key, model=model)
        return self._build_response_row(result_model)

    @staticmethod
    def _build_response_row(row: PackagingDepletionModel) -> dict:
        weekly = row.weekly_overview
        return {
            "sku": row.sku_code,
            "skuName": row.sku_name,
            "category": row.category,
            "commodityGroup": row.commodity_group,
            "buyer": row.buyer,
            "supplementNeed": row.supplement_need,
            "isEarlySupplementNeeded": row.is_early_supplement_need,
            "unitsShort": row.units_short,
            "weeklyOverview": (
                {
                    "demand": weekly.total_demand,
                    "startWeekInventory": weekly.bow_inventory,
                    "unitsOrdered": weekly.units_ordered,
                    "unitsReceived": weekly.units_received,
                    "eowInventory": weekly.eow_inventory,
                    "overconsumption": weekly.overconsumption,
                }
                if not row.is_liner_group
                else _EMPTY_WEEKLY
            ),
            "dailyNeeds": [
                {
                    "date": daily.day.isoformat(),
                    "weekDay": daily.week_day,
                    "onHand": daily.on_hand,
                    "onHandOverride": daily.on_hand_override,
                    "onHandProjected": daily.on_hand_projected,
                    "onHandMinOnHandProjected": daily.on_hand_min_on_hand_projected,
                    "demand": daily.demand,
                    "incoming": daily.incoming,
                    "incomingOverride": daily.incoming_override,
                    "eodInventory": daily.eod_inventory,
                    "status": daily.status,
                }
                for daily in row.daily_needs
            ],
            "linerGroup": formatting.to_string(row.liner_group),
            "liners": (
                [PackagingDepletionV2._build_response_row(ln) for ln in row.liners] if row.is_liner_group else None
            ),
        }

    @staticmethod
    def _build_model_from_override_request(body: dict) -> PackagingDepletionModel:
        weekly = body["weeklyOverview"]
        daily = body["dailyNeeds"]
        return PackagingDepletionModel(
            sku_code=body["sku"],
            sku_name=body["skuName"],
            category=body["category"],
            commodity_group=body["commodityGroup"],
            market=context.get_request_context().market,
            buyer=body["buyer"],
            supplement_need=body["supplementNeed"],
            units_short=body["unitsShort"],
            weekly_overview=(
                PackagingDepletionV2._build_weekly_from_request(weekly, daily) if not body["liners"] else None
            ),
            daily_needs=PackagingDepletionV2._build_daily_from_request(daily),
            liner_group=_LINER_GROUP_BY_NAME.get(body["linerGroup"]),
            liners=(
                [PackagingDepletionV2._build_model_from_override_request(ln) for ln in body["liners"]]
                if body["liners"]
                else None
            ),
        )

    @staticmethod
    def _build_weekly_from_request(weekly: dict, daily: list[dict]) -> DepletionWeeklyItem:
        return DepletionWeeklyItem(
            total_demand=weekly["demand"],
            bow_inventory=weekly["startWeekInventory"],
            units_ordered=weekly["unitsOrdered"],
            units_received=weekly["unitsReceived"],
            eow_inventory=weekly["eowInventory"],
            estimated_eow_inventory=daily[-1]["eodInventory"] - daily[-1]["incoming"],
        )

    @staticmethod
    def _build_daily_from_request(daily: Iterable[dict]) -> list[DailyNeeds]:
        return [
            DailyNeeds(
                day=date.fromisoformat(d["date"]),
                week_day=d["weekDay"],
                on_hand=d["onHand"],
                on_hand_override=d["onHandOverride"],
                demand=d["demand"],
                incoming=d["incoming"],
                incoming_override=d["incomingOverride"],
                on_hand_projected=d["onHandProjected"],
                on_hand_min_on_hand_projected=d["onHandMinOnHandProjected"],
                eod_inventory=d["eodInventory"],
                status=d["status"],
            )
            for d in daily
        ]
