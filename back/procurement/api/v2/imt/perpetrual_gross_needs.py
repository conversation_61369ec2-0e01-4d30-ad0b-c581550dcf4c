from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.managers.imt.v2.inventory.calculation import gross_need
from procurement.managers.imt.v2.inventory.calculation.gross_need import PgnItem


class PerpetualGrossNeed(base_api.BaseApiWithSites):
    permission = Permissions.IMT_ING_DEPL_V2

    def get(self):
        if not self.sites:
            return []
        result = gross_need.get_pgn_data(self.brand, self.sites[0])
        return [self.build_response(item) for item in result]

    @staticmethod
    def build_response(item: PgnItem):
        weeks = {
            str(week): {
                "days": {date.strftime(DATE_FORMAT): val for date, val in week_data.days.items()},
                "sum": week_data.sum,
            }
            for week, week_data in sorted(item.weeks.items())
        }
        return {
            "sku": item.sku_code,
            "skuName": item.sku_name,
            "category": item.category,
            "commodityGroup": item.commodity_group,
            "buyer": item.buyer,
            "weeks": weeks,
        }
