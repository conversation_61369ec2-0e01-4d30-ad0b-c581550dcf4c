from datetime import date

from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.core.request_utils import warnings
from procurement.managers.imt.v2.inventory.calculation import calculations

from .schema.calculations_schema import get_calculation_schema


class Calculation(base_api.BaseApiWithSites):
    permission = Permissions.IMT_ING_DEPL_V2

    def get(self):
        args = request.args
        validate(args, get_calculation_schema)
        date_from = None
        date_to = None
        if "dateFrom" in args:
            date_from = date.fromisoformat(args["dateFrom"])
        if "dateTo" in args:
            date_to = date.fromisoformat(args["dateTo"])
        if not self.sites:
            return []
        try:
            result = calculations.get_inventory_calculations(
                brand=self.brand, site=self.sites[0], date_from=date_from, date_to=date_to
            )
            return list(map(self.parse_response_item, sorted(result, key=lambda v: v.sku_name)))
        except ValueError as exc:
            warnings.add_message(str(exc))
            return None

    @staticmethod
    def parse_response_item(item: calculations.CalculationItem) -> dict:
        return {
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "purchasingCategory": item.category,
            "commodityGroup": item.commodity_group,
            "buyers": item.buyer,
            "days": [
                {
                    "day": day.day.strftime(DATE_FORMAT),
                    "week": str(day.week),
                    "carryover": day.carryover,
                    "stockBuffer": day.stock_buffer,
                    "adjustment": day.adjustment,
                    "incoming": day.incoming_scheduled,
                    "inbound": day.inbound_received,
                    "consumption": day.consumption,
                    "closingStock": day.closing_stock,
                    "dailyNeeds": day.daily_needs,
                    "stockDifference": day.stock_difference,
                    "status": day.status,
                }
                for day in item.days
            ],
        }
