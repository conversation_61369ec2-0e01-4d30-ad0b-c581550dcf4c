from typing import Any

from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import Domain
from procurement.core import dates as date_util
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.typing import SITE
from procurement.data.models.constants import CommentType
from procurement.managers.social import comment as comment_service
from procurement.managers.social.comment import CommentResource

from .schema import comment as schema

PERMISSION_MAPPING = {
    Domain.IMT.name.lower(): Permissions.IMT_COMMENT_V2,
    Domain.PIMT.name.lower(): Permissions.PIMT_COMMENT_V2,
}


class CommentBase(base_api.BaseApi):
    def _verify_permission(self) -> None:
        domain = request.view_args.get("domain")

        self.permission = PERMISSION_MAPPING.get(domain)
        if not self.permission:
            raise InputParamsValidationException(
                f"Invalid domain {domain}, must be one of: {PERMISSION_MAPPING.keys()}"
            )
        super()._verify_permission()


class Comment(CommentBase):
    def post(self, domain: str, resource_type: str):
        validate(domain, schema.domain)
        validate(resource_type, schema.resource_type)
        validate(request.json, schema.post_comment)
        _id = comment_service.upsert_comment(
            comment_service.CommentInput(
                domain=Domain.__members__[domain.upper()],
                week=ScmWeek.from_str(request.json["week"]),
                site=request.json["site"],
                brand=request.json.get("brand"),
                comment=request.json["comment"],
                resource_id=request.json["resourceId"],
                resource_type=CommentType[resource_type.upper()],
            ),
        )
        return {"id": _id}, 201

    def get(self, domain: str, resource_type: str):
        validate(domain, schema.domain)
        validate(resource_type, schema.resource_type)
        validate(request.args, schema.get_comment)
        week = ScmWeek.from_str(request.args["week"])
        domain = Domain.__members__[domain.upper()]
        brand = request.args.get("brand")
        sites = utils.split_comma_string(request.args["sites"])
        resource_id = request.args["resourceId"]
        comments_result = comment_service.get_comments_view(
            week=week,
            sites=sites,
            brand=brand,
            comment_resource=CommentResource(
                domain=domain, resource_id=resource_id, resource_type=CommentType(resource_type)
            ),
        )
        res = self._build_get_comment_response(comments_result, sites)
        return res

    @staticmethod
    def _build_get_comment_response(comments, sites) -> dict[SITE, dict[str, Any]]:
        res = {site: {} for site in sites}
        for item in comments:
            res[item.site] = {
                "lastUpdated": str(item.last_updated),
                "lastUpdatedBy": item.updated_by,
                "value": item.comment,
            }
        return res

    def delete(self, domain: str, resource_type: str):
        validate(domain, schema.domain)
        validate(resource_type, schema.resource_type)
        validate(request.args, schema.delete_comment)
        domain = Domain.__members__[domain.upper()]
        week = date_util.ScmWeek.from_str(request.args["week"])
        brand = request.args.get("brand")
        site = request.args["site"]
        resource_id = request.args["resourceId"]
        comment_service.delete_comment(
            week=week,
            brand=brand,
            site=site,
            comment_resource=CommentResource(
                domain=domain, resource_id=resource_id, resource_type=CommentType[resource_type.upper()]
            ),
        )
        return None, 204


class CommentPreview(CommentBase):
    def get(self, domain: str, resource_type: str):
        validate(resource_type, schema.resource_type)
        validate(request.args, schema.preview_comment)
        domain = Domain.__members__[domain.upper()]
        brand = request.args.get("brand")
        sites = utils.split_comma_string(request.args["sites"])
        weeks = tuple(ScmWeek.from_str(week.strip()) for week in request.args["week"].split(","))
        return self._build_response(
            comment_service.get_preview(domain, weeks, brand, sites, CommentType[resource_type.upper()])
        )

    @staticmethod
    def _build_response(comment_preview_data: dict[ScmWeek, dict[str, dict[str, list[str]]]]):
        return {str(week): resource_id_by_site for week, resource_id_by_site in comment_preview_data.items()}


class CommentLogBase(base_api.BaseApi):
    @staticmethod
    def _get_additional_fields(item: comment_service.Comment) -> dict[str, Any]:
        additional_fields = {}
        if item.brand:
            additional_fields["brand"] = item.brand
        if item.region:
            additional_fields["region"] = item.region
        return additional_fields


class SkuCommentLog(CommentLogBase):
    permission = Permissions.IMT_COMMENT_LOG_V2

    def get(self, domain):
        validate(request.args, schema.comments_log)
        domain = Domain.__members__[domain.upper()]
        week = date_util.ScmWeek.from_str(request.args["week"])
        return [self._build_comment_log_response(item) for item in comment_service.get_sku_comments_log(domain, week)]

    def _build_comment_log_response(self, item: comment_service.Comment) -> dict[str, Any]:
        return {
            "id": item.comment_id,
            "site": item.site,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "commodityGroup": item.commodity_group_name,
            "purchasingCategory": item.purchasing_category_name,
            "comment": item.comment,
            "lastUpdated": item.last_updated.isoformat(),
            "lastUpdatedBy": item.updated_by,
            **self._get_additional_fields(item),
        }


class PoCommentLog(CommentLogBase):
    permission = Permissions.IMT_COMMENT_LOG_V2

    def get(self, domain):
        validate(request.args, schema.comments_log)
        domain = Domain.__members__[domain.upper()]
        week = date_util.ScmWeek.from_str(request.args["week"])

        return [self.build_get_response(item) for item in comment_service.get_po_comments_log(domain, week)]

    def build_get_response(self, item: comment_service.Comment) -> dict[str, Any]:
        return {
            "id": item.comment_id,
            "site": item.site,
            "skuCode": item.sku_code,
            "skuName": item.sku_name,
            "poNumber": item.po_number,
            "comment": item.comment,
            "lastUpdated": item.last_updated.isoformat(),
            "lastUpdatedBy": item.updated_by,
            **self._get_additional_fields(item),
        }
