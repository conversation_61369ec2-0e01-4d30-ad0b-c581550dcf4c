from flask import Blueprint

from .comments import Comment, CommentPreview, PoCommentLog, SkuCommentLog
from .me import Me
from .release_log import ReleaseLogLatest
from .user_view_state import UserViewStateItem, UserViewStateResource
from .week_info import QuartersWeeks, WeekInfo


def register_api() -> Blueprint:
    api_bp = Blueprint("social-v2", __name__)
    for path, resource in {
        "viewstate/": UserViewStateResource.as_view("viewstates"),
        "viewstate/<int:view_id>/": UserViewStateItem.as_view("viewstate-info"),
        "me/": Me.as_view("me"),
        "comment/<domain>/<resource_type>/": Comment.as_view("comments"),
        "comment/<domain>/<resource_type>/preview/": CommentPreview.as_view("comments-preview"),
        "comment/<domain>/po/log/": PoCommentLog.as_view("comments-log-po"),
        "comment/<domain>/sku/log/": SkuCommentLog.as_view("comments-log-sku"),
        "week-info/<week>/": WeekInfo.as_view("week-info"),
        "quarters-weeks/": QuartersWeeks.as_view("quarters-weeks"),
        "release-log/latest": ReleaseLogLatest.as_view("release-log-latest"),
    }.items():
        api_bp.add_url_rule(path, view_func=resource)
    return api_bp
