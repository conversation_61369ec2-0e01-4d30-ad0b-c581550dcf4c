state_add = {
    "type": "object",
    "properties": {"resource": {"type": "string"}, "name": {"type": "string"}, "state": {"type": "object"}},
    "required": ["resource", "name", "state"],
}

state_update = {"type": "object", "properties": {"name": {"type": "string"}, "state": {"type": "object"}}}

state_get = {"type": "object", "properties": {"resource": {"type": "string"}}, "required": ["resource"]}
