from procurement.data.models.constants import CommentType

post_comment = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "site": {"type": "string"},
        "resourceId": {"type": "string"},
        "comment": {
            "type": "string",
            "maxLength": 4000,
        },
    },
    "required": ["site", "resourceId", "comment"],
    "additionalProperties": False,
}

get_comment = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "sites": {"type": "string"},
        "resourceId": {"type": "string"},
    },
    "required": ["week", "sites", "resourceId"],
    "additionalProperties": False,
}

delete_comment = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "week": {"type": "string", "pattern": "^2[0-9]{3}-W[0-9]{2}$"},
        "site": {"type": "string"},
        "resourceId": {"type": "string"},
    },
    "required": ["brand", "week", "site", "resourceId"],
    "additionalProperties": False,
}

resource_type = {
    "type": "string",
    "enum": list(CommentType),
}

domain = {
    "type": "string",
    "enum": ["imt", "pimt"],
}

preview_comment = {
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "week": {"type": "string"},
        "sites": {"type": "string", "minLength": 2},
    },
    "required": ["sites", "week"],
}

comments_log = {
    "type": "object",
    "properties": {
        "week": {"type": "string"},
    },
    "required": ["week"],
}
