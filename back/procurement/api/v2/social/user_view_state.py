from flask import request
from jsonschema import validate

from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.data.dto.procurement.user_view_state import UserViewState
from procurement.managers.social import user_view_state

from .schema import user_view_state as schema


class UserViewStateBase(base_api.BaseApi):
    permission = Permissions.GENERAL

    @staticmethod
    def _build_response(item: UserViewState):
        resp = {"id": item.state_id, "name": item.name}
        if item.state:
            resp["state"] = item.state
        return resp


class UserViewStateResource(UserViewStateBase):
    def get(self):
        request_args = request.args
        validate(request_args, schema.state_get)
        return [self._build_response(item) for item in user_view_state.get_states(request_args["resource"])]

    def post(self):
        data = request.json
        validate(data, schema.state_add)
        return user_view_state.add_state(data)


class UserViewStateItem(UserViewStateBase):
    def get(self, view_id: int):
        return self._build_response(user_view_state.get_state(view_id))

    def delete(self, view_id: int):
        user_view_state.delete_state(view_id)
        return None, 204

    def patch(self, view_id: int):
        data = request.json
        validate(data, schema.state_update)
        user_view_state.update_state(view_id, data)
