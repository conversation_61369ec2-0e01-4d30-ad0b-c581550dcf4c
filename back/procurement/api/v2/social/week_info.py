from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.core.dates import ScmQuarter, ScmWeek, quarters
from procurement.managers.admin import dc_admin
from procurement.managers.social import week_info
from procurement.managers.social.week_info import BrandInfo, SiteInfo


class WeekQuarterInfo(base_api.BaseApi):
    permission = Permissions.GENERAL


class QuartersWeeks(WeekQuarterInfo):
    def get(self):
        return {
            "quarters": self._build_response(quarters.get_weeks_for_quarters()),
            "defaultCurrentWeek": str(ScmWeek.current_week()),
        }

    def _build_response(self, quarters_weeks_dict: dict[ScmQuarter, list[ScmWeek]]):
        return {
            str(quarter): self._build_quarter_info(quarters_weeks)
            for quarter, quarters_weeks in quarters_weeks_dict.items()
        }

    @staticmethod
    def _build_quarter_info(quarters_weeks: list[ScmWeek]):
        return [str(week) for week in quarters_weeks]


class WeekInfo(WeekQuarterInfo):
    def get(self, week: str):
        week = ScmWeek.from_str(week)
        return {
            "brandInfo": {
                brand: self._build_response(items) for brand, items in week_info.get_configs_for_week(week).items()
            },
            "multiBrandDcs": list(dc_admin.get_all_enabled_multi_brand_sites(week)),
        }

    def _build_response(self, week_config_info: BrandInfo):
        return {
            "currentWeek": str(week_config_info.current_week),
            "consolidated": week_config_info.consolidated,
            "brandName": week_config_info.brand_name,
            "sites": [self._build_site_info(site) for site in week_config_info.sites],
        }

    @staticmethod
    def _build_site_info(site_info: SiteInfo):
        return {
            "name": site_info.name,
            "fullName": site_info.full_name,
            "is3pl": site_info.is_3pl,
            "isFulfilmentOnly": site_info.is_fulfilment_only,
            "isHj": site_info.is_hj,
            "isHjAutostore": site_info.is_hj_autostore,
            "isGrn": site_info.is_grn,
            "discardSource": site_info.discard_source,
            "inventoryType": site_info.inventory_type,
            "receivingType": site_info.receiving_type,
        }
