from procurement.api import base_api
from procurement.auth.permissions import Permissions
from procurement.managers.admin import brand_admin
from procurement.managers.social.models import UserInfo, UserPermissions


class Me(base_api.BaseApi):
    permission = Permissions.GENERAL
    should_verify_permission = False

    def get(self):
        ctx = self.request_context
        return self._build_response(ctx.user_info, ctx.user_permissions)

    @staticmethod
    def _build_response(item: UserInfo, user_permissions: UserPermissions) -> dict:
        return {
            "id": item.user_id,
            "email": item.email,
            "name": item.name,
            "roles": item.roles,
            "picture": item.picture,
            "brands": list(brand_admin.get_latest_brands()),
            "isBuyer": item.is_buyer,
            "userPermissions": {
                "availableSites": list({s for bs in user_permissions.available_sites.values() for s in bs}),
                "permissions": list(user_permissions.permissions),
            },
        }
