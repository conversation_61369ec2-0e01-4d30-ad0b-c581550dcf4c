import logging
import os
import random
import socket
import traceback
from collections.abc import Iterable, Iterator, Sequence
from typing import Any, Callable, ParamSpec, TypeVar

import sqlalchemy as sqla
from sqlalchemy import Connection, Engine, Row
from sqlalchemy import event as sqla_event
from sqlalchemy import exc as sqla_exc

from procurement.client.slack import alerts
from procurement.client.slack.alerts import HjConnectionErrorAlert
from procurement.constants.hellofresh_constant import APP_TIMEZONE
from procurement.core.config_utils import config, killswitch
from procurement.core.exceptions.db_errors import HjOperationalError
from procurement.core.metrics import ApplicationMetrics

logger = logging.getLogger(__name__)


def on_db_connect(connection_record, **kwargs):  # pylint: disable=unused-argument
    connection_record.info["pid"] = os.getpid()


def on_db_checkout(connection_record, connection_proxy, **kwargs):  # pylint: disable=unused-argument
    pid = os.getpid()
    if connection_record.info["pid"] != pid:
        connection_record.dbapi_connection = connection_proxy.dbapi_connection = None
        raise sqla_exc.DisconnectionError(
            f"Connection record belongs to pid {connection_record.info['pid']}, attempting to check out in pid {pid}"
        )


def on_before_cursor_execute(statement: str, **kwargs):  # pylint: disable=unused-argument
    request_type = statement[: statement.find(" ")].upper().replace("(", "")
    func_name = ""
    if killswitch.enable_sql_stack_traced_metric:
        stack = reversed(traceback.extract_stack(limit=15))
        func_name = next((f.name for f in stack if "procurement/repository" in f.filename), "")
    ApplicationMetrics.db_metrics(request_type, func_name)


class DatabaseService:
    def __init__(self, url: sqla.URL, gather_metrics: bool, read_only_url: sqla.URL | None = None):
        options = {"options": f"-c timezone={str(APP_TIMEZONE)}"}
        self._engine = sqla.create_engine(url=url, connect_args=options)
        if read_only_url:
            self._ro_engine = sqla.create_engine(url=read_only_url, isolation_level="AUTOCOMMIT", connect_args=options)
            self._ro_balance_weight = config["database"].get("ro_balance_weight", 0.5)
        else:
            self._ro_engine = self._engine
            self._ro_balance_weight = 0
        self._init_events([self._engine, self._ro_engine] if read_only_url else [self._engine], gather_metrics)

    def transaction(self) -> Iterator[Connection]:
        return self._engine.begin()

    def _balanced_ro_conn(self) -> Engine:
        if self._ro_balance_weight >= 1:
            return self._ro_engine
        if self._ro_balance_weight == 0:
            return self._engine
        # since AWS RDS does not support balancing between replicas and main DB (at least not per query)
        # we are doing simple random balancing here
        # see: https://stackoverflow.com/a/59174248
        chance = random.random()  # nosec B311
        return self._ro_engine if chance < self._ro_balance_weight else self._engine

    def apply_query(
        self,
        query: sqla.Executable,
        params: list[dict[str, Any]] | dict[str, Any] = None,
        transaction: Connection = None,
    ) -> Sequence[Row] | int:
        """Use for query that alters data (insert, delete, update)"""
        if (
            isinstance(params, list)
            and not params
            or query.is_insert
            and not query._multi_values  # pylint: disable=protected-access
            and not query._values  # pylint: disable=protected-access
        ):
            return 0
        if transaction:
            res = transaction.execute(query, params)
            return res.fetchall() if res.returns_rows else res.rowcount
        with self._engine.connect() as connection:
            res = connection.execute(query, params)
            connection.commit()
            return res.fetchall() if res.returns_rows else res.rowcount

    def select_all(
        self, query: sqla.Executable, params: dict[str, Any] = None, transaction: Connection = None
    ) -> Sequence[Row]:
        """Use for select query"""
        if transaction:
            return transaction.execute(query, params).all()
        with self._balanced_ro_conn().connect() as connection:
            return connection.execute(query, params).all()

    def select_one(
        self, query: sqla.Executable, params: dict[str, Any] = None, transaction: Connection = None
    ) -> Row | None:
        """Use for selection of one record"""
        if transaction:
            return transaction.execute(query, params).first()
        with self._balanced_ro_conn().connect() as connection:
            return connection.execute(query, params).first()

    def select_scalar(
        self, query: sqla.Executable, params: dict[str, Any] = None, transaction: Connection = None
    ) -> Any | None:
        res = self.select_one(query, params, transaction)
        return res[0] if res else None

    @staticmethod
    def _init_events(engines: list[Engine], gather_metrics: bool) -> None:
        for engine in engines:
            sqla_event.listen(engine, "connect", on_db_connect, named=True)
            sqla_event.listen(engine, "checkout", on_db_checkout, named=True)
            if gather_metrics:
                sqla_event.listen(engine, "before_cursor_execute", on_before_cursor_execute, named=True)


P = ParamSpec("P")
R = TypeVar("R")


def _process_hj_error(func: Callable[P, R]) -> Callable[P, R]:
    def wrapper(self, *args: P.args, **kwargs: P.kwargs) -> R:
        raised_exc = None
        try:
            return func(self, *args, **kwargs)
        except sqla_exc.OperationalError as exc:
            raised_exc = exc
            raise HjOperationalError() from exc
        finally:
            msg = str(raised_exc) if raised_exc else None
            alerts.preventative_alert(HjConnectionErrorAlert(self.display_name, msg))

    return wrapper


class HighjumpService(DatabaseService):
    def __init__(self, url: sqla.URL, gather_metrics: bool, display_name: str):
        self.display_name = display_name
        super().__init__(url=url, gather_metrics=gather_metrics)

    @staticmethod
    def _prepare_query(query: str, params: dict[str, Any] = None) -> sqla.text:
        params = params or {}
        query = sqla.text(query)
        binds = []
        for key, value in params.items():
            if not isinstance(value, str) and isinstance(value, Iterable):
                binds.append(sqla.bindparam(key, type_=sqla.String(), expanding=True))
        if binds:
            query = query.bindparams(*binds)
        return query

    @_process_hj_error
    def select_all(self, query: str, params: dict[str, Any] = None, transaction: Connection = None) -> Sequence[Row]:
        query = self._prepare_query(query, params)
        return super().select_all(query, params, transaction)

    @_process_hj_error
    def apply_query(
        self,
        query: str,
        params: list[dict[str, Any]] | dict[str, Any] = None,
        transaction: Connection = None,
    ) -> Sequence[Row] | None:
        query = self._prepare_query(query, params)
        return super().apply_query(query, params, transaction)

    @_process_hj_error
    def select_one(self, query: str, params: dict[str, Any] = None, transaction: Connection = None) -> Row | None:
        query = self._prepare_query(query, params)
        return super().select_one(query, params, transaction)


def prepare_sqlalchemy_database(database_config: dict) -> DatabaseService:
    host = database_config.get("host", "localhost")
    params = {
        "drivername": "postgresql+psycopg2",
        "username": database_config.get("user"),
        "password": database_config.get("password"),
        # no comma means the host was a docker container or localhost
        # so adding this workaround since plain host does not always work in these cases
        "host": host if "." in host else socket.gethostbyname(host),
        "port": database_config.get("port", 5432),
        "database": database_config.get("name"),
    }
    main_url = sqla.URL.create(**params)

    ro_url = None
    if "ro_host" in database_config:
        params["host"] = database_config["ro_host"]
        ro_url = sqla.URL.create(**params)

    return DatabaseService(
        url=main_url,
        read_only_url=ro_url,
        gather_metrics=True,
    )


def prepare_hj_db(hj_db_config: dict, display_name: str) -> HighjumpService:
    return HighjumpService(
        url=sqla.URL.create(
            drivername="mssql+pyodbc",
            host=socket.gethostbyname(hj_db_config.get("host", "localhost")),
            username=hj_db_config.get("user", "sa"),
            password=hj_db_config.get("password", "My!StrongPassword"),
            port=hj_db_config.get("port", 1433),
            database=hj_db_config.get("database", "master"),
            query={"driver": "ODBC Driver 18 for SQL Server", "TrustServerCertificate": "yes"},
        ),
        gather_metrics=False,
        display_name=display_name,
    )


hj = prepare_hj_db(config["hj_db"], display_name="HF HighJump")
green_chef_hj = prepare_hj_db(config["green_chef_hj_db"], display_name="GC HighJump")
canada_hj = prepare_hj_db(config["canada_hj_db"], display_name="CA HighJump")
app_db = prepare_sqlalchemy_database(config["database"])
