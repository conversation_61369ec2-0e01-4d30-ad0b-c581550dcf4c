import logging
from dataclasses import asdict, dataclass, field

from procurement.core.cache_utils.factory import QUEUES
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import (
    ActionType,
    CommentEvent,
    NetworkDepletionCommentEvent,
    PoCommentEvent,
    ReplenishmentCommentEvent,
    SkuCommentEvent,
    WCCEvent,
    WhSkuCommentEvent,
)
from procurement.core.request_utils import context
from procurement.data.models.constants import CommentType

COMMENT_CHANGED_EVENT = "comment:changed"
WCC_CHANGED_EVENT = "coverageData:changed"
REPLENISHMENT_COMMENT_CHANGED_EVENT = "replenishmentComment:changed"
NETWORK_DEPLETION_COMMENT_CHANGED_EVENT = "networkDepletionComment:changed"

logger = logging.getLogger(__name__)


@dataclass
class NotificationEvent:
    type: str
    version: str = field(default=1, init=False)


@dataclass
class Notification:
    event: str
    data: NotificationEvent


@dataclass
class CommentNotificationEvent(NotificationEvent):
    action: str
    domain: str
    key: str
    week: str
    brand: str
    site: str
    updated_by: str
    message: str = None


@dataclass
class WCCNotificationEvent(NotificationEvent):
    action: str
    skuCode: str  # pylint: disable=invalid-name
    poNumber: str  # pylint: disable=invalid-name
    brand: str
    site: str
    week: str
    count: int | None
    isLastPo: bool  # pylint: disable=invalid-name


@dataclass
class ReplenishmentNotificationEvent(NotificationEvent):
    action: str
    market: str
    region: str
    skuCode: str
    text: str


@dataclass
class NetworkDepletionNotificationEvent(NotificationEvent):
    action: str
    market: str
    week: str


def _map_action_type(action_type: ActionType, with_added=True):
    if action_type == ActionType.DELETED:
        return "DELETED"
    if action_type == ActionType.UPDATED:
        return "UPDATED"
    if action_type == ActionType.ADDED and with_added:
        if with_added:
            return "ADDED"
        raise ValueError("Unsupported ActionType 'ADDED'")
    raise ValueError(f"Unknown ActionType {str(action_type)}")


def _push_notification(notification: Notification):
    try:
        QUEUES.user_notifications.push(asdict(notification))
    except Exception:
        logger.warning("Error on sending event %s:%s", notification.event, notification.data.type, exc_info=True)


@EVENT_BUS.subscribe(SkuCommentEvent)
def sku_comments_handler(event: SkuCommentEvent):
    """
    Example:
    {
      event: 'comment:changed',
      data: {
        type: 'po' | 'sku',
        key: // sku or po value,
        ...
        // message is redundant
      }
    }
    """
    _handle_comment_change(event, CommentType.SKU)


@EVENT_BUS.subscribe(PoCommentEvent)
def po_comments_handler(event: PoCommentEvent):
    _handle_comment_change(event, CommentType.PO)


@EVENT_BUS.subscribe(WhSkuCommentEvent)
def wh_sku_comments_handler(event: WhSkuCommentEvent):
    _handle_comment_change(event, CommentType.WH_SKU)


def _handle_comment_change(event: CommentEvent, comment_type: CommentType):
    data = CommentNotificationEvent(
        type=comment_type,
        domain=event.domain.value,
        week=str(event.week),
        brand=event.brand,
        site=event.site,
        key=event.item_key,
        action=_map_action_type(event.action_type, with_added=False),
        message=event.payload,
        updated_by=context.get_request_context().user_info.email,
    )
    _push_notification(Notification(event=COMMENT_CHANGED_EVENT, data=data))


@EVENT_BUS.subscribe(WCCEvent)
def wcc_handler(event: WCCEvent):
    data = WCCNotificationEvent(
        type="wcc",
        action=_map_action_type(event.action_type, with_added=True),
        skuCode=event.sku_code,
        poNumber=event.po_number,
        brand=event.brand,
        site=event.site,
        week=str(event.week),
        count=event.count_by_sku,
        isLastPo=event.is_last_po,
    )
    _push_notification(Notification(event=WCC_CHANGED_EVENT, data=data))


@EVENT_BUS.subscribe(ReplenishmentCommentEvent)
def replenishment_comments_handler(event: ReplenishmentCommentEvent) -> None:
    market = context.get_request_context().market
    data = ReplenishmentNotificationEvent(
        type="replenishmentComment",
        action=_map_action_type(event.action_type, with_added=False),
        market=market,
        region=event.region,
        skuCode=event.sku_code,
        text=event.text,
    )
    _push_notification(Notification(event=REPLENISHMENT_COMMENT_CHANGED_EVENT, data=data))


@EVENT_BUS.subscribe(NetworkDepletionCommentEvent)
def network_depletion_comments_handler(event: NetworkDepletionCommentEvent) -> None:
    market = context.get_request_context().market
    data = NetworkDepletionNotificationEvent(
        type="networkDepletionComment",
        action=_map_action_type(event.action_type, with_added=False),
        market=market,
        week=str(event.week),
    )
    _push_notification(Notification(event=NETWORK_DEPLETION_COMMENT_CHANGED_EVENT, data=data))
