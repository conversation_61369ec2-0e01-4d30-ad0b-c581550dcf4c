import dataclasses
import logging
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from decimal import Decimal
from enum import StrEnum

from procurement.auth.permissions import Permissions
from procurement.client.googlesheets import googlesheet_utils
from procurement.constants.hellofresh_constant import BRAND_FJ, IngredientCategory, SkuStatus, UnitOfMeasure
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.api_errors import InvalidPermission
from procurement.core.exceptions.validation_errors import BatchValidationException
from procurement.core.request_utils import context, warnings
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE, SKU_NAME, UNITS
from procurement.data.dto.inventory.discarding import Discard
from procurement.data.dto.sku import SkuCodeNameStatusCategory, SkuMeta
from procurement.data.googlesheet_model.imt_data_dump import DiscardData3PL, DiscardRow
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.discard import DiscardModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.datasync.framework import warnings as datasync_warnings
from procurement.managers.imt import hybrid_needs as hn_service
from procurement.managers.imt.discards import DiscardItem, DiscardsContext
from procurement.managers.sku import culinary_sku
from procurement.repository.inventory import discarding as discarding_repo
from procurement.repository.ordering import culinary_sku as culinary_sku_repo

logger = logging.getLogger(__name__)

BrandSiteSkuCodeDate = tuple[str, str, str, date]

_WARNINGS_KEY = "gridFormWarnings"

FJ_DISCARD_RATE_THRESHOLD_BY_CATEGORY_MAPPING = {
    IngredientCategory.PROTEIN: Decimal("0.01"),
}
DEFAULT_FJ_DISCARD_RATE_THRESHOLD = Decimal("0.08")
DEFAULT_DISCARD_RATE_THRESHOLD = Decimal("0.04")


@dataclasses.dataclass
class DiscardInput:
    brand: str
    site: str
    discard_time: datetime
    sku_name: str
    num_units: int
    comment: str
    ui_row_id: int


@dataclasses.dataclass
class DiscardData:
    discard_id: int
    user: str
    site: str
    sku_name: str
    category: str
    discarded_time: datetime
    quantity: int
    week: str
    brand: str
    timestamp: datetime
    comment: str
    discard_rate: Decimal | None
    unit_of_measure: UnitOfMeasure
    is_editable: bool = True


class DiscardWarning(StrEnum):
    DISCARD_RATE = "DISCARD_RATE"
    RETIRED_SKU = "RETIRED_SKU"


class DiscardValidateInput:
    def __init__(
        self,
        brands_to_dc: dict[str, dict[str, DcConfig]],
        discard_quantities: dict[BrandSiteSkuCodeDate, int],
        production_needs: dict[BrandSiteSkuCodeDate, UNITS],
        aggregated_inputs: dict[BrandSiteSkuCodeDate, int],
    ):
        self.brands_to_dc = brands_to_dc
        self.discard_quantities = discard_quantities
        self.production_needs = production_needs
        self.aggregated_inputs = aggregated_inputs

    def validate_item(self, discard: DiscardInput) -> BatchValidationException | None:
        row_errors = {}
        if discard.brand not in self.brands_to_dc:
            row_errors["brand"] = "Invalid Brand value"
        elif discard.site not in self.brands_to_dc.get(discard.brand, {}):
            row_errors["site"] = "Invalid Site value"

        sku_meta = _get_sku_meta(brand=discard.brand)

        if discard.num_units <= 0:
            row_errors["numUnits"] = "Quantity must be positive number"
        if discard.sku_name not in sku_meta.keys():
            row_errors["skuName"] = "SKU Name does not exist"
        if discard.comment and len(discard.comment) > 4000:
            row_errors["comment"] = "Comment should be less then 4000 symbols long"

        if row_errors:
            return BatchValidationException.build_error(discard.ui_row_id, row_errors)
        return None

    def check_warnings(self, items: list[DiscardInput]) -> None:
        discard_rate_warnings = defaultdict(list)
        retired_sku_warnings_rows = []
        for item in items:
            sku_meta = _get_sku_meta(brand=item.brand)
            discard_rate_threshold = self._get_discard_rate_threshold(item, sku_meta)
            if self._is_discard_rate_invalid(item, sku_meta, discard_rate_threshold):
                discard_rate_warnings[discard_rate_threshold].append(item.ui_row_id)
            if self._is_sku_retired(item, sku_meta):
                retired_sku_warnings_rows.append(item.ui_row_id)

        for threshold, rows in discard_rate_warnings.items():
            percentage = int(threshold * Decimal("100"))
            warnings.add_message(
                warning={
                    "type": DiscardWarning.DISCARD_RATE,
                    "message": f"The running total discard rate for this SKU is greater than {percentage}%. "
                    "Please notify your supervisor and create a ticket for Procurement "
                    "if you have not already",
                    "rows": rows,
                },
                key=_WARNINGS_KEY,
            )
        if retired_sku_warnings_rows:
            warnings.add_message(
                warning={
                    "type": DiscardWarning.RETIRED_SKU,
                    "message": "This SKU is retired",
                    "rows": retired_sku_warnings_rows,
                },
                key=_WARNINGS_KEY,
            )

    @staticmethod
    def _get_discard_rate_threshold(discard: DiscardInput, sku_meta: dict[str, SkuCodeNameStatusCategory]) -> Decimal:
        if sku := sku_meta.get(discard.sku_name):
            if discard.brand == BRAND_FJ:
                return FJ_DISCARD_RATE_THRESHOLD_BY_CATEGORY_MAPPING.get(
                    sku.category, DEFAULT_FJ_DISCARD_RATE_THRESHOLD
                )
        return DEFAULT_DISCARD_RATE_THRESHOLD

    @staticmethod
    def _is_sku_retired(discard: DiscardInput, sku_meta: dict[str, SkuCodeNameStatusCategory]) -> bool:
        if sku := sku_meta.get(discard.sku_name):
            return sku.sku_status in [SkuStatus.ARCHIVED, SkuStatus.INACTIVE]
        return False

    def _is_discard_rate_invalid(
        self, discard: DiscardInput, sku_meta: dict[str, SkuCodeNameStatusCategory], discard_rate_threshold: Decimal
    ) -> bool:
        discard_date = discard.discard_time.date()
        sku = sku_meta.get(discard.sku_name)
        brand_site_sku_date = (discard.brand, discard.site, sku.sku_code if sku else None, discard_date)
        production_need = self.production_needs.get(brand_site_sku_date, 0)
        input_quantity = self._get_input_quantity(brand_site_sku_date)
        discard_rate = _calculate_discard_rate(input_quantity, production_need)
        return discard_rate and discard_rate > discard_rate_threshold

    def _get_input_quantity(self, brand_site_sku_code_date: BrandSiteSkuCodeDate) -> int:
        input_quantity = self.aggregated_inputs.get(brand_site_sku_code_date, 0)
        discard_quantity = self.discard_quantities.get(brand_site_sku_code_date, 0)
        return discard_quantity + input_quantity


@request_cache
def _get_sku_meta(brand: str, search_key: str = None) -> dict[SKU_NAME, SkuCodeNameStatusCategory]:
    return {sku.sku_name: sku for sku in culinary_sku_repo.get_sku_codes_statuses_categories_by_name(brand, search_key)}


def _calculate_aggregated(items: list[DiscardInput]) -> dict[BrandSiteSkuCodeDate, int]:
    aggregated_inputs = defaultdict(int)
    for item in items:
        sku_meta = _get_sku_meta(brand=item.brand)
        sku = sku_meta.get(item.sku_name)
        if not sku:
            continue
        aggregated_inputs[(item.brand, item.site, sku.sku_code, item.discard_time.date())] += item.num_units
    return aggregated_inputs


def validate_data(week: ScmWeek, items: list[DiscardInput]) -> None:
    available_brands = brand_admin.get_brand_ids(week)
    brands_sites = frozenset((item.brand, item.site) for item in items if item.brand in available_brands)

    aggregated_inputs = _calculate_aggregated(items)
    brands_to_dc = {}
    for brand in brand_admin.get_brand_ids(week):
        enabled_sites = dc_admin.get_enabled_sites(week, brand)
        brands_to_dc[brand] = {
            site_name: dc
            for site_name, dc in enabled_sites.items()
            if dc.source != DataSource.HIGHJUMP or not killswitch.disable_discard_form_for_hj
        }

    v_input = DiscardValidateInput(
        brands_to_dc=brands_to_dc,
        discard_quantities=_get_discard_quantities(discarding_repo.get_discards(week)),
        production_needs=_get_hybrid_needs_by_week(week, brands_sites),
        aggregated_inputs=aggregated_inputs,
    )
    errors = []
    for item in items:
        error = v_input.validate_item(item)
        if error:
            errors.append(error)
    if errors:
        raise BatchValidationException("Invalid batch", errors)
    v_input.check_warnings(items)


def _calculate_discard_rate(discard_quantity: int, production_need: UNITS | None) -> Decimal | None:
    return round((Decimal(discard_quantity) / production_need), 4) if production_need else None


def _get_hybrid_needs_by_week(
    week: ScmWeek, brands_sites: frozenset, sku_codes: frozenset = None, check_data_loaded: bool = True
) -> dict[BrandSiteSkuCodeDate, UNITS]:
    hybrid_needs = defaultdict(int)
    for brand, site in brands_sites:
        results = hn_service.get_hn_data(brand, site, week, sku_codes, check_data_loaded)
        for result in results:
            hybrid_needs[(brand, site, result.sku_code, result.day)] += result.value
    return hybrid_needs


def _get_discard_quantities(discard_data: Iterable[Discard]) -> dict[BrandSiteSkuCodeDate, int]:
    discard_quantities = defaultdict(int)
    for item in discard_data:
        discard_quantities[(item.brand, item.dc, item.sku, item.discarded_datetime.date())] += item.quantity
    return discard_quantities


def _build_discard_data(
    discard_quantities: dict[BrandSiteSkuCodeDate, int],
    production_needs: dict[BrandSiteSkuCodeDate, UNITS],
    sku_meta_by_sku: dict[SKU_CODE, SkuMeta],
    item: Discard,
) -> DiscardData:
    brand_dc_sku_date = (item.brand, item.dc, item.sku, item.discarded_datetime.date())
    discard_rate = _calculate_discard_rate(
        discard_quantities[brand_dc_sku_date], production_needs.get(brand_dc_sku_date)
    )
    sku_meta_item = sku_meta_by_sku.get(item.sku)
    return DiscardData(
        discard_id=item.id,
        user=item.user,
        site=item.dc,
        sku_name=sku_meta_item.sku_name if sku_meta_item else None,
        category=sku_meta_item.category if sku_meta_item else None,
        discarded_time=item.discarded_datetime,
        quantity=item.quantity,
        week=item.week,
        brand=item.brand,
        timestamp=item.timestamp,
        comment=item.comment,
        discard_rate=discard_rate,
        unit_of_measure=sku_meta_item.unit if sku_meta_item else None,
        is_editable=item.source != DataSource.HIGHJUMP.name,
    )


def _build_discard_from_hj(
    hj_discard: DiscardItem,
    week: ScmWeek,
    brand: str,
) -> Discard:
    return Discard(
        id=-1,
        user=None,
        timestamp=hj_discard.discard_date,
        dc=hj_discard.site,
        sku=hj_discard.sku_code,
        discarded_datetime=hj_discard.discard_date,
        quantity=hj_discard.quantity,
        quality_instructions=None,
        reason=None,
        source=DataSource.HIGHJUMP.name,
        week=str(week),
        brand=brand,
        comment=None,
        updated_by=None,
        deleted_by=None,
        deleted_ts=None,
        market=context.get_request_context().market,
    )


def _get_hj_discard_data(brand: str, site: str, week: ScmWeek, sku_code: str | None) -> list[Discard]:
    discards_context = DiscardsContext(brand=brand, site=site, week=week, sku_code=sku_code)

    return [
        _build_discard_from_hj(wip_hj_discard, week=week, brand=brand)
        for wip_hj_discard in discards_context.wip_hj_discards
    ]


def get_discards_by_week(week: ScmWeek) -> list[DiscardData]:
    discard_data = discarding_repo.get_discards(week)
    if killswitch.display_hj_discards_in_discard_form:
        hj_brands_sites = (dc for dc in dc_admin.get_all_market_dcs(week=week) if dc.source == DataSource.HIGHJUMP)
        hj_discards = []
        for dc in hj_brands_sites:
            hj_discards.extend(_get_hj_discard_data(dc.brand, dc.sheet_name, week, sku_code=None))
        discard_data.extend(hj_discards)

    brands_sites = frozenset((item.brand, item.dc) for item in discard_data)
    available_sites = context.get_request_context().user_permissions.available_sites
    brands_sites = frozenset((brand, site) for brand, site in brands_sites if site in available_sites.get(brand, []))
    discard_data = [item for item in discard_data if (item.brand, item.dc) in brands_sites]
    production_needs = _get_hybrid_needs_by_week(
        week, brands_sites, sku_codes=frozenset(item.sku for item in discard_data), check_data_loaded=False
    )

    return _get_discard_with_rate(discard_data, production_needs)


def get_discards_by_change_date(change_date: date) -> list[DiscardData]:
    discard_data = discarding_repo.get_discards_by_change_date(change_date=change_date)
    production_needs = _get_hn_by_discard_data(discard_data)
    data = _get_discard_with_rate(discard_data, production_needs)
    return data


def _get_hn_by_discard_data(discards: Iterable[Discard]) -> dict[BrandSiteSkuCodeDate, UNITS]:
    brand_sites_by_week = defaultdict(set)
    for item in discards:
        brand_sites_by_week[ScmWeek.from_str(item.week)].add((item.brand, item.dc))

    hybrid_needs = {}
    for week, brand_sites in brand_sites_by_week.items():
        hybrid_needs.update(_get_hybrid_needs_by_week(week, frozenset(brand_sites), check_data_loaded=False))
    return hybrid_needs


def _get_discard_with_rate(
    discard_data: Iterable[Discard], production_needs: dict[BrandSiteSkuCodeDate, UNITS]
) -> list[DiscardData]:
    discard_quantities = _get_discard_quantities(discard_data)

    sku_codes = frozenset(item.sku for item in discard_data)

    sku_meta_by_sku = culinary_sku.get_sku_meta_by_code(site=None, sku_codes=sku_codes)

    return [_build_discard_data(discard_quantities, production_needs, sku_meta_by_sku, item) for item in discard_data]


def get_discard_data(site: str, week: ScmWeek, brand: str, sku_code: str | None = None) -> list[Discard]:
    return discarding_repo.get_discards(week, brand, site, sku_code=sku_code)


def add_discards(data: list[DiscardInput], week: ScmWeek) -> None:
    validate_data(week, data)
    market = context.get_request_context().market

    discard_data = []
    for item in data:
        sku_meta = _get_sku_meta(brand=item.brand)
        discard_data.append(
            {
                DiscardModel.user: context.get_request_context().user_info.email,
                DiscardModel.timestamp: datetime.now(),
                DiscardModel.dc: item.site,
                DiscardModel.sku: sku_meta[item.sku_name].sku_code,
                DiscardModel.discarded_datetime: item.discard_time,
                DiscardModel.quantity: item.num_units,
                DiscardModel.source: DataSource.APP.name,
                DiscardModel.week: str(week),
                DiscardModel.brand: item.brand,
                DiscardModel.comment: item.comment,
                DiscardModel.market: market,
            }
        )

    discarding_repo.add_discard_data(discard_data)


def import_data_to_db(week: ScmWeek, brand: str):
    """
    Imports all brands data from the :param brand: document (practically from the HF doc)
    """
    market = context.get_request_context().market
    logger.info("Discards sync: starting for %s week", week)
    available_sites = [
        site
        for b in brand_admin.get_brands(week)
        for site in dc_admin.get_enabled_sites(week, b).values()
        if site.is_3pl and site.source == DataSource.GSHEET
    ]
    valid_sku_codes = {sku.sku_code for sku in culinary_sku_repo.get_sku_codes_statuses_categories_by_name(brand)}
    for site in available_sites:
        imported_items = gsheet_import(week, site.sheet_name, brand)
        logger.debug("Discards sync: got %s items", len(imported_items))
        discard_data = []
        for item in imported_items:
            try:
                googlesheet_utils.validate_required_fields(
                    item, ["username", "sku", "date_of_discard", "time_of_discard"]
                )

                if item.sku not in valid_sku_codes:
                    raise ValueError(f"Invalid SKU code: {item.sku}, associated with {item.sku_name} SKU name")

                discarded_datetime = googlesheet_utils.parse_datetime_str_12h_or_24h(
                    item.date_of_discard, item.time_of_discard, item.am_or_pm
                )
                units_discarded = googlesheet_utils.to_int(
                    item.number_of_units_discarded, nullable=False, round_up=True, default_value=0
                )
                if not units_discarded:
                    continue

                discard_item = {
                    DiscardModel.user: item.username,
                    DiscardModel.timestamp: googlesheet_utils.parse_datetime(item.timestamp),
                    DiscardModel.dc: site.sheet_name,
                    DiscardModel.sku: item.sku,
                    DiscardModel.discarded_datetime: discarded_datetime,
                    DiscardModel.quantity: units_discarded,
                    DiscardModel.quality_instructions: "",
                    DiscardModel.reason: "",
                    DiscardModel.source: DataSource.GSHEET.name,
                    DiscardModel.week: str(week),
                    DiscardModel.brand: site.brand,
                    DiscardModel.market: market,
                }

                discard_data.append(discard_item)
            except ValueError as exc:
                datasync_warnings.notify_sheet_error(
                    item, exc, message="Error when importing discard data", dc=site.sheet_name, skipped=True
                )

        discarding_repo.delete_imported_discard_data(week, site.sheet_name)
        discarding_repo.add_discard_data(discard_data)
        logger.info(
            "Discards sync: finished import data for week %s and dc %s; read count: %s; success: %s",
            week,
            site.sheet_name,
            len(imported_items),
            len(discard_data),
        )


def gsheet_import(week: ScmWeek, dc: str, brand: str) -> list[DiscardRow]:
    data = gsheet_admin.read_gsheet(DiscardData3PL(dc), brand=brand, week=week)
    datasync_warnings.notify_records_errors(data, dc=dc, skipped=False)
    return data


def delete_discard(item_id: int) -> None:
    discard = discarding_repo.get_discard_by_id(item_id)
    available_sites = context.get_request_context().user_permissions.available_sites
    if discard.dc not in available_sites.get(discard.brand, []):
        raise InvalidPermission(Permissions.IMT_DISCARD_V1.write, discard.brand, discard.dc)
    if discard:
        discarding_repo.mark_discard_as_deleted(
            discard_id=item_id, deleted_by=context.get_request_context().user_info.email, deleted_ts=datetime.now()
        )


def update_discard_item(discard_id: int, data: dict) -> None:
    parameter_map = {
        "numberOfUnits": DiscardModel.quantity,
        "comment": DiscardModel.comment,
        "discardedDateTime": DiscardModel.discarded_datetime,
    }
    params = {parameter_map[key]: data[key] for key in data}
    if any(params.keys()):
        user_email = context.get_request_context().user_info.email
        discarding_repo.update_discard_item(discard_id, params, user_email)


def get_discard_week_by_id(item_id: int) -> ScmWeek:
    return ScmWeek.from_str(discarding_repo.get_discard_by_id(item_id).week)


def get_skus_for_autocomplete(brand: str, search_key: str) -> list[SKU_NAME]:
    return list(_get_sku_meta(brand=brand, search_key=search_key))
