import logging
from collections import defaultdict
from datetime import datetime
from decimal import Decimal

from sqlalchemy import Select

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import PO_SKU_KEY, UNITS
from procurement.data.dto.inventory.receiving import ReceivingItem
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import receiving as receiving_repo

logger = logging.getLogger(__name__)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_receiving_group_by_po_number_and_sku_code(
    weeks_dc_objects: list[tuple[ScmWeek, DcConfig]], sku_codes: set[str] = None
) -> dict[PO_SKU_KEY, list[ReceivingItem]]:
    if not weeks_dc_objects:
        return {}
    result = []
    for week, dc_object in weeks_dc_objects:
        result.extend(receiving_repo.get_received_data(week, dc_object.brand, dc_object.sheet_name, sku_codes))
    non_hj_receipt = utils.group_by(result, lambda it: (it.po_number, it.sku_code))
    return non_hj_receipt


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_receiving_by_date(
    dc_object: DcConfig, receive_date_from: datetime, receive_date_to: datetime
) -> dict[PO_SKU_KEY, list[ReceivingItem]]:
    rows = receiving_repo.get_received_by_date(dc_object, receive_date_from, receive_date_to)
    return _map_rows_by_po_sku(rows)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_sku_filtered_receiving(po_skus: Select) -> dict[PO_SKU_KEY, list[ReceivingItem]]:
    rows = receiving_repo.get_po_sku_filtered_receives(po_skus)
    return _map_rows_by_po_sku(rows)


def _map_rows_by_po_sku(rows: list[ReceivingItem]) -> dict[PO_SKU_KEY, list[ReceivingItem]]:
    non_hj_receipt = defaultdict(list)
    for row in rows:
        uid = (row.po_number, row.sku_code)
        non_hj_receipt[uid].append(row)
    non_hj_receipt.default_factory = None
    return non_hj_receipt


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_receiving_item_by_po_number_and_sku_code(po_number: str, sku_code: str) -> list[ReceivingItem]:
    return receiving_repo.get_received_by_po_sku(po_number, sku_code)


def get_received_weeks(site: str, receive_date_from: datetime, receive_date_to: datetime) -> set[ScmWeek]:
    return receiving_repo.get_received_weeks(site, receive_date_from, receive_date_to)


def get_manual_receive_row_total_qty(receipt: ReceivingItem) -> UNITS:
    def get_val_or_0(val: UNITS | None) -> UNITS:
        return val or 0

    total = Decimal()

    if receipt.case_size_one:
        total += get_val_or_0(receipt.case_count_one_total_units) * get_val_or_0(receipt.case_size_one)
        total += get_val_or_0(receipt.case_count_two_total_units) * get_val_or_0(receipt.case_size_two)
        total += get_val_or_0(receipt.case_count_three_total_units) * get_val_or_0(receipt.case_size_three)
        return total

    total += get_val_or_0(receipt.case_count_one_total_units)
    total += get_val_or_0(receipt.case_count_two_total_units)
    total += get_val_or_0(receipt.case_count_three_total_units)

    count = 1 if receipt.case_count_one_total_units else 0
    count += 1 if receipt.case_count_two_total_units else 0
    count += 1 if receipt.case_count_three_total_units else 0

    avg = round(total / count if count > 0 else 0)
    return (receipt.total_cases_received or 0) * avg
