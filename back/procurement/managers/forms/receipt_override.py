import dataclasses
import logging
from collections import defaultdict
from datetime import date, datetime
from typing import NamedTuple

from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, NOT_DELIVERED_PAST_DUE, RECEIVED_STATUSES
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ManualFormScheduleExportEvent
from procurement.core.exceptions.validation_errors import BatchValidationException
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, BRAND, PO_NUMBER, SKU_CODE, SKU_NAME, PoSku
from procurement.data.dto.inventory.grn import GrnPo
from procurement.data.dto.inventory.receipt_override import ReceiptSku
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.receipt_override import ReceiptOverrideModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import highjump
from procurement.managers.imt import po_void as po_void_service
from procurement.managers.ordering import advance_shipping_notice, service
from procurement.managers.ordering.advance_shipping_notice import AsnPoStatusCalculations
from procurement.managers.ordering.grn import GrnPurchaseOrder
from procurement.managers.ordering.service import PoExportContext
from procurement.managers.receipt import grn
from procurement.repository.inventory import receipt_override as receipt_override_repo
from procurement.repository.inventory.receipt_override import ReceiptOverrideKey

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class ReceiptOverrideInput:
    week: str
    brand: str
    dc: str
    po_number: str
    sku_code: str
    sku_name: str
    adjusted_received_volume: int
    adjusted_received_cases: int | None
    adjusted_receiving_date: date | None
    ui_row_id: int
    comment: str | None


@dataclasses.dataclass
class ReceiptOverrideData:
    entry_type: str
    brand: str
    site: str
    po_number: str
    supplier_name: str
    sku_code: str
    sku_name: str
    quantity_received: int
    adjusted_received_volume: int
    difference_receipt_vs_adj: int
    cases_received: int
    adjusted_received_cases: int
    adjusted_receiving_date: date | None
    case_difference_receipt_vs_adj: int
    submitted_by: str
    timestamp: datetime
    comment: str | None


class Receipt(NamedTuple):
    supplier_name: str
    quantity_received: int
    cases_received: int
    sku_name: str


class _OverrideKey(NamedTuple):
    bob_code: str
    po_number: str
    sku_code: str

    def __str__(self):
        return f"[{self.bob_code}, {self.po_number}, {self.sku_code}]"


@ApplicationMetrics.functional_methods()
def get_data(week: ScmWeek) -> list[ReceiptOverrideData]:
    available_sites = context.get_request_context().user_permissions.available_sites
    receipts_by_brand = {}
    result = []
    for item in receipt_override_repo.get_extended_receipt_overrides_by_week(week=week):
        if item.site not in available_sites.get(item.brand, []):
            continue

        bob_code = dc_admin.get_enabled_sites(week, item.brand)[item.site].bob_code
        if item.brand not in receipts_by_brand:
            receipts_by_brand[item.brand] = _get_receipts(week, item.brand)
        receipts = receipts_by_brand[item.brand]
        item_key = _OverrideKey(bob_code, item.po_number, item.sku_code)
        quantity_received = (receipts[item_key].quantity_received or 0) if item_key in receipts else 0
        cases_received = (receipts[item_key].cases_received or 0) if item_key in receipts else 0
        supplier = receipts[item_key].supplier_name if item_key in receipts else ""
        result.append(
            ReceiptOverrideData(
                entry_type="Receipt Override",
                brand=item.brand,
                site=item.site,
                po_number=item.po_number,
                supplier_name=supplier,
                sku_code=item.sku_code,
                sku_name=item.sku_name,
                quantity_received=quantity_received,
                adjusted_received_volume=item.qty,
                difference_receipt_vs_adj=quantity_received - item.qty,
                cases_received=cases_received,
                adjusted_received_cases=item.cases,
                case_difference_receipt_vs_adj=cases_received - (item.cases or 0),
                adjusted_receiving_date=item.receiving_date,
                submitted_by=item.user,
                timestamp=item.upd_tmst,
                comment=item.comment,
            )
        )
    return result


class OverrideValidationContext:
    def __init__(self, week: ScmWeek):
        self.week = week
        self.error_rows = defaultdict(dict)
        self.item_to_site_map = {}
        self.existing_rows = self._get_existing_rows()
        self.duplicates: dict[_OverrideKey, list] = defaultdict(list)
        self._tpl_pos: dict[BOB_CODE, dict[tuple[PO_NUMBER, SKU_CODE], SKU_NAME]] = {}
        self._received: dict[BOB_CODE, dict[tuple[PO_NUMBER, SKU_CODE], SKU_NAME]] = defaultdict(dict)
        self._fetched_brands: set[BRAND] = set()

    def _get_existing_rows(self):
        existing_rows = set()
        for item in receipt_override_repo.get_receipt_override_items_for_validation(self.week):
            sites = dc_admin.get_enabled_sites(self.week, item.brand)
            if item.site in sites:
                existing_rows.add(_OverrideKey(sites[item.site].bob_code, item.po_number, item.sku_code))
        return existing_rows

    def validate_item(self, item: ReceiptOverrideInput):
        self._validate_basic_data(item)
        if item.ui_row_id not in self.error_rows:
            self._validate_receipt_data(item)

    def _validate_basic_data(self, item: ReceiptOverrideInput):
        if item.brand not in brand_admin.get_brand_ids(self.week):
            self.error_rows[item.ui_row_id] = {"brand": "Invalid Brand value"}
            return

        dcs = dc_admin.get_enabled_sites(self.week, item.brand)
        self.item_to_site_map.update({site.bob_code: code for code, site in dcs.items()})
        if item.dc not in dcs:
            self.error_rows[item.ui_row_id]["dc"] = "Invalid Site value"
        elif not item.po_number:
            self.error_rows[item.ui_row_id]["poNumber"] = "PO# is empty"
        elif not item.sku_code:
            self.error_rows[item.ui_row_id]["skuCode"] = "Sku code is empty"

    def _validate_receipt_data(self, item: ReceiptOverrideInput):
        dc = dc_admin.get_enabled_sites(self.week, item.brand)[item.dc]
        override_key = _OverrideKey(dc.bob_code, item.po_number, item.sku_code)
        item_key = (item.po_number, item.sku_code)
        eligible_items = self._get_tpl_pos(dc) if dc.is_3pl and dc.receiving_type.is_grn else self._get_received_pos(dc)
        self.duplicates[override_key].append(item.ui_row_id)
        key = f"[{item.dc}, {item.po_number}, {item.sku_code}]"

        if item_key not in eligible_items:
            msg = f"{key} entry not found!"
            self.error_rows[item.ui_row_id] = {"dc": msg, "poNumber": msg, "skuCode": msg}

        elif item.sku_name != eligible_items[item_key]:
            self.error_rows[item.ui_row_id]["skuName"] = f"{key} with '{item.sku_name}' sku name not found!"

        if override_key in self.existing_rows:
            self.duplicates[override_key].append(item.ui_row_id)

    def validate_duplicates(self):
        for key, rows in self.duplicates.items():
            if len(rows) > 1:
                for row in rows:
                    msg = (
                        f"[{self.item_to_site_map[key.bob_code]}, {key.po_number}, {key.sku_code}]"
                        f" entry has duplicates!"
                    )
                    self.error_rows[row] = {"dc": msg, "poNumber": msg, "skuCode": msg}

    def get_errors(self):
        return [
            BatchValidationException.build_error(row, columns) for row, columns in self.error_rows.items() if columns
        ]

    def _get_received_pos(self, dc: DcConfig) -> dict[tuple[PO_NUMBER, SKU_CODE], SKU_NAME]:
        if dc.brand not in self._fetched_brands:
            for k, r in _get_receipts(self.week, dc.brand).items():
                self._received[k.bob_code][(k.po_number, k.sku_code)] = r.sku_name
        return self._received.get(dc.bob_code, {})

    def _get_tpl_pos(self, dc: DcConfig) -> dict[tuple[PO_NUMBER, SKU_CODE], SKU_NAME]:
        if (fetched := self._tpl_pos.get(dc.bob_code)) is not None:
            return fetched
        po_grn = grn.get_grn_receipts_pos(self.week, dc)
        res = {
            (po, sku.sku_code): sku.sku_name
            for po, skus in get_tpl_pos_eligible_for_override(self.week, dc, po_grn).items()
            for sku in skus
        }
        self._tpl_pos[dc.bob_code] = res
        return res


@ApplicationMetrics.functional_methods()
def validate_data(week: ScmWeek, items: list[ReceiptOverrideInput]):
    """
        Validates that:
        * you try to override exists entries
        * don't have duplicates it input list
        * don't have duplicates at previous entered entries.

    :return: list of errors if any. Several columns may present.
    [
        {
            row: <row index>,
            msg: "error msg",
            columns: [<column name>]
        },
        ...
    ]

    """
    validation_context = OverrideValidationContext(week)

    for item in items:
        validation_context.validate_item(item)

    validation_context.validate_duplicates()

    if errors := validation_context.get_errors():
        raise BatchValidationException("Invalid Receipt Override input", errors)


@ApplicationMetrics.functional_methods()
def add_items(week: ScmWeek, items: list[ReceiptOverrideInput]):
    validate_data(week, items)
    market = context.get_request_context().market

    now = datetime.now()

    data = [
        {
            ReceiptOverrideModel.user: context.get_request_context().user_info.email,
            ReceiptOverrideModel.dc: item.dc,
            ReceiptOverrideModel.brand: item.brand,
            ReceiptOverrideModel.week: str(week),
            ReceiptOverrideModel.source: DataSource.APP,
            ReceiptOverrideModel.po_number: item.po_number,
            ReceiptOverrideModel.sku_code: item.sku_code,
            ReceiptOverrideModel.qty: item.adjusted_received_volume,
            ReceiptOverrideModel.cases: item.adjusted_received_cases,
            ReceiptOverrideModel.receiving_date: item.adjusted_receiving_date,
            ReceiptOverrideModel.upd_tmst: now,
            ReceiptOverrideModel.market: market,
            ReceiptOverrideModel.cre_tmst: now,
            ReceiptOverrideModel.comment: item.comment,
        }
        for item in items
    ]

    receipt_override_repo.add_receipt_override_data(data)
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))


@ApplicationMetrics.functional_methods()
def edit_item(
    override_key: ReceiptOverrideKey,
    sku_code: SKU_CODE,
    qty: int | None,
    cases: int | None,
    comment: str | None,
    receiving_date: date | None,
):
    updates = {
        ReceiptOverrideModel.user: context.get_request_context().user_info.email,
        ReceiptOverrideModel.upd_tmst: datetime.now(),
    }
    if qty is not None:
        updates[ReceiptOverrideModel.qty] = qty
    if cases is not None:
        updates[ReceiptOverrideModel.cases] = cases
    if comment is not None:
        updates[ReceiptOverrideModel.comment] = comment
    if receiving_date is not None:
        updates[ReceiptOverrideModel.receiving_date] = receiving_date
    receipt_override_repo.update_receipt_override_item(override_key, sku_code, updates)
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=override_key.week))


@ApplicationMetrics.functional_methods()
def delete_item(week: ScmWeek, brand: str, dc: str, po_sku: PoSku):
    receipt_override_repo.delete_receipt_override_by_po_sku(
        context.get_request_context().user_info.email,
        ReceiptOverrideKey(week=week, brand=brand, site=dc, po_number=po_sku.po_number),
        po_sku.sku_code,
    )
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))


def _get_receipts(week: ScmWeek, brand: str) -> dict[_OverrideKey, Receipt]:
    receipts = {}
    for item in grn.get_grn_receipts(week=week, brand=brand):
        receipts[_OverrideKey(item.bob_code, item.po_number, item.sku_code)] = Receipt(
            supplier_name=item.supplier_name,
            quantity_received=item.quantity_received,
            cases_received=item.cases_received,
            sku_name=item.sku_name,
        )
    if not killswitch.hj_grn_enabled:
        for item in highjump.get_hj_receipts_for_override(scm_week=week):
            receipts[_OverrideKey(item.bob_code, item.po_number, item.sku_code)] = Receipt(
                supplier_name=item.supplier_name,
                quantity_received=item.quantity_received,
                cases_received=item.cases_received,
                sku_name=item.sku_name,
            )
    return receipts


def get_pos(week: ScmWeek, brand: str, site: str) -> list[PO_NUMBER]:
    dc = dc_admin.get_site(brand=brand, site=site, week=week)
    if not dc:
        return []
    po_grn = grn.get_grn_receipts_pos(week, dc)
    if dc.is_3pl and dc.receiving_type.is_grn:
        acceptance_po_sku = get_tpl_pos_eligible_for_override(week, dc, po_grn)
        return list(acceptance_po_sku.keys())
    hj_pos = [] if killswitch.hj_grn_enabled else highjump.get_hj_receipts_pos(week, dc)
    return list(set(hj_pos).union(it.po_number for it in po_grn))


def get_skus(week: ScmWeek, brand: str, site: str, po_number: str) -> list[ReceiptSku]:
    dc = dc_admin.get_site(brand=brand, site=site, week=week)
    if not dc:
        return []
    receipt_override_skus = receipt_override_repo.get_receipt_override_skus(
        ReceiptOverrideKey(week=week, brand=brand, site=site, po_number=po_number)
    )
    po_grn = grn.get_grn_skus_by_po(week, po_number, dc, receipt_override_skus)
    if dc.is_3pl and dc.receiving_type.is_grn:
        return list(get_tpl_pos_eligible_for_override(week, dc, po_grn).get(po_number, []))
    hj_skus = (
        [] if killswitch.hj_grn_enabled else highjump.get_hj_skus_by_po(week, po_number, dc, receipt_override_skus)
    )
    return list({ReceiptSku(sku_code=it.sku_code, sku_name=it.sku_name) for it in po_grn}.union(hj_skus))


def get_tpl_pos_eligible_for_override(
    week: ScmWeek, dc: DcConfig, grn_pos: list[GrnPo]
) -> dict[PO_NUMBER, set[ReceiptSku]]:
    receipts = defaultdict(list)
    for item in grn_pos:
        receipts[(item.order_number, item.sku_code)].append(item)
    receipts.default_factory = None
    po_numbers = {item.po_number for item in grn_pos}
    po_void_map = po_void_service.get_po_filtered_po_void(dc_object=dc, po_numbers=po_numbers)
    po_asn_data = advance_shipping_notice.get_advance_shipping_notice_by_po_numbers(po_numbers)
    pos = service.get_purchase_orders(PoExportContext(weeks=(week,), site=dc.sheet_name, brand=dc.brand))
    eligible_for_override = defaultdict(set)
    accepted_statuses = {
        NOT_DELIVERED_PAST_DUE,
        *AWAITING_DELIVERY_STATUSES,
        *RECEIVED_STATUSES,
    }
    for po in pos:
        uid = (po.po_number, po.sku_code)
        po_grn_dto = GrnPurchaseOrder(
            po_number=po.po_number,
            sku_code=po.sku_code,
            quantity=po.quantity,
            delivery_time_start=po.delivery_time_start,
            supplier_code=po.supplier_code,
            grn_receipts=receipts.get(uid),
        )
        asn_calculations = AsnPoStatusCalculations(po_asn_data.get(uid), po)
        po_status = service.calculate_po_status(
            po_voids={(po_void.po_number, po_void.sku_code) for po_void in po_void_map.get(po.po_number, [])},
            purchase_order=po_grn_dto,
            po_ack_status=None,
            asn_calculations=asn_calculations,
        )
        if po_status in accepted_statuses or po_status == asn_calculations.po_status:
            eligible_for_override[po.po_number].add(ReceiptSku(po.sku_code, po.sku_name))
    eligible_for_override.default_factory = None
    return eligible_for_override
