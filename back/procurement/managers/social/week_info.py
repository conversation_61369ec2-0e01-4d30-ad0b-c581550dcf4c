from dataclasses import dataclass

from procurement.core.dates import ScmWeek
from procurement.managers.admin import brand_admin, dc_admin


@dataclass
class SiteInfo:
    is_3pl: bool
    is_fulfilment_only: bool
    is_hj: bool
    is_hj_autostore: bool
    is_grn: bool
    name: str
    full_name: str
    discard_source: str
    inventory_type: str
    receiving_type: str


@dataclass
class BrandInfo:
    current_week: ScmWeek
    consolidated: bool
    brand_name: str
    sites: list[SiteInfo]


def get_configs_for_week(week: ScmWeek) -> dict[str, BrandInfo]:
    brands_by_id = brand_admin.get_brands(week)
    result = {}
    for brand_id, brand_info in brands_by_id.items():
        sites_info = [
            SiteInfo(
                name=site.sheet_name,
                full_name=site.site_name,
                is_3pl=site.is_3pl,
                is_fulfilment_only=site.is_fulfilment_only,
                is_hj=site.receiving_type.is_high_jump,
                is_hj_autostore=site.is_hj_autostore,
                is_grn=site.receiving_type.is_in_grn,
                inventory_type=site.inventory_type,
                receiving_type=site.receiving_type,
                discard_source=site.source,
            )
            for site in dc_admin.get_enabled_sites(week, brand_id).values()
        ]
        current_week = ScmWeek.current_week(brand_info.scm_week_config)
        week_config = BrandInfo(
            current_week=current_week,
            consolidated=brand_info.consolidated,
            brand_name=brand_info.name,
            sites=sites_info,
        )
        result[brand_id] = week_config
    return result
