import logging
from collections import defaultdict
from datetime import datetime
from typing import Any

from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import MARKET_CA, MARKET_US
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import NewDCAzureRoleEvent
from procurement.core.exceptions.api_errors import UserNotExisting
from procurement.core.request_utils import context
from procurement.core.typing import MARKET, ROLE
from procurement.data.dto.procurement.user import User
from procurement.repository.inventory import buyer as buyer_repo
from procurement.repository.procurement import user as user_repo

from . import permissions as permission_service
from .models import UserInfo

logger = logging.getLogger(__name__)


def get_user_from_db(user_id) -> User | None:
    return user_repo.get_user_by_id(user_id)


def get_by_mail(mail) -> User | None:
    return user_repo.get_by_email(mail)


def update_user_roles(user: User, new_roles_full_names: list[str]):
    roles_by_market = _get_roles_by_market(new_roles_full_names)

    for market, roles in roles_by_market.items():
        new_roles_dict = {r.full_name: r.id for r in user_repo.get_roles_by_fullname(roles)}

        unknown_roles = set(new_roles_full_names) - new_roles_dict.keys()
        if unknown_roles:
            logger.warning("Unknown roles %s for user %s. These roles will not be set!", unknown_roles, user.email)

        _update_roles_for_user(user, new_roles_dict, market)


def _get_roles_by_market(roles: list[str]) -> dict[MARKET, list[ROLE]]:
    roles_by_market = defaultdict(list)
    for role in roles:
        if "_us_" in role:
            roles_by_market[MARKET_US].append(role.replace("_us_", "_"))
        elif "_ca_" in role:
            roles_by_market[MARKET_CA].append(role.replace("_ca_", "_"))
        else:
            roles_by_market[MARKET_US].append(role)
            roles_by_market[MARKET_CA].append(role)
    return roles_by_market


def _update_roles_for_user(user: User, new_roles: dict[ROLE, int], market: str) -> None:
    current_roles = user_repo.get_role_full_names_list(user.email, market)
    if not new_roles.keys() == set(current_roles):
        logger.info(
            "Updating roles for user %s. Old roles: %s, new roles: %s", user.email, current_roles, new_roles.keys()
        )

        user_repo.remove_user_roles(user.id, market)
        user_repo.add_user_role(user.id, new_roles.values(), market)


@EVENT_BUS.subscribe(NewDCAzureRoleEvent)
def add_dc_azure_role(event: NewDCAzureRoleEvent) -> None:
    if not user_repo.is_role_exist(name=event.name):
        user_repo.add_dc_role(short_name=event.name, full_name=event.name)


def get_user_info(user_id: int) -> UserInfo:
    user = get_user_from_db(user_id)
    if not user:
        raise UserNotExisting()
    user_repo.update_user_login(user.id, datetime.now())
    market = context.get_request_context().market
    is_buyer = buyer_repo.is_user_buyer(user.id)
    return UserInfo(
        user_id=user.id,
        email=user.email,
        is_buyer=is_buyer,
        name=f"{user.first_name} {user.last_name}",
        roles=user_repo.get_role_names_list(user.id, market),
        picture=user.picture,
        user_permissions=permission_service.get_user_permissions(user.email),
    )


def get_service_account_info() -> UserInfo:
    return UserInfo(
        user_id=-1,
        email="<EMAIL>",
        name="Service Account",
        user_permissions=permission_service.get_service_account_permissions(),
    )


def upsert_user(user_data: dict[ColumnElement, Any]) -> None:
    user_repo.upsert_user_by_email(user_data)
