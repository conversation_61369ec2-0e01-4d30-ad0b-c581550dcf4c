from procurement.core.request_utils import context
from procurement.data.dto.procurement.user_view_state import UserViewState
from procurement.repository.procurement import user_view_state as uvs_repo


def get_states(resource_id: str) -> list[UserViewState]:
    return uvs_repo.get_states(context.get_request_context().user_info.user_id, resource_id)


def get_state(_id: int) -> UserViewState | None:
    user_config = uvs_repo.get_state_by_id(_id)
    uvs_repo.update_state(_id)
    return user_config


def add_state(data: dict) -> int:
    return uvs_repo.add_state(
        context.get_request_context().user_info.user_id,
        resource=data["resource"],
        name=data["name"],
        state=data["state"],
    )


def update_state(_id: int, data: dict) -> None:
    uvs_repo.update_state(_id, state=data.get("state"), name=data.get("name"))


def delete_state(_id: int) -> None:
    uvs_repo.delete_state(_id)
