import dataclasses

from procurement.core.typing import BRAND, MARKET, SITE

Permission = str


@dataclasses.dataclass
class UserPermissions:
    available_sites: dict[BRAND, set[SITE]]
    permissions: set[Permission]


NO_PERMISSIONS = UserPermissions({}, set())


@dataclasses.dataclass
class UserInfo:
    user_id: int
    email: str
    name: str
    user_permissions: dict[MARKET, UserPermissions]
    is_buyer: bool = False
    roles: list[str] | None = None
    picture: str | None = None
