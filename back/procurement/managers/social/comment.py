from collections import defaultdict
from datetime import datetime
from typing import Type

from procurement.constants.hellofresh_constant import Domain
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ActionType, CommentEvent, PoCommentEvent, SkuCommentEvent, WhSkuCommentEvent
from procurement.core.exceptions.db_errors import ForbidResource
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.request_utils import context
from procurement.data.dto.procurement.comment import Comment, CommentInput, CommentResource, CommentView
from procurement.data.models.constants import CommentType
from procurement.data.models.social.comment import CommentModel
from procurement.managers.admin import dc_admin
from procurement.managers.pimt import partners as pimt_partners
from procurement.repository.procurement import comment as repository
from procurement.repository.procurement.context import CommentContext


def _validate(comment: CommentInput) -> None:
    if ScmWeek.current_week() - 2 > comment.week:
        raise ForbidResource("Comment is not editable for this week")


def validate_params(brand, domain) -> None:
    if not brand and domain == Domain.IMT:
        raise InputParamsValidationException("Parameter 'brand' must be specified for domain 'IMT'")


def _check_partner_name(comment: CommentInput) -> None:
    pimt_partners_names = [wh.code for wh in pimt_partners.get_all_partners()]

    if comment.site not in pimt_partners_names:
        raise InputParamsValidationException(
            f"Invalid Warehouse code '{comment.site}', possible values: {pimt_partners_names}"
        )


def upsert_comment(comment: CommentInput) -> int:
    _validate(comment)
    brand = None if comment.domain == Domain.PIMT else comment.brand
    if comment.domain == Domain.IMT:
        week_sites = dc_admin.get_enabled_sites(comment.week, comment.brand)
        if comment.site not in week_sites:
            raise InputParamsValidationException(
                f"Invalid IMT site ({comment.site}) for week {comment.week}, possible values: {week_sites}"
            )
    elif comment.domain == Domain.PIMT:
        if comment.resource_type in (CommentType.PO, CommentType.WH_SKU):
            _check_partner_name(comment)
        elif comment.site not in pimt_partners.get_regions():
            _check_partner_name(comment)

    upsert_comment_id = repository.upsert_comment(
        {
            CommentModel.week: comment.week.to_number_format(),
            CommentModel.site: comment.site,
            CommentModel.brand: brand,
            CommentModel.domain: comment.domain,
            CommentModel.comment: comment.comment,
            CommentModel.resource_id: comment.resource_id,
            CommentModel.updated_by: context.get_request_context().user_info.email,
            CommentModel.last_updated: datetime.now(),
            CommentModel.resource_type: comment.resource_type,
        }
    )
    event_type = _get_event_type(comment.resource_type)
    if event_type:
        EVENT_BUS.publish(
            event_type(
                domain=comment.domain,
                week=comment.week,
                brand=comment.brand,
                site=comment.site,
                item_key=comment.resource_id,
                action_type=ActionType.UPDATED,
                payload=comment.comment,
            )
        )
    return upsert_comment_id


def _build_comment_log(domain: Domain, comments: list[Comment]) -> list[Comment]:
    region_by_wh = {wh.code: wh.region for wh in pimt_partners.get_all_partners()}
    for item in comments:
        if domain == Domain.PIMT:
            if item.resource_type == CommentType.SKU:
                item.region = item.site
                item.site = None
            else:
                item.region = region_by_wh[item.site]

        if item.resource_type == CommentType.PO:
            item.commodity_group_name = None
            item.purchasing_category_name = None
        else:
            item.sku_code = item.po_number
            item.po_number = None
    return comments


def get_sku_comments_log(domain: Domain, week: ScmWeek) -> list[Comment]:
    return _build_comment_log(domain, repository.get_sku_comments_log(domain, week))


def get_po_comments_log(domain: Domain, week: ScmWeek) -> list[Comment]:
    return _build_comment_log(domain, repository.get_po_comments_log(domain, week))


def get_comments_view(
    week: ScmWeek,
    sites: list,
    brand: str | None,
    comment_resource: CommentResource,
) -> list[CommentView]:
    validate_params(brand, comment_resource.domain)
    brand = None if comment_resource.domain == Domain.PIMT else brand
    comment_context = CommentContext(
        week=week,
        brand=brand,
        sites=sites,
        domain=comment_resource.domain,
        resource_id=comment_resource.resource_id,
        resource_type=comment_resource.resource_type,
    )
    comments = repository.get_comments_view(comment_context)
    return comments


def get_preview(
    domain: Domain, weeks: tuple[ScmWeek, ...], brand: str, sites: list[str], resource_type: CommentType
) -> dict[ScmWeek, dict[str, dict[str, list[str]]]]:
    validate_params(brand, domain)
    if domain == Domain.PIMT:
        return _get_preview_for_pimt(domain, weeks, sites, resource_type)

    dcs = dc_admin.get_all_sites_with_consolidation_by_brand(brand, sites, week=min(weeks)).values()
    dcs_by_brand = defaultdict(list)
    for dc in dcs:
        dcs_by_brand[dc.brand].append(dc.sheet_name)

    comment_contexts = [
        CommentContext(weeks=weeks, brand=_brand, sites=_sites, domain=domain, resource_type=resource_type)
        for _brand, _sites in dcs_by_brand.items()
    ]
    comments = []
    res = {week: {_brand: {site: [] for site in _sites} for _brand, _sites in dcs_by_brand.items()} for week in weeks}
    for comment_context in comment_contexts:
        comments.extend(repository.get_preview(comment_context))
    for comment in comments:
        res[comment.week][comment.brand][comment.site].append(comment.resource_id)
    return res


def _get_preview_for_pimt(
    domain: Domain, weeks: tuple[ScmWeek, ...], sites: list[str], resource_type: CommentType
) -> dict[ScmWeek, dict[str, dict[str, list[str]]]]:
    if resource_type == CommentType.WH_SKU:
        if sites[0] in pimt_partners.get_regions():
            sites = [wh.code for wh in pimt_partners.get_all_partners() if wh.region in sites]
    comment_context = CommentContext(weeks=weeks, brand=None, sites=sites, domain=domain, resource_type=resource_type)
    comments = repository.get_preview(comment_context)
    res = {week: {Domain.PIMT.name: {site: [] for site in sites}} for week in weeks}

    for comment in comments:
        res[comment.week][Domain.PIMT.name][comment.site].append(comment.resource_id)
    return res


def delete_comment(week: ScmWeek, brand: str, site: str, comment_resource: CommentResource) -> int:
    validate_params(brand, comment_resource.domain)
    brand = None if comment_resource.domain == Domain.PIMT else brand
    comment_context = CommentContext(
        week=week,
        brand=brand,
        site=site,
        domain=comment_resource.domain,
        resource_id=comment_resource.resource_id,
        resource_type=comment_resource.resource_type,
    )
    comment_id = repository.delete_comment(comment_context)
    event_type = _get_event_type(comment_resource.resource_type)
    if event_type:
        EVENT_BUS.publish(
            event_type(
                domain=comment_resource.domain,
                week=week,
                brand=brand,
                site=site,
                item_key=comment_resource.resource_id,
                action_type=ActionType.DELETED,
            )
        )
    return comment_id


def _get_event_type(resource_type: CommentType) -> Type[CommentEvent] | None:
    return (
        # fmt: off
        SkuCommentEvent if resource_type == CommentType.SKU else
        PoCommentEvent if resource_type == CommentType.PO else
        WhSkuCommentEvent if resource_type == CommentType.WH_SKU else
        None
        # fmt: on
    )
