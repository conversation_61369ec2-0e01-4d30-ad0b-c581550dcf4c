from collections import defaultdict

from procurement.auth.permissions import Permissions
from procurement.core.typing import BRAND, MARKET, SITE
from procurement.managers.admin import dc_admin
from procurement.repository.procurement import user as user_repo

from .models import UserPermissions


def get_user_permissions(user_id: int) -> dict[MARKET, UserPermissions]:
    return {m.code: _calculate_user_permissions(user_id, m.code) for m in dc_admin.get_markets(user_id=user_id)}


def get_service_account_permissions() -> dict[MARKET, UserPermissions]:
    all_sites = {b: set(bs) for b, bs in dc_admin.get_all_sites().items()}
    permissions = UserPermissions(
        available_sites=all_sites,
        permissions=Permissions.get_all_permissions(),
    )
    return {m.code: permissions for m in dc_admin.get_markets(ignore_permissions=True)}


def _calculate_user_permissions(user_id: int, market: str) -> UserPermissions:
    roles = set(user_repo.get_role_full_names_list(user_id, market))

    return UserPermissions(
        available_sites=_get_available_sites(roles),
        permissions=set(user_repo.get_user_permissions(user_id, market)),
    )


def _get_available_sites(user_roles: set[str]) -> dict[BRAND, set[SITE]]:
    dc_user_sites = defaultdict(set)
    available_sites = defaultdict(set)
    all_sites = (s for bs in dc_admin.get_all_sites().values() for s in bs.values())
    for site in all_sites:
        if site.dc_azure_role in user_roles:
            dc_user_sites[site.brand].add(site.id)
        else:
            available_sites[site.brand].add(site.id)
    return dc_user_sites or available_sites
