import logging
from collections import defaultdict
from collections.abc import Collection, Iterable
from datetime import date, datetime, timedelta
from decimal import Decimal
from enum import Enum, StrEnum

import sqlalchemy as sqla
from sqlalchemy import Select

from procurement.constants.hellofresh_constant import (
    MARKET_CA,
    HjWipConsumptionType,
    IngredientCategory,
    InventoryInputType,
    ReceiveInputType,
)
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BRAND, PO_NUMBER, PO_SKU_KEY, SITE, SKU_CODE
from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.dto.highjump.receipts import HjReceiptItem
from procurement.data.dto.highjump.snapshots import WeeklyPalletSnapshot
from procurement.data.dto.inventory.inventory import Inventory
from procurement.data.dto.inventory.receipt_override import ReceiptData, ReceiptSku
from procurement.data.models.highjump.highjump import DiscardType, HjDiscardModel, HJReceiptsModel, HjWipModel
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.grn import LegacyHjGoodsReceiptNoteModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.datasync.framework import warnings
from procurement.repository import highjump as hj_repo
from procurement.repository.highjump.pallet_snapshot import (
    HjPackagingSnapshotKey,
    HjPalletSnapshotAggregatedData,
    HjPalletSnapshotData,
)
from procurement.repository.highjump.receipts import HjReceiptContext, HjReceiptData
from procurement.repository.inventory import inventory
from procurement.services.database import app_db

logger = logging.getLogger(__name__)
HJReceipts = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else HJReceiptsModel


@ApplicationMetrics.functional_methods()
def get_sku_pallet_quantity(brand, dc, week: ScmWeek) -> Iterable[WeeklyPalletSnapshot]:
    wh_id = get_wh_id_by_site(week, brand, dc)
    if not wh_id:
        return []
    result = hj_repo.get_sku_pallet_quantity(week=week, wh_ids=[wh_id])
    return result


@ApplicationMetrics.functional_methods()
def get_hj_discards(
    week: ScmWeek, brand: str, site: str, sku_code: str | None = None, tran_type: DiscardType | None = None
) -> list[HjDiscard]:
    wh_id = get_wh_id_by_site(week, brand, site)
    if not wh_id:
        return []
    week_config = brand_admin.get_week_config(brand)
    return hj_repo.get_hj_discards(
        wh_id,
        # [GD-4552] discards from Wednesdays are ment
        # to be on previous week second Wednesday
        week.get_first_day(week_config) + timedelta(days=1),
        week.get_last_day(week_config) + timedelta(days=2),
        sku_code=sku_code,
        tran_type=tran_type,
    )


def get_hj_discards_by_tran_type(
    week: ScmWeek, brand: str, site: str, sku_code: str, tran_type: DiscardType | None = None
) -> dict[DiscardType, list[HjDiscard]]:
    return utils.group_by(
        get_hj_discards(week, brand, site, sku_code=sku_code, tran_type=tran_type),
        lambda item: item.tran_type,
    )


def get_hj_discard_by_tran_type_sku_date(
    week: ScmWeek, brand: str, site: str
) -> dict[DiscardType, dict[SKU_CODE, dict[date, list[HjDiscard]]]]:
    hj_discards_by_type = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
    for item in get_hj_discards(week, brand, site):
        hj_discards_by_type[item.tran_type][item.sku_code][item.discard_date.date()].append(item)
    return hj_discards_by_type


@ApplicationMetrics.functional_methods()
def get_receipts_group_by_po_number_and_sku_code(
    weeks_dc_objects: list[tuple[ScmWeek, DcConfig]],
    selected_sku_codes: set[SKU_CODE] = None,
) -> dict[PO_SKU_KEY, list[HjReceiptItem]]:
    if not weeks_dc_objects:
        return {}

    hj_receipts = defaultdict(list)
    for week, dc_object in weeks_dc_objects:
        for receipt in _build_base_receipts_query(week, dc_object, selected_sku_codes):
            combination_id = (receipt.po_number, receipt.sku_code)
            hj_receipts[combination_id].append(receipt)

    hj_receipts.default_factory = None
    return hj_receipts


def _build_base_receipts_query(week: ScmWeek, dc_object: DcConfig, selected_sku_codes) -> list[HjReceiptItem]:
    hj_name = dc_object.high_jump_name
    if not hj_name:
        return []

    return hj_repo.get_receipts(
        receipt_context=HjReceiptContext(scm_week=week, wh_ids={hj_name}, sku_codes=selected_sku_codes)
    )


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_hj_receipt_by_receive_date(
    dc_object: DcConfig,
    date_from: datetime,
    date_to: datetime,
    sku_codes: frozenset[SKU_CODE] = None,
    weeks: list[ScmWeek] = None,
) -> dict[PO_SKU_KEY, list[HjReceiptItem]]:
    hj_receipts = defaultdict(list)
    for row in hj_repo.get_received_by_receive_date(
        dc_object.high_jump_name, date_from, date_to, sku_codes=sku_codes, weeks=weeks
    ):
        hj_receipts[(row.po_number, row.sku_code)].append(row)
    hj_receipts.default_factory = None
    return hj_receipts


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_sku_filtered_hj_receipt(po_sku_filtering_query: Select) -> dict[PO_SKU_KEY, list[HjReceiptItem]]:
    hj_receipts = defaultdict(list)
    for row in hj_repo.get_po_sku_filtered_receives(po_sku_filtering_query):
        uid = (row.po_number, row.sku_code)
        hj_receipts[uid].append(row)
    hj_receipts.default_factory = None
    return hj_receipts


def get_hj_receipts_for_override(scm_week: ScmWeek) -> list[ReceiptData]:
    return hj_repo.get_receipts_for_override(week=scm_week)


def get_hj_receipts_by_po_number_and_sku_code(po_number: PO_NUMBER, sku_code: SKU_CODE) -> list[HjReceiptItem]:
    return hj_repo.get_receipts(receipt_context=HjReceiptContext(po_number=po_number, sku_codes={sku_code}))


def update_pallet_snapshot(brand: str) -> None:
    week_config = brand_admin.get_week_config(brand)
    snapshot_week = ScmWeek.current_week(week_config)
    wh_ids = _get_wh_ids_by_brand(snapshot_week, brand, HjType.INVENTORY)
    if not _can_proceed_with_ids(wh_ids):
        return
    items = hj_repo.get_hj_pallet_snapshot(wh_ids=wh_ids)
    # TODO: potential TZ issue, each DC should be written with its own time
    now = datetime.now()
    inventory.update_hj_inventory_snapshot(brand=brand, data=items, snapshot_timestamp=now)
    merged_pallet_snapshots = _merge_pallet_snapshots(items)
    weeks_to_update = (
        [snapshot_week, snapshot_week - 1]
        if snapshot_week.get_first_day(week_config) == now.date()
        else [snapshot_week]
    )

    for week in weeks_to_update:
        hj_repo.delete_hj_pallet_snapshot_by_week_wh_ids(week, wh_ids=wh_ids.keys())
        hj_repo.hj_pallet_snapshot_insert_many(merged_pallet_snapshots, week)


def update_packaging_pallet_snapshot(dcs: list[DcConfig]) -> None:
    wh_ids = [dc.high_jump_name for dc in dcs]
    items = hj_repo.get_hj_pallet_snapshot(wh_ids=wh_ids, category=IngredientCategory.PACKAGING)
    today = date.today()
    hj_repo.delete_packaging_snapshot(snapshot_date=today, wh_ids=wh_ids)
    hj_repo.insert_packaging_snapshot(snapshot=_merge_packaging_snapshot(items), snapshot_date=today)


def get_dcs_for_hj_inventory_sync() -> list[DcConfig]:
    dcs = []
    for brand in brand_admin.get_brands(week=ScmWeek.current_week()).values():
        snapshot_week = ScmWeek.current_week(brand.scm_week_config)
        dcs.extend(_get_wh_ids_by_brand(snapshot_week, brand.code, HjType.INVENTORY).values())
    _can_proceed_with_ids(dcs)
    return dcs


def get_packaging_snapshots(dc_object: DcConfig, date_from: date, date_to: date) -> dict[SKU_CODE, dict[date, int]]:
    if dc_object.receiving_type.is_manual:
        return {}
    res = defaultdict(dict)
    rows = hj_repo.get_packaging_snapshots(wh_id=dc_object.high_jump_name, date_from=date_from, date_to=date_to)
    for row in rows:
        res[row.sku_code][row.snapshot_date] = row.pallet_quantity
    res.default_factory = None
    return res


def get_latest_packaging_snapshot_date() -> date | None:
    return hj_repo.get_latest_packaging_snapshot_date()


def _merge_pallet_snapshots(snapshots: Iterable[HjPalletSnapshotData]) -> list[HjPalletSnapshotAggregatedData]:
    res = defaultdict(list)
    for snapshot in snapshots:
        key = (snapshot.sku_code, snapshot.wh_id, snapshot.expiration_date)
        if snapshot.sku_code is None or snapshot.wh_id is None:
            continue
        res[key].append(snapshot)
    return [
        HjPalletSnapshotAggregatedData(
            sku_code=sku_code,
            wh_id=wh,
            pallet_quantity=sum(s.pallet_quantity or 0 for s in snapshot),
            expiration_date=expiration_date,
            unique_license_plate_count=len({s.license_plate for s in snapshot}),
        )
        for (sku_code, wh, expiration_date), snapshot in res.items()
    ]


def _merge_packaging_snapshot(snapshots: Iterable[HjPalletSnapshotData]) -> dict[HjPackagingSnapshotKey, int]:
    res = defaultdict(int)
    for snapshot in snapshots:
        key = HjPackagingSnapshotKey(snapshot.sku_code, snapshot.wh_id)
        if None in key:
            continue
        res[key] += snapshot.pallet_quantity or 0
    return res


def update_inv_snapshot(dc_dto: DcConfig):
    dc_date = dc_dto.site_time_now().date()

    items = hj_repo.get_hj_pallet_snapshot([dc_dto.high_jump_name])
    merged_pallet_snapshots = _merge_pallet_snapshots(items)
    hj_repo.delete_hj_inv_snapshot(dc_dto.high_jump_name, dc_date)
    hj_repo.hj_inv_snapshot_insert_many(merged_pallet_snapshots, dc_date)


def delete_old_inv_snapshots() -> None:
    hj_repo.delete_old_inv_snapshots()


def update_hj_discard(brand: str) -> None:
    week_config = brand_admin.get_week_config(brand)
    wh_ids = _get_wh_ids_by_brand(ScmWeek.current_week(week_config), brand, HjType.DISCARD)
    if not _can_proceed_with_ids(wh_ids):
        return
    items = hj_repo.get_hj_query_discard(wh_ids=wh_ids.keys())
    min_end_date = min(
        (item[HjDiscardModel.discard_date] for item in items if item[HjDiscardModel.discard_date]),
        default=datetime.max,
    )
    hj_repo.delete_hj_discard(min_end_date, wh_ids=wh_ids.keys())
    hj_repo.hj_discard_insert_many(items)


def get_latest_inv_snapshot_day(dc_object: DcConfig) -> date | None:
    return hj_repo.get_latest_inv_snapshot_day(dc_object.high_jump_name)


def get_bod_snapshots(
    dc_object: DcConfig, date_from: date, date_to: date, sku_codes: set[SKU_CODE] = None
) -> list[Inventory]:
    return hj_repo.get_inv_snapshots(dc_object.high_jump_name, date_from, date_to, sku_codes)


def get_current_hj_pallets_inventory(dc_object: DcConfig, week: ScmWeek) -> list[Inventory]:
    return hj_repo.get_current_hj_pallets_inventory(wh_id=dc_object.high_jump_name, week=week)


def get_hj_units_on_floor(dc_object: DcConfig, sku_codes: set[str] = None) -> dict[SKU_CODE, int]:
    units_by_sku = defaultdict(int)
    for record in hj_repo.get_units_on_floor(dc_object, sku_codes):
        units_by_sku[record.sku_code] = record.quantity
    units_by_sku.default_factory = None
    return units_by_sku


def update_receipts(brand: str) -> None:
    week_config = brand_admin.get_week_config(brand)
    wh_ids = _get_wh_ids_by_brand(ScmWeek.current_week(week_config), brand, HjType.RECEIVING)
    if not _can_proceed_with_ids(wh_ids):
        return
    items = hj_repo.get_hj_receipt(wh_ids=wh_ids.keys())

    po_sku = [(item.po_number, item.sku_code) for item in items]
    if not items:
        raise ValueError("No data in HJ receipts view! Skipping receipts update step...")

    deleted = hj_repo.delete_non_grn_hj_receipts_by_po_sku(po_sku)
    hj_repo.insert_non_grn_hj_receipts(items)

    # TODO: remove this workaround once CA HJ sites are in GRN
    if context.get_request_context().market == MARKET_CA:
        _duplicate_canada_receipts_to_grn_table(items)

    if len(items) < 10 or len(items) * 3 < deleted:
        warnings.notify_warning(f"Low amount of HJ records. Old receipts count: {deleted}, new count: {len(items)}")


def _duplicate_canada_receipts_to_grn_table(records: list[HjReceiptData]) -> None:
    if not records:
        return
    unique_records = {}
    for rec in records:
        key = (rec.po_number, rec.sku_code, rec.status)
        if existing := unique_records.get(key):
            existing.receipt_time_est = utils.max_of_two(rec.receipt_time_est, existing.receipt_time_est)
            existing.quantity_received += rec.quantity_received
            existing.cases_received += rec.quantity_received
        else:
            unique_records[key] = rec
    updates = [
        {
            LegacyHjGoodsReceiptNoteModel.wh_id: r.wh_id,
            LegacyHjGoodsReceiptNoteModel.bob_code: r.wh_id,
            LegacyHjGoodsReceiptNoteModel.supplier_name: r.supplier_name,
            LegacyHjGoodsReceiptNoteModel.po_number: r.po_number,
            LegacyHjGoodsReceiptNoteModel.cases_received: r.cases_received,
            LegacyHjGoodsReceiptNoteModel.quantity_received: r.quantity_received,
            LegacyHjGoodsReceiptNoteModel.sku_code: r.sku_code,
            LegacyHjGoodsReceiptNoteModel.sku_name: r.sku_name,
            LegacyHjGoodsReceiptNoteModel.scm_week_raw: r.scm_week_raw,
            LegacyHjGoodsReceiptNoteModel.status: r.status,
            LegacyHjGoodsReceiptNoteModel.receipt_time_est: r.receipt_time_est,
            LegacyHjGoodsReceiptNoteModel.unit: r.unit,
            LegacyHjGoodsReceiptNoteModel.market: r.market,
        }
        for r in unique_records.values()
    ]
    with app_db.transaction() as trns:
        app_db.apply_query(
            sqla.delete(LegacyHjGoodsReceiptNoteModel).where(
                LegacyHjGoodsReceiptNoteModel.po_number.in_({r.po_number for r in records})
            ),
            transaction=trns,
        )
        query = sqla.insert(LegacyHjGoodsReceiptNoteModel).values(updates)
        app_db.apply_query(query, transaction=trns)


def update_wip(brand: str) -> None:
    week_config = brand_admin.get_week_config(brand)
    wh_ids = _get_wh_ids_by_brand(ScmWeek.current_week(week_config), brand, HjType.INVENTORY)
    if not _can_proceed_with_ids(wh_ids):
        return
    items = []
    for row in hj_repo.get_hj_query_wip(wh_ids=wh_ids.keys()):
        if not utils.is_in_int4_range(row[HjWipModel.quantity]):
            warnings.notify_warning(f"Invalid HJ WIP quantity: {row}")
        else:
            items.append(row)

    if not items:
        logger.warning("No HJ WIP data for %s", brand)
        return
    hj_repo.delete_hj_wip(wh_ids=wh_ids.keys())
    hj_repo.hj_wip_insert_many(items)


def get_wh_id_by_site(week: ScmWeek, brand: BRAND, site: SITE) -> str | None:
    site = dc_admin.get_enabled_sites(week, brand).get(site)
    if site:
        return site.high_jump_name
    return None


def get_hj_receipts_pos(week: ScmWeek, dc: DcConfig) -> list[PO_NUMBER]:
    return hj_repo.get_hj_receipts_pos(week, dc.high_jump_name)


def get_hj_skus_by_po(week: ScmWeek, po_number: str, dc: DcConfig, exclude_skus: list[str]) -> list[ReceiptSku]:
    return hj_repo.get_hj_receipt_skus(week, po_number, dc.high_jump_name, exclude_skus)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_received_weeks(wh_id: str, receive_date_from: datetime, receive_date_to: datetime) -> set[ScmWeek]:
    return hj_repo.get_received_weeks(wh_id, receive_date_from, receive_date_to)


class HjType(Enum):
    INVENTORY = 0
    RECEIVING = 1
    DISCARD = 2


@request_cache
def _get_wh_ids_by_brand(week: ScmWeek, brand: str, hj_type: HjType) -> dict[str, DcConfig]:
    all_dcs = tuple(
        # fmt: off
        dc
        for _brand in brand_admin.get_brands(week)
        for dc in dc_admin.get_enabled_sites(week, _brand).values()
        if (
            (dc.inventory_type == InventoryInputType.HJ) if hj_type == HjType.INVENTORY
            else (dc.receiving_type == ReceiveInputType.HIGH_JUMP) if hj_type == HjType.RECEIVING
            else (dc.source == DataSource.HIGHJUMP or dc.inventory_type == InventoryInputType.HJ)
        )
        # fmt: on
    )
    # making sure that each warehouse is picked only once during import
    # because we have some warehouses mapped to several sites (e.g. GC02 -> {GC SW, EP WE})
    wh_map = defaultdict(list)
    for dc in all_dcs:
        wh_map[dc.high_jump_name].append(dc)
    # brands are always sorted in brand_admin.get_brands, so getting [0] will return same brand each time
    return {hj_name: dcs[0] for hj_name, dcs in wh_map.items() if dcs[0].brand == brand}


def _can_proceed_with_ids(wh_ids: Collection) -> bool:
    if not wh_ids:
        warnings.notify_warning("No sites to sync")
        return False
    return True


def update_hj_wip_consumption(brand: str) -> None:
    week_config = brand_admin.get_week_config(brand)
    wh_ids = _get_wh_ids_by_brand(ScmWeek.current_week(week_config), brand, HjType.INVENTORY)
    if not _can_proceed_with_ids(wh_ids):
        return
    items = hj_repo.get_hj_query_wip_consumption(brand=brand, wh_ids=wh_ids.keys())
    hj_repo.hj_wip_consumption_insert_many(items)


class TransactionType(StrEnum):
    DIRECTED_MOVE = "Directed Move (Put)"
    AUTO_MOVE = "Auto Move"
    FULL_PALLET_MOVE = "Full Pallet Move (Put)"
    PUTAWAY = "Putaway (Put)"


class DestinationLocation(StrEnum):
    CARRYOVER = "CARRYOVER"
    CRISPER = "CRISPER"
    PRODUCTION = "PRODUCTION"
    SMRLEA_STG = "SMRLEA STG"


def get_hj_wip_consumption(wh_id: str, week: ScmWeek) -> dict[HjWipConsumptionType, dict[SKU_CODE, Decimal]]:
    result = defaultdict(lambda: defaultdict(Decimal))

    for item in hj_repo.get_hj_wip_consumption(wh_id, week):
        if (
            item.tran_type == TransactionType.AUTO_MOVE
            or (
                item.tran_type == TransactionType.FULL_PALLET_MOVE
                and item.destination_location == DestinationLocation.PRODUCTION
            )
            or (
                item.tran_type == TransactionType.PUTAWAY
                and item.destination_location == DestinationLocation.SMRLEA_STG
            )
        ):
            result[HjWipConsumptionType.PUTAWAY_TO_PRODUCTION][item.sku_code] += item.quantity

        if (
            item.destination_location in (DestinationLocation.CARRYOVER, DestinationLocation.CRISPER)
            and item.tran_type == TransactionType.FULL_PALLET_MOVE
        ):
            result[HjWipConsumptionType.CARRYOVER][item.sku_code] += item.quantity

        if item.tran_type == TransactionType.DIRECTED_MOVE:
            result[HjWipConsumptionType.INITIAL_PULL][item.sku_code] += item.quantity

    return result
