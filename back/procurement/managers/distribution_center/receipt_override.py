import itertools
import logging
from collections import defaultdict

from sqlalchemy import Select

from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import PO_SKU_KEY
from procurement.data.dto.inventory.receipt_override import ReceiptOverride
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import receipt_override as receipt_override_repo

logger = logging.getLogger(__name__)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_receipt_overrides_group_by_po_number_and_sku_code(
    weeks_dc_objects: list[tuple[ScmWeek, DcConfig]], sku_codes: set[str] = None
) -> dict[PO_SKU_KEY, list[ReceiptOverride]]:
    if not weeks_dc_objects:
        return {}
    receipt_override = defaultdict(list)
    if sku_codes is not None:
        if not sku_codes:
            return {}
    results = [
        receipt_override_repo.get_receipt_override_by_week_dc_brand(
            week=week, site=dc_object.sheet_name, brand=dc_object.brand, sku_codes=sku_codes
        )
        for week, dc_object in weeks_dc_objects
    ]

    for _override in itertools.chain.from_iterable(results):
        combination_id = (_override.po_number, _override.sku_code)
        receipt_override[combination_id].append(_override)
    receipt_override.default_factory = None
    return receipt_override


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_sku_filtered_receipt_overrides(
    dc_object: DcConfig, po_skus: Select
) -> dict[PO_SKU_KEY, list[ReceiptOverride]]:
    rows = receipt_override_repo.get_receipt_override_by_po_sku_query(dc_object.brand, po_skus)
    receipt_overrides = defaultdict(list)
    for row in rows:
        uid = (row.po_number, row.sku_code)
        receipt_overrides[uid].append(row)
    receipt_overrides.default_factory = None
    return receipt_overrides


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_receipt_overrides_item_by_po_number_and_sku_code(po_number: str, sku_code: str) -> list[ReceiptOverride]:
    return list(receipt_override_repo.get_receipt_override_by_po_sku(po_number, sku_code))
