import logging
from collections import defaultdict
from datetime import date, datetime
from decimal import Decimal

from procurement.client.googlesheets import googlesheet_utils
from procurement.client.googlesheets.googlesheet_utils import (
    GSHEET_DATE_FORMAT,
    GSHEET_DATETIME_FORMAT_12H_1,
    GSHEET_DATETIME_FORMAT_12H_1_SEC,
)
from procurement.constants.hellofresh_constant import BRAND_GC
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.cycle_counts import CycleCountInput
from procurement.data.googlesheet_model.cycle_counts_gc import CycleCountGc, CycleCountGcRow
from procurement.data.googlesheet_model.imt_data_dump import CycleCount3pl, CycleCount3plRow
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.datasync.framework import warnings as datasync_warnings
from procurement.repository.inventory import cycle_counts as cycle_counts_repo

logger = logging.getLogger(__name__)


def import_3pl_cycle_counts_data_to_db(brand: str, week: ScmWeek) -> None:
    """
    Imports Cycle Counts for all brands except GC from :param brand: document
    """
    available_sites = [
        site
        for b in brand_admin.get_brands(week)
        for site in dc_admin.get_enabled_sites(week, b).values()
        if b != BRAND_GC and site.is_3pl
    ]
    for site in available_sites:
        data = gsheet_admin.read_gsheet(CycleCount3pl(site.sheet_name), brand=brand, week=week)
        datasync_warnings.notify_records_errors(data, dc=site.sheet_name, skipped=False)
        _import_cycle_counts(data, site, week)


def import_gc_cycle_counts_data_to_db(week: ScmWeek) -> None:
    for site in dc_admin.get_enabled_sites(week, BRAND_GC).values():
        data = gsheet_admin.read_gsheet(CycleCountGc(site.sheet_name), brand=BRAND_GC, week=week)
        datasync_warnings.notify_records_errors(data, dc=site.sheet_name, skipped=False)
        _import_cycle_counts(data, site, week)


def _import_cycle_counts(data: list[CycleCount3plRow] | list[CycleCountGcRow], site: DcConfig, week: ScmWeek) -> None:
    cycle_counts_data = defaultdict(dict)

    for item in data:
        try:
            cycle_count_item = _read_gc_row(item, site) if site.brand == BRAND_GC else _read_3pl_row(item, site, week)
            if not utils.is_valid_sku_code(cycle_count_item.sku_code):
                raise ValueError(f"Invalid SKU Code format '{cycle_count_item.sku_code}'")
            sku_day_item = cycle_counts_data[(cycle_count_item.sku_code, cycle_count_item.cycle_count_day)]
            if cycle_count_item.date_for_comparison in sku_day_item:
                sku_day_item[cycle_count_item.date_for_comparison].units += cycle_count_item.units
            else:
                sku_day_item[cycle_count_item.date_for_comparison] = cycle_count_item
        except ValueError as exc:
            datasync_warnings.notify_sheet_error(
                item, exc, message="Error when importing Cycle Count data", dc=site.sheet_name, skipped=True
            )
    data_for_upsert = [
        cycle_counts_by_sku_day[max(cycle_counts_by_sku_day)] for cycle_counts_by_sku_day in cycle_counts_data.values()
    ]

    cycle_counts_repo.delete_cycle_counts_by_week(site.brand, site.sheet_name, week)
    cycle_counts_repo.upsert_cycle_counts(data_for_upsert)
    logger.info(
        "Cycle Counts sync: finished import data for week %s and dc %s; read count: %s; success: %s",
        week,
        site.sheet_name,
        len(data),
        len(cycle_counts_data),
    )


def _read_gc_row(item: CycleCountGcRow, site: DcConfig) -> CycleCountInput:
    googlesheet_utils.validate_required_fields(item, ["sku_code", "submitted_time", "units", "scm_week"])
    record_week = ScmWeek.from_str(item.scm_week)
    return CycleCountInput(
        sku_code=item.sku_code,
        site=site.sheet_name,
        cycle_count_day=record_week.get_first_day(),
        units=item.units,
        brand=site.brand,
        date_for_comparison=datetime.strptime(item.submitted_time, GSHEET_DATETIME_FORMAT_12H_1),
        scm_week=record_week,
    )


def _read_3pl_row(item: CycleCount3plRow, site: DcConfig, week: ScmWeek) -> CycleCountInput:
    googlesheet_utils.validate_required_fields(item, ["sku_code", "production_boh", "units", "date_of_count"])
    return CycleCountInput(
        sku_code=item.sku_code,
        site=site.sheet_name,
        cycle_count_day=datetime.strptime(item.production_boh, GSHEET_DATE_FORMAT).date(),
        units=item.units,
        brand=site.brand,
        date_for_comparison=datetime.strptime(item.date_of_count, GSHEET_DATETIME_FORMAT_12H_1_SEC),
        scm_week=week,
    )


def get_ingredient_depletion_cycle_counts(
    dc_object: DcConfig, date_from: date, date_to: date
) -> dict[SKU_CODE, dict[date, dict[date, Decimal]]]:
    res = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
    rows = cycle_counts_repo.get_cycle_counts(
        brand=dc_object.brand, site=dc_object.sheet_name, date_from=date_from, date_to=date_to
    )
    for row in rows:
        res[row.sku_code][row.cycle_count_day][date.max] += row.units
    return res


def get_packaging_depletion_cycle_counts(
    dc_object: DcConfig, date_from: date, date_to: date
) -> dict[SKU_CODE, dict[date, Decimal]]:
    res = defaultdict(lambda: defaultdict(Decimal))
    rows = cycle_counts_repo.get_cycle_counts(
        brand=dc_object.brand, site=dc_object.sheet_name, date_from=date_from, date_to=date_to
    )
    for row in rows:
        res[row.sku_code][row.cycle_count_day] += row.units
    return res


def get_latest_snapshot_date(brand: str, site: str) -> date | None:
    return cycle_counts_repo.get_latest_snapshot_date(brand, site)
