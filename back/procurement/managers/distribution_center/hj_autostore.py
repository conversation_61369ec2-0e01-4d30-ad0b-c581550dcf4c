import logging

from pyodbc import DatabaseError
from sqlalchemy.exc import OperationalError

from procurement.core.cache_utils.factory import CACHES
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.db_errors import HjOperationalError
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import BRAND, SITE, SKU_CODE
from procurement.repository.highjump import autostore

from . import highjump

logger = logging.getLogger(__name__)


def _get_hj_autostore(week: ScmWeek, brand: BRAND, site: SITE) -> dict[SKU_CODE, int]:
    wh_id = highjump.get_wh_id_by_site(week, brand, site)
    if not wh_id:
        return {}
    try:
        return autostore.get_hj_autostore_info_by_wh_id(wh_id)
    except (OperationalError, DatabaseError, HjOperationalError):
        logger.error("High Jump server is unavailable or does not exist", exc_info=True)
        return {}


@ApplicationMetrics.functional_methods()
def get_hj_autostore(week: ScmWeek, brand: BRAND, site: SITE) -> dict[SKU_CODE, int]:
    return CACHES.hj_autostore.get(site, lambda: _get_hj_autostore(week, brand, site))
