from collections import defaultdict
from collections.abc import Iterable

from procurement.client import ics
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, BRAND, ORDER_NUMBER, PO_NUMBER, SITE, SKU_CODE, SKU_NAME
from procurement.data.dto.inventory.ics import IcsTicketWithSiteBrand
from procurement.data.models.inventory.ics import IcsTicketsModel
from procurement.managers.admin import dc_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.dc_inventory.network_depletion import preset_configuration
from procurement.repository.inventory import ics as ics_repo
from procurement.repository.ordering import culinary_sku

PROCUREMENT_WORKSPACE = 3


def import_ics_tickets() -> None:
    market = context.get_request_context().market
    client = ics.get_client(market)

    site_hj_name_bob_code_to_bob_code = _get_site_hj_name_bob_code_to_bob_code()
    sku_code_by_name = _get_sku_code_by_name()
    invalid_items = []
    result = []

    for ticket in client.get_tickets(workspace_id=PROCUREMENT_WORKSPACE):
        try:
            custom_fields = ticket["custom_fields"]
            po_number = custom_fields["po"]
            order_number_from_po_number = utils.get_order_number_from_po_number(po_number)
            bob_code = site_hj_name_bob_code_to_bob_code[custom_fields["site"].split(" - ")[-1]]
            request_type = custom_fields["request_type"].split(" - ")[-1]
            result.append(
                {
                    IcsTicketsModel.market: market,
                    IcsTicketsModel.week: ScmWeek.from_str(custom_fields["week"]).to_number_format(),
                    IcsTicketsModel.bob_code: bob_code,
                    IcsTicketsModel.sku_code: sku_code_by_name.get(custom_fields["sku"]),
                    IcsTicketsModel.po_number: po_number,
                    IcsTicketsModel.order_number: order_number_from_po_number,
                    IcsTicketsModel.subject: ticket["subject"],
                    IcsTicketsModel.ticket_link: custom_fields["ticket_link"],
                    IcsTicketsModel.production_impact: custom_fields["production_impact"],
                    IcsTicketsModel.request_type: request_type,
                    IcsTicketsModel.ticket_id: ticket["id"],
                    IcsTicketsModel.status: ticket["status"],
                    IcsTicketsModel.updated_at: ticket["updated_at"],
                }
            )
        except Exception:
            invalid_items.append(str(ticket["id"]))

    if invalid_items:
        warnings.notify_warning(f"Failed to load tickets {', '.join(invalid_items)}")
    if result:
        ics_repo.upsert_ics_tickets(result)


def _get_site_hj_name_bob_code_to_bob_code() -> dict[str, BOB_CODE]:
    site_hj_name_bob_code_to_bob_code = {}
    for site in dc_admin.get_all_market_dcs(ignore_user=True):
        site_hj_name_bob_code_to_bob_code[site.high_jump_name] = site.bob_code
        site_hj_name_bob_code_to_bob_code[site.bob_code] = site.bob_code
    return site_hj_name_bob_code_to_bob_code


def _get_sku_code_by_name() -> dict[SKU_NAME, SKU_CODE]:
    return {item.sku_name: item.sku_code for item in culinary_sku.get_sku_name_by_sku_codes()}


def get_ics_tickets_preview(
    brand: str, sites: list[str], weeks: Iterable[ScmWeek]
) -> dict[ScmWeek, dict[SITE, list[tuple[SKU_CODE | None, PO_NUMBER | None, ORDER_NUMBER | None]]]]:
    bob_code_dc_mapping = dc_admin.get_all_sites_with_consolidation_by_brand(brand, sites, max(weeks))
    items = ics_repo.get_ics_tickets_preview(bob_codes=list(bob_code_dc_mapping.keys()), weeks=weeks)

    result = defaultdict(lambda: defaultdict(list))
    for item in items:
        if item.sku_code or item.po_number:
            result[item.week][bob_code_dc_mapping[item.bob_code].sheet_name].append(
                (item.sku_code, item.po_number, item.order_number)
            )

    return result


def get_ics_tickets(
    brands: list[str], sites: list[str], sku_code: str | None, po_number: PO_NUMBER | ORDER_NUMBER | None, week: ScmWeek
) -> list[IcsTicketWithSiteBrand]:
    bob_code_dc_mapping = dc_admin.get_all_brand_site_combinations(brands, sites, week)
    items = ics_repo.get_ics_tickets(list(bob_code_dc_mapping.keys()), sku_code, po_number, week)
    return [
        IcsTicketWithSiteBrand(
            ticket_id=it.ticket_id,
            ticket_link=it.ticket_link,
            po_number=it.po_number,
            order_number=it.order_number,
            sku_code=it.sku_code,
            bob_code=it.bob_code,
            subject=it.subject,
            week=week,
            production_impact=it.production_impact,
            status=it.status,
            request_type=it.request_type,
            updated_at=it.updated_at,
            site=bob_code_dc_mapping[it.bob_code].sheet_name if it.bob_code in bob_code_dc_mapping else None,
            brand=bob_code_dc_mapping[it.bob_code].brand if it.bob_code in bob_code_dc_mapping else None,
        )
        for it in items
    ]


def get_network_depletion_ics_tickets_preview(
    preset_id: int, week: ScmWeek
) -> dict[BRAND, dict[ScmWeek, dict[SITE, list[tuple[SKU_CODE | None, PO_NUMBER | None, ORDER_NUMBER | None]]]]]:
    res = {}
    preset = preset_configuration.get_user_preset_by_id(preset_id)
    for brand, sites in preset.brands.items():
        res[brand] = get_ics_tickets_preview(brand, sites, (week,))
    return res
