from procurement.core.cache_utils.factory import LOCAL_CACHES, LocalCacheObject
from procurement.core.kafka.listener import kafka_listener
from procurement.core.pubsub.events import InvalidateCacheEvent, ResetKafkaTopicEvent
from procurement.core.pubsub.handlers import RedisHandler


class LocalCacheInvalidateHandler(RedisHandler):
    def _process(self, msg: InvalidateCacheEvent):
        cache_obj: LocalCacheObject = getattr(LOCAL_CACHES, msg.cache_name)
        cache_obj.delete(msg.cache_key)


class KafkaResetOffsetHandler(RedisHandler):
    def _process(self, msg: ResetKafkaTopicEvent):
        kafka_listener.reset_offset(msg.topic, msg.read_last)
