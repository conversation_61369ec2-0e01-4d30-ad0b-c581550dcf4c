from __future__ import annotations

from abc import ABC, abstractmethod
from decimal import Decimal
from functools import cached_property
from typing import final

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.constants.ordering import DC_ROLLOVER_INVENTORY_SUPPLIER
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.imt.ingredients_depletion.core import IngredientSummary
from procurement.managers.imt.models import PoMasterViewResult


class BufferAnalysis(ABC):

    @property
    @abstractmethod
    def ingredient(self) -> IngredientSummary: ...

    @property
    @abstractmethod
    def po_sku_items(self) -> list[PoMasterViewResult]: ...

    @property
    @abstractmethod
    def units_needed(self) -> UNITS | None: ...

    @property
    @abstractmethod
    def buffer_quantity(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def allowed_buffer(self) -> Decimal | None: ...

    @property
    @abstractmethod
    def autobagger_supplier(self) -> str | None: ...

    @cached_property
    def supplier(self) -> str | None:
        return self._top_price_po.supplier if self._top_price_po else None

    @cached_property
    def case_yield(self) -> Decimal | None:
        return self._top_price_po.case_size if self._top_price_po else None

    @cached_property
    def case_cost(self) -> Decimal | None:
        return self._top_price_po.case_price if self._top_price_po else None

    @cached_property
    def po_buffer(self) -> Decimal | None:
        if self.ingredient.purchasing_category_name != IngredientCategory.PRODUCE:
            return None
        if not self.po_sku_items:
            return None
        buffer = max(
            (item.buffer for item in self.po_sku_items if item.buffer is not None),
            default=Decimal(),
        )
        return buffer

    @cached_property
    def quantity_needed(self) -> Decimal | None:
        buffer_value = self.allowed_buffer or self.po_buffer
        if not buffer_value:
            return None
        return buffer_value * (self.units_needed or 0) - (self.buffer_quantity or 0)

    @cached_property
    def case_total(self) -> int | None:
        if self._has_no_buffer or self.quantity_needed is None:
            return None
        return round(self.quantity_needed / self.case_yield) if self.case_yield else 0

    @cached_property
    def total_cost(self) -> Decimal | None:
        if self._has_no_buffer or None in (self.case_total, self.case_cost):
            return None
        return self.case_total * self.case_cost

    def _is_actual_supplier(self, supplier: str) -> bool:
        return supplier not in (
            self.autobagger_supplier,
            DC_ROLLOVER_INVENTORY_SUPPLIER,
        )

    @final
    @cached_property
    def _has_no_buffer(self) -> bool:
        return not self.po_buffer

    @final
    @cached_property
    def _top_price_po(self) -> PoMasterViewResult | None:
        if self.quantity_needed is None:
            return None
        unit_price_pos = (
            (po.case_price / po.case_size, po)
            for po in self.po_sku_items
            if po.case_size and self._is_actual_supplier(po.supplier)
        )
        if self.quantity_needed >= 0:
            return min(unit_price_pos, default=[None, None])[1]
        return max(unit_price_pos, default=[None, None])[1]


class AggregatedBufferAnalysis(BufferAnalysis):
    def __init__(
        self,
        buffers: list[BufferAnalysis],
        units_needed: UNITS | None,
        buffer_quantity: int | None,
    ):
        self.buffers = AggregatedData[list[BufferAnalysis], BufferAnalysis](items=buffers)
        self._units_needed = units_needed
        self._buffer_quantity = buffer_quantity

    @property
    def ingredient(self) -> IngredientSummary:
        return self.buffers.any_item(lambda it: it.ingredient)

    @cached_property
    def po_sku_items(self) -> list[PoMasterViewResult]:
        return self.buffers.sum_items(lambda it: it.po_sku_items, start=[])

    @property
    def units_needed(self) -> UNITS | None:
        return self._units_needed

    @property
    def buffer_quantity(self) -> int | None:
        return self._buffer_quantity

    @cached_property
    def allowed_buffer(self) -> Decimal | None:
        return self.buffers.any_item(lambda buffer: buffer.allowed_buffer)

    @property
    def autobagger_supplier(self) -> str | None:
        return self.buffers.any_item(lambda it: it.autobagger_supplier)
