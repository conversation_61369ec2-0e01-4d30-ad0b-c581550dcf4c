from abc import ABC, abstractmethod
from decimal import Decimal
from functools import cached_property
from typing import final

from procurement.core.typing import SKU_CODE
from procurement.managers.aggregation import AggregatedData


class BulkValues(ABC):
    @abstractmethod
    @cached_property
    def warehouse_name(self) -> str: ...

    @abstractmethod
    @cached_property
    def bulk_units_in_hj(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def bulk_sku_code(self) -> SKU_CODE: ...

    @abstractmethod
    @cached_property
    def bulk_units_ordered(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def bulk_units_received(self) -> Decimal | None: ...

    @cached_property
    def bulk_discards(self) -> int | None:
        return None

    @final
    @cached_property
    def delta(self) -> Decimal | None:
        if not self.bulk_sku_code or None in (self.bulk_units_ordered, self.bulk_units_received):
            return None
        return self.bulk_units_ordered - self.bulk_units_received


class AggregatedBulkValues(BulkValues):
    def __init__(self, bulk_values: list[BulkValues]):
        self.bulk_values = AggregatedData[list[BulkValues], BulkValues](items=bulk_values)
        self.unique_bulk_values = AggregatedData[list[BulkValues], BulkValues](
            items=list({b.warehouse_name: b for b in bulk_values}.values())
        )

    @cached_property
    def warehouse_name(self) -> str:
        raise ValueError("Aggregate don't have warehouse name")

    @cached_property
    def bulk_sku_code(self) -> SKU_CODE | None:
        return self.bulk_values.any_item(lambda bulk: bulk.bulk_sku_code)

    @cached_property
    def bulk_units_ordered(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return self.bulk_values.sum_if_present(lambda bulk: bulk.bulk_units_ordered)

    @cached_property
    def bulk_units_received(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return self.bulk_values.sum_if_present(lambda bulk: bulk.bulk_units_received)

    @cached_property
    def bulk_units_in_hj(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return self.unique_bulk_values.sum_if_present(getter=lambda bulk: bulk.bulk_units_in_hj)

    @cached_property
    def bulk_discards(self) -> int | None:
        if not self.bulk_sku_code:
            return None
        return self.unique_bulk_values.sum_if_present(getter=lambda bulk: bulk.bulk_discards)
