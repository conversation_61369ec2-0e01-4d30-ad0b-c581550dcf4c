from __future__ import annotations

import dataclasses
import itertools
from abc import ABC, abstractmethod
from collections.abc import Iterable
from datetime import date
from decimal import Decimal
from functools import cached_property
from typing import Any, Callable, TypeVar, final

from procurement.core import utils
from procurement.core.typing import UNITS
from procurement.data.models.inventory import DataSource
from procurement.managers.aggregation import AggregatedData
from procurement.managers.depletion.constants import (
    DAY_PASSED,
    NO_IMPACT,
    PROD_DAYS_AHEAD,
    PRODUCTION_WILL_STOP,
    SCHEDULED_DELIVERY_NECESSARY,
    SUPPLEMENT_NEEDED,
)
from procurement.managers.imt.bulk_sku import DEFAULT_PICK_CONVERSION

T = TypeVar("T")


@dataclasses.dataclass
class DepletionDay:
    inventory: UNITS
    units_delivered: UNITS
    discards: int
    on_hand: UNITS
    production_needs: UNITS
    eod_inventory_production: UNITS
    units_on_order: UNITS
    eod_inventory_order: UNITS
    status_in_week: str
    total_on_hand: UNITS
    total_production_need: UNITS
    supplemented_need_status: str
    total_on_hand_minus_total_production_need: UNITS


class DailyData:
    def __init__(  # pylint: disable=too-many-arguments
        self,
        units_delivered: list[UNITS],
        discards: list[UNITS],
        production_need_values: list[UNITS],
        prev_eow_inventory: UNITS,
        prod_week_length: UNITS,
        weekly_snapshot: UNITS | None,
        weekly_snapshot_day: int,
        manual_inventory_day: int,
        manual_form_inventory: int,
    ):
        self.units_delivered = units_delivered
        self.discards = discards
        self.production_need_values = production_need_values
        self.prev_eow_inventory = prev_eow_inventory
        self.inventory_values = []
        self.eod_inventory_production = []

        self.start_of_day_inventories = []

        self.days_on_hands = []
        self.prod_week_length = prod_week_length
        self.weekly_snapshot_day = weekly_snapshot_day
        self.weekly_snapshot = weekly_snapshot

        self.manual_inventory_day = manual_inventory_day
        self.manual_form_inventory = manual_form_inventory

    def build_data(self) -> DailyData:
        for day in range(self.prod_week_length):
            self.start_of_day_inventories.append(self._get_snapshot_by_day(day))
            self.inventory_values.append(self._get_inventory_by_day(day))
            self.days_on_hands.append(self._get_days_on_hands_by_day(day))
            self.eod_inventory_production.append(self._get_eod_inventory_production_by_day(day))
        return self

    def _get_snapshot_by_day(self, day: int) -> UNITS | None:
        if day == 0:
            return self.prev_eow_inventory
        if day == self.weekly_snapshot_day and self.weekly_snapshot is not None:
            return self.weekly_snapshot
        return None

    def _get_inventory_by_day(self, day: int) -> UNITS:
        manual_inventory = self.manual_form_inventory if day == self.manual_inventory_day else 0
        return (
            self.start_of_day_inventories[day]
            if self.start_of_day_inventories[day] is not None
            else self.eod_inventory_production[day - 1]
        ) + manual_inventory

    def _get_days_on_hands_by_day(self, day: int) -> UNITS:
        return self.inventory_values[day] + self.units_delivered[day] - self.discards[day]

    def _get_eod_inventory_production_by_day(self, day: int) -> UNITS:
        return self.days_on_hands[day] - self.production_need_values[day]


class DailyDepletion(ABC):
    @abstractmethod
    @cached_property
    def sku_code(self) -> str: ...

    @abstractmethod
    @cached_property
    def weekly_snapshot_day(self) -> int: ...

    @abstractmethod
    @cached_property
    def manual_inventory_day(self) -> int: ...

    @abstractmethod
    @cached_property
    def manual_form_inventory(self) -> int: ...

    @abstractmethod
    @cached_property
    def days(self) -> tuple[date, ...]: ...

    @abstractmethod
    @cached_property
    def daily_calc(self) -> DailyData: ...

    @abstractmethod
    @cached_property
    def production_needs(self) -> list[UNITS]: ...

    @abstractmethod
    @cached_property
    def live_row_need(self) -> UNITS: ...

    @abstractmethod
    @cached_property
    def units_on_order(self) -> list[UNITS]: ...

    @abstractmethod
    @cached_property
    def discards(self) -> list[int]: ...

    @abstractmethod
    @cached_property
    def units_delivered(self) -> list[UNITS]: ...

    @abstractmethod
    @cached_property
    def sku_pallet_quantity(self) -> Decimal: ...

    @cached_property
    def status_in_week(self) -> list[str]:
        res = []
        for week_index in range(self.daily_calc.prod_week_length):
            res.append(
                self._get_status_in_week_for_element(
                    week_index, self.eod_inventory_production[week_index], self.eod_inventory_order[week_index]
                )
            )
        return res

    @cached_property
    def supplemented_need_status(self) -> list[str]:
        return self.get_statuses(self.total_on_hand)

    @cached_property
    def pick_conversions(self) -> int:
        return DEFAULT_PICK_CONVERSION

    @cached_property
    def hj_supplemented_need_status(self) -> list[str]:
        zero_receives = [Decimal()] * len(self.units_delivered)
        on_hand = [self.sku_pallet_quantity] + [None] * (len(self.units_delivered) - 1)
        total_on_hand_hj = self._get_daily_total_on_hand(on_hand, zero_receives, zero_receives)
        return self.get_statuses_hj(total_on_hand_hj)

    def get_statuses(self, total_on_hand: Iterable[UNITS]) -> list[str]:
        statuses = []
        today = date.today()
        for on_hand, prod_need, day in zip(total_on_hand, self.total_production_need, self.days):
            statuses.append(self._get_status_for_day(today, day, on_hand, Decimal(), prod_need))
        return statuses

    def get_statuses_hj(self, total_on_hand: Iterable[UNITS]) -> list[str]:
        statuses = []
        today = date.today()
        prev_on_order = 0
        for on_hand, prod_need, day, units_on_order in zip(
            total_on_hand, self.total_production_need, self.days, self.units_on_order
        ):
            statuses.append(self._get_status_for_day(today, day, on_hand, prev_on_order + units_on_order, prod_need))
            prev_on_order += units_on_order
        return statuses

    def _get_status_for_day(
        self, today: date, day: date, total_on_hand: UNITS, production: UNITS, total_prod_need: UNITS
    ):
        if day < today:
            return DAY_PASSED
        if total_prod_need != 0 and total_on_hand + production < total_prod_need and self.live_row_need > 0:
            return SUPPLEMENT_NEEDED
        return NO_IMPACT

    @final
    @cached_property
    def by_day(self) -> dict[date, DepletionDay]:
        return {
            day: DepletionDay(
                inventory=self.inventory[i],
                units_delivered=self.units_delivered[i],
                discards=self.discards[i],
                on_hand=self.on_hand[i],
                production_needs=self.production_needs[i],
                eod_inventory_production=self.eod_inventory_production[i],
                units_on_order=self.units_on_order[i],
                eod_inventory_order=self.eod_inventory_order[i],
                status_in_week=self.status_in_week[i],
                total_on_hand=self.total_on_hand[i],
                total_production_need=self.total_production_need[i],
                supplemented_need_status=self.supplemented_need_status[i],
                total_on_hand_minus_total_production_need=self.total_on_hand_minus_total_production_need[i],
            )
            for i, day in enumerate(self.days)
        }

    @final
    @cached_property
    def end_of_week_inventory(self) -> UNITS | None:
        if not (self.total_on_hand and self.total_production_need):
            return None
        return self.total_on_hand[-1] - self.total_production_need[-1]

    @final
    @cached_property
    def inventory(self) -> list[UNITS]:
        return self.daily_calc.inventory_values

    @final
    @cached_property
    def on_hand(self) -> list[UNITS]:
        return self.daily_calc.days_on_hands

    @final
    @cached_property
    def eod_inventory_production(self) -> list[UNITS]:
        return self.daily_calc.eod_inventory_production

    @final
    @cached_property
    def eod_inventory_order(self) -> list[UNITS]:
        return [eod_p + uo for (eod_p, uo) in zip(self.eod_inventory_production, self.units_on_order)]

    @cached_property
    def total_on_hand(self) -> list[UNITS]:
        return self._get_daily_total_on_hand(
            self.daily_calc.start_of_day_inventories,
            self.units_delivered,
            self.units_on_order,
            manual_inventory_day=self.manual_inventory_day,
        )

    @cached_property
    def total_production_need(self) -> list[UNITS]:
        prev = 0
        res = []
        for elem in self.production_needs:
            current = elem + prev
            res.append(current)
            prev = current
        return res

    @final
    def _get_daily_total_on_hand(
        self,
        units_on_hand: list[UNITS],
        units_delivered: list[UNITS],
        units_on_order: list[UNITS],
        manual_inventory_day: int | None = None,
    ) -> list[UNITS]:
        res = []
        for day, on_order in enumerate([0] + units_on_order[:-1]):
            manual_inventory = self.manual_form_inventory if day == manual_inventory_day else 0
            actual_on_hand = units_on_hand[day] if units_on_hand[day] is not None else res[day - 1]
            res.append(int(actual_on_hand + on_order + units_delivered[day] - self.discards[day] + manual_inventory))

        return res

    @cached_property
    def total_on_hand_minus_total_production_need(self) -> list[UNITS]:
        return [
            total_on_hand - total_production_need
            for (total_on_hand, total_production_need) in zip(self.total_on_hand, self.total_production_need)
        ]

    @staticmethod
    def _get_status_in_week_for_element(
        prod_week_day: int, eod_inventory_production: UNITS, eod_inventory_order: UNITS
    ) -> str:
        if eod_inventory_production >= 0:
            return NO_IMPACT
        if eod_inventory_order >= 0 or prod_week_day < PROD_DAYS_AHEAD:  # SCM Week not started
            return SCHEDULED_DELIVERY_NECESSARY
        return PRODUCTION_WILL_STOP

    @staticmethod
    def _get_daily_quantities(
        days: tuple[date], skus: dict[date, Iterable[Any]], qty_field_getter: Callable[[Any], T]
    ) -> list[T]:
        res = []
        quantity_by_day = {day: sum(map(qty_field_getter, values)) for day, values in skus.items()}
        prod_week_start = days[0]
        for day in days:
            res.append(quantity_by_day.get(day, 0))
        # add previous days to first production day
        res[0] += sum(value for day, value in quantity_by_day.items() if day < prod_week_start)
        return res


class AggregatedDailyDepletion(DailyDepletion):
    def __init__(self, dailies: list[DailyDepletion]):
        self.dailies: AggregatedData[list[DailyDepletion], DailyDepletion] = AggregatedData(items=dailies)

    @cached_property
    def sku_code(self) -> str:
        return self.dailies.any_item(lambda daily: daily.sku_code)

    @cached_property
    def weekly_snapshot_day(self) -> int:
        return self.dailies.max_item(lambda daily: daily.weekly_snapshot_day)

    @cached_property
    def manual_inventory_day(self) -> int:
        return self.dailies.max_item(lambda daily: daily.manual_inventory_day)

    @cached_property
    def manual_form_inventory(self) -> int:
        return self.dailies.sum_if_present(lambda daily: daily.daily_calc.manual_form_inventory)

    @cached_property
    def days(self) -> tuple[date, ...]:
        return self.dailies.any_item(lambda daily: daily.days, default=tuple())

    @cached_property
    def daily_calc(self) -> DailyData:
        return DailyData(
            units_delivered=self.units_delivered,
            discards=self.discards,
            production_need_values=self.production_needs,
            prev_eow_inventory=self.dailies.sum_if_present(lambda daily: daily.daily_calc.prev_eow_inventory),
            prod_week_length=self.dailies.any_item(lambda daily: daily.daily_calc.prod_week_length, default=0),
            weekly_snapshot_day=self.weekly_snapshot_day,
            weekly_snapshot=self.dailies.sum_if_present(lambda daily: daily.daily_calc.weekly_snapshot),
            manual_inventory_day=self.manual_inventory_day,
            manual_form_inventory=self.manual_form_inventory,
        ).build_data()

    @cached_property
    def production_needs(self) -> list[int]:
        return self.dailies.sum_array(lambda daily: daily.production_needs) or []

    @cached_property
    def live_row_need(self) -> list[int]:
        return self.dailies.sum_if_present(lambda daily: daily.live_row_need)

    @cached_property
    def units_on_order(self) -> list[int]:
        return self.dailies.sum_array(lambda daily: daily.units_on_order) or []

    @cached_property
    def discards(self) -> list[int]:
        dailies_by_discard_type = utils.group_by(
            self.dailies.raw,
            lambda d: d.context.dc_object.source if d.context.dc_object else None,
        )
        hj_discard_dailies = dailies_by_discard_type.pop(DataSource.HIGHJUMP, [])
        discard_values = [
            d.discards
            for d in itertools.chain.from_iterable(dailies_by_discard_type.values())
            if d.discards is not None
        ]
        hj_unique_discards = {
            d.context.dc_object.high_jump_name: d.discards for d in hj_discard_dailies if d.discards is not None
        }
        discard_values.extend(hj_unique_discards.values())
        return AggregatedData(discard_values).sum_array(lambda discard_by_day: discard_by_day) or []

    @cached_property
    def units_delivered(self) -> list[int]:
        return self.dailies.sum_array(lambda daily: daily.units_delivered) or []

    @cached_property
    def sku_pallet_quantity(self) -> Decimal:
        return self.dailies.sum_if_present(lambda daily: daily.sku_pallet_quantity)

    @cached_property
    def total_on_hand(self) -> list[int]:
        return self.dailies.sum_array(lambda daily: daily.total_on_hand) or []
