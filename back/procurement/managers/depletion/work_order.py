from abc import ABC, abstractmethod
from decimal import Decimal
from functools import cached_property

from procurement.managers.aggregation import AggregatedData


class DepletionWorkOrderItem(ABC):
    @abstractmethod
    @cached_property
    def required_lbs(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def staged_lbs(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def buffered_forecast(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def wo_row_need(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def staged_plus_hj_minus_received(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def hj_minus_wo_row_need(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def hj_plus_to_be_delivered_minus_wo_row_need(self) -> Decimal: ...


class AggregatedDepletionWorkOrderItem(DepletionWorkOrderItem):
    def __init__(self, work_order_items: list[DepletionWorkOrderItem]):
        self.work_order_items = AggregatedData[list[DepletionWorkOrderItem], DepletionWorkOrderItem](
            items=work_order_items
        )

    @cached_property
    def required_lbs(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.required_lbs)

    @cached_property
    def staged_lbs(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.staged_lbs)

    @cached_property
    def buffered_forecast(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.buffered_forecast)

    @cached_property
    def wo_row_need(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.wo_row_need)

    @cached_property
    def staged_plus_hj_minus_received(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.staged_plus_hj_minus_received)

    @cached_property
    def hj_minus_wo_row_need(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.hj_minus_wo_row_need)

    @cached_property
    def hj_plus_to_be_delivered_minus_wo_row_need(self) -> Decimal:
        return self.work_order_items.sum_if_present(lambda wo: wo.hj_plus_to_be_delivered_minus_wo_row_need)
