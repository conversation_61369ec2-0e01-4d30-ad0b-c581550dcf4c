from datetime import date, timed<PERSON>ta
from typing import NamedTuple

from procurement.core.dates.weeks import ScmWeek, ScmWeekConfig

from .constants import PROD_DAYS_AFTER, PROD_DAYS_AHEAD


class DepletionWeekday(NamedTuple):
    day: date
    weekday_title: str


def get_weekdays(week: ScmWeek, week_config: ScmWeekConfig) -> tuple[DepletionWeekday, ...]:
    prod_week_start = week.get_first_day(week_config) - timedelta(days=PROD_DAYS_AHEAD)
    prod_week_length = week_config.length + PROD_DAYS_AHEAD + PROD_DAYS_AFTER
    return tuple(
        DepletionWeekday(day, title)
        for day, title in zip(
            (prod_week_start + timedelta(days=i) for i in range(prod_week_length)),
            week.get_weekday_titles(week_config, extend_left=PROD_DAYS_AHEAD, extend_right=PROD_DAYS_AFTER),
        )
    )
