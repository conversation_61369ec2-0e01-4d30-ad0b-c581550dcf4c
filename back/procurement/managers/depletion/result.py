from __future__ import annotations

import itertools
from abc import ABC, abstractmethod
from functools import cached_property
from typing import final

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates.weeks import ScmWeek, ScmWeekConfig
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.depletion.constants import NO_IMPACT, PROD_DAYS_AFTER, PROD_DAYS_AHEAD, SUPPLEMENT_NEEDED

from . import utils
from .buffer_analysis import AggregatedBufferAnalysis, BufferAnalysis
from .bulk import AggregatedBulkValues, BulkValues
from .constants import PRODUCTION_WILL_STOP, SCHEDULED_DELIVERY_NECESSARY
from .daily import AggregatedDailyDepletion, DailyDepletion
from .weekly import AggregatedDepletionWeeklyItem, DepletionWeeklyItem
from .work_order import AggregatedDepletionWorkOrderItem, DepletionWorkOrderItem


class IngredientDepletionResult(ABC):
    _packaged_items: AggregatedData[list[IngredientDepletionResult], IngredientDepletionResult]

    @abstractmethod
    @cached_property
    def brands(self) -> set[str]: ...

    @abstractmethod
    @cached_property
    def sites(self) -> set[str]: ...

    @abstractmethod
    @cached_property
    def week(self) -> ScmWeek: ...

    @abstractmethod
    @cached_property
    def week_config(self) -> ScmWeekConfig: ...

    @abstractmethod
    @cached_property
    def sku_code(self) -> str: ...

    @abstractmethod
    @cached_property
    def sku_name(self) -> str: ...

    @abstractmethod
    @cached_property
    def unit_of_measure(self) -> UnitOfMeasure | None: ...

    @abstractmethod
    @cached_property
    def category(self) -> str: ...

    @abstractmethod
    @cached_property
    def commodity_group(self) -> str | None: ...

    @abstractmethod
    @cached_property
    def buyer(self) -> str | None: ...

    @abstractmethod
    @cached_property
    def impacted_recipes(self) -> set[str]: ...

    @abstractmethod
    @cached_property
    def forecast(self) -> UNITS | None: ...

    @abstractmethod
    @cached_property
    def plan(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def live_row_need(self) -> UNITS | None: ...

    @abstractmethod
    @cached_property
    def row_forecast(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def planned_production(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def hj_unit(self) -> UnitOfMeasure | None: ...

    @final
    @cached_property
    def delta(self) -> float | None:
        return utils.calc_delta(self.forecast, self.plan) if self.plan is not None else None

    @abstractmethod
    @cached_property
    def units_to_produce_by_autobagger(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def daily(self) -> DailyDepletion | None: ...

    @cached_property
    def supplement_need(self) -> str | None:
        return self.get_supplement_need(self.daily.supplemented_need_status)

    @cached_property
    def supplement_need_hj(self) -> str:
        return self.get_supplement_need(self.daily.hj_supplemented_need_status)

    def get_supplement_need(self, statuses: list[str]) -> str:
        weekday_titles = ScmWeek.get_weekday_titles(
            self.week_config, extend_left=PROD_DAYS_AHEAD, extend_right=PROD_DAYS_AFTER
        )
        for day, status in zip(weekday_titles, statuses):
            if status == SUPPLEMENT_NEEDED:
                return day
        return NO_IMPACT

    @final
    @cached_property
    def end_of_week_inventory(self) -> int | None:
        return self.daily.end_of_week_inventory if self.daily else None

    @abstractmethod
    @cached_property
    def critical_delivery_status(self) -> str | None: ...

    @abstractmethod
    @cached_property
    def weekly_overview(self) -> DepletionWeeklyItem:
        # TODO: make it dependent on daily result
        ...

    @abstractmethod
    @cached_property
    def buffer_analysis(self) -> BufferAnalysis | None: ...

    @abstractmethod
    @cached_property
    def bulk_values(self) -> BulkValues: ...

    @property
    def child_depletion(self) -> list[IngredientDepletionResult]:
        return []

    @cached_property
    def packaged_depl_items(self) -> list[IngredientDepletionResult]:
        return []

    @abstractmethod
    @cached_property
    def work_order_section(self) -> DepletionWorkOrderItem | None: ...


class AggregatedIngredientDepletionResult(IngredientDepletionResult):
    def __init__(self, child_ing_depl: list[IngredientDepletionResult]):
        self._child_depletion = AggregatedData[list[IngredientDepletionResult], IngredientDepletionResult](
            items=child_ing_depl
        )

    @cached_property
    def brands(self) -> set[str]:
        return self._child_depletion.union(lambda depl: depl.brands)

    @cached_property
    def sites(self) -> set[str]:
        return self._child_depletion.union(lambda depl: depl.sites)

    @cached_property
    def week(self) -> ScmWeek:
        return self._child_depletion.any_item(lambda depl: depl.week)

    @cached_property
    def week_config(self) -> ScmWeekConfig:
        return self._child_depletion.any_item(lambda depl: depl.week_config)

    @cached_property
    def sku_code(self) -> str:
        return self._child_depletion.any_item(lambda depl: depl.sku_code)

    @cached_property
    def sku_name(self) -> str:
        return self._child_depletion.any_item(lambda depl: depl.sku_name)

    @cached_property
    def unit_of_measure(self) -> UnitOfMeasure | None:
        return self._child_depletion.any_item(lambda depl: depl.unit_of_measure)

    @cached_property
    def category(self) -> str:
        return self._child_depletion.any_item(lambda depl: depl.category)

    @cached_property
    def commodity_group(self) -> str | None:
        return self._child_depletion.any_item(lambda depl: depl.commodity_group)

    @cached_property
    def buyer(self) -> str | None:
        return self._child_depletion.any_item(lambda depl: depl.buyer)

    @cached_property
    def hj_unit(self) -> UnitOfMeasure | None:
        return self._child_depletion.any_item(lambda depl: depl.hj_unit)

    @cached_property
    def impacted_recipes(self) -> set[str]:
        return self._child_depletion.union(lambda depl: depl.impacted_recipes, ignore_falsy=True)

    @cached_property
    def plan(self) -> int | None:
        return self._child_depletion.sum_if_present(lambda depl: depl.plan)

    @cached_property
    def live_row_need(self) -> None:
        return self._child_depletion.sum_if_present(lambda depl: depl.live_row_need)

    @cached_property
    def row_forecast(self) -> int | None:
        return self._child_depletion.sum_if_present(lambda depl: depl.row_forecast)

    @cached_property
    def planned_production(self) -> int | None:
        return self._child_depletion.sum_if_present(lambda depl: depl.planned_production)

    @cached_property
    def forecast(self) -> UNITS | None:
        return self._child_depletion.sum_if_present(lambda depl: depl.forecast)

    @cached_property
    def units_to_produce_by_autobagger(self) -> int | None:
        return self._child_depletion.sum_if_present(lambda depl: depl.units_to_produce_by_autobagger)

    @cached_property
    def weekly_overview(self) -> AggregatedDepletionWeeklyItem:
        return AggregatedDepletionWeeklyItem(
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            weekly_overviews=list(filter(None, (depl.weekly_overview for depl in self._child_depletion.raw))),
        )

    @cached_property
    def bulk_values(self) -> AggregatedBulkValues:
        return AggregatedBulkValues(
            bulk_values=list(filter(None, (depl.bulk_values for depl in self._child_depletion.raw)))
        )

    @cached_property
    def buffer_analysis(self) -> AggregatedBufferAnalysis:
        return AggregatedBufferAnalysis(
            buffers=list(filter(None, (depl.buffer_analysis for depl in self._child_depletion.raw))),
            units_needed=self.weekly_overview.units_needed,
            buffer_quantity=self.weekly_overview.buffer_quantity,
        )

    @cached_property
    def daily(self) -> AggregatedDailyDepletion:
        return AggregatedDailyDepletion(dailies=list(filter(None, (depl.daily for depl in self._child_depletion.raw))))

    @cached_property
    def critical_delivery_status(self) -> str:
        child_critical_deliveries = list(
            filter(None, (depl.critical_delivery_status for depl in self._child_depletion.raw))
        )
        if PRODUCTION_WILL_STOP in child_critical_deliveries:
            return PRODUCTION_WILL_STOP
        if SCHEDULED_DELIVERY_NECESSARY in child_critical_deliveries:
            return SCHEDULED_DELIVERY_NECESSARY
        return NO_IMPACT

    @property
    def child_depletion(self) -> list[IngredientDepletionResult]:
        return self._child_depletion.raw if len(self._child_depletion.raw) > 1 else []

    @cached_property
    def packaged_depl_items(self) -> list[IngredientDepletionResult]:
        return list(itertools.chain.from_iterable(it.packaged_depl_items for it in self._child_depletion.raw))

    @cached_property
    def work_order_section(self) -> AggregatedDepletionWorkOrderItem:
        return AggregatedDepletionWorkOrderItem(
            work_order_items=list(filter(None, (depl.work_order_section for depl in self._child_depletion.raw)))
        )
