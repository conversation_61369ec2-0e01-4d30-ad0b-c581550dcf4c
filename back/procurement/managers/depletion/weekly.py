import itertools
from abc import ABC, abstractmethod
from decimal import Decima<PERSON>
from functools import cached_property
from typing import NamedTuple, final

from procurement.core import utils
from procurement.core.typing import UNITS
from procurement.data.models.inventory import DataSource
from procurement.managers.aggregation import AggregatedData


class AllocationPriceRange(NamedTuple):
    min_price: Decimal
    max_price: Decimal


class DepletionWeeklyItem(ABC):

    @abstractmethod
    @cached_property
    def sku_code(self) -> str: ...

    @abstractmethod
    @cached_property
    def warehouse_name(self) -> str: ...

    @abstractmethod
    @cached_property
    def start_of_day_inventory(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def units_to_produce_by_autobagger(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def units_needed(self) -> UNITS | None: ...

    @cached_property
    def units_received(self) -> Decimal:
        return self.units_received_pre_snapshot + self.units_received_post_snapshot

    @abstractmethod
    @cached_property
    def units_received_pre_snapshot(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def units_received_post_snapshot(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def units_ordered(self) -> Decimal: ...

    @property
    def prev_eow_inventory(self) -> UNITS | None:
        return 0

    @final
    @cached_property
    def units_in_house_minus_row_need(self) -> Decimal:
        return self.hj_units_in_house - (self.row_need or Decimal())

    @final
    @cached_property
    def units_in_house_minus_row_need_minus_forecast(self) -> Decimal:
        return self.units_in_house_minus_row_need - (self.next_week_forecast or Decimal())

    @cached_property
    def total_on_hand_minus_prod_needs(self) -> Decimal:
        return self.total_on_hand - (self.units_needed or Decimal())

    @final
    @cached_property
    def hj_autostore_inv_plus_in_house(self) -> Decimal | None:
        return self.hj_units_in_house + self.hj_autostore_inventory if self.hj_autostore_inventory else None

    @final
    @cached_property
    def start_of_day_inv_minus_hj_units_in_house(self) -> Decimal:
        return (self.start_of_day_inventory or Decimal()) - self.hj_units_in_house

    @abstractmethod
    @cached_property
    def wip_hj_discards(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def hj_discards(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def discards(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def donations(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def total_on_hand(self) -> int: ...

    @abstractmethod
    @cached_property
    def wip_consumption_carryover(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def wip_consumption_initial_pull(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def wip_consumption_putaway_to_production(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def in_progress_hj(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def awaiting_delivery(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def not_delivered(self) -> Decimal | None: ...

    @cached_property
    def buffer_quantity(self) -> Decimal | None:
        if None in (
            self.total_on_hand,
            self.units_needed,
            self.in_progress_hj,
            self.awaiting_delivery,
            self.not_delivered,
        ):
            return None
        return (
            self.total_on_hand - self.units_needed + self.in_progress_hj + self.awaiting_delivery + self.not_delivered
        )

    @cached_property
    def buffer_percent(self) -> Decimal | None:
        return round(Decimal(self.buffer_quantity) / self.units_needed, 4) if self.units_needed else None

    @abstractmethod
    @cached_property
    def hj_units_in_house(self) -> Decimal: ...

    @abstractmethod
    @cached_property
    def row_need(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def prev_week_row_need(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def next_week_forecast(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def hj_snapshot(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def inventory(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def pulls(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def hj_autostore_inventory(self) -> int | None: ...

    @abstractmethod
    @cached_property
    def units_on_prod_floor(self) -> Decimal | None: ...

    @abstractmethod
    @cached_property
    def allocation_price_range(self) -> AllocationPriceRange | None: ...


class AggregatedDepletionWeeklyItem(DepletionWeeklyItem):
    def __init__(
        self,
        weekly_overviews: list[DepletionWeeklyItem],
        units_to_produce_by_autobagger: int | None,
    ):
        self.weekly_overviews = AggregatedData[list[DepletionWeeklyItem], DepletionWeeklyItem](items=weekly_overviews)
        self.unique_weekly_overviews = AggregatedData[list[DepletionWeeklyItem], DepletionWeeklyItem](
            items=list({w.warehouse_name: w for w in self.weekly_overviews.raw}.values())
        )
        self._units_to_produce_by_autobagger = units_to_produce_by_autobagger

    @cached_property
    def sku_code(self) -> str:
        return self.weekly_overviews.any_item(lambda depl: depl.sku_code)

    @cached_property
    def warehouse_name(self) -> str:
        raise ValueError("Aggregate don't have warehouse name")

    @cached_property
    def start_of_day_inventory(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.start_of_day_inventory)

    @cached_property
    def units_to_produce_by_autobagger(self) -> int | None:
        return self._units_to_produce_by_autobagger

    @cached_property
    def units_needed(self) -> UNITS | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.units_needed)

    @cached_property
    def units_ordered(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.units_ordered)

    @cached_property
    def units_received_pre_snapshot(self) -> Decimal:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.units_received_pre_snapshot)

    @cached_property
    def units_received_post_snapshot(self) -> Decimal:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.units_received_post_snapshot)

    @cached_property
    def hj_units_in_house(self) -> Decimal | None:
        return self.unique_weekly_overviews.sum_if_present(getter=lambda weekly: weekly.hj_units_in_house)

    @cached_property
    def hj_discards(self) -> int:
        return self.unique_weekly_overviews.sum_if_present(getter=lambda weekly: weekly.hj_discards)

    @cached_property
    def units_on_prod_floor(self) -> Decimal | None:
        return self.unique_weekly_overviews.sum_if_present(getter=lambda weekly: weekly.units_on_prod_floor)

    @cached_property
    def row_need(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.row_need)

    @cached_property
    def prev_week_row_need(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.prev_week_row_need)

    @cached_property
    def next_week_forecast(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.next_week_forecast)

    @cached_property
    def hj_snapshot(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.hj_snapshot)

    @cached_property
    def inventory(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.inventory)

    @cached_property
    def discards(self) -> int | None:
        weeklies_by_discard_type = utils.group_by(
            self.weekly_overviews.raw,
            lambda w: w.context.dc_object.source if w.context.dc_object else None,
        )
        hj_discard_weeklies = weeklies_by_discard_type.pop(DataSource.HIGHJUMP, [])
        discard_values = [
            w.discards
            for w in itertools.chain.from_iterable(weeklies_by_discard_type.values())
            if w.discards is not None
        ]
        hj_unique_discards = {
            w.context.dc_object.high_jump_name: w.discards for w in hj_discard_weeklies if w.discards is not None
        }
        discard_values.extend(hj_unique_discards.values())
        return sum(discard_values) if discard_values else None

    @cached_property
    def donations(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.donations)

    @cached_property
    def wip_hj_discards(self) -> int:
        return self.unique_weekly_overviews.sum_if_present(getter=lambda weekly: weekly.wip_hj_discards)

    @cached_property
    def pulls(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.pulls)

    @cached_property
    def in_progress_hj(self) -> int | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.in_progress_hj)

    @cached_property
    def awaiting_delivery(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.awaiting_delivery)

    @cached_property
    def not_delivered(self) -> Decimal | None:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.not_delivered)

    @cached_property
    def total_on_hand(self) -> int:
        sum_on_hand = self.weekly_overviews.sum_if_present(lambda weekly: weekly.total_on_hand)
        sum_prev_inv = self.weekly_overviews.sum_if_present(lambda weekly: weekly.prev_eow_inventory)
        if sum_on_hand < 0 and sum_prev_inv < 0:
            return 0
        return sum_on_hand

    @cached_property
    def wip_consumption_carryover(self) -> Decimal:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.wip_consumption_carryover)

    @cached_property
    def wip_consumption_initial_pull(self) -> Decimal:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.wip_consumption_initial_pull)

    @cached_property
    def wip_consumption_putaway_to_production(self) -> Decimal:
        return self.weekly_overviews.sum_if_present(lambda weekly: weekly.wip_consumption_putaway_to_production)

    @cached_property
    def hj_autostore_inventory(self) -> int | None:
        return self.unique_weekly_overviews.sum_if_present(getter=lambda weekly: weekly.hj_autostore_inventory)

    @cached_property
    def allocation_price_range(self) -> AllocationPriceRange | None:
        min_allocation_price = self.weekly_overviews.min_item(
            lambda i: i.allocation_price_range.min_price if i.allocation_price_range else None
        )
        max_allocation_price = self.weekly_overviews.max_item(
            lambda i: i.allocation_price_range.max_price if i.allocation_price_range else None
        )
        return (
            AllocationPriceRange(min_allocation_price, max_allocation_price)
            if min_allocation_price is not None
            else None
        )
