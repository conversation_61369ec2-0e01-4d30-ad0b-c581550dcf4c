from collections import defaultdict
from decimal import Decimal

from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE, WH_CODE
from procurement.managers.pimt import partners as partner_service
from procurement.repository.pimt import inventory


def get_inventory_data(warehouses: frozenset[WH_CODE] = None) -> dict[SKU_CODE, dict[WH_CODE, Decimal]]:
    data = defaultdict(lambda: defaultdict(Decimal))
    wh_codes = [wh.code for wh in partner_service.get_all_partners() if warehouses is None or wh.code in warehouses]
    for item in inventory.get_unified_inventory_in_house(wh_codes):
        data[item.sku_code][item.wh_code] += item.total
    return data


def get_inventory_data_by_week(wh_code: str, week: ScmWeek) -> dict[SKU_CODE, dict[WH_CODE, Decimal]]:
    data = defaultdict(lambda: defaultdict(Decimal))
    for item in inventory.get_unified_inventory_in_house([wh_code], week):
        data[item.sku_code][item.wh_code] += item.total
    return data
