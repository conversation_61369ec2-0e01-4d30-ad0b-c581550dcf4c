import dataclasses
import logging
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any, TypeVar

import parse as template_parse
from dateutil.relativedelta import relativedelta

from procurement.client.googlesheets.googlesheet_utils import GSHEET_DATE_FORMAT
from procurement.constants.hellofresh_constant import (
    APP_TIMEZONE,
    RECEIVE_EXPIRATION_PROTEIN_RANGE_MONTHS,
    IngredientCategory,
    PimtExceptionType,
)
from procurement.constants.ordering import NOT_DELIVERED_PAST_DUE
from procurement.core import utils
from procurement.core.cache_utils.factory import CACHES
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context as request_context
from procurement.core.typing import CATEGORY_NAME, LOT_CODE, ORDER_NUMBER, PO_NUMBER, SKU_CODE, SKU_NAME, UNITS
from procurement.data.dto.ordering.purchase_order.pimt_pos import InventoryPo
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.dto.sku import SkuMeta
from procurement.data.googlesheet_model.gsheet_inventory import PimtInventoryDataModel
from procurement.data.models.pimt.exceptions import ExceptionModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.imt.export.export_base import ExportSheet, ExportWorkbook, Row, TabularData
from procurement.managers.pimt import email_export, master_replenishment
from procurement.managers.pimt import partners as partner_service
from procurement.managers.pimt import purchase_order
from procurement.managers.pimt.master_replenishment import MasterReplenishment
from procurement.managers.sku import culinary_sku
from procurement.repository.ordering.purchase_order import pimt_pos
from procurement.repository.pimt import exceptions as exceptions_rep
from procurement.repository.pimt import goods_receipt_notification as grn_repo
from procurement.repository.pimt import inventory
from procurement.repository.pimt import partner as partner_repo

logger = logging.getLogger(__name__)

DATE_ERROR_TEMPLATE = "Cannot parse value {value} to date"


@dataclasses.dataclass
class FeedDataInput:
    order_number: str
    sku: str
    partner: str
    supplier: str | None = None
    po_number: str | None = None
    is_e2open_grn: bool | None = None
    lot_code: str | None = None
    expiration_date: datetime | None = None
    invalid_date: str | None = None
    units_received: UNITS | None = None
    delivery_time: datetime | None = None


@dataclasses.dataclass
class WrongInventoryData:
    partner: str
    po_number: str
    sku_code: str
    sku_name: str
    lot_code: str
    supplier: str
    expiration_date: str
    exception_reason: str
    error_date: date = None


@dataclasses.dataclass
class WrongGrnData:
    partner: str
    po_number: str
    sku_code: str
    sku_name: str
    units: Decimal
    delivery_time: datetime | None
    exception_reason: str
    error_date: date = None


@dataclasses.dataclass
class CountPerCent:
    count: int
    per_cent: float
    total_count: int | None = None


@dataclasses.dataclass
class PartnerMetric:
    partner: str
    invalid_rows: CountPerCent
    by_type: dict[PimtExceptionType, CountPerCent]


@dataclasses.dataclass
class UndeliveredData:
    tpw: str
    supplier: str
    po_number: PO_NUMBER
    sku_code: SKU_CODE
    sku_name: SKU_NAME
    category: CATEGORY_NAME
    scheduled_delivery_date: datetime
    po_status: str
    assigned_buyer: str
    po_buyer: str


@dataclasses.dataclass
class ExceptionMetricsData:
    export_date: date
    invalid_rows: CountPerCent
    by_partner: list[PartnerMetric]
    week: ScmWeek

    @staticmethod
    def _build_exception_metric(exceptions: dict[str, int]) -> dict[PimtExceptionType, CountPerCent]:
        total_exceptions = sum(exceptions.values())
        res = {}
        for exception in PimtExceptionType.get_ui_exceptions():
            ex_count = exceptions.get(exception, 0)
            res[exception] = CountPerCent(ex_count, ExceptionMetricsData._percent_ratio(total_exceptions, ex_count))
        return res

    @staticmethod
    def _get_exceptions_by_partner(partner, values):
        invalid_records = values["invalid_records"]
        total_records = values["total_records"]
        per_cent_by_partner = ExceptionMetricsData._percent_ratio(total_records, invalid_records)
        by_type = ExceptionMetricsData._build_exception_metric(values["exceptions"])

        return PartnerMetric(
            partner=partner,
            invalid_rows=CountPerCent(count=invalid_records, per_cent=per_cent_by_partner, total_count=total_records),
            by_type=by_type,
        )

    @staticmethod
    def from_db_dict(item: ExceptionModel, days_to_weeks: dict[date, ScmWeek]) -> "ExceptionMetricsData":
        stats = item.stats
        by_partner = [
            ExceptionMetricsData._get_exceptions_by_partner(partner, values)
            for partner, values in stats["stats"].items()
        ]
        total_records = sum(value.invalid_rows.total_count for value in by_partner)
        count_invalid_records = sum(value.invalid_rows.count for value in by_partner)
        total_per_cent = ExceptionMetricsData._percent_ratio(total_records, count_invalid_records)

        return ExceptionMetricsData(
            export_date=item.exception_date,
            invalid_rows=CountPerCent(count=count_invalid_records, per_cent=total_per_cent, total_count=total_records),
            by_partner=by_partner,
            week=days_to_weeks[item.exception_date],
        )

    @staticmethod
    def _percent_ratio(total: int, part: int) -> float:
        return round((part / total) * 100 if total else 0, 2)


class PimtExceptionsExportSheetData(TabularData):
    def __init__(self, report_data: Iterable[WrongInventoryData]):
        title = "PIMT Daily Exceptions Report"
        super().__init__(title, report_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Partner",
                    "PO Number",
                    "SKU Code",
                    "SKU Name",
                    "Lot Code",
                    "Supplier",
                    "Expiration Date",
                    "Exception Reason",
                    "Error Origination Date",
                ]
            )
        ]

    def _get_row_data(self, raw_item: WrongInventoryData) -> Iterable[Any]:
        return (
            raw_item.partner,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.lot_code,
            raw_item.supplier,
            raw_item.expiration_date,
            raw_item.exception_reason,
            raw_item.error_date,
        )


class PimtGrnExceptionsExportSheetData(TabularData):
    def __init__(self, report_data: Iterable[WrongGrnData]):
        title = "PIMT Daily GRN Exceptions Report"
        super().__init__(title, report_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Partner",
                    "PO Number",
                    "SKU Code",
                    "SKU Name",
                    "Units Received",
                    "Delivery Time",
                    "Exception Reason",
                    "Error Origination Date",
                ]
            )
        ]

    def _get_row_data(self, raw_item: WrongGrnData) -> Iterable[Any]:
        return (
            raw_item.partner,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.units,
            raw_item.delivery_time,
            raw_item.exception_reason,
            raw_item.error_date,
        )


class PimtUndeliveredSheetData(TabularData):
    def __init__(self, undelivered_data: Iterable[UndeliveredData]):
        title = "PIMT Undelivered"
        super().__init__(title, undelivered_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "3PW",
                    "Supplier",
                    "PO Number",
                    "SKU Code",
                    "SKU Name",
                    "Category",
                    "Scheduled Delivery Date",
                    "PO Status",
                    "Assigned Buyer",
                    "PO Buyer",
                ]
            )
        ]

    def _get_row_data(self, raw_item: UndeliveredData) -> Iterable[Any]:
        return (
            raw_item.tpw,
            raw_item.supplier,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.category,
            raw_item.scheduled_delivery_date,
            raw_item.po_status,
            raw_item.assigned_buyer,
            raw_item.po_buyer,
        )


# TODO: refactor this export to classes
@dataclasses.dataclass
class ExceptionContext:
    item: FeedDataInput
    error_statuses: list[PimtExceptionType]
    skus: dict[SKU_CODE, SkuMeta]
    po_sku_combinations: dict[tuple[ORDER_NUMBER, SKU_CODE], InventoryPo]


def pimt_daily_report():
    wrong_inventory_data = process_pimt_inventory()
    grn_invalid_data = _process_grn_data()
    undelivered_data = _get_undelivered_data()
    file = _get_pimt_daily_report_file(wrong_inventory_data, undelivered_data, grn_invalid_data)
    email_export.send_pimt_exceptions_report(file)


def process_pimt_inventory() -> list[WrongInventoryData]:
    current_date = datetime.today()
    inventory_data, count_records_in_partners = _get_inventory_data()

    invalid_data, partner_count_invalid_records = _get_invalid_data(inventory_data)
    count_of_exceptions_in_partners = _get_count_of_exceptions_in_partners(invalid_data, count_records_in_partners)
    stats = _get_stats(count_of_exceptions_in_partners, count_records_in_partners, partner_count_invalid_records)
    market = request_context.get_request_context().market
    exceptions_rep.insert_data(current_date, stats, market)
    pimt_inventory_data = _remove_duplicates(invalid_data)
    cached_invalid_inventory_data = CACHES.invalid_pimt_inventory_data.get() or {}
    fill_wrong_error_date(pimt_inventory_data, cached_invalid_inventory_data)
    CACHES.invalid_pimt_inventory_data.put(pimt_inventory_data)
    return list(pimt_inventory_data.values())


def _process_grn_data() -> list[WrongGrnData]:
    grn_data = {(item.po_number, item.sku_code): item for item in _get_grn_exceptions_data()}
    cached_data = CACHES.invalid_pimt_grn_data.get() or {}
    fill_wrong_error_date(grn_data, cached_data)
    CACHES.invalid_pimt_grn_data.put(grn_data)
    return list(grn_data.values())


KEY = TypeVar("KEY")
VALUE = TypeVar("VALUE", WrongInventoryData, WrongGrnData)


def fill_wrong_error_date(db_data: dict[KEY, VALUE], cache_data: dict[KEY, VALUE]) -> None:
    today = date.today()
    for key, item in db_data.items():
        cache_item = cache_data.get(key)
        item.error_date = cache_item.error_date if cache_item else today


def _get_invalid_data(inventory_data: list[FeedDataInput]) -> tuple[list[WrongInventoryData], dict[str, int]]:
    inventory_pos, inventory_skus = _get_pos_and_skus(inventory_data)

    po_sku_combinations = _get_po_number_sku_code_from_ot(inventory_pos)
    culinary_by_sku_codes = culinary_sku.get_sku_meta_by_code(site=None, sku_codes=frozenset(inventory_skus))
    grn_lines_date_mapping = _get_grn(inventory_pos)
    replenishment_info = master_replenishment.get_master_replenishment()

    return _calculate_invalid_data(
        inventory_data, po_sku_combinations, grn_lines_date_mapping, culinary_by_sku_codes, replenishment_info
    )


def _get_pos_and_skus(data: list[FeedDataInput]) -> tuple[set[PO_NUMBER], set[SKU_CODE]]:
    pos = set()
    skus = set()
    for item in data:
        pos.add(item.order_number)
        skus.add(item.sku)
    pos.discard(None)
    skus.discard(None)
    return pos, skus


def _get_inventory_data():
    inventory_data = []
    count_records_in_partners = {}
    whs = partner_repo.get_all_warehouses(request_context.get_request_context().market)
    for wh in whs:
        if wh.is_e2open_inventory:
            records = _get_today_e2open_inventory_data(wh)
        else:
            records = get_inventory_feed_data(wh, PimtInventoryDataModel(wh.code))
        count_records_in_partners[wh.code] = len(records)
        inventory_data.extend(records)
    return inventory_data, count_records_in_partners


def _get_today_e2open_inventory_data(wh: Warehouse) -> list[FeedDataInput]:
    e2open_inventory_data = []

    for item in inventory.get_e2_open_unified_inventory_for_export(wh_code=wh.code, date_from=date.today()):
        expiration_date = datetime.combine(item.expiration_date, datetime.min.time()) if item.expiration_date else None
        e2open_inventory_data.append(
            FeedDataInput(
                sku=item.sku_code,
                order_number=item.order_number,
                po_number=item.po_number,
                partner=wh.code,
                is_e2open_grn=wh.is_e2open_grn,
                lot_code=item.lot_code,
                expiration_date=expiration_date,
            )
        )
    return e2open_inventory_data


def get_inventory_feed_data(wh: Warehouse, model: PimtInventoryDataModel) -> list[FeedDataInput]:
    rows = gsheet_admin.read_gsheet(model, is_formatted=False)
    inventory_feed_data = []
    for row in rows:
        try:
            if utils.is_green_chef_sku(row.sku_code):
                continue
            inventory_feed_data.append(
                FeedDataInput(
                    sku=row.sku_code,
                    order_number=utils.get_order_number_from_po_number(row.po_number),
                    po_number=row.po_number,
                    partner=wh.code,
                    is_e2open_grn=wh.is_e2open_grn,
                    lot_code=row.lot,
                    expiration_date=row.expiration_date,
                    invalid_date=_catch_date_errors(row),
                )
            )
        except (ValueError, IndexError) as exc:
            warnings.notify_sheet_error(row, exc, f"Error while importing {wh.code} Data", skipped=True)
    return inventory_feed_data


def _catch_date_errors(row):
    for error in row.meta.errors:
        if error["field"] == "expiration_date":
            error_message = error["error"].args[0]
            if "Cannot parse value" in error_message:
                return template_parse.parse(DATE_ERROR_TEMPLATE, error_message)["value"]
    return None


def _get_po_number_sku_code_from_ot(inventory_pos: set[str]) -> dict[tuple[str, str], InventoryPo]:
    return {(item.order_number, item.sku_code): item for item in pimt_pos.get_inventory_po_sku(inventory_pos)}


def _get_grn(inventory_pos: set[str]) -> dict[tuple[ORDER_NUMBER, SKU_CODE], datetime]:
    grn_lines = grn_repo.get_grn_receive_date_by_pos(inventory_pos, warehouses=partner_service.get_all_partners())
    return {(i.order_number, i.sku_code): i.receive_date for i in grn_lines}


def _calculate_invalid_data(
    inventory_data: list[FeedDataInput],
    po_sku_combinations: dict[tuple[ORDER_NUMBER, SKU_CODE], InventoryPo],
    grn_lines_date_mapping: dict[tuple[ORDER_NUMBER, SKU_CODE], datetime],
    skus: dict[SKU_CODE, SkuMeta],
    replenishment_info: MasterReplenishment,
) -> tuple[list[WrongInventoryData], dict]:
    wrong_inventory_data = []
    partner_count_invalid_records = defaultdict(int)
    order_numbers = set(item[0] for item in po_sku_combinations)

    for item in inventory_data:
        context = ExceptionContext(item=item, error_statuses=[], skus=skus, po_sku_combinations=po_sku_combinations)

        sku_name = _fill_error_statuses_by_po_sku_error(context, order_numbers)
        expiration_date = _fill_error_statuses_by_expiration_date_error(
            context, grn_lines_date_mapping, replenishment_info
        )

        if context.error_statuses:
            partner_count_invalid_records[item.partner] += 1
            for status in context.error_statuses:
                _add_wrong_inventory_item(
                    wrong_inventory_data,
                    item,
                    status.value,
                    sku_name,
                    expiration_date,
                )

    return wrong_inventory_data, partner_count_invalid_records


def _fill_error_statuses_by_po_sku_error(context: ExceptionContext, order_numbers: set[PO_NUMBER]) -> SKU_NAME:
    po_sku_combination = context.item.order_number, context.item.sku
    if po_sku_combination in context.po_sku_combinations:
        context.item.supplier = context.po_sku_combinations[po_sku_combination].supplier

    sku_name = ""
    is_wrong_sku = False

    if context.item.sku not in context.skus:
        context.error_statuses.append(PimtExceptionType.INVALID_SKU)
        is_wrong_sku = True
    elif sku_meta := context.skus.get(context.item.sku):
        sku_name = sku_meta.sku_name

    if not context.item.order_number:
        context.error_statuses.append(PimtExceptionType.MISSING_PO)
    elif context.item.order_number not in order_numbers:
        context.error_statuses.append(PimtExceptionType.INVALID_PO_NUMBER)
    elif po_sku_combination not in context.po_sku_combinations and not is_wrong_sku:
        context.error_statuses.append(PimtExceptionType.INVALID_PO_SKU_COMBINATION)

    return sku_name


def _fill_error_statuses_by_expiration_date_error(
    context: ExceptionContext,
    grn_lines_date_mapping: dict[tuple[ORDER_NUMBER, SKU_CODE], datetime],
    replenishment_info: MasterReplenishment,
):
    expiration_date = context.item.expiration_date
    invalid_date = context.item.invalid_date
    po_sku_combination = context.item.order_number, context.item.sku
    po_item = context.po_sku_combinations.get(po_sku_combination)
    grn_date_receive = grn_lines_date_mapping.get(po_sku_combination)
    sku_meta = context.skus.get(context.item.sku)
    is_protein = sku_meta and sku_meta.category == IngredientCategory.PROTEIN
    shelf_life = replenishment_info.shelf_life(context.item.sku, sku_meta.category if sku_meta else None)

    if sku_meta and sku_meta.category == IngredientCategory.PACKAGING:
        return expiration_date.strftime(GSHEET_DATE_FORMAT) if expiration_date else None

    if not context.item.expiration_date:
        if not invalid_date:
            context.error_statuses.append(PimtExceptionType.MISSING_EXPIRATION_DATE)
    elif context.item.is_e2open_grn:
        if grn_date_receive:
            _fill_expiration_date_errors(
                expiration_date,
                # TODO: replace this conversion when all expiration dates would be tz aware
                grn_date_receive.astimezone(APP_TIMEZONE).replace(tzinfo=None),
                is_protein,
                context.error_statuses,
                shelf_life,
            )
    elif po_item:
        if po_item.delivery_time_start:
            _fill_expiration_date_errors(
                expiration_date, po_item.delivery_time_start, is_protein, context.error_statuses, shelf_life
            )
    expiration_date = expiration_date.strftime(GSHEET_DATE_FORMAT) if expiration_date else None
    if invalid_date:
        if status := _get_invalid_date_statuses(invalid_date):
            context.error_statuses.append(status)
        expiration_date = invalid_date
    return expiration_date


def _get_invalid_date_statuses(invalid_date: str) -> PimtExceptionType | None:
    if not utils.is_valid_date(invalid_date):
        return PimtExceptionType.INVALID_EXPIRATION_DATE
    if not _is_valid_date_format(invalid_date):
        return PimtExceptionType.INCORRECT_FORMAT_OF_EXPIRATION_DATE
    return None


def _fill_expiration_date_errors(
    expiration_date: datetime,
    date_receive: datetime,
    is_protein: bool,
    error_statuses: list[PimtExceptionType],
    shelf_life: int | None,
) -> list[PimtExceptionType]:
    if expiration_date <= date_receive:
        error_statuses.append(PimtExceptionType.EARLIER_THEN_RECEIVE_EXPIRATION_DATE)
    elif is_protein and shelf_life and expiration_date > date_receive + timedelta(days=shelf_life):
        error_statuses.append(PimtExceptionType.EXPIRATION_DATE_TOO_HIGH)
    elif is_protein and expiration_date.date() < date_receive.date() + relativedelta(
        months=RECEIVE_EXPIRATION_PROTEIN_RANGE_MONTHS
    ):
        error_statuses.append(PimtExceptionType.INVALID_PROTEIN_EXPIRATION_DATE)
    return error_statuses


def _is_valid_date_format(date_: datetime | str):
    if isinstance(date_, datetime):
        date_ = str(date_)
    try:
        datetime.strptime(date_, GSHEET_DATE_FORMAT)
    except ValueError:
        return False
    return True


def _add_wrong_inventory_item(
    wrong_inventory_data: list,
    item: FeedDataInput,
    exception_reason: str,
    sku_name: SKU_NAME | None,
    expiration_date: str,
):
    wrong_inventory_data.append(
        WrongInventoryData(
            partner=item.partner,
            po_number=item.po_number,
            sku_name=sku_name,
            sku_code=item.sku,
            lot_code=item.lot_code,
            supplier=item.supplier,
            expiration_date=expiration_date,
            exception_reason=exception_reason,
        )
    )


def _get_pimt_daily_report_file(
    invalid_data: list[WrongInventoryData],
    undelivered_data: list[UndeliveredData],
    grn_invalid_data: list[WrongGrnData],
) -> bytes:
    return (
        ExportWorkbook()
        .add_sheet(ExportSheet(PimtExceptionsExportSheetData(invalid_data)))
        .add_sheet(ExportSheet(PimtUndeliveredSheetData(undelivered_data)))
        .add_sheet(ExportSheet(PimtGrnExceptionsExportSheetData(grn_invalid_data)))
        .binary
    )


def _get_stats(
    count_of_exceptions_in_partners: dict[str, dict],
    count_records_in_partners: dict[str, int],
    partner_count_invalid_records: dict[str, int],
):
    return {
        "version": 1,
        "stats": {
            partner_name: {
                "invalid_records": partner_count_invalid_records.get(partner_name, 0),
                "total_records": records,
                "exceptions": count_of_exceptions_in_partners.get(partner_name, {}),
            }
            for partner_name, records in count_records_in_partners.items()
        },
    }


def _remove_duplicates(
    invalid_data: list[WrongInventoryData],
) -> dict[tuple[PO_NUMBER, SKU_CODE, LOT_CODE, str], WrongInventoryData]:
    return {(item.po_number, item.sku_code, item.lot_code, item.exception_reason): item for item in invalid_data}


def get_daily_exception_metrics(week_from: ScmWeek, week_to: ScmWeek) -> list[ExceptionMetricsData]:
    if week_from > week_to:
        raise ValueError("week_from should be less than week_to")
    date_form, date_to = week_from.get_first_day(), week_to.get_last_day()
    market = request_context.get_request_context().market
    days_to_weeks = {}
    for week in ScmWeek.range(week_from, week_to):
        for day in week.week_days():
            days_to_weeks[day] = week
    return [
        ExceptionMetricsData.from_db_dict(item.t, days_to_weeks)
        for item in exceptions_rep.get_count_exceptions_data(date_form, date_to, market)
    ]


def _get_count_of_exceptions_in_partners(
    wrong_inventory_data: list[WrongInventoryData], count_records_in_partners: dict[str, int]
):
    count_of_exceptions_in_partners = {partner: defaultdict(int) for partner in count_records_in_partners.keys()}
    for item in wrong_inventory_data:
        if item.exception_reason in (
            PimtExceptionType.INCORRECT_FORMAT_OF_EXPIRATION_DATE,
            PimtExceptionType.INVALID_EXPIRATION_DATE,
            PimtExceptionType.EARLIER_THEN_RECEIVE_EXPIRATION_DATE,
            PimtExceptionType.MISSING_EXPIRATION_DATE,
            PimtExceptionType.INVALID_PROTEIN_EXPIRATION_DATE,
            PimtExceptionType.EXPIRATION_DATE_TOO_HIGH,
        ):
            count_of_exceptions_in_partners[item.partner][PimtExceptionType.INVALID_OR_MISSING_EXPIRATION_DATE] += 1
            continue
        count_of_exceptions_in_partners[item.partner][item.exception_reason] += 1

    return count_of_exceptions_in_partners


def _get_undelivered_data() -> list[UndeliveredData]:
    current_week = ScmWeek.current_week()
    partners_list = partner_service.get_all_partners(e2open_grn=True)
    po_data = []
    for week in ScmWeek.range(current_week - 2, current_week + 1):
        for partner_obj in partners_list:
            po_data.extend(purchase_order.get_po_data_by_week(weeks=[week], wh=partner_obj))
    return _build_undelivered_data(po_data)


def _build_undelivered_data(po_data: list[purchase_order.PoStatusData]) -> list[UndeliveredData]:
    undelivered_data = [
        UndeliveredData(
            tpw=po_item.partner,
            supplier=po_item.supplier,
            po_number=po_item.po_number,
            sku_code=po_item.sku_code,
            sku_name=po_item.sku_name,
            category=po_item.category,
            scheduled_delivery_date=po_item.scheduled_delivery_date,
            po_status=po_item.po_status,
            assigned_buyer=po_item.buyer,
            po_buyer=po_item.po_buyer,
        )
        for po_item in po_data
        if po_item.po_status == NOT_DELIVERED_PAST_DUE
    ]
    return undelivered_data


def _get_grn_exceptions_data() -> list[WrongGrnData]:
    grn_data = [
        FeedDataInput(
            order_number=r.order_number,
            sku=r.sku_code,
            partner=r.wh_code,
            units_received=r.units_received,
            delivery_time=r.receipt_time_est,
        )
        for r in grn_repo.get_grn_by_record_date(date.today(), warehouses=partner_service.get_all_partners())
    ]

    grn_pos, grn_skus = _get_pos_and_skus(grn_data)

    po_sku_combinations = _get_po_number_sku_code_from_ot(grn_pos)
    skus = culinary_sku.get_sku_meta_by_code(site=None, sku_codes=frozenset(grn_skus))

    order_numbers = set(item[0] for item in po_sku_combinations)

    wrong_grn_data = []

    for item in grn_data:
        context = ExceptionContext(item=item, error_statuses=[], skus=skus, po_sku_combinations=po_sku_combinations)
        sku_name = _fill_error_statuses_by_po_sku_error(context, order_numbers)

        _fill_error_statuses_by_delivery_time_error(item, context.error_statuses)

        if context.error_statuses:
            for status in context.error_statuses:
                wrong_grn_data.append(
                    WrongGrnData(
                        partner=item.partner,
                        po_number=item.order_number,
                        sku_code=item.sku,
                        sku_name=sku_name,
                        units=item.units_received,
                        delivery_time=item.delivery_time,
                        exception_reason=status.value,
                    )
                )
    return wrong_grn_data


def _fill_error_statuses_by_delivery_time_error(item, error_statuses):
    if not item.delivery_time:
        error_statuses.append(PimtExceptionType.INVALID_DELIVERY_TIME)
