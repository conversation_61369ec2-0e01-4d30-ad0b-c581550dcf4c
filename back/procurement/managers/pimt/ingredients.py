from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.sku import IngredientDetails
from procurement.repository.pimt import partner
from procurement.repository.pimt import sku as sku_repo


@request_cache
def get_ingredient_details(
    sku_code: SKU_CODE = None, non_inventory_sku_codes: frozenset[SKU_CODE] = None
) -> tuple[IngredientDetails, ...]:
    """
    Returns ingredient details for PIMT Inventory SKUs.
    :param sku_code: if you need only one specific SKU.
    :param non_inventory_sku_codes: if other than Inventory SKUs should be fetched specify them here.
    :return:
    """
    sites = partner.get_sites()
    res = sku_repo.get_ingredient_details(sku_codes=[sku_code] if sku_code else None, sites=sites)
    if non_inventory_sku_codes:
        if missing_skus := non_inventory_sku_codes - {sku.sku_code for sku in res}:
            res.extend(sku_repo.get_ingredient_details(sku_codes=missing_skus, sites=sites))
    return tuple(res)
