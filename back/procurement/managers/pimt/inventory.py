import dataclasses
import logging
from collections import defaultdict
from datetime import date, datetime, timedelta
from decimal import Decimal
from functools import cached_property
from typing import NamedTuple

from procurement.constants.hellofresh_constant import (
    NOT_TPW_SOURCE,
    TPW_SOURCE,
    ExpirationStatusEnum,
    HjInventoryState,
    InventoryInputType,
    InventoryState,
)
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.db_errors import HjOperationalError
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import PO_NUMBER, SITE, SKU_CODE, SKU_NAME, UNITS, WH_CODE
from procurement.data.dto.inventory.unified_inventory import UnifiedExpiredInventory
from procurement.data.dto.pimt.packaging_info import PackagingInfo
from procurement.data.dto.pimt.sku import IngredientDetails
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.forecasts import forecast_manager
from procurement.managers.forecasts.forecast_manager import ForecastData
from procurement.managers.imt import buyers as buyer_manager
from procurement.managers.models import ExpirationInfo
from procurement.managers.pimt import ingredients
from procurement.managers.pimt import master_replenishment as master_replenishment_service
from procurement.managers.pimt import packaging_info, partners
from procurement.managers.pimt.models import MasterReplenishment
from procurement.repository.highjump import HjPalletSnapshotData, pallet_snapshot
from procurement.repository.inventory import inventory
from procurement.repository.pimt import inventory as pimt_inventory_repo

logger = logging.getLogger(__name__)


@dataclasses.dataclass(slots=True)
class InventoryDashboard:
    po_number: PO_NUMBER
    sku_code: SKU_CODE
    sku_name: SKU_NAME
    lot: str
    partner: str
    supplier: str
    supplier_code: str | None
    cases: int
    case_price: Decimal
    case_size: UNITS
    total_units: UNITS
    receive_date: datetime | None
    purchasing_category: str
    commodity_group: list[str | None]
    shelf_life: int
    expiration_date: date | None
    days_until_expiration: int | None
    buyer: str
    unit_price: Decimal
    next_week_used: ScmWeek
    forecast: Decimal | None
    source: str
    cost_if_discarded: Decimal
    distinction: str
    is_expired: bool
    pallet_quantity: UNITS
    state: InventoryState = InventoryState.AVAILABLE


class InventoryContext:
    def __init__(self, wh_by_code: dict[str, Warehouse], sku_code: str | None):
        self.sku_code = sku_code
        self.wh_by_code = wh_by_code

        self.sites = set()
        for wh in wh_by_code.values():
            self.sites.update(wh.regional_dcs)

        self.sites_dto = []
        for brand in brand_admin.get_brand_ids():
            for site in dc_admin.get_enabled_sites(week=ScmWeek.current_week(), brand=brand).values():
                if site.sheet_name in self.sites:
                    self.sites_dto.append(site)

        self.today = date.today()

    @cached_property
    def master_replenishment(self) -> MasterReplenishment:
        return master_replenishment_service.get_master_replenishment()

    @cached_property
    def buyers_by_region_sku(self) -> dict[tuple[str, SKU_CODE], str]:
        return buyer_manager.get_buyers_by_site_sku(sites=self.sites)

    @cached_property
    def forecast_data_by_sku_code_site(self) -> dict[tuple[SKU_CODE, SITE], ForecastData]:
        forecast = forecast_manager.get_forecast_manager()
        return forecast.get_next_existing_forecast_data(sku_code=self.sku_code, sites=self.sites_dto)

    @cached_property
    def ingredient_details(self) -> dict[SKU_CODE, IngredientDetails]:
        return {s.sku_code: s for s in ingredients.get_ingredient_details(sku_code=self.sku_code)}

    @cached_property
    def packaging_info(self) -> dict[SKU_CODE, PackagingInfo]:
        return packaging_info.get_packaging_info()


class SkuCodeWarehouse(NamedTuple):
    sku_code: SKU_CODE
    wh: WH_CODE


@dataclasses.dataclass
class SkuExpirationInfo:
    receive_date: datetime | None
    expiration_date: date
    expiration_source: str
    days_until: int | None
    distinction: str
    is_expired: bool


@request_cache
def get_expiring_inventory_report(
    sku_code: SKU_CODE = None, available_inv_only: bool = True, warehouses: frozenset[WH_CODE] = None
) -> list[InventoryDashboard]:
    wh_by_code = {wh.code: wh for wh in partners.get_all_partners() if warehouses is None or wh.code in warehouses}

    context = InventoryContext(wh_by_code=wh_by_code, sku_code=sku_code)

    res = []
    for row in _get_inventories(sku_code=sku_code, available_inv_only=available_inv_only, warehouses=warehouses):
        if row.wh_code in wh_by_code and (
            (sku := context.ingredient_details.get(row.sku_code)) and not sku.is_packaging
        ):
            res.append(_build_inventory_item(context, row, sku))
    return res


@request_cache
def _get_inventories(
    sku_code: SKU_CODE = None, available_inv_only: bool = True, warehouses: frozenset[str] = None
) -> tuple[UnifiedExpiredInventory, ...]:
    whs = [wh for wh in partners.get_all_partners() if warehouses is None or wh.code in warehouses]

    return pimt_inventory_repo.get_expiring_inventory_report_data(
        whs, sku_code=sku_code, available_inv_only=available_inv_only
    )


def _build_inventory_item(
    context: InventoryContext, row: UnifiedExpiredInventory, sku: IngredientDetails
) -> InventoryDashboard:
    wh = context.wh_by_code[row.wh_code]
    sku_shelf_life = context.master_replenishment.shelf_life(
        sku_code=sku.sku_code, category=sku.purchasing_category, sites=frozenset(wh.regional_dcs)
    )

    sku_expiration_info = _build_sku_expiration_info(row, context.today, sku_shelf_life, wh)

    total_forecast = 0
    min_week_forecast = None
    for forecast_data in filter(
        None, (context.forecast_data_by_sku_code_site.get((sku.sku_code, site)) for site in wh.regional_dcs)
    ):
        total_forecast += forecast_data.forecast
        min_week_forecast = utils.min_of_two(min_week_forecast, forecast_data.scm_week)

    min_week_forecast = None if min_week_forecast == datetime.max else min_week_forecast
    unit_price = row.case_price / row.case_size if row.case_size and row.case_price else Decimal()
    wh_buyers = set()
    for site in wh.regional_dcs:
        if buyer := context.buyers_by_region_sku.get((site, sku.sku_code)):
            wh_buyers.add(buyer)

    buyers = ",".join(wh_buyers)
    pck_info = context.packaging_info.get(sku.sku_code)

    return InventoryDashboard(
        po_number=row.po_number,
        sku_code=sku.sku_code,
        sku_name=sku.sku_name,
        lot=row.lot_code,
        partner=row.wh_code,
        supplier=row.supplier,
        supplier_code=row.supplier_code,
        cases=row.cases,
        case_price=row.case_price,
        case_size=row.case_size,
        total_units=row.total,
        receive_date=sku_expiration_info.receive_date,
        commodity_group=sku.commodity_groups,
        purchasing_category=sku.purchasing_category,
        shelf_life=sku_shelf_life,
        expiration_date=sku_expiration_info.expiration_date,
        days_until_expiration=sku_expiration_info.days_until,
        buyer=buyers,
        unit_price=unit_price,
        next_week_used=min_week_forecast,
        forecast=total_forecast if total_forecast else None,
        source=sku_expiration_info.expiration_source,
        cost_if_discarded=unit_price * row.total,
        distinction=sku_expiration_info.distinction,
        is_expired=sku_expiration_info.is_expired,
        pallet_quantity=row.cases * pck_info.cases_per_pallet if pck_info else row.cases,
        state=InventoryState.AVAILABLE if row.state == InventoryState.AVAILABLE else row.state,
    )


def _build_sku_expiration_info(
    row: UnifiedExpiredInventory, today: date, sku_shelf_life: int, wh: Warehouse
) -> SkuExpirationInfo:
    receive_date = row.grn_delivery_time_start or row.po_delivery_time_start
    sku_delta = timedelta(days=sku_shelf_life) if sku_shelf_life is not None else None
    expiration_date, source = _get_expiration_date_and_source(row, sku_delta, wh)
    days_until_expiration = (expiration_date - today).days if expiration_date else None

    return SkuExpirationInfo(
        receive_date=receive_date,
        expiration_date=expiration_date,
        expiration_source=source,
        days_until=days_until_expiration,
        distinction=_get_distinction(days_until_expiration),
        is_expired=days_until_expiration is not None and days_until_expiration <= 0,
    )


def _get_expiration_date_and_source(
    row: UnifiedExpiredInventory, shelf_life: timedelta | None, wh: Warehouse
) -> tuple[date, str]:
    expiration_date = (
        row.inventory_expiration_date.date()
        if isinstance(row.inventory_expiration_date, datetime)
        else row.inventory_expiration_date
    )
    if expiration_date:
        return expiration_date, TPW_SOURCE
    if shelf_life is not None:
        if wh.is_e2open_grn and row.grn_delivery_time_start:
            expiration_date = row.grn_delivery_time_start.date() + shelf_life
        elif row.po_delivery_time_start:
            expiration_date = row.po_delivery_time_start.date() + shelf_life
    return expiration_date, NOT_TPW_SOURCE if expiration_date else None


def _get_distinction(days_until: int | None) -> str:
    if days_until is None:
        return ""
    if days_until <= 0:
        return ExpirationStatusEnum.EXPIRED
    if days_until <= 30:
        return ExpirationStatusEnum.LESS_THAN_30
    if days_until <= 60:
        return ExpirationStatusEnum.LESS_THAN_60
    return ExpirationStatusEnum.MORE_THAN_60


def update_wh_hj_inventory():
    whs = partners.get_hj_inventory_warehouses()
    if not whs:
        return
    updates = {w.code: [] for w in whs}
    wh_code_map = {w.hj_name: w.code for w in whs}
    try:
        for row in pallet_snapshot.get_hj_pallet_snapshot(wh_ids=[w.hj_name for w in whs], available_only=False):
            updates[wh_code_map[row.wh_id]].append(row)
    except HjOperationalError as err:
        warnings.notify_warning(message=f"HJ is unavailable. Reason: {err.__cause__}")
        raise
    for wh_code, pimt_inventory in updates.items():
        _update_hj_inv_table(wh_code, pimt_inventory)


def _update_hj_inv_table(wh_code: WH_CODE, updates: list[HjPalletSnapshotData]):
    if not updates:
        warnings.notify_warning(message=f"No inventory records for '{wh_code}'! Keeping old inventory")
        return
    snapshot_timestamp = datetime.now()
    inventory.delete_inventory_by_warehouse_snapshot_timestamp(
        wh_codes=[wh_code], inventory_type=InventoryInputType.HJ, snapshot_timestamp=snapshot_timestamp
    )
    inventory.insert_inventory(
        [
            {
                UnifiedInventoryModel.sku_code: item.sku_code,
                UnifiedInventoryModel.po_number: item.po_number,
                UnifiedInventoryModel.order_number: utils.get_order_number_from_po_number(item.po_number),
                UnifiedInventoryModel.unit_quantity: item.pallet_quantity,
                UnifiedInventoryModel.lot_code: item.license_plate,
                UnifiedInventoryModel.wh_code: wh_code,
                UnifiedInventoryModel.expiration_date: item.expiration_date,
                UnifiedInventoryModel.inventory_type: InventoryInputType.HJ,
                UnifiedInventoryModel.inventory_status: HjInventoryState.to_unified_state(item.status),
                UnifiedInventoryModel.snapshot_timestamp: snapshot_timestamp,
                UnifiedInventoryModel.location_id: item.location_id,
            }
            for item in updates
            if item.sku_code
        ]
    )


def get_expiring_in(days: int) -> dict[SkuCodeWarehouse, ExpirationInfo]:
    today = date.today()
    master_replenishment = master_replenishment_service.get_master_replenishment()
    ingredient_details = {s.sku_code: s for s in ingredients.get_ingredient_details()}
    wh_by_code = {wh.code: wh for wh in partners.get_all_partners()}

    res = defaultdict(ExpirationInfo)
    for row in _get_inventories():
        if sku_info := ingredient_details.get(row.sku_code):
            wh = wh_by_code[row.wh_code]
            sku_shelf_life = master_replenishment.shelf_life(
                sku_code=sku_info.sku_code,
                category=sku_info.purchasing_category,
                sites=frozenset(wh.regional_dcs),
            )
            sku_expiration_info = _build_sku_expiration_info(row, today, sku_shelf_life, wh)

            if sku_expiration_info.days_until is not None and 0 < sku_expiration_info.days_until <= days:
                key = SkuCodeWarehouse(row.sku_code, row.wh_code)
                if key in res:
                    res[key].total += row.total
                    res[key].min_expiration_date = utils.min_of_two(
                        res[key].min_expiration_date, sku_expiration_info.expiration_date
                    )
                    continue
                res[key] = ExpirationInfo(total=row.total, min_expiration_date=sku_expiration_info.expiration_date)
    return res


def get_expiring_inventory_data_by_sku_wh(
    available_inv_only: bool = True, warehouses: frozenset[str] = None
) -> dict[tuple[SKU_CODE, WH_CODE], list[InventoryDashboard]]:
    aging_data = get_expiring_inventory_report(available_inv_only=available_inv_only, warehouses=warehouses)
    res = defaultdict(list)
    for item in aging_data:
        res[(item.sku_code, item.partner)].append(item)
    return res


def delete_old_inventory_records():
    wh_codes = [wh.code for wh in partners.get_all_partners()]
    inventory.delete_old_inventory_records(wh_codes)
