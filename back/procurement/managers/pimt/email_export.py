import logging
from datetime import datetime

from procurement.client.mailing import mail_service
from procurement.core.config_utils import config

logger = logging.getLogger(__name__)


def send_pimt_exceptions_report(file: bytes) -> None:
    mail_service.send_mail(
        mailing_config=config["mailing"]["pimt"]["exceptions-report"],
        file=file,
        formatters={"date": datetime.today().strftime("%m/%d/%Y")},
    )


def send_pimt_3pw_monthly_report(file: bytes, **kwargs) -> None:
    mail_service.send_mail(mailing_config=config["mailing"]["pimt"]["monthly-3pw-report"], file=file, formatters=kwargs)
