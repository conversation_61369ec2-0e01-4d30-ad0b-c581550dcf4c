from collections.abc import Iterable
from functools import cached_property

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.imt.packaging_inventory import LinerGroupEnum

from .base import SafetyStockItem, WeeklyStockItem
from .context import REGIONAL_TOTAL_OWNERSHIP, SafetyStockContext


class DemandLevelSafetyStockItem(SafetyStockItem):
    def __init__(self, demand_pipeline: str, region: str, items: list[SafetyStockItem], context: SafetyStockContext):
        self._demand_pipeline = demand_pipeline
        self._region = region
        self.sku_items = AggregatedData[list[SafetyStockItem], SafetyStockItem](items)
        self.context = context

    def get_child_items(self) -> Iterable[SafetyStockItem]:
        return (child for item in self.sku_items.raw for child in item.get_child_items())

    @cached_property
    def liner_group(self) -> LinerGroupEnum:
        return self.sku_items.any_item(lambda item: item.liner_group)

    @cached_property
    def forecast_by_week(self) -> dict[ScmWeek, UNITS]:
        return self.sku_items.any_item(lambda item: item.forecast_by_week)

    @cached_property
    def item_name(self) -> str:
        return self.demand_pipeline

    @cached_property
    def demand_pipeline(self) -> str | None:
        return self.sku_items.any_item(lambda item: item.demand_pipeline)

    @cached_property
    def packaging_region(self) -> str:
        return self._region

    @cached_property
    def sku_codes(self) -> set[str]:
        return self.sku_items.union(lambda item: item.sku_codes)

    @cached_property
    def sku_name(self) -> None:
        return None

    @cached_property
    def category(self) -> str:
        return self.sku_items.any_item(lambda item: item.category)

    @cached_property
    def ownership(self) -> str:
        return REGIONAL_TOTAL_OWNERSHIP

    @cached_property
    def units_per_truck_load(self) -> int:
        return self.sku_items.min_item(lambda item: item.units_per_truck_load, default=0)

    @cached_property
    def weekly_items(self) -> list[WeeklyStockItem]:
        items = [self._build_first_week()]

        first_sku: list[WeeklyStockItem] = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)

        for weekly_item in utils.iskip(first_sku, 1):
            prev_item = items[-1]
            items.append(self._build_next_week(weekly_item, prev_item, current_week_idx=len(items)))

        return items

    @property
    def has_any_active_week(self) -> bool:
        return any(
            w.hf_owned_boh > 0
            or w.vendor_owned_boh > 0
            or w.live_demand
            or w.incoming_pos
            or w.outbound_pos
            or w.vendor_pos_to_dc
            for w in self.weekly_items
        )

    def _build_first_week(self) -> WeeklyStockItem:
        sku_items = self.sku_items.raw

        first_sku = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)[0]

        first_item = WeeklyStockItem(
            week=first_sku.week,
            hf_owned_boh=first_sku.hf_owned_boh,
            vendor_owned_boh=first_sku.vendor_owned_boh,
            vendor_pos_to_dc=first_sku.vendor_pos_to_dc,
            live_demand=first_sku.live_demand,
            incoming_pos=first_sku.incoming_pos,
            hf_outbound_pos=first_sku.hf_outbound_pos,
            vendor_outbound_pos=first_sku.vendor_outbound_pos,
            units_per_truck_load=first_sku.units_per_truck_load,
            avg_forecast=first_sku.avg_forecast,
            hf_planned_depletion=first_sku.hf_planned_depletion,
            vendor_planned_depletion=first_sku.vendor_planned_depletion,
        )

        for sku_item in utils.iskip(sku_items, 1):
            first_week = sku_item.weekly_items[0]
            first_item.hf_owned_boh += first_week.hf_owned_boh
            first_item.vendor_owned_boh += first_week.vendor_owned_boh
            first_item.incoming_pos += first_week.incoming_pos
            first_item.hf_outbound_pos += first_week.hf_outbound_pos
            first_item.vendor_outbound_pos += first_week.vendor_outbound_pos
            first_item.live_demand += first_week.live_demand
            first_item.avg_forecast += first_week.avg_forecast
            first_item.units_per_truck_load += first_week.units_per_truck_load
            first_item.vendor_pos_to_dc += first_week.vendor_pos_to_dc
            first_item.hf_planned_depletion += first_week.hf_planned_depletion
            first_item.vendor_planned_depletion += first_week.vendor_planned_depletion

        first_item.units_per_truck_load = (
            first_item.units_per_truck_load / len(sku_items) if first_item.units_per_truck_load else None
        )
        return first_item

    def _build_next_week(
        self, current_item: WeeklyStockItem, prev_item: WeeklyStockItem, current_week_idx: int
    ) -> WeeklyStockItem:
        sku_items = self.sku_items.raw

        aggregated_item = WeeklyStockItem(
            week=current_item.week,
            hf_owned_boh=prev_item.get_next_hf_owned_boh(),
            vendor_owned_boh=prev_item.get_next_vendor_owned_boh(),
            live_demand=current_item.live_demand,
            incoming_pos=current_item.incoming_pos,
            hf_outbound_pos=current_item.hf_outbound_pos,
            vendor_outbound_pos=current_item.vendor_outbound_pos,
            units_per_truck_load=current_item.units_per_truck_load,
            avg_forecast=current_item.avg_forecast,
            vendor_pos_to_dc=current_item.vendor_pos_to_dc,
            hf_planned_depletion=current_item.hf_planned_depletion,
            vendor_planned_depletion=current_item.vendor_planned_depletion,
        )

        for item in utils.iskip(sku_items, 1):
            current_week = item.weekly_items[current_week_idx]
            aggregated_item.incoming_pos += current_week.incoming_pos
            aggregated_item.hf_outbound_pos += current_week.hf_outbound_pos
            aggregated_item.vendor_outbound_pos += current_week.vendor_outbound_pos
            aggregated_item.units_per_truck_load += current_week.units_per_truck_load
            aggregated_item.vendor_pos_to_dc += current_week.vendor_pos_to_dc
            aggregated_item.hf_planned_depletion += current_week.hf_planned_depletion
            aggregated_item.vendor_planned_depletion += current_week.vendor_planned_depletion

        aggregated_item.units_per_truck_load = (
            aggregated_item.units_per_truck_load / len(sku_items) if aggregated_item.units_per_truck_load else None
        )

        return aggregated_item
