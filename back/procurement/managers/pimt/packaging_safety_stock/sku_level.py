from collections.abc import Iterable
from decimal import Decimal
from functools import cached_property

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.imt.packaging_inventory import LinerGroupEnum

from .base import SafetyStockItem, WeeklyStockItem
from .context import REGIONAL_TOTAL_OWNERSHIP, SafetyStockContext
from .supplier_level import SupplierLevelSafetyStockItem


class SkuLevelSafetyStockItem(SafetyStockItem):
    def __init__(
        self, sku_code: str, region: str, sku_items: list[SupplierLevelSafetyStockItem], context: SafetyStockContext
    ):
        self.sku_code = sku_code
        self.region = region
        self.sku_items = AggregatedData[list[SupplierLevelSafetyStockItem], SupplierLevelSafetyStockItem](sku_items)
        self.context = context

    def get_child_items(self) -> Iterable[SafetyStockItem]:
        return self.sku_items.raw

    @cached_property
    def liner_group(self) -> LinerGroupEnum:
        return self.sku_items.any_item(lambda item: item.liner_group)

    @cached_property
    def item_name(self) -> str:
        return self.sku_name

    @cached_property
    def forecast_by_week(self) -> dict[ScmWeek, UNITS]:
        return self.sku_items.any_item(lambda item: item.forecast_by_week)

    @cached_property
    def demand_pipeline(self) -> str | None:
        return self.sku_items.any_item(lambda item: item.demand_pipeline)

    @cached_property
    def sku_codes(self) -> set[str]:
        return {self.sku_code}

    @cached_property
    def packaging_region(self) -> str:
        return self.sku_items.any_item(lambda item: item.packaging_region)

    @cached_property
    def sku_name(self) -> str:
        return self.sku_items.any_item(lambda item: item.sku_name)

    @cached_property
    def category(self) -> str:
        return self.sku_items.any_item(lambda item: item.category)

    @cached_property
    def ownership(self) -> str:
        return REGIONAL_TOTAL_OWNERSHIP

    @cached_property
    def units_per_truck_load(self) -> int:
        return self.sku_items.min_item(lambda item: item.units_per_truck_load, default=0)

    @cached_property
    def weekly_items(self) -> list[WeeklyStockItem]:
        items = [self._build_first_week()]

        first_supplier_weeklies: list[WeeklyStockItem] = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)
        vendor_po_values = self.sku_items.sum_array(lambda it: [w.vendor_pos_to_dc for w in it.weekly_items])
        hf_planned_depletion_values = self.sku_items.sum_array(
            lambda it: [w.hf_planned_depletion for w in it.weekly_items]
        )
        vendor_planned_depletion_values = self.sku_items.sum_array(
            lambda it: [w.vendor_planned_depletion for w in it.weekly_items]
        )

        for week_idx, current_item in enumerate(utils.iskip(first_supplier_weeklies, 1), 1):
            prev_item = items[-1]
            items.append(
                self._build_next_week(
                    current_item,
                    prev_item,
                    vendor_po_values[week_idx],
                    hf_planned_depletion_values[week_idx],
                    vendor_planned_depletion_values[week_idx],
                )
            )

        return items

    def _build_first_week(self) -> WeeklyStockItem:
        first_supplier = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)[0]

        first_item = WeeklyStockItem(
            week=first_supplier.week,
            hf_owned_boh=0,
            vendor_owned_boh=0,
            live_demand=first_supplier.live_demand,
            incoming_pos=first_supplier.incoming_pos,
            outbound_pos=first_supplier.outbound_pos,
            units_per_truck_load=first_supplier.units_per_truck_load,
            avg_forecast=first_supplier.avg_forecast,
            vendor_pos_to_dc=0,
            hf_planned_depletion=Decimal(),
            vendor_planned_depletion=Decimal(),
        )

        for supplier_item in self.sku_items.raw:
            first_week = supplier_item.weekly_items[0]
            first_item.hf_owned_boh += first_week.hf_owned_boh
            first_item.vendor_owned_boh += first_week.vendor_owned_boh
            first_item.vendor_pos_to_dc += first_week.vendor_pos_to_dc
            first_item.hf_planned_depletion += first_week.hf_planned_depletion
            first_item.vendor_planned_depletion += first_week.vendor_planned_depletion

        return first_item

    @staticmethod
    def _build_next_week(
        current_item: WeeklyStockItem,
        prev_item: WeeklyStockItem,
        vendor_pos: int,
        hf_planned_depletion: Decimal,
        vendor_planned_depletion: Decimal,
    ) -> WeeklyStockItem:
        aggregated_item = WeeklyStockItem(
            week=current_item.week,
            hf_owned_boh=prev_item.get_next_hf_owned_boh(),
            vendor_owned_boh=prev_item.get_next_vendor_owned_boh(vendor_pos),
            live_demand=current_item.live_demand,
            incoming_pos=current_item.incoming_pos,
            outbound_pos=current_item.outbound_pos,
            units_per_truck_load=current_item.units_per_truck_load,
            avg_forecast=current_item.avg_forecast,
            vendor_pos_to_dc=vendor_pos,
            hf_planned_depletion=hf_planned_depletion,
            vendor_planned_depletion=vendor_planned_depletion,
        )

        return aggregated_item
