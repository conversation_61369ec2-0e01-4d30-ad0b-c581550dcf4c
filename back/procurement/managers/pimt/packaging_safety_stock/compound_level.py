import dataclasses
from collections.abc import Iterable
from functools import cached_property
from typing import Self

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.imt.packaging_inventory import LinerGroupEnum

from .base import SafetyStockItem, WeeklyStockItem
from .context import REGIONAL_TOTAL_OWNERSHIP, SafetyStockContext


class _AggregatedCompound(SafetyStockItem):
    def __init__(self, items: Iterable[SafetyStockItem]):
        items_iter = iter(items)
        item = next(items_iter)
        self._items = [item]
        self._liner_group = item.liner_group
        self._demand_pipeline = item.demand_pipeline
        self._item_name = item.item_name
        self._packaging_region = item.packaging_region
        self._sku_codes = item.sku_codes
        self._sku_name = item.sku_name
        self._category = item.category
        self._ownership = item.ownership
        self._forecast_by_week = dict(item.forecast_by_week)
        self._units_per_truck_load = item.units_per_truck_load
        self._weekly_items = list(map(dataclasses.replace, item.weekly_items))
        for it in items_iter:
            self.add(it)

    def add(self, item: SafetyStockItem) -> Self:
        for week, forecast in item.forecast_by_week.items():
            self._forecast_by_week[week] = self._forecast_by_week.get(week, 0) + forecast
        self._units_per_truck_load += item.units_per_truck_load
        for self_weekly, item_weekly in zip(self._weekly_items, item.weekly_items):
            self_weekly.hf_owned_boh += item_weekly.hf_owned_boh
            self_weekly.vendor_owned_boh += item_weekly.vendor_owned_boh
            self_weekly.live_demand += item_weekly.live_demand
            self_weekly.incoming_pos += item_weekly.incoming_pos
            self_weekly.hf_outbound_pos += item_weekly.hf_outbound_pos
            self_weekly.vendor_outbound_pos += item_weekly.vendor_outbound_pos
            self_weekly.units_per_truck_load += item_weekly.units_per_truck_load
            self_weekly.avg_forecast += item_weekly.avg_forecast
        self._items.append(item)
        return self

    def get_child_items(self) -> Iterable[SafetyStockItem]:
        return (child for it in self._items for child in it.get_child_items())

    @property
    def liner_group(self) -> str:
        return self._liner_group

    @property
    def demand_pipeline(self) -> str | None:
        return self._demand_pipeline

    @property
    def item_name(self) -> str:
        return self._item_name

    @property
    def packaging_region(self) -> str:
        return self._packaging_region

    @property
    def sku_codes(self) -> set[str]:
        return self._sku_codes

    @property
    def sku_name(self) -> str | None:
        return self._sku_name

    @property
    def category(self) -> str:
        return self._category

    @property
    def ownership(self) -> str:
        return self._ownership

    @property
    def forecast_by_week(self) -> dict[ScmWeek, UNITS]:
        return self._forecast_by_week

    @property
    def units_per_truck_load(self) -> int:
        return self._units_per_truck_load

    @property
    def weekly_items(self) -> list[WeeklyStockItem]:
        return self._weekly_items


class CompoundLevelSafetyStockItem(SafetyStockItem):
    def __init__(self, compound_name: str, items: list[SafetyStockItem], context: SafetyStockContext):
        self.compound_name = compound_name
        agg_compound = utils.group_by(items, lambda it: it.sku_code)
        _items = []
        for compounds in agg_compound.values():
            # GD-5369 - case when there are several same HF owned SKUs from different sites under one demand pipeline
            if len(compounds) > 1:
                _items.append(_AggregatedCompound(compounds))
            else:
                _items.append(compounds[0])

        self.sku_items = AggregatedData[list[SafetyStockItem], SafetyStockItem](_items)
        self.context = context

    @cached_property
    def item_name(self) -> str:
        return self.compound_name

    @cached_property
    def liner_group(self) -> LinerGroupEnum:
        return self.sku_items.any_item(lambda item: item.liner_group)

    def get_child_items(self) -> Iterable[SafetyStockItem]:
        return (child for item in self.sku_items.raw for child in item.get_child_items())

    @cached_property
    def forecast_by_week(self) -> dict[ScmWeek, UNITS]:
        return self.sku_items.any_item(lambda item: item.forecast_by_week)

    @cached_property
    def demand_pipeline(self) -> str | None:
        return self.sku_items.any_item(lambda item: item.demand_pipeline)

    @cached_property
    def packaging_region(self) -> str:
        return self.sku_items.any_item(lambda item: item.packaging_region)

    @cached_property
    def sku_codes(self) -> set[str]:
        return self.sku_items.union(lambda item: item.sku_codes)

    @cached_property
    def sku_name(self) -> None:
        return None

    @cached_property
    def category(self) -> str:
        return self.sku_items.any_item(lambda item: item.category)

    @cached_property
    def ownership(self) -> str:
        return REGIONAL_TOTAL_OWNERSHIP

    @cached_property
    def units_per_truck_load(self) -> int:
        return self.sku_items.min_item(lambda item: item.units_per_truck_load, default=0)

    @cached_property
    def weekly_items(self) -> list[WeeklyStockItem]:
        items = [self._build_first_week()]

        first_sku: list[WeeklyStockItem] = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)

        for index, weekly_item in enumerate(utils.iskip(first_sku, 1), 1):
            items.append(self._build_next_week(weekly_item, index))

        return items

    def _build_first_week(self) -> WeeklyStockItem:

        first_sku = self.sku_items.any_item(lambda sku_i: sku_i.weekly_items)[0]

        first_item = WeeklyStockItem(
            week=first_sku.week,
            hf_owned_boh=first_sku.hf_owned_boh,
            vendor_owned_boh=first_sku.vendor_owned_boh,
            live_demand=first_sku.live_demand,
            incoming_pos=first_sku.incoming_pos,
            hf_outbound_pos=first_sku.hf_outbound_pos,
            vendor_outbound_pos=first_sku.vendor_outbound_pos,
            units_per_truck_load=first_sku.units_per_truck_load,
            avg_forecast=first_sku.avg_forecast,
            vendor_pos_to_dc=first_sku.vendor_pos_to_dc,
            hf_planned_depletion=first_sku.hf_planned_depletion,
            vendor_planned_depletion=first_sku.vendor_planned_depletion,
        )

        for sku_item in utils.iskip(self.sku_items.raw, 1):
            first_week = sku_item.weekly_items[0]
            first_item.hf_owned_boh = min(first_item.hf_owned_boh, first_week.hf_owned_boh)
            first_item.vendor_owned_boh = min(first_item.vendor_owned_boh, first_week.vendor_owned_boh)
            first_item.incoming_pos = min(first_item.incoming_pos, first_week.incoming_pos)
            first_item.hf_outbound_pos = min(first_item.hf_outbound_pos, first_week.hf_outbound_pos)
            first_item.vendor_outbound_pos = min(first_item.vendor_outbound_pos, first_week.vendor_outbound_pos)
            first_item.live_demand = max(first_item.live_demand, first_week.live_demand)
            first_item.avg_forecast = max(first_item.avg_forecast, first_week.avg_forecast)
            first_item.units_per_truck_load = min(first_item.units_per_truck_load, first_week.units_per_truck_load)
            first_item.hf_planned_depletion = min(first_item.hf_planned_depletion, first_week.hf_planned_depletion)
            first_item.vendor_planned_depletion = min(
                first_item.vendor_planned_depletion, first_week.vendor_planned_depletion
            )

        return first_item

    def _build_next_week(self, current_item: WeeklyStockItem, current_week_idx: int) -> WeeklyStockItem:
        sku_items = self.sku_items.raw

        aggregated_item = WeeklyStockItem(
            week=current_item.week,
            hf_owned_boh=current_item.hf_owned_boh,
            vendor_owned_boh=current_item.vendor_owned_boh,
            live_demand=current_item.live_demand,
            incoming_pos=current_item.incoming_pos,
            hf_outbound_pos=current_item.hf_outbound_pos,
            vendor_outbound_pos=current_item.vendor_outbound_pos,
            units_per_truck_load=current_item.units_per_truck_load,
            avg_forecast=current_item.avg_forecast,
            vendor_pos_to_dc=current_item.vendor_pos_to_dc,
            hf_planned_depletion=current_item.hf_planned_depletion,
            vendor_planned_depletion=current_item.vendor_planned_depletion,
        )

        for item in utils.iskip(sku_items, 1):
            current_week = item.weekly_items[current_week_idx]
            aggregated_item.hf_owned_boh = min(aggregated_item.hf_owned_boh, current_week.hf_owned_boh)
            aggregated_item.vendor_owned_boh = min(aggregated_item.vendor_owned_boh, current_week.vendor_owned_boh)
            aggregated_item.incoming_pos = min(aggregated_item.incoming_pos, current_week.incoming_pos)
            aggregated_item.hf_outbound_pos = min(aggregated_item.hf_outbound_pos, current_week.hf_outbound_pos)
            aggregated_item.vendor_outbound_pos = min(
                aggregated_item.vendor_outbound_pos, current_week.vendor_outbound_pos
            )
            aggregated_item.units_per_truck_load = min(
                aggregated_item.units_per_truck_load, current_week.units_per_truck_load
            )
            aggregated_item.hf_planned_depletion = min(
                aggregated_item.hf_planned_depletion, current_week.hf_planned_depletion
            )
            aggregated_item.vendor_planned_depletion = min(
                aggregated_item.vendor_planned_depletion, current_week.vendor_planned_depletion
            )

        return aggregated_item
