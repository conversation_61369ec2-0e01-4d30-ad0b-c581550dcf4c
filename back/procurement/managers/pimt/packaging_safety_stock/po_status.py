import itertools

from procurement.constants.hellofresh_constant import PoType
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers import po_status_common
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.imt.purchase_order import po_status as imt_po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusResult
from procurement.managers.pimt import purchase_order as pimt_po_status

from .context import DASHBOARD_WEEKS_AHEAD, SafetyStockContext


def get_packaging_safety_stock_po_status(
    packaging_region: str, pimt_region: str, demand_pipeline: str
) -> list[PoStatusResult]:
    context_ = SafetyStockContext(pimt_region)
    skus = {
        item.sku_code
        for item in context_.demand_pipeline_sku_mapping.values()
        if item.demand_pipeline == demand_pipeline
    }
    warehouses = [wh for wh in context_.warehouses if wh.packaging_regions and packaging_region in wh.packaging_regions]
    if not warehouses:
        return []
    dcs = context_.dcs_by_pck_region.get(packaging_region, [])
    weeks = list(ScmWeek.range(ScmWeek.current_week(), ScmWeek.current_week() + DASHBOARD_WEEKS_AHEAD))

    result = [
        *_get_imt_po_status(dcs, weeks, skus),
        *_get_pimt_po_status(warehouses, weeks, skus),
    ]

    return result


def _get_imt_po_status(dcs: list[DcConfig], weeks: list[ScmWeek], skus: set[SKU_CODE]) -> list[PoStatusResult]:
    result = []
    dcs_by_brand = utils.group_by(dcs, lambda it: it.brand)
    for brand, dcs_ in dcs_by_brand.items():
        po_status = imt_po_status.get_po_status(brand=brand, sites=[dc.sheet_name for dc in dcs_], weeks=tuple(weeks))
        for item in itertools.chain.from_iterable(po_status.values()):
            if item.sku_code in skus:
                item.po_type = PoType.OUTBOUND
                result.append(item)
    return result


def _get_pimt_po_status(warehouses: list[Warehouse], weeks: list[ScmWeek], skus: set[SKU_CODE]) -> list[PoStatusResult]:
    result = []
    for wh in warehouses:
        po_status_result_items = po_status_common.convert_po_status_data_to_result(
            [item for item in pimt_po_status.get_po_data_by_week(weeks=weeks, wh=wh) if item.sku_code in skus]
        )
        for item in po_status_result_items:
            item.po_type = PoType.INBOUND
            result.append(item)
    return result
