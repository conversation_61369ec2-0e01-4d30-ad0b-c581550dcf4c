from collections import defaultdict

from procurement.managers.imt.packaging_inventory import CompoundPartEnum

from .compound_level import CompoundLevelSafetyStockItem
from .context import SafetyStockContext
from .demand_level import DemandLevelSafetyStockItem
from .sku_level import SkuLevelSafetyStockItem
from .supplier_level import HfOwnedSafetyStock, VendorOwnedSafetyStock


def get_safety_stock_dashboard(pimt_region: str) -> list[DemandLevelSafetyStockItem]:
    context = SafetyStockContext(pimt_region)

    items_by_demand_pipeline_region = defaultdict(list)
    compounds_by_sku_bob_code = defaultdict(list)

    for sku_code, pck_region in context.packaging_sku_import_data.keys():
        if sku_code not in context.demand_pipeline_sku_mapping or sku_code not in context.sku_meta_by_code:
            continue
        sku_items = []
        hf_managed_inventory = context.get_hf_managed_inventory(sku_code, pck_region)
        sku_items.append(HfOwnedSafetyStock(sku_code, pck_region, hf_managed_inventory, context))

        for inventory in context.vendor_managed_inventory.get((sku_code, pck_region), {}):
            sku_items.append(VendorOwnedSafetyStock(sku_code, pck_region, inventory, context))

        sku_item = SkuLevelSafetyStockItem(sku_code, pck_region, sku_items, context)

        if part := CompoundPartEnum.get_compound_part(sku_item.sku_name):
            compounds_by_sku_bob_code[(sku_item.sku_name.replace(part, ""), pck_region)].append(sku_item)
        else:
            items_by_demand_pipeline_region[(sku_item.demand_pipeline, sku_item.packaging_region)].append(sku_item)

    for (compound_name, pck_region), compounds in compounds_by_sku_bob_code.items():
        compound_item = CompoundLevelSafetyStockItem(compound_name, compounds, context)
        items_by_demand_pipeline_region[compound_item.demand_pipeline, compound_item.packaging_region].append(
            compound_item
        )

    return [
        DemandLevelSafetyStockItem(demand_pipeline, region, items, context)
        for (demand_pipeline, region), items in items_by_demand_pipeline_region.items()
    ]
