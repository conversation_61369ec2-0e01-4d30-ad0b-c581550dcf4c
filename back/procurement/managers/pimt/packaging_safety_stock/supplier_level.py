from abc import ABCMeta, abstractmethod
from collections.abc import Iterable
from decimal import Decimal
from functools import cached_property

from procurement.constants.hellofresh_constant import BRAND_HF
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.data.dto.inventory.demand_pipeline import DemandPipelineSkuMapping
from procurement.data.dto.inventory.vendor_managed_inventory import VendorManagedInventory
from procurement.data.dto.pimt.pakcaging_safety_stock import PlannedDepletionKey
from procurement.data.dto.sku import SkuMeta
from procurement.managers.imt.packaging_inventory import LinerGroupEnum

from .base import SafetyStockItem, WeeklyStockItem
from .context import DASHBOARD_WEEKS_AHEAD, OWNED_BY_TEMPlATE, SafetyStockContext


class SupplierLevelSafetyStockItem(SafetyStockItem, metaclass=ABCMeta):
    def __init__(self, sku_code: str, pck_region: str, context: SafetyStockContext, owner: str):
        self._sku_code = sku_code
        self._pck_region = pck_region
        self.context = context
        self._owner = owner

    def get_child_items(self) -> Iterable[SafetyStockItem]:
        return [self]

    @cached_property
    def item_name(self) -> str:
        return self.sku_name

    @cached_property
    def sku_meta(self) -> SkuMeta:
        return self.context.sku_meta_by_code.get(self._sku_code)

    @cached_property
    def _demand_pipeline_mapping(self) -> DemandPipelineSkuMapping:
        return self.context.demand_pipeline_sku_mapping.get(self._sku_code)

    @cached_property
    def demand_pipeline(self) -> str | None:
        return self._demand_pipeline_mapping.demand_pipeline

    @cached_property
    def sku_codes(self) -> set[str]:
        return {self._sku_code}

    @cached_property
    def packaging_region(self) -> str:
        return self._pck_region

    @cached_property
    def sku_name(self) -> str:
        return self.sku_meta.sku_name

    @cached_property
    def category(self) -> str:
        return self._demand_pipeline_mapping.sku_type

    @cached_property
    def ownership(self) -> str:
        return OWNED_BY_TEMPlATE.format(self._owner)

    @cached_property
    def units_per_truck_load(self) -> int:
        return self.context.packaging_sku_import_data[(self._sku_code, self.packaging_region)].units_per_truck or 0

    @cached_property
    def forecast_by_week(self):
        return self.context.long_term_forecasts_by_week.get((self.demand_pipeline, self.packaging_region), {})

    def get_forecasts(self, start_week: ScmWeek, end_week: ScmWeek) -> list[UNITS]:
        return list(filter(None, [self.forecast_by_week.get(week) for week in ScmWeek.range(start_week, end_week)]))

    @cached_property
    @abstractmethod
    def hf_owned_boh(self) -> int: ...

    @cached_property
    @abstractmethod
    def vendor_owned_boh(self) -> int: ...

    @abstractmethod
    def get_vendor_pos_to_dc(self, week: ScmWeek) -> int: ...

    @abstractmethod
    def get_inbounds(self, week: ScmWeek) -> Decimal: ...

    @abstractmethod
    def get_hf_outbounds(self, week: ScmWeek) -> Decimal: ...

    @abstractmethod
    def get_vendor_outbounds(self, week: ScmWeek) -> Decimal: ...

    @abstractmethod
    def get_hf_planned_depletion(self, week: ScmWeek) -> Decimal: ...

    @abstractmethod
    def get_vendor_planned_depletion(self, week: ScmWeek) -> Decimal: ...

    @cached_property
    def weekly_items(self) -> list[WeeklyStockItem]:
        current_demand = self.context.in_week_demand.get(self.packaging_region, {}).get(self._sku_code, 0)
        forecasts = self.get_forecasts(self.context.current_week + 1, self.context.current_week + 3)
        items = [
            WeeklyStockItem(
                week=self.context.current_week,
                hf_owned_boh=self.hf_owned_boh,
                vendor_owned_boh=self.vendor_owned_boh,
                live_demand=current_demand,
                incoming_pos=self.get_inbounds(self.context.current_week),
                hf_outbound_pos=self.get_hf_outbounds(self.context.current_week),
                vendor_outbound_pos=self.get_vendor_outbounds(self.context.current_week),
                units_per_truck_load=self.units_per_truck_load,
                avg_forecast=Decimal(current_demand + sum(forecasts)) / (len(forecasts) + 1),
                vendor_pos_to_dc=self.get_vendor_pos_to_dc(self.context.current_week),
                hf_planned_depletion=self.get_hf_planned_depletion(self.context.current_week),
                vendor_planned_depletion=self.get_vendor_planned_depletion(self.context.current_week),
            )
        ]

        for week in ScmWeek.range(self.context.current_week + 1, self.context.current_week + DASHBOARD_WEEKS_AHEAD):
            prev_item = items[-1]
            forecasts = self.get_forecasts(week, week + 4)
            vendor_pos = self.get_vendor_pos_to_dc(week)
            items.append(
                WeeklyStockItem(
                    week=week,
                    hf_owned_boh=prev_item.get_next_hf_owned_boh(),
                    vendor_owned_boh=prev_item.get_next_vendor_owned_boh(),
                    live_demand=int(self.forecast_by_week.get(week, Decimal())),
                    incoming_pos=self.get_inbounds(week),
                    hf_outbound_pos=self.get_hf_outbounds(week),
                    vendor_outbound_pos=self.get_vendor_outbounds(week),
                    units_per_truck_load=self.units_per_truck_load,
                    avg_forecast=Decimal(sum(forecasts)) / len(forecasts) if forecasts else Decimal(),
                    vendor_pos_to_dc=vendor_pos,
                    hf_planned_depletion=self.get_hf_planned_depletion(week),
                    vendor_planned_depletion=self.get_vendor_planned_depletion(week),
                )
            )
        return items

    @cached_property
    def liner_group(self) -> LinerGroupEnum:
        return LinerGroupEnum.map_sku_name_to_liner_group(self.context.market, self.sku_name)


class HfOwnedSafetyStock(SupplierLevelSafetyStockItem):
    def __init__(self, sku_code: str, pck_region: str, inventory: UNITS, context: SafetyStockContext):
        self._inventory = inventory
        super().__init__(sku_code, pck_region, context, owner=BRAND_HF)

    @cached_property
    def hf_owned_boh(self) -> int:
        return self._inventory

    @cached_property
    def vendor_owned_boh(self) -> int:
        return 0

    def get_vendor_pos_to_dc(self, week: ScmWeek) -> int:
        # always 0 since we will have this value in vendor rows
        return 0

    def get_inbounds(self, week: ScmWeek) -> Decimal:
        return self.context.get_inbounds(self._sku_code, self.packaging_region, week)

    def get_hf_outbounds(self, week: ScmWeek) -> Decimal:
        return self.context.get_outbounds(self._sku_code, self.packaging_region, week)

    def get_vendor_outbounds(self, week: ScmWeek) -> Decimal:
        return Decimal()

    def get_hf_planned_depletion(self, week: ScmWeek) -> Decimal:
        if week == self.context.current_week:
            return Decimal()
        return self.context.planned_depletion.get(
            PlannedDepletionKey(week, self.packaging_region, self.demand_pipeline, self._sku_code, self._owner),
            Decimal(),
        )

    def get_vendor_planned_depletion(self, week: ScmWeek) -> Decimal:
        return Decimal()


class VendorOwnedSafetyStock(SupplierLevelSafetyStockItem):
    def __init__(
        self, sku_code: str, pck_region: str, vendor_inventory: VendorManagedInventory, context: SafetyStockContext
    ):
        super().__init__(sku_code, pck_region, context, owner=vendor_inventory.supplier_name)
        self.vendor_inventory = vendor_inventory

    @cached_property
    def hf_owned_boh(self) -> int:
        return 0

    @cached_property
    def vendor_owned_boh(self) -> int:
        return self.vendor_inventory.units + self.vendor_inventory.inbound

    def get_vendor_pos_to_dc(self, week: ScmWeek) -> int:
        key = (self._owner, self.packaging_region, week, self._sku_code)
        return round(self.context.supplier_outbound_pos.get(key, Decimal()))

    def get_inbounds(self, week: ScmWeek) -> Decimal:
        return Decimal()

    def get_hf_outbounds(self, week: ScmWeek) -> Decimal:
        return Decimal()

    def get_vendor_outbounds(self, week: ScmWeek) -> Decimal:
        return Decimal(self.vendor_inventory.outbound) if week == self.context.current_week else Decimal()

    def get_hf_planned_depletion(self, week: ScmWeek) -> Decimal:
        return Decimal()

    def get_vendor_planned_depletion(self, week: ScmWeek) -> Decimal:
        return self.context.planned_depletion.get(
            PlannedDepletionKey(week, self.packaging_region, self.demand_pipeline, self._sku_code, self._owner),
            Decimal(),
        )
