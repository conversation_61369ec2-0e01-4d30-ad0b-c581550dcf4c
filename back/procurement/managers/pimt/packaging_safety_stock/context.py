import itertools
from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context as request_context
from procurement.core.typing import BOB_CODE, DEMAND_PIPELINE, PCK_REGION, SKU_CODE, SUPPLIER, UNITS, WH_CODE
from procurement.data.dto.inventory.demand_pipeline import DemandPipelineSkuMapping
from procurement.data.dto.inventory.packaging_sku_import import PackagingSkuImportData
from procurement.data.dto.inventory.vendor_managed_inventory import VendorManagedInventory
from procurement.data.dto.pimt.pakcaging_safety_stock import PlannedDepletionKey
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.dto.sku import SkuMeta
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.pimt import ops_purchase_order, partners
from procurement.managers.sku import culinary_sku
from procurement.repository.forecast import packaging_forecast
from procurement.repository.inventory import demand_pipeline_sku_mapping as demand_pipeline_repo
from procurement.repository.inventory import packaging_depletion, packagins_sku_import, vendor_managed_inventory
from procurement.repository.ordering.purchase_order import pimt_pos
from procurement.repository.pimt import inventory as inventory_repo
from procurement.repository.pimt import planned_depletion

OWNED_BY_TEMPlATE = "{} Owned"
REGIONAL_TOTAL_OWNERSHIP = "Regional Total"
DASHBOARD_WEEKS_AHEAD = 7


class SafetyStockContext:
    def __init__(self, pimt_region: str):
        self.pimt_region = pimt_region
        self.market = request_context.get_request_context().market
        # For packaging Factor also uses Wed-Wed production week, so we can use default week config for any DC
        self.current_week = ScmWeek.current_week()

    @cached_property
    def warehouses(self) -> list[Warehouse]:
        return [wh for wh in partners.get_all_partners() if wh.region == self.pimt_region]

    @cached_property
    def registered_suppliers(self) -> set[str]:
        return {s for wh in partners.get_all_partners() for s in wh.ot_suppliers}

    @cached_property
    def suppliers(self) -> set[SUPPLIER]:
        return {item.supplier_name for item in self._vendor_managed_inventory}

    @cached_property
    def packaging_regions(self) -> set[PCK_REGION]:
        return set(itertools.chain.from_iterable(wh.packaging_regions or [] for wh in self.warehouses))

    @cached_property
    def wh_code_by_packaging_region(self) -> dict[PCK_REGION, list[WH_CODE]]:
        wh_by_region = defaultdict(list)
        for warehouse in self.warehouses:
            for packaging_region in warehouse.packaging_regions or []:
                wh_by_region[packaging_region].append(warehouse.code)
        return wh_by_region

    @cached_property
    def dcs_by_pck_region(self) -> dict[PCK_REGION, list[DcConfig]]:
        sites_by_gsheet_code = utils.group_by(dc_admin.get_all_market_dcs(), lambda it: it.sheet_name)
        pck_region_sites = defaultdict(set)
        for wh in self.warehouses:
            for pck_region in wh.packaging_regions or []:
                pck_region_sites[pck_region].update(wh.regional_dcs)
        res = defaultdict(list)
        for pck_region, sites in pck_region_sites.items():
            res[pck_region].extend(itertools.chain.from_iterable(filter(None, map(sites_by_gsheet_code.get, sites))))
        return res

    @cached_property
    def pck_region_by_bob_code(self) -> dict[BOB_CODE, PCK_REGION]:
        return {dc.bob_code: reg for reg, dcs in self.dcs_by_pck_region.items() for dc in dcs}

    @cached_property
    def demand_pipeline_sku_mapping(self) -> dict[SKU_CODE, DemandPipelineSkuMapping]:
        return {
            item.sku_code: item for item in demand_pipeline_repo.get_packaging_demand_pipeline_sku_mapping(self.market)
        }

    @cached_property
    def sku_meta_by_code(self) -> dict[SKU_CODE, SkuMeta]:
        return culinary_sku.get_sku_meta_by_code(site=None, sku_codes=frozenset(self.demand_pipeline_sku_mapping))

    @cached_property
    def _vendor_managed_inventory(self) -> list[VendorManagedInventory]:
        return vendor_managed_inventory.get_vendor_managed_inventory(self.market, self.packaging_regions)

    @cached_property
    def vendor_managed_inventory(self) -> dict[tuple[SKU_CODE, PCK_REGION], list[VendorManagedInventory]]:
        inventories = defaultdict(list)
        for item in self._vendor_managed_inventory:
            inventories[(item.sku_code, item.region)].append(item)
        return inventories

    @cached_property
    def packaging_sku_import_data(self) -> dict[tuple[SKU_CODE, PCK_REGION], PackagingSkuImportData]:
        return {
            (item.sku_code, item.bob_code): item
            for item in packagins_sku_import.get_packaging_sku_import_data(self.packaging_regions)
        }

    @cached_property
    def in_week_demand(self) -> dict[PCK_REGION, dict[SKU_CODE, int]]:
        sku_demand_by_pck_region = defaultdict(lambda: defaultdict(int))
        for pck_region in self.packaging_regions:
            dcs = self.dcs_by_pck_region[pck_region]
            for dc in dcs:
                demands = packaging_depletion.get_packaging_demand(
                    weeks=[self.current_week], site=dc.sheet_name, brand=dc.brand
                )
                for item in demands:
                    sku_demand_by_pck_region[pck_region][item.sku_code] += sum(item.demand_by_day)
        return sku_demand_by_pck_region

    @cached_property
    def long_term_forecasts_by_week(self) -> dict[tuple[DEMAND_PIPELINE, PCK_REGION], dict[ScmWeek, UNITS]]:
        site_to_pck_region = {
            (dc.sheet_name, dc.brand): reg for reg, dcs in self.dcs_by_pck_region.items() for dc in dcs
        }
        forecasts = defaultdict(lambda: defaultdict(int))
        for item in packaging_forecast.get_forecasts_by_demand_pipeline(self.weeks):
            key = (item.site, item.brand)
            if key in site_to_pck_region:
                forecasts[(item.demand_pipeline, site_to_pck_region[key])][item.week] += item.forecast
        return forecasts

    @cached_property
    def hf_managed_inventory(self) -> dict[SKU_CODE, dict[WH_CODE, UNITS]]:
        inventory = defaultdict(dict)
        for item in inventory_repo.get_unified_inventory_in_house([wh.code for wh in self.warehouses]):
            inventory[item.sku_code][item.wh_code] = item.total
        return inventory

    def get_hf_managed_inventory(self, sku_code: str, packaging_region: str) -> UNITS:
        packaging_region_warehouses = self.wh_code_by_packaging_region[packaging_region]
        return sum(
            inventory
            for wh, inventory in self.hf_managed_inventory.get(sku_code, {}).items()
            if wh in packaging_region_warehouses
        )

    @cached_property
    def weeks(self) -> list[ScmWeek]:
        current_week = ScmWeek.current_week()
        return list(ScmWeek.range(current_week - 1, current_week + DASHBOARD_WEEKS_AHEAD + 1))

    @cached_property
    def inbounds_by_week(self) -> dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]:
        return ops_purchase_order.get_po_inbound_by_weeks(self.weeks)

    def get_inbounds(self, sku_code: str, packaging_region: str, week: ScmWeek):
        inbounds = Decimal()
        for wh_code in self.wh_code_by_packaging_region[packaging_region]:
            inbounds += self.inbounds_by_week[wh_code].get(sku_code, {}).get(week, Decimal())
        return inbounds

    @cached_property
    def outbounds_by_week(self) -> dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]:
        return ops_purchase_order.get_po_outbound_by_weeks(self.weeks)

    def get_outbounds(self, sku_code: str, packaging_region: str, week: ScmWeek):
        outbounds = Decimal()
        for wh_code in self.wh_code_by_packaging_region[packaging_region]:
            outbounds += self.outbounds_by_week[wh_code].get(sku_code, {}).get(week, Decimal())

        return outbounds

    @cached_property
    def supplier_outbound_pos(self) -> dict[tuple[SUPPLIER, PCK_REGION, ScmWeek, SKU_CODE], Decimal]:
        pos_by_key = defaultdict(Decimal)
        for item in pimt_pos.get_supplier_outbound_pos(
            suppliers=self.suppliers - self.registered_suppliers,
            bob_codes=self.pck_region_by_bob_code.keys(),
            weeks=self.weeks,
        ):
            pck_region = self.pck_region_by_bob_code.get(item.bob_code)
            if pck_region:
                pos_by_key[(item.supplier, pck_region, item.week, item.sku_code)] += item.quantity
        return pos_by_key

    @cached_property
    def planned_depletion(self) -> dict[PlannedDepletionKey, Decimal]:
        return planned_depletion.get_planned_depletion(self.weeks, self.packaging_regions)
