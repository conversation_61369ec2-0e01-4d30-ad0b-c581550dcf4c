from __future__ import annotations

import dataclasses
from abc import ABC, abstractmethod
from collections.abc import Iterable
from decimal import Decimal
from functools import cached_property

from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.imt.packaging_inventory import LinerGroupEnum


@dataclasses.dataclass
class WeeklyStockItem:
    week: ScmWeek
    hf_owned_boh: int
    vendor_owned_boh: int
    vendor_pos_to_dc: int
    live_demand: int
    incoming_pos: Decimal
    hf_outbound_pos: Decimal
    vendor_outbound_pos: Decimal
    units_per_truck_load: int
    avg_forecast: Decimal
    hf_planned_depletion: Decimal
    vendor_planned_depletion: Decimal

    @property
    def total_inventory(self) -> int:
        return self.hf_owned_boh + self.vendor_owned_boh

    @property
    def outbound_pos(self) -> Decimal:
        return self.hf_outbound_pos or self.vendor_outbound_pos

    @cached_property
    def truck_loads(self) -> Decimal:
        if self.units_per_truck_load:
            return round((self.total_inventory + self.incoming_pos) / Decimal(self.units_per_truck_load), 1)
        return Decimal()

    @cached_property
    def weeks_on_hand(self) -> Decimal:
        return (
            round((self.total_inventory + self.incoming_pos) / self.avg_forecast, 1) if self.avg_forecast else Decimal()
        )

    def get_next_hf_owned_boh(self) -> int:
        return round(self.hf_owned_boh - self.hf_outbound_pos + self.incoming_pos - self.hf_planned_depletion)

    def get_next_vendor_owned_boh(self) -> int:
        return round(
            self.vendor_owned_boh - self.vendor_pos_to_dc - self.vendor_planned_depletion - self.vendor_outbound_pos
        )


class SafetyStockItem(ABC):
    @abstractmethod
    def get_child_items(self) -> Iterable[SafetyStockItem]: ...

    @cached_property
    @abstractmethod
    def demand_pipeline(self) -> str | None: ...

    @cached_property
    @abstractmethod
    def item_name(self) -> str: ...

    @cached_property
    @abstractmethod
    def packaging_region(self) -> str: ...

    @cached_property
    @abstractmethod
    def sku_codes(self) -> set[str]: ...

    @cached_property
    @abstractmethod
    def sku_name(self) -> str | None: ...

    @cached_property
    @abstractmethod
    def category(self) -> str: ...

    @cached_property
    @abstractmethod
    def ownership(self) -> str: ...

    @cached_property
    @abstractmethod
    def forecast_by_week(self) -> dict[ScmWeek, UNITS]: ...

    @cached_property
    @abstractmethod
    def units_per_truck_load(self) -> int: ...

    @cached_property
    @abstractmethod
    def weekly_items(self) -> list[WeeklyStockItem]: ...

    @cached_property
    @abstractmethod
    def liner_group(self) -> LinerGroupEnum: ...
