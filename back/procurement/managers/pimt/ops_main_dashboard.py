import itertools
from collections.abc import Iterable
from dataclasses import dataclass
from datetime import date
from decimal import Decimal

from procurement.client.slack import alerts
from procurement.client.slack.alerts import PimtInventoryOutdated
from procurement.constants.hellofresh_constant import InventoryState
from procurement.core.typing import BRAND, SKU_CODE, UNITS, WH_CODE
from procurement.managers.admin import brand_admin
from procurement.managers.pimt import ingredients as ingredient_service
from procurement.managers.pimt import inventory as inventory_service
from procurement.managers.pimt import ops_purchase_order as ops_service
from procurement.managers.pimt import partners as partner_service
from procurement.managers.pimt.inventory import InventoryDashboard
from procurement.managers.pimt.ops_purchase_order import PimtPoData
from procurement.repository.pimt import inventory


@dataclass
class SkuPartnerInventory:
    site: str
    total_on_hand: UNITS
    available: UNITS
    units_on_hold: UNITS
    units_booked_for_outbound: UNITS
    inbound: Decimal
    outbound: Decimal
    expired_inventory: UNITS | None


@dataclass
class SkuTotal:
    total_on_hand: UNITS
    available: UNITS
    units_on_hold: UNITS
    units_booked_for_outbound: UNITS
    inbound: Decimal
    outbound: Decimal
    expired_inventory: UNITS


@dataclass
class SkuInventory:
    sku_code: str
    sku_name: str
    category: str
    commodity_group: list[str]
    ops: dict[WH_CODE, SkuPartnerInventory]
    total: SkuTotal
    brands: list[BRAND] | None = None


@dataclass
class AggregatedInventory:
    site: WH_CODE
    updated_date: date
    is_outdated_date: bool
    in_house_cost: Decimal
    total_on_hand_units: UNITS


@dataclass
class TotalItem:
    cost: Decimal
    units_count: UNITS


@dataclass
class TotalDetails:
    value: Decimal
    total_on_hand: TotalItem
    available: UNITS
    units_on_hold: UNITS
    units_booked_for_outbound: UNITS
    inbound: UNITS
    outbound: UNITS
    expired_inventory: UNITS
    inventory_updated: date
    is_outdated_date: bool


@dataclass
class OpsMainDashboardModel:
    items: list[SkuInventory]
    aggr: list[AggregatedInventory]
    total: TotalDetails


@dataclass
class OpsWarehouseData:
    in_house: UNITS = 0
    total_price: Decimal = Decimal()
    available: UNITS = 0
    units_on_hold: UNITS = 0
    units_booked_for_outbound: UNITS = 0


@dataclass
class OpsItem:
    sku_code: SKU_CODE
    ops: dict[WH_CODE, OpsWarehouseData]
    total_in_house: UNITS = 0
    total_available: UNITS = 0
    total_on_hold: UNITS = 0
    total_booked_for_outbound: UNITS = 0


@dataclass
class OpsTotal:
    total_in_house: UNITS = 0
    total_cost: Decimal = Decimal()
    total_available: UNITS = 0
    total_on_hold: UNITS = 0
    total_booked_for_outbound: UNITS = 0


@dataclass
class OpsInHouseDashboard:
    items: dict[SKU_CODE, OpsItem]
    aggr: dict[WH_CODE, OpsTotal]
    total: OpsTotal


@dataclass
class ExpiredItem:
    expired_by_wh: dict[WH_CODE, int]
    total_expired: int = 0


@dataclass
class ExpiredItemDashboard:
    items: dict[SKU_CODE, ExpiredItem]
    total_by_wh: dict[WH_CODE, int]
    total: int


def get_expired_dashboard_items(
    expired_inventory_data: dict[tuple[SKU_CODE, WH_CODE], list[InventoryDashboard]],
) -> ExpiredItemDashboard:
    expired_items_by_sku = {}
    whs = {wh.code for wh in partner_service.get_all_partners()}
    today = date.today()

    for (sku_code, wh_code), items in expired_inventory_data.items():
        if wh_code not in whs:
            continue
        if sku_code not in expired_items_by_sku:
            expired_items_by_sku[sku_code] = ExpiredItem(expired_by_wh={wh: 0 for wh in whs})
        item = expired_items_by_sku[sku_code]
        expired_items = _calculate_expired_items(items, today)
        item.expired_by_wh[wh_code] += expired_items
        item.total_expired += expired_items
    total_by_wh = _build_total_expired_items_by_wh(expired_items_by_sku.values())
    return ExpiredItemDashboard(
        expired_items_by_sku,
        total_by_wh,
        sum(total for total in total_by_wh.values()),
    )


def _build_total_expired_items_by_wh(expired_items: Iterable[ExpiredItem]) -> dict[WH_CODE, int]:
    totals = {}
    for wh in partner_service.get_all_partners():
        totals[wh.code] = sum(itertools.chain(item.expired_by_wh[wh.code] for item in expired_items))
    return totals


def _calculate_expired_items(expiring_inventory_data: list[InventoryDashboard], today: date) -> UNITS:
    return sum(
        item.total_units for item in expiring_inventory_data if item.expiration_date and item.expiration_date <= today
    )


def get_in_house_dashboard_items(
    expired_inventory_data: dict[tuple[SKU_CODE, WH_CODE], list[InventoryDashboard]],
) -> OpsInHouseDashboard:
    ops_item_by_sku = {}
    whs = {wh.code for wh in partner_service.get_all_partners()}
    today = date.today()

    for (sku_code, wh_code), items in expired_inventory_data.items():
        if wh_code not in whs:
            continue
        if sku_code not in ops_item_by_sku:
            ops_item_by_sku[sku_code] = OpsItem(
                sku_code=sku_code,
                ops={partner: OpsWarehouseData() for partner in whs},
            )
        in_house = _calculate_in_house(items, today)
        available = _calculate_quantity(items, InventoryState.AVAILABLE)
        units_on_hold = _calculate_quantity(items, InventoryState.ON_HOLD)
        units_booked_for_outbound = _calculate_quantity(items, InventoryState.BOOKED_FOR_OUTBOUND)

        item = ops_item_by_sku[sku_code]
        item.ops[wh_code].in_house += in_house
        item.ops[wh_code].available += available
        item.ops[wh_code].units_on_hold += units_on_hold
        item.ops[wh_code].units_booked_for_outbound += units_booked_for_outbound
        item.ops[wh_code].total_price += _calculate_total_price(items, today)

        item.total_in_house += in_house
        item.total_available += available
        item.total_on_hold += units_on_hold
        item.total_booked_for_outbound += units_booked_for_outbound
    partners_total = _build_partners_total(ops_item_by_sku.values())
    return OpsInHouseDashboard(ops_item_by_sku, partners_total, _build_dashboard_total(partners_total))


def _calculate_in_house(expiring_inventory_data: list[InventoryDashboard], today: date) -> UNITS:
    return sum(
        item.total_units for item in expiring_inventory_data if item.expiration_date and item.expiration_date > today
    )


def _calculate_quantity(expiring_inventory_data: list[InventoryDashboard], state: InventoryState) -> UNITS:
    return sum(item.total_units for item in expiring_inventory_data if item.state == state)


def _calculate_total_price(expiring_inventory_data: list[InventoryDashboard], today: date) -> Decimal:
    return sum(
        item.total_units * item.unit_price
        for item in expiring_inventory_data
        if item.expiration_date and item.expiration_date > today and item.cases and item.case_price
    )


def _build_partners_total(items: Iterable[OpsItem]) -> dict[WH_CODE, OpsTotal]:
    totals = {}
    for wh in partner_service.get_all_partners():
        partner_code = wh.code
        total = OpsTotal()
        for item in items:
            partners = item.ops
            total.total_cost += partners[partner_code].total_price
            total.total_in_house += partners[partner_code].in_house
            total.total_available += partners[partner_code].available
            total.total_on_hold += partners[partner_code].units_on_hold
            total.total_booked_for_outbound += partners[partner_code].units_booked_for_outbound
        totals[partner_code] = total
    return totals


def _build_dashboard_total(partner_totals: dict[str, OpsTotal]):
    ops_total = OpsTotal()
    for item in partner_totals.values():
        ops_total.total_in_house += item.total_in_house
        ops_total.total_cost += item.total_cost
        ops_total.total_available += item.total_available
        ops_total.total_on_hold += item.total_on_hold
        ops_total.total_booked_for_outbound += item.total_booked_for_outbound
    return ops_total


def get_sku_details(
    inventory_data: OpsInHouseDashboard,
    po_inbound: PimtPoData,
    po_outbound: PimtPoData,
    expired_inventory_dashboard: ExpiredItemDashboard = None,
) -> list[SkuInventory]:
    items = []
    non_inventory_sku_codes = frozenset(itertools.chain(po_inbound.wh_total, po_outbound.wh_total))
    brand_ids_by_name = brand_admin.get_brand_ids_by_name()
    for sku in ingredient_service.get_ingredient_details(non_inventory_sku_codes=non_inventory_sku_codes):
        if sku.is_unlabeled:
            continue
        sku_code = sku.sku_code
        sku_inventory = inventory_data.items.get(sku_code)
        if not expired_inventory_dashboard:
            expired_inventory_dashboard = ExpiredItemDashboard(items={}, total_by_wh={}, total=0)
        expired_inventory_item = expired_inventory_dashboard.items.get(sku_code)

        sku_total = SkuTotal(
            total_on_hand=sku_inventory.total_in_house if sku_inventory else 0,
            inbound=po_inbound.wh_total.get(sku_code) or Decimal(),
            outbound=po_outbound.wh_total.get(sku_code) or Decimal(),
            expired_inventory=expired_inventory_item.total_expired if expired_inventory_item else 0,
            available=sku_inventory.total_available if sku_inventory else 0,
            units_on_hold=sku_inventory.total_on_hold if sku_inventory else 0,
            units_booked_for_outbound=sku_inventory.total_booked_for_outbound if sku_inventory else 0,
        )

        if sku_total.total_on_hand or sku_total.inbound or sku_total.outbound or sku_total.expired_inventory:
            sku_details = SkuInventory(
                sku_code=sku.sku_code,
                sku_name=sku.sku_name,
                brands=[brand_ids_by_name[brand] for brand in sku.brands if brand in brand_ids_by_name],
                commodity_group=sku.commodity_groups,
                category=sku.purchasing_category,
                ops=_get_sku_partner_details(sku_code, sku_inventory, po_inbound, po_outbound, expired_inventory_item),
                total=sku_total,
            )
            items.append(sku_details)
    return items


def _get_sku_partner_details(
    sku_code: SKU_CODE,
    sku_inventory: OpsItem,
    po_inbound: PimtPoData,
    po_outbound: PimtPoData,
    expired_inventory_item: ExpiredItem,
) -> dict[WH_CODE, SkuPartnerInventory]:
    return {
        wh.name: SkuPartnerInventory(
            site=wh.name,
            total_on_hand=sku_inventory.ops[wh.code].in_house if sku_inventory else 0,
            inbound=po_inbound.by_wh[wh.code].get(sku_code) or Decimal(),
            outbound=po_outbound.by_wh[wh.code].get(sku_code) or Decimal(),
            expired_inventory=expired_inventory_item.expired_by_wh[wh.code] if expired_inventory_item else 0,
            available=sku_inventory.ops[wh.code].available if sku_inventory else 0,
            units_on_hold=sku_inventory.ops[wh.code].units_on_hold if sku_inventory else 0,
            units_booked_for_outbound=sku_inventory.ops[wh.code].units_booked_for_outbound if sku_inventory else 0,
        )
        for wh in partner_service.get_all_partners()
    }


def _get_aggregated_data(
    inventory_data: OpsInHouseDashboard, update_date_by_wh: dict[WH_CODE, date]
) -> list[AggregatedInventory]:
    aggr_items = []
    current_date = date.today()
    for wh in partner_service.get_all_partners():
        item = inventory_data.aggr[wh.code]
        updated_date = update_date_by_wh.get(wh.code)
        aggr = AggregatedInventory(
            site=wh.name,
            updated_date=updated_date,
            is_outdated_date=_get_is_outdated_date(updated_date, current_date),
            in_house_cost=item.total_cost,
            total_on_hand_units=item.total_in_house,
        )
        aggr_items.append(aggr)
    return aggr_items


def get_total(inventory_data, po_inbound, po_outbound, expired_inventory, updated_date_by_wh) -> TotalDetails:
    inventory_updated = min(filter(None, updated_date_by_wh.values()), default=None)
    return TotalDetails(
        value=inventory_data.total.total_cost,
        total_on_hand=TotalItem(cost=inventory_data.total.total_cost, units_count=inventory_data.total.total_in_house),
        available=inventory_data.total.total_available,
        units_on_hold=inventory_data.total.total_on_hold,
        units_booked_for_outbound=inventory_data.total.total_booked_for_outbound,
        inbound=po_inbound.total,
        outbound=po_outbound.total,
        expired_inventory=expired_inventory.total,
        inventory_updated=inventory_updated,
        is_outdated_date=_get_is_outdated_date(inventory_updated, date.today()),
    )


def _get_is_outdated_date(updated_date: date | None, current_date: date) -> bool:
    return updated_date and updated_date < current_date


def build_ops_main_dashboard():
    po_inbound = ops_service.get_po_inbound()
    po_outbound = ops_service.get_po_outbound()
    expired_inventory_data = inventory_service.get_expiring_inventory_data_by_sku_wh(available_inv_only=False)
    expired_inventory = get_expired_dashboard_items(expired_inventory_data)
    inventory_data = get_in_house_dashboard_items(expired_inventory_data)

    items = get_sku_details(inventory_data, po_inbound, po_outbound, expired_inventory)
    update_date_by_wh = _get_update_date_by_wh()
    aggr_items = _get_aggregated_data(inventory_data, update_date_by_wh)
    total = get_total(inventory_data, po_inbound, po_outbound, expired_inventory, update_date_by_wh)

    return OpsMainDashboardModel(items=items, aggr=aggr_items, total=total)


def _get_update_date_by_wh() -> dict[WH_CODE, date]:
    wh_codes = [wh.code for wh in partner_service.get_all_partners()]
    return {item.wh_code: item.last_updated for item in inventory.get_max_last_update_time(wh_codes)}


def alert_update_date():
    today = date.today()
    alerts.preventative_alert(
        PimtInventoryOutdated(
            [wh_code for wh_code, last_updated in _get_update_date_by_wh().items() if last_updated < today]
        )
    )


def get_ops_main_data_for_export() -> list[SkuInventory]:
    po_inbound = ops_service.get_po_inbound()
    po_outbound = ops_service.get_po_outbound()
    expired_inventory_data = inventory_service.get_expiring_inventory_data_by_sku_wh()
    inventory_data = get_in_house_dashboard_items(expired_inventory_data)
    return get_sku_details(inventory_data, po_inbound, po_outbound)
