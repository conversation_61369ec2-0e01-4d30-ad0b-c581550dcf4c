from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from decimal import Decimal
from functools import cached_property

from procurement.constants.hellofresh_constant import APP_TIMEZONE, UnitOfMeasure
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, NOT_DELIVERED_PAST_DUE, PO_CANCELLED, PO_VOID
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import PO_NUMBER, SKU_CODE, UNITS
from procurement.data.dto.ordering.asn import AdvanceShippingNoticeDto
from procurement.data.dto.ordering.purchase_order.pimt_pos import PimtPoItem, PimtPoItemBase
from procurement.data.dto.ordering.shipment import Shipment
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.dto.sku import SkuMeta
from procurement.managers.imt import buyers
from procurement.managers.ordering import advance_shipping_notice as asn_service
from procurement.managers.ordering import po_acknowledgement, service
from procurement.managers.ordering import shipment as shipment_service
from procurement.managers.ordering.advance_shipping_notice import AsnPoStatusCalculations
from procurement.managers.ordering.po_acknowledgement import PoAcknowledgementData, POAcknowledgementState
from procurement.managers.pimt import partners
from procurement.managers.sku import culinary_sku
from procurement.repository.ordering import po_void as po_void_repo
from procurement.repository.ordering import purchase_order as pimt_po_repo


class PoDataContext:
    def __init__(self, purchase_orders: list[PimtPoItemBase], warehouse: Warehouse, weeks: Iterable[ScmWeek]):
        self.sku_codes = frozenset(item.sku_code for item in purchase_orders)
        self.po_numbers = {item.po_number for item in purchase_orders}
        self.warehouse = warehouse
        self.weeks = weeks

    @cached_property
    def buyers_by_sku(self) -> dict[SKU_CODE, str]:
        buyer_list_by_sku = defaultdict(set)
        for site in self.warehouse.regional_dcs:
            for sku, buyer in buyers.get_buyers_by_sku(site=site).items():
                buyer_list_by_sku[sku].add(buyer)
        return {sku: ",".join(buyer_list) for sku, buyer_list in buyer_list_by_sku.items()}

    @cached_property
    def ingredient_info_by_code(self) -> dict[SKU_CODE, SkuMeta]:
        return culinary_sku.get_sku_meta_by_code(self.warehouse.regional_dcs[0], sku_codes=self.sku_codes)

    @cached_property
    def po_voids(self) -> set[tuple[PO_NUMBER, SKU_CODE]]:
        return {(item.po_number, item.sku_code) for item in po_void_repo.get_po_void_by_weeks(self.weeks)}

    @cached_property
    def po_ack_data(self) -> dict[tuple[PO_NUMBER, SKU_CODE], PoAcknowledgementData]:
        return po_acknowledgement.get_po_acknowledgements_proposed_data(self.po_numbers)

    @cached_property
    def asn_data(self) -> dict[tuple[PO_NUMBER, SKU_CODE], AdvanceShippingNoticeDto]:
        return asn_service.get_advance_shipping_notice_by_po_numbers(self.po_numbers)

    @cached_property
    def shipment_data(self) -> dict[PO_NUMBER, Shipment]:
        return shipment_service.get_shipments_by_po(po_numbers=self.po_numbers)


class PoStatusData:
    def __init__(self, item: PimtPoItemBase, context: PoDataContext):
        self.item = item
        self.context = context

    @cached_property
    def week(self) -> ScmWeek:
        return ScmWeek.from_str(self.item.week)

    @cached_property
    def ack_item(self) -> PoAcknowledgementData | None:
        return self.context.po_ack_data.get((self.po_number, self.sku_code))

    @cached_property
    def asn_item(self) -> AsnPoStatusCalculations:
        return AsnPoStatusCalculations(self.context.asn_data.get((self.po_number, self.sku_code)), self.item)

    @cached_property
    def shipment_data(self) -> Shipment | None:
        return self.context.shipment_data.get(self.po_number)

    @property
    def partner(self) -> str:
        return self.context.warehouse.code

    @property
    def sku_code(self) -> str:
        return self.item.sku_code

    @property
    def sku_name(self) -> str:
        return self.item.sku_name

    @property
    def po_number(self) -> str:
        return self.item.po_number

    @property
    def supplier(self) -> str:
        return self.item.supplier

    @cached_property
    def category(self) -> str:
        return self.context.ingredient_info_by_code[self.sku_code].category

    @property
    def order_size(self) -> int:
        return self.item.order_size

    @property
    def case_price(self) -> Decimal:
        return self.item.case_price

    @property
    def case_size(self) -> Decimal:
        return self.item.case_size

    @property
    def quantity_ordered(self) -> Decimal:
        return self.item.quantity

    @property
    def scheduled_delivery_date(self) -> datetime:
        return self.item.delivery_time_start

    @property
    def emergency_reason(self) -> str:
        return self.item.emergency_reason

    @property
    def shipping_method(self) -> str:
        return self.item.shipping_method

    @property
    def date_received(self) -> datetime | None:
        return self.item.grn_receive_date.astimezone(APP_TIMEZONE) if self.item.grn_receive_date else None

    @cached_property
    def buyer(self) -> str:
        return self.context.buyers_by_sku.get(self.sku_code)

    @property
    def po_buyer(self) -> str:
        return self.item.ordered_by

    @cached_property
    def unit_of_measure(self) -> UnitOfMeasure:
        return self.context.ingredient_info_by_code[self.sku_code].unit

    @cached_property
    def quantity_received(self) -> UNITS:
        return self.item.grn_units or 0

    @cached_property
    def case_received(self) -> int:
        return self.item.cases_received or (round(self.quantity_received / self.case_size) if self.case_size else 0)

    @cached_property
    def case_size_received(self) -> Decimal:
        return round(Decimal(self.quantity_received) / self.case_received, 3) if self.case_received else Decimal()

    @cached_property
    def total_price(self) -> Decimal:
        return self.case_received * self.case_price

    @property
    def total_price_received(self) -> Decimal:
        return (self.quantity_received * (self.case_price / self.case_size)) if self.case_size else Decimal()

    @cached_property
    def proposed_quantity_cases(self) -> int | None:
        return self.ack_item.quantity_cases if self.ack_item else None

    @cached_property
    def proposed_quantity_units(self) -> int | None:
        return self.ack_item.quantity_units if self.ack_item else None

    @cached_property
    def proposed_units_per_cases(self) -> int | None:
        return self.ack_item.units_per_cases if self.ack_item else None

    @cached_property
    def proposed_delivery_date(self) -> datetime | None:
        return self.ack_item.promised_date if self.ack_item else None

    @cached_property
    def ack_status(self) -> POAcknowledgementState | None:
        return self.ack_item.state if self.ack_item else None

    @property
    def asn_shipment_date(self) -> date | None:
        return self.asn_item.shipment_date

    @property
    def asn_planned_delivery_time(self) -> date | None:
        return self.asn_item.planned_delivery_time

    @property
    def asn_shipped_quantity_cases(self) -> int:
        return self.asn_item.shipped_quantity_cases

    @property
    def asn_unit_of_measure(self) -> UnitOfMeasure:
        return self.asn_item.unit_of_measure

    @property
    def asn_case_count(self) -> int:
        return self.asn_item.case_count

    @property
    def asn_shipped_quantity_units(self) -> int:
        return self.asn_item.shipped_quantity_units

    @property
    def asn_requires_high_attention(self) -> bool:
        return self.asn_item.asn_requires_high_attention

    @property
    def asn_requires_attention(self) -> bool:
        return self.asn_item.asn_requires_attention

    @cached_property
    def po_status(self) -> str:
        return service.calculate_po_status(
            po_voids=self.context.po_voids,
            purchase_order=self.item,
            po_ack_status=self.ack_status,
            asn_calculations=self.asn_item,
        )

    @cached_property
    def receive_variance(self) -> int:
        if self.po_status in (*AWAITING_DELIVERY_STATUSES, NOT_DELIVERED_PAST_DUE, PO_VOID, PO_CANCELLED):
            return 0
        return abs(self.quantity_ordered - self.quantity_received)

    @property
    def load_number(self) -> str | None:
        return self.shipment_data.load_number if self.shipment_data else None

    @property
    def pallet_count(self) -> int | None:
        return self.shipment_data.pallet_count if self.shipment_data else None

    @property
    def carrier_name(self) -> str | None:
        return self.shipment_data.carrier_name if self.shipment_data else None

    @property
    def appointment_time(self) -> date | None:
        return (
            self.shipment_data.appointment_time.date()
            if self.shipment_data and self.shipment_data.appointment_time
            else None
        )

    @cached_property
    def origin_location(self) -> str | None:
        return (
            ", ".join(
                val
                for val in (
                    self.shipment_data.origin_location.locality,
                    self.shipment_data.origin_location.administrative_area,
                )
                if val
            )
            if self.shipment_data
            else None
        )

    @property
    def has_multiple_transfer_items(self) -> bool:
        return self.item.has_multiple_transfer_items

    @property
    def transfer_source_bob_code(self) -> str | None:
        return self.item.transfer_source_bob_code


def get_po_data_by_week(weeks: list[ScmWeek], wh: Warehouse) -> list[PoStatusData]:
    return _get_po_data(purchase_orders=pimt_po_repo.get_pimt_purchase_order(wh, weeks), wh=wh, weeks=weeks)


def get_po_data_by_sku_code(weeks: Iterable[ScmWeek], wh: Warehouse, sku_code: SKU_CODE) -> list[PoStatusData]:
    return _get_po_data(purchase_orders=pimt_po_repo.get_pimt_purchase_order(wh, weeks, sku_code), wh=wh, weeks=weeks)


def get_to_data(bob_code: str, po_number: PO_NUMBER, sku_code: SKU_CODE) -> list[PoStatusData]:
    wh = next(w for w in partners.get_all_partners() if w.bob_code == bob_code)
    return _get_po_data(
        purchase_orders=pimt_po_repo.get_pimt_transfer_order(wh=wh, po_number=po_number, sku_code=sku_code),
        wh=wh,
        weeks=[utils.get_week_from_po_number(po_number)],
    )


def get_po_data_by_date_range(wh: Warehouse, date_from: datetime, date_to: datetime) -> list[PoStatusData]:
    pos = pimt_po_repo.get_pimt_purchase_order_by_date_range(wh, date_from, date_to)
    weeks = {po.week for po in pos}
    return _get_po_data(
        purchase_orders=pimt_po_repo.get_pimt_purchase_order_by_date_range(wh, date_from, date_to),
        wh=wh,
        weeks=[ScmWeek.from_str(w) for w in weeks],
    )


def get_po_data_by_receiving_date_range(wh: Warehouse, date_from: datetime, date_to: datetime) -> list[PoStatusData]:
    pos = pimt_po_repo.get_pimt_purchase_order_by_receiving_date(wh, date_from, date_to)
    weeks = {po.week for po in pos}
    return _get_po_data(
        purchase_orders=pos,
        wh=wh,
        weeks=[ScmWeek.from_str(w) for w in weeks],
    )


def _get_po_data(
    purchase_orders: list[PimtPoItemBase] | None, wh: Warehouse, weeks: Iterable[ScmWeek]
) -> list[PoStatusData]:
    if not purchase_orders:
        return []

    po_context = PoDataContext(purchase_orders=purchase_orders, warehouse=wh, weeks=weeks)
    return [PoStatusData(po_item, po_context) for po_item in purchase_orders]


def get_to_data_by_receiving_date_range(wh: Warehouse, date_from: datetime, date_to: datetime) -> list[PoStatusData]:
    pos = pimt_po_repo.get_pimt_transfer_order_by_receiving_date(wh, date_from, date_to)
    weeks = {po.week for po in pos}
    return _get_po_data(
        purchase_orders=pos,
        wh=wh,
        weeks=[ScmWeek.from_str(w) for w in weeks],
    )


def get_undelivered_inbound_pos(warehouse: Warehouse, sku_code: str, weeks: list[ScmWeek]) -> list[PimtPoItem]:
    return pimt_po_repo.get_inbound_pos(sku_code=sku_code, warehouse=warehouse, weeks=weeks)


def get_network_depletion_inbound_po_status(warehouse: Warehouse, weeks: list[ScmWeek]) -> list[PoStatusData]:
    purchase_orders = pimt_po_repo.get_inbound_pos_for_network_depletion(warehouse=warehouse, weeks=weeks)
    return _get_po_data(purchase_orders, warehouse, weeks)
