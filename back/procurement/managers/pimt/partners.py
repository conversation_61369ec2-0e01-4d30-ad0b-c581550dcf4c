import dataclasses

from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, SUPPLIER, WH_CODE
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.repository.ordering import supplier as supplier_repo
from procurement.repository.pimt import partner as partner_repo


@dataclasses.dataclass
class WarehouseOrder:
    wh_code: str
    order: int


def get_all_partners(e2open_grn: bool = False, market: str = None) -> tuple[Warehouse, ...]:
    market = market or context.get_request_context().market
    return partner_repo.get_all_warehouses(market=market, e2open_grn=e2open_grn)


def get_old_inventory_warehouses(market: str) -> list[Warehouse]:
    market = market or context.get_request_context().market
    return partner_repo.get_old_inventory_warehouses(market=market)


def get_hj_inventory_warehouses() -> list[Warehouse]:
    return partner_repo.get_hj_inventory_warehouses(market=context.get_request_context().market)


def get_bob_code_to_warehouse() -> dict[BOB_CODE, WH_CODE]:
    return partner_repo.get_whs_by_bob_code(market=context.get_request_context().market)


def get_supplier_to_warehouse() -> dict[SUPPLIER, WH_CODE]:
    return partner_repo.get_supplier_to_warehouse(market=context.get_request_context().market)


def add_warehouse(warehouse: Warehouse) -> None:
    warehouse.bob_code = warehouse.bob_code or None
    _InputValidator().validate_new_warehouse(warehouse)
    partner_repo.add_warehouse(warehouse)


def update_warehouse(warehouse: Warehouse) -> None:
    warehouse.bob_code = warehouse.bob_code or None
    _InputValidator().validate_edit_warehouse(warehouse)
    partner_repo.update_warehouse(warehouse)


def delete_warehouse(code: str) -> None:
    partner_repo.delete_warehouse(code, context.get_request_context().market)


def update_order(data: list[WarehouseOrder]) -> None:
    new_order = {partner.wh_code: partner.order for partner in data}
    if new_order:
        partner_repo.update_order(new_order, context.get_request_context().market)


def get_order() -> list[WarehouseOrder]:
    orders = []
    for code, order in partner_repo.get_order(context.get_request_context().market):
        orders.append(WarehouseOrder(wh_code=code, order=order))
    return orders


def get_regions() -> set[str]:
    return {partner.region for partner in get_all_partners()}


class _InputValidator:
    def __init__(self):
        self._whs: dict[WH_CODE, BOB_CODE] = {
            wh: bc for bc, wh in partner_repo.get_whs_by_bob_code(context.get_request_context().market).items()
        }

    def validate_new_warehouse(self, wh: Warehouse):
        self._validate_unique_code(wh)
        self.validate_edit_warehouse(wh)

    def validate_edit_warehouse(self, wh: Warehouse) -> None:
        self._validate_unique_bob_code(wh)
        self._validate_inventory_type(wh)
        self._validate_supplier_names(wh)

    @staticmethod
    def _validate_supplier_names(wh: Warehouse) -> None:
        valid_supplier_names = supplier_repo.get_all_supplier_names()
        for supplier_name in wh.ot_suppliers:
            if supplier_name not in valid_supplier_names:
                raise InvalidRequestException(f"Supplier name '{supplier_name}' is invalid")

    def _validate_unique_code(self, wh: Warehouse):
        if wh.code in self._whs:
            raise InvalidRequestException(f"Warehouse code '{wh.code}' is already in use")

    def _validate_unique_bob_code(self, wh: Warehouse):
        if wh.bob_code and wh.bob_code in (bc for c, bc in self._whs.items() if c != wh.code):
            raise InvalidRequestException(f"Bob Code '{wh.bob_code}' is already in use")

    @staticmethod
    def _validate_inventory_type(wh: Warehouse):
        if wh.inventory_type.is_high_jump and not wh.hj_name:
            raise InvalidRequestException("'HJ Name' field is required for HJ Inventory Warehouses")
