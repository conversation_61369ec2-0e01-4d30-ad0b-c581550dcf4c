from typing import Type

from procurement.data.googlesheet_model.pimt.pimt_export import PimtExport
from procurement.managers.exports import utils

from .base import PimtBaseExport
from .csac import CsacExport
from .ops_main import OpsMainGdpExport, OpsMainProteinExport
from .replenishment import ReplenishmentExportTotalV2, ReplenishmentExportV2
from .strategy import StrategyExport


def _get_gsheet_id() -> str:
    return utils.get_gsheet_id(PimtExport.parent_doc)


def _execute_export(model: Type[PimtBaseExport], gsheet_id: str):
    model(gsheet_id=gsheet_id).write_to_sheet()


def strategy_export():
    gsheet_id = _get_gsheet_id()
    _execute_export(StrategyExport, gsheet_id)


def ops_main_protein_export():
    gsheet_id = _get_gsheet_id()
    _execute_export(OpsMainProteinExport, gsheet_id)


def ops_main_gdp_export():
    gsheet_id = _get_gsheet_id()
    _execute_export(OpsMainGdpExport, gsheet_id)


def csac_export():
    gsheet_id = _get_gsheet_id()
    _execute_export(CsacExport, gsheet_id)


def replenishment_export():
    gsheet_id = _get_gsheet_id()
    _execute_export(ReplenishmentExportV2, gsheet_id)
    _execute_export(ReplenishmentExportTotalV2, gsheet_id)
