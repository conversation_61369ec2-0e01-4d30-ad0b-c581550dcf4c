from __future__ import annotations

import dataclasses
from datetime import date, datetime
from decimal import Decimal

from procurement.core.typing import UNITS
from procurement.data.googlesheet_model.pimt.pimt_export import StrategyMainDashboardSheet
from procurement.managers.exports.base import BaseExportModel
from procurement.managers.pimt import inventory as inventory_service
from procurement.managers.pimt.inventory import InventoryDashboard

from .base import PimtBaseExport


@dataclasses.dataclass
class StrategyExportData(BaseExportModel):
    partner: str
    sku_name: str
    sku_code: str
    category: str
    supplier: str
    supplier_code: str
    receive_date: datetime
    lot: str
    po_number: str
    cases: int
    total_units: UNITS
    case_size: UNITS
    case_price: Decimal
    unit_price: Decimal
    expiration_date: date
    days_until_expiration: int | None
    source: str

    @staticmethod
    def from_dashboard_item(item: InventoryDashboard, context: None) -> StrategyExportData:
        return StrategyExportData(
            partner=item.partner,
            sku_name=item.sku_name,
            sku_code=item.sku_code,
            category=item.purchasing_category,
            supplier=item.supplier,
            supplier_code=item.supplier_code,
            receive_date=item.receive_date,
            lot=item.lot,
            po_number=item.po_number,
            cases=item.cases,
            total_units=item.total_units,
            case_size=item.case_size,
            case_price=round(item.case_price or 0, 2),
            unit_price=item.unit_price,
            expiration_date=item.expiration_date,
            days_until_expiration=item.days_until_expiration,
            source=item.source,
        )


class StrategyExport(PimtBaseExport):
    gsheet_model = StrategyMainDashboardSheet()
    result_model = StrategyExportData

    def _get_data(self) -> list[StrategyExportData]:
        return [
            StrategyExportData.from_dashboard_item(item, context=None)
            for item in inventory_service.get_expiring_inventory_report()
        ]
