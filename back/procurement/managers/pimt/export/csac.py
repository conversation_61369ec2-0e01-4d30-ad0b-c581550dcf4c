from __future__ import annotations

import dataclasses
from decimal import Decimal

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.core.typing import WH_CODE
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.googlesheet_model.pimt.pimt_export import CsacSheet
from procurement.managers.exports.base import BaseExportModel
from procurement.managers.pimt import inventory as inventory_service
from procurement.managers.pimt import partners as partner_service
from procurement.managers.pimt.inventory import InventoryDashboard

from .base import PimtBaseExport


def _calculate_average_cost(cost: int, total_units: int) -> Decimal:
    return round(Decimal(cost) / total_units, 2) if total_units > 0 else Decimal()


@dataclasses.dataclass
class CsacData(BaseExportModel):
    sku_code: str
    sku_name: str
    site: str
    cost: int
    total_units: int
    cold_storage_facility: str
    average_cost: Decimal

    @staticmethod
    def from_dashboard_item(item: InventoryDashboard, context: dict[WH_CODE, Warehouse]) -> CsacData:
        wh_info = context[item.partner]
        cost = item.unit_price * item.total_units
        return CsacData(
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            site=",".join(wh_info.regional_dcs),
            cost=cost,
            total_units=item.total_units,
            cold_storage_facility=wh_info.name,
            average_cost=_calculate_average_cost(cost, item.total_units),
        )


class CsacExport(PimtBaseExport):
    _accepted_categories = (IngredientCategory.PROTEIN,)
    result_model = CsacData
    gsheet_model = CsacSheet()

    def _get_data(self) -> list[CsacData]:
        csac_group_data = {}

        for item in inventory_service.get_expiring_inventory_report():
            if item.purchasing_category not in self._accepted_categories:
                continue
            key = (item.sku_code, item.partner)
            if key in csac_group_data:
                csac_group_data[key].cost += item.unit_price * item.total_units
                csac_group_data[key].total_units += item.total_units
                csac_group_data[key].average_cost = _calculate_average_cost(
                    csac_group_data[key].cost, csac_group_data[key].total_units
                )
            else:
                csac_group_data[key] = CsacData.from_dashboard_item(
                    item, context={wh.code: wh for wh in partner_service.get_all_partners()}
                )
        return list(csac_group_data.values())
