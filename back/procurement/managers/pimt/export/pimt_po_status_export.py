import abc
from collections.abc import Collection, Iterable
from datetime import date, datetime, time, timedelta

from procurement.constants.ordering import PO_VOID
from procurement.core.dates import ScmWeek
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.exports.po_filter import AllPosWithTransfersFilter, PoFilterBase, PoFilterManager
from procurement.managers.imt.export import export_base
from procurement.managers.imt.export.export_base import ExportCsv, ExportSheet, ExportWorkbook, TabularData
from procurement.managers.pimt import email_export as pimt_email_export
from procurement.managers.pimt import partners
from procurement.managers.pimt import purchase_order as pimt_po_manager
from procurement.managers.pimt.purchase_order import PoStatusData


class PimtPoStatusExportCsvData(TabularData):
    def __init__(self, report_data: Iterable[PoStatusData]):
        super().__init__("PIMT monthly 3PW receipt export", report_data)

    @property
    def column_headers(self) -> list[export_base.Row]:
        return [
            export_base.Row(
                content=[
                    "3PW",
                    "Supplier",
                    "PO #",
                    "SKU",
                    "SKU Name",
                    "Category",
                    "Scheduled Delivery Date",
                    "PO Status",
                    "Receiving Variance (units)",
                    "Order Size",
                    "Case Price",
                    "Case Size",
                    "Quantity Received",
                    "Cases Received",
                    "Date Received",
                    "Total Price",
                    "Emergency Reason",
                    "Buyer",
                    "Ship Method",
                ]
            )
        ]

    def _get_row_data(self, raw_item: PoStatusData) -> Iterable:
        return (
            raw_item.partner,
            raw_item.supplier,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.category,
            raw_item.scheduled_delivery_date,
            raw_item.po_status,
            raw_item.receive_variance,
            raw_item.order_size,
            raw_item.case_price,
            raw_item.case_size,
            raw_item.quantity_received,
            raw_item.case_received,
            raw_item.date_received,
            raw_item.total_price,
            raw_item.emergency_reason,
            raw_item.buyer,
            raw_item.shipping_method,
        )


def _get_pimt_monthly_3pw_report_file(invalid_data: Iterable[PoStatusData]) -> bytes:
    return ExportCsv(PimtPoStatusExportCsvData(invalid_data)).write(",").binary


def _get_export_data(date_from, date_to) -> list[PoStatusData]:
    po_data = []
    for wh in partners.get_all_partners(e2open_grn=True):
        po_data.extend(pimt_po_manager.get_po_data_by_date_range(wh=wh, date_from=date_from, date_to=date_to))
    return po_data


def po_export() -> None:
    first_day_of_current_month = datetime.combine(date.today().replace(day=1), time())

    first_day_of_previous_month = (first_day_of_current_month - timedelta(days=1)).replace(day=1)

    export_data = _get_export_data(date_from=first_day_of_previous_month, date_to=first_day_of_current_month)

    data_for_export = filter(lambda item: item.po_status != PO_VOID, export_data)

    file = _get_pimt_monthly_3pw_report_file(data_for_export)

    pimt_email_export.send_pimt_3pw_monthly_report(
        file,
        date_from=first_day_of_previous_month.strftime("%m/%d/%Y"),
        date_to=first_day_of_current_month.strftime("%m/%d/%Y"),
    )


class PimtFinanceExportData(TabularData):
    def __init__(self, report_data: Iterable[PoStatusData]):
        super().__init__("PIMT PO Status", report_data)

    @property
    def column_headers(self) -> list[export_base.Row]:
        return [
            export_base.Row(
                content=[
                    "3PW",
                    "Supplier",
                    "PO #",
                    "SKU",
                    "SKU Name",
                    "Category",
                    "Purchasing UOM",
                    "Scheduled Delivery Date",
                    "PO Status",
                    "Receiving Variance (units)",
                    "Order Size",
                    "Case Price",
                    "Case Size",
                    "Case Size Received",
                    "Quantity Ordered",
                    "Quantity Received",
                    "Cases Received",
                    "Date Received",
                    "Total Price PO",
                    "Total Price Received",
                    "Emergency Reason",
                    "Assigned Buyer",
                    "PO Buyer",
                    "Ship Method",
                ]
            )
        ]

    def _get_row_data(self, raw_item: PoStatusData) -> Iterable:
        return (
            raw_item.partner,
            raw_item.supplier,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.category,
            raw_item.unit_of_measure,
            raw_item.scheduled_delivery_date,
            raw_item.po_status,
            raw_item.receive_variance,
            raw_item.order_size,
            raw_item.case_price,
            raw_item.case_size,
            raw_item.case_size_received,
            raw_item.quantity_ordered,
            raw_item.quantity_received,
            raw_item.case_received,
            raw_item.date_received,
            raw_item.total_price,
            raw_item.total_price_received,
            raw_item.emergency_reason,
            raw_item.buyer,
            raw_item.po_buyer,
            raw_item.shipping_method,
        )


class PimtFinancialData(abc.ABC):
    def __init__(self, po_filter: str):
        self.po_filter: PoFilterBase = PoFilterManager.get_filter(po_filter)

    def get_data(self, warehouses: Iterable[Warehouse] | None = None) -> Collection[PoStatusData]:
        date_from, date_to = self._get_date_range()
        items = []
        warehouses = warehouses or partners.get_all_partners(e2open_grn=True)
        for wh in warehouses:
            items.extend(
                self.po_filter.apply(
                    pimt_po_manager.get_po_data_by_receiving_date_range(wh=wh, date_from=date_from, date_to=date_to)
                )
            )
            if isinstance(self.po_filter, AllPosWithTransfersFilter):
                items.extend(
                    pimt_po_manager.get_to_data_by_receiving_date_range(wh=wh, date_from=date_from, date_to=date_to)
                )
        items.sort(key=lambda item: item.date_received.date() if item.date_received else date.max)
        return items

    @abc.abstractmethod
    def _get_date_range(self) -> tuple[datetime, datetime]:
        pass


class PimtDailyFinancialData(PimtFinancialData):
    def __init__(self, export_date: datetime, po_filter: str):
        super().__init__(po_filter)
        self.export_date = export_date - timedelta(days=1)

    def _get_date_range(self) -> tuple[datetime, datetime]:
        return self.export_date, self.export_date + timedelta(days=1)


class PimtWeeklyFinancialData(PimtFinancialData):
    def __init__(self, po_filter: str, week_from: ScmWeek, week_to: ScmWeek | None = None):
        super().__init__(po_filter)
        self.week_from = week_from
        self.week_to = week_to or week_from

    def _get_date_range(self) -> tuple[datetime, datetime]:
        return (
            datetime.combine(self.week_from.get_first_day(), time()),
            datetime.combine(self.week_to.get_last_production_day(), time()) + timedelta(days=1),
        )


class PimtMonthlyFinancialData(PimtFinancialData):
    def __init__(self, export_date: datetime, po_filter: str):
        super().__init__(po_filter)
        self.date_from = export_date.replace(day=1)
        self.date_to = (self.date_from + timedelta(days=31)).replace(day=1)

    def _get_date_range(self) -> tuple[datetime, datetime]:
        return self.date_from, self.date_to


class PimtCustomFinancialData(PimtFinancialData):
    def __init__(self, date_from: datetime, date_to: datetime, po_filter: str):
        super().__init__(po_filter)
        self.date_from = date_from
        self.date_to = date_to

    def _get_date_range(self) -> tuple[datetime, datetime]:
        return self.date_from, self.date_to + timedelta(days=1)


def generate_daily_pimt_financial_excel(day: date, warehouses: Iterable[Warehouse], po_filter: str) -> bytes:
    data = PimtDailyFinancialData(export_date=datetime.combine(day, time()), po_filter=po_filter)
    return _generate_excel(data, warehouses)


def generate_monthly_pimt_financial_excel(day: date, warehouses: Iterable[Warehouse], po_filter: str) -> bytes:
    data = PimtMonthlyFinancialData(export_date=datetime.combine(day, time()), po_filter=po_filter)
    return _generate_excel(data, warehouses)


def generate_weekly_pimt_financial_excel(week: ScmWeek, warehouses: Iterable[Warehouse], po_filter: str) -> bytes:
    data = PimtWeeklyFinancialData(week_from=week, po_filter=po_filter)
    return _generate_excel(data, warehouses)


def generate_weekly_range_pimt_financial_excel(
    week_from: ScmWeek, week_to: ScmWeek, warehouses: Iterable[Warehouse], po_filter: str
) -> bytes:
    data = PimtWeeklyFinancialData(week_from=week_from, week_to=week_to, po_filter=po_filter)
    return _generate_excel(data, warehouses)


def generate_custom_pimt_financial_excel(
    date_from: date, date_to: date, warehouses: Iterable[Warehouse], po_filter: str
) -> bytes:
    data = PimtCustomFinancialData(
        date_from=datetime.combine(date_from, time()), date_to=datetime.combine(date_to, time()), po_filter=po_filter
    )
    return _generate_excel(data, warehouses)


def _generate_excel(data: PimtFinancialData, warehouses: Iterable[Warehouse]) -> bytes:
    export_table_data = PimtFinanceExportData(data.get_data(warehouses))
    return ExportWorkbook().add_sheet(ExportSheet(export_table_data)).binary
