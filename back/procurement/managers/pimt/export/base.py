import abc

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.managers.exports.base import BaseExport


class PimtBaseExport(BaseExport, metaclass=abc.ABCMeta):
    def __init__(self, gsheet_id: str):
        self.gsheet_id = gsheet_id

    @staticmethod
    def _is_category_acceptable(item_category: str, categories: tuple[IngredientCategory, ...]) -> bool:
        return item_category in categories
