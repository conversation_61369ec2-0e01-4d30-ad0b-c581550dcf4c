from __future__ import annotations

import dataclasses
from decimal import Decimal

from procurement.constants.hellofresh_constant import DEPLETION_WEEKS_AHEAD
from procurement.core.dates.weeks import ScmWeek
from procurement.data.googlesheet_model.pimt.pimt_export import ReplenishmentSheet, ReplenishmentSheetTotal
from procurement.managers.dc_inventory.replenishment import replenishment
from procurement.managers.dc_inventory.replenishment.region_level import ReplenishmentItem
from procurement.managers.exports.base import BaseExportModel, ExportContext
from procurement.managers.pimt import partners as partner_service

from .base import PimtBaseExport


@dataclasses.dataclass
class ReplenishmentDataV2(BaseExportModel):
    region: str
    brand: str
    sku: str
    sku_name: str
    category: str
    subcategory: str
    buyer: str
    replenishment_buyer: str
    replenishment_type: str
    lead_time: int | None
    row_needs: int
    forecasts: list[int]
    total_forecast: int
    average_weekly_demand: int
    total_core_inventory: Decimal
    total_tpl_inventory: Decimal
    vendor_managed_inventory: Decimal
    total_wh_inventory: Decimal
    expired_inventory: Decimal
    total_inventory_cost: Decimal
    dc_inventory_cost: Decimal
    tpl_inventory_cost: Decimal
    tpw_inventory_cost: Decimal
    expire_soon_inventory: Decimal
    total_inventory_wos: Decimal
    inbounds: list[Decimal]
    total_inbound: int
    total_undelivered: int
    end_of_weeks: list[Decimal]
    end_of_weeks_wos: list[Decimal]

    @staticmethod
    def from_dashboard_item(item: ReplenishmentItem, context: ExportContext | None) -> ReplenishmentDataV2:
        return ReplenishmentDataV2(
            region=item.region or "Total",
            brand=",".join(item.sku_overview.brands),
            sku=item.sku_overview.sku_code,
            sku_name=item.sku_overview.sku_name,
            category=item.sku_overview.category,
            subcategory=item.sku_overview.subcategory,
            buyer=",".join(item.buyers),
            replenishment_buyer=",".join(item.replenishment_buyers),
            replenishment_type=item.replenishment_type,
            lead_time=item.lead_time_weeks,
            row_needs=item.row_needs,
            forecasts=list(item.forecasts.values()),
            total_forecast=item.total_forecast,
            average_weekly_demand=item.avg_weekly_usage,
            total_core_inventory=item.total_core_inventory,
            total_tpl_inventory=item.total_tpl_inventory,
            total_wh_inventory=item.current_wh_inventory,
            vendor_managed_inventory=item.total_vendor_managed_inventory,
            expired_inventory=item.expired_inventory,
            expire_soon_inventory=item.expiring_in_sixty_days,
            total_inventory_wos=item.total_wos_inventory,
            inbounds=list(item.inbounds.values()),
            total_inbound=item.total_inbounds,
            total_undelivered=item.undelivered,
            end_of_weeks=list(item.end_of_weeks.values()),
            end_of_weeks_wos=list(item.end_of_weeks_wos.values()),
            total_inventory_cost=item.total_inventory_cost,
            dc_inventory_cost=item.dc_inventory_cost,
            tpl_inventory_cost=item.tpl_inventory_cost,
            tpw_inventory_cost=item.tpw_inventory_cost,
        )


class ReplenishmentExportV2(PimtBaseExport):
    result_model = ReplenishmentDataV2

    def __init__(self, gsheet_id: str):
        super().__init__(gsheet_id)
        self.gsheet_model = ReplenishmentSheet()
        current_week = ScmWeek.current_week()
        weeks = ScmWeek.range(current_week, current_week + DEPLETION_WEEKS_AHEAD)
        self.gsheet_model.sheet_header.format_headers({f"week_{n}": w for n, w in enumerate(weeks)})

    def _get_data(self) -> list[ReplenishmentDataV2]:
        return [
            ReplenishmentDataV2.from_dashboard_item(item, context=None)
            for region in partner_service.get_regions()
            for item in replenishment.get_replenishment_view(region)
        ]


class ReplenishmentExportTotalV2(PimtBaseExport):
    result_model = ReplenishmentDataV2

    def __init__(self, gsheet_id: str):
        super().__init__(gsheet_id)
        self.gsheet_model = ReplenishmentSheetTotal()
        current_week = ScmWeek.current_week()
        weeks = ScmWeek.range(current_week, current_week + DEPLETION_WEEKS_AHEAD)
        self.gsheet_model.sheet_header.format_headers({f"week_{n}": w for n, w in enumerate(weeks)})

    def _get_data(self) -> list[ReplenishmentDataV2]:
        return [
            ReplenishmentDataV2.from_dashboard_item(item, context=None)
            for item in replenishment.get_replenishment_view(None)
        ]
