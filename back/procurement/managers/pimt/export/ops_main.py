from __future__ import annotations

import dataclasses

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.core.typing import UNITS
from procurement.data.googlesheet_model.pimt.pimt_export import (
    OpsMainDashboardGroceryDairySheet,
    OpsMainDashboardProteinSheet,
)
from procurement.managers.exports.base import BaseExportModel
from procurement.managers.pimt import ops_main_dashboard
from procurement.managers.pimt.ops_main_dashboard import SkuPartnerInventory

from .base import PimtBaseExport


@dataclasses.dataclass
class OpsMainContext:
    sku_code: str
    sku_name: str
    category: str
    commodity_group: list[str]
    site: str | None = None


@dataclasses.dataclass
class OpsMainData(BaseExportModel):
    site: str
    sku_code: str
    sku_name: str
    category: str
    commodity_group: str
    in_house: UNITS
    inbound: UNITS
    outbound: UNITS

    @staticmethod
    def from_dashboard_item(item: SkuPartnerInventory, context: OpsMainContext) -> OpsMainData:
        return OpsMainData(
            site=context.site,
            sku_code=context.sku_code,
            sku_name=context.sku_name,
            category=context.category,
            commodity_group=", ".join(filter(None, context.commodity_group)),
            in_house=item.total_on_hand,
            inbound=item.inbound,
            outbound=item.outbound,
        )


class BaseOpsMainExport(PimtBaseExport):
    _accepted_categories: tuple[IngredientCategory, ...]
    result_model = OpsMainData

    def _get_data(self) -> list[BaseExportModel]:
        result_items = []
        for item in ops_main_dashboard.get_ops_main_data_for_export():
            if item.category not in self._accepted_categories:
                continue
            context = OpsMainContext(
                sku_code=item.sku_code,
                sku_name=item.sku_name,
                category=item.category,
                commodity_group=item.commodity_group,
            )
            for wh_code, wh_inventory in item.ops.items():
                context.site = wh_code
                result_items.append(OpsMainData.from_dashboard_item(wh_inventory, context))
        return result_items


class OpsMainProteinExport(BaseOpsMainExport):
    gsheet_model = OpsMainDashboardProteinSheet()
    _accepted_categories = (IngredientCategory.PROTEIN,)


class OpsMainGdpExport(BaseOpsMainExport):
    gsheet_model = OpsMainDashboardGroceryDairySheet()
    _accepted_categories = (IngredientCategory.GROCERY, IngredientCategory.DAIRY, IngredientCategory.PRODUCE)
