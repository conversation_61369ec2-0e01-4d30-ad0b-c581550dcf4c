from collections.abc import Iterable
from typing import Any

from confluent_kafka import Message
from confluent_kafka.schema_registry.avro import AvroDeserializer
from confluent_kafka.serialization import SerializationContext, StringDeserializer
from sqlalchemy import ColumnElement

from procurement.core.kafka import handlers as kafka_handler
from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.packaging_info import PackagingInfo
from procurement.data.models.pimt.packaging_info import PackagingInfoModel
from procurement.repository.pimt import packaging_info as packaging_info_repo


class PackagingInfoHandler(kafka_handler.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return AvroDeserializer(
            schema_registry_client=kafka_handler.get_schema_registry_client(),
            from_dict=self._from_dict,
        )(value, ctx)

    def _process(self, msg: Message) -> None:
        packaging_info_repo.upsert_packaging_info(msg.value())

    @staticmethod
    def _from_dict(item: dict, _) -> dict[ColumnElement, Any]:
        return {
            PackagingInfoModel.supplier_sku_uuid: item["supplier_sku_id"],
            PackagingInfoModel.cases_per_pallet: item["cases_per_pallet"],
            PackagingInfoModel.units_per_pallet: item["units_per_pallet"],
        }


def get_packaging_info(skus: Iterable[SKU_CODE] = None) -> dict[SKU_CODE, PackagingInfo]:
    return {item.sku_code: item for item in packaging_info_repo.get_packaging_info(skus)}
