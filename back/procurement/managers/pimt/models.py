from collections.abc import Iterable
from decimal import Decimal
from functools import lru_cache

from procurement.constants.hellofresh_constant import MAX_SUPPLIER_LEAD_TIME_WEEKS, IngredientCategory
from procurement.core.typing import SITE, SKU_CODE
from procurement.data.dto.pimt.master_replenishment import MasterReplenishmentItem


class ShelfLifeCollection:
    PROTEIN_SHELF_LIFE = 365

    def __init__(self, shelf_life_by_site_sku: dict[tuple[SKU_CODE, SITE], int]):
        self._shelf_life = shelf_life_by_site_sku

    def get(self, site: str, sku_code: str, commodity_groups: Iterable[str]) -> int | None:
        if (shelf_life := self._shelf_life.get((sku_code, site))) is not None:
            return shelf_life
        if any(IngredientCategory.PROTEIN in cg for cg in filter(None, commodity_groups)):
            return self.PROTEIN_SHELF_LIFE
        return None

    def get_min(self, sites: Iterable[SITE], sku_code: SKU_CODE, commodity_groups: Iterable[str]) -> int | None:
        """
        :return: minimum shelf life across given sites
        """
        lives = (self.get(site, sku_code, commodity_groups) for site in sites)
        return min(filter(None, lives), default=None)


class MasterReplenishment:
    def __init__(self, master_replenishment: Iterable[MasterReplenishmentItem]):
        self.master_replenishment = {(r.sku_code, r.site): r for r in master_replenishment}
        # iterating over dict values to avoid exhausted generator or other one-time iterables
        self.shelf_lives = ShelfLifeCollection(
            {(r.sku_code, r.site): r.shelf_life for r in self.master_replenishment.values()}
        )
        self._sites = {r.site for r in self.master_replenishment.values()}

    def replenishments(self, sku_code: SKU_CODE, sites: Iterable[SITE] = None) -> list[MasterReplenishmentItem]:
        """
        :return: replenishment items for given sites or all sites available in this MasterReplenishment
        """
        return [
            r for r in (self.master_replenishment.get((sku_code, s)) for s in sites or self._sites) if r is not None
        ]

    @lru_cache(maxsize=54)
    def shelf_life(self, sku_code: SKU_CODE, category: str, sites: Iterable[SITE] = None) -> int | None:
        """
        :param sku_code: -
        :param category: purchasing category
        :param sites: if sites is omitted then uses all available sites in this MasterReplenishment
        :return: shelf life for the SKU
        """
        return self.shelf_lives.get_min(sites or self._sites, sku_code, [category])

    def reduced_replenishment(self, sku_code: SKU_CODE) -> MasterReplenishmentItem:
        """
        :return: aggregated replenishment item across all sites available in this MasterReplenishment
        """
        shelf_life = None
        max_supplier_lead_time = None
        replenishment_type = ""
        status = None
        site = None
        market = False
        market_factor = Decimal()
        min_order_qty = Decimal()
        buyers = set()
        request_for_proposal = Decimal()

        for item in self.replenishments(sku_code):
            site = item.site or site
            status = item.status or status
            request_for_proposal = item.request_for_proposal or request_for_proposal
            replenishment_type = item.replenishment_type or replenishment_type
            market = item.market or market
            market_factor = item.market_factor or market_factor
            min_order_qty = item.min_order_qty or min_order_qty
            request_for_proposal = item.request_for_proposal or request_for_proposal
            shelf_life = min(
                (sl for sl in (shelf_life, item.shelf_life) if sl is not None),
                default=None,
            )
            max_supplier_lead_time = max(
                (t for t in (item.max_supplier_lead_time, max_supplier_lead_time) if t is not None),
                default=None,
            )
            buyers.update(item.buyers)

        return MasterReplenishmentItem(
            site=next(iter(self._sites), None),
            sku_code=sku_code,
            status=status,
            replenishment_type=replenishment_type,
            shelf_life=shelf_life,
            request_for_proposal=request_for_proposal,
            max_supplier_lead_time=max_supplier_lead_time or MAX_SUPPLIER_LEAD_TIME_WEEKS,
            market_factor=market_factor,
            market=market,
            min_order_qty=min_order_qty,
            buyers=buyers,
        )
