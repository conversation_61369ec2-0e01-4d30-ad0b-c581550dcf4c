import logging
from collections import defaultdict
from collections.abc import Collection
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import NamedTuple

from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.typing import SKU_CODE, UNITS, WH_CODE
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.pimt import partners
from procurement.repository.ordering import purchase_order

logger = logging.getLogger(__name__)


class PimtPoData(NamedTuple):
    by_wh: dict[WH_CODE, dict[SKU_CODE, Decimal]]
    wh_total: dict[SKU_CODE, Decimal]
    total: UNITS


class PoDataByDcType(NamedTuple):
    core_data: dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]
    tpl_data: dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]


@log_wrapper
def get_po_inbound() -> PimtPoData:
    bob_code_to_wh = partners.get_bob_code_to_warehouse()
    delivery_from = _get_po_date_from()
    whs = partners.get_all_partners()
    po_by_wh = {wh.code: {} for wh in whs}
    wh_by_code: dict[str, Warehouse] = {wh.code: wh for wh in whs}
    total = defaultdict(Decimal)
    destinations = list(bob_code_to_wh.keys())

    for sku_po in purchase_order.get_pimt_inbound_orders(destinations, delivery_from):
        wh_code = bob_code_to_wh[sku_po.bob_code]
        wh_data = po_by_wh[wh_code]
        grn_qty = sku_po.grn_quantity if wh_by_code[wh_code].receiving_type.is_grn and sku_po.grn_quantity else 0
        total_quantity = max(sku_po.total_quantity - grn_qty, Decimal())
        wh_data[sku_po.sku_code] = wh_data.get(sku_po.sku_code, 0) + total_quantity
        total[sku_po.sku_code] += total_quantity

    return PimtPoData(by_wh=po_by_wh, wh_total=total, total=sum(total.values()))


@log_wrapper
def get_po_outbound() -> PimtPoData:
    delivery_from = _get_po_date_from()
    po_by_wh = {wh.code: {} for wh in partners.get_all_partners()}
    total = defaultdict(Decimal)
    suppliers_to_wh = partners.get_supplier_to_warehouse()
    wh_by_bob_code = {wh.bob_code: wh.code for wh in partners.get_all_partners()}

    for sku_po in purchase_order.get_pimt_outbound_orders(suppliers_to_wh.keys(), wh_by_bob_code.keys(), delivery_from):
        wh = suppliers_to_wh.get(sku_po.supplier) or wh_by_bob_code.get(sku_po.source_bob_code)
        wh_po_data = po_by_wh[wh]
        wh_po_data[sku_po.sku_code] = wh_po_data.get(sku_po.sku_code, 0) + sku_po.total_quantity
        total[sku_po.sku_code] += sku_po.total_quantity

    return PimtPoData(by_wh=po_by_wh, wh_total=total, total=sum(total.values()))


def _get_po_date_from():
    return datetime.now().date() - timedelta(days=3)


@log_wrapper
def get_po_inbound_by_weeks(weeks: list[ScmWeek]) -> dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]:
    bob_code_to_wh = partners.get_bob_code_to_warehouse()
    po_by_wh = {wh.code: {} for wh in partners.get_all_partners()}
    wh_by_code = {wh.code: wh for wh in partners.get_all_partners()}
    destinations = list(bob_code_to_wh.keys())

    for sku_po in purchase_order.get_pimt_inbound_orders_by_weeks(destinations, weeks):
        wh_code = bob_code_to_wh[sku_po.bob_code]
        grn_qty = sku_po.grn_quantity if wh_by_code[wh_code].is_e2open_grn and sku_po.grn_quantity else 0
        total_quantity = max(sku_po.total_quantity - grn_qty, Decimal())
        _update_wh_week_data(po_by_wh[wh_code], sku_po, total_quantity)

    return po_by_wh


@log_wrapper
def get_po_inbound_undelivered(weeks: list[ScmWeek]) -> dict[WH_CODE, dict[SKU_CODE, Decimal]]:
    bob_code_to_wh = partners.get_bob_code_to_warehouse()
    po_by_wh = {wh.code: defaultdict(Decimal) for wh in partners.get_all_partners()}
    wh_by_code = {wh.code: wh for wh in partners.get_all_partners()}
    destinations = list(bob_code_to_wh.keys())

    for sku_po in purchase_order.get_pimt_inbound_undelivered(destinations, weeks, date.today()):
        wh_code = bob_code_to_wh[sku_po.bob_code]
        grn_qty = sku_po.grn_quantity if wh_by_code[wh_code].is_e2open_grn and sku_po.grn_quantity else 0
        total_quantity = max(sku_po.total_quantity - grn_qty, Decimal())
        po_by_wh[wh_code][sku_po.sku_code] += total_quantity
    return po_by_wh


@log_wrapper
def get_po_outbound_by_weeks(
    weeks: list[ScmWeek], warehouses: Collection[Warehouse] | None = None
) -> dict[WH_CODE, dict[SKU_CODE, dict[ScmWeek, Decimal]]]:
    warehouses = warehouses or partners.get_all_partners()
    outbound_by_wh = {wh.code: {} for wh in warehouses}
    suppliers_to_wh = {supplier: wh.code for wh in warehouses for supplier in wh.ot_suppliers}
    suppliers = list(suppliers_to_wh.keys())
    wh_by_bob_code = {wh.bob_code: wh.code for wh in warehouses}

    for sku_po in purchase_order.get_pimt_outbound_orders_by_weeks(suppliers, wh_by_bob_code.keys(), weeks):
        _wh_code = suppliers_to_wh.get(sku_po.supplier) or wh_by_bob_code.get(sku_po.source_bob_code)
        _update_wh_week_data(outbound_by_wh[_wh_code], sku_po, sku_po.total_quantity)

    return outbound_by_wh


def _update_wh_week_data(wh_data: dict[SKU_CODE, dict[ScmWeek, Decimal]], sku, total_quantity: Decimal):
    if sku.sku_code not in wh_data:
        wh_data[sku.sku_code] = {}
    week = ScmWeek.from_str(sku.week)
    sku_data = wh_data[sku.sku_code]
    sku_data[week] = sku_data.get(week, Decimal()) + total_quantity
