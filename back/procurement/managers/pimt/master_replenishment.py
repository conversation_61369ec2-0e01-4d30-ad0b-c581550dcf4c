from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from procurement.client.googlesheets.googlesheet_utils import percents_to_decimal, validate_required_fields
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SITE, WH_CODE
from procurement.data.googlesheet_model.master_replenishment import MasterReplenishmentSheet
from procurement.data.models.pimt.replenishment import MasterReplenishmentModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.pimt import partners
from procurement.managers.pimt.models import MasterReplenishment
from procurement.repository.pimt import replenishment as replenishment_repository


class MasterReplenishmentSku(NamedTuple):
    sku_code: str
    wh_code: str


def update_master_replenishment():
    updates = []
    for row in gsheet_admin.read_gsheet(MasterReplenishmentSheet(), is_formatted=True):
        try:
            validate_required_fields(row, ["sku_code", "site"])
            updates.append(
                {
                    MasterReplenishmentModel.site: row.site,
                    MasterReplenishmentModel.sku_code: row.sku_code,
                    MasterReplenishmentModel.status: row.status,
                    MasterReplenishmentModel.market: row.market.lower() == "y",
                    MasterReplenishmentModel.replenishment_type: row.replenishment_type,
                    MasterReplenishmentModel.shelf_life: row.shelf_life,
                    MasterReplenishmentModel.max_supplier_lead_time: row.max_supplier_lead_time,
                    MasterReplenishmentModel.min_order_qty: Decimal(row.min_order_qty) if row.min_order_qty else None,
                    MasterReplenishmentModel.request_for_proposal: (
                        percents_to_decimal(row.request_for_proposal) if row.request_for_proposal else None
                    ),
                    MasterReplenishmentModel.market_factor: Decimal(row.market_factor) if row.market_factor else None,
                    MasterReplenishmentModel.replenishment_buyer: row.replenishment_buyer,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, f"Error while importing Replenishment for SKU={row.sku_code}, Site={row.site}", skipped=True
            )

    replenishment_repository.delete_master_repl_data()
    replenishment_repository.update_master_replenishment(updates)


@request_cache
def get_master_replenishment(sku_codes: set[str] | None = None) -> MasterReplenishment:
    return MasterReplenishment(replenishment_repository.get_master_replenishment(sku_codes))


def _build_warehouses_by_site(warehouses: frozenset[str]) -> dict[SITE, list[WH_CODE]]:
    warehouses_by_site = defaultdict(list)
    for warehouse in (wh for wh in partners.get_all_partners() if wh.code in warehouses):
        for site in warehouse.regional_dcs:
            warehouses_by_site[site].append(warehouse.code)
    return warehouses_by_site
