from procurement.constants.hellofresh_constant import BRAND_FJ, MARKET_US
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.inventory.ingredient import PkgData
from procurement.managers.admin import dc_admin
from procurement.managers.ordering import manufactured_sku
from procurement.repository.forecast import oscar
from procurement.repository.inventory import ingredient


def get_pkg_data(week: ScmWeek, site: str, brand: str) -> list[PkgData]:
    dc_object = dc_admin.get_site(brand=brand, site=site, week=week)
    pkg_name = dc_object.pkg_name if dc_object.pkg_name else site
    market = context.get_request_context().market
    if market != MARKET_US:
        items = oscar.get_canada_recipe_data(week, site, brand)
    elif brand == BRAND_FJ and killswitch.kafka_factor_pkg_consumption_enabled:
        items = manufactured_sku.load_factor_pkg_data(week)
    else:
        items = ingredient.load_pkg_data(week, pkg_name, brand)
    return sorted(
        items,
        key=lambda item: (0, int(item.slot)) if item.slot.isdigit() else (1, item.slot),
    )
