from collections import defaultdict
from collections.abc import Iterable
from datetime import datetime

from sqlalchemy import Select

from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import ORDER_NUMBER, SKU_CODE, PoSku
from procurement.data.dto.inventory.grn import GrnPo
from procurement.data.dto.inventory.receipt_override import ReceiptData
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import grn as grn_repo


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_grns_group_by_po_number_and_sku_code(
    weeks_dc_objects: list[tuple[ScmWeek, DcConfig]], sku_codes: set[str] = None
) -> dict[tuple[ORDER_NUMBER, SKU_CODE], list[GrnPo]]:
    grns = defaultdict(list)
    weeks = []
    dc_objects = []
    for week, dc_object in weeks_dc_objects:
        weeks.append(int(week))
        if dc_object.bob_code:
            dc_objects.append(dc_object)
    for item in grn_repo.get_grn_receive_data_by_week_bob_code_and_sku_code(
        weeks=weeks, dc_objects=dc_objects, sku_codes=sku_codes
    ):
        grns[(item.order_number, item.sku_code)].append(item)
    grns.default_factory = None
    return grns


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_sku_filtered_grn(
    dc_object: DcConfig, po_sku_filtering_query: Select
) -> dict[tuple[ORDER_NUMBER, SKU_CODE], list[GrnPo]]:
    grns = defaultdict(list)
    for item in grn_repo.get_grn_by_po_sku_query(dc_object, po_sku_filtering_query):
        grns[(item.order_number, item.sku_code)].append(item)
    grns.default_factory = None
    return grns


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_grn_receipt_by_receive_date(
    dc_object: DcConfig, date_from: datetime, date_to: datetime, weeks: list[ScmWeek] = None
) -> dict[tuple[ORDER_NUMBER, SKU_CODE], list[GrnPo]]:
    return _build_grn_receipt_data(
        grn_repo.get_grn_receive_data_by_receive_date_and_bob_code(date_from, date_to, dc_object, weeks),
    )


def _build_grn_receipt_data(grn: list[GrnPo]) -> dict[tuple[ORDER_NUMBER, SKU_CODE], list[GrnPo]]:
    grns = defaultdict(list)
    for item in grn:
        grns[(item.order_number, item.sku_code)].append(item)
    grns.default_factory = None
    return grns


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_grn_item_by_order_number_and_sku_code(order_number: str, sku_code: str, dc_object: DcConfig) -> list[GrnPo]:
    return grn_repo.get_grn_by_po_sku(order_number, sku_code, dc_object)


def get_received_weeks(dc_object: DcConfig, date_from: datetime, date_to: datetime) -> list[ScmWeek]:
    return grn_repo.get_grn_received_weeks_by_receive_date(dc_object=dc_object, date_from=date_from, date_to=date_to)


def get_grn_receipts_pos(week: ScmWeek, dc_object: DcConfig) -> list[GrnPo]:
    return grn_repo.get_grn_receive_data_by_week_bob_code_and_sku_code([int(week)], [dc_object])


def get_grn_skus_by_po(week: ScmWeek, po_number: str, dc_object: DcConfig, exclude_skus: list[str]) -> list[GrnPo]:
    return grn_repo.get_grn_receipt_skus(week, po_number, dc_object, exclude_skus)


def get_grn_receipts(week: ScmWeek, brand: str) -> list[ReceiptData]:
    sites = list(dc_admin.get_enabled_sites(week, brand).values())
    return grn_repo.get_grn_receipts(week, sites)


def get_received_keys(
    weeks: Iterable[ScmWeek],
    sites: Iterable[DcConfig] | None = None,
    whs: Iterable[Warehouse] | None = None,
    sku_codes: set[str] | None = None,
) -> set[PoSku]:
    grn_selectors = []
    if sites:
        grn_selectors.extend(
            (s.bob_code, s.receiving_type.grn_source_name) for s in sites if s.receiving_type.is_in_grn
        )
    if whs:
        grn_selectors.extend((wh.bob_code, wh.receiving_type.grn_source_name) for wh in whs if wh.receiving_type.is_grn)
    if not grn_selectors:
        return set()
    return grn_repo.get_received_keys(weeks=weeks, selectors=grn_selectors, sku_codes=sku_codes)
