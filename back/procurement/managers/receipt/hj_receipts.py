from collections.abc import Iterable

from procurement.core.dates import ScmWeek
from procurement.core.typing import PoSku
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository import highjump as hj_repo


def get_received_hj_keys(
    weeks: Iterable[ScmWeek],
    sites: Iterable[DcConfig],
    whs: Iterable[Warehouse],
    sku_codes: set[str] | None = None,
) -> set[PoSku]:
    hj_ids = set(s.high_jump_name for s in sites if s.receiving_type.is_high_jump)
    hj_ids.update(wh.hj_name for wh in whs if wh.receiving_type.is_hj)
    if not hj_ids:
        return set()
    return hj_repo.get_received_hj_keys(weeks=weeks, wh_ids=hj_ids, sku_codes=sku_codes)
