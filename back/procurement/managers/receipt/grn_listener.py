import logging
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Any

import pytz
from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext, StringDeserializer
from hellofresh.proto.stream.distribution_center.inbound.goods_received_note.v1 import record_pb2

from procurement.constants.hellofresh_constant import APP_TIMEZONE, GrnSource
from procurement.constants.protobuf import GRN_IMT_UOM_MAP
from procurement.core import utils
from procurement.core.cache_utils.caching import no_args_temp_cache
from procurement.core.dates import ScmWeek
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.typing import BOB_CODE, UNITS
from procurement.data.dto.inventory.grn import GrnInput
from procurement.managers import kafka_utils
from procurement.managers.admin import dc_admin
from procurement.managers.kafka_utils import build_datetime
from procurement.managers.pimt import partners
from procurement.repository.inventory import grn

logger = logging.getLogger(__name__)


class GrnHandler(kafka_handlers.KafkaHandler):
    _ACCEPTED_HEADERS = {s.encode() for s in GrnSource}

    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, record_pb2.GoodsReceivedNoteValue, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        source = self.get_source_header(msg)
        if source in self._ACCEPTED_HEADERS:
            _update_grn(msg.value(), source.decode())

    @staticmethod
    def get_source_header(msg: Message) -> bytes:
        return next((val for key, val in msg.headers() if key == "wmsName"), b"")


def _update_grn(value: record_pb2.GoodsReceivedNoteValue, source: str):
    site_by_bob_code = kafka_utils.get_site_by_bob_code_map()
    dc = site_by_bob_code.get(value.dc_code)
    known_bob_codes = kafka_utils.get_known_bob_codes()
    if value.dc_code not in known_bob_codes or (dc and not dc.enabled):
        return
    try:
        week = ScmWeek.from_short_number(int(value.reference[:4]))
    except ValueError:
        logger.warning("Invalid PO number format to extract SCM week: '%s'", value.reference)
        return
    grn_data = {}
    now = datetime.now()

    for delivery in value.deliveries:
        dc_tz = pytz.timezone(delivery.delivery_time.time_zone.id)
        receipt_time = dc_tz.localize(build_datetime(delivery.delivery_time)).astimezone(APP_TIMEZONE)

        for line in delivery.lines:
            key = (value.reference, line.sku_code, line.state)
            units_received, cases_received = _get_units_cases_received(line)
            if existing_grn := grn_data.get(key):
                existing_grn.receipt_time_est = max(
                    filter(None, (existing_grn.receipt_time_est, receipt_time)), default=None
                )
                existing_grn.units_received += units_received
                existing_grn.cases_received += cases_received
                continue
            uom = GRN_IMT_UOM_MAP.get(line.sku_uom)
            grn_data[key] = GrnInput(
                bob_code=value.dc_code,
                po_number=value.reference,
                order_number=utils.get_order_number_from_po_number(value.reference),
                sku_code=line.sku_code,
                week=week,
                units_received=units_received,
                cases_received=cases_received,
                receipt_time_est=receipt_time,
                status=line.state,
                unit=uom,
                update_ts=now,
                source=source,
            )
    grn.upsert_grn(grn_data.values(), known_bob_codes)
    if source == GrnSource.HJ:
        grn.upsert_legacy_grn_hj(grn_data.values(), site_by_bob_code)


def _get_units_cases_received(line: Any) -> tuple[UNITS, UNITS]:
    units_received = Decimal(line.palletized_quantity.value)
    case_size = Decimal(line.case_size.value)
    cases_received = units_received / case_size if case_size else 0
    return units_received, cases_received


@no_args_temp_cache(timedelta(minutes=2))
def _get_warehouse_bob_codes() -> set[BOB_CODE]:
    return {
        w.bob_code for m in dc_admin.get_markets(True) for w in partners.get_all_partners(market=m.code) if w.bob_code
    }
