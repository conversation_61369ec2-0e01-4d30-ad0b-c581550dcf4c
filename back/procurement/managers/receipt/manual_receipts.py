from collections.abc import Iterable

from procurement.core.dates import ScmWeek
from procurement.core.typing import PoSku
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import receiving


def get_received_manual_keys(
    weeks: Iterable[ScmWeek], sites: Iterable[DcConfig], sku_codes: set[str] | None = None
) -> set[PoSku]:
    manual_sites = [s for s in sites if s.receiving_type.is_manual]
    if not manual_sites:
        return set()
    return receiving.get_received_keys(weeks=weeks, sites=manual_sites, sku_codes=sku_codes)
