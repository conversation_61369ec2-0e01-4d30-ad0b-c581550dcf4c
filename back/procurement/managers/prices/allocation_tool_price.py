from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.allocation_tool_price import AllocationPriceInput
from procurement.data.models.inventory.allocation_price import AllocationPriceModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import allocation_tool_price as allocation_repo
from procurement.repository.snowflake import allocation_tool_price


class AllocationToolKey(NamedTuple):
    week: int
    site: str
    sku_code: str


def update_allocation_prices(brand: str) -> None:
    updates: dict[AllocationToolKey, list[AllocationPriceInput]] = defaultdict(list)
    weeks = set()
    for item in allocation_tool_price.get_allocation_prices_from_snowflake(brand=brand):
        key = AllocationToolKey(week=item.week, site=item.site, sku_code=item.sku_code)
        updates[key].append(item)
        weeks.add(item.week)

    normalized_updates = defaultdict(Decimal)

    for key, prices in updates.items():
        if total_percentage := sum(p.award_percentage for p in prices):
            normalized_updates[key] = sum(p.unit_price * p.award_percentage / total_percentage for p in prices)
        else:
            normalized_updates[key] = Decimal()

    allocation_repo.delete_allocation_prices(brand, weeks)

    allocation_repo.update_allocation_prices(
        updates=[
            {
                AllocationPriceModel.scm_week: key.week,
                AllocationPriceModel.brand: brand,
                AllocationPriceModel.site: key.site,
                AllocationPriceModel.sku_code: key.sku_code,
                AllocationPriceModel.price: value,
            }
            for key, value in normalized_updates.items()
        ]
    )


def get_allocation_prices(week: ScmWeek, dc_obj: DcConfig, brand: str) -> dict[SKU_CODE, Decimal]:
    return {
        row.sku_code: row.cost for row in allocation_repo.get_allocation_prices(week=week, dc_obj=dc_obj, brand=brand)
    }
