from procurement.managers.imt.purchase_order.po_status import PoShipmentResult, PoStatusResult
from procurement.managers.pimt.purchase_order import PoStatusData


def convert_po_status_data_to_result(po_status_data: list[PoStatusData]) -> list[PoStatusResult]:
    return [
        PoStatusResult(
            week=po_item.week,
            brand="",
            site=po_item.partner,
            po_number=po_item.po_number,
            po_status=po_item.po_status,
            receive_variance=po_item.receive_variance,
            supplier=po_item.supplier,
            sku_name=po_item.sku_name,
            sku_code=po_item.sku_code,
            scheduled_delivery_date=po_item.scheduled_delivery_date,
            order_size=po_item.order_size,
            order_unit=po_item.unit_of_measure,
            case_size=po_item.case_size,
            quantity_ordered=po_item.quantity_ordered,
            quantity_received=po_item.quantity_received,
            cases_received=po_item.case_received,
            case_size_received=po_item.case_size_received,
            emergency_reason=po_item.emergency_reason,
            case_price=po_item.case_price,
            total_price=po_item.total_price,
            ship_method=po_item.shipping_method,
            po_buyer=po_item.po_buyer,
            proposed_quantity_cases=po_item.proposed_quantity_cases,
            proposed_units_per_case=po_item.proposed_units_per_cases,
            proposed_quantity_units=po_item.proposed_quantity_units,
            proposed_delivery_date=po_item.proposed_delivery_date,
            total_price_received=po_item.total_price_received,
            asn_shipment_date=po_item.asn_shipment_date,
            asn_planned_delivery_time=po_item.asn_planned_delivery_time,
            asn_shipped_quantity_cases=po_item.asn_shipped_quantity_cases,
            asn_units_of_measure=po_item.asn_unit_of_measure,
            asn_case_count=po_item.asn_case_count,
            asn_shipped_quantity_units=po_item.asn_shipped_quantity_units,
            asn_requires_high_attention=po_item.asn_requires_high_attention,
            asn_requires_attention=po_item.asn_requires_attention,
            date_received=po_item.date_received.date() if po_item.date_received is not None else None,
            datetime_received=po_item.date_received,
            shipment_data=PoShipmentResult.from_shipment(po_item.shipment_data) if po_item.shipment_data else None,
            purchasing_unit=po_item.unit_of_measure,
            category=po_item.category,
            buyers=po_item.buyer,
            case_price_flag=None,
            allocation_price=None,
            percentage_of_the_forecasted=None,
            transfer_source_bob_code=po_item.transfer_source_bob_code,
            has_multiple_transfer_items=po_item.has_multiple_transfer_items,
        )
        for po_item in po_status_data
    ]
