from datetime import datetime, timedelta
from decimal import Decimal

from confluent_kafka.cimpl import Message
from google.type.money_pb2 import Money

from procurement.core.cache_utils.caching import no_args_temp_cache
from procurement.core.dates import ScmWeek
from procurement.core.typing import BOB_CODE, WH_CODE
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.pimt import partners

AVRO_TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"

_NANOS_KOEF = Decimal("1e-9")


def build_datetime(raw_datetime) -> datetime | None:
    if raw_datetime and raw_datetime.year > 0:
        return datetime(
            year=raw_datetime.year,
            month=raw_datetime.month,
            day=raw_datetime.day,
            hour=raw_datetime.hours,
            minute=raw_datetime.minutes,
            second=raw_datetime.seconds,
            microsecond=raw_datetime.nanos,
        )
    return None


def build_timestamp(raw_timestamp) -> datetime:
    return datetime.utcfromtimestamp(raw_timestamp.seconds)


def read_avro_datetime(datetime_str: str | None) -> datetime | None:
    if not datetime_str or datetime_str.startswith("0001"):
        return None
    valid_length = 27
    if len(datetime_str) > valid_length:  # most likely nanoseconds instead of micro
        datetime_str = datetime_str[: valid_length - 1] + "Z"
    try:
        return datetime.strptime(datetime_str, AVRO_TIME_FORMAT)
    except ValueError:
        return None


def get_message_timestamp(msg: Message) -> datetime:
    return datetime.utcfromtimestamp(msg.timestamp()[1] / 1000)


def parse_money(val: Money) -> Decimal:
    """
    Parses Money value as decimal.Decimal ignoring currency.
    :param val: pb money value
    :return: money value in decimal
    """
    return (Decimal(val.units) + Decimal(val.nanos) * _NANOS_KOEF).normalize()


def read_headers(msg: Message) -> dict[str, bytes]:
    return {h[0]: h[1] for h in msg.headers()}


@no_args_temp_cache(timedelta(minutes=5))
def get_site_by_bob_code_map() -> dict[BOB_CODE, DcConfig]:
    curr_week = ScmWeek.current_week()
    return {
        site.bob_code: site
        for week in (curr_week, curr_week + 11)  # to capture currently active DCs and any new DC in near future
        for market in dc_admin.get_markets(ignore_permissions=True)
        for brand in brand_admin.get_latest_brands(market=market.code)
        for site in dc_admin.get_all_dcs(week=week, brand=brand, market=market.code, only_enabled=False).values()
        if site.bob_code
    }


@no_args_temp_cache(timedelta(minutes=5))
def get_wh_by_bob_code() -> dict[BOB_CODE, WH_CODE]:
    return {
        wh.bob_code: wh.code
        for market in dc_admin.get_markets(ignore_permissions=True)
        for wh in partners.get_all_partners(market=market.code)
        if wh.bob_code
    }


@no_args_temp_cache(timedelta(minutes=5))
def get_known_bob_codes() -> set[str]:
    return set(get_site_by_bob_code_map().keys()).union(get_wh_by_bob_code())
