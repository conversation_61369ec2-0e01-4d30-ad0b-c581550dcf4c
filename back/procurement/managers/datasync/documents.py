from collections.abc import Iterable
from datetime import date

from procurement.client.slack import slack_client
from procurement.client.slack.formats import SlackFormat
from procurement.constants.hellofresh_constant import BRAND_HF, PKG_WEEKS_AHEAD
from procurement.core.dates.constants import Weekday
from procurement.core.dates.weeks import ScmWeek
from procurement.data.googlesheet_model.hybrid_needs import HybridNeedsDocModel, HybridNeedsDocModel3PL
from procurement.data.googlesheet_model.imt_data_dump import ImtDataDump3PLDocModel
from procurement.data.googlesheet_model.mock_plan_calculation import MockPlanCalculationDocModel
from procurement.data.googlesheet_model.pkg import NewPkgDocModelV3
from procurement.managers.admin import gsheet_admin

from .framework.runner import <PERSON>llelJobRunner


def submit_doc_availability_check():
    ParallelJobRunner.submit_single_step_job("weekly_doc_validation", _validate_docs)


def _validate_docs():
    if not (check_config := _get_current_day_config()):
        return
    missing_docs = gsheet_admin.check_weekly_docs_availability(check_config)
    if missing_docs:
        missing_docs_text = [
            f" - {doc.brand} '{doc.name}' for week{'s' if len(weeks) > 1 else ''} {', '.join(map(str, weeks))}"
            for (doc, weeks) in missing_docs
        ]
        slack_client.post(
            f"{SlackFormat.channel()}, following documents are missing:\n"
            + "\n".join(missing_docs_text)
            + "\nPlease add them in order to have accurate data within IMT views"
        )


# TODO: brands unification
def _get_current_day_config() -> dict[tuple[str, str], Iterable[ScmWeek]] | None:
    current_week = ScmWeek.current_week()
    current_and_next_week = [current_week, current_week + 1]
    current_and_future_weeks = list(ScmWeek.range(current_week, current_week + PKG_WEEKS_AHEAD))
    configs_by_day = {
        Weekday.MON: {
            (HybridNeedsDocModel.doc_code, BRAND_HF): current_and_next_week,
            (NewPkgDocModelV3.doc_code, BRAND_HF): current_and_future_weeks,
        },
        Weekday.TUE: {
            (ImtDataDump3PLDocModel.doc_code, BRAND_HF): current_and_next_week,
            (HybridNeedsDocModel3PL.doc_code, BRAND_HF): current_and_next_week,
        },
        Weekday.WED: {
            (MockPlanCalculationDocModel.doc_code, BRAND_HF): current_and_future_weeks,
        },
    }
    return configs_by_day.get(date.today().weekday())
