from datetime import date, timed<PERSON>ta

from procurement.constants.hellofresh_constant import BRAND_FJ
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.managers.admin import brand_admin
from procurement.managers.imt.export import po_status_finance
from procurement.managers.imt.export.po_status_finance import FinanceExportConfig
from procurement.managers.imt.purchase_order.po_status_export import po_status_export

from .framework.runner import ParallelJobRunner

EXPORT_PO_NAME = "PO status export to Gsheet"
WEEKLY_TEMPLATE = "Weekly Financial PO Export"
MONTHLY_TEMPLATE = "Monthly Financial PO Export"
CORE_FINANCIAL_DAILY_EXPORT_NAME = "Daily Financial PO Export"
CORE_FINANCIAL_WEEKLY_EXPORT_NAME = "HF, EP, GC " + WEEKLY_TEMPLATE
CORE_FINANCIAL_MONTHLY_EXPORT_NAME = "HF, EP, GC " + MON<PERSON><PERSON>Y_TEMPLATE
FACTOR_FINANCIAL_WEEKLY_EXPORT_NAME = "Factor " + WEEKLY_TEMPLATE
FACTOR_FINANCIAL_MONTHLY_EXPORT_NAME = "Factor " + MONTHLY_TEMPLATE


def launch_po_export():
    ParallelJobRunner.submit_single_step_job(EXPORT_PO_NAME, po_status_export, is_alert_enabled=False)


def launch_factor_weekly_financial_export(week: ScmWeek | None = None, market: str = None) -> None:
    market = market or context.get_request_context().market
    week = week or ScmWeek.current_week(brand_admin.get_latest_brands(market=market)[BRAND_FJ].scm_week_config) - 1
    ParallelJobRunner.submit_single_step_job(
        FACTOR_FINANCIAL_WEEKLY_EXPORT_NAME,
        po_status_finance.send_weekly_financial_email,
        config_name=FinanceExportConfig.FACTOR,
        week=week,
        market=market,
    )


def launch_core_weekly_financial_export(week: ScmWeek | None = None) -> None:
    week = week or ScmWeek.current_week() - 1
    ParallelJobRunner.submit_single_step_job(
        CORE_FINANCIAL_WEEKLY_EXPORT_NAME,
        po_status_finance.send_weekly_financial_email,
        config_name=FinanceExportConfig.CORE,
        week=week,
    )


def launch_factor_monthly_financial_export(export_date: date | None = None) -> None:
    export_date = export_date or _get_prev_month_date()
    ParallelJobRunner.submit_single_step_job(
        FACTOR_FINANCIAL_MONTHLY_EXPORT_NAME,
        po_status_finance.send_monthly_financial_email,
        config_name=FinanceExportConfig.FACTOR,
        export_date=export_date,
    )


def launch_core_monthly_financial_export(export_date: date | None = None) -> None:
    export_date = export_date or _get_prev_month_date()
    ParallelJobRunner.submit_single_step_job(
        CORE_FINANCIAL_MONTHLY_EXPORT_NAME,
        po_status_finance.send_monthly_financial_email,
        config_name=FinanceExportConfig.CORE,
        export_date=export_date,
    )


def launch_core_daily_financial_export(export_date: date | None = None) -> None:
    export_date = export_date or date.today()
    ParallelJobRunner.submit_single_step_job(
        CORE_FINANCIAL_DAILY_EXPORT_NAME,
        po_status_finance.send_daily_financial_email,
        config_name=FinanceExportConfig.CORE,
        export_date=export_date,
    )


def _get_prev_month_date() -> date:
    return date.today().replace(day=1) - timedelta(days=1)
