import dataclasses
import logging
from collections import defaultdict
from datetime import date
from decimal import Decimal
from functools import cached_property
from uuid import uuid4

from google.type import date_pb2, decimal_pb2
from hellofresh.proto.stream.ordering.sku_inventory_demand_forecast.week_based.v1 import (
    sku_inventory_demand_forecast_week_based_pb2 as sku_model,
)

from procurement.client.googlesheets import googlesheet_utils
from procurement.client.slack import slack_client
from procurement.client.slack.formats import SlackFormat
from procurement.constants.hellofresh_constant import (
    BRAND_FJ,
    BRAND_GC,
    MARKET_US,
    SITE_AU,
    SITE_BR,
    SITE_JO,
    SITE_SW,
    UnitOfMeasure,
)
from procurement.constants.protobuf import IMT_DEMAND_UOM_MAP, DemandUnitOfMeasure
from procurement.core import utils
from procurement.core.cache_utils.factory import CACHES
from procurement.core.config_utils import killswitch
from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek
from procurement.core.exceptions.base_errors import NestedException
from procurement.core.kafka import producers
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, SKU_CODE, UNITS
from procurement.data.dto.sku import SkuMetaWithBrands
from procurement.data.googlesheet_model.joliet_ope_skus import JolietOpeSkuSheet
from procurement.data.models.inventory.joliet_ope_skus import JolietOpeSkuModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.imt import hybrid_needs
from procurement.managers.imt.bulk_sku import BulkSkuManager, EmptyBulkSkuManager
from procurement.managers.imt.v2.inventory.calculation import calculations, daily_needs_service
from procurement.managers.imt.v2.inventory.calculation.core.models import DailyWeekAwareForecastData
from procurement.repository.inventory import joliet_ope_skus as joliet_ope_skus_repo
from procurement.repository.ordering import culinary_sku

from .framework import warnings
from .framework.runner import ParallelJobRunner

logger = logging.getLogger(__name__)

FORECAST_UPLOAD_NAME = "Kafka forecast Upload"

# pylint: disable=no-member
PB_ZERO = decimal_pb2.Decimal(value="0")

DailyWeekAwareForecastItem = dict[ScmWeek, UNITS]


def _default_dict_int():
    return defaultdict(int)


def _is_gc_sku(sku_code: str) -> bool:
    return sku_code.startswith(f"{BRAND_GC},")


@dataclasses.dataclass
class UploadStatistic:
    message_count: int = 0
    unique_sku: set[str] = dataclasses.field(default_factory=set)


class ForecastJobContext:
    def __init__(self, culinary_skus: dict[SKU_CODE, SkuMetaWithBrands], forced: bool):
        self.week_config = DEFAULT_WEEK_CONFIG
        self.date_from, self.date_to = calculations.get_forecast_date_range(self.week_config)
        self.week = ScmWeek.from_date(self.date_from, self.week_config)
        self.sku_mapping: dict[SKU_CODE, SkuMetaWithBrands] = culinary_skus
        self.not_found_in_culinary: set[SKU_CODE] = set()
        self.should_upload_count: int = 0
        self.uploaded_count: int = 0
        self.statistics: dict[tuple[BOB_CODE, ScmWeek], UploadStatistic] = defaultdict(UploadStatistic)
        self.unknown_uom: dict[UnitOfMeasure, set[SKU_CODE]] = defaultdict(set)
        # JO accumulation kept hardcoded because of SKU based aggregation, see ForecastJobContext.joliet_ope_skus
        self.accumulated_joliet_forecast: DailyWeekAwareForecastData = defaultdict(_default_dict_int)
        # SW accumulation is also SKU based
        self.accumulated_sw_forecast: DailyWeekAwareForecastData = defaultdict(_default_dict_int)
        self.aggregated_forecast: dict[BOB_CODE, DailyWeekAwareForecastData] = {}
        self.missed_bob_codes = set()
        self.brand_mapping = brand_admin.get_latest_brands()
        self.forced = forced

    def get_sku_code_mapping(self, sku_codes: set[SKU_CODE]) -> dict[SKU_CODE, SkuMetaWithBrands]:
        self.not_found_in_culinary.update(sku_codes.difference(self.sku_mapping))
        return self.sku_mapping

    def get_zero_forecast(self, brand: str) -> dict[date, dict[ScmWeek, Decimal]]:
        week_config = self.brand_mapping[brand].scm_week_config
        res = defaultdict(dict)
        for week in ScmWeek.range(*calculations.get_forecast_week_range(week_config)):
            for day in week.production_days(week_config):
                if day < self.date_from or day > self.date_to:
                    continue
                if brand == BRAND_FJ:
                    res[day] = {week: Decimal()}
                else:
                    res[day][week] = Decimal()
        return res

    def count_message_sent_stat(self, bob_code: str, week: ScmWeek, sku_code: str) -> None:
        if self.forced:
            # don't gather detailed statistic for force uploads since there will be a lot of SKUs and messages
            return
        statistic = self.statistics[(bob_code, week)]
        statistic.message_count += 1
        statistic.unique_sku.add(sku_code)

    @cached_property
    def all_sites(self) -> dict[BOB_CODE, DcConfig]:
        return {dc.bob_code: dc for dc in dc_admin.get_all_market_dcs(ignore_user=True)}

    @cached_property
    def aggregated_sites(self) -> dict[BOB_CODE, DcConfig]:
        """
        Returns the mapping of the sites which forecasts should be aggregated under one bob code.
        The mapping is
        {the bob code of the site that needs to be sent under different bob code} -> {the site to use for aggregation}.
        The aggregation site is also included under its own bob code if there is no other OPE bob code set for it.
        """
        aggregated_sites = {}
        if not killswitch.aggregated_forecast_upload_enabled:
            return aggregated_sites
        for dc in self.all_sites.values():
            if dc.ope_bob_code:
                ope_site = self.all_sites[dc.ope_bob_code]
                aggregated_sites[dc.bob_code] = ope_site
                if not ope_site.ope_bob_code:  # if it's not mapped to something different
                    # add the aggregated site itself to remove it from regular upload
                    aggregated_sites[ope_site.bob_code] = ope_site
        return aggregated_sites

    @cached_property
    def joliet_ope_skus(self) -> set[str]:
        return joliet_ope_skus_repo.get_joliet_ope_skus()

    @staticmethod
    def get_cache_data(bob_code: str) -> DailyWeekAwareForecastData:
        return CACHES.forecast_upload.get(bob_code) or {}

    @staticmethod
    def update_cache(bob_code: str, data: DailyWeekAwareForecastData) -> None:
        CACHES.forecast_upload.put(bob_code, data)


def launch_upload(initiated_by: str = "app schedule", force: bool = False) -> None:
    market = context.get_request_context().market
    ParallelJobRunner.submit_single_step_job(
        FORECAST_UPLOAD_NAME, upload_forecast_pipeline, initiated_by=initiated_by, force=force, market=market
    )


def _process_sku_date_item(
    cached_item: DailyWeekAwareForecastItem,
    new_item: DailyWeekAwareForecastItem,
    force: bool,
) -> DailyWeekAwareForecastItem:
    update_item = {}

    all_weeks = frozenset(cached_item).union(new_item)
    for week in all_weeks:
        new_value = new_item.get(week)
        cached_value = cached_item.get(week)
        # Item in cache and in new data (Should calculate change)
        if cached_value and new_value:
            # Always add current value if it's a force upload
            if cached_value != new_value or force:
                update_item[week] = new_value
        # Item is in cache and not 0 but not in new data (Should zero it)
        elif cached_value:
            update_item[week] = 0
        # Item is in new data and not 0 but not in cache (Should send it)
        elif new_value:
            update_item[week] = new_value
        # If forced and both values were 0 then keep it anyway
        elif force:
            update_item[week] = 0
        # else: do nothing if both values are zeros
    return update_item


def get_updates(
    normalized_data: DailyWeekAwareForecastData,
    cached_data: DailyWeekAwareForecastData,
    upload_start_date: date,
    force: bool = False,
) -> DailyWeekAwareForecastData:
    all_skus_dates = frozenset(cached_data).union(normalized_data)

    updates = {}

    for sku_date in all_skus_dates:
        if sku_date[1] < upload_start_date:
            continue
        try:
            updates[sku_date] = _process_sku_date_item(
                cached_data.get(sku_date, {}), normalized_data.get(sku_date, {}), force
            )
        except Exception:
            logger.warning("Failed to process item with sku %s , date %s", *sku_date)
            updates[sku_date] = cached_data.get(sku_date, {})
    return updates


def _prepare_kafka_data(
    updates: DailyWeekAwareForecastData, bob_code: str, job_context: ForecastJobContext
) -> list[tuple[str, str]]:
    updates_for_kafka = []
    for (sku_code, forecast_date), forecast_data in updates.items():
        sku_meta = job_context.sku_mapping.get(sku_code)
        if not forecast_data or not sku_meta:
            continue
        unit = IMT_DEMAND_UOM_MAP.get(sku_meta.unit) if sku_meta.unit else DemandUnitOfMeasure.UNIT
        if not unit:
            job_context.unknown_uom[sku_meta.unit].add(sku_code)
            continue
        for week, quantity in forecast_data.items():
            key = sku_model.WeekSkuInventoryDemandForecastKey(
                sku_id=sku_meta.sku_uuid,
                distribution_center_bob_code=bob_code,
                # pylint: disable=no-member
                date=date_pb2.Date(year=forecast_date.year, month=forecast_date.month, day=forecast_date.day),
                production_week=sku_model.WeekSkuInventoryDemandForecastKey.WeekDate(year=week.year, week=week.week),
            )
            value = sku_model.WeekSkuInventoryDemandForecastVal(
                # pylint: disable=no-member
                forecasted_demanded_qty=decimal_pb2.Decimal(value=str(quantity)),
                unit=unit,
                forecasted_needed_qty=PB_ZERO,
                production_week_start_stock=PB_ZERO,
            )
            updates_for_kafka.append((key.SerializeToString(), value.SerializeToString()))
            job_context.count_message_sent_stat(bob_code, week, sku_code)
    return updates_for_kafka


def _send_data_to_kafka(updates: list[tuple[str, str]]) -> None:
    for chunk in utils.chunked(updates, 5000):
        with producers.inv_demand_forecast.producing() as producer:
            for key, value in chunk:
                producer.produce_raw(key=key, value=value)


def _get_normalized_forecasts(
    dc_object: DcConfig, job_context: ForecastJobContext, use_delivery_date_needs: bool, bulk_manager: BulkSkuManager
) -> DailyWeekAwareForecastData:
    needs = daily_needs_service.get_daily_needs_by_dc_object(
        dc_object, job_context.date_from, job_context.date_to, use_delivery_date_needs
    )
    _merge_packaged_needs_to_bulk(needs, bulk_manager)
    normalized_data = normalize_data(needs, job_context)
    if dc_object.market == MARKET_US and dc_object.brand == BRAND_FJ:
        normalized_data = reduce_factor_forecast_days(normalized_data)
    if job_context.forced:
        _add_missing_skus(normalized_data, job_context, dc_object)
    return normalized_data


def _merge_packaged_needs_to_bulk(needs: DailyWeekAwareForecastData, bulk_manager: BulkSkuManager):
    if isinstance(bulk_manager, EmptyBulkSkuManager):
        return
    packaged_key_to_remove = []
    bulk_values_to_add = {}
    for (sku, day), by_week in needs.items():
        bulk_sku = bulk_manager.get_bulk_sku(sku)
        if not bulk_sku or not bulk_sku.is_master_sku:
            continue
        packaged_key_to_remove.append((sku, day))
        bulk_day_key = (bulk_sku.bulk_sku_code, day)
        bulk_data = bulk_values_to_add.get(bulk_day_key) or needs.get(bulk_day_key, {})
        for week, val in by_week.items():
            bulk_data[week] = bulk_data.get(week, 0) + val * bulk_sku.pick_conversion
        bulk_values_to_add[bulk_day_key] = bulk_data
    for key in packaged_key_to_remove:
        del needs[key]
    needs.update(bulk_values_to_add)


def _add_missing_skus(needs: DailyWeekAwareForecastData, job_context: ForecastJobContext, dc: DcConfig) -> None:
    """
    Adds missing SKUs with zero forecast for each day
    """
    skus_present = {sku for sku, _ in needs.keys()}

    zeros = job_context.get_zero_forecast(dc.brand)
    for sku_meta in job_context.sku_mapping.values():
        if sku_meta.sku_code in skus_present or job_context.brand_mapping[dc.brand].name not in sku_meta.brands:
            continue
        for day, forecasts in zeros.items():
            needs[(sku_meta.sku_code, day)] = forecasts


def normalize_data(
    fetcher_data: DailyWeekAwareForecastData,
    job_context: ForecastJobContext,
) -> DailyWeekAwareForecastData:
    sku_set = {sku_code for sku_code, _ in fetcher_data.keys()}
    normalized_data = defaultdict(dict)
    sku_meta = job_context.get_sku_code_mapping(sku_set)
    for (sku_key, date_key), needs_by_week in fetcher_data.items():
        if sku_key in sku_meta:
            normalized_data[(sku_key, date_key)] = {k: max(0, round(v, 2)) for k, v in needs_by_week.items()}
            job_context.should_upload_count += len(needs_by_week)
    return normalized_data


def reduce_factor_forecast_days(normalized_data: DailyWeekAwareForecastData) -> DailyWeekAwareForecastData:
    reduced_data = defaultdict(_default_dict_int)
    day_mapper = hybrid_needs.get_factor_needs_day_mapping_func(use_production_days=False)

    for (sku_code, day), forecasts in normalized_data.items():
        for week, value in (forecasts or {}).items():
            actual_day = day_mapper(week, day)
            reduced_data[(sku_code, actual_day)][week] += value
    return reduced_data


def _remove_joliet_forecast(job_context: ForecastJobContext, updates: DailyWeekAwareForecastData) -> None:
    for sku_day, forecasts in updates.items():
        if sku_day[0] not in job_context.joliet_ope_skus:
            continue

        for week, forecast in forecasts.items():
            job_context.accumulated_joliet_forecast[sku_day][week] += forecast

    for sku_day, week_values in job_context.accumulated_joliet_forecast.items():
        _normalize_week_values(week_values)
        updates.pop(sku_day, None)


def _remove_gc_skus_from_sw_forecasts(job_context: ForecastJobContext, updates: DailyWeekAwareForecastData) -> None:
    for (sku_code, day), forecasts in updates.items():
        sku = job_context.sku_mapping.get(sku_code)
        if not sku or not _is_gc_sku(sku.sku_name):
            continue

        for week, forecast in forecasts.items():
            job_context.accumulated_sw_forecast[(sku_code, day)][week] += forecast

    for sku_day, week_values in job_context.accumulated_sw_forecast.items():
        _normalize_week_values(week_values)
        updates.pop(sku_day, None)


def _normalize_week_values(week_values: dict[ScmWeek, UNITS]) -> None:
    for week, value in week_values.items():
        week_values[week] = round(value, 2)


def _send_forecast(  # pylint: disable=too-many-arguments
    normalized_data: DailyWeekAwareForecastData,
    site: str,
    brand: str,
    bob_code: str,
    job_context: ForecastJobContext,
):
    cached_data = job_context.get_cache_data(bob_code)
    updates = get_updates(normalized_data, cached_data, job_context.date_from, job_context.forced)

    kafka_data = _prepare_kafka_data(updates, bob_code, job_context)
    _send_data_to_kafka(kafka_data)
    job_context.uploaded_count += len(kafka_data)
    logger.info(
        "%s forecast items were sent for %s %s (bob=%s), forced=%s",
        len(kafka_data),
        brand,
        site,
        bob_code,
        job_context.forced,
    )

    job_context.update_cache(bob_code, normalized_data)


def _remove_accumulated_forecast_items(
    normalized_data: DailyWeekAwareForecastData, dc_object: DcConfig, job_context: ForecastJobContext
) -> None:
    if dc_object.sheet_name in (SITE_AU, SITE_BR) and dc_object.market == MARKET_US:
        _remove_joliet_forecast(job_context, normalized_data)
    if dc_object.sheet_name == SITE_SW and killswitch.combined_ep_gc_forecast_enabled:
        _remove_gc_skus_from_sw_forecasts(job_context, normalized_data)


def _process_forecast_by_brand(job_context: ForecastJobContext, brand: str):
    market = context.get_request_context().market
    is_factor_us = brand == BRAND_FJ and market == MARKET_US
    bulk_manager = BulkSkuManager(brand) if killswitch.kafka_ope_bulk_skus_enabled else EmptyBulkSkuManager()
    for dc_object in dc_admin.get_enabled_sites(job_context.week, brand).values():
        if not dc_object.bob_code:
            job_context.missed_bob_codes.add(dc_object.sheet_name)
            continue
        if dc_object.sheet_name == SITE_JO and is_factor_us:
            continue
        try:
            normalized_data = _get_normalized_forecasts(
                dc_object, job_context, use_delivery_date_needs=is_factor_us, bulk_manager=bulk_manager
            )
            _remove_accumulated_forecast_items(normalized_data, dc_object, job_context)

            if dc_object.bob_code in job_context.aggregated_sites:
                _add_aggregated_forecast(job_context, dc_object, normalized_data)
            else:
                _send_forecast(normalized_data, dc_object.sheet_name, dc_object.brand, dc_object.bob_code, job_context)

        except ValueError:
            continue

    if is_factor_us:
        _upload_joliet_forecast(job_context)


def upload_forecast_pipeline(initiated_by: str, force: bool = False) -> None:
    try:
        job_context = ForecastJobContext(culinary_sku.get_sku_meta_by_code(), force)

        logger.info("Uploading Kafka forecast for period %s - %s", job_context.date_from, job_context.date_to)
        for brand in brand_admin.get_brand_ids(job_context.week):
            _process_forecast_by_brand(job_context, brand)
        if killswitch.combined_ep_gc_forecast_enabled:
            _upload_sw_forecast(job_context)
        _upload_aggregated_sites(job_context)
    except Exception as exc:
        _process_after_error(initiated_by, exc)
    else:
        _process_no_error(job_context, initiated_by)


def _add_aggregated_forecast(
    job_context: ForecastJobContext, dc: DcConfig, normalized_data: DailyWeekAwareForecastData
) -> None:
    agg_bob_code = job_context.aggregated_sites[dc.bob_code].bob_code
    if agg_bob_code != dc.bob_code:
        # add empty values for the site that was aggregated to another bob code
        # that will push zeros for previous bob code to not duplicate values in OPE
        job_context.aggregated_forecast[dc.bob_code] = {}
    if not job_context.aggregated_forecast.get(agg_bob_code):
        job_context.aggregated_forecast[agg_bob_code] = normalized_data
        return
    agg_forecast = job_context.aggregated_forecast[agg_bob_code]
    for sku_date, week_values in normalized_data.items():
        agg_week_values = agg_forecast.get(sku_date, {})
        for week, val in week_values.items():
            agg_week_values[week] = agg_week_values.get(week, 0) + val
        agg_forecast[sku_date] = agg_week_values


def _upload_aggregated_sites(job_context: ForecastJobContext) -> None:
    for bob_code, agg_forecast in job_context.aggregated_forecast.items():
        for week_values in agg_forecast.values():
            _normalize_week_values(week_values)
        dc = job_context.all_sites[bob_code]
        _send_forecast(agg_forecast, site=dc.sheet_name, brand=dc.brand, bob_code=dc.bob_code, job_context=job_context)


def _upload_joliet_forecast(job_context: ForecastJobContext) -> None:
    _send_forecast(
        job_context.accumulated_joliet_forecast,
        site=SITE_JO,
        brand=BRAND_FJ,
        bob_code=SITE_JO,
        job_context=job_context,
    )


def _upload_sw_forecast(job_context: ForecastJobContext) -> None:
    _send_forecast(
        job_context.accumulated_sw_forecast,
        site=SITE_SW,
        brand=BRAND_GC,
        bob_code=SITE_SW,
        job_context=job_context,
    )


def _process_after_error(initiated_by: str, exc: Exception):
    incident_uuid = uuid4()
    slack_client.post_to_forecast(
        f"{SlackFormat.channel()} Kafka Forecast upload {context.get_request_context().market} failed. "
        f"Upload was initiated by {initiated_by} Incident {incident_uuid}"
    )
    raise NestedException(uuid=str(incident_uuid), from_exc=exc) from exc


def _process_no_error(job_context: ForecastJobContext, initiated_by: str):
    market = context.get_request_context().market
    logger.info(
        "Forecast upload completed for market %s. Total message count: %s.\n  Detailed stats:\n  %s",
        market,
        job_context.uploaded_count,
        "\n  ".join(
            f"[{key[0]}, {str(key[1])}] -- msg_count: {val.message_count}, unique_sku_count: {len(val.unique_sku)}"
            for key, val in job_context.statistics.items()
        ),
    )
    logger.debug("Full statistic: %s", job_context.statistics)
    msg = (
        f"Kafka Forecast upload {market} succeeded. Initiated by {initiated_by}.\n"
        f"Calculated forecast for {job_context.should_upload_count} items,\n"
        f"but uploaded {job_context.uploaded_count} items.\n"
    )
    if not job_context.forced and job_context.statistics:
        bob_codes_updated = {bob_code for bob_code, _ in job_context.statistics.keys()}
        weeks_updated = {week for _, week in job_context.statistics.keys()}
        msg += (
            f"Detailed stats:\n"
            f"  * {SlackFormat.bold("DCs updated (bob codes)")}: {bob_codes_updated}\n"  # noqa: E999
            f"  * {SlackFormat.bold("Weeks updated")}: {weeks_updated}\n"
        )
    if job_context.not_found_in_culinary:
        msg += (
            f"{SlackFormat.channel()}\n"
            f"But Failed to get sku_uuid by sku_code from Culinary for {job_context.not_found_in_culinary}\n"
        )
    if job_context.unknown_uom:
        missing_uoms_str = "\n".join(f"{uom}: {skus}" for uom, skus in job_context.unknown_uom.items())
        msg += f"Following SKUs were not pushed due to unknown mapping for their UOMs\n {missing_uoms_str}\n"
    if job_context.missed_bob_codes:
        bob_code_warning = (
            f"WARNING: the following sites were not uploaded because "
            f"of missing Bob Code {job_context.missed_bob_codes}.\n"
        )
        warnings.notify_warning(bob_code_warning)
        msg = bob_code_warning + msg
    slack_client.post_to_forecast(msg)


def import_joliet_ope_skus():
    sheet_data = gsheet_admin.read_gsheet(JolietOpeSkuSheet())
    if not sheet_data:
        return
    updates = []
    for row in sheet_data:
        try:
            googlesheet_utils.validate_required_fields(row, ["should_include", "sku_code"])
            if row.should_include and row.sku_code:
                updates.append({JolietOpeSkuModel.sku_code: row.sku_code})
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, f"Error while importing {JolietOpeSkuSheet.sheet_name} data", skipped=True
            )
    joliet_ope_skus_repo.delete_joliet_ope_skus()
    joliet_ope_skus_repo.insert_joliet_ope_skus(updates)
