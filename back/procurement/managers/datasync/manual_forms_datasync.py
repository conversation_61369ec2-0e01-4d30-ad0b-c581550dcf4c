import logging
from datetime import timedelta

from procurement.constants.hellofresh_constant import BRAND_HF
from procurement.core.config_utils import config, killswitch
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ManualFormScheduleExportEvent
from procurement.core.typing import SITE
from procurement.data.googlesheet_model.imt_data_dump import ProcurementInputs3PL
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.imt import inventory
from procurement.repository.ordering import po_void

from .framework.runner import <PERSON>lle<PERSON><PERSON><PERSON><PERSON>un<PERSON>
from .framework.step import Step

ENTRY_TYPE_VOID_PO = "Void PO"
ENTRY_TYPE_PULL = "Pull"
ENTRY_TYPE_PUT = "Inventory"
INPUT_EXPORT_JOB_NAME = "Export Manual Input {week}"

logger = logging.getLogger(__name__)


@EVENT_BUS.subscribe(ManualFormScheduleExportEvent)
def schedule_export(event: ManualFormScheduleExportEvent) -> None:
    if killswitch.export_enabled:
        logger.info("%s job scheduled.", INPUT_EXPORT_JOB_NAME.format(week=event.week))
        runner = _export_pipeline_runner(
            week=event.week, postpone_timeout=timedelta(seconds=config.get("export", {}).get("postpone_timeout", 120))
        )
        if not runner:
            return None

        runner.submit_if_not_pending()
    return None


def _export_pipeline_runner(week: ScmWeek, postpone_timeout: timedelta | int) -> ParallelJobRunner | None:
    fast_update = config.get("export", {}).get("fast_update", False)
    runner = ParallelJobRunner(INPUT_EXPORT_JOB_NAME.format(week=week))

    for brand in brand_admin.get_brand_ids(week):
        tpl_sites = {site.sheet_name for site in dc_admin.get_enabled_sites(week, brand).values() if site.is_3pl}
        if not tpl_sites:
            continue

        runner.add_next_step(
            Step(
                f"Exporting {brand} Pull Put Void Manual Inputs",
                _execute_pull_put_void_export,
                postpone_timeout=postpone_timeout,
                week=str(week),
                brand=brand,
                sites=tpl_sites,
                fast_update=fast_update,
            )
        )

    if not runner.queue:
        return None

    return runner


def _execute_pull_put_void_export(week: str, brand: str, sites: set[SITE], fast_update: bool) -> None:
    for sheet_name in sites:
        model = ProcurementInputs3PL(sheet_name)
        data = _get_pull_put_void_export_data(week, sheet_name, brand)
        logger.info("Exporting %s %s %s Pull/Put/Void records.", len(data), sheet_name, brand)
        # all 3PL data is stored in HF document
        gsheet_admin.update_gsheet(model=model, week=week, brand=BRAND_HF, data=data, fast_update=fast_update)


def _get_pull_put_void_export_data(week, dc, brand) -> list[dict]:
    return [
        {
            "entry_type": ENTRY_TYPE_PULL if item.qty < 0 else ENTRY_TYPE_PUT,
            "sku_name": item.sku_name,
            "quantity": abs(item.qty),
        }
        for item in inventory.export_pull_put_items(week, dc, brand)
    ] + [
        {"entry_type": ENTRY_TYPE_VOID_PO, "po_number": item.po_number, "sku_name": item.sku_name}
        for item in po_void.get_voided_po_number_and_sku_name((week,), dc, brand)
    ]
