import logging

from procurement.core.dates import ScmWeek
from procurement.data.googlesheet_model.imt_data_dump import Receive3PL, ReceiveRow
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.receive import ReceivingModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory import receiving
from procurement.services.database import app_db

from ...client.googlesheets import googlesheet_utils
from .framework import warnings

logger = logging.getLogger(__name__)


def load_data(week: ScmWeek, brand: str) -> list[dict]:
    """
    Imports all brands data from the :param brand: document (practically from the HF doc)
    """
    result = []
    sites = [s for b in brand_admin.get_brands(week) for s in dc_admin.get_enabled_sites(week, b).values() if s.is_3pl]
    read_count = 0

    for dc_object in sites:
        if not dc_object.receiving_type.is_manual:
            continue
        data = _load_receiving(week, brand, dc_object)
        warnings.notify_records_errors(data, skipped=False)
        for item in data:
            read_count += 1

            try:
                googlesheet_utils.validate_required_fields(
                    item,
                    ["receipt_tmst", "truck_arrival_date", "supplier", "po", "sku_code", "user", "ingredient_name"],
                )

                result.append(
                    {
                        ReceivingModel.username: item.user,
                        ReceivingModel.supplier: item.supplier,
                        ReceivingModel.po: item.po,
                        ReceivingModel.sku_ingredient: item.ingredient_name,
                        ReceivingModel.total_cases_received: item.cases_received,
                        ReceivingModel.case_count_one_total_units: item.case_count_1_units,
                        ReceivingModel.case_count_two_total_units: item.case_count_2_units,
                        ReceivingModel.case_count_three_total_units: item.case_count_3_units,
                        ReceivingModel.sku_code: item.sku_code,
                        ReceivingModel.source: DataSource.GSHEET.name,
                        ReceivingModel.brand: dc_object.brand,
                        ReceivingModel.dc: dc_object.sheet_name,
                        ReceivingModel.receive_timestamp: googlesheet_utils.parse_datetime(item.receipt_tmst),
                        ReceivingModel.arrival_timestamp: googlesheet_utils.parse_date(item.truck_arrival_date),
                        ReceivingModel.week: str(week),
                        ReceivingModel.case_size_one: item.cases_size_1,
                        ReceivingModel.case_size_two: item.cases_size_2,
                        ReceivingModel.case_size_three: item.cases_size_3,
                        ReceivingModel.delivery_status: "Active",
                    }
                )

            except ValueError as exc:
                warnings.notify_sheet_error(
                    item, exc, "Unable to parse row of Non HJ Receipt data", dc=dc_object.sheet_name, skipped=True
                )

    with app_db.transaction() as transaction:
        receiving.remove_imported_receiving_data(week, sites=[s.sheet_name for s in sites], transaction=transaction)
        receiving.add_receiving_data(result, transaction)

    logger.info(
        "Finished import Non HJ Receive data for week %s; read count: %s; success: %s", week, read_count, len(result)
    )

    return result


def _load_receiving(week: ScmWeek, brand: str, dc_object: DcConfig) -> list[ReceiveRow]:
    data = gsheet_admin.read_gsheet(Receive3PL(dc_object.sheet_name), week=week, brand=brand)
    warnings.notify_records_errors(data, skipped=False)
    return data
