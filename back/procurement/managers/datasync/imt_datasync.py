import logging
from functools import cached_property

from procurement.constants.hellofresh_constant import BRAND_FJ, BRAND_HF, MARKET_CA, MARKET_US, MOCK_PLAN_WEEKS_AHEAD
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import BulkSkusChangedEvent
from procurement.core.request_utils import context
from procurement.data.googlesheet_model.alternative_sku_mapping import AlternativeSkuDocModel, RwaSkuDocModel
from procurement.data.googlesheet_model.consolidated_packaging_demand import ConsolidatedPackagingDemandModel
from procurement.data.googlesheet_model.cycle_counts_gc import CycleCountGcDocModel
from procurement.data.googlesheet_model.gsheet_inventory import IMT_INVENTORY_MODELS, ImtInventoryDocModel
from procurement.data.googlesheet_model.hybrid_needs import HybridNeedsDocModel, HybridNeedsDocModel3PL
from procurement.data.googlesheet_model.imt_data_dump import ImtDataDump3PLDocModel
from procurement.data.googlesheet_model.joliet_ope_skus import JolietOpeSkus
from procurement.data.googlesheet_model.master_replenishment import MasterReplenishmentDocModel
from procurement.data.googlesheet_model.mock_plan_calculation import MockPlanCalculationDocModel
from procurement.data.googlesheet_model.packaging_demand import InWeekPackagingDemand, PackagingSkuDemand
from procurement.data.googlesheet_model.pkg import NewPkgDocModel, NewPkgDocModelV3, PkgDocModel
from procurement.data.googlesheet_model.staged_inventory import StagedInventory
from procurement.data.googlesheet_model.total_procurement_purchasing import TotalProcurementPurchasing
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel, PackagingSkuModel
from procurement.managers import ics
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.datasync import hj_datasync
from procurement.managers.distribution_center import cycle_counts
from procurement.managers.forecasts import oscar
from procurement.managers.forms import discarding
from procurement.managers.imt import (
    ambient_safety_stock_packaging,
    commodity_groups,
    consolidated_packaging_demand,
    hybrid_needs,
    mock_plan_calculation,
    packaging_inventory,
    strategy_manager,
)
from procurement.managers.imt.export import bulk_sku_export
from procurement.managers.imt.product_kit_guide import ingredients as pkg_ingredients
from procurement.managers.inventory import gsheet as gsheet_inventory
from procurement.managers.ordering import staged_inventory
from procurement.managers.pimt import master_replenishment
from procurement.managers.prices import allocation_tool_price
from procurement.managers.sku import alternative_sku_mapping
from procurement.repository.inventory import gsheet_admin as gs_repo

from . import forecast_upload, receive_datasync
from .framework.runner import ParallelJobRunner
from .framework.step import Step

logger = logging.getLogger(__name__)

FUTURE_PKG_NAME = "Future PKG sync"
IMT_SYNC_NAME = "IMT Sync {week}"
BULK_SKUS_EXPORT_TO_S3_SYNC_NAME = "Send Bulk SKUs export to S3"


def launch_sync_with_shift_from_current_week(market: str | None = None, shift_from_current: int = 0):
    market = market or context.get_request_context().market
    scm_week = ScmWeek.current_week() + shift_from_current
    sync_imt_pipeline(week=scm_week, market=market).submit_if_not_in_progress()


def launch_sync_for_week(week: ScmWeek):
    sync_imt_pipeline(week=week, upload_forecast=False).submit_if_not_in_progress()


class SyncBuilder:
    def __init__(self, brand: str, sync_week: ScmWeek, market: str, alert_enabled: bool = True):
        self.brand = brand
        self.week = sync_week
        self.steps = {}
        self.brand_config = brand_admin.get_brands(week=self.week, market=market)[self.brand]
        self.market = market

        self._current_week = ScmWeek.current_week(self.brand_config.scm_week_config)
        self._brand_docs = {gsheet.doc_code for gsheet in gs_repo.get_meta(brand=self.brand, market=market)}
        self._alert_enabled = alert_enabled

        self._doc_codes_to_steps = {
            NewPkgDocModel.doc_code: [self._import_sku_and_recipes],
            NewPkgDocModelV3.doc_code: [self._import_sku_and_recipes],
            HybridNeedsDocModel3PL.doc_code: [self._import_hybrid_needs_3pl],
            ImtDataDump3PLDocModel.doc_code: [
                self._import_3pl_non_hj_receive,
                self._import_3pl_discards,
                self._import_3pl_cycle_counts,
            ],
            RwaSkuDocModel.doc_code: [self._import_rwa_sku_mapping],
            InWeekPackagingDemand.doc_code: [self._import_packaging_demand],
            ConsolidatedPackagingDemandModel.doc_code: [self._import_consolidated_packaging_demand],
        }

    def _add_current_week_steps(self) -> None:
        self._doc_codes_to_steps.update(
            {
                TotalProcurementPurchasing.doc_code: [self._import_commodity_group],
                AlternativeSkuDocModel.doc_code: [self._import_organic_sku_mapping, self._import_packaging_sku_mapping],
                CycleCountGcDocModel.doc_code: [self._import_gc_cycle_counts],
                JolietOpeSkus.doc_code: [self._import_joliet_ope_skus],
                MasterReplenishmentDocModel.doc_code: [self._import_master_replenishment],
                StagedInventory.doc_code: [self._import_staged_inventory],
            }
        )
        self._add_hj_steps()
        self._add_mock_plan()
        self._add_gsheet_inventory_import()
        self._add_oscar_snowflake_step()
        self._add_allocation_price_step()
        self._add_ambient_safety_stock_data()
        self._add_strategy_manager_step()
        self._add_ics_ticket_import_step()
        self._add_sf_canada_packaging_demand_step()
        self._add_topo_sku_import()

    def get_pipeline(self) -> ParallelJobRunner:
        pipeline = ParallelJobRunner(
            name=f"sync for {self.brand} for {self.week} started",
            market=self.brand_config.market,
            is_alert_enabled=self._alert_enabled,
        )
        self._add_hybrid_needs_steps()
        self._add_pkg_step()

        if self.week == self._current_week:
            self._add_current_week_steps()

        for doc_code in self._brand_docs:
            self.steps.update(dict.fromkeys(self._doc_codes_to_steps.get(doc_code, [])))
        if self.steps:
            pipeline.add_next_step(Step(f"Run sync for {self.brand}", _build_root_imt_sync_step)).add_next_step(
                *self.steps
            )

        return pipeline

    def _get_branded_name(self, job_name: str):
        return f"{self.brand} {job_name}"

    @cached_property
    def _import_hj_pallets(self) -> Step:
        return Step(self._get_branded_name("Importing HJ Pallet"), hj_datasync.update_hj_pallet, brand=self.brand)

    @cached_property
    def _import_hj_receipts(self) -> Step:
        return Step(self._get_branded_name("Importing HJ Receipts"), hj_datasync.update_hj_receipts, brand=self.brand)

    @cached_property
    def _import_hj_wip(self) -> Step:
        return Step(self._get_branded_name("Importing HJ WIP"), hj_datasync.update_hj_wip, brand=self.brand)

    @cached_property
    def _import_hj_discard(self) -> Step:
        return Step(self._get_branded_name("Importing HJ Discard"), hj_datasync.update_hj_discard, brand=self.brand)

    @cached_property
    def _import_sku_and_recipes(self) -> Step:
        return Step(
            self._get_branded_name("Importing SKU and Recipes"),
            pkg_ingredients.update_mealkit_and_ingredients_tables,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_oscar(self) -> Step:
        return Step(self._get_branded_name("Importing Oscar"), oscar.update_data_by_brand, brand=self.brand)

    @cached_property
    def _import_hybrid_needs(self) -> Step:
        return Step(
            self._get_branded_name("Importing Hybrid Needs"), hybrid_needs.update_data, week=self.week, brand=self.brand
        )

    @cached_property
    def _import_hybrid_needs_3pl(self) -> Step:
        return Step(
            self._get_branded_name("Importing Hybrid Need 3PL"),
            hybrid_needs.update_data,
            week=self.week,
            brand=self.brand,
            is_3pl=True,
        )

    @cached_property
    def _import_hybrid_needs_snowflake(self) -> Step:
        return Step(
            self._get_branded_name("Importing Snowflake Hybrid Needs"),
            hybrid_needs.update_hn_snowflake,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_delivery_date_needs(self) -> Step:
        return Step(
            self._get_branded_name("Importing Delivery Date Need"),
            hybrid_needs.upsert_delivery_date_needs,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_commodity_group(self) -> Step:
        return Step(
            self._get_branded_name("Importing Categories and Buyers"), commodity_groups.update_categories_and_buyers
        )

    @cached_property
    def _import_organic_sku_mapping(self) -> Step:
        return Step(
            self._get_branded_name("Importing Organic SKU mapping"),
            alternative_sku_mapping.update_alternative_sku_mapping,
            model=OrganicSkuModel,
        )

    @cached_property
    def _import_packaging_sku_mapping(self) -> Step:
        return Step(
            self._get_branded_name("Importing Packaging SKU mapping"),
            alternative_sku_mapping.update_alternative_sku_mapping,
            model=PackagingSkuModel,
        )

    @cached_property
    def _import_rwa_sku_mapping(self) -> Step:
        return Step(
            self._get_branded_name("Importing RWA SKU mapping"),
            alternative_sku_mapping.update_rwa_sku_mapping,
        )

    @cached_property
    def _import_3pl_non_hj_receive(self) -> Step:
        return Step(
            self._get_branded_name("Import 3PL Non HJ Receive"),
            receive_datasync.load_data,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_3pl_cycle_counts(self) -> Step:
        return Step(
            self._get_branded_name("Import 3PL Cycle Counts"),
            cycle_counts.import_3pl_cycle_counts_data_to_db,
            brand=self.brand,
            week=self.week,
        )

    @cached_property
    def _import_3pl_discards(self) -> Step:
        return Step(
            self._get_branded_name("Importing 3PL Discarding Ingredients"),
            discarding.import_data_to_db,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_packaging_demand(self) -> Step:
        return Step(
            self._get_branded_name(f"Import {PackagingSkuDemand.sheet_name}"),
            packaging_inventory.update_packaging_demand_data,
            str_week=str(self.week),
            brand=self.brand,
        )

    @cached_property
    def _import_consolidated_packaging_demand(self) -> Step:
        return Step(
            self._get_branded_name("Import Consolidated Packaging Demand"),
            consolidated_packaging_demand.update_consolidated_packaging_demand_data,
        )

    @cached_property
    def _import_ambient_safety_stock_data(self) -> Step:
        return Step(
            self._get_branded_name("Import Ambient Safety Stock Data"),
            ambient_safety_stock_packaging.update_ambient_safety_stock_data,
        )

    def _import_mock_plan(self, week: ScmWeek) -> Step:
        return Step(
            self._get_branded_name(f"Importing Mock Plan Calculation for week {week}"),
            mock_plan_calculation.update_compiled_data,
            week=week,
            brand=self.brand,
        )

    def _import_factor_mock_plan(self, week: ScmWeek) -> Step:
        return Step(
            f"Importing Factor Mock Plan Calculation for week {week}",
            mock_plan_calculation.update_compiled_data,
            week=week,
            brand=BRAND_FJ,
        )

    @cached_property
    def _import_hybrid_needs_shift_level(self) -> Step:
        return Step(
            self._get_branded_name("Importing Hybrid Needs Shift-Level"),
            hybrid_needs.update_shift_level_data,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_oscar_snowflake(self) -> Step:
        return Step(
            self._get_branded_name("Importing Oscar from snowflake"),
            oscar.update_forecast_snowflake,
            week=self.week,
            brand=self.brand,
        )

    @cached_property
    def _import_gc_cycle_counts(self) -> Step:
        return Step(
            self._get_branded_name("Import GC Cycle Counts"),
            cycle_counts.import_gc_cycle_counts_data_to_db,
            week=self.week,
        )

    @cached_property
    def _import_joliet_ope_skus(self) -> Step:
        return Step(self._get_branded_name("Import Joliet OPE SKUs"), forecast_upload.import_joliet_ope_skus)

    @cached_property
    def _import_allocation_prices(self) -> Step:
        return Step(
            self._get_branded_name("Import Allocation Prices"),
            allocation_tool_price.update_allocation_prices,
            brand=self.brand,
        )

    def _import_gsheet_inventory(self, bob_code: str) -> Step:
        return Step(
            self._get_branded_name(f"Import Gsheet Inventory for {bob_code}"),
            gsheet_inventory.update_gsheet_inventory,
            wh_code=bob_code,
            models=IMT_INVENTORY_MODELS,
        )

    @cached_property
    def _import_master_replenishment(self) -> Step:
        return Step("Import Master Replenishment", master_replenishment.update_master_replenishment)

    @cached_property
    def _import_staged_inventory(self) -> Step:
        return Step("Import Staged Inventory", staged_inventory.import_staged_inventory)

    def _add_ambient_safety_stock_data(self) -> None:
        if self.brand == BRAND_HF and self.market == MARKET_US:
            self.steps.update(dict.fromkeys([self._import_ambient_safety_stock_data]))

    def _add_strategy_manager_step(self):
        if self.brand == BRAND_HF and self.market == MARKET_US:
            step = Step(
                "Importing Strategy Manager from snowflake",
                strategy_manager.update_strategy_manager_snowflake,
            )
            self.steps[step] = None

    def _add_ics_ticket_import_step(self) -> None:
        if self.brand == BRAND_HF and self.market == MARKET_US:
            step = Step("Import ICS Tickets", ics.import_ics_tickets)
            self.steps[step] = None

    def _add_oscar_snowflake_step(self) -> None:
        # GD-5119: Canada snowflake forecast view is available only on staging
        if self.market == MARKET_US or killswitch.use_oscar_for_ca_forecast:
            self.steps[self._import_oscar_snowflake] = None

    def _add_allocation_price_step(self):
        if self.market == MARKET_US:
            self.steps[self._import_allocation_prices] = None

    def _add_hj_steps(self) -> None:
        self.steps.update(dict.fromkeys([self._import_hj_pallets, self._import_hj_wip, self._import_hj_discard]))
        if not killswitch.hj_grn_enabled:
            self.steps[self._import_hj_receipts] = None

    def _add_pkg_step(self) -> None:
        if self.brand != BRAND_FJ:
            self._doc_codes_to_steps.update({PkgDocModel.doc_code: [self._import_sku_and_recipes]})
        elif not killswitch.kafka_factor_pkg_consumption_enabled:
            self._doc_codes_to_steps.update({PkgDocModel.doc_code: [self._import_sku_and_recipes]})

    def _add_mock_plan(self) -> None:
        if MockPlanCalculationDocModel.doc_code in self._brand_docs:
            self.steps.update(
                dict.fromkeys(
                    [
                        self._import_mock_plan(week)
                        for week in ScmWeek.range(self.week + 1, self.week + MOCK_PLAN_WEEKS_AHEAD)
                    ]
                )
            )
            # Factor mock plans are also located in HF document, so adding these steps here
            if self.brand == BRAND_HF:
                self.steps.update(
                    dict.fromkeys(
                        [
                            self._import_factor_mock_plan(week)
                            for week in ScmWeek.range(self.week + 1, self.week + MOCK_PLAN_WEEKS_AHEAD)
                        ]
                    )
                )

    def _add_gsheet_inventory_import(self) -> None:
        if ImtInventoryDocModel.doc_code in self._brand_docs:
            self.steps.update(
                dict.fromkeys(
                    [
                        self._import_gsheet_inventory(site.bob_code)
                        for brand in brand_admin.get_brand_ids()
                        for site in dc_admin.get_enabled_sites(week=self.week, brand=brand, market=self.market).values()
                        if site.inventory_type.is_gsheet
                    ]
                )
            )

    def _add_hybrid_needs_steps(self) -> None:
        gsheet_sites = []
        snowflake_sites = []

        for site in dc_admin.get_enabled_sites(self.week, self.brand, market=self.market).values():
            if site.hybrid_needs_data_source.is_snowflake():
                snowflake_sites.append(site.sheet_name)
            else:
                gsheet_sites.append(site.sheet_name)

        if snowflake_sites:
            self.steps[self._import_hybrid_needs_snowflake] = None

        if gsheet_sites:
            hn_steps = [self._import_hybrid_needs, self._import_hybrid_needs_shift_level]
        else:
            hn_steps = []

        if self.brand == BRAND_FJ:
            hn_steps.append(self._import_delivery_date_needs)

        self._doc_codes_to_steps[HybridNeedsDocModel.doc_code] = hn_steps

    def _add_sf_canada_packaging_demand_step(self) -> None:
        if killswitch.consume_new_packaging_demand and self.market == MARKET_CA and self.brand == BRAND_HF:
            self.steps[self._import_packaging_demand_from_snowflake] = None
        if killswitch.consume_new_packaging_demand:
            self._doc_codes_to_steps.pop(ConsolidatedPackagingDemandModel.doc_code, None)

    def _add_topo_sku_import(self) -> None:
        if self.brand == BRAND_HF and self.market == MARKET_US:
            self.steps[self._import_topo_sku] = None

    @cached_property
    def _import_topo_sku(self) -> Step:
        return Step(
            self._get_branded_name("Importing Topo SKU List"),
            gsheet_inventory.update_topo_sku_list,
        )

    @cached_property
    def _import_packaging_demand_from_snowflake(self) -> Step:
        return Step(
            self._get_branded_name("Importing Packaging Demand from Snowflake"),
            consolidated_packaging_demand.update_packaging_demand_data_snowflake,
            week=self.week,
        )


def sync_imt_pipeline(week: ScmWeek, upload_forecast: bool | None = None, market: str | None = None):
    market = market or context.get_request_context().market
    _upload_forecast = (week == ScmWeek.current_week()) if upload_forecast is None else upload_forecast
    runner = (
        ParallelJobRunner(IMT_SYNC_NAME.format(week=week), market=market, is_alert_enabled=False)
        .add_next_step(Step(f"Root step of the Imt datasync on {week}", _build_root_imt_sync_step))
        .add_next_step(
            *(
                SyncBuilder(brand, week, market, alert_enabled=False).get_pipeline()
                for brand in brand_admin.get_brands(week=week, market=market)
            )
        )
    )
    if killswitch.forecast_upload_enabled and _upload_forecast:
        runner.add_next_step(Step("Schedule forecast upload", forecast_upload.launch_upload, is_hidden=True))
    return runner


def _build_root_imt_sync_step():
    """Empty function to build the root step"""
    return


def launch_future_pkg_sync():
    steps = []
    for brand_code, brand in brand_admin.get_brands(week=ScmWeek.current_week(), market=MARKET_US).items():
        if brand_code == BRAND_FJ and killswitch.kafka_factor_pkg_consumption_enabled:
            continue
        steps.append(_build_future_pkg_runner(ScmWeek.current_week(brand.scm_week_config), brand_code))
    ParallelJobRunner(FUTURE_PKG_NAME).add_next_step(*steps).submit_if_not_in_progress()


def _build_future_pkg_runner(sync_week: ScmWeek, brand: str) -> ParallelJobRunner:
    runner = ParallelJobRunner(f"Importing future {brand} SKU and Recipes")
    for week in ScmWeek.range(sync_week + 1, sync_week + 5):
        runner.add_next_step(
            Step(
                f"Importing future SKU and Recipes for {brand} week {week}",
                pkg_ingredients.update_mealkit_and_ingredients_tables,
                week=week,
                brand=brand,
            )
        )
    return runner


@EVENT_BUS.subscribe(BulkSkusChangedEvent)
def launch_bulk_skus_export_to_s3(_: BulkSkusChangedEvent = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        BULK_SKUS_EXPORT_TO_S3_SYNC_NAME, bulk_sku_export.upload_bulk_skus_csv_to_s3
    )
