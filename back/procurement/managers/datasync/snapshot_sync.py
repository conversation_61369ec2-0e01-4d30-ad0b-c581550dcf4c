import logging
from collections import defaultdict
from datetime import datetime, time, timedelta
from decimal import Decimal
from typing import Any, Callable

from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import APP_TIMEZONE, InventoryInputType
from procurement.core import config_utils, utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.models.inventory.weekly_snapshot import WeeklySnapshotModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.inventory import snapshots
from procurement.repository.highjump import pallet_snapshot
from procurement.repository.inventory import inventory_snapshot
from procurement.repository.inventory import weekly_snapshots as weekly_snapshot_repo

from .framework.runner import ParallelJobRunner

logger = logging.getLogger(__name__)

SCHEDULE_SNAPSHOT_START_OF_DAY_INVENTORY = "Schedule snapshot start of day inventory {brands}"
SNAPSHOT_START_OF_DAY_INVENTORY = "Snapshot start of day inventory {timezone}"
SNAPSHOT_OUTDATED_ALERT = "Inventory snapshot outdated check"


def schedule_snapshot_start_of_day_inventory(brands: list[str], market: str = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        job_name=SCHEDULE_SNAPSHOT_START_OF_DAY_INVENTORY.format(brands=brands),
        market=market or context.get_request_context().market,
        func=_schedule_snapshot_start_of_day_inventory,
        brands=brands,
    )


def _schedule_snapshot_start_of_day_inventory(brands: list[str]) -> None:
    enabled_brands = brand_admin.get_latest_brands()
    week_configs = {enabled_brands[b].scm_week_config for b in brands}
    if not week_configs:
        raise ValueError(f"No active brands among {brands}")
    if len(week_configs) > 1:
        raise ValueError("All brands should have same SCM Week config")

    dcs = list(filter(lambda dc: dc.brand in brands, dc_admin.get_all_market_dcs(ignore_user=True)))

    schedule_at_dc_local_time(
        local_time=time(3, 0),
        dcs=dcs,
        tz_templated_name=f"{SNAPSHOT_START_OF_DAY_INVENTORY} {brands}",
        func=_do_weekly_snapshot,
    )


def schedule_at_dc_local_time(
    local_time: time, dcs: list[DcConfig], tz_templated_name: str, func: Callable[[list[DcConfig]], None]
) -> None:
    """
    Schedules a job at DC local time. DCs with same timezone will be processed in one job.
    :param local_time: DC local time to run at
    :param dcs: list of DCs to run for
    :param tz_templated_name: job name in format of "Job Name {timezone}"
    :param func: function to run
    """
    tz_dict = utils.group_by(dcs, lambda dc: dc.timezone)

    for timezone, _dcs in tz_dict.items():
        local_now = datetime.now(APP_TIMEZONE)
        dc_sync_time = timezone.localize(datetime.combine(local_now.date(), local_time))
        postpone_timeout = max(dc_sync_time - local_now, timedelta())
        job_name = tz_templated_name.format(timezone=timezone)
        postpone_sec = None if config_utils.is_ci else timedelta(seconds=postpone_timeout.seconds)

        logger.info("Scheduled '%s' for %s to run in %s", job_name, [dc.bob_code for dc in _dcs], postpone_timeout)
        ParallelJobRunner.submit_single_step_job(
            job_name,
            func,
            postpone_timeout=postpone_sec,
            dcs=_dcs,
        )


def _do_weekly_snapshot(dcs: list[DcConfig]) -> None:
    WeeklySnapshotWriter(dcs).save_snapshot()


class WeeklySnapshotWriter:
    def __init__(self, dcs: list[DcConfig]):
        self._dcs = dcs
        self._week = ScmWeek.current_week(brand_admin.get_week_config(dcs[0].brand))
        self._int_week = int(self._week)
        self._market = context.get_request_context().market

        self._snapshots = defaultdict(Decimal)

    def save_snapshot(self):
        wmsl_sites = []
        hj_sites = []
        logger.info(
            "Saving start of day inventory snapshot for sites %s", [(dc.brand, dc.site_name) for dc in self._dcs]
        )
        for site in self._dcs:
            if site.inventory_type == InventoryInputType.WMSL:
                wmsl_sites.append(site)
            if site.inventory_type == InventoryInputType.HJ:
                hj_sites.append(site)

        self._process_hj(hj_sites)
        self._process_wmsl(wmsl_sites)

        weekly_snapshot_repo.delete_weekly_snapshots(
            wh_codes=[dc.bob_code for dc in self._dcs], market=self._market, week=self._week
        )
        for chunk in utils.chunked(self._build_inserts(), 2000):
            weekly_snapshot_repo.insert_weekly_snapshots(chunk)

    def _process_hj(self, hj_sites: list[DcConfig]) -> None:
        if not hj_sites:
            return
        hj_name_to_bob_codes = defaultdict(list)
        for site in hj_sites:
            hj_name_to_bob_codes[site.high_jump_name].append(site.bob_code)

        for pallet in pallet_snapshot.get_hj_pallet_snapshot(hj_name_to_bob_codes):
            for bob_code in hj_name_to_bob_codes[pallet.wh_id]:
                self._snapshots[(bob_code, pallet.sku_code, pallet.expiration_date)] += pallet.pallet_quantity

    def _process_wmsl(self, wmsl_sites: list[DcConfig]) -> None:
        if not wmsl_sites:
            return
        for snapshot in inventory_snapshot.get_latest_snapshots([site.bob_code for site in wmsl_sites]):
            self._snapshots[(snapshot.bob_code, snapshot.sku_code, snapshot.expiration_date)] += snapshot.units

    def _build_inserts(self) -> list[dict[ColumnElement, Any]]:
        return [
            {
                WeeklySnapshotModel.market: self._market,
                WeeklySnapshotModel.wh_code: bob_code,
                WeeklySnapshotModel.sku_code: sku_code,
                WeeklySnapshotModel.week: self._int_week,
                WeeklySnapshotModel.quantity: quantity,
                WeeklySnapshotModel.expiration_date: expiration_date,
            }
            for (bob_code, sku_code, expiration_date), quantity in self._snapshots.items()
        ]


def launch_snapshot_date_outdated(market: str = None) -> None:
    market = market or context.get_request_context().market
    ParallelJobRunner.submit_single_step_job(SNAPSHOT_OUTDATED_ALERT, snapshots.alert_outdated_snapshots, market=market)
