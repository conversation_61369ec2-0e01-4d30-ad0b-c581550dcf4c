from procurement.core import config_utils
from procurement.core.cache_utils.factory import CACHES
from procurement.core.request_utils import context

from .framework import queries
from .framework.job_spec import JobSpec
from .job_constants import JobsEnum


def get_filtered_jobs() -> dict[str, JobSpec]:
    permissions = context.get_request_context().user_permissions
    filtered_jobs = {
        name: job for name, job in JobsEnum.get_all().items() if job.permission.read in permissions.permissions
    }

    for job in filtered_jobs.values():
        job.fill_runs()

    return filtered_jobs


def get_info(job_spec: JobSpec, arguments: dict):
    job_name = job_spec.name_template.format(**arguments)
    if config_utils.is_ci:
        # Sync tests are polling job status every few seconds to check if it's
        # completed, cache would add delay to every sync test.
        return queries.get_job_info(job_name)
    return CACHES.job_info.get(job_name, lambda: queries.get_job_info(job_name))
