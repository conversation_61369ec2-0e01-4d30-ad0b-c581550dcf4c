import logging

from procurement.constants.hellofresh_constant import MARKET_US, WAREHOUSE_FIDELITY
from procurement.core.request_utils import context
from procurement.data.googlesheet_model.gsheet_inventory import CANADA_PIMT_INVENTORY_MODELS, PIMT_INVENTORY_MODELS
from procurement.managers.inventory import gsheet as gsheet_inventory
from procurement.managers.pimt import inventory, ops_main_dashboard, partners, pimt_daily_exception_report
from procurement.managers.pimt.export import pimt_export, pimt_po_status_export
from procurement.repository.inventory.inventory import SNAPSHOT_LIFETIME

from .framework.runner import ParallelJobRunner
from .framework.step import Step

logger = logging.getLogger(__name__)


PIMT_SYNC_NAME = "PIMT Sync"
PIMT_HJ_SYNC_NAME = "PIMT HJ Sync"
PIMT_EXCEPTION_REPORT_SYNC_NAME = "PIMT Daily Exceptions Report"
PIMT_3PW_REPORT_SYNC_NAME = "PIMT Monthly 3PW Report"
PIMT_INVENTORY_ALERT_CHECK_SYNC_NAME = "PIMT Inventory update alert check"
PIMT_CSAC_EXPORT_SYNC_NAME = "PIMT CSAC Export"
PIMT_OPS_MAIN_DASHBOARD_PROTEIN_EXPORT_SYNC_NAME = "PIMT Ops Main Dashboard Protein Export"
PIMT_OPS_MAIN_DASHBOARD_GDP_EXPORT_SYNC_NAME = "PIMT Ops Main Dashboard G/D/P Export"
PIMT_STRATEGY_EXPORT_SYNC_NAME = "PIMT Strategy Main Dashboard Export"
PIMT_REPLENISHMENT_EXPORT_SYNC_NAME = "PIMT Replenishment Export"


def launch_pimt_sync(market: str = None) -> None:
    sync_pimt_pipeline(market=market).submit_if_not_in_progress()


def launch_pimt_hj_sync():
    ParallelJobRunner.submit_single_step_job(
        PIMT_HJ_SYNC_NAME, inventory.update_wh_hj_inventory, is_alert_enabled=False
    )


def sync_pimt_pipeline(market: str = None) -> ParallelJobRunner:
    market = market or context.get_request_context().market
    job = ParallelJobRunner(PIMT_SYNC_NAME, market=market)
    delete_old_inventory_snapshots = Step(
        f"Delete inventory snapshots older than {SNAPSHOT_LIFETIME} days", inventory.delete_old_inventory_records
    )
    fidelity_import_step = Step("Import Fidelity Inventory", gsheet_inventory.update_fidelity_inventory)
    for step in (
        *_build_gsheet_inventory_steps(market=market),
        delete_old_inventory_snapshots,
        fidelity_import_step,
    ):
        job.add_next_step(step)

    return job


def _build_gsheet_inventory_steps(market: str) -> list[Step]:
    return [
        Step(
            f"Importing {wh.code} Data",
            gsheet_inventory.update_gsheet_inventory,
            wh_code=wh.code,
            models=PIMT_INVENTORY_MODELS if market == MARKET_US else CANADA_PIMT_INVENTORY_MODELS,
        )
        for wh in partners.get_old_inventory_warehouses(market)
        if WAREHOUSE_FIDELITY not in wh.name
    ]


def launch_csac_export():
    ParallelJobRunner.submit_single_step_job(
        PIMT_CSAC_EXPORT_SYNC_NAME, pimt_export.csac_export, is_alert_enabled=False
    )


def launch_ops_main_protein_export():
    ParallelJobRunner.submit_single_step_job(
        PIMT_OPS_MAIN_DASHBOARD_PROTEIN_EXPORT_SYNC_NAME, pimt_export.ops_main_protein_export, is_alert_enabled=False
    )


def launch_ops_main_gdp_export():
    ParallelJobRunner.submit_single_step_job(
        PIMT_OPS_MAIN_DASHBOARD_GDP_EXPORT_SYNC_NAME, pimt_export.ops_main_gdp_export, is_alert_enabled=False
    )


def launch_strategy_export():
    ParallelJobRunner.submit_single_step_job(
        PIMT_STRATEGY_EXPORT_SYNC_NAME, pimt_export.strategy_export, is_alert_enabled=False
    )


def launch_replenishment_export():
    ParallelJobRunner.submit_single_step_job(
        PIMT_REPLENISHMENT_EXPORT_SYNC_NAME, pimt_export.replenishment_export, is_alert_enabled=False
    )


def launch_pimt_daily_exception_report():
    ParallelJobRunner.submit_single_step_job(
        PIMT_EXCEPTION_REPORT_SYNC_NAME, pimt_daily_exception_report.pimt_daily_report
    )


def launch_pimt_monthly_3pw_report():
    ParallelJobRunner.submit_single_step_job(PIMT_3PW_REPORT_SYNC_NAME, pimt_po_status_export.po_export)


def launch_pimt_update_date_alert():
    ParallelJobRunner.submit_single_step_job(PIMT_INVENTORY_ALERT_CHECK_SYNC_NAME, ops_main_dashboard.alert_update_date)
