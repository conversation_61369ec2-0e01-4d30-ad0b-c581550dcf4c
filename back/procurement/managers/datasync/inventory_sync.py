from collections.abc import Iterable
from datetime import datetime
from typing import Any

from procurement.client.s3_service import S3BucketName
from procurement.constants.hellofresh_constant import MARKET_CA, MARKET_US
from procurement.core.request_utils import context
from procurement.data.dto.inventory.unified_inventory import UnifiedInventoryDto
from procurement.managers.datasync.framework.runner import Parallel<PERSON>obRunner
from procurement.managers.imt.export import s3_export
from procurement.managers.imt.export.export_base import ExportCsv, Row, TabularData
from procurement.managers.inventory.unified_inventory import UnifiedInventoryManager

EXPORT_JOB_NAME = "ExportInventoryToS3"


def schedule_inventory_export_to_s3():
    ParallelJobRunner.submit_single_step_job(EXPORT_JOB_NAME, _inventory_export_to_s3, market=MARKET_US)


class InventoryExportCsvData(TabularData):
    def __init__(self, export_data: list[UnifiedInventoryDto]):
        super().__init__("Inventory CSV export to S3", export_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Site Code",
                    "SKU Code",
                    "Units",
                    "Expiration Date",
                    "Status",
                    "Location ID",
                    "LP/Lot Code",
                    "PO Number",
                    "Supplier",
                    "Snapshot Timestamp",
                ]
            )
        ]

    def _get_row_data(self, raw_item: UnifiedInventoryDto) -> Iterable[Any]:
        return (
            raw_item.bob_code,
            raw_item.sku_code,
            raw_item.units,
            raw_item.expiration_date,
            raw_item.status,
            raw_item.location_id,
            raw_item.lot_code,
            raw_item.po_number,
            raw_item.supplier,
            raw_item.snapshot_timestamp,
        )


def get_inventory_data() -> list[UnifiedInventoryDto]:
    inventory_manager = UnifiedInventoryManager()
    inv = inventory_manager.get_live_inventory(wip_inventory_included=True)

    return [
        UnifiedInventoryDto(
            source=it.source,
            bob_code=(
                inventory_manager.all_warehouses.get(it.bob_code).bob_code
                if it.bob_code in inventory_manager.all_warehouses
                else it.bob_code
            ),
            po_number=it.po_number,
            sku_code=it.sku_code,
            units=it.units,
            status=it.status,
            snapshot_timestamp=it.snapshot_timestamp,
            lot_code=it.lot_code,
            location_id=it.location_id,
            expiration_date=it.expiration_date,
            supplier=it.supplier,
        )
        for it in inv
    ]


def _inventory_export_to_s3():
    export_date = datetime.now().replace(microsecond=0)
    data = get_inventory_data()
    market = context.get_request_context().market
    try:
        context.get_request_context().market = MARKET_CA
        data.extend(get_inventory_data())
    finally:
        context.get_request_context().market = market
    csv_data = ExportCsv(InventoryExportCsvData(data)).write().binary
    filename = f"us_ca_inventory_snapshot_{export_date.isoformat(sep='_')}.csv"
    s3_export.export_to_s3(
        file=csv_data,
        file_name=filename,
        bucket=S3BucketName.IMT_FORMS,
        folder="us_ca_inventory_snapshot",
    )
