import logging
from datetime import time

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.config_utils import killswitch
from procurement.core.request_utils import context
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import highjump

from . import snapshot_sync
from .framework.runner import ParallelJobRunner

logger = logging.getLogger(__name__)

DAILY_HJ_INVENTORY_SCHEDULE_SYNC_NAME = "Daily HJ inventory schedule"
SCHEDULE_UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME = "Schedule update HJ Packaging Pallet Snapshot"
UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME = "Update HJ Packaging Pallet Snapshot {timezone}"
HJ_INV_SYNC_TIMEZONE_NAME = "HJ Inventory Sync for {timezone}"
WIP_CONSUMPTION_SYNC_NAME = "Import WIP Consumption"


def update_hj_pallet(brand: str) -> None:
    highjump.update_pallet_snapshot(brand=brand)


def update_hj_receipts(brand: str) -> None:
    highjump.update_receipts(brand=brand)


def update_hj_wip(brand: str) -> None:
    highjump.update_wip(brand=brand)


def update_hj_wip_consumption() -> None:
    for brand in brand_admin.get_brand_ids():
        highjump.update_hj_wip_consumption(brand=brand)


def update_hj_discard(brand: str) -> None:
    highjump.update_hj_discard(brand=brand)


def submit_wip_consumption(market: str = None) -> None:
    market = market or context.get_request_context().market
    if killswitch.wip_consumption_import_enabled:
        ParallelJobRunner.submit_single_step_job(WIP_CONSUMPTION_SYNC_NAME, update_hj_wip_consumption, market=market)


def submit_hj_inv_schedule_job_us() -> None:
    ParallelJobRunner.submit_single_step_job(
        DAILY_HJ_INVENTORY_SCHEDULE_SYNC_NAME, _submit_hj_inv_schedule_job_us, market=MARKET_US
    )


def _submit_hj_inv_schedule_job_us() -> None:
    dcs = [dc for dc in dc_admin.get_all_market_dcs() if dc.receiving_type.is_high_jump]
    snapshot_sync.schedule_at_dc_local_time(
        local_time=time(5, 0),
        dcs=dcs,
        tz_templated_name=HJ_INV_SYNC_TIMEZONE_NAME,
        func=update_hj_inv_site,
    )


def update_hj_inv_site(dcs: list[DcConfig]) -> None:
    for dc in dcs:
        highjump.update_inv_snapshot(dc)


def schedule_packaging_inventory_update(at_time: time = None, market: str = None) -> None:
    market = market or context.get_request_context().market
    at_time = at_time or (time(5, 30) if market == MARKET_US else time(3, 0))
    ParallelJobRunner.submit_single_step_job(
        SCHEDULE_UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME,
        _schedule_packaging_inventory_update,
        market=market,
        at_time=at_time,
    )


def _schedule_packaging_inventory_update(at_time: time) -> None:
    all_dcs = highjump.get_dcs_for_hj_inventory_sync()
    snapshot_sync.schedule_at_dc_local_time(
        local_time=at_time,
        dcs=all_dcs,
        tz_templated_name=UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME,
        func=highjump.update_packaging_pallet_snapshot,
    )
