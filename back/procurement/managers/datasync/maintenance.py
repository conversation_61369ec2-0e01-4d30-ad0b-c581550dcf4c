from procurement.managers.distribution_center import highjump
from procurement.repository.inventory import ingredient
from procurement.repository.procurement import parallel_sync_log

from .framework.runner import ParallelJobRunner

CLEANUP_JOB_NAME = "Clean Up parallel sync log"


def submit_cleanup_job():
    ParallelJobRunner.submit_single_step_job(CLEANUP_JOB_NAME, _cleanup_db)


def _cleanup_db():
    parallel_sync_log.clean_up_parallel_sync_log()
    ingredient.clean_up_categories()
    ingredient.clean_up_commodity_groups()
    highjump.delete_old_inv_snapshots()
