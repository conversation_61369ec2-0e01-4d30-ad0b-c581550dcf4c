from datetime import date

from procurement.constants.hellofresh_constant import PartOfDay
from procurement.managers.dc_inventory.export import network_depletion
from procurement.managers.imt.export.imt_daily_export import daily_export

from .framework.runner import ParallelJobRunner

PACKAGING_DEPLETION_DAILY_EXPORT_SYNC_NAME = "IMT Packaging Depletion Daily Export"
WEEKLY_LINER_GUIDANCE_DAILY_EXPORT_SYNC_NAME = "IMT Weekly Liner Guidance Depletion Daily Export"
PACKAGING_PO_STATUS_DAILY_EXPORT_SYNC_NAME = "IMT Packaging PO Status Daily Export {part_of_day}"
FACTOR_PO_STATUS_DAILY_EXPORT_SYNC_NAME = "IMT Factor PO Status Daily Export"
FACTOR_NETWORK_DEPLETION_MODULE_EXPORT_SYNC_NAME = "Factor Network Depletion Export {part_of_day}"


def launch_packaging_depletion_daily_export(export_date: date = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        PACKAGING_DEPLETION_DAILY_EXPORT_SYNC_NAME,
        daily_export.packaging_depletion_daily_export,
        export_date=export_date,
    )


def launch_weekly_liner_guidance_daily_export(export_date: date = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        WEEKLY_LINER_GUIDANCE_DAILY_EXPORT_SYNC_NAME,
        daily_export.weekly_liner_guidance_daily_export,
        export_date=export_date,
    )


def launch_packaging_po_status_daily_export(part_of_day: PartOfDay, export_date: date = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        PACKAGING_PO_STATUS_DAILY_EXPORT_SYNC_NAME.format(part_of_day=part_of_day),
        daily_export.packaging_po_status_daily_export,
        part_of_day=part_of_day,
        export_date=export_date,
    )


def launch_factor_po_status_daily_export(export_date: date = None, **kwargs) -> None:  # pylint: disable=unused-argument
    ParallelJobRunner.submit_single_step_job(
        FACTOR_PO_STATUS_DAILY_EXPORT_SYNC_NAME, daily_export.factor_po_status_daily_export, export_date=export_date
    )


def launch_factor_network_depletion_daily_export(part_of_day: PartOfDay, export_date: date = None) -> None:
    ParallelJobRunner.submit_single_step_job(
        FACTOR_NETWORK_DEPLETION_MODULE_EXPORT_SYNC_NAME.format(part_of_day=part_of_day),
        network_depletion.factor_network_depletion_daily_export,
        part_of_day=part_of_day,
        export_date=export_date,
    )
