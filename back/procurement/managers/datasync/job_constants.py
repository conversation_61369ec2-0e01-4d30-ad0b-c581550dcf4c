import enum

from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import BRAND_HF, MARKET_CA, MARKET_US
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.request_utils import context
from procurement.managers.imt.export import manual_forms_export

from . import (
    allowed_buffers_upload,
    forecast_upload,
    hj_datasync,
    imt_daily_exception_export,
    imt_datasync,
    inventory_sync,
    maintenance,
    pimt_datasync,
    po_export_datasync,
    snapshot_sync,
)
from .framework.job_spec import (
    BoolJobArgument,
    DateJobArgument,
    JobSpec,
    PartOfDayJobArgument,
    StringJobArgument,
    WeekJobArgument,
)

_timezone_arg = StringJobArgument(key="timezone", label="Timezone", description="Timezone", required=True)


class JobsEnum(enum.Enum):
    IMT_SYNC = JobSpec(
        name_template=imt_datasync.IMT_SYNC_NAME,
        arguments=[WeekJobArgument(label="Sync Week", description="Week of the sync")],
        runner=imt_datasync.launch_sync_for_week,
        permission=Permissions.IMT_SYNC_JOB_V1,
        markets=[MARKET_US, MARKET_CA],
    )

    PIMT_SYNC = JobSpec(
        name_template=pimt_datasync.PIMT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_pimt_sync,
        permission=Permissions.PIMT_SYNC_JOB_V1,
        markets=[MARKET_US, MARKET_CA],
    )

    CSAC_EXPORT = JobSpec(
        name_template=pimt_datasync.PIMT_CSAC_EXPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_csac_export,
        permission=Permissions.PIMT_EXPORT_V1,
        markets=[MARKET_US],
    )

    OPS_MAIN_PROTEIN_EXPORT = JobSpec(
        name_template=pimt_datasync.PIMT_OPS_MAIN_DASHBOARD_PROTEIN_EXPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_ops_main_protein_export,
        permission=Permissions.PIMT_EXPORT_V1,
        markets=[MARKET_US],
    )

    OPS_MAIN_GDP_EXPORT = JobSpec(
        name_template=pimt_datasync.PIMT_OPS_MAIN_DASHBOARD_GDP_EXPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_ops_main_gdp_export,
        permission=Permissions.PIMT_EXPORT_V1,
        markets=[MARKET_US],
    )

    STRATEGY_EXPORT = JobSpec(
        name_template=pimt_datasync.PIMT_STRATEGY_EXPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_strategy_export,
        permission=Permissions.PIMT_EXPORT_V1,
        markets=[MARKET_US],
    )

    REPLENISHMENT_EXPORT = JobSpec(
        name_template=pimt_datasync.PIMT_REPLENISHMENT_EXPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_replenishment_export,
        permission=Permissions.PIMT_EXPORT_V1,
        markets=[MARKET_US],
    )

    PIMT_DAILY_EXCEPTION_REPORT = JobSpec(
        name_template=pimt_datasync.PIMT_EXCEPTION_REPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_pimt_daily_exception_report,
        permission=Permissions.PIMT_DAILY_REPORT_V1,
        markets=[MARKET_US],
    )

    PIMT_MONTHLY_3PW_REPORT = JobSpec(
        name_template=pimt_datasync.PIMT_3PW_REPORT_SYNC_NAME,
        arguments=[],
        runner=pimt_datasync.launch_pimt_monthly_3pw_report,
        permission=Permissions.PIMT_MONTHLY_FINANCIAL_REPORT_V1,
        markets=[MARKET_US],
    )

    CLEANUP_JOB = JobSpec(
        name_template=maintenance.CLEANUP_JOB_NAME,
        arguments=[],
        runner=maintenance.submit_cleanup_job,
        permission=Permissions.CLEANUP_JOB_V1,
        markets=[MARKET_US],
    )

    HJ_INV_SYNC = JobSpec(
        name_template=hj_datasync.DAILY_HJ_INVENTORY_SCHEDULE_SYNC_NAME,
        arguments=[],
        runner=hj_datasync.submit_hj_inv_schedule_job_us,
        permission=Permissions.HJ_INV_SYNC_JOB_V1,
        markets=[MARKET_US],
    )

    # actual snapshot job (not the schedule) to check on its status during tests
    HJ_INV_SYNC_RUN = JobSpec(
        name_template=hj_datasync.HJ_INV_SYNC_TIMEZONE_NAME,
        arguments=[_timezone_arg],
        # empty runner since it's only for status check
        runner=lambda: None,
        permission=Permissions.HJ_INV_SYNC_JOB_V1,
        markets=[MARKET_US],
        is_hidden=True,
    )

    HJ_WIP_CONSUMPTION_SYNC = JobSpec(
        name_template=hj_datasync.WIP_CONSUMPTION_SYNC_NAME,
        arguments=[],
        runner=hj_datasync.submit_wip_consumption,
        permission=Permissions.HJ_INV_SYNC_JOB_V1,
        markets=[MARKET_CA],
    )

    HJ_PACKAGING_SNAPSHOT = JobSpec(
        name_template=hj_datasync.SCHEDULE_UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME,
        arguments=[],
        runner=hj_datasync.schedule_packaging_inventory_update,
        permission=Permissions.HJ_PCK_SNAPSHOT_JOB,
        markets=[MARKET_US, MARKET_CA],
    )

    # actual snapshot job (not the schedule) to check on its status during tests
    HJ_PACKAGING_SNAPSHOT_RUN = JobSpec(
        name_template=hj_datasync.UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT_SYNC_NAME,
        arguments=[_timezone_arg],
        # empty runner since it's only for status check
        runner=lambda: None,
        permission=Permissions.HJ_PCK_SNAPSHOT_JOB,
        markets=[MARKET_US, MARKET_CA],
        is_hidden=True,
    )

    # added to test s3 export
    UNIFIED_INVENTORY_EXPORT_TO_S3 = JobSpec(
        name_template=inventory_sync.EXPORT_JOB_NAME,
        arguments=[],
        runner=inventory_sync.schedule_inventory_export_to_s3,
        permission=Permissions.MANUAL_FORMS_EXPORT_TO_S3_V1,
        markets=[MARKET_US],
        is_hidden=True,
    )

    FORECAST_UPLOAD = JobSpec(
        name_template=forecast_upload.FORECAST_UPLOAD_NAME,
        arguments=[
            BoolJobArgument(
                label="Ignore cached records", description="Send all data regardless if it was sent before", key="force"
            ),
            StringJobArgument(
                key="email",
                label="User email",
                description="Email of user who started the sync",
                required=False,
                is_hidden=True,
                new_key="initiated_by",
                request_param_mapper=lambda email: f"user {email}",
            ),
        ],
        runner=forecast_upload.launch_upload,
        permission=Permissions.IMT_FORECASTS_UPLOAD_SYNC_JOB_V1,
        is_hidden=not killswitch.forecast_upload_enabled,
        markets=[MARKET_US, MARKET_CA],
    )

    PO_EXPORT = JobSpec(
        name_template=po_export_datasync.EXPORT_PO_NAME,
        arguments=[],
        runner=po_export_datasync.launch_po_export,
        permission=Permissions.IMT_PO_SYNC_JOB_V1,
        markets=[MARKET_US],
    )

    FACTOR_WEEKLY_FINANCIAL_EXPORT = JobSpec(
        name_template=po_export_datasync.FACTOR_FINANCIAL_WEEKLY_EXPORT_NAME,
        arguments=[WeekJobArgument(label="Export Week", description="Week of the export")],
        runner=po_export_datasync.launch_factor_weekly_financial_export,
        permission=Permissions.FINANCIAL_EXPORT_EMAIL,
        markets=[MARKET_US],
    )

    FACTOR_MONTHLY_FINANCIAL_EXPORT = JobSpec(
        name_template=po_export_datasync.FACTOR_FINANCIAL_MONTHLY_EXPORT_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description=("Any day of the month that should be exported"),
            ),
        ],
        runner=po_export_datasync.launch_factor_monthly_financial_export,
        permission=Permissions.FINANCIAL_EXPORT_EMAIL,
        markets=[MARKET_US],
    )

    CORE_WEEKLY_FINANCIAL_EXPORT = JobSpec(
        name_template=po_export_datasync.CORE_FINANCIAL_WEEKLY_EXPORT_NAME,
        arguments=[WeekJobArgument(label="Export Week", description="Week of the export")],
        runner=po_export_datasync.launch_core_weekly_financial_export,
        permission=Permissions.FINANCIAL_EXPORT_EMAIL,
        markets=[MARKET_US],
    )

    CORE_MONTHLY_FINANCIAL_EXPORT = JobSpec(
        name_template=po_export_datasync.CORE_FINANCIAL_MONTHLY_EXPORT_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description=("Any day of the month that should be exported"),
            ),
        ],
        runner=po_export_datasync.launch_core_monthly_financial_export,
        permission=Permissions.FINANCIAL_EXPORT_EMAIL,
        markets=[MARKET_US],
    )

    CORE_DAILY_FINANCIAL_EXPORT = JobSpec(
        name_template=po_export_datasync.CORE_FINANCIAL_DAILY_EXPORT_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description=(
                    "For which day should export be executed. " "i.e. to export po's for 7 June enter 8 June."
                ),
            ),
        ],
        runner=po_export_datasync.launch_core_daily_financial_export,
        permission=Permissions.FINANCIAL_EXPORT_EMAIL,
        markets=[MARKET_US],
    )

    PACKAGING_DEPLETION_DAILY_EXPORT = JobSpec(
        name_template=imt_daily_exception_export.PACKAGING_DEPLETION_DAILY_EXPORT_SYNC_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            )
        ],
        runner=imt_daily_exception_export.launch_packaging_depletion_daily_export,
        permission=Permissions.IMT_DAILY_EXPORT_V1,
        markets=[MARKET_US],
    )

    WEEKLY_LINER_GUIDANCE_DAILY_EXPORT = JobSpec(
        name_template=imt_daily_exception_export.WEEKLY_LINER_GUIDANCE_DAILY_EXPORT_SYNC_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            )
        ],
        runner=imt_daily_exception_export.launch_weekly_liner_guidance_daily_export,
        permission=Permissions.IMT_DAILY_EXPORT_V1,
        markets=[MARKET_US],
    )

    PACKAGING_PO_STATUS_DAILY_EXPORT = JobSpec(
        name_template=imt_daily_exception_export.PACKAGING_PO_STATUS_DAILY_EXPORT_SYNC_NAME,
        arguments=[
            PartOfDayJobArgument(
                key="part_of_day",
                label="Part of day",
                description="To update data based on AM logic, please enter ‘AM’.\n"
                "To update data based on PM logic, please enter ‘PM’.",
            ),
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            ),
        ],
        runner=imt_daily_exception_export.launch_packaging_po_status_daily_export,
        permission=Permissions.IMT_DAILY_EXPORT_V1,
        markets=[MARKET_US],
    )

    FACTOR_PO_STATUS_DAILY_EXPORT = JobSpec(
        name_template=imt_daily_exception_export.FACTOR_PO_STATUS_DAILY_EXPORT_SYNC_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            ),
        ],
        runner=imt_daily_exception_export.launch_factor_po_status_daily_export,
        permission=Permissions.IMT_DAILY_EXPORT_V1,
        markets=[MARKET_US],
    )

    FACTOR_NETWORK_DEPLETION_DAILY_EXPORT = JobSpec(
        name_template=imt_daily_exception_export.FACTOR_NETWORK_DEPLETION_MODULE_EXPORT_SYNC_NAME,
        arguments=[
            PartOfDayJobArgument(
                key="part_of_day",
                label="Part of day",
                description="To update data based on AM logic, please enter ‘AM’.\n"
                "To update data based on PM logic, please enter ‘PM’.",
            ),
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            ),
        ],
        runner=imt_daily_exception_export.launch_factor_network_depletion_daily_export,
        permission=Permissions.IMT_DAILY_EXPORT_V1,
        markets=[MARKET_US],
    )

    FUTURE_PKG_SYNC = JobSpec(
        name_template=imt_datasync.FUTURE_PKG_NAME,
        arguments=[],
        runner=imt_datasync.launch_future_pkg_sync,
        permission=Permissions.FUTURE_PKG_SYNC_JOB_V1,
        markets=[MARKET_US],
    )

    MANUAL_FORMS_EXPORT_TO_S3 = JobSpec(
        name_template=manual_forms_export.S3_EXPORT_JOB_NAME,
        arguments=[
            DateJobArgument(
                key="export_date",
                label="Export date",
                description="For which day should export be executed.",
            )
        ],
        runner=manual_forms_export.submit_manual_forms_export_to_s3,
        permission=Permissions.MANUAL_FORMS_EXPORT_TO_S3_V1,
        markets=[MARKET_US],
    )

    BULK_SKUS_EXPORT = JobSpec(
        name_template=imt_datasync.BULK_SKUS_EXPORT_TO_S3_SYNC_NAME,
        arguments=[],
        runner=imt_datasync.launch_bulk_skus_export_to_s3,
        permission=Permissions.IMT_BULK_SKU_V1,
        markets=[MARKET_US],
    )

    SNAPSHOT_START_OF_DAY = JobSpec(
        name_template=snapshot_sync.SCHEDULE_SNAPSHOT_START_OF_DAY_INVENTORY,
        arguments=[
            StringJobArgument(
                key="brands",
                label="Brands",
                description="List of comma separated brands i.e. HF,EP",
                required=True,
                request_param_mapper=utils.split_comma_string,
                default_query_values=lambda: [BRAND_HF],
            ),
        ],
        is_hidden=True,
        runner=snapshot_sync.schedule_snapshot_start_of_day_inventory,
        permission=Permissions.SNAPSHOT_START_OF_DAY_INVENTORY,
        markets=[MARKET_CA],
    )

    # actual snapshot job (not the schedule) to check on its status during tests
    SNAPSHOT_START_OF_DAY_RUN = JobSpec(
        name_template=snapshot_sync.SNAPSHOT_START_OF_DAY_INVENTORY + "{brands}",
        arguments=[
            _timezone_arg,
            StringJobArgument(
                key="brands",
                label="Brands",
                description="List of comma separated brands i.e. HF,EP",
                required=True,
                request_param_mapper=utils.split_comma_string,
            ),
        ],
        # empty runner since it's only for status check
        runner=lambda: None,
        permission=Permissions.SNAPSHOT_START_OF_DAY_INVENTORY,
        markets=[MARKET_CA],
        is_hidden=True,
    )

    SNAPSHOT_OUTDATED_CHECK = JobSpec(
        name_template=snapshot_sync.SNAPSHOT_OUTDATED_ALERT,
        arguments=[],
        runner=snapshot_sync.launch_snapshot_date_outdated,
        permission=Permissions.SNAPSHOT_START_OF_DAY_INVENTORY,
        is_hidden=True,
        markets=[MARKET_CA],
    )

    ALLOWED_BUFFER = JobSpec(
        name_template=allowed_buffers_upload.ALLOWED_BUFFERS_SYNC_NAME,
        arguments=[],
        runner=allowed_buffers_upload.submit_import_allowed_buffers_job,
        permission=Permissions.IMT_SYNC_JOB_V1,
        markets=[MARKET_US],
    )

    @staticmethod
    def get_all() -> dict[str, JobSpec]:
        market = context.get_request_context().market
        return {job.name: job.value for job in JobsEnum.__members__.values() if market in job.value.markets}

    @staticmethod
    def get_names() -> list[str]:
        return [job.name for job in JobsEnum.__members__.values()]
