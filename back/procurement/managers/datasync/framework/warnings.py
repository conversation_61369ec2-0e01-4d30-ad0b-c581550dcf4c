import json
import logging
from collections.abc import Iterable

from rq import get_current_job
from rq.job import Job

from procurement.data.googlesheet_model.core import Sheet<PERSON><PERSON><PERSON>, SheetRow
from procurement.data.googlesheet_model.util import build_row_errors, build_sheet_error
from procurement.managers.datasync.framework.datasync_constants import (
    WARNING_KEY,
    WARNING_SEPARATOR,
    WARNINGS_COUNT_KEY,
)

logger = logging.getLogger(__name__)

WARNING_THRESHOLD = 100


def _add_warning_for_parallel(message: dict, job: Job) -> None:
    """
    Build and save job warnings info
    :param message: a dictionary of the warning message
    that will be stored in the job's meta information
    :param job: job to add warning
    """
    warnings_count = job.meta.get(WARNINGS_COUNT_KEY, 0)
    if warnings_count >= WARNING_THRESHOLD:
        return
    message = json.dumps(message)
    warnings_count += 1
    if warnings_count >= WARNING_THRESHOLD:
        message = WARNING_SEPARATOR.join((message, "Warnings threshold exceeded, further warnings suppressed"))
    if WARNING_KEY in job.meta:
        message = WARNING_SEPARATOR.join((job.meta[WARNING_KEY], message))

    job.meta[WARNINGS_COUNT_KEY] = warnings_count
    job.meta[WARNING_KEY] = message
    job.save_meta()


def _add_warning(message: dict, job: Job = None) -> None:
    """
    Add warning to job's meta
    :param message: a dictionary of the warning message
    that will be stored in the job's meta information
    :param job: job to add warning
    """
    if not job:
        job = get_current_job()
    if not message:
        logger.warning("Method datasync._add_warning: failed with empty message")
        return
    if job and job.meta:
        _add_warning_for_parallel(message, job)


def notify_sheet_error(
    row: SheetRow | None, error: Exception, message: str = None, model: SheetModel = None, **kwargs
) -> None:
    """
    Used to add error about single row
    For example, when importing, an error was found that the data types did not match to according one
    (The error also appears on the UI )
    :param row: SheetRow
    :param error: python's exception
    :param message: a string of the warning message
    that will be stored in the job's meta information
    :param model: The google sheet model in which the error occurred
    :param kwargs: additional arguments to message
    """
    _add_warning(build_sheet_error(row, error, message, model, **kwargs))


def notify_records_errors(data: Iterable[SheetRow], message: str = None, **kwargs) -> None:
    """
    Adds errors from parsing google sheets to job's meta
    :param data: list of SheetRow instances
    :param message: warning message
    :param kwargs: additional kwargs to build the warning
    """
    errors = build_row_errors(data, message, **kwargs)
    for error in errors:
        _add_warning(error)


def notify_warning(message: dict | str, job: Job = None, **kwargs) -> None:
    """
    Used to add a warning message to job's meta
    :param message: a dictionary or string (will be converted to dict) of the warning message
    that will be stored in the job's meta information
    :param job: job to which we should add message
    :param kwargs: additional kwargs to the warning
    """
    if isinstance(message, str):
        message = {"message": message, **kwargs}

    message.update(kwargs)

    logger.warning(message)
    _add_warning(message, job=job)
