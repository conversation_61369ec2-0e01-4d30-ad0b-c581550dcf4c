import itertools
import logging
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta

from rq import Queue
from rq.job import Job

from procurement.core.cache_utils.constants import worker_queues
from procurement.core.log import log_wrapper
from procurement.core.request_utils import context
from procurement.data.models.social.parallel_sync_log import SyncStatus
from procurement.managers.datasync.framework.datasync_constants import (
    BUILD_RESULT_PREFIX,
    MARKET_KEY,
    PIPELINE_RUN_ID,
    STATUS_KEY,
    WARNING_KEY,
)
from procurement.repository.procurement import parallel_sync_log

from .models import JobStatus, JobStatusCollection, ProxyJobStatus

logger = logging.getLogger(__name__)

status_registry_dict = {
    "failed": lambda worker_queue: worker_queue.failed_job_registry,
    "started": lambda worker_queue: worker_queue.started_job_registry,
    "deferred": lambda worker_queue: worker_queue.deferred_job_registry,
    "scheduled": lambda worker_queue: worker_queue.scheduled_job_registry,
    "finished": lambda worker_queue: worker_queue.finished_job_registry,
}

JOB_STATUS = str  # pylint: disable=invalid-name
JOB_ID = str  # pylint: disable=invalid-name


def build_result_name(job_name: str):
    """
    Build a name for the last step of every job
    :param job_name: name of the job
    :return: last step name
    """
    return f"{BUILD_RESULT_PREFIX}_{job_name}"


def get_all_jobs_with_status_parallel_db(job_name: str) -> JobStatusCollection | None:
    """
    Get job's run with all it's dependants (steps)
    :param job_name: name of the job to check
    :return: job and it's steps
    """
    last_sync_imt_job = parallel_sync_log.get_by_name(job_name)
    if not last_sync_imt_job:
        return None

    job_collection = [
        ProxyJobStatus(child_job.job_name, child_job.sync_status, child_job.warnings, child_job.parent_job_ids)
        for child_job in parallel_sync_log.get_child_jobs(last_sync_imt_job.id)
    ]

    return to_tree_jobs_collection(
        job_collection, job_name, last_sync_imt_job.sync_time, SyncStatus(last_sync_imt_job.sync_status)
    )


def get_from_db_or_empty_jobs_info(job_name: str) -> JobStatusCollection:
    """
    Get job and it's steps from database
    :param job_name: name of the job
    :return: job and it's dependants (steps) if there is job record in database
    """
    if not (collection := get_all_jobs_with_status_parallel_db(job_name)):
        logger.warning("You never launched parallel sync for: %s", job_name)
        collection = JobStatusCollection()
    return collection


def to_tree_jobs_collection(
    jobs_proxy_collection: list[ProxyJobStatus],
    parent_name: str,
    sync_time: datetime = None,
    sync_status: SyncStatus = None,
):
    """
    Build JobCollection (job and its steps) for list of jobs
    :param jobs_proxy_collection: List of job to build JobCollection
    :param parent_name: the name of the main jobs
    :param sync_time: (optional) execution time to be set on jobs collection
    :param sync_status: (optional) status to set on jobs collection
    :return: job collection with dependant steps
    """
    jobs_collection = JobStatusCollection(status=sync_status, sync_time=sync_time)

    jobs = {job.job_name: job for job in jobs_proxy_collection}

    def loop(inner_parent_name):
        dependants = [
            loop(job_name)
            for job_name, data in jobs.items()
            if data.parent_job_ids and inner_parent_name in data.parent_job_ids
        ]

        if warnings := jobs[inner_parent_name].warnings:
            jobs_collection.warning_count += 1
        if (status := jobs[inner_parent_name].sync_status) == SyncStatus.FAILED:
            jobs_collection.error_count += 1

        return JobStatus(
            name=inner_parent_name.split("_")[0],
            status=status,
            warnings=warnings,
            dependants=sorted(dependants, key=lambda item: item.name),
        )

    jobs_collection.jobs = [
        loop(job_name) for job_name, data in jobs.items() if data.parent_job_ids and parent_name in data.parent_job_ids
    ]

    if jobs_collection.jobs:
        jobs_collection.root_job = jobs_collection.jobs[0]
    else:
        logger.error("The unexpected behaviour, children jobs was not uploaded along with a parent job")
    return jobs_collection


def get_all_jobs_with_status_parallel_redis(run_id: str, job_name: str):
    """
    Get job and its steps from redis
    :param run_id: job's uuid
    :param job_name: name of the job
    :return: job and its steps
    """
    registry_list = [
        worker_queues.main_queue.failed_job_registry,
        worker_queues.main_queue.started_job_registry,
        worker_queues.main_queue.finished_job_registry,
        worker_queues.main_queue.deferred_job_registry,
        worker_queues.main_queue.scheduled_job_registry,
        worker_queues.main_queue,
    ]
    job_collection = []
    for registry in registry_list:
        for job_id in filter(lambda x: run_id in x, registry.get_job_ids()):
            job = worker_queues.main_queue.fetch_job(job_id)
            if not job or job.meta["hidden"] or job.meta[MARKET_KEY] != context.get_request_context().market:
                continue

            job_collection.append(
                ProxyJobStatus(
                    job_id,
                    SyncStatus(job.meta[STATUS_KEY]),
                    job.meta.get(WARNING_KEY),
                    job._dependency_ids,  # pylint: disable=protected-access
                )
            )
    return to_tree_jobs_collection(job_collection, job_name)


def get_job_info(job_name: str) -> dict:
    """
    Get information about last job run
    :param job_name: name of the job
    :return: job run metadata (warnings, errors, if is progress)
    """

    job_in_progress = get_job_in_progress_by_name(
        build_result_name(job_name), worker_queues.main_queue, context.get_request_context().market
    )

    response = {"is_fresh": None}

    if job_in_progress:
        # get from redis
        collection = get_all_jobs_with_status_parallel_redis(job_in_progress.meta[PIPELINE_RUN_ID], job_name)
    else:
        collection = get_from_db_or_empty_jobs_info(job_name)
        if collection.sync_time:
            response["is_fresh"] = (datetime.timestamp(datetime.now()) - collection.sync_time.timestamp()) / 3600 < 2

    if not collection.jobs:
        jobs = []
    else:
        jobs = [collection.root_job.convert_to_json()]

    response["last_sync"] = {
        "jobs": jobs,
        "time": collection.sync_time.isoformat() if collection.sync_time else None,
        "warnings": collection.warning_count or None,
        "errors": collection.error_count or None,
        "success": collection.status == SyncStatus.SUCCESS,
        "in_progress": bool(job_in_progress),
    }

    return response


def get_job_in_progress_by_name(job_name: str, worker_queue: Queue, market: str) -> Job | None:
    """
    Get job from redis
    :param job_name: name of the job
    :param worker_queue: redis queue where job is placed
    :param market:
    :return: job if in progress
    """
    _requeue_expired_jobs(worker_queue)
    pending = worker_queue.get_job_ids()
    started = worker_queue.started_job_registry.get_job_ids()
    scheduled = worker_queue.scheduled_job_registry.get_job_ids()
    deferred = _get_deferred_jobs(job_name, worker_queue, market)

    for job_id in started + pending + scheduled + deferred:
        if job_name in job_id:
            job = worker_queue.fetch_job(job_id)
            if (
                job
                and job.meta[STATUS_KEY] in {SyncStatus.QUEUED, SyncStatus.IN_PROGRESS}
                and job.meta[MARKET_KEY] == market
            ):
                return job

    return None


def is_any_job_started(run_id: str, worker_queue: Queue, market: str) -> bool:
    """
    Check whether job started or not
    :param run_id: uuid of the job
    :param worker_queue: redis queue where job is placed
    :param market:
    :return: whether job started or not
    """
    all_jobs = itertools.chain(
        worker_queue.started_job_registry.get_job_ids(),
        worker_queue.failed_job_registry.get_job_ids(),
        worker_queue.finished_job_registry.get_job_ids(),
    )

    for redis_job_id in all_jobs:
        if run_id in redis_job_id:
            job = worker_queue.fetch_job(redis_job_id)
            if (
                job
                and job.meta[STATUS_KEY] in {SyncStatus.SUCCESS, SyncStatus.IN_PROGRESS, SyncStatus.FAILED}
                and job.meta[MARKET_KEY] == market
            ):
                return True
    return False


@log_wrapper
def _get_deferred_jobs(job_name: str, worker_queue: Queue, market: str):
    """
    Select jobs with status 'deferred' (Only deferred jobs with active dependencies should be selected.)
    :param job_name: name of the job
    :param worker_queue: redis queue where job is placed
    :return: deferred jobs list
    """

    _clean_stuck_deferred_jobs(job_name, worker_queue, market)

    return worker_queue.deferred_job_registry.get_job_ids()


def _clean_stuck_deferred_jobs(job_name: str, worker_queue: Queue, market: str) -> None:
    """
    Clean job by name if its stuck in deferred state
    :param job_name: name of the job
    :param worker_queue: redis queue where job is placed
    """
    for job_id in worker_queue.deferred_job_registry.get_job_ids():
        if job_name in job_id:
            _check_and_clean_stuck_deferred_jobs(worker_queue.fetch_job(job_id), worker_queue, market)


def _check_and_clean_stuck_deferred_jobs(job: Job, worker_queue: Queue, market: str) -> bool:
    """
    Check if any dependent job is running, otherwise kill job that is not active/has no active dependants
    :param job: rq job obtained from redis queue
    :param worker_queue: redis queue where job is placed
    :return: whether job not stuck
    """
    if not job or job.meta[MARKET_KEY] != market:
        return False

    if job.is_started or job.is_scheduled or job.is_queued:
        return True

    if job.last_heartbeat and job.last_heartbeat > datetime.utcnow() - timedelta(seconds=120):
        return True

    alive_job = False
    for dependency in job.fetch_dependencies():
        if _check_and_clean_stuck_deferred_jobs(dependency, worker_queue, market):
            alive_job = True
            break

    if not alive_job and job.is_deferred:
        job = worker_queue.fetch_job(job.id)
        if job and job.is_deferred:
            worker_queue.deferred_job_registry.remove(job)
    return alive_job


def _requeue_expired_jobs(worker_queue: Queue) -> None:
    """
    Try to requeue expired jobs that was stuck after expired jobs were clean
    :param worker_queue: redis queue where job is placed
    """
    expired_jobs = worker_queue.started_job_registry.get_expired_job_ids()
    worker_queue.started_job_registry.cleanup()
    for job_id in expired_jobs:
        try:
            job = worker_queue.fetch_job(job_id)
            if job and job.retries_left and job.retries_left > 0:
                job.retries_left = job.retries_left - 1
                worker_queue.failed_job_registry.requeue(job)
        except Exception:
            logger.warning("It is not possible to requeue job %s", job_id, exc_info=True)


def get_job_run(job_name: str, arguments: dict) -> dict:
    """
    Get information about the last run of the job built from job_template and it's arguments
    :param job_name: name of the job
    :param arguments: params that alter job name
    :return: information about the job status
    """
    job_in_progress = get_job_in_progress_by_name(
        build_result_name(job_name), worker_queues.main_queue, context.get_request_context().market
    )
    last_update = None
    status = None
    if job := parallel_sync_log.get_by_name(job_name):
        last_update = job.sync_time.isoformat()
        status = job.sync_status
    item = {
        "name": job_name,
        "arguments": arguments,
        "in_progress": bool(job_in_progress),
        "status": status,
        "last_updated": last_update,
    }
    return item


def get_job_info_by_status(is_detailed: bool, queue_name: str, statuses: list[str] | None = None) -> dict:
    """
    Gets jobs and their info by registry
    :param is_detailed: if True, checks only the job's existence else provides detailed report about this job
    :param queue_name: redis queue where job is placed
    :param statuses: (optional) what job registries to query
    :return: information about the job
    """
    worker_queue = getattr(worker_queues, queue_name, None)

    if not worker_queue:
        raise ValueError(f"The '{queue_name}' queue does not exist. Available queues: {worker_queues._fields}")

    return _get_jobs_info(_get_status_jobs(statuses, worker_queue), is_detailed, worker_queue)


def _get_status_jobs(statuses: list[str], queue: Queue) -> dict[JOB_STATUS, list[JOB_ID]]:
    """
    Gets jobs by requested registries
    :param statuses: list of registry names
    :param queue: redis queue where job is placed
    :return: jobs by registry
    """
    status_jobs = {}

    if not statuses:
        statuses = status_registry_dict.keys()

    for stat in statuses:
        if get_job_registry_fn := status_registry_dict.get(stat):
            status_jobs[stat] = get_job_registry_fn(queue).get_job_ids()
        else:
            raise ValueError(f"The {stat} status does not exist")

    return status_jobs


def _get_jobs_info(jobs_by_registry: dict[JOB_STATUS, list[JOB_ID]], is_detailed: bool, worker_queue: Queue):
    """
    Build information for every job
    :param jobs_by_registry: status and all jobs that have this one
    :param is_detailed: if True, checks only the job's existence else provides detailed report about this job
    :param worker_queue: redis queue where job is placed
    :return: jobs info by registry
    """
    result = defaultdict(dict)

    for status, job_ids in jobs_by_registry.items():
        for job_id in job_ids:
            job_data = worker_queue.fetch_job(job_id) if is_detailed else None
            result[status][job_id] = (
                {
                    "meta": job_data.get_meta(),
                    "created_at": job_data.created_at,
                    "ended_at": job_data.ended_at,
                    "retries_left": job_data.retries_left,
                    "timeout": job_data.timeout,
                    "ttl": job_data.ttl,
                    "result_ttl": job_data.result_ttl,
                    "failure_ttl": job_data.failure_ttl,
                    "worker_name": job_data.worker_name,
                    "retry_intervals": job_data.retry_intervals,
                    "last_heartbeat": job_data.last_heartbeat,
                    "dependency_ids": job_data.dependency_ids,
                    "dependent_ids": job_data.dependent_ids,
                    "is_canceled": job_data.is_canceled,
                    "is_deferred": job_data.is_deferred,
                    "is_failed": job_data.is_failed,
                    "is_finished": job_data.is_finished,
                    "is_queued": job_data.is_queued,
                    "is_scheduled": job_data.is_scheduled,
                    "is_started": job_data.is_started,
                    "is_stopped": job_data.is_stopped,
                }
                if job_data
                else {}
            )
    return result
