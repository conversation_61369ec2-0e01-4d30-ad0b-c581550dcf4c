import json
from datetime import datetime

from procurement.data.models.social.parallel_sync_log import SyncStatus
from procurement.managers.datasync.framework.datasync_constants import STATUS_KEY, WARNING_KEY, WARNING_SEPARATOR


class ProxyJobStatus:
    """
    This class is used as an intermediate class between Redis,
    ParallelSyncLog, SyncLog job statuses and actual JobStatus
    """

    def __init__(
        self,
        job_name: str | None = None,
        sync_status: str | None = None,
        warnings: str | None = None,
        parent_job_ids: list[int] | None = None,
    ):
        self.job_name = job_name
        self.sync_status = SyncStatus(sync_status) if sync_status else None
        self.warnings = warnings
        self.parent_job_ids = parent_job_ids or []


class JobStatus:
    """
    Represents job's info and is used for serialization/deserialization
    """

    def __init__(self, name=None, status=None, warnings=None, dependants=None):
        self.name = name
        self.status = SyncStatus(status) if status else None
        self.warnings = self.decode_warnings(warnings)
        self.dependants = dependants or []

    @staticmethod
    def decode_warnings(text: str):
        """
        Deserialize warning
        :param text: serialized warning
        :return: list of deserialized warnings
        """
        if not text:
            return None
        return [JobStatus._decode_warning(w) for w in text.split(WARNING_SEPARATOR)]

    def convert_to_json(self) -> dict:
        """
        Prepares job's info to be passed to api
        :return: job status in json format
        """
        dependants_json = [item.convert_to_json() for item in self.dependants or []]

        converted_job_status = {
            "job_name": self.name,
            STATUS_KEY: self.status,
            WARNING_KEY: self.warnings,
            "dependants": dependants_json,
        }
        return converted_job_status

    @staticmethod
    def _decode_warning(warning):
        """
        Deserialize warning
        :param warning: serialized warning
        :return: deserialized warning
        """
        try:
            return json.loads(warning)
        except Exception:
            return {"message": warning}


class JobStatusCollection:
    """
    Represents collection of jobs
    """

    def __init__(
        self,
        jobs: list[JobStatus] = None,
        status: SyncStatus = None,
        sync_time: datetime = None,
    ):
        self.jobs = jobs or []
        self.status = status
        self.sync_time = sync_time

        self.error_count = 0
        self.warning_count = 0
        self.root_job = None
