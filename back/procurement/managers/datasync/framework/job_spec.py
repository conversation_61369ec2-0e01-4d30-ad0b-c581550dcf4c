import dataclasses
import itertools
from collections.abc import Iterable
from datetime import datetime
from typing import Any, Callable

from procurement.auth.permissions import Permissions
from procurement.constants.hellofresh_constant import PartOfDay
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.validation_errors import JobArgumentValidationException

from . import queries


class JobArgument:
    """
    Represent arguments of the RQ job, how to specify it on the Frontend and correctly pass it to the worker
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        key: str,
        label: str,
        description: str,
        required: bool,
        ui_type: str,
        default_query_values: Callable[[], Iterable[Any]] = list,
        is_hidden: bool = False,
        new_key: str = None,
        request_param_mapper: Callable[[Any], Any] = None,
    ):
        self.key = key
        self.label = label
        self.description = description
        self.required = required
        self.ui_type = ui_type
        self.default_query_values = default_query_values
        self.is_hidden = is_hidden
        self.new_key = new_key
        self.request_param_mapper = request_param_mapper

    def clean(self, arguments: dict):
        """
        Ensures that required arguments are specified and then properly converted to be passed to the worker
        :param arguments: arguments obtained from frontend
        :return: argument ready to be passed to worker
        """

        value = arguments.get(self.key)
        if self.required and value is None:
            raise JobArgumentValidationException(f"Argument {self.key} is required")
        if self.request_param_mapper:
            value = self.request_param_mapper(value)
        return value

    @property
    def python_name(self):
        return self.new_key or self.key


class WeekJobArgument(JobArgument):
    def __init__(self, label: str, description: str, key: str = "week", required: bool = True):
        super().__init__(
            key=key,
            label=label,
            description=description + " (example: 2021-W25)",
            required=required,
            ui_type="string",
            default_query_values=self.default_week_range,
            request_param_mapper=ScmWeek.from_str,
        )

    @staticmethod
    def default_week_range():
        cur_week = ScmWeek.current_week()
        return [str(week) for week in ScmWeek.range(cur_week - 2, cur_week + 2)]


class BoolJobArgument(JobArgument):
    def __init__(self, label: str, description: str, key: str, required: bool = False):
        super().__init__(
            key=key,
            label=label,
            description=description + " (default: false)",
            required=required,
            ui_type="string",
            request_param_mapper=lambda value: value and value.upper() == "TRUE",
        )


class StringJobArgument(JobArgument):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        label: str,
        description: str,
        key: str,
        required: bool = False,
        is_hidden: bool = False,
        new_key: str = None,
        request_param_mapper: Callable[[str], Any] = None,
        default_query_values: Callable[[], Iterable] = list,
    ):
        super().__init__(
            key=key,
            label=label,
            description=description,
            required=required,
            ui_type="string",
            default_query_values=default_query_values,
            is_hidden=is_hidden,
            new_key=new_key,
            request_param_mapper=request_param_mapper,
        )


class DateJobArgument(JobArgument):
    def __init__(self, key: str, label: str, description: str, required: bool = False):
        super().__init__(
            key=key,
            label=label,
            description=description + " Format is: mm-dd-year (default: Today)",
            required=required,
            ui_type="string",
            request_param_mapper=lambda _date: datetime.strptime(_date, "%m-%d-%Y").date() if _date else None,
        )


class PartOfDayJobArgument(JobArgument):
    def __init__(self, key: str, label: str, description: str, required: bool = True):
        super().__init__(
            key=key,
            label=label,
            description=description,
            required=required,
            ui_type="string",
            default_query_values=lambda: ["AM", "PM"],
            request_param_mapper=lambda part_of_day: part_of_day and PartOfDay[part_of_day.upper()],
        )


@dataclasses.dataclass
class JobSpec:
    """
    Represent job and contains common manipulation routines
    """

    name_template: str
    arguments: Iterable[JobArgument]
    runner: Callable
    permission: Permissions
    markets: list[str]
    runs: list[dict] = dataclasses.field(default_factory=list)
    is_hidden: bool = False

    @property
    def name(self):
        return self.name_template.format(**{argument.python_name: "" for argument in self.arguments}).strip()

    def fill_runs(self) -> None:
        arg_values = (
            [(arg.python_name, arg_val) for arg_val in arg.default_query_values() or [None]] for arg in self.arguments
        )
        matrix = list(itertools.product(*arg_values)) or [tuple()]

        run_meta = {}
        for key in matrix:
            args = dict(key)
            name = self.name_template.format(**args)
            run_meta[name] = args

        self.runs.clear()
        for name, arguments in run_meta.items():
            self.runs.append(queries.get_job_run(job_name=name, arguments=arguments))

    def clean_arguments(self, arguments: dict):
        """
        Prepare arguments obtained from UI to be passed to worker.
        Also validates that all required arguments are present
        :param arguments: all arguments passed from UI
        :return: arguments ready to be passed to worker
        """
        cleaned = {}
        for argument_spec in self.arguments:
            if value := argument_spec.clean(arguments):
                cleaned[argument_spec.new_key or argument_spec.key] = value
        return cleaned

    def execute_job(self, arguments: dict):
        """
        Schedule job's execution on worker
        :param arguments: job's arguments
        """
        return self.runner(**arguments)
