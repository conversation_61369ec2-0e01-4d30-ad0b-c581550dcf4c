from datetime import timed<PERSON><PERSON>
from typing import Callable


class Step:
    """
    The class is used to describe the step of the job's execution
    """

    def __init__(
        self,
        name: str,
        func: Callable,
        *,
        is_hidden: bool = False,
        postpone_timeout: timedelta | int = None,
        **kwargs,
    ):
        """
        :param name: Step name (prefer to use single_string_with_underscores)
        :param func: step function to execute
        :param is_hidden: if True: this step not be shown in list
        :param postpone_timeout: if postpone timeout is declared in the job,
        then this number will be equal to the lifetime of the job in seconds
        :param kwargs: function kwargs
        """
        self.name = name
        self.func = func
        self.kwargs = kwargs
        self.dependencies = None
        self.parent_job = None
        self.is_hidden = is_hidden
        self.run_id = None
        self.postpone_timeout = postpone_timeout

    def __str__(self) -> str:
        return f"""{{
            job_id: {self.job_id},
            func: {self.func},
            kwargs: {self.kwargs}
        }}"""

    @property
    def job_id(self) -> str:
        if self.run_id is None:
            raise ValueError("run_id is not defined")
        return f"{self.name}_{self.run_id}"
