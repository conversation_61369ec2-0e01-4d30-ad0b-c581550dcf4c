from __future__ import annotations

import logging
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Callable

import rq
from rq import Queue, Retry
from rq.job import Job
from rq.timeouts import JobTimeoutException

from procurement.client.slack import alerts
from procurement.client.slack.alerts import JobFailedAlert
from procurement.core.cache_utils.constants import worker_queues
from procurement.core.config_utils import config
from procurement.core.exceptions.gsheets import GSheetValidationError
from procurement.core.exceptions.worker_exceptions_handlers import worker_custom_exception_handler
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.data.models.social.parallel_sync_log import ParallelSyncLogModel, SyncStatus
from procurement.managers.social import users
from procurement.repository.procurement import parallel_sync_log

from . import common, queries
from .datasync_constants import MARKET_KEY, PIPELINE_RUN_ID, STATUS_KEY, WARNING_KEY
from .step import Step
from .warnings import _add_warning

JOB_MAX_RETRY = 3
logger = logging.getLogger(__name__)


class ParallelJobRunner:
    """
    Contains routines to set up and execute a bunch of steps
    """

    def __init__(self, name: str, redis_queue: str = "main_queue", market: str | None = None, is_alert_enabled=True):
        self.name = name
        self.parent = None
        self.redis_queue = redis_queue
        self.queue = []
        self.run_id = str(uuid.uuid4())
        self.market = market or context.get_request_context().market

        self._parent_status = SyncStatus.SUCCESS
        self._parent_id = None
        self._result_list_to_insert = []
        self._finish_time = datetime.now().astimezone()
        self.is_alert_enabled = is_alert_enabled

    def add_next_step(self, *steps: Step | ParallelJobRunner) -> ParallelJobRunner:
        """
        Add one more level of steps to be executed
        :param steps: list of steps to be executed on that level
        :return: current run_step
        """
        if steps:
            self.queue.append(steps)
        return self

    def _add_build_result_step(self) -> None:
        """
        Adds last step that saves ParallelSyncLog record into db
        """
        build_result_step = Step(
            queries.build_result_name(self.name),
            self._build_result,
            is_hidden=True,
        )
        build_result_step.run_id = self.run_id
        self.add_next_step(build_result_step)

    def set_parent_parallel_job_runner(self, paren_job: ParallelJobRunner) -> None:
        """
        Set parent run_step, if a current run_step is a child of another run_step
        :param paren_job: parent run_step
        """
        self.parent = paren_job

    def get_jobs_id(self, steps: list[ParallelJobRunner | Step]) -> list[str]:
        """
        Get the list of job/step ids that were submitted
        :param steps: list of steps/jobs to get ids
        :return: list of ids that were submitted
        """
        res = []
        for step in steps:
            if isinstance(step, ParallelJobRunner):
                res.extend([job_id for steps_queue in step.queue for job_id in self.get_jobs_id(steps_queue)])
            elif step.job_id:
                res.append(step.job_id)
        return res

    def submit(self) -> None:
        """
        Enqueue job and all its dependent steps
        """
        self._add_build_result_step()
        logger.info("Job '%s' with uuid '%s' is starting", self.name, self.run_id)

        for index, steps in enumerate(self.queue):
            if index:
                self.run_step(steps, self.get_jobs_id(self.queue[index - 1]), self.run_id)
            else:
                self.run_step(steps, [self.name], self.run_id)

    def submit_if_not_in_progress(self) -> None:
        """
        Enqueue job if it's not already in progress
        """
        job_name = queries.build_result_name(self.name)
        submitted_job = queries.get_job_in_progress_by_name(job_name, self._get_redis_queue(), self.market)
        if submitted_job:
            submitted_job_id = submitted_job.meta[PIPELINE_RUN_ID]
            logger.info("The sync %s is already in progress with a run_id %s", self.name, submitted_job_id)
            return
        self.submit()

    def submit_if_not_pending(self) -> None:
        """
        Submit if such a job is not scheduled and not pending.
        It can be useful for a case when job is caused by some modification in the app
        E.g. export to GSheet on adding record in manual forms
        """
        redis_queue = self._get_redis_queue()
        job_name = queries.build_result_name(self.name)
        submitted_job = queries.get_job_in_progress_by_name(job_name, redis_queue, self.market)
        if submitted_job:
            submitted_job_id = submitted_job.meta[PIPELINE_RUN_ID]
            if not queries.is_any_job_started(submitted_job_id, redis_queue, self.market):
                logger.info("The sync %s has been already submitted with a run_id %s", self.name, submitted_job_id)
                return
        self.submit()

    def _get_redis_queue(self) -> Queue:
        """
        Get queue from know queues by name.
        :return: redis queue
        """
        # This action is needed for avoiding TypeError: can't pickle _thread.lock objects,
        # when self.redis_queue (if it is an instance of Queue) try to be pickled
        return getattr(worker_queues, self.redis_queue)

    def _process_step(self, step: Step, dependencies, run_id: str) -> None:
        """
        Enqueue step
        :param step: step to be scheduled
        :param dependencies: list of children of this step
        :param run_id: UUID of the parent job
        """
        step.parent_job = self
        step.run_id = run_id
        step.dependencies = dependencies

        job_meta = {
            STATUS_KEY: SyncStatus.QUEUED,
            PIPELINE_RUN_ID: run_id,
            MARKET_KEY: self.market,
            "hidden": step.is_hidden,
            "name": step.name,
            "is_parallel": True,
        }
        kwargs = {
            "job_id": step.job_id,
            "meta": job_meta,
            "retry": Retry(max=JOB_MAX_RETRY),
            "result_ttl": config["rq"]["result_ttl"],
            "ttl": config["rq"]["queued_ttl"],
            "depends_on": dependencies,
        }
        redis_queue = self._get_redis_queue()
        if step.postpone_timeout:
            redis_queue.enqueue_in(step.postpone_timeout, self._job_wrapper, step, **kwargs)
        else:
            redis_queue.enqueue(self._job_wrapper, step, **kwargs)

    def _process_runner(self, runner: ParallelJobRunner, dependencies, run_id: str) -> None:
        """
        Enqueue child runner
        :param runner: runner that should be enqueued with it's child steps
        :param dependencies: List of child steps
        :param run_id: UUID of parent job
        """
        runner.set_parent_parallel_job_runner(self)
        for inner_job_index, inner_job_steps in enumerate(runner.queue):
            if inner_job_index:
                runner.run_step(
                    inner_job_steps,
                    [inner_step.job_id for inner_step in runner.queue[inner_job_index - 1]],
                    run_id,
                )
            else:
                runner.run_step(inner_job_steps, dependencies, run_id)

    def run_step(self, steps: list[ParallelJobRunner | Step], dependencies, run_id: str) -> None:
        """
        Enqueue step or job that should be executed
        :param steps: list of steps that should be executed in parallel
        :param dependencies: list of dependant jobs
        :param run_id: UUID of parent job
        """
        for step in steps:
            if isinstance(step, ParallelJobRunner):
                self._process_runner(step, dependencies, run_id)
            else:
                self._process_step(step, dependencies, run_id)

    def _job_wrapper(self, step: Step) -> None:
        """
        Wrapper around functions calls that do retries, rollbacks, warning notifications, and exception handling
        :param step: step that should be executed
        """
        job = rq.get_current_job()
        if job.meta.get(STATUS_KEY) != SyncStatus.FAILED:
            common.set_job_status(job, SyncStatus.IN_PROGRESS)
            job_context = context.get_request_context()
            job_context.user_info = users.get_service_account_info()
            job_context.market = job.meta[MARKET_KEY]
            try:
                metric = ApplicationMetrics.datasync_measurement(metric_type="parallel_sync")
                function = metric(step.func)
                function(**step.kwargs)
                common.set_job_status(job, SyncStatus.SUCCESS)
            except JobTimeoutException as exc:
                if job.retries_left and job.retries_left > 0:
                    logger.warning("Retry job %s, retry count %s", job.id, job.retries_left)
                    raise
                logger.error("Job %s was failed by timeout", job.id)
                _add_warning("Job was failed by timeout")
                self._processing_after_exception(job)
                worker_custom_exception_handler(exc)
            except Exception as exc:
                _add_warning({"message": str(exc)})
                logger.exception(exc)
                self._processing_after_exception(job)
                worker_custom_exception_handler(exc)

    def _processing_after_exception(self, job: Job) -> None:
        """
        Responsible for setting failure status on a job and all its children
        :param job: current job
        """
        common.set_job_status(job, SyncStatus.FAILED)

    @staticmethod
    def single_job(  # pylint: disable=too-many-arguments
        name: str,
        func: Callable,
        redis_queue: str = None,
        postpone_timeout: timedelta = None,
        is_alert_enabled: bool = True,
        market: str | None = None,
        **kwargs,
    ) -> ParallelJobRunner:
        """
        Creates job run_step with a single step in it
        :param name: name of the job
        :param func: function to be executed on a worker
        :param redis_queue: queue to enqueue the job in
        :param postpone_timeout: time to wait before enqueuing the job
        :param is_alert_enabled: should raise slack alert on job failure
        :param market: market for which sync should be scheduled
        :param kwargs: arguments to the function
        :return: new parallel job run_step with a single step
        """
        single_step = Step(name="step::" + name, func=func, postpone_timeout=postpone_timeout, **kwargs)
        return ParallelJobRunner(name, redis_queue, is_alert_enabled=is_alert_enabled, market=market).add_next_step(
            single_step
        )

    @staticmethod
    def _run_step_without_worker(step) -> bool:
        """
        Call function instead of passing it to the worker
        :param step: step with function which should be launched
        :return: boolean value in depends on the exception
        """
        try:
            logger.info("Started sync for: %s", step.name)
            step.func(**step.kwargs)
        except GSheetValidationError as exc:
            logger.exception(exc)
            return True
        except Exception as exc:
            logger.exception(exc)
            # The step will be skipped in case there is no tab in the document.
        return False

    def run_jobs_without_workers(self) -> None:
        """
        Run jobs without workers (in the main thread). Use it for tests only
        """
        should_stop = False
        for steps in self.queue:
            for step in steps:
                if isinstance(step, ParallelJobRunner):
                    step.run_jobs_without_workers()
                else:
                    should_stop = self._run_step_without_worker(step)
                if should_stop:
                    return

    @staticmethod
    def submit_single_step_job(
        job_name,
        func,
        *,
        postpone_timeout: timedelta | int | None = None,
        is_alert_enabled=True,
        market: str | None = None,
        **kwargs,
    ) -> None:
        """
        Create a job that consists of a single step and submit it in the queue (if not already in progress).
        :param postpone_timeout:
        :param job_name: name of the job
        :param func: a function that should be executed on a worker
        :param is_alert_enabled:
        :param market:
        :param kwargs: arguments, that should be passed to function
        """
        ParallelJobRunner.single_job(
            job_name,
            func=func,
            redis_queue="main_queue",
            postpone_timeout=postpone_timeout,
            is_alert_enabled=is_alert_enabled,
            market=market,
            **kwargs,
        ).submit_if_not_in_progress()

    def _build_result(self) -> None:
        self._save_parent_job()
        self._process_children(self.queue)

        if job_failed := self._parent_status == SyncStatus.FAILED:
            parallel_sync_log.update_job_status(self._parent_id, self._parent_status)

        parallel_sync_log.insert_parallel_sync_logs(self._result_list_to_insert)

        if self.is_alert_enabled:
            alerts.preventative_alert(JobFailedAlert(self.name, job_failed))

    def _save_parent_job(self) -> None:
        self._parent_id = parallel_sync_log.insert_parent_sync_log(
            {
                ParallelSyncLogModel.market: context.get_request_context().market,
                ParallelSyncLogModel.job_name: self.name,
                ParallelSyncLogModel.sync_time: self._finish_time,
                ParallelSyncLogModel.sync_status: self._parent_status,
            }
        )

    def _process_children(self, job_queue: list[list[ParallelJobRunner | Step]]) -> None:
        for steps in job_queue:
            for step in steps:
                if isinstance(step, ParallelJobRunner):
                    self._process_children(step.queue)
                elif step.name and not step.is_hidden:
                    self._result_list_to_insert.append(self._build_children_insert(step))

    def _build_children_insert(self, step: Step) -> dict:
        job_meta = self._get_redis_queue().fetch_job(step.job_id).meta
        status = job_meta[STATUS_KEY]
        dependencies = [dependency.replace(f"_{step.run_id}", "") for dependency in step.dependencies or []]

        step_failed = status == SyncStatus.FAILED

        if step_failed:
            self._parent_status = status

        return {
            ParallelSyncLogModel.job_name: step.name,
            ParallelSyncLogModel.sync_time: self._finish_time,
            ParallelSyncLogModel.sync_status: SyncStatus.FAILED if step_failed else SyncStatus.SUCCESS,
            ParallelSyncLogModel.global_parent_id: self._parent_id,
            ParallelSyncLogModel.warnings: job_meta.get(WARNING_KEY),
            ParallelSyncLogModel.parent_job_ids: dependencies,
            ParallelSyncLogModel.market: self.market,
        }
