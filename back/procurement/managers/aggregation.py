from collections.abc import Collection, Iterable, Sequence
from typing import Callable, Generic, TypeVar

ITEM_T = TypeVar("ITEM_T")  # pylint: disable=invalid-name
_T = TypeVar("_T")
COLLECTION_T = TypeVar("COLLECTION_T", bound=Collection)  # pylint: disable=invalid-name


class AggregatedData(Generic[COLLECTION_T, ITEM_T]):
    def __init__(self, items: COLLECTION_T, iterator_getter: Callable[[COLLECTION_T], Iterable[ITEM_T]] = None):
        self.raw = items
        self._iterator_getter = iterator_getter

    @property
    def _iterable(self) -> Iterable[ITEM_T]:
        if self._iterator_getter:
            return self._iterator_getter(self.raw)
        return self.raw

    def any_item(self, getter: Callable[[ITEM_T], _T], *, default: _T = None) -> _T:
        return next((i for i in map(getter, self._iterable) if i is not None), default)

    def sum_items(self, getter: Callable[[ITEM_T], _T], start: _T | None = 0) -> _T:
        return sum(filter(None, map(getter, self._iterable)), start=start)

    def sum_if_present(self, getter: Callable[[ITEM_T], _T], *, default: _T = None) -> _T | None:
        not_null_items = self._remove_nones(map(getter, self._iterable))
        if not not_null_items:
            return default
        return sum(not_null_items)

    def sum_array(self, getter: Callable[[ITEM_T], Iterable[_T] | None]) -> list[_T] | None:
        not_null_items = self._remove_nones(map(getter, self._iterable))
        if not not_null_items:
            return None
        return [sum(filter(None, column)) for column in zip(*not_null_items)]

    def min_item(self, getter: Callable[[ITEM_T], _T], *, default: _T = None) -> _T:
        return min(
            (i for i in (getter(r) for r in self._iterable) if i is not None),
            default=default,
        )

    def max_item(self, getter: Callable[[ITEM_T], _T], *, default: _T = None) -> _T:
        return max(
            (i for i in map(getter, self._iterable) if i is not None),
            default=default,
        )

    def concat_items(self, getter: Callable[[ITEM_T], str], *, delim: str = ", ", unique: bool = False) -> str:
        items = (i for i in map(getter, self._iterable) if i is not None)
        return delim.join(sorted(set(items) if unique else items))

    def union(self, getter: Callable[[ITEM_T], Iterable[_T] | None], *, ignore_falsy: bool = False) -> set[_T]:
        union_items = [i for i in map(getter, self._iterable) if ignore_falsy and i or i is not None]
        result = set()
        for item in union_items:
            result.update(item)
        return result

    def min_string_array(
        self, getter: Callable[[ITEM_T], Iterable[str] | None], string_order: Sequence[str]
    ) -> list[str]:
        return [
            next((s for s in string_order if s in strings), string_order[-1])
            for strings in zip(*map(getter, self._iterable))
        ]

    def average_of_items(self, getter: Callable[[ITEM_T], _T]) -> _T | None:
        total = self.sum_if_present(getter)
        if total is None:
            return None
        return total / sum(getter(i) is not None for i in self._iterable)

    @staticmethod
    def _remove_nones(iterable: Iterable[_T | None]) -> list[_T]:
        return [it for it in iterable if it is not None]
