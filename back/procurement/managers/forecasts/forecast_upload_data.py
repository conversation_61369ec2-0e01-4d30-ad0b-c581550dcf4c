from datetime import date
from typing import Named<PERSON>uple

from procurement.core.cache_utils.factory import CACHES
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.admin import dc_admin


class ForecastUploadItem(NamedTuple):
    sku_code: str
    week: ScmWeek
    day: date
    site: str
    brand: str
    quantity: UNITS


def get_forecast_upload_data():
    items = []
    for dc in dc_admin.get_all_market_dcs():
        if not dc.bob_code:
            continue
        data = CACHES.forecast_upload.get(dc.bob_code) or {}
        for (sku_code, forecast_date), forecast_data in data.items():
            for week, quantity in forecast_data.items():
                items.append(
                    ForecastUploadItem(
                        sku_code=sku_code,
                        week=week,
                        day=forecast_date,
                        site=dc.sheet_name,
                        brand=dc.brand,
                        quantity=quantity,
                    )
                )
    return items
