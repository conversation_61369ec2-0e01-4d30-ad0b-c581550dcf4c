import dataclasses
from collections import defaultdict
from decimal import Decimal
from typing import Named<PERSON>uple

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import (
    BRAND_GC,
    BRAND_HF,
    DEPLETION_WEEKS_AHEAD,
    MARKET_US,
    UnitOfMeasure,
)
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SITE, SKU_CODE
from procurement.data.googlesheet_model.oscar import (
    GreenChefMostRecentForecast,
    HfMostRecentForecast,
    MostRecentForecast,
)
from procurement.data.models.forecast.canada_forecast import CanadaForecastModel, CanadaForecastRecipeModel
from procurement.data.models.forecast.oscar import OscarModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.forecast import oscar as oscar_repo
from procurement.repository.snowflake import oscar_snowflake
from procurement.services.database import app_db


def update_data_by_brand(brand: str) -> None:
    parsers_by_brand = {
        BRAND_GC: _parse_green_chef_oscar,
    }
    updates = parsers_by_brand.get(brand, _parse_gil_oscar)(brand)
    oscar_repo.update_data(brand, updates)


def _parse_gil_oscar(brand: str) -> list[dict]:
    model = HfMostRecentForecast(brand) if brand == BRAND_HF else MostRecentForecast(brand)
    data = gsheet_admin.read_gsheet(model, brand=brand)
    current_mrf = list(row for row in data if row.forecast is not None)
    warnings.notify_records_errors(current_mrf, skipped=False)
    updates = []
    for item in current_mrf:
        try:
            validate_required_fields(item, ["site", "scm_week", "sku_code", "forecast"])
            updates.append(
                {
                    OscarModel.dc: item.site,
                    OscarModel.scm_week: item.scm_week,
                    OscarModel.sku_code: item.sku_code,
                    OscarModel.forecast: Decimal(item.forecast),
                    OscarModel.brand: brand,
                    OscarModel.units: UnitOfMeasure.UNIT.short_name,
                    OscarModel.forecast_by_day: None,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                item, exc, "Error when importing Oscar Forecast data", site=item.site, skipped=True
            )
    return updates


PACK_DAY_TO_INDEX = {"Thu-0": 0, "Fri": 1, "Sat": 2, "Sun": 3, "Mon": 4, "Tue": 5, "Wed": 6, "Thu-1": 7}


class OscarKey(NamedTuple):
    site: SITE
    week: ScmWeek
    sku_code: SKU_CODE
    units: str


def _parse_green_chef_oscar(brand: str) -> list[dict]:
    data = gsheet_admin.read_gsheet(GreenChefMostRecentForecast())
    warnings.notify_records_errors(data, skipped=False)
    aggregated_data = {}
    for item in data:
        try:
            validate_required_fields(item, ["site", "scm_week", "sku_code", "forecast", "units", "pack_day"])
            key = OscarKey(item.site, ScmWeek.from_str(item.scm_week), item.sku_code, item.units)
            if key not in aggregated_data:
                aggregated_data[key] = [Decimal()] * 8
            aggregated_data[key][PACK_DAY_TO_INDEX[item.pack_day]] += item.forecast
        except ValueError as exc:
            warnings.notify_sheet_error(
                item, exc, "Error when importing Oscar Forecast data", site=item.site, skipped=True
            )
    updates = [
        {
            OscarModel.dc: key.site,
            OscarModel.scm_week: str(key.week),
            OscarModel.sku_code: key.sku_code,
            OscarModel.forecast: sum(forecasts),
            OscarModel.brand: brand,
            OscarModel.units: key.units,
            OscarModel.forecast_by_day: forecasts,
        }
        for key, forecasts in aggregated_data.items()
    ]
    return updates


def update_forecast_snowflake(week: ScmWeek, brand: str) -> None:
    market = context.get_request_context().market
    sites = list(
        {
            item.sheet_name
            for w in ScmWeek.range(week, week + DEPLETION_WEEKS_AHEAD)
            for item in dc_admin.get_enabled_sites(w, brand).values()
        }
    )
    if market == MARKET_US:
        _update_us_forecast_snowflake(week=week, brand=brand, sites=sites)
    else:
        _update_ca_forecast_snowflake(week=week, brand=brand, sites=sites)


def _update_us_forecast_snowflake(week: ScmWeek, brand: str, sites: list[str]) -> None:
    for site_batch in utils.chunked(sites, 5):
        forecasts = oscar_snowflake.get_forecasts(week, brand, sites=site_batch)
        updates = []
        for item in forecasts:
            if item.sku_code.isdigit():
                continue
            updates.append(
                {
                    OscarModel.dc: item.site,
                    OscarModel.scm_week: item.scm_week,
                    OscarModel.sku_code: item.sku_code,
                    OscarModel.forecast: item.quantity,
                    OscarModel.brand: brand,
                    OscarModel.forecast_by_day: None,
                }
            )
        oscar_repo.update_data(brand, updates)


class RecipeKey(NamedTuple):
    site: str
    week: ScmWeek
    sku_code: str
    recipe_id: str


@dataclasses.dataclass
class RecipeValue:
    recipe_name: str
    picks: dict[str, int]


class ForecastKey(NamedTuple):
    site: str
    week: ScmWeek
    sku_code: str
    day: str


def _update_ca_forecast_snowflake(week: ScmWeek, brand: str, sites: list[str]) -> None:
    end_week = week + DEPLETION_WEEKS_AHEAD
    forecasts = oscar_snowflake.get_ca_staging_forecasts(week, end_week, sites=sites)
    recipes = {}
    forecasts_dict = defaultdict(Decimal)

    for item in forecasts:
        if item.sku_code.isdigit():
            continue

        forecasts_dict[
            ForecastKey(site=item.site, sku_code=item.sku_code, week=item.scm_week, day=item.day)
        ] += item.quantity

        recipe_key = RecipeKey(site=item.site, sku_code=item.sku_code, week=item.scm_week, recipe_id=item.recipe)
        if recipe_key not in recipes:
            recipes[recipe_key] = RecipeValue(recipe_name=item.recipe_name, picks={item.size: item.picks})
        else:
            recipes[recipe_key].picks[item.size] = item.picks

    weeks = list(ScmWeek.range(week, end_week))
    if not forecasts_dict:
        warnings.notify_warning(
            f"No valid Forecast and Recipe data found in the snowflake table for {brand}: {sites} "
            f"between weeks {week} and {end_week}. Existing values are preserved."
        )
        return
    _update_forecasts(weeks, sites, brand, forecasts_dict)
    _update_recipes(weeks, sites, brand, recipes)


def _update_forecasts(
    weeks: list[ScmWeek], sites: list[str], brand: str, forecasts: dict[ForecastKey, Decimal]
) -> None:
    week_config = brand_admin.get_week_config(brand)
    first_day = week_config.start_weekday
    days_by_weeks = {}
    forecast_updates = []
    for key, quantity in forecasts.items():
        if key.week not in days_by_weeks:
            week_days = key.week.week_days(week_config)
            days_by_weeks[key.week] = {str(first_day + i): day for i, day in enumerate(week_days)}
        forecast_updates.append(
            {
                CanadaForecastModel.site: key.site,
                CanadaForecastModel.scm_week: key.week.to_number_format(),
                CanadaForecastModel.sku_code: key.sku_code,
                CanadaForecastModel.value: quantity,
                CanadaForecastModel.brand: brand,
                CanadaForecastModel.day: days_by_weeks[key.week][key.day],
            }
        )
    with app_db.transaction() as trn:
        oscar_repo.delete_canada_forecast_data(weeks, sites, brand, trn)
        oscar_repo.insert_canada_data(forecast_updates, trn)


def _update_recipes(weeks: list[ScmWeek], sites: list[str], brand: str, recipes: dict) -> None:
    recipe_updates = []
    for key, value in recipes.items():
        recipe_updates.append(
            {
                CanadaForecastRecipeModel.site: key.site,
                CanadaForecastRecipeModel.week: key.week.to_number_format(),
                CanadaForecastRecipeModel.sku_code: key.sku_code,
                CanadaForecastRecipeModel.brand: brand,
                CanadaForecastRecipeModel.recipe: key.recipe_id,
                CanadaForecastRecipeModel.recipe_name: value.recipe_name,
                CanadaForecastRecipeModel.picks: value.picks,
            }
        )
    with app_db.transaction() as trn:
        oscar_repo.delete_canada_recipe_data(weeks, sites, brand, trn)
        oscar_repo.insert_canada_recipe_data(recipe_updates, trn)
