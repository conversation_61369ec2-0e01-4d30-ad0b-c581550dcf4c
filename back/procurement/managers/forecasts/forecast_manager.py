import dataclasses
from abc import ABC, abstractmethod
from collections.abc import Iterable
from typing import Type

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SITE, SKU_CODE, UNITS
from procurement.data.dto.forecasts.forecasts import Forecast
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.forecast import oscar as oscar_repo
from procurement.repository.forecast import packaging_forecast as packaging_forecast_repo


@dataclasses.dataclass
class ForecastData:
    forecast: UNITS
    scm_week: ScmWeek


class ForecastManager(ABC):
    @staticmethod
    @abstractmethod
    def get_next_existing_forecast_data(
        sku_code: str | None, sites: Iterable[DcConfig]
    ) -> dict[tuple[SKU_CODE, SITE], ForecastData]: ...

    @staticmethod
    @abstractmethod
    def get_forecast_oscar_by_sku_code(week: ScmWeek, site: str, brand: str) -> dict[SKU_CODE, UNITS]: ...

    @staticmethod
    @abstractmethod
    def get_all_forecasts(
        weeks: Iterable[ScmWeek], sites: Iterable[str] | None = None, sku_codes: set[str] | None = None
    ) -> tuple[Forecast, ...]: ...

    @staticmethod
    @abstractmethod
    def get_forecast_by_sku_code(
        site: str, week: ScmWeek, brand: str, sku_codes: frozenset[str] | None = None
    ) -> dict[str, UNITS]: ...

    @staticmethod
    @abstractmethod
    def get_forecast_by_weeks(site: str, weeks: Iterable[ScmWeek], brand: str) -> dict[ScmWeek, list[Forecast]]: ...

    @staticmethod
    def get_packaging_forecast_by_weeks(site: str, weeks: list[ScmWeek], brand: str) -> dict[ScmWeek, list[Forecast]]:
        return utils.group_by(
            packaging_forecast_repo.get_packaging_forecast_for_weeks(site=site, weeks=weeks, brand=brand),
            lambda it: it.scm_week,
        )


class UsForecastManager(ForecastManager):
    @staticmethod
    def get_next_existing_forecast_data(
        sku_code: str | None, sites: Iterable[DcConfig]
    ) -> dict[tuple[SKU_CODE, SITE], ForecastData]:
        raw_data = oscar_repo.get_next_existing_forecast(sites=sites, week=ScmWeek.current_week(), sku_code=sku_code)
        return {
            (item.sku_code, item.site): ForecastData(forecast=item.forecast, scm_week=item.scm_week)
            for item in raw_data
        }

    @staticmethod
    def get_forecast_oscar_by_sku_code(week: ScmWeek, site: str, brand: str) -> dict[SKU_CODE, UNITS]:
        return {
            item.sku_code: item.forecast
            for item in oscar_repo.get_ingredients_with_forecast(week=week, site=site, brand=brand)
        }

    @staticmethod
    def get_all_forecasts(
        weeks: Iterable[ScmWeek], sites: Iterable[str] | None = None, sku_codes: set[str] | None = None
    ) -> tuple[Forecast, ...]:
        return tuple(oscar_repo.get_forecasts(weeks, sites=sites, sku_codes=sku_codes))

    @staticmethod
    def get_forecast_by_sku_code(
        site: str, week: ScmWeek, brand: str, sku_codes: frozenset[str] | None = None
    ) -> dict[str, UNITS]:
        return {
            o.sku_code: o.forecast
            for o in oscar_repo.get_forecasts(
                sites=[site], weeks=frozenset((week,)) if week else None, brand=brand, sku_codes=sku_codes
            )
        }

    @staticmethod
    def get_forecast_by_weeks(site: str, weeks: Iterable[ScmWeek], brand: str) -> dict[ScmWeek, list[Forecast]]:
        return utils.group_by(oscar_repo.get_forecasts(sites=[site], weeks=weeks, brand=brand), lambda it: it.scm_week)


class CaForecastManager(ForecastManager):
    @staticmethod
    def get_next_existing_forecast_data(
        sku_code: str | None, sites: Iterable[DcConfig]
    ) -> dict[tuple[SKU_CODE, SITE], ForecastData]:
        raw_data = oscar_repo.get_next_existing_canada_forecast(
            sites=sites, week=ScmWeek.current_week(), sku_code=sku_code
        )
        return {
            (item.sku_code, item.site): ForecastData(forecast=item.forecast, scm_week=item.scm_week)
            for item in raw_data
        }

    @staticmethod
    def get_forecast_oscar_by_sku_code(week: ScmWeek, site: str, brand: str) -> dict[SKU_CODE, UNITS]:
        return {
            item.sku_code: item.forecast
            for item in oscar_repo.get_canada_ingredients_with_forecast(week=week, site=site, brand=brand)
        }

    @staticmethod
    def get_all_forecasts(
        weeks: Iterable[ScmWeek], sites: Iterable[str] | None = None, sku_codes: set[str] | None = None
    ) -> list[Forecast]:
        return oscar_repo.get_canada_forecasts(weeks, sites=sites, sku_codes=sku_codes)

    @staticmethod
    def get_forecast_by_sku_code(
        site: str, week: ScmWeek, brand: str, sku_codes: frozenset[str] | None = None
    ) -> dict[str, UNITS]:
        return {
            o.sku_code: o.forecast
            for o in oscar_repo.get_canada_forecasts(
                sites=[site], weeks=frozenset((week,)) if week else None, brand=brand, sku_codes=sku_codes
            )
        }

    @staticmethod
    def get_forecast_by_weeks(site: str, weeks: Iterable[ScmWeek], brand: str) -> dict[ScmWeek, list[Forecast]]:
        return utils.group_by(
            oscar_repo.get_canada_forecasts(sites=[site], weeks=weeks, brand=brand), lambda it: it.scm_week
        )


def get_forecast_manager() -> Type[ForecastManager]:
    market = context.get_request_context().market
    return UsForecastManager if market == MARKET_US or killswitch.use_oscar_for_ca_forecast else CaForecastManager
