import logging
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.data.dto.ordering.shipment import Shipment

logger = logging.getLogger(__name__)

# TODO: move these to constants package
CASE_TYPE = "CASE_TYPE"
UNIT_TYPE = "UNIT_TYPE"


@dataclass
class PurchaseOrder:
    dc: str
    sku_code: str
    sku_id: int
    sku_name: str
    supplier_code: str
    supplier: str
    po_number: str
    ot_po_status: str
    is_sent: bool
    week: ScmWeek
    order_date: datetime
    delivery_time_start: datetime
    order_size: int
    order_unit: str
    case_price: Decimal
    case_size: Decimal
    case_unit: UnitOfMeasure | None
    quantity: Decimal
    buffer: Decimal
    total_price: Decimal
    emergency_reason: str
    brand: str
    shipping_method: str
    po_buyer: str
    bob_code: str
    shipment_data: Shipment | None = None
    transfer_source_bob_code: str | None = None
    has_multiple_transfer_items: bool = False

    @property
    def po_sku_combination(self) -> str:
        return f"{self.po_number}{self.sku_code}"


def map_order_units(value: str) -> str:
    if value == CASE_TYPE:
        return "Case"
    if value == UNIT_TYPE:
        return "Unit"
    logger.warning("DB contains unexpected order_unit")
    return value
