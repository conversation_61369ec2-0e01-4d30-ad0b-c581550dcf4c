from decimal import Decimal

from confluent_kafka import Message
from confluent_kafka.serialization import Message<PERSON>ield, SerializationContext
from hellofresh.proto.stream.ordering.purchase_quantity_recommendation.v1alpha1.purchase_quantity_recommendation_pb2 import (  # noqa: E501
    PurchaseQuantityRecommendationKey,
    PurchaseQuantityRecommendationVal,
)

from procurement.core.dates import ScmWeek
from procurement.core.kafka import handlers as kafka_handler
from procurement.core.kafka.handlers import KafkaHandler
from procurement.data.dto.ordering.purchase_qty_recommendation import PurchaseQtyRecommendation
from procurement.data.models.ordering.purchase_qty_recommendation import PurchaseQtyRecommendationModel
from procurement.managers import kafka_utils
from procurement.repository.ordering import purchase_qty_recommendation as purchase_qty_recommendation_repo


class PurchaseQtyRecommendationHandler(KafkaHandler):
    _ignore_empty_messages = False

    def __init__(self):
        super().__init__()

    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return kafka_handler.parse_protobuf(key, PurchaseQuantityRecommendationKey, MessageField.VALUE)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handler.parse_protobuf(value, PurchaseQuantityRecommendationVal, MessageField.VALUE)

    def _is_key_accepted(self, msg: Message) -> bool:
        return msg.key().dc_code in kafka_utils.get_site_by_bob_code_map()

    def _process(self, msg: Message) -> None:
        key: PurchaseQuantityRecommendationKey = msg.key()
        value: PurchaseQuantityRecommendationVal = msg.value()
        week = int(ScmWeek(key.year_week.year, key.year_week.week))
        site = key.dc_code
        supplier_uuid = key.supplier_id
        sku_uuid = key.sku_id
        if not value.purchase_quantity_recommendations:
            purchase_qty_recommendation_repo.delete_purchase_qty_recommendation(week, site, sku_uuid, supplier_uuid)
            return
        updates = []
        for item in value.purchase_quantity_recommendations:
            if not item.total_qty.value:
                continue
            updates.append(
                {
                    PurchaseQtyRecommendationModel.site: site,
                    PurchaseQtyRecommendationModel.supplier_uuid: supplier_uuid,
                    PurchaseQtyRecommendationModel.week: week,
                    PurchaseQtyRecommendationModel.sku_uuid: sku_uuid,
                    PurchaseQtyRecommendationModel.quantity: Decimal(item.total_qty.value),
                }
            )
        purchase_qty_recommendation_repo.upsert_purchase_qty_recommendations(updates)


def get_purchase_qty_recommendations(
    sku_codes: list[str], bob_codes: set[str], weeks: tuple[ScmWeek, ...]
) -> list[PurchaseQtyRecommendation]:
    return purchase_qty_recommendation_repo.get_purchase_qty_recommendations(sku_codes, bob_codes, weeks)
