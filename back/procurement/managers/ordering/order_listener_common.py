from datetime import datetime
from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.constants.protobuf import OT_UOM_MAP
from procurement.managers.ordering import CASE_TYPE, UNIT_TYPE


def is_order_sent(sent_time: datetime) -> bool:
    return sent_time.year > 1970


def get_order_unit(sku) -> str:
    return CASE_TYPE if sku.WhichOneof("packaging") == "case_packaging" else UNIT_TYPE


def calculate_case_unit_and_size(sku, order_unit: str) -> tuple[UnitOfMeasure, Decimal]:
    if order_unit == CASE_TYPE:
        case_size = Decimal(sku.case_packaging.size.value)
        case_unit = OT_UOM_MAP.get(sku.case_packaging.unit)
    else:
        case_size = Decimal(1)
        case_unit = UnitOfMeasure.UNIT
    return case_unit, case_size
