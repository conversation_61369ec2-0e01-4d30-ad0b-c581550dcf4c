import logging
from decimal import Decimal
from typing import NamedTuple

from confluent_kafka import Message
from confluent_kafka.serialization import Message<PERSON><PERSON>, SerializationContext
from hellofresh.proto.stream.transfer_order.v1 import transfer_order_item_pb2, transfer_order_pb2

from procurement.constants.hellofresh_constant import NO_BRAND, UnitOfMeasure
from procurement.constants.protobuf import OT_UOM_MAP, TransferOrderStatus
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates.weeks import ScmWeek
from procurement.core.exceptions.listener_errors import MessageUnprocessableException
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.kafka.handlers import KafkaHandler
from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.managers import kafka_utils
from procurement.managers.ordering import CASE_TYPE, order_listener_common  # TODO: move this to constant folder
from procurement.repository.ordering.purchase_order import updates as po_repo
from procurement.services.database import app_db

logger = logging.getLogger(__name__)


class OrderLineInputModel(NamedTuple):
    line_item_uuid: str
    sku_uuid: str
    order_size: int
    order_unit: str
    case_price: Decimal
    case_size: Decimal
    case_unit: UnitOfMeasure
    quantity: Decimal
    total_price: Decimal
    original_order: str | None
    original_supplier_id: str | None
    original_supplier_code: str | None
    original_lot_code: str | None


class ToHandler(KafkaHandler):
    def __init__(self):
        super().__init__()

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, transfer_order_pb2.TransferOrder, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        to = msg.value()
        if to.destination_dc_code not in kafka_utils.get_known_bob_codes():
            return
        try:
            status = TransferOrderStatus(to.status)
            if not to.source_dc_code:
                raise ValueError("Empty source DC field")
            if not to.transfer_order_number:
                raise ValueError("Missing order number")
            if not to.sent_time.IsInitialized():
                raise ValueError("Missing sent time")
        except ValueError as exc:
            logger.exception(
                "Transfer order message %s can't be processed because of unknown ot_status '%s'",
                to.id,
                to.status,
            )
            raise MessageUnprocessableException() from exc
        _process_order(to, status)


def _process_order(to: transfer_order_pb2.TransferOrder, status: TransferOrderStatus) -> None:
    if (
        status in (TransferOrderStatus.CANCELLED, TransferOrderStatus.DELETED)
        or status == TransferOrderStatus.REJECTED
        and not killswitch.consume_rejected_orders
    ):
        with app_db.transaction() as trn:
            po_repo.delete_pos([to.transfer_order_number], trn)
    elif status != TransferOrderStatus.UNSPECIFIED and to.items:
        print(status.name)
        to_record = _parse_order(to, status)
        line_items = list(map(_parse_order_line, to.items))
        aggregated_line_items = _aggregate_transfer_order_skus(to, line_items)
        aggregated_transfer_line_items = _aggregate_transfer_line_items(to, line_items)
        with app_db.transaction() as trn:
            po_repo.upsert_pos([to_record], trn)
            po_repo.upsert_po_sku(aggregated_line_items, trn)
            po_repo.delete_to_sku(to.id, trn)
            po_repo.insert_to_sku(aggregated_transfer_line_items, trn)


def _parse_order(to: transfer_order_pb2.TransferOrder, status: TransferOrderStatus) -> dict:
    if site_info := kafka_utils.get_site_by_bob_code_map().get(to.destination_dc_code):
        brand = site_info.brand
    else:
        brand = NO_BRAND
    order_date = to.sent_time.ToDatetime()
    return {
        PurchaseOrderModel.po_uuid: to.id,
        PurchaseOrderModel.brand: brand,
        PurchaseOrderModel.po_number: to.transfer_order_number,
        PurchaseOrderModel.order_number: utils.get_order_number_from_po_number(to.transfer_order_number),
        PurchaseOrderModel.supplier_code: None,
        PurchaseOrderModel.supplier: None,
        PurchaseOrderModel.ot_po_status: status.name,
        PurchaseOrderModel.is_sent: order_listener_common.is_order_sent(order_date),
        PurchaseOrderModel.week: str(ScmWeek(year=to.production_week.year, week=to.production_week.week)),
        PurchaseOrderModel.order_date: order_date,
        PurchaseOrderModel.delivery_time_start: to.delivery_start_time.ToDatetime(),
        PurchaseOrderModel.shipping_method: to.shipping_method,
        PurchaseOrderModel.emergency_reason: to.reason_text or None,
        PurchaseOrderModel.ordered_by: to.creator_email,
        PurchaseOrderModel.ot_last_updated: to.update_time.ToDatetime(),
        PurchaseOrderModel.bob_code: to.destination_dc_code,
        PurchaseOrderModel.deleted: False,
        PurchaseOrderModel.source_bob_code: to.source_dc_code,
    }


def _parse_order_line(sku: transfer_order_item_pb2.TransferOrderItem) -> OrderLineInputModel:
    order_unit = order_listener_common.get_order_unit(sku)
    case_price = kafka_utils.parse_money(sku.price)
    case_unit, case_size = order_listener_common.calculate_case_unit_and_size(sku=sku, order_unit=order_unit)

    return OrderLineInputModel(
        line_item_uuid=sku.id,
        sku_uuid=sku.sku_id,
        order_size=sku.order_size,
        order_unit=order_unit,
        case_price=case_price,
        case_size=case_size,
        case_unit=case_unit,
        quantity=sku.order_size * case_size,
        total_price=kafka_utils.parse_money(sku.total_price),
        original_supplier_id=sku.supplier_id or None,  # replacing empty strings
        original_supplier_code=sku.supplier_code or None,
        original_order=sku.original_po_number or None,
        original_lot_code=sku.lot_number or None,
    )


def _calculate_case_unit_and_size(
    sku: transfer_order_item_pb2.TransferOrderItem, order_unit: str
) -> tuple[UnitOfMeasure, Decimal]:
    if order_unit == CASE_TYPE:
        case_size = Decimal(sku.case_packaging.size.value)
        case_unit = OT_UOM_MAP.get(sku.case_packaging.unit)
    else:
        case_size = Decimal(1)
        case_unit = UnitOfMeasure.UNIT
    return case_unit, case_size


def _aggregate_transfer_order_skus(to: transfer_order_pb2.TransferOrder, skus: list[OrderLineInputModel]) -> list[dict]:
    grouped_skus = utils.group_by(skus, lambda s: s.sku_uuid)
    res = []
    for sku_group in grouped_skus.values():
        order_size = 0
        quantity = 0
        total_price = Decimal()
        for sku in sku_group:
            order_size += sku.order_size
            quantity += sku.quantity
            total_price += sku.total_price
        sku = sku_group[0]
        res.append(
            {
                PurchaseOrderSkuModel.po_uuid: to.id,
                PurchaseOrderSkuModel.sku_uuid: sku.sku_uuid,
                PurchaseOrderSkuModel.order_size: order_size,
                PurchaseOrderSkuModel.order_unit: sku.order_unit,
                PurchaseOrderSkuModel.case_price: total_price / order_size if order_size else 0,
                PurchaseOrderSkuModel.case_size: round(quantity / order_size) if order_size else 0,
                PurchaseOrderSkuModel.case_unit: sku.case_unit.short_name,
                PurchaseOrderSkuModel.quantity: quantity,
                PurchaseOrderSkuModel.buffer: None,
                PurchaseOrderSkuModel.total_price: total_price,
            }
        )
    return res


def _calculate_aggregated_values(sku_items: list[OrderLineInputModel]):
    order_size = sum(sku.order_size for sku in sku_items)
    quantity = sum(sku.quantity for sku in sku_items)
    total_price = sum(sku.total_price for sku in sku_items)

    case_size = quantity / order_size if order_size else Decimal("0")
    case_price = total_price / order_size if order_size else Decimal("0")

    return {
        TransferOrderSkuModel.order_size: order_size,
        TransferOrderSkuModel.quantity: quantity,
        TransferOrderSkuModel.total_price: total_price,
        TransferOrderSkuModel.case_size: round(case_size, 9),
        TransferOrderSkuModel.case_price: round(case_price, 9),
    }


def _aggregate_transfer_line_items(to: transfer_order_pb2.TransferOrder, skus: list[OrderLineInputModel]) -> list[dict]:
    grouped_skus = utils.group_by(skus, lambda s: (s.sku_uuid, s.original_supplier_id))
    res = []
    for supplier_sku_group in grouped_skus.values():
        agg_values = _calculate_aggregated_values(supplier_sku_group)
        any_grouped_sku: OrderLineInputModel = supplier_sku_group[0]
        res.append(
            {
                TransferOrderSkuModel.po_uuid: to.id,
                TransferOrderSkuModel.line_item_uuid: any_grouped_sku.line_item_uuid,
                TransferOrderSkuModel.sku_uuid: any_grouped_sku.sku_uuid,
                TransferOrderSkuModel.order_unit: any_grouped_sku.order_unit,
                TransferOrderSkuModel.case_unit: any_grouped_sku.case_unit.short_name,
                TransferOrderSkuModel.original_po_number: any_grouped_sku.original_order,
                TransferOrderSkuModel.original_supplier_id: any_grouped_sku.original_supplier_id,
                TransferOrderSkuModel.original_supplier_code: any_grouped_sku.original_supplier_code,
                TransferOrderSkuModel.original_lot_code: any_grouped_sku.original_lot_code,
                **agg_values,
            }
        )

    return res
