import logging
from collections.abc import Iterable
from datetime import date
from decimal import Decimal
from functools import cached_property

from confluent_kafka import Message
from confluent_kafka.serialization import Message<PERSON>ield, SerializationContext, StringDeserializer
from hellofresh.proto.stream.supply.advance_shipping_notice.v2.advance_shipment_notification_pb2 import PurchaseOrderAsn

from procurement.constants.hellofresh_constant import MARKET_US, UnitOfMeasure
from procurement.constants.ordering import (
    ASN_PARTIALLY_SHIPPED,
    ASN_SHIPPED,
    INVALID,
    SUPPLIER_ACCEPTED_WITH_CHANGES,
    SUPPLIER_CANCELLED,
)
from procurement.constants.protobuf import GRN_IMT_UOM_MAP, GrnUnitMeasure
from procurement.core.exceptions.listener_errors import MessageUnprocessableException
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.request_utils import context
from procurement.core.typing import PO_NUMBER, SKU_CODE
from procurement.data.dto.ordering.asn import AdvanceShippingNoticeDto
from procurement.data.dto.ordering.purchase_order.pimt_pos import PimtPoItemBase
from procurement.data.models.ordering.advance_shipping_notice import (
    AdvanceShippingNoticeModel,
    AdvanceShippingNoticeState,
)
from procurement.managers.kafka_utils import build_datetime
from procurement.managers.ordering import PurchaseOrder
from procurement.repository.ordering import advance_shipping_notice as advance_shipping_notice_repo

logger = logging.getLogger(__name__)

SHIPPING_STATE_PO_STATUS_MAPPING = {
    AdvanceShippingNoticeState.SHIPPING_STATE_SHIPPED: ASN_SHIPPED,
    AdvanceShippingNoticeState.SHIPPING_STATE_PARTIALLY_SHIPPED: ASN_PARTIALLY_SHIPPED,
    AdvanceShippingNoticeState.SHIPPING_STATE_UNSPECIFIED: INVALID,
    AdvanceShippingNoticeState.SHIPPING_STATE_ON_HOLD: SUPPLIER_ACCEPTED_WITH_CHANGES,
    AdvanceShippingNoticeState.SHIPPING_STATE_CANCELLED: SUPPLIER_CANCELLED,
}


class AdvanceShippingNoticeHandler(kafka_handlers.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, PurchaseOrderAsn, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        upsert_advance_shipping_notice(msg.value())


def upsert_advance_shipping_notice(value: PurchaseOrderAsn) -> None:
    order_number = value.purchase_order_number
    po_uuid = value.purchase_order_id
    shipment_time = build_datetime(value.shipment.shipment_time)
    planned_delivery_time = build_datetime(value.shipment.planned_delivery_time)
    advance_shipping_notices = []
    for asn_item in value.items:
        try:
            uom, packing_size = _get_asn_packing_size_and_uom(asn_item.shipped_quantity)
            advance_shipping_notices.append(
                {
                    AdvanceShippingNoticeModel.order_number: order_number,
                    AdvanceShippingNoticeModel.po_uuid: po_uuid,
                    AdvanceShippingNoticeModel.shipment_time: shipment_time,
                    AdvanceShippingNoticeModel.planned_delivery_time: planned_delivery_time,
                    AdvanceShippingNoticeModel.sku_code: asn_item.sku_code,
                    AdvanceShippingNoticeModel.shipping_state: AdvanceShippingNoticeState(asn_item.shipping_state),
                    AdvanceShippingNoticeModel.size: asn_item.shipped_quantity.order_size,
                    AdvanceShippingNoticeModel.packing_size: packing_size,
                    AdvanceShippingNoticeModel.unit_measure: uom,
                }
            )
        except ValueError as exc:
            logger.exception("Message can't be processed with po_number=%s because of", order_number)
            raise MessageUnprocessableException() from exc
    advance_shipping_notice_repo.upsert_advance_shipping_notice(advance_shipping_notices)


def _get_asn_packing_size_and_uom(
    shipped_quantity: PurchaseOrderAsn.AsnItem.Quantity,
) -> tuple[GrnUnitMeasure, Decimal]:
    if shipped_quantity.WhichOneof("packaging") == "case_packaging":
        packing_size = (
            Decimal(shipped_quantity.case_packaging.size.value) if shipped_quantity.case_packaging.size.value else None
        )
        return GrnUnitMeasure(shipped_quantity.case_packaging.unit), packing_size
    return GrnUnitMeasure.UNIT, Decimal(1)


def get_advance_shipping_notice_by_po_numbers(
    po_numbers: Iterable[PO_NUMBER],
) -> dict[tuple[PO_NUMBER, SKU_CODE], AdvanceShippingNoticeDto]:
    return {
        (item.po_number, item.sku_code): item
        for item in advance_shipping_notice_repo.get_advance_shipping_notice_by_po_numbers(po_numbers)
    }


class AsnPoStatusCalculations:
    def __init__(self, asn_data: AdvanceShippingNoticeDto | None, purchase_order: PimtPoItemBase | PurchaseOrder):
        self.asn_data = None if context.get_request_context().market != MARKET_US else asn_data
        self.po = purchase_order

    @cached_property
    def unit_of_measure(self) -> UnitOfMeasure | None:
        if not self.asn_data:
            return None
        return GRN_IMT_UOM_MAP.get(self.asn_data.unit_measure)

    @property
    def shipment_date(self) -> date | None:
        return self.asn_data.shipment_time.date() if self.asn_data else None

    @property
    def planned_delivery_time(self) -> date | None:
        return self.asn_data.planned_delivery_time.date() if self.asn_data else None

    @property
    def shipped_quantity_cases(self) -> int | None:
        return self.asn_data.size if self.asn_data else None

    @cached_property
    def case_count(self) -> int | None:
        return int(self.asn_data.packing_size) if self.asn_data else None

    @cached_property
    def shipped_quantity_units(self) -> int | None:
        if not self.unit_of_measure:
            return None
        if self.unit_of_measure == UnitOfMeasure.UNIT:
            return self.asn_data.size
        return self.asn_data.size * int(self.asn_data.packing_size)

    @property
    def asn_requires_high_attention(self) -> bool:
        return self.asn_data and (
            self.shipped_quantity_units != self.po.quantity
            or self.planned_delivery_time > self.po.delivery_time_start.date()
        )

    @property
    def asn_requires_attention(self) -> bool:
        return (
            self.asn_data
            and not self.asn_requires_high_attention
            and (
                self.case_count != self.po.case_size
                or self.planned_delivery_time < self.po.delivery_time_start.date()
                or self.asn_data.shipping_state == AdvanceShippingNoticeState.SHIPPING_STATE_PARTIALLY_SHIPPED
            )
        )

    @property
    def po_status(self) -> str | None:
        if not self.asn_data:
            return None
        return SHIPPING_STATE_PO_STATUS_MAPPING.get(self.asn_data.shipping_state)
