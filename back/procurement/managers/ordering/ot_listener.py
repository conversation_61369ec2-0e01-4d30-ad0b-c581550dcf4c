import logging
from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext
from hellofresh.proto.stream.supply.procurement.purchase_order.v1 import purchase_order_pb2
from sqlalchemy import Connection

from procurement.constants.hellofresh_constant import NO_BRAND, UnitOfMeasure
from procurement.constants.protobuf import OrderingToolPoStatus, ShippingMethod
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates.weeks import ScmWeek
from procurement.core.exceptions.listener_errors import MessageUnprocessableException
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.kafka.handlers import KafkaHandler
from procurement.core.write_buffer import WriteBuffer
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.managers import kafka_utils
from procurement.managers.ordering import order_listener_common
from procurement.repository.ordering.purchase_order import updates as po_repo
from procurement.services.database import app_db

logger = logging.getLogger(__name__)


class OrderLineInputModel(NamedTuple):
    sku_uuid: str
    order_size: int
    order_unit: str
    case_price: Decimal
    case_size: Decimal
    case_unit: UnitOfMeasure
    quantity: Decimal
    buffer: Decimal
    total_price: Decimal


class OrderInputModel(NamedTuple):
    order_number: str
    po_uuid: str
    brand: str
    market: str
    po_number: str
    supplier_code: int
    ot_po_status: OrderingToolPoStatus
    week: ScmWeek
    order_date: datetime
    delivery_time_start: datetime
    emergency_reason: str
    ordered_by: str
    ot_last_updated: datetime
    shipping_method: str
    bob_code: str
    lines: list[OrderLineInputModel]


class OtHandler(KafkaHandler):
    def __init__(self):
        super().__init__()
        self._buffer = WriteBuffer[OrderInputModel]("orders", sink=_save_orders)

    def close(self) -> None:
        super().close()
        self._buffer.flush(empty=True)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, purchase_order_pb2.PurchaseOrder, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        po = msg.value()
        if po.distribution_center_code not in kafka_utils.get_known_bob_codes():
            return
        try:
            status = OrderingToolPoStatus(po.status)
        except ValueError as exc:
            logger.exception(
                "OT message %s can't be processed because of unknown ot_status '%s'",
                po.id,
                po.status,
            )
            raise MessageUnprocessableException() from exc
        if not po.send_time.IsInitialized():
            raise MessageUnprocessableException()
        order = _parse_order(po, status)
        self._buffer.write(order)


def _parse_order(po: purchase_order_pb2.PurchaseOrder, ot_status: OrderingToolPoStatus) -> OrderInputModel:
    if site_info := kafka_utils.get_site_by_bob_code_map().get(po.distribution_center_code):
        brand = site_info.brand
    else:
        brand = NO_BRAND
    try:
        shipping_method = ShippingMethod(po.shipping.method)
    except ValueError as exc:
        logger.exception(
            "OT message %s can't be processed because of unknown shipping_method '%s'",
            po.id,
            po.shipping.method,
        )
        raise MessageUnprocessableException() from exc
    send_time = po.send_time.ToDatetime()
    return OrderInputModel(
        order_number=po.revision.number.formatted,
        po_uuid=po.id,
        brand=brand,
        market=po.region_code,
        po_number=po.revision.formatted,
        supplier_code=po.supplier_code,
        ot_po_status=ot_status,
        week=ScmWeek(year=po.year_week.year, week=po.year_week.week),
        order_date=send_time,
        delivery_time_start=po.expected_arrival.start_time.ToDatetime(),
        emergency_reason=po.emergency_reason or "",
        ordered_by=po.creator_email,
        ot_last_updated=po.update_time.ToDatetime(),
        shipping_method=" ".join(word.capitalize() for word in shipping_method.name.split(sep="_")),
        bob_code=po.distribution_center_code,
        lines=[_parse_order_line(s) for s in po.order_items],
    )


def _parse_order_line(sku) -> OrderLineInputModel:
    order_unit = order_listener_common.get_order_unit(sku)
    case_price = kafka_utils.parse_money(sku.price)
    case_unit, case_size = order_listener_common.calculate_case_unit_and_size(sku=sku, order_unit=order_unit)

    return OrderLineInputModel(
        sku_uuid=sku.sku_id,
        order_size=sku.order_size,
        order_unit=order_unit,
        case_price=case_price,
        case_size=case_size,
        case_unit=case_unit,
        quantity=Decimal(sku.quantity.value),
        buffer=Decimal(str(sku.buffer.value)),
        total_price=kafka_utils.parse_money(sku.total_price),
    )


def _is_po_input_valid(po: OrderInputModel) -> bool:
    if None in po or not po.lines:
        return False
    for line in po.lines:
        if None in line:
            return False
    return True


def _save_orders(updates: list[OrderInputModel]) -> None:
    pos: dict[str, OrderInputModel] = {}
    for update in updates:
        if not _is_po_input_valid(update):
            logger.warning("Invalid item consumed -- %s", update)
            continue
        pos[update.order_number] = update
    to_update = []
    for order in pos.values():
        if order.ot_po_status not in (OrderingToolPoStatus.DELETED, OrderingToolPoStatus.UNSPECIFIED) and (
            killswitch.consume_rejected_orders or order.ot_po_status != OrderingToolPoStatus.REJECTED
        ):
            to_update.append(order)
    with app_db.transaction() as trns:
        po_repo.delete_pos(pos, transaction=trns)
        if to_update:
            _upsert_pos(to_update, trns)
            _upsert_po_sku_item(to_update, trns)


def _upsert_pos(pos: list[OrderInputModel], transaction: Connection) -> None:
    po_repo.upsert_pos(
        pos=[
            {
                PurchaseOrderModel.po_uuid: po.po_uuid,
                PurchaseOrderModel.brand: po.brand,
                PurchaseOrderModel.po_number: po.po_number,
                PurchaseOrderModel.order_number: utils.get_order_number_from_po_number(po.po_number),
                PurchaseOrderModel.supplier_code: po.supplier_code,
                PurchaseOrderModel.ot_po_status: po.ot_po_status.name,
                PurchaseOrderModel.is_sent: order_listener_common.is_order_sent(po.order_date),
                PurchaseOrderModel.week: str(po.week),
                PurchaseOrderModel.order_date: po.order_date,
                PurchaseOrderModel.delivery_time_start: po.delivery_time_start,
                PurchaseOrderModel.emergency_reason: po.emergency_reason,
                PurchaseOrderModel.ordered_by: po.ordered_by,
                PurchaseOrderModel.ot_last_updated: po.ot_last_updated,
                PurchaseOrderModel.shipping_method: po.shipping_method,
                PurchaseOrderModel.bob_code: po.bob_code,
                PurchaseOrderModel.deleted: False,
            }
            for po in pos
        ],
        transaction=transaction,
    )


def _upsert_po_sku_item(pos: list[OrderInputModel], transaction: Connection) -> None:
    po_repo.upsert_po_sku(
        po_skus=[
            {
                PurchaseOrderSkuModel.po_uuid: po.po_uuid,
                PurchaseOrderSkuModel.sku_uuid: sku.sku_uuid,
                PurchaseOrderSkuModel.order_size: sku.order_size,
                PurchaseOrderSkuModel.order_unit: sku.order_unit,
                PurchaseOrderSkuModel.case_price: sku.case_price,
                PurchaseOrderSkuModel.case_size: sku.case_size,
                PurchaseOrderSkuModel.case_unit: sku.case_unit.short_name,
                PurchaseOrderSkuModel.quantity: sku.quantity,
                PurchaseOrderSkuModel.buffer: sku.buffer,
                PurchaseOrderSkuModel.total_price: sku.total_price,
            }
            for po in pos
            for sku in po.lines
        ],
        transaction=transaction,
    )
