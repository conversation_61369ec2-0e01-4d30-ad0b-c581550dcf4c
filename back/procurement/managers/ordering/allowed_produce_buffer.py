from decimal import Decimal

from procurement.core.dates.weeks import ScmWeek
from procurement.data.models.ordering.allowed_produce_buffer import AllowedProduceBufferModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.ordering import allowed_produce_buffer as allowed_buffer_repo
from procurement.repository.snowflake import allowed_buffer


def update_allowed_buffers() -> None:
    current_week = ScmWeek.current_week()
    updates = [
        {
            AllowedProduceBufferModel.week: int(item.week),
            AllowedProduceBufferModel.brand: item.brand,
            AllowedProduceBufferModel.site: item.site,
            AllowedProduceBufferModel.sku_code: item.sku_code,
            AllowedProduceBufferModel.total_buffer: item.total_buffer,
        }
        for item in allowed_buffer.get_allowed_buffers_from_snowflake(
            start_week=current_week - 2, end_week=current_week + 4
        )
    ]
    allowed_buffer_repo.update_allowed_buffers(updates=updates)


def get_allowed_produce_buffers_by_sku_code(dc_obj: DcConfig, week: ScmWeek) -> dict[str, Decimal]:
    return {
        item.sku_code: item.total_buffer for item in allowed_buffer_repo.get_allowed_buffers(dc_obj=dc_obj, week=week)
    }
