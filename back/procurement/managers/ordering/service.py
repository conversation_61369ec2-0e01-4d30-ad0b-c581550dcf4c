import dataclasses
from collections.abc import Iterable, Set
from datetime import date, datetime
from decimal import Decimal
from typing import Protocol

from sqlalchemy import Select

from procurement.constants.hellofresh_constant import CHARITY_EMERGENCY_REASON, MARKET_CA, POAcknowledgementState
from procurement.constants.ordering import (
    DC_ROLLOVER_INVENTORY_SUPPLIER,
    DELIVERY_REJECTED,
    INVALID,
    IS_SENT,
    IS_SENT_PENDING_ACCEPTANCE,
    NOT_DELIVERED_PAST_DUE,
    PO_VOID,
    RECEIVED_ACCURATE,
    RECEIVED_OVER,
    RECEIVED_UNDER,
    SUPPLIER_ACCEPTED,
    SUPPLIER_ACCEPTED_WITH_CHANGES,
    SUPPLIER_REJECTED,
)
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import PO_NUMBER, SKU_CODE, SKU_NAME, UNITS
from procurement.data.dto.ordering.purchase_order.incoming import IncomingOrder
from procurement.managers.admin import common_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.ordering import PurchaseOrder
from procurement.managers.ordering.advance_shipping_notice import AsnPoStatusCalculations
from procurement.managers.ordering.grn import GrnPurchaseOrder
from procurement.repository.inventory.context import TimeRangeContext
from procurement.repository.ordering import culinary_sku
from procurement.repository.ordering import purchase_order as po_repo
from procurement.repository.ordering.purchase_order import PoFilters


@dataclasses.dataclass
class PoExportContext:
    weeks: tuple[ScmWeek, ...]
    site: str
    brand: str
    sku_codes: Set[str] = None
    category_filter: frozenset[str] = None
    group_filter: frozenset[str] = None
    include_empty_categories: bool = False
    include_shipment_data: bool = False
    is_future_pull: bool = False
    filter_general_supplier: bool = False
    filter_hf_supplier: bool = False


class OrderStatusCalculationData(Protocol):
    po_number: str
    sku_code: str
    quantity: UNITS
    has_grn: bool
    grn_units: UNITS
    delivery_time_start: datetime
    supplier_code: int


def get_purchase_orders(po_export_context: PoExportContext) -> list[PurchaseOrder]:
    week_dcs = [
        (week, dc_admin.get_site(week=week, brand=po_export_context.brand, site=po_export_context.site))
        for week in po_export_context.weeks
    ]
    if not week_dcs:
        return []
    db_result = po_repo.get_po_data_db(
        weeks_dcs=week_dcs,
        skus=po_export_context.sku_codes,
        filters=PoFilters(
            category_filter=po_export_context.category_filter,
            group_filter=po_export_context.group_filter,
            include_empty_categories=po_export_context.include_empty_categories,
            is_future_pull=po_export_context.is_future_pull,
            filter_general_supplier=po_export_context.filter_general_supplier,
            filter_hf_supplier=po_export_context.filter_hf_supplier,
        ),
        include_shipment_data=po_export_context.include_shipment_data,
    )
    return db_result


def get_purchase_order(po_number: str, sku_code: str) -> PurchaseOrder:
    return po_repo.get_po_data_item(po_number, sku_code)


def get_transfer_order(po_number: str, sku_code: str) -> list[PurchaseOrder]:
    return po_repo.get_to_data_item(po_number, sku_code)


def get_transfer_orders_by_date_range(
    bob_codes: list[str], date_from: datetime, date_to: datetime
) -> list[PurchaseOrder]:
    return po_repo.get_to_data(bob_codes, TimeRangeContext(date_from, date_to))


def get_po_sku_filtered_purchase_orders(
    dc_object: DcConfig,
    po_sku_filtering_query: Select,
    filter_general_supplier: bool = False,
    filter_hf_supplier: bool = False,
) -> list[PurchaseOrder]:
    return po_repo.get_po_sku_filtered_purchase_orders(
        dc_object,
        po_sku_filtering_query,
        PoFilters(
            filter_general_supplier=filter_general_supplier,
            filter_hf_supplier=filter_hf_supplier,
        ),
    )


def get_sku_filtered_purchase_orders(
    skus: Select, dc_object: DcConfig, date_from: datetime, date_to: datetime, with_shipment: bool = False
) -> list[PurchaseOrder]:
    return po_repo.get_purchase_orders_by_sku_codes(
        skus=skus, dc_object=dc_object, date_from=date_from, date_to=date_to, with_shipment=with_shipment
    )


def get_incoming_orders_excluding_receives(
    dc_object: DcConfig,
    date_from: datetime,
    date_to: datetime,
    only_early_receives=True,
) -> list[IncomingOrder]:
    return po_repo.get_incoming_orders_excluding_receives(dc_object, date_from, date_to, only_early_receives)


def get_sku_names_by_codes(sku_codes: frozenset[str]) -> dict[SKU_CODE, SKU_NAME]:
    return {sku.sku_code: sku.sku_name for sku in culinary_sku.get_sku_name_by_sku_codes(sku_codes)}


def process_po_ack_status(po_ack_status: POAcknowledgementState) -> str | None:
    if po_ack_status == POAcknowledgementState.ACCEPTED:
        return SUPPLIER_ACCEPTED
    if po_ack_status == POAcknowledgementState.ACCEPTED_WITH_CHANGES:
        return SUPPLIER_ACCEPTED_WITH_CHANGES
    if po_ack_status == POAcknowledgementState.REJECTED:
        return SUPPLIER_REJECTED
    if po_ack_status == POAcknowledgementState.UNSPECIFIED:
        return INVALID
    return None


def process_received_po(po_quantity: int | Decimal, quantity_received: int | Decimal) -> str | None:
    if po_quantity == quantity_received:
        return RECEIVED_ACCURATE
    if po_quantity > quantity_received:
        return RECEIVED_UNDER
    if po_quantity < quantity_received:
        return RECEIVED_OVER
    return None


def calculate_po_status(
    po_voids: set[tuple[PO_NUMBER, SKU_CODE]],
    purchase_order: OrderStatusCalculationData | GrnPurchaseOrder,
    po_ack_status: POAcknowledgementState | None,
    asn_calculations: AsnPoStatusCalculations,
    override_received_units: UNITS | None = None,
) -> str:
    if (purchase_order.po_number, purchase_order.sku_code) in po_voids:
        return PO_VOID

    if purchase_order.has_grn or override_received_units is not None:
        received_units = override_received_units if override_received_units is not None else purchase_order.grn_units
        return (
            DELIVERY_REJECTED if received_units == 0 else process_received_po(purchase_order.quantity, received_units)
        )

    if purchase_order.delivery_time_start.date() < date.today():
        return NOT_DELIVERED_PAST_DUE

    if (asn_po_status := asn_calculations.po_status) is not None:
        return asn_po_status

    if po_ack_status is not None:
        return process_po_ack_status(po_ack_status)

    return IS_SENT_PENDING_ACCEPTANCE if context.get_request_context().market != MARKET_CA else IS_SENT


def is_po_auto_voided(po: IncomingOrder | PurchaseOrder) -> bool:
    return po.quantity == 0 or po.emergency_reason == CHARITY_EMERGENCY_REASON


def is_po_supplier_auto_received(po: IncomingOrder | PurchaseOrder, dc_autobagger_supplier: str | None) -> bool:
    # any non-empty string in case if it's not an autobagger DC
    dc_autobagger_supplier = dc_autobagger_supplier or DC_ROLLOVER_INVENTORY_SUPPLIER
    return po.supplier in (dc_autobagger_supplier, DC_ROLLOVER_INVENTORY_SUPPLIER)


def get_inbound_quantities(
    weeks: Iterable[ScmWeek],
    bob_codes: Iterable[str],
    exclude_internal_transfers: bool = True,
    sku_codes: set[str] | None = None,
) -> list[IncomingOrder]:
    internal_suppliers = (
        common_admin.get_all_internal_suppliers().union(common_admin.get_all_bob_codes())
        if exclude_internal_transfers
        else None
    )
    return po_repo.get_inbound_orders(
        weeks=weeks, bob_codes=bob_codes, exclude_suppliers=internal_suppliers, sku_codes=sku_codes
    )
