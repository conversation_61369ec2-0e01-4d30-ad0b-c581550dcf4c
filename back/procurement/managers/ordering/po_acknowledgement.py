import dataclasses
import logging
from collections.abc import Iterable
from datetime import datetime
from decimal import Decimal
from typing import Any

from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext, StringDeserializer
from hellofresh.proto.stream.supply.purchase_order.acknowledgement.v2 import purchase_order_acknowledgement_pb2
from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import POAcknowledgementState, PoAckUnitOfMeasure
from procurement.core import utils
from procurement.core.exceptions.listener_errors import MessageUnprocessableException
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import PO_NUMBER, SKU_CODE
from procurement.data.models.ordering.po_acknowledgement import POAcknowledgementLineItemsModel
from procurement.managers.kafka_utils import build_datetime
from procurement.repository.ordering import po_acknowledgement as po_acknowledgement_repo

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class PoAcknowledgementData:
    promised_date: datetime
    state: POAcknowledgementState
    unit_of_measure: dataclasses.InitVar[PoAckUnitOfMeasure]
    size: dataclasses.InitVar[int]
    packing_size: dataclasses.InitVar[int]

    quantity_cases: int = dataclasses.field(init=False)
    quantity_units: int = dataclasses.field(init=False)
    units_per_cases: int = dataclasses.field(init=False)

    def __post_init__(self, unit_of_measure: PoAckUnitOfMeasure, size: int, packing_size: int):
        self.quantity_cases = size if unit_of_measure == PoAckUnitOfMeasure.CASE else ""
        self.quantity_units = size * packing_size if unit_of_measure == PoAckUnitOfMeasure.CASE else size
        self.units_per_cases = packing_size if unit_of_measure == PoAckUnitOfMeasure.CASE else ""


class POAcknowledgementHandler(kafka_handlers.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(
            value, purchase_order_acknowledgement_pb2.PurchaseOrderAcknowledgement, MessageField.VALUE
        )

    def _process(self, msg: Message) -> None:
        update_po_acknowledgement(msg.value())


def update_po_acknowledgement(value: purchase_order_acknowledgement_pb2.PurchaseOrderAcknowledgement) -> None:
    po_acknowledgement_line_items = []

    for po_line_item in value.items:
        if item := _build_po_line_item(po_line_item, value.purchase_order_number, value.purchase_order_id):
            po_acknowledgement_line_items.append(item)

    upsert_po_acknowledgement(po_acknowledgement_line_items)


def _build_po_line_item(po_line_item, order_number, po_uuid) -> dict[ColumnElement, Any] | None:
    sku_code = po_line_item.sku_code
    sku_id = utils.get_sku_id_from_sku_code(sku_code)
    if sku_id is None:
        ApplicationMetrics.non_critical_exceptions(
            service="PurchaseOrderAcknowledgement Kafka handler",
            message=f"Failed to parse sku_id from sku_code: {sku_code}",
        )
        return None
    try:
        state = POAcknowledgementState(po_line_item.state)
    except ValueError as exc:
        logger.exception("Acknowledgement Message with id=%s can't be processed because of", po_line_item.id)
        raise MessageUnprocessableException() from exc
    promised_quantity = po_line_item.promised_quantity
    unit_of_measure, packing_size = _get_unit_of_measure_and_packing_size(promised_quantity)
    return {
        POAcknowledgementLineItemsModel.po_uuid: po_uuid,
        POAcknowledgementLineItemsModel.order_number: order_number,
        POAcknowledgementLineItemsModel.sku_code: sku_code,
        POAcknowledgementLineItemsModel.sku_id: sku_id,
        POAcknowledgementLineItemsModel.state: int(state),
        POAcknowledgementLineItemsModel.number_of_pallets: po_line_item.pallets_quantity,
        POAcknowledgementLineItemsModel.promised_date: build_datetime(po_line_item.promised_time),
        POAcknowledgementLineItemsModel.unit_of_measure: str(unit_of_measure),
        POAcknowledgementLineItemsModel.size: promised_quantity.order_size,
        POAcknowledgementLineItemsModel.packing_size: packing_size,
    }


def _get_unit_of_measure_and_packing_size(promised_quantity) -> tuple[PoAckUnitOfMeasure, int]:
    if promised_quantity.WhichOneof("packaging") == "case_packaging":
        unit_of_measure = PoAckUnitOfMeasure.CASE
        packing_size = (
            round(Decimal(promised_quantity.case_packaging.size.value))
            if promised_quantity.case_packaging.size.value
            else None
        )  # TODO migrate to Decimal
    else:
        unit_of_measure = PoAckUnitOfMeasure.UNIT
        packing_size = 1
    return unit_of_measure, packing_size


def upsert_po_acknowledgement(po_line_items: list[dict[ColumnElement, Any]]) -> None:
    po_acknowledgement_repo.upsert_po_acknowledgement_and_line_items(po_line_items)


def get_po_acknowledgements_proposed_data(
    po_numbers: Iterable[PO_NUMBER],
) -> dict[tuple[PO_NUMBER, SKU_CODE], PoAcknowledgementData]:
    po_acknowledgement_proposed_data = po_acknowledgement_repo.get_po_acknowledgements_proposed_data(po_numbers)
    return {
        (item.po_number, item.sku_code): PoAcknowledgementData(
            unit_of_measure=item.unit_of_measure,
            size=item.size,
            packing_size=item.packing_size,
            promised_date=item.promised_date,
            state=item.state,
        )
        for item in po_acknowledgement_proposed_data
    }
