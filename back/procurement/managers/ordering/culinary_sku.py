import json
from datetime import datetime
from typing import Any, Type

from confluent_kafka import Message
from confluent_kafka.schema_registry.avro import AvroDeserializer
from confluent_kafka.serialization import SerializationContext, StringDeserializer
from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import (
    BRAND_CP_FULL,
    BRAND_HF_CP_FULL,
    BRAND_HF_FULL,
    MARKET_US,
    UnitOfMeasure,
)
from procurement.core import utils
from procurement.core.kafka import handlers as kafka_handler
from procurement.data.dto.sku import SupplierSkuRecord
from procurement.data.models.inventory.ingredient import PurchasingCategoryModel, PurchasingSubcategoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.managers import kafka_utils
from procurement.repository.inventory import ingredient as ingredient_repo
from procurement.repository.inventory import supplier_sku as supplier_sku_repo
from procurement.repository.ordering import culinary_sku as culinary_sku_repo

from ..admin import dc_admin
from . import hardcoded_sku_brands

_MANUFACTURED_SKU: str = "Manufactured SKU"


class CulinarySkuHandler(kafka_handler.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        deserializer = AvroDeserializer(
            schema_registry_client=kafka_handler.get_schema_registry_client(),
            from_dict=self._from_dict,
        )
        return deserializer(value, ctx)

    def _process(self, msg: Message) -> None:
        if msg.value():
            culinary_sku_repo.upsert_culinary_sku(msg.value())

    @staticmethod
    def _get_brands(item: dict) -> list[str]:
        market = item["market"].upper()
        if market == MARKET_US:
            return hardcoded_sku_brands.BRANDS.get(item["code"], item["brands"])
        brands = set(item["brands"])
        if BRAND_CP_FULL in brands or BRAND_HF_FULL in brands:
            brands -= {BRAND_CP_FULL, BRAND_HF_FULL}
            brands.add(BRAND_HF_CP_FULL)
        return list(brands)

    @staticmethod
    def _get_category_model_id(
        raw_category: str | None, model: Type[PurchasingCategoryModel | PurchasingSubcategoryModel]
    ) -> int | None:
        if not raw_category:
            return None
        category = raw_category.strip()
        return ingredient_repo.get_or_create_purchasing_category_model_id(category, model) if category else None

    def _from_dict(self, item: dict, *args) -> dict[ColumnElement, Any]:  # pylint: disable=unused-argument
        market = item["market"].upper()
        if market not in dc_admin.get_all_market_codes():
            return {}
        last_updated = kafka_utils.read_avro_datetime(item["updated_at"] or item["created_at"]) or datetime.now()
        extras = json.loads(json.loads(item.get("extras", "{}")).get("extras", "{}"))
        raw_unit = extras.get("wms_uom") or UnitOfMeasure.EACH.short_name
        unit = UnitOfMeasure.inexact_value_of(raw_unit)
        purchasing_category_id = self._get_category_model_id(extras.get("purchasing_category"), PurchasingCategoryModel)
        purchasing_subcategory_id = self._get_category_model_id(item["subcategory"], PurchasingSubcategoryModel)

        return {
            CulinarySkuModel.sku_uuid: item["id"],
            CulinarySkuModel.sku_code: item["code"],
            CulinarySkuModel.sku_id: utils.get_sku_id_from_sku_code(item["code"]),
            CulinarySkuModel.sku_name: item["name"],
            CulinarySkuModel.last_updated: last_updated,
            CulinarySkuModel.status: item["status"],
            CulinarySkuModel.storage_location: item["cooling_type"]["name"],
            CulinarySkuModel.unit: unit.short_name if unit else (raw_unit.strip().lower() or None),
            CulinarySkuModel.brands: self._get_brands(item),
            CulinarySkuModel.sku_type: item["type"].strip().lower(),
            CulinarySkuModel.purchasing_category_id: purchasing_category_id,
            CulinarySkuModel.purchasing_subcategory_id: purchasing_subcategory_id,
            CulinarySkuModel.market: market,
            CulinarySkuModel.is_manufactured_sku: bool(
                item["product_types"] and _MANUFACTURED_SKU in item["product_types"]
            ),
        }


class SupplierSkuHandler(kafka_handler.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        deserializer = AvroDeserializer(
            schema_registry_client=kafka_handler.get_schema_registry_client(),
            from_dict=SupplierSkuHandler._from_dict,
        )
        return deserializer(value, ctx)

    def _process(self, msg: Message) -> None:
        supplier_sku_repo.update_supplier_sku(msg.value())

    @staticmethod
    def _from_dict(item: dict, *args) -> SupplierSkuRecord:  # pylint: disable=unused-argument
        tmst_str = (item["updated_at"] or item["created_at"])[:26]
        try:
            tmst = datetime.strptime(tmst_str, "%Y-%m-%dT%H:%M:%S.%f")
        except ValueError:
            tmst = datetime.strptime(tmst_str[:19], "%Y-%m-%dT%H:%M:%S")
        return SupplierSkuRecord(
            supplier_sku_uuid=str(item["id"]),
            culinary_sku_uuid=str(item["culinary_sku_id"]),
            last_updated=tmst,
        )
