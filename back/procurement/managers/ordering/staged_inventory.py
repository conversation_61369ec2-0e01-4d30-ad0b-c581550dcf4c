from collections import defaultdict
from typing import NamedTuple

from procurement.client.googlesheets import googlesheet_utils
from procurement.core.dates.weeks import ScmWeek
from procurement.data.googlesheet_model.staged_inventory import StagedInventorySheet
from procurement.data.models.ordering.staged_inventory import StagedInventoryModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.ordering import staged_inventory as staged_inventory_repo


class StagedInventoryKey(NamedTuple):
    week: int
    site: str
    sub_recipe_id: str


def import_staged_inventory() -> None:
    sheet_data = gsheet_admin.read_gsheet(StagedInventorySheet())
    if not sheet_data:
        return
    data = defaultdict(int)
    for row in sheet_data:
        try:
            googlesheet_utils.validate_required_fields(row, ["week", "site", "sub_recipe_id"])
            data[
                StagedInventoryKey(week=int(ScmWeek.from_str(row.week)), site=row.site, sub_recipe_id=row.sub_recipe_id)
            ] += (row.quantity or 0)
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, f"Error while importing {StagedInventorySheet.sheet_name} data", skipped=True
            )
    updates = [
        {
            StagedInventoryModel.week: key.week,
            StagedInventoryModel.site: key.site,
            StagedInventoryModel.sub_recipe_id: key.sub_recipe_id,
            StagedInventoryModel.quantity: value,
        }
        for key, value in data.items()
    ]
    staged_inventory_repo.insert_staged_inventory(updates)
