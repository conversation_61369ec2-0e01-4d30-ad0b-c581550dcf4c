import logging
from collections.abc import Iterable
from typing import Any

from confluent_kafka import Message
from confluent_kafka.serialization import Message<PERSON><PERSON>, SerializationContext, StringDeserializer
from hellofresh.proto.service.ordering.purchase_order.v1beta1 import purchase_order_number_pb2 as po_num_pb2
from hellofresh.proto.stream.distribution_center.third_party.blujay.inbound.blujay_appointment.v1beta1 import (
    blujay_appointment_pb2 as appointment_pb2,
)
from hellofresh.proto.stream.distribution_center.third_party.blujay.inbound.purchase_order_shipment.v1beta1 import (
    purchase_order_shipment_pb2 as shipment_pb2,
)
from sqlalchemy import ColumnElement

from procurement.core.config_utils import killswitch
from procurement.core.exceptions.listener_errors import MessageUnprocessableException
from procurement.core.kafka import handlers as kafka_handlers
from procurement.core.typing import PO_NUMBER
from procurement.data.dto.ordering.shipment import Shipment
from procurement.data.models.ordering.shipment import AppointmentStateEnum, POShipmentModel
from procurement.managers import kafka_utils
from procurement.repository.ordering import shipment as shipment_repo

logger = logging.getLogger(__name__)


def update_blu_jay_shipment(value: shipment_pb2.PurchaseOrderShipment) -> None:
    if killswitch.blujay_filter_out_carrier and value.carrier_name == "CONNELL TRANSPORT":
        return

    location = value.origin_location
    shipment = {
        POShipmentModel.po_number: value.purchase_order_revision.formatted,
        POShipmentModel.load_number: value.load_number,
        POShipmentModel.pallet_count: value.pallet_count,
        POShipmentModel.carrier_name: value.carrier_name,
        POShipmentModel.execution_event: value.execution_event,
        POShipmentModel.region_code: location.region_code,
        POShipmentModel.postal_code: location.postal_code,
        POShipmentModel.administrative_area: location.administrative_area,
        POShipmentModel.locality: location.locality,
        POShipmentModel.address_lines: location.address_lines,
        POShipmentModel.organization: location.organization,
        POShipmentModel.appointment_time: kafka_utils.build_datetime(value.appointment_time),
    }

    shipment_repo.upsert_po_shipment(shipment)


class BluJayShipmentHandler(kafka_handlers.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(key, po_num_pb2.PurchaseOrderNumber, MessageField.KEY)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, shipment_pb2.PurchaseOrderShipment, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        update_blu_jay_shipment(msg.value())


def update_blu_jay_appointment(value: appointment_pb2.BluJayAppointment):
    po_number = value.purchase_order_revision.formatted
    try:
        state = AppointmentStateEnum(value.appointment_state)
    except ValueError as exc:
        logger.exception("Message can't be processed with po_number=%s because of", po_number)
        raise MessageUnprocessableException() from exc
    appointment = {
        POShipmentModel.po_number: po_number,
        POShipmentModel.appointment_time: kafka_utils.build_datetime(value.appointment_time),
        POShipmentModel.appointment_state: state.value,
        POShipmentModel.batch_sequence: value.batch_sequence,
    }
    upsert_blu_jay_appointment(appointment)


def upsert_blu_jay_appointment(appointment: dict[ColumnElement, Any]):
    batch_sequence = shipment_repo.get_shipment_by_po_number(appointment[POShipmentModel.po_number])
    if batch_sequence is None or batch_sequence >= appointment[POShipmentModel.batch_sequence]:
        return
    shipment_repo.upsert_appointment_time_in_po_shipment(appointment)


class BluJayAppointmentHandler(kafka_handlers.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handlers.parse_protobuf(value, appointment_pb2.BluJayAppointment, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        update_blu_jay_appointment(msg.value())


def get_shipments_by_po(po_numbers: Iterable[str]) -> dict[PO_NUMBER, Shipment]:
    return {item.po_number: item for item in shipment_repo.get_shipment_by_po_numbers(po_numbers)}
