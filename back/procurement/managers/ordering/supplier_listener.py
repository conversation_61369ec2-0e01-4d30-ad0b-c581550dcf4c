from datetime import datetime
from typing import Any

from confluent_kafka import Message
from confluent_kafka.schema_registry.avro import AvroDeserializer
from confluent_kafka.serialization import SerializationContext
from sqlalchemy import ColumnElement

from procurement.core.kafka import handlers
from procurement.core.kafka.handlers import Kafka<PERSON>andler
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers import kafka_utils
from procurement.managers.admin import dc_admin
from procurement.repository.ordering import supplier


class SupplierHandler(KafkaHandler):
    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return AvroDeserializer(
            schema_registry_client=handlers.get_schema_registry_client(), from_dict=self._from_dict
        )(value, ctx)

    def _process(self, msg: Message) -> None:
        if msg.value():
            supplier.upsert_supplier(msg.value())

    @staticmethod
    def _from_dict(item: dict, _) -> dict[ColumnElement, Any]:
        market = item["market"].upper()
        if market not in dc_admin.get_all_market_codes():
            return {}
        update_ts = kafka_utils.read_avro_datetime(item["updated_at"] or item["created_at"]) or datetime.now()
        return {
            SupplierModel.id: str(item["id"]),
            SupplierModel.code: item["code"],
            SupplierModel.market: market,
            SupplierModel.name: item["name"],
            SupplierModel.legal_name: item["legal_name"],
            SupplierModel.last_updated: update_ts,
        }
