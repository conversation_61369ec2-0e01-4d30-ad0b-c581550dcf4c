_SKUS = (
    "PCK-10-100021-1",
    "PCK-10-21196-1",
    "PCK-10-21628-1",
    "PCK-10-21629-1",
    "PCK-10-21197-1",
    "PCK-10-21668-1",
    "PCK-10-21669-1",
    "PCK-10-21192-1",
    "PCK-10-21193-1",
    "PCK-10-21591-1",
    "PCK-10-21592-1",
    "PCK-10-21207-1",
    "PCK-10-21208-1",
    "PCK-10-21194-1",
    "PCK-10-21195-1",
    "PCK-10-101179-1",
    "PCK-10-101188-1",
    "PCK-10-20181-1",
    "PCK-10-20182-1",
    "PCK-10-20183-1",
    "PCK-10-21130-1",
    "PCK-10-21666-1",
    "PCK-10-21667-1",
    "PCK-10-21147-1",
    "PCK-10-21148-1",
    "PCK-50-21534-1",
    "PCK-50-21535-1",
    "PCK-10-21209-1",
    "PCK-10-21210-1",
    "PCK-10-21606-1",
    "PCK-10-21607-1",
    "PCK-10-21103-1",
    "PCK-10-21104-1",
    "PCK-10-21782-1",
    "PCK-10-21783-1",
    "PCK-10-101173-1",
    "PCK-10-101174-1",
    "PCK-10-21241-1",
    "PCK-10-21242-1",
    "PCK-10-101028-1",
    "PCK-10-101029-1",
    "PCK-10-21118-1",
    "PCK-10-21131-1",
    "PCK-10-21670-1",
    "PCK-10-21671-1",
    "PCK-10-21630-1",
    "PCK-10-21631-1",
    "PCK-10-21149-1",
    "PCK-10-21150-1",
    "PCK-10-21624-1",
    "PCK-10-21625-1",
    "PCK-10-21205-1",
    "PCK-10-21206-1",
    "PCK-10-21112-1",
    "PCK-10-21113-1",
    "PCK-10-101187-1",
    "PCK-10-101189-1",
    "PCK-10-101175-1",
    "PCK-10-101176-1",
    "PCK-10-21243-1",
    "PCK-10-21244-1",
    "PCK-10-101030-1",
    "PCK-10-101031-1",
    "PCK-10-20146-7",
    "PCK-10-20055-7",
    "PCK-10-21271-1",
    "PCK-10-21126-1",
    "PCK-10-10915-1",
    "PCK-10-10916-1",
    "PCK-10-21127-1",
)
_BRANDS = ("HelloFresh", "EveryPlate", "Green Chef", "Factor US", "Factor")


BRANDS = {s: _BRANDS for s in _SKUS}
