from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from procurement.data.dto.inventory.grn import GrnPo


@dataclass
class GrnPurchaseOrder:
    po_number: str
    sku_code: str
    quantity: Decimal
    delivery_time_start: datetime
    supplier_code: str
    grn_receipts: list[GrnPo] | None = None

    @property
    def has_grn(self) -> bool:
        return bool(self.grn_receipts)

    @property
    def grn_units(self) -> Decimal:
        return sum(receipt.units_received for receipt in self.grn_receipts) if self.grn_receipts else Decimal()
