from typing import Any

from confluent_kafka import Message
from confluent_kafka.schema_registry.avro import AvroDeserializer
from confluent_kafka.serialization import SerializationContext
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.core.kafka import handlers
from procurement.core.kafka.handlers import KafkaHandler
from procurement.data.models.ordering.supplier_splits import SupplierSplitsModel
from procurement.managers.admin import dc_admin
from procurement.repository.ordering import supplier_splits


class SupplierSplitsHandler(KafkaHandler):
    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return AvroDeserializer(
            schema_registry_client=handlers.get_schema_registry_client(), from_dict=self._from_dict
        )(value, ctx)

    def _process(self, msg: Message) -> None:
        if msg.value():
            supplier_splits.upsert_supplier_splits(msg.value())

    @staticmethod
    def _from_dict(item: dict, _) -> list[dict[ColumnElement, Any]]:
        market = item["market"].upper()
        if market not in dc_admin.get_all_market_codes():
            return []
        updates = []
        sku_code = item["culinary_sku_code"]
        for splits_data in item["splits_data"]:
            dc = splits_data["dc"]
            for split in splits_data["splits"]:
                start_week = ScmWeek.from_str(split["range"]["start"]["planning_week"]).to_number_format()
                end_week = ScmWeek.from_str(split["range"]["end"]["planning_week"]).to_number_format()
                start_date = split["range"]["start"]["date"]
                end_date = split["range"]["end"]["date"]
                for supplier_data in split["suppliers_data"]:
                    updates.append(
                        {
                            SupplierSplitsModel.dc: dc,
                            SupplierSplitsModel.sku_code: sku_code,
                            SupplierSplitsModel.lead_time: supplier_data["lead_time"],
                            SupplierSplitsModel.min_order_quantity: supplier_data["min_order_quantity"],
                            SupplierSplitsModel.incremental_order_quantity: supplier_data["incremental_order_quantity"],
                            SupplierSplitsModel.start_week: start_week,
                            SupplierSplitsModel.end_week: end_week,
                            SupplierSplitsModel.start_date: start_date,
                            SupplierSplitsModel.end_date: end_date,
                            SupplierSplitsModel.market: market,
                        }
                    )
        return updates


def get_moq_ioq_values(sku_codes: list[str], bob_codes: set[str]):
    return supplier_splits.get_moq_ioq_values(sku_codes=sku_codes, bob_codes=bob_codes)
