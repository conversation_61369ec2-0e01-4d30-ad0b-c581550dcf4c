import dataclasses
import logging
from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from confluent_kafka import Message
from confluent_kafka.serialization import Message<PERSON><PERSON>, SerializationContext, StringDeserializer
from hellofresh.proto.shared.v1.brand_pb2 import BRAND_FACTOR75 as BRAND_FACTOR
from hellofresh.proto.stream.rte.manufactured_sku.v4.manufactured_sku_pb2 import ManufacturedSku as ManufacturedSkuPb

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.kafka import handlers as kafka_handler
from procurement.core.kafka.handlers import KafkaHandler
from procurement.core.typing import MANUFACTURED_CODE, MANUFACTURED_UUID, SKU_CODE, SKU_UUID
from procurement.data.dto.inventory.ingredient import PkgData
from procurement.data.dto.ordering.manufactured_skus import ManufacturedSku, ManufacturedSkuPart
from procurement.data.dto.sku import SkuDetail
from procurement.data.models.ordering.manufactured_sku import (
    ManufacturedSkuModel,
    ManufacturedSkuPartModel,
    ManufacturedSkuWeekModel,
)
from procurement.managers.sku import culinary_sku
from procurement.repository.ordering import manufactured_sku as manufactured_sku_repo

logger = logging.getLogger(__name__)


@dataclasses.dataclass(slots=True)
class PartValue:
    unit_of_measure: UnitOfMeasure
    quantity: Decimal
    is_manufactured: bool


class ManufacturedSkuHandler(KafkaHandler):
    def __init__(self):
        super().__init__()

    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handler.parse_protobuf(value, ManufacturedSkuPb, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        value: ManufacturedSkuPb = msg.value()

        manufactured_sku_repo.upsert_manufactured_sku(
            {
                ManufacturedSkuModel.manufactured_uuid: value.manufactured_sku_id,
                ManufacturedSkuModel.manufactured_code: value.sku_code,
                ManufacturedSkuModel.manufactured_name: value.name,
                ManufacturedSkuModel.market: value.market.upper(),
            }
        )
        parts = {}
        for part in value.bill_of_materials:
            quantity, unit_of_measure = _extract_quantity_and_uom(part, UnitOfMeasure.POUND)
            if part.sku_id not in parts:
                parts[part.sku_id] = PartValue(unit_of_measure, quantity, part.is_manufactured.value)
            else:
                parts[part.sku_id].quantity += quantity
        if parts:
            manufactured_sku_repo.upsert_manufactured_sku_parts(
                [
                    {
                        ManufacturedSkuPartModel.root_uuid: value.manufactured_sku_id,
                        ManufacturedSkuPartModel.part_uuid: sku_id,
                        ManufacturedSkuPartModel.unit_of_measure: str(part.unit_of_measure),
                        ManufacturedSkuPartModel.quantity: part.quantity,
                        ManufacturedSkuPartModel.is_manufactured: part.is_manufactured,
                    }
                    for sku_id, part in parts.items()
                ]
            )
        if not killswitch.kafka_factor_pkg_consumption_enabled or BRAND_FACTOR not in value.brands:
            return
        weeks = (
            {ScmWeek.from_str(menu_week.menu_week) for menu_week in value.menu_weeks if not menu_week.is_locked.value}
            if value.menu_weeks
            else None
        )
        if weeks:
            updates = [
                {
                    ManufacturedSkuWeekModel.manufactured_uuid: value.manufactured_sku_id,
                    ManufacturedSkuWeekModel.week: int(week),
                }
                for week in weeks
            ]
            manufactured_sku_repo.update_manufactured_sku_weeks(value.manufactured_sku_id, updates=updates)
        else:
            manufactured_sku_repo.delete_manufactured_sku_weeks(value.manufactured_sku_id)


def _extract_quantity_and_uom(
    part: ManufacturedSkuPb.BillOfMaterialItem, expected_uom: UnitOfMeasure
) -> tuple[Decimal | None, UnitOfMeasure | None]:
    for quantity_item in part.quantity:
        if (uom := UnitOfMeasure.inexact_value_of(quantity_item.unit_of_measure)) == expected_uom:
            return quantity_item.quantity, uom
    return None, None


def get_manufactured_skus_uuid_by_code() -> dict[MANUFACTURED_CODE, MANUFACTURED_UUID]:
    return {item.manufactured_code: item.manufactured_uuid for item in manufactured_sku_repo.get_manufactured_skus()}


def get_manufactured_skus_by_uuid() -> dict[MANUFACTURED_UUID, ManufacturedSku]:
    return {item.manufactured_uuid: item for item in manufactured_sku_repo.get_manufactured_skus()}


def get_manufactured_skus_name_by_code() -> dict[str, str]:
    return {
        item.manufactured_code: item.manufactured_name
        for item in manufactured_sku_repo.get_manufactured_skus_name_code()
    }


def get_manufactured_sku_parts() -> dict[MANUFACTURED_UUID, list[ManufacturedSkuPart]]:
    return utils.group_by(manufactured_sku_repo.get_manufactured_sku_parts(), lambda item: item.root_uuid)


@dataclasses.dataclass
class SkuBreakdownDetail:
    sku_code: str
    sku_name: str
    sku_id: int
    purchasing_category: str
    unit_of_measure: UnitOfMeasure
    sub_recipe_code: str
    sub_recipe_name: str
    quantity: Decimal


class ManufacturedSkuBreakdown:
    def __init__(self):
        self.known_breakdowns = defaultdict(lambda: defaultdict(Decimal))
        self.detailed_breakdowns = defaultdict(dict)

    @cached_property
    def manufactured_sku_uuid_by_code(self) -> dict[MANUFACTURED_CODE, MANUFACTURED_UUID]:
        return get_manufactured_skus_uuid_by_code()

    @cached_property
    def manufactured_skus_by_uuid(self) -> dict[MANUFACTURED_UUID, ManufacturedSku]:
        return get_manufactured_skus_by_uuid()

    @cached_property
    def manufactured_sku_parts(self) -> dict[MANUFACTURED_UUID, list[ManufacturedSkuPart]]:
        return get_manufactured_sku_parts()

    @cached_property
    def sku_details_by_uuid(self) -> dict[SKU_UUID, SkuDetail]:
        return culinary_sku.get_sku_details_by_uuid()

    def _sync_detailed_breakdown_with_aggregated_quantity(self, manufactured_uuid: str) -> None:
        for sku_code, quantity in self.known_breakdowns[manufactured_uuid].items():
            self.detailed_breakdowns[manufactured_uuid][sku_code].quantity = quantity

    def get_breakdown(self, manufactured_code: str) -> dict[SKU_CODE, Decimal]:
        manufactured_uuid = self.manufactured_sku_uuid_by_code.get(manufactured_code)
        if manufactured_uuid not in self.known_breakdowns:
            self._get_breakdown_inner(manufactured_uuid, manufactured_uuid, Decimal("1"))
        return self.known_breakdowns[manufactured_uuid]

    def get_detailed_breakdown(self, manufactured_code: str) -> dict[SKU_CODE, SkuBreakdownDetail]:
        manufactured_uuid = self.manufactured_sku_uuid_by_code.get(manufactured_code)
        if manufactured_uuid not in self.known_breakdowns:
            self._get_breakdown_inner(manufactured_uuid, manufactured_uuid, Decimal("1"), detailed=True)
        self._sync_detailed_breakdown_with_aggregated_quantity(manufactured_uuid)
        return self.detailed_breakdowns[manufactured_uuid]

    def _get_breakdown_inner(
        self, manufactured_uuid: str, parent_uuid: str, parent_quantity: Decimal, detailed: bool = False
    ) -> None:
        for part in self.manufactured_sku_parts.get(parent_uuid, []):
            quantity = part.quantity * parent_quantity
            if part.is_manufactured:
                self._get_breakdown_inner(manufactured_uuid, part.part_uuid, quantity, detailed)
            else:
                sku = self.sku_details_by_uuid[part.part_uuid]
                self.known_breakdowns[manufactured_uuid][sku.sku_code] += quantity
                if detailed:
                    parent_details = self.manufactured_skus_by_uuid.get(parent_uuid)
                    if parent_details and sku.sku_code not in self.detailed_breakdowns[manufactured_uuid]:
                        breakdown_detail = SkuBreakdownDetail(
                            sku_code=sku.sku_code,
                            sku_name=sku.sku_name,
                            sku_id=sku.sku_id,
                            purchasing_category=sku.purchasing_category,
                            unit_of_measure=part.unit_of_measure,
                            sub_recipe_code=parent_details.manufactured_code,
                            sub_recipe_name=parent_details.manufactured_name,
                            quantity=Decimal("0"),  # default
                        )
                        self.detailed_breakdowns[manufactured_uuid][sku.sku_code] = breakdown_detail


def load_factor_pkg_data(week: ScmWeek) -> list[PkgData]:
    manufactured_skus = manufactured_sku_repo.get_manufactured_recipe_skus_by_week(week)
    manufactured_sku_breakdown = ManufacturedSkuBreakdown()
    pkg_data = []
    for item in manufactured_skus:
        detailed_breakdowns_by_sku = manufactured_sku_breakdown.get_detailed_breakdown(item.manufactured_code)
        for sku_code, detailed_breakdown in detailed_breakdowns_by_sku.items():
            pkg_data.append(
                PkgData(
                    sku_code=sku_code,
                    weight_amount=detailed_breakdown.quantity,
                    weight_unit=str(detailed_breakdown.unit_of_measure),
                    sku_id=detailed_breakdown.sku_id,
                    sku_name=detailed_breakdown.sku_name,
                    code=detailed_breakdown.sub_recipe_code,
                    slot=item.manufactured_code,
                    meal_name=item.manufactured_name,
                    purcasing_category=detailed_breakdown.purchasing_category,
                    sub_recipe=detailed_breakdown.sub_recipe_name,
                    storage_location=None,
                    allergens=None,
                    picks_2p=None,
                    picks_3p=None,
                    picks_4p=None,
                    picks_6p=None,
                )
            )
    return pkg_data
