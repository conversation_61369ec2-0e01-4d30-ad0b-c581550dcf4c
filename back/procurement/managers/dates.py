from datetime import date, timedelta

from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek, ScmWeekConfig


class WeekByDateFinder:
    def __init__(
        self,
        weeks: list[ScmWeek],
        week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG,
        offset: int = 0,
        extend_first_week_to_left: bool = False,
    ):
        sorted_weeks = sorted(weeks)
        self.extend_first_week_to_left = extend_first_week_to_left
        self.first_week = sorted_weeks[0]
        self.first_day_of_first_week = self.first_week.get_first_day(week_config)
        offset_timedelta = timedelta(days=offset)
        self.week_index = {
            (day + offset_timedelta): week for week in sorted_weeks for day in week.week_days(week_config)
        }

    def get_week(self, date_to_check: date | None) -> ScmWeek | None:
        if not date_to_check:
            return None
        if date_to_check < self.first_day_of_first_week:
            return self.first_week if self.extend_first_week_to_left else None
        return self.week_index.get(date_to_check)
