import abc
from enum import StrEnum

from procurement.constants.ordering import RECEIVED_STATUSES
from procurement.managers.imt.purchase_order.po_status import PoStatusResult
from procurement.managers.pimt.purchase_order import PoStatusData


class PoFilter(StrEnum):
    ALL_POS = "allPos"
    RECEIVED_POS = "receivedPos"
    ALL_POS_WITH_TRANSFERS = "allPosWithTransfers"


class PoFilterBase(abc.ABC):
    @abc.abstractmethod
    def apply(self, items: list[PoStatusResult | PoStatusData]): ...


class AllPosFilter(PoFilterBase):
    def apply(self, items: list[PoStatusResult | PoStatusData]):
        # Excluding TOs for this filter
        return [it for it in items if not it.transfer_source_bob_code]


class AllPosWithTransfersFilter(AllPosFilter):
    pass


class ReceivedPosFilter(PoFilterBase):
    def apply(self, items: list[PoStatusResult | PoStatusData]):
        return list(filter(lambda item: item.po_status in RECEIVED_STATUSES, items))


class PoFilterManager:
    po_filter_mapping: dict[str, PoFilterBase] = {
        PoFilter.ALL_POS.value: AllPosFilter(),
        PoFilter.RECEIVED_POS.value: ReceivedPosFilter(),
        PoFilter.ALL_POS_WITH_TRANSFERS.value: AllPosWithTransfersFilter(),
    }

    @staticmethod
    def get_filter(po_filter: str) -> PoFilterBase:
        return PoFilterManager.po_filter_mapping.get(po_filter, AllPosFilter())
