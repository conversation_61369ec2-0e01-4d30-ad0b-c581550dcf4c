from __future__ import annotations

import dataclasses
from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.managers.depletion.result import IngredientDepletionResult

from .base import BaseExportModel


@dataclasses.dataclass
class IngredientDepletionExportData(BaseExportModel):
    brand: str
    week: ScmWeek
    site: str
    sku_code: str
    sku_name: str
    category: str
    buyer: str
    commodity_group: str
    impacted_recipes: str
    plan: int
    forecast_oscar: Decimal
    delta: float
    supplement_need: str
    critical_delivery: str
    units_needed: Decimal
    units_ordered: Decimal
    units_received: Decimal
    units_in_house_hj: Decimal
    row_need: int
    previous_week_row_need: int
    units_in_house_minus_row_need: Decimal
    next_week_forecast: Decimal
    units_in_house_min_row_need_min_forecast: Decimal
    units_scheduled_to_be_produced_by_autobagger: int
    inventory: int
    discards: int
    donations: int
    pulls: int
    total_on_hand: int
    total_on_hand_minus_production_needs: Decimal
    in_progress_hj: Decimal
    not_delivered: Decimal
    buffer_quantity: Decimal
    buffer_percent: Decimal | None
    awaiting_delivery: Decimal
    bulk_units_ordered: Decimal
    bulk_units_received: Decimal
    bulk_delta: Decimal
    bulk_units_in_hj: Decimal
    unit_of_measure: UnitOfMeasure

    @staticmethod
    def from_dashboard_item(item: IngredientDepletionResult, context: None) -> IngredientDepletionExportData:
        weekly = item.weekly_overview
        bulk_data = item.bulk_values
        return IngredientDepletionExportData(
            brand=", ".join(sorted(item.brands)),
            week=item.week,
            site=", ".join(sorted(item.sites)),
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            category=item.category,
            commodity_group=item.commodity_group,
            buyer=item.buyer,
            impacted_recipes=", ".join(sorted(item.impacted_recipes)),
            plan=item.plan,
            forecast_oscar=item.forecast,
            delta=item.delta,
            supplement_need=item.supplement_need,
            critical_delivery=item.critical_delivery_status,
            units_needed=weekly.units_needed,
            units_ordered=weekly.units_ordered,
            units_received=weekly.units_received,
            units_in_house_hj=weekly.hj_units_in_house,
            row_need=weekly.row_need,
            previous_week_row_need=weekly.prev_week_row_need,
            units_in_house_minus_row_need=weekly.units_in_house_minus_row_need,
            next_week_forecast=weekly.next_week_forecast,
            units_in_house_min_row_need_min_forecast=weekly.units_in_house_minus_row_need_minus_forecast,
            units_scheduled_to_be_produced_by_autobagger=weekly.units_to_produce_by_autobagger,
            inventory=weekly.inventory,
            discards=weekly.discards,
            donations=weekly.donations,
            pulls=weekly.pulls,
            total_on_hand=weekly.total_on_hand,
            total_on_hand_minus_production_needs=weekly.total_on_hand_minus_prod_needs,
            in_progress_hj=weekly.in_progress_hj,
            not_delivered=weekly.not_delivered,
            buffer_quantity=weekly.buffer_quantity,
            buffer_percent=weekly.buffer_percent,
            awaiting_delivery=weekly.awaiting_delivery,
            bulk_units_ordered=bulk_data.bulk_units_ordered,
            bulk_units_received=bulk_data.bulk_units_received,
            bulk_delta=bulk_data.delta,
            bulk_units_in_hj=bulk_data.bulk_units_in_hj,
            unit_of_measure=item.unit_of_measure,
        )
