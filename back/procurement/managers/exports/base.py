from __future__ import annotations

import abc
import dataclasses
from collections.abc import Iterable
from typing import ClassVar, Type, TypeVar

from procurement.data.googlesheet_model import core as gsheet_core
from procurement.data.googlesheet_model.core import SheetModel

InputModel = TypeVar("InputModel")
ExportContext = TypeVar("ExportContext")


@dataclasses.dataclass
class BaseExportModel(abc.ABC):
    @staticmethod
    @abc.abstractmethod
    def from_dashboard_item(item: InputModel, context: ExportContext | None) -> BaseExportModel: ...


class BaseExport(abc.ABC):
    gsheet_model: SheetModel
    result_model: ClassVar[Type[BaseExportModel]]
    gsheet_id: str

    def _get_export_data(self) -> Iterable[BaseExportModel]:
        return self._order_data(self._get_data())

    def write_to_sheet(self) -> None:
        gsheet_core.write_to_sheet(
            self.gsheet_model, [dataclasses.asdict(item) for item in self._get_export_data()], self.gsheet_id
        )

    @abc.abstractmethod
    def _get_data(self) -> list[BaseExportModel]: ...

    @staticmethod
    def _order_data(items: Iterable[BaseExportModel]) -> Iterable[BaseExportModel]:
        return items
