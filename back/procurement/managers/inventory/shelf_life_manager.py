import json
from typing import Any

from confluent_kafka import Message
from confluent_kafka.schema_registry.avro import AvroDeserializer
from confluent_kafka.serialization import SerializationContext, StringDeserializer
from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import MARKET_CA, MARKET_US
from procurement.core.kafka import handlers as kafka_handler
from procurement.data.dto.inventory.shelf_life import ShelfLife
from procurement.data.models.inventory.shelf_life import ShelfLifeModel
from procurement.repository.inventory import shelf_life as shelf_life_repo


class ShelfLifeHandler(kafka_handler.KafkaHandler):
    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return StringDeserializer()(key, ctx)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        deserializer = AvroDeserializer(
            schema_registry_client=kafka_handler.get_schema_registry_client(),
            from_dict=self._from_dict,
        )
        return deserializer(value, ctx)

    def _process(self, msg: Message) -> None:
        if msg.value():
            shelf_life_repo.upsert_shelf_life(msg.value())

    @staticmethod
    def _from_dict(item: dict, _) -> dict[ColumnElement, Any]:
        if item["market"].upper() not in {MARKET_US, MARKET_CA}:
            return {}
        extras = json.loads(item.get("extras", "{}"))
        shelf_life = extras.get("min_shelf_life_on_deliver")
        return {
            ShelfLifeModel.sku_uuid: item["culinary_sku_id"],
            ShelfLifeModel.shelf_life: max(shelf_life, 0),
        }


def get_shelf_life_values(sku_codes: list[str]) -> list[ShelfLife]:
    return shelf_life_repo.get_shelf_life(sku_codes)
