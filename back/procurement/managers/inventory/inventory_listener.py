import dataclasses
import itertools
import logging
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from decimal import Decimal
from typing import NamedTuple
from uuid import UUID

from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext
from hellofresh.proto.stream.distribution_center.inventory.snapshot.v1.inventory_snapshot_pb2 import (
    InventorySnapshotKey,
    InventorySnapshotValue,
)

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.constants.hellofresh_constant import InventoryState as UnifiedInvState
from procurement.constants.protobuf import InventoryLocationType, InventoryState
from procurement.core import utils
from procurement.core.kafka import handlers as kafka_handler
from procurement.core.kafka.handlers import KafkaHand<PERSON>
from procurement.core.typing import WH_CODE
from procurement.core.write_buffer import WriteBuffer
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.data.models.inventory.inventory_snapshot import InventorySnapshotModel
from procurement.managers import kafka_utils
from procurement.repository.inventory import inventory, inventory_snapshot
from procurement.services.database import app_db

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class SnapshotInput:
    msg_ts: datetime
    wh_code: str
    sku_code: str
    snapshot_ts: datetime
    snapshot_id: UUID
    expiration_date: date
    location_type: InventoryLocationType
    location_id: str
    state: InventoryState
    inventory_type: InventoryInputType
    unit_quantity: Decimal
    case_size: int

    po_number: str
    lot_code: str

    def key(self) -> tuple:
        return (
            self.wh_code,
            self.sku_code,
            self.snapshot_ts,
            self.expiration_date,
            self.location_type,
            self.location_id,
            self.state,
            self.inventory_type,
        )

    def required_fields(self) -> tuple:
        return (
            self.wh_code,
            self.sku_code,
            self.snapshot_ts,
            self.location_type,
            self.location_id,
            self.state,
            self.inventory_type,
        )


class InventoryHandler(KafkaHandler):
    _WMS_TYPE_MAPPING = {
        b"HJ": InventoryInputType.HJ,
        b"WMSL": InventoryInputType.WMSL,
        b"E2Open": InventoryInputType.E2OPEN,
    }

    def __init__(self):
        super().__init__()
        self._buffer = WriteBuffer[SnapshotInput]("inv_snapshot", sink=_save_records, size=50000)

    def deserialize_key(self, key: bytes, ctx: SerializationContext):
        return kafka_handler.parse_protobuf(key, InventorySnapshotKey, MessageField.KEY)

    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return kafka_handler.parse_protobuf(value, InventorySnapshotValue, MessageField.VALUE)

    def _is_header_accepted(self, msg: Message) -> bool:
        headers = kafka_utils.read_headers(msg)
        return headers.get("wmsName") in self._WMS_TYPE_MAPPING

    def _is_key_accepted(self, msg: Message) -> bool:
        key = msg.key()
        wh_code = kafka_utils.get_wh_by_bob_code().get(key.distribution_center_code)
        if not wh_code:
            known_bob_codes = kafka_utils.get_site_by_bob_code_map()
            dc = known_bob_codes.get(key.distribution_center_code)
            if not dc or not dc.enabled:
                return False
        return True

    def close(self) -> None:
        super().close()
        self._buffer.flush(empty=True)

    def _process(self, msg: Message) -> None:
        value: InventorySnapshotValue = msg.value()
        snapshot = value.inventory_snapshot
        headers = kafka_utils.read_headers(msg)

        if "Hold" in snapshot.location_id:
            return
        # TODO: store warehouse inventory with bob codes as well
        wh_code = kafka_utils.get_wh_by_bob_code().get(value.distribution_center_code)
        if not wh_code:
            wh_code = value.distribution_center_code
        snapshot_ts = kafka_utils.build_timestamp(value.snapshot_time)
        state = InventoryState(value.stock_state)

        # unavailable expiration date is sent as '0001-01-01' or min unix timestamp
        expiration_time = kafka_utils.build_timestamp(value.expiration_time)
        expiration_date = expiration_time.date() if expiration_time.year > 1970 else None

        location = InventoryLocationType(snapshot.location_type)
        self._buffer.write(
            SnapshotInput(
                msg_ts=kafka_utils.get_message_timestamp(msg),
                wh_code=wh_code,
                sku_code=value.sku_code,
                po_number=value.purchase_order_revision.formatted if value.purchase_order_revision else None,
                snapshot_ts=snapshot_ts,
                snapshot_id=UUID(snapshot.snapshot_id),
                expiration_date=expiration_date,
                location_type=location,
                location_id=snapshot.location_id,
                state=state,
                inventory_type=self._WMS_TYPE_MAPPING[headers["wmsName"]],
                unit_quantity=Decimal(snapshot.quantity.value),
                case_size=snapshot.case_size,
                lot_code=value.lot_code,
            )
        )


def _save_records(records: list[SnapshotInput]) -> None:
    snapshots = []
    unified_snapshot = []
    for item in records:
        if item.inventory_type == InventoryInputType.E2OPEN:
            unified_snapshot.append(item)
        else:
            snapshots.append(item)
    if snapshots:
        _save_snapshots(snapshots)
    if unified_snapshot:
        _save_unified_snapshot(unified_snapshot)


def _save_snapshots(snapshots: list[SnapshotInput]) -> None:
    updates = _merge_snapshots(snapshots)
    # in case of re-consumption two adjacent snapshots might be in the same batch resulting in more than 1 timestamp
    snapshot_times = {}
    for update in updates:
        key = (update.wh_code, update.inventory_type)
        snapshot_times[key] = utils.max_of_two(snapshot_times.get(key), update.snapshot_ts)
    with app_db.transaction() as trns:
        inventory_snapshot.delete_old_snapshot(new_snapshot_ts_by_wh=snapshot_times, transaction=trns)
        inventory_snapshot.upsert_snapshots(
            snapshots=[
                {
                    InventorySnapshotModel.wh_code: s.wh_code,
                    InventorySnapshotModel.sku_code: s.sku_code,
                    InventorySnapshotModel.snapshot_ts: s.snapshot_ts,
                    InventorySnapshotModel.expiration_date: s.expiration_date,
                    InventorySnapshotModel.location_type: s.location_type.name,
                    InventorySnapshotModel.location_id: s.location_id,
                    InventorySnapshotModel.state: s.state.name,
                    InventorySnapshotModel.inventory_type: s.inventory_type,
                    InventorySnapshotModel.unit_quantity: s.unit_quantity,
                }
                for s in updates
            ],
            transaction=trns,
        )
    logger.info("Consumed WMSL/HJ records: %s", len(snapshots))
    logger.info("Consumed WMSL/HJ inventory timestamps: %s", snapshot_times)


def _merge_snapshots(snapshots: list[SnapshotInput]) -> Iterable[SnapshotInput]:
    updates = {}
    for item in snapshots:
        key = item.key()
        if None in item.required_fields() or item.unit_quantity is None:
            logger.warning("Invalid item consumed -- %s", item)
            continue
        existing = updates.get(key)
        if existing:
            existing.unit_quantity += item.unit_quantity
        else:
            updates[key] = item
    return updates.values()


class InventoryKey(NamedTuple):
    sku_code: str
    po_number: str
    wh_code: str
    lot_code: str
    inventory_status: UnifiedInvState
    expiration_date: date
    snapshot_timestamp: datetime
    snapshot_id: UUID


@dataclasses.dataclass
class InventoryValue:
    case_quantity: int = 0
    unit_quantity: int = 0


def _save_unified_snapshot(unified_snapshot: list[SnapshotInput]) -> None:
    agg_inventory, totals_count = _merge_e2open_snapshot(unified_snapshot)
    snapshots_by_id: dict[tuple[date, WH_CODE], dict[UUID, list[tuple[InventoryKey, InventoryValue]]]] = defaultdict(
        lambda: defaultdict(list)
    )
    for key, val in agg_inventory.items():
        snapshots_by_id[(key.snapshot_timestamp.date(), key.wh_code)][key.snapshot_id].append((key, val))
    latest_snapshots: dict[tuple[date, WH_CODE], list[tuple[InventoryKey, InventoryValue]]] = {
        (snapshot_date, wh_code): snapshot_ids[next(reversed(snapshot_ids))]
        for (snapshot_date, wh_code), snapshot_ids in snapshots_by_id.items()
    }

    for (snapshot_date, wh_code), snapshots in latest_snapshots.items():
        inventory.delete_inventory_by_warehouse_snapshot_id(
            wh_codes=[wh_code],
            inventory_type=InventoryInputType.E2OPEN,
            snapshot_date=snapshot_date,
            # snapshot id from any item's InventoryKey value
            snapshot_id=snapshots[0][0].snapshot_id,
        )

    update_ts = datetime.now()
    inventory.upsert_inventory(
        [
            {
                UnifiedInventoryModel.po_number: key.po_number,
                UnifiedInventoryModel.sku_code: key.sku_code,
                UnifiedInventoryModel.order_number: utils.get_order_number_from_po_number(key.po_number),
                UnifiedInventoryModel.case_quantity: value.case_quantity,
                UnifiedInventoryModel.unit_quantity: value.unit_quantity or None,
                UnifiedInventoryModel.lot_code: key.lot_code,
                UnifiedInventoryModel.wh_code: key.wh_code,
                UnifiedInventoryModel.expiration_date: key.expiration_date,
                UnifiedInventoryModel.inventory_type: InventoryInputType.E2OPEN,
                UnifiedInventoryModel.inventory_status: key.inventory_status,
                UnifiedInventoryModel.snapshot_timestamp: key.snapshot_timestamp,
                UnifiedInventoryModel.snapshot_id: key.snapshot_id,
                UnifiedInventoryModel.imt_update_ts: update_ts,
            }
            for key, value in itertools.chain.from_iterable(latest_snapshots.values())
        ]
    )
    logger.info("Consumed E2OPEN records: %s (%s TOTAL records ignored)", len(unified_snapshot), totals_count)
    logger.info("Consumed inventory timestamps: %s", [(key, list(items)) for key, items in snapshots_by_id.items()])


def _merge_e2open_snapshot(snapshot: list[SnapshotInput]) -> tuple[dict[InventoryKey, InventoryValue], int]:
    agg_snapshot: dict[InventoryKey, InventoryValue] = defaultdict(InventoryValue)
    totals_count = 0

    for item in snapshot:
        if item.state == InventoryState.TOTAL:
            totals_count += 1
            continue
        key = InventoryKey(
            sku_code=item.sku_code,
            po_number=item.po_number,
            wh_code=item.wh_code,
            lot_code=item.lot_code,
            inventory_status=item.state.to_unified_state(),
            expiration_date=item.expiration_date,
            snapshot_timestamp=item.snapshot_ts,
            snapshot_id=item.snapshot_id,
        )
        # In case of E2Open unit_quantity (message.quantity) = cases
        agg_item = agg_snapshot[key]
        agg_item.case_quantity += item.unit_quantity
        agg_item.unit_quantity += item.unit_quantity * item.case_size

    return agg_snapshot, totals_count
