import dataclasses
import itertools
from collections.abc import Iterable
from datetime import date, datetime, time
from functools import cached_property
from typing import NamedTuple

from procurement.constants.hellofresh_constant import InventoryInputType, InventoryState
from procurement.constants.protobuf import InventoryState as PbInventoryState
from procurement.core import utils
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, HJ_NAME, SKU_CODE, UNITS, WH_CODE
from procurement.data.dto.inventory.unified_inventory import UnifiedInventoryDto
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.dto.sku import SkuMeta
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.ordering import manufactured_sku
from procurement.managers.pimt import partners
from procurement.managers.sku import culinary_sku
from procurement.repository.highjump import wip as wip_inventory
from procurement.repository.inventory import cycle_counts, inventory, inventory_snapshot


class UnifiedInventoryItem(NamedTuple):
    brands: set[str] | None
    source: InventoryInputType
    bob_code: BOB_CODE
    site_name: str
    po_number: str | None
    sku_code: str
    sku_name: str | None
    category: str | None
    units: UNITS
    status: InventoryState
    lot_code: str
    snapshot_timestamp: datetime
    location_id: str | None
    expiration_date: date | None
    supplier: str | None


@dataclasses.dataclass(slots=True)
class _InventorySelectors:
    """
    Inventory selecting filter values for each inventory source table
    """

    unified_selector: dict[BOB_CODE, InventoryInputType] = dataclasses.field(default_factory=dict)
    cycle_counts_selector: list[DcConfig] = dataclasses.field(default_factory=list)
    wmsl_selector: list[BOB_CODE] = dataclasses.field(default_factory=list)
    hj_wh_ids: list[HJ_NAME] = dataclasses.field(default_factory=list)


class UnifiedInventoryManager:

    def __init__(self, *, sku_codes: Iterable[str] | None = None, bob_codes: Iterable[str] | None = None):
        """
        If bob_codes is omitted or contains multi-brand DCs, their shared inventory for such DCs will be under
        corresponding multi-brand DC code instead of a bob code or warehouse code
        """
        self.market = context.get_request_context().market
        self.sku_codes = sku_codes
        self.bob_codes = None
        if bob_codes:
            self.bob_codes = set(bob_codes)
            all_multibrand_sites = utils.group_by(
                filter(lambda s: s.multi_brand_dc, self._all_sites.values()), lambda it: it.multi_brand_dc
            )
            # extend bob codes with any other related multibrand bob_code to pull latest available inventory
            for sites in all_multibrand_sites.values():
                if any(s.bob_code in self.bob_codes for s in sites):
                    self.bob_codes.update(s.bob_code for s in sites)

    def get_live_inventory(self, wip_inventory_included: bool = False) -> list[UnifiedInventoryDto]:
        inv = [
            inv
            for inv in itertools.chain(
                self._get_unified_inventory(), self._get_cycle_counts_inventory(), self._get_wmsl_inventory()
            )
            if inv.units
        ]
        if wip_inventory_included:
            inv.extend(self._get_wip_inventory())
        return inv

    def get_rich_live_inventory(self, wip_inventory_included: bool = False) -> list[UnifiedInventoryItem]:
        inv = self.get_live_inventory(wip_inventory_included=wip_inventory_included)
        sku_codes = frozenset(it.sku_code for it in inv)
        sku_meta = self._get_sku_meta(sku_codes)
        return [self._enrich_inventory_dto(it, sku_meta.get(it.sku_code)) for it in inv]

    def _get_unified_inventory(self) -> list[UnifiedInventoryDto]:
        if not self._selectors.unified_selector:
            return []
        return inventory.get_unified_inventory(
            self._selectors.unified_selector,
            sku_codes=self.sku_codes,
            multibrand_wh_code_mapping=self._multibrand_wh_code_mapping,
        )

    def _get_cycle_counts_inventory(self) -> list[UnifiedInventoryDto]:
        res = []
        for dc in self._selectors.cycle_counts_selector:
            res.extend(
                UnifiedInventoryDto(
                    source=InventoryInputType.CYCLE_COUNT,
                    bob_code=dc.bob_code,
                    sku_code=it.sku_code,
                    units=it.quantity,
                    status=InventoryState.AVAILABLE,
                    snapshot_timestamp=(
                        datetime.combine(it.snapshot_timestamp, time()) if it.snapshot_timestamp else None
                    ),
                    lot_code=None,
                    location_id=None,
                    expiration_date=it.expiration_date,
                    supplier=None,
                    po_number=None,
                )
                for it in cycle_counts.get_current_cycle_count_inventory(
                    brand=dc.brand, site=dc.sheet_name, sku_codes=self.sku_codes
                )
            )
        return res

    def _get_wmsl_inventory(self) -> list[UnifiedInventoryDto]:
        if not self._selectors.wmsl_selector:
            return []
        return [
            UnifiedInventoryDto(
                source=InventoryInputType.WMSL,
                bob_code=it.bob_code,
                sku_code=it.sku_code,
                units=it.units,
                status=PbInventoryState[it.state].to_unified_state(),
                snapshot_timestamp=datetime.combine(it.snapshot_date, time()) if it.snapshot_date else None,
                lot_code=None,
                location_id=it.location_id,
                expiration_date=it.expiration_date,
                supplier=None,
                po_number=None,
            )
            for it in inventory_snapshot.get_latest_snapshots(
                bob_codes=self._selectors.wmsl_selector, sku_codes=self.sku_codes
            )
        ]

    def _get_wip_inventory(self) -> list[UnifiedInventoryDto]:
        bob_code_by_site_hj_name_mapping = {site.high_jump_name: site.bob_code for site in self._all_sites.values()}
        today = datetime.today()
        return [
            UnifiedInventoryDto(
                source=InventoryInputType.HJ,
                snapshot_timestamp=today,
                bob_code=bob_code_by_site_hj_name_mapping.get(it.wh_id),
                po_number="",
                sku_code=it.sku_code,
                units=it.quantity,
                status=InventoryState.WIP,
                lot_code=None,
                location_id=None,
                expiration_date=None,
                supplier=None,
            )
            for it in wip_inventory.get_wip_inventory(self._selectors.hj_wh_ids)
        ]

    def _enrich_inventory_dto(self, dto: UnifiedInventoryDto, sku_meta: SkuMeta | None) -> UnifiedInventoryItem:
        if wh := self.all_warehouses.get(dto.bob_code):
            dc_name = wh.name
        else:
            dc = self._multibrand_primary_site.get(dto.bob_code) or self._all_sites.get(dto.bob_code)
            dc_name = dc.site_name if dc else ""
        sku_name = sku_meta.sku_name if sku_meta else None
        if sku_name is None:
            transformed_sku_code = dto.sku_code.replace("_", "-")
            sku_name = self.manufactured_sku_name_by_code.get(transformed_sku_code)
        return UnifiedInventoryItem(
            brands=sku_meta.brands if sku_meta else None,
            source=dto.source,
            bob_code=dto.bob_code,
            po_number=dto.po_number,
            site_name=dc_name,
            sku_code=dto.sku_code,
            sku_name=sku_name,
            category=sku_meta.category if sku_meta else None,
            units=dto.units,
            status=dto.status,
            lot_code=dto.lot_code,
            snapshot_timestamp=dto.snapshot_timestamp,
            location_id=dto.location_id,
            expiration_date=dto.expiration_date,
            supplier=dto.supplier,
        )

    @cached_property
    def manufactured_sku_name_by_code(self) -> dict[str, str]:
        return manufactured_sku.get_manufactured_skus_name_by_code()

    @staticmethod
    def _get_sku_meta(sku_codes: frozenset[str] | None) -> dict[SKU_CODE, SkuMeta]:
        if not sku_codes:
            return {}
        return culinary_sku.get_sku_meta_by_code(site=None, sku_codes=sku_codes)

    @cached_property
    def _all_sites(self) -> dict[BOB_CODE, DcConfig]:
        return {dc.bob_code: dc for dc in dc_admin.get_all_market_dcs(market=self.market)}

    @cached_property
    def _multibrand_wh_code_mapping(self) -> dict[BOB_CODE, str]:
        """
        To map multi-brand DC bob code to corresponding multi-brand code
        """
        return {dc.bob_code: dc.multi_brand_dc for dc in itertools.chain.from_iterable(self._multibrand_sites.values())}

    @cached_property
    def _multibrand_sites(self) -> dict[str, list[DcConfig]]:
        bob_codes = self.bob_codes or self._all_sites.keys()
        return utils.group_by(
            filter(lambda s: s and s.multi_brand_dc, map(self._all_sites.get, bob_codes)),
            lambda it: it.multi_brand_dc,
        )

    @cached_property
    def _multibrand_primary_site(self) -> dict[str, DcConfig]:
        return {
            multibrand_code: next((dc for dc in dcs if multibrand_code == dc.bob_code), dcs[0])
            for multibrand_code, dcs in self._multibrand_sites.items()
        }

    # TODO: migrate all WH inventory to bob codes
    @cached_property
    def all_warehouses(self) -> dict[WH_CODE, Warehouse]:
        return {wh.code: wh for wh in partners.get_all_partners(market=self.market)}

    @cached_property
    def _selectors(self) -> _InventorySelectors:
        res = _InventorySelectors()
        hj_ids = set()
        self._add_warehouse_selectors(res, hj_ids)
        self._add_site_selectors(res, hj_ids)
        if self._multibrand_sites:
            self._remove_redundant_multibrand_sites(res.unified_selector)
        res.hj_wh_ids.extend(hj_ids)
        return res

    def _add_warehouse_selectors(self, res: _InventorySelectors, hj_ids: set[str]) -> None:
        for wh in self.all_warehouses.values():
            if self.bob_codes and wh.bob_code not in self.bob_codes:
                continue
            # TODO: migrate all WH inventory to bob codes
            res.unified_selector[wh.code] = wh.inventory_type
            if wh.inventory_type.is_high_jump:
                hj_ids.add(wh.hj_name)

    def _add_site_selectors(self, res: _InventorySelectors, hj_ids: set[str]) -> None:
        for site in self._all_sites.values():
            if self.bob_codes and site.bob_code not in self.bob_codes:
                continue
            if site.inventory_type.is_in_unified_inventory or site.inventory_type.is_high_jump:
                res.unified_selector[site.bob_code] = site.inventory_type
                if site.inventory_type.is_high_jump:
                    hj_ids.add(site.high_jump_name)
            elif site.inventory_type.is_cycle_count:
                res.cycle_counts_selector.append(site)
            elif site.inventory_type.is_wmsl:
                res.wmsl_selector.append(site.bob_code)

    def _remove_redundant_multibrand_sites(self, selector: dict[BOB_CODE, InventoryInputType]) -> None:
        inventory_timestamps = inventory.get_latest_inventory_timestamps(
            {s.bob_code: s.inventory_type for sites in self._multibrand_sites.values() for s in sites}
        )
        for sites in self._multibrand_sites.values():
            code_with_max_ts = max(
                (
                    (s.bob_code, s.inventory_type)
                    for s in sites
                    if (s.bob_code, s.inventory_type) in inventory_timestamps
                ),
                key=inventory_timestamps.get,
                default=None,
            )
            if not code_with_max_ts:
                continue
            for site in sites:
                if (site.bob_code, site.inventory_type) != code_with_max_ts:
                    selector.pop(site.bob_code)
