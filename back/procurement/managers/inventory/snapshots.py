from collections import defaultdict
from datetime import date
from decimal import Decimal

from procurement.client.slack import alerts
from procurement.client.slack.alerts import KafkaInventorySnapshotOutdated
from procurement.constants.hellofresh_constant import WmslLocationType
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.inventory_snapshots import InventorySnapshot
from procurement.managers.admin import brand_admin, dc_admin
from procurement.repository.inventory import inventory_snapshot


def get_ingredient_depletion_snapshots(
    bob_code: str, date_from: date, date_to: date
) -> dict[WmslLocationType, dict[SKU_CODE, dict[date, dict[date, Decimal]]]]:
    res = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal))))
    snapshots = get_snapshots(bob_code=bob_code, date_from=date_from, date_to=date_to)
    for row in snapshots:
        res[row.location_type][row.sku_code][row.snapshot_date][row.expiration_date] += row.units
    return res


def get_packaging_depletion_snapshots(
    bob_code: str, date_from: date, date_to: date
) -> dict[SKU_CODE, dict[date, Decimal]]:
    res = defaultdict(lambda: defaultdict(Decimal))
    snapshots = get_snapshots(bob_code=bob_code, date_from=date_from, date_to=date_to)
    for row in snapshots:
        res[row.sku_code][row.snapshot_date] += row.units or 0
    return res


def get_snapshots(bob_code: str, date_from: date, date_to: date) -> list[InventorySnapshot]:
    return inventory_snapshot.get_snapshots(bob_code=bob_code, date_from=date_from, date_to=date_to)


def alert_outdated_snapshots():
    bob_codes = set()
    for brand in brand_admin.get_brand_ids():
        bob_codes.update(
            (site.bob_code, site.inventory_type)
            for site in dc_admin.get_enabled_sites(ScmWeek.current_week(), brand=brand).values()
        )

    today = date.today()

    alerts.preventative_alert(
        KafkaInventorySnapshotOutdated(
            market=context.get_request_context().market,
            bob_codes={
                i.bob_code
                for i in inventory_snapshot.get_latest_snapshot_dates_by_wh()
                if i.snapshot_date < today and (i.bob_code, i.inventory_type) in bob_codes
            },
        )
    )
