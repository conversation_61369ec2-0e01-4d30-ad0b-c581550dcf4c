from collections import defaultdict
from datetime import date, datetime
from decimal import Decimal

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.inventory import Inventory
from procurement.repository.inventory import inventory as inventory_repo


def get_ingredient_inventory_records(
    bob_code: str, inventory_type: InventoryInputType, date_from: date, date_to: date
) -> dict[SKU_CODE, dict[date, dict[date, Decimal]]]:
    res = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
    inventory = get_inventory_records(bob_code, inventory_type, date_from, date_to)
    for row in inventory:
        res[row.sku_code][row.snapshot_timestamp][row.expiration_date] += row.quantity
    return res


def get_packaging_depletion_inventory_records(
    bob_code: str, inventory_type: InventoryInputType, date_from: date, date_to: date
) -> dict[SKU_CODE, dict[date, Decimal]]:
    res = defaultdict(lambda: defaultdict(Decimal))
    inventory = get_inventory_records(bob_code, inventory_type, date_from, date_to)
    for row in inventory:
        res[row.sku_code][row.snapshot_timestamp] += row.quantity
    return res


def get_inventory_records(
    bob_code: str, inventory_type: InventoryInputType, date_from: date, date_to: date
) -> list[Inventory]:
    return inventory_repo.get_inventory_records(
        bob_code=bob_code, inventory_type=inventory_type, date_from=date_from, date_to=date_to
    )


def get_latest_inventory(bob_code: str, inventory_type: InventoryInputType) -> list[Inventory]:
    return inventory_repo.get_latest_inventory_records(bob_code=bob_code, inventory_type=inventory_type)


def get_latest_inventory_date(bob_code: str, inventory_type: InventoryInputType) -> datetime:
    return inventory_repo.get_max_date(bob_code=bob_code, inventory_type=inventory_type) or datetime.now()
