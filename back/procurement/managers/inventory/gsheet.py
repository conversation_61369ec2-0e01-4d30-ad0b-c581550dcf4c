from collections.abc import Iterable
from datetime import datetime, time
from typing import Type

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import MARKET_US, InventoryInputType, InventoryState
from procurement.core import utils
from procurement.core.request_utils import context
from procurement.data.googlesheet_model.gsheet_inventory import (
    CanadaInventoryDataModel,
    CanadaInventoryRow,
    FidelityInventoryDataModel,
    InventoryDataModel,
    InventoryDateSheet,
    InventoryRow,
    PimtInventoryDateSheet,
    TopoSkuDataModel,
)
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.data.models.ordering.topo_sku import TopoSkuModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.pimt import partners
from procurement.repository.inventory import inventory
from procurement.repository.ordering import topo_sku


def update_gsheet_inventory(wh_code: str, models: tuple[Type[InventoryDataModel], Type[InventoryDateSheet]]) -> None:
    inventory_model, snapshot_date_model = models
    snapshot_timestamp = read_snapshot_date(snapshot_date_model)
    inventory_reader = (
        _read_inventory_items_us if context.get_request_context().market == MARKET_US else _read_inventory_items_ca
    )
    updates = list(inventory_reader(wh_code, snapshot_timestamp, inventory_model(wh_code)))
    delete_ts = datetime.combine(snapshot_timestamp.date(), time.max)
    inventory.delete_inventory_by_warehouse_snapshot_timestamp(
        wh_codes=[wh_code], inventory_type=InventoryInputType.GSHEET, snapshot_timestamp=delete_ts
    )
    inventory.insert_inventory(updates)


def update_fidelity_inventory() -> None:
    snapshot_timestamp = read_snapshot_date(PimtInventoryDateSheet)
    wh_code_by_bob_code = partners.get_bob_code_to_warehouse()

    updates_by_po_sku_lot_wh_code = {}
    wh_to_delete = set()

    for row in gsheet_admin.read_gsheet(FidelityInventoryDataModel(), is_formatted=False):
        try:
            validate_required_fields(row, ["sku_code", "po_number", "lot", "bob_code"])
            if row.sku_code.isnumeric():
                raise ValueError(f"Invalid SKU Code format '{row.sku_code}'")
            wh_code = wh_code_by_bob_code.get(row.bob_code)
            if not wh_code:
                continue

            wh_to_delete.add(wh_code)

            key = (row.po_number, row.sku_code, row.lot, row.expiration_date, row.bob_code)
            if key in updates_by_po_sku_lot_wh_code:
                inventory_item = updates_by_po_sku_lot_wh_code[key]
                inventory_item[UnifiedInventoryModel.case_quantity] += row.cases or 0
                continue
            updates_by_po_sku_lot_wh_code[key] = _fill_base_fields(row, wh_code, snapshot_timestamp)
            updates_by_po_sku_lot_wh_code[key].update(
                {
                    UnifiedInventoryModel.po_number: row.po_number,
                    UnifiedInventoryModel.order_number: utils.get_order_number_from_po_number(row.po_number),
                }
            )
        except (ValueError, IndexError) as exc:
            warnings.notify_sheet_error(row, exc, "Error while importing Fidelity Data", skipped=True)

    delete_ts = datetime.combine(snapshot_timestamp.date(), time.max)
    inventory.delete_inventory_by_warehouse_snapshot_timestamp(
        wh_codes=wh_to_delete, inventory_type=InventoryInputType.GSHEET, snapshot_timestamp=delete_ts
    )
    inventory.insert_inventory(list(updates_by_po_sku_lot_wh_code.values()))


def read_snapshot_date(model: Type[InventoryDateSheet]) -> datetime:
    data = gsheet_admin.read_gsheet(model())
    if data and data[0].update_date:
        return data[0].update_date
    raise ValueError("Empty update date")


def _read_inventory_items_ca(
    wh_code: str, snapshot_timestamp: datetime, sheet_model: CanadaInventoryDataModel
) -> Iterable[dict]:
    updates_by_po_sku_lot = {}
    for row in gsheet_admin.read_gsheet(sheet_model, is_formatted=False):
        try:
            validate_required_fields(row, ["sku_code", "lot"])
            if row.sku_code.isnumeric():
                raise ValueError(f"Invalid SKU Code format '{row.sku_code}'")
            key = (row.sku_code, row.lot, row.expiration_date)
            if key in updates_by_po_sku_lot:
                inventory_item = updates_by_po_sku_lot[key]
                inventory_item[UnifiedInventoryModel.case_quantity] += row.cases or 0
                inventory_item[UnifiedInventoryModel.unit_quantity] += row.units or (
                    inventory_item[UnifiedInventoryModel.case_quantity]
                    * inventory_item[UnifiedInventoryModel.case_size]
                )
                continue
            updates_by_po_sku_lot[key] = _fill_base_fields(row, wh_code, snapshot_timestamp)
            updates_by_po_sku_lot[key].update(
                {
                    UnifiedInventoryModel.supplier: row.supplier,
                    UnifiedInventoryModel.case_size: row.case_size,
                    UnifiedInventoryModel.unit_quantity: (
                        ((row.cases or 0) * (row.case_size or 0)) if row.units is None else row.units
                    ),
                }
            )
        except (ValueError, IndexError) as exc:
            warnings.notify_sheet_error(row, exc, f"Error while importing {wh_code} Data", skipped=True)
    return updates_by_po_sku_lot.values()


def _read_inventory_items_us(
    wh_code: str, snapshot_timestamp: datetime, sheet_model: InventoryDataModel | CanadaInventoryDataModel
) -> Iterable[dict]:
    updates_by_po_sku_lot = {}
    for row in gsheet_admin.read_gsheet(sheet_model, is_formatted=False):
        try:
            validate_required_fields(row, ["sku_code", "po_number", "lot"])
            if row.sku_code.isnumeric():
                raise ValueError(f"Invalid SKU Code format '{row.sku_code}'")
            key = (row.po_number, row.sku_code, row.lot, row.expiration_date)
            if key in updates_by_po_sku_lot:
                inventory_item = updates_by_po_sku_lot[key]
                inventory_item[UnifiedInventoryModel.case_quantity] += row.cases or 0
                continue
            updates_by_po_sku_lot[key] = _fill_base_fields(row, wh_code, snapshot_timestamp)
            updates_by_po_sku_lot[key].update(
                {
                    UnifiedInventoryModel.po_number: row.po_number,
                    UnifiedInventoryModel.order_number: utils.get_order_number_from_po_number(row.po_number),
                }
            )
        except (ValueError, IndexError) as exc:
            warnings.notify_sheet_error(row, exc, f"Error while importing {wh_code} Data", skipped=True)
    return updates_by_po_sku_lot.values()


def _fill_base_fields(row: CanadaInventoryRow | InventoryRow, wh_code: str, snapshot_timestamp: datetime) -> dict:
    return {
        UnifiedInventoryModel.sku_code: row.sku_code,
        UnifiedInventoryModel.case_quantity: row.cases or 0,
        UnifiedInventoryModel.lot_code: row.lot,
        UnifiedInventoryModel.wh_code: wh_code,
        UnifiedInventoryModel.expiration_date: row.expiration_date,
        UnifiedInventoryModel.inventory_type: InventoryInputType.GSHEET,
        UnifiedInventoryModel.inventory_status: InventoryState.AVAILABLE,
        UnifiedInventoryModel.snapshot_timestamp: snapshot_timestamp,
    }


def update_topo_sku_list() -> None:
    updates = []
    for row in gsheet_admin.read_gsheet(TopoSkuDataModel(), is_formatted=False):
        try:
            validate_required_fields(row, ["dc", "sku_code"])
            dcs = utils.split_comma_string(row.dc)
            for dc in dcs:
                updates.append(
                    {
                        TopoSkuModel.site: dc,
                        TopoSkuModel.sku_code: row.sku_code,
                    }
                )

        except (ValueError, IndexError) as exc:
            warnings.notify_sheet_error(row, exc, "Error while importing TOPO SKUs Data", skipped=True)

    if not updates:
        return
    topo_sku.upsert_topo_skus(updates)
