from itertools import chain
from typing import Any, NamedTuple

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.core.cache_utils.factory import CACHES
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import NewDCAzureRoleEvent, SiteChangedEvent
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.request_utils import context
from procurement.core.typing import SITE
from procurement.data.models.inventory.site import SiteModel
from procurement.data.models.inventory.weekly_config import WeeklyConfigModel
from procurement.managers.admin import brand_admin
from procurement.repository.inventory import weekly_config
from procurement.repository.ordering import supplier as supplier_repo
from procurement.services.database import app_db

from . import queries
from .constants import DcProperty


class WeeklyConfigInput(NamedTuple):
    brand: str
    week: ScmWeek
    receiving_type: str
    inventory_type: InventoryInputType
    source: str
    enabled: bool
    ordering_tool_name: str
    hj_name: str
    site_short: str
    site_full: str
    is_3pl: bool
    is_fulfillment_only: bool
    is_hj_autostore: bool
    timezone: str
    bob_code: str
    dc_azure_role: str | None
    hybrid_needs_data_source: str
    is_wip_consumption_enabled: bool
    optional: dict[str, Any]
    supplier_names: list[str]


def update_sequence(new_order: dict[SITE, str | int]) -> None:
    weekly_config.update_sequence(new_order)


def upsert_weekly_config(config_input: WeeklyConfigInput) -> None:
    DcProperty.validate_values(config_input.optional)

    if multi_brand_dc := config_input.optional.get("multi_brand_dc"):
        _validate_multi_brand_dc(config_input.brand, config_input.week, multi_brand_dc)

    if config_input.brand not in brand_admin.get_brand_ids(config_input.week):
        raise ValueError("Invalid Brand value")

    supplier_codes = list(supplier_repo.get_supplier_codes_by_names(config_input.supplier_names))

    with app_db.transaction() as transaction:
        request_context = context.get_request_context()
        weekly_config.upsert_site(
            {
                SiteModel.id: config_input.site_short,
                SiteModel.brand: config_input.brand,
                SiteModel.market: request_context.market,
                SiteModel.name: config_input.site_full,
                SiteModel.timezone: config_input.timezone,
                SiteModel.dc_azure_role: config_input.dc_azure_role,
                SiteModel.sequence_number: weekly_config.get_max_site_sequence() + 1,
            },
            transaction=transaction,
        )

        properties = {
            DcProperty.HIGH_JUMP_NAME.name: config_input.hj_name,
            DcProperty.RECEIVING_TYPE.name: config_input.receiving_type,
            DcProperty.INVENTORY_TYPE.name: config_input.inventory_type.name,
            DcProperty.SOURCE.name: config_input.source,
            DcProperty.ORDERING_TOOL_NAME.name: config_input.ordering_tool_name,
            DcProperty.IS_3PL.name: config_input.is_3pl,
            DcProperty.IS_HJ_AUTOSTORE.name: config_input.is_hj_autostore,
            DcProperty.IS_FULFILMENT_ONLY.name: config_input.is_fulfillment_only,
            DcProperty.HYBRID_NEEDS_DATA_SOURCE.name: config_input.hybrid_needs_data_source,
            DcProperty.IS_WIP_CONSUMPTION_ENABLED.name: config_input.is_wip_consumption_enabled,
            DcProperty.BOB_CODE.name: config_input.bob_code,
            DcProperty.SUPPLIER_CODES.name: supplier_codes,
            **config_input.optional,
        }

        weekly_config.upsert_weekly_config(
            {
                WeeklyConfigModel.site: config_input.site_short,
                WeeklyConfigModel.brand: config_input.brand,
                WeeklyConfigModel.market: request_context.market,
                WeeklyConfigModel.week: config_input.week.to_number_format(),
                WeeklyConfigModel.enabled: config_input.enabled,
                WeeklyConfigModel.properties: properties,
                WeeklyConfigModel.user_id: request_context.user_info.user_id,
            },
            transaction=transaction,
        )

        weekly_config.bulk_delete_configs(
            queries.get_next_configs(config_input.week, config_input.brand, config_input.site_short).keys()
        )

        if config_input.dc_azure_role:
            EVENT_BUS.publish(NewDCAzureRoleEvent(name=config_input.dc_azure_role))

    EVENT_BUS.publish(SiteChangedEvent())


def _validate_multi_brand_dc(brand: str, week: ScmWeek, multi_brand_dc: str) -> None:
    brands = brand_admin.get_brand_ids(week)
    multi_brand_configs = filter(
        lambda c: c.multi_brand_dc == multi_brand_dc,
        chain(*(queries.get_all_dcs(week=week, brand=brand, only_enabled=False).values() for brand in brands)),
    )
    if multi_brand_config := next(multi_brand_configs, None):
        if brand_admin.get_week_config(brand) != brand_admin.get_week_config(multi_brand_config.brand):
            raise InputParamsValidationException("All Brands in Multi Brand DC must have same production week config")


def delete_configs(brand: str, site: str) -> None:
    weekly_config.delete_configs(brand, site)
    EVENT_BUS.publish(SiteChangedEvent())


@EVENT_BUS.subscribe(SiteChangedEvent)
def drop_all_configs(_: SiteChangedEvent):
    CACHES.sites_configs.delete()
