from __future__ import annotations

from datetime import datetime
from typing import NamedTuple

import pytz
from pytz.tzinfo import BaseTzInfo

from procurement.constants.hellofresh_constant import HybridNeedsDataSourceType, InventoryInputType, ReceiveInputType
from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.weekly_config import WeeklyConfig
from procurement.data.models.inventory import DataSource

from .constants import DcProperty


class DcConfig(NamedTuple):
    id: str
    brand: str
    sheet_name: str
    site_name: str
    sequence_number: int
    week: ScmWeek
    enabled: bool
    properties: dict
    timezone: BaseTzInfo
    dc_azure_role: str
    market: str

    @staticmethod
    def from_weekly_config(item: WeeklyConfig) -> DcConfig:
        return DcConfig(
            id=item.id,
            brand=item.brand,
            sheet_name=item.site,
            site_name=item.site_name,
            sequence_number=item.sequence_number,
            week=item.week,
            enabled=item.enabled,
            properties=item.properties,
            timezone=pytz.timezone(item.timezone),
            dc_azure_role=item.dc_azure_role,
            market=item.market,
        )

    @property
    def receiving_type(self) -> ReceiveInputType:
        return ReceiveInputType(self.properties[DcProperty.RECEIVING_TYPE.name])

    @property
    def inventory_type(self) -> InventoryInputType:
        return InventoryInputType[self.properties[DcProperty.INVENTORY_TYPE.name]]

    @property
    def source(self) -> DataSource:
        return DataSource(self.properties.get(DcProperty.SOURCE.name, DataSource.GSHEET.name))

    def site_time_now(self) -> datetime:
        return datetime.now(self.timezone)

    @property
    def ordering_tool_name(self) -> str:
        return self.properties[DcProperty.ORDERING_TOOL_NAME.name]

    @property
    def high_jump_name(self) -> str:
        return self.properties[DcProperty.HIGH_JUMP_NAME.name]

    @property
    def ot_emergency_reason(self) -> str:
        return self.properties.get(DcProperty.OT_EMERGENCY_REASON.name)

    @property
    def pkg_name(self) -> str:
        return self.properties.get(DcProperty.PKG_NAME.name)

    @property
    def is_3pl(self) -> bool:
        return self.properties.get(DcProperty.IS_3PL.name, False)

    @property
    def is_fulfilment_only(self) -> bool:
        return self.properties.get(DcProperty.IS_FULFILMENT_ONLY.name, False)

    @property
    def is_hj_autostore(self) -> bool:
        return self.properties.get(DcProperty.IS_HJ_AUTOSTORE.name, False)

    @property
    def autobagger_supplier(self) -> str:
        return self.properties.get(DcProperty.AUTOBAGGER_SUPPLIER.name, f"Autobagger - {self.sheet_name}")

    @property
    def locked_pricing(self) -> str:
        return self.properties.get(DcProperty.LOCKED_PRICING.name)

    @property
    def bob_code(self) -> str:
        return self.properties.get(DcProperty.BOB_CODE.name)

    @property
    def multi_brand_dc(self) -> str:
        return self.properties.get(DcProperty.MULTI_BRAND_DC.name)

    @property
    def hybrid_needs_data_source(self) -> HybridNeedsDataSourceType:
        return HybridNeedsDataSourceType(self.properties[DcProperty.HYBRID_NEEDS_DATA_SOURCE.name])

    @property
    def is_wip_consumption_enabled(self) -> bool:
        return self.properties.get(DcProperty.IS_WIP_CONSUMPTION_ENABLED.name, False)

    @property
    def supplier_codes(self) -> list[int]:
        return self.properties.get(DcProperty.SUPPLIER_CODES.name, [])

    @property
    def consolidated_site_code(self) -> str | None:
        return self.properties.get(DcProperty.CONSOLIDATED_SITE_CODE.name)

    @property
    def ope_bob_code(self) -> str | None:
        return self.properties.get(DcProperty.OPE_BOB_CODE.name)
