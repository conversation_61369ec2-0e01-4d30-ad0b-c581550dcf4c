from collections.abc import Collection
from datetime import timedelta

from pytz.tzinfo import BaseTzInfo

from procurement.constants.hellofresh_constant import BRAND_HF
from procurement.core import utils
from procurement.core.cache_utils.caching import no_args_temp_cache
from procurement.core.cache_utils.factory import CACHES
from procurement.core.dates import ScmWeek
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BOB_CODE, BRAND, CONSOLIDATED_SITE, MULTI_BRAND_DCS, SITE
from procurement.data.dto.config import Market
from procurement.data.dto.inventory.site import Site, SiteSequence
from procurement.managers.admin import brand_admin
from procurement.repository.inventory import weekly_config
from procurement.repository.procurement import distribution_center, markets
from procurement.repository.procurement import user as user_repo

from .dto import DcConfig


def get_configs(week: ScmWeek, brand: str = None) -> list[DcConfig]:
    brands = [brand] if brand else brand_admin.get_brand_ids(week)
    return [item for _brand in brands for item in get_all_dcs(week, _brand, only_enabled=False).values()]


def get_site(brand: str, site: str, week: ScmWeek = None) -> DcConfig | None:
    if not week:
        week_config = brand_admin.get_week_config(brand)
        week = str(ScmWeek.current_week(week_config))
    return get_enabled_sites(week=week, brand=brand).get(site)


@request_cache
@ApplicationMetrics.functional_methods()
def get_all_dcs(week: ScmWeek, brand: str, only_enabled: bool = True, market: str = None) -> dict[str, DcConfig]:
    return {
        key: DcConfig.from_weekly_config(value)
        for (key, value) in weekly_config.get_all_configs(week, brand, only_enabled, market).items()
    }


def get_next_configs(week: ScmWeek, brand: str, site: str) -> dict[int, ScmWeek]:
    return {config.id: config.week for config in weekly_config.get_next_configs(week, brand, site)}


@request_cache
def get_all_sites() -> dict[BRAND, dict[SITE, Site]]:
    return weekly_config.get_all_sites()


def _get_enabled_sites(week: ScmWeek, brand: str, ignore_user: bool = False, market: str = None) -> dict[str, DcConfig]:
    market = market or context.get_request_context().market
    return _filter_sites(
        sites=CACHES.sites_configs.get(
            f"{market}:{week}:{brand}",
            provider=lambda: get_all_dcs(week=week, brand=brand, market=market),
        ),
        ignore_user=ignore_user,
    )


def _filter_sites(sites: dict[str, DcConfig], ignore_user: bool) -> dict[str, DcConfig]:
    if not ignore_user:
        available_sites = context.get_request_context().user_permissions.available_sites
        return {site: site_dto for site, site_dto in sites.items() if site in available_sites.get(site_dto.brand, [])}
    return sites


@request_cache
def get_enabled_sites(
    week: str | ScmWeek, brand: str, ignore_user: bool = False, market: str = None
) -> dict[str, DcConfig]:
    return _get_enabled_sites(
        week=ScmWeek.from_str(week) if isinstance(week, str) else week,
        brand=brand,
        ignore_user=ignore_user,
        market=market,
    )


@request_cache
def get_all_market_dcs(ignore_user: bool = False, market: str = None, week: ScmWeek | None = None) -> list[DcConfig]:
    brands = (brand_admin.get_brands(week) if week else brand_admin.get_latest_brands(market=market)).values()
    return [
        dc
        for brand in brands
        for dc in _get_enabled_sites(
            week or ScmWeek.current_week(brand.scm_week_config), brand.code, ignore_user, market
        ).values()
    ]


def get_timezones_map(market: str | None = None) -> dict[BOB_CODE, BaseTzInfo]:
    timezones = distribution_center.get_timezones(market)
    # any timezone from site configs takes precedence over dc registry since they might be incorrect
    for dc in get_all_market_dcs(ignore_user=True, market=market):
        timezones[dc.bob_code] = dc.timezone
    return timezones


@request_cache
def get_all_enabled_multi_brand_sites(
    week: ScmWeek, ignore_user: bool = False
) -> dict[MULTI_BRAND_DCS, list[DcConfig]]:
    return utils.group_by(
        filter(lambda dc: dc.multi_brand_dc, get_all_market_dcs(week=week, ignore_user=ignore_user)),
        lambda dc: dc.multi_brand_dc,
    )


@request_cache
def get_all_enabled_consolidated_sites(
    week: ScmWeek, ignore_user: bool = False
) -> dict[CONSOLIDATED_SITE, list[DcConfig]]:
    return utils.group_by(
        filter(lambda dc: dc.consolidated_site_code, get_all_market_dcs(week=week, ignore_user=ignore_user)),
        lambda dc: dc.consolidated_site_code,
    )


def get_main_consolidated_site(sites: Collection[DcConfig]) -> DcConfig:
    main_by_bob_code = next((s for s in sites if s.bob_code == s.consolidated_site_code), None)
    main_as_hf = next((s for s in sites if s.brand == BRAND_HF), None)
    first_site = next(iter(sites))
    return main_by_bob_code or main_as_hf or first_site


def get_all_sites_with_consolidation_by_brand(
    brand: str, sites: list[str], week: ScmWeek, ignore_user: bool = False, with_multi_brand: bool = True
) -> dict[BOB_CODE, DcConfig]:
    enabled_sites = get_enabled_sites(week=week, brand=brand, ignore_user=ignore_user)

    bob_code_dc_mapping = {}
    for site in sites:
        enabled_site = enabled_sites.get(site)
        if not enabled_site:
            continue

        bob_code_dc_mapping[enabled_site.bob_code] = enabled_site
        if enabled_site.multi_brand_dc and with_multi_brand:
            for dc in get_all_enabled_multi_brand_sites(week=week, ignore_user=ignore_user).get(
                enabled_site.multi_brand_dc, []
            ):
                bob_code_dc_mapping[dc.bob_code] = dc
        if enabled_site.consolidated_site_code:
            for dc in get_all_enabled_consolidated_sites(week=week, ignore_user=ignore_user).get(
                enabled_site.consolidated_site_code, []
            ):
                bob_code_dc_mapping[dc.bob_code] = dc

    return bob_code_dc_mapping


def get_all_brand_site_combinations(
    brands: list[str], sites: list[str], week: ScmWeek, ignore_user: bool = False
) -> dict[BOB_CODE, DcConfig]:
    bob_code_dc_mapping = {}
    for brand in brands:
        enabled_sites = get_enabled_sites(week, brand, ignore_user=ignore_user)
        matched_sites = filter(None, map(enabled_sites.get, sites))
        for site in matched_sites:
            bob_code_dc_mapping[site.bob_code] = site
    return bob_code_dc_mapping


def get_market_sites_sequence() -> list[SiteSequence]:
    return list(
        {
            site.sheet_name: SiteSequence(id=site.sheet_name, name=site.site_name, sequence_number=site.sequence_number)
            for site in get_all_market_dcs()
        }.values()
    )


@request_cache
def get_markets(ignore_permissions: bool = False, user_id: int | None = None) -> list[Market]:
    available_markets = markets.get_markets()

    if ignore_permissions:
        return available_markets

    user_id = context.get_request_context().user_info.user_id if user_id is None else user_id
    user_markets = user_repo.get_user_markets(user_id)
    available_markets = [market for market in available_markets if market.code in user_markets]
    return available_markets


@no_args_temp_cache(timedelta(minutes=5))
def get_all_market_codes() -> Collection[str]:
    return tuple(m.code for m in get_markets(ignore_permissions=True))
