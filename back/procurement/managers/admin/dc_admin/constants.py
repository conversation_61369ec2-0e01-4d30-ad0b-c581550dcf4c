from __future__ import annotations

from collections.abc import Iterable
from enum import Enum
from typing import Any, Callable, NamedTuple

from procurement.constants.hellofresh_constant import (
    TIMEZONE_NAMES,
    HybridNeedsDataSourceType,
    InventoryInputType,
    ReceiveInputType,
)
from procurement.core.exceptions.validation_errors import InvalidDataException
from procurement.data.models.inventory import DataSource


class PropertyEnumBase(Enum):
    def __init__(self, *_):
        self._name_ = self._name_.lower()


class PropertyType(str, PropertyEnumBase):
    STRING = "string"
    ARRAY = "array"
    BOOLEAN = "boolean"


class Property(NamedTuple):
    required: bool
    display: bool = True
    label: str | None = None
    description: str | None = None
    type: PropertyType | None = None
    values: Callable[..., Iterable[Any]] | None = None


class DcProperty(Property, PropertyEnumBase):
    def __new__(cls, *prop):
        _id = len(cls.__members__) + 1
        obj = Property.__new__(cls, *prop)
        obj._value_ = _id
        return obj

    LOCKED_PRICING = Property(
        required=False,
        label="Locked Pricing dc cost",
        description="Column name by which cost will be extracted ",
        type=PropertyType.STRING,
    )
    PKG_NAME = Property(
        required=False,
        label="PKG DC",
        description="Alternative ID to extract records related to DC from the Production Kit Guide data source.",
        type=PropertyType.STRING,
    )
    OT_EMERGENCY_REASON = Property(
        required=False,
        label="Ordering Tool Emergency Reason",
        description="Alternative ID to extract records related to DC from the Ordering Tool data source.",
        type=PropertyType.STRING,
    )
    AUTOBAGGER_SUPPLIER = Property(
        required=False,
        label="Autobagger Supplier name",
        description="Autobagger Supplier name for calculation HJ status",
        type=PropertyType.STRING,
    )

    DC_AZURE_ROLE = Property(
        required=False,
        label="Azure DC User role",
        description="DC User role created in Azure for this site",
        type=PropertyType.STRING,
    )
    BOB_CODE = Property(
        required=True, label="DC BOB Code", description="BOB Code of the Site", type=PropertyType.STRING
    )
    MULTI_BRAND_DC = Property(
        required=False,
        label="Multi Brand DC",
        description="Code of the DC if it holds multiple brands",
        type=PropertyType.STRING,
    )

    IS_3PL = Property(required=True, type=PropertyType.BOOLEAN)

    IS_HJ_AUTOSTORE = Property(required=True, type=PropertyType.BOOLEAN)

    IS_FULFILMENT_ONLY = Property(required=True, type=PropertyType.BOOLEAN)

    HIGH_JUMP_NAME = Property(required=True, type=PropertyType.STRING)

    RECEIVING_TYPE = Property(required=True, type=PropertyType.STRING, values=lambda: list(ReceiveInputType))

    INVENTORY_TYPE = Property(required=True, type=PropertyType.STRING, values=lambda: list(InventoryInputType))

    SOURCE = Property(
        required=True,
        type=PropertyType.STRING,
        values=lambda: [source.value for source in DataSource.__members__.values()],
    )
    TIMEZONE = Property(required=True, label="Timezone", type=PropertyType.STRING, values=lambda: TIMEZONE_NAMES)
    ORDERING_TOOL_NAME = Property(required=True, type=PropertyType.STRING)

    HYBRID_NEEDS_DATA_SOURCE = Property(
        required=True, type=PropertyType.STRING, values=lambda: list(HybridNeedsDataSourceType)
    )
    IS_WIP_CONSUMPTION_ENABLED = Property(required=True, type=PropertyType.BOOLEAN)
    SUPPLIER_CODES = Property(required=True, type=PropertyType.ARRAY)
    CONSOLIDATED_SITE_CODE = Property(
        required=False,
        label="Consolidated Site Code",
        description="Sites with the same code will have consolidated depletion views "
        "(don't confuse with Multi Brand DC which applies to inventory)",
        type=PropertyType.STRING,
    )
    OPE_BOB_CODE = Property(
        required=False,
        label="OPE bob code",
        description="Use this bob code instead of the site's own bob code for OPE forecast uploads. "
        "The value has to match the bob code of an actual active site in the IMT config",
        type=PropertyType.STRING,
    )

    @staticmethod
    def validate_values(to_validate: dict):
        fields: list[DcProperty] = [field for field in DcProperty.__members__.values() if field.values]
        for field in fields:
            if field.name not in to_validate:
                continue

            allowed_values = field.values()
            validate_values = to_validate[field.name]
            if isinstance(validate_values, list):
                is_valid = all(value in allowed_values for value in validate_values)
            else:
                is_valid = validate_values in allowed_values

            if not is_valid:
                raise InvalidDataException(f"Invalid value for {field.name}. Possible values are {allowed_values}")

    @staticmethod
    def get_optional() -> list[DcProperty]:
        return [item for item in DcProperty.__members__.values() if not item.required]

    @staticmethod
    def get_required() -> list[DcProperty]:
        return [item for item in DcProperty.__members__.values() if item.required]
