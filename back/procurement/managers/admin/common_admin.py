import itertools

from procurement.constants.hellofresh_constant import HARDCODED_SUPPLIER_NAMES
from procurement.core.typing import BOB_CODE
from procurement.managers.admin import dc_admin
from procurement.managers.pimt import partners


def get_all_internal_suppliers() -> set[str]:
    internal_suppliers = {site.ordering_tool_name for site in dc_admin.get_all_market_dcs()}
    internal_suppliers.update(itertools.chain.from_iterable(wh.ot_suppliers for wh in partners.get_all_partners()))
    internal_suppliers.update(HARDCODED_SUPPLIER_NAMES)
    return internal_suppliers


def get_all_bob_codes() -> set[BOB_CODE]:
    bob_codes = {site.bob_code for site in dc_admin.get_all_market_dcs()}
    bob_codes.update(wh.bob_code for wh in partners.get_all_partners())
    bob_codes.discard(None)
    return bob_codes
