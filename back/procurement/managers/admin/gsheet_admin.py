import logging
import re
from collections import defaultdict
from collections.abc import Iterable
from datetime import datetime
from typing import Type

from procurement.client.googlesheets import GSheetsInvalidSheetError, SpreadsheetDetails, gsheet_client
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.gsheets import GSheetValidationError
from procurement.core.request_utils import context, warnings
from procurement.data.googlesheet_model.core import GsheetDocModel, SheetModel, SheetRowT
from procurement.data.models.social.gsheet import GsheetAdminModel, GsheetMetaModel
from procurement.managers.datasync.framework import warnings as datasync_warnings
from procurement.repository.inventory import gsheet_admin as repository

GSHEET_ID_FROM_URL_PATTERN = "/spreadsheets/d/([a-zA-Z0-9-_]+)"
GSHEET_URL_FORMAT = "https://docs.google.com/spreadsheets/d/{gsheet_id}"

logger = logging.getLogger(__name__)


def get_gsheet_id(doc: GsheetDocModel, brand: str = None, week: ScmWeek = None) -> str | None:
    return repository.get_gsheet_id(doc.doc_code, week, brand)


def read_gsheet(
    model: SheetModel[SheetRowT], brand: str = None, week: ScmWeek = None, is_formatted: bool = True
) -> list[SheetRowT]:
    gsheet_id = get_gsheet_id(model.parent_doc, brand, week)
    if gsheet_id:
        try:
            return model.read(gsheet_id, is_formatted=is_formatted)
        except GSheetsInvalidSheetError:
            error_message = f"Please check that {model.sheet_name} tab exists"
    else:
        error_message = f"The gsheet was not found by doc_code={model.parent_doc.doc_code} week={week}, brand={brand}"
    datasync_warnings.notify_warning(error_message)
    return []


def update_gsheet(
    model: SheetModel[SheetRowT], brand: str, week: str, data: list[dict], fast_update: bool = True
) -> None:
    sheet_id = repository.get_gsheet_id(model.parent_doc.doc_code, week, brand)
    if not sheet_id:
        raise ValueError(f"Missing export gsheet id for {brand}::{week}::{model.parent_doc.doc_code}")
    model.create_sheet_if_not_exist(sheet_id)
    if sheet_id:
        model.write(sheet_id, [row.to_list() for row in model.from_records(data)], fast_update=fast_update)
        return
    logger.warning("No gsheet id to export %s for brand %s week %s", model.sheet_name, brand, week)


def save_gsheet_admin(scm_week: ScmWeek, meta_id: str, gsheet_url: str) -> str:
    user_info = context.get_request_context().user_info
    gsheet_meta = _get_gsheet_meta(meta_id)
    gsheet_id = extract_gsheet_id(gsheet_url)

    actual_doc_details = gsheet_client.get_doc_details(gsheet_id)

    _validate_doc_title(actual_doc_details, gsheet_meta, scm_week)

    saved_gsheet_id = _save(gsheet_meta=gsheet_meta, scm_week=scm_week, gsheet_id=gsheet_id, user_id=user_info.user_id)

    week_part = f"for week={str(scm_week)} " if gsheet_meta.weekly else ""
    logger.info("GSheet %s updated %s by %s", gsheet_meta.doc_code, week_part, user_info.email)
    return saved_gsheet_id


def delete_gsheet_url(scm_week, meta_id):
    gsheet_meta = _get_gsheet_meta(meta_id)

    if not gsheet_meta.weekly:
        scm_week = None

    repository.delete_gsheet_url(gsheet_meta.id, scm_week)

    logger.info(
        "GSheet with meta_id=%s week=%s was deleted by %s",
        meta_id,
        scm_week,
        context.get_request_context().user_info.email,
    )


def check_weekly_docs_availability(
    check_config: dict[tuple[str, str], Iterable[ScmWeek]],
) -> list[tuple[GsheetMetaModel, list[ScmWeek]]]:
    """
    Returns missing document's weeks in pairs (GsheetMeta, weeks)
    :param check_config: describes what weeks to check for each document
        in format of dict (doc_code, brand) -> list(weeks).
        e.g. {("pkg", "HF"): [2021-W46, 2021-W47]}
    """
    doc_codes = {doc_code for doc_code, _ in check_config}
    weeks = {week for weeks in check_config.values() for week in weeks}
    docs_meta = repository.get_meta(doc_codes=doc_codes)
    docs_map = {(doc.doc_code, doc.brand): doc for doc in docs_meta}
    loaded_docs = {
        (doc.meta_id, ScmWeek.from_str(doc.scm_week))
        for doc in repository.get_docs(scm_weeks=weeks, doc_codes=doc_codes)
    }
    missing_docs = defaultdict(list)
    for code_brand, weeks in check_config.items():
        if not (doc := docs_map.get(code_brand)):
            continue
        for week in weeks:
            if (doc.id, week) not in loaded_docs:
                missing_docs[doc.id].append(week)
    docs_map = {doc.id: doc for doc in docs_meta}
    return [(docs_map[doc_id], weeks) for doc_id, weeks in missing_docs.items()]


def _save(gsheet_meta, scm_week, gsheet_id, user_id):
    gsheet_admin_existing_id = repository.get_gsheet_admin(gsheet_meta.id, scm_week)
    if gsheet_admin_existing_id:
        repository.update_gsheet_admin(
            gsheet_admin_existing_id,
            values={
                GsheetAdminModel.gsheet_id: gsheet_id,
                GsheetAdminModel.updated_tmst: datetime.now().astimezone(),
                GsheetAdminModel.user_id: user_id,
            },
        )
        return gsheet_admin_existing_id
    return repository.insert_gsheet_admin(
        meta_id=gsheet_meta.id,
        gsheet_id=gsheet_id,
        scm_week=scm_week if gsheet_meta.weekly else None,
        user_id=user_id,
    )


def get_docs(project: str, scm_week: ScmWeek = None, brand: str = None):
    result = {"general": [], "weekly": []}
    loaded_docs = {doc.meta_id: doc for doc in repository.get_docs((scm_week,), brand=brand, project=project)}

    for meta in repository.get_meta(brand=brand, project=project):
        doc_details = {"metaId": meta.id, "name": meta.name}

        if loaded_doc := loaded_docs.get(meta.id):
            doc_details["status"] = "VALID"
            doc_details["gsheetId"] = loaded_doc.gsheet_id
            doc_details["url"] = GSHEET_URL_FORMAT.format(gsheet_id=loaded_doc.gsheet_id)

        doc_group = "weekly" if meta.weekly else "general"
        if meta.visible:
            result[doc_group].append(doc_details)
    return result


def extract_gsheet_id(gsheet_id):
    id_search = re.search(GSHEET_ID_FROM_URL_PATTERN, gsheet_id)
    if id_search:
        return id_search.group(1)
    return gsheet_id


def _get_gsheet_meta(meta_id):
    return repository.get_meta_by_id(meta_id)


def _validate_doc_title(actual_doc_details: SpreadsheetDetails, gsheet_meta: GsheetMetaModel, scm_week: ScmWeek = None):
    week_number, year = (scm_week.week, scm_week.year) if scm_week else (None, None)
    options = {"week_number": week_number, "scm_week": scm_week, "year": year}
    expected_title = gsheet_meta.title_template.format_map(options)
    if expected_title.strip() != actual_doc_details.title.strip():
        raise GSheetValidationError(
            f"The title does not match the expected value: "
            f'actual title - "{actual_doc_details.title}"; '
            f'expected title - "{expected_title}"'
        )


def notify_warning_context_if_doc_invalid(week: ScmWeek, brand: str, doc_model: Type[GsheetDocModel]):
    if not repository.get_gsheet_id(doc_model.doc_code, week, brand):
        warnings.add_message(f"This view is presented without {repository.get_doc_name(doc_model)} data.")
