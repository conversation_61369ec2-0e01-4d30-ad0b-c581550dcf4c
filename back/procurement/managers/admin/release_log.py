from datetime import date

from procurement.data.dto.procurement.release_log import ReleaseLog
from procurement.repository.procurement import release_log as release_log_repo


def create_release_log(release_log_input: ReleaseLog) -> None:
    release_log_repo.create_release_log(
        features=release_log_input.features, release_date=release_log_input.release_date
    )


def update_release_log(release_log_input: ReleaseLog) -> None:
    return release_log_repo.update_release_log(
        release_date=release_log_input.release_date,
        features=release_log_input.features,
    )


def get_release_logs_page(for_dc_user: bool, date_from: date | None) -> tuple[list[ReleaseLog], list[date]]:
    return release_log_repo.get_release_logs_page(for_dc_user, date_from), release_log_repo.get_page_keys(for_dc_user)


def get_latest_releases(for_dc_user: bool) -> list[ReleaseLog]:
    return release_log_repo.get_latest_releases(for_dc_user)
