from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext
from hellofresh.proto.stream.scm.registry.dc.v1beta1.distribution_center_pb2 import DistributionCenter

from procurement.constants.protobuf import WmsType
from procurement.core.kafka import handlers
from procurement.core.kafka.handlers import KafkaHandler
from procurement.data.models.social.distribution_center import DcModel
from procurement.repository.procurement import distribution_center


class DcHandler(KafkaHandler):
    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return handlers.parse_protobuf(value, DistributionCenter, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        value: DistributionCenter = msg.value()
        distribution_center.upsert_dc(
            {
                DcModel.id: value.id,
                DcModel.bob_code: value.code,
                DcModel.name: value.name,
                DcModel.enabled: value.enabled,
                DcModel.tz: value.time_zone,
                DcModel.is_third_party: value.third_party_managed,
                DcModel.market: value.market[0].code.upper(),
                DcModel.wms_type: WmsType(value.wms_system).name if value.wms_system else None,
                DcModel.parent_id: value.parent_id,
                DcModel.organization: value.billing_address.organization,
            }
        )
