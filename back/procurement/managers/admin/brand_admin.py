from collections.abc import Iterable

from procurement.core.cache_utils.factory import LOCAL_CACHES
from procurement.core.dates import ScmWeek, ScmWeekConfig
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import SiteChangedEvent
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.pubsub import publisher as pubsub
from procurement.core.pubsub.events import InvalidateCacheEvent
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BRAND, MARKET
from procurement.data.dto.config import Brand
from procurement.repository.inventory import weekly_config


def edit_brand_config(brand: Brand):
    week = brand.config_week
    _validate_consolidated_weeks(brand)
    existing_config = get_brands(week, with_disabled=True).get(brand.code)
    if existing_config and not _is_brand_equal(brand, existing_config):
        _validate_week_config_change(brand, existing_config)
        _update_brand(brand)
    if _is_brand_weekly_equal(brand, existing_config):
        return
    previous_config = get_brands(week - 1, with_disabled=True).get(brand.code)
    if _is_brand_weekly_equal(brand, previous_config):
        _delete_brand_config(existing_config.config_week, existing_config.code)
    else:
        latest_config = get_latest_brands(with_disabled=True).get(brand.code)
        _validate_week_config_change(brand, latest_config)
        _add_brand_config(brand)
    next_config = _get_next_config(brand)
    if _is_brand_weekly_equal(brand, next_config):
        _delete_brand_config(next_config.config_week, next_config.code)


def update_order(brands: Iterable[BRAND]):
    all_brands = get_all_brand_ids_ordered()
    if set(brands) != set(all_brands):
        raise ValueError("Not all brands ordered.")
    _update_brands_order(brands)


@request_cache
def get_brands(week: ScmWeek = None, with_disabled: bool = False, market: str = None) -> dict[str, Brand]:
    week = week or ScmWeek.current_week()
    market = market or context.get_request_context().market
    all_brands = _get_all_brands()
    return next(
        iter(
            {code: config for code, config in brands.items() if with_disabled or config.enabled}
            for config_week, brands in all_brands.get(market, {}).items()
            if config_week <= week
        ),
        {},
    )


@request_cache
def get_week_config(brand: str, market: str | None = None) -> ScmWeekConfig:
    return get_latest_brands(with_disabled=True, market=market)[brand].scm_week_config


def get_latest_brands(with_disabled: bool = False, market: str = None) -> dict[str, Brand]:
    return get_brands(week=ScmWeek.max(), with_disabled=with_disabled, market=market)


@request_cache
def get_all_brand_ids_ordered() -> tuple[BRAND, ...]:
    market = context.get_request_context().market
    return weekly_config.get_all_brands_ordered(market)


@request_cache
def get_brand_ids(week: ScmWeek = None, market: str = None) -> frozenset[BRAND]:
    return frozenset(get_brands(week=week, market=market).keys())


def get_brand_ids_by_name(week: ScmWeek = None) -> dict[str, BRAND]:
    return {b.name: b.code for b in get_brands(week).values()}


def _is_brand_weekly_equal(first: Brand | None, second: Brand | None) -> bool:
    if first is None or second is None:
        return False
    return first.code == second.code and first.enabled == second.enabled


def _is_brand_equal(first: Brand, second: Brand) -> bool:
    return (
        # fmt: off
        (first.name, first.consolidated, first.scm_week_config, first.country_code)
        == (second.name, second.consolidated, second.scm_week_config, second.country_code)
        # fmt: on
    )


def _validate_week_config_change(first: Brand, second: Brand | None):
    if second and first.scm_week_config != second.scm_week_config:
        raise InputParamsValidationException("Week config can not be changed")


def _validate_consolidated_weeks(changed_brand: Brand):
    if not changed_brand.consolidated or not changed_brand.enabled:
        return
    consolidated_week_config = next(
        (
            b.scm_week_config
            for b in get_brands(changed_brand.config_week).values()
            if b.code != changed_brand.code and b.consolidated
        ),
        None,
    )
    if consolidated_week_config and consolidated_week_config != changed_brand.scm_week_config:
        raise InputParamsValidationException("All consolidated Brands must have same production week config")


def _get_next_config(brand: Brand) -> Brand | None:
    """
    Returns next changed config for supplied brand config if such exists
    """
    all_brands = _get_all_brands()
    next_configs = []
    for config_week, configs in all_brands.get(brand.market, {}).items():
        if config_week <= brand.config_week:
            break
        next_config = configs[brand.code]
        if next_config.config_week > brand.config_week:
            next_configs.append(next_config)
    return next_configs[-1] if next_configs else None


def _get_all_brands() -> dict[MARKET, dict[ScmWeek, dict[BRAND, Brand]]]:
    return LOCAL_CACHES.brands.get(provider=weekly_config.get_brands)


def _add_brand_config(brand: Brand):
    weekly_config.upsert_brand_config(brand)
    pubsub.publish_event(InvalidateCacheEvent(LOCAL_CACHES.brands.key))
    EVENT_BUS.publish(SiteChangedEvent())


def _update_brand(brand: Brand):
    weekly_config.upsert_brand(brand)
    pubsub.publish_event(InvalidateCacheEvent(LOCAL_CACHES.brands.key))


def _update_brands_order(brands: Iterable[BRAND]):
    weekly_config.update_brands_order(brands)
    pubsub.publish_event(InvalidateCacheEvent(LOCAL_CACHES.brands.key))


def _delete_brand_config(week: ScmWeek, brand: str):
    weekly_config.delete_brand_config(week, brand)
    pubsub.publish_event(InvalidateCacheEvent(LOCAL_CACHES.brands.key))
    EVENT_BUS.publish(SiteChangedEvent())
