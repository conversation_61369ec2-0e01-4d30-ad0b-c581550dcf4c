from __future__ import annotations

import dataclasses
from collections.abc import Iterable
from datetime import date, timedelta
from itertools import chain

from procurement.constants.hellofresh_constant import BRAND_FJ, NO, YES, PartOfDay
from procurement.core.dates.weeks import ScmWeek
from procurement.core.typing import BOB_CODE, WH_CODE
from procurement.data.googlesheet_model.imt_daily_export import (
    FactorAmNetworkQueried,
    FactorNetworkDeplSheet,
    FactorPmNetworkQueried,
)
from procurement.managers.admin import brand_admin
from procurement.managers.dc_inventory.network_depletion import dashboard
from procurement.managers.dc_inventory.network_depletion.preset_configuration import (
    FACTOR_AC_PRESET,
    FACTOR_AG_PRESET,
    FACTOR_BO_PRESET,
    FACTOR_PRESET,
    FACTOR_UB_PRESET,
)
from procurement.managers.depletion.result import AggregatedIngredientDepletionResult, IngredientDepletionResult
from procurement.managers.exports import utils
from procurement.managers.exports.base import BaseExport, BaseExportModel
from procurement.managers.exports.models import IngredientDepletionExportData
from procurement.managers.pimt import partners


@dataclasses.dataclass
class NetworkDepletionExportData(IngredientDepletionExportData):
    is_consolidated: str

    @staticmethod
    def from_dashboard_item(
        item: IngredientDepletionResult, context: dict[WH_CODE, BOB_CODE]
    ) -> NetworkDepletionExportData:
        weekly = item.weekly_overview
        bulk_data = item.bulk_values
        return NetworkDepletionExportData(
            brand=", ".join(sorted(item.brands)),
            week=item.week,
            site=", ".join(sorted(context.get(site, site) for site in item.sites)),
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            category=item.category,
            commodity_group=item.commodity_group,
            buyer=item.buyer,
            impacted_recipes=", ".join(sorted(item.impacted_recipes)),
            plan=item.plan,
            forecast_oscar=item.forecast,
            delta=item.delta,
            supplement_need=item.supplement_need,
            critical_delivery=item.critical_delivery_status,
            units_needed=weekly.units_needed,
            units_ordered=weekly.units_ordered,
            units_received=weekly.units_received,
            units_in_house_hj=weekly.hj_units_in_house,
            row_need=weekly.row_need,
            previous_week_row_need=weekly.prev_week_row_need,
            units_in_house_minus_row_need=weekly.units_in_house_minus_row_need,
            next_week_forecast=weekly.next_week_forecast,
            units_in_house_min_row_need_min_forecast=weekly.units_in_house_minus_row_need_minus_forecast,
            units_scheduled_to_be_produced_by_autobagger=weekly.units_to_produce_by_autobagger,
            inventory=weekly.inventory,
            discards=weekly.discards,
            donations=weekly.donations,
            pulls=weekly.pulls,
            total_on_hand=weekly.total_on_hand,
            total_on_hand_minus_production_needs=weekly.total_on_hand_minus_prod_needs,
            in_progress_hj=weekly.in_progress_hj,
            not_delivered=weekly.not_delivered,
            buffer_quantity=weekly.buffer_quantity,
            buffer_percent=weekly.buffer_percent,
            awaiting_delivery=weekly.awaiting_delivery,
            bulk_units_ordered=bulk_data.bulk_units_ordered,
            bulk_units_received=bulk_data.bulk_units_received,
            bulk_delta=bulk_data.delta,
            bulk_units_in_hj=bulk_data.bulk_units_in_hj,
            unit_of_measure=item.unit_of_measure,
            is_consolidated=YES if isinstance(item, AggregatedIngredientDepletionResult) else NO,
        )


class NetworkDepletionExport(BaseExport):
    result_model = IngredientDepletionExportData

    def __init__(self, gsheet_id: str, weeks: Iterable[ScmWeek]):
        self.weeks = weeks
        self.gsheet_id = gsheet_id

    def _get_data(self) -> list[BaseExportModel]:
        items = []
        wh_code_to_bob_code = {wh.code: wh.bob_code for wh in partners.get_all_partners() if wh.bob_code}
        for week in self.weeks:
            for agg_item in chain(
                dashboard.build_dashboard(week=week, preset=FACTOR_PRESET),
                dashboard.build_dashboard(week=week, preset=FACTOR_AG_PRESET),
                dashboard.build_dashboard(week=week, preset=FACTOR_AC_PRESET),
                dashboard.build_dashboard(week=week, preset=FACTOR_UB_PRESET),
                dashboard.build_dashboard(week=week, preset=FACTOR_BO_PRESET),
            ):
                items.append(NetworkDepletionExportData.from_dashboard_item(agg_item, wh_code_to_bob_code))
                if len(agg_item.child_depletion) > 1:
                    for item in agg_item.child_depletion:
                        items.append(NetworkDepletionExportData.from_dashboard_item(item, wh_code_to_bob_code))
        return items


class FactorAmNetworkDepletionExport(NetworkDepletionExport):
    gsheet_model = FactorAmNetworkQueried()


class FactorPmNetworkDepletionExport(NetworkDepletionExport):
    gsheet_model = FactorPmNetworkQueried()


def factor_network_depletion_daily_export(part_of_day: PartOfDay, export_date: date = None):
    gsheet_id = utils.get_gsheet_id(FactorNetworkDeplSheet.parent_doc)
    export_date = export_date or date.today()

    if part_of_day == PartOfDay.AM:
        model = FactorAmNetworkDepletionExport
    else:
        model = FactorPmNetworkDepletionExport
    weeks = _get_factor_weeks(export_date)

    model(gsheet_id=gsheet_id, weeks=weeks).write_to_sheet()


def _get_factor_weeks(export_date: date) -> Iterable[ScmWeek]:
    week_config = brand_admin.get_week_config(BRAND_FJ)
    day_ahead = export_date + timedelta(days=1)

    export_week = ScmWeek.from_date(day_ahead, week_config=week_config)
    if export_week.is_overlapped_by_previous_week(day_ahead, week_config):
        return export_week - 1, export_week

    return (export_week,)
