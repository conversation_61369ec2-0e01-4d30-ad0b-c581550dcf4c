import itertools
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime, time
from decimal import Decimal
from functools import cached_property
from typing import NamedTuple

from procurement.constants.hellofresh_constant import DEPLETION_WEEKS_AHEAD, IngredientCategory, InventoryState
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, PO_NUMBER, SITE, SKU_CODE, PoSku
from procurement.data.dto.forecasts.forecasts import Forecast
from procurement.data.dto.inventory.unified_inventory import UnifiedInventoryDto
from procurement.data.dto.ordering.purchase_order.incoming import IncomingOrder
from procurement.data.dto.pimt.packaging_info import PackagingInfo
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.dto.sku import SkuMeta
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.dc_inventory.replenishment import replenishment_override
from procurement.managers.forecasts import forecast_manager
from procurement.managers.imt import buyers, production_need, strategy_manager
from procurement.managers.imt.production_need import BrandSite
from procurement.managers.inventory import shelf_life_manager
from procurement.managers.inventory.unified_inventory import UnifiedInventoryManager
from procurement.managers.ordering import purchase_qty_recommendation_listener
from procurement.managers.ordering import service as po_service
from procurement.managers.ordering import supplier_splits_listener
from procurement.managers.pimt import master_replenishment as master_replenishment_service
from procurement.managers.pimt import packaging_info as packaging_info_service
from procurement.managers.pimt import partners as partner_service
from procurement.managers.pimt.models import MasterReplenishment
from procurement.managers.receipt import grn, hj_receipts, manual_receipts
from procurement.managers.sku import culinary_sku
from procurement.repository.inventory import allocation_tool_price, inventory
from procurement.repository.inventory import receipt_override as receipt_override_repo
from procurement.repository.inventory import vendor_managed_inventory as vendor_managed_inventory
from procurement.repository.ordering import topo_sku


class MinAndIncrementalQtyValue(NamedTuple):
    moq: int
    ioq: int


class ReplenishmentDataContext:
    def __init__(
        self, region: str | None, use_topo_recommendation: bool | None = None, sku_codes: Iterable[str] | None = None
    ):
        self.region: str = region
        self.use_topo_recommendation: bool | None = use_topo_recommendation
        self.week: ScmWeek = ScmWeek.current_week()
        self.today: date = date.today()
        self.sku_codes = frozenset(sku_codes) if sku_codes else None
        self.market = context.get_request_context().market
        regional_sites = {}
        all_sites: dict[SITE, list[DcConfig]] = utils.group_by(dc_admin.get_all_market_dcs(), lambda dc: dc.sheet_name)
        warehouses = []
        for wh in (w for w in partner_service.get_all_partners() if w.region == region or region is None):
            if wh.regional_dcs:
                warehouses.append(wh)
            for dc_code in wh.regional_dcs:
                regional_sites[dc_code] = all_sites.get(dc_code, [])
        self.warehouses: tuple[Warehouse, ...] = tuple(warehouses)
        self.sites: tuple[DcConfig, ...] = tuple(itertools.chain(*regional_sites.values()))
        self.site_codes: set[SITE] = set(regional_sites.keys())
        self.bob_codes: set[SITE] = set(site.bob_code for site in self.sites)

    @cached_property
    def replenishment_weeks(self) -> tuple[ScmWeek, ...]:
        return tuple(ScmWeek.range(self.week, self.week + DEPLETION_WEEKS_AHEAD))

    @cached_property
    def undelivered_weeks(self) -> tuple[ScmWeek, ...]:
        return tuple(ScmWeek.range(self.week - 4, self.week - 1))

    @cached_property
    def _forecasts(self) -> list[Forecast]:
        forecast = forecast_manager.get_forecast_manager()
        return [
            f
            for f in forecast.get_all_forecasts(
                weeks=self.replenishment_weeks, sites=[s.sheet_name for s in self.sites], sku_codes=self.sku_codes
            )
            if f.forecast
        ]

    @cached_property
    def forecasts(self) -> dict[SKU_CODE, dict[ScmWeek, Decimal]]:
        res = defaultdict(lambda: defaultdict(Decimal))
        for it in self._forecasts:
            res[it.sku_code][it.scm_week] += it.forecast
        return res

    @cached_property
    def buyers(self) -> dict[SKU_CODE, set[str]]:
        res = defaultdict(set)
        for site_sku, buyer in buyers.get_buyers_by_site_sku(sites=self.site_codes, sku_codes=self.sku_codes).items():
            res[site_sku[1]].add(buyer)
        return res

    @cached_property
    def skus_info(self) -> dict[SKU_CODE, SkuMeta]:
        if not self.warehouses:
            return {}
        return {
            sku.sku_code: sku
            for sku in culinary_sku.get_sku_meta(
                site=None, sku_codes=self._get_sku_listing(), ignore_category=IngredientCategory.PACKAGING
            )
            if not sku.is_manufactured_sku
        }

    def _get_sku_listing(self) -> frozenset[SKU_CODE]:
        active_inventory_skus = (
            s
            for s, inv in self.inventory.items()
            if any(True for it in inv if it.status == InventoryState.AVAILABLE and it.units)
        )
        forecast_skus = (f.sku_code for f in self._forecasts if f.forecast)
        po_skus = (sku for sku in self.po_inbound)
        skus = itertools.chain(active_inventory_skus, forecast_skus, po_skus)
        if self.sku_codes:
            return frozenset(filter(lambda sku: sku in self.sku_codes, skus))
        return frozenset(skus)

    @cached_property
    def _incoming_pos(self) -> dict[ScmWeek, list[IncomingOrder]]:
        if not self.sites and not self.warehouses:
            return {}
        weeks = self.undelivered_weeks + self.replenishment_weeks
        bob_codes = [s.bob_code for s in self.sites] + [w.bob_code for w in self.warehouses]
        inbound_pos = po_service.get_inbound_quantities(weeks=weeks, bob_codes=bob_codes, sku_codes=self.sku_codes)
        site_autobagger_map = {s.bob_code: s.autobagger_supplier for s in self.sites if s.autobagger_supplier}
        res = defaultdict(list)
        for po in inbound_pos:
            po_line_key = PoSku(po.po_number, po.sku_code)
            if not (
                po_service.is_po_auto_voided(po)
                or po_service.is_po_supplier_auto_received(po, site_autobagger_map.get(po.bob_code))
                or self._is_po_received(po_line_key, self._received_keys)
            ):
                res[po.week].append(po)
        return res

    @cached_property
    def _received_keys(self) -> set[PoSku]:
        weeks = self.undelivered_weeks + self.replenishment_weeks
        received = grn.get_received_keys(weeks=weeks, sites=self.sites, whs=self.warehouses, sku_codes=self.sku_codes)
        received.update(
            hj_receipts.get_received_hj_keys(
                weeks=weeks, sites=self.sites, whs=self.warehouses, sku_codes=self.sku_codes
            )
        )
        received.update(
            manual_receipts.get_received_manual_keys(weeks=weeks, sites=self.sites, sku_codes=self.sku_codes)
        )
        for dc in self.sites:
            received.update(receipt_override_repo.get_valid_receipt_override_keys(dc.sheet_name, dc.brand, weeks))
        return received

    @staticmethod
    def _is_po_received(po_line_key: PoSku, received_keys: set[PoSku]) -> bool:
        short_po_line_key = PoSku(utils.get_order_number_from_po_number(po_line_key.po_number), po_line_key.sku_code)
        return po_line_key in received_keys or short_po_line_key in received_keys

    def _flatten_incoming_pos(self, weeks: Iterable[ScmWeek]) -> Iterable[IncomingOrder]:
        return itertools.chain.from_iterable(self._incoming_pos.get(w, []) for w in weeks)

    @cached_property
    def po_inbound(self) -> dict[SKU_CODE, dict[ScmWeek, Decimal]]:
        res = defaultdict(lambda: defaultdict(Decimal))
        for po in self._flatten_incoming_pos(self.replenishment_weeks):
            res[po.sku_code][po.week] += po.quantity
        return res

    @cached_property
    def undelivered(self) -> dict[SKU_CODE, Decimal]:
        res = defaultdict(Decimal)
        today = datetime.combine(self.today, time())
        for po in self._flatten_incoming_pos(self.undelivered_weeks):
            if po.delivery_time_start < today:
                res[po.sku_code] += po.quantity
        return res

    @cached_property
    def inventory(self) -> dict[SKU_CODE, list[UnifiedInventoryDto]]:
        bob_codes = {s.bob_code for s in self.sites}
        bob_codes.update(w.bob_code for w in self.warehouses)
        inv = UnifiedInventoryManager(bob_codes=bob_codes, sku_codes=self.sku_codes).get_live_inventory()
        return utils.group_by(inv, lambda it: it.sku_code)

    @cached_property
    def vendor_managed_inventory(self) -> dict[SKU_CODE, Decimal]:
        inventories = defaultdict(Decimal)
        vmi = vendor_managed_inventory.get_vendor_managed_inventory(
            packaging_regions={
                region for w in self.warehouses if w.packaging_regions for region in w.packaging_regions
            },
            market=self.market,
        )
        for item in vmi:
            inventories[item.sku_code] += item.units
        return inventories

    @cached_property
    def master_replenishment(self) -> MasterReplenishment:
        return master_replenishment_service.get_master_replenishment(sku_codes=self.sku_codes)

    @cached_property
    def packaging_info_by_sku(self) -> dict[SKU_CODE, PackagingInfo]:
        return packaging_info_service.get_packaging_info(self.skus_info.keys())

    @cached_property
    def row_needs(self) -> dict[SKU_CODE, int]:
        week = self.replenishment_weeks[0]
        dcs = [BrandSite(brand=site.brand, site=site.sheet_name) for site in self.sites]
        row_need = production_need.get_row_need(dcs=dcs, week=week)
        return {sku: row_need.get(sku, 0) for sku in self.skus_info}

    @cached_property
    def strategy_managers(self) -> dict[SKU_CODE, set[str]]:
        return strategy_manager.get_strategy_managers(sku_codes=self.sku_codes)

    @cached_property
    def po_prices(self) -> dict[tuple[PO_NUMBER, SKU_CODE], Decimal]:
        po_numbers = {inv.po_number for inv in itertools.chain.from_iterable(self.inventory.values())}

        return {
            (inv.po_number, inv.sku_code): inv.cost for inv in inventory.get_inventory_unit_costs(po_numbers=po_numbers)
        }

    @cached_property
    def allocation_sku_prices(self) -> dict[tuple[SKU_CODE, BOB_CODE], Decimal]:
        return {
            (allocation_price.sku_code, dc_obj.bob_code): allocation_price.cost
            for dc_obj in self.sites
            for allocation_price in allocation_tool_price.get_allocation_prices(
                week=self.week, dc_obj=dc_obj, brand=dc_obj.brand, sku_codes=self.sku_codes
            )
        }

    @cached_property
    def override_info(self) -> dict[SKU_CODE, Decimal]:
        return {
            item.sku_code: item.override
            for item in replenishment_override.get_replenishment_overrides(region=self.region, sku_codes=self.sku_codes)
        }

    @cached_property
    def purchase_qty_recommendation(self) -> dict[SKU_CODE, dict[ScmWeek, Decimal]]:
        result = defaultdict(lambda: defaultdict(Decimal))
        for item in purchase_qty_recommendation_listener.get_purchase_qty_recommendations(
            list(self.skus_info), self.bob_codes, self.replenishment_weeks
        ):
            if self._is_in_topo_skus(item.sku_code):
                result[item.sku_code][item.week] = item.quantity
        return result

    def _is_in_topo_skus(self, sku_code: SKU_CODE) -> bool:
        return killswitch.use_topo_for_all_skus or sku_code in self.topo_skus

    @cached_property
    def supplier_splits(self) -> dict[SKU_CODE, MinAndIncrementalQtyValue]:
        return {
            item.sku_code: MinAndIncrementalQtyValue(moq=item.moq, ioq=item.ioq)
            for item in supplier_splits_listener.get_moq_ioq_values(list(self.skus_info), self.bob_codes)
        }

    @cached_property
    def topo_skus(self) -> set[SKU_CODE]:
        return topo_sku.get_topo_skus(bob_codes=self.bob_codes)

    @cached_property
    def shelf_life(self) -> dict[SKU_CODE, int]:
        return {
            item.sku_code: item.shelf_life_value
            for item in shelf_life_manager.get_shelf_life_values(list(self.skus_info))
        }
