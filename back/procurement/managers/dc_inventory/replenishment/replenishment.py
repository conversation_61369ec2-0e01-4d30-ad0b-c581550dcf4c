from procurement.constants.hellofresh_constant import BRAND_FJ_FULL
from procurement.managers.dc_inventory.replenishment.context import ReplenishmentDataContext
from procurement.managers.dc_inventory.replenishment.region_level import ReplenishmentItem


def get_replenishment_view(region: str | None, use_topo_recommendation: bool | None = None) -> list[ReplenishmentItem]:
    context = ReplenishmentDataContext(region, use_topo_recommendation)
    replenishments = []
    for sku in context.skus_info.values():
        item = ReplenishmentItem(context=context, sku=sku)
        if BRAND_FJ_FULL not in item.sku_overview.brands or all(
            prefix not in sku.sku_code for prefix in ["PCK", "OTH"]
        ):
            replenishments.append(item)
    return [
        repl
        for repl in replenishments
        if repl.has_active_inventory or repl.total_forecast or any(repl.inbounds.values())
    ]


def get_replenishment_item(region: str, sku_code: str) -> ReplenishmentItem:
    context = ReplenishmentDataContext(region, sku_codes={sku_code})
    sku = context.skus_info[sku_code]
    return ReplenishmentItem(context=context, sku=sku)
