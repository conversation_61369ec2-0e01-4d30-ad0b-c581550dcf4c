from decimal import Decimal

from procurement.data.dto.pimt.replenishment_override import ReplenishmentOverrideItem
from procurement.repository.pimt import replenishment_override


def get_replenishment_overrides(region: str, sku_codes: set[str] | None = None) -> list[ReplenishmentOverrideItem]:
    return replenishment_override.get_replenishment_overrides(region=region, sku_codes=sku_codes)


def upsert_replenishment_override(region: str, sku_code: str, value: Decimal) -> None:
    replenishment_override.upsert_replenishment_override(region=region, sku_code=sku_code, value=value)


def delete_replenishment_override(region: str, sku_code: str) -> None:
    replenishment_override.delete_replenishment_override(region=region, sku_code=sku_code)
