from datetime import date, datetime

from procurement.constants.ordering import N_A_PO_VOID, PO_VOID, RECEIVED_STATUSES, SUPPLIER_REJECTED
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.managers import po_status_common
from procurement.managers.admin import common_admin
from procurement.managers.dc_inventory.replenishment.context import ReplenishmentDataContext
from procurement.managers.imt.purchase_order import po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusIncludeFlags, PoStatusResult
from procurement.managers.pimt import purchase_order


def _get_replenishment_po_status(
    repl_context: ReplenishmentDataContext, sku_code: str, weeks: tuple[ScmWeek, ...]
) -> list[PoStatusResult]:
    internal_suppliers = common_admin.get_all_internal_suppliers()
    res = []
    for site in repl_context.sites:
        res.extend(
            po_status.get_po_status_by_sku_code(
                brand=site.brand,
                site=site.sheet_name,
                weeks=weeks,
                sku_code=sku_code,
                include_flags=PoStatusIncludeFlags(
                    include_bulk_skus=True, include_org_cv_skus=True, include_packaged_skus=False
                ),
            )
        )
    for wh in repl_context.warehouses:
        res.extend(
            po_status_common.convert_po_status_data_to_result(
                purchase_order.get_po_data_by_sku_code(weeks, wh, sku_code)
            )
        )
    return [item for item in res if item.supplier not in internal_suppliers and item.sku_code == sku_code]


def get_replenishment_po_status(
    region: str | None,
    sku_code: SKU_CODE,
) -> list[PoStatusResult]:
    repl_context = ReplenishmentDataContext(region)
    return _get_replenishment_po_status(repl_context, sku_code, repl_context.replenishment_weeks)


def get_replenishment_undelivered_po_status(region: str | None, sku_code: SKU_CODE) -> list[PoStatusResult]:
    repl_context = ReplenishmentDataContext(region)
    pos = _get_replenishment_po_status(repl_context, sku_code, repl_context.undelivered_weeks)
    today = date.today()
    undelivered_pos_status = []
    for po in pos:
        po.scheduled_delivery_date = (
            po.scheduled_delivery_date.date()
            if isinstance(po.scheduled_delivery_date, datetime)
            else po.scheduled_delivery_date
        )
        if (
            po.quantity_received == 0
            and po.scheduled_delivery_date < today
            and po.po_status not in RECEIVED_STATUSES
            and po.po_status not in (N_A_PO_VOID, PO_VOID, SUPPLIER_REJECTED)
        ):
            undelivered_pos_status.append(po)
    return undelivered_pos_status
