from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.managers.forecasts import forecast_manager

from .context import ReplenishmentDataContext


class Forecast(NamedTuple):
    site: str
    scm_week: ScmWeek
    sku_code: str
    forecast: UNITS


class ForecastKey(NamedTuple):
    site: str
    scm_week: ScmWeek
    sku_code: str


def get_forecasts(region: str, sku_code: str) -> list[Forecast]:
    repl_context = ReplenishmentDataContext(region)
    forecast = forecast_manager.get_forecast_manager()
    forecasts = defaultdict(Decimal)
    for f in forecast.get_all_forecasts(
        weeks=repl_context.replenishment_weeks, sites=[s.sheet_name for s in repl_context.sites], sku_codes={sku_code}
    ):
        if f.forecast:
            forecasts[ForecastKey(site=f.site, scm_week=f.scm_week, sku_code=f.sku_code)] += f.forecast
    return [
        Forecast(site=key.site, scm_week=key.scm_week, sku_code=key.sku_code, forecast=value)
        for key, value in forecasts.items()
    ]
