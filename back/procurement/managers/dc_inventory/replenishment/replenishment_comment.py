from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ActionType, ReplenishmentCommentEvent
from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.replenishment_comment import ReplenishmentCommentItem
from procurement.repository.pimt import replenishment_comment


def get_replenishment_comments_preview(region: str) -> list[SKU_CODE]:
    return replenishment_comment.get_replenishment_comments_preview(region)


def get_replenishment_comment(region: str, sku_code: str) -> ReplenishmentCommentItem:
    return replenishment_comment.get_replenishment_comment(region, sku_code)


def upsert_replenishment_comments(region: str, sku_code: str, text: str) -> None:
    replenishment_comment.upsert_replenishment_comments(region, sku_code, text)
    EVENT_BUS.publish(
        ReplenishmentCommentEvent(region=region, sku_code=sku_code, text=text, action_type=ActionType.UPDATED)
    )


def delete_replenishment_comments(region: str, sku_code: str) -> None:
    EVENT_BUS.publish(ReplenishmentCommentEvent(region=region, sku_code=sku_code, action_type=ActionType.DELETED))
    replenishment_comment.delete_replenishment_comments(region, sku_code)
