import math
from decimal import Decimal
from functools import cached_property
from typing import Iterable, Protocol

from procurement.constants.hellofresh_constant import IngredientCategory, InventoryState, ReplenishmentType
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.data.dto.inventory.unified_inventory import UnifiedInventoryDto
from procurement.data.dto.sku import SkuMeta
from procurement.managers.dc_inventory.replenishment.context import ReplenishmentDataContext
from procurement.managers.pimt.models import MasterReplenishment

HIGH_FORECAST_WEEK_THRESHOLD_MULTIPLIER = 2
EOW_BUFFER_PERCENTAGE_THRESHOLD = Decimal(0.2)


class ReplenishmentProto(Protocol):
    region: str


class ReplenishmentItem:
    def __init__(
        self,
        context: ReplenishmentDataContext,
        sku: SkuMeta,
    ):
        self._context = context
        self.sku_overview = sku
        sku_code = sku.sku_code
        self._forecasts = context.forecasts.get(sku_code, {})
        self.region = context.region
        self.row_needs = utils.ceil_subone_integer(context.row_needs.get(sku_code))
        self.undelivered = context.undelivered.get(sku_code, Decimal())
        self._vendor_managed_inventory = context.vendor_managed_inventory.get(sku_code, Decimal())
        self._inventory = context.inventory.get(sku_code, {})
        self.packaging_info = context.packaging_info_by_sku.get(sku_code)

        sku_master_rep = context.master_replenishment.replenishments(sku_code=sku_code, sites=context.site_codes)
        master_repl = MasterReplenishment(sku_master_rep)
        self.master_replenishment = master_repl.reduced_replenishment(sku_code)
        self.shelf_life = master_repl.shelf_life(sku_code, sku.category) or 0
        self.lead_time_weeks = self.master_replenishment.max_supplier_lead_time
        self.replenishment_type = self.master_replenishment.replenishment_type
        self.is_market_sku = self.master_replenishment.market
        self.market_factor = self.master_replenishment.market_factor
        self.request_for_proposal = self.master_replenishment.request_for_proposal
        self.min_order_qty = self.master_replenishment.min_order_qty
        self.replenishment_buyers = self.master_replenishment.buyers

    def _aggregate_active_inventory(self, inventory_codes: set[str]) -> Decimal:
        if not inventory_codes:
            return Decimal()
        res = sum(inv.units for inv in self._get_active_inventory(inventory_codes))
        return Decimal(res)

    def _get_active_inventory(self, inventory_codes: set[str]) -> Iterable[UnifiedInventoryDto]:
        for inv in self._inventory:
            if (
                inv.bob_code in inventory_codes
                and inv.status == InventoryState.AVAILABLE
                and (inv.expiration_date is None or inv.expiration_date > self._context.today)
            ):
                yield inv

    @cached_property
    def buyers(self) -> set[str]:
        return self._context.buyers.get(self.sku_overview.sku_code, set())

    @cached_property
    def strategy_manager(self) -> set[str]:
        return self._context.strategy_managers.get(self.sku_overview.sku_code, set())

    @cached_property
    def total_core_inventory(self) -> Decimal:
        return self._aggregate_active_inventory({s.bob_code for s in self._context.sites if not s.is_3pl})

    @cached_property
    def total_tpl_inventory(self) -> Decimal:
        return self._aggregate_active_inventory({s.bob_code for s in self._context.sites if s.is_3pl})

    @property
    def total_vendor_managed_inventory(self) -> Decimal:
        return self._vendor_managed_inventory

    @cached_property
    def current_wh_inventory(self) -> Decimal:
        return self._aggregate_active_inventory({w.code for w in self._context.warehouses})

    @cached_property
    def total_on_hand(self) -> Decimal:
        return self.total_core_inventory + self.total_tpl_inventory + self.current_wh_inventory

    @cached_property
    def expiring_in_sixty_days(self) -> Decimal:
        res = sum(
            inv.units
            for inv in self._inventory
            if inv.expiration_date and 0 < (inv.expiration_date - self._context.today).days < 60
        )
        return Decimal(res)

    @cached_property
    def expired_inventory(self) -> Decimal:
        res = sum(
            inv.units for inv in self._inventory if (inv.expiration_date and inv.expiration_date <= self._context.today)
        )
        return Decimal(res)

    @cached_property
    def _current_inventory(self) -> UNITS:
        return self.total_on_hand_override if self.total_on_hand_override is not None else self.total_on_hand

    @cached_property
    def total_wos_inventory(self) -> UNITS:
        return round(self._current_inventory / self.avg_weekly_usage, 1) if self.avg_weekly_usage else 0

    @cached_property
    def has_active_inventory(self) -> bool:
        return bool(self._current_inventory)

    @cached_property
    def avg_weekly_usage(self) -> int:
        weeks_with_forecast = (
            self._number_of_weeks_running if self.is_market_sku else len(self._context.replenishment_weeks)
        )
        return round(self.total_forecast / weeks_with_forecast) if weeks_with_forecast else 0

    @cached_property
    def total_forecast(self) -> UNITS:
        return sum(forecast for forecast in self._forecasts.values())

    @cached_property
    def forecasts(self) -> dict[ScmWeek, UNITS]:
        return {w: self._forecasts.get(w, Decimal()) for w in self._context.replenishment_weeks}

    @property
    def high_forecast_weeks(self) -> dict[ScmWeek, bool]:
        threshold = HIGH_FORECAST_WEEK_THRESHOLD_MULTIPLIER * self.avg_weekly_usage
        return {w: v > threshold for w, v in self.forecasts.items()}

    @cached_property
    def _inbounds(self) -> dict[ScmWeek, Decimal]:
        inbounds = self._context.po_inbound.get(self.sku_overview.sku_code, {})
        return {w: inbounds.get(w, Decimal()) for w in self._context.replenishment_weeks}

    @cached_property
    def existing_inbound(self) -> Decimal:
        return sum(self._inbounds.values())

    @property
    def _topo_inbounds(self):
        inbounds = dict(self._inbounds)
        for week, qty in self.purchase_qty_recommendation.items():
            inbounds[week] += qty
        return inbounds

    @property
    def inbounds(self) -> dict[ScmWeek, Decimal]:
        return self._topo_inbounds if self._context.use_topo_recommendation else self._inbounds

    def _get_end_of_weeks(self, inbounds: dict[ScmWeek, Decimal]) -> dict[ScmWeek, UNITS]:
        eow = {}
        current_inventory = self._current_inventory
        current_week = self._context.week
        for week, forecast in self.forecasts.items():
            if week == current_week:
                needs = self.row_needs
            else:
                needs = forecast
            current_inventory = current_inventory + inbounds.get(week, 0) - needs
            eow[week] = current_inventory
        return eow

    @cached_property
    def end_of_weeks(self) -> dict[ScmWeek, UNITS]:
        return self._get_end_of_weeks(self.inbounds)

    @cached_property
    def _topo_end_of_weeks(self):
        return self._get_end_of_weeks(self._topo_inbounds)

    @cached_property
    def end_of_weeks_wos(self) -> dict[ScmWeek, UNITS]:
        return {
            week: round(eow / self.avg_weekly_usage, 1) if self.avg_weekly_usage else 0
            for week, eow in self.end_of_weeks.items()
        }

    @cached_property
    def end_of_weeks_buffer_flags(self) -> dict[ScmWeek, bool]:
        eow_buffer_flags = {}
        for week, eow in self.end_of_weeks.items():
            on_order = self.inbounds.get(week)
            threshold = (self.total_on_hand + on_order) * EOW_BUFFER_PERCENTAGE_THRESHOLD
            eow_buffer_flags[week] = eow < threshold
        return eow_buffer_flags

    @cached_property
    def total_inbounds(self) -> UNITS:
        return sum(self.inbounds.values())

    @cached_property
    def _number_of_weeks_running(self) -> int:
        return sum(map(bool, self._forecasts.values()))

    @cached_property
    def shelf_life_weeks(self) -> int:
        return math.floor(self.shelf_life / 7)

    @cached_property
    def _week_po_placed_remaining_units(self) -> int | None:
        if not self.week_po_needs_to_be_placed:
            return None
        eow = self.end_of_weeks.get(self.week_po_needs_to_be_placed)
        return eow if eow is not None else self.current_wh_inventory

    @cached_property
    def _planned_inventory_exceeds_forecast(self) -> bool:
        return (
            self.current_wh_inventory + self.total_core_inventory + self.total_tpl_inventory + self.existing_inbound
            > self.total_forecast
        )

    @cached_property
    def total_on_hand_override(self) -> Decimal | None:
        return self._context.override_info.get(self.sku_overview.sku_code)

    def _calc_replenishment_type_reorder_quantity_units(self) -> int | None:
        if not (self.shelf_life_weeks or self.lead_time_weeks):
            return None
        if self._number_of_weeks_running < self.lead_time_weeks:
            return self.avg_weekly_usage * self._number_of_weeks_running - round(self.existing_inbound)
        if self.shelf_life_weeks - self.lead_time_weeks < 5:
            return 3 * self.avg_weekly_usage - round(self.existing_inbound)
        if self.shelf_life_weeks - self.lead_time_weeks >= 5:
            return self.avg_weekly_usage * self.lead_time_weeks - round(self.existing_inbound)
        return None

    @cached_property
    def required_order_quantity_units(self) -> int | None:
        if self._is_reorder_not_needed or not self.week_po_needs_to_arrive:
            return None
        if self.is_market_sku:
            if self._number_of_weeks_running > self.lead_time_weeks:
                return self.market_target_inventory
            return self.avg_weekly_usage * self._number_of_weeks_running * round(self.market_factor)
        if self.sku_overview.category == IngredientCategory.PROTEIN:
            return self._calculate_required_order_quantity_units_protein()
        return self._calculate_required_order_quantity_units_non_protein()

    def _calculate_required_order_quantity_units_protein(self) -> int:
        repl_type = self.replenishment_type
        if (
            repl_type == ReplenishmentType.REPLENISHMENT
            and self.avg_weekly_usage * self.lead_time_weeks > self.min_order_qty
        ):
            return self.avg_weekly_usage * self.lead_time_weeks
        if repl_type == ReplenishmentType.DYNAMIC and self.avg_weekly_usage * 1.5 > self.min_order_qty:
            return round(self.avg_weekly_usage * 1.5)
        return round(self.min_order_qty)

    def _calculate_required_order_quantity_units_non_protein(self) -> int | None:
        repl_type = self.replenishment_type
        if repl_type == ReplenishmentType.DYNAMIC:
            return round(1.5 * self.avg_weekly_usage)
        if repl_type == ReplenishmentType.MODIFIED_REPLENISHMENT:
            if self._number_of_weeks_running < self.lead_time_weeks:
                return self.avg_weekly_usage * self._number_of_weeks_running - round(self.existing_inbound)
            return 2 * self.avg_weekly_usage - round(self.existing_inbound)
        if repl_type == ReplenishmentType.REPLENISHMENT:
            return self._calc_replenishment_type_reorder_quantity_units()
        return None

    @cached_property
    def required_order_quantity_cases(self) -> int | None:
        if not self.required_order_quantity_units:
            return None
        if not self.packaging_info:
            return 0
        avg_units_per_pallet = self.packaging_info.units_per_pallet or 1
        avg_cases_per_pallet = self.packaging_info.cases_per_pallet or 1
        avg_units_per_case = avg_units_per_pallet / avg_cases_per_pallet
        return round(self.required_order_quantity_units / avg_units_per_case)

    @cached_property
    def required_order_quantity_pallets(self) -> int | None:
        if not self.required_order_quantity_cases:
            return None
        if not self.packaging_info:
            return 0
        return round(self.required_order_quantity_cases / (self.packaging_info.units_per_pallet or 1))

    def __get_scm_week_where_remaining_units_less_then(self, value_to_compare: int | None) -> ScmWeek | None:
        return (
            next(
                (week for week, eow in self.end_of_weeks.items() if eow <= value_to_compare),
                None,
            )
            if value_to_compare
            else None
        )

    def _calc_modified_replenishment_type_week_po_needs_to_arrive(self) -> ScmWeek | None:
        return self.__get_scm_week_where_remaining_units_less_then(self.min_target_inventory)

    def _calc_replenishment_type_week_po_needs_to_arrive(self):
        if self.shelf_life_weeks - self.lead_time_weeks < 5:
            return self.__get_scm_week_where_remaining_units_less_then(self.min_target_inventory)

        week = self.__get_scm_week_where_remaining_units_less_then(self.reorder_point)
        return week + self.lead_time_weeks if week else None

    @cached_property
    def week_po_needs_to_arrive(self) -> ScmWeek | None:
        repl_type = self.replenishment_type
        if self._is_reorder_not_needed:
            return None
        if self.sku_overview.category == IngredientCategory.PROTEIN or repl_type == ReplenishmentType.DYNAMIC:
            week = self.__get_scm_week_where_remaining_units_less_then(self.reorder_point)
            return week + self.lead_time_weeks if week else None
        if repl_type == ReplenishmentType.REPLENISHMENT:
            return self._calc_replenishment_type_week_po_needs_to_arrive()
        if repl_type == ReplenishmentType.MODIFIED_REPLENISHMENT:
            return self._calc_modified_replenishment_type_week_po_needs_to_arrive()
        return None

    @cached_property
    def _is_reorder_not_needed(self) -> bool:
        repl_type = self.replenishment_type
        return (
            not self.has_active_inventory
            or (repl_type == ReplenishmentType.REPLENISHMENT and self._planned_inventory_exceeds_forecast)
            or (not repl_type or repl_type == ReplenishmentType.DETERMINISTIC)
        )

    @cached_property
    def week_po_needs_to_be_placed(self) -> ScmWeek | None:
        if self.week_po_needs_to_arrive and self.lead_time_weeks:
            return self.week_po_needs_to_arrive - self.lead_time_weeks
        return None

    def _calc_replenishment_type_reorder_point(self) -> int | None:
        if (
            self.shelf_life_weeks
            and self.lead_time_weeks
            and self.shelf_life_weeks - self.lead_time_weeks < 5
            and self.week_po_needs_to_be_placed
        ):
            return self._week_po_placed_remaining_units
        if self.shelf_life_weeks - self.lead_time_weeks >= 5:
            return self.avg_weekly_usage * (self.lead_time_weeks + 2) - round(self.existing_inbound)
        return None

    def _reorder_point_protein(self) -> int | None:
        repl_type = self.replenishment_type
        if repl_type == ReplenishmentType.REPLENISHMENT:
            return self.avg_weekly_usage * self.lead_time_weeks + (self.min_target_inventory or 0)
        if repl_type == ReplenishmentType.DYNAMIC:
            return round(self.avg_weekly_usage * 2.5)
        return None

    def _reorder_point_non_protein(self) -> int | None:
        repl_type = self.replenishment_type
        if repl_type == ReplenishmentType.DYNAMIC:
            return round(self.avg_weekly_usage * 2.5)
        if repl_type == ReplenishmentType.REPLENISHMENT:
            return self._calc_replenishment_type_reorder_point()
        if repl_type == ReplenishmentType.MODIFIED_REPLENISHMENT:
            return self._week_po_placed_remaining_units
        return None

    @cached_property
    def reorder_point(self) -> int | None:
        repl_type = self.replenishment_type
        category = self.sku_overview.category
        if not repl_type or repl_type == ReplenishmentType.DETERMINISTIC:
            return None
        if self.is_market_sku:
            return 500 + self.market_target_inventory
        if category == IngredientCategory.PROTEIN:
            return self._reorder_point_protein()
        return self._reorder_point_non_protein()

    @cached_property
    def market_target_inventory(self) -> int:
        return round(self.avg_weekly_usage * self.lead_time_weeks * self.market_factor)

    @cached_property
    def min_target_inventory(self) -> int | None:
        repl_type = self.replenishment_type
        if not repl_type or repl_type == ReplenishmentType.DETERMINISTIC:
            return None
        if self.is_market_sku:
            return 500
        if self.sku_overview.category == IngredientCategory.PROTEIN:
            return self._calc_min_target_inv_protein()
        return self._calc_min_target_inv_non_protein()

    def _calc_min_target_inv_protein(self) -> int | None:
        repl_type = self.replenishment_type
        if repl_type == ReplenishmentType.REPLENISHMENT:
            return 3 * self.avg_weekly_usage
        if repl_type == ReplenishmentType.MODIFIED_REPLENISHMENT:
            return 2 * self.avg_weekly_usage
        return None

    def _calc_min_target_inv_non_protein(self) -> int | None:
        repl_type = self.replenishment_type
        if repl_type == ReplenishmentType.DYNAMIC:
            return round(Decimal("1.5") * self.avg_weekly_usage)
        if repl_type in (ReplenishmentType.MODIFIED_REPLENISHMENT, ReplenishmentType.REPLENISHMENT):
            return 2 * self.avg_weekly_usage
        return None

    def _get_inventory_cost(self, inventory_codes: set[str]) -> Decimal:
        cost = sum(
            inv.units
            * (
                self._context.po_prices.get((inv.po_number, inv.sku_code), 0)
                or self._context.allocation_sku_prices.get((inv.sku_code, inv.bob_code), 0)
            )
            for inv in self._get_active_inventory(inventory_codes)
        )
        return Decimal(cost)

    @cached_property
    def dc_inventory_cost(self) -> Decimal:
        return self._get_inventory_cost({s.bob_code for s in self._context.sites if not s.is_3pl})

    @cached_property
    def tpl_inventory_cost(self) -> Decimal:
        return self._get_inventory_cost({s.bob_code for s in self._context.sites if s.is_3pl})

    @cached_property
    def tpw_inventory_cost(self) -> Decimal:
        return self._get_inventory_cost({w.code for w in self._context.warehouses})

    @cached_property
    def total_inventory_cost(self) -> Decimal:
        return self.dc_inventory_cost + self.tpl_inventory_cost + self.tpw_inventory_cost

    @cached_property
    def purchase_qty_recommendation(self) -> dict[ScmWeek, Decimal]:
        return self._context.purchase_qty_recommendation.get(self.sku_overview.sku_code, {})

    @cached_property
    def pqr_total(self) -> Decimal:
        return sum(self.purchase_qty_recommendation.values(), Decimal()) if self.purchase_qty_recommendation else None

    @cached_property
    def ordering_week(self) -> ScmWeek | None:
        return self._context.week if self.pqr_total and self.pqr_total > 0 else None

    @cached_property
    def delivery_weeks(self) -> list[ScmWeek] | None:
        if purchase_qty_item := self.purchase_qty_recommendation:
            return list(purchase_qty_item)
        return None

    @cached_property
    def projected_shortage_week(self) -> ScmWeek | None:
        for week, value in self._topo_end_of_weeks.items():
            if value < 0:
                return week
        return None

    @cached_property
    def min_order_quantity(self) -> int:
        if supplier_split_item := self._context.supplier_splits.get(self.sku_overview.sku_code):
            return supplier_split_item.moq
        return 0

    @cached_property
    def incremental_order_quantity(self) -> int:
        if supplier_split_item := self._context.supplier_splits.get(self.sku_overview.sku_code):
            return supplier_split_item.ioq
        return 0

    @cached_property
    def projected_shortage_qty(self) -> UNITS | None:
        return self._topo_end_of_weeks[self.projected_shortage_week] if self.projected_shortage_week else None

    @cached_property
    def shelf_life_value(self) -> int | None:
        return self._context.shelf_life.get(self.sku_overview.sku_code)
