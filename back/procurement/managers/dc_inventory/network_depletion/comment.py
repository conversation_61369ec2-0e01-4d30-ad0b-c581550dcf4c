from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ActionType, NetworkDepletionCommentEvent
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.network_depletion_comment import NetworkDepletionCommentItem
from procurement.managers.admin import dc_admin
from procurement.managers.pimt import partners
from procurement.repository.inventory import network_depletion_comment

from . import preset_configuration


def get_network_depletion_comments_preview(preset_id: int, week: ScmWeek) -> list[SKU_CODE]:
    resource_id = _get_resource_id_by_preset_id(preset_id, week)
    return network_depletion_comment.get_network_depletion_comments_preview(resource_id, week)


def get_network_depletion_comment(preset_id: int, week: ScmWeek, sku_code: str) -> NetworkDepletionCommentItem:
    resource_id = _get_resource_id_by_preset_id(preset_id, week)
    return network_depletion_comment.get_network_depletion_comment(resource_id, week, sku_code)


def upsert_network_depletion_comments(preset_id: int, week: ScmWeek, sku_code: str, text: str) -> None:
    resource_id = _get_resource_id_by_preset_id(preset_id, week)
    network_depletion_comment.upsert_network_depletion_comments(resource_id, week, sku_code, text)
    EVENT_BUS.publish(NetworkDepletionCommentEvent(week=week, action_type=ActionType.UPDATED))


def delete_network_depletion_comments(preset_id: int, week: ScmWeek, sku_code: str) -> None:
    resource_id = _get_resource_id_by_preset_id(preset_id, week)
    EVENT_BUS.publish(NetworkDepletionCommentEvent(week=week, action_type=ActionType.DELETED))
    network_depletion_comment.delete_network_depletion_comments(resource_id, week, sku_code)


def _get_resource_id_by_preset_id(preset_id: int, week: ScmWeek) -> str:
    preset = preset_configuration.get_user_preset_by_id(preset_id)
    bob_codes = set()
    for brand, sites in (preset.brands or {}).items():
        site_by_name = dc_admin.get_enabled_sites(week, brand)
        for site in sites:
            if site in site_by_name:
                bob_codes.add(site_by_name[site].bob_code)
    bob_code_by_wh = {partner.code: partner.bob_code for partner in partners.get_all_partners()}
    for wh_code in preset.warehouses or []:
        bob_codes.add(bob_code_by_wh[wh_code])
    return ",".join(sorted(bob_codes))
