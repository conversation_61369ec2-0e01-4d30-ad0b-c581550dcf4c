from __future__ import annotations

import itertools
from collections import defaultdict
from collections.abc import Iterable
from datetime import date
from decimal import Decimal
from functools import cached_property

from procurement.client.googlesheets.googlesheet_utils import safe_sum
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, NOT_DELIVERED_PAST_DUE, PO_VOID
from procurement.core import utils
from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek, ScmWeekConfig
from procurement.core.typing import BULK_SKU_CODE, PACKAGED_SKU_CODE, SKU_CODE, UNITS
from procurement.data.dto.pimt.sku import IngredientDetails
from procurement.managers.depletion import core
from procurement.managers.depletion.bulk import BulkValues
from procurement.managers.depletion.constants import DAY_PASSED, NO_IMPACT
from procurement.managers.depletion.daily import DailyData, DailyDepletion
from procurement.managers.depletion.result import IngredientDepletionResult
from procurement.managers.depletion.weekly import AllocationPriceRange, DepletionWeeklyItem
from procurement.managers.imt import bulk_sku
from procurement.managers.pimt import ingredient_depletion as pimt_depletion
from procurement.managers.pimt import ingredients, partners
from procurement.managers.pimt import purchase_order as pimt_po_status
from procurement.managers.pimt.purchase_order import PoStatusData


class WarehouseDepletionContext:
    def __init__(
        self,
        wh_code: str,
        week: ScmWeek,
        week_config: ScmWeekConfig,
        non_inventory_skus: Iterable[SKU_CODE] | None = None,
    ):
        self.week = week
        self.wh_code = wh_code
        self.non_inventory_skus = frozenset(non_inventory_skus or [])
        self.warehouse = next(iter((wh for wh in partners.get_all_partners() if wh.code == wh_code)))
        self.dc_object = None  # to comply with SiteDepletionContext
        self.days = tuple(d.day for d in core.get_weekdays(week, week_config))
        self.prod_week_length = len(self.days)
        self.zeros = [0] * len(self.days)
        self.today = date.today()

    @cached_property
    def in_house_by_sku(self):
        return pimt_depletion.get_inventory_data_by_week(wh_code=self.wh_code, week=self.week)

    @cached_property
    def ingredients(self) -> tuple[IngredientDetails, ...]:
        non_inv_skus = frozenset(itertools.chain(self.non_inventory_skus, self._po_status_items.keys()))
        return ingredients.get_ingredient_details(non_inventory_sku_codes=non_inv_skus)

    @cached_property
    def bulk_skus_mapping(self) -> dict[PACKAGED_SKU_CODE, BULK_SKU_CODE]:
        return {packaged_sku: bulk.bulk_sku_code for packaged_sku, bulk in bulk_sku.get_bulk_skus_mapping().items()}

    @cached_property
    def _po_status_items(self) -> dict[SKU_CODE, list[PoStatusData]]:
        items = defaultdict(list)
        for item in pimt_po_status.get_network_depletion_inbound_po_status(self.warehouse, weeks=[self.week]):
            items[item.sku_code].append(item)
        return items

    def get_po_master_view_sku(self, sku_code: str) -> list[PoStatusData]:
        return self._po_status_items.get(sku_code, [])


class WarehouseDepletionWeeklyItem(DepletionWeeklyItem):
    def __init__(self, context: WarehouseDepletionContext, ingredient: IngredientDetails):
        self.context = context
        self.ingredient = ingredient
        self.po_master_view_sku = self.context.get_po_master_view_sku(ingredient.sku_code)

    @cached_property
    def sku_code(self) -> str:
        return self.ingredient.sku_code

    @cached_property
    def warehouse_name(self) -> str:
        return self.context.warehouse.hj_name or self.context.warehouse.bob_code

    @cached_property
    def start_of_day_inventory(self) -> None:
        return None

    @cached_property
    def units_to_produce_by_autobagger(self) -> None:
        return None

    @cached_property
    def units_needed(self) -> None:
        return None

    @cached_property
    def units_received_pre_snapshot(self) -> Decimal:
        return Decimal()

    @cached_property
    def units_received_post_snapshot(self) -> Decimal:
        return get_total_quantity_received(self.po_master_view_sku)

    @cached_property
    def units_ordered(self) -> Decimal:
        return get_units_ordered(self.po_master_view_sku)

    @cached_property
    def wip_hj_discards(self) -> None:
        return None

    @cached_property
    def hj_discards(self) -> None:
        return None

    @cached_property
    def discards(self) -> None:
        return None

    @cached_property
    def donations(self) -> int | None:
        return None

    @cached_property
    def total_on_hand(self) -> int:
        return int(self.units_received)

    @cached_property
    def wip_consumption_carryover(self) -> Decimal:
        return Decimal()

    @cached_property
    def wip_consumption_initial_pull(self) -> Decimal:
        return Decimal()

    @cached_property
    def wip_consumption_putaway_to_production(self) -> Decimal:
        return Decimal()

    @cached_property
    def in_progress_hj(self) -> None:
        return None

    @cached_property
    def awaiting_delivery(self) -> Decimal:
        return safe_sum(
            mv.quantity_ordered for mv in self.po_master_view_sku if mv.po_status in AWAITING_DELIVERY_STATUSES
        )

    @cached_property
    def not_delivered(self) -> Decimal:
        return safe_sum(mv.quantity_ordered for mv in self.po_master_view_sku if mv.po_status == NOT_DELIVERED_PAST_DUE)

    @cached_property
    def hj_units_in_house(self) -> Decimal:
        return self.context.in_house_by_sku.get(self.ingredient.sku_code, {}).get(self.context.wh_code, Decimal())

    @cached_property
    def row_need(self) -> None:
        return None

    @cached_property
    def prev_week_row_need(self) -> None:
        return None

    @cached_property
    def next_week_forecast(self) -> None:
        return None

    @cached_property
    def hj_snapshot(self) -> None:
        return None

    @cached_property
    def inventory(self) -> None:
        return None

    @cached_property
    def pulls(self) -> None:
        return None

    @cached_property
    def hj_autostore_inventory(self) -> None:
        return None

    @cached_property
    def units_on_prod_floor(self) -> None:
        return None

    @cached_property
    def allocation_price_range(self) -> AllocationPriceRange | None:
        return None


class WarehouseBulkValues(BulkValues):
    def __init__(self, context: WarehouseDepletionContext, sku_code: str):
        self.context = context
        self.sku_code = sku_code

    @cached_property
    def warehouse_name(self) -> str:
        return self.context.warehouse.hj_name or self.context.warehouse.bob_code

    @cached_property
    def po_master_view_bulk_sku(self) -> list[PoStatusData] | None:
        if not self.bulk_sku_code:
            return None
        return self.context.get_po_master_view_sku(self.bulk_sku_code)

    @cached_property
    def bulk_units_in_hj(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return self.context.in_house_by_sku.get(self.bulk_sku_code, {}).get(self.context.wh_code, Decimal())

    @cached_property
    def bulk_sku_code(self) -> str | None:
        return self.context.bulk_skus_mapping.get(self.sku_code)

    @cached_property
    def bulk_units_ordered(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return get_units_ordered(self.po_master_view_bulk_sku)

    @cached_property
    def bulk_units_received(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return get_total_quantity_received(self.po_master_view_bulk_sku)


class WarehouseIngredientDepletionResult(IngredientDepletionResult):
    def __init__(self, ingredient: IngredientDetails, context: WarehouseDepletionContext):
        self.context = context
        self.ingredient = ingredient

    @staticmethod
    def build_results(context: WarehouseDepletionContext) -> list[WarehouseIngredientDepletionResult]:
        return [
            WarehouseIngredientDepletionResult(ingredient=ingredient, context=context)
            for ingredient in context.ingredients
        ]

    @cached_property
    def brands(self) -> set[str]:
        return set()

    @cached_property
    def sites(self) -> set[str]:
        return {self.context.wh_code}

    @cached_property
    def week(self) -> ScmWeek:
        return self.context.week

    @cached_property
    def week_config(self) -> ScmWeekConfig:
        return DEFAULT_WEEK_CONFIG

    @cached_property
    def sku_code(self) -> str:
        return self.ingredient.sku_code

    @cached_property
    def sku_name(self) -> str:
        return self.ingredient.sku_name

    @cached_property
    def unit_of_measure(self) -> None:
        return None

    @cached_property
    def category(self) -> str:
        return self.ingredient.purchasing_category

    @cached_property
    def commodity_group(self) -> None:
        return None

    @cached_property
    def buyer(self) -> None:
        return None

    @cached_property
    def impacted_recipes(self) -> set[str]:
        return set()

    @cached_property
    def forecast(self) -> None:
        return None

    @cached_property
    def plan(self) -> None:
        return None

    @cached_property
    def live_row_need(self) -> None:
        return None

    @cached_property
    def row_forecast(self) -> None:
        return None

    @cached_property
    def planned_production(self) -> None:
        return None

    @cached_property
    def hj_unit(self) -> None:
        return None

    @cached_property
    def units_to_produce_by_autobagger(self) -> None:
        return None

    @cached_property
    def daily(self) -> DailyDepletion:
        return WarehouseDailyDepletion(self.context, self.sku_code)

    @cached_property
    def supplement_need(self) -> None:
        return None

    @cached_property
    def supplement_need_hj(self) -> None:
        return None

    @cached_property
    def critical_delivery_status(self) -> None:
        return None

    @cached_property
    def weekly_overview(self) -> WarehouseDepletionWeeklyItem:
        return WarehouseDepletionWeeklyItem(context=self.context, ingredient=self.ingredient)

    @cached_property
    def buffer_analysis(self) -> None:
        return None

    @cached_property
    def bulk_values(self) -> WarehouseBulkValues:
        return WarehouseBulkValues(context=self.context, sku_code=self.sku_code)

    @cached_property
    def work_order_section(self) -> None:
        return None


class WarehouseDailyDepletion(DailyDepletion):
    def __init__(self, context: WarehouseDepletionContext, sku: SKU_CODE):
        self.context = context
        self.sku = sku

    @cached_property
    def sku_code(self) -> str:
        return self.sku

    @cached_property
    def days(self) -> tuple[date, ...]:
        return self.context.days

    @cached_property
    def manual_inventory_day(self) -> int:
        return 0

    @cached_property
    def manual_form_inventory(self) -> int:
        return 0

    @cached_property
    def weekly_snapshot_day(self) -> int:
        return 0

    @cached_property
    def daily_calc(self) -> DailyData:
        return DailyData(
            units_delivered=self.units_delivered,
            discards=self.discards,
            production_need_values=self.production_needs,
            prev_eow_inventory=0,
            prod_week_length=self.context.prod_week_length,
            weekly_snapshot=0,
            weekly_snapshot_day=0,
            manual_inventory_day=self.manual_inventory_day,
            manual_form_inventory=self.manual_form_inventory,
        ).build_data()

    @cached_property
    def production_needs(self) -> list[UNITS]:
        return self.context.zeros

    @cached_property
    def live_row_need(self) -> UNITS:
        return 0

    @cached_property
    def units_on_order(self) -> list[UNITS]:
        pos = self.context.get_po_master_view_sku(self.sku)
        if not pos:
            return self.context.zeros
        pos_by_receive_date: dict[date, list[PoStatusData]] = defaultdict(list)
        for po in pos:
            if po.scheduled_delivery_date and po.po_status in AWAITING_DELIVERY_STATUSES:
                pos_by_receive_date[po.scheduled_delivery_date.date()].append(po)
        return self._get_daily_quantities(self.context.days, pos_by_receive_date, lambda po: po.quantity_ordered)

    @cached_property
    def discards(self) -> list[int]:
        return self.context.zeros

    @cached_property
    def units_delivered(self) -> list[UNITS]:
        pos = self.context.get_po_master_view_sku(self.sku)
        if not pos:
            return self.context.zeros
        pos_by_delivery_date: dict[date, list[PoStatusData]] = defaultdict(list)
        for po in pos:
            if po.date_received:
                pos_by_delivery_date[po.date_received.date()].append(po)
        res = self._get_daily_quantities(self.context.days, pos_by_delivery_date, lambda po: po.quantity_received)
        return res

    @cached_property
    def sku_pallet_quantity(self) -> Decimal:
        return Decimal()

    @cached_property
    def status_in_week(self) -> list[str]:
        res = []
        for week_index in range(self.context.prod_week_length):
            res.append(
                self._get_status_in_week_for_element(
                    week_index, self.eod_inventory_production[week_index], self.eod_inventory_order[week_index]
                )
            )
        return res

    @cached_property
    def total_on_hand(self) -> list[UNITS]:
        # Confirmed Inventory considered 0 for warehouses
        start_of_day_inventory = [0] + [None] * (len(self.units_delivered) - 1)
        return self._get_daily_total_on_hand(start_of_day_inventory, self.units_delivered, self.units_on_order)

    @cached_property
    def supplemented_need_status(self) -> list[str]:
        return [DAY_PASSED if self.context.today > day else NO_IMPACT for day in self.context.days]

    @cached_property
    def hj_supplemented_need_status(self) -> list[str]:
        return self.supplemented_need_status


def get_units_ordered(po_master_view_sku: Iterable[PoStatusData]) -> Decimal:
    return utils.ceil_subone_decimal(
        safe_sum(mv.quantity_ordered for mv in po_master_view_sku if not mv.po_status == PO_VOID)
    )


def get_total_quantity_received(po_master_view_sku: Iterable[PoStatusData]) -> Decimal:
    return utils.ceil_subone_decimal(safe_sum(mv.quantity_received for mv in po_master_view_sku))
