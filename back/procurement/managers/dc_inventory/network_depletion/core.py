from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek
from procurement.managers.admin import brand_admin
from procurement.managers.depletion import core as depl_core
from procurement.managers.depletion.core import DepletionWeekday

from . import preset_configuration


def get_weekdays(preset_id: int, week: ScmWeek) -> tuple[DepletionWeekday, ...]:
    preset = preset_configuration.get_user_preset_by_id(preset_id)
    brand = next(filter(None, preset.brands), None)
    week_config = brand_admin.get_week_config(brand) if brand else DEFAULT_WEEK_CONFIG
    return depl_core.get_weekdays(week, week_config)
