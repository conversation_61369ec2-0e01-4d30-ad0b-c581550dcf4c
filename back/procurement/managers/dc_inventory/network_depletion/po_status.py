from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.managers import po_status_common
from procurement.managers.dc_inventory.network_depletion.preset_configuration import get_user_preset_by_id
from procurement.managers.imt.purchase_order.po_status import (
    PoStatusIncludeFlags,
    PoStatusResult,
    get_po_status_by_sku_code,
)
from procurement.managers.pimt import purchase_order
from procurement.managers.pimt.partners import get_all_partners


def get_po_status(preset_id: int, week: ScmWeek, sku_code: SKU_CODE) -> list[PoStatusResult]:
    preset = get_user_preset_by_id(preset_id)
    res = []
    for brand, sites in preset.brands.items():
        for site in sites:
            res.extend(
                get_po_status_by_sku_code(
                    brand=brand,
                    site=site,
                    weeks=(week,),
                    sku_code=sku_code,
                    include_flags=PoStatusIncludeFlags(
                        include_bulk_skus=True, include_org_cv_skus=True, include_packaged_skus=False
                    ),
                )
            )
    for warehouse in [wh for wh in get_all_partners() if wh.code in preset.warehouses]:
        res.extend(
            po_status_common.convert_po_status_data_to_result(
                purchase_order.get_po_data_by_sku_code([week], warehouse, sku_code)
            )
        )

    return res
