import logging
from collections import defaultdict
from collections.abc import Iterable

from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.typing import SKU_CODE
from procurement.data.dto.procurement.network_depletion import UserPreset
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.depletion.result import AggregatedIngredientDepletionResult
from procurement.managers.imt.ingredients_depletion.core import IngredientDepletionContext
from procurement.managers.imt.ingredients_depletion.result import SiteIngredientDepletionResult
from procurement.managers.pimt import partners

from . import preset_configuration
from .pimt_depletion import WarehouseDepletionContext, WarehouseIngredientDepletionResult

logger = logging.getLogger(__name__)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_network_depletion(week: ScmWeek, preset_id: int) -> list[AggregatedIngredientDepletionResult]:
    preset = preset_configuration.get_user_preset_by_id(preset_id)
    return build_dashboard(week, preset)


def build_dashboard(week: ScmWeek, preset: UserPreset) -> list[AggregatedIngredientDepletionResult]:
    imt_depletion_items_by_sku = _get_imt_depletion(week, preset)
    # add SKUs with demand from IMT to PIMT context as they might not have inventory in warehouses,
    # but we still want to build depletion for them to aggregate other values
    pimt_depletion_items_by_sku = _get_pimt_depletion(week, preset, imt_depletion_items_by_sku.keys())

    agg_depl = (
        AggregatedIngredientDepletionResult(
            child_ing_depl=imt_depletion_items_by_sku.get(sku, []) + pimt_depletion_items_by_sku.get(sku, [])
        )
        for sku in imt_depletion_items_by_sku
    )
    # removing aggregated rows where we got no plan and forecast values at the end
    # because site's depletion part also includes SKUs with no demand
    return [it for it in agg_depl if it.plan or it.forecast]


def _get_imt_depletion(week: ScmWeek, preset: UserPreset) -> dict[SKU_CODE, list[SiteIngredientDepletionResult]]:
    depletion_items_by_sku = defaultdict(list)
    available_brands = brand_admin.get_brands(week)
    actual_preset = [(brand, sites) for brand, sites in preset.brands.items() if brand in available_brands]
    for brand, sites in actual_preset:
        available_sites = dc_admin.get_enabled_sites(week, brand)
        _sites = filter(None, map(available_sites.get, sites))
        items = _get_ingredient_depl_data(week, brand, _sites)
        for item in items:
            depletion_items_by_sku[item.sku_code].append(item)
    return depletion_items_by_sku


def _get_ingredient_depl_data(
    week: ScmWeek, brand: str, sites: Iterable[DcConfig]
) -> list[SiteIngredientDepletionResult]:
    return [
        item
        for site in sites
        for item in SiteIngredientDepletionResult.build_results(
            context=IngredientDepletionContext(
                week=week,
                site=site.sheet_name,
                brand=brand,
                include_bulk_skus=True,
                include_no_demand_skus=True,
            )
        )
    ]


def _get_pimt_depletion(
    week: ScmWeek, preset: UserPreset, imt_skus_with_demand: Iterable[SKU_CODE]
) -> dict[SKU_CODE, list[WarehouseIngredientDepletionResult]]:
    depletion_items_by_sku = defaultdict(list)

    available_warehouses = set(wh.code for wh in partners.get_all_partners()).intersection(preset.warehouses)
    week_config = brand_admin.get_week_config(next(iter(preset.brands))) if preset.brands else DEFAULT_WEEK_CONFIG
    for warehouse in available_warehouses:
        context = WarehouseDepletionContext(
            wh_code=warehouse, week=week, week_config=week_config, non_inventory_skus=imt_skus_with_demand
        )
        items = WarehouseIngredientDepletionResult.build_results(context)
        for item in items:
            depletion_items_by_sku[item.sku_code].append(item)

    return depletion_items_by_sku
