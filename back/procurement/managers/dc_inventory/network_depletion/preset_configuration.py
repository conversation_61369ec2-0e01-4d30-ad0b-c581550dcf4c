from typing import NamedTuple

from procurement.constants.hellofresh_constant import (
    BRAND_FJ,
    SITE_AC,
    SITE_AG,
    SITE_AU,
    SITE_BO,
    SITE_BR,
    SITE_UB,
    WAREHOUSE_JOLIET,
)
from procurement.core.dates.weeks import ScmWeek, ScmWeekConfig
from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, SITE, WH_CODE
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.pimt import partners
from procurement.repository.procurement import network_depletion_preset as preset_repo
from procurement.repository.procurement.network_depletion_preset import UserPreset

COUNT_OF_USER_PRESETS = 3

DEFAULT_PRESETS = [
    UserPreset(preset_id=_id, name=name, brands={}, warehouses=[])
    for _id, name in enumerate(["Preset 1", "Preset 2", "Preset 3"], start=1)
]

FACTOR_PRESET = UserPreset(
    preset_id=1, name="Factor Preset", brands={BRAND_FJ: [SITE_AU, SITE_BR]}, warehouses=[WAREHOUSE_JOLIET]
)

FACTOR_AG_PRESET = UserPreset(preset_id=2, name="Factor Preset", brands={BRAND_FJ: [SITE_AG]}, warehouses=[])

FACTOR_AC_PRESET = UserPreset(preset_id=3, name="Factor Preset", brands={BRAND_FJ: [SITE_AC]}, warehouses=[])

FACTOR_UB_PRESET = UserPreset(preset_id=4, name="Factor Preset", brands={BRAND_FJ: [SITE_UB]}, warehouses=[])

FACTOR_BO_PRESET = UserPreset(preset_id=5, name="Factor Preset", brands={BRAND_FJ: [SITE_BO]}, warehouses=[])


class PresetFormBrand(NamedTuple):
    brand_code: str
    brand_name: str
    scm_week_config: ScmWeekConfig
    sites: list[SITE]


class PresetForm(NamedTuple):
    brands: list[PresetFormBrand]
    warehouses: list[WH_CODE]


def get_preset_form(week: ScmWeek) -> PresetForm:
    brands_info = [
        PresetFormBrand(
            brand_code=brand_id,
            brand_name=brand_info.name,
            scm_week_config=brand_info.scm_week_config,
            sites=[site.sheet_name for site in dc_admin.get_enabled_sites(week, brand_id).values()],
        )
        for brand_id, brand_info in brand_admin.get_brands(week).items()
    ]
    warehouses = [warehouse.code for warehouse in partners.get_all_partners()]
    return PresetForm(brands=brands_info, warehouses=warehouses)


def get_user_presets() -> list[UserPreset]:
    request_context = context.get_request_context()
    user_presets = preset_repo.get_user_presets(
        user_id=request_context.user_info.user_id, market=request_context.market
    )
    if not user_presets:
        return DEFAULT_PRESETS
    # if user has less than 3 presets
    if user_presets and len(user_presets) < COUNT_OF_USER_PRESETS:
        user_presets_ids = {item.preset_id for item in user_presets}
        user_presets.extend([item for item in DEFAULT_PRESETS if item.preset_id not in user_presets_ids])
        return user_presets
    return user_presets


def get_user_preset_by_id(preset_id: int) -> UserPreset:
    request_context = context.get_request_context()
    preset = preset_repo.get_user_preset_by_id(
        user_id=request_context.user_info.user_id,
        preset_id=preset_id,
        market=request_context.market,
    )
    return preset or next(iter(DEFAULT_PRESETS))


def update_presets(presets: list[UserPreset]) -> None:
    _validate_week_config(presets)
    request_context = context.get_request_context()
    user_id = request_context.user_info.user_id
    market = request_context.market
    user_presets = preset_repo.get_user_presets(user_id=user_id, market=market)
    # adding default presets for users which have less than 3 presets
    if len(user_presets) != COUNT_OF_USER_PRESETS:
        user_presets_ids = {item.preset_id for item in user_presets}
        user_presets_ids.update(item.preset_id for item in presets)
        presets.extend(item for item in DEFAULT_PRESETS if item.preset_id not in user_presets_ids)
    preset_repo.update_presets(presets, user_id, market)


def _validate_week_config(presets: list[UserPreset]):
    brands = brand_admin.get_latest_brands()
    for preset in presets:
        scm_week_configs = {brands[preset_brand].scm_week_config for preset_brand in preset.brands}
        if len(scm_week_configs) > 1:
            raise InputParamsValidationException("Sites with different week configs cannot be selected.")


def get_bob_codes_for_preset(preset_id: int) -> list[BOB_CODE]:
    bob_codes_by_brand_site = {(dc.brand, dc.sheet_name): dc.bob_code for dc in dc_admin.get_all_market_dcs()}
    bob_codes_by_wh_codes = {wh.code: wh.bob_code for wh in partners.get_all_partners()}
    preset = get_user_preset_by_id(preset_id)
    bob_codes = []

    for brand, sites in preset.brands.items():
        for site in sites:
            bob_codes.append(bob_codes_by_brand_site[(brand, site)])

    for wh in preset.warehouses:
        bob_codes.append(bob_codes_by_wh_codes[wh])
    return bob_codes
