import dataclasses
import itertools
import math
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from functools import cached_property

from procurement.constants.hellofresh_constant import (
    APP_TIMEZONE,
    BRAND_FJ,
    MARKET_CA,
    MARKET_US,
    IngredientCategory,
    ReplenishmentType,
)
from procurement.constants.ordering import HJ_CLOSED_STATUSES
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import (
    BRAND,
    CATEGORY_NAME,
    PURCHASING_CATEGORY_NAME,
    SITE,
    SKU_CODE,
    SKU_NAME,
    SUPPLIER,
    UNITS,
    WH_CODE,
)
from procurement.data.dto.forecasts.forecasts import Forecast
from procurement.data.dto.inventory.inventory import Inventory
from procurement.data.dto.ordering.culinary_sku import CulinarySku
from procurement.data.dto.ordering.purchase_order.incoming import IncomingOrder
from procurement.data.dto.procurement.inventory_module import (
    InventoryModuleComment,
    InventoryModuleCommentInput,
    InventoryModuleInHouseAdjustment,
    InventoryModuleInHouseAdjustmentInput,
)
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.dates import WeekByDateFinder
from procurement.managers.distribution_center import highjump
from procurement.managers.forecasts import forecast_manager
from procurement.managers.forms import receive as receive_service
from procurement.managers.imt import buyers as buyers_service
from procurement.managers.imt import production_need as production_need_service
from procurement.managers.imt import production_needs_packaging as production_needs_packaging_service
from procurement.managers.imt.ingredients_depletion.core import (
    CA_EXPIRING_INVENTORY_EXCLUDE_DAYS,
    US_EXPIRING_INVENTORY_EXCLUDE_DAYS,
)
from procurement.managers.inventory import imt as imt_inventory
from procurement.managers.models import ExpirationInfo
from procurement.managers.ordering import service as po_service
from procurement.managers.pimt import ingredient_depletion as pimt_ing_dep_service
from procurement.managers.pimt import inventory as pimt_inventory_service
from procurement.managers.pimt import partners as partner_service
from procurement.managers.receipt import grn
from procurement.repository.inventory import cycle_counts
from procurement.repository.ordering import culinary_sku as sku_repo
from procurement.repository.ordering.purchase_order import pimt_pos
from procurement.repository.procurement import inventory_module as inventory_repo

EXPIRATION_PERIOD = 90
FUTURE_WEEKS = 9
FUTURE_ON_HAND_WEEKS = 6
EXPIRATION_OFFSET = 9
TEN_DAYS = timedelta(days=10)


class InventoryData:
    """Holds data from different Inventories"""

    def __init__(self, dc_obj: DcConfig, week_range: list[ScmWeek]):
        self._dc_obj = dc_obj

        self.bod_inventory_today: dict[SKU_CODE, UNITS] = defaultdict(int)
        self.bod_inventory_yesterday: dict[SKU_CODE, UNITS] = defaultdict(int)
        self.bod_expiring_by_week_sku_code: dict[SKU_CODE, dict[ScmWeek, UNITS]] = defaultdict(lambda: defaultdict(int))

        self.current_inventory: dict[SKU_CODE, UNITS] = defaultdict(int)
        self.unique_license_count_by_sku: dict[SKU_CODE, UNITS] = defaultdict(int)
        self.expiring_dc_by_sku_code: dict[SKU_CODE, ExpirationInfo] = defaultdict(ExpirationInfo)

        self.week_config = brand_admin.get_week_config(self._dc_obj.brand)
        self._week_finder: WeekByDateFinder = WeekByDateFinder(
            week_range, self.week_config, offset=EXPIRATION_OFFSET, extend_first_week_to_left=True
        )
        self._today: date = date.today()
        self._end_of_expiration_period = self._today + timedelta(days=EXPIRATION_PERIOD)
        self.market = context.get_request_context().market

        self._init_bod_data()
        self._init_current_data()

    @cached_property
    def bod_inventory(self) -> list[Inventory]:
        date_to = date.today()
        date_from = date_to - timedelta(days=1)
        if self._dc_obj.inventory_type.is_high_jump:
            return highjump.get_bod_snapshots(dc_object=self._dc_obj, date_from=date_from, date_to=date_to)
        if self._dc_obj.inventory_type.is_cycle_count:
            return cycle_counts.get_bod_cycle_counts_inventory(brand=self._dc_obj.brand, site=self._dc_obj.sheet_name)
        if self._dc_obj.inventory_type.is_in_unified_inventory:
            return imt_inventory.get_latest_inventory(self._dc_obj.bob_code, self._dc_obj.inventory_type)
        return []

    @cached_property
    def _current_inventory(self) -> list[Inventory]:
        if self._dc_obj.inventory_type.is_high_jump:
            return highjump.get_current_hj_pallets_inventory(
                dc_object=self._dc_obj, week=ScmWeek.current_week(self.week_config)
            )
        if self._dc_obj.inventory_type.is_cycle_count:
            return cycle_counts.get_current_cycle_count_inventory(
                brand=self._dc_obj.brand, site=self._dc_obj.sheet_name
            )
        if self._dc_obj.inventory_type.is_in_unified_inventory:
            return self.bod_inventory
        return []

    def _init_bod_data(self) -> None:
        for record in self.bod_inventory:
            if record.quantity <= 0 or record.expiration_date and record.expiration_date < self._today:
                continue
            if record.snapshot_timestamp == self._today or self._dc_obj.is_3pl:
                self.bod_inventory_today[record.sku_code] += record.quantity
                if exp_week := self._week_finder.get_week(record.expiration_date):
                    self.bod_expiring_by_week_sku_code[record.sku_code][exp_week] += record.quantity
            else:
                self.bod_inventory_yesterday[record.sku_code] += record.quantity

    def _init_current_data(self) -> None:
        current_inventory = defaultdict(list)
        for inv in self._current_inventory:
            current_inventory[inv.sku_code].append(inv)

        for sku_code, records in current_inventory.items():
            self._process_expiration_date(records)
            if not self._dc_obj.is_3pl:
                self.unique_license_count_by_sku[sku_code] += sum(r.license_count or 0 for r in records)
            self.current_inventory[sku_code] += self._get_non_expiring_pallet_qty(sku_code, records)

    @cached_property
    def _ingredient_category_by_sku_code(self) -> dict[SKU_CODE, CATEGORY_NAME]:
        return sku_repo.get_culinary_category_by_sku_codes(
            sku_codes={item.sku_code for item in self._current_inventory}
        )

    def _is_pallet_expiring(self, sku_code: str, expiration_date: date) -> bool:
        expiring_inventory_exclude_days = (
            CA_EXPIRING_INVENTORY_EXCLUDE_DAYS if self.market != MARKET_US else US_EXPIRING_INVENTORY_EXCLUDE_DAYS
        )
        sku_category = self._ingredient_category_by_sku_code.get(sku_code)
        return (
            self._dc_obj.brand != BRAND_FJ
            and self.market != MARKET_CA
            and expiration_date
            and (not sku_category or sku_category != IngredientCategory.PRODUCE)
            and self._today + timedelta(days=expiring_inventory_exclude_days) >= expiration_date
        )

    def _get_non_expiring_pallet_qty(self, sku_code: str, sku_inventory: list[Inventory]) -> UNITS:
        pallet_quantity = Decimal()
        for inv in sku_inventory:
            if not self._is_pallet_expiring(sku_code, inv.expiration_date):
                pallet_quantity += utils.ceil_subone_decimal(inv.quantity)
        return pallet_quantity

    def _process_expiration_date(self, records: Iterable[Inventory]) -> None:
        for record in records:
            if not record.expiration_date or record.expiration_date > self._end_of_expiration_period:
                continue
            self.expiring_dc_by_sku_code[record.sku_code].total += record.quantity
            self.expiring_dc_by_sku_code[record.sku_code].min_expiration_date = utils.min_of_two(
                self.expiring_dc_by_sku_code[record.sku_code].min_expiration_date, record.expiration_date
            )


class TpwData:
    """Calculates 3PW data for whole dashboard"""

    def __init__(self, sites: list[str]):
        self.sites = sites
        self.wh_codes = []
        self.supplier_codes = []
        self.wh_bob_codes = []
        self.whs_by_site = defaultdict(list)
        self.suppliers_by_site = defaultdict(list)
        self.in_house_by_partner_sku: dict[WH_CODE, dict[SKU_CODE, Decimal]] = defaultdict(lambda: defaultdict(Decimal))
        self.network_in_house_by_sku: dict[SKU_CODE, Decimal] = defaultdict(Decimal)
        self._calculate_partners()
        self._calculate_in_house()

    def _calculate_partners(self) -> None:
        for wh in partner_service.get_all_partners():
            if any(site in wh.regional_dcs for site in self.sites):
                self.wh_codes.append(wh.code)
                self.supplier_codes.extend(wh.ot_suppliers)
                self.wh_bob_codes.append(wh.bob_code)
                for site in wh.regional_dcs:
                    self.whs_by_site[site].append(wh.code)
                    self.suppliers_by_site[site].extend(wh.ot_suppliers)

    def _calculate_in_house(self) -> None:
        """Calculate in house per partner"""
        for sku_code, in_house_by_partner in pimt_ing_dep_service.get_inventory_data().items():
            for wh_code, in_house in in_house_by_partner.items():
                self.network_in_house_by_sku[sku_code] += in_house
                if wh_code in self.wh_codes:
                    self.in_house_by_partner_sku[wh_code][sku_code] += in_house

    @cached_property
    def expiring_by_partner_sku(self) -> dict[WH_CODE, dict[SKU_CODE, ExpirationInfo]]:
        """Calculate expiring inventory per partner"""
        expiring_by_partner_sku_dict = defaultdict(dict)
        pimt_expiring_by_sku_partner = pimt_inventory_service.get_expiring_in(days=EXPIRATION_PERIOD)
        for key, expiring in pimt_expiring_by_sku_partner.items():
            expiring_by_partner_sku_dict[key.wh][key.sku_code] = expiring
        return expiring_by_partner_sku_dict

    @cached_property
    def outbound_by_supplier_sku(self) -> dict[SUPPLIER, dict[SKU_CODE, Decimal]]:
        """Calculate outbound per supplier"""
        outbound_by_supplier_sku_dict = defaultdict(lambda: defaultdict(Decimal))
        for item in pimt_pos.get_pimt_outbound_orders(self.supplier_codes, self.wh_bob_codes, date.today()):
            outbound_by_supplier_sku_dict[item.supplier][item.sku_code] += item.total_quantity
        return outbound_by_supplier_sku_dict


@dataclasses.dataclass
class IngredientSummary:
    sku: SKU_CODE
    sku_name: SKU_NAME = None
    category: PURCHASING_CATEGORY_NAME = None
    storage_location: str = None
    buyer: str | None = None
    commodity_group: str | None = None
    is_replenishment_sku: bool = False


@dataclasses.dataclass
class SupplementNeed:
    week: ScmWeek
    qty: int


@dataclasses.dataclass
class AvailableInventorySummary:
    weeks_on_hand: int
    pallets_on_hand: int | None
    supplement_need: SupplementNeed
    dc_expiring_inventory: UNITS
    units_in_house_hj: UNITS
    less_than_ten_days_expiration: date | None


@dataclasses.dataclass
class TpwInventory:
    units_in_house_today: Decimal
    units_in_house_today_network: Decimal
    tpw_expiring_inventory: Decimal
    tpw_outbound: Decimal
    less_than_ten_days_expiration: date | None
    tpw_weeks_on_hand: int


@dataclasses.dataclass
class WeekInventory:
    inbound: Decimal
    units_expiring: int
    remaining_inventory: int
    start_of_week_inventory: int
    row_need: Decimal
    week: ScmWeek


@dataclasses.dataclass
class CurrentWeekInventory(WeekInventory):
    units_in_house_yesterday: int
    received: int
    previous_week_row_need: Decimal
    units_in_house_adjustment: int
    undelivered_past_due_pos: Decimal


@dataclasses.dataclass
class Comment:
    text: str
    last_edited_by: str


@dataclasses.dataclass
class SkuInventoryModule:
    ingredient_summary: IngredientSummary
    available_inventory_summary: AvailableInventorySummary
    tpw_inventory: TpwInventory
    current_week_inventory: CurrentWeekInventory
    upcoming_weeks_inventories: list[WeekInventory]
    comment: Comment


@dataclasses.dataclass
class InventorySkuContext:
    inventory_today: int
    inventory_yesterday: int
    expiring_by_week: dict[ScmWeek, int]


@dataclasses.dataclass
class WeekInventorySkuContext:
    start_of_week_inventory: int
    inbounds_by_week: dict[ScmWeek, Decimal]
    production_needs_by_week: dict[ScmWeek, UNITS]
    expiring_by_week: dict[ScmWeek, int]


@dataclasses.dataclass
class CurrentWeekInventorySkuContext:
    weeks_range: list[ScmWeek]
    inventory: InventorySkuContext
    sku_code: SKU_CODE
    inbounds_by_week: dict[ScmWeek, Decimal]
    undelivered_past_due_pos: Decimal
    received: int
    production_needs_by_week: dict[ScmWeek, UNITS]
    in_house_adjustment: int


@dataclasses.dataclass
class TpwInventorySkuContext:
    in_house_by_sku_code: dict[SKU_CODE, Decimal]
    outbound_by_sku_code: dict[SKU_CODE, Decimal]
    avg_forecast_by_sku_code: dict[SKU_CODE, Decimal]
    expiring_inventory_by_sku_code: dict[SKU_CODE, ExpirationInfo]
    network_in_house_by_sku_code: dict[SKU_CODE, Decimal]


class InventoryModuleSite:
    """Prepare data for site"""

    def __init__(
        self,
        dc_object: DcConfig,
        weeks_range: list[ScmWeek],
        tpw_data: TpwData,
    ):
        self.dc_object = dc_object
        self.weeks_range = weeks_range
        self.weeks_on_hand_range = weeks_range[1 : FUTURE_ON_HAND_WEEKS + 1]
        self.tpw_data = tpw_data

        self.forecasts_by_sku_code = _get_forecasts_by_sku_codes(self._get_forecast_by_weeks())
        self.avg_forecasts_by_sku_code = self._get_avg_forecasts_by_sku_codes()
        self.inbounds_by_sku_code = self._calculate_inbounds()
        self.undelivered_past_due_pos_by_sku_code = self._calculate_undelivered_past_due_pos()

        self.inv_data = InventoryData(dc_object, self.weeks_range)

    @cached_property
    def ingredient_summary_by_sku_code(self) -> dict[SKU_CODE, IngredientSummary]:
        """Gather information about skus"""

        culinary_sku_query = _get_culinary_skus(
            site=self.dc_object.sheet_name,
            inventory_skus={item.sku_code for item in self.inv_data.bod_inventory},
            inbound_sku_codes={item.sku_code for item in self.inbounds},
            forecast_sku_codes=set(self.forecasts_by_sku_code.keys()),
        )

        buyers = buyers_service.get_buyers_filter_by_sku_codes(
            brand=self.dc_object.brand,
            site=self.dc_object.sheet_name,
            sku_codes=[item.sku_code for item in culinary_sku_query],
        )
        return {
            culinary.sku_code: IngredientSummary(
                sku=culinary.sku_code,
                sku_name=culinary.sku_name,
                category=culinary.purchasing_category,
                storage_location=culinary.storage_location,
                buyer=buyers.get(culinary.sku_code),
                commodity_group=culinary.commodity_group,
                is_replenishment_sku=culinary.replenishment_type == ReplenishmentType.REPLENISHMENT,
            )
            for culinary in culinary_sku_query
        }

    @cached_property
    def tpw_inv_by_sku_code(self) -> dict[SKU_CODE, TpwInventory]:
        """Construct site's information about its partners."""
        whs = self.tpw_data.whs_by_site[self.dc_object.sheet_name]
        suppliers = self.tpw_data.suppliers_by_site[self.dc_object.sheet_name]

        in_house_by_sku_code = _calculate_tpw_in_house(whs, self.tpw_data)

        expiring_inventory_by_sku_code = _calculate_tpw_expiring(whs, self.tpw_data)

        outbound_by_sku_code = _calculate_tpw_outbound(suppliers, self.tpw_data)

        return _build_tpw_inventory(
            TpwInventorySkuContext(
                in_house_by_sku_code=in_house_by_sku_code,
                avg_forecast_by_sku_code=self.avg_forecasts_by_sku_code,
                outbound_by_sku_code=outbound_by_sku_code,
                expiring_inventory_by_sku_code=expiring_inventory_by_sku_code,
                network_in_house_by_sku_code=self.tpw_data.network_in_house_by_sku,
            )
        )

    @cached_property
    def production_needs_by_sku_code(self) -> dict[SKU_CODE, dict[ScmWeek, UNITS]]:
        """Build production needs(RestOfWeek need) for each sku"""
        production_needs_by_sku_code = defaultdict(dict)
        week_config = brand_admin.get_week_config(self.dc_object.brand)
        current_week = ScmWeek.current_week(week_config)
        production_needs_weeks = itertools.takewhile(lambda w: w <= current_week, self.weeks_range)
        forecast_weeks = itertools.dropwhile(lambda w: w <= current_week, self.weeks_range)

        for week in [self.weeks_range[0] - 1, *production_needs_weeks]:
            prod_needs = itertools.chain(
                production_need_service.get_production_need(
                    self.dc_object.sheet_name, week, self.dc_object.brand
                ).values(),
                production_needs_packaging_service.get_production_needs_packaging(
                    self.dc_object.sheet_name, week, self.dc_object.brand
                ),
            )
            for item in prod_needs:
                production_needs_by_sku_code[item.sku_code][week] = item.row_need
        for week in forecast_weeks:
            for sku_code, week_forecast in self.forecasts_by_sku_code.items():
                if forecast := week_forecast.get(week):
                    production_needs_by_sku_code[sku_code][week] = forecast

        return production_needs_by_sku_code

    @cached_property
    def received_by_sku_code(self) -> dict[SKU_CODE, int]:
        dc_timezone = self.dc_object.timezone
        date_from = dc_timezone.localize(self.receiving_date_from).astimezone(APP_TIMEZONE)
        date_to = dc_timezone.localize(datetime.combine(datetime.today(), time())).astimezone(APP_TIMEZONE) + timedelta(
            days=1
        )

        if self.dc_object.receiving_type.is_high_jump:
            if not killswitch.hj_grn_enabled:
                date_from = date_from.replace(tzinfo=None)
                date_to = date_to.replace(tzinfo=None)
            return self._get_hj_receives(date_from=date_from, date_to=date_to)

        if self.dc_object.receiving_type.is_manual:
            return self._get_manual_receives(
                date_from=date_from.replace(tzinfo=None), date_to=date_to.replace(tzinfo=None)
            )

        if self.dc_object.receiving_type.is_in_grn:
            return self._get_grn_receives(date_from=date_from, date_to=date_to)
        return {}

    @cached_property
    def receiving_date_from(self) -> datetime:
        if self.dc_object.inventory_type.is_cycle_count:
            date_from = datetime.combine(
                cycle_counts.get_latest_snapshot_date(self.dc_object.brand, self.dc_object.sheet_name) or date.today(),
                time(),
            )
        elif self.dc_object.inventory_type.is_in_unified_inventory:
            date_from = imt_inventory.get_latest_inventory_date(self.dc_object.bob_code, self.dc_object.inventory_type)
        else:
            date_from = datetime.combine(datetime.today(), time())

        return date_from + timedelta(seconds=1)

    def _get_hj_receives(self, date_from: datetime, date_to: datetime) -> dict[SKU_CODE, int]:
        receive_by_sku_code = defaultdict(int)
        hj_receipts = highjump.get_hj_receipt_by_receive_date(self.dc_object, date_from, date_to)
        for po_sku, receipts in hj_receipts.items():
            receive_by_sku_code[po_sku[1]] += sum(
                receipt.quantity_received for receipt in receipts if receipt.status in HJ_CLOSED_STATUSES
            )
        return receive_by_sku_code

    def _get_manual_receives(self, date_from: datetime, date_to: datetime) -> dict[SKU_CODE, int]:
        receive_by_sku_code = defaultdict(int)
        receipts = receive_service.get_receiving_by_date(self.dc_object, date_from, date_to)

        for po_sku, receipts in receipts.items():
            receive_by_sku_code[po_sku[1]] += sum(
                receive_service.get_manual_receive_row_total_qty(receipt) for receipt in receipts
            )
        return receive_by_sku_code

    def _get_grn_receives(self, date_from: datetime, date_to: datetime) -> dict[SKU_CODE, int]:
        receive_by_sku_code = defaultdict(int)
        grn_receipts = grn.get_grn_receipt_by_receive_date(self.dc_object, date_from, date_to)
        for order_sku, receipts in grn_receipts.items():
            receive_by_sku_code[order_sku[1]] += sum(receipt.units_received for receipt in receipts)
        return receive_by_sku_code

    @cached_property
    def comments_by_sku_code(self) -> dict[SKU_CODE, InventoryModuleComment]:
        return {
            comment.sku_code: comment
            for comment in inventory_repo.get_comments_for_brand_and_site(
                self.dc_object.brand, self.dc_object.sheet_name
            )
        }

    @cached_property
    def in_house_adjustment_by_sku_code(self) -> dict[SKU_CODE, int]:
        return {
            in_house_adjustment.sku_code: in_house_adjustment.value
            for in_house_adjustment in inventory_repo.get_in_house_adjustment_for_brand_and_site(
                self.dc_object.brand, self.dc_object.sheet_name
            )
        }

    @cached_property
    def inbounds(self) -> list[IncomingOrder]:
        current_week = self.weeks_range[0]
        last_week = self.weeks_range[-1]
        week_config = brand_admin.get_week_config(self.dc_object.brand)
        date_from = current_week.get_first_day(week_config)
        date_to = last_week.get_last_day(week_config) + timedelta(days=1)
        return po_service.get_incoming_orders_excluding_receives(
            self.dc_object, date_from, date_to, only_early_receives=False
        )

    @property
    def site_info(self) -> list[SkuInventoryModule]:
        """Get records for site"""
        return self._build_site_dashboard()

    def _get_forecast_by_weeks(self):
        result = defaultdict(list)
        forecast = forecast_manager.get_forecast_manager()
        forecasts_by_week = forecast.get_forecast_by_weeks(
            site=self.dc_object.sheet_name, weeks=self.weeks_range, brand=self.dc_object.brand
        )
        forecasts_packaging_by_week = forecast.get_packaging_forecast_by_weeks(
            site=self.dc_object.sheet_name, weeks=self.weeks_range, brand=self.dc_object.brand
        )
        for week in self.weeks_range:
            if forecasts := forecasts_by_week.get(week):
                result[week] += forecasts
            if packaging_forecasts := forecasts_packaging_by_week.get(week):
                result[week] += packaging_forecasts
        result.default_factory = None
        return result

    def _calculate_inbounds(self) -> dict[SKU_CODE, dict[ScmWeek, Decimal]]:
        current_week = self.weeks_range[0]
        last_week = self.weeks_range[-1]
        week_config = brand_admin.get_week_config(self.dc_object.brand)
        po_by_sku_code_and_week = defaultdict(lambda: defaultdict(Decimal))
        week_finder = WeekByDateFinder(
            list(ScmWeek.range(current_week, last_week)), week_config, extend_first_week_to_left=True
        )

        for record in self.inbounds:
            po_by_sku_code_and_week[record.sku_code][
                week_finder.get_week(record.delivery_time_start.date())
            ] += record.quantity
        return po_by_sku_code_and_week

    def _calculate_undelivered_past_due_pos(self) -> dict[SKU_CODE, Decimal]:
        po_count_by_sku_code = defaultdict(Decimal)
        today = date.today()

        for record in self.inbounds:
            if record.delivery_time_start.date() < today:
                po_count_by_sku_code[record.sku_code] += record.quantity
        return po_count_by_sku_code

    def _build_site_dashboard(self) -> list[SkuInventoryModule]:
        sku_codes = set(
            itertools.chain(
                self.forecasts_by_sku_code.keys(),
                self.inbounds_by_sku_code.keys(),
                self.inv_data.bod_inventory_today.keys(),
                self.inv_data.current_inventory.keys(),
            )
        )

        return [
            self._build_sku_inventory(sku_code)
            for sku_code in sku_codes
            if sku_code in self.ingredient_summary_by_sku_code
        ]

    def _build_sku_inventory(self, sku_code: SKU_CODE) -> SkuInventoryModule:
        """Build one record of the dashboard"""
        inventory = InventorySkuContext(
            inventory_today=self.inv_data.bod_inventory_today.get(sku_code, 0),
            inventory_yesterday=self.inv_data.bod_inventory_yesterday.get(sku_code, 0),
            expiring_by_week=self.inv_data.bod_expiring_by_week_sku_code.get(sku_code, {}),
        )
        current_week_inventory_sku_context = CurrentWeekInventorySkuContext(
            weeks_range=self.weeks_range,
            inventory=inventory,
            sku_code=sku_code,
            inbounds_by_week=self.inbounds_by_sku_code.get(sku_code, {}),
            undelivered_past_due_pos=self.undelivered_past_due_pos_by_sku_code.get(sku_code, 0),
            received=self.received_by_sku_code.get(sku_code, 0),
            production_needs_by_week=self.production_needs_by_sku_code.get(sku_code, {}),
            in_house_adjustment=self.in_house_adjustment_by_sku_code.get(sku_code),
        )
        inventory_current_week, inventory_next_weeks = _build_weeks_inventory(
            self.dc_object.brand,
            current_week_inventory_sku_context,
        )
        available_inventory_summary = _build_available_inventory_summary(
            sku_code,
            self.inv_data,
            self.forecasts_by_sku_code,
            [inventory_current_week, *inventory_next_weeks],
        )
        return SkuInventoryModule(
            ingredient_summary=self.ingredient_summary_by_sku_code.get(sku_code, IngredientSummary(sku=sku_code)),
            available_inventory_summary=available_inventory_summary,
            current_week_inventory=inventory_current_week,
            tpw_inventory=self.tpw_inv_by_sku_code.get(sku_code),
            upcoming_weeks_inventories=inventory_next_weeks,
            comment=_build_comment(self.comments_by_sku_code.get(sku_code)),
        )

    def _get_avg_forecasts_by_sku_codes(self) -> dict[SKU_CODE, Decimal]:
        forecasts_avg_by_sku_code = {}
        for sku_code, forecast_items in self.forecasts_by_sku_code.items():
            weeks_forecasts = [forecast_items.get(w, Decimal()) for w in self.weeks_on_hand_range]
            forecasts_avg_by_sku_code[sku_code] = Decimal(sum(weeks_forecasts) / len(weeks_forecasts))
        return forecasts_avg_by_sku_code


class InventoryModuleDashboard:
    """Represents Whole dashboard"""

    def __init__(self, brand: str, sites: list[str]):
        self.brand = brand
        self.sites = sites

    def get_inventory_by_site(self) -> dict[SITE, list[SkuInventoryModule]]:
        """Builds Records by requested sites"""
        week_config = brand_admin.get_week_config(self.brand)
        current_week = ScmWeek.current_week(week_config)
        dc_objects_by_sites = dc_admin.get_enabled_sites(week=current_week, brand=self.brand)
        weeks_range = list(ScmWeek.range(current_week, current_week + FUTURE_WEEKS))

        tpw_data = TpwData(self.sites)

        return {
            site: InventoryModuleSite(dc_objects_by_sites[site], weeks_range, tpw_data).site_info for site in self.sites
        }


def _calculate_supplement_need(
    week_inventories: list[WeekInventory],
) -> SupplementNeed | None:
    for inventory in week_inventories:
        if inventory.remaining_inventory < 0:
            return SupplementNeed(week=inventory.week, qty=abs(inventory.remaining_inventory))
    return None


def _build_available_inventory_summary(
    sku_code: SKU_CODE,
    inventory: InventoryData,
    forecasts: dict[SKU_CODE, dict[ScmWeek, UNITS]],
    week_inventories: list[WeekInventory],
) -> AvailableInventorySummary:
    """Build information about sku availability for all dashboard weeks"""
    ten_days_from_now = date.today() + TEN_DAYS
    forecast = forecasts.get(sku_code, {}).values()
    total_forecast = sum(forecast)
    if total_forecast:
        weeks_on_hand = math.floor(inventory.bod_inventory_today.get(sku_code, 0) * len(forecast) / total_forecast)
    else:
        weeks_on_hand = 0

    supplement_need = _calculate_supplement_need(week_inventories)
    dc_expiring_inventory = inventory.expiring_dc_by_sku_code.get(sku_code, ExpirationInfo())

    less_than_ten_days_expiration = get_ten_days_before_expiration(dc_expiring_inventory, ten_days_from_now)

    return AvailableInventorySummary(
        weeks_on_hand=weeks_on_hand,
        units_in_house_hj=inventory.current_inventory.get(sku_code, 0),
        supplement_need=supplement_need,
        dc_expiring_inventory=int(dc_expiring_inventory.total),
        pallets_on_hand=inventory.unique_license_count_by_sku.get(sku_code),
        less_than_ten_days_expiration=less_than_ten_days_expiration,
    )


def _build_comment(comment: InventoryModuleComment | None) -> Comment | None:
    if comment:
        return Comment(text=comment.text, last_edited_by=comment.last_edited_by_email)
    return None


def _build_weeks_inventory(
    brand: BRAND, week_context: CurrentWeekInventorySkuContext
) -> tuple[CurrentWeekInventory, list[WeekInventory]]:
    inventory_current_week = _build_current_week_inventory(brand, week_context)

    inventory_next_weeks = []
    remaining_inventory = inventory_current_week.remaining_inventory

    for week in week_context.weeks_range[1:]:
        week_inventory_context = WeekInventorySkuContext(
            start_of_week_inventory=remaining_inventory,
            inbounds_by_week=week_context.inbounds_by_week,
            production_needs_by_week=week_context.production_needs_by_week,
            expiring_by_week=week_context.inventory.expiring_by_week,
        )
        inventory_next_weeks.append(_build_week_inventory(week_inventory_context, week))
        remaining_inventory = inventory_next_weeks[-1].remaining_inventory
    return inventory_current_week, inventory_next_weeks


def _build_current_week_inventory(brand: BRAND, week_context: CurrentWeekInventorySkuContext) -> CurrentWeekInventory:
    """Build inventory for current week"""
    week_config = brand_admin.get_week_config(brand)
    current_week = ScmWeek.current_week(week_config)
    week_inventory_context = WeekInventorySkuContext(
        start_of_week_inventory=week_context.inventory.inventory_today,
        inbounds_by_week=week_context.inbounds_by_week,
        production_needs_by_week=week_context.production_needs_by_week,
        expiring_by_week=week_context.inventory.expiring_by_week,
    )
    week_inventory = _build_week_inventory(week_inventory_context, current_week)
    return CurrentWeekInventory(
        units_in_house_yesterday=week_context.inventory.inventory_yesterday,
        start_of_week_inventory=week_inventory.start_of_week_inventory,
        inbound=week_inventory.inbound,
        undelivered_past_due_pos=week_context.undelivered_past_due_pos,
        received=week_context.received,
        row_need=week_inventory.row_need,
        units_expiring=week_inventory.units_expiring,
        remaining_inventory=week_inventory.remaining_inventory
        + week_context.received
        + (week_context.in_house_adjustment if week_context.in_house_adjustment else 0),
        week=current_week,
        previous_week_row_need=week_context.production_needs_by_week.get(current_week - 1, Decimal()),
        units_in_house_adjustment=week_context.in_house_adjustment,
    )


def _build_week_inventory(week_inventory_context: WeekInventorySkuContext, week: ScmWeek) -> WeekInventory:
    """Build inventory for the specified week"""
    inbound = week_inventory_context.inbounds_by_week.get(week, Decimal())
    units_expiring = week_inventory_context.expiring_by_week.get(week, 0)
    production_needs = week_inventory_context.production_needs_by_week.get(week, Decimal())
    return WeekInventory(
        start_of_week_inventory=week_inventory_context.start_of_week_inventory,
        inbound=inbound,
        units_expiring=units_expiring,
        row_need=production_needs,
        remaining_inventory=week_inventory_context.start_of_week_inventory
        + inbound
        - units_expiring
        - production_needs,
        week=week,
    )


def _get_culinary_skus(
    site: str, inventory_skus: set[str], inbound_sku_codes: set[str], forecast_sku_codes: set[str]
) -> list[CulinarySku]:
    skus = forecast_sku_codes.union(inventory_skus).union(inbound_sku_codes)
    return sku_repo.get_culinary_sku_by_sku_codes(site=site, sku_codes=skus)


def get_ten_days_before_expiration(expiration_info: ExpirationInfo, ten_days_from_now: date) -> date | None:
    if not expiration_info or not expiration_info.min_expiration_date:
        return None
    return (
        expiration_info.min_expiration_date
        if expiration_info.min_expiration_date <= ten_days_from_now
        else expiration_info.min_expiration_date - TEN_DAYS
    )


def _calculate_tpw_in_house(partners: list[WH_CODE], tpw_data: TpwData) -> dict[SKU_CODE, Decimal]:
    """Calculate in house quantity for all partners of the site"""
    in_house_by_sku_code = defaultdict(Decimal)
    for partner_code in partners:
        for sku_code, in_house in tpw_data.in_house_by_partner_sku[partner_code].items():
            in_house_by_sku_code[sku_code] += in_house
    return in_house_by_sku_code


def _calculate_tpw_expiring(partners: list[WH_CODE], tpw_data: TpwData) -> dict[SKU_CODE, ExpirationInfo]:
    """Calculate expiring quantity for all partners of the site"""
    expiring_inventory_by_sku_code = defaultdict(ExpirationInfo)
    for partner_code in partners:
        for sku_code, expiring in tpw_data.expiring_by_partner_sku[partner_code].items():
            expiring_inventory_by_sku_code[sku_code].total += expiring.total
            expiring_inventory_by_sku_code[sku_code].min_expiration_date = utils.min_of_two(
                expiring_inventory_by_sku_code[sku_code].min_expiration_date, expiring.min_expiration_date
            )
    return expiring_inventory_by_sku_code


def _calculate_tpw_outbound(suppliers: list[SUPPLIER], tpw_data: TpwData) -> dict[SKU_CODE, Decimal]:
    """Calculate outbound quantity for all suppliers of the site"""
    outbound_by_sku_code = defaultdict(Decimal)
    for supplier in suppliers:
        for sku_code, outbound in tpw_data.outbound_by_supplier_sku[supplier].items():
            outbound_by_sku_code[sku_code] += outbound
    return outbound_by_sku_code


def _build_tpw_inventory(tpw_inventory_context: TpwInventorySkuContext) -> dict[SKU_CODE, TpwInventory]:
    """Build TPW section of dashboard record"""
    tpw_inventory_by_sku_code = {}
    ten_days_from_now = date.today() + TEN_DAYS
    for sku_code, in_house in tpw_inventory_context.in_house_by_sku_code.items():
        expiration_info = tpw_inventory_context.expiring_inventory_by_sku_code.get(sku_code)
        less_than_ten_days_expiration = None
        avg_forecast = tpw_inventory_context.avg_forecast_by_sku_code.get(sku_code)
        if expiration_info:
            less_than_ten_days_expiration = get_ten_days_before_expiration(expiration_info, ten_days_from_now)
        tpw_inventory_by_sku_code[sku_code] = TpwInventory(
            units_in_house_today=in_house,
            units_in_house_today_network=tpw_inventory_context.network_in_house_by_sku_code.get(sku_code, Decimal()),
            tpw_expiring_inventory=expiration_info.total if expiration_info else Decimal(),
            tpw_outbound=tpw_inventory_context.outbound_by_sku_code.get(sku_code, Decimal()),
            less_than_ten_days_expiration=less_than_ten_days_expiration,
            tpw_weeks_on_hand=round(in_house / avg_forecast) if avg_forecast else 0,
        )
    return tpw_inventory_by_sku_code


def _get_forecasts_by_sku_codes(forecasts: dict[ScmWeek, list[Forecast]]) -> dict[SKU_CODE, dict[ScmWeek, Decimal]]:
    """Get forecasts only where they are present for all dashboard weeks"""
    forecasts_by_sku_code = defaultdict(dict)
    for forecast_values in forecasts.values():
        for forecast in forecast_values:
            forecasts_by_sku_code[forecast.sku_code][forecast.scm_week] = forecast.forecast
    return forecasts_by_sku_code


def get_inventory_sites(week: ScmWeek, brand: str = None) -> dict[BRAND, list[SITE]]:
    """Get sites that could be displayed on dashboard"""
    available_sites = {}
    brands = [brand] if brand else brand_admin.get_brand_ids(week)
    for brand_code in brands:
        available_sites[brand_code] = [site for site, dto in dc_admin.get_enabled_sites(week, brand_code).items()]
    return available_sites


def add_comment(sku_code: SKU_CODE, brand: BRAND, site: SITE, comment: str) -> tuple[InventoryModuleCommentInput, str]:
    user_info = context.get_request_context().user_info
    comment = InventoryModuleCommentInput(
        sku_code=sku_code, brand=brand, site=site, text=comment, last_edited_by_id=user_info.user_id
    )
    inventory_repo.add_comment(comment)
    return comment, user_info.email


def _save_in_house_adjustment(
    sku_code: SKU_CODE, brand: BRAND, site: SITE, adjustment: InventoryModuleInHouseAdjustment
) -> None:
    inventory_repo.add_in_house_adjustment(
        InventoryModuleInHouseAdjustmentInput(
            sku_code=sku_code, brand=brand, site=site, in_house_adjustment=adjustment.value
        )
    )


def _build_inventory_by_in_house_adjustment(
    brand: BRAND, sku_inventory: SkuInventoryModule, adjustment: InventoryModuleInHouseAdjustment | None
) -> SkuInventoryModule:
    current_week = sku_inventory.current_week_inventory
    upcoming_weeks = sku_inventory.upcoming_weeks_inventories
    weeks_data = list(itertools.chain([current_week], upcoming_weeks))

    inventory = InventorySkuContext(
        inventory_today=current_week.start_of_week_inventory,
        inventory_yesterday=current_week.units_in_house_yesterday,
        expiring_by_week={week_item.week: week_item.units_expiring for week_item in weeks_data},
    )
    current_week_inventory_sku_context = CurrentWeekInventorySkuContext(
        weeks_range=[week_item.week for week_item in weeks_data],
        inventory=inventory,
        sku_code=sku_inventory.ingredient_summary.sku,
        inbounds_by_week={week_item.week: week_item.inbound for week_item in weeks_data},
        undelivered_past_due_pos=sku_inventory.current_week_inventory.undelivered_past_due_pos,
        received=current_week.received,
        production_needs_by_week={week_item.week: week_item.row_need for week_item in weeks_data},
        in_house_adjustment=adjustment.value if adjustment else None,
    )

    current_week_inventory, upcoming_weeks_inventories = _build_weeks_inventory(
        brand,
        current_week_inventory_sku_context,
    )
    sku_inventory.current_week_inventory = current_week_inventory
    sku_inventory.upcoming_weeks_inventories = upcoming_weeks_inventories
    sku_inventory.available_inventory_summary.supplement_need = _calculate_supplement_need(
        [current_week_inventory, *upcoming_weeks_inventories]
    )
    return sku_inventory


def process_inventory_by_in_house_adjustment(
    sku_inventory: SkuInventoryModule, brand: BRAND, site: SITE
) -> SkuInventoryModule:
    sku_code = sku_inventory.ingredient_summary.sku
    units_in_house_adjustment = sku_inventory.current_week_inventory.units_in_house_adjustment
    if units_in_house_adjustment is None:
        adjustment = None
        inventory_repo.delete_in_house_adjustment_by_brand_site_and_sku(brand, site, sku_code)
    else:
        adjustment = InventoryModuleInHouseAdjustment(
            sku_code=sku_code, value=sku_inventory.current_week_inventory.units_in_house_adjustment
        )
        _save_in_house_adjustment(sku_code, brand, site, adjustment)
    return _build_inventory_by_in_house_adjustment(brand, sku_inventory, adjustment)
