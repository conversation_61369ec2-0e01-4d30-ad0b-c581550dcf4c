import dataclasses
import logging
from datetime import datetime
from typing import Named<PERSON>uple

from sqlalchemy import Select

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ManualFormScheduleExportEvent
from procurement.core.exceptions.validation_errors import BatchValidationException
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.core.typing import PO_NUMBER, SKU_NAME
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.po_void import PoVoidModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.imt.purchase_order.po_master_view import po_export
from procurement.managers.ordering import PurchaseOrder
from procurement.repository.ordering import po_void as po_void_repo
from procurement.repository.ordering import purchase_order

INVALID_VOID_POS = "Invalid void purchase orders"

NOT_UNIQUE_IN_BATCH = " This item is not unique in batch."
NOT_UNIQUE_IN_SUBMITTED = "This item was already submitted by someone"

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class PoVoidInput:
    po_number: str
    sku_name: str
    dc: str
    brand: str
    ui_row_id: int
    last_updated: datetime
    comment: str | None = None


class ValidatePoVoidContext(NamedTuple):
    dcs: dict[str, DcConfig]
    batch: list[PoVoidInput]
    pos: dict[PO_NUMBER, dict[SKU_NAME, list[PurchaseOrder]]]
    submitted_void_pos: set[str]
    void_po: PoVoidInput
    void_po_index: int
    week: ScmWeek


@ApplicationMetrics.functional_methods()
def get_po_void_response(week: ScmWeek) -> list[PoVoidDto]:
    available_sites = context.get_request_context().user_permissions.available_sites
    return [item for item in po_void_repo.get_po_void_info(week) if item.dc in available_sites.get(item.brand, [])]


@ApplicationMetrics.functional_methods()
def get_po_void(
    weeks: tuple[ScmWeek, ...], dc: str, brand: str, sku_codes: set[str] = None
) -> dict[str, list[PoVoidDto]]:
    return utils.group_by(
        po_void_repo.get_voided_po_number_and_sku_name(weeks, dc, brand, sku_codes), lambda it: it.po_number
    )


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_filtered_po_void(dc_object: DcConfig, po_numbers: Select | set[str]) -> dict[str, list[PoVoidDto]]:
    return utils.group_by(
        po_void_repo.get_voided_purchase_orders_by_po_sku_filter(dc_object, po_numbers), lambda it: it.po_number
    )


@ApplicationMetrics.functional_methods()
def get_po_void_by_po_number_and_sku_code(po_number: str, sku_code: str) -> list[PoVoidDto]:
    return po_void_repo.get_po_void_by_po_number_and_sku_code(po_number, sku_code)


def validate_void_pos(items: list[PoVoidInput], week: ScmWeek, pos=None) -> None:
    if not pos:
        pos = po_export.get_po_export_by_po_number(week)
    submitted_void_pos = {
        f"${po.brand}{po.dc}{po.po_number}{po.sku_name}" for po in po_void_repo.get_po_void_info(week)
    }
    errors = []
    for index, void_po in enumerate(items):
        dcs = dc_admin.get_enabled_sites(week, void_po.brand)
        error = validate_void_po(
            ValidatePoVoidContext(
                void_po_index=index,
                void_po=void_po,
                pos=pos,
                batch=items,
                dcs=dcs,
                submitted_void_pos=submitted_void_pos,
                week=week,
            )
        )
        if error:
            errors.append(BatchValidationException.build_error(void_po.ui_row_id, error))
    if errors:
        raise BatchValidationException(INVALID_VOID_POS, errors)


def validate_void_po(po_void_context: ValidatePoVoidContext) -> dict[str, str]:
    if not is_unique_in_submitted(po_void_context.void_po, po_void_context.submitted_void_pos):
        return {
            "brand": NOT_UNIQUE_IN_SUBMITTED,
            "dc": NOT_UNIQUE_IN_SUBMITTED,
            "poNumber": NOT_UNIQUE_IN_SUBMITTED,
            "skuName": NOT_UNIQUE_IN_SUBMITTED,
        }

    if not is_unique(po_void_context.void_po, po_void_context.batch, po_void_context.void_po_index):
        return {
            "brand": NOT_UNIQUE_IN_BATCH,
            "dc": NOT_UNIQUE_IN_BATCH,
            "poNumber": NOT_UNIQUE_IN_BATCH,
            "skuName": NOT_UNIQUE_IN_BATCH,
        }

    return validate_po_fields(po_void_context.dcs, po_void_context.pos, po_void_context.void_po, po_void_context.week)


def is_unique(void_po, items, index_in_items) -> bool:
    return len(items) <= 1 or not any(
        (
            index != index_in_items
            and void_po.dc == item.dc
            and void_po.brand == item.brand
            and void_po.po_number == item.po_number
            and void_po.sku_name == item.sku_name
        )
        for (index, item) in enumerate(items)
    )


def is_unique_in_submitted(void_po, submitted_void_pos) -> bool:
    return f"${void_po.brand}{void_po.dc}{void_po.po_number}{void_po.sku_name}" not in submitted_void_pos


def validate_po_fields(dcs, pos, void_po, week: ScmWeek) -> dict[str, str]:
    po_number = void_po.po_number
    sku_name = void_po.sku_name
    dc = void_po.dc
    brand = void_po.brand
    dc_object = dcs.get(dc)
    invalid_fields = {}
    if brand not in brand_admin.get_brand_ids(week):
        invalid_fields["brand"] = "Invalid brand name"
    elif not dc_object:
        invalid_fields["dc"] = "This site is not active for the specified brand on week " + str(week)
    if po_number not in pos:
        invalid_fields["poNumber"] = "This PO number does not belong to week " + str(week)
    elif sku_name not in pos[po_number]:
        invalid_fields["skuName"] = "This SKU name was not found in this PO"
    elif dc_object and dc_object.ordering_tool_name != pos[po_number][sku_name].dc:
        msg = "This PO does not belong to the specified brand and site"
        invalid_fields["brand"] = msg
        invalid_fields["dc"] = msg
    return invalid_fields


def add_po_void(items, week: ScmWeek) -> None:
    pos = po_export.get_po_export_by_po_number(week)
    validate_void_pos(items, week, pos)
    data = []
    user_email = context.get_request_context().user_info.email
    market = context.get_request_context().market
    created_timestamp = datetime.now()
    for item in items:
        po_void_item = {
            PoVoidModel.user: user_email,
            PoVoidModel.dc: item.dc,
            PoVoidModel.po_number: item.po_number,
            PoVoidModel.sku_code: pos[item.po_number][item.sku_name].sku_code,
            PoVoidModel.supplier_name: pos[item.po_number][item.sku_name].supplier,
            PoVoidModel.source: DataSource.APP.name,
            PoVoidModel.brand: item.brand,
            PoVoidModel.comment: item.comment,
            PoVoidModel.week: str(week),
            PoVoidModel.last_updated: item.last_updated,
            PoVoidModel.market: market,
            PoVoidModel.cre_tmst: created_timestamp,
        }
        data.append(po_void_item)
    if data:
        po_void_repo.po_void_insert_many(data)
        EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))


def edit_po_void_comment(po_void_id: int, comment: str) -> None:
    po_void_repo.edit_po_void_comment(po_void_id=po_void_id, comment=comment)


def get_po_void_week_by_id(po_void_id: int) -> ScmWeek:
    return po_void_repo.get_po_void_week_by_id(po_void_id)


def delete_po_void(po_void_id: int) -> None:
    week = get_po_void_week_by_id(po_void_id=po_void_id)
    po_void_repo.mark_po_void_as_deleted(
        po_void_id=po_void_id,
        deleted_by=context.get_request_context().user_info.email,
    )
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))


def get_po_void_pos(week: ScmWeek, brand: str, site: str) -> list[str]:
    dc = dc_admin.get_site(brand, site, week)
    if not dc:
        return []
    return purchase_order.get_pos_by_week_brand_site(week, brand, dc)


def get_po_void_skus(week: ScmWeek, brand: str, site: str, po_number: str) -> list[str]:
    return po_void_repo.get_po_void_skus(week, brand, site, po_number)
