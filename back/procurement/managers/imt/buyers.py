from collections.abc import Collection, Iterable

from procurement.client.googlesheets import googlesheet_utils as gsheet_utils
from procurement.core.request_utils import context
from procurement.core.typing import SITE, SKU_CODE
from procurement.data.googlesheet_model.total_procurement_purchasing import TppRow
from procurement.data.models.inventory.buyer_sku import BuyerSkuModel
from procurement.managers.datasync.framework import warnings
from procurement.repository.inventory import buyer as buyer_repo
from procurement.repository.procurement import user as user_repo


def update_data(sheet_rows: Iterable[TppRow]):
    market = context.get_request_context().market
    users_map = {user.email.lower(): user.id for user in user_repo.get_user_emails()}
    logged_invalid_emails = set()

    def validate_row(row) -> bool:
        gsheet_utils.validate_required_fields(row, ["sku_code", "email", "dc", "brand"])
        if row.email.lower() not in users_map:
            if row.email in logged_invalid_emails:
                return False
            logged_invalid_emails.add(row.email)
            raise ValueError(f"Unknown user '{row.email}'")
        return True

    data = []
    for row in sheet_rows:
        try:
            if not row.email or not validate_row(row):
                continue  # skip unassigned skus and logged unknown emails to truncate logs
            data.append(
                {
                    BuyerSkuModel.user_id: users_map[row.email.lower()],
                    BuyerSkuModel.sku_code: row.sku_code,
                    BuyerSkuModel.site: row.dc,
                    BuyerSkuModel.brand: row.brand,
                    BuyerSkuModel.market: market,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(row, exc, "Error when importing Buyers SKUs data", skipped=True)

    buyer_repo.update_data(data)


def get_buyers() -> dict[int, str]:
    return {b.id: f"{b.first_name} {b.last_name}" for b in buyer_repo.get_buyer_names()}


def get_buyers_by_sku(brand: str = None, site: str = None) -> dict[SKU_CODE, str]:
    return {sku_code: row.full_name for row in buyer_repo.get_buyers(brand, site) for sku_code in row.sku_codes}


def get_buyers_by_site_sku(
    sites: Collection[str], brand: str = None, sku_codes: set[str] | None = None
) -> dict[tuple[SITE, SKU_CODE], str]:
    return {
        (row.site, sku_code): row.full_name
        for row in buyer_repo.get_buyers_by_sku_site(sites, brand=brand, sku_codes=sku_codes)
        for sku_code in row.sku_codes
    }


def get_buyers_filter_by_sku_codes(brand: str, site: str, sku_codes: Collection[str]) -> dict[SKU_CODE, str]:
    return {
        sku_code: row.full_name
        for row in buyer_repo.get_buyers(brand, site, sku_codes=sku_codes)
        for sku_code in row.sku_codes
    }
