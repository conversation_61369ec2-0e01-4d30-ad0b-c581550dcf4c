import logging
from collections.abc import Iterable
from decimal import Decimal

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_FJ, PRODUCTION_TYPE_KITTING
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.base_errors import NonCriticalWrapperException
from procurement.core.typing import BRAND, SITE
from procurement.data.googlesheet_model.mock_plan_calculation import Compiled, CompiledFactor, MockPlanRow
from procurement.data.models.forecast.mock_plan_calculation import MockPlanModel
from procurement.managers.admin import dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.datasync.framework import warnings
from procurement.repository.forecast import mock_plan_calculation as mock_plan_calculation_repo

logger = logging.getLogger(__name__)


def update_compiled_data(week: ScmWeek, brand: BRAND) -> None:
    gsheet_model = CompiledFactor if brand == BRAND_FJ else Compiled
    logger.info("Compiled sync: Start updating %s week", week)
    sheet_data: list[MockPlanRow] = gsheet_admin.read_gsheet(gsheet_model(), week=week, is_formatted=False)
    if not sheet_data:
        raise NonCriticalWrapperException(ValueError(f"There is no mock_plan_calculation document for week {week}"))
    updates = []
    number_week = week.to_number_format()
    for row in sheet_data:
        try:
            _validate_compiled_input(row, dc_admin.get_enabled_sites(week, row.brand))
            weights = [weight or Decimal() for weight in row.weight]
            updates.append(
                {
                    MockPlanModel.site: row.site,
                    MockPlanModel.week: number_week,
                    MockPlanModel.brand: row.brand,
                    MockPlanModel.production_type: row.production_type,
                    MockPlanModel.weights: weights,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(row, exc, f"Error while importing {gsheet_model.sheet_name} Data", skipped=True)
    mock_plan_calculation_repo.update_mock_plan(updates)


def _validate_compiled_input(row: MockPlanRow, sites: Iterable[SITE]):
    validate_required_fields(row, ["brand", "site", "production_type"])
    if row.site not in sites:
        raise ValueError(f'The Site "{row.site}" is unavailable in the app')


def get_mock_plans_by_week_and_kitting_type(
    dc_object: DcConfig, weeks: frozenset[ScmWeek]
) -> dict[tuple[ScmWeek, bool], list[Decimal]]:
    rows = mock_plan_calculation_repo.get_mock_plans(dc_object, weeks)
    return {(row.week, row.production_type == PRODUCTION_TYPE_KITTING): row.weights for row in rows}
