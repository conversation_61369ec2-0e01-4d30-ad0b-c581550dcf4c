from datetime import date, timedelta
from decimal import Decimal

from procurement.constants.hellofresh_constant import MOCK_PLAN_ERROR_WEEKS_OUT
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import warnings
from procurement.managers.admin import brand_admin
from procurement.managers.imt.models import ProductionNeedsPackaging
from procurement.repository.forecast import mock_plan_calculation, packaging_forecast
from procurement.repository.inventory import packaging_depletion

PACKAGING_INV_DAYS_IN_WEEK = 8

_ZERO_MOCK_PLAN = [Decimal()] * PACKAGING_INV_DAYS_IN_WEEK


def get_production_needs_packaging(site: str, week: ScmWeek, brand: str) -> list[ProductionNeedsPackaging]:
    week_config = brand_admin.get_week_config(brand)
    week_first_day = week.get_first_day(week_config)
    start_day_index = next(
        (
            shift
            for shift in range(PACKAGING_INV_DAYS_IN_WEEK)
            if week_first_day + timedelta(days=shift) >= date.today()
        ),
        PACKAGING_INV_DAYS_IN_WEEK,
    )

    production_needs_packaging = get_packaging_inventory_data(site, week, brand, start_day_index)
    if not production_needs_packaging:
        production_needs_packaging = _build_forecasted_data(site, week, brand, start_day_index)
    return production_needs_packaging


def get_packaging_inventory_data(
    site: str, week: ScmWeek, brand: str, start_day_index: int
) -> list[ProductionNeedsPackaging]:
    return [
        ProductionNeedsPackaging(sku_code=item.sku_code, row_need=sum(map(round, item.demand_by_day[start_day_index:])))
        for item in packaging_depletion.get_packaging_demand(weeks=[week], site=site, brand=brand)
    ]


def _build_forecasted_data(
    site: str, week: ScmWeek, brand: str, start_day_index: int
) -> list[ProductionNeedsPackaging]:
    weights = mock_plan_calculation.get_assembly_mock_plan(site, week, brand)
    week_config = brand_admin.get_week_config(brand)
    if not weights:
        if week < ScmWeek.current_week(week_config) + MOCK_PLAN_ERROR_WEEKS_OUT:
            warnings.add_message(f"Mock plan calculation missed for weeks {week}")
        weights = _ZERO_MOCK_PLAN
    for idx in range(start_day_index):
        weights[idx] = Decimal()
    return [
        ProductionNeedsPackaging(
            sku_code=forecast.sku_code,
            row_need=Decimal(sum(utils.ceil_subone_decimal(weight * forecast.forecast) for weight in weights)),
        )
        for forecast in packaging_forecast.get_forecasts(site, week, brand)
    ]
