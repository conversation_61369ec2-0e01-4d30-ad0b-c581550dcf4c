import dataclasses
import logging
from collections import defaultdict
from datetime import date, datetime

from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ManualFormScheduleExportEvent
from procurement.core.exceptions.api_errors import InvalidRequestException
from procurement.core.exceptions.validation_errors import BatchValidationException
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BRAND, SKU_CODE, SKU_NAME
from procurement.data.dto.inventory.pull_put import PullPutDto
from procurement.data.models.inventory.inventory_pull_put import PullPutModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.repository.inventory import ingredient as ingredient_repo
from procurement.repository.ordering import culinary_sku as culinary_sku_repo

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class PullPutInput:
    brand: str
    dc: str
    sku_name: str
    qty: int
    comment: str
    user_email: str
    row: int


def get_pull_put_items(
    week: ScmWeek, brand: str = None, dc: str = None, sku_codes: set[str] = None, fetch_sku: bool = False
) -> list[PullPutDto]:
    logger.debug("Getting override inventory for brand %s, state: %s, week: %s", brand, dc, str(week))
    available_sites = context.get_request_context().user_permissions.available_sites

    return [
        item
        for item in ingredient_repo.get_pull_put(week, fetch_sku, brand, dc, sku_codes)
        if item.dc in available_sites.get(item.brand, [])
    ]


def add_pull_put(items: list[PullPutInput], week: ScmWeek, source="WEB_APP"):
    now = datetime.now()
    market = context.get_request_context().market
    data = list(
        filter(
            lambda item: item[PullPutModel.sku_code],
            (
                {
                    PullPutModel.user_email: item.user_email,
                    PullPutModel.brand: item.brand.upper(),
                    PullPutModel.dc: item.dc.upper(),
                    PullPutModel.week: str(week),
                    PullPutModel.source: source.upper(),
                    PullPutModel.sku_code: _get_skus(brand=item.brand).get(item.sku_name),
                    PullPutModel.qty: item.qty,
                    PullPutModel.comment: item.comment,
                    PullPutModel.upd_tmst: now,
                    PullPutModel.cre_tmst: now,
                    PullPutModel.market: market,
                }
                for item in items
            ),
        )
    )
    if data:
        ids = ingredient_repo.insert_many_pull_put(data)
        EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))
        return ids
    return []


def delete_pull_put(record_id: int, week: ScmWeek):
    ingredient_repo.delete_pull_put_by_id(record_id=record_id, user_email=context.get_request_context().user_info.email)
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=week))


def get_pull_put_week_by_id(item_id: int) -> ScmWeek:
    return ingredient_repo.get_pull_put_week_by_id(item_id)


def edit_pull_put(record_id: int, new_qty: int, new_comment: str) -> PullPutDto:
    item = ingredient_repo.get_pull_put_by_id(record_id)
    if not item:
        raise InvalidRequestException("Inventory entry does not exist")
    update_timestamp = datetime.now()
    ingredient_repo.edit_pull_put_item(record_id, new_qty, new_comment, update_timestamp)
    item.qty = new_qty
    item.comment = new_comment
    item.updated_at = update_timestamp
    EVENT_BUS.publish(ManualFormScheduleExportEvent(week=item.week))

    return item


def validate_batch_pull_put(items, week: ScmWeek):
    batch_info = defaultdict(int)
    if not items:
        return batch_info
    brands = brand_admin.get_brand_ids(week)
    brands_to_dc = {}

    for brand in brands:
        brands_to_dc[brand] = dc_admin.get_enabled_sites(week, brand)
    errors = []
    for item in items:
        valid = True
        row_errors = {}
        if item.brand not in brands:
            row_errors["brand"] = "Invalid Brand value"
            valid = False
        elif item.dc not in brands_to_dc.get(item.brand, {}):
            row_errors["dc"] = "Invalid Site value"
            valid = False

        if item.qty == 0:
            row_errors["qty"] = "Quantity must be non zero"
            valid = False
        if item.sku_name not in _get_skus(item.brand):
            row_errors["skuName"] = "SKU Name does not exist"
            valid = False

        if valid:
            batch_info[item.brand + " " + item.dc] += 1
        else:
            errors.append(BatchValidationException.build_error(item.row, row_errors))
    if errors:
        raise BatchValidationException("Invalid batch", errors)
    return batch_info


def export_pull_put_items(week: str, dc: str, brand: str):
    return ingredient_repo.export_pull_put_items(week, dc, brand)


def get_skus(brand: BRAND, search_key: str) -> list[SKU_NAME]:
    return list(_get_skus(brand, search_key).keys())


@request_cache
def _get_skus(brand: BRAND, search_key: str = None) -> dict[SKU_NAME, SKU_CODE]:
    return {
        sku.sku_name: sku.sku_code
        for sku in culinary_sku_repo.get_sku_codes_statuses_categories_by_name(brand=brand, search_key=search_key)
    }


def get_pull_put_items_for_export(export_date: date):
    return ingredient_repo.get_pull_put_items_for_export(export_date)
