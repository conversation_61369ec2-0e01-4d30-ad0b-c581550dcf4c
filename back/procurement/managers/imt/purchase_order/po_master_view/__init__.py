import dataclasses
import logging
from abc import ABC, abstractmethod
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from functools import cached_property

from sqlalchemy import Select

from procurement.constants.hellofresh_constant import APP_TIMEZONE, FUTURE_WEEK_PULL_REASONS
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import ORDER_NUMBER, PO_SKU_KEY, SITE, SKU_CODE
from procurement.data.dto.highjump.receipts import HjReceiptItem
from procurement.data.dto.inventory.grn import GrnPo
from procurement.data.dto.inventory.receipt_override import ReceiptOverride
from procurement.data.dto.inventory.receiving import ReceivingItem
from procurement.data.dto.ordering.asn import AdvanceShippingNoticeDto
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.data.models.highjump.highjump import HJReceiptsModel
from procurement.data.models.inventory.alternative_sku_mapping import PackagingSkuModel
from procurement.data.models.inventory.grn import LegacyHjGoodsReceiptNoteModel
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import highjump as hj_service
from procurement.managers.distribution_center import receipt_override as receipt_override_service
from procurement.managers.forecasts import forecast_manager
from procurement.managers.forms import receive as receiving_service
from procurement.managers.imt import po_void as po_void_service
from procurement.managers.imt.bulk_sku import BulkSkuManager
from procurement.managers.imt.models import PoMasterViewResult
from procurement.managers.ordering import PurchaseOrder
from procurement.managers.ordering import advance_shipping_notice as asn_service
from procurement.managers.ordering import po_acknowledgement as ack_service
from procurement.managers.ordering import service as po_service
from procurement.managers.ordering.po_acknowledgement import PoAcknowledgementData
from procurement.managers.ordering.service import PoExportContext
from procurement.managers.prices import allocation_tool_price
from procurement.managers.receipt import grn
from procurement.managers.sku import alternative_sku_mapping
from procurement.repository import repository_utils
from procurement.repository.inventory import packaging_depletion
from procurement.repository.ordering import purchase_order as po_repo

from . import po_export
from .calculated_fields import GrnCalculations, HjCalculations, ManualCalculations

logger = logging.getLogger(__name__)
HJReceiptsSqla = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else HJReceiptsModel

PO_CASE_PRICE_FLAG_PERCENTAGE_THRESHOLD = Decimal(1.15)


@dataclasses.dataclass
class PoStatusContextItem:
    dc_object: DcConfig
    week: ScmWeek
    po_data: PurchaseOrder
    proposed_data: PoAcknowledgementData | None
    asn_data: AdvanceShippingNoticeDto
    units_needed: Decimal
    allocation_price: Decimal | None = None
    po_voids: list[PoVoidDto] = dataclasses.field(default_factory=list)
    non_hj_receipt: list[ReceivingItem] = dataclasses.field(default_factory=list)
    receipt_override: list[ReceiptOverride] = dataclasses.field(default_factory=list)
    hj_receipt: list[HjReceiptItem] = dataclasses.field(default_factory=list)
    grn_receipt: list[GrnPo] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class ReceivingData:
    hj_receipt: dict[PO_SKU_KEY, list[HjReceiptItem]] = dataclasses.field(default_factory=dict)
    receipt_override: dict[PO_SKU_KEY, list[ReceiptOverride]] = dataclasses.field(default_factory=dict)
    non_hj_receipt: dict[PO_SKU_KEY, list[ReceivingItem]] = dataclasses.field(default_factory=dict)
    grn_receipt: dict[tuple[ORDER_NUMBER, SKU_CODE], list[GrnPo]] = dataclasses.field(default_factory=dict)


class PoStatusContext(ABC):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        site: str | None,
        brand: str | None,
        items: list[PoStatusContextItem] = None,
        filter_general_supplier: bool = False,
        filter_hf_supplier: bool = False,
    ):
        self.site = site
        self.brand = brand
        self._items = items
        self._filter_general_supplier = filter_general_supplier
        self._filter_hf_supplier = filter_hf_supplier
        self._dc_objects = None

    @abstractmethod
    @log_wrapper
    @ApplicationMetrics.functional_methods()
    def _fetch_items(self): ...

    @abstractmethod
    @cached_property
    def _dcs_with_weeks(self) -> Iterable[tuple[DcConfig | None, ScmWeek]]: ...

    @cached_property
    def _dc_by_week(self) -> dict[ScmWeek, DcConfig]:
        return {week: dc for dc, week in self._dcs_with_weeks if dc}

    @cached_property
    def bulk_sku_manager(self):
        return BulkSkuManager(self.brand)

    def _get_unique_type_dc_objects(self) -> list[tuple[DcConfig, list[ScmWeek]]]:
        """
        Returns list of unique dc objects in terms of receiving type.
        For example if dc receiving type was changed during weeks,
        there will be two dc objects with different receiving types.
        """
        dc_objects = self._dcs_with_weeks
        dc_by_receiving_type = defaultdict(list)
        for dc, week in dc_objects:
            if dc:
                dc_by_receiving_type[dc.receiving_type].append((dc, week))
        res = [
            (dc_weeks[0][0], [week for _, week in dc_weeks])
            for receiving_type, dc_weeks in dc_by_receiving_type.items()
        ]
        return res

    @cached_property
    def dc_objects(self) -> tuple[DcConfig, ...]:
        """
        Returns list of unique dc objects in terms of receiving type
        actually present in this context's items.
        For example if dc receiving type was changed during weeks,
        there will be two dc objects with different receiving types,
        but if all items were from one dc config there will be only one dc object.
        """
        return tuple(dc for dc, _ in self._get_unique_type_dc_objects())

    @cached_property
    def any_dc_object(self) -> DcConfig | None:
        return next(iter(self.dc_objects), None)

    @property
    def items(self):
        if self._items is None:
            self._fetch_items()
        return self._items

    @items.setter
    def items(self, value: list[PoStatusContextItem]):
        self._items = value if value is not None else []
        self._dc_objects = None

    @staticmethod
    def _get_order_request_week(order: PurchaseOrder) -> ScmWeek:
        return (order.week - 1) if order.emergency_reason in FUTURE_WEEK_PULL_REASONS else order.week

    @staticmethod
    def get_forecasts(weeks: Iterable[ScmWeek], site: str) -> dict[tuple[SKU_CODE, SITE, ScmWeek], Decimal]:
        forecast = forecast_manager.get_forecast_manager()
        rows = forecast.get_all_forecasts(weeks=weeks, sites=[site])
        forecasts = defaultdict(Decimal)
        for row in rows:
            forecasts[(row.sku_code, row.site, row.scm_week)] += row.forecast
        return forecasts

    def get_forecasts_for_bulk_sku(
        self, sku_code: str, request_week: ScmWeek, forecasts: dict[tuple[SKU_CODE, SITE, ScmWeek], Decimal]
    ) -> Decimal:
        packaged_skus = self.bulk_sku_manager.get_packaged_skus(sku_code)
        forecast = Decimal()
        for sku in packaged_skus:
            forecast += forecasts.get(
                (sku, self.site, request_week), Decimal()
            ) * self.bulk_sku_manager.get_pick_conversion(sku)
        return forecast


class PoStatusByWeekContext(PoStatusContext):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        site: str,
        brand: str,
        weeks: tuple[ScmWeek, ...],
        items: list[PoStatusContextItem] = None,
        sku_codes: frozenset[str] = None,
        category_filter: frozenset[str] = None,
        group_filter: frozenset[str] = None,
        include_empty_categories: bool = False,
        include_shipment_data: bool = False,
        exclude_po_skus: frozenset[PO_SKU_KEY] = None,
        filter_general_supplier: bool = False,
        filter_hf_supplier: bool = False,
    ):
        super().__init__(site, brand, items, filter_general_supplier, filter_hf_supplier)
        self.weeks = weeks
        self._sku_codes = sku_codes
        self._category_filter = category_filter
        self._group_filter = group_filter
        self._include_empty_categories = include_empty_categories
        self._include_shipment_data = include_shipment_data
        self._exclude_po_skus = exclude_po_skus or set()

    @cached_property
    def _dcs_with_weeks(self) -> Iterable[tuple[DcConfig | None, ScmWeek]]:
        return tuple(
            filter(
                lambda x: x[0],
                ((dc_admin.get_site(week=week, brand=self.brand, site=self.site), week) for week in self.weeks),
            )
        )

    def _fetch_items(self):
        self._items = []

        if not self._dc_by_week:
            return

        po_data = po_export.get_po_export_data(
            PoExportContext(
                weeks=self.weeks,
                site=self.site,
                brand=self.brand,
                sku_codes=self._sku_codes,
                category_filter=self._category_filter,
                group_filter=self._group_filter,
                include_empty_categories=self._include_empty_categories,
                include_shipment_data=self._include_shipment_data,
                filter_general_supplier=self._filter_general_supplier,
                filter_hf_supplier=self._filter_hf_supplier,
            )
        )
        if not po_data:
            return

        po_void_map = po_void_service.get_po_void(
            weeks=self.weeks, brand=self.brand, dc=self.site, sku_codes=self._sku_codes
        )
        receiving_data = PoStatusByWeekContext.__get_receipt_data(self._dc_by_week, po_data, self._sku_codes)

        po_numbers = {po.po_number for po in po_data}
        po_ack_data = ack_service.get_po_acknowledgements_proposed_data(po_numbers)

        po_asn_data = asn_service.get_advance_shipping_notice_by_po_numbers(po_numbers)
        forecasts = self.get_forecasts(self.weeks, self.site)

        for po_item in po_data:
            uid = (po_item.po_number, po_item.sku_code)
            order_sku = (utils.get_order_number_from_po_number(po_item.po_number), po_item.sku_code)
            if uid in self._exclude_po_skus:
                continue
            request_week = self._get_order_request_week(po_item)
            po_ack_item = po_ack_data.get(uid)
            if request_week not in self._dc_by_week:
                continue
            dc_obj = self._dc_by_week[request_week]
            if self.bulk_sku_manager.is_bulk(po_item.sku_code):
                forecast = self.get_forecasts_for_bulk_sku(po_item.sku_code, request_week, forecasts)
            else:
                forecast = forecasts.get((po_item.sku_code, dc_obj.sheet_name, request_week), Decimal())
            self._items.append(
                PoStatusContextItem(
                    dc_object=dc_obj,
                    week=po_item.week,
                    po_data=po_item,
                    allocation_price=self.allocation_prices_by_week.get(po_item.week, {}).get(po_item.sku_code),
                    po_voids=po_void_map.get(po_item.po_number, []),
                    non_hj_receipt=receiving_data.non_hj_receipt.get(uid, []),
                    receipt_override=receiving_data.receipt_override.get(uid, []),
                    hj_receipt=receiving_data.hj_receipt.get(uid, []),
                    grn_receipt=receiving_data.grn_receipt.get(order_sku, []),
                    proposed_data=po_ack_item,
                    asn_data=po_asn_data.get(uid),
                    units_needed=forecast,
                )
            )

    @cached_property
    def allocation_prices_by_week(self) -> dict[ScmWeek, dict[SKU_CODE, Decimal]]:
        return {
            week: allocation_tool_price.get_allocation_prices(
                week=week, dc_obj=self._dc_by_week[week], brand=self.brand
            )
            for week in self.weeks
            if week in self._dc_by_week
        }

    @staticmethod
    def __get_receipt_data(
        dc_objects_by_week: dict[ScmWeek, DcConfig],
        po_data: list[PurchaseOrder],
        sku_codes: frozenset[str] = None,
    ) -> ReceivingData:
        manual_dcs = []
        hj_dcs = []
        grn_dcs = []
        for week, dc_object in dc_objects_by_week.items():
            if dc_object.receiving_type.is_high_jump:
                hj_dcs.append((week, dc_object))
            elif dc_object.receiving_type.is_in_grn:
                grn_dcs.append((week, dc_object))
            else:
                manual_dcs.append((week, dc_object))

        receipt_override = receipt_override_service.get_receipt_overrides_group_by_po_number_and_sku_code(
            weeks_dc_objects=hj_dcs + grn_dcs, sku_codes=sku_codes
        )
        hj_receipt = hj_service.get_receipts_group_by_po_number_and_sku_code(
            weeks_dc_objects=hj_dcs, selected_sku_codes=sku_codes
        )

        if len(dc_objects_by_week) == 1:
            po_sku_codes = {po.sku_code for po in po_data if po.emergency_reason in FUTURE_WEEK_PULL_REASONS}
            week, dc_object = next(iter(dc_objects_by_week.items()))
            hj_receipt_next_week = hj_service.get_receipts_group_by_po_number_and_sku_code(
                [(week.next(), dc_object)], selected_sku_codes=po_sku_codes
            )
            hj_receipt.update(hj_receipt_next_week)

        non_hj_receipt = receiving_service.get_receiving_group_by_po_number_and_sku_code(
            weeks_dc_objects=manual_dcs, sku_codes=sku_codes
        )
        grn_receipt = grn.get_grns_group_by_po_number_and_sku_code(weeks_dc_objects=grn_dcs, sku_codes=sku_codes)
        return ReceivingData(
            hj_receipt=hj_receipt,
            receipt_override=receipt_override,
            non_hj_receipt=non_hj_receipt,
            grn_receipt=grn_receipt,
        )


class PoSkuFilteredPoStatusContext(PoStatusContext):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        site: str,
        brand: str,
        items: list[PoStatusContextItem] = None,
        filter_general_supplier: bool = False,
        filter_hf_supplier: bool = False,
    ):
        super().__init__(site, brand, items, filter_general_supplier, filter_hf_supplier)

    @abstractmethod
    def get_po_sku_filtering_query(self, dc_object: DcConfig, weeks: list[ScmWeek]) -> Select:
        """
        Returns select query of corresponding (po_number, sku_code) pairs related to this context,
        that might be used for fetching additional data from other tables.
        :param dc_object:
        :param weeks: is used to select only the range when that DC config is active
        :return:
        """

    @cached_property
    def _dcs_with_weeks(self) -> Iterable[tuple[DcConfig | None, ScmWeek]]:
        return [(dc_admin.get_site(week=week, brand=self.brand, site=self.site), week) for week in self._weeks]

    @abstractmethod
    @cached_property
    def _weeks(self) -> tuple[ScmWeek, ...]: ...

    def _get_receiving_data(self) -> ReceivingData:
        res = ReceivingData()
        for dc, weeks in self._get_unique_type_dc_objects():
            if dc.receiving_type.is_high_jump:
                self._fetch_hj_receives(res, dc, weeks)
                self._fetch_hj_override(res, dc, weeks)
            elif dc.receiving_type.is_in_grn:
                self._fetch_grn_receives(res, dc, weeks)
                self._fetch_hj_override(res, dc, weeks)
            else:
                self._fetch_manual_receives(res, dc, weeks)
        return res

    def _fetch_hj_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.hj_receipt.update(
            hj_service.get_po_sku_filtered_hj_receipt(po_sku_filtering_query=self.get_po_sku_filtering_query(dc, weeks))
        )

    def _fetch_hj_override(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.receipt_override.update(
            receipt_override_service.get_po_sku_filtered_receipt_overrides(
                dc_object=dc,
                po_skus=self.get_po_sku_filtering_query(dc, weeks),
            )
        )

    def _fetch_grn_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.grn_receipt = grn.get_po_sku_filtered_grn(
            dc_object=dc,
            po_sku_filtering_query=self.get_po_sku_filtering_query(dc, weeks),
        )

    def _fetch_manual_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.non_hj_receipt = receiving_service.get_po_sku_filtered_receiving(
            po_skus=self.get_po_sku_filtering_query(dc, weeks)
        )

    def _fetch_items(self):
        self._items = []
        dc = self.any_dc_object
        po_data = self._get_po_data()
        if not po_data:
            return

        po_void_map = po_void_service.get_po_void(weeks=self._weeks, dc=dc.sheet_name, brand=dc.brand)
        receiving_data = self._get_receiving_data()
        po_numbers = {po.po_number for po in po_data}
        po_ack_data = ack_service.get_po_acknowledgements_proposed_data(po_numbers)
        po_asn_data = asn_service.get_advance_shipping_notice_by_po_numbers(po_numbers)
        forecasts = self.get_forecasts(self._weeks, self.site)

        for po_item in po_data:
            uid = (po_item.po_number, po_item.sku_code)
            order_sku = (utils.get_order_number_from_po_number(po_item.po_number), po_item.sku_code)
            request_week = self._get_order_request_week(po_item)
            if request_week not in self._dc_by_week:
                continue
            po_ack_item = po_ack_data.get(uid)
            dc_obj = self._dc_by_week[request_week]
            if self.bulk_sku_manager.is_bulk(po_item.sku_code):
                forecast = self.get_forecasts_for_bulk_sku(po_item.sku_code, request_week, forecasts)
            else:
                forecast = forecasts.get((po_item.sku_code, dc_obj.sheet_name, request_week), Decimal())
            self._items.append(
                PoStatusContextItem(
                    dc_object=dc_obj,
                    week=request_week,
                    po_data=po_item,
                    po_voids=po_void_map.get(po_item.po_number, []),
                    non_hj_receipt=receiving_data.non_hj_receipt.get(uid, []),
                    receipt_override=receiving_data.receipt_override.get(uid, []),
                    hj_receipt=receiving_data.hj_receipt.get(uid, []),
                    grn_receipt=receiving_data.grn_receipt.get(order_sku, []),
                    proposed_data=po_ack_item,
                    asn_data=po_asn_data.get(uid),
                    units_needed=forecast,
                )
            )

    def _get_po_data(self) -> Iterable[PurchaseOrder]:
        res = []
        for dc, weeks in self._get_unique_type_dc_objects():
            res.extend(
                po_export.get_po_sku_filtered_po_data(
                    dc_object=dc,
                    po_sku_filtering_query=self.get_po_sku_filtering_query(dc, weeks),
                    filter_general_supplier=self._filter_general_supplier,
                    filter_hf_supplier=self._filter_hf_supplier,
                )
            )
        return res


class PoStatusByReceivingDateContext(PoSkuFilteredPoStatusContext):
    """
    Returns PO Master View context for SKUs received during [date_from, date_to) period.
    Note that date_to is exclusive. Default is midnight of current day.
    So date_from = 2021-03-14 00:00:00 with date_to 2021-03-15 00:00:00 will return context
    for all items received on 3/14
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        site: str,
        brand: str,
        date_from: datetime,
        date_to: datetime,
        items: list[PoStatusContextItem] = None,
        category_filter: frozenset[str] = None,
        group_filter: frozenset[str] = None,
        include_empty_categories: bool = True,
        filter_general_supplier: bool = False,
        filter_hf_supplier: bool = False,
    ):
        super().__init__(site, brand, items, filter_general_supplier, filter_hf_supplier)
        if killswitch.hj_grn_enabled and self.dc_objects[0].receiving_type.is_high_jump:
            dc_timezone = self.any_dc_object.timezone
            self.date_from = dc_timezone.localize(date_from).astimezone(APP_TIMEZONE)
            self.date_to = dc_timezone.localize(date_to).astimezone(APP_TIMEZONE)
        else:
            self.date_from = date_from
            self.date_to = date_to

        self._category_filter = category_filter
        self._group_filter = group_filter
        self._include_empty_categories = include_empty_categories

    def get_po_sku_filtering_query(self, dc_object: DcConfig, weeks: list[ScmWeek]) -> Select:
        return po_repo.get_po_sku_code_query_by_receiving_date(
            dc_object=dc_object,
            receive_date_from=self.date_from,
            receive_date_to=self.date_to,
            weeks=weeks,
            filters=po_repo.PoFilters(
                category_filter=self._category_filter,
                group_filter=self._group_filter,
                include_empty_categories=self._include_empty_categories,
            ),
        )

    @cached_property
    def _weeks(self) -> tuple[ScmWeek, ...]:
        return tuple(
            po_export.get_received_weeks(
                site=self.site,
                brand=self.brand,
                receive_date_from=self.date_from,
                receive_date_to=self.date_to,
            )
        )

    def _fetch_hj_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.hj_receipt.update(
            hj_service.get_hj_receipt_by_receive_date(
                dc_object=dc, date_from=self.date_from, date_to=self.date_to, weeks=weeks
            )
        )

    def _fetch_grn_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.grn_receipt.update(
            grn.get_grn_receipt_by_receive_date(
                dc_object=dc, date_from=self.date_from, date_to=self.date_to, weeks=weeks
            )
        )

    def _fetch_manual_receives(self, receiving_data: ReceivingData, dc: DcConfig, weeks: list[ScmWeek]) -> None:
        receiving_data.non_hj_receipt.update(
            receiving_service.get_receiving_by_date(
                dc_object=dc, receive_date_from=self.date_from, receive_date_to=self.date_to
            )
        )


class PoStatusByPoSkuContext(PoStatusContext):
    def __init__(self, po_number: str, sku_code: str, item: PoStatusContextItem = None):
        super().__init__(site=None, brand=None, items=[item] if item else None)
        self.po_number = po_number
        self.sku_code = sku_code

    @cached_property
    def _purchase_orders(self) -> list[PurchaseOrder]:
        return [po_service.get_purchase_order(po_number=self.po_number, sku_code=self.sku_code)]

    @cached_property
    def _dcs_with_weeks(self) -> Iterable[tuple[DcConfig | None, ScmWeek]]:
        purchase_order = self._purchase_orders[0]
        dc = next(
            (
                (item, purchase_order.week)
                for item in dc_admin.get_enabled_sites(week=purchase_order.week, brand=purchase_order.brand).values()
                if item.bob_code == purchase_order.bob_code
            ),
            None,
        )
        return [dc] if dc else []

    def _fetch_items(self):
        dc_object = self.any_dc_object
        if not dc_object:
            raise ValueError(f"No dc object found for PO: {self.po_number}")

        po_void_item = po_void_service.get_po_void_by_po_number_and_sku_code(self.po_number, self.sku_code)

        receiving_data = PoStatusByPoSkuContext.__get_receipt_data(dc_object, self.po_number, self.sku_code)

        po_ack_data = ack_service.get_po_acknowledgements_proposed_data([self.po_number])

        po_ack_item = po_ack_data.get((self.po_number, self.sku_code))
        po_asn_item = asn_service.get_advance_shipping_notice_by_po_numbers([self.po_number]).get(
            (self.po_number, self.sku_code)
        )

        self.site = dc_object.sheet_name
        # any order works here to get brand and order week
        self.brand = self._purchase_orders[0].brand
        week = self._get_order_request_week(self._purchase_orders[0])
        dc_obj = self._dc_by_week[week]
        forecasts = self.get_forecasts([week], self.site)
        uid = (self.po_number, self.sku_code)
        order_sku = (utils.get_order_number_from_po_number(self.po_number), self.sku_code)

        if self.bulk_sku_manager.is_bulk(self.sku_code):
            forecast = self.get_forecasts_for_bulk_sku(self.sku_code, week, forecasts)
        else:
            forecast = forecasts.get((self.sku_code, dc_obj.sheet_name, week), Decimal())

        self._items = [
            PoStatusContextItem(
                dc_object=dc_obj,
                week=week,
                po_data=po_sku,
                po_voids=po_void_item,
                non_hj_receipt=receiving_data.non_hj_receipt.get(uid, []),
                receipt_override=receiving_data.receipt_override.get(uid, []),
                hj_receipt=receiving_data.hj_receipt.get(uid, []),
                grn_receipt=receiving_data.grn_receipt.get(order_sku, []),
                proposed_data=po_ack_item,
                asn_data=po_asn_item,
                units_needed=forecast,
            )
            for po_sku in self._purchase_orders
        ]

    @staticmethod
    def __get_receipt_data(dc_object: DcConfig, po_number: str, sku_code: str) -> ReceivingData:
        receipt_override_items = None
        hj_receipt_items = None
        non_hj_receipt_items = None
        grn_receipt_items = None
        uid = (po_number, sku_code)
        order_sku = (utils.get_order_number_from_po_number(po_number), sku_code)
        if dc_object.receiving_type.is_high_jump:
            receipt_override_items = receipt_override_service.get_receipt_overrides_item_by_po_number_and_sku_code(
                po_number, sku_code
            )
            hj_receipt_items = hj_service.get_hj_receipts_by_po_number_and_sku_code(
                po_number=po_number, sku_code=sku_code
            )
        elif dc_object.receiving_type.is_in_grn:
            receipt_override_items = receipt_override_service.get_receipt_overrides_item_by_po_number_and_sku_code(
                po_number, sku_code
            )
            grn_receipt_items = grn.get_grn_item_by_order_number_and_sku_code(
                utils.get_order_number_from_po_number(po_number), sku_code, dc_object
            )
        else:
            non_hj_receipt_items = receiving_service.get_receiving_item_by_po_number_and_sku_code(po_number, sku_code)
        return ReceivingData(
            hj_receipt={uid: hj_receipt_items} if hj_receipt_items else {},
            receipt_override={uid: receipt_override_items} if receipt_override_items else {},
            non_hj_receipt={uid: non_hj_receipt_items} if non_hj_receipt_items else {},
            grn_receipt={order_sku: grn_receipt_items} if grn_receipt_items else {},
        )


class ToHjReceiptsFilter:
    @staticmethod
    def filter_hj_receipts(items: list[PoStatusContextItem]):
        # remove receipts with non-matching suppliers, since TO can have multiple lines with same SKU (same PO+SKU key)
        # but different original supplier
        for po_line in items:
            if not po_line.po_data.supplier_code:
                continue
            # for now only HJ receipts have the supplier code
            po_line.hj_receipt = [
                it
                for it in po_line.hj_receipt
                if not it.supplier_code or it.supplier_code == po_line.po_data.supplier_code
            ]


class ToStatusByPoSkuContext(PoStatusByPoSkuContext):
    @cached_property
    def _purchase_orders(self) -> list[PurchaseOrder]:
        return po_service.get_transfer_order(po_number=self.po_number, sku_code=self.sku_code)

    def _fetch_items(self):
        super()._fetch_items()
        if not killswitch.transfer_orders_receipt_supplier_filtering_enabled:
            return
        ToHjReceiptsFilter.filter_hj_receipts(self._items)


class ToStatusByReceivingDateContext(PoStatusByReceivingDateContext):
    def _get_po_data(self) -> Iterable[PurchaseOrder]:
        return self._cached_po_data

    @cached_property
    def _cached_po_data(self) -> Iterable[PurchaseOrder]:
        if not (bob_codes := [dc.bob_code for dc in self.dc_objects]):
            return []
        return po_service.get_transfer_orders_by_date_range(
            bob_codes=bob_codes, date_from=self.date_from, date_to=self.date_to
        )

    def _fetch_items(self):
        super()._fetch_items()
        if not killswitch.transfer_orders_receipt_supplier_filtering_enabled:
            return
        ToHjReceiptsFilter.filter_hj_receipts(self._items)


class PackagingPoStatusContext(PoSkuFilteredPoStatusContext):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        site: str,
        brand: str,
        scheduled_date_from: date,
        scheduled_date_to: date,
        week: ScmWeek,
        sku_code: SKU_CODE = None,
        with_shipment: bool = False,
        combined_demand_dcs: Iterable[DcConfig] = None,
    ):
        super().__init__(site, brand)
        self.date_from = datetime.combine(scheduled_date_from, time())
        self.date_to = datetime.combine(scheduled_date_to + timedelta(days=1), time())
        self.week = week
        self.sku_code = sku_code
        self._with_shipment = with_shipment
        self._combined_demand_dcs = combined_demand_dcs

    def get_po_sku_filtering_query(self, dc_object: DcConfig, weeks: list[ScmWeek]) -> Select:
        return po_repo.get_po_skus_by_sku_codes_query(
            skus=self._get_needed_skus_query(),
            dc_object=dc_object,
            date_from=self.date_from,
            date_to=self.date_to,
        )

    def _get_po_data(self) -> Iterable[PurchaseOrder]:
        if not self.any_dc_object:
            return []
        return po_service.get_sku_filtered_purchase_orders(
            skus=self._get_needed_skus_query(),
            dc_object=self.any_dc_object,
            date_from=self.date_from,
            date_to=self.date_to,
            with_shipment=self._with_shipment,
        )

    @cached_property
    def _main_dc(self) -> DcConfig:
        return dc_admin.get_site(brand=self.brand, site=self.site, week=self.week)

    @cached_property
    def _weeks(self) -> tuple[ScmWeek, ...]:
        return po_repo.get_scheduled_weeks_by_sku_codes_query(
            skus=self._get_needed_skus_query(),
            dc_object=self._main_dc,
            date_from=self.date_from,
            date_to=self.date_to,
        )

    def _get_needed_skus_query(self) -> Select:
        alternative_skus = alternative_sku_mapping.get_all_alternative_sku(PackagingSkuModel)
        if self.sku_code:
            sku_codes = {self.sku_code}
            if alternative_sku := alternative_skus.get(self.sku_code):
                sku_codes.add(alternative_sku)
            return repository_utils.make_values(sku_codes, ["sku_code"]).select()
        dcs = iter(self._combined_demand_dcs or [self._main_dc])
        dc = next(dcs)
        query = packaging_depletion.get_packaging_demand_sku_codes_query(
            week=self.week, site=dc.sheet_name, alternative_skus=alternative_skus
        )
        for dc in dcs:
            query = query.union(
                packaging_depletion.get_packaging_demand_sku_codes_query(
                    week=self.week, site=dc.sheet_name, alternative_skus=alternative_skus
                )
            ).select()
        return query


@request_cache
def get_list_po(
    weeks: tuple[ScmWeek, ...],
    site: str,
    brand: str,
    sku_codes: frozenset[str] = None,
    include_shipment_data: bool = False,
) -> list[PoMasterViewResult]:
    po_status_context = PoStatusByWeekContext(
        weeks=weeks,
        site=site,
        brand=brand,
        sku_codes=sku_codes,
        include_shipment_data=include_shipment_data,
    )
    return build_po_status(po_status_context)


def get_list_po_by_receiving_date(
    site: str, brand: str, date_from: datetime, date_to: datetime
) -> list[PoMasterViewResult]:
    return build_po_status(PoStatusByReceivingDateContext(site=site, brand=brand, date_from=date_from, date_to=date_to))


def get_po_by_sku(
    weeks: tuple[ScmWeek, ...],
    dc: str,
    brand: str,
    include_shipment_data: bool = False,
    sku_codes: frozenset[str] = None,
) -> dict[SKU_CODE, list[PoMasterViewResult]]:
    items = get_list_po(weeks, dc, brand, sku_codes, include_shipment_data=include_shipment_data)
    return utils.group_by(items, lambda it: it.sku_code)


@log_wrapper
@ApplicationMetrics.functional_methods()
def build_po_status(po_status_context: PoStatusContext) -> list[PoMasterViewResult]:
    result: list[PoMasterViewResult] = []
    for po_item_context in po_status_context.items:
        base_fields = {
            "dc_object": po_item_context.dc_object,
            "purchase_order": po_item_context.po_data,
            "po_voids": po_item_context.po_voids,
            "po_ack_proposed_data": po_item_context.proposed_data,
            "asn_data": po_item_context.asn_data,
            "units_needed": po_item_context.units_needed,
        }
        if po_item_context.dc_object.receiving_type.is_high_jump:
            calculator = HjCalculations(
                hj_receipts=po_item_context.hj_receipt,
                receipt_overrides=po_item_context.receipt_override,
                autobagger_supplier=po_item_context.dc_object.autobagger_supplier,
                **base_fields,
            )
        elif po_item_context.dc_object.receiving_type.is_in_grn:
            calculator = GrnCalculations(
                grn_receipts=po_item_context.grn_receipt,
                receipt_overrides=po_item_context.receipt_override,
                **base_fields,
            )
        else:
            calculator = ManualCalculations(non_hj_receipts=po_item_context.non_hj_receipt, **base_fields)

        calculated = dataclasses.asdict(calculator.get_result())

        po_data = po_item_context.po_data
        po_fields = dataclasses.asdict(po_data)
        po_fields["dc"] = po_item_context.dc_object.sheet_name
        po_fields["shipment_data"] = po_data.shipment_data
        po_fields["allocation_price"] = po_item_context.allocation_price
        po_fields["case_price_flag"] = (
            po_item_context.allocation_price
            and (po_data.case_price / po_data.case_size if po_data.case_size else 0)
            > po_item_context.allocation_price * PO_CASE_PRICE_FLAG_PERCENTAGE_THRESHOLD
        )
        res_item = PoMasterViewResult(**po_fields, **calculated)
        result.append(res_item)
    return result


@log_wrapper
@ApplicationMetrics.functional_methods()
def build_po_status_dropdown(po_status_context: PoStatusContext) -> list[PoMasterViewResult]:
    res = build_po_status(po_status_context)
    if not (
        killswitch.transfer_orders_receipt_supplier_filtering_enabled
        and any(it.has_multiple_transfer_items for it in res)
    ):
        return res
    result = []
    for item in res:
        if item.has_multiple_transfer_items:
            context = ToStatusByPoSkuContext(
                po_number=item.po_number,
                sku_code=item.sku_code,
            )
            items = build_po_status(context)
            result.extend(items)
        else:
            result.append(item)

    return result
