import abc
import dataclasses
from collections import defaultdict
from datetime import date, datetime, time
from decimal import Decimal
from functools import cached_property

from procurement.constants.hellofresh_constant import APP_TIMEZONE, MARKET_CA, UnitOfMeasure
from procurement.constants.ordering import (
    CANC<PERSON>LED,
    <PERSON>L<PERSON><PERSON>,
    CLOSED_PARTIAL_REJECTION,
    DELIVERY_REJECTED,
    HJ_CLOSED_STATUSES,
    HJ_END_STATUSES,
    IN_PROGRESS_HJ_FORMAT,
    IS_SENT,
    IS_SENT_PENDING_ACCEPTANCE,
    N_A_PO_VOID,
    NOT_DELIVERED_PAST_DUE,
    RECEIPT_CANCELLED,
    RECEIVED_ACCURATE,
    RECEIVED_OVER,
    RECEIVED_PARTIAL_REJECTION,
    RECEIVED_UNDER,
    REJECTED,
    SUPPLIER_REJECTED,
)
from procurement.constants.protobuf import OrderingToolPoStatus
from procurement.core.request_utils import context
from procurement.core.typing import UNITS
from procurement.data.dto.highjump.receipts import HjReceiptItem
from procurement.data.dto.inventory.grn import GrnPo
from procurement.data.dto.inventory.receipt_override import ReceiptOverride
from procurement.data.dto.inventory.receiving import ReceivingItem
from procurement.data.dto.ordering.asn import AdvanceShippingNoticeDto
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.forms import receive as receive_service
from procurement.managers.ordering import PurchaseOrder, service
from procurement.managers.ordering.advance_shipping_notice import AsnPoStatusCalculations
from procurement.managers.ordering.grn import GrnPurchaseOrder
from procurement.managers.ordering.po_acknowledgement import PoAcknowledgementData


@dataclasses.dataclass
class CalculatedFieldsResult:
    scheduled_delivery_date: date | None
    total_cases_received: int
    total_quantity_received: Decimal
    total_quantity_received_by_date: dict[date, Decimal]
    total_price_received: Decimal
    case_size_received: Decimal
    actual_delivery_date: date | None
    actual_delivery_datetime: datetime | None
    po_received: bool
    po_void: bool
    po_status: str
    receive_variance: Decimal
    cost: Decimal
    proposed_quantity_cases: int | None
    proposed_units_per_cases: int | None
    proposed_quantity_units: int | None
    proposed_delivery_date: datetime | None
    asn_shipment_date: date | None
    asn_planned_delivery_time: date | None
    asn_shipped_quantity_cases: int
    asn_units_of_measure: UnitOfMeasure
    asn_case_count: int
    asn_shipped_quantity_units: int
    asn_requires_high_attention: bool
    asn_requires_attention: bool
    hj_unit: UnitOfMeasure | None
    percentage_of_the_forecasted: Decimal


class BaseCalculations(abc.ABC):
    """Represents core algorithm and serves as a base class for HJ and Manual calculations"""

    _NOT_CALCULATED = object()

    def __init__(  # pylint: disable=too-many-arguments
        self,
        dc_object: DcConfig,
        purchase_order: PurchaseOrder,
        po_voids: list[PoVoidDto],
        asn_data: AdvanceShippingNoticeDto,
        units_needed: Decimal,
        po_ack_proposed_data: PoAcknowledgementData | None = None,
    ):
        self.dc_object = dc_object
        self.purchase_order = purchase_order
        self.po_voids = po_voids
        self.po_ack_proposed_data = po_ack_proposed_data
        self.units_needed = units_needed

        self.proposed_quantity_cases = None
        self.proposed_units_per_cases = None
        self.proposed_quantity_units = None
        self.proposed_delivery_date = None

        if self.po_ack_proposed_data:
            self.proposed_quantity_cases = self.po_ack_proposed_data.quantity_cases
            self.proposed_units_per_cases = self.po_ack_proposed_data.units_per_cases
            self.proposed_quantity_units = self.po_ack_proposed_data.quantity_units
            self.proposed_delivery_date = self.po_ack_proposed_data.promised_date

        self._result = self._NOT_CALCULATED
        self.asn_calculations = AsnPoStatusCalculations(asn_data, self.purchase_order)

    @cached_property
    def scheduled_delivery_date(self) -> date | None:
        return self.purchase_order.delivery_time_start.date()

    @cached_property
    def case_size_received(self) -> Decimal:
        return round(self.total_qty_received / self.total_cases_received, 3) if self.total_cases_received else Decimal()

    @cached_property
    def total_price_received(self) -> Decimal:
        return (
            (self.total_qty_received * (self.purchase_order.case_price / self.purchase_order.case_size))
            if self.purchase_order.case_size
            else Decimal()
        )

    @cached_property
    def is_po_received(self) -> bool:
        return self._get_is_po_received()

    @cached_property
    def is_po_past_due(self) -> bool | None:
        today = date.today()
        if today > self.scheduled_delivery_date and not self.is_po_received:
            return True
        if today <= self.scheduled_delivery_date or self.is_po_received:
            return False
        return None

    @cached_property
    def is_po_void(self) -> bool:
        """
        Calculate whether this PO voided.
        """
        return service.is_po_auto_voided(self.purchase_order) or any(
            v.sku_code and v.sku_code == self.purchase_order.sku_code for v in self.po_voids
        )

    @cached_property
    def po_status(self) -> str | None:
        if self.is_po_void:
            return N_A_PO_VOID

        supplier_rejected = (
            SUPPLIER_REJECTED if self.purchase_order.ot_po_status == OrderingToolPoStatus.REJECTED.name else None
        )

        return (
            self._get_po_status()
            or supplier_rejected
            or self.asn_calculations.po_status
            or self._get_po_status_from_acknowledgment()
            or self._get_po_status_is_sent()
        )

    def _get_po_status_from_acknowledgment(self) -> str | None:
        return service.process_po_ack_status(self.po_ack_proposed_data.state if self.po_ack_proposed_data else None)

    @staticmethod
    def _get_po_status_is_sent() -> str:
        return IS_SENT_PENDING_ACCEPTANCE if context.get_request_context().market != MARKET_CA else IS_SENT

    @cached_property
    def actual_delivery_date(self) -> date | None:
        return self.actual_delivery_datetime.date() if self.actual_delivery_datetime else None

    @cached_property
    def receive_variance(self) -> Decimal:
        """
        Get difference between received and expected PO quantity.

        :return: Differed quantity
        """
        if self.po_status in (RECEIVED_UNDER, RECEIVED_OVER):
            return self.total_qty_received - self.purchase_order.quantity
        return Decimal()

    @cached_property
    def cost(self) -> Decimal:
        """
        Calculate total cost of PO if it was received.

        :return: total cost of PO
        """
        total_price = self.purchase_order.total_price
        if self.is_po_received:
            if self.po_status == RECEIVED_UNDER:
                return self.total_qty_received / self.purchase_order.quantity * total_price
            if self.po_status in (RECEIVED_ACCURATE, RECEIVED_OVER):
                return total_price
        return Decimal()

    @cached_property
    def percentage_of_the_forecasted(self) -> Decimal:
        return (
            max(self.total_qty_received, self.purchase_order.quantity) / self.units_needed
            if self.units_needed
            else Decimal()
        )

    def get_result(self) -> CalculatedFieldsResult:
        """
        Build result of algorithm's calculations.

        :return: Result of calculations
        """
        if self._result is self._NOT_CALCULATED:
            self._result = CalculatedFieldsResult(
                scheduled_delivery_date=self.scheduled_delivery_date,
                total_cases_received=self.total_cases_received,
                total_quantity_received=self.total_qty_received,
                total_quantity_received_by_date=self.total_qty_received_by_date,
                total_price_received=self.total_price_received,
                case_size_received=self.case_size_received,
                actual_delivery_date=self.actual_delivery_date,
                actual_delivery_datetime=self.actual_delivery_datetime,
                po_received=self.is_po_received,
                po_void=self.is_po_void,
                po_status=self.po_status,
                receive_variance=self.receive_variance,
                cost=self.cost,
                proposed_quantity_cases=self.proposed_quantity_cases,
                proposed_units_per_cases=self.proposed_units_per_cases,
                proposed_quantity_units=self.proposed_quantity_units,
                proposed_delivery_date=self.proposed_delivery_date,
                asn_shipment_date=self.asn_calculations.shipment_date,
                asn_planned_delivery_time=self.asn_calculations.planned_delivery_time,
                asn_shipped_quantity_cases=self.asn_calculations.shipped_quantity_cases,
                asn_units_of_measure=self.asn_calculations.unit_of_measure,
                asn_case_count=self.asn_calculations.case_count,
                asn_shipped_quantity_units=self.asn_calculations.shipped_quantity_units,
                asn_requires_high_attention=self.asn_calculations.asn_requires_high_attention,
                asn_requires_attention=self.asn_calculations.asn_requires_attention,
                hj_unit=self._get_hj_unit(),
                percentage_of_the_forecasted=self.percentage_of_the_forecasted,
            )
        return self._result

    @cached_property
    @abc.abstractmethod
    def total_cases_received(self) -> UNITS: ...

    @cached_property
    @abc.abstractmethod
    def total_qty_received(self) -> UNITS: ...

    @property
    @abc.abstractmethod
    def total_qty_received_by_date(self) -> dict[date, Decimal]: ...

    @cached_property
    @abc.abstractmethod
    def actual_delivery_datetime(self) -> datetime | None: ...

    @abc.abstractmethod
    def _get_po_status(self) -> str | None: ...

    @abc.abstractmethod
    def _get_is_po_received(self) -> bool: ...

    @abc.abstractmethod
    def _get_additional_status(self) -> bool | None: ...

    @abc.abstractmethod
    def _get_hj_unit(self) -> UnitOfMeasure | None: ...


class HjCalculations(BaseCalculations):
    """Represents calculations bound to High Jump"""

    def __init__(
        self,
        hj_receipts: list[HjReceiptItem],
        receipt_overrides: list[ReceiptOverride],
        autobagger_supplier: str,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.autobagger_supplier = autobagger_supplier
        self.hj_receipts = hj_receipts
        self.receipt_overrides = receipt_overrides

    def _get_po_status(self) -> str | None:
        hj_status = self._get_hj_status()
        status = None

        if self._check_supplier() and self.is_po_received:
            status = RECEIVED_ACCURATE
        elif hj_status == CLOSED:
            status = self._process_closed_receipt()
        elif hj_status == CLOSED_PARTIAL_REJECTION:
            status = RECEIVED_PARTIAL_REJECTION
        elif hj_status == REJECTED:
            status = DELIVERY_REJECTED
        elif hj_status == CANCELLED:
            status = RECEIPT_CANCELLED
        elif not self.is_po_received:
            status = self._process_not_received(hj_status)
        return status

    def _process_closed_receipt(self) -> str | None:
        qty = self.purchase_order.quantity
        if self.total_qty_received == qty:
            return RECEIVED_ACCURATE
        if self.total_qty_received > qty:
            return RECEIVED_OVER
        if self.total_qty_received < qty:
            return RECEIVED_UNDER
        return None

    def _process_not_received(self, hj_status: str) -> str | None:
        if hj_status is None and self.is_po_past_due:
            return NOT_DELIVERED_PAST_DUE
        if hj_status and hj_status not in HJ_END_STATUSES and self.total_qty_received == 0:
            return IN_PROGRESS_HJ_FORMAT.format(hj_status)
        return None

    def _get_hj_status(self) -> str | None:
        if self._check_supplier():
            return CLOSED
        if self.hj_receipts:
            end_status = (hj.status for hj in self.hj_receipts if hj.status in HJ_END_STATUSES)
            return next(end_status, self.hj_receipts[0].status)
        return None

    def _check_supplier(self) -> bool:
        return service.is_po_supplier_auto_received(self.purchase_order, self.autobagger_supplier)

    @cached_property
    def total_cases_received(self) -> int:
        cases_override = [ro.cases for ro in self.receipt_overrides if ro.cases is not None]
        if cases_override:
            return sum(cases_override)
        hj_rows = filter(lambda hj_row: hj_row.status in (CLOSED, CLOSED_PARTIAL_REJECTION), self.hj_receipts)
        return sum(map(lambda hj_row: hj_row.cases_received or 0, hj_rows))

    @cached_property
    def total_qty_received(self) -> UNITS:
        return Decimal(str(sum(self.total_qty_received_by_date.values())))

    @cached_property
    def total_qty_received_by_date(self) -> dict[date, Decimal]:
        if self.receipt_overrides:
            receipts = (
                (ro.receiving_date or self.scheduled_delivery_date, Decimal(str(ro.qty or 0)))
                for ro in self.receipt_overrides
            )
        else:
            receipts = (
                (hj.receipt_time_est.date(), hj.quantity_received)
                for hj in self.hj_receipts
                if hj.status in HJ_CLOSED_STATUSES and hj.receipt_time_est
            )

        result = defaultdict(Decimal)

        for r_date, qty in receipts:
            result[r_date] += qty

        return dict(result)

    @cached_property
    def actual_delivery_datetime(self) -> datetime | None:
        if self.receipt_overrides:
            latest_date = max((ro.receiving_date for ro in self.receipt_overrides if ro.receiving_date), default=None)
            return datetime.combine(latest_date or self.scheduled_delivery_date, time())
        receive_datetime = max((hj.receipt_time_est for hj in self.hj_receipts if hj.receipt_time_est), default=None)
        if receive_datetime:
            return APP_TIMEZONE.localize(receive_datetime)
        return None

    def _get_is_po_received(self) -> bool:
        return self._get_hj_status() in HJ_END_STATUSES

    def _get_additional_status(self) -> bool | None:
        return self._get_hj_status()

    def _get_hj_unit(self) -> UnitOfMeasure | None:
        return UnitOfMeasure.inexact_value_of(
            next((hj_receipt.unit for hj_receipt in self.hj_receipts if hj_receipt.unit), None)
        )


class ManualCalculations(BaseCalculations):
    """Represents calculations related to Receiving (Manual)"""

    def __init__(self, non_hj_receipts: list[ReceivingItem], **kwargs):
        super().__init__(**kwargs)
        self.non_hj_receipts = non_hj_receipts

    def _get_po_status(self) -> str | None:
        if self.is_po_received:
            return self._process_received_po()
        return self._process_not_received_po()

    def _process_received_po(self) -> str | None:
        return service.process_received_po(self.purchase_order.quantity, self.total_qty_received)

    def _process_not_received_po(self) -> str | None:
        if self.non_hj_receipts:
            return DELIVERY_REJECTED
        if self.is_po_past_due:
            return NOT_DELIVERED_PAST_DUE
        return None

    @cached_property
    def total_cases_received(self) -> int:
        return sum(
            map(
                lambda non_hj_row: non_hj_row.total_cases_received or 0,
                self.non_hj_receipts,
            )
        )

    @cached_property
    def total_qty_received(self) -> UNITS:
        return sum(map(receive_service.get_manual_receive_row_total_qty, self.non_hj_receipts))

    @cached_property
    def total_qty_received_by_date(self) -> dict[date, Decimal]:
        if self.actual_delivery_date:
            return {self.actual_delivery_date: self.total_qty_received}
        return {}

    @cached_property
    def actual_delivery_datetime(self) -> datetime | None:
        if self.is_po_received:
            dates = [nhj.receive_timestamp for nhj in self.non_hj_receipts if nhj.receive_timestamp]
            if dates:
                return min(dates)
        return None

    def _get_is_po_received(self) -> bool:
        return self.total_qty_received > 0

    def _get_additional_status(self) -> bool | None:
        return None

    def _get_hj_unit(self) -> UnitOfMeasure | None:
        return None


class GrnCalculations(BaseCalculations):
    """Represents calculations bound to GRN"""

    def __init__(self, grn_receipts: list[GrnPo], receipt_overrides: list[ReceiptOverride], **kwargs):
        super().__init__(**kwargs)
        self.grn_receipts = grn_receipts
        self.receipt_overrides = receipt_overrides
        self.grn_purchase_order = GrnPurchaseOrder(
            po_number=self.purchase_order.po_number,
            sku_code=self.purchase_order.sku_code,
            quantity=self.purchase_order.quantity,
            delivery_time_start=self.purchase_order.delivery_time_start,
            supplier_code=self.purchase_order.supplier_code,
            grn_receipts=self.grn_receipts,
        )

    def _get_po_status(self) -> str | None:
        return service.calculate_po_status(
            po_voids={(po_void.po_number, po_void.sku_code) for po_void in self.po_voids},
            purchase_order=self.grn_purchase_order,
            po_ack_status=self.po_ack_proposed_data.state if self.po_ack_proposed_data else None,
            asn_calculations=self.asn_calculations,
            override_received_units=self._received_override,
        )

    @cached_property
    def total_cases_received(self) -> int:
        cases_override = [ro.cases for ro in self.receipt_overrides if ro.cases is not None]
        if cases_override:
            return sum(cases_override)
        return int(
            sum(r.cases_received or 0 for r in self.grn_receipts)
            or self.grn_purchase_order.grn_units / (self.purchase_order.case_size or 1)
        )

    @cached_property
    def total_qty_received(self) -> UNITS:
        if self._received_override is not None:
            return self._received_override
        return self.grn_purchase_order.grn_units

    @cached_property
    def _received_override(self) -> UNITS | None:
        if self.receipt_overrides:
            return Decimal(str(sum(over_row.qty or 0 for over_row in self.receipt_overrides)))
        return None

    @cached_property
    def actual_delivery_datetime(self) -> datetime | None:
        if self.receipt_overrides:
            latest_date = max((ro.receiving_date for ro in self.receipt_overrides if ro.receiving_date), default=None)
            return datetime.combine(latest_date or self.scheduled_delivery_date, time())
        if self.grn_receipts:
            if dates := [grn.receipt_time_est for grn in self.grn_receipts if grn.receipt_time_est]:
                return max(dates).astimezone(self.dc_object.timezone)
        return None

    def _get_is_po_received(self) -> bool:
        return self.total_qty_received > 0

    def _get_additional_status(self) -> bool | None:
        return None

    def _get_hj_unit(self) -> UnitOfMeasure | None:
        return None

    @cached_property
    def total_qty_received_by_date(self) -> dict[date, Decimal]:
        if self.actual_delivery_date:
            return {self.actual_delivery_date: self.total_qty_received}
        return {}
