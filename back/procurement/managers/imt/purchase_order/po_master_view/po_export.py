import dataclasses
import logging
from collections import defaultdict
from datetime import datetime

from sqlalchemy import Select

from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import PO_NUMBER, SKU_NAME
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import highjump as hj_service
from procurement.managers.forms import receive as receiving_service
from procurement.managers.ordering import PurchaseOrder
from procurement.managers.ordering import service as po_service
from procurement.managers.ordering.service import PoExportContext
from procurement.managers.receipt import grn

logger = logging.getLogger(__name__)


@request_cache
def get_po_export_by_po_number(week: ScmWeek) -> dict[PO_NUMBER, dict[SKU_NAME, list[PurchaseOrder]]]:
    pos = defaultdict(dict)
    for brand in brand_admin.get_brand_ids(week):
        for site in dc_admin.get_enabled_sites(week, brand):
            for po in get_po_export_data(PoExportContext(weeks=(week,), site=site, brand=brand)):
                pos[po.po_number][po.sku_name] = po
    return pos


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_export_data(po_export_contex: PoExportContext, load_future: bool = True) -> list[PurchaseOrder]:
    ordering_tool_data = po_service.get_purchase_orders(po_export_contex)

    if len(po_export_contex.weeks) == 1 and load_future:
        next_week_po_context = dataclasses.replace(
            po_export_contex, weeks=(po_export_contex.weeks[0] + 1,), is_future_pull=True
        )
        # see: https://hellofresh.atlassian.net/wiki/spaces/GD/pages/1744241901/POs+-+Future+Week+Pulls
        ordering_tool_data += po_service.get_purchase_orders(next_week_po_context)

    return ordering_tool_data


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_po_sku_filtered_po_data(
    dc_object: DcConfig,
    po_sku_filtering_query: Select,
    filter_general_supplier: bool = False,
    filter_hf_supplier: bool = False,
) -> list[PurchaseOrder]:
    return po_service.get_po_sku_filtered_purchase_orders(
        dc_object, po_sku_filtering_query, filter_general_supplier, filter_hf_supplier
    )


def get_received_weeks(site: str, brand: str, receive_date_from: datetime, receive_date_to: datetime) -> set[ScmWeek]:
    # TODO: not sure if wh_id will ever change so pulling just current id, if it's HJ
    # also assuming that dc receiving type never changes back from HJ to manual
    week_config = brand_admin.get_week_config(brand)
    week = ScmWeek.from_date(receive_date_to, week_config)
    latest_dc_object = dc_admin.get_site(brand=brand, site=site, week=week)
    wh_id = None
    if latest_dc_object:
        wh_id = latest_dc_object.high_jump_name
    weeks = set()
    if wh_id:
        weeks = hj_service.get_received_weeks(
            wh_id=wh_id, receive_date_from=receive_date_from, receive_date_to=receive_date_to
        )
    weeks.update(
        receiving_service.get_received_weeks(
            site=site, receive_date_from=receive_date_from, receive_date_to=receive_date_to
        )
    )
    if latest_dc_object.receiving_type.is_in_grn:
        weeks.update(
            grn.get_received_weeks(dc_object=latest_dc_object, date_from=receive_date_from, date_to=receive_date_to)
        )
    return weeks
