import dataclasses
import logging
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from decimal import Decimal

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.constants.ordering import LOAD_VOIDED, LOAD_WITHDREW, ORDER_REMOVED
from procurement.core.dates import ScmWeek
from procurement.core.typing import BRAND, PO_NUMBER, SITE, SKU_CODE, SKU_NAME, UNITS
from procurement.data.dto.ordering.purchase_order.common import PoSku
from procurement.data.dto.ordering.shipment import Shipment
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.managers.admin import dc_admin
from procurement.managers.imt import bulk_sku, buyers
from procurement.managers.imt.purchase_order import PoMasterViewResult, po_master_view
from procurement.managers.imt.purchase_order.po_master_view import (
    PoStatusByWeekContext,
    ToStatusByPoSkuContext,
    ToStatusByReceivingDateContext,
)
from procurement.managers.sku import alternative_sku_mapping, culinary_sku
from procurement.repository.ordering import purchase_order as po_repo

logger = logging.getLogger(__name__)


@dataclasses.dataclass
class PoShipmentResult:
    load_number: str | None = None
    pallet_count: int | None = None
    carrier_name: str | None = None
    origin_location: str | None = None
    appointment_time: datetime | None = None

    @staticmethod
    def from_shipment(shipment: Shipment) -> "PoShipmentResult":
        location = shipment.origin_location
        location_text = ", ".join(val for val in (location.locality, location.administrative_area) if val)
        return PoShipmentResult(
            load_number=shipment.load_number,
            pallet_count=shipment.pallet_count,
            carrier_name=shipment.carrier_name,
            origin_location=location_text or None,
            appointment_time=shipment.appointment_time,
        )

    @staticmethod
    def is_shipment_event_acceptable(shipment) -> bool:
        return shipment.execution_event not in (LOAD_WITHDREW, LOAD_VOIDED, ORDER_REMOVED)


# TODO: replace that with some kind of composition
@dataclasses.dataclass
class PoStatusResult:
    week: ScmWeek
    brand: str
    site: str
    po_number: PO_NUMBER
    po_status: str
    receive_variance: int
    supplier: str
    sku_name: SKU_NAME
    sku_code: SKU_CODE
    scheduled_delivery_date: date
    order_size: int
    case_size: Decimal
    case_price_flag: bool | None
    allocation_price: Decimal | None
    quantity_ordered: Decimal
    quantity_received: UNITS
    cases_received: int
    case_size_received: Decimal
    emergency_reason: str
    case_price: Decimal
    total_price: Decimal
    ship_method: str
    po_buyer: str
    proposed_quantity_cases: int
    proposed_units_per_case: int
    proposed_quantity_units: int
    proposed_delivery_date: datetime
    total_price_received: Decimal
    asn_shipment_date: datetime | None
    asn_planned_delivery_time: datetime | None
    asn_shipped_quantity_cases: int
    asn_units_of_measure: UnitOfMeasure
    asn_case_count: int
    asn_shipped_quantity_units: int
    asn_requires_high_attention: bool
    asn_requires_attention: bool
    shipment_data: PoShipmentResult | None
    date_received: date | None
    datetime_received: datetime | None
    order_unit: UnitOfMeasure | None
    percentage_of_the_forecasted: Decimal | None
    category: str | None = None
    buyers: str | None = None
    purchasing_unit: UnitOfMeasure | None = None
    po_type: str | None = None
    transfer_source_bob_code: str | None = None
    has_multiple_transfer_items: bool = False

    @staticmethod
    def from_po_master_view_result(
        po_item: PoMasterViewResult, shipment_data: PoShipmentResult | None
    ) -> "PoStatusResult":
        return PoStatusResult(
            week=po_item.week,
            brand=po_item.brand,
            site=po_item.dc,
            po_number=po_item.po_number,
            po_status=po_item.po_status,
            receive_variance=abs(po_item.receive_variance),
            supplier=po_item.supplier,
            sku_name=po_item.sku_name,
            sku_code=po_item.sku_code,
            scheduled_delivery_date=po_item.scheduled_delivery_date,
            order_size=po_item.order_size,
            order_unit=po_item.case_unit,
            case_size=po_item.case_size,
            allocation_price=po_item.allocation_price,
            case_price_flag=po_item.case_price_flag,
            quantity_ordered=po_item.quantity,
            quantity_received=po_item.total_quantity_received,
            cases_received=po_item.total_cases_received,
            case_size_received=po_item.case_size_received,
            emergency_reason=po_item.emergency_reason,
            case_price=po_item.case_price,
            total_price=po_item.total_price,
            ship_method=po_item.shipping_method,
            po_buyer=po_item.po_buyer,
            proposed_quantity_cases=po_item.proposed_quantity_cases,
            proposed_units_per_case=po_item.proposed_units_per_cases,
            proposed_quantity_units=po_item.proposed_quantity_units,
            proposed_delivery_date=po_item.proposed_delivery_date,
            total_price_received=po_item.total_price_received,
            asn_shipment_date=po_item.asn_shipment_date,
            asn_planned_delivery_time=po_item.asn_planned_delivery_time,
            asn_shipped_quantity_cases=po_item.asn_shipped_quantity_cases,
            asn_units_of_measure=po_item.asn_units_of_measure,
            asn_case_count=po_item.asn_case_count,
            asn_shipped_quantity_units=po_item.asn_shipped_quantity_units,
            asn_requires_high_attention=po_item.asn_requires_high_attention,
            asn_requires_attention=po_item.asn_requires_attention,
            shipment_data=shipment_data,
            date_received=po_item.actual_delivery_date,
            percentage_of_the_forecasted=po_item.percentage_of_the_forecasted,
            datetime_received=po_item.actual_delivery_datetime,
            transfer_source_bob_code=po_item.transfer_source_bob_code,
            has_multiple_transfer_items=po_item.has_multiple_transfer_items,
        )


def get_po_status(brand: str, sites: Iterable[str], weeks: tuple[ScmWeek, ...]) -> dict[SITE, list[PoStatusResult]]:
    res = {}
    for site in sites:
        master_items = po_master_view.get_list_po(weeks=weeks, site=site, brand=brand, include_shipment_data=True)
        res[site] = to_extended_po_status(brand, site, master_items)
    return res


def get_po_status_by_receiving_date_range(
    brand: str, sites: Iterable[str], date_from: datetime, date_to: datetime
) -> list[PoStatusResult]:
    return [
        item
        for site in sites
        for item in to_extended_po_status(
            brand, site, po_master_view.get_list_po_by_receiving_date(site, brand, date_from, date_to)
        )
    ]


def get_po_status_for_buyer_dashboard(
    week: ScmWeek, sites: Iterable[str], brand: str, skus_by_site: dict[SITE, set[SKU_CODE]]
) -> dict[SITE, list[PoStatusResult]]:
    res = {}
    for site in sites:
        sku_codes = skus_by_site.get(site)
        res[site] = to_extended_po_status(
            brand=brand,
            site=site,
            sku_po_master_view_list=po_master_view.get_list_po(
                weeks=(week,), site=site, brand=brand, sku_codes=frozenset(sku_codes) if sku_codes else None
            ),
        )
    return res


@dataclasses.dataclass
class PoStatusIncludeFlags:
    include_bulk_skus: bool
    include_org_cv_skus: bool
    include_packaged_skus: bool
    include_consolidated_dcs: bool = False


def get_po_status_by_sku_code(
    sku_code: str, weeks: tuple[ScmWeek, ...], site: str, brand: str, include_flags: PoStatusIncludeFlags
) -> list[PoStatusResult]:
    sku_codes = [sku_code]
    if include_flags.include_consolidated_dcs:
        dcs = dc_admin.get_all_sites_with_consolidation_by_brand(
            brand=brand, sites=[site], week=weeks[0], with_multi_brand=False
        ).values()
    else:
        dcs = [dc_admin.get_site(brand=brand, site=site, week=weeks[0])]
    res = []
    for dc in filter(None, dcs):
        if include_flags.include_bulk_skus and (
            bulk_sku_item := bulk_sku.get_bulk_skus_mapping(dc.brand).get(sku_code)
        ):
            sku_codes.append(bulk_sku_item.bulk_sku_code)
        if include_flags.include_org_cv_skus:
            org_cv_mapping = alternative_sku_mapping.get_all_alternative_sku(OrganicSkuModel)
            org_cv_skus = filter(None, {org_cv_mapping.get(sku) for sku in sku_codes})
            sku_codes.extend(org_cv_skus)
        if include_flags.include_packaged_skus and (
            packaged_sku_items := bulk_sku.get_packaged_skus_by_bulk(dc.brand).get(sku_code)
        ):
            sku_codes.extend(packaged_sku_items)

        po_status_context = PoStatusByWeekContext(
            weeks=weeks,
            site=dc.sheet_name,
            brand=dc.brand,
            sku_codes=frozenset(sku_codes),
            include_shipment_data=True,
        )
        res.extend(po_master_view.build_po_status_dropdown(po_status_context))
    return to_extended_po_status(brand=brand, site=site, sku_po_master_view_list=res)


def get_to_status(po_number: PO_NUMBER, sku_code: SKU_CODE) -> list[PoStatusResult]:
    context = ToStatusByPoSkuContext(po_number=po_number, sku_code=sku_code)
    master_items = po_master_view.build_po_status(context)
    return to_extended_po_status(brand=context.brand, site=context.site, sku_po_master_view_list=master_items)


def get_to_status_by_receiving_date_range(
    brand: str, sites: Iterable[str], date_from: datetime, date_to: datetime
) -> list[PoStatusResult]:
    res = []
    for site in sites:
        context = ToStatusByReceivingDateContext(brand=brand, site=site, date_from=date_from, date_to=date_to)
        master_items = po_master_view.build_po_status(context)
        res.extend(to_extended_po_status(brand=context.brand, site=context.site, sku_po_master_view_list=master_items))
    return res


def get_po_status_for_multi_brand_dc(multi_brand_dc: str, sku_code: str, week: ScmWeek) -> list[PoStatusResult]:
    sites = dc_admin.get_all_enabled_multi_brand_sites(week).get(multi_brand_dc, [])
    return [
        item
        for site in sites
        for item in get_po_status_by_sku_code(
            sku_code=sku_code,
            weeks=(week,),
            site=site.sheet_name,
            brand=site.brand,
            include_flags=PoStatusIncludeFlags(
                include_bulk_skus=True, include_org_cv_skus=True, include_packaged_skus=False
            ),
        )
    ]


def build_po_res_base_with_shipment(sku_po_master_view: PoMasterViewResult) -> PoStatusResult:
    if (shipment_data := sku_po_master_view.shipment_data) and PoShipmentResult.is_shipment_event_acceptable(
        shipment_data
    ):
        shipment_result = PoShipmentResult.from_shipment(shipment_data)
    else:
        shipment_result = None

    return PoStatusResult.from_po_master_view_result(sku_po_master_view, shipment_result)


def to_extended_po_status(
    brand: str, site: str, sku_po_master_view_list: list[PoMasterViewResult]
) -> list[PoStatusResult]:
    if not sku_po_master_view_list:
        return []
    # TODO: could be different buyers for standardized SKUs
    buyers_by_sku = buyers.get_buyers_by_sku(brand, site)
    sku_codes = frozenset(item.sku_code for item in sku_po_master_view_list)
    sku_details = culinary_sku.get_sku_meta_by_code(site, sku_codes=sku_codes)
    result = []
    for sku_po_master_view in sku_po_master_view_list:
        po_res = build_po_res_base_with_shipment(sku_po_master_view)
        if sku := sku_details.get(sku_po_master_view.sku_code):
            po_res.category = sku.category
            po_res.purchasing_unit = sku.unit
        po_res.buyers = buyers_by_sku.get(sku_po_master_view.sku_code)
        result.append(po_res)
    return result


def purchase_order_to_master_view(po_items: list[PoSku]) -> list[PoMasterViewResult]:
    result = []
    for po_item in po_items:
        try:
            context = po_master_view.PoStatusByPoSkuContext(po_item.po_number, po_item.sku_code)
            result.extend(po_master_view.build_po_status(context))
        except ValueError as err:
            logger.error(err)
            continue
    return result


def get_today_pos_buyer_dashboard(buyer_id: int) -> dict[BRAND, dict[SITE, list[PoStatusResult]]]:
    po_items_by_brand_site: dict[tuple[BRAND, SITE], list] = defaultdict(list)

    for item in purchase_order_to_master_view(po_repo.get_pos_by_buyer_and_delivery_time_start(buyer_id)):
        po_items_by_brand_site[(item.brand, item.dc)].append(item)

    result = defaultdict(dict)
    for (brand, site), items in po_items_by_brand_site.items():
        result[brand][site] = to_extended_po_status(brand=brand, site=site, sku_po_master_view_list=items)

    return result
