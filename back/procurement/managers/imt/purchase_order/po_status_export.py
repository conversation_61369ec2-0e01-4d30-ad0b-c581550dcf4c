import logging
from collections import defaultdict

from procurement.constants.hellofresh_constant import BRAND_HF
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.googlesheet_model import core
from procurement.data.googlesheet_model.po_status_export import PoStatusSheet
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.imt import buyers
from procurement.managers.ordering import map_order_units

from . import po_master_view

logger = logging.getLogger(__name__)
#  TODO: migrate to same classes as imt_daily_exceptions


def _convert_item(item: po_master_view.PoMasterViewResult, buyers_by_sku: dict[SKU_CODE, str]) -> dict:
    return {
        "supplier": item.supplier,
        "po_sku": item.po_sku_combination,
        "po_num": item.po_number,
        "sku_name": item.sku_name,
        "sku": item.sku_code,
        "delivery_date": item.scheduled_delivery_date,
        "status": item.po_status,
        "order_size": item.order_size,
        "order_unit": map_order_units(item.order_unit),
        "case_size": item.case_size,
        "case_unit": str(item.case_unit),
        "ordered": item.quantity,
        "received": item.total_quantity_received,
        "cases_received": item.total_cases_received,
        "emergency_reason": item.emergency_reason,
        "received_date": item.actual_delivery_date,
        "shipping_method": item.shipping_method,
        "week": item.week,
        "buyer": buyers_by_sku.get(item.sku_code),
    }


def po_status_export() -> None:
    gsheet_id = gsheet_admin.get_gsheet_id(PoStatusSheet.parent_doc, brand=BRAND_HF)
    if not gsheet_id:
        raise ValueError("No gsheet set to export PO Status")

    current_week = ScmWeek.current_week()

    data_by_brand_site = defaultdict(list)
    for week in ScmWeek.range(current_week - 2, current_week + 5):
        for brand in brand_admin.get_brand_ids(week):
            for site_obj in dc_admin.get_enabled_sites(week, brand).values():
                site = site_obj.sheet_name
                site_data = po_master_view.get_list_po((week,), site, brand)
                buyers_by_sku = buyers.get_buyers_by_sku(brand, site)
                data_by_brand_site[(brand, site)].extend(_convert_item(data, buyers_by_sku) for data in site_data)

    for (brand, site), data in data_by_brand_site.items():
        core.write_to_sheet(PoStatusSheet(brand, site), data, gsheet_id)
