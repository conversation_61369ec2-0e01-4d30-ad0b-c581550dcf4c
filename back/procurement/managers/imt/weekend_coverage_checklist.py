import dataclasses
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from enum import StrEnum
from typing import Any, NamedTuple

from sqlalchemy import ColumnElement

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.constants.hellofresh_constant import CROSSDOCK_SHIP_METHOD, FREIGHT_ON_BOARD_SHIP_METHOD
from procurement.core.dates import ScmWeek
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import ActionType, WCCEvent
from procurement.core.exceptions.validation_errors import BatchValidationException, InputParamsValidationException
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BRAND, PO_NUMBER, SITE, SKU_CODE, SKU_NAME, PoSku
from procurement.data.dto.inventory.wcc import WeekendCoverageChecklistFullItem
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.data.models.inventory.weekend_coverage_checklist import WeekendCoverageChecklistModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.imt.models import ChecklistModel, ChecklistSubmissionModel
from procurement.managers.imt.purchase_order import po_master_view
from procurement.managers.sku import alternative_sku_mapping
from procurement.repository.inventory import weekend_coverage_checklist as checklist_repo
from procurement.repository.ordering import purchase_order as po_repo


@dataclasses.dataclass
class PoMeta:
    brand: str
    dc: str
    week: str
    po_number: str
    emergency_reason: str
    shipping_method: str
    carrier_name: str
    skus: dict[SKU_NAME, SKU_CODE] = dataclasses.field(default_factory=dict)


@dataclasses.dataclass
class BrandSiteSku:
    brand: str
    site: str
    sku_code: str


@dataclasses.dataclass
class WCCNotifyModel:
    action_type: ActionType
    po_number: PO_NUMBER
    sku_code: str
    brand: str
    site: str
    week: ScmWeek
    count: int


class PoShipMethod(NamedTuple):
    po_number: PO_NUMBER
    ship_method: str


class _ValidationErrorMsg(StrEnum):
    PO_NOT_FOUND_IN_WEEK = "PO Number is not found in selected week"
    SKU_NOT_FOUND_IN_PO = "SKU Name is not found in this PO"
    INVALID_CHECKLIST_INPUT = "Invalid Weekend Coverage Checklist input"
    FIELD_IS_MANDATORY = "This field is mandatory"
    SHIP_METHOD_FOB_DATE_ERROR = (
        "'FOB Pick Up Date' can't be set for non 'Freight on Board' or 'Crossdock' shipping order"
    )

    def __str__(self) -> str:
        return str.__str__(self)


def get_checklist_preview(
    weeks: tuple[ScmWeek, ...], brand: str = None, sites: Iterable[str] = None
) -> dict[str, dict[PO_NUMBER, int]]:
    organic_sku_map = alternative_sku_mapping.get_all_alternative_sku(OrganicSkuModel)
    result = defaultdict(dict)
    for row in checklist_repo.get_checklist_preview_counts(weeks=weeks, brand=brand, sites=sites):
        result[f"{row.brand}-{row.site}-{row.sku_code}"][row.po_number] = row.count
        if alt_sku := organic_sku_map.get(row.sku_code):
            result[f"{row.brand}-{row.site}-{alt_sku}"][row.po_number] = row.count
    return dict(result)


def get_po_numbers_data_by_week(week: ScmWeek) -> list[PoShipMethod]:
    return [PoShipMethod(po_number, po_meta.shipping_method) for po_number, po_meta in _get_po_meta(week).items()]


def get_pos_sku_names(week: ScmWeek, po_number: str) -> list[str]:
    po_meta = _get_po_meta(week, po_numbers=frozenset((po_number,)))
    return list(po_meta[po_number].skus.keys()) if po_meta else []


def _get_po_statuses(
    week: ScmWeek, items: Iterable[BrandSiteSku]
) -> dict[tuple[SITE, BRAND, SKU_CODE, PO_NUMBER], str]:
    skus_by_site_brand = defaultdict(set)
    for item in items:
        skus_by_site_brand[(item.site, item.brand)].add(item.sku_code)

    status_by_site_brand_sku_po = defaultdict(str)
    for (site, brand), sku_codes in skus_by_site_brand.items():
        for item in po_master_view.get_list_po((week,), site, brand, frozenset(sku_codes)):
            status_by_site_brand_sku_po[(site, brand, item.sku_code, item.po_number)] = item.po_status
    return status_by_site_brand_sku_po


def _build_checklist_items(items: Iterable[WeekendCoverageChecklistFullItem], week: ScmWeek) -> list[ChecklistModel]:
    po_statuses = _get_po_statuses(
        week, [BrandSiteSku(brand=item.brand, site=item.site, sku_code=item.sku_code) for item in items]
    )
    available_sites = context.get_request_context().user_info.user_permissions.available_sites
    return [
        _map_checklist_model_row(row, po_statuses[(row.site, row.brand, row.sku_code, row.po_number)])
        for row in items
        if row.site in available_sites.get(row.brand, [])
    ]


def get_checklist_by_week(week: ScmWeek) -> list[ChecklistModel]:
    items = checklist_repo.get_checklist_by_week(week=week)
    return _build_checklist_items(items, week)


def get_checklist_by_po_sku(po_number: str, sku_code: str) -> list[ChecklistModel]:
    items = checklist_repo.get_checklist_by_po_sku(po_number=po_number, sku_code=sku_code)
    week = next(iter(items)).week
    return _build_checklist_items(items, week)


def _map_checklist_model_row(row: WeekendCoverageChecklistFullItem, po_status: str) -> ChecklistModel:
    return ChecklistModel(
        ui_row_id=None,
        po_number=row.po_number,
        sku_name=row.sku_name,
        po_landing_day=row.po_landing_day,
        production_day_affected=row.production_day_affected,
        to_check=row.to_check,
        contact_name_vendor_carrier=row.contact_name_vendor_carrier,
        email_phone=row.email_phone,
        back_up_vendor=row.back_up_vendor,
        checklist_id=row.id,
        brand=row.brand,
        site=row.site,
        sku_code=row.sku_code,
        updated_by=row.updated_by,
        status=row.status,
        comment=row.comment,
        carrier_name=(
            row.carrier_name
            if row.shipping_method and row.shipping_method.lower() == FREIGHT_ON_BOARD_SHIP_METHOD
            else ""
        ),
        ship_method=row.shipping_method,
        fob_pick_up_date=row.fob_pick_up_date,
        po_status=po_status,
    )


def validate_checklist_submission(week: ScmWeek, items: Iterable[ChecklistSubmissionModel]):
    po_numbers = frozenset(item.po_number for item in items)
    po_meta = _get_po_meta(week, po_numbers)
    errors = []
    for item in items:
        row_errors = _validate_all_mandatory_field(
            [
                (item.po_number, "poNumber"),
                (item.sku_name, "skuName"),
                (item.po_landing_day, "poLandingDay"),
                (item.production_day_affected, "productionDayAffected"),
            ]
        )
        if not row_errors and item.po_number not in po_meta:
            row_errors["poNumber"] = _ValidationErrorMsg.PO_NOT_FOUND_IN_WEEK
        if not row_errors and item.sku_name not in po_meta[item.po_number].skus:
            row_errors["skuName"] = _ValidationErrorMsg.SKU_NOT_FOUND_IN_PO
        if not row_errors and not _validate_ship_method_fob_date(
            po_meta[item.po_number].shipping_method, item.fob_pick_up_date
        ):
            row_errors["fobPickUpDate"] = _ValidationErrorMsg.SHIP_METHOD_FOB_DATE_ERROR
        if row_errors:
            errors.append(BatchValidationException.build_error(index=item.ui_row_id, columns=row_errors))
    if errors:
        raise BatchValidationException(_ValidationErrorMsg.INVALID_CHECKLIST_INPUT, errors)


def validate_ship_method_fob_date(checklist_id: int, fob_date: date | None) -> None:
    checklist = checklist_repo.get_checklist_by_id(checklist_id=checklist_id)
    po_meta = _get_po_meta(checklist.week, frozenset({checklist.po_number}))
    shipping_method = po_meta.get(checklist.po_number) and po_meta[checklist.po_number].shipping_method
    if not _validate_ship_method_fob_date(shipping_method, fob_date):
        raise InputParamsValidationException(_ValidationErrorMsg.SHIP_METHOD_FOB_DATE_ERROR)


def _validate_all_mandatory_field(values_ui_names: list[tuple[Any, str]]) -> dict[str, str]:
    return {ui_name: _ValidationErrorMsg.FIELD_IS_MANDATORY for value, ui_name in values_ui_names if not value}


def _validate_ship_method_fob_date(shipping_method: str, fob_date: date | None) -> bool:
    return not fob_date or shipping_method.lower() in (FREIGHT_ON_BOARD_SHIP_METHOD, CROSSDOCK_SHIP_METHOD)


def prepare_data_for_submit(
    week: ScmWeek,
    items: Iterable[ChecklistSubmissionModel],
    po_meta: dict[PO_NUMBER, PoMeta],
    ot_site_map: dict[str, DcConfig],
) -> list[dict[ColumnElement, Any]]:
    validate_checklist_submission(week, items)
    data = []
    now = datetime.now()
    user_id = context.get_request_context().user_info.user_id
    market = context.get_request_context().market
    for item in items:
        data.append(
            {
                WeekendCoverageChecklistModel.po_number: item.po_number,
                WeekendCoverageChecklistModel.production_day_affected: item.production_day_affected,
                WeekendCoverageChecklistModel.po_landing_day: item.po_landing_day,
                WeekendCoverageChecklistModel.to_check: item.to_check,
                WeekendCoverageChecklistModel.contact_name_vendor_carrier: item.contact_name_vendor_carrier,
                WeekendCoverageChecklistModel.email_phone: item.email_phone,
                WeekendCoverageChecklistModel.back_up_vendor: item.back_up_vendor,
                WeekendCoverageChecklistModel.week: week.to_number_format(),
                WeekendCoverageChecklistModel.brand: po_meta[item.po_number].brand,
                WeekendCoverageChecklistModel.site: ot_site_map[po_meta[item.po_number].dc].sheet_name,
                WeekendCoverageChecklistModel.sku_code: po_meta[item.po_number].skus[item.sku_name],
                WeekendCoverageChecklistModel.updated_by_id: user_id,
                WeekendCoverageChecklistModel.last_updated: now,
                WeekendCoverageChecklistModel.fob_pick_up_date: item.fob_pick_up_date,
                WeekendCoverageChecklistModel.market: market,
            }
        )
    return data


def submit_checklist(week: ScmWeek, items: Iterable[ChecklistSubmissionModel]) -> list[ChecklistModel]:
    po_meta = _get_po_meta(week, frozenset(item.po_number for item in items))
    ot_site_map = _get_ot_site_map(week)
    data = prepare_data_for_submit(week, items, po_meta, ot_site_map)
    checklist_ids = checklist_repo.insert_checklist(rows=data)
    result = []
    po_statuses = _get_po_statuses(
        week,
        [
            BrandSiteSku(
                brand=item[WeekendCoverageChecklistModel.brand],
                site=item[WeekendCoverageChecklistModel.site],
                sku_code=item[WeekendCoverageChecklistModel.sku_code],
            )
            for item in data
        ],
    )
    email = context.get_request_context().user_info.email

    for checklist_id, item in zip(checklist_ids, items):
        po_data = po_meta[item.po_number]
        brand = po_data.brand
        site = ot_site_map[po_data.dc].sheet_name
        sku_code = po_data.skus[item.sku_name]
        result.append(
            ChecklistModel(
                **dataclasses.asdict(item),
                checklist_id=checklist_id.id,
                brand=brand,
                site=site,
                sku_code=sku_code,
                updated_by=email,
                ship_method=po_data.shipping_method,
                carrier_name=(
                    po_data.carrier_name if po_data.shipping_method.lower() == FREIGHT_ON_BOARD_SHIP_METHOD else ""
                ),
                po_status=po_statuses[(site, brand, sku_code, item.po_number)],
            )
        )
        _publish_update_event(ActionType.ADDED, week, brand, site, PoSku(sku_code=sku_code, po_number=item.po_number))
    return result


def _get_ot_site_map(week: ScmWeek) -> dict[str, DcConfig]:
    return {
        dc_object.ordering_tool_name: dc_object
        for brand in brand_admin.get_brand_ids(week)
        for dc_object in dc_admin.get_enabled_sites(week=week, brand=brand).values()
    }


@request_cache
def _get_po_meta(week: ScmWeek, po_numbers: frozenset[str] = None) -> dict[PO_NUMBER, PoMeta]:
    po_meta = {}
    ot_dcs = list(_get_ot_site_map(week).keys())
    for row in po_repo.get_po_meta_by_week(week=week, ot_dcs=ot_dcs, po_numbers=po_numbers):
        meta = po_meta.get(row.po_number) or PoMeta(
            row.brand,
            row.ot_dc,
            row.week,
            row.po_number,
            row.emergency_reason,
            shipping_method=row.shipping_method,
            carrier_name=row.carrier_name,
        )
        meta.skus[row.sku_name] = row.sku_code
        po_meta[row.po_number] = meta
    return po_meta


def update_checklist_item(checklist_id: int, data: dict) -> None:
    parameter_name, value = next(iter(data.items()))
    if parameter_name == "fob_pick_up_date":
        value = datetime.strptime(value, DATE_FORMAT) if value else None
        validate_ship_method_fob_date(checklist_id, value)
    user_id = context.get_request_context().user_info.user_id
    checklist_repo.update_checklist_item(checklist_id, parameter_name, value, user_id)
    new_wcc = checklist_repo.get_checklist_by_id(checklist_id=checklist_id)
    _publish_update_event(
        ActionType.UPDATED,
        new_wcc.week,
        new_wcc.brand,
        new_wcc.site,
        PoSku(sku_code=new_wcc.sku_code, po_number=new_wcc.po_number),
    )


def delete_checklist_item(checklist_id: int) -> None:
    old_wcc = checklist_repo.get_checklist_by_id(checklist_id=checklist_id)
    checklist_repo.delete_checklist_item(checklist_id)
    _publish_update_event(
        ActionType.DELETED,
        old_wcc.week,
        old_wcc.brand,
        old_wcc.site,
        PoSku(sku_code=old_wcc.sku_code, po_number=old_wcc.po_number),
    )


def _publish_update_event(
    action_type: ActionType,
    week: ScmWeek,
    brand: str,
    site: str,
    po_sku: PoSku,
) -> None:
    po_number = po_sku.po_number
    sku_code = po_sku.sku_code
    items = checklist_repo.get_checklist_preview_counts(weeks=(week,), brand=brand, sites=[site], sku_code=sku_code)
    if action_type == ActionType.DELETED:
        is_last_po = not items
        items = [item for item in items if item.po_number == po_number]
        if not items:
            EVENT_BUS.publish(
                WCCEvent(
                    action_type=action_type,
                    po_number=po_number,
                    sku_code=sku_code,
                    brand=brand,
                    site=site,
                    week=week,
                    is_last_po=is_last_po,
                )
            )
            return
        action_type = ActionType.UPDATED

    for item in items:
        if item.po_number == po_number:
            EVENT_BUS.publish(
                WCCEvent(
                    action_type=action_type,
                    po_number=po_number,
                    sku_code=sku_code,
                    brand=brand,
                    site=site,
                    week=week,
                    count_by_sku=item.count,
                )
            )
