from collections import defaultdict

from procurement.data.models.inventory.strategy_manager import StrategyManagerModel
from procurement.repository.inventory import strategy_manager as strategy_manager_repo
from procurement.repository.snowflake import startegy_managment


def update_strategy_manager_snowflake():
    strategy_managers = startegy_managment.get_strategy_managers()
    updates = []
    for item in strategy_managers:
        updates.append(
            {
                StrategyManagerModel.sku_code: item.sku_code,
                StrategyManagerModel.manager: item.manager,
            }
        )
    strategy_manager_repo.update_data(updates)


def get_strategy_managers(sku_codes: set[str] | None = None) -> dict[str, set[str]]:
    managers = strategy_manager_repo.get_strategy_managers(sku_codes)
    result = defaultdict(set)
    for item in managers:
        result[item.sku_code].add(item.manager)
    return result
