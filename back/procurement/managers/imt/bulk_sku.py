import dataclasses
import logging
from collections import defaultdict
from collections.abc import Iterable
from functools import cached_property

from procurement.constants.hellofresh_constant import ALL_BRANDS, NO_BRAND
from procurement.core.events.event_bus import EVENT_BUS
from procurement.core.events.events import BulkSkusChangedEvent
from procurement.core.typing import BRAND, BUL<PERSON>_SKU_CODE, PACKAGED_SKU_CODE, SKU_CODE, SKU_NAME
from procurement.data.dto.inventory.bulk_skus import BulkSku, BulkSkuWithNames
from procurement.data.models.inventory.bulk_skus import BulkSkusModel, MasterBulkSkuModel
from procurement.repository.inventory import bulk_sku as bulk_sku_repo
from procurement.repository.ordering import culinary_sku as sku_repo

logger = logging.getLogger(__name__)

MAPPING_ALREADY_EXISTS = "Mapping already exists"
MAPPING_ALREADY_EXISTS_FOR_A_BRAND = (
    "Mapping already exists for one of the Brands, please use the edit/delete buttons to modify existing mapping"
)
PACKAGED_SKU_ALREADY_MAPPED = (
    "This Packaged SKU is already mapped to a different Bulk SKU. "
    "Please use the edit/delete buttons to modify existing mapping."
)
MAPPING_DOES_NOT_EXIST = "Mapping does not exist"

DEFAULT_PICK_CONVERSION = 1


@dataclasses.dataclass
class BulkSkusData:
    brands: frozenset[BRAND]
    bulk_sku_code: SKU_CODE
    packaged_sku_code: SKU_CODE
    pick_conversion: int
    is_master_sku: bool
    bulk_sku_name: SKU_NAME = ""
    packaged_sku_name: SKU_NAME = ""


def upsert_bulk_sku(bulk_sku: BulkSkusData, is_edit: bool = False):
    if is_edit:
        _validate_edit_bulk_sku(bulk_sku)
    else:
        _validate_new_bulk_sku(bulk_sku)
    bulk_sku_repo.upsert_bulk_skus(
        {
            BulkSkusModel.brands: bulk_sku.brands,
            BulkSkusModel.bulk_sku_code: bulk_sku.bulk_sku_code,
            BulkSkusModel.packaged_sku_code: bulk_sku.packaged_sku_code,
            BulkSkusModel.pick_conversion: bulk_sku.pick_conversion,
        }
    )
    if bulk_sku.is_master_sku:
        bulk_sku_repo.insert_bulk_master_sku(
            {
                MasterBulkSkuModel.bulk_sku_code: bulk_sku.bulk_sku_code,
            }
        )
    elif is_edit:
        bulk_sku_repo.delete_bulk_master_sku(bulk_sku.bulk_sku_code)
    EVENT_BUS.publish(BulkSkusChangedEvent())


def _validate_new_bulk_sku(new_mapping: BulkSkusData):
    existing_mapping = _get_bulk_skus_mapping(new_mapping.packaged_sku_code)
    if ALL_BRANDS in existing_mapping:
        raise ValueError(PACKAGED_SKU_ALREADY_MAPPED)
    if ALL_BRANDS in new_mapping.brands and existing_mapping:
        raise ValueError(MAPPING_ALREADY_EXISTS_FOR_A_BRAND)
    _validate_new_brand_mapping(new_mapping, existing_mapping)


def _validate_new_brand_mapping(new_mapping: BulkSkusData, existing_mapping: dict[BRAND, BULK_SKU_CODE]):
    if any(existing_mapping.get(brand) == new_mapping.bulk_sku_code for brand in new_mapping.brands):
        raise ValueError(MAPPING_ALREADY_EXISTS)
    if any(brand in existing_mapping for brand in new_mapping.brands):
        raise ValueError(PACKAGED_SKU_ALREADY_MAPPED)


def _validate_edit_bulk_sku(new_mapping: BulkSkusData):
    existing_mapping = _get_bulk_skus_mapping(new_mapping.packaged_sku_code)
    _validate_mapping_exists(new_mapping, existing_mapping)
    if ALL_BRANDS in new_mapping.brands:
        is_different_skus_mapped = any(sku != new_mapping.bulk_sku_code for sku in existing_mapping.values())
        if is_different_skus_mapped:
            raise ValueError(MAPPING_ALREADY_EXISTS_FOR_A_BRAND)
    else:
        _validate_existing_mapping_edit(new_mapping, existing_mapping)


def _validate_mapping_exists(new_mapping: BulkSkusData, existing_mapping: dict[BRAND, BULK_SKU_CODE]):
    if new_mapping.bulk_sku_code not in existing_mapping.values():
        raise ValueError(MAPPING_DOES_NOT_EXIST)


def _validate_existing_mapping_edit(new_mapping: BulkSkusData, existing_mapping: dict[BRAND, BULK_SKU_CODE]):
    for brand in new_mapping.brands:
        existing_sku = existing_mapping.get(brand)
        if existing_sku and existing_sku != new_mapping.bulk_sku_code:
            raise ValueError(PACKAGED_SKU_ALREADY_MAPPED)


def _get_bulk_skus_mapping(packaged_sku: PACKAGED_SKU_CODE) -> dict[BRAND, BULK_SKU_CODE]:
    return {
        brand: record.bulk_sku_code
        for record in bulk_sku_repo.get_bulk_skus(packaged_sku_codes=[packaged_sku])
        for brand in record.brands
    }


def get_bulk_skus_mapping(brand: str | None = None) -> dict[PACKAGED_SKU_CODE, BulkSku]:
    res = {}
    for record in bulk_sku_repo.get_bulk_skus(brand=brand):
        res[record.packaged_sku_code] = record
    return res


def get_packaged_skus_by_bulk(brand: str | None = None) -> dict[BULK_SKU_CODE, list[PACKAGED_SKU_CODE]]:
    mapping = defaultdict(list)
    for p_sku, b_sku in get_bulk_skus_mapping(brand).items():
        mapping[b_sku.bulk_sku_code].append(p_sku)
    return mapping


def get_bulk_skus_with_names() -> Iterable[BulkSkuWithNames]:
    return bulk_sku_repo.get_bulk_skus_with_names()


def get_bulk_skus() -> Iterable[BulkSku]:
    return bulk_sku_repo.get_bulk_skus()


def get_sku_name_by_sku(bulk_sku: BulkSkusData) -> dict[SKU_CODE, SKU_NAME]:
    sku_codes = (bulk_sku.bulk_sku_code, bulk_sku.packaged_sku_code)
    return {sku.sku_code: sku.sku_name for sku in sku_repo.get_sku_name_by_sku_codes(sku_codes)}


def delete_bulk_sku(bulk_sku: BulkSkusData):
    bulk_sku_repo.delete_bulk_sku(bulk_sku.packaged_sku_code, bulk_sku.bulk_sku_code)


class BulkSkuManager:
    def __init__(self, brand: str):
        self.brand = brand

    @cached_property
    def bulk_skus_mapping(self) -> dict[PACKAGED_SKU_CODE, BulkSku]:
        return get_bulk_skus_mapping(self.brand)

    @cached_property
    def reversed_bulk_skus_mapping(self) -> dict[BULK_SKU_CODE, list[PACKAGED_SKU_CODE]]:
        return get_packaged_skus_by_bulk(self.brand)

    def get_pick_conversion(self, sku_code: str) -> int:
        if sku_code in self.bulk_skus_mapping:
            return self.bulk_skus_mapping[sku_code].pick_conversion
        return DEFAULT_PICK_CONVERSION

    def get_bulk_sku(self, packaged_sku: PACKAGED_SKU_CODE) -> BulkSku | None:
        return self.bulk_skus_mapping.get(packaged_sku)

    def get_bulk_sku_code(self, packaged_sku: PACKAGED_SKU_CODE) -> BULK_SKU_CODE | None:
        sku = self.bulk_skus_mapping.get(packaged_sku)
        return sku.bulk_sku_code if sku else None

    def get_packaged_skus(self, bulk_sku: BULK_SKU_CODE) -> list[PACKAGED_SKU_CODE]:
        return self.reversed_bulk_skus_mapping.get(bulk_sku, [])

    def get_associated_skus(
        self, sku_code: BULK_SKU_CODE | PACKAGED_SKU_CODE
    ) -> tuple[BULK_SKU_CODE | None, list[PACKAGED_SKU_CODE]]:
        if self.is_bulk(sku_code):
            return sku_code, self.get_packaged_skus(sku_code)
        bulk_sku = self.get_bulk_sku(sku_code)
        if bulk_sku:
            return bulk_sku.bulk_sku_code, self.get_packaged_skus(bulk_sku.bulk_sku_code)
        return None, [sku_code]

    def is_bulk(self, sku_code: str) -> bool:
        return sku_code in self.reversed_bulk_skus_mapping

    def is_packaged(self, sku_code: str) -> bool:
        return sku_code in self.bulk_skus_mapping


class EmptyBulkSkuManager(BulkSkuManager):
    def __init__(self):
        super().__init__(NO_BRAND)

    @cached_property
    def bulk_skus_mapping(self) -> dict[PACKAGED_SKU_CODE, BulkSku]:
        return {}

    @cached_property
    def reversed_bulk_skus_mapping(self) -> dict[BULK_SKU_CODE, list[PACKAGED_SKU_CODE]]:
        return {}
