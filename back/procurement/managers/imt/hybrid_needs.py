import logging
from collections.abc import Iterable
from datetime import date, timed<PERSON><PERSON>
from typing import Any

from sqlalchemy import ColumnElement

from procurement.client.googlesheets.googlesheet_utils import validate_required_field, validate_required_fields
from procurement.constants.hellofresh_constant import (
    BRAND_FJ,
    BRAND_HF,
    GsheetProductionPlanType,
    ProductionPlanType,
    SnowflakeProductionPlanType,
)
from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.hybrid_needs import HybridNeedsDto, ShiftNeedsDto
from procurement.data.googlesheet_model.hybrid_needs import (
    DeliveryDateNeeds,
    HnStatusRow,
    HybridNeedsDocModel,
    HybridNeedsDocModel3PL,
    HybridNeedsIngredients,
    HybridNeedsIngredients3PL,
    HybridNeedsIngredients3plStatus,
    HybridNeedsIngredientsFactor,
    HybridNeedsIngredientsFactorStatus,
    HybridNeedsIngredientsShiftLevel,
    HybridNeedsIngredientsStatus,
    HybridNeedsSheetModel,
)
from procurement.data.models.inventory.hybrid_needs import (
    DeliveryDateNeedsModel,
    HybridNeedsIngredientsModel,
    HybridNeedsIngredientsShiftLevelModel,
    HybridNeedsIngredientsStatusModel,
    HybridNeedsLiveUsageModel,
)
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.datasync.framework import warnings
from procurement.repository import repository_utils
from procurement.repository.inventory import hybrid_needs as hybrid_needs_repo
from procurement.repository.snowflake import hybrid_needs
from procurement.repository.snowflake.hybrid_needs import HybridNeedSnowflakeItem

logger = logging.getLogger(__name__)

SNOWFLAKE_STATUS_MAPPING = {
    SnowflakeProductionPlanType.COMPLETE: ProductionPlanType.ACTUAL,
    SnowflakeProductionPlanType.PROJECTED: ProductionPlanType.PROJECTED,
}

GSHEET_STATUS_MAPPING = {
    GsheetProductionPlanType.PLAN: ProductionPlanType.ACTUAL,
    GsheetProductionPlanType.FORECAST: ProductionPlanType.PROJECTED,
}


def get_hn_data(
    brand: str, site: str, week, sku_codes: set[str] = None, check_data_loaded: bool = True
) -> list[HybridNeedsDto]:
    if check_data_loaded:
        _check_data_loaded(week, brand, site)
    return hybrid_needs_repo.get_hybrid_needs_by_week(site=site, brand=brand, week=week, sku_codes=sku_codes)


def _check_data_loaded(week: ScmWeek, brand: str, site: str) -> None:
    dc_obj = dc_admin.get_site(brand=brand, site=site, week=week)
    if dc_obj and dc_obj.hybrid_needs_data_source.is_gsheet():
        doc_model = HybridNeedsDocModel3PL if dc_obj.is_3pl else HybridNeedsDocModel
        gsheet_admin.notify_warning_context_if_doc_invalid(week, brand, doc_model)


def get_data_by_dates(site: str, brand: str, date_from: date, date_to: date) -> list[HybridNeedsDto]:
    if date_to <= date_from:
        return []
    return hybrid_needs_repo.get_hybrid_needs_by_date(site=site, brand=brand, date_from=date_from, date_to=date_to)


def get_delivery_date_needs_by_dates(site: str, brand: str, date_from: date, date_to: date) -> list[HybridNeedsDto]:
    if date_to <= date_from:
        return []
    return hybrid_needs_repo.get_delivery_date_needs_by_date(
        site=site, brand=brand, date_from=date_from, date_to=date_to
    )


def get_hn_shift_data(brand: str, site: str, week: ScmWeek) -> list[ShiftNeedsDto]:
    return hybrid_needs_repo.get_hybrid_needs_shift_by_week(site=site, brand=brand, week=week)


def get_latest_available_hn_week(site: str, brand: str) -> ScmWeek | None:
    return hybrid_needs_repo.get_latest_available_hn_week(site=site, brand=brand)


def get_latest_available_delivery_date_needs_week(site: str, brand: str) -> ScmWeek | None:
    return hybrid_needs_repo.get_latest_available_delivery_date_needs_week(site=site, brand=brand)


def _need_cleanup_repo_data(current_data_count: int, gsheet_data_count: int, site: str, excepts: set) -> bool:
    # To prevent data loss (ex. User accidentally removed every data from doc),
    # we need to remove data only when count of gsheet data is more than half of db data
    # also we need to save invalid data during data removing
    if current_data_count - gsheet_data_count < current_data_count / 2:
        warnings.notify_warning(f"Hybrid needs sync: rewriting for {site}" + f" except: {excepts}" if excepts else "")
        return True
    return False


def update_data(week: ScmWeek, brand: str, is_3pl: bool = False) -> None:
    if brand == BRAND_FJ:
        dcs = [dc for dc in dc_admin.get_enabled_sites(week, brand).values() if dc.hybrid_needs_data_source.is_gsheet()]
        status_model = HybridNeedsIngredientsFactorStatus
        needs_model = HybridNeedsIngredientsFactor
        gsheet_brand = brand
    elif is_3pl:
        # All 3PL HN are in the HF gsheet, so all brands are pulled during the HF sync step
        # Except Factor, they have its own ghseet
        dcs = [
            dc
            for dc in dc_admin.get_all_market_dcs(week=week)
            if dc.hybrid_needs_data_source.is_gsheet() and dc.is_3pl and dc.brand != BRAND_FJ
        ]
        status_model = HybridNeedsIngredients3plStatus
        needs_model = HybridNeedsIngredients3PL
        gsheet_brand = BRAND_HF
    else:
        dcs = [
            dc
            for dc in dc_admin.get_enabled_sites(week, brand).values()
            if dc.hybrid_needs_data_source.is_gsheet() and not dc.is_3pl
        ]
        status_model = HybridNeedsIngredientsStatus
        needs_model = HybridNeedsIngredients
        gsheet_brand = brand

    if dcs:
        _update_default_hybrid_needs(week, dcs, needs_model, gsheet_brand)
        _update_hybrid_needs_status(week, dcs, status_model, gsheet_brand)


def update_hn_snowflake(week: ScmWeek, brand: str) -> None:
    sites = [
        site.sheet_name
        for site in dc_admin.get_enabled_sites(week, brand).values()
        if site.hybrid_needs_data_source.is_snowflake()
    ]
    update_items = []
    statuses = []
    live_usages = []
    str_week = str(week)
    int_week = int(week)

    for site in sites:
        for item in hybrid_needs.get_hybrid_needs_from_snowflake(week, site, brand):
            update_items.append(_build_hn_ingredient_item(brand, site, str_week, item))
            statuses.append(_build_hn_status_item(brand, site, str_week, item))
            live_usages.append(_build_hn_live_usage_item(brand, site, int_week, item))

        hybrid_needs_repo.remove_ingredients_data(brand, site, week, except_skus=set())

    _insert_hybrid_needs_status_data(statuses)
    _insert_hybrid_needs_data(update_items)
    _insert_hybrid_needs_live_usage_data(live_usages)


def _build_hn_ingredient_item(
    brand: str, site: str, week: str, item: HybridNeedSnowflakeItem
) -> dict[ColumnElement, Any]:
    return {
        HybridNeedsIngredientsModel.sku_code: item.sku_code,
        HybridNeedsIngredientsModel.dc: site,
        HybridNeedsIngredientsModel.scm_week: week,
        HybridNeedsIngredientsModel.date: item.day,
        HybridNeedsIngredientsModel.value: item.daily_need,
        HybridNeedsIngredientsModel.brand: brand,
    }


def _build_hn_status_item(brand: str, site: str, week: str, item: HybridNeedSnowflakeItem) -> dict[ColumnElement, Any]:
    status = SnowflakeProductionPlanType(item.status.capitalize())
    status = SNOWFLAKE_STATUS_MAPPING.get(status, status)
    return {
        HybridNeedsIngredientsStatusModel.scm_week: week,
        HybridNeedsIngredientsStatusModel.day: item.day,
        HybridNeedsIngredientsStatusModel.site: site,
        HybridNeedsIngredientsStatusModel.brand: brand,
        HybridNeedsIngredientsStatusModel.status: status,
    }


def _build_hn_live_usage_item(
    brand: str, site: str, week: int, item: HybridNeedSnowflakeItem
) -> dict[ColumnElement, Any]:
    return {
        HybridNeedsLiveUsageModel.site: site,
        HybridNeedsLiveUsageModel.sku_code: item.sku_code,
        HybridNeedsLiveUsageModel.scm_week: week,
        HybridNeedsLiveUsageModel.day: item.day,
        HybridNeedsLiveUsageModel.brand: brand,
        HybridNeedsLiveUsageModel.value: item.live_usage,
    }


def _update_hybrid_needs_status(
    week: ScmWeek, dcs: list[DcConfig], model: type[HybridNeedsSheetModel], gsheet_brand: str
) -> None:
    logger.info("Hybrid needs sync: Start updating HybridNeedsStatus table for %s week.", week)
    update_items = []
    for dc in dcs:
        sheet_data = gsheet_admin.read_gsheet(model(dc.sheet_name), week=week, brand=gsheet_brand)
        update_items.extend(_get_gsheet_hybrid_need_status(week, dc.brand, dc.sheet_name, sheet_data))
    _insert_hybrid_needs_status_data(update_items)


STATUSES_MAPPING = {
    GsheetProductionPlanType.PLAN: ProductionPlanType.ACTUAL,
    GsheetProductionPlanType.FORECAST: ProductionPlanType.PROJECTED,
}


def _get_gsheet_hybrid_need_status(
    week: ScmWeek, brand: str, site: str, sheet_data: list[HnStatusRow]
) -> list[dict[ColumnElement, Any]]:
    if not sheet_data:
        return []
    result = []
    week_first_day = week.get_first_day(week_config=brand_admin.get_week_config(brand))
    str_week = str(week)
    try:
        for idx, status in enumerate(sheet_data[0].daily_statuses):
            day = week_first_day + timedelta(days=idx)
            validate_required_field(status, f"daily_status - {status} : {day}")
            status = GsheetProductionPlanType(status.capitalize())
            status = GSHEET_STATUS_MAPPING.get(status, status)
            result.append(
                {
                    HybridNeedsIngredientsStatusModel.scm_week: str_week,
                    HybridNeedsIngredientsStatusModel.day: day,
                    HybridNeedsIngredientsStatusModel.site: site,
                    HybridNeedsIngredientsStatusModel.brand: brand,
                    HybridNeedsIngredientsStatusModel.status: status,
                }
            )
    except ValueError as exc:
        warnings.notify_sheet_error(sheet_data[0], exc, "Error when importing Hybrid Needs Status data", skipped=True)
    return result


def _update_default_hybrid_needs(
    week: ScmWeek, dcs: list[DcConfig], model: type[HybridNeedsSheetModel], gsheet_brand: str
) -> None:
    # NOTE: all dcs should have same week config
    production_days = week.production_days(brand_admin.get_week_config(dcs[0].brand))
    _update_hybrid_needs_data(week, dcs, production_days, model, gsheet_brand)


def _update_hybrid_needs_data(
    week: ScmWeek, dcs: list[DcConfig], days: Iterable[date], model: type[HybridNeedsSheetModel], gsheet_brand: str
) -> None:
    logger.info("Hybrid needs sync: Start updating HybridNeeds table for %s week.", week)
    update_items = []
    for dc in dcs:
        hybrid_need_items, invalids = _get_gsheet_hybrid_need(week, dc.brand, dc.sheet_name, days, model, gsheet_brand)
        update_items.extend(hybrid_need_items)
        current_data_count = hybrid_needs_repo.get_ingredients_data_count(dc.brand, dc.sheet_name, week)
        except_skus = {item.sku_code for item in invalids if item and item.sku_code}
        if _need_cleanup_repo_data(current_data_count, len(hybrid_need_items), dc.sheet_name, except_skus):
            hybrid_needs_repo.remove_ingredients_data(dc.brand, dc.sheet_name, week, except_skus=except_skus)
    _insert_hybrid_needs_data(update_items)


def _insert_hybrid_needs_data(update_items: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=HybridNeedsIngredientsModel,
        data=update_items,
        keys=[
            HybridNeedsIngredientsModel.sku_code,
            HybridNeedsIngredientsModel.dc,
            HybridNeedsIngredientsModel.scm_week,
            HybridNeedsIngredientsModel.date,
            HybridNeedsIngredientsModel.brand,
        ],
        preserve_fields=[HybridNeedsIngredientsModel.value],
    )


def _insert_hybrid_needs_status_data(statuses: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        HybridNeedsIngredientsStatusModel,
        statuses,
        keys=[
            HybridNeedsIngredientsStatusModel.scm_week,
            HybridNeedsIngredientsStatusModel.day,
            HybridNeedsIngredientsStatusModel.site,
            HybridNeedsIngredientsStatusModel.brand,
        ],
        preserve_fields=[HybridNeedsIngredientsStatusModel.status],
    )


def _insert_hybrid_needs_live_usage_data(update_items: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=HybridNeedsLiveUsageModel,
        data=update_items,
        keys=[
            HybridNeedsLiveUsageModel.sku_code,
            HybridNeedsLiveUsageModel.site,
            HybridNeedsLiveUsageModel.scm_week,
            HybridNeedsLiveUsageModel.day,
            HybridNeedsLiveUsageModel.brand,
        ],
        preserve_fields=[HybridNeedsLiveUsageModel.value],
    )


def _get_gsheet_hybrid_need(
    week: ScmWeek, brand: str, site: str, days: Iterable[date], model: type[HybridNeedsSheetModel], gsheet_brand: str
) -> tuple[list[dict], list]:
    sheet_data = gsheet_admin.read_gsheet(model(site), week=week, brand=gsheet_brand)
    updates = []
    invalid = []
    for row in sheet_data:
        try:
            validate_required_fields(row, ["sku_code"])
            for idx, day in enumerate(days):
                validate_required_field(row.days[idx], f"{row.sku_code} : {day}")
                updates.append(
                    {
                        HybridNeedsIngredientsModel.sku_code: row.sku_code,
                        HybridNeedsIngredientsModel.dc: site,
                        HybridNeedsIngredientsModel.scm_week: str(week),
                        HybridNeedsIngredientsModel.date: day,
                        HybridNeedsIngredientsModel.value: row.days[idx],
                        HybridNeedsIngredientsModel.brand: brand,
                    }
                )
        except ValueError as exc:
            invalid.append(row)
            warnings.notify_sheet_error(row, exc, "Error when importing Hybrid Needs data", skipped=True)
    return updates, invalid


def upsert_delivery_date_needs(week: ScmWeek, brand: str) -> None:
    days = week.production_days(brand_admin.get_week_config(brand))
    sites = [dc_name for dc_name, dto in dc_admin.get_enabled_sites(week, brand).items()]

    update_items = []
    for site in sites:
        delivery_date_need_items, invalids = _get_gsheet_delivery_date_need(week, brand, site, days)
        update_items.extend(delivery_date_need_items)
        except_skus = {item.sku_code for item in invalids if item and item.sku_code}
        hybrid_needs_repo.remove_delivery_date_needs_data(brand, site, week, except_skus=except_skus)

    repository_utils.safe_bulk_insert_sqla(
        model=DeliveryDateNeedsModel,
        data=update_items,
        keys=[
            DeliveryDateNeedsModel.sku_code,
            DeliveryDateNeedsModel.dc,
            DeliveryDateNeedsModel.scm_week,
            DeliveryDateNeedsModel.date,
            DeliveryDateNeedsModel.brand,
        ],
        preserve_fields=[DeliveryDateNeedsModel.value],
    )


def _get_gsheet_delivery_date_need(
    week: ScmWeek, brand: str, site: str, days: Iterable[date]
) -> tuple[list[dict], list]:
    sheet_data = gsheet_admin.read_gsheet(DeliveryDateNeeds(site), week=week, brand=brand)
    updates = []
    invalid = []
    for row in sheet_data:
        try:
            validate_required_fields(row, ["sku_code"])
            for idx, day in enumerate(days):
                validate_required_field(row.days[idx], f"{row.sku_code} : {day}")
                updates.append(
                    {
                        DeliveryDateNeedsModel.sku_code: row.sku_code,
                        DeliveryDateNeedsModel.dc: site,
                        DeliveryDateNeedsModel.scm_week: str(week),
                        DeliveryDateNeedsModel.date: day,
                        DeliveryDateNeedsModel.value: row.days[idx],
                        DeliveryDateNeedsModel.brand: brand,
                    }
                )
        except ValueError as exc:
            invalid.append(row)
            warnings.notify_sheet_error(row, exc, "Error when importing Delivery Date Needs data", skipped=True)
    return updates, invalid


def update_shift_level_data(week: ScmWeek, brand: str) -> None:
    sites = [dc_name for dc_name, dto in dc_admin.get_enabled_sites(week, brand).items()]
    days_from_wednesday = week.production_days(brand_admin.get_week_config(brand))

    logger.info(
        "Hybrid needs shift level sync: Start updating HybridNeedsIngredientsShiftLevel table for %s week.", week
    )
    update_items = []
    for site in sites:
        shift_level_items, invalids = _get_gsheet_hybrid_need_shift_level(week, brand, site, days_from_wednesday)
        update_items.extend(shift_level_items)
        current_data_count = hybrid_needs_repo.get_shift_level_data_count(brand, site, week)
        except_skus = {item.sku_code for item in invalids if item and item.sku_code}
        if _need_cleanup_repo_data(current_data_count, len(shift_level_items), site, except_skus):
            hybrid_needs_repo.remove_shift_level_data(brand, site, week, except_skus=except_skus)

    repository_utils.safe_bulk_insert_sqla(
        model=HybridNeedsIngredientsShiftLevelModel,
        data=update_items,
        keys=[
            HybridNeedsIngredientsShiftLevelModel.sku_code,
            HybridNeedsIngredientsShiftLevelModel.dc,
            HybridNeedsIngredientsShiftLevelModel.scm_week,
            HybridNeedsIngredientsShiftLevelModel.date,
            HybridNeedsIngredientsShiftLevelModel.brand,
        ],
        preserve_fields=[
            HybridNeedsIngredientsShiftLevelModel.value_day,
            HybridNeedsIngredientsShiftLevelModel.value_night,
            HybridNeedsIngredientsShiftLevelModel.value_third,
        ],
    )


def _get_gsheet_hybrid_need_shift_level(week: ScmWeek, brand: str, site: str, days_from_wednesday: Iterable[date]):
    data = gsheet_admin.read_gsheet(HybridNeedsIngredientsShiftLevel(site), week=week, brand=brand)
    updates = []
    invalid = []
    empty_sku_count = 0
    for item in data:
        if empty_sku_count >= 10:  # TODO: tmep workaround to ignore 12k+ rows with #REF! data in the sheet
            break
        # GSheet excepts two shifts at last day, and we need to add values for night and third shifts
        item.days_shifts.extend([0, 0])
        try:
            if not item.sku_code:
                empty_sku_count += 1
            else:
                empty_sku_count = 0  # only 10 empty codes in a row
            validate_required_fields(item, ["sku_code"])
            for idx, day in enumerate(days_from_wednesday):
                day_idx = idx * 3  # Index have to increased to get true shifts for true day
                validate_required_field(item.days_shifts[day_idx], f"{item.sku_code} : {day}")
                validate_required_field(item.days_shifts[day_idx + 1], f"{item.sku_code} : {day}")
                validate_required_field(item.days_shifts[day_idx + 2], f"{item.sku_code} : {day}")
                updates.append(
                    {
                        HybridNeedsIngredientsShiftLevelModel.sku_code: item.sku_code,
                        HybridNeedsIngredientsShiftLevelModel.dc: site,
                        HybridNeedsIngredientsShiftLevelModel.scm_week: str(week),
                        HybridNeedsIngredientsShiftLevelModel.date: day,
                        HybridNeedsIngredientsShiftLevelModel.value_day: item.days_shifts[day_idx + 0],
                        HybridNeedsIngredientsShiftLevelModel.value_night: item.days_shifts[day_idx + 1],
                        HybridNeedsIngredientsShiftLevelModel.value_third: item.days_shifts[day_idx + 2],
                        HybridNeedsIngredientsShiftLevelModel.brand: brand,
                    }
                )
        except ValueError as exc:
            invalid.append(item)
            warnings.notify_sheet_error(item, exc, "Error when importing Hybrid Needs Shift-Level data", skipped=True)
    return updates, invalid
