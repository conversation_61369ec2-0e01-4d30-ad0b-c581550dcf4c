import dataclasses
import logging
import math
from collections import defaultdict
from collections.abc import Iterable
from typing import Any

from sqlalchemy import ColumnElement

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_HF, BRAND_HF_CP_FULL, BRAND_HF_FULL
from procurement.core.dates import ScmWeek
from procurement.core.exceptions.base_errors import NonCriticalWrapperException
from procurement.core.request_utils import context
from procurement.data.dto.snowflake.packaging_demand import CanadaPackagingDemandDto
from procurement.data.googlesheet_model.consolidated_packaging_demand import (
    ConsolidatedDemandWeekData,
    InWeekConsolidatedDemand,
    InWeekConsolidatedDemandRow,
    WeeklyConsolidatedDemandRow,
)
from procurement.data.models.inventory.packaging_demand import PackagingDemandModel
from procurement.data.models.inventory.packaging_sku_profile import PackagingSkuProfileModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.inventory import packaging_depletion as packaging_depletion_repo
from procurement.repository.inventory import packaging_sku_profile as packaging_sku_profile_repo
from procurement.repository.snowflake import packaging_demand as packaging_demand_repo

logger = logging.getLogger(__name__)

CA_BRAND_NAME_MAP = {
    BRAND_HF_FULL: BRAND_HF,
    BRAND_HF_CP_FULL: BRAND_HF,
}

DAY_TO_INDEX = {
    "Thursday": 1,
    "Friday": 2,
    "Saturday": 3,
    "Sunday": 4,
    "Monday": 5,
    "Tuesday": 6,
    "Wednesday": 7,
}

_SNOWFLAKE_CA_BRAND_MAP = {"HFCA": BRAND_HF, "CKCA": BRAND_HF}


def update_consolidated_packaging_demand_data() -> None:
    logger.info("Consolidated Packaging Demand sync: Start updating")
    available_sites = set()
    for brand in brand_admin.get_brand_ids():
        week_config = brand_admin.get_week_config(brand)
        available_sites.update(dc_admin.get_enabled_sites(ScmWeek.current_week(week_config), brand))
    week = _get_consolidated_packaging_start_week()
    _update_consolidated_packaging_demand(week, available_sites)


def _update_consolidated_packaging_demand(week: ScmWeek, sites: set[str]) -> None:
    sheet_data = gsheet_admin.read_gsheet(InWeekConsolidatedDemand())
    if not sheet_data:
        raise NonCriticalWrapperException(
            ValueError(f"There is no '{InWeekConsolidatedDemand.sheet_name}' consolidated_packaging_demand document")
        )

    _update_packaging_demand(week, sheet_data, sites)
    _update_packaging_sku_profile(sheet_data)


def _update_packaging_demand(week: ScmWeek, sheet_data: list[InWeekConsolidatedDemandRow], sites: set[str]) -> None:
    market = context.get_request_context().market
    week_number = int(week)
    next_week_number = int(week + 1)
    demand_updates = []
    for row in sheet_data:
        try:
            _validate_consolidated_packaging_demand_row(row, sites)
            demand_updates.extend(
                [
                    {
                        PackagingDemandModel.week: week_number,
                        PackagingDemandModel.site: row.site,
                        PackagingDemandModel.brand: CA_BRAND_NAME_MAP[row.brand],
                        PackagingDemandModel.sku_code: row.sku_code,
                        PackagingDemandModel.demand_by_day: [0] + row.demand_by_day_w1,
                        PackagingDemandModel.market: market,
                    },
                    {
                        PackagingDemandModel.week: next_week_number,
                        PackagingDemandModel.site: row.site,
                        PackagingDemandModel.brand: CA_BRAND_NAME_MAP[row.brand],
                        PackagingDemandModel.sku_code: row.sku_code,
                        PackagingDemandModel.demand_by_day: [0] + row.demand_by_day_w2,
                        PackagingDemandModel.market: market,
                    },
                ]
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, f"Error while importing {InWeekConsolidatedDemand.sheet_name} Data", skipped=True
            )
    packaging_depletion_repo.update_packaging_demand(demand_updates)


def _update_packaging_sku_profile(sheet_data: list[InWeekConsolidatedDemandRow]) -> None:
    market = context.get_request_context().market
    sku_profiles = {}
    for row in sheet_data:
        try:
            validate_required_fields(row, ["sku_code"])
            sku_profiles[row.sku_code] = {
                PackagingSkuProfileModel.sku_code: row.sku_code,
                PackagingSkuProfileModel.size: row.size,
                PackagingSkuProfileModel.profile: None,
                PackagingSkuProfileModel.type: None,
                PackagingSkuProfileModel.market: market,
            }
        except ValueError as exc:
            warnings.notify_sheet_error(
                row,
                exc,
                f"Error while importing Packaging Sku Profile {InWeekConsolidatedDemand.sheet_name} data",
                skipped=True,
            )
    packaging_sku_profile_repo.upsert_packaging_sku_profile(list(sku_profiles.values()))


def _get_consolidated_packaging_start_week() -> ScmWeek:
    gsheet_week = gsheet_admin.read_gsheet(ConsolidatedDemandWeekData())
    if not gsheet_week:
        raise NonCriticalWrapperException(
            ValueError(f"There is no '{InWeekConsolidatedDemand.sheet_name}' consolidated_packaging_demand document")
        )
    try:
        week = ScmWeek.from_str(gsheet_week[0].week)
    except ValueError as exc:
        message = f"Incorrect week value data: {ConsolidatedDemandWeekData.sheet_name} - {gsheet_week[0].week}"
        warnings.notify_sheet_error(gsheet_week[0], exc, message, skipped=True)
        raise NonCriticalWrapperException(ValueError(message)) from exc
    return week


def _validate_consolidated_packaging_demand_row(
    row: InWeekConsolidatedDemandRow | WeeklyConsolidatedDemandRow, sites: set[str]
) -> None:
    validate_required_fields(row, ["site", "brand", "sku_code"])
    if row.site not in sites:
        raise ValueError(f'The Site "{row.site}" is unavailable in the app')


@dataclasses.dataclass(slots=True)
class ConsolidatedPackagingDemandRowSnowflake:
    site: str
    sku_code: str


def _process_packaging_demand_data(
    raw_results: Iterable[CanadaPackagingDemandDto], sync_week: ScmWeek
) -> list[dict[ColumnElement, Any]]:
    market = context.get_request_context().market
    demands_by_site_brand_week_sku = defaultdict(lambda: [0] * 8)

    for row in raw_results:
        brand = _SNOWFLAKE_CA_BRAND_MAP.get(row.brand)
        if not brand:
            continue
        key = (row.site, brand, row.sku_code, row.week)

        if (day_index := DAY_TO_INDEX.get(row.weekday.strip())) is not None:
            demands_by_site_brand_week_sku[key][day_index] += row.quantity

    if not demands_by_site_brand_week_sku:
        raise NonCriticalWrapperException(
            ValueError(f"There is no latest records for week {sync_week} in the Snowflake table for packaging demand")
        )
    demand_updates = []

    for (site, brand, sku_code, week), demand_by_day in demands_by_site_brand_week_sku.items():
        demand_updates.append(
            {
                PackagingDemandModel.week: int(ScmWeek.from_str(week)),
                PackagingDemandModel.site: site,
                PackagingDemandModel.brand: brand,
                PackagingDemandModel.sku_code: sku_code,
                PackagingDemandModel.demand_by_day: list(map(math.ceil, demand_by_day)),
                PackagingDemandModel.market: market,
            }
        )

    return demand_updates


def _validate_consolidated_packaging_demand_row_snowflake(
    row: ConsolidatedPackagingDemandRowSnowflake, sites: set[str]
) -> None:
    validate_required_fields(row, ["site", "sku_code"])
    if row.site not in sites:
        raise ValueError(f'The Site "{row.site}" is unavailable in the app')


def _update_consolidated_packaging_demand_snowflake(week: ScmWeek) -> None:
    rows = packaging_demand_repo.get_packaging_demand_snowflake(week - 1)
    demand_updates = _process_packaging_demand_data(rows, week)
    if not demand_updates:
        return
    packaging_depletion_repo.update_packaging_demand(demand_updates)


def update_packaging_demand_data_snowflake(week: ScmWeek) -> None:
    try:
        _update_consolidated_packaging_demand_snowflake(week)
    except Exception as e:
        logger.error("Error updating packaging demand from Snowflake", stack_info=True)
        raise NonCriticalWrapperException(e) from e
