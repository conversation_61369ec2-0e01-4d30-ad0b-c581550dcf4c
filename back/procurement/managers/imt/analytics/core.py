from functools import cached_property

from procurement.core.typing import SKU_CODE
from procurement.managers.imt.ingredients_depletion.core import IngredientDepletionContext, IngredientSummary

ALL_POS = "All POs"
NO_POS_PLACED = "No POs Placed"
SKU_OVER_BUDGET = "SKU is Over Budget!"
NO_POS_PLACED_FOR_THIS_SKU = "No PO(s) Placed for this SKU Yet!"
SKU_IS_NOT_RUNNING = "This SKU isn't running this week! Confirm the PO(s)."


class AnalyticsContext(IngredientDepletionContext):
    @cached_property
    def ingredient_summary(self) -> dict[SKU_CODE, IngredientSummary]:
        summary = super().ingredient_summary
        summary.update(
            self._build_ingredient_summary(
                frozenset(
                    (b_sku.bulk_sku_code for b_sku in self.bulk_sku_manager.bulk_skus_mapping.values()) - summary.keys()
                )
            )
        )
        summary.update(self._build_ingredient_summary(frozenset(self.po_master_view.keys())))
        return summary
