import itertools
from abc import ABC, abstractmethod
from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, DC_ROLLOVER_INVENTORY_SUPPLIER, RECEIVED_STATUSES
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.typing import PO_NUMBER, SKU_CODE, UNITS
from procurement.data.dto.inventory.top_variance_comments import TopVarianceComment, TopVarianceCommentInput
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.aggregation import AggregatedData
from procurement.managers.imt.bulk_sku import DEFAULT_PICK_CONVERSION
from procurement.managers.imt.ingredients_depletion.core import IngredientSummary
from procurement.managers.imt.models import PoMasterViewResult
from procurement.repository.inventory import top_variance_comments as top_variance_comment_repo

from .core import AnalyticsContext

ALL_POS = "All POs"
NO_POS_PLACED = "No POs Placed"
SKU_OVER_BUDGET = "SKU is Over Budget!"
NO_POS_PLACED_FOR_THIS_SKU = "No PO(s) Placed for this SKU Yet!"
SKU_IS_NOT_RUNNING = "This SKU isn't running this week! Confirm the PO(s)."
INGREDIENT_SUBCATEGORY_FILTER_NAME = "IQF"


class TopVarianceContext(AnalyticsContext):
    @cached_property
    def ingredient_summary(self) -> dict[SKU_CODE, IngredientSummary]:
        return {
            code: sku
            for code, sku in super().ingredient_summary.items()
            if not (
                sku.purchasing_category_name == IngredientCategory.PRODUCE
                and sku.purchasing_subcategory_name
                and INGREDIENT_SUBCATEGORY_FILTER_NAME in sku.purchasing_subcategory_name
            )
        }

    @cached_property
    def comments(self) -> dict[tuple[SKU_CODE, PO_NUMBER | None], TopVarianceComment]:
        return {
            (item.sku_code, item.po_number): item
            for item in top_variance_comment_repo.get_top_variance_comments(self.brand, self.site, self.week)
        }


class AbstractTopVarianceItem(ABC):
    @property
    @abstractmethod
    def ingredient(self) -> IngredientSummary: ...

    @property
    @abstractmethod
    def site(self) -> str: ...

    @property
    @abstractmethod
    def brand(self) -> str: ...

    @cached_property
    @abstractmethod
    def buyer(self) -> str: ...

    @property
    @abstractmethod
    def sku_code(self) -> str: ...

    @property
    @abstractmethod
    def sku_name(self) -> str: ...

    @property
    @abstractmethod
    def po_number(self) -> str: ...

    @property
    @abstractmethod
    def category(self) -> str: ...

    @cached_property
    @abstractmethod
    def comment(self) -> TopVarianceComment | None: ...

    @property
    @abstractmethod
    def forecast(self) -> UNITS: ...

    @cached_property
    @abstractmethod
    def allocation_price(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def unit_price(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def unit_price_flag(self) -> bool: ...

    @cached_property
    @abstractmethod
    def incoming_value(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def received_price(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def received_quantity(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def incoming_quantity(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def budgeted_cost(self) -> Decimal: ...

    @cached_property
    @abstractmethod
    def supplier(self) -> str | None: ...

    @cached_property
    @abstractmethod
    def emergency_reason(self) -> str | None: ...

    @cached_property
    @abstractmethod
    def pick_conversion(self) -> int: ...


class PoSkuTopVarianceItem(AbstractTopVarianceItem):
    last_edited_by: str

    def __init__(
        self,
        ingredient: IngredientSummary,
        po_item: PoMasterViewResult,
        context: TopVarianceContext,
    ):
        self.context = context
        self._ingredient = ingredient
        self.master_sku_code = ingredient.sku_code
        self.po_item = po_item

    @cached_property
    def comment_key(self) -> tuple[SKU_CODE, PO_NUMBER | None]:
        return self.po_item.sku_code, self.po_item.po_number

    @property
    def ingredient(self) -> IngredientSummary:
        return self._ingredient

    @property
    def site(self) -> str:
        return self.context.site

    @property
    def brand(self) -> str:
        return self.context.brand

    @cached_property
    def buyer(self) -> str:
        return self.context.buyers.get(self.sku_code)

    @property
    def sku_code(self) -> str:
        return self.po_item.sku_code

    @property
    def sku_name(self) -> str:
        return self.po_item.sku_name

    @property
    def category(self) -> str:
        return self.ingredient.purchasing_category_name

    @cached_property
    def comment(self) -> TopVarianceComment | None:
        return self.context.comments.get(self.comment_key)

    @cached_property
    def forecast(self) -> UNITS:
        return self.context.forecast.get(self.sku_code, Decimal())

    @cached_property
    def allocation_price(self) -> Decimal:
        return self.context.allocation_prices.get(self.sku_code, Decimal())

    @cached_property
    def incoming_value(self) -> Decimal:
        return self.po_item.total_price if self.is_incoming else Decimal()

    @cached_property
    def _is_supplier_accepted(self) -> bool:
        return self.po_item.supplier not in (self.context.dc_object.autobagger_supplier, DC_ROLLOVER_INVENTORY_SUPPLIER)

    @cached_property
    def _is_received(self) -> bool:
        return self._is_supplier_accepted and self.po_item.po_status in RECEIVED_STATUSES

    @cached_property
    def is_incoming(self) -> bool:
        return self.po_item.po_status in AWAITING_DELIVERY_STATUSES

    @cached_property
    def received_price(self) -> Decimal:
        return self.po_item.total_price if self._is_received else Decimal()

    @cached_property
    def received_quantity(self) -> Decimal:
        return self.po_item.total_quantity_received if self._is_received else Decimal()

    @cached_property
    def incoming_quantity(self) -> Decimal:
        return self.po_item.quantity if self.is_incoming else Decimal()

    @cached_property
    def unit_price(self) -> Decimal:
        return self.po_item.case_price / self.po_item.case_size

    @cached_property
    def unit_price_flag(self) -> bool:
        return self.allocation_price and self.unit_price > self.allocation_price

    @property
    def po_number(self) -> str:
        return self.po_item.po_number

    @cached_property
    def budgeted_cost(self) -> Decimal:
        return self.context.forecast.get(self.master_sku_code, Decimal()) * self.context.allocation_prices.get(
            self.master_sku_code, Decimal()
        )

    @cached_property
    def supplier(self) -> str | None:
        return self.po_item.supplier

    @cached_property
    def emergency_reason(self) -> str | None:
        return self.po_item.emergency_reason

    @cached_property
    def pick_conversion(self) -> int:
        if self.sku_code != self.master_sku_code and self.context.bulk_sku_manager.is_bulk(self.sku_code):
            packaged_sku = next(iter(self.context.bulk_sku_manager.get_packaged_skus(self.sku_code)))
            return self.context.bulk_sku_manager.bulk_skus_mapping[packaged_sku].pick_conversion
        return DEFAULT_PICK_CONVERSION


class SkuTopVarianceItem(AbstractTopVarianceItem):
    def __init__(self, ingredient: IngredientSummary):
        self._ingredient = ingredient

    @cached_property
    @abstractmethod
    def po_sku_items(self) -> AggregatedData[list[PoSkuTopVarianceItem], PoSkuTopVarianceItem]: ...

    @cached_property
    @abstractmethod
    def pos_for_not_running_sku(self) -> bool: ...

    @cached_property
    @abstractmethod
    def is_forecast_exists_but_no_po(self) -> bool: ...

    @cached_property
    @abstractmethod
    def has_needs(self) -> bool: ...

    @cached_property
    @abstractmethod
    def quantity_variance(self) -> Decimal | None: ...

    @cached_property
    def supplier(self) -> None:
        return None

    @cached_property
    def emergency_reason(self) -> None:
        return None

    @cached_property
    def ingredient(self) -> IngredientSummary:
        return self._ingredient

    @property
    def sku_code(self) -> str:
        return self.ingredient.sku_code

    @property
    def sku_name(self) -> str:
        return self.ingredient.sku_name

    @property
    def category(self) -> str:
        return self.ingredient.purchasing_category_name

    @property
    def po_number(self) -> str:
        if self.does_have_po:
            return ALL_POS
        return NO_POS_PLACED

    @cached_property
    def incoming_value(self) -> Decimal | None:
        return self.po_sku_items.sum_items(lambda item: item.incoming_value)

    @cached_property
    def received_price(self) -> Decimal | None:
        return self.po_sku_items.sum_items(lambda item: item.received_price)

    @cached_property
    def received_quantity(self) -> Decimal:
        return self.po_sku_items.sum_items(
            lambda item: (item.received_quantity / item.pick_conversion) if item.pick_conversion else 0
        )

    @cached_property
    def incoming_quantity(self) -> Decimal:
        return self.po_sku_items.sum_items(
            lambda item: (item.incoming_quantity / item.pick_conversion) if item.pick_conversion else 0
        )

    @cached_property
    def budgeted_cost(self) -> Decimal:
        return self.forecast * self.allocation_price

    @cached_property
    def unit_price(self) -> Decimal:
        return self.po_sku_items.average_of_items(lambda item: item.unit_price)

    @cached_property
    def unit_price_flag(self) -> bool:
        return any(item.unit_price_flag for item in self.po_sku_items.raw)

    @cached_property
    def price_variance(self) -> Decimal:
        return self.received_price + self.incoming_value - self.budgeted_cost

    @cached_property
    def does_have_po(self) -> bool:
        return len(self.po_sku_items.raw) > 0

    @cached_property
    def variance_status(self) -> str | None:
        if self.is_over_budget:
            return SKU_OVER_BUDGET
        if self.is_forecast_exists_but_no_po:
            return NO_POS_PLACED_FOR_THIS_SKU
        if self.pos_for_not_running_sku:
            return SKU_IS_NOT_RUNNING
        return None

    @cached_property
    def is_over_budget(self) -> bool:
        return self.price_variance > 0 and self.has_needs


class PackagedSkuTopVarianceItem(SkuTopVarianceItem):
    def __init__(
        self, context: TopVarianceContext, ingredient: IngredientSummary, po_sku_items: list[PoSkuTopVarianceItem]
    ):
        super().__init__(ingredient)
        self.context = context
        self._po_sku_items = AggregatedData[list[PoSkuTopVarianceItem], PoSkuTopVarianceItem](po_sku_items)

    @cached_property
    def po_sku_items(self) -> AggregatedData[list[PoSkuTopVarianceItem], PoSkuTopVarianceItem]:
        return self._po_sku_items

    @property
    def site(self) -> str:
        return self.context.site

    @property
    def brand(self) -> str:
        return self.context.brand

    @cached_property
    def buyer(self) -> str:
        return self.context.buyers.get(self.sku_code)

    @cached_property
    def comment(self) -> TopVarianceComment | None:
        return self.context.comments.get((self.sku_code, None))

    @cached_property
    def forecast(self) -> UNITS:
        return self.context.forecast.get(self.sku_code, Decimal())

    @cached_property
    def allocation_price(self) -> Decimal:
        return self.context.allocation_prices.get(self.sku_code, Decimal())

    @cached_property
    def pick_conversion(self) -> int:
        bulk_sku = self.context.bulk_sku_manager.get_bulk_sku(self.sku_code)
        return bulk_sku.pick_conversion if bulk_sku else DEFAULT_PICK_CONVERSION

    @cached_property
    def pos_for_not_running_sku(self) -> bool:
        return not (self.context.bulk_sku_manager.is_bulk(self.sku_code) or self.has_needs) and self.does_have_po

    @cached_property
    def is_forecast_exists_but_no_po(self) -> bool:
        return self.has_needs and not self.does_have_po

    @cached_property
    def has_needs(self) -> bool:
        return self.forecast > 0

    @cached_property
    def quantity_variance(self) -> Decimal | None:
        return (self.received_quantity + self.incoming_quantity - self.forecast) * self.allocation_price


class AbstractAggregatedTopVarianceItem(SkuTopVarianceItem, ABC):
    def __init__(self, ingredient: IngredientSummary, top_variances: list[SkuTopVarianceItem]):
        super().__init__(ingredient)
        self._items = AggregatedData[list[SkuTopVarianceItem], SkuTopVarianceItem](items=top_variances)

    @property
    def items(self) -> AggregatedData[list[SkuTopVarianceItem], SkuTopVarianceItem]:
        return self._items

    @cached_property
    def po_sku_items(self) -> AggregatedData[list[PoSkuTopVarianceItem], PoSkuTopVarianceItem]:
        return AggregatedData[list[PoSkuTopVarianceItem], PoSkuTopVarianceItem](
            items=[po for variance_item in self.items.raw for po in variance_item.po_sku_items.raw]
        )

    @cached_property
    def pos_for_not_running_sku(self) -> bool:
        return any(p_item.pos_for_not_running_sku for p_item in self.items.raw)

    @cached_property
    def pick_conversion(self) -> int:
        return DEFAULT_PICK_CONVERSION

    @cached_property
    def quantity_variance(self) -> Decimal | None:
        return (
            # fmt: off
            (self.received_quantity + self.incoming_quantity)
            * self.allocation_price
            + self.items.sum_items(lambda p_item: p_item.quantity_variance * p_item.pick_conversion)
            # fmt: on
        )


class BulkSkuTopVarianceItem(AbstractAggregatedTopVarianceItem):
    def __init__(
        self,
        ingredient: IngredientSummary,
        context: TopVarianceContext,
        packaged_top_variances: list[SkuTopVarianceItem],
    ):
        super().__init__(ingredient, packaged_top_variances)
        self.context = context

    @cached_property
    def site(self) -> str:
        return self.items.any_item(lambda it: it.site)

    @cached_property
    def brand(self) -> str:
        return self.items.any_item(lambda it: it.brand)

    @cached_property
    def buyer(self) -> str:
        return self.context.buyers.get(self.sku_code)

    @cached_property
    def comment(self) -> TopVarianceComment | None:
        return self.context.comments.get((self.sku_code, None))

    @cached_property
    def forecast(self) -> UNITS:
        return self.items.any_item(lambda it: it.forecast)

    @cached_property
    def allocation_price(self) -> Decimal:
        return self.context.allocation_prices.get(self.sku_code, Decimal())

    @cached_property
    def budgeted_cost(self) -> Decimal:
        return self.items.sum_items(lambda p_item: p_item.budgeted_cost)

    @cached_property
    def is_forecast_exists_but_no_po(self) -> bool:
        return self.budgeted_cost > 0 and not self.does_have_po

    @cached_property
    def has_needs(self) -> bool:
        return True


class AggregatedTopVariance(AbstractAggregatedTopVarianceItem):
    @cached_property
    def site(self) -> str:
        return self.items.concat_items(lambda it: it.site, unique=True)

    @cached_property
    def brand(self) -> str:
        return self.items.concat_items(lambda it: it.brand, unique=True)

    @cached_property
    def buyer(self) -> str:
        return self.items.concat_items(lambda it: it.buyer, unique=True)

    @cached_property
    def comment(self) -> TopVarianceComment | None:
        comments = list(filter(None, (it.comment for it in self.items.raw)))
        if not comments:
            return None
        is_all_comments_empty = all(not c.comment for c in comments)
        return TopVarianceComment(
            sku_code=self.sku_code,
            po_number=None,
            comment="\n\n".join(c.comment for c in comments if c.comment),
            # ignoring users that deleted previous comments, except when it's the only comment data
            last_edited_by=", ".join(set(c.last_edited_by for c in comments if c.comment or is_all_comments_empty)),
        )

    @cached_property
    def forecast(self) -> UNITS:
        return self.items.sum_items(lambda it: it.forecast)

    @cached_property
    def allocation_price(self) -> Decimal:
        return self.items.max_item(lambda it: it.allocation_price, default=Decimal())

    @cached_property
    def is_forecast_exists_but_no_po(self) -> bool:
        return self.has_needs and all(not it.does_have_po for it in self.items.raw)

    @cached_property
    def has_needs(self) -> bool:
        return any(it.has_needs for it in self.items.raw)

    @cached_property
    def quantity_variance(self) -> Decimal | None:
        return self.items.sum_items(lambda it: it.quantity_variance)


def get_top_variances(brand: str, sites: list[str], week: ScmWeek) -> list[SkuTopVarianceItem]:
    result = []
    dcs = list(filter(None, (dc_admin.get_site(brand=brand, site=site, week=week) for site in sites)))
    for dc in dcs:
        if not dc.consolidated_site_code or not killswitch.top_variance_aggregation_enabled:
            result.extend(_build_dc_top_variance(dc, week))
            continue
        consolidated_dcs = dc_admin.get_all_enabled_consolidated_sites(week)[dc.consolidated_site_code]
        dc_result = utils.group_by(
            itertools.chain.from_iterable(_build_dc_top_variance(con_dc, week) for con_dc in consolidated_dcs),
            lambda it: it.sku_code,
        )
        result.extend(AggregatedTopVariance(dc_items[0].ingredient, dc_items) for dc_items in dc_result.values())
    return result


def _build_dc_top_variance(dc: DcConfig, week: ScmWeek) -> list[SkuTopVarianceItem]:
    context = TopVarianceContext(week=week, site=dc.sheet_name, brand=dc.brand, include_bulk_skus=True)
    bulk_items = defaultdict(list)
    result = []
    for ingredient in context.ingredient_summary.values():
        bulk_sku, packaged_skus = context.bulk_sku_manager.get_associated_skus(ingredient.sku_code)
        pos = context.get_po_master_view_sku(ingredient.sku_code)
        po_items = (PoSkuTopVarianceItem(ingredient, item, context) for item in pos)

        if len(packaged_skus) > 1:
            # bulk SKUS are treated as packaged for simplicity of aggregation
            bulk_items[bulk_sku].append(
                PackagedSkuTopVarianceItem(ingredient=ingredient, context=context, po_sku_items=list(po_items))
            )
        elif not context.bulk_sku_manager.is_bulk(ingredient.sku_code):
            if bulk_sku:
                pos += context.get_po_master_view_sku(bulk_sku)
            result.append(
                PackagedSkuTopVarianceItem(ingredient=ingredient, context=context, po_sku_items=list(po_items))
            )

    for bulk_sku, items in bulk_items.items():
        if len(items) > 1:
            result.append(
                BulkSkuTopVarianceItem(
                    ingredient=context.ingredient_summary[bulk_sku], context=context, packaged_top_variances=items
                )
            )

    return result


def upsert_top_variance_comment(comment_item: TopVarianceCommentInput) -> None:
    top_variance_comment_repo.upsert_top_variance_comment(comment_item)
