from procurement.managers.imt.purchase_order import po_master_view, po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusResult


def get_wcc_po_status_by_po_number_sku(po_number: str, sku_code: str) -> list[PoStatusResult]:
    po_status_context = po_master_view.PoStatusByPoSkuContext(po_number, sku_code)
    if sku_po_master_view_list := po_master_view.build_po_status_dropdown(po_status_context):
        return po_status.to_extended_po_status(
            sku_po_master_view_list[0].brand, sku_po_master_view_list[0].dc, sku_po_master_view_list
        )
    return []
