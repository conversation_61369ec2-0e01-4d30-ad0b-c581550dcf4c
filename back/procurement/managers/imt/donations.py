from procurement.core.dates import ScmWeek
from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.models.highjump.highjump import DiscardType
from procurement.managers.admin import dc_admin
from procurement.managers.imt.discards import DiscardItem, DiscardsContext


def _convert_hj_discards_to_discard_items(hj_discards: list[HjDiscard], site: str) -> list[DiscardItem]:
    return [
        DiscardItem(
            site=site,
            sku_code=item.sku_code,
            discard_date=item.discard_date,
            quantity=item.quantity,
            tran_type=item.tran_type,
        )
        for item in hj_discards
    ]


def get_donations(sku_code: str, brands: list[str], sites: list[str], week: ScmWeek) -> list[DiscardItem]:
    donations = set()
    brand_sites = dc_admin.get_all_sites()
    for brand in brands:
        for site in sites:
            if site not in brand_sites[brand]:
                continue
            discard_context = DiscardsContext(
                brand=brand, site=site, week=week, sku_code=sku_code, tran_type=DiscardType.DONATION
            )
            donations.update(
                _convert_hj_discards_to_discard_items(discard_context.hj_discards.get(DiscardType.DONATION, []), site)
            )
    return list(donations)
