import dataclasses
from collections import defaultdict
from datetime import date
from decimal import Decimal
from functools import cached_property
from typing import NamedTuple

from procurement.constants.hellofresh_constant import ProductionPlanType
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import SITE, SKU_CODE, UNITS
from procurement.data.dto.inventory.hybrid_needs import HybridNeedsDto, HybridNeedsLiveUsage, ShiftNeedsDto
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.depletion import utils as depletion_utils
from procurement.managers.forecasts import forecast_manager
from procurement.managers.imt import buyers as buyers_service
from procurement.managers.imt import hybrid_needs
from procurement.managers.imt.v2.inventory.calculation import daily_needs_service
from procurement.managers.sku import culinary_sku
from procurement.repository.inventory import hybrid_needs as hybrid_needs_repo


@dataclasses.dataclass
class DailyValue:
    value_day: UNITS | None
    value_night: UNITS | None
    value_third: UNITS | None
    value: UNITS
    status: str
    live_usage: UNITS


class ProductionDayTitle(NamedTuple):
    plan_type: ProductionPlanType
    weekday_title: str


@dataclasses.dataclass
class ProductionNeedResult:
    sku_code: str
    daily: dict[date, DailyValue]

    def __post_init__(self):
        self._today = date.today()

    @cached_property
    def total(self) -> UNITS:
        return sum(daily_value.value for daily_value in self.daily.values())

    @cached_property
    def row_need(self) -> UNITS:
        return sum(daily_value.value for day, daily_value in self.daily.items() if day >= self._today)

    @cached_property
    def live_consumption(self) -> UNITS:
        return sum(
            daily_value.live_usage
            for day, daily_value in self.daily.items()
            if day == self._today and daily_value.status == ProductionPlanType.PROJECTED
        )

    @cached_property
    def live_row_need(self) -> UNITS:
        return max(self.row_need - self.live_consumption, 0)

    @cached_property
    def row_forecast(self):
        return sum(
            daily_value.value
            for day, daily_value in self.daily.items()
            if daily_value.status == ProductionPlanType.PROJECTED
        )

    @cached_property
    def planned_production(self):
        return sum(
            daily_value.value
            for day, daily_value in self.daily.items()
            if daily_value.status == ProductionPlanType.ACTUAL
        )


@dataclasses.dataclass
class ExtendedProductionNeedResult(ProductionNeedResult):
    sku_name: str | None
    delta: Decimal | None
    buyers: str | None


def _dto_from_daily_needs(
    daily_needs_item: tuple[tuple[SKU_CODE, date], dict[ScmWeek, int] | None], week: ScmWeek
) -> HybridNeedsDto:
    key, value = daily_needs_item
    return HybridNeedsDto(sku_code=key[0], day=key[1], value=(value.get(week, 0) if value else 0), scm_week=week)


def get_extended_production_need(
    sites: list[str], week: ScmWeek, brand: str
) -> dict[SITE, list[ExtendedProductionNeedResult]]:
    res = {}
    sku_meta = {}
    forecast = forecast_manager.get_forecast_manager()
    for site in sites:
        buyers_by_sku = buyers_service.get_buyers_by_sku(brand, site)
        forecast_oscar = forecast.get_forecast_oscar_by_sku_code(site=site, week=week, brand=brand)
        production_needs = get_production_need(site, week, brand)

        if sku_codes := frozenset(sku for sku in production_needs if sku not in sku_meta):
            sku_meta.update(culinary_sku.get_sku_meta_by_code(site=site, sku_codes=sku_codes))

        res[site] = [
            ExtendedProductionNeedResult(
                sku_code=production_needs_item.sku_code,
                daily=production_needs_item.daily,
                sku_name=sku.sku_name if (sku := sku_meta.get(production_needs_item.sku_code)) else None,
                delta=depletion_utils.calc_delta(
                    forecast_oscar.get(production_needs_item.sku_code), production_needs_item.total
                ),
                buyers=buyers_by_sku.get(production_needs_item.sku_code),
            )
            for production_needs_item in production_needs.values()
        ]
    return res


def _get_daily_needs(site: str, week: ScmWeek, brand: str) -> list[HybridNeedsDto]:
    needs = daily_needs_service.get_daily_needs_by_site_week(site=site, brand=brand, week=week)
    return [_dto_from_daily_needs(item, week) for item in needs.items()]


def _get_shift_needs(site: str, week: ScmWeek, brand: str) -> dict[tuple[SKU_CODE, date], ShiftNeedsDto]:
    return {(item.sku_code, item.day): item for item in hybrid_needs.get_hn_shift_data(brand, site, week)}


def get_production_need_headers(site: str, week: ScmWeek, brand: str) -> dict[date, ProductionDayTitle]:
    statuses = {item.day: item.status for item in hybrid_needs_repo.get_hybrid_needs_status_by_week(site, brand, week)}
    today = date.today()
    week_config = brand_admin.get_week_config(brand)
    return {
        # TODO: correctly fix default behaviour
        day: ProductionDayTitle(
            statuses.get(day, ProductionPlanType.ACTUAL if day <= today else ProductionPlanType.PROJECTED), title
        )
        for day, title in zip(week.production_days(week_config), ScmWeek.get_weekday_titles(week_config))
    }


def _get_live_usages(site: str, week: ScmWeek, brand: str) -> dict[tuple[SKU_CODE, date], HybridNeedsLiveUsage]:
    return {
        (item.sku_code, item.day): item.value
        for item in hybrid_needs_repo.get_hybrid_needs_live_usages(site, brand, week)
    }


def get_production_need(site: str, week: ScmWeek, brand: str) -> dict[SKU_CODE, ProductionNeedResult]:
    daily_needs_data = _get_daily_needs(site, week, brand)
    shift_needs = _get_shift_needs(site, week, brand)
    needs_daily_headers = get_production_need_headers(site, week, brand)
    live_usages = _get_live_usages(site, week, brand)

    production_need_result = {}
    for item in daily_needs_data:
        if item.sku_code not in production_need_result:
            production_need_result[item.sku_code] = ProductionNeedResult(
                sku_code=item.sku_code,
                daily={},
            )

        shift_need = shift_needs.get((item.sku_code, item.day))
        live_usage = live_usages.get((item.sku_code, item.day), Decimal())

        production_need_result[item.sku_code].daily[item.day] = DailyValue(
            value_day=shift_need.value_day if shift_need else None,
            value_night=shift_need.value_night if shift_need else None,
            value_third=shift_need.value_third if shift_need else None,
            value=item.value,
            status=needs_daily_headers[item.day].plan_type,
            live_usage=live_usage,
        )
    dc = dc_admin.get_site(brand=brand, site=site, week=week)
    if dc and dc.is_hj_autostore:
        _shift_hj_autostore_needs(production_need_result)
    production_need_result = {sku_code: item for sku_code, item in production_need_result.items() if item.total > 0}
    return production_need_result


def _shift_hj_autostore_needs(needs: dict[SKU_CODE, ProductionNeedResult]) -> None:
    for sku_needs in needs.values():
        days = sorted(sku_needs.daily)
        needs_values = [sku_needs.daily[d].value for d in days]
        # add first day needs to the second day
        needs_values[1] += needs_values[0]
        # shift all needs one day earlier
        needs_values.append(0)
        for day, value in zip(days, utils.iskip(needs_values, 1)):
            sku_needs.daily[day].value = value


class BrandSite(NamedTuple):
    brand: str
    site: str


def get_row_need(dcs: list[BrandSite], week: ScmWeek) -> dict[SKU_CODE, int]:
    result = defaultdict(int)
    for dc in dcs:
        for sku, production_need in get_production_need(site=dc.site, week=week, brand=dc.brand).items():
            result[sku] += production_need.row_need
    return result
