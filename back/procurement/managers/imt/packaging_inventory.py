from __future__ import annotations

import logging
from collections import defaultdict, namedtuple
from collections.abc import Iterable
from datetime import date, datetime
from enum import Enum, StrEnum
from typing import NamedTuple

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_GC, MARKET_US
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.packaging import PackagingOverride, PackagingOverrideKey
from procurement.data.googlesheet_model.packaging_demand import PackagingDemandRow, PackagingSkuDemand
from procurement.data.models.inventory.packaging_demand import PackagingDemandModel
from procurement.data.models.inventory.packaging_override import PackagingOverrideModel
from procurement.managers.admin import brand_admin, gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.inventory import packaging_depletion, packaging_sku_profile

logger = logging.getLogger(__name__)


class CompoundPartEnum(StrEnum):
    COMPOUND_PART_A = "Part A"
    COMPOUND_PART_B = "Part B"

    @staticmethod
    def get_compound_part(sku_name: str) -> str | None:
        if CompoundPartEnum.COMPOUND_PART_A in sku_name:
            return CompoundPartEnum.COMPOUND_PART_A
        if CompoundPartEnum.COMPOUND_PART_B in sku_name:
            return CompoundPartEnum.COMPOUND_PART_B
        return None


class LinerGroupEnum(namedtuple("LinerGroupEnum", ["full_name", "disp_name"]), Enum):
    XS_WIN = "XSMALL WINTER", "Liner - XS - WIN"
    XS_SPR = "XSMALL SPRING", "Liner - XS - SPR"
    XS_SUM = "XSMALL SUMMER", "Liner - XS - SUM"
    XS2_SUM = "XSMALL 2 SUMMER", "Liner - XSmall - SUM"
    XS_SUP = "XSMALL SUPER SUMMER", "Liner - XS - SUP"
    S_WIN = "SMALL WINTER", "Liner - Small - WIN"
    S_SPR = "SMALL SPRING", "Liner - Small - SPR"
    S_SUM = "SMALL SUMMER", "Liner - Small - SUM"
    S_SUP = "SMALL SUPER SUMMER", "Liner - Small - SUP"
    M_WIN = "MEDIUM WINTER", "Liner - Medium - WIN"
    M_SPR = "MEDIUM SPRING", "Liner - Medium - SPR"
    M_SUM = "MEDIUM SUMMER", "Liner - Medium - SUM"
    M_SUP = "MEDIUM SUPER SUMMER", "Liner - Medium - SUP"
    L_WIN = "LARGE WINTER", "Liner - Large - WIN"
    L_SPR = "LARGE SPRING", "Liner - Large - SPR"
    L_SUM = "LARGE SUMMER", "Liner - Large - SUM"
    L_SUP = "LARGE SUPER SUMMER", "Liner - Large - SUP"
    XL_WIN = "XLARGE WINTER", "Liner - XLarge - WIN"
    XL_SPR = "XLARGE SPRING", "Liner - XLarge - SPR"
    XL_SUM = "XLARGE SUMMER", "Liner - XLarge - SUM"
    XL_SUP = "XLARGE SUPER SUMMER", "Liner - XLarge - SUP"
    RTBS_WIN = "RTBS WINTER", "Liner - RTBS - WIN"
    RTBS_SPR = "RTBS SPRING", "Liner - RTBS - SPR"
    RTBS_SUM = "RTBS SUMMER", "Liner - RTBS - SUM"
    RTBS_SUP = "RTBS SUPER SUMMER", "Liner - RTBS - SUP"
    LINERLESS = "LINERLESS", "Linerless"

    def __new__(cls, *args):
        obj = tuple.__new__(cls, args)
        obj.order = len(cls.__members__)
        return obj

    def __str__(self):
        return self.disp_name

    @staticmethod
    def map_sku_name_to_liner_group(market: str, sku_name: str | None) -> LinerGroupEnum | None:
        if (
            market != MARKET_US
            or not sku_name
            or (
                not sku_name.startswith("Liner,")
                and not sku_name.startswith(f"{BRAND_GC}, Liner")
                and not sku_name.startswith("Factor, Liner")
            )
        ):
            return None
        size_set = (
            # fmt: off
            SeasonSizeSet.WIN if "WIN" in sku_name else
            SeasonSizeSet.SPR if "SPR" in sku_name else
            SeasonSizeSet.SUM if "SUM" in sku_name else
            SeasonSizeSet.SUP if "SUP" in sku_name
            else None
            # fmt: on
        )
        return SizeSet.map_size(sku_name, size_set) if size_set else None


class SizeSet(NamedTuple):
    xsmall: LinerGroupEnum
    small: LinerGroupEnum
    medium: LinerGroupEnum
    large: LinerGroupEnum
    xlarge: LinerGroupEnum
    rtbs: LinerGroupEnum

    @staticmethod
    def map_size(sku_name: str, size_set: SeasonSizeSet) -> LinerGroupEnum | None:
        return (
            # fmt: off
            size_set.xsmall if "XS" in sku_name else
            size_set.small if "SMALL" in sku_name else
            size_set.medium if "MEDIUM" in sku_name else
            size_set.large if "LARGE" in sku_name else
            size_set.xlarge if "XL" in sku_name else
            size_set.rtbs if "RTBS" in sku_name
            else None
            # fmt: on
        )


class SeasonSizeSet(SizeSet, Enum):
    WIN = SizeSet(
        LinerGroupEnum.XS_WIN,
        LinerGroupEnum.S_WIN,
        LinerGroupEnum.M_WIN,
        LinerGroupEnum.L_WIN,
        LinerGroupEnum.XL_WIN,
        LinerGroupEnum.RTBS_WIN,
    )
    SPR = SizeSet(
        LinerGroupEnum.XS_SPR,
        LinerGroupEnum.S_SPR,
        LinerGroupEnum.M_SPR,
        LinerGroupEnum.L_SPR,
        LinerGroupEnum.XL_SPR,
        LinerGroupEnum.RTBS_SPR,
    )
    SUM = SizeSet(
        LinerGroupEnum.XS_SUM,
        LinerGroupEnum.S_SUM,
        LinerGroupEnum.M_SUM,
        LinerGroupEnum.L_SUM,
        LinerGroupEnum.XL_SUM,
        LinerGroupEnum.RTBS_SUM,
    )
    SUP = SizeSet(
        LinerGroupEnum.XS_SUP,
        LinerGroupEnum.S_SUP,
        LinerGroupEnum.M_SUP,
        LinerGroupEnum.L_SUP,
        LinerGroupEnum.XL_SUP,
        LinerGroupEnum.RTBS_SUP,
    )


def update_packaging_demand_data(str_week: str, brand: str) -> None:
    week = ScmWeek.from_str(str_week)
    sheet_data = gsheet_admin.read_gsheet(PackagingSkuDemand(), week=week, brand=brand)
    if not sheet_data:
        return
    _update_packaging_demand(week, sheet_data)
    _update_packaging_sku_profile(sheet_data)


def _update_packaging_demand(week: ScmWeek, sheet_data: list[PackagingDemandRow]) -> None:
    market = context.get_request_context().market
    brand_map = brand_admin.get_brand_ids_by_name(week)
    number_week = int(week)
    demand_updates = []
    for row in sheet_data:
        try:
            _validate_sku_demand_row(row, brand_map)
            demand_updates.append(
                {
                    PackagingDemandModel.week: number_week,
                    PackagingDemandModel.site: row.site,
                    PackagingDemandModel.brand: brand_map[row.full_brand],
                    PackagingDemandModel.sku_code: row.sku_code,
                    PackagingDemandModel.demand_by_day: row.demand_by_day,
                    PackagingDemandModel.market: market,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, f"Error while importing {PackagingSkuDemand.sheet_name} data", skipped=True
            )

    packaging_depletion.update_packaging_demand(demand_updates)


def _update_packaging_sku_profile(sheet_data: list[PackagingDemandRow]) -> None:
    market = context.get_request_context().market
    sku_profiles = {}
    for row in sheet_data:
        try:
            sku_profiles[row.sku_code] = {
                "sku_code": row.sku_code,
                "size": row.size,
                "type": row.sku_type,
                "profile": row.demand_profile,
                "market": market,
            }
        except ValueError as exc:
            warnings.notify_sheet_error(
                row,
                exc,
                f"Error while importing Packaging Sku Profile {PackagingSkuDemand.sheet_name} data",
                skipped=True,
            )
    packaging_sku_profile.upsert_packaging_sku_profile(list(sku_profiles.values()))


def get_packaging_demand(weeks: Iterable[ScmWeek], site: str, brand: str) -> dict[ScmWeek, dict[SKU_CODE, list[int]]]:
    demands = packaging_depletion.get_packaging_demand(weeks=weeks, site=site, brand=brand)
    res = {int(w): {} for w in weeks}
    for item in demands:
        res[item.week][item.sku_code] = item.demand_by_day
    return {ScmWeek.from_number(week): demand for week, demand in res.items()}


def _validate_sku_demand_row(row, brand_map: dict[str, str]) -> None:
    validate_required_fields(row, ["full_brand", "site", "sku_code"])
    if row.full_brand not in brand_map:
        raise ValueError(f"Unknown brand '{row.full_brand}'")


def get_packaging_overrides(
    weeks: Iterable[ScmWeek], site: str, brand: str
) -> dict[ScmWeek, dict[SKU_CODE, dict[date, PackagingOverride]]]:
    packaging_overrides = packaging_depletion.get_packaging_overrides(weeks=weeks, site=site, brand=brand)
    res = {int(w): defaultdict(dict) for w in weeks}
    for item in packaging_overrides:
        res[item.week][item.sku_code][item.day] = PackagingOverride(item.on_hand_override, item.incoming_override)
    return {ScmWeek.from_number(week): overrides for week, overrides in res.items()}


def save_packaging_override(key: PackagingOverrideKey, override: PackagingOverride, user_id: int) -> None:
    existing_override = packaging_depletion.get_packaging_override(
        PackagingOverrideKey(week=key.week, site=key.site, brand=key.brand, sku_code=key.sku_code, day=key.day)
    )
    if existing_override == override:
        return
    packaging_depletion.save_packaging_override(
        {
            PackagingOverrideModel.week: int(key.week),
            PackagingOverrideModel.site: key.site,
            PackagingOverrideModel.brand: key.brand,
            PackagingOverrideModel.sku_code: key.sku_code,
            PackagingOverrideModel.day: key.day,
            PackagingOverrideModel.on_hand_override: override.on_hand_override,
            PackagingOverrideModel.incoming_override: override.incoming_override,
            PackagingOverrideModel.updated_by: user_id,
            PackagingOverrideModel.last_updated: datetime.now(),
        }
    )
