from dataclasses import dataclass
from datetime import date, datetime
from decimal import Decimal
from typing import NamedTuple

from procurement.constants.hellofresh_constant import DayOfWeek, UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.dto.ordering.shipment import Shipment
from procurement.data.models.inventory.weekend_coverage_checklist import ChecklistStatus


class PoMasterViewResult(NamedTuple):
    dc: str
    bob_code: str
    sku_code: str
    sku_id: int
    sku_name: str
    supplier_code: int
    supplier: str
    po_number: str
    ot_po_status: str
    is_sent: bool
    week: ScmWeek
    order_date: datetime
    delivery_time_start: datetime
    order_size: int
    order_unit: str
    case_price: Decimal
    case_size: Decimal
    case_unit: UnitOfMeasure | None
    allocation_price: Decimal | None
    case_price_flag: bool
    quantity: Decimal
    buffer: Decimal
    total_price: Decimal
    emergency_reason: str
    scheduled_delivery_date: date
    total_cases_received: int
    total_quantity_received: Decimal
    total_quantity_received_by_date: dict[date, Decimal]
    case_size_received: Decimal
    actual_delivery_date: date
    actual_delivery_datetime: datetime
    po_received: bool
    po_void: bool
    po_status: str
    receive_variance: int
    cost: float
    shipping_method: str
    po_buyer: str
    total_price_received: Decimal
    proposed_quantity_cases: int | None
    proposed_units_per_cases: int | None
    proposed_quantity_units: int | None
    proposed_delivery_date: datetime | None
    asn_shipment_date: datetime | None
    asn_planned_delivery_time: datetime | None
    asn_shipped_quantity_cases: int
    asn_units_of_measure: UnitOfMeasure
    asn_case_count: int
    asn_shipped_quantity_units: int
    asn_requires_high_attention: bool
    asn_requires_attention: bool
    hj_unit: UnitOfMeasure | None
    brand: str | None
    percentage_of_the_forecasted: Decimal
    shipment_data: Shipment = None
    transfer_source_bob_code: str | None = None
    has_multiple_transfer_items: bool = False

    @property
    def po_sku_combination(self) -> str:
        return f"{self.po_number}{self.sku_code}"


@dataclass
class ChecklistSubmissionModel:
    ui_row_id: int | None
    po_number: str
    sku_name: str
    po_landing_day: DayOfWeek
    production_day_affected: DayOfWeek
    to_check: str
    contact_name_vendor_carrier: str
    email_phone: str
    back_up_vendor: str
    fob_pick_up_date: date


@dataclass
class ChecklistModel(ChecklistSubmissionModel):
    checklist_id: int
    brand: str
    site: str
    sku_code: str
    updated_by: str | None
    ship_method: str
    po_status: str
    status: ChecklistStatus | None = None
    comment: str | None = None
    carrier_name: str = None


@dataclass
class ProductionNeedsPackaging:
    sku_code: SKU_CODE
    row_need: Decimal
