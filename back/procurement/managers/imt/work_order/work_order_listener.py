from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from confluent_kafka import Message
from confluent_kafka.serialization import MessageField, SerializationContext
from hellofresh.proto.stream.rte.work_order.v2.work_order_pb2 import WorkOrder

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates.weeks import ScmWeek
from procurement.core.kafka import handlers
from procurement.core.kafka.handlers import KafkaHandler
from procurement.data.models.inventory.work_order import WorkOrderModel
from procurement.repository.inventory import work_order


class WorkOrderItemKey(NamedTuple):
    wo_uuid: str
    menu_week: int
    high_jump_warehouse_id: str
    sku_code: str
    recipe_sku: str


class WorkOrderHandler(KafkaHandler):
    def deserialize_value(self, value: bytes, ctx: SerializationContext):
        return handlers.parse_protobuf(value, WorkOrder, MessageField.VALUE)

    def _process(self, msg: Message) -> None:
        value: WorkOrder = msg.value()
        if not value.locked:
            return
        res = defaultdict(Decimal)
        if any(map(value.sub_recipe_sku.code.startswith, ["SUB", "REC"])):
            process_wo_item(value, value.bill_of_materials, res)
        else:
            process_sku_item(value, res)

        updates = []
        for item, quantity in res.items():
            if ScmWeek.is_valid_number(item.menu_week):
                week = int(item.menu_week)
            else:
                week = int(ScmWeek.from_str(item.menu_week))
            updates.append(
                {
                    WorkOrderModel.wo_uuid: item.wo_uuid,
                    WorkOrderModel.menu_week: week,
                    WorkOrderModel.high_jump_warehouse_id: item.high_jump_warehouse_id,
                    WorkOrderModel.sku_code: item.sku_code,
                    WorkOrderModel.quantity: quantity,
                    WorkOrderModel.recipe_sku: item.recipe_sku,
                }
            )
        work_order.upsert_work_orders(updates)


def process_wo_item(value: WorkOrder, bill_of_materials: list, res: dict):
    for item in bill_of_materials:
        if not item.is_manufactured:
            quantity = get_quantity(item.quantities)
            res[
                WorkOrderItemKey(
                    wo_uuid=value.id,
                    menu_week=value.menu_week,
                    high_jump_warehouse_id=value.high_jump_warehouse_id,
                    sku_code=item.code,
                    recipe_sku=value.recipe_sku.code,
                )
            ] += quantity
        else:
            process_wo_item(value, item.bill_of_materials, res)


def process_sku_item(value: WorkOrder, res: dict):
    quantity = get_quantity(value.quantities)
    res[
        WorkOrderItemKey(
            wo_uuid=value.id,
            menu_week=value.menu_week,
            high_jump_warehouse_id=value.high_jump_warehouse_id,
            sku_code=value.sub_recipe_sku.code,
            recipe_sku=value.recipe_sku.code,
        )
    ] += quantity


def get_quantity(quantities):
    quantity = Decimal()
    for qty in quantities:
        if UnitOfMeasure.inexact_value_of(qty.unit_of_measure) == UnitOfMeasure.POUND:
            quantity = qty.quantity
    return Decimal(str(quantity))
