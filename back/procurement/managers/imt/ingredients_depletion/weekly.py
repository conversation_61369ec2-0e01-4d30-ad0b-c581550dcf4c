import itertools
from collections.abc import Iterable
from datetime import date
from decimal import Decimal
from functools import cached_property

from procurement.client.googlesheets.googlesheet_utils import safe_sum
from procurement.constants.hellofresh_constant import BRAND_GC, MARKET_US, InventoryInputType
from procurement.constants.ordering import IN_PROGRESS_HJ, NOT_DELIVERED_PAST_DUE
from procurement.core import utils
from procurement.core.typing import UNITS
from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.dto.inventory.discarding import Discard
from procurement.data.models.highjump.highjump import DiscardType
from procurement.data.models.inventory import DataSource
from procurement.managers.depletion.weekly import AllocationPriceRange, DepletionWeeklyItem
from procurement.managers.imt.purchase_order import PoMasterViewResult

from .core import IngredientDepletionContext, IngredientSummary


class SiteDepletionWeeklyItem(DepletionWeeklyItem):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        context: IngredientDepletionContext,
        ingredient: IngredientSummary,
        units_to_produce_by_autobagger: int,
        start_of_day_inv: int,
        prev_eow_inventory: Decimal,
    ):
        self.context = context
        self.ingredient = ingredient
        self.po_master_view_sku = self.context.get_po_master_view_sku(ingredient.sku_code) or []
        self._start_of_day_inventory = Decimal(start_of_day_inv)
        self._units_to_produce_by_autobagger = units_to_produce_by_autobagger
        self._prev_eow_inventory = prev_eow_inventory

    @cached_property
    def sku_code(self) -> str:
        return self.ingredient.sku_code

    @property
    def prev_eow_inventory(self) -> UNITS | None:
        return self._prev_eow_inventory

    @cached_property
    def warehouse_name(self) -> str:
        return self.context.dc_object.high_jump_name

    @cached_property
    def start_of_day_inventory(self) -> Decimal:
        return self._start_of_day_inventory

    @cached_property
    def units_to_produce_by_autobagger(self) -> int:
        return self._units_to_produce_by_autobagger

    @cached_property
    def units_needed(self) -> UNITS | None:
        return self.context.forecast.get(self.sku_code)

    @cached_property
    def units_received_pre_snapshot(self) -> Decimal:
        return utils.ceil_subone_decimal(
            safe_sum(
                qty
                for item in self.po_master_view_sku
                for receipt_date, qty in item.total_quantity_received_by_date.items()
                if receipt_date < self.context.weekly_snapshot_date
            )
        )

    @cached_property
    def units_received_post_snapshot(self) -> Decimal:
        return utils.ceil_subone_decimal(
            safe_sum(
                qty
                for item in self.po_master_view_sku
                for receipt_date, qty in item.total_quantity_received_by_date.items()
                if receipt_date >= self.context.weekly_snapshot_date
            )
        )

    @cached_property
    def units_ordered(self) -> Decimal:
        return get_units_ordered(self.po_master_view_sku)

    @cached_property
    def wip_hj_discards(self) -> int:
        wip_discard = get_discards(
            self.context.actual_hj_discards.get(DiscardType.WIP_DISCARD, {}).get(self.sku_code, {})
        )
        bulk_discards = get_discards(
            self.context.actual_hj_discards.get(DiscardType.BULK_DISCARD, {}).get(self.sku_code, {})
        )
        autobugger_discards = get_discards(
            self.context.actual_hj_discards.get(DiscardType.AUTOBUGGER_DISCARD, {}).get(self.sku_code, {})
        )
        return wip_discard + bulk_discards + autobugger_discards

    @cached_property
    def hj_discards(self) -> int:
        return get_discards(self.context.actual_hj_discards.get(DiscardType.DISCARD, {}).get(self.sku_code, {}))

    @cached_property
    def discards(self) -> int:
        if self.context.brand == BRAND_GC:
            return self.wip_hj_discards + get_discards(self.context.non_hj_discard_data.get(self.sku_code, {}))
        return (
            self.wip_hj_discards
            if self.context.dc_object.source == DataSource.HIGHJUMP
            else get_discards(self.context.non_hj_discard_data.get(self.sku_code, {}))
        )

    @cached_property
    def donations(self) -> int:
        return get_discards(self.context.actual_hj_discards.get(DiscardType.DONATION, {}).get(self.sku_code, {}))

    @cached_property
    def total_on_hand(self) -> int:
        if self.context.market != MARKET_US:
            if self.context.today < self.context.weekly_snapshot_date:
                start_of_week_units = self.prev_eow_inventory
                units_received = self.units_received_pre_snapshot
            else:
                start_of_week_units = self.context.get_weekly_snapshot(self.sku_code) or 0
                units_received = self.units_received_post_snapshot
        else:
            units_received = self.units_received
            start_of_week_units = Decimal()

        return int(start_of_week_units + units_received + self.inventory - self.discards - self.pulls)

    @cached_property
    def wip_consumption_carryover(self) -> Decimal:
        return self.context.wip_consumption_carryover.get(self.sku_code, Decimal())

    @cached_property
    def wip_consumption_initial_pull(self) -> Decimal:
        return self.context.wip_consumption_initial_pull.get(self.sku_code, Decimal())

    @cached_property
    def wip_consumption_putaway_to_production(self) -> Decimal:
        return self.context.wip_consumption_putaway_to_production.get(self.sku_code, Decimal())

    @cached_property
    def in_progress_hj(self) -> Decimal:
        if self.context.dc_object.receiving_type.is_high_jump:
            return self.context.sum_quantity_with_status(
                lambda status: status.startswith(IN_PROGRESS_HJ), self.sku_code
            )
        return Decimal()

    @cached_property
    def awaiting_delivery(self) -> Decimal:
        return self.context.awaiting_delivery(self.sku_code)

    @cached_property
    def not_delivered(self) -> Decimal:
        return self.context.sum_quantity_with_status(lambda status: status == NOT_DELIVERED_PAST_DUE, self.sku_code)

    @cached_property
    def hj_units_in_house(self) -> Decimal:
        return self.context.get_units_in_house(self.sku_code)

    @cached_property
    def row_need(self) -> int:
        return self.context.plan[self.sku_code].row_need

    @cached_property
    def prev_week_row_need(self) -> int:
        return self.context.prev_week_row_need[self.sku_code].row_need

    @cached_property
    def next_week_forecast(self) -> Decimal | None:
        return self.context.forecast_next_week.get(self.sku_code)

    @cached_property
    def hj_snapshot(self) -> Decimal | None:
        return self.context.get_weekly_snapshot_raw(self.sku_code) if self.context.market != MARKET_US else None

    @cached_property
    def inventory(self) -> int:
        return self.context.get_inventories(self.sku_code)

    @cached_property
    def pulls(self) -> int:
        return self.context.get_pulls(self.sku_code)

    @cached_property
    def hj_autostore_inventory(self) -> int | None:
        return self.context.hj_autostore.get(self.sku_code)

    @cached_property
    def units_on_prod_floor(self) -> Decimal:
        return self.context.units_on_prod_floor.get(self.sku_code, Decimal())

    @cached_property
    def allocation_price_range(self) -> AllocationPriceRange | None:
        allocation_price = self.context.allocation_prices.get(self.sku_code)
        return AllocationPriceRange(allocation_price, allocation_price) if allocation_price is not None else None

    @cached_property
    def buffer_quantity(self) -> Decimal | None:
        if self.context.market != MARKET_US and self.context.dc_object.inventory_type == InventoryInputType.WMSL:
            return None
        return super().buffer_quantity

    @cached_property
    def buffer_percent(self) -> Decimal | None:
        if self.context.market != MARKET_US and self.context.dc_object.inventory_type == InventoryInputType.WMSL:
            return None
        return super().buffer_percent


def get_units_ordered(po_master_view_sku: Iterable[PoMasterViewResult]) -> Decimal:
    return utils.ceil_subone_decimal(safe_sum(mv.quantity for mv in po_master_view_sku if not mv.po_void))


def get_total_quantity_received(po_master_view_sku: Iterable[PoMasterViewResult]) -> Decimal:
    return utils.ceil_subone_decimal(safe_sum(mv.total_quantity_received for mv in po_master_view_sku))


def get_discards(discard_sku: dict[date, list[HjDiscard | Discard]]) -> int:
    return sum(item.quantity for item in itertools.chain.from_iterable(discard_sku.values()))
