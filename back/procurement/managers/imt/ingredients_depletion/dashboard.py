import logging
from collections.abc import Iterable

from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import warnings
from procurement.managers.admin import dc_admin

from . import aggregated
from .core import IngredientDepletionContext
from .master_bulk import BulkMasterDepletionResult
from .result import SiteIngredientDepletionResult

logger = logging.getLogger(__name__)


def filter_missing_dcs(week: ScmWeek, dcs: Iterable[str], brand: str) -> tuple[list[str], list[str]]:
    available_dcs = dc_admin.get_enabled_sites(week, brand)
    available = []
    not_available = []

    for dc in dcs:
        if dc in available_dcs:
            available.append(dc)
        else:
            not_available.append(dc)
    return available, not_available


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_ingredient_depletion(
    week: ScmWeek,
    sites: Iterable[str],
    brand: str,
    include_bulk_skus: bool = False,
) -> dict[str, list[SiteIngredientDepletionResult]]:
    available_sites, not_available_sites = filter_missing_dcs(week, sites, brand)
    res = {}
    for site in available_sites:
        context = IngredientDepletionContext(week=week, site=site, brand=brand, include_bulk_skus=include_bulk_skus)
        res[site] = SiteIngredientDepletionResult.build_results(context=context)
    for site in not_available_sites:
        warnings.add_message(f"Site {site} is not available for brand {brand} at week {week}")
        res[site] = []
    return res


def get_dashboard_depletion(
    week: ScmWeek, sites: Iterable[str], brand: str
) -> dict[str, list[SiteIngredientDepletionResult]]:
    available_sites, not_available_sites = filter_missing_dcs(week, sites, brand)
    res = {}
    for site in available_sites:
        context = IngredientDepletionContext(week=week, site=site, brand=brand, include_bulk_skus=True)
        if context.dc_object.consolidated_site_code and killswitch.show_aggregated_depletion_on_ingredient_depletion:
            res[site] = aggregated.get_aggregated_ingredient_depletion(
                week=week,
                aggregated_site=context.dc_object.consolidated_site_code,
                base_depletion_type=BulkMasterDepletionResult,
            )
        else:
            res[site] = BulkMasterDepletionResult.build_results(context=context)
    for site in not_available_sites:
        warnings.add_message(f"Site {site} is not available for brand {brand} at week {week}")
        res[site] = []
    return res
