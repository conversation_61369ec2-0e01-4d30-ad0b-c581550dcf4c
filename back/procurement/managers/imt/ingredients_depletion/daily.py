from __future__ import annotations

import functools
from collections import defaultdict
from datetime import date
from decimal import Decimal
from functools import cached_property
from typing import TypeVar

from procurement.constants.hellofresh_constant import BRAND_GC, MARKET_US
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, IN_PROGRESS_HJ
from procurement.core.typing import UNITS
from procurement.data.models.highjump.highjump import DiscardType
from procurement.data.models.inventory import DataSource
from procurement.managers.depletion.constants import CANADA_SNAPSHOT_DAY
from procurement.managers.depletion.daily import DailyData, DailyDepletion
from procurement.managers.imt.models import PoMasterViewResult

from .core import IngredientDepletionContext

T = TypeVar("T")


class SiteDailyDepletion(DailyDepletion):
    def __init__(
        self,
        context: IngredientDepletionContext,
        sku_code: str,
        units_to_produce_by_autobagger: int,
        prev_eow_inventory: UNITS,
    ):
        self.context = context
        self._sku_code = sku_code
        self.po_master_view_sku = self.context.get_po_master_view_sku(sku_code)
        self.units_to_produce_by_autobagger = units_to_produce_by_autobagger
        self.prev_eow_inventory = prev_eow_inventory

    @cached_property
    def sku_code(self) -> str:
        return self._sku_code

    @cached_property
    def weekly_snapshot_day(self) -> int:
        return self.context.weekly_snapshot_day

    @cached_property
    def manual_inventory_day(self) -> int:
        return self.weekly_snapshot_day

    @cached_property
    def manual_form_inventory(self) -> int:
        return self.context.get_inventories(self.sku_code) - self.context.get_pulls(self.sku_code)

    @cached_property
    def days(self) -> tuple[date, ...]:
        return self.context.days

    @cached_property
    def daily_calc(self) -> DailyData:
        return DailyData(
            units_delivered=self.units_delivered,
            discards=self.discards,
            production_need_values=self.production_needs,
            prev_eow_inventory=self.prev_eow_inventory,
            prod_week_length=self.context.prod_week_length,
            weekly_snapshot_day=self.weekly_snapshot_day,
            weekly_snapshot=self.context.get_weekly_snapshot(self.sku_code),
            manual_inventory_day=self.manual_inventory_day,
            manual_form_inventory=self.manual_form_inventory,
        ).build_data()

    @cached_property
    def production_needs(self) -> list[UNITS]:
        production_need_days = {
            day: max(0, daily.value) for day, daily in self.context.plan[self.sku_code].daily.items()
        }
        if not production_need_days:
            return [Decimal()] * len(self.context.days)
        res = [production_need_days.get(day, 0) for day in self.context.days]
        # capture needs from last production day in last depletion day needs since it's not shown on the dashboard
        last_prod_day_demand = production_need_days.get(
            self.context.week.get_last_production_day(self.context.week_config), 0
        )
        res[-1] += last_prod_day_demand

        return res

    @cached_property
    def pick_conversions(self) -> int:
        return self.context.bulk_sku_manager.get_pick_conversion(self.sku_code)

    @cached_property
    def units_on_order(self) -> list[UNITS]:
        if not self.po_master_view_sku:
            return [Decimal()] * len(self.context.days)
        pos_by_receive_date: dict[date, list[PoMasterViewResult]] = defaultdict(list)
        for po in self.po_master_view_sku:
            if po.scheduled_delivery_date and (
                po.po_status in AWAITING_DELIVERY_STATUSES or po.po_status.startswith(IN_PROGRESS_HJ)
            ):
                pos_by_receive_date[po.scheduled_delivery_date].append(po)
        return self._get_daily_quantities(self.context.days, pos_by_receive_date, lambda po: po.quantity)

    @cached_property
    def discards(self) -> list[int]:
        get_dailies = functools.partial(
            self._get_daily_quantities, days=self.context.days, qty_field_getter=lambda d: d.quantity
        )
        non_hj_discards = get_dailies(skus=self.context.non_hj_discard_data.get(self.sku_code, {}))

        wip_discards = get_dailies(
            skus=self.context.actual_hj_discards.get(DiscardType.WIP_DISCARD, {}).get(self.sku_code, {})
        )
        bulk_discards = get_dailies(
            skus=self.context.actual_hj_discards.get(DiscardType.BULK_DISCARD, {}).get(self.sku_code, {})
        )
        autobugger_discards = get_dailies(
            skus=self.context.actual_hj_discards.get(DiscardType.AUTOBUGGER_DISCARD, {}).get(self.sku_code, {})
        )
        hj_discards = [
            wip + bulk + autobugger for wip, bulk, autobugger in zip(wip_discards, bulk_discards, autobugger_discards)
        ]

        if self.context.brand == BRAND_GC:
            return [hj_discard + non_hj_discard for hj_discard, non_hj_discard in zip(hj_discards, non_hj_discards)]
        return hj_discards if self.context.dc_object.source == DataSource.HIGHJUMP else non_hj_discards

    @cached_property
    def units_delivered(self) -> list[UNITS]:
        if not self.po_master_view_sku:
            return [Decimal()] * len(self.context.days)
        delivered_by_day = defaultdict(list)
        for po in self.po_master_view_sku:
            for date_, qty in po.total_quantity_received_by_date.items():
                delivered_by_day[date_].append(qty)
        res = self._get_daily_quantities(self.context.days, delivered_by_day, lambda it: it)
        if self.context.dc_object.receiving_type.is_high_jump:
            # add Autobagger units to production day of market
            production_day = CANADA_SNAPSHOT_DAY if self.context.market != MARKET_US else 0
            res[production_day] += self._get_hj_units_delivered()
        return res

    def _get_hj_units_delivered(self) -> UNITS:
        hj_sum = sum(
            po.total_quantity_received
            for po in self.po_master_view_sku
            if not po.actual_delivery_date and po.po_received
        )
        return self.units_to_produce_by_autobagger if hj_sum == 0 else 0

    @cached_property
    def sku_pallet_quantity(self) -> Decimal:
        return self.context.get_sku_pallet_qty(self.sku_code)

    @cached_property
    def live_row_need(self) -> UNITS:
        return self.context.plan[self.sku_code].live_row_need
