from __future__ import annotations

import itertools
import logging
from collections import defaultdict
from collections.abc import Collection
from datetime import date, timedelta
from decimal import Decimal
from functools import cached_property
from typing import Callable, NamedTuple

from procurement.client.googlesheets.googlesheet_utils import safe_sum
from procurement.constants.hellofresh_constant import (
    BRAND_FJ,
    BRAND_GC,
    MARKET_CA,
    MARKET_US,
    HjWipConsumptionType,
    IngredientCategory,
    UnitOfMeasure,
    WmslLocationType,
)
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES
from procurement.core import utils as core_utils
from procurement.core.config_utils import killswitch
from procurement.core.dates.weeks import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE, UNITS
from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.dto.inventory.discarding import Discard
from procurement.data.dto.inventory.pull_put import PullPutDto
from procurement.data.models.highjump.highjump import DiscardType
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.depletion import core
from procurement.managers.depletion.constants import CANADA_SNAPSHOT_DAY
from procurement.managers.distribution_center import cycle_counts, highjump
from procurement.managers.distribution_center import hj_autostore as hj_autostore_manager
from procurement.managers.forecasts import forecast_manager
from procurement.managers.forms import discarding
from procurement.managers.imt import buyers, inventory, production_need
from procurement.managers.imt.bulk_sku import BulkSkuManager, EmptyBulkSkuManager
from procurement.managers.imt.product_kit_guide import ingredients as pkg_ingredients
from procurement.managers.imt.production_need import ProductionNeedResult
from procurement.managers.imt.purchase_order import PoMasterViewResult, po_master_view
from procurement.managers.inventory import imt as imt_inventory
from procurement.managers.inventory import snapshots as inv_snapshots
from procurement.managers.ordering import allowed_produce_buffer
from procurement.managers.ordering.manufactured_sku import ManufacturedSkuBreakdown
from procurement.managers.prices import allocation_tool_price
from procurement.managers.sku import alternative_sku_mapping, culinary_sku
from procurement.repository.forecast import oscar as oscar_repo
from procurement.repository.inventory import weekly_snapshots as weekly_snapshot_repo
from procurement.repository.inventory import work_order
from procurement.repository.ordering import staged_inventory

logger = logging.getLogger(__name__)

US_EXPIRING_INVENTORY_EXCLUDE_DAYS = 10
CA_EXPIRING_INVENTORY_EXCLUDE_DAYS = 12


class IngredientSummary(NamedTuple):
    sku_code: str
    sku_id: int
    sku_name: str
    unit_of_measure: UnitOfMeasure
    purchasing_category_name: str
    purchasing_subcategory_name: str
    commodity_group_name: str
    meal_numbers: tuple[str, ...]


class IngredientDepletionContext:
    def __init__(  # pylint: disable=too-many-arguments
        self,
        week: ScmWeek,
        site: str,
        brand: str,
        sku_codes: set[SKU_CODE] | None = None,
        include_bulk_skus: bool = False,
        remake_sku_units: dict[SKU_CODE, int] | None = None,
        remake_day: str | None = None,
        include_no_demand_skus: bool = False,
    ):
        self.week = week
        self.site = site
        self.brand = brand
        self.market = context.get_request_context().market
        self.dc_object = dc_admin.get_site(brand=brand, site=site, week=week)
        self.min_inventory_week = min(week, ScmWeek.current_week(brand_admin.get_week_config(brand)))
        self.week_config = brand_admin.get_week_config(self.brand)
        self.weekly_snapshot_day = CANADA_SNAPSHOT_DAY if self.market != MARKET_US else 0

        self.scm_week_start = week.get_first_day(self.week_config)
        self.today = date.today()
        days = core.get_weekdays(week, self.week_config)
        self.prod_week_start = days[0].day
        self.current_week = ScmWeek.current_week(self.week_config)

        self.weekly_snapshot_date = self.prod_week_start + timedelta(days=self.weekly_snapshot_day)
        self.prod_week_length = len(days)
        self.days = tuple(depl_day.day for depl_day in days)
        self.sku_codes: frozenset[str] | None = frozenset(sku_codes) if sku_codes else None
        self.include_bulk_skus = include_bulk_skus
        self.include_no_demand_skus = include_no_demand_skus

        self.remake_sku_units = remake_sku_units or {}
        self.remake_day = remake_day

    @cached_property
    def is_old_week(self) -> bool:
        return self.week < ScmWeek.current_week(self.week_config) - 2

    @cached_property
    def previous_week_context(self) -> IngredientDepletionContext:
        return IngredientDepletionContext(
            week=self.week - 1,
            site=self.site,
            brand=self.brand,
            sku_codes=self.sku_codes,
            include_bulk_skus=self.include_bulk_skus,
            remake_sku_units=self.remake_sku_units,
            remake_day=self.remake_day,
            include_no_demand_skus=self.include_no_demand_skus,
        )

    @cached_property
    def allocation_prices(self) -> dict[SKU_CODE, Decimal]:
        return allocation_tool_price.get_allocation_prices(week=self.week, dc_obj=self.dc_object, brand=self.brand)

    @cached_property
    def sku_meals(self) -> dict[SKU_CODE, list[str]]:
        if self.market != MARKET_US:
            return self._canada_forecast_recipes

        pkg_ingredient_dc = self.dc_object.pkg_name if self.dc_object.pkg_name else self.site
        return pkg_ingredients.get_sku_meal_numbers(
            week=self.week, site=pkg_ingredient_dc, brand=self.brand, sku_codes=self.sku_codes
        )

    @cached_property
    def _canada_forecast_recipes(self) -> dict[SKU_CODE, list[str]]:
        res = {}
        for recipe in oscar_repo.get_canada_forecast_recipes(
            week=self.week, site=self.dc_object.sheet_name, brand=self.brand
        ):
            res[recipe.sku_code] = recipe.recipes
        return res

    @cached_property
    def ingredient_summary(self) -> dict[SKU_CODE, IngredientSummary]:
        summary = {
            **self.dashboard_skus,
            **self._build_ingredient_summary(frozenset(self.bulk_sku_manager.reversed_bulk_skus_mapping)),
        }
        if self.market != MARKET_US:
            summary.update(self._build_ingredient_summary(frozenset(self.weekly_snapshots - summary.keys())))
        return summary

    @cached_property
    def dashboard_skus(self) -> dict[SKU_CODE, IngredientSummary]:
        if self.market != MARKET_US:
            sku_codes = list(self.plan)
        else:
            sku_codes = list(self.forecast if self.brand in (BRAND_GC, BRAND_FJ) else self.sku_meals)
        include_skus_from_previous_week = (
            # only CA market has inventory carryover from previous week,
            # see SiteIngredientDepletionResult.prev_eow_inventory
            self.market == MARKET_CA
            and not self.is_old_week
            and self.include_no_demand_skus
            and self.previous_week_context.dc_object
            and killswitch.ing_depl_consider_prev_week_inv
        )
        if self.include_no_demand_skus:
            sku_codes.extend(self.po_master_view)
            sku_codes.extend(self._sku_pallets)
            sku_codes.extend(itertools.chain.from_iterable(self._hj_wip_consumption.values()))
            sku_codes.extend(it.sku_code for it in self._inventory_inputs)
        if include_skus_from_previous_week:
            sku_codes.extend(self.previous_week_context.dashboard_skus.keys())
        return self._build_ingredient_summary(frozenset(sku_codes))

    def _build_ingredient_summary(self, sku_codes: frozenset[str]) -> dict[SKU_CODE, IngredientSummary]:
        sku_meta = culinary_sku.get_sku_meta(site=self.site, sku_codes=sku_codes)
        empty_meals = tuple()
        return {
            sku.sku_code: IngredientSummary(
                sku_code=sku.sku_code,
                sku_id=sku.sku_id,
                sku_name=sku.sku_name,
                unit_of_measure=sku.unit,
                purchasing_category_name=sku.category,
                purchasing_subcategory_name=sku.subcategory,
                commodity_group_name=sku.commodity_group,
                meal_numbers=tuple(self.sku_meals.get(sku.sku_code, empty_meals)),
            )
            for sku in sku_meta
        }

    @cached_property
    def buyers(self):
        return buyers.get_buyers_by_sku(self.brand, self.site)

    @cached_property
    def bulk_sku_manager(self) -> BulkSkuManager:
        return BulkSkuManager(self.brand) if self.include_bulk_skus else EmptyBulkSkuManager()

    @cached_property
    def organic_sku_mapping(self) -> dict[SKU_CODE, SKU_CODE]:
        skus = set(self.ingredient_summary)
        for sku in self.ingredient_summary:
            b_sku = self.bulk_sku_manager.get_bulk_sku(sku)
            skus.add(b_sku.bulk_sku_code if b_sku else None)
        skus.discard(None)
        full_mapping = alternative_sku_mapping.get_all_alternative_sku(OrganicSkuModel)
        sku_mapping = {s: full_mapping[s] for s in skus if s in full_mapping}
        sku_mapping.update({alt_sku: orig_sku for orig_sku, alt_sku in sku_mapping.items()})
        return sku_mapping

    @cached_property
    def plan(self) -> dict[str, ProductionNeedResult]:
        return self._get_production_need_ingredients(self.week)

    @cached_property
    def prev_week_row_need(self) -> dict[str, ProductionNeedResult]:
        return self._get_production_need_ingredients(self.week - 1)

    @log_wrapper
    @ApplicationMetrics.functional_methods()
    def _get_production_need_ingredients(self, week: ScmWeek) -> dict[SKU_CODE, ProductionNeedResult]:
        result = production_need.get_production_need(site=self.site, week=week, brand=self.brand)
        return defaultdict(lambda: ProductionNeedResult("", {}), result)

    @cached_property
    def hj_autostore(self) -> dict[SKU_CODE, int]:
        if self.dc_object.is_hj_autostore:
            return hj_autostore_manager.get_hj_autostore(week=self.week, brand=self.brand, site=self.site)
        return {}

    def get_pulls(self, sku_code: SKU_CODE) -> int:
        return safe_sum(self._pulls.get(sku_code, []) + self._pulls.get(self.organic_sku_mapping.get(sku_code), []))

    def get_inventories(self, sku_code: SKU_CODE) -> int:
        return safe_sum(self._puts.get(sku_code, []) + self._puts.get(self.organic_sku_mapping.get(sku_code), []))

    @cached_property
    def weekly_snapshots_raw(self) -> dict[SKU_CODE, Decimal]:
        res = defaultdict(Decimal)
        for item in weekly_snapshot_repo.get_weekly_snapshots(
            market=self.market, bob_code=self.dc_object.bob_code, week=self.week
        ):
            res[item.sku_code] += item.quantity
        return res

    @cached_property
    def weekly_snapshots(self) -> dict[SKU_CODE, Decimal]:
        if not self.dc_object.is_wip_consumption_enabled:
            return self.weekly_snapshots_raw
        res = defaultdict(Decimal, self.weekly_snapshots_raw)
        for sku_code, wip in itertools.chain(
            self.wip_consumption_initial_pull.items(),
            self.wip_consumption_carryover.items(),
            self.wip_consumption_putaway_to_production.items(),
        ):
            res[sku_code] += wip
        return res

    def _get_weekly_snapshot(self, sku_code: str, snapshots: dict[SKU_CODE, Decimal]) -> Decimal | None:
        default_value = Decimal() if snapshots else None
        weekly_snapshot = snapshots.get(sku_code, default_value)
        organic_sku = self.organic_sku_mapping.get(sku_code)
        organic_snapshot = snapshots.get(organic_sku, default_value)

        if weekly_snapshot is None and organic_snapshot is None:
            return None
        return (weekly_snapshot or Decimal()) + (organic_snapshot or Decimal())

    def get_weekly_snapshot(self, sku_code: str) -> Decimal | None:
        return self._get_weekly_snapshot(sku_code, self.weekly_snapshots)

    def get_weekly_snapshot_raw(self, sku_code: str) -> Decimal | None:
        return self._get_weekly_snapshot(sku_code, self.weekly_snapshots_raw)

    @cached_property
    @log_wrapper
    def _pulls(self) -> dict[SKU_CODE, list[int]]:
        res = defaultdict(list)
        for input_item in self._inventory_inputs:
            if input_item.qty < 0:
                res[input_item.sku_code].append(-input_item.qty)
        res.default_factory = None
        return res

    @cached_property
    @log_wrapper
    def _puts(self) -> dict[SKU_CODE, list[int]]:
        res = defaultdict(list)
        for input_item in self._inventory_inputs:
            if input_item.qty > 0:
                res[input_item.sku_code].append(input_item.qty)
        res.default_factory = None
        return res

    @cached_property
    @log_wrapper
    @ApplicationMetrics.functional_methods()
    def _inventory_inputs(self) -> list[PullPutDto]:
        sku_codes = self.sku_codes
        if sku_codes:
            organic_sku_codes = {self.organic_sku_mapping.get(sku) for sku in sku_codes}
            organic_sku_codes.discard(None)
            sku_codes = sku_codes.union(organic_sku_codes)
        return inventory.get_pull_put_items(brand=self.brand, dc=self.site, week=self.week, sku_codes=sku_codes)

    @cached_property
    def forecast(self) -> dict[SKU_CODE, UNITS]:
        return self.__get_forecast(week=self.week)

    @cached_property
    def forecast_next_week(self) -> dict[SKU_CODE, UNITS]:
        return self.__get_forecast(week=self.week + 1)

    @log_wrapper
    @ApplicationMetrics.functional_methods()
    def __get_forecast(self, week) -> dict[SKU_CODE, UNITS]:
        if self.market != MARKET_US:
            return {sku_code: needs.total for sku_code, needs in self._get_production_need_ingredients(week).items()}
        forecast = forecast_manager.get_forecast_manager()
        return forecast.get_forecast_by_sku_code(site=self.site, week=week, brand=self.brand, sku_codes=self.sku_codes)

    @cached_property
    def po_master_view(self) -> dict[SKU_CODE, list[PoMasterViewResult]]:
        return po_master_view.get_po_by_sku((self.week,), self.site, self.brand)

    def get_po_master_view_sku(self, sku_code: str) -> list[PoMasterViewResult]:
        po_master_view_sku = self.po_master_view.get(sku_code, [])
        if alternative_sku := self.organic_sku_mapping.get(sku_code):
            po_master_view_sku = po_master_view_sku + self.po_master_view.get(alternative_sku, [])
        return po_master_view_sku

    def units_to_produce_by_autobagger(self, sku_code: str) -> int:
        return safe_sum(
            mv.quantity
            for mv in self.get_po_master_view_sku(sku_code) or []
            if mv.supplier == self.dc_object.autobagger_supplier
        )

    @cached_property
    @log_wrapper
    def hj_discards(self) -> dict[DiscardType, dict[SKU_CODE, dict[date, list[HjDiscard]]]]:
        hj_discards = highjump.get_hj_discard_by_tran_type_sku_date(week=self.week, brand=self.brand, site=self.site)
        for discard_type, discards in hj_discards.items():
            hj_discards[discard_type] = self._include_org_discards(discards)
        return hj_discards

    @cached_property
    @ApplicationMetrics.functional_methods()
    def non_hj_discard_data(self) -> dict[SKU_CODE, dict[date, list[Discard]]]:
        res = defaultdict(lambda: defaultdict(list))
        for discard_item in discarding.get_discard_data(self.site, self.week, self.brand):
            res[discard_item.sku][discard_item.discarded_datetime.date()].append(discard_item)
        res.default_factory = None
        discards = self._include_org_discards(res)
        logger.debug("Non HJ discard len: %s", len(discards))
        return discards

    def _include_org_discards(
        self, all_discards: dict[SKU_CODE, dict[date, list[Discard | HjDiscard]]]
    ) -> dict[SKU_CODE, dict[date, list[Discard | HjDiscard]]]:
        res = defaultdict(lambda: dict(list))
        for sku in all_discards:
            discards = all_discards.get(sku, {})
            alternative_sku = self.organic_sku_mapping.get(sku)
            alt_discards = all_discards.get(alternative_sku, {})
            res[sku] = {day: discards.get(day, []) + alt_discards.get(day, []) for day in {*discards, *alt_discards}}
            if alternative_sku and alternative_sku not in all_discards:
                res[alternative_sku] = res[sku]
        return res

    def _is_pallet_expiring(self, sku_code: SKU_CODE, expiration_date: date) -> bool:
        expiring_inventory_exclude_days = (
            CA_EXPIRING_INVENTORY_EXCLUDE_DAYS if self.market != MARKET_US else US_EXPIRING_INVENTORY_EXCLUDE_DAYS
        )
        packaged_skus = (
            self.ingredient_summary.get(p_sku) for p_sku in self.bulk_sku_manager.get_packaged_skus(sku_code)
        )
        sku_is_bulk = self.bulk_sku_manager.is_bulk(sku_code)
        sku = (
            self.ingredient_summary.get(sku_code) if not sku_is_bulk else next(iter(filter(None, packaged_skus)), None)
        ) or self.ingredient_summary.get(self.organic_sku_mapping.get(sku_code))
        return (
            self.brand != BRAND_FJ
            and context.get_request_context().market != MARKET_CA
            and expiration_date
            and (not sku or sku.purchasing_category_name != IngredientCategory.PRODUCE)
            and self.today + timedelta(days=expiring_inventory_exclude_days) >= expiration_date
        )

    def _get_non_expiring_pallet_qty(self, sku_code: SKU_CODE, sku_pallet_data: dict[date, int]) -> Decimal:
        pallet_quantity = Decimal()
        for _expiration_date, _qty in sku_pallet_data.items():
            if not self._is_pallet_expiring(sku_code, _expiration_date):
                pallet_quantity += core_utils.ceil_subone_decimal(Decimal(_qty))
        return pallet_quantity

    def get_sku_pallet_qty(self, sku_code: str) -> Decimal:
        sku_pallet_quantity = self._get_non_expiring_pallet_qty(sku_code, self._sku_pallets.get(sku_code, {}))
        if alternative_sku := self.organic_sku_mapping.get(sku_code):
            sku_pallet_quantity += self._get_non_expiring_pallet_qty(
                alternative_sku, self._sku_pallets.get(alternative_sku, {})
            )
        return sku_pallet_quantity

    @cached_property
    def _sku_pallets(self) -> dict[SKU_CODE, dict[date, Decimal]]:
        # TODO: use UnifiedInventoryManager
        if self.dc_object.inventory_type.is_high_jump:
            return self._hj_sku_pallet_quantity
        if self.dc_object.inventory_type.is_cycle_count:
            return self._cycle_counts
        if self.dc_object.inventory_type.is_in_unified_inventory:
            return self._unified_inventory
        if self.dc_object.inventory_type.is_wmsl:
            return self._wmsl_inventory
        return {}

    @cached_property
    @log_wrapper
    def _hj_sku_pallet_quantity(self) -> dict[SKU_CODE, dict[date, Decimal]]:
        res = defaultdict(lambda: defaultdict(Decimal))
        sku_pallet = highjump.get_sku_pallet_quantity(brand=self.brand, dc=self.site, week=self.min_inventory_week)
        for item in sku_pallet:
            res[item.sku_code][item.expiration_date] += item.pallet_quantity
        return res

    @cached_property
    def _cycle_counts(self) -> dict[SKU_CODE, dict[date, Decimal]]:
        week_cycle_counts = cycle_counts.get_ingredient_depletion_cycle_counts(
            dc_object=self.dc_object,
            date_from=self.min_inventory_week.get_first_day(self.week_config),
            date_to=self.min_inventory_week.get_last_production_day(self.week_config),
        )
        return {sku_code: data[max(data)] for sku_code, data in week_cycle_counts.items()}

    @cached_property
    def _wmsl_snapshots(self) -> dict[WmslLocationType, dict[SKU_CODE, dict[date, dict[date, Decimal]]]]:
        return inv_snapshots.get_ingredient_depletion_snapshots(
            bob_code=self.dc_object.bob_code,
            date_from=self.min_inventory_week.get_first_day(self.week_config),
            date_to=self.min_inventory_week.get_last_production_day(self.week_config),
        )

    @cached_property
    def _wmsl_inventory(self) -> dict[SKU_CODE, dict[date, Decimal]]:
        snapshots = self._wmsl_snapshots.get(WmslLocationType.STORAGE, {})
        return {sku_code: data[max(data)] for sku_code, data in snapshots.items()}

    @cached_property
    def _unified_inventory(self) -> dict[SKU_CODE, dict[date, Decimal]]:
        records = imt_inventory.get_ingredient_inventory_records(
            bob_code=self.dc_object.bob_code,
            inventory_type=self.dc_object.inventory_type,
            date_from=self.min_inventory_week.get_first_day(self.week_config),
            date_to=self.min_inventory_week.get_last_production_day(self.week_config),
        )
        latest_snapshot = max(day for skus in records.values() for day in skus.keys())
        return {sku_code: data[latest_snapshot] for sku_code, data in records.items() if latest_snapshot in data}

    @cached_property
    def units_on_prod_floor(self) -> dict[SKU_CODE, int]:
        if self.dc_object.inventory_type.is_wmsl:
            snapshots = self._wmsl_snapshots.get(WmslLocationType.PRODUCTION, {})
            return {sku_code: sum(data[max(data)].values()) for sku_code, data in snapshots.items()}
        return highjump.get_hj_units_on_floor(self.dc_object, self.sku_codes)

    @cached_property
    def allowed_buffers(self) -> dict[SKU_CODE, Decimal]:
        return allowed_produce_buffer.get_allowed_produce_buffers_by_sku_code(dc_obj=self.dc_object, week=self.week)

    @cached_property
    def _hj_wip_consumption(self) -> dict[HjWipConsumptionType, dict[SKU_CODE, Decimal]]:
        if self.week <= ScmWeek.current_week(self.week_config):
            return highjump.get_hj_wip_consumption(self.dc_object.high_jump_name, self.week)
        return {}

    @cached_property
    def wip_consumption_carryover(self) -> dict[SKU_CODE, Decimal]:
        return self._hj_wip_consumption.get(HjWipConsumptionType.CARRYOVER, {})

    @cached_property
    def wip_consumption_initial_pull(self) -> dict[SKU_CODE, Decimal]:
        return self._hj_wip_consumption.get(HjWipConsumptionType.INITIAL_PULL, {})

    @cached_property
    def wip_consumption_putaway_to_production(self) -> dict[SKU_CODE, Decimal]:
        return self._hj_wip_consumption.get(HjWipConsumptionType.PUTAWAY_TO_PRODUCTION, {})

    @cached_property
    def actual_hj_discards(self) -> dict[DiscardType, dict[SKU_CODE, dict[date, list[HjDiscard]]]]:
        if self.brand == BRAND_FJ:
            return self.extended_hj_discards
        return self.hj_discards

    @cached_property
    def extended_hj_discards(self) -> dict[DiscardType, dict[SKU_CODE, dict[date, list[HjDiscard]]]]:
        discards = {}
        for discard_type, discards_by_sku in self.hj_discards.items():
            discards[discard_type] = self._process_discard_by_type(discards_by_sku)
        return discards

    @cached_property
    def manufactured_sku_breakdown(self) -> ManufacturedSkuBreakdown:
        return ManufacturedSkuBreakdown()

    def _process_discard_by_type(
        self, discards_by_sku: dict[SKU_CODE, dict[date, list[HjDiscard]]]
    ) -> dict[SKU_CODE, dict[date, list[HjDiscard]]]:
        result = defaultdict(lambda: defaultdict(list))
        for sku_code, discards_by_day in discards_by_sku.items():
            transformed_sku_code = sku_code.replace("_", "-")
            if transformed_sku_code not in self.manufactured_sku_breakdown.manufactured_sku_uuid_by_code:
                for day, discards in discards_by_day.items():
                    result[sku_code][day].extend(discards)
            else:
                for day, discards in discards_by_day.items():
                    aggregated_discard = self._aggregate_manufactured_discards(discards)
                    manufactured_discards = self.manufactured_sku_breakdown.get_breakdown(transformed_sku_code)
                    for sub_sku_code, ingredient_quantity in manufactured_discards.items():
                        result[sub_sku_code][day].append(
                            HjDiscard(
                                sub_sku_code,
                                aggregated_discard.wh_id,
                                aggregated_discard.tran_type,
                                aggregated_discard.discard_date,
                                ingredient_quantity * aggregated_discard.quantity,
                            )
                        )
        return result

    @staticmethod
    def _aggregate_manufactured_discards(discards: Collection[HjDiscard]) -> HjDiscard:
        base_discard = next(iter(discards))
        return HjDiscard(
            sku_code=base_discard.sku_code,
            wh_id=base_discard.wh_id,
            tran_type=base_discard.tran_type,
            discard_date=base_discard.discard_date,
            quantity=sum(item.quantity for item in discards),
        )

    def get_units_in_house(self, sku_code: str):
        units = self.get_sku_pallet_qty(sku_code)
        return (
            self.previous_week_context.get_sku_pallet_qty(sku_code)
            if not units and self.week == self.current_week + 1
            else units
        )

    def sum_quantity_with_status(self, status_filter: Callable[[str], bool], sku_code: str) -> Decimal:
        po_master_view_sku = self.get_po_master_view_sku(sku_code) or []
        return safe_sum(mv.quantity for mv in po_master_view_sku or [] if status_filter(mv.po_status))

    def awaiting_delivery(self, sku_code: str) -> Decimal:
        awaiting_delivery = self.sum_quantity_with_status(lambda status: status in AWAITING_DELIVERY_STATUSES, sku_code)

        if self.dc_object.receiving_type.is_high_jump:
            awaiting_delivery += self.units_to_produce_by_autobagger(sku_code)

        return awaiting_delivery

    @cached_property
    def staged_inventory(self) -> defaultdict[SKU_CODE, Decimal]:
        staged_inv = staged_inventory.get_staged_inventory(self.week, self.site)
        res = defaultdict(Decimal)
        for item in staged_inv:
            if item.sub_recipe_id not in self.manufactured_sku_breakdown.manufactured_sku_uuid_by_code:
                res[item.sub_recipe_id] += item.quantity
            else:
                breakdown = self.manufactured_sku_breakdown.get_breakdown(item.sub_recipe_id)
                for sku_code, quantity in breakdown.items():
                    res[sku_code] += quantity * item.quantity
        return res

    @cached_property
    def work_order(self) -> dict[SKU_CODE, Decimal]:
        res = defaultdict(Decimal)
        for item in work_order.get_work_orders(self.dc_object.high_jump_name, self.week):
            res[item.sku_code] += item.quantity
        return res
