from collections import defaultdict
from decimal import Decimal
from functools import cached_property

from procurement.core.config_utils import killswitch
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.depletion.daily import DailyDepletion
from procurement.managers.depletion.result import IngredientDepletionResult
from procurement.managers.depletion.weekly import DepletionWeeklyItem

from .bulk import SiteBulkValues
from .core import IngredientDepletionContext, IngredientSummary
from .daily import SiteDailyDepletion
from .result import SiteIngredientDepletionResult
from .weekly import SiteDepletionWeeklyItem


class BulkMasterDailyDepletion(SiteDailyDepletion):
    def __init__(
        self,
        context: IngredientDepletionContext,
        sku_code: str,
        units_to_produce_by_autobagger: int,
        prev_eow_inventory: UNITS,
        packed_dailies: list[DailyDepletion],
    ):
        super().__init__(context, sku_code, units_to_produce_by_autobagger, prev_eow_inventory)
        self.packed_dailies = AggregatedData(packed_dailies)

    @cached_property
    def production_needs(self) -> list[UNITS]:
        return (
            self.packed_dailies.sum_array(lambda daily: [it * daily.pick_conversions for it in daily.production_needs])
            or []
        )

    @cached_property
    def live_row_need(self) -> UNITS:
        return self.packed_dailies.sum_if_present(lambda it: it.live_row_need * it.pick_conversions)


class BulkMasterWeeklyDepletion(SiteDepletionWeeklyItem):
    def __init__(  # pylint: disable=too-many-arguments
        self,
        context: IngredientDepletionContext,
        ingredient: IngredientSummary,
        units_to_produce_by_autobagger: int,
        start_of_day_inv: int,
        prev_eow_inventory: Decimal,
        packed_weeklies: list[DepletionWeeklyItem],
    ):
        super().__init__(context, ingredient, units_to_produce_by_autobagger, start_of_day_inv, prev_eow_inventory)
        self.packed_weeklies = AggregatedData(packed_weeklies)

    @cached_property
    def units_needed(self) -> UNITS | None:
        return self.packed_weeklies.sum_if_present(
            lambda item: (item.units_needed or Decimal())
            * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def row_need(self) -> int:
        return self.packed_weeklies.sum_items(
            lambda item: (item.row_need or Decimal()) * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def prev_week_row_need(self) -> int:
        return self.packed_weeklies.sum_items(
            lambda item: (item.prev_week_row_need or Decimal())
            * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def next_week_forecast(self) -> Decimal | None:
        return self.packed_weeklies.sum_items(
            lambda item: (item.next_week_forecast or Decimal())
            * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )


class BulkMasterDepletionResult(SiteIngredientDepletionResult):
    def __init__(
        self,
        ingredient: IngredientSummary,
        context: IngredientDepletionContext,
        packed_items: list[SiteIngredientDepletionResult],
    ):
        super().__init__(ingredient, context)
        self._packaged_items = AggregatedData(packed_items)

    @staticmethod
    def build_results(context: IngredientDepletionContext) -> list[SiteIngredientDepletionResult]:
        result = []
        ingredient_by_bulk_sku = defaultdict(list)
        for ingredient in context.dashboard_skus.values():
            site_item = SiteIngredientDepletionResult(ingredient=ingredient, context=context)
            bulk_sku = context.bulk_sku_manager.get_bulk_sku(site_item.sku_code)
            master_bulk_skus = {
                it.bulk_sku_code for it in context.bulk_sku_manager.bulk_skus_mapping.values() if it.is_master_sku
            }

            if killswitch.show_bulk_on_ingredient_depletion and bulk_sku and bulk_sku.is_master_sku:
                ingredient_by_bulk_sku[bulk_sku.bulk_sku_code].append(site_item)
            elif ingredient.sku_code not in master_bulk_skus or not killswitch.show_bulk_on_ingredient_depletion:
                result.append(site_item)

        for bulk_sku, packed_skus in ingredient_by_bulk_sku.items():
            result.append(
                BulkMasterDepletionResult(
                    ingredient=context.ingredient_summary[bulk_sku], context=context, packed_items=packed_skus
                )
            )
        return result

    @cached_property
    def forecast(self) -> UNITS | None:
        return self._packaged_items.sum_if_present(
            lambda item: (item.forecast or 0) * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def plan(self) -> int:
        return self._packaged_items.sum_items(
            lambda item: (item.plan or 0) * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def live_row_need(self) -> UNITS:
        return self._packaged_items.sum_if_present(
            lambda item: item.live_row_need * self.context.bulk_sku_manager.get_pick_conversion(item.sku_code)
        )

    @cached_property
    def row_forecast(self) -> None:
        return None

    @cached_property
    def planned_production(self) -> None:
        return None

    @cached_property
    def hj_unit(self) -> None:
        return None

    @cached_property
    def units_to_produce_by_autobagger(self) -> int:
        return self._packaged_items.sum_items(lambda item: item.units_to_produce_by_autobagger)

    @cached_property
    def daily(self) -> BulkMasterDailyDepletion:
        return BulkMasterDailyDepletion(
            context=self.context,
            sku_code=self.sku_code,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
            packed_dailies=[item.daily for item in self._packaged_items.raw],
        )

    def _calculate_eow_inventory(self, context: IngredientDepletionContext) -> Decimal:
        return Decimal()

    @cached_property
    def weekly_overview(self) -> BulkMasterWeeklyDepletion:
        return BulkMasterWeeklyDepletion(
            context=self.context,
            ingredient=self.ingredient,
            start_of_day_inv=self.daily.by_day[self._critical_delivery_date].on_hand,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
            packed_weeklies=[item.weekly_overview for item in self._packaged_items.raw],
        )

    @cached_property
    def bulk_values(self) -> SiteBulkValues:
        return self._packaged_items.any_item(lambda item: item.bulk_values)

    @cached_property
    def packaged_depl_items(self) -> list[IngredientDepletionResult]:
        return self._packaged_items.raw
