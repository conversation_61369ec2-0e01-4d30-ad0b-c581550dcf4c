import logging
from collections import defaultdict

from procurement.core.dates import ScmWeek
from procurement.core.log import log_wrapper
from procurement.core.metrics import ApplicationMetrics
from procurement.managers.admin import dc_admin
from procurement.managers.depletion.result import AggregatedIngredientDepletionResult

from .core import IngredientDepletionContext
from .result import SiteIngredientDepletionResult

logger = logging.getLogger(__name__)


@log_wrapper
@ApplicationMetrics.functional_methods()
def get_aggregated_ingredient_depletion(
    week: ScmWeek,
    aggregated_site: str,
    base_depletion_type: type[SiteIngredientDepletionResult] = SiteIngredientDepletionResult,
) -> list[AggregatedIngredientDepletionResult]:
    sites = {dc.bob_code: dc for dc in dc_admin.get_all_enabled_multi_brand_sites(week=week).get(aggregated_site, [])}
    for dc in dc_admin.get_all_enabled_consolidated_sites(week=week).get(aggregated_site, []):
        sites[dc.bob_code] = dc

    depletion_by_sku = defaultdict(list)
    for site in sites.values():
        context = IngredientDepletionContext(
            week=week, site=site.sheet_name, brand=site.brand, include_bulk_skus=True, include_no_demand_skus=True
        )
        for item in base_depletion_type.build_results(context=context):
            depletion_by_sku[item.sku_code].append(item)
    aggregated = (AggregatedIngredientDepletionResult(items) for items in depletion_by_sku.values())
    return [it for it in aggregated if it.forecast or it.plan]
