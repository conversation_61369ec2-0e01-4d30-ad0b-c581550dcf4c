from __future__ import annotations

from decimal import Decimal
from functools import cached_property

from procurement.core.typing import UNITS
from procurement.managers.depletion.buffer_analysis import BufferAnalysis
from procurement.managers.imt.models import PoMasterViewResult

from .core import IngredientDepletionContext, IngredientSummary


class SiteBufferAnalysis(BufferAnalysis):
    def __init__(
        self,
        context: IngredientDepletionContext,
        ingredient: IngredientSummary,
        units_needed: UNITS | None,
        buffer_quantity: int | None,
    ):
        self._context = context
        self._ingredient = ingredient
        self._po_sku_items = context.get_po_master_view_sku(ingredient.sku_code)
        self._units_needed = units_needed
        self._buffer_quantity = buffer_quantity

    @property
    def ingredient(self) -> IngredientSummary:
        return self._ingredient

    @property
    def po_sku_items(self) -> list[PoMasterViewResult]:
        return self._po_sku_items

    @property
    def units_needed(self) -> UNITS | None:
        return self._units_needed

    @property
    def buffer_quantity(self) -> int | None:
        return self._buffer_quantity

    @property
    def autobagger_supplier(self) -> str | None:
        return self._context.dc_object.autobagger_supplier

    @cached_property
    def allowed_buffer(self) -> Decimal | None:
        return self._context.allowed_buffers.get(self.ingredient.sku_code)
