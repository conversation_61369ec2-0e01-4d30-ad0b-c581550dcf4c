from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from procurement.constants.hellofresh_constant import BRAND_FJ, UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.core.typing import MEAL_NUMBER, SKU_CODE, UNITS
from procurement.data.dto.inventory.ingredient import Meal
from procurement.data.models.inventory.remake_tool import RemakeToolModel
from procurement.repository.inventory import ingredient as ingredient_repo
from procurement.repository.inventory import remake_tool as remake_repo

from . import remake_ingredient_depletion
from .remake_ingredient_depletion import RemakeToolIngredientDepletionResult

PICKS_2P = "picks_2p"
PICKS_4P = "picks_4p"
PICKS_6P = "picks_6p"

OZ_TO_LBS_COEFFICIENT = Decimal("0.0625")


class RemakeToolSlot(NamedTuple):
    picks_2p: int
    picks_4p: int
    picks_6p: int


def get_remake_data(week: ScmWeek, site: str, brand: str) -> dict[MEAL_NUMBER, RemakeToolSlot]:
    mealkits = ingredient_repo.get_meals(week, site, brand)
    return _get_remake_units_data(week, site, mealkits)


def get_remake_depletion(
    week: ScmWeek, site: str, brand: str, remake_tool_data: dict[MEAL_NUMBER, RemakeToolSlot], day: str
) -> list[RemakeToolIngredientDepletionResult]:
    mealkits = ingredient_repo.get_meals(week, site, brand)
    remake_sku_units = _get_sku_units(brand, mealkits, remake_tool_data)
    return remake_ingredient_depletion.get_remake_ingredient_depletion(
        week, site, brand, remake_sku_units, remake_day=day
    )


def _get_remake_units_data(week: ScmWeek, site: str, mealkits: list[Meal]) -> dict[MEAL_NUMBER, RemakeToolSlot]:
    rmt_data = remake_repo.get_remake_tool_data(week, site, meals=frozenset(mealkit.slot for mealkit in mealkits))
    return defaultdict(
        lambda: RemakeToolSlot(0, 0, 0),
        {rmt.meal: RemakeToolSlot(rmt.picks_2p, rmt.picks_4p, rmt.picks_6p) for rmt in rmt_data},
    )


def _get_sku_units(
    brand: str, mealkits: list[Meal], rmt_slots: dict[MEAL_NUMBER, RemakeToolSlot]
) -> dict[SKU_CODE, UNITS]:
    units_calculation = _factor_units_calculation if brand == BRAND_FJ else _core_units_calculation

    sku_units = defaultdict(int)
    for mealkit in mealkits:
        remake_tool_slot = rmt_slots[mealkit.slot]
        sku_units[mealkit.sku_code] += units_calculation(remake_tool_slot, mealkit)

    return sku_units


def _core_units_calculation(rmt_slot: RemakeToolSlot, meal: Meal) -> UNITS:
    return (
        (rmt_slot.picks_2p * meal.picks_2p) + (rmt_slot.picks_4p * meal.picks_4p) + (rmt_slot.picks_6p * meal.picks_6p)
    )


def _factor_units_calculation(rmt_slot: RemakeToolSlot, meal: Meal) -> UNITS:
    coefficient = OZ_TO_LBS_COEFFICIENT if meal.unit_of_measure == UnitOfMeasure.OUNCE else 1
    return coefficient * rmt_slot.picks_2p * meal.weight_amount


def insert_data(input_dict: dict[str], week: ScmWeek, site: str) -> dict[MEAL_NUMBER, RemakeToolSlot]:
    upsert_rt = []
    rmt_slots = defaultdict(lambda: RemakeToolSlot(0, 0, 0))

    for meal, picks in input_dict.items():
        picks_2p = picks[PICKS_2P]
        picks_4p = picks[PICKS_4P]
        picks_6p = picks.get(PICKS_6P, 0)
        upsert_rt.append(
            {
                RemakeToolModel.meal: meal,
                RemakeToolModel.picks_2p: picks_2p,
                RemakeToolModel.picks_4p: picks_4p,
                RemakeToolModel.picks_6p: picks_6p,
                RemakeToolModel.week: str(week),
                RemakeToolModel.dc: site,
            }
        )
        rmt_slots[meal] = RemakeToolSlot(picks_2p, picks_4p, picks_6p)

    remake_repo.upsert_remake_tool_data(upsert_rt)
    return rmt_slots
