from __future__ import annotations

from decimal import Decimal
from functools import cached_property

from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.managers.depletion import core
from procurement.managers.imt.ingredients_depletion.core import IngredientDepletionContext
from procurement.managers.imt.ingredients_depletion.daily import SiteDailyDepletion
from procurement.managers.imt.ingredients_depletion.result import SiteIngredientDepletionResult
from procurement.managers.imt.ingredients_depletion.weekly import SiteDepletionWeeklyItem


class RemakeToolDailyDepletion(SiteDailyDepletion):
    @cached_property
    def total_production_need(self) -> list[int]:
        remake_units = self.context.remake_sku_units.get(self.sku_code, 0)
        prev = 0
        res = []
        remake_tool_days = [
            item.weekday_title for item in core.get_weekdays(self.context.week, self.context.week_config)
        ]

        for index, elem in enumerate(self.production_needs):
            current = elem + prev
            if index >= 2 and remake_tool_days[index] == self.context.remake_day:
                current_remake_units = remake_units
            else:
                current_remake_units = 0

            res.append(current + current_remake_units)
            prev = current + current_remake_units
        return res


class RemakeToolWeeklyDepletionItem(SiteDepletionWeeklyItem):
    @cached_property
    def remake_units(self) -> int:
        return round(self.context.remake_sku_units.get(self.sku_code, 0))

    @cached_property
    def row_need(self) -> int:
        return super().row_need + self.remake_units

    @cached_property
    def total_on_hand_minus_prod_needs(self) -> Decimal:
        return super().total_on_hand_minus_prod_needs + self.remake_units

    @cached_property
    def units_needed_plus_remake(self) -> Decimal:
        return (self.units_needed or Decimal()) + self.remake_units

    @cached_property
    def buffer_quantity(self) -> Decimal | None:
        if super().buffer_quantity is None:
            return None
        return super().buffer_quantity - self.remake_units

    @cached_property
    def buffer_percent(self) -> Decimal | None:
        return (
            round(Decimal(self.buffer_quantity) / self.units_needed_plus_remake, 4)
            if self.units_needed_plus_remake and self.buffer_quantity is not None
            else None
        )


class RemakeToolIngredientDepletionResult(SiteIngredientDepletionResult):
    @cached_property
    def daily(self) -> RemakeToolDailyDepletion:
        return RemakeToolDailyDepletion(
            context=self.context,
            sku_code=self.ingredient.sku_code,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
        )

    @cached_property
    def weekly_overview(self) -> RemakeToolWeeklyDepletionItem:
        return RemakeToolWeeklyDepletionItem(
            context=self.context,
            ingredient=self.ingredient,
            start_of_day_inv=self.daily.by_day[self._critical_delivery_date].on_hand,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
        )

    @staticmethod
    def build_results(context: IngredientDepletionContext) -> list[RemakeToolIngredientDepletionResult]:
        return [
            RemakeToolIngredientDepletionResult(ingredient=ingredient, context=context)
            for ingredient in context.dashboard_skus.values()
        ]


def get_remake_ingredient_depletion(
    week: ScmWeek, site: str, brand: str, remake_sku_units: dict[SKU_CODE, int] | None, remake_day: str
) -> list[RemakeToolIngredientDepletionResult]:
    context = IngredientDepletionContext(
        week=week, site=site, brand=brand, remake_sku_units=remake_sku_units, remake_day=remake_day
    )
    return RemakeToolIngredientDepletionResult.build_results(context=context)
