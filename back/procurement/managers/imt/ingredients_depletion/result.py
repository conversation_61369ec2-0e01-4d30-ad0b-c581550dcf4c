from __future__ import annotations

from datetime import date
from functools import cached_property

from procurement.constants.hellofresh_constant import MARKET_CA, IngredientCategory, UnitOfMeasure
from procurement.core import utils
from procurement.core.dates.weeks import ScmWeek, ScmWeekConfig
from procurement.core.typing import UNITS
from procurement.managers.aggregation import AggregatedData
from procurement.managers.depletion.result import IngredientDepletionResult

from .buffer_analysis import SiteBufferAnalysis
from .bulk import SiteBulkValues
from .core import IngredientDepletionContext, IngredientSummary
from .daily import SiteDailyDepletion
from .weekly import SiteDepletionWeeklyItem
from .work_order import SiteDWorkOrder


class SiteIngredientDepletionResult(IngredientDepletionResult):
    def __init__(self, ingredient: IngredientSummary, context: IngredientDepletionContext):
        self._packaged_items = AggregatedData([])
        self.context = context
        self.ingredient = ingredient
        self.production_need_ingredients_sku = self.context.plan[self.sku_code]
        self.po_master_view_sku = self.context.get_po_master_view_sku(self.sku_code)

    @staticmethod
    def build_results(context: IngredientDepletionContext) -> list[SiteIngredientDepletionResult]:
        return [
            SiteIngredientDepletionResult(ingredient=ingredient, context=context)
            for ingredient in context.dashboard_skus.values()
        ]

    @cached_property
    def brands(self) -> set[str]:
        return {self.context.brand}

    @cached_property
    def sites(self) -> set[str]:
        return {self.context.site}

    @cached_property
    def week(self) -> ScmWeek:
        return self.context.week

    @cached_property
    def week_config(self) -> ScmWeekConfig:
        return self.context.week_config

    @cached_property
    def sku_code(self) -> str:
        return self.ingredient.sku_code

    @cached_property
    def sku_name(self) -> str:
        return self.ingredient.sku_name

    @cached_property
    def unit_of_measure(self) -> UnitOfMeasure:
        return self.ingredient.unit_of_measure

    @cached_property
    def category(self) -> str:
        return self.ingredient.purchasing_category_name

    @cached_property
    def commodity_group(self) -> str:
        return self.ingredient.commodity_group_name

    @cached_property
    def buyer(self) -> str:
        return self.context.buyers.get(self.sku_code)

    @cached_property
    def impacted_recipes(self) -> set[str]:
        sorted_impacted_recipes = sorted(
            self.ingredient.meal_numbers,
            key=lambda item: (0, int(item)) if item.isdigit() else (1, item),
        )
        return {"-".join(sorted_impacted_recipes)} if sorted_impacted_recipes else set()

    @cached_property
    def forecast(self) -> UNITS | None:
        return self.context.forecast.get(self.ingredient.sku_code)

    @cached_property
    def plan(self) -> int:
        return utils.ceil_subone_integer(self.production_need_ingredients_sku.total)

    @cached_property
    def live_row_need(self) -> UNITS:
        return self.production_need_ingredients_sku.live_row_need

    @cached_property
    def row_forecast(self) -> int:
        return utils.ceil_subone_integer(self.production_need_ingredients_sku.row_forecast)

    @cached_property
    def planned_production(self) -> int:
        return utils.ceil_subone_integer(self.production_need_ingredients_sku.planned_production)

    @cached_property
    def hj_unit(self) -> UnitOfMeasure | None:
        return next((record.hj_unit for record in self.po_master_view_sku if record.hj_unit), None)

    @cached_property
    def units_to_produce_by_autobagger(self) -> int:
        return self.context.units_to_produce_by_autobagger(self.sku_code)

    @cached_property
    def daily(self) -> SiteDailyDepletion:
        return SiteDailyDepletion(
            context=self.context,
            sku_code=self.sku_code,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
        )

    @cached_property
    def prev_eow_inventory(self) -> UNITS:
        if self.context.market != MARKET_CA or self.category == IngredientCategory.PRODUCE:
            return 0

        result = self._calculate_eow_inventory(self.context.previous_week_context)
        if self.context.include_no_demand_skus:
            return result
        return max(0, result)

    def _calculate_eow_inventory(self, context: IngredientDepletionContext) -> UNITS | None:
        if not context.dc_object:
            return 0
        if context.is_old_week or context.weekly_snapshots:
            prev_week_eow = 0
        else:
            prev_week_eow = self._calculate_eow_inventory(context.previous_week_context)

        return SiteDailyDepletion(
            context=context,
            sku_code=self.sku_code,
            units_to_produce_by_autobagger=self.context.units_to_produce_by_autobagger(self.sku_code),
            prev_eow_inventory=prev_week_eow,
        ).end_of_week_inventory

    @cached_property
    def critical_delivery_status(self) -> str:
        return self.daily.by_day[self._critical_delivery_date].status_in_week

    @cached_property
    def _critical_delivery_date(self) -> date:
        current_week = ScmWeek.current_week(self.week_config)

        if self.context.week > current_week:
            critical_delivery_date = self.context.prod_week_start
        elif self.context.week < current_week:
            critical_delivery_date = self.context.week.get_first_day(self.week_config)
        else:
            critical_delivery_date = self.context.today

        return critical_delivery_date

    @cached_property
    def weekly_overview(self) -> SiteDepletionWeeklyItem:
        return SiteDepletionWeeklyItem(
            context=self.context,
            ingredient=self.ingredient,
            start_of_day_inv=self.daily.by_day[self._critical_delivery_date].on_hand,
            units_to_produce_by_autobagger=self.units_to_produce_by_autobagger,
            prev_eow_inventory=self.prev_eow_inventory,
        )

    @cached_property
    def buffer_analysis(self) -> SiteBufferAnalysis:
        return SiteBufferAnalysis(
            context=self.context,
            ingredient=self.ingredient,
            units_needed=self.weekly_overview.units_needed,
            buffer_quantity=self.weekly_overview.buffer_quantity,
        )

    @cached_property
    def bulk_values(self) -> SiteBulkValues:
        return SiteBulkValues(context=self.context, sku_code=self.sku_code)

    @cached_property
    def work_order_section(self) -> SiteDWorkOrder:
        return SiteDWorkOrder(context=self.context, sku_code=self.sku_code)
