from decimal import Decimal
from functools import cached_property

from procurement.core.typing import SKU_CODE
from procurement.data.models.highjump.highjump import DiscardType
from procurement.managers.depletion.bulk import BulkValues
from procurement.managers.imt.models import PoMasterViewResult

from . import weekly
from .core import IngredientDepletionContext


class SiteBulkValues(BulkValues):
    def __init__(self, context: IngredientDepletionContext, sku_code: str):
        self.context = context
        self.sku_code = sku_code

    @cached_property
    def warehouse_name(self) -> str:
        return self.context.dc_object.high_jump_name

    @cached_property
    def po_master_view_bulk_sku(self) -> list[PoMasterViewResult] | None:
        if not self.bulk_sku_code:
            return None
        return self.context.get_po_master_view_sku(self.bulk_sku_code) or []

    @cached_property
    def bulk_units_in_hj(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return self.context.get_sku_pallet_qty(self.bulk_sku_code)

    @cached_property
    def bulk_sku_code(self) -> SKU_CODE | None:
        b_sku = self.context.bulk_sku_manager.get_bulk_sku(self.sku_code)
        return b_sku.bulk_sku_code if b_sku else None

    @cached_property
    def bulk_units_ordered(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return weekly.get_units_ordered(self.po_master_view_bulk_sku)

    @cached_property
    def bulk_units_received(self) -> Decimal | None:
        if not self.bulk_sku_code:
            return None
        return weekly.get_total_quantity_received(self.po_master_view_bulk_sku)

    @cached_property
    def bulk_discards(self) -> int | None:
        if not self.bulk_sku_code:
            return None
        discard_bulk_sku = self.context.actual_hj_discards.get(DiscardType.DISCARD, {}).get(self.bulk_sku_code, {})
        return weekly.get_discards(discard_bulk_sku)
