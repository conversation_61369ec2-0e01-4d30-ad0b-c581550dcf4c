from __future__ import annotations

from decimal import Decimal
from functools import cached_property

from procurement.managers.depletion.work_order import DepletionWorkOrderItem
from procurement.managers.imt.ingredients_depletion import weekly

from .core import IngredientDepletionContext


class SiteDWorkOrder(DepletionWorkOrderItem):
    def __init__(
        self,
        context: IngredientDepletionContext,
        sku_code: str,
    ):
        self.context = context
        self._sku_code = sku_code

    @cached_property
    def sku_code(self) -> str:
        return self._sku_code

    @cached_property
    def _units_received(self):
        po_master_view_sku = self.context.get_po_master_view_sku(self.sku_code) or []
        return weekly.get_total_quantity_received(item for item in po_master_view_sku)

    @cached_property
    def _units_in_house(self):
        return self.context.get_units_in_house(self.sku_code)

    @cached_property
    def required_lbs(self) -> Decimal:
        return self.context.work_order.get(self.sku_code, Decimal())

    @cached_property
    def staged_lbs(self) -> Decimal:
        return self.context.staged_inventory.get(self.sku_code, Decimal())

    @cached_property
    def buffered_forecast(self) -> Decimal:
        return Decimal("1.05") * self.context.forecast.get(self.sku_code, Decimal())

    @cached_property
    def wo_row_need(self) -> Decimal:
        if self.buffered_forecast > self.required_lbs:
            return self.buffered_forecast - self.staged_lbs
        return self.required_lbs - self.staged_lbs

    @cached_property
    def staged_plus_hj_minus_received(self) -> Decimal:
        return self.staged_lbs + self._units_in_house - self._units_received

    @cached_property
    def hj_minus_wo_row_need(self) -> Decimal:
        return self._units_in_house - self.wo_row_need

    @cached_property
    def hj_plus_to_be_delivered_minus_wo_row_need(self) -> Decimal:
        return self._units_in_house + self.context.awaiting_delivery(self.sku_code) - self.wo_row_need
