import itertools
from collections import defaultdict
from datetime import datetime
from functools import cached_property
from typing import NamedTuple

from procurement.constants.hellofresh_constant import BRAND_GC
from procurement.core.dates import ScmWeek
from procurement.core.typing import PACKAGED_SKU_CODE, SKU_CODE, UNITS
from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.dto.inventory.bulk_skus import BulkSku
from procurement.data.models.highjump.highjump import DiscardType
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.managers.admin import dc_admin
from procurement.managers.distribution_center import highjump
from procurement.managers.forms import discarding
from procurement.managers.imt import bulk_sku
from procurement.managers.sku import alternative_sku_mapping


class DiscardItem(NamedTuple):
    site: str
    sku_code: str
    discard_date: datetime
    quantity: UNITS
    tran_type: DiscardType | None = None


class DiscardsContext:
    def __init__(self, brand: str, site: str, week: ScmWeek, sku_code: str, tran_type: str = None):
        self.brand = brand
        self.site = site
        self.week = week
        self.sku_code = sku_code
        self.tran_type = tran_type

    @cached_property
    def non_hj_discard_data(self) -> list[DiscardItem]:
        discards = discarding.get_discard_data(self.site, self.week, self.brand, sku_code=self.sku_code)
        if alternative_sku := self.organic_sku_mapping.get(self.sku_code):
            discards.extend(discarding.get_discard_data(self.site, self.week, self.brand, sku_code=alternative_sku))
        return [
            DiscardItem(site=self.site, sku_code=item.sku, discard_date=item.discarded_datetime, quantity=item.quantity)
            for item in discards
        ]

    @cached_property
    def wip_hj_discards(self) -> list[DiscardItem]:
        return [
            DiscardItem(
                site=self.site,
                sku_code=item.sku_code,
                discard_date=item.discard_date,
                quantity=item.quantity,
                tran_type=item.tran_type,
            )
            for item in itertools.chain(
                self.hj_discards.get(DiscardType.WIP_DISCARD, []),
                self.hj_discards.get(DiscardType.BULK_DISCARD, []),
                self.hj_discards.get(DiscardType.AUTOBUGGER_DISCARD, []),
            )
        ]

    @cached_property
    def hj_discards(self) -> dict[DiscardType, list[HjDiscard]]:
        hj_discards = defaultdict(
            list,
            highjump.get_hj_discards_by_tran_type(
                week=self.week, brand=self.brand, site=self.site, sku_code=self.sku_code, tran_type=self.tran_type
            ),
        )
        if alternative_sku := self.organic_sku_mapping.get(self.sku_code):
            for discard_type, discards in highjump.get_hj_discards_by_tran_type(
                week=self.week, brand=self.brand, site=self.site, sku_code=alternative_sku, tran_type=self.tran_type
            ).items():
                hj_discards[discard_type].extend(discards)
        return hj_discards

    @cached_property
    def organic_sku_mapping(self) -> dict[SKU_CODE, SKU_CODE]:
        skus = {self.sku_code}
        b_sku = self.bulk_skus_mapping.get(self.sku_code)
        if b_sku:
            skus.add(b_sku.bulk_sku_code)
        full_mapping = alternative_sku_mapping.get_all_alternative_sku(OrganicSkuModel)
        sku_mapping = {s: full_mapping[s] for s in skus if s in full_mapping}
        sku_mapping.update({alt_sku: orig_sku for orig_sku, alt_sku in sku_mapping.items()})
        return sku_mapping

    @cached_property
    def bulk_skus_mapping(self) -> dict[PACKAGED_SKU_CODE, BulkSku]:
        return bulk_sku.get_bulk_skus_mapping(self.brand)


def get_discards(site: str, brand: str, week: ScmWeek, sku_code: str, tran_type: DiscardType = None):
    context = DiscardsContext(brand=brand, site=site, week=week, sku_code=sku_code, tran_type=tran_type)
    dc_object = dc_admin.get_site(brand=brand, site=site, week=week)
    if brand == BRAND_GC:
        return context.wip_hj_discards + context.non_hj_discard_data
    return context.wip_hj_discards if dc_object.source == DataSource.HIGHJUMP else context.non_hj_discard_data
