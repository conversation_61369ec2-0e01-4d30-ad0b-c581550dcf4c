import logging

from procurement.client.googlesheets import googlesheet_utils
from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_HF
from procurement.core import utils
from procurement.core.dates.weeks import ScmWeek
from procurement.core.exceptions.base_errors import NonCriticalWrapperException
from procurement.core.request_utils import context
from procurement.data.dto.pimt.pakcaging_safety_stock import PlannedDepletionKey
from procurement.data.googlesheet_model.ambient_safety_stock import (
    LongTermForecastImport,
    PlannedDepletion,
    SafetyCombinedReport,
    SafetySkuMappingImport,
    SafetySkuMappingImportRow,
)
from procurement.data.models.forecast.demand_pipeline_sku_mapping import DemandPipelineSkuMappingModel
from procurement.data.models.forecast.packaging_long_term_forecast import PackagingLongTermForecastModel
from procurement.data.models.inventory.packaging_units_per_truck_load import PackagingSkuImportModel
from procurement.data.models.inventory.vendor_managed_inventory import VendorManagedInventoryModel
from procurement.data.models.pimt.planned_depletion import PlannedDepletionModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.forecast import packaging_forecast as packaging_forecast_repo
from procurement.repository.inventory import demand_pipeline_sku_mapping as demand_pipeline_repo
from procurement.repository.inventory import packagins_sku_import, vendor_managed_inventory
from procurement.repository.pimt import planned_depletion

logger = logging.getLogger(__name__)

DOCUMENT_EXCEPTION_TPL = "There is no valid data in '{}' tab of Ambient Safety Stock document"
ROW_EXCEPTION_TPL = "Error while importing '{}' Data"


def update_ambient_safety_stock_data() -> None:
    logger.info("Master Ambient Safety Stock Data sync: Start updating")
    market = context.get_request_context().market
    _update_safety_combined_reports(market=market)
    _update_safety_sku_mapping_import(market=market)
    _update_safety_long_term_forecast_import(market=market)
    _update_planned_depletion_data(market=market)


def _update_safety_combined_reports(market: str) -> None:
    sheet_data = gsheet_admin.read_gsheet(SafetyCombinedReport(), is_formatted=False)
    updates = {}
    for row in sheet_data:
        try:
            validate_required_fields(row, ["sku_code", "region", "supplier", "is_hfo"])
            if row.is_hfo or not (row.units_on_hand or row.inbound or row.outbound):
                continue
            key = (row.sku_code, row.region, row.supplier)
            if existing := updates.get(key):
                existing[VendorManagedInventoryModel.units] = googlesheet_utils.safe_sum(
                    (existing[VendorManagedInventoryModel.units], row.units_on_hand)
                )
                existing[VendorManagedInventoryModel.inbound] = googlesheet_utils.safe_sum(
                    (existing[VendorManagedInventoryModel.inbound], row.inbound)
                )
                existing[VendorManagedInventoryModel.outbound] = googlesheet_utils.safe_sum(
                    (existing[VendorManagedInventoryModel.outbound], row.outbound)
                )
            else:
                updates[key] = {
                    VendorManagedInventoryModel.region: row.region,
                    VendorManagedInventoryModel.sku_code: row.sku_code,
                    VendorManagedInventoryModel.market: market,
                    VendorManagedInventoryModel.supplier_name: row.supplier,
                    VendorManagedInventoryModel.units: row.units_on_hand,
                    VendorManagedInventoryModel.inbound: row.inbound,
                    VendorManagedInventoryModel.outbound: row.outbound,
                }
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, ROW_EXCEPTION_TPL.format(SafetyCombinedReport.sheet_name), skipped=True
            )
    if not updates:
        raise NonCriticalWrapperException(ValueError(DOCUMENT_EXCEPTION_TPL.format(SafetyCombinedReport.sheet_name)))
    vendor_managed_inventory.upsert_vendor_managed_inventory(list(updates.values()))


def _update_safety_long_term_forecast_import(market: str) -> None:
    sheet_data = gsheet_admin.read_gsheet(LongTermForecastImport())
    if not sheet_data:
        raise NonCriticalWrapperException(ValueError(DOCUMENT_EXCEPTION_TPL.format(LongTermForecastImport.sheet_name)))
    updates = []
    for row in sheet_data:
        try:
            validate_required_fields(row, ["week", "brand", "site", "demand_pipeline", "forecast"])
            updates.append(
                {
                    PackagingLongTermForecastModel.week: int(ScmWeek.from_str(row.week)),
                    PackagingLongTermForecastModel.brand: row.brand,
                    PackagingLongTermForecastModel.site: row.site,
                    PackagingLongTermForecastModel.forecast: row.forecast,
                    PackagingLongTermForecastModel.market: market,
                    PackagingLongTermForecastModel.demand_pipeline: row.demand_pipeline,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, ROW_EXCEPTION_TPL.format(LongTermForecastImport.sheet_name), skipped=True
            )
    packaging_forecast_repo.update_packaging_long_term_forecast(updates)


def _update_safety_sku_mapping_import(market: str) -> None:
    sheet_data = gsheet_admin.read_gsheet(SafetySkuMappingImport())
    if not sheet_data:
        raise NonCriticalWrapperException(ValueError(DOCUMENT_EXCEPTION_TPL.format(SafetySkuMappingImport.sheet_name)))

    _update_packaging_sku_import_data(sheet_data)
    _update_demand_pipeline_sku_mapping(sheet_data, market)


def _update_packaging_sku_import_data(sheet_data: list[SafetySkuMappingImportRow]) -> None:
    updates = []
    for row in sheet_data:
        try:
            validate_required_fields(row, ["bob_code", "sku_code"])
            updates.append(
                {
                    PackagingSkuImportModel.bob_code: row.bob_code,
                    PackagingSkuImportModel.sku_code: row.sku_code,
                    PackagingSkuImportModel.units_per_truck_load: row.units_per_truck_load,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(
                row, exc, ROW_EXCEPTION_TPL.format(SafetySkuMappingImport.sheet_name), skipped=True
            )
    packagins_sku_import.upsert_packaging_sku_import_data(updates)


def _update_demand_pipeline_sku_mapping(sheet_data: list[SafetySkuMappingImportRow], market: str) -> None:
    demand_pipelines = {}
    for row in sheet_data:
        try:
            validate_required_fields(row, ["sku_code", "sku_type", "demand_pipeline"])
            demand_pipelines[(row.sku_code, row.demand_pipeline)] = {
                DemandPipelineSkuMappingModel.sku_code: row.sku_code,
                DemandPipelineSkuMappingModel.sku_type: row.sku_type,
                DemandPipelineSkuMappingModel.market: market,
                DemandPipelineSkuMappingModel.demand_pipeline: row.demand_pipeline,
            }
        except ValueError as exc:
            warnings.notify_sheet_error(
                row,
                exc,
                f"Error while importing Long-Term Sku Forecast Mapping {SafetySkuMappingImport.sheet_name} data",
                skipped=True,
            )
    demand_pipeline_repo.update_demand_pipeline_sku_mapping(demand_pipelines.values())


def _update_planned_depletion_data(market: str) -> None:
    sheet_data = gsheet_admin.read_gsheet(PlannedDepletion())
    if not sheet_data:
        raise NonCriticalWrapperException(ValueError(DOCUMENT_EXCEPTION_TPL.format(PlannedDepletion.sheet_name)))
    updates = {}
    weeks = set()
    for row in sheet_data:
        try:
            validate_required_fields(row, ["week", "region", "owner", "demand_pipeline", "sku_code", "quantity"])
            for sku in utils.split_comma_string(row.sku_code):
                if not sku:
                    continue
                week = ScmWeek.from_str(row.week)
                week_int = int(week)
                weeks.add(week_int)
                if row.owner == "HF Owned":
                    row.owner = BRAND_HF
                key = PlannedDepletionKey(week, row.region, row.demand_pipeline, sku, row.owner)
                if it := updates.get(key):
                    it[PlannedDepletionModel.quantity] += row.quantity
                else:
                    updates[key] = {
                        PlannedDepletionModel.sku_code: sku,
                        PlannedDepletionModel.region: row.region,
                        PlannedDepletionModel.week: week_int,
                        PlannedDepletionModel.market: market,
                        PlannedDepletionModel.owner: row.owner,
                        PlannedDepletionModel.quantity: row.quantity,
                        PlannedDepletionModel.demand_pipeline: row.demand_pipeline,
                    }
        except ValueError as exc:
            warnings.notify_sheet_error(row, exc, ROW_EXCEPTION_TPL.format(PlannedDepletion.sheet_name), skipped=True)
    planned_depletion.delete_historical_data_from_planned_depl(weeks)
    planned_depletion.upsert_planned_depletion(updates.values())
