from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.googlesheet_model.total_procurement_purchasing import AllSkus
from procurement.data.models.inventory.ingredient import IngredientSiteCommodityGroupModel
from procurement.managers.admin import brand_admin, dc_admin, gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.imt import buyers
from procurement.repository.inventory import ingredient as ingredient_repo


def _read_category_data(sheet_rows):
    """
    removing duplicates by brand, since categories are defined only by sku_id + site
    """
    categories = {}
    shown_duplicates = set()
    for row in sheet_rows:
        try:
            validate_required_fields(row, ["sku_code", "dc"])
            sku_site = (row.sku_code, row.dc)
            existing_row = categories.get(sku_site)
            if (
                existing_row
                and sku_site not in shown_duplicates
                and existing_row.commodity_group != row.commodity_group
            ):
                warnings.notify_sheet_error(
                    row=row,
                    error=None,
                    message=f"{row.dc}'s SKU {row.sku_code} has duplicate with different category data. "
                    "Last most complete row will be preserved.",
                )
                shown_duplicates.add(sku_site)
            if not existing_row or row.commodity_group:
                categories[sku_site] = row
        except ValueError as exc:
            warnings.notify_sheet_error(row=row, error=exc, message="Error while importing commodity groups data")
    return categories.values()


def _update_categories(sheet_rows) -> None:
    commodity_groups = {item.commodity_group.strip() for item in sheet_rows if item.commodity_group}
    commodity_groups.discard("")
    ingredient_repo.update_commodities(commodity_groups)


def _update_ingredients_cgs(sheet_rows) -> None:
    market = context.get_request_context().market
    group_id_map = {cg.group_name: cg.id for cg in ingredient_repo.get_commodity_groups()}
    commodity_group_updates = []
    for item in sheet_rows:
        commodity_group_updates.append(
            {
                IngredientSiteCommodityGroupModel.sku_code: item.sku_code,
                IngredientSiteCommodityGroupModel.site: item.dc,
                IngredientSiteCommodityGroupModel.market: market,
                IngredientSiteCommodityGroupModel.commodity_group_id: (
                    group_id_map.get(item.commodity_group.strip()) if item.commodity_group else None
                ),
            }
        )
    ingredient_repo.update_commodity_groups(commodity_group_updates)


def update_categories_and_buyers() -> None:
    model = AllSkus()
    sheet_rows = gsheet_admin.read_gsheet(model)
    warnings.notify_records_errors(sheet_rows, skipped=False)
    valid_rows = _filter_rows(sheet_rows, model)
    if not valid_rows:
        raise ValueError("There is no valid data in TPP document")
    buyers.update_data(valid_rows)
    category_sheet_rows = _read_category_data(valid_rows)
    _update_categories(category_sheet_rows)
    _update_ingredients_cgs(category_sheet_rows)


def _filter_rows(sheet_rows: list, model: AllSkus) -> list:
    available_sites = {}
    for brand in brand_admin.get_brand_ids():
        week_config = brand_admin.get_week_config(brand)
        available_sites[brand] = dc_admin.get_enabled_sites(ScmWeek.current_week(week_config), brand)

    valid_rows = []

    for row in sheet_rows:
        try:
            if row.brand not in available_sites:
                raise ValueError(f"Brand {row.brand} is not valid")
            if row.dc not in available_sites[row.brand]:
                raise ValueError(f"Site {row.dc} is not valid for brand {row.brand}")
            valid_rows.append(row)
        except ValueError as exc:
            warnings.notify_sheet_error(row, exc, f"Error while importing {model.sheet_name} Data", skipped=True)

    return valid_rows
