import logging
from collections import defaultdict
from decimal import Decimal
from typing import NamedTuple

from sqlalchemy import Connection

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_FJ
from procurement.core.dates import ScmWeek
from procurement.core.typing import MEAL_NUMBER, RECIPE_CODE, SKU_CODE
from procurement.data.googlesheet_model.pkg import FactorPkgRow, NewPkg3pRow, NewPkgRow, PkgRow
from procurement.data.models.inventory.mealkit import MealkitIngredientModel, MealkitModel
from procurement.managers.datasync.framework import warnings
from procurement.repository.inventory import ingredient

logger = logging.getLogger(__name__)

PKG_ROWS = list[PkgRow] | list[NewPkgRow] | list[FactorPkgRow] | list[NewPkg3pRow]


class _MealkitIngredientKey(NamedTuple):
    recipe_code: RECIPE_CODE
    slot: MEAL_NUMBER
    sku_code: SKU_CODE
    is_in_kitbox: bool
    picks_2p: int
    picks_3p: int
    picks_4p: int
    picks_6p: int
    weight_amount: Decimal
    weight_unit: str


class MealkitValue(NamedTuple):
    meal_name: str
    recipe_name: str


def _get_mealkit_id_dict(
    week: ScmWeek, brand: str, transaction: Connection
) -> dict[tuple[RECIPE_CODE, MEAL_NUMBER], int]:
    return {
        (i.code, i.slot): i.id
        for i in ingredient.get_mealkit_by_week_and_brand(week=week, brand=brand, transaction=transaction)
    }


def _update_mealkit_ingredient(
    week: ScmWeek, current_data: dict[_MealkitIngredientKey, set[str]], brand: str, transaction: Connection
):
    mealkit_id_dict = _get_mealkit_id_dict(week, brand, transaction)
    updates = []
    for mealkit_key, dc_list in current_data.items():
        mealkit_id = mealkit_id_dict.get((mealkit_key.recipe_code, mealkit_key.slot))
        if not mealkit_id:
            continue
        updates.append(
            {
                MealkitIngredientModel.mealkit_id: mealkit_id,
                MealkitIngredientModel.sku_code: mealkit_key.sku_code,
                MealkitIngredientModel.is_in_kitbox: mealkit_key.is_in_kitbox,
                MealkitIngredientModel.picks_2p: mealkit_key.picks_2p,
                MealkitIngredientModel.picks_3p: mealkit_key.picks_3p,
                MealkitIngredientModel.picks_4p: mealkit_key.picks_4p,
                MealkitIngredientModel.picks_6p: mealkit_key.picks_6p,
                MealkitIngredientModel.dc_list: dc_list,
                MealkitIngredientModel.weight_amount: mealkit_key.weight_amount,
                MealkitIngredientModel.weight_unit: mealkit_key.weight_unit,
            }
        )
    ingredient.insert_many_mealkit_ingredient(updates=updates, transaction=transaction)


def _update_mealkit(
    current_data: dict[tuple[RECIPE_CODE, MEAL_NUMBER], MealkitValue],
    brand: str,
    week: ScmWeek,
    transaction: Connection,
):
    str_week = str(week)
    updates = [
        {
            MealkitModel.code: recipe_code,
            MealkitModel.slot: meal_number,
            MealkitModel.meal_name: mealkit_value.meal_name,
            MealkitModel.scm_week: str_week,
            MealkitModel.brand: brand,
            MealkitModel.sub_recipe: mealkit_value.recipe_name,
        }
        for (recipe_code, meal_number), mealkit_value in current_data.items()
    ]
    ingredient.delete_mealkit_by_week_and_brand(week, brand, transaction)
    ingredient.insert_many_mealkit(updates=updates, transaction=transaction)


def _group_data_by_recipes(brand: str, current_data: PKG_ROWS) -> dict[tuple[RECIPE_CODE, MEAL_NUMBER], MealkitValue]:
    groups = {}
    fields = ["recipe_code", "meal_number", "meal_name"]
    if brand == BRAND_FJ:
        fields.append("recipe_name")

    for mealkit in current_data:
        try:
            validate_required_fields(mealkit, fields)
            recipe_name = getattr(mealkit, "recipe_name", None)

            groups[(mealkit.recipe_code, mealkit.meal_number)] = MealkitValue(
                meal_name=mealkit.meal_name, recipe_name=recipe_name
            )
        except ValueError as exc:
            warnings.notify_sheet_error(mealkit, exc, "Error while importing Mealkit Recipe data", skipped=True)

    return groups


def _group_mealkit_ingredient(brand: str, current_data: PKG_ROWS) -> dict[_MealkitIngredientKey, set[str]]:
    mealkit_ingredient_data = defaultdict(set)
    fields = ["meal_number", "recipe_code", "sku_code", "site"]
    if brand != BRAND_FJ:
        fields.extend(["picks_2p", "picks_4p"])
    for mealkit_record in current_data:
        try:
            validate_required_fields(mealkit_record, fields)
            mealkit_key = _MealkitIngredientKey(
                recipe_code=mealkit_record.recipe_code,
                slot=mealkit_record.meal_number,
                sku_code=mealkit_record.sku_code,
                is_in_kitbox=mealkit_record.is_in_kitbox,
                picks_2p=mealkit_record.picks_2p if brand != BRAND_FJ else 2,
                picks_3p=getattr(mealkit_record, "picks_3p", 0) or 0,
                picks_4p=getattr(mealkit_record, "picks_4p", 0),
                picks_6p=getattr(mealkit_record, "picks_6p", 0) or 0,
                weight_unit=mealkit_record.weight_unit,
                weight_amount=mealkit_record.weight_amount,
            )
            mealkit_ingredient_data[mealkit_key].add(mealkit_record.site)
        except ValueError as exc:
            warnings.notify_sheet_error(mealkit_record, exc, "Error when importing Mealkit Ingredient data")

    mealkit_ingredient_data.default_factory = None
    return mealkit_ingredient_data


def update_mealkit_data(current_data: PKG_ROWS, week: ScmWeek, brand: str, transaction: Connection) -> None:
    recipe_group = _group_data_by_recipes(brand, current_data)
    mealkit_ingredient_group = _group_mealkit_ingredient(brand, current_data)

    if recipe_group and mealkit_ingredient_group:
        _update_mealkit(recipe_group, brand, week, transaction)
        _update_mealkit_ingredient(week, mealkit_ingredient_group, brand, transaction)
