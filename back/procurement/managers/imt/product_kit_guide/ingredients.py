import logging
from typing import NamedTuple, Type

from sqlalchemy import Connection

from procurement.client.googlesheets.googlesheet_utils import validate_required_fields
from procurement.constants.hellofresh_constant import BRAND_FJ, BRAND_GC
from procurement.core.dates import ScmWeek
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE
from procurement.data.googlesheet_model.core import SheetModel
from procurement.data.googlesheet_model.pkg import (
    CoreAllDCsOffering,
    CoreAllDCsOfferingV3,
    FactorAllDCsOffering,
    FactorPkgRow,
    GcAllDCsOffering,
    NewCoreAllDCsOffering,
    NewPkg3pRow,
    NewPkgRow,
    NewTplAllDCsOffering,
    PkgRow,
    TplAllDCsOffering,
    TplAllDCsOfferingV3,
)
from procurement.data.models.inventory.ingredient import IngredientModel
from procurement.managers.admin import dc_admin, gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.managers.imt.product_kit_guide import mealkit
from procurement.repository.inventory import ingredient as ingredient_repository
from procurement.services.database import app_db

RECIPE_TYPE_BP_DROP = "BP Drop"
SKU_TYPE_MEDIA = "Media"

logger = logging.getLogger(__name__)


PKG_ROWS = list[PkgRow] | list[NewPkgRow] | list[FactorPkgRow]


class PkgModels(NamedTuple):
    core_model: Type[SheetModel]
    tpl_model: Type[SheetModel]


def _read_input_region(
    week: ScmWeek, brand: str, pkg_models: PkgModels
) -> list[PkgRow] | list[NewPkgRow] | list[NewPkg3pRow]:
    data = gsheet_admin.read_gsheet(model=pkg_models.core_model(), week=week, brand=brand)
    has_3pl = any(dc for dc in dc_admin.get_enabled_sites(week, brand).values() if dc.is_3pl)
    if has_3pl:
        data.extend(gsheet_admin.read_gsheet(pkg_models.tpl_model(), week=week, brand=brand))
    warnings.notify_records_errors(data, skipped=False)
    return data


def update_mealkit_and_ingredients_tables(week: ScmWeek, brand: str) -> None:
    if brand == BRAND_FJ:
        update_factor_mealkit_and_ingredients_tables(week)
    else:
        update_core_brands_mealkit_and_ingredients_tables(week, brand)


def _update_mealkit_and_ingredients(data: PKG_ROWS, week: ScmWeek, brand: str, trns: Connection) -> None:
    _update_ingredients(data, brand, trns)
    mealkit.update_mealkit_data(data, week, brand, trns)


def update_core_brands_mealkit_and_ingredients_tables(week: ScmWeek, brand: str) -> None:
    if gsheet_admin.get_gsheet_id(CoreAllDCsOfferingV3.parent_doc, brand, week) is not None:
        pkg_models = PkgModels(core_model=CoreAllDCsOfferingV3, tpl_model=TplAllDCsOfferingV3)
    elif gsheet_admin.get_gsheet_id(NewCoreAllDCsOffering.parent_doc, brand, week) is not None:
        pkg_models = PkgModels(core_model=NewCoreAllDCsOffering, tpl_model=NewTplAllDCsOffering)
    else:
        pkg_models = PkgModels(core_model=CoreAllDCsOffering, tpl_model=TplAllDCsOffering)

    if brand == BRAND_GC:
        pkg_models = pkg_models._replace(core_model=GcAllDCsOffering)

    pkg_data = _read_input_region(week, brand, pkg_models)

    with app_db.transaction() as trns:
        _update_mealkit_and_ingredients(pkg_data, week, brand, trns)


def _update_ingredients(pkg_data: PKG_ROWS, brand: str, transaction: Connection) -> None:
    updates = []
    added_sku_codes = set()
    for item in pkg_data:
        try:
            if item.sku_code in added_sku_codes:
                continue
            validate_required_fields(item, ["sku_code"])
            added_sku_codes.add(item.sku_code)
            updates.append(
                {
                    IngredientModel.sku_code: item.sku_code,
                    IngredientModel.is_receipt_bp_drop: (item.recipe_type == RECIPE_TYPE_BP_DROP),
                    IngredientModel.is_type_media: (item.sku_type == SKU_TYPE_MEDIA),
                    IngredientModel.pack_size_amount: item.pack_size_amount,
                    IngredientModel.pack_size_unit: item.pack_size_unit,
                    IngredientModel.storage_location: item.storage_location,
                    IngredientModel.brand: brand,
                    IngredientModel.allergens: item.allergens,
                }
            )
        except ValueError as exc:
            warnings.notify_sheet_error(item, exc, "Error when importing Ingredient data", skipped=True)

    ingredient_repository.update_ingredients(update_data=updates, transaction=transaction)


@request_cache
def get_sku_meal_numbers(
    week: ScmWeek, site: str, brand: str, sku_codes: frozenset[str] = None
) -> dict[SKU_CODE, list[str]]:
    return {
        row.sku_code: row.meal_numbers
        for row in ingredient_repository.get_sku_meal_numbers(week=week, site=site, brand=brand, sku_codes=sku_codes)
    }


def get_kitting_flags_by_weeks(
    pkg_site: str, brand: str, weeks: frozenset[ScmWeek]
) -> dict[ScmWeek, dict[SKU_CODE, bool]]:
    res = {week: {} for week in weeks}
    for row in ingredient_repository.get_kitting_flags_by_weeks(pkg_site=pkg_site, brand=brand, weeks=weeks):
        res[row.week][row.sku_code] = row.is_in_kitbox
    return res


def update_factor_mealkit_and_ingredients_tables(week: ScmWeek) -> None:
    data = gsheet_admin.read_gsheet(FactorAllDCsOffering(), week=week, brand=BRAND_FJ)
    with app_db.transaction() as trns:
        _update_mealkit_and_ingredients(data, week, BRAND_FJ, trns)
