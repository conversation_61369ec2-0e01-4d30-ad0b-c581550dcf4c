import dataclasses
from collections.abc import Iterable
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from typing import NamedTuple

from procurement.constants.hellofresh_constant import MOCK_PLAN_WEEKS_AHEAD
from procurement.core.dates.weeks import ScmWeek, ScmWeekConfig
from procurement.core.typing import COMMODITY_GROUP_NAME, PURCHASING_CATEGORY_NAME, SKU_CODE, SKU_NAME, UNITS
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.imt import buyers
from procurement.managers.ordering import service as po_service
from procurement.managers.sku import culinary_sku

from .core.depletion import DepletionDataFetcher
from .core.highjump_data import HjDataFetcher
from .core.models import CalculationContext, DailyForecastData, DailyQuantityData
from .core.tpl_data import TplDataFetcher

BUFFER_PCT = Decimal("-0.05")


@dataclasses.dataclass
class CalculationDayItem:
    day: date
    week: ScmWeek
    carryover: Decimal = Decimal()
    stock_buffer: int = 0
    adjustment: int = 0
    incoming_scheduled: Decimal = Decimal()
    inbound_received: int = 0
    consumption: UNITS | None = 0
    closing_stock: Decimal | None = None
    daily_needs: int = 0
    stock_difference: int = 0
    status: str | None = None


DUMMY_CALC = CalculationDayItem(None, None)


@dataclasses.dataclass
class CalculationItem:
    sku_code: SKU_CODE
    days: list[CalculationDayItem]
    sku_name: SKU_NAME = None
    category: PURCHASING_CATEGORY_NAME = None
    commodity_group: COMMODITY_GROUP_NAME = None
    buyer: str | None = None


class DailyQuantities(NamedTuple):
    carryover: DailyQuantityData
    scheduled_incoming: DailyQuantityData
    inbound_received: DailyQuantityData


def get_inventory_calculations(
    brand: str,
    site: str,
    date_from: date = None,
    date_to: date = None,
) -> list[CalculationItem]:
    week_config = brand_admin.get_week_config(brand)
    if not date_from or not date_to:
        date_from, date_to = _get_default_date_range(week_config=week_config)
    dc_object = get_dc_object(brand, site, ScmWeek.from_date(date_from, week_config))
    calculation_data = _get_data_fetcher(dc_object=dc_object, date_from=date_from, date_to=date_to).fetch_daily_data()
    _normalize_consumption(calculation_data)
    calculations = _calculate_days(
        sorted_days=_get_date_range(date_from, date_to),
        sku_codes=calculation_data.sku_codes,
        daily_quantities=DailyQuantities(
            carryover=calculation_data.carryover,
            scheduled_incoming=calculation_data.scheduled_incoming,
            inbound_received=calculation_data.inbound_received,
        ),
        consumption=calculation_data.consumption,
        today=calculation_data.time_context.today,
        week_config=week_config,
    )
    res = _trim_non_requested_days(calculations, date_from)
    _fill_sku_details(res, dc_object, calculation_data.sku_codes)
    _fill_stock_diff(res)
    return res


def get_forecast_week_range(week_config: ScmWeekConfig) -> tuple[ScmWeek, ScmWeek]:
    today = date.today()
    start_week = ScmWeek.from_date(datetime.combine(today - timedelta(days=1), time()), week_config)
    end_week = start_week + MOCK_PLAN_WEEKS_AHEAD
    start_weekday = week_config.start_weekday
    if today.weekday() in (start_weekday - 1, start_weekday):
        end_week = end_week + 1
    return start_week, end_week


def get_forecast_date_range(week_config: ScmWeekConfig) -> tuple[date, date]:
    start_week, end_week = get_forecast_week_range(week_config)
    date_to = end_week.get_last_day(week_config) + timedelta(days=1)  # Wed of week after
    date_from = start_week.get_first_day(week_config)
    return date_from, date_to


def get_dc_object(brand: str, site: str, config_week: ScmWeek) -> DcConfig:
    dc_object = dc_admin.get_site(week=config_week, brand=brand, site=site)
    if not dc_object:
        raise ValueError(f"{site} site is not active for week {config_week}")
    return dc_object


def _get_data_fetcher(dc_object: DcConfig, date_from: date, date_to: date) -> DepletionDataFetcher:
    if dc_object.receiving_type.is_high_jump:
        return HjDataFetcher(dc_object=dc_object, date_from=date_from, date_to=date_to)
    if dc_object.receiving_type.is_manual:
        return TplDataFetcher(dc_object=dc_object, date_from=date_from, date_to=date_to)
    raise ValueError(f"{dc_object.receiving_type} inventory type is not supported.")


def _normalize_consumption(context: CalculationContext) -> None:
    for key, needs in context.consumption.items():
        context.consumption[key] = max(0, needs)


def _get_default_date_range(week_config: ScmWeekConfig, today: date = None) -> tuple[date, date]:
    today = today or date.today()
    current_week = ScmWeek.from_date(datetime.combine(today, time()), week_config)
    end_week = current_week + MOCK_PLAN_WEEKS_AHEAD
    date_to = end_week.get_last_production_day(week_config)
    date_from = current_week.get_first_day(week_config)
    return date_from, date_to


def _get_date_range(date_from: date, date_to: date) -> tuple[date]:
    return tuple(date_from + timedelta(d) for d in range((date_to - date_from).days + 1))


def _calculate_days(  # pylint: disable=too-many-arguments
    sorted_days: Iterable[date],
    sku_codes: frozenset[SKU_CODE],
    daily_quantities: DailyQuantities,
    consumption: DailyForecastData,
    today: date,
    week_config: ScmWeekConfig,
) -> list[tuple[SKU_CODE, list[CalculationDayItem]]]:
    res = []
    weeks = [ScmWeek.from_date(day, week_config) for day in sorted_days]
    for sku_code in sku_codes:
        days = [DUMMY_CALC]  # dummy item to avoid index check
        is_consumption_valid = True
        for ind, day in enumerate(sorted_days):
            key = (sku_code, day)

            calc_item = CalculationDayItem(
                day=day,
                week=weeks[ind],
                carryover=Decimal(daily_quantities.carryover.get(key, 0)) if day <= today else days[ind].closing_stock,
                stock_buffer=(
                    round(daily_quantities.carryover[key] * BUFFER_PCT) if key in daily_quantities.carryover else 0
                ),
                incoming_scheduled=daily_quantities.scheduled_incoming.get(key, Decimal()),
                inbound_received=daily_quantities.inbound_received.get(key, 0),
                consumption=consumption.get(key, 0) if is_consumption_valid else None,
            )
            if calc_item.consumption is None:
                is_consumption_valid = False
            if is_consumption_valid:
                _fulfill_day_forecast(calc_item, today)
            days.append(calc_item)
        days.pop(0)
        res.append((sku_code, days))
    return res


def _fulfill_day_forecast(day_item: CalculationDayItem, today: date):
    day_item.closing_stock = (
        day_item.carryover
        + day_item.adjustment
        + (day_item.inbound_received if day_item.day <= today else day_item.incoming_scheduled)
        - day_item.consumption
    )
    if day_item.closing_stock < 0:
        day_item.daily_needs = min(-day_item.closing_stock, Decimal(day_item.consumption))
    day_item.status = _get_day_status(day_item, today)


def _get_day_status(day_item: CalculationDayItem, today: date) -> str:
    if day_item.day < today:
        return "Day Passed"
    if day_item.closing_stock >= 0:
        return "Clear"
    return "Attention Needed"


def _trim_non_requested_days(
    calculations: Iterable[tuple[SKU_CODE, list[CalculationDayItem]]],
    requested_start_date: date,
) -> list[CalculationItem]:
    return [
        CalculationItem(
            sku_code=sku_code,
            days=[day for day in days if day.day >= requested_start_date],
        )
        for sku_code, days in calculations
    ]


def _fill_sku_details(items: list[CalculationItem], dc_object: DcConfig, sku_codes: frozenset[SKU_CODE]):
    sku_details = culinary_sku.get_sku_meta_by_code(dc_object.sheet_name, sku_codes)
    ot_sku_names = po_service.get_sku_names_by_codes(sku_codes=sku_codes)
    buyers_by_sku = buyers.get_buyers_by_sku(brand=dc_object.brand, site=dc_object.sheet_name)
    for item in items:
        if sku_detail := sku_details.get(item.sku_code):
            item.sku_name = sku_detail.sku_name
            item.category = sku_detail.category
            item.commodity_group = sku_detail.commodity_group
        if not item.sku_name:
            item.sku_name = ot_sku_names.get(item.sku_code)
        item.buyer = buyers_by_sku.get(item.sku_code)


def _fill_stock_diff(items: list[CalculationItem]):
    for item in items:
        # first day will got stock_difference = 0, as we don't have data for previous day
        past_closing_stock = item.days[0].carryover
        for day_item in item.days:
            day_item.stock_difference = abs(day_item.carryover - past_closing_stock)
            past_closing_stock = day_item.closing_stock
            if day_item.closing_stock is None:
                break
