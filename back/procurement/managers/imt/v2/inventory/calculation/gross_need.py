import dataclasses
from collections import defaultdict
from collections.abc import Iterable
from datetime import date

from procurement.constants.hellofresh_constant import BRAND_FJ
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE, UNITS
from procurement.managers.admin import brand_admin
from procurement.managers.imt import buyers as buyers_service
from procurement.managers.imt import hybrid_needs
from procurement.managers.sku import culinary_sku

from . import calculations, daily_needs_service

Days = dict[date, int]


@dataclasses.dataclass
class PgnWeekItem:
    days: Days
    sum: int


@dataclasses.dataclass
class PgnItem:
    sku_code: str
    sku_name: str | None
    category: str | None
    commodity_group: str | None
    buyer: str
    weeks: dict[ScmWeek, PgnWeekItem] = dataclasses.field(default_factory=dict)


class PgnCalculations:
    def __init__(self, brand: str, site: str):
        self._result = {}
        self._data_by_week = self._build_data_by_week(brand, site)
        self._days_by_week = defaultdict(set)

        self._buyers = buyers_service.get_buyers_by_sku(brand=brand, site=site)
        sku_codes = frozenset(k[0] for k in self._data_by_week)
        self._sku_metadata = culinary_sku.get_sku_meta_by_code(site=site, sku_codes=sku_codes)

    @staticmethod
    def _build_data_by_week(brand: str, site: str) -> dict[tuple[SKU_CODE, ScmWeek], dict[date, UNITS]]:
        data_by_week = defaultdict(lambda: defaultdict(int))
        week_config = brand_admin.get_week_config(brand)
        date_from, date_to = calculations.get_forecast_date_range(week_config)
        is_factor = brand == BRAND_FJ
        consumption = daily_needs_service.get_daily_needs_by_site(
            site=site,
            brand=brand,
            date_from=date_from,
            date_to=date_to,
            use_delivery_date_needs=is_factor,
        )
        day_mapper = hybrid_needs.get_factor_needs_day_mapping_func(use_production_days=False)
        for (sku_code, forecast_date), forecast_data in consumption.items():
            for week, quantity in forecast_data.items():
                actual_date = day_mapper(week, forecast_date) if is_factor else forecast_date
                data_by_week[(sku_code, week)][actual_date] += max(0, quantity)
        return data_by_week

    def _process_existing_consumption(self):
        for (sku_code, week), days in self._data_by_week.items():
            if sku_code not in self._result:
                sku = self._sku_metadata.get(sku_code)
                self._result[sku_code] = PgnItem(
                    sku_code=sku_code,
                    sku_name=sku.sku_name if sku else None,
                    category=sku.category if sku else None,
                    commodity_group=sku.commodity_group if sku else None,
                    buyer=self._buyers.get(sku_code, ""),
                )
            self._result[sku_code].weeks[week] = PgnWeekItem(days=days, sum=sum(days.values()))
            self._days_by_week[week].update(days.keys())

    def _fill_missing_days(self):
        for item in self._result.values():
            for week, required_days in self._days_by_week.items():
                if not (week_item := item.weeks.get(week)):
                    week_item = PgnWeekItem(days={}, sum=0)
                    item.weeks[week] = week_item

                item_days = week_item.days
                missed_days = required_days - item_days.keys()

                item_days.update({day: 0 for day in missed_days})

    def get_result(self) -> Iterable[PgnItem]:
        self._process_existing_consumption()
        self._fill_missing_days()
        return self._result.values()


def get_pgn_data(brand: str, site: str) -> Iterable[PgnItem]:
    return PgnCalculations(brand, site).get_result()
