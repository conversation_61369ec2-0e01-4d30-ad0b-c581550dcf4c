from collections import defaultdict
from datetime import date, datetime, time, timedelta

from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import highjump

from .depletion import DepletionDataFetcher
from .models import DailyQuantityData, TimeContext


class HjDataFetcher(DepletionDataFetcher):
    def __init__(self, dc_object: DcConfig, date_from: date, date_to: date):
        if dc_object.receiving_type.is_manual:
            raise ValueError("Given site has manual receive type")
        today = date.today()
        self.hj_snapshot_date_to = datetime.combine(today, time())
        latest_snapshot_day = self._get_latest_snapshot_day(dc_object)
        if latest_snapshot_day:
            self.hj_snapshot_date_to = min(latest_snapshot_day, self.hj_snapshot_date_to)
        time_context = TimeContext(
            today=today,
            requested_start_date=date_from,
            requested_end_date=date_to,
            calculation_date_from=min(datetime.combine(date_from, time()), self.hj_snapshot_date_to),
            calculation_date_to=datetime.combine(date_to, time()) + timedelta(days=1),
        )
        super().__init__(dc_object, time_context)

    @staticmethod
    def _get_latest_snapshot_day(dc_object: DcConfig) -> datetime | None:
        latest_day = highjump.get_latest_inv_snapshot_day(dc_object)
        return datetime.combine(latest_day, time()) if latest_day else None

    def _get_inbound_received(self) -> DailyQuantityData:
        hj_receives = highjump.get_hj_receipt_by_receive_date(
            dc_object=self.dc_object,
            date_from=self.time_context.calculation_date_from,
            date_to=self.time_context.calculation_date_to,
        )
        inbound = defaultdict(int)
        for receives in hj_receives.values():
            for receive in receives:
                inbound[(receive.sku_code, receive.receipt_time_est.date())] += receive.quantity_received
        inbound.default_factory = None
        return inbound

    def _get_carryovers(self) -> DailyQuantityData:
        snapshots = highjump.get_bod_snapshots(
            dc_object=self.dc_object,
            date_from=self.time_context.calculation_date_from.date(),
            date_to=self.hj_snapshot_date_to.date(),
        )
        res = defaultdict(int)
        for snapshot in snapshots:
            res[(snapshot.sku_code, snapshot.snapshot_timestamp)] += snapshot.quantity
        return res
