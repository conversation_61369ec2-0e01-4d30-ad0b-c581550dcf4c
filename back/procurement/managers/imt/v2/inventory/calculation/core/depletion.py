from abc import ABC, abstractmethod
from collections import defaultdict
from decimal import Decimal

from procurement.core.request_utils import context
from procurement.managers.admin import brand_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.ordering import service as po_service

from .consumption import ConsumptionService
from .models import CalculationContext, DailyQuantityData, TimeContext


class DepletionDataFetcher(ABC):
    def __init__(self, dc_object: DcConfig, time_context: TimeContext):
        self.dc_object = dc_object
        self.time_context = time_context
        self.market = context.get_request_context().market
        self.week_config = brand_admin.get_week_config(self.dc_object.brand)

    def fetch_daily_data(self) -> CalculationContext:
        consumption = ConsumptionService(
            self.dc_object,
            self.time_context.requested_start_date,
            self.time_context.requested_end_date,
        ).get_week_aware_consumption(remove_end_overlaps=False)
        sku_codes = frozenset(key[0] for key in consumption)
        return CalculationContext(
            dc_object=self.dc_object,
            time_context=self.time_context,
            carryover=self._get_carryovers(),
            scheduled_incoming=self._get_scheduled_incoming(),
            inbound_received=self._get_inbound_received(),
            consumption=ConsumptionService.aggregate_week_aware_to_daily_consumption(consumption),
            sku_codes=sku_codes,
        )

    @abstractmethod
    def _get_inbound_received(self) -> DailyQuantityData:
        pass

    @abstractmethod
    def _get_carryovers(self) -> DailyQuantityData:
        pass

    def _get_scheduled_incoming(self) -> DailyQuantityData:
        incoming_orders = po_service.get_incoming_orders_excluding_receives(
            dc_object=self.dc_object,
            date_from=self.time_context.calculation_date_from,
            date_to=self.time_context.calculation_date_to,
        )
        res = defaultdict(Decimal)
        for incoming_order in incoming_orders:
            day = incoming_order.delivery_time_start.date()
            res[(incoming_order.sku_code, day)] += incoming_order.quantity
        res.default_factory = None
        return res
