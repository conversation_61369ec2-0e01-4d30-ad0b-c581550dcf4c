import dataclasses
from datetime import date, datetime

from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE, UNITS
from procurement.managers.admin.dc_admin import DcConfig

DailyQuantityData = dict[tuple[SKU_CODE, date], UNITS]
DailyForecastData = dict[tuple[SKU_CODE, date], UNITS]

# Because of week overlap (explained below) we need to store weekly value for each day
# to be able to send it to Ordering Tool later. Most of the days will have only one week value,
# except WED and THU, they will have dict of two values for each overlapping week.
# Example structure:
# {
#   (sku1, date_of_TUE): {2021-W29: 100},
#   (sku1, date_of_WEN): {2021-W29: 120, 2021-W30: 90},
#   (sku1, date_of_THU): {2021-W29: 60, 2021-W30: 42},
#   (sku1, date_of_FRI): {2021-W30: 140}
# }
DailyWeekAwareForecastData = dict[tuple[SKU_CODE, date], dict[ScmWeek, UNITS]]

# Each week has also forecast data from WED and THU of following week.
# Forecast data example:
#                                                 |---------| --> these two days are technically from week 30
# W29: [WEN - THU - FRI - SAT - SUN - MON - TUE] - WEN - THU.....  but week 29 also has forecast data for them
# W30: ...........................................[WEN - THU - FRI - SAT - SUN - MON - TUE] - WEN - THU


@dataclasses.dataclass
class TimeContext:
    today: date  # base day for calculations
    requested_start_date: date  # requested start date
    requested_end_date: date  # requested end date (inclusive)
    calculation_date_from: datetime  # datetime start for calculations
    calculation_date_to: datetime  # datetime end for calculations (exclusive)


@dataclasses.dataclass
class CalculationContext:
    dc_object: DcConfig
    time_context: TimeContext
    carryover: DailyQuantityData
    scheduled_incoming: DailyQuantityData
    inbound_received: DailyQuantityData
    consumption: DailyForecastData
    sku_codes: frozenset[SKU_CODE]
