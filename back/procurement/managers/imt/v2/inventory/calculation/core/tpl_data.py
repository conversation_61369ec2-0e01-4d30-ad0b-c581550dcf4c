from collections import defaultdict
from datetime import date, datetime, time, timedelta

from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.forms import receive as receive_service
from procurement.repository.inventory import receiving as receiving_repo

from .depletion import DepletionDataFetcher
from .models import DailyQuantityData, TimeContext


class TplDataFetcher(DepletionDataFetcher):
    def __init__(self, dc_object: DcConfig, date_from: date, date_to: date):
        time_context = TimeContext(
            today=date_from,
            requested_start_date=date_from,
            requested_end_date=date_to,
            calculation_date_from=datetime.combine(date_from, time()),
            calculation_date_to=datetime.combine(date_to, time()) + timedelta(days=1),
        )
        super().__init__(dc_object, time_context)

    def _get_inbound_received(self) -> DailyQuantityData:
        receives = receiving_repo.get_received_by_date(
            dc_object=self.dc_object,
            receive_date_from=self.time_context.calculation_date_from,
            receive_date_to=self.time_context.calculation_date_to,
        )
        res = defaultdict(int)
        for receive in receives:
            res[
                (receive.sku_code, receive.receive_timestamp.date())
            ] += receive_service.get_manual_receive_row_total_qty(receive)
        res.default_factory = None
        return res

    def _get_carryovers(self) -> DailyQuantityData:
        return {}  # assume 0 carryover for 3PL site
