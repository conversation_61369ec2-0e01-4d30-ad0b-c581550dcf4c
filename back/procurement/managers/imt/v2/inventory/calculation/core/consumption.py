import dataclasses
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import Any, Callable

from procurement.constants.hellofresh_constant import (
    BRAND_EP,
    BRAND_FJ,
    BRAND_GC,
    BRAND_HF,
    MARKET_US,
    MOCK_PLAN_ERROR_WEEKS_OUT,
)
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context, warnings
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.hybrid_needs import HybridNeedsDto
from procurement.managers.admin import brand_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.forecasts import forecast_manager
from procurement.managers.imt import hybrid_needs as hybrid_needs_service
from procurement.managers.imt import mock_plan_calculation
from procurement.managers.imt.product_kit_guide import ingredients
from procurement.repository.forecast import oscar

from .models import DailyForecastData, DailyWeekAwareForecastData


@dataclasses.dataclass
class _MockPlanCalculationContext:
    weeks_set: frozenset[ScmWeek]
    dc_object: DcConfig

    def __post_init__(self):
        self.interpolation_by_week_kitting = mock_plan_calculation.get_mock_plans_by_week_and_kitting_type(
            dc_object=self.dc_object, weeks=self.weeks_set
        )
        forecast = forecast_manager.get_forecast_manager()
        self.oscar_by_week = forecast.get_forecast_by_weeks(
            site=self.dc_object.sheet_name, weeks=self.weeks_set, brand=self.dc_object.brand
        )
        self.kitting_flags = self._get_kitting_flags(self.weeks_set)

    def _get_kitting_flags(self, weeks: frozenset[ScmWeek]) -> dict[ScmWeek, dict[SKU_CODE, bool]]:
        if self.dc_object.brand in (BRAND_HF, BRAND_EP):
            return ingredients.get_kitting_flags_by_weeks(
                pkg_site=self.dc_object.pkg_name or self.dc_object.sheet_name, brand=self.dc_object.brand, weeks=weeks
            )
        return {week: {} for week in weeks}


class ConsumptionService:
    def __init__(self, dc_object: DcConfig, date_from: date, date_to: date, use_delivery_date_needs: bool = False):
        self.dc_object: DcConfig = dc_object
        self.date_from = date_from
        self.date_to = date_to + timedelta(days=1)
        self.use_delivery_date_needs = use_delivery_date_needs
        self.market = context.get_request_context().market
        self.week_config = brand_admin.get_week_config(self.dc_object.brand)

    @staticmethod
    def aggregate_week_aware_to_daily_consumption(consumption: DailyWeekAwareForecastData) -> DailyForecastData:
        return {key: sum(week_data.values()) for key, week_data in consumption.items()}

    def get_week_aware_consumption(self, remove_end_overlaps: bool = True) -> DailyWeekAwareForecastData:
        if self.market != MARKET_US:
            needs = self._get_canada_consumption(date_from=self.date_from, date_to=self.date_to)
        else:
            needs = self._get_hn_mock_plan_consumption(date_from=self.date_from, date_to=self.date_to)
        needs = self._fill_missing_dates(needs)
        if remove_end_overlaps:
            self._remove_end_overlaps(needs)
        return needs

    def _fill_missing_dates(self, needs: DailyWeekAwareForecastData) -> DailyWeekAwareForecastData:
        days = [self.date_from + timedelta(days=i) for i in range((self.date_to - self.date_from).days)]
        res = defaultdict()
        skus = {key[0] for key in needs}
        for sku_code in skus:
            for day in days:
                key = (sku_code, day)
                res[key] = needs.get(key, {})
        return res

    def _remove_end_overlaps(self, data: DailyWeekAwareForecastData) -> None:
        one_day = timedelta(days=1)
        start_week = ScmWeek.from_date(self.date_from - one_day, self.week_config)
        end_week = ScmWeek.from_date(self.date_to + one_day, self.week_config)

        for value in data.values():
            if value:
                value.pop(start_week, None)
                value.pop(end_week, None)

    def _get_canada_consumption(self, date_from: date, date_to: date) -> DailyWeekAwareForecastData:
        brand = self.dc_object.brand
        weeks = list(ScmWeek.range_from_dates(date_from, date_to, self.week_config))
        forecasts = oscar.get_canada_forecasts_by_date(brand, self.dc_object.sheet_name, weeks)
        return self._map_needs_by_day(needs=forecasts, week_getter=lambda it: it.scm_week)

    def _get_hn_mock_plan_consumption(self, date_from: date, date_to: date) -> DailyWeekAwareForecastData:
        latest_week_query = (
            hybrid_needs_service.get_latest_available_delivery_date_needs_week
            if self.use_delivery_date_needs
            else hybrid_needs_service.get_latest_available_hn_week
        )
        hn_latest_week = latest_week_query(site=self.dc_object.sheet_name, brand=self.dc_object.brand) or ScmWeek.min()
        current_week = ScmWeek.current_week(self.week_config)
        if hn_latest_week < current_week:
            warnings.add_message(
                f"Hybrid Needs is not set up for week {current_week}" ", using OSCAR Plan instead",
            )
        if self.dc_object.brand == BRAND_FJ:
            forecast_start_date = (hn_latest_week + 1).get_first_day(self.week_config)
        else:
            forecast_start_date = (
                date_from if hn_latest_week < current_week else (current_week + 1).get_first_day(self.week_config)
            )
        needs = self._get_hn_data(
            date_from=date_from, date_to=min(forecast_start_date + timedelta(days=self.week_config.overlap), date_to)
        )
        consumption = self._map_needs_by_day(needs=needs, week_getter=lambda hn: hn.scm_week)
        if forecast_start_date < date_to:
            forecasts = self._get_oscar_mock_plan(max(forecast_start_date, date_from), date_to)
            self._merge_forecast_to_needs(forecasts, consumption)
        return consumption

    def _get_hn_data(self, date_from: date, date_to: date) -> list[HybridNeedsDto]:
        if self.use_delivery_date_needs:
            return hybrid_needs_service.get_delivery_date_needs_by_dates(
                site=self.dc_object.sheet_name, brand=self.dc_object.brand, date_from=date_from, date_to=date_to
            )
        return hybrid_needs_service.get_data_by_dates(
            site=self.dc_object.sheet_name, brand=self.dc_object.brand, date_from=date_from, date_to=date_to
        )

    @staticmethod
    def _merge_forecast_to_needs(forecasts: DailyWeekAwareForecastData, consumption: DailyWeekAwareForecastData):
        for sku_day_key, forecast in forecasts.items():
            for week, oscar_week_value in forecast.items():
                sku_day_value = consumption.get(sku_day_key, {})
                sku_day_value[week] = oscar_week_value
                consumption[sku_day_key] = sku_day_value

    @staticmethod
    def _map_needs_by_day(
        needs: list[HybridNeedsDto], week_getter: Callable[[Any], ScmWeek]
    ) -> DailyWeekAwareForecastData:
        res = defaultdict(dict)
        for needs_item in needs:
            key = (needs_item.sku_code, needs_item.day)
            res[key][week_getter(needs_item)] = needs_item.value
        res.default_factory = None
        return res

    def _get_oscar_mock_plan(self, date_from: date, date_to: date) -> DailyWeekAwareForecastData:
        week_config = self.week_config
        calculation_context = _MockPlanCalculationContext(
            weeks_set=frozenset(ScmWeek.range_from_dates(date_from, date_to, week_config)),
            dc_object=self.dc_object,
        )
        self._warn_missing_weeks(calculation_context.interpolation_by_week_kitting, calculation_context.weeks_set)

        res = defaultdict(dict)
        absent_mock_plan = [Decimal()] * week_config.length
        for week in calculation_context.weeks_set:
            first_day = week.get_first_day(week_config)
            for forecast in calculation_context.oscar_by_week.get(week, []):
                week_kitting_flags = calculation_context.kitting_flags[week]
                weights = calculation_context.interpolation_by_week_kitting.get(
                    (week, week_kitting_flags.get(forecast.sku_code, False)), absent_mock_plan
                )
                for idx, weight in enumerate(weights):
                    week_day = first_day + timedelta(days=idx)
                    if date_from <= week_day < date_to:
                        res[(forecast.sku_code, week_day)][week] = (
                            utils.ceil_subone_decimal(forecast.forecast * weight)
                            if self.dc_object.brand == BRAND_GC
                            else utils.ceil_subone_integer(forecast.forecast * weight)
                        )

        res.default_factory = None
        return res

    def _warn_missing_weeks(self, mock_plan: dict[tuple[ScmWeek, bool], list[Decimal]], weeks: frozenset[ScmWeek]):
        db_weeks = {item[0] for item in mock_plan.keys()}
        max_alert_week = ScmWeek.current_week(self.week_config) + MOCK_PLAN_ERROR_WEEKS_OUT
        # removing last week because of overlap days
        if missing_weeks := {week for week in weeks if week < max_alert_week} - db_weeks - {max(weeks)}:
            warnings.add_message(f"Mock plan calculation missed for weeks {', '.join(sorted(map(str, missing_weeks)))}")
