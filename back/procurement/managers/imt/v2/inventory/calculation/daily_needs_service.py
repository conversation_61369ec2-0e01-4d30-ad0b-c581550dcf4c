from datetime import date

from procurement.core.dates import ScmWeek
from procurement.managers.admin import brand_admin
from procurement.managers.admin.dc_admin import DcConfig

from . import calculations
from .core.consumption import ConsumptionService
from .core.models import DailyWeekAwareForecastData


def get_daily_needs_by_dc_object(
    dc_object: DcConfig, date_from: date, date_to: date, use_delivery_date_needs: bool = False
) -> DailyWeekAwareForecastData:
    service = ConsumptionService(
        dc_object=dc_object, date_from=date_from, date_to=date_to, use_delivery_date_needs=use_delivery_date_needs
    )
    return service.get_week_aware_consumption()


def get_daily_needs_by_site(
    site: str, brand: str, date_from: date, date_to: date, use_delivery_date_needs: bool = False
) -> DailyWeekAwareForecastData:
    week_config = brand_admin.get_week_config(brand)
    try:
        dc_object = calculations.get_dc_object(brand, site, ScmWeek.from_date(date_from, week_config))
    except ValueError:
        return {}
    return get_daily_needs_by_dc_object(dc_object, date_from, date_to, use_delivery_date_needs)


def get_daily_needs_by_site_week(
    site: str, brand: str, week: ScmWeek, use_delivery_date_needs: bool = False
) -> DailyWeekAwareForecastData:
    week_config = brand_admin.get_week_config(brand)
    date_from, date_to = week.to_production_date_range(week_config)
    return get_daily_needs_by_site(site, brand, date_from, date_to, use_delivery_date_needs)
