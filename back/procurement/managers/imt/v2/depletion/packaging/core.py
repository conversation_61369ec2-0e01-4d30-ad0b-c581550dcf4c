from __future__ import annotations

from collections import defaultdict
from collections.abc import Iterable
from dataclasses import dataclass
from datetime import date, timed<PERSON>ta
from typing import NamedTuple

from procurement.constants.hellofresh_constant import MARKET_US, InventoryInputType, PartOfDay
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, RECEIVED_STATUSES
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE, UNITS
from procurement.data.dto.sku import SkuMeta, SkuProfile
from procurement.managers.depletion.constants import DAY_PASSED, NO_IMPACT, SUPPLEMENT_NEEDED
from procurement.managers.imt.packaging_inventory import CompoundPartEnum, LinerGroupEnum

WEEKDAYS_TITLES_US = (
    "Wednesday PM",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday AM",
)

ACCEPTED_INCOMING_STATUSES = {
    *AWAITING_DELIVERY_STATUSES,
    *RECEIVED_STATUSES,
}

ZERO_WEEKLY_VALUES = [0] * len(WEEKDAYS_TITLES_US)
SUPPLEMENT_NEEDED_EARLY_DAYS = 2


class SupplementNeedStatus(NamedTuple):
    status: str
    is_early: bool


@dataclass
class DailyNeeds:
    day: date
    week_day: str
    on_hand: int | None
    on_hand_override: int | None
    demand: int
    incoming: int
    incoming_override: int | None
    on_hand_projected: int = 0
    on_hand_min_on_hand_projected: int | None = None
    eod_inventory: int = 0
    status: str = NO_IMPACT

    def get_calculated_on_hand(self, ignore_projected: bool = False) -> int | None:
        return next(
            (
                v
                for v in (self.on_hand_override, self.on_hand, None if ignore_projected else self.on_hand_projected)
                if v is not None
            ),
            None,
        )

    def get_calculated_incoming(self) -> int:
        return next(v for v in (self.incoming_override, self.incoming) if v is not None)


@dataclass
class DepletionWeeklyItem:
    total_demand: int
    bow_inventory: int | None
    units_ordered: int
    units_received: int
    eow_inventory: int | None
    estimated_eow_inventory: int

    @property
    def overconsumption(self) -> int | None:
        if self.bow_inventory is None or self.eow_inventory is None:
            return None
        return self.bow_inventory + self.units_received - self.total_demand - self.eow_inventory

    @property
    def estimated_overconsumption(self):
        if self.bow_inventory is None or self.estimated_eow_inventory is None:
            return None
        return self.bow_inventory + self.units_received - self.total_demand - self.estimated_eow_inventory


@dataclass
class PackagingDepletionRaw:
    sku_meta: dict[SKU_CODE, SkuMeta]
    buyer_by_sku: dict[SKU_CODE, str]
    today: date
    dailies: dict[SKU_CODE, list[DailyNeeds]]
    weeklies: dict[SKU_CODE, DepletionWeeklyItem]
    sku_profiles: dict[SKU_CODE, SkuProfile]


@dataclass
class PackagingDepletionModel:
    sku_code: SKU_CODE | None
    sku_name: str | None
    category: str | None
    market: str
    commodity_group: str | None
    buyer: str | None
    units_short: str | None
    weekly_overview: DepletionWeeklyItem | None
    daily_needs: list[DailyNeeds]
    supplement_need: str
    is_early_supplement_need: bool = False
    liner_group: LinerGroupEnum | None = None
    liners: list[PackagingDepletionModel] | None = None
    sku_size: str | None = None
    sku_type: str | None = None
    sku_profile: str | None = None

    def __post_init__(self):
        if self.liner_group is None:
            self.liner_group = LinerGroupEnum.map_sku_name_to_liner_group(self.market, self.sku_name)

    @property
    def is_liner_group(self) -> bool:
        return bool(self.liners)


def get_days_of_week(week: ScmWeek) -> tuple[date, ...]:
    days = week.week_days()
    return *days, days[-1] + timedelta(days=1)


def complete_dailies(dailies: list[DailyNeeds], today: date, prev_week_inventory: int = 0):
    prev_inventory = prev_week_inventory
    for daily in dailies:
        daily.on_hand_projected = prev_inventory
        _calculate_daily_fields(daily)
        daily.status = _get_day_status(daily, today)
        prev_inventory = daily.eod_inventory


def _calculate_daily_fields(daily: DailyNeeds):
    daily.on_hand_min_on_hand_projected = daily.on_hand - daily.on_hand_projected if daily.on_hand is not None else None
    on_hand = daily.get_calculated_on_hand()
    daily.eod_inventory = on_hand - daily.demand
    if not daily.week_day.endswith(PartOfDay.AM):
        # For past days only received POs are included
        # since others will be "Past Due" and are not selected
        daily.eod_inventory += daily.get_calculated_incoming()


def calculate_weekly(
    inventory_type: InventoryInputType,
    dailies: list[DailyNeeds],
    units_ordered: UNITS,
    units_received: UNITS,
    demand: int,
):
    if context.get_request_context().market != MARKET_US or inventory_type.is_high_jump:
        bow_inventory = dailies[0].get_calculated_on_hand(ignore_projected=True)
        eow_inventory = dailies[-1].eod_inventory
    else:
        bow_inventory = 0
        eow_inventory = 0
    return DepletionWeeklyItem(
        total_demand=demand,
        bow_inventory=bow_inventory,
        units_ordered=round(units_ordered),
        units_received=round(units_received),
        eow_inventory=eow_inventory,
        estimated_eow_inventory=dailies[-1].eod_inventory - dailies[-1].get_calculated_incoming(),
    )


def build_model_rows(raw_depletion: PackagingDepletionRaw) -> list[PackagingDepletionModel]:
    res = []
    market = context.get_request_context().market
    for sku_code, daily in raw_depletion.dailies.items():
        sku = raw_depletion.sku_meta.get(sku_code)
        weekly = raw_depletion.weeklies[sku_code]
        supplement_need = _get_week_needs_status(daily)
        sku_size = sku_type = sku_profile = None
        if profile := raw_depletion.sku_profiles.get(sku_code):
            sku_size, sku_type, sku_profile = profile
        units_short = None
        if daily[0].day <= raw_depletion.today <= daily[-1].day:
            current_daily = next(d for d in daily if d.day == raw_depletion.today)
            units_short = (
                weekly.total_demand - current_daily.get_calculated_on_hand() - current_daily.get_calculated_incoming()
            )
        res.append(
            PackagingDepletionModel(
                sku_code=sku_code,
                sku_name=sku.sku_name if sku else None,
                category=sku.category if sku else None,
                market=market,
                commodity_group=sku.commodity_group if sku else None,
                buyer=raw_depletion.buyer_by_sku.get(sku_code),
                supplement_need=supplement_need.status,
                is_early_supplement_need=supplement_need.is_early,
                units_short=units_short,
                weekly_overview=weekly,
                daily_needs=daily,
                sku_size=sku_size,
                sku_type=sku_type,
                sku_profile=sku_profile,
            )
        )
    return res


def group_liners(items: list[PackagingDepletionModel], today: date) -> list[PackagingDepletionModel]:
    groups = defaultdict(list)
    res = []
    for item in items:
        if item.liner_group:
            groups[item.liner_group].append(item)
        else:
            res.append(item)
    res.extend(_build_liner_parent(group, children, today) for group, children in groups.items())
    return res


def _get_day_status(daily: DailyNeeds, today: date) -> str:
    return (
        # fmt: off
        DAY_PASSED if today > daily.day else
        NO_IMPACT if daily.demand == 0 or daily.get_calculated_on_hand() > daily.demand
        else SUPPLEMENT_NEEDED
        # fmt: on
    )


def _get_week_needs_status(daily_needs: list[DailyNeeds]) -> SupplementNeedStatus:
    supplement_idx, supplement_needed = next(
        ((day_idx, d) for day_idx, d in enumerate(daily_needs) if d.status == SUPPLEMENT_NEEDED), (None, None)
    )
    if supplement_needed:
        if daily_needs[supplement_idx].day - timedelta(days=SUPPLEMENT_NEEDED_EARLY_DAYS) >= date.today():
            supplement_index = (
                supplement_idx
                if supplement_idx < SUPPLEMENT_NEEDED_EARLY_DAYS
                else supplement_idx - SUPPLEMENT_NEEDED_EARLY_DAYS
            )
            return SupplementNeedStatus(daily_needs[supplement_index].week_day, True)
        return SupplementNeedStatus(supplement_needed.week_day, False)
    return SupplementNeedStatus(NO_IMPACT, False)


def _build_liner_parent(
    group: LinerGroupEnum,
    children: list[PackagingDepletionModel],
    today: date,
) -> PackagingDepletionModel:
    grouped_dailies = _merge_compound_daily_items(children)
    dailies = _merge_daily(grouped_dailies)
    for daily in dailies:
        _calculate_daily_fields(daily)
        daily.status = _get_day_status(daily, today)
    return PackagingDepletionModel(
        sku_code=None,
        sku_name=str(group),
        category=", ".join({i.category for i in children if i.category}),
        commodity_group="Liner",
        market=children[0].market,
        buyer=", ".join({i.buyer for i in children if i.buyer}),
        supplement_need=_get_supplemented_need(dailies),
        units_short=None,
        weekly_overview=None,
        daily_needs=dailies,
        liner_group=group,
        liners=children,
    )


def _merge_compound_daily_items(children: list[PackagingDepletionModel]) -> list[list[DailyNeeds]]:
    res = []
    compounds: dict[str, list[list[DailyNeeds]]] = defaultdict(list)
    for item in children:
        if part := CompoundPartEnum.get_compound_part(item.sku_name):
            compounds[item.sku_name.replace(part, "")].append(item.daily_needs)
        else:
            res.append(item.daily_needs)
    for parts in compounds.values():
        daily = []
        for day in range(len(parts[0])):
            first_part = parts[0][day]
            daily.append(
                DailyNeeds(
                    day=first_part.day,
                    week_day=first_part.week_day,
                    on_hand=_min_part((p[day].on_hand for p in parts)),
                    on_hand_override=_min_part((p[day].on_hand_override for p in parts), ignore_nones=True),
                    demand=_min_part(p[day].demand for p in parts) or 0,
                    incoming=_min_part(p[day].incoming for p in parts) or 0,
                    incoming_override=_min_part((p[day].incoming_override for p in parts), ignore_nones=True),
                    on_hand_projected=_min_part(p[day].on_hand_projected for p in parts) or 0,
                )
            )

        res.append(daily)
    return res


def _merge_daily(daily_groups: list[list[DailyNeeds]]) -> list[DailyNeeds]:
    res = []
    for day in range(len(daily_groups[0])):
        first_daily = daily_groups[0][day]
        on_hand_overrides = [d[day].on_hand_override for d in daily_groups if d[day].on_hand_override is not None]
        incoming_overrides = [d[day].incoming_override for d in daily_groups if d[day].incoming_override is not None]
        on_hand = (
            sum(d[day].on_hand or 0 for d in daily_groups)
            if any(d[day].on_hand is not None for d in daily_groups)
            else None
        )
        res.append(
            DailyNeeds(
                day=first_daily.day,
                week_day=first_daily.week_day,
                on_hand=on_hand,
                on_hand_override=sum(on_hand_overrides) if on_hand_overrides else None,
                demand=sum(d[day].demand for d in daily_groups),
                incoming=sum(d[day].incoming for d in daily_groups),
                incoming_override=sum(incoming_overrides) if incoming_overrides else None,
                on_hand_projected=sum(d[day].on_hand_projected or 0 for d in daily_groups),
            )
        )
    return res


def _get_supplemented_need(dailies: Iterable[DailyNeeds]) -> str:
    return next((daily.week_day for daily in dailies if daily.status == SUPPLEMENT_NEEDED), NO_IMPACT)


def _min_part(parts: Iterable[int | None], ignore_nones: bool = False) -> int | None:
    _min = None
    for part in parts:
        if part is None:
            if ignore_nones:
                continue
            return None
        if not part:
            return 0
        if _min is None or part < _min:
            _min = part
    return _min
