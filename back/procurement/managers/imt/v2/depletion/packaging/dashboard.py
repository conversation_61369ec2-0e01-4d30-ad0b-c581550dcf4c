from collections import defaultdict
from collections.abc import Callable
from datetime import date, timedelta
from functools import cached_property

from procurement.constants.hellofresh_constant import MARKET_CA, MARKET_US
from procurement.constants.ordering import IN_PROGRESS_HJ
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek, Weekday
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE, UNITS
from procurement.data.dto.inventory.packaging import PackagingOverride
from procurement.data.dto.sku import SkuMeta, SkuProfile
from procurement.data.models.inventory.alternative_sku_mapping import PackagingSkuModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.distribution_center import cycle_counts, highjump
from procurement.managers.imt import buyers, packaging_inventory
from procurement.managers.imt.models import PoMasterViewResult
from procurement.managers.imt.purchase_order import po_master_view
from procurement.managers.imt.purchase_order.po_master_view import PackagingPoStatusContext
from procurement.managers.inventory import imt as imt_inventory
from procurement.managers.inventory import snapshots as inv_snapshots
from procurement.managers.sku import alternative_sku_mapping, culinary_sku
from procurement.repository.inventory import packaging_sku_profile

from . import core
from .core import (
    ACCEPTED_INCOMING_STATUSES,
    WEEKDAYS_TITLES_US,
    ZERO_WEEKLY_VALUES,
    DailyNeeds,
    DepletionWeeklyItem,
    PackagingDepletionModel,
    PackagingDepletionRaw,
)

_HARDCODED_OE_OH_MAPPING = (
    {
        "OE": "OH",
        "OH": "OE",
    }
    if killswitch.canada_pck_depl_on_oh_aggregation_enabled
    else {}
)

US_WEEK_LIMIT = 1
CA_WEEK_LIMIT = 8


class PackagingDepletionDashboard:
    def __init__(self, week: ScmWeek, site: str, brand: str):
        self.market = context.get_request_context().market
        self.site = site
        self.brand = brand
        self.dc_object = dc_admin.get_site(week=week, site=site, brand=brand)
        self._prev_week = week - 1
        self._prev_week_days = core.get_days_of_week(self._prev_week)
        self.week = week
        if self.market == MARKET_CA and (mapped_bob_code := _HARDCODED_OE_OH_MAPPING.get(self.dc_object.bob_code)):
            mapped_dc = next(
                filter(
                    lambda dc: dc.bob_code == mapped_bob_code,
                    dc_admin.get_enabled_sites(week=self.week, brand=self.brand).values(),
                ),
                None,
            )
            self.dcs = list(filter(None, (self.dc_object, mapped_dc)))
        elif self.dc_object.multi_brand_dc:
            self.dcs = list(dc_admin.get_all_enabled_multi_brand_sites(self.week)[self.dc_object.multi_brand_dc])
        else:
            self.dcs = [self.dc_object]
        self.weekdays = core.get_days_of_week(self.week)
        self.today = date.today()
        self._result: list[PackagingDepletionModel] = None

    @cached_property
    def _demands(self) -> dict[ScmWeek, dict[SKU_CODE, list[int]]]:
        def get_demands(dc: DcConfig) -> dict[ScmWeek, dict[SKU_CODE, list[int]]]:
            return packaging_inventory.get_packaging_demand(
                weeks=[self._prev_week, self.week], site=dc.sheet_name, brand=dc.brand
            )

        if self.dc_object.bob_code not in _HARDCODED_OE_OH_MAPPING:
            return get_demands(self.dc_object)
        res = {}
        for _dc in self.dcs:
            dc_demands = get_demands(_dc)
            for week, sku_demands in dc_demands.items():
                existing_week_demand = res.get(week)
                if not existing_week_demand:
                    res[week] = dict(sku_demands)
                else:
                    self._merge_sku_demand(existing_week_demand, sku_demands)
        return res

    @staticmethod
    def _merge_sku_demand(existing_demand: dict[SKU_CODE, list[int]], additional_demand: dict[SKU_CODE, list[int]]):
        for sku, demand in additional_demand.items():
            existing_sku_demand = existing_demand.get(sku)
            if not existing_sku_demand:
                existing_demand[sku] = list(demand)
                continue
            for i, value in enumerate(demand):
                existing_sku_demand[i] += value

    @cached_property
    def _inv_snapshot(self) -> dict[SKU_CODE, dict[date, int]]:
        return self._get_snapshot(
            lambda dc: highjump.get_packaging_snapshots(
                dc_object=dc, date_from=self._prev_week_days[0], date_to=self.weekdays[-1] + timedelta(days=1)
            )
        )

    @cached_property
    def _cycle_counts(self) -> dict[SKU_CODE, dict[date, UNITS]]:
        return self._get_snapshot(
            lambda dc: cycle_counts.get_packaging_depletion_cycle_counts(
                dc_object=dc, date_from=self._prev_week_days[0], date_to=self.weekdays[-1]
            )
        )

    @cached_property
    def _unified_inventory(self) -> dict[SKU_CODE, dict[date, UNITS]]:
        return self._get_snapshot(
            lambda dc: imt_inventory.get_packaging_depletion_inventory_records(
                bob_code=dc.bob_code,
                inventory_type=dc.inventory_type,
                date_from=self._prev_week_days[0],
                date_to=self.weekdays[-1],
            )
        )

    @cached_property
    def _wmsl_snapshots(self) -> dict[SKU_CODE, dict[date, UNITS]]:
        return self._get_snapshot(
            lambda dc: inv_snapshots.get_packaging_depletion_snapshots(
                bob_code=dc.bob_code,
                date_from=self._prev_week_days[0],
                date_to=self.weekdays[-1] + timedelta(days=1),
            )
        )

    def _get_snapshot(
        self, snapshot_getter: Callable[[DcConfig], dict[SKU_CODE, dict[date, UNITS]]]
    ) -> dict[SKU_CODE, dict[date, UNITS]]:
        if self.dc_object.bob_code not in _HARDCODED_OE_OH_MAPPING:
            return snapshot_getter(self.dc_object)
        snapshots = map(snapshot_getter, self.dcs)
        res = {sku: dict(units) for sku, units in next(snapshots).items()}
        for snapshot in snapshots:
            self._merge_snapshots(res, snapshot)
        return res

    @staticmethod
    def _merge_snapshots(
        final_snapshot: dict[SKU_CODE, dict[date, UNITS]], snapshot: dict[SKU_CODE, dict[date, UNITS]]
    ) -> None:
        for sku, units in snapshot.items():
            existing_units = final_snapshot.get(sku)
            if not existing_units:
                final_snapshot[sku] = dict(units)
                continue
            for day, value in units.items():
                existing_units[day] = existing_units.get(day, 0) + value

    @cached_property
    def _latest_inv_snapshot_date(self) -> date:
        default_date = self._prev_week_days[0] - timedelta(days=1)
        if self.dc_object.inventory_type.is_cycle_count:
            return cycle_counts.get_latest_snapshot_date(self.brand, self.site) or default_date
        if self.dc_object.inventory_type.is_high_jump:
            return highjump.get_latest_packaging_snapshot_date() or default_date
        return default_date

    def _get_on_hand(self, sku_code: SKU_CODE, day: date) -> int | None:
        inventory_on_hands, existing_days = self._get_on_hand_source_and_existing_days()

        if day in inventory_on_hands.get(sku_code, {}):
            on_hand = inventory_on_hands.get(sku_code, {}).get(day)
        else:
            on_hand = 0 if day in existing_days else None

        alternative_sku = self._alternative_skus.get(sku_code)
        if day in inventory_on_hands.get(alternative_sku, {}):
            alternative_on_hand = inventory_on_hands.get(alternative_sku, {}).get(day)
        else:
            alternative_on_hand = 0 if day in existing_days else None
        on_hands = on_hand, alternative_on_hand
        return None if all(v is None for v in on_hands) else sum(filter(None, on_hands))

    def _get_on_hand_source_and_existing_days(self) -> tuple[dict[SKU_CODE, dict[date, UNITS]], set[date]]:
        total_on_hand = self._get_on_hand_source()
        existing_days = set()
        for value in total_on_hand.values():
            existing_days.update(value)
        return self._get_on_hand_source(), existing_days

    def _get_on_hand_source(self) -> dict[SKU_CODE, dict[date, UNITS]]:
        if self.dc_object.inventory_type.is_cycle_count:
            return self._cycle_counts
        if self.dc_object.inventory_type.is_high_jump:
            return self._inv_snapshot
        if self.dc_object.inventory_type.is_in_unified_inventory:
            return self._unified_inventory
        if self.dc_object.inventory_type.is_wmsl:
            return self._wmsl_snapshots
        return {}

    def _get_incoming(self, sku_code: SKU_CODE, day: date) -> UNITS:
        return self._get_sku_incoming(self._po_master_items.get(sku_code, {}), day) + self._get_sku_incoming(
            self._po_master_items.get(self._alternative_skus.get(sku_code), {}), day
        )

    @staticmethod
    def _get_sku_incoming(incomings: dict[date, list[PoMasterViewResult]], day: date) -> UNITS:
        return sum(
            po.total_quantity_received or po.quantity
            for po in incomings.get(day, [])
            if po.po_status in ACCEPTED_INCOMING_STATUSES or po.po_status.startswith(IN_PROGRESS_HJ)
        )

    @cached_property
    def _po_master_items(self) -> dict[SKU_CODE, dict[date, list[PoMasterViewResult]]]:
        po_master_items = defaultdict(lambda: defaultdict(list))
        for dc_obj in self.dcs:
            ctx = PackagingPoStatusContext(
                site=dc_obj.sheet_name,
                brand=dc_obj.brand,
                scheduled_date_from=self._prev_week_days[0],
                scheduled_date_to=self.weekdays[-1],
                week=self.week,
                combined_demand_dcs=self.dcs if self.dc_object.bob_code in _HARDCODED_OE_OH_MAPPING else None,
            )
            for item in po_master_view.build_po_status_dropdown(ctx):
                po_master_items[item.sku_code][item.scheduled_delivery_date].append(item)
        return po_master_items

    @cached_property
    def _sku_codes(self) -> frozenset[SKU_CODE]:
        return frozenset(self._demands[self.week])

    @cached_property
    def _overrides(self) -> dict[ScmWeek, dict[SKU_CODE, dict[date, PackagingOverride]]]:
        return packaging_inventory.get_packaging_overrides(
            weeks=[self._prev_week, self.week], site=self.site, brand=self.brand
        )

    @cached_property
    def _sku_meta(self) -> dict[SKU_CODE, SkuMeta]:
        return culinary_sku.get_sku_meta_by_code(site=self.site, sku_codes=self._sku_codes)

    @cached_property
    def _buyer_by_sku(self) -> dict[SKU_CODE, str]:
        return buyers.get_buyers_by_sku(self.brand, self.site)

    @cached_property
    def _sku_profile(self) -> dict[SKU_CODE, SkuProfile]:
        return packaging_sku_profile.get_packaging_sku_profile(sku_codes=self._sku_codes)

    @cached_property
    def _alternative_skus(self) -> dict[SKU_CODE, SKU_CODE]:
        return alternative_sku_mapping.get_all_alternative_sku(PackagingSkuModel)

    @cached_property
    def _next_week_bow_inventory(self) -> dict[SKU_CODE, int | None]:
        return {
            sku_code: self._get_on_hand(sku_code, self.weekdays[-1] + timedelta(days=1)) for sku_code in self._sku_codes
        }

    @cached_property
    def _current_week_bow_inventory(self) -> dict[SKU_CODE, int | None]:
        return {sku_code: self._get_on_hand(sku_code, self.weekdays[1]) for sku_code in self._sku_codes}

    def get_result(self) -> list[PackagingDepletionModel]:
        week_limit = CA_WEEK_LIMIT if self.market == MARKET_CA else US_WEEK_LIMIT
        if self._result is not None:
            return self._result
        if (
            not self.dc_object
            or self.week > ScmWeek.current_week(brand_admin.get_week_config(self.dc_object.brand)) + week_limit
        ):
            self._result = []
            return self._result
        dailies = self._calculate_dailies()
        weeklies = self._build_weekly_overview_from_data(dailies)
        items = core.build_model_rows(
            PackagingDepletionRaw(
                sku_meta=self._sku_meta,
                buyer_by_sku=self._buyer_by_sku,
                today=self.today,
                dailies=dailies,
                weeklies=weeklies,
                sku_profiles=self._sku_profile,
            )
        )
        self._result = core.group_liners(items=items, today=self.today)
        return self._result

    def _calculate_dailies(self) -> dict[SKU_CODE, list[DailyNeeds]]:
        dailies = {}
        for _week, weekdays in [[self._prev_week, self._prev_week_days], [self.week, self.weekdays]]:
            dailies[_week] = {sku: self._build_initial_daily_from_data(_week, weekdays, sku) for sku in self._sku_codes}
        self._complete_current_dailies(dailies)
        return dailies[self.week]

    def _build_initial_daily_from_data(self, week: ScmWeek, days: tuple[date, ...], sku: str) -> list[DailyNeeds]:
        demand = self._demands[week].get(sku, ZERO_WEEKLY_VALUES)
        overrides = self._overrides[week].get(sku, {})
        res = []
        for weekday, day in enumerate(days):
            override = overrides.get(day)
            if self.market != MARKET_US:
                if weekday <= 1:
                    on_hand = self._get_on_hand(sku, day)
                    on_hand = on_hand or (0 if on_hand is not None and any(demand) else None)
                else:
                    on_hand = None
            else:
                on_hand = self._get_on_hand(sku, day)

            res.append(
                DailyNeeds(
                    day=day,
                    week_day=WEEKDAYS_TITLES_US[weekday],
                    on_hand=on_hand,
                    on_hand_override=override.on_hand_override if override else None,
                    demand=demand[weekday],
                    incoming=round(self._get_incoming(sku, day)),
                    incoming_override=override.incoming_override if override else None,
                )
            )
        if self.market != MARKET_US:
            # (see GD-4778) Canada's packaging depletion week is Thu-Wed and does not overlap.
            # core::complete_dailies will work as usual, carrying the previous week projected on hand
            # to Thursday instead of Wednesday PM as in case of US, other calculations are the same.
            res.pop(0)
            res[-1].week_day = str(Weekday.WED)
        else:
            # to have complete Wednesday demand, previous week AM demand is carried over to combine with PM demand
            last_am_demand = self._demands.get(week - 1, {}).get(sku, [0])[-1]
            res[0].demand += last_am_demand
        return res

    def _complete_current_dailies(self, dailies: dict[ScmWeek, dict[SKU_CODE, list[DailyNeeds]]]) -> None:
        for daily_items in dailies[self._prev_week].values():
            core.complete_dailies(dailies=daily_items, today=self.today)
        for sku, daily_items in dailies[self.week].items():
            prev_daily = dailies[self._prev_week][sku][-1]
            core.complete_dailies(dailies=daily_items, today=self.today, prev_week_inventory=prev_daily.eod_inventory)
            if self.market != MARKET_US and (next_bow := self._next_week_bow_inventory.get(sku)) is not None:
                daily_items[-1].eod_inventory = next_bow

    @staticmethod
    def _merge_sku_pos_with_alternative(
        sku_pos: dict[date, list[PoMasterViewResult]], alternative_sku_pos: dict[date, list[PoMasterViewResult]]
    ) -> dict[date, list[PoMasterViewResult]]:
        merged_sku_pos = defaultdict(list)
        for pos_by_date in (sku_pos, alternative_sku_pos):
            for po_date, pos in pos_by_date.items():
                merged_sku_pos[po_date].extend(pos)
        return merged_sku_pos

    def _build_weekly_overview_from_data(
        self, dailies: dict[SKU_CODE, list[DailyNeeds]]
    ) -> dict[SKU_CODE, DepletionWeeklyItem]:
        weeklies = {}
        demands = self._demands[self.week]
        po_master_items = self._po_master_items
        for sku in self._sku_codes:
            sku_pos = po_master_items.get(sku, {})
            if alternative_sku := self._alternative_skus.get(sku):
                if alternative_sku_pos := po_master_items.get(alternative_sku):
                    sku_pos = self._merge_sku_pos_with_alternative(sku_pos, alternative_sku_pos)
            weeklies[sku] = core.calculate_weekly(
                inventory_type=self.dc_object.inventory_type,
                dailies=dailies[sku],
                units_ordered=sum(
                    sum(po.quantity for po in sku_pos.get(d, []) if not po.po_void) for d in self.weekdays
                ),
                units_received=sum(
                    sum(po.total_quantity_received for po in sku_pos.get(d, []) if not po.po_void)
                    for d in self.weekdays
                ),
                demand=sum(demands[sku]),
            )
        return weeklies
