from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.managers.imt.models import PoMasterViewResult
from procurement.managers.imt.purchase_order import po_master_view
from procurement.managers.imt.purchase_order.po_master_view import PackagingPoStatusContext
from procurement.managers.imt.v2.depletion.packaging import PackagingDepletionDashboard


def get_packaging_po_status(week: ScmWeek, site: str, brand: str, sku_code: SKU_CODE) -> list[PoMasterViewResult]:
    res = []
    dashboard = PackagingDepletionDashboard(week=week, site=site, brand=brand)
    for _dc in dashboard.dcs:
        ctx = PackagingPoStatusContext(
            site=_dc.sheet_name,
            brand=_dc.brand,
            scheduled_date_from=dashboard.weekdays[0],
            scheduled_date_to=dashboard.weekdays[-1],
            week=week,
            sku_code=sku_code,
            with_shipment=True,
        )
        res.extend(po for po in po_master_view.build_po_status_dropdown(ctx) if not po.po_void)
    return res
