from collections.abc import Iterable
from datetime import date

from procurement.core import utils
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.packaging import PackagingOverride, PackagingOverrideKey
from procurement.data.dto.sku import SkuMeta
from procurement.managers.admin import dc_admin
from procurement.managers.imt import packaging_inventory

from . import core
from .core import PackagingDepletionModel, PackagingDepletionRaw


def override_depletion(key: PackagingOverrideKey, model: PackagingDepletionModel) -> PackagingDepletionModel:
    override_item = _extract_override_data(model, key.sku_code, key.day)
    packaging_inventory.save_packaging_override(
        key=key, override=override_item, user_id=context.get_request_context().user_info.user_id
    )
    items = model.liners if model.is_liner_group else [model]
    today = date.today()
    _recalculate_dailies(items, today)
    sku_meta = _extract_sku_meta(items)
    buyers = _extract_buyer_by_sku(items)
    site = dc_admin.get_site(key.brand, key.site, key.week)
    rows = core.build_model_rows(
        PackagingDepletionRaw(
            sku_meta=sku_meta,
            buyer_by_sku=buyers,
            today=today,
            dailies={i.sku_code: i.daily_needs for i in items},
            weeklies={
                i.sku_code: core.calculate_weekly(
                    inventory_type=site.inventory_type,
                    dailies=i.daily_needs,
                    units_ordered=i.weekly_overview.units_ordered,
                    units_received=i.weekly_overview.units_received,
                    demand=i.weekly_overview.total_demand,
                )
                for i in items
            },
            sku_profiles={},
        )
    )
    return core.group_liners(items=rows, today=today)[0]


def _recalculate_dailies(items: Iterable[PackagingDepletionModel], today: date) -> None:
    for item in items:
        core.complete_dailies(
            dailies=item.daily_needs, today=today, prev_week_inventory=item.daily_needs[0].on_hand_projected
        )


def _extract_sku_meta(items: Iterable[PackagingDepletionModel]) -> dict[SKU_CODE, SkuMeta]:
    return {
        i.sku_code: SkuMeta(
            sku_code=i.sku_code,
            sku_id=utils.get_sku_id_from_sku_code(i.sku_code),
            sku_name=i.sku_name,
            unit=None,
            category=i.category,
            subcategory=None,
            commodity_group=i.commodity_group,
            brands=set(),
            is_manufactured_sku=False,
        )
        for i in items
    }


def _extract_buyer_by_sku(items: Iterable[PackagingDepletionModel]) -> dict[SKU_CODE, str]:
    return {i.sku_code: i.buyer for i in items if i.buyer}


def _extract_override_data(model: PackagingDepletionModel, sku_code: str, override_date: date) -> PackagingOverride:
    if model.is_liner_group:
        item = next((ln for ln in model.liners if ln.sku_code == sku_code), None)
        if not item:
            raise ValueError(f"SKU {sku_code} was not found in given data")
    else:
        item = model
    day = next((d for d in item.daily_needs if d.day == override_date), None)
    if not day:
        raise ValueError(f"Day {override_date} was not found in given data")
    return PackagingOverride(on_hand_override=day.on_hand_override, incoming_override=day.incoming_override)
