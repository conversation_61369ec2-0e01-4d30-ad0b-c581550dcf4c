import abc
import csv
import dataclasses
import itertools
from codecs import getwriter
from collections.abc import Iterable
from io import BytesIO
from string import ascii_uppercase
from typing import Any

from openpyxl import Workbook
from openpyxl.styles import PatternFill


@dataclasses.dataclass
class Row:
    content: Iterable[str]
    merge_cells: bool = False
    color: str | None = None


class TabularData(metaclass=abc.ABCMeta):
    def __init__(self, title: str, data: Iterable[Any]):
        self.title = title
        self.data = data

    def to_write_format(self) -> Iterable[Iterable[str]]:
        return itertools.chain(
            (row.content for row in self.column_headers),
            (row.content for row in self._rows),
        )

    @property
    @abc.abstractmethod
    def column_headers(self) -> list[Row]:
        pass

    @abc.abstractmethod
    def _get_row_data(self, raw_item: Any) -> Iterable[Any]:
        pass

    @property
    def _rows(self) -> Iterable[Row]:
        def xstr(cell: Any) -> str:
            return "" if cell is None else str(cell)

        return (Row(content=map(xstr, self._get_row_data(raw_item))) for raw_item in self.data)


class ExportSheet:
    def __init__(self, data: TabularData):
        self.data = data

    @staticmethod
    def _set_readable_width(sheet):
        for column_cells in sheet.columns:
            max_width = max(len(cell.value or "") for cell in column_cells)
            sheet.column_dimensions[column_cells[0].column_letter].width = max_width + 2

    def _apply_header_style(self, sheet):
        letters = dict(enumerate(ascii_uppercase, start=1))
        for number, row in enumerate(self.data.column_headers, start=1):
            if row.color:
                cells_range = f"A{number}:{letters[sheet.max_column]}{number}"
                cells = sheet[cells_range][0]
                for cell in cells:
                    cell.fill = PatternFill("solid", fgColor=row.color)
            if row.merge_cells:
                sheet.merge_cells(start_row=number, end_row=number, start_column=1, end_column=sheet.max_column)

    def write(self, workbook: Workbook) -> "ExportSheet":
        sheet = workbook.create_sheet(self.data.title)
        for row in self.data.to_write_format():
            sheet.append(list(row))

        self._set_readable_width(sheet)

        self._apply_header_style(sheet)
        return self


class ExportWorkbook:
    def __init__(self):
        self.workbook = Workbook()
        self.workbook.remove(self.workbook.active)

    def add_sheet(self, sheet: ExportSheet) -> "ExportWorkbook":
        sheet.write(self.workbook)
        return self

    @property
    def binary(self) -> bytes:
        virtual_workbook = BytesIO()
        self.workbook.save(virtual_workbook)
        return virtual_workbook.getvalue()


class ExportCsv:
    def __init__(self, data: TabularData):
        self.export_file = BytesIO()
        stream_writer = getwriter("utf-8")
        self.wrapper_file = stream_writer(self.export_file)
        self.data = data

    @property
    def binary(self) -> bytes:
        return self.export_file.getvalue()

    def write(self, separator: str = ",") -> "ExportCsv":
        writer = csv.writer(self.wrapper_file, dialect="excel", delimiter=separator)
        for row in self.data.to_write_format():
            writer.writerow(row)
        return self
