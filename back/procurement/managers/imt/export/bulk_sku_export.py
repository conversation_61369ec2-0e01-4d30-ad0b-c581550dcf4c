from collections.abc import Iterable
from datetime import datetime
from typing import Any

from procurement.client.s3_service import S3BucketName
from procurement.data.dto.inventory.bulk_skus import BulkSku
from procurement.managers.imt import bulk_sku as bulk_sku_service
from procurement.managers.imt.export import s3_export
from procurement.managers.imt.export.export_base import ExportCsv, Row, TabularData


class BulkSkuExportCsvData(TabularData):
    def __init__(self, export_data: Iterable[Any]):
        super().__init__("Export Bulk Skus CSV", export_data)

    @property
    def column_headers(self) -> list[Row]:
        return [Row(content=["Brands", "Bulk SKU Code", "Packaged SKU Code", "Pick Conversion", "Bulk Master SKU"])]

    def _get_row_data(self, raw_item: BulkSku) -> Iterable[Any]:
        return (
            raw_item.brands,
            raw_item.bulk_sku_code,
            raw_item.packaged_sku_code,
            raw_item.pick_conversion,
            raw_item.is_master_sku,
        )


def upload_bulk_skus_csv_to_s3() -> None:
    export_date = datetime.now()
    data = bulk_sku_service.get_bulk_skus()
    s3_export.export_to_s3(
        file=ExportCsv(BulkSkuExportCsvData(data)).write().binary,
        file_name=f"bulk_skus_{export_date.strftime("%y%m%d_%H%M")}.csv",
        bucket=S3BucketName.IMT_FORMS,
        folder="us_btg__imt-bulk_skus",
    )
