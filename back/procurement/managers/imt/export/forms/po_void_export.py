from collections.abc import Iterable
from datetime import date
from typing import Any

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.client.s3_service import S3BucketName
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.managers.imt.export import s3_export
from procurement.managers.imt.export.export_base import ExportCsv, Row, TabularData
from procurement.repository.ordering import po_void


class PoVoidExportCsvData(TabularData):
    def __init__(self, export_data: Iterable[Any]):
        super().__init__("Export PO Void CSV", export_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Brand",
                    "Site",
                    "PO Number",
                    "Supplier",
                    "# SKU",
                    "SKU Name",
                    "Submitted By",
                    "Timestamp",
                    "Comments",
                ]
            )
        ]

    def _get_row_data(self, raw_item: PoVoidDto) -> Iterable[Any]:
        return (
            raw_item.brand,
            raw_item.dc,
            raw_item.po_number,
            raw_item.supplier,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.user,
            raw_item.timestamp,
            raw_item.comment,
        )


def upload_po_void_csv(export_date: str):
    export_date = date.fromisoformat(export_date)
    data = po_void.get_po_void_info_by_date(export_date)
    s3_export.export_to_s3(
        file=ExportCsv(PoVoidExportCsvData(data)).write().binary,
        file_name=f"manual_voids_{export_date.strftime(DATE_FORMAT)}.csv",
        bucket=S3BucketName.IMT_FORMS,
        folder="us_btg__imt-voids",
    )
