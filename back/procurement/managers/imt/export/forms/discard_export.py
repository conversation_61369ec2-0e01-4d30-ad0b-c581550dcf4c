from collections.abc import Iterable
from datetime import date
from typing import Any

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT, GSHEET_DATE_FORMAT, GSHEET_TIME_FORMAT_LOCALE
from procurement.client.s3_service import S3BucketName
from procurement.managers.forms import discarding as discard_service
from procurement.managers.forms.discarding import DiscardData
from procurement.managers.imt.export import s3_export
from procurement.managers.imt.export.export_base import ExportCsv, Row, TabularData


class DiscardExportCsvData(TabularData):
    def __init__(self, export_data: Iterable[Any]):
        super().__init__("Export Discards CSV", export_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Brand",
                    "Site",
                    "Date Discarded",
                    "Time Discarded",
                    "Category",
                    "SKU Name",
                    "Discard Quantity",
                    "UOM",
                    "Discard Rate",
                    "Username",
                    "Comments",
                ]
            )
        ]

    def _get_row_data(self, raw_item: DiscardData) -> Iterable[Any]:
        return (
            raw_item.brand,
            raw_item.site,
            raw_item.discarded_time.strftime(GSHEET_DATE_FORMAT),
            raw_item.discarded_time.strftime(GSHEET_TIME_FORMAT_LOCALE),
            raw_item.category,
            raw_item.sku_name,
            raw_item.quantity,
            raw_item.unit_of_measure,
            raw_item.discard_rate,
            raw_item.user,
            raw_item.comment,
        )


def upload_discard_csv(export_date: str):
    export_date = date.fromisoformat(export_date)
    data = discard_service.get_discards_by_change_date(export_date)
    s3_export.export_to_s3(
        file=ExportCsv(DiscardExportCsvData(data)).write().binary,
        file_name=f"manual_discards_{export_date.strftime(DATE_FORMAT)}.csv",
        bucket=S3BucketName.IMT_FORMS,
        folder="us_btg__imt-discards",
    )
