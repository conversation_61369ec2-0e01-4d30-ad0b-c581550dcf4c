from collections.abc import Iterable
from datetime import date
from typing import Any

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.client.s3_service import S3BucketName
from procurement.data.dto.inventory.pull_put import PullPutDto
from procurement.managers.imt import inventory as inventory_service
from procurement.managers.imt.export import export_base, s3_export
from procurement.managers.imt.export.export_base import ExportCsv, TabularData


class PullPutDailyExportCsvData(TabularData):
    def __init__(self, export_data: Iterable[Any]):
        super().__init__("Inventory Pull Put CSV export", export_data)

    @property
    def column_headers(self) -> list[export_base.Row]:
        return [
            export_base.Row(
                content=[
                    "Brand",
                    "DC",
                    "Type",
                    "SKU #",
                    "SKU Name",
                    "Quantity",
                    "Last Edited By",
                    "Edited At",
                    "Comments",
                ]
            )
        ]

    def _get_row_data(self, raw_item: PullPutDto) -> Iterable[Any]:
        return (
            raw_item.brand,
            raw_item.dc,
            raw_item.pull_put_type,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.qty,
            raw_item.user_email,
            raw_item.updated_at,
            raw_item.comment,
        )


def upload_pull_put_export_csv(export_date: str):
    export_date = date.fromisoformat(export_date)
    data = inventory_service.get_pull_put_items_for_export(export_date)
    s3_export.export_to_s3(
        file=ExportCsv(PullPutDailyExportCsvData(data)).write().binary,
        file_name=f"inventory_input_{export_date.strftime(DATE_FORMAT)}.csv",
        bucket=S3BucketName.IMT_FORMS,
        folder="us_btg__imt-inventory",
    )
