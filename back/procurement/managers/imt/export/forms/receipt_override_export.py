import logging
from collections.abc import Iterable
from datetime import date
from typing import Any

from procurement.client.googlesheets.googlesheet_utils import DATE_FORMAT
from procurement.client.s3_service import S3BucketName
from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.receipt_override import ReceiptData
from procurement.managers.forms.receipt_override import ReceiptOverrideData
from procurement.managers.imt.export import export_base, s3_export
from procurement.managers.imt.export.export_base import ExportCsv, TabularData
from procurement.managers.receipt import grn
from procurement.repository.inventory import receipt_override as receipt_override_repo

logger = logging.getLogger(__name__)


class ReceiptOverrideDailyExportCsvData(TabularData):
    def __init__(self, export_data: Iterable[Any]):
        super().__init__("Export HJ Override CSV", export_data)

    @property
    def column_headers(self) -> list[export_base.Row]:
        return [
            export_base.Row(
                content=[
                    "Entry Type",
                    "Brand",
                    "Site",
                    "PO #",
                    "Supplier Name",
                    "SKU #",
                    "SKU Name",
                    "Quantity Received in HighJump",
                    "Adjusted Received Volume",
                    "Difference: HighJump vs Adjusted",
                    "Adjusted Receiving Date",
                    "Submitted By",
                    "Timestamp",
                ]
            )
        ]

    def _get_row_data(self, raw_item: ReceiptOverrideData) -> Iterable[Any]:
        return (
            raw_item.entry_type,
            raw_item.brand,
            raw_item.site,
            raw_item.po_number,
            raw_item.supplier_name,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.quantity_received,
            raw_item.adjusted_received_volume,
            raw_item.difference_receipt_vs_adj,
            raw_item.adjusted_receiving_date,
            raw_item.submitted_by,
            raw_item.timestamp,
        )


def _prepare_receipt_override_export_data(export_date: date) -> list[ReceiptOverrideData]:
    result = []
    week = ScmWeek.from_date(export_date)
    receipts_by_brand = {}
    unreceived_dummy_record = ReceiptData("", "", "", "", "", 0, 0)
    for item in receipt_override_repo.get_receipt_override_by_date(export_date):
        if item.brand not in receipts_by_brand:
            receipts_by_brand[item.brand] = {
                (receipt.po_number, receipt.sku_code): receipt
                for receipt in grn.get_grn_receipts(week=week, brand=item.brand)
            }
        receipt = receipts_by_brand[item.brand].get((item.po_number, item.sku_code), unreceived_dummy_record)
        quantity_received = receipt.quantity_received or 0
        cases_received = receipt.cases_received or 0
        result.append(
            ReceiptOverrideData(
                entry_type="Receipt Override",
                site=item.site,
                brand=item.brand,
                po_number=item.po_number,
                supplier_name=receipt.supplier_name,
                sku_code=item.sku_code,
                sku_name=item.sku_name,
                quantity_received=quantity_received,
                adjusted_received_volume=item.qty,
                difference_receipt_vs_adj=quantity_received - item.qty,
                cases_received=cases_received,
                adjusted_received_cases=item.cases,
                case_difference_receipt_vs_adj=cases_received - (item.cases or 0),
                adjusted_receiving_date=item.receiving_date,
                submitted_by=item.user,
                timestamp=item.upd_tmst,
                comment=item.comment,
            )
        )
    return result


def upload_receipt_override_csv(export_date: str):
    logger.info("Sending receipt override daily csv report...")
    export_date = date.fromisoformat(export_date)
    data = _prepare_receipt_override_export_data(export_date)
    s3_export.export_to_s3(
        file=ExportCsv(ReceiptOverrideDailyExportCsvData(data)).write().binary,
        file_name=f"hj_overrides_{export_date.strftime(DATE_FORMAT)}.csv",
        bucket=S3BucketName.IMT_FORMS,
        folder="us_btg__imt-overrides",
    )
    logger.info("Receipt override daily csv report sent.")
