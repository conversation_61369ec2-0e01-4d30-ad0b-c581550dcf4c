from procurement.client import file_export
from procurement.client.s3_service import S3BucketName, S3Service
from procurement.core.config_utils import killswitch


def export_to_s3(file: bytes, file_name: str, bucket: S3BucketName, folder: str = None):
    if killswitch.export_to_local_files_enabled:
        file_export.dump_to_file(file_name=file_name, file_bytes=file)
    else:
        S3Service.upload_bytes(file=file, s3_file_name=file_name, bucket=bucket, folder=folder)
