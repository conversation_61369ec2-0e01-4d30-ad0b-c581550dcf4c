from __future__ import annotations

import abc
import dataclasses
from collections.abc import Iterable
from datetime import timedelta
from typing import Type

from procurement.client.googlesheets.googlesheet_utils import FRONT_FORMAT
from procurement.constants.hellofresh_constant import NO_BRAND, PACKAGING_OTHER_CODE, PartOfDay, UnitOfMeasure
from procurement.constants.ordering import AWAITING_DELIVERY_STATUSES, NOT_DELIVERED_PAST_DUE, RECEIPT_CANCELLED
from procurement.core.dates.constants import Weekday
from procurement.core.dates.weeks import ScmWeek
from procurement.core.typing import SITE
from procurement.data.googlesheet_model.imt_daily_export import FactorUndeliveredExport, PackagingUndeliveredExport
from procurement.managers.datasync.framework import warnings
from procurement.managers.exports.base import BaseExportModel
from procurement.managers.imt.purchase_order import po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusResult

from . import base
from .base import ImtBaseExport, ImtExportContext, InputModel


@dataclasses.dataclass
class IngredientPoExportData(BaseExportModel):
    brand: str
    week: ScmWeek
    site: str
    sku_code: str
    sku_name: str
    category: str
    buyer: str
    supplier: str
    po: str
    scheduled_delivery_date: str
    status: str
    appointment_time: str

    @staticmethod
    def from_dashboard_item(item: PoStatusResult, context: ImtExportContext) -> IngredientPoExportData:
        return IngredientPoExportData(
            brand=context.brand,
            site=context.site,
            sku_code=item.sku_code,
            buyer=item.buyers,
            supplier=item.supplier,
            po=item.po_number,
            sku_name=item.sku_name,
            category=item.category,
            scheduled_delivery_date=item.scheduled_delivery_date.strftime(FRONT_FORMAT),
            status=item.po_status,
            week=context.week,
            appointment_time=(
                item.shipment_data.appointment_time.strftime(FRONT_FORMAT)
                if item.shipment_data and item.shipment_data.appointment_time
                else None
            ),
        )


@dataclasses.dataclass
class PackagingPoExportData(IngredientPoExportData):
    quantity_ordered: int

    @staticmethod
    def from_dashboard_item(item: PoStatusResult, context: ImtExportContext) -> PackagingPoExportData:
        return PackagingPoExportData(
            brand=context.brand,
            site=context.site,
            sku_code=item.sku_code,
            buyer=item.buyers,
            supplier=item.supplier,
            po=item.po_number,
            quantity_ordered=int(item.quantity_ordered),
            sku_name=item.sku_name,
            category=item.category,
            scheduled_delivery_date=item.scheduled_delivery_date.strftime(FRONT_FORMAT),
            status=item.po_status,
            week=context.week,
            appointment_time=(
                item.shipment_data.appointment_time.strftime(FRONT_FORMAT)
                if item.shipment_data and item.shipment_data.appointment_time
                else None
            ),
        )


class BasePoStatusExport(ImtBaseExport, metaclass=abc.ABCMeta):
    def _get_dashboard_raw_data(
        self, week: ScmWeek, brand: str, sites: Iterable[SITE]
    ) -> dict[SITE, list[PoStatusResult]]:
        return po_status.get_po_status(weeks=(week,), brand=brand, sites=sites)

    @staticmethod
    def _is_past_due(item: PoStatusResult) -> bool:
        return item.po_status == NOT_DELIVERED_PAST_DUE

    @staticmethod
    def _is_cancelled(item: PoStatusResult) -> bool:
        return item.po_status == RECEIPT_CANCELLED

    @staticmethod
    def _is_awaiting_delivery(item: PoStatusResult) -> bool:
        return item.po_status in AWAITING_DELIVERY_STATUSES

    @staticmethod
    def _get_production_day(item: PoStatusResult) -> str:
        days_for_brand = base.get_production_days_for_brand(item.week, item.brand)
        day_in_week = base.get_production_day_for_week(item.week, item.scheduled_delivery_date)
        return days_for_brand.get(day_in_week)


class PackagingPoStatusExport(BasePoStatusExport):
    result_model = PackagingPoExportData
    gsheet_model = PackagingUndeliveredExport()

    @abc.abstractmethod
    def _close_schedule(self, item: PoStatusResult) -> bool:
        pass

    def _should_include(self, week: ScmWeek, item: PoStatusResult) -> bool:
        return (
            self._is_packaging(item)
            and not self._is_packaging_other(item)
            and (self._close_past_due(item) or self._close_awaiting(item))
        )

    def _close_past_due(self, item: PoStatusResult) -> bool:
        return self._is_past_due(item) and self._close_schedule(item)

    def _close_awaiting(self, item: PoStatusResult) -> bool:  # pylint: disable=unused-argument
        return False

    @staticmethod
    def _is_packaging_other(item: InputModel) -> bool:
        return item.sku_code.startswith(PACKAGING_OTHER_CODE)


class AmPackagingPoStatusExport(PackagingPoStatusExport):
    def _close_schedule(self, item: PoStatusResult) -> bool:
        return item.scheduled_delivery_date == (self.export_date - timedelta(days=1))


class PmPackagingPoStatusExport(PackagingPoStatusExport):
    def _close_schedule(self, item: PoStatusResult) -> bool:
        return item.scheduled_delivery_date in {self.export_date, self.export_date - timedelta(days=1)}

    def _close_awaiting(self, item: PoStatusResult) -> bool:
        return self._is_awaiting_delivery(item) and item.scheduled_delivery_date == self.export_date


@dataclasses.dataclass
class FactorPoExportData(BaseExportModel):
    brand: str
    site: str
    sku_code: str
    sku_name: str
    purchasing_uom: UnitOfMeasure
    buyer: str
    supplier: str
    po: str
    scheduled_delivery_date: str
    status: str

    @staticmethod
    def from_dashboard_item(item: PoStatusResult, context: ImtExportContext) -> FactorPoExportData:
        return FactorPoExportData(
            brand=context.brand,
            site=context.site,
            sku_code=item.sku_code,
            buyer=item.buyers,
            supplier=item.supplier,
            po=item.po_number,
            sku_name=item.sku_name,
            scheduled_delivery_date=item.scheduled_delivery_date.strftime(FRONT_FORMAT),
            status=item.po_status,
            purchasing_uom=item.purchasing_unit,
        )


class FactorPoStatusExport(BasePoStatusExport):
    result_model = FactorPoExportData
    gsheet_model = FactorUndeliveredExport()

    def _should_include(self, week: ScmWeek, item: PoStatusResult) -> bool:
        if item.brand == NO_BRAND:
            warnings.notify_warning(
                f"FactorPoStatusExport -- {item.po_number} will be skipped because of the missing brand data"
            )
            return False
        return (
            self._is_past_due(item)
            or self._is_cancelled(item)
            or self._get_production_day(item)
            in {
                Weekday.TUE.numbered_title(),
                Weekday.WED.numbered_title(number=2),
            }
        )


def get_packaging_po_status_export_model(part_of_day: PartOfDay) -> Type[PackagingPoStatusExport]:
    if part_of_day == PartOfDay.AM:
        return AmPackagingPoStatusExport
    return PmPackagingPoStatusExport
