from collections.abc import Iterable
from datetime import date
from typing import Type

from procurement.constants.hellofresh_constant import BRAND_FJ, PartOfDay
from procurement.core.dates.constants import Weekday
from procurement.core.dates.weeks import ScmWeek
from procurement.core.typing import BRAND, SITE
from procurement.data.googlesheet_model.imt_daily_export import ImtDailyExport
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.exports import utils

from . import packaging_depletion, po_status
from .base import ImtBaseExport
from .packaging_depletion import WeeklyLinerGuidanceExport
from .po_status import FactorPoStatusExport


def _get_gsheet_id() -> str:
    return utils.get_gsheet_id(ImtDailyExport.parent_doc)


def packaging_depletion_daily_export(export_date: date = None):
    gsheet_id = _get_gsheet_id()
    export_date = export_date or date.today()
    week_day = export_date.weekday()

    if model := packaging_depletion.get_packaging_depletion_export_model():
        _execute_export(model, export_date, gsheet_id, week_day)


def packaging_po_status_daily_export(part_of_day: PartOfDay, export_date: date = None):
    gsheet_id = _get_gsheet_id()
    export_date = export_date or date.today()
    week_day = export_date.weekday()

    if model := po_status.get_packaging_po_status_export_model(part_of_day):
        _execute_export(model, export_date, gsheet_id, week_day)


def weekly_liner_guidance_daily_export(export_date: date = None):
    gsheet_id = _get_gsheet_id()
    export_date = export_date or date.today()

    WeeklyLinerGuidanceExport(
        export_date=export_date,
        gsheet_id=gsheet_id,
        sites_by_brand_week=_get_sites_by_brand_week([ScmWeek.from_date(export_date)], exclude_non_3pls=False),
    ).write_to_sheet()


def factor_po_status_daily_export(export_date: date = None):
    gsheet_id = _get_gsheet_id()
    export_date = export_date or date.today()

    FactorPoStatusExport(
        export_date=export_date, gsheet_id=gsheet_id, sites_by_brand_week=_get_factor_sites(export_date)
    ).write_to_sheet()


def _execute_export(model: Type[ImtBaseExport], export_date: date, gsheet_id: str, week_day: int):
    sites_by_brand_week = _get_sites_by_brand_week_for_week_day(export_date, week_day)

    model(export_date=export_date, gsheet_id=gsheet_id, sites_by_brand_week=sites_by_brand_week).write_to_sheet()


def _get_sites_by_brand_week_for_week_day(
    export_date: date, week_day: int
) -> dict[tuple[BRAND, ScmWeek], Iterable[SITE]]:
    export_week = ScmWeek.from_date(export_date)
    requested_weeks = (export_week, export_week + 1) if week_day == Weekday.TUE else (export_week,)
    return _get_sites_by_brand_week(requested_weeks)


def _get_sites_by_brand_week(
    weeks: Iterable[ScmWeek], *, exclude_non_3pls: bool = True
) -> dict[tuple[BRAND, ScmWeek], Iterable[SITE]]:
    sites_by_brand_week = {}
    for week in weeks:
        for brand in brand_admin.get_brand_ids(week):
            if brand == BRAND_FJ:
                continue
            sites_by_brand_week[(brand, week)] = [
                site
                for site, dto in dc_admin.get_enabled_sites(week, brand).items()
                if not exclude_non_3pls or dto.is_3pl
            ]
    return sites_by_brand_week


def _get_factor_sites(export_date: date) -> dict[tuple[BRAND, ScmWeek], Iterable[SITE]]:
    export_week = ScmWeek.from_date(export_date, week_config=brand_admin.get_week_config(BRAND_FJ))
    export_week_day = export_date.weekday()

    if export_week_day >= Weekday.FRI or export_week_day <= Weekday.MON:
        export_weeks = (export_week - 1, export_week)
    elif export_week_day == Weekday.TUE:
        export_weeks = (export_week, export_week + 1)
    else:
        export_weeks = (export_week,)

    return {(BRAND_FJ, week): dc_admin.get_enabled_sites(week, BRAND_FJ).keys() for week in export_weeks}
