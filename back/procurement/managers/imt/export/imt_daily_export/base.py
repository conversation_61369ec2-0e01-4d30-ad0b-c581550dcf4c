import abc
import dataclasses
from collections.abc import Iterable
from datetime import date
from typing import TypeVar

from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.core.dates import ScmWeek
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BRAND, SITE
from procurement.managers.admin import brand_admin
from procurement.managers.depletion.constants import PROD_DAYS_AFTER, PROD_DAYS_AHEAD
from procurement.managers.exports.base import BaseExport, BaseExportModel

BRAND_WEEK = tuple[BRAND, ScmWeek]  # pylint: disable=invalid-name


InputModel = TypeVar("InputModel")


@dataclasses.dataclass
class ImtExportContext:
    export_date: date
    week: ScmWeek
    brand: str
    site: str


class ImtBaseExport(BaseExport):
    def __init__(self, export_date: date, gsheet_id: str, sites_by_brand_week: dict[BRAND_WEEK, Iterable[SITE]]):
        self.export_date = export_date
        self.gsheet_id = gsheet_id
        self.sites_by_brand_week = sites_by_brand_week

    def _get_data(self) -> list[BaseExportModel]:
        items = []
        for (brand, week), sites in self.sites_by_brand_week.items():
            for site, raw_items in self._get_dashboard_raw_data(week=week, brand=brand, sites=sites).items():
                context = ImtExportContext(export_date=self.export_date, week=week, brand=brand, site=site)
                items.extend(
                    self.result_model.from_dashboard_item(item=raw_item, context=context)
                    for raw_item in raw_items
                    if self._should_include(week, raw_item)
                )

        return items

    @abc.abstractmethod
    def _get_dashboard_raw_data(self, week: ScmWeek, brand: str, sites: Iterable[SITE]) -> dict[SITE, list[InputModel]]:
        pass

    @staticmethod
    def _order_data(items: Iterable[BaseExportModel]) -> Iterable[BaseExportModel]:
        return sorted(
            sorted(sorted(items, key=lambda item: item.sku_code), key=lambda item: item.brand, reverse=True),
            key=lambda item: item.site,
        )

    @abc.abstractmethod
    def _should_include(self, week: ScmWeek, item: InputModel) -> bool:
        pass

    @staticmethod
    def _is_packaging(item: InputModel) -> bool:
        return item.category == IngredientCategory.PACKAGING


def get_production_day_for_week(week: ScmWeek, day: date) -> int:
    return (day - week.get_first_day()).days


@request_cache
def get_production_days_for_brand(week: ScmWeek, brand: str) -> dict[int, str]:
    titles = ScmWeek.get_weekday_titles(
        week_config=brand_admin.get_brands(week)[brand].scm_week_config,
        extend_left=PROD_DAYS_AHEAD,
        extend_right=PROD_DAYS_AFTER,
    )
    return dict(enumerate(titles, start=-PROD_DAYS_AHEAD))
