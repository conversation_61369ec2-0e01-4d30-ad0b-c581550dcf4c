from __future__ import annotations

import abc
import dataclasses
from collections.abc import Iterable
from functools import cached_property
from typing import Type

from procurement.core.dates import ScmWeek
from procurement.core.typing import BRAND, SITE, SKU_CODE
from procurement.data.googlesheet_model.imt_daily_export import PackagingDepletionExportSheet, WeeklyLinerGuidance
from procurement.managers.exports.base import BaseExportModel
from procurement.managers.imt.v2.depletion.packaging.core import PackagingDepletionModel
from procurement.managers.imt.v2.depletion.packaging.dashboard import PackagingDepletionDashboard

from .base import ImtBaseExport, ImtExportContext


@dataclasses.dataclass
class PackagingDepletionExportData(BaseExportModel):
    brand: str
    site: str
    sku_code: str
    sku_name: str
    buyer: str
    commodity_group: str
    beginning_on_hand_today: int
    demand_today: int
    incoming_pos_today: int
    beginning_on_hand_tomorrow: int
    demand_tomorrow: int
    units_short: int
    critical_deliveries: int

    @staticmethod
    def from_dashboard_item(item: PackagingDepletionModel, context: ImtExportContext) -> PackagingDepletionExportData:
        export_date_index = (context.export_date - context.week.get_first_day()).days
        export_date_daily = item.daily_needs[export_date_index]
        day_after_export_date_daily = item.daily_needs[export_date_index + 1]
        export_date_on_hand = export_date_daily.on_hand or export_date_daily.on_hand_projected or 0
        day_after_export_date_on_hand = day_after_export_date_daily.get_calculated_on_hand()
        return PackagingDepletionExportData(
            brand=context.brand,
            site=context.site,
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            buyer=item.buyer,
            commodity_group=item.commodity_group,
            beginning_on_hand_today=export_date_on_hand,
            demand_today=export_date_daily.demand,
            incoming_pos_today=export_date_daily.incoming,
            beginning_on_hand_tomorrow=day_after_export_date_on_hand,
            demand_tomorrow=day_after_export_date_daily.demand,
            units_short=export_date_on_hand - export_date_daily.demand,
            critical_deliveries=day_after_export_date_on_hand
            - day_after_export_date_daily.demand
            - export_date_daily.incoming,
        )


class BasePackagingDepletionExport(ImtBaseExport, metaclass=abc.ABCMeta):
    def _get_dashboard_raw_data(
        self, week: ScmWeek, brand: str, sites: Iterable[SITE]
    ) -> dict[SITE, list[PackagingDepletionModel]]:
        result = {}
        for site in sites:
            site_results = []
            for item in PackagingDepletionDashboard(week, site, brand).get_result():
                if item.is_liner_group:
                    site_results.extend(item.liners)
                else:
                    site_results.append(item)
            result[site] = site_results
        return result


class PackagingDepletionExport(BasePackagingDepletionExport):
    gsheet_model = PackagingDepletionExportSheet()
    result_model = PackagingDepletionExportData

    def _get_data(self) -> list[PackagingDepletionExportData]:
        return [item for brand in self.brands for item in self._get_export_items(brand)]

    def _get_export_items(self, brand: str) -> Iterable[PackagingDepletionExportData]:
        result = []
        for site, items in self._get_dashboard_raw_data(
            self.first_week, brand, self.sites_by_brand_week[(brand, self.first_week)]
        ).items():
            context = ImtExportContext(export_date=self.export_date, week=self.first_week, brand=brand, site=site)
            self._add_next_week_demands(items, brand, site)
            result.extend(
                PackagingDepletionExportData.from_dashboard_item(item, context)
                for item in items
                if self._should_include(self.first_week, item)
            )

        return result

    def _add_next_week_demands(self, items: list[PackagingDepletionModel], brand: str, site: str) -> None:
        for item in items:
            item.daily_needs[-1].demand += self.next_week_demands.get((brand, site, item.sku_code), 0)

    @cached_property
    def weeks(self) -> list[ScmWeek]:
        return list(sorted({week for _, week in self.sites_by_brand_week.keys()}))

    @cached_property
    def first_week(self) -> ScmWeek | None:
        return next(iter(self.weeks), None)

    @cached_property
    def brands(self) -> list[str]:
        return [brand for brand, week in self.sites_by_brand_week.keys() if week == self.first_week]

    @cached_property
    def next_week_demands(self) -> dict[tuple[BRAND, SITE, SKU_CODE], int]:
        if len(self.weeks) != 2:
            return {}
        next_week = self.first_week + 1
        next_week_demands = {}
        for brand in self.brands:
            for site, items in self._get_dashboard_raw_data(
                next_week, brand, self.sites_by_brand_week[(brand, next_week)]
            ).items():
                for item in items:
                    next_week_demands[(brand, site, item.sku_code)] = item.daily_needs[0].demand
        return next_week_demands

    def _should_include(self, week: ScmWeek, item: PackagingDepletionModel) -> bool:
        return self._has_overconsumption(week, item) or self._require_attention(week, item)

    def _has_overconsumption(self, week: ScmWeek, item: PackagingDepletionModel) -> bool:
        export_date_index = (self.export_date - week.get_first_day()).days
        export_date_daily = item.daily_needs[export_date_index]
        return export_date_daily.demand > (export_date_daily.on_hand or 0)

    def _require_attention(self, week: ScmWeek, item: PackagingDepletionModel) -> bool:
        export_date_index = (self.export_date - week.get_first_day()).days
        export_date_daily = item.daily_needs[export_date_index]
        day_after_export_date_daily = item.daily_needs[export_date_index + 1]
        return export_date_daily.incoming > 0 and (
            ((day_after_export_date_daily.on_hand or 0) - day_after_export_date_daily.demand * 1.25)
            - export_date_daily.incoming
            > 0
        )


@dataclasses.dataclass
class WeeklyLinerGuidanceExportData(BaseExportModel):
    brand: str
    site: str
    commodity_group: str
    sku_name: str
    sku_code: str
    buyer: str
    eow_inventory: int
    overconsumption: int

    @staticmethod
    def from_dashboard_item(item: PackagingDepletionModel, context: ImtExportContext) -> WeeklyLinerGuidanceExportData:
        return WeeklyLinerGuidanceExportData(
            brand=context.brand,
            site=context.site,
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            buyer=item.buyer,
            commodity_group=item.commodity_group,
            eow_inventory=item.weekly_overview.estimated_eow_inventory,
            overconsumption=item.weekly_overview.estimated_overconsumption,
        )


class WeeklyLinerGuidanceExport(BasePackagingDepletionExport):
    gsheet_model = WeeklyLinerGuidance()
    result_model = WeeklyLinerGuidanceExportData

    def _should_include(self, week: ScmWeek, item: PackagingDepletionModel) -> bool:
        return True


def get_packaging_depletion_export_model() -> Type[PackagingDepletionExport]:
    return PackagingDepletionExport
