import abc
from collections.abc import Collection, Iterable
from datetime import date, datetime, time, timedelta
from enum import StrEnum
from typing import Any

from procurement.client.googlesheets.googlesheet_utils import GSHEET_DATE_FORMAT
from procurement.client.mailing import mail_service
from procurement.constants.hellofresh_constant import BRAND_EP, BRAND_FJ, BRAND_GC, BRAND_HF
from procurement.core.config_utils import config
from procurement.core.dates import ScmWeek, ScmWeekConfig
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.exports.po_filter import AllPosWithTransfersFilter, PoFilter, PoFilterBase, PoFilterManager
from procurement.managers.imt.purchase_order import po_status
from procurement.managers.imt.purchase_order.po_status import PoStatusResult
from procurement.managers.pimt.export.pimt_po_status_export import (
    PimtCustomFinancialData,
    PimtDailyFinancialData,
    PimtFinanceExportData,
    PimtMonthlyFinancialData,
    PimtWeeklyFinancialData,
)

from .export_base import ExportCsv, ExportSheet, ExportWorkbook, Row, TabularData


class FinanceExportConfig(StrEnum):
    FACTOR = "factor"
    CORE = "core"


CONFIG_TO_BRANDS = {
    FinanceExportConfig.FACTOR: (BRAND_FJ,),
    FinanceExportConfig.CORE: (BRAND_HF, BRAND_EP, BRAND_GC),
}


class FinanceExportData(TabularData):
    def __init__(self, export_data: Collection[PoStatusResult]):
        super().__init__("PO Status", export_data)

    @property
    def column_headers(self) -> list[Row]:
        return [
            Row(
                content=[
                    "Brand",
                    "DC",
                    "Supplier",
                    "PO #",
                    "SKU",
                    "SKU Name",
                    "Category",
                    "Purchasing UOM",
                    "Scheduled Delivery Date",
                    "PO Status",
                    "Receiving Variance (units)",
                    "Order Size",
                    "Order Unit",
                    "Case Price",
                    "Case Size",
                    "Quantity Ordered",
                    "Quantity Received",
                    "Cases Received",
                    "Date Received",
                    "Total Price PO",
                    "Total Price Received",
                    "Emergency Reason",
                    "Assigned Buyer",
                    "PO Buyer",
                    "Ship Method",
                ]
            )
        ]

    def _get_row_data(self, raw_item: PoStatusResult) -> tuple[Any, ...]:
        return (
            raw_item.brand,
            raw_item.site,
            raw_item.supplier,
            raw_item.po_number,
            raw_item.sku_code,
            raw_item.sku_name,
            raw_item.category,
            raw_item.purchasing_unit,
            raw_item.scheduled_delivery_date.strftime(GSHEET_DATE_FORMAT),
            raw_item.po_status,
            raw_item.receive_variance,
            raw_item.order_size,
            raw_item.order_unit,
            raw_item.case_price,
            raw_item.case_size,
            raw_item.quantity_ordered,
            raw_item.quantity_received,
            raw_item.cases_received,
            raw_item.date_received,
            raw_item.total_price,
            raw_item.total_price_received,
            raw_item.emergency_reason,
            raw_item.buyers,
            raw_item.po_buyer,
            raw_item.ship_method,
        )


class FinancialData(abc.ABC):
    def __init__(self, po_filter: str):
        self.po_filter: PoFilterBase = PoFilterManager.get_filter(po_filter)

    def get_data(self, brands: Iterable[str]) -> Collection[PoStatusResult]:
        items = []
        for brand in brands:
            week_config = brand_admin.get_week_config(brand)
            date_from, date_to = self._get_date_range(week_config)
            week = ScmWeek.from_date(date_to - timedelta(days=1), week_config)
            sites = dc_admin.get_enabled_sites(week, brand).keys()
            items.extend(
                self.po_filter.apply(
                    po_status.get_po_status_by_receiving_date_range(
                        brand=brand, sites=sites, date_from=date_from, date_to=date_to
                    )
                )
            )
            if isinstance(self.po_filter, AllPosWithTransfersFilter):
                items.extend(
                    po_status.get_to_status_by_receiving_date_range(
                        brand=brand, sites=sites, date_from=date_from, date_to=date_to
                    )
                )
        items.sort(key=lambda item: item.date_received or date.max)
        return items

    @abc.abstractmethod
    def _get_date_range(self, week_config: ScmWeekConfig | None = None) -> tuple[datetime, datetime]:
        pass

    @property
    @abc.abstractmethod
    def export_period(self) -> str:
        pass

    @property
    @abc.abstractmethod
    def title_date(self) -> str:
        pass

    @property
    @abc.abstractmethod
    def body_date(self) -> str:
        pass

    @property
    @abc.abstractmethod
    def attachment_date(self) -> str:
        pass


class DailyFinancialData(FinancialData):
    def __init__(self, export_date: datetime, po_filter: str):
        super().__init__(po_filter)
        self.export_date = export_date - timedelta(days=1)

    def _get_date_range(self, week_config: ScmWeekConfig | None = None) -> tuple[datetime, datetime]:
        return self.export_date, self.export_date + timedelta(days=1)

    @property
    def export_period(self) -> str:
        return "Daily"

    @property
    def title_date(self) -> str:
        return self.export_date.strftime(GSHEET_DATE_FORMAT)

    @property
    def body_date(self) -> str:
        return self.title_date

    @property
    def attachment_date(self) -> str:
        return self.title_date


class WeeklyFinancialData(FinancialData):
    def __init__(self, po_filter: str, week_from: ScmWeek, week_to: ScmWeek | None = None):
        super().__init__(po_filter)
        self.week_from = week_from
        self.week_to = week_to or week_from

    def _get_date_range(self, week_config: ScmWeekConfig | None = None) -> tuple[datetime, datetime]:
        return (
            datetime.combine(self.week_from.get_first_day(week_config), time()),
            datetime.combine(self.week_to.get_last_production_day(week_config), time()) + timedelta(days=1),
        )

    @property
    def export_period(self) -> str:
        return "Weekly"

    @property
    def title_date(self) -> str:
        if self.week_from == self.week_to:
            return f"W{self.week_from.week}"
        return f"W{self.week_from.week}-W{self.week_to.week}"

    @property
    def body_date(self) -> str:
        if self.week_from == self.week_to:
            return str(self.week_from)
        return str(self.week_from) + " - " + str(self.week_to)

    @property
    def attachment_date(self) -> str:
        return self.body_date


class MonthlyFinancialData(FinancialData):
    def __init__(self, export_date: datetime, po_filter: str):
        super().__init__(po_filter)
        self.date_from = export_date.replace(day=1)
        self.date_to = (self.date_from + timedelta(days=31)).replace(day=1)

    def _get_date_range(self, week_config: ScmWeekConfig | None = None) -> tuple[datetime, datetime]:
        return self.date_from, self.date_to

    @property
    def export_period(self) -> str:
        return "Monthly"

    @property
    def title_date(self) -> str:
        return f"{self.date_from.strftime(GSHEET_DATE_FORMAT)}-{self.date_to.strftime(GSHEET_DATE_FORMAT)}"

    @property
    def body_date(self) -> str:
        return self.title_date

    @property
    def attachment_date(self) -> str:
        return self.date_to.strftime(GSHEET_DATE_FORMAT)


class CustomFinancialData(FinancialData):
    def __init__(self, date_from: datetime, date_to: datetime, po_filter: str):
        super().__init__(po_filter)
        self.date_from = date_from
        self.date_to = date_to

    def _get_date_range(self, week_config: ScmWeekConfig | None = None) -> tuple[datetime, datetime]:
        return self.date_from, self.date_to + timedelta(days=1)

    @property
    def export_period(self) -> str:
        return "Custom"

    @property
    def title_date(self) -> str:
        return f"{self.date_from.strftime(GSHEET_DATE_FORMAT)}-{self.date_to.strftime(GSHEET_DATE_FORMAT)}"

    @property
    def body_date(self) -> str:
        return self.title_date

    @property
    def attachment_date(self) -> str:
        return self.date_to.strftime(GSHEET_DATE_FORMAT)


def send_monthly_financial_email(config_name: FinanceExportConfig, export_date: date) -> None:
    data = MonthlyFinancialData(
        export_date=datetime.combine(export_date, time()), po_filter=PoFilter.ALL_POS_WITH_TRANSFERS
    )
    _send_email(data, config_name)


def send_weekly_financial_email(config_name: FinanceExportConfig, week: ScmWeek):
    data = WeeklyFinancialData(week_from=week, po_filter=PoFilter.ALL_POS_WITH_TRANSFERS)
    _send_email(data, config_name)


def send_daily_financial_email(config_name: FinanceExportConfig, export_date: date) -> None:
    data = DailyFinancialData(
        export_date=datetime.combine(export_date, time()), po_filter=PoFilter.ALL_POS_WITH_TRANSFERS
    )
    _send_email(data, config_name)


def generate_daily_financial_excel(
    day: date, brands: Iterable[str], po_filter: str, include_warehouses: bool = False
) -> bytes:
    export_date = datetime.combine(day, time())
    data = [FinanceExportData(DailyFinancialData(export_date=export_date, po_filter=po_filter).get_data(brands))]
    if include_warehouses:
        data.append(
            PimtFinanceExportData(PimtDailyFinancialData(export_date=export_date, po_filter=po_filter).get_data())
        )
    return _build_excel(data)


def generate_monthly_financial_excel(
    day: date, brands: Iterable[str], po_filter: str, include_warehouses: bool = False
) -> bytes:
    export_date = datetime.combine(day, time())
    data = [FinanceExportData(MonthlyFinancialData(export_date=export_date, po_filter=po_filter).get_data(brands))]
    if include_warehouses:
        data.append(
            PimtFinanceExportData(PimtMonthlyFinancialData(export_date=export_date, po_filter=po_filter).get_data())
        )
    return _build_excel(data)


def generate_weekly_financial_excel(
    week: ScmWeek, brands: Iterable[str], po_filter: str, include_warehouses: bool = False
) -> bytes:
    data = [FinanceExportData(WeeklyFinancialData(week_from=week, po_filter=po_filter).get_data(brands))]
    if include_warehouses:
        data.append(PimtFinanceExportData(PimtWeeklyFinancialData(week_from=week, po_filter=po_filter).get_data()))
    return _build_excel(data)


def generate_weekly_range_financial_excel(
    week_from: ScmWeek, week_to: ScmWeek, brands: Iterable[str], po_filter: str, include_warehouses: bool = False
) -> bytes:
    data = [
        FinanceExportData(
            WeeklyFinancialData(week_from=week_from, week_to=week_to, po_filter=po_filter).get_data(brands)
        )
    ]
    if include_warehouses:
        data.append(
            PimtFinanceExportData(
                PimtWeeklyFinancialData(week_from=week_from, week_to=week_to, po_filter=po_filter).get_data()
            )
        )
    return _build_excel(data)


def generate_custom_financial_excel(
    date_from: date, date_to: date, brands: Iterable[str], po_filter: str, include_warehouses: bool = False
) -> bytes:
    _date_from = datetime.combine(date_from, time())
    _date_to = datetime.combine(date_to, time())
    data = [
        FinanceExportData(
            CustomFinancialData(date_from=_date_from, date_to=_date_to, po_filter=po_filter).get_data(brands)
        )
    ]
    if include_warehouses:
        data.append(
            PimtFinanceExportData(
                PimtCustomFinancialData(date_from=_date_from, date_to=_date_to, po_filter=po_filter).get_data()
            )
        )
    return _build_excel(data)


def _send_email(export_data: FinancialData, config_name: FinanceExportConfig) -> None:
    mail_service.send_mail(
        mailing_config=config["mailing"]["finance"][config_name],
        file=ExportCsv(FinanceExportData(export_data.get_data(CONFIG_TO_BRANDS[config_name]))).write().binary,
        formatters={
            "export_period": export_data.export_period,
            "title_date": export_data.title_date,
            "body_date": export_data.body_date,
            "attachment_date": export_data.attachment_date,
        },
    )


def _build_excel(tabs: Iterable[TabularData]) -> bytes:
    doc = ExportWorkbook()
    for tab in tabs:
        doc.add_sheet(ExportSheet(tab))
    return doc.binary
