import logging
from datetime import date, timedelta

from procurement.managers.datasync.framework.runner import <PERSON>llel<PERSON><PERSON><PERSON><PERSON><PERSON>
from procurement.managers.datasync.framework.step import Step

from .forms import discard_export, inventory_pull_put_export, po_void_export, receipt_override_export

logger = logging.getLogger(__name__)

S3_EXPORT_JOB_NAME = "Send manual forms export to S3"


def submit_manual_forms_export_to_s3(export_date: date = None):
    export_date = export_date or date.today()
    export_date_str = (export_date - timedelta(hours=1)).isoformat()
    runner = ParallelJobRunner(S3_EXPORT_JOB_NAME).add_next_step(
        Step(
            "Export discard CSV to S3",
            discard_export.upload_discard_csv,
            export_date=export_date_str,
        ),
        Step(
            "Export Receipt Override CSV to S3",
            receipt_override_export.upload_receipt_override_csv,
            export_date=export_date_str,
        ),
        Step(
            "Export PO Void CSV to S3",
            po_void_export.upload_po_void_csv,
            export_date=export_date_str,
        ),
        Step(
            "Export inventory pull put CSV to S3",
            inventory_pull_put_export.upload_pull_put_export_csv,
            export_date=export_date_str,
        ),
    )
    runner.submit_if_not_in_progress()
