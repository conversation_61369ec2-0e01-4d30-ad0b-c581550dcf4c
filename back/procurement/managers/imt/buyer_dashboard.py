import dataclasses
from collections import defaultdict
from collections.abc import Iterable

from procurement.constants.ordering import NOT_DELIVERED_PAST_DUE, RECEIVED_OVER, RECEIVED_UNDER
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BRAND, SITE, SKU_CODE
from procurement.managers.admin import dc_admin
from procurement.managers.depletion.constants import NO_IMPACT
from procurement.repository.inventory import buyer as buyer_repo

from .ingredients_depletion.core import IngredientDepletionContext
from .ingredients_depletion.result import SiteIngredientDepletionResult
from .purchase_order import po_status


@dataclasses.dataclass
class BuyerDashboardModel:
    week: ScmWeek
    depletion: dict[str, dict[str, list[SiteIngredientDepletionResult]]]
    po_status: dict[str, dict[str, list]] | None = None


def get_buyer_dashboard(buyer_id: int, week: ScmWeek) -> list[BuyerDashboardModel]:
    buyers_skus_by_brand_site = get_buyers_skus(buyer_id)
    next_week = week + 1
    return [
        BuyerDashboardModel(
            week=week,
            depletion=_get_depletion_dashboard(week, buyers_skus_by_brand_site),
            po_status=_get_po_status_dashboard(week, buyers_skus_by_brand_site),
        ),
        BuyerDashboardModel(week=next_week, depletion=_get_depletion_dashboard(next_week, buyers_skus_by_brand_site)),
    ]


def get_buyer_critical_dashboard(week: ScmWeek) -> BuyerDashboardModel:
    buyers_skus_by_brand_site = get_buyers_skus(context.get_request_context().user_info.user_id)
    return BuyerDashboardModel(week=week, depletion=_get_depletion_dashboard(week, buyers_skus_by_brand_site))


def get_buyers_skus(buyer_id: int) -> dict[BRAND, dict[SITE, set[SKU_CODE]]]:
    data = defaultdict(lambda: defaultdict(set))
    for row in buyer_repo.get_buyers_skus(buyer_id):
        data[row.brand][row.site].add(row.sku_code)
    return data


def _get_depletion_dashboard(week: ScmWeek, buyers_skus_by_brand_site: dict[BRAND, dict[SITE, set[SKU_CODE]]]):
    result = {}
    for brand in buyers_skus_by_brand_site.keys():
        skus_by_site = buyers_skus_by_brand_site[brand]
        available_sites = _get_available_sites(week, brand, skus_by_site.keys())
        depletion_data = _get_buyer_ingredient_depletion(
            week=week, sites=available_sites, brand=brand, skus_by_site=skus_by_site
        )
        result[brand] = depletion_data
    return result


def _get_buyer_ingredient_depletion(
    week: ScmWeek,
    sites: Iterable[str],
    brand: str,
    skus_by_site: dict[SITE, set[SKU_CODE]],
) -> dict[str, list[SiteIngredientDepletionResult]]:
    res = {}
    for site in sites:
        res[site] = [
            item
            for item in SiteIngredientDepletionResult.build_results(
                context=IngredientDepletionContext(week=week, site=site, brand=brand, sku_codes=skus_by_site[site])
            )
            if item.supplement_need != NO_IMPACT
        ]
    return res


def _get_po_status_dashboard(
    week: ScmWeek, buyers_skus_by_brand_site: dict[BRAND, dict[SITE, set[SKU_CODE]]]
) -> dict[str, dict[str, list[po_status.PoStatusResult]]]:
    result = {}
    for brand in buyers_skus_by_brand_site.keys():
        skus_by_site = buyers_skus_by_brand_site[brand]
        available_sites = _get_available_sites(week, brand, skus_by_site.keys())
        status_data = po_status.get_po_status_for_buyer_dashboard(
            week=week, sites=available_sites, brand=brand, skus_by_site=skus_by_site
        )
        for site, data in status_data.items():
            status_data[site] = [
                item for item in data if item.po_status in (NOT_DELIVERED_PAST_DUE, RECEIVED_OVER, RECEIVED_UNDER)
            ]
        result[brand] = status_data
    return result


def get_today_po_status_dashboard(buyer_id: int):
    po_items = po_status.get_today_pos_buyer_dashboard(buyer_id)
    return po_items


def _get_available_sites(week: ScmWeek, brand: str, buyers_sites: Iterable[str]) -> list[str]:
    return [site for site in dc_admin.get_enabled_sites(week=week, brand=brand) if site in buyers_sites]
