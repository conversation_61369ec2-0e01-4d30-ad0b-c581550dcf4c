from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE, SKU_UUID
from procurement.data.dto.sku import SkuDetail, SkuMeta
from procurement.repository.ordering import culinary_sku


@request_cache
def get_sku_meta(
    site: str | None, *, sku_codes: frozenset[str] = None, ignore_category: IngredientCategory | None = None
) -> tuple[SkuMeta, ...]:
    return culinary_sku.get_sku_meta(site, sku_codes, ignore_category)


def get_sku_meta_by_code(
    site: str | None, sku_codes: frozenset[str] = None, ignore_category: IngredientCategory | None = None
) -> dict[SKU_CODE, SkuMeta]:
    return {sku.sku_code: sku for sku in get_sku_meta(site=site, sku_codes=sku_codes, ignore_category=ignore_category)}


def get_sku_details_by_uuid() -> dict[SKU_UUID, SkuDetail]:
    return {sku.sku_uuid: sku for sku in culinary_sku.get_skus_details()}
