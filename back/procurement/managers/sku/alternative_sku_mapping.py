from typing import Type

from procurement.client.googlesheets import googlesheet_utils
from procurement.constants.hellofresh_constant import BRAND_GC, BRAND_HF
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE
from procurement.data.dto.sku import AlternativeSkuMapping
from procurement.data.googlesheet_model.alternative_sku_mapping import (
    AlternativeSkuRow,
    OrganicSkuSheetModel,
    PackagingSkuSheetModel,
    RwaSkuSheetModel,
)
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel, PackagingSkuModel
from procurement.managers.admin import gsheet_admin
from procurement.managers.datasync.framework import warnings
from procurement.repository.inventory import alternative_sku_mapping as alternative_sku_mapping_repo


def _update_alternative_sku_mapping(
    model: Type[PackagingSkuModel] | Type[OrganicSkuModel], data: list[AlternativeSkuRow]
) -> None:
    current_mapping = set(alternative_sku_mapping_repo.get_alternative_sku_mapping(model))
    new_mapping = set()
    for row in data:
        try:
            googlesheet_utils.validate_required_fields(row, ["original_sku_code", "interchangable_sku_code"])
            new_mapping.add(
                AlternativeSkuMapping(
                    original_sku_code=row.original_sku_code,
                    interchangable_sku_code=row.interchangable_sku_code,
                )
            )
        except ValueError as exc:
            warnings.notify_sheet_error(row, exc, "Error when importing SKU mapping", skipped=True)
    to_delete = current_mapping - new_mapping
    to_add = new_mapping - current_mapping
    if to_delete:
        alternative_sku_mapping_repo.delete_alternative_sku_mapping(to_delete, model)
    if to_add:
        alternative_sku_mapping_repo.add_alternative_sku_mapping(to_add, model)


def update_alternative_sku_mapping(model: Type[PackagingSkuModel] | Type[OrganicSkuModel]):
    gsheet_model = PackagingSkuSheetModel if model == PackagingSkuModel else OrganicSkuSheetModel
    data = gsheet_admin.read_gsheet(gsheet_model(), brand=BRAND_GC)
    _update_alternative_sku_mapping(model, data)


def update_rwa_sku_mapping():
    data = gsheet_admin.read_gsheet(RwaSkuSheetModel(), brand=BRAND_HF)
    _update_alternative_sku_mapping(OrganicSkuModel, data)


@request_cache
def get_all_alternative_sku(model: Type[PackagingSkuModel] | Type[OrganicSkuModel]) -> dict[SKU_CODE, SKU_CODE]:
    sku_mapping = {}
    for mapping in alternative_sku_mapping_repo.get_alternative_sku_mapping(model):
        sku_mapping[mapping.interchangable_sku_code] = mapping.original_sku_code
        sku_mapping[mapping.original_sku_code] = mapping.interchangable_sku_code
    return sku_mapping
