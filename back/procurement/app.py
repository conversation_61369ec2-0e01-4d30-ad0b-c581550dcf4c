from flask import Flask
from flask_compress import Compress
from flask_cors import CORS

from procurement.core import metrics

from . import api
from .api import error_handling
from .application_config import FlaskConfig
from .auth import login
from .auth.common import jwt
from .constants.hellofresh_constant import MARKET_HEADER
from .core.json_serializers import SimpleJsonProvider

Flask.json_provider_class = SimpleJsonProvider


def create_app():
    app = Flask(__name__)
    app.config.from_object(FlaskConfig())

    jwt.init_app(app)
    CORS(app, resources={r"*": {"origins": "*"}}, supports_credentials=True, expose_headers=[MARKET_HEADER])
    auth_bp = login.create_auth()
    app.register_blueprint(auth_bp, url_prefix="/auth/")
    app.register_blueprint(api.register_api())
    app.register_error_handler(Exception, error_handling.generic_handler)
    metrics.init_flask_api(app)

    Compress(app)

    return app
