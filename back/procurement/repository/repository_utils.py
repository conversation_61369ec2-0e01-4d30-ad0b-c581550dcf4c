import logging
from collections.abc import Collection
from decimal import Decimal
from typing import Any, Type

import sqlalchemy as sqla
from sqlalchemy import Case, ColumnElement, Connection, Values
from sqlalchemy.dialects import postgresql as psql_sqla
from sqlalchemy.orm import DeclarativeBase

from procurement.services.database import app_db

logger = logging.getLogger(__name__)

__BATCH_LIMIT = 500
__BATCH_SIZE = 100


class _ComparableNull:
    def __eq__(self, other):
        return False

    def __lt__(self, other):
        return False

    def __le__(self, other):
        return False

    def __ge__(self, other):
        return False

    def __gt__(self, other):
        return True


def safe_bulk_insert_sqla(
    model: Type[DeclarativeBase],
    data: Collection[dict[ColumnElement, Any]],
    keys: list[ColumnElement],
    preserve_fields: list[ColumnElement] | None = None,
    transaction: Connection = None,
):
    if not data:
        return

    updates = {}

    # A sorting of updating entity keys to prevent possible deadlock. For example. Transaction A updates keys 1 and
    # 2. Transaction B updates keys 2 and 1. If they  work simultaneously they could block each other. If
    # transaction B do updates with sorted keys (1,2) then it has a conflict with A but will not block
    # transaction A.
    null_key_val = _ComparableNull()
    data = sorted(data, key=lambda item: tuple(null_key_val if item[key] is None else item[key] for key in keys))
    duplicates = {}

    for item in data:
        key_values = tuple(item[key] for key in keys)
        if key_values in updates:
            duplicates[str(item)] = key_values, item
        updates[key_values] = item

    for key_values, item in duplicates.values():
        logger.warning(
            "Duplicate in bulk insert to %s by keys %s = %s;\n    First entrance: %s;\n            second: %s",
            model,
            keys,
            key_values,
            updates[key_values],
            item,
        )

    query = psql_sqla.insert(model)
    if preserve_fields:
        query = query.on_conflict_do_update(
            index_elements=keys, set_={field: getattr(query.excluded, field.name) for field in preserve_fields}
        )
    else:
        query = query.on_conflict_do_nothing(index_elements=keys)
    app_db.apply_query(query=query.values(list(updates.values())), transaction=transaction)


def make_values(values: Collection, names: Collection[str]) -> Values:
    is_scalar = len(names) == 1
    return Values(
        *(sqla.column(name) for name in names),
        name="_".join(names) + "s",
    ).data([(v,) if is_scalar else v for v in values])


def make_value(value: str | int | float | Decimal | None):
    return sqla.literal_column(str(value) if value is not None else "NULL")


def replace_zero(column: ColumnElement, value: int = 1) -> Case:
    return Case((column == 0, value), else_=column)


def order_number_from_po_number(column: ColumnElement):
    return sqla.func.split_part(column, "_", 1)
