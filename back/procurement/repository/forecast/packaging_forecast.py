import math
from typing import Collection

import sqlalchemy as sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.forecasts.forecasts import Forecast, PackagingForecast, SkuForecast
from procurement.data.models.forecast.demand_pipeline_sku_mapping import DemandPipelineSkuMappingModel
from procurement.data.models.forecast.packaging_long_term_forecast import PackagingLongTermForecastModel
from procurement.data.models.inventory.brand import BrandModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def get_forecasts(site: str, week: ScmWeek, brand: str) -> list[SkuForecast]:
    market = context.get_request_context().market
    forecasts = app_db.select_all(
        sqla.select(DemandPipelineSkuMappingModel.sku_code, PackagingLongTermForecastModel.forecast)
        .join(
            DemandPipelineSkuMappingModel,
            onclause=(DemandPipelineSkuMappingModel.demand_pipeline == PackagingLongTermForecastModel.demand_pipeline),
        )
        .join(BrandModel, onclause=(BrandModel.name == PackagingLongTermForecastModel.brand))
        .where(
            PackagingLongTermForecastModel.site == site,
            PackagingLongTermForecastModel.week == int(week),
            BrandModel.id == brand,
            PackagingLongTermForecastModel.market == market,
        )
    )
    return [SkuForecast(sku_code=item.sku_code, forecast=item.forecast) for item in forecasts]


def get_packaging_forecast_for_weeks(site: str, weeks: list[ScmWeek], brand: str) -> list[Forecast]:
    market = context.get_request_context().market
    forecasts = app_db.select_all(
        sqla.select(
            PackagingLongTermForecastModel.site,
            PackagingLongTermForecastModel.week,
            PackagingLongTermForecastModel.forecast,
            BrandModel.id.label("brand"),
            DemandPipelineSkuMappingModel.sku_code,
        )
        .join(
            DemandPipelineSkuMappingModel,
            onclause=(DemandPipelineSkuMappingModel.demand_pipeline == PackagingLongTermForecastModel.demand_pipeline),
        )
        .join(BrandModel, onclause=(BrandModel.name == PackagingLongTermForecastModel.brand))
        .where(
            PackagingLongTermForecastModel.site == site,
            PackagingLongTermForecastModel.week.in_([int(week) for week in weeks]),
            BrandModel.id == brand,
            PackagingLongTermForecastModel.market == market,
        )
    )
    return [
        Forecast(
            site=item.site,
            scm_week=ScmWeek.from_number(item.week),
            sku_code=item.sku_code,
            forecast=math.ceil(item.forecast),
            brand=item.brand,
        )
        for item in forecasts
    ]


def get_forecasts_by_demand_pipeline(weeks: list[ScmWeek]) -> list[PackagingForecast]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            PackagingLongTermForecastModel.demand_pipeline,
            PackagingLongTermForecastModel.forecast,
            PackagingLongTermForecastModel.site,
            BrandModel.id.label("brand"),
            PackagingLongTermForecastModel.week,
        )
        .join(BrandModel, onclause=(BrandModel.name == PackagingLongTermForecastModel.brand))
        .where(
            PackagingLongTermForecastModel.market == market, PackagingLongTermForecastModel.week.in_(map(int, weeks))
        )
    )

    return [
        PackagingForecast(
            demand_pipeline=item.demand_pipeline,
            forecast=item.forecast,
            site=item.site,
            brand=item.brand,
            week=ScmWeek.from_number(item.week),
        )
        for item in app_db.select_all(query)
    ]


def update_packaging_long_term_forecast(data: Collection[dict]):
    update_weeks = set(item[PackagingLongTermForecastModel.week] for item in data)
    app_db.apply_query(
        sqla.delete(PackagingLongTermForecastModel).where(PackagingLongTermForecastModel.week.in_(update_weeks))
    )
    repository_utils.safe_bulk_insert_sqla(
        model=PackagingLongTermForecastModel,
        data=data,
        keys=[
            PackagingLongTermForecastModel.week,
            PackagingLongTermForecastModel.brand,
            PackagingLongTermForecastModel.site,
            PackagingLongTermForecastModel.market,
            PackagingLongTermForecastModel.demand_pipeline,
        ],
        preserve_fields=[PackagingLongTermForecastModel.forecast],
    )
