import math
from collections.abc import Iterable

import sqlalchemy as sqla
from sqlalchemy import Connection, Row
from sqlalchemy.dialects import postgresql as psql

from procurement.client.slack import alerts
from procurement.client.slack.alerts import OscarPackagingMissingForecastAlert
from procurement.constants.hellofresh_constant import IngredientCategory
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BRAND
from procurement.data.dto.forecasts.forecasts import (
    CanadaForecastRecipes,
    Forecast,
    NextExistingForecastDto,
    SkuForecast,
)
from procurement.data.dto.inventory.hybrid_needs import HybridNeedsDto
from procurement.data.dto.inventory.ingredient import PkgData
from procurement.data.models.forecast.canada_forecast import CanadaForecastModel, CanadaForecastRecipeModel
from procurement.data.models.forecast.oscar import OscarModel
from procurement.data.models.inventory.ingredient import PurchasingCategoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.sku import culinary_sku
from procurement.repository import repository_utils
from procurement.services.database import app_db

COUNT_SKUS_TO_ALERT = 5


def update_data(brand: str, updates: list) -> None:
    if not updates:
        return
    week_dc_to_update = {(u[OscarModel.scm_week], u[OscarModel.dc]) for u in updates}  # about 50-100 items
    week_dc = sqla.tuple_(OscarModel.scm_week, OscarModel.dc)
    app_db.apply_query(sqla.delete(OscarModel).where(week_dc.in_(week_dc_to_update), OscarModel.brand == brand))

    _notify_forecast_oscar_alerts(updates, brand)
    repository_utils.safe_bulk_insert_sqla(
        model=OscarModel,
        data=updates,
        keys=[OscarModel.dc, OscarModel.scm_week, OscarModel.sku_code, OscarModel.brand],
        preserve_fields=[OscarModel.forecast, OscarModel.units, OscarModel.forecast_by_day],
    )


def delete_canada_forecast_data(weeks: list[ScmWeek], sites: list[str], brand: str, trn: Connection) -> None:
    app_db.apply_query(
        sqla.delete(CanadaForecastModel).where(
            CanadaForecastModel.scm_week.in_(map(int, weeks)),
            CanadaForecastModel.site.in_(sites),
            CanadaForecastModel.brand == brand,
        ),
        transaction=trn,
    )


def insert_canada_data(updates: list, transaction: Connection) -> None:
    if not updates:
        return
    repository_utils.safe_bulk_insert_sqla(
        model=CanadaForecastModel,
        data=updates,
        keys=[
            CanadaForecastModel.site,
            CanadaForecastModel.brand,
            CanadaForecastModel.scm_week,
            CanadaForecastModel.sku_code,
            CanadaForecastModel.day,
        ],
        preserve_fields=[CanadaForecastModel.value],
        transaction=transaction,
    )


def delete_canada_recipe_data(weeks: list[ScmWeek], sites: list[str], brand: str, trn: Connection) -> None:
    app_db.apply_query(
        sqla.delete(CanadaForecastRecipeModel).where(
            CanadaForecastRecipeModel.week.in_(map(int, weeks)),
            CanadaForecastRecipeModel.site.in_(sites),
            CanadaForecastRecipeModel.brand == brand,
        ),
        transaction=trn,
    )


def insert_canada_recipe_data(updates: list, transaction: Connection) -> None:
    if not updates:
        return
    query = psql.insert(CanadaForecastRecipeModel).values(updates)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=CanadaForecastRecipeModel.get_primary_key_columns(),
            set_=CanadaForecastRecipeModel.get_excluded_columns(query),
        ),
        transaction=transaction,
    )


def get_canada_recipe_data(week: ScmWeek, site: str, brand: str) -> list[PkgData]:
    query = (
        sqla.select(
            CanadaForecastRecipeModel.sku_code,
            CulinarySkuModel.sku_id,
            CulinarySkuModel.sku_name,
            CanadaForecastRecipeModel.recipe,
            CanadaForecastRecipeModel.recipe_name,
            CanadaForecastRecipeModel.picks,
            PurchasingCategoryModel.name.label("purchasing_category"),
        )
        .join(CulinarySkuModel, onclause=(CanadaForecastRecipeModel.sku_code == CulinarySkuModel.sku_code))
        .join(
            PurchasingCategoryModel,
            isouter=True,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
        )
        .where(
            CanadaForecastRecipeModel.week == int(week),
            CanadaForecastRecipeModel.site == site,
            CanadaForecastRecipeModel.brand == brand,
            CulinarySkuModel.market == context.get_request_context().market,
        )
    )
    return [
        PkgData(
            sku_code=item.sku_code,
            weight_amount=None,
            weight_unit=None,
            storage_location=None,
            allergens=None,
            sku_id=item.sku_id,
            sku_name=item.sku_name,
            code=item.recipe,
            slot=item.recipe,
            meal_name=item.recipe_name,
            picks_2p=item.picks.get("2", 0) if item.picks else 0,
            picks_3p=item.picks.get("3", 0) if item.picks else 0,
            picks_4p=item.picks.get("4", 0) if item.picks else 0,
            picks_6p=item.picks.get("6", 0) if item.picks else 0,
            purcasing_category=item.purchasing_category,
            sub_recipe=None,
        )
        for item in app_db.select_all(query)
    ]


def _notify_forecast_oscar_alerts(valid_items: list[dict], brand: BRAND):
    sku_code_sites = {
        item[OscarModel.sku_code]: item[OscarModel.dc] for item in valid_items if not item[OscarModel.forecast]
    }
    not_packaging_skus = {
        item.sku_code
        for item in culinary_sku.get_sku_meta(site=None, sku_codes=frozenset(sku_code_sites))
        if item.category != IngredientCategory.PACKAGING
    }
    if len(not_packaging_skus) >= COUNT_SKUS_TO_ALERT:
        sites_alert = {site for sku_code, site in sku_code_sites.items() if sku_code in not_packaging_skus}
    else:
        sites_alert = set()
    max_week = max((i[OscarModel.scm_week] for i in valid_items), default=ScmWeek.current_week())
    alerts.preventative_alert(OscarPackagingMissingForecastAlert(sites_alert, brand, max_week))


def get_ingredients_with_forecast(week: ScmWeek, site: str, brand: str) -> list[SkuForecast]:
    return [
        SkuForecast(sku_code=item.sku_code, forecast=item.forecast)
        for item in app_db.select_all(
            sqla.select(OscarModel.sku_code, OscarModel.forecast).where(
                OscarModel.brand == brand,
                OscarModel.dc == site,
                OscarModel.scm_week == str(week),
            )
        )
    ]


def get_canada_ingredients_with_forecast(week: ScmWeek, site: str, brand: str) -> list[SkuForecast]:
    return [
        SkuForecast(sku_code=item.sku_code, forecast=item.forecast)
        for item in app_db.select_all(
            sqla.select(CanadaForecastModel.sku_code, sqla.func.sum(CanadaForecastModel.value).label("forecast"))
            .where(
                CanadaForecastModel.brand == brand,
                CanadaForecastModel.site == site,
                CanadaForecastModel.scm_week == int(week),
            )
            .group_by(CanadaForecastModel.sku_code)
        )
    ]


def get_forecasts(
    weeks: Iterable[ScmWeek] | None = None,
    sites: Iterable[str] | None = None,
    brand: str | None = None,
    sku_codes: set[str] | None = None,
) -> list[Forecast]:
    oscar_query = sqla.select(OscarModel)

    if utils.is_any_collection_empty(sites, weeks, sku_codes, brand):
        return []
    if sites:
        oscar_query = oscar_query.where(OscarModel.dc.in_(sites))
    if weeks:
        oscar_query = oscar_query.where(OscarModel.scm_week.in_(list(map(str, weeks))))
    if brand:
        oscar_query = oscar_query.where(OscarModel.brand == brand)
    if sku_codes:
        oscar_query = oscar_query.where(OscarModel.sku_code.in_(sku_codes))

    return _build_oscar_dto(app_db.select_all(oscar_query))


def get_canada_forecasts(
    weeks: Iterable[ScmWeek] | None = None,
    sites: Iterable[str] | None = None,
    brand: str | None = None,
    sku_codes: set[str] | None = None,
) -> list[Forecast]:
    oscar_query = sqla.select(
        CanadaForecastModel.site,
        CanadaForecastModel.scm_week,
        CanadaForecastModel.sku_code,
        sqla.func.sum(CanadaForecastModel.value).label("forecast"),
        CanadaForecastModel.brand,
    ).group_by(
        CanadaForecastModel.site, CanadaForecastModel.scm_week, CanadaForecastModel.sku_code, CanadaForecastModel.brand
    )

    if utils.is_any_collection_empty(sites, weeks, sku_codes, brand):
        return []
    if sites:
        oscar_query = oscar_query.where(CanadaForecastModel.site.in_(sites))
    if weeks:
        oscar_query = oscar_query.where(CanadaForecastModel.scm_week.in_(list(map(int, weeks))))
    if brand:
        oscar_query = oscar_query.where(CanadaForecastModel.brand == brand)
    if sku_codes:
        oscar_query = oscar_query.where(CanadaForecastModel.sku_code.in_(sku_codes))

    return _build_canada_forecast_dto(app_db.select_all(oscar_query))


def _build_oscar_dto(items: list[Row]) -> list[Forecast]:
    return [
        Forecast(
            site=item.dc,
            scm_week=ScmWeek.from_str(item.scm_week),
            sku_code=item.sku_code,
            forecast=math.ceil(item.forecast),
            brand=item.brand,
        )
        for item in items
    ]


def _build_canada_forecast_dto(items: list[Row]) -> list[Forecast]:
    return [
        Forecast(
            site=item.site,
            scm_week=ScmWeek.from_number(item.scm_week),
            sku_code=item.sku_code,
            forecast=math.ceil(item.forecast),
            brand=item.brand,
        )
        for item in items
    ]


def get_next_existing_forecast(
    sites: Iterable[DcConfig], week: ScmWeek, sku_code: str | None = None
) -> list[NextExistingForecastDto]:
    subq = (
        sqla.select(
            OscarModel.sku_code,
            sqla.func.min(OscarModel.scm_week).label("week"),
            OscarModel.dc,
        )
        .where(
            OscarModel.forecast > 0,
            OscarModel.scm_week > str(week),
            OscarModel.dc.in_([site.sheet_name for site in sites]),
        )
        .group_by(OscarModel.sku_code, OscarModel.dc)
    )
    if sku_code:
        subq = subq.where(OscarModel.sku_code == sku_code)

    subq = subq.subquery()

    items = app_db.select_all(
        sqla.select(
            OscarModel.sku_code,
            OscarModel.scm_week,
            OscarModel.dc,
            sqla.func.sum(OscarModel.forecast).label("forecast"),
        )
        .join(
            subq,
            onclause=sqla.and_(
                subq.c.sku_code == OscarModel.sku_code, subq.c.week == OscarModel.scm_week, subq.c.dc == OscarModel.dc
            ),
        )
        .group_by(OscarModel.sku_code, OscarModel.scm_week, OscarModel.dc)
    )
    return [
        NextExistingForecastDto(
            site=item.dc,
            scm_week=ScmWeek.from_str(item.scm_week),
            sku_code=item.sku_code,
            forecast=math.ceil(item.forecast),
        )
        for item in items
    ]


def get_canada_forecasts_by_date(brand: str, site: str, weeks: list[ScmWeek]) -> list[HybridNeedsDto]:
    query = sqla.select(
        CanadaForecastModel.sku_code, CanadaForecastModel.scm_week, CanadaForecastModel.day, CanadaForecastModel.value
    ).where(
        CanadaForecastModel.brand == brand,
        CanadaForecastModel.site == site,
        CanadaForecastModel.scm_week.in_(map(int, weeks)),
    )
    return [
        HybridNeedsDto(
            sku_code=forecast.sku_code,
            scm_week=ScmWeek.from_number(forecast.scm_week),
            day=forecast.day,
            value=forecast.value,
        )
        for forecast in app_db.select_all(query)
    ]


def get_canada_forecast_recipes(week: ScmWeek, site: str, brand: str):
    query = (
        sqla.select(
            CanadaForecastRecipeModel.sku_code, sqla.func.array_agg(CanadaForecastRecipeModel.recipe).label("recipes")
        )
        .where(
            CanadaForecastRecipeModel.week == int(week),
            CanadaForecastRecipeModel.site == site,
            CanadaForecastRecipeModel.brand == brand,
        )
        .group_by(CanadaForecastRecipeModel.sku_code)
    )
    return [CanadaForecastRecipes(sku_code=item.sku_code, recipes=item.recipes) for item in app_db.select_all(query)]


def get_next_existing_canada_forecast(
    sites: Iterable[DcConfig], week: ScmWeek, sku_code: str | None = None
) -> list[NextExistingForecastDto]:
    subq = (
        sqla.select(
            CanadaForecastModel.sku_code,
            sqla.func.min(CanadaForecastModel.scm_week).label("week"),
            CanadaForecastModel.site,
        )
        .where(
            CanadaForecastModel.value > 0,
            CanadaForecastModel.scm_week > int(week),
            CanadaForecastModel.site.in_([site.sheet_name for site in sites]),
        )
        .group_by(CanadaForecastModel.sku_code, CanadaForecastModel.site)
    )
    if sku_code:
        subq = subq.where(CanadaForecastModel.sku_code == sku_code)

    subq = subq.subquery()

    items = app_db.select_all(
        sqla.select(
            CanadaForecastModel.sku_code,
            CanadaForecastModel.scm_week,
            CanadaForecastModel.site,
            sqla.func.sum(CanadaForecastModel.value).label("value"),
        )
        .join(
            subq,
            onclause=sqla.and_(
                subq.c.sku_code == CanadaForecastModel.sku_code,
                subq.c.week == CanadaForecastModel.scm_week,
                subq.c.site == CanadaForecastModel.site,
            ),
        )
        .group_by(CanadaForecastModel.sku_code, CanadaForecastModel.scm_week, CanadaForecastModel.site)
    )
    return [
        NextExistingForecastDto(
            site=item.site,
            scm_week=ScmWeek.from_number(item.scm_week),
            sku_code=item.sku_code,
            forecast=math.ceil(item.value),
        )
        for item in items
    ]
