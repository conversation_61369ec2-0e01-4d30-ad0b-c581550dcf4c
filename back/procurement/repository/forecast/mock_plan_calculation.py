from collections.abc import Iterable
from decimal import Decimal

import sqlalchemy as sqla

from procurement.constants.hellofresh_constant import PRODUCTION_TYPE_ASSEMBLY
from procurement.core.dates import ScmWeek
from procurement.data.dto.forecasts.mock_plan import MockPlan
from procurement.data.models.forecast.mock_plan_calculation import MockPlanModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository import repository_utils
from procurement.services.database import app_db


def update_mock_plan(data: list[dict]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=MockPlanModel,
        data=data,
        keys=[MockPlanModel.week, MockPlanModel.brand, MockPlanModel.site, MockPlanModel.production_type],
        preserve_fields=[MockPlanModel.weights],
    )


def get_mock_plans(dc_object: DcConfig, weeks: Iterable[ScmWeek]) -> list[MockPlan]:
    return [
        MockPlan(week=ScmWeek.from_number(item.week), production_type=item.production_type, weights=item.weights)
        for item in app_db.select_all(
            sqla.select(MockPlanModel.week, MockPlanModel.production_type, MockPlanModel.weights).where(
                MockPlanModel.site == dc_object.sheet_name,
                MockPlanModel.brand == dc_object.brand,
                MockPlanModel.week.in_([w.to_number_format() for w in weeks]),
            )
        )
    ]


def get_assembly_mock_plan(site: str, week: ScmWeek, brand: str) -> list[Decimal] | None:
    return app_db.select_scalar(
        sqla.select(MockPlanModel.weights).where(
            MockPlanModel.site == site,
            MockPlanModel.brand == brand,
            MockPlanModel.week == week.to_number_format(),
            MockPlanModel.production_type == PRODUCTION_TYPE_ASSEMBLY,
        )
    )
