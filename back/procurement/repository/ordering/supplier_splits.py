from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.data.dto.ordering.supplier_split import SupplierSplit
from procurement.data.models.ordering.supplier_splits import SupplierSplitsModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_supplier_splits(data: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=SupplierSplitsModel,
        data=data,
        keys=[
            SupplierSplitsModel.dc,
            SupplierSplitsModel.sku_code,
            SupplierSplitsModel.start_week,
            SupplierSplitsModel.end_week,
            SupplierSplitsModel.market,
        ],
        preserve_fields=[
            SupplierSplitsModel.lead_time,
            SupplierSplitsModel.min_order_quantity,
            SupplierSplitsModel.incremental_order_quantity,
            SupplierSplitsModel.start_date,
            SupplierSplitsModel.end_date,
        ],
    )


def get_moq_ioq_values(sku_codes: list[str], bob_codes: set[str]) -> list[SupplierSplit]:
    query = (
        sqla.select(
            sqla.func.max(SupplierSplitsModel.min_order_quantity).label("moq"),
            sqla.func.max(SupplierSplitsModel.incremental_order_quantity).label("ioq"),
            SupplierSplitsModel.sku_code,
        )
        .where(SupplierSplitsModel.dc.in_(bob_codes), SupplierSplitsModel.sku_code.in_(sku_codes))
        .group_by(SupplierSplitsModel.sku_code)
    )
    return [SupplierSplit(moq=item.moq, ioq=item.ioq, sku_code=item.sku_code) for item in app_db.select_all(query)]
