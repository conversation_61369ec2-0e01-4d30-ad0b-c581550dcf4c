from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.dto.ordering.shipment import PostalAddress, Shipment
from procurement.data.models.ordering.shipment import POShipmentModel
from procurement.services.database import app_db


def upsert_po_shipment(shipment: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(POShipmentModel).values(shipment)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[POShipmentModel.po_number],
            set_={
                POShipmentModel.load_number: query.excluded.load_number,
                POShipmentModel.pallet_count: query.excluded.pallet_count,
                POShipmentModel.carrier_name: query.excluded.carrier_name,
                POShipmentModel.region_code: query.excluded.region_code,
                POShipmentModel.postal_code: query.excluded.postal_code,
                POShipmentModel.administrative_area: query.excluded.administrative_area,
                POShipmentModel.locality: query.excluded.locality,
                POShipmentModel.address_lines: query.excluded.address_lines,
                POShipmentModel.organization: query.excluded.organization,
                POShipmentModel.appointment_time: query.excluded.appointment_time,
                POShipmentModel.execution_event: query.excluded.execution_event,
            },
        )
    )


def upsert_appointment_time_in_po_shipment(appointment: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(POShipmentModel)
    app_db.apply_query(
        query.values(appointment).on_conflict_do_update(
            index_elements=[POShipmentModel.po_number],
            set_={
                POShipmentModel.appointment_time: query.excluded.appointment_time,
                POShipmentModel.appointment_state: query.excluded.appointment_state,
                POShipmentModel.batch_sequence: query.excluded.batch_sequence,
            },
        )
    )


def get_shipment_by_po_number(po_number: str) -> int | None:
    return app_db.select_scalar(
        # returns -1 in case if the shipment record exists but no appointment time updates happened yet (in this
        # case the batch_sequence is NULL because this field is not available in the shipment topic and only present
        # in the appointment updates)
        sqla.select(sqla.func.coalesce(POShipmentModel.batch_sequence, -1)).where(
            POShipmentModel.po_number == po_number
        )
    )


def get_shipment_by_po_numbers(po_numbers: Iterable[str]) -> list[Shipment]:
    query = sqla.select(
        POShipmentModel.po_number,
        POShipmentModel.load_number,
        POShipmentModel.pallet_count,
        POShipmentModel.carrier_name,
        POShipmentModel.appointment_time,
        POShipmentModel.execution_event,
        POShipmentModel.region_code,
        POShipmentModel.postal_code,
        POShipmentModel.administrative_area,
        POShipmentModel.locality,
        POShipmentModel.address_lines,
        POShipmentModel.organization,
    ).where(POShipmentModel.po_number.in_(po_numbers))

    return [
        Shipment(
            po_number=item.po_number,
            load_number=item.load_number,
            pallet_count=item.pallet_count,
            carrier_name=item.carrier_name,
            origin_location=PostalAddress(
                region_code=item.region_code,
                postal_code=item.postal_code,
                administrative_area=item.administrative_area,
                locality=item.locality,
                address_lines=item.address_lines,
                organization=item.organization,
            ),
            appointment_time=item.appointment_time,
            execution_event=item.execution_event,
        )
        for item in app_db.select_all(query)
    ]
