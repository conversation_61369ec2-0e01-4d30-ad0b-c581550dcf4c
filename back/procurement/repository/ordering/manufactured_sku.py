from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla
from sqlalchemy.orm import InstrumentedAttribute

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.ordering.manufactured_skus import (
    ManufacturedSku,
    ManufacturedSkuNameCode,
    ManufacturedSkuPart,
)
from procurement.data.models.ordering.manufactured_sku import (
    ManufacturedSkuModel,
    ManufacturedSkuPartModel,
    ManufacturedSkuWeekModel,
)
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_manufactured_sku(data: dict[InstrumentedAttribute, Any]) -> None:
    query = psql_sqla.insert(ManufacturedSkuModel).values(data)

    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=ManufacturedSkuModel.get_primary_key_columns(),
            set_=ManufacturedSkuModel.get_excluded_columns(query),
        )
    )


def upsert_manufactured_sku_parts(data: list[dict[InstrumentedAttribute, Any]]) -> None:
    query = psql_sqla.insert(ManufacturedSkuPartModel).values(data)

    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=ManufacturedSkuPartModel.get_primary_key_columns(),
            set_=ManufacturedSkuPartModel.get_excluded_columns(query),
        )
    )


def update_manufactured_sku_weeks(manufactured_uuid: str, updates: list[dict[ColumnElement, Any]]) -> None:
    delete_manufactured_sku_weeks(manufactured_uuid)
    repository_utils.safe_bulk_insert_sqla(
        model=ManufacturedSkuWeekModel,
        data=updates,
        keys=[
            ManufacturedSkuWeekModel.manufactured_uuid,
            ManufacturedSkuWeekModel.week,
        ],
    )


def delete_manufactured_sku_weeks(manufactured_uuid: str) -> None:
    app_db.apply_query(
        sqla.delete(ManufacturedSkuWeekModel).where(
            ManufacturedSkuWeekModel.manufactured_uuid == manufactured_uuid,
        )
    )


def get_manufactured_recipe_skus_by_week(week: ScmWeek) -> list[ManufacturedSkuNameCode]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            ManufacturedSkuModel.manufactured_code,
            ManufacturedSkuModel.manufactured_name,
        )
        .join(
            ManufacturedSkuWeekModel,
            onclause=ManufacturedSkuModel.manufactured_uuid == ManufacturedSkuWeekModel.manufactured_uuid,
        )
        .where(
            ManufacturedSkuModel.market == market,
            ManufacturedSkuWeekModel.week == int(week),
        )
    )
    return [
        ManufacturedSkuNameCode(manufactured_name=item.manufactured_name, manufactured_code=item.manufactured_code)
        for item in app_db.select_all(query)
    ]


def get_manufactured_skus() -> list[ManufacturedSku]:
    market = context.get_request_context().market
    query = sqla.select(
        ManufacturedSkuModel.manufactured_uuid,
        ManufacturedSkuModel.manufactured_code,
        ManufacturedSkuModel.manufactured_name,
    ).where(ManufacturedSkuModel.market == market)
    return [
        ManufacturedSku(
            manufactured_uuid=item.manufactured_uuid,
            manufactured_code=item.manufactured_code,
            manufactured_name=item.manufactured_name,
        )
        for item in app_db.select_all(query)
    ]


def get_manufactured_skus_name_code() -> list[ManufacturedSkuNameCode]:
    market = context.get_request_context().market
    query = sqla.select(ManufacturedSkuModel.manufactured_name, ManufacturedSkuModel.manufactured_code).where(
        ManufacturedSkuModel.market == market
    )
    return [
        ManufacturedSkuNameCode(manufactured_name=item.manufactured_name, manufactured_code=item.manufactured_code)
        for item in app_db.select_all(query)
    ]


def get_manufactured_sku_parts() -> list[ManufacturedSkuPart]:
    query = sqla.select(
        ManufacturedSkuPartModel.root_uuid,
        ManufacturedSkuPartModel.part_uuid,
        ManufacturedSkuPartModel.unit_of_measure,
        ManufacturedSkuPartModel.quantity,
        ManufacturedSkuPartModel.is_manufactured,
    )
    return [
        ManufacturedSkuPart(
            root_uuid=item.root_uuid,
            part_uuid=item.part_uuid,
            unit_of_measure=UnitOfMeasure.inexact_value_of(item.unit_of_measure),
            quantity=item.quantity,
            is_manufactured=item.is_manufactured,
        )
        for item in app_db.select_all(query)
    ]
