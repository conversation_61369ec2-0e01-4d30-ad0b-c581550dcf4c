from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.constants.hellofresh_constant import POAcknowledgementState, PoAckUnitOfMeasure
from procurement.core.typing import PO_NUMBER
from procurement.data.dto.ordering.po_acknowledgement import PoAcknowledgement
from procurement.data.models.ordering.po_acknowledgement import POAcknowledgementLineItemsModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def get_po_acknowledgements_proposed_data(po_numbers: Iterable[PO_NUMBER]) -> list[PoAcknowledgement]:
    query = (
        sqla.select(
            POAcknowledgementLineItemsModel.unit_of_measure,
            POAcknowledgementLineItemsModel.size,
            POAcknowledgementLineItemsModel.packing_size,
            POAcknowledgementLineItemsModel.promised_date,
            POAcknowledgementLineItemsModel.sku_code,
            POAcknowledgementLineItemsModel.state,
            PurchaseOrderModel.po_number,
        )
        .join(PurchaseOrderModel, onclause=(PurchaseOrderModel.po_uuid == POAcknowledgementLineItemsModel.po_uuid))
        .where(PurchaseOrderModel.po_number.in_(po_numbers))
    )
    return [
        PoAcknowledgement(
            unit_of_measure=PoAckUnitOfMeasure(item.unit_of_measure),
            size=item.size,
            packing_size=item.packing_size or 0,
            promised_date=item.promised_date,
            sku_code=item.sku_code,
            state=POAcknowledgementState(item.state),
            po_number=item.po_number,
        )
        for item in app_db.select_all(query)
    ]


def upsert_po_acknowledgement_and_line_items(po_line_items: list[dict[ColumnElement, Any]]):
    repository_utils.safe_bulk_insert_sqla(
        model=POAcknowledgementLineItemsModel,
        data=po_line_items,
        keys=[POAcknowledgementLineItemsModel.po_uuid, POAcknowledgementLineItemsModel.sku_code],
        preserve_fields=[
            POAcknowledgementLineItemsModel.order_number,
            POAcknowledgementLineItemsModel.sku_id,
            POAcknowledgementLineItemsModel.state,
            POAcknowledgementLineItemsModel.number_of_pallets,
            POAcknowledgementLineItemsModel.unit_of_measure,
            POAcknowledgementLineItemsModel.size,
            POAcknowledgementLineItemsModel.packing_size,
            POAcknowledgementLineItemsModel.promised_date,
        ],
    )
