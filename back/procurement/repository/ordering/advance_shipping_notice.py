from collections.abc import Iterable

import sqlalchemy as sqla

from procurement.core.typing import PO_NUMBER
from procurement.data.dto.ordering.asn import AdvanceShippingNoticeDto
from procurement.data.models.ordering.advance_shipping_notice import AdvanceShippingNoticeModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_advance_shipping_notice(advance_shipping_notices: list):
    repository_utils.safe_bulk_insert_sqla(
        model=AdvanceShippingNoticeModel,
        data=advance_shipping_notices,
        keys=[AdvanceShippingNoticeModel.po_uuid, AdvanceShippingNoticeModel.sku_code],
        preserve_fields=[
            AdvanceShippingNoticeModel.shipment_time,
            AdvanceShippingNoticeModel.order_number,
            AdvanceShippingNoticeModel.planned_delivery_time,
            AdvanceShippingNoticeModel.shipping_state,
            AdvanceShippingNoticeModel.size,
            AdvanceShippingNoticeModel.packing_size,
            AdvanceShippingNoticeModel.unit_measure,
        ],
    )


def get_advance_shipping_notice_by_po_numbers(po_numbers: Iterable[PO_NUMBER]) -> list[AdvanceShippingNoticeDto]:
    query = (
        sqla.select(
            PurchaseOrderModel.po_number,
            AdvanceShippingNoticeModel.sku_code,
            AdvanceShippingNoticeModel.shipment_time,
            AdvanceShippingNoticeModel.planned_delivery_time,
            AdvanceShippingNoticeModel.shipping_state,
            AdvanceShippingNoticeModel.unit_measure,
            AdvanceShippingNoticeModel.size,
            AdvanceShippingNoticeModel.packing_size,
        )
        .join(PurchaseOrderModel, onclause=(PurchaseOrderModel.po_uuid == AdvanceShippingNoticeModel.po_uuid))
        .where(PurchaseOrderModel.po_number.in_(po_numbers))
    )
    return [
        AdvanceShippingNoticeDto(
            po_number=item.po_number,
            sku_code=item.sku_code,
            shipment_time=item.shipment_time,
            planned_delivery_time=item.planned_delivery_time,
            shipping_state=item.shipping_state,
            packing_size=item.packing_size,
            unit_measure=item.unit_measure,
            size=item.size,
        )
        for item in app_db.select_all(query)
    ]
