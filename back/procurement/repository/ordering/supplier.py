from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql

from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import MARKET
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.admin import dc_admin
from procurement.services.database import app_db


def upsert_supplier(supplier: dict[ColumnElement, Any]) -> None:
    last_update = app_db.select_scalar(
        sqla.select(SupplierModel.last_updated).where(SupplierModel.id == supplier[SupplierModel.id])
    )
    if last_update and last_update > supplier[SupplierModel.last_updated]:
        return
    query = psql.insert(SupplierModel).values(supplier)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=SupplierModel.get_primary_key_columns(),
            set_=SupplierModel.get_excluded_columns(query),
        )
    )


def get_all_supplier_names() -> set[str]:
    market = context.get_request_context().market
    return set(_get_all_suppliers()[market].values())


def get_supplier_codes_by_names(supplier_names: list[str]) -> set[int]:
    market = context.get_request_context().market
    query = sqla.select(SupplierModel.code).where(
        SupplierModel.name.in_(supplier_names), SupplierModel.market == market
    )
    return {supplier.code for supplier in app_db.apply_query(query)}


def get_supplier_name_by_code(market: str | None = None) -> dict[int, str]:
    market = market or context.get_request_context().market
    return _get_all_suppliers()[market]


def get_supplier_names_by_search_key(search_key: str) -> list[str]:
    market = context.get_request_context().market
    query = sqla.select(SupplierModel.name).where(
        SupplierModel.name.ilike(f"%{search_key}%"), SupplierModel.market == market
    )
    return [supplier.name for supplier in app_db.apply_query(query)]


@request_cache
def _get_all_suppliers() -> dict[MARKET, dict[int, str]]:
    res = {}
    for market in dc_admin.get_markets():
        query = sqla.select(SupplierModel.code, SupplierModel.name).where(SupplierModel.market == market.code)
        res[market.code] = {supplier.code: supplier.name for supplier in app_db.apply_query(query)}
    return res
