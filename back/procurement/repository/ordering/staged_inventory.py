from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.data.dto.ordering.staged_inventory import StagedInventory
from procurement.data.models.ordering.staged_inventory import StagedInventoryModel
from procurement.services.database import app_db


def insert_staged_inventory(data: list[dict[ColumnElement, Any]]) -> None:
    query = psql_sqla.insert(StagedInventoryModel).values(data)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=StagedInventoryModel.get_primary_key_columns(),
            set_=StagedInventoryModel.get_excluded_columns(query),
        )
    )


def get_staged_inventory(week: ScmWeek, site: str) -> list[StagedInventory]:
    return [
        StagedInventory(sub_recipe_id=item.sub_recipe_id, quantity=item.quantity)
        for item in app_db.select_all(
            sqla.select(StagedInventoryModel.sub_recipe_id, StagedInventoryModel.quantity).where(
                StagedInventoryModel.week == int(week), StagedInventoryModel.site == site
            )
        )
    ]
