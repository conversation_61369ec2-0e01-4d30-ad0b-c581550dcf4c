import dataclasses
from collections.abc import Iterable

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select

from procurement.constants.hellofresh_constant import BACKHAUL, FUTURE_WEEK_PULL_REASONS, GENERAL_SUPPLIER, HF_INVENTORY
from procurement.core.config_utils import killswitch
from procurement.core.request_utils import context
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
)
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.ordering.context import BasePOFiltersContext


@dataclasses.dataclass(frozen=True)
class PoFilters:
    category_filter: frozenset[str] = None
    group_filter: frozenset[str] = None
    include_empty_categories: bool = False
    is_future_pull: bool = None
    filter_general_supplier: bool = False
    filter_hf_supplier: bool = False

    def apply(self, site: DcConfig, query: Select, sku_code_column: ColumnElement = None) -> Select:
        base_po_filters = BasePOFiltersContext(
            category_filter=self.category_filter,
            group_filter=self.group_filter,
            include_empty_categories=self.include_empty_categories,
        )
        query = filter_categories(query, sku_code_column, site, base_po_filters)

        reason_filter = None
        if self.is_future_pull:
            reason_filter = PurchaseOrderModel.emergency_reason.in_(FUTURE_WEEK_PULL_REASONS)
        if self.is_future_pull is False:  # is not applied when is_future_pull flag is not set explicitly
            reason_filter = sqla.not_(PurchaseOrderModel.emergency_reason.in_(FUTURE_WEEK_PULL_REASONS))
        if reason_filter is not None and killswitch.transfer_orders_enabled:
            reason_filter |= PurchaseOrderModel.emergency_reason.is_(None)
        if reason_filter is not None:
            query = query.where(reason_filter)
        if self.filter_general_supplier:
            query = filter_general_supplier(query)
        if self.filter_hf_supplier:
            query = filter_hf_supplier(query)

        return query


def filter_ot(ot_query: Select, dc_object: DcConfig) -> Select:
    if dc_object and dc_object.bob_code:
        dc_filter = PurchaseOrderModel.bob_code == dc_object.bob_code
        if dc_object.ot_emergency_reason:
            dc_filter |= PurchaseOrderModel.emergency_reason == dc_object.ot_emergency_reason
        ot_query = ot_query.where(dc_filter)
    return ot_query


def filter_general_supplier(ot_query: Select) -> Select:
    return ot_query.where(
        sqla.or_(PurchaseOrderModel.emergency_reason != BACKHAUL, SupplierModel.name != GENERAL_SUPPLIER)
    )


def filter_hf_supplier(ot_query: Select) -> Select:
    return ot_query.where(
        sqla.or_(
            SupplierModel.name.is_(None),
            sqla.not_(SupplierModel.name.endswith(HF_INVENTORY)),
        )
    )


def filter_categories(
    query: Select, sku_code_column: ColumnElement, site: DcConfig, base_filters: BasePOFiltersContext
) -> Select:
    if base_filters.category_filter:
        query = _filter_category(
            query, sku_code_column, base_filters.category_filter, base_filters.include_empty_categories
        )

    if base_filters.group_filter:
        query = _filter_group(
            query, site, sku_code_column, base_filters.group_filter, base_filters.include_empty_categories
        )

    return query


def _filter_category(
    query: Select, sku_code_column: ColumnElement, category_filter: Iterable[str], include_empty_categories: bool
) -> Select:
    market = context.get_request_context().market
    where_clause = PurchasingCategoryModel.name.in_(category_filter)
    if include_empty_categories:
        where_clause = sqla.or_(where_clause, PurchasingCategoryModel.name.is_(None))
    return (
        query.join(
            CulinarySkuModel,
            isouter=True,
            onclause=sqla.and_(sku_code_column == CulinarySkuModel.sku_code, CulinarySkuModel.market == market),
        )
        .join(
            PurchasingCategoryModel,
            isouter=True,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
        )
        .where(where_clause)
    )


def _filter_group(
    query: Select,
    site: DcConfig,
    sku_code_column: ColumnElement,
    group_filter: frozenset[str],
    include_empty_categories: bool,
) -> Select:
    market = context.get_request_context().market
    if not site:
        raise ValueError("To use group filter, pass site object")
    where_clause = CommodityGroupModel.group_name.in_(group_filter)
    if include_empty_categories:
        where_clause = sqla.or_(where_clause, CommodityGroupModel.group_name.is_(None))
    return (
        query.join(
            IngredientSiteCommodityGroupModel,
            isouter=True,
            onclause=sqla.and_(
                sku_code_column == IngredientSiteCommodityGroupModel.sku_code,
                IngredientSiteCommodityGroupModel.site == site.sheet_name,
            ),
        )
        .join(
            CommodityGroupModel,
            isouter=True,
            onclause=(CommodityGroupModel.id == IngredientSiteCommodityGroupModel.commodity_group_id),
        )
        .where(IngredientSiteCommodityGroupModel.market == market, where_clause)
    )
