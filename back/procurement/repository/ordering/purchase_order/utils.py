from collections import defaultdict
from collections.abc import Iterable

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select

from procurement.constants.hellofresh_constant import FUTURE_WEEK_PULL_REASONS, MANY_SUPPLIERS
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.typing import BOB_CODE, PO_NUMBER, PO_SKU_KEY
from procurement.data.dto.ordering.purchase_order.meta import PoMeta
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.data.models.ordering.shipment import POShipmentModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.data.models.social.distribution_center import DcModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db


def get_po_meta_by_week(week: ScmWeek, ot_dcs: Iterable[str], po_numbers: Iterable[str] = None) -> list[PoMeta]:
    future_week = week + 1
    query = (
        sqla.select(
            PurchaseOrderModel.brand,
            PurchaseOrderModel.week,
            PurchaseOrderModel.po_number,
            PurchaseOrderModel.emergency_reason,
            PurchaseOrderModel.shipping_method,
            POShipmentModel.carrier_name,
            DcModel.name.label("dc"),
            CulinarySkuModel.sku_code,
            CulinarySkuModel.sku_name,
        )
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(DcModel, onclause=(PurchaseOrderModel.bob_code == DcModel.bob_code))
        .join(POShipmentModel, isouter=True, onclause=(PurchaseOrderModel.po_number == POShipmentModel.po_number))
        .where(
            DcModel.name.in_(ot_dcs)
            & (
                (PurchaseOrderModel.week == str(week))
                | (
                    (PurchaseOrderModel.week == str(future_week))
                    & PurchaseOrderModel.emergency_reason.in_(FUTURE_WEEK_PULL_REASONS)
                )
            )
        )
    )

    if po_numbers is not None:
        return [
            _build_po_meta(item)
            for item in app_db.select_all(query.where(PurchaseOrderModel.po_number.in_(po_numbers)))
        ]
    return [_build_po_meta(item) for item in app_db.select_all(query)]


def _build_po_meta(item: Row) -> PoMeta:
    return PoMeta(
        brand=item.brand,
        week=item.week,
        po_number=item.po_number,
        emergency_reason=item.emergency_reason,
        shipping_method=item.shipping_method,
        carrier_name=item.carrier_name,
        ot_dc=item.dc,
        sku_code=item.sku_code,
        sku_name=item.sku_name,
    )


def get_pos_by_week_ot_dc(week: ScmWeek, ot_dc: str) -> Select:
    future_week = week + 1
    query = (
        sqla.select(PurchaseOrderModel.po_number)
        .join(DcModel, onclause=(PurchaseOrderModel.bob_code == DcModel.bob_code))
        .where(
            (DcModel.name == ot_dc)
            & (
                (PurchaseOrderModel.week == str(week))
                | (
                    (PurchaseOrderModel.week == str(future_week))
                    & (PurchaseOrderModel.emergency_reason.in_(FUTURE_WEEK_PULL_REASONS))
                )
            )
        )
    )
    return query


def get_pos_by_week_brand_site(week: ScmWeek, brand: str, dc_object: DcConfig) -> list[PO_NUMBER]:
    query = (
        sqla.select(PurchaseOrderModel.po_number)
        .distinct()
        .where(
            PurchaseOrderModel.bob_code == dc_object.bob_code,
            PurchaseOrderModel.brand == brand,
            PurchaseOrderModel.week == str(week),
        )
    )
    return [item.po_number for item in app_db.select_all(query)]


def get_transfer_orders_suppliers(to_numbers: Iterable[PO_NUMBER]) -> dict[PO_SKU_KEY, list[str]]:
    query = (
        sqla.select(PurchaseOrderModel.po_number, CulinarySkuModel.sku_code, SupplierModel.name.label("supplier_name"))
        .join(TransferOrderSkuModel)
        .join(CulinarySkuModel, onclause=TransferOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(SupplierModel, onclause=TransferOrderSkuModel.original_supplier_id == SupplierModel.id, isouter=True)
        .where(PurchaseOrderModel.po_number.in_(to_numbers))
    )
    res = defaultdict(list)
    for row in app_db.select_all(query):
        res[(row.po_number, row.sku_code)].append(row.supplier_name)
    return res


def get_supplier_from_po_row(po_row, to_suppliers: list[str] | None = None) -> str | None:
    supplier = po_row.supplier
    if po_row.supplier is None and po_row.source_bob_code and killswitch.transfer_orders_enabled:
        supplier = MANY_SUPPLIERS
    if to_suppliers and killswitch.transfer_orders_enabled and len(set(to_suppliers)) == 1:
        supplier = to_suppliers[0]
    return supplier


def join_shipment(ot_query: Select, po_number_field: ColumnElement) -> Select:
    return ot_query.join(POShipmentModel, onclause=(po_number_field == POShipmentModel.po_number), isouter=True)


def get_po_bob_code(po_number: str) -> BOB_CODE | None:
    return app_db.select_scalar(
        sqla.select(PurchaseOrderModel.bob_code).where(PurchaseOrderModel.po_number == po_number)
    )
