from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Connection
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.services.database import app_db


def upsert_pos(pos: list[dict[ColumnElement, Any]], transaction: Connection = None) -> None:
    query = psql_sqla.insert(PurchaseOrderModel)
    query = query.on_conflict_do_update(
        index_elements=[PurchaseOrderModel.po_uuid],
        set_={
            PurchaseOrderModel.bob_code: query.excluded.bob_code,
            PurchaseOrderModel.brand: query.excluded.brand,
            PurchaseOrderModel.po_number: query.excluded.po_number,
            PurchaseOrderModel.supplier_code: query.excluded.supplier_code,
            PurchaseOrderModel.ot_po_status: query.excluded.ot_po_status,
            PurchaseOrderModel.is_sent: query.excluded.is_sent,
            PurchaseOrderModel.week: query.excluded.week,
            PurchaseOrderModel.order_date: query.excluded.order_date,
            PurchaseOrderModel.delivery_time_start: query.excluded.delivery_time_start,
            PurchaseOrderModel.emergency_reason: query.excluded.emergency_reason,
            PurchaseOrderModel.ordered_by: query.excluded.ordered_by,
            PurchaseOrderModel.ot_last_updated: query.excluded.ot_last_updated,
            PurchaseOrderModel.shipping_method: query.excluded.shipping_method,
            PurchaseOrderModel.deleted: query.excluded.deleted,
        },
    ).values(pos)
    app_db.apply_query(query, transaction=transaction)


def upsert_po_sku(po_skus: list[dict[ColumnElement, Any]], transaction: Connection = None) -> None:
    query = psql_sqla.insert(PurchaseOrderSkuModel)
    query = query.on_conflict_do_update(
        index_elements=[PurchaseOrderSkuModel.po_uuid, PurchaseOrderSkuModel.sku_uuid],
        set_={
            PurchaseOrderSkuModel.order_size: query.excluded.order_size,
            PurchaseOrderSkuModel.order_unit: query.excluded.order_unit,
            PurchaseOrderSkuModel.case_price: query.excluded.case_price,
            PurchaseOrderSkuModel.case_size: query.excluded.case_size,
            PurchaseOrderSkuModel.case_unit: query.excluded.case_unit,
            PurchaseOrderSkuModel.quantity: query.excluded.quantity,
            PurchaseOrderSkuModel.buffer: query.excluded.buffer,
            PurchaseOrderSkuModel.total_price: query.excluded.total_price,
        },
    ).values(po_skus)
    app_db.apply_query(query, transaction=transaction)


def insert_to_sku(to_skus: list[dict[ColumnElement, Any]], transaction: Connection = None) -> None:
    query = psql_sqla.insert(TransferOrderSkuModel)
    query = query.on_conflict_do_update(
        index_elements=TransferOrderSkuModel.get_primary_key_columns(),
        set_={
            TransferOrderSkuModel.order_size: query.excluded.order_size,
            TransferOrderSkuModel.order_unit: query.excluded.order_unit,
            TransferOrderSkuModel.case_price: query.excluded.case_price,
            TransferOrderSkuModel.case_size: query.excluded.case_size,
            TransferOrderSkuModel.case_unit: query.excluded.case_unit,
            TransferOrderSkuModel.quantity: query.excluded.quantity,
            TransferOrderSkuModel.total_price: query.excluded.total_price,
            TransferOrderSkuModel.original_po_number: query.excluded.original_po_number,
            TransferOrderSkuModel.original_supplier_code: query.excluded.original_supplier_code,
            TransferOrderSkuModel.original_lot_code: query.excluded.original_lot_code,
        },
    ).values(to_skus)
    app_db.apply_query(query, transaction=transaction)


def delete_to_sku(to_uuid: str, transaction: Connection = None) -> None:
    app_db.apply_query(
        sqla.delete(TransferOrderSkuModel).where(TransferOrderSkuModel.po_uuid == to_uuid), transaction=transaction
    )


def delete_pos(order_numbers: Iterable[str], transaction: Connection = None) -> None:
    app_db.apply_query(
        sqla.delete(PurchaseOrderModel).where(PurchaseOrderModel.order_number.in_(order_numbers)),
        transaction=transaction,
    )
