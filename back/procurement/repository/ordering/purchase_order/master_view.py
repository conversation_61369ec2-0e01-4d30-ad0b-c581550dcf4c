from collections.abc import Iterable, Set
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select, Subquery
from sqlalchemy.sql.base import ReadOnlyColumnCollection
from sqlalchemy.sql.elements import KeyedColumnElement, Label

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.ordering.purchase_order.common import PoSku
from procurement.data.dto.ordering.shipment import PostalAddress, Shipment
from procurement.data.models.highjump.highjump import HJReceiptsModel
from procurement.data.models.inventory.grn import GoodsReceiptNoteModel, LegacyHjGoodsReceiptNoteModel
from procurement.data.models.inventory.receive import ReceivingModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.data.models.ordering.shipment import AppointmentStateEnum, POShipmentModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.data.models.social.distribution_center import DcModel
from procurement.data.models.social.user import UserModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.ordering import PurchaseOrder
from procurement.repository import highjump as hj_repo
from procurement.repository import repository_utils
from procurement.repository.inventory import grn
from procurement.repository.inventory import receiving as receiving_repo
from procurement.repository.inventory.context import TimeRangeContext
from procurement.services.database import app_db

from . import filtering
from . import utils as po_utils
from .filtering import PoFilters

HJReceiptsSqla = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else HJReceiptsModel


def get_scheduled_weeks_by_sku_codes_query(
    skus: Select, dc_object: DcConfig, date_from: datetime, date_to: datetime
) -> tuple[ScmWeek, ...]:
    fields = (PurchaseOrderModel.week,)
    query = _get_scheduled_pos_by_sku_codes_query(
        skus.subquery(), dc_object, fields, TimeRangeContext(date_from=date_from, date_to=date_to)
    )[0].distinct()
    return tuple(ScmWeek.from_str(r.week) for r in app_db.select_all(query))


def get_po_skus_by_sku_codes_query(skus: Select, dc_object: DcConfig, date_from: datetime, date_to: datetime) -> Select:
    fields = (PurchaseOrderModel.po_number, PurchaseOrderModel.order_number, CulinarySkuModel.sku_code)
    return _get_scheduled_pos_by_sku_codes_query(
        skus.subquery(), dc_object, fields, TimeRangeContext(date_from=date_from, date_to=date_to)
    )[0]


def get_purchase_orders_by_sku_codes(
    skus: Select, dc_object: DcConfig, date_from: datetime, date_to: datetime, with_shipment: bool = False
) -> list[PurchaseOrder]:
    fields = _complete_purchase_order_columns(with_shipment)
    query, po_fields = _get_scheduled_pos_by_sku_codes_query(
        skus.subquery(), dc_object, fields, TimeRangeContext(date_from=date_from, date_to=date_to)
    )
    if with_shipment:
        query = po_utils.join_shipment(query, po_fields.po_number)
    return _po_query_to_purchase_orders(query, with_shipment)


def _get_scheduled_pos_by_sku_codes_query(
    skus: Subquery,
    dc_object: DcConfig,
    fields: Iterable[ColumnElement | Label],
    time_range_context: TimeRangeContext,
) -> (Select, ReadOnlyColumnCollection[str, KeyedColumnElement[Any]]):
    """
    Use with caution! Result of skus select should not be too large.
    Filtering by SKU codes with an actual query is expensive due to the way postgres builds it,
    so using the hack with "OFFSET 0" to force postgres to do PO filtering first.
    :return: Tuple of the query and fields from nested PO query in case if any further joining is needed.
    Fields can be accessed as
    query, po_fields = _get_scheduled_pos_by_sku_codes_query(...)
    po_fields.sku_uuid
    """
    subquery_fields = {}
    other_fields = []
    subquery_tables = (
        PurchaseOrderModel.__table__,
        PurchaseOrderSkuModel.__table__,
        SupplierModel.__table__,
        DcModel.__table__,
    )
    for field in fields:
        if next(p.table for p in field.proxy_set if p.table is not None) in subquery_tables:
            subquery_fields[field.name] = field
        else:
            other_fields.append(field)

    explicit_po_subquery = (
        sqla.select(*{**subquery_fields, PurchaseOrderSkuModel.sku_uuid.name: PurchaseOrderSkuModel.sku_uuid}.values())
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(
            SupplierModel,
            onclause=(
                (PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == dc_object.market)
            ),
            isouter=killswitch.transfer_orders_enabled,
        )
        .join(DcModel, onclause=(DcModel.bob_code == PurchaseOrderModel.bob_code))
        .where(
            PurchaseOrderModel.delivery_time_start >= time_range_context.date_from,
            PurchaseOrderModel.delivery_time_start < time_range_context.date_to,
            PurchaseOrderModel.bob_code == dc_object.bob_code,
        )
        # does nothing but forces Postgres to apply delivery_time_start filtering
        # on PurchaseOrder before joining with SKUs, which greatly increases performance
        # (last checked on Postgres 15)
        .offset(0)
        .subquery()
    )

    result_fields = [getattr(explicit_po_subquery.c, col.name) for col in subquery_fields.values()]
    result_fields.extend(other_fields)

    query = (
        sqla.select(*result_fields)
        .select_from(explicit_po_subquery)
        .join(CulinarySkuModel, onclause=explicit_po_subquery.c.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(skus, onclause=skus.c.sku_code == CulinarySkuModel.sku_code)
    )
    return query, explicit_po_subquery.c


def get_po_data_item(po_number: str, sku_code: str) -> PurchaseOrder:
    market = context.get_request_context().market
    po_data_query = (
        sqla.select(*_complete_purchase_order_columns(with_shipment=True))
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(DcModel, onclause=sqla.and_(PurchaseOrderModel.bob_code == DcModel.bob_code, DcModel.market == market))
        .join(
            SupplierModel,
            onclause=sqla.and_(PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == market),
            isouter=killswitch.transfer_orders_enabled,
        )
        .where(PurchaseOrderModel.po_number == po_number, CulinarySkuModel.sku_code == sku_code)
    )
    po_data_query = po_utils.join_shipment(po_data_query, PurchaseOrderModel.po_number)
    return _po_query_to_purchase_orders(po_data_query, include_shipment_data=True)[0]


def _get_base_to_data_query(filters: list[ColumnElement]) -> Select:
    market = context.get_request_context().market
    fields = _complete_transfer_order_columns(with_shipment=True)
    po_data_query = (
        sqla.select(*fields)
        .select_from(PurchaseOrderModel)
        .join(TransferOrderSkuModel)
        .join(CulinarySkuModel, onclause=TransferOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(DcModel, onclause=sqla.and_(PurchaseOrderModel.bob_code == DcModel.bob_code, DcModel.market == market))
        .join(
            SupplierModel,
            onclause=TransferOrderSkuModel.original_supplier_id == SupplierModel.id,
            isouter=True,
        )
        .where(*filters)
    )
    po_data_query = po_utils.join_shipment(po_data_query, PurchaseOrderModel.po_number)
    return po_data_query


def get_to_data_item(po_number: str, sku_code: str) -> list[PurchaseOrder]:
    filters = [PurchaseOrderModel.po_number == po_number, CulinarySkuModel.sku_code == sku_code]
    return _po_query_to_purchase_orders(_get_base_to_data_query(filters), include_shipment_data=True)


def get_to_data(bob_codes: list[str], time_range_context: TimeRangeContext) -> list[PurchaseOrder]:
    filters = [
        PurchaseOrderModel.bob_code.in_(bob_codes),
        PurchaseOrderModel.delivery_time_start >= time_range_context.date_from,
        PurchaseOrderModel.delivery_time_start < time_range_context.date_to,
    ]
    return _po_query_to_purchase_orders(_get_base_to_data_query(filters), include_shipment_data=True)


def get_po_sku_filtered_purchase_orders(
    dc_object: DcConfig, po_sku_filtering_query: Select, filters: PoFilters
) -> list[PurchaseOrder]:
    po_query = po_sku_filtering_query.subquery()
    query = (
        sqla.select(*_complete_purchase_order_columns())
        .join(
            po_query,
            onclause=(
                (
                    (po_query.c.po_number == PurchaseOrderModel.po_number)
                    | (po_query.c.po_number == PurchaseOrderModel.order_number)
                )
                & (PurchaseOrderModel.brand == dc_object.brand)
            ),
        )
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(
            CulinarySkuModel,
            onclause=(
                (po_query.c.sku_code == CulinarySkuModel.sku_code)
                & (PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
            ),
        )
        .join(DcModel, onclause=(PurchaseOrderModel.bob_code == DcModel.bob_code))
        .join(
            SupplierModel,
            onclause=sqla.and_(
                PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == dc_object.market
            ),
            isouter=killswitch.transfer_orders_enabled,
        )
    )
    query = filtering.filter_ot(query, dc_object)
    query = filters.apply(dc_object, query)
    return _po_query_to_purchase_orders(query)


def get_po_data_db(
    weeks_dcs: list[tuple[ScmWeek, DcConfig | None]],
    filters: PoFilters = None,
    skus: Set[str] = None,
    include_shipment_data: bool = False,
) -> list[PurchaseOrder]:
    return [
        po_data
        for week, site in weeks_dcs
        for po_data in _get_po_data(
            week, filters=filters, site=site, skus=skus, include_shipment_data=include_shipment_data
        )
    ]


def _get_po_data(
    week: ScmWeek,
    filters: PoFilters = None,
    site: DcConfig = None,
    skus: list[str] = None,
    include_shipment_data: bool = False,
) -> list[PurchaseOrder]:
    filters = filters or PoFilters()
    market = context.get_request_context().market
    po_data_query = (
        sqla.select(*_complete_purchase_order_columns(include_shipment_data))
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel, onclause=PurchaseOrderModel.po_uuid == PurchaseOrderSkuModel.po_uuid)
        .join(CulinarySkuModel, onclause=PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(DcModel, onclause=sqla.and_(PurchaseOrderModel.bob_code == DcModel.bob_code, DcModel.market == market))
        .join(
            SupplierModel,
            onclause=sqla.and_(PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == market),
            isouter=killswitch.transfer_orders_enabled,
        )
        .where(PurchaseOrderModel.week == str(week), PurchaseOrderModel.is_sent)
    )

    if skus is not None:
        po_data_query = po_data_query.where(CulinarySkuModel.sku_code.in_(skus))

    if include_shipment_data:
        po_data_query = po_utils.join_shipment(po_data_query, PurchaseOrderModel.po_number)

    po_data_query = filtering.filter_ot(po_data_query, site)
    po_data_query = filters.apply(site, po_data_query, sku_code_column=CulinarySkuModel.sku_code)

    return _po_query_to_purchase_orders(po_data_query, include_shipment_data)


def get_po_sku_code_query_by_receiving_date(
    dc_object: DcConfig,
    receive_date_from: datetime,
    receive_date_to: datetime,
    weeks: list[ScmWeek],
    filters: PoFilters = None,
) -> Select:
    filters = filters or PoFilters()
    if dc_object.receiving_type.is_high_jump:
        fields = (HJReceiptsSqla.po_number, HJReceiptsSqla.sku_code)
        query = hj_repo.query_received(
            wh_id=dc_object.high_jump_name,
            receive_date_from=receive_date_from,
            receive_date_to=receive_date_to,
            fields=fields,
            weeks=weeks,
        )
    elif dc_object.receiving_type.is_in_grn:
        fields = (GoodsReceiptNoteModel.order_number.label("po_number"), GoodsReceiptNoteModel.sku_code)
        query = grn.get_grn_received_items_by_receive_date_sqla(
            dc_object=dc_object,
            date_from=receive_date_from,
            date_to=receive_date_to,
            fields=fields,
            weeks=weeks,
        )
    else:
        fields = (ReceivingModel.po.label("po_number"), ReceivingModel.sku_code)
        query = receiving_repo.query_received(
            time_range_context=TimeRangeContext(date_from=receive_date_from, date_to=receive_date_to),
            site=dc_object.sheet_name,
            fields=fields,
            weeks=weeks,
        )
    query = filters.apply(dc_object, query, sku_code_column=fields[1])
    return query


def get_pos_by_buyer_and_delivery_time_start(buyer_id: int, day: date = None) -> list[PoSku]:
    if not day:
        day = date.today()
    query = (
        sqla.select(PurchaseOrderModel.po_number, CulinarySkuModel.sku_code)
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(UserModel, onclause=(PurchaseOrderModel.ordered_by.startswith(UserModel.email)))
        .where(
            PurchaseOrderModel.delivery_time_start >= day,
            PurchaseOrderModel.delivery_time_start < day + timedelta(days=1),
            PurchaseOrderModel.is_sent,
            UserModel.id == buyer_id,
        )
    )
    return [PoSku(po_number=item.po_number, sku_code=item.sku_code) for item in app_db.apply_query(query)]


def _common_po_fields() -> list[ColumnElement]:
    return [
        DcModel.name.label("dc"),
        PurchaseOrderModel.supplier_code,
        PurchaseOrderModel.source_bob_code,
        PurchaseOrderModel.po_number,
        PurchaseOrderModel.bob_code,
        PurchaseOrderModel.ot_po_status,
        PurchaseOrderModel.is_sent,
        PurchaseOrderModel.week,
        PurchaseOrderModel.order_date,
        PurchaseOrderModel.delivery_time_start,
        PurchaseOrderModel.emergency_reason,
        PurchaseOrderModel.brand,
        PurchaseOrderModel.shipping_method,
        PurchaseOrderModel.ordered_by,
        CulinarySkuModel.sku_code,
        CulinarySkuModel.sku_name,
        CulinarySkuModel.sku_id,
    ]


def _complete_purchase_order_columns(with_shipment: bool = False) -> list[ColumnElement]:
    columns = _common_po_fields()
    columns.extend(
        [
            PurchaseOrderSkuModel.order_size,
            PurchaseOrderSkuModel.order_unit,
            PurchaseOrderSkuModel.case_price,
            PurchaseOrderSkuModel.case_size,
            PurchaseOrderSkuModel.case_unit,
            PurchaseOrderSkuModel.quantity,
            PurchaseOrderSkuModel.buffer,
            PurchaseOrderSkuModel.total_price,
            SupplierModel.name.label("supplier"),
        ]
    )
    if with_shipment:
        columns.extend(POShipmentModel.get_non_key_columns().values())
    return columns


def _complete_transfer_order_columns(with_shipment: bool = False) -> list[ColumnElement]:
    columns = _common_po_fields()
    columns.remove(PurchaseOrderModel.supplier_code)
    columns.extend(
        [
            TransferOrderSkuModel.order_size,
            TransferOrderSkuModel.order_unit,
            TransferOrderSkuModel.case_price,
            TransferOrderSkuModel.case_size,
            TransferOrderSkuModel.case_unit,
            TransferOrderSkuModel.quantity,
            repository_utils.make_value(None).label("buffer"),
            TransferOrderSkuModel.total_price,
            # adding different supplier name expression to return empty string instead of None
            # when requesting TO line items directly, so it won't be replaced with MANY_SUPPLIERS in the DTO
            sqla.func.coalesce(SupplierModel.name, "").label("supplier"),
            TransferOrderSkuModel.original_supplier_code.label("supplier_code"),
        ]
    )
    if with_shipment:
        columns.extend(POShipmentModel.get_non_key_columns().values())
    return columns


def _po_query_to_purchase_orders(query: Select, include_shipment_data: bool = False):
    rows = app_db.select_all(query)
    to_suppliers = po_utils.get_transfer_orders_suppliers({r.po_number for r in rows if r.source_bob_code})
    return [
        _db_row_to_purchase_order(row, include_shipment_data, to_suppliers.get((row.po_number, row.sku_code)))
        for row in rows
    ]


def _db_row_to_purchase_order(po_data: Row, include_shipment_data: bool = False, to_suppliers: list[str] | None = None):
    shipment_model = (
        Shipment(
            po_number=po_data.po_number,
            load_number=po_data.load_number,
            pallet_count=po_data.pallet_count,
            carrier_name=po_data.carrier_name,
            origin_location=PostalAddress(
                region_code=po_data.region_code,
                postal_code=po_data.postal_code,
                administrative_area=po_data.administrative_area,
                locality=po_data.locality,
                address_lines=po_data.address_lines,
                organization=po_data.organization,
            ),
            execution_event=po_data.execution_event,
            appointment_time=(
                po_data.appointment_time if po_data.appointment_state != AppointmentStateEnum.VOIDED else None
            ),
        )
        if include_shipment_data
        else None
    )
    supplier = po_utils.get_supplier_from_po_row(po_data, to_suppliers)
    return PurchaseOrder(
        dc=po_data.dc,
        bob_code=po_data.bob_code,
        sku_code=po_data.sku_code,
        sku_id=po_data.sku_id,
        sku_name=po_data.sku_name,
        supplier_code=po_data.supplier_code,
        supplier=supplier,
        po_number=po_data.po_number,
        ot_po_status=po_data.ot_po_status,
        is_sent=po_data.is_sent,
        week=ScmWeek.from_str(po_data.week),
        order_date=po_data.order_date,
        delivery_time_start=po_data.delivery_time_start,
        order_size=po_data.order_size,
        order_unit=po_data.order_unit,
        case_price=po_data.case_price,
        case_size=po_data.case_size,
        case_unit=UnitOfMeasure.inexact_value_of(po_data.case_unit),
        quantity=po_data.quantity,
        buffer=po_data.buffer / 100 if po_data.buffer else Decimal(),
        total_price=po_data.total_price,
        emergency_reason=po_data.emergency_reason,
        brand=po_data.brand,
        shipping_method=po_data.shipping_method,
        po_buyer=po_data.ordered_by,
        shipment_data=shipment_model,
        transfer_source_bob_code=po_data.source_bob_code,
        has_multiple_transfer_items=bool(to_suppliers and len(to_suppliers) > 1),
    )
