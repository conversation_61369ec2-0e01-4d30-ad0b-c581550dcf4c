from collections.abc import Iterable
from datetime import datetime

import sqlalchemy as sqla
from sqlalchemy import Select

from procurement.constants.hellofresh_constant import MANY_SUPPLIERS
from procurement.constants.ordering import HJ_END_STATUSES
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE
from procurement.data.dto.ordering.purchase_order.incoming import IncomingOrder
from procurement.data.models.highjump.highjump import HJReceiptsModel as OriginalHjReceiptsModel
from procurement.data.models.inventory.grn import LegacyHjGoodsReceiptNoteModel
from procurement.data.models.inventory.po_void import PoVoidModel
from procurement.data.models.inventory.receive import ReceivingModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db

HJReceiptsModel = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else OriginalHjReceiptsModel


def get_incoming_orders_excluding_receives(
    dc_object: DcConfig,
    date_from: datetime,
    date_to: datetime,
    only_early_receives=True,
) -> list[IncomingOrder]:
    """
    This query will return PO SKU items that are scheduled to receive in [date_from, date_to)
    excluding items that were already received.
    When :param only_early_receives:is set to true then exclude items that were ONLY received BEFORE scheduled date.
    """
    query = _base_incoming_query()
    query = query.where(
        PurchaseOrderModel.delivery_time_start >= date_from,
        PurchaseOrderModel.delivery_time_start < date_to,
        PurchaseOrderModel.bob_code == dc_object.bob_code,
    )
    if dc_object.receiving_type.is_high_jump:
        receiving_po_number_field = HJReceiptsModel.po_number
        query = _join_hj(query, dc_object.high_jump_name, only_early_receives)
    else:
        receiving_po_number_field = ReceivingModel.po
        query = _join_manual(query, dc_object.sheet_name, only_early_receives)
    query = query.where(receiving_po_number_field.is_(None))
    return _to_incoming_orders(query)


def _join_manual(query: Select, site: str, only_early_receives=True) -> Select:
    join_condition = (
        (PurchaseOrderModel.po_number == ReceivingModel.po)
        & (CulinarySkuModel.sku_code == ReceivingModel.sku_code)
        & (ReceivingModel.dc == site)
    )
    if only_early_receives:
        join_condition &= PurchaseOrderModel.delivery_time_start.cast(
            sqla.DATE
        ) > ReceivingModel.receive_timestamp.cast(sqla.DATE)
    return query.join(ReceivingModel, isouter=True, onclause=join_condition)


def _join_hj(query: Select, hj_name: str, only_early_receives=True) -> Select:
    join_condition = (
        (PurchaseOrderModel.po_number == HJReceiptsModel.po_number)
        & (CulinarySkuModel.sku_code == HJReceiptsModel.sku_code)
        & (HJReceiptsModel.status.in_(HJ_END_STATUSES))
        & (HJReceiptsModel.wh_id == hj_name)
    )
    if only_early_receives:
        join_condition &= PurchaseOrderModel.delivery_time_start.cast(
            sqla.DATE
        ) > HJReceiptsModel.receipt_time_est.cast(sqla.DATE)
    return query.join(HJReceiptsModel, isouter=True, onclause=join_condition)


def get_inbound_orders(
    weeks: Iterable[ScmWeek],
    bob_codes: Iterable[BOB_CODE],
    exclude_suppliers: Iterable[str] | None = None,
    sku_codes: set[str] | None = None,
) -> list[IncomingOrder]:
    query = _base_incoming_query().where(
        PurchaseOrderModel.week.in_(map(str, weeks)), PurchaseOrderModel.bob_code.in_(bob_codes)
    )
    if sku_codes is not None:
        query = query.where(CulinarySkuModel.sku_code.in_(sku_codes))
    if exclude_suppliers:
        query = query.where(
            ~SupplierModel.name.in_(exclude_suppliers)
            | ~PurchaseOrderModel.source_bob_code.in_(exclude_suppliers)
            | (SupplierModel.name.is_(None) & PurchaseOrderModel.source_bob_code.is_(None))
        )
    return _to_incoming_orders(query)


def _base_incoming_query() -> Select:
    market = context.get_request_context().market
    return (
        sqla.select(
            PurchaseOrderModel.po_number,
            PurchaseOrderModel.week,
            PurchaseOrderModel.bob_code,
            CulinarySkuModel.sku_code,
            CulinarySkuModel.sku_name,
            PurchaseOrderSkuModel.quantity,
            PurchaseOrderModel.delivery_time_start,
            PurchaseOrderModel.emergency_reason,
            SupplierModel.name.label("supplier_name"),
            PurchaseOrderModel.source_bob_code,
        )
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(PoVoidModel, onclause=(PurchaseOrderModel.po_number == PoVoidModel.po_number), isouter=True)
        .join(
            SupplierModel,
            isouter=True,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
        )
        .where(
            PurchaseOrderModel.is_sent,
            ~PurchaseOrderModel.deleted,
            PoVoidModel.po_number.is_(None),
        )
    )


def _to_incoming_orders(query: Select) -> list[IncomingOrder]:
    return [
        IncomingOrder(
            po_number=row.po_number,
            bob_code=row.bob_code,
            week=ScmWeek.from_str(row.week),
            sku_code=row.sku_code,
            sku_name=row.sku_name,
            quantity=row.quantity,
            delivery_time_start=row.delivery_time_start,
            supplier=(
                MANY_SUPPLIERS
                if row.supplier_name is None and row.source_bob_code and killswitch.transfer_orders_enabled
                else row.supplier_name
            ),
            emergency_reason=row.emergency_reason,
        )
        for row in app_db.select_all(query)
    ]
