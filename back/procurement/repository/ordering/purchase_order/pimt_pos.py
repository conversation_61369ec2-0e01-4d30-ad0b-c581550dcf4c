from collections.abc import Iterable
from datetime import date, datetime

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select, Subquery
from sqlalchemy.sql import ColumnExpressionArgument

from procurement.constants.ordering import HJ_CLOSED_STATUSES
from procurement.constants.protobuf import GrnDeliveryLineState
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, ORDER_NUMBER, PO_NUMBER, SKU_CODE
from procurement.data.dto.ordering.purchase_order.pimt_pos import (
    InventoryPo,
    PimtInboundOrder,
    PimtOutboundOrder,
    PimtPoItem,
    PimtPoItemBase,
    SupplierOutboundPo,
)
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.models.highjump.highjump import HJReceiptsModel
from procurement.data.models.inventory.grn import GoodsReceiptNoteModel, LegacyHjGoodsReceiptNoteModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.data.models.ordering.shipment import POShipmentModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.data.models.social.distribution_center import DcModel
from procurement.repository import highjump as hj_repo
from procurement.repository import repository_utils
from procurement.repository.inventory import grn
from procurement.repository.pimt import inventory
from procurement.services.database import app_db

from . import utils as po_utils

HJReceiptsSqla = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else HJReceiptsModel


def get_inbound_pos(warehouse: Warehouse, weeks: Iterable[ScmWeek], sku_code: str | None = None) -> list[PimtPoItem]:
    return [
        _build_po_item(item)
        for item in app_db.select_all(
            _exclude_inventory(_get_base_inbound_pos(warehouse, weeks, exclude_grn_received=True, sku_code=sku_code))
        )
    ]


def get_inbound_pos_for_network_depletion(warehouse: Warehouse, weeks: Iterable[ScmWeek]) -> list[PimtPoItem]:
    return [
        _build_po_item(item)
        for item in app_db.select_all(_get_base_inbound_pos(warehouse, weeks, exclude_grn_received=False))
    ]


def _query_po_items(query: Select) -> list[PimtPoItem]:
    rows = app_db.select_all(query)
    transfer_suppliers = po_utils.get_transfer_orders_suppliers({r.po_number for r in rows if r.source_bob_code})
    return [_build_po_item(r, transfer_suppliers.get((r.po_number, r.sku_code))) for r in rows]


def _build_po_item(item, to_suppliers: list[str] | None = None):
    supplier = po_utils.get_supplier_from_po_row(item, to_suppliers)
    return PimtPoItem(
        po_number=item.po_number,
        week=item.week,
        sku_code=item.sku_code,
        quantity=item.quantity,
        delivery_time_start=item.delivery_time_start,
        case_size=item.case_size,
        cases_received=item.cases_received,
        grn_units=item.grn_units,
        supplier_code=item.supplier_code,
        has_grn=item.has_grn,
        supplier=supplier,
        sku_name=item.sku_name,
        order_size=item.order_size,
        case_price=item.case_price,
        total_price=item.total_price,
        shipping_method=item.shipping_method,
        emergency_reason=item.emergency_reason,
        appointment_time=item.appointment_time,
        grn_receive_date=item.grn_receive_date,
        ordered_by=item.ordered_by,
        transfer_source_bob_code=item.source_bob_code,
        has_multiple_transfer_items=False,
    )


def _get_base_inbound_pos(
    warehouse: Warehouse, weeks: Iterable[ScmWeek], exclude_grn_received: bool = True, sku_code: str | None = None
) -> Select:
    grn_query = _get_grn_query_by_weeks(warehouse, weeks)
    market = context.get_request_context().market

    query = (
        PurchaseOrderModel.base_select(
            PurchaseOrderModel.po_number,
            PurchaseOrderModel.supplier_code,
            PurchaseOrderModel.ordered_by,
            PurchaseOrderModel.week,
            CulinarySkuModel.sku_code,
            CulinarySkuModel.sku_name,
            PurchaseOrderModel.delivery_time_start,
            PurchaseOrderSkuModel.order_size,
            PurchaseOrderSkuModel.case_size,
            PurchaseOrderSkuModel.quantity,
            PurchaseOrderSkuModel.case_price,
            PurchaseOrderSkuModel.total_price,
            PurchaseOrderModel.shipping_method,
            PurchaseOrderModel.emergency_reason,
            PurchaseOrderModel.source_bob_code,
            POShipmentModel.appointment_time,
            SupplierModel.name.label("supplier"),
            (grn_query.c.sku_code.is_not(None)).label("has_grn"),
            grn_query.c.receive_date.label("grn_receive_date"),
            grn_query.c.units.label("grn_units"),
            grn_query.c.cases_received,
        )
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(
            POShipmentModel,
            onclause=(PurchaseOrderModel.po_number == POShipmentModel.po_number),
            isouter=True,
        )
        .join(
            grn_query,
            isouter=True,
            onclause=(
                (PurchaseOrderModel.order_number == grn_query.c.order_number)
                & (CulinarySkuModel.sku_code == grn_query.c.sku_code)
            ),
        )
        .join(
            SupplierModel,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
        )
        .where(
            PurchaseOrderModel.week.in_(list(map(str, weeks))),
            PurchaseOrderModel.bob_code == warehouse.bob_code,
        )
    )
    if sku_code:
        query = query.where(CulinarySkuModel.sku_code == sku_code)

    if exclude_grn_received:
        query = query.where(grn_query.c.sku_code.is_(None))
    return query


def _get_po_grn_select(grn_query: Subquery) -> Select:
    market = context.get_request_context().market
    query = (
        PurchaseOrderModel.base_select(
            *_get_po_common_fields(grn_query),
            PurchaseOrderSkuModel.order_size,
            PurchaseOrderSkuModel.case_price,
            PurchaseOrderSkuModel.case_size,
            PurchaseOrderSkuModel.quantity,
            SupplierModel.name.label("supplier"),
        )
        .join(PurchaseOrderSkuModel, onclause=(PurchaseOrderModel.po_uuid == PurchaseOrderSkuModel.po_uuid))
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(
            grn_query,
            isouter=True,
            onclause=(
                (PurchaseOrderModel.order_number == grn_query.c.order_number)
                & (CulinarySkuModel.sku_code == grn_query.c.sku_code)
            ),
        )
        .join(
            SupplierModel,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
            isouter=killswitch.transfer_orders_enabled,
        )
    )
    return query


def _get_to_grn_select(grn_query: Subquery) -> Select:
    query = (
        PurchaseOrderModel.base_select(
            *_get_po_common_fields(grn_query),
            TransferOrderSkuModel.order_size,
            TransferOrderSkuModel.case_price,
            TransferOrderSkuModel.case_size,
            TransferOrderSkuModel.quantity,
            # adding different supplier name expression to return empty string instead of None
            # when requesting TO line items directly, so it won't be replaced with MANY_SUPPLIERS in the DTO
            sqla.func.coalesce(SupplierModel.name, "").label("supplier"),
        )
        .join(TransferOrderSkuModel, onclause=(PurchaseOrderModel.po_uuid == TransferOrderSkuModel.po_uuid))
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == TransferOrderSkuModel.sku_uuid))
        .join(
            grn_query,
            isouter=True,
            onclause=(
                (PurchaseOrderModel.order_number == grn_query.c.order_number)
                & (CulinarySkuModel.sku_code == grn_query.c.sku_code)
            ),
        )
        .join(
            SupplierModel,
            onclause=TransferOrderSkuModel.original_supplier_id == SupplierModel.id,
            isouter=True,
        )
    )
    return query


def _get_po_common_fields(grn_query: Subquery) -> list[ColumnElement]:
    return [
        PurchaseOrderModel.po_number,
        PurchaseOrderModel.delivery_time_start,
        PurchaseOrderModel.emergency_reason,
        PurchaseOrderModel.shipping_method,
        PurchaseOrderModel.ordered_by,
        PurchaseOrderModel.supplier_code,
        PurchaseOrderModel.week,
        PurchaseOrderModel.source_bob_code,
        CulinarySkuModel.sku_code,
        CulinarySkuModel.sku_name,
        (grn_query.c.sku_code.is_not(None)).label("has_grn"),
        grn_query.c.receive_date.label("grn_receive_date"),
        grn_query.c.units.label("grn_units"),
        grn_query.c.cases_received,
    ]


def get_pimt_purchase_order(wh: Warehouse, weeks: Iterable[ScmWeek], sku_code: str = None) -> list[PimtPoItemBase]:
    grn_query = _get_grn_query_by_weeks(wh, weeks)
    query = _get_po_grn_select(grn_query).where(
        PurchaseOrderModel.week.in_(list(map(str, weeks))), PurchaseOrderModel.bob_code == wh.bob_code
    )
    if sku_code:
        query = query.where(CulinarySkuModel.sku_code == sku_code)
    return _query_base_po_items(query)


def get_pimt_transfer_order(wh: Warehouse, po_number: PO_NUMBER, sku_code: SKU_CODE) -> list[PimtPoItemBase]:
    grn_query = _get_grn_query_by_po(wh, po_number)
    query = _get_to_grn_select(grn_query).where(
        PurchaseOrderModel.po_number == po_number,
        CulinarySkuModel.sku_code == sku_code,
    )
    return [_build_base_po_item(item) for item in app_db.select_all(query)]


def _query_base_po_items(query: Select) -> list[PimtPoItemBase]:
    rows = app_db.select_all(query)
    transfer_suppliers = po_utils.get_transfer_orders_suppliers({r.po_number for r in rows if r.source_bob_code})
    return [_build_base_po_item(it, to_suppliers=transfer_suppliers.get((it.po_number, it.sku_code))) for it in rows]


def _build_base_po_item(item, to_suppliers: list[str] | None = None) -> PimtPoItemBase:
    supplier = po_utils.get_supplier_from_po_row(item, to_suppliers)
    return PimtPoItemBase(
        po_number=item.po_number,
        week=item.week,
        sku_code=item.sku_code,
        quantity=item.quantity,
        delivery_time_start=item.delivery_time_start,
        case_size=item.case_size,
        cases_received=item.cases_received,
        supplier=supplier,
        sku_name=item.sku_name,
        order_size=item.order_size,
        case_price=item.case_price,
        shipping_method=item.shipping_method,
        emergency_reason=item.emergency_reason,
        grn_units=item.grn_units,
        has_grn=item.has_grn,
        supplier_code=item.supplier_code,
        grn_receive_date=item.grn_receive_date,
        ordered_by=item.ordered_by,
        transfer_source_bob_code=item.source_bob_code,
        has_multiple_transfer_items=bool(to_suppliers and len(to_suppliers) > 1),
    )


def get_pimt_purchase_order_by_date_range(wh: Warehouse, date_from: datetime, date_to: datetime):
    grn_query = _get_grn_query_by_date_range(wh, date_from, date_to)
    query = _get_po_grn_select(grn_query).where(
        (
            (
                (PurchaseOrderModel.delivery_time_start >= date_from)
                & (PurchaseOrderModel.delivery_time_start < date_to)
                & (grn_query.c.receive_date.is_(None))
            )
            | ((grn_query.c.receive_date >= date_from) & (grn_query.c.receive_date < date_to))
        ),
        PurchaseOrderModel.bob_code == wh.bob_code,
    )
    return _query_base_po_items(query)


def get_pimt_purchase_order_by_receiving_date(
    wh: Warehouse, date_from: datetime, date_to: datetime
) -> list[PimtPoItemBase]:
    grn_query = _get_grn_query_by_date_range(wh, date_from, date_to)
    query = _get_po_grn_select(grn_query).where(
        PurchaseOrderModel.bob_code == wh.bob_code,
        grn_query.c.receive_date >= date_from,
        grn_query.c.receive_date < date_to,
    )
    return _query_base_po_items(query)


def get_pimt_transfer_order_by_receiving_date(
    wh: Warehouse, date_from: datetime, date_to: datetime
) -> list[PimtPoItemBase]:
    grn_query = _get_grn_query_by_date_range(wh, date_from, date_to)
    query = _get_to_grn_select(grn_query).where(
        PurchaseOrderModel.bob_code == wh.bob_code,
        grn_query.c.receive_date >= date_from,
        grn_query.c.receive_date < date_to,
    )
    return [_build_base_po_item(item) for item in app_db.select_all(query)]


def _get_grn_query_by_weeks(wh: Warehouse, weeks: Iterable[ScmWeek]) -> Subquery:
    return _get_grn_po_query(wh).where(PurchaseOrderModel.week.in_(list(map(str, weeks)))).subquery()


def _get_grn_query_by_po(wh: Warehouse, po_number: PO_NUMBER) -> Subquery:
    return _get_grn_po_query(wh).where(PurchaseOrderModel.po_number == po_number).subquery()


def _get_grn_po_query(warehouse: Warehouse) -> Select:
    if warehouse.receiving_type.is_hj and killswitch.use_hj_for_grn_hj_warehouses:
        return _get_grn_hj_query(warehouse)
    return (
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.receipt_time_est.label("receive_date"),
            GoodsReceiptNoteModel.units_received.label("units"),
            GoodsReceiptNoteModel.cases_received,
        )
        .join(
            PurchaseOrderModel,
            onclause=(PurchaseOrderModel.order_number == GoodsReceiptNoteModel.order_number),
        )
        .where(
            PurchaseOrderModel.is_sent,
            PurchaseOrderModel.bob_code == warehouse.bob_code,
            GoodsReceiptNoteModel.bob_code == warehouse.bob_code,
            GoodsReceiptNoteModel.source == warehouse.receiving_type.grn_source_name,
            GoodsReceiptNoteModel.status == GrnDeliveryLineState.CLOSED,
        )
    )


def _get_grn_hj_query(warehouse: Warehouse) -> Select:
    return (
        sqla.select(
            repository_utils.order_number_from_po_number(HJReceiptsSqla.po_number).label("order_number"),
            HJReceiptsSqla.sku_code,
            HJReceiptsSqla.receipt_time_est.label("receive_date"),
            HJReceiptsSqla.quantity_received.label("units"),
            HJReceiptsSqla.cases_received,
        )
        .join(
            PurchaseOrderModel,
            onclause=(PurchaseOrderModel.po_number == HJReceiptsSqla.po_number),
        )
        .where(
            ~PurchaseOrderModel.deleted,
            PurchaseOrderModel.is_sent,
            PurchaseOrderModel.bob_code == warehouse.bob_code,
            HJReceiptsSqla.wh_id == warehouse.hj_name,
            HJReceiptsSqla.status.in_(HJ_CLOSED_STATUSES),
        )
    )


def _get_grn_query_by_date_range(wh: Warehouse, date_from: datetime, date_to: datetime) -> Subquery:
    receipt_time_col = (
        HJReceiptsSqla.receipt_time_est
        if wh.receiving_type.is_hj and killswitch.use_hj_for_grn_hj_warehouses
        else GoodsReceiptNoteModel.receipt_time_est
    )
    return (
        _get_grn_po_query(wh)
        .where(
            receipt_time_col >= date_from,
            receipt_time_col < date_to,
        )
        .subquery()
    )


def get_pimt_inbound_orders(destinations: Iterable[str], delivery_from: date) -> list[PimtInboundOrder]:
    return [
        PimtInboundOrder(
            bob_code=item.bob_code,
            grn_quantity=item.grn_quantity,
            sku_code=item.sku_code,
            total_quantity=item.total_quantity,
        )
        for item in app_db.select_all(
            _get_pimt_orders_query(
                location_filter_values=destinations,
                scheduled_date_from=delivery_from,
                inbound=True,
                include_grn_data=True,
            )
        )
    ]


def get_pimt_outbound_orders(
    supplier: Iterable[str], source_bob_codes: Iterable[str], delivery_from: date
) -> list[PimtOutboundOrder]:
    locations = set(supplier).union(source_bob_codes)
    return [
        PimtOutboundOrder(
            total_quantity=item.total_quantity,
            sku_code=item.sku_code,
            supplier=item.supplier,
            source_bob_code=item.source_bob_code,
        )
        for item in app_db.select_all(
            _get_pimt_orders_query(
                location_filter_values=locations,
                scheduled_date_from=delivery_from,
                inbound=False,
            )
        )
    ]


def get_pimt_inbound_orders_by_weeks(destinations: Iterable[str], weeks: Iterable[ScmWeek]) -> list[PimtInboundOrder]:
    return [
        PimtInboundOrder(
            bob_code=item.bob_code,
            grn_quantity=item.grn_quantity,
            sku_code=item.sku_code,
            total_quantity=item.total_quantity,
            week=item.week,
        )
        for item in app_db.select_all(
            _get_pimt_orders_query(
                location_filter_values=destinations, by_weeks=weeks, inbound=True, include_grn_data=True
            )
        )
    ]


def get_pimt_outbound_orders_by_weeks(
    suppliers: Iterable[str], source_bob_codes: Iterable[BOB_CODE], weeks: Iterable[ScmWeek]
) -> list[PimtOutboundOrder]:
    locations = set(suppliers).union(source_bob_codes)
    return [
        PimtOutboundOrder(
            total_quantity=item.total_quantity - (item.grn_quantity or 0),
            sku_code=item.sku_code,
            supplier=item.supplier,
            week=item.week,
            bob_code=item.bob_code,
            source_bob_code=item.source_bob_code,
        )
        for item in app_db.select_all(
            _get_pimt_orders_query(
                location_filter_values=locations, by_weeks=weeks, inbound=False, include_grn_data=True
            )
        )
    ]


def get_pimt_inbound_undelivered(
    destinations: Iterable[str],
    weeks: Iterable[ScmWeek],
    delivery_to: date,
) -> list[PimtInboundOrder]:
    return [
        PimtInboundOrder(
            bob_code=item.bob_code,
            grn_quantity=item.grn_quantity,
            sku_code=item.sku_code,
            total_quantity=item.total_quantity,
            week=item.week,
        )
        for item in app_db.select_all(
            _get_pimt_orders_query(
                location_filter_values=destinations,
                by_weeks=weeks,
                inbound=True,
                include_grn_data=True,
                scheduled_date_to=delivery_to,
            )
        )
    ]


def _get_pimt_orders_query(
    location_filter_values: Iterable[str],
    by_weeks: Iterable[ScmWeek] | None = None,
    inbound: bool = False,
    include_grn_data: bool = False,
    scheduled_date_from: date | None = None,
    scheduled_date_to: date | None = None,
):
    filters = []
    market = context.get_request_context().market
    group_fields = [CulinarySkuModel.sku_code, PurchaseOrderModel.bob_code]
    agg_fields = [sqla.func.sum(PurchaseOrderSkuModel.quantity).label("total_quantity")]

    if inbound:
        filters.append(PurchaseOrderModel.bob_code.in_(location_filter_values))
    else:
        group_fields.append(SupplierModel.name.label("supplier"))
        group_fields.append(PurchaseOrderModel.source_bob_code)
        filters.append(
            (SupplierModel.name.in_(location_filter_values))
            | (PurchaseOrderModel.source_bob_code.in_(location_filter_values))
        )

    if by_weeks:
        filters.append(PurchaseOrderModel.week.in_(list(map(str, by_weeks))))
        group_fields.append(PurchaseOrderModel.week)

    if scheduled_date_from:
        filters.append(PurchaseOrderModel.delivery_time_start >= scheduled_date_from)
    if scheduled_date_to:
        filters.append(PurchaseOrderModel.delivery_time_start < scheduled_date_to)

    if include_grn_data:
        grn_query = _get_hj_grn_units_query_with_hj(filters)
        agg_fields.append(sqla.func.sum(grn_query.c.qty).label("grn_quantity"))

    query = (
        PurchaseOrderModel.base_select(*group_fields, *agg_fields)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid))
    )

    if inbound:
        query = _exclude_inventory(query)
    else:
        query = _exclude_imt_inventory(query, filters)
        query = query.join(
            SupplierModel,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
            isouter=killswitch.transfer_orders_enabled,
        )

    if include_grn_data:
        query = query.join(
            grn_query,
            isouter=True,
            onclause=(
                (CulinarySkuModel.sku_code == grn_query.c.sku_code)
                & (PurchaseOrderModel.order_number == grn_query.c.order_number)
            ),
        )
    return query.where(*filters).group_by(*group_fields)


def _get_hj_grn_units_query_with_hj(filters: list[ColumnExpressionArgument]) -> Subquery:
    return (
        sqla.select(
            repository_utils.order_number_from_po_number(
                sqla.func.coalesce(HJReceiptsSqla.po_number, GoodsReceiptNoteModel.order_number)
            ).label("order_number"),
            sqla.func.coalesce(HJReceiptsSqla.sku_code, GoodsReceiptNoteModel.sku_code).label("sku_code"),
            sqla.func.coalesce(HJReceiptsSqla.quantity_received, GoodsReceiptNoteModel.units_received).label("qty"),
        )
        .select_from(PurchaseOrderModel)
        .join(
            GoodsReceiptNoteModel,
            isouter=True,
            onclause=(PurchaseOrderModel.order_number == GoodsReceiptNoteModel.order_number),
        )
        .join(
            HJReceiptsSqla,
            isouter=True,
            onclause=(PurchaseOrderModel.po_number == HJReceiptsSqla.po_number),
        )
        .join(DcModel, onclause=(PurchaseOrderModel.bob_code == DcModel.bob_code))
        .where(
            (
                (GoodsReceiptNoteModel.status == GrnDeliveryLineState.CLOSED)
                | HJReceiptsSqla.status.in_(HJ_CLOSED_STATUSES)
            ),
            ~PurchaseOrderModel.deleted,
            PurchaseOrderModel.is_sent,
            *filters,
        )
        .subquery()
    )


def _exclude_inventory(query: Select) -> Select:
    filtering_inv_subq = inventory.get_unique_po_sku_inventory()
    return query.join(
        filtering_inv_subq,
        isouter=True,
        onclause=(
            (PurchaseOrderModel.order_number == filtering_inv_subq.c.order_number)
            & (CulinarySkuModel.sku_code == filtering_inv_subq.c.sku_code)
        ),
    ).where(filtering_inv_subq.c.order_number.is_(None))


def _exclude_imt_inventory(query: Select, filters: list[ColumnExpressionArgument]) -> Select:
    query = _exclude_hj_receipts(query, filters)
    query = _exclude_pimt_grn_receipts(query, filters)
    return query


def _exclude_hj_receipts(query: Select, filters: list[ColumnExpressionArgument]) -> Select:
    market = context.get_request_context().market
    hj_receipts_subq = hj_repo.get_unique_po_sku_hj_receipts(
        sqla.select(PurchaseOrderModel.po_number)
        .join(
            SupplierModel,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
            isouter=killswitch.transfer_orders_enabled,
        )
        .where(*filters)
        .subquery()
    )
    query = query.join(
        hj_receipts_subq,
        isouter=True,
        onclause=(
            (PurchaseOrderModel.po_number == hj_receipts_subq.c.po_number)
            & (CulinarySkuModel.sku_code == hj_receipts_subq.c.sku_code)
        ),
    ).where(hj_receipts_subq.c.po_number.is_(None))
    return query


def _exclude_pimt_grn_receipts(query: Select, filters: list[ColumnExpressionArgument]) -> Select:
    market = context.get_request_context().market
    grn_receipts_subq = grn.get_unique_pimt_po_sku_grn_receipts(
        sqla.select(PurchaseOrderModel.order_number)
        .join(
            SupplierModel,
            onclause=((PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == market)),
            isouter=killswitch.transfer_orders_enabled,
        )
        .where(*filters)
        .subquery()
    )
    query = query.join(
        grn_receipts_subq,
        isouter=True,
        onclause=(
            (PurchaseOrderModel.order_number == grn_receipts_subq.c.order_number)
            & (CulinarySkuModel.sku_code == grn_receipts_subq.c.sku_code)
        ),
    ).where(grn_receipts_subq.c.order_number.is_(None))
    return query


def get_inventory_po_sku(inventory_po: set[ORDER_NUMBER]) -> list[InventoryPo]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            PurchaseOrderModel.order_number,
            PurchaseOrderModel.delivery_time_start,
            CulinarySkuModel.sku_code,
            SupplierModel.name.label("supplier"),
        )
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid))
        .join(
            SupplierModel,
            onclause=sqla.and_(PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == market),
        )
        .where(PurchaseOrderModel.order_number.in_(inventory_po))
    )
    return [
        InventoryPo(
            order_number=item.order_number,
            sku_code=item.sku_code,
            delivery_time_start=item.delivery_time_start,
            supplier=item.supplier,
        )
        for item in app_db.select_all(query)
    ]


def get_supplier_outbound_pos(
    suppliers: Iterable[str], bob_codes: Iterable[str], weeks: list[ScmWeek]
) -> list[SupplierOutboundPo]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            PurchaseOrderModel.week,
            SupplierModel.name.label("supplier"),
            PurchaseOrderModel.bob_code,
            CulinarySkuModel.sku_code,
            sqla.func.sum(PurchaseOrderSkuModel.quantity).label("quantity"),
        )
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid))
        .join(
            SupplierModel,
            onclause=sqla.and_(PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == market),
        )
        .where(
            PurchaseOrderModel.bob_code.in_(bob_codes),
            SupplierModel.name.in_(suppliers),
            PurchaseOrderModel.week.in_(map(str, weeks)),
        )
        .group_by(PurchaseOrderModel.week, SupplierModel.name, PurchaseOrderModel.bob_code, CulinarySkuModel.sku_code)
    )
    return _to_supplier_outbound_po(query) + (
        _get_supplier_outbound_tos(suppliers=suppliers, bob_codes=bob_codes, weeks=weeks)
        if killswitch.transfer_orders_enabled
        else []
    )


def _get_supplier_outbound_tos(
    suppliers: Iterable[str], bob_codes: Iterable[str], weeks: list[ScmWeek]
) -> list[SupplierOutboundPo]:
    query = (
        sqla.select(
            PurchaseOrderModel.week,
            SupplierModel.name.label("supplier"),
            PurchaseOrderModel.bob_code,
            CulinarySkuModel.sku_code,
            sqla.func.sum(TransferOrderSkuModel.quantity).label("quantity"),
        )
        .join(TransferOrderSkuModel)
        .join(CulinarySkuModel, onclause=(TransferOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid))
        .join(
            SupplierModel,
            onclause=sqla.and_(TransferOrderSkuModel.original_supplier_id == SupplierModel.id),
        )
        .where(
            PurchaseOrderModel.bob_code.in_(bob_codes),
            SupplierModel.name.in_(suppliers),
            PurchaseOrderModel.week.in_(map(str, weeks)),
        )
        .group_by(PurchaseOrderModel.week, SupplierModel.name, PurchaseOrderModel.bob_code, CulinarySkuModel.sku_code)
    )
    return _to_supplier_outbound_po(query)


def _to_supplier_outbound_po(query: Select) -> list[SupplierOutboundPo]:
    return [
        SupplierOutboundPo(
            supplier=item.supplier,
            bob_code=item.bob_code,
            week=ScmWeek.from_str(item.week),
            sku_code=item.sku_code,
            quantity=item.quantity,
        )
        for item in app_db.select_all(query)
    ]
