from typing import Iterable

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.typing import SKU_CODE
from procurement.data.models.ordering.topo_sku import TopoSkuModel
from procurement.services.database import app_db


def upsert_topo_skus(
    updates: Iterable[dict],
) -> None:
    with app_db.transaction() as transaction:
        # truncate the table to remove all existing records
        app_db.apply_query(
            sqla.delete(TopoSkuModel),
            transaction=transaction,
        )
        query = psql_sqla.insert(TopoSkuModel)
        query = query.on_conflict_do_nothing(
            index_elements=TopoSkuModel.get_primary_key_columns(),
        ).values(updates)
        app_db.apply_query(query, transaction=transaction)


def get_topo_skus(bob_codes: Iterable[str]) -> set[SKU_CODE]:
    query = sqla.select(
        TopoSkuModel.sku_code,
    ).where(
        TopoSkuModel.site.in_(bob_codes),
    )
    return {row.sku_code for row in app_db.select_all(query)}
