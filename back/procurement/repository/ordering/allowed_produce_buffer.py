from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates.weeks import ScmWeek
from procurement.data.dto.ordering.allowed_produce_buffer import AllowedBufferItem
from procurement.data.models.ordering.allowed_produce_buffer import AllowedProduceBufferModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db


def update_allowed_buffers(updates: list[dict[ColumnElement, Any]]) -> None:
    query = psql_sqla.insert(AllowedProduceBufferModel)
    query = query.on_conflict_do_update(
        index_elements=[
            AllowedProduceBufferModel.week,
            AllowedProduceBufferModel.brand,
            AllowedProduceBufferModel.site,
            AllowedProduceBufferModel.sku_code,
        ],
        set_={
            AllowedProduceBufferModel.total_buffer: query.excluded.total_buffer,
        },
    ).values(updates)
    app_db.apply_query(query)


def get_allowed_buffers(dc_obj: DcConfig, week: ScmWeek) -> list[AllowedBufferItem]:
    return [
        AllowedBufferItem(sku_code=item.sku_code, total_buffer=item.total_buffer)
        for item in app_db.select_all(
            sqla.select(AllowedProduceBufferModel.sku_code, AllowedProduceBufferModel.total_buffer).where(
                AllowedProduceBufferModel.week == int(week),
                (AllowedProduceBufferModel.site == dc_obj.sheet_name)
                | (AllowedProduceBufferModel.site == dc_obj.bob_code),
                AllowedProduceBufferModel.brand == dc_obj.brand,
            )
        )
    ]
