from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.core.dates.weeks import ScmWeek
from procurement.data.dto.ordering.purchase_qty_recommendation import PurchaseQtyRecommendation
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_qty_recommendation import PurchaseQtyRecommendationModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_purchase_qty_recommendations(data: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=PurchaseQtyRecommendationModel,
        data=data,
        keys=[
            PurchaseQtyRecommendationModel.site,
            PurchaseQtyRecommendationModel.sku_uuid,
            PurchaseQtyRecommendationModel.supplier_uuid,
            PurchaseQtyRecommendationModel.week,
        ],
        preserve_fields=[
            PurchaseQtyRecommendationModel.quantity,
        ],
    )


def delete_purchase_qty_recommendation(week: int, site: str, sku_uuid: str, supplier_uuid: str):
    app_db.apply_query(
        sqla.delete(PurchaseQtyRecommendationModel).where(
            PurchaseQtyRecommendationModel.week == week,
            PurchaseQtyRecommendationModel.site == site,
            PurchaseQtyRecommendationModel.sku_uuid == sku_uuid,
            PurchaseQtyRecommendationModel.supplier_uuid == supplier_uuid,
        )
    )


def get_purchase_qty_recommendations(
    sku_codes: list[str], bob_codes: set[str], weeks: tuple[ScmWeek, ...]
) -> list[PurchaseQtyRecommendation]:
    query = (
        sqla.select(
            PurchaseQtyRecommendationModel.week,
            sqla.func.sum(PurchaseQtyRecommendationModel.quantity).label("quantity"),
            CulinarySkuModel.sku_code,
        )
        .join(CulinarySkuModel, onclause=(PurchaseQtyRecommendationModel.sku_uuid == CulinarySkuModel.sku_uuid))
        .where(
            CulinarySkuModel.sku_code.in_(sku_codes),
            PurchaseQtyRecommendationModel.site.in_(bob_codes),
            PurchaseQtyRecommendationModel.week.in_(map(int, weeks)),
        )
        .group_by(PurchaseQtyRecommendationModel.week, CulinarySkuModel.sku_code)
    )
    return [
        PurchaseQtyRecommendation(
            quantity=item.quantity,
            week=ScmWeek.from_number(item.week),
            sku_code=item.sku_code,
        )
        for item in app_db.select_all(query)
    ]
