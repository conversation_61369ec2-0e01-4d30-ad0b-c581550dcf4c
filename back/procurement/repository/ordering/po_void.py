from collections.abc import Collection
from datetime import date, datetime, timedelta
from typing import Any, Iterable

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import PoSku
from procurement.data.dto.ordering.po_void import PoVoidDto
from procurement.data.models.inventory.po_void import PoVoidModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.managers.admin import dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db


def _to_dto(item: Row) -> PoVoidDto:
    return PoVoidDto(
        sku_code=item.sku_code,
        sku_name=item.sku_name,
        po_number=item.po_number,
        dc=item.dc,
        brand=item.brand,
        comment=item.comment,
        supplier=item.supplier_name,
        user=item.user,
        timestamp=item.cre_tmst,
        id=item.id,
    )


def get_po_void_skus(week: ScmWeek, brand: str, site: str, po_number: str) -> list[str]:
    str_week = str(week)
    market = context.get_request_context().market

    site_object = dc_admin.get_site(brand=brand, site=site, week=week)

    if not site_object:
        return []

    bob_code = site_object.bob_code

    subq = PoVoidModel.base_select(PoVoidModel.sku_code).where(
        PoVoidModel.week == str_week,
        PoVoidModel.brand == brand,
        PoVoidModel.dc == site,
        PoVoidModel.po_number == po_number,
        PoVoidModel.market == market,
    )

    skus = (
        sqla.select(CulinarySkuModel.sku_name)
        .distinct()
        .join(PurchaseOrderSkuModel, onclause=PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
        .join(
            PurchaseOrderModel,
            onclause=sqla.and_(
                PurchaseOrderSkuModel.po_uuid == PurchaseOrderModel.po_uuid,
                PurchaseOrderModel.bob_code == bob_code,
                PurchaseOrderModel.brand == brand,
                PurchaseOrderModel.week == str_week,
                PurchaseOrderModel.po_number == po_number,
                ~PurchaseOrderModel.deleted,
                PurchaseOrderModel.is_sent,
            ),
        )
        .where(
            CulinarySkuModel.sku_code.not_in(subq),
            CulinarySkuModel.market == market,
        )
    )

    return [sku.sku_name for sku in app_db.select_all(skus)]


def get_voided_po_sku(site: str, brand: str, weeks: Collection[ScmWeek]) -> set[PoSku]:
    market = context.get_request_context().market
    query = PoVoidModel.base_select(
        PoVoidModel.po_number,
        PoVoidModel.sku_code,
    ).where(
        PoVoidModel.dc == site,
        PoVoidModel.brand == brand,
        PoVoidModel.market == market,
        PoVoidModel.week.in_(list(map(str, weeks))),
    )
    return {PoSku(row.po_number, row.sku_code) for row in app_db.select_all(query)}


def get_voided_po_number_and_sku_name(
    weeks: tuple[ScmWeek, ...], site: str, brand: str, sku_codes: set[str] = None
) -> list[PoVoidDto]:
    market = context.get_request_context().market
    query = (
        PoVoidModel.base_select(
            PoVoidModel.id,
            PoVoidModel.sku_code,
            PoVoidModel.po_number,
            PoVoidModel.dc,
            PoVoidModel.brand,
            PoVoidModel.comment,
            PoVoidModel.supplier_name,
            PoVoidModel.user,
            PoVoidModel.cre_tmst,
            CulinarySkuModel.sku_name,
        )
        .join(
            PurchaseOrderModel,
            onclause=sqla.and_(
                PurchaseOrderModel.po_number == PoVoidModel.po_number,
                ~PurchaseOrderModel.deleted,
                PurchaseOrderModel.is_sent,
            ),
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(PoVoidModel.sku_code == CulinarySkuModel.sku_code, CulinarySkuModel.market == market),
        )
        .where(
            PoVoidModel.week.in_([str(week).upper() for week in weeks]),
            PoVoidModel.market == market,
            PoVoidModel.dc == site.upper(),
            PoVoidModel.brand == brand.upper(),
        )
    )

    if sku_codes is not None:
        if not sku_codes:
            return []
        query = query.where(PoVoidModel.sku_code.in_(sku_codes))

    return [_to_dto(row) for row in app_db.select_all(query)]


def get_po_void_by_po_number_and_sku_code(po_number: str, sku_code: str) -> list[PoVoidDto]:
    market = context.get_request_context().market
    query = PoVoidModel.base_select(
        PoVoidModel.id,
        PoVoidModel.sku_code,
        PoVoidModel.po_number,
        PoVoidModel.dc,
        PoVoidModel.brand,
        PoVoidModel.comment,
        PoVoidModel.supplier_name,
        PoVoidModel.user,
        PoVoidModel.cre_tmst,
        CulinarySkuModel.sku_name,
    ).join(
        CulinarySkuModel,
        onclause=sqla.and_(
            PoVoidModel.sku_code == CulinarySkuModel.sku_code,
            PoVoidModel.po_number == po_number,
            PoVoidModel.sku_code == sku_code,
            PoVoidModel.market == market,
        ),
    )

    return [_to_dto(row) for row in app_db.select_all(query)]


def get_voided_purchase_orders_by_po_sku_filter(dc_object: DcConfig, po_numbers: Select | set[str]) -> list[PoVoidDto]:
    market = context.get_request_context().market

    query = (
        PoVoidModel.base_select(
            PoVoidModel.id,
            PoVoidModel.sku_code,
            PoVoidModel.po_number,
            PoVoidModel.dc,
            PoVoidModel.brand,
            PoVoidModel.comment,
            PoVoidModel.supplier_name,
            PoVoidModel.user,
            PoVoidModel.cre_tmst,
            CulinarySkuModel.sku_name,
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(PoVoidModel.sku_code == CulinarySkuModel.sku_code, CulinarySkuModel.market == market),
        )
        .where(
            PoVoidModel.dc == dc_object.sheet_name,
            PoVoidModel.brand == dc_object.brand,
            PoVoidModel.market == market,
            PoVoidModel.po_number.in_(po_numbers),
        )
    )

    return [_to_dto(row) for row in app_db.select_all(query)]


def _get_po_void_info(where_conditions: list[Any]):
    market = context.get_request_context().market

    query = (
        PoVoidModel.base_select(
            PoVoidModel.id,
            PoVoidModel.sku_code,
            PoVoidModel.po_number,
            PoVoidModel.dc,
            PoVoidModel.brand,
            PoVoidModel.comment,
            PoVoidModel.supplier_name,
            PoVoidModel.user,
            PoVoidModel.cre_tmst,
            CulinarySkuModel.sku_name,
        )
        .join(
            PurchaseOrderModel,
            onclause=sqla.and_(
                PurchaseOrderModel.po_number == PoVoidModel.po_number,
                ~PurchaseOrderModel.deleted,
                PurchaseOrderModel.is_sent,
            ),
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                PoVoidModel.sku_code == CulinarySkuModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(
            *where_conditions,
            PoVoidModel.market == market,
        )
    )

    return [_to_dto(row) for row in app_db.select_all(query)]


def get_po_void_info(week: ScmWeek) -> list[PoVoidDto]:
    return _get_po_void_info([PoVoidModel.week == str(week)])


def get_po_void_info_by_date(change_date: date) -> list[PoVoidDto]:
    return _get_po_void_info(
        [PoVoidModel.last_updated >= change_date, PoVoidModel.last_updated < change_date + timedelta(days=1)]
    )


def get_po_void_week_by_id(po_void_id: int) -> ScmWeek:
    return ScmWeek.from_str(
        app_db.select_scalar(
            PoVoidModel.base_select(
                PoVoidModel.week,
            ).where(PoVoidModel.id == po_void_id)
        )
    )


def po_void_insert_many(po_voids_input: list[dict[ColumnElement, Any]]) -> None:
    with app_db.transaction() as transaction:
        app_db.apply_query(sqla.insert(PoVoidModel).values(po_voids_input), transaction=transaction)


def edit_po_void_comment(po_void_id: int, comment: str) -> None:
    query = (
        sqla.update(PoVoidModel)
        .where(PoVoidModel.id == po_void_id)
        .values({PoVoidModel.comment: comment, PoVoidModel.last_updated: datetime.now()})
    )
    app_db.apply_query(query)


def mark_po_void_as_deleted(po_void_id: int, deleted_by: str) -> None:
    query = (
        sqla.update(PoVoidModel)
        .where(PoVoidModel.id == po_void_id)
        .values(
            {
                PoVoidModel.deleted_by: deleted_by,
                PoVoidModel.deleted_ts: datetime.now(),
            }
        )
    )
    app_db.apply_query(query)


def get_po_void_by_weeks(weeks: Iterable[ScmWeek]) -> list[PoVoidDto]:
    market = context.get_request_context().market
    query = (
        PoVoidModel.base_select(
            PoVoidModel.id,
            PoVoidModel.sku_code,
            PoVoidModel.po_number,
            PoVoidModel.dc,
            PoVoidModel.brand,
            PoVoidModel.comment,
            PoVoidModel.supplier_name,
            PoVoidModel.user,
            PoVoidModel.cre_tmst,
            CulinarySkuModel.sku_name,
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                PoVoidModel.sku_code == CulinarySkuModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(
            PoVoidModel.week.in_(list(map(str, weeks))),
            PoVoidModel.market == market,
        )
    )
    return [_to_dto(row) for row in app_db.select_all(query)]
