from collections.abc import Collection
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.constants.hellofresh_constant import IngredientCategory, UnitOfMeasure
from procurement.core.request_utils import context
from procurement.core.typing import CATEGORY_NAME, SKU_CODE
from procurement.data.dto.ordering.culinary_sku import CulinarySku
from procurement.data.dto.sku import SkuCodeName, SkuCodeNameStatusCategory, SkuDetail, SkuMeta, SkuMetaWithBrands
from procurement.data.models.inventory.brand import BrandModel
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
    PurchasingSubcategoryModel,
)
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.pimt.replenishment import MasterReplenishmentModel
from procurement.services.database import app_db


def upsert_culinary_sku(culinary_sku: dict[ColumnElement, Any]) -> None:
    existing_data = app_db.select_scalar(
        sqla.select(CulinarySkuModel.last_updated).where(
            CulinarySkuModel.sku_uuid == culinary_sku[CulinarySkuModel.sku_uuid]
        )
    )
    if not existing_data or existing_data <= culinary_sku[CulinarySkuModel.last_updated]:
        _upsert_culinary_sku(culinary_sku)


def _upsert_culinary_sku(culinary_sku: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(CulinarySkuModel).values(culinary_sku)

    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[CulinarySkuModel.sku_uuid],
            set_=CulinarySkuModel.get_excluded_columns(query),
        )
    )


def get_sku_meta_by_code() -> dict[SKU_CODE, SkuMetaWithBrands]:
    market = context.get_request_context().market

    return {
        i.sku_code: SkuMetaWithBrands(
            sku_code=i.sku_code,
            sku_uuid=i.sku_uuid,
            sku_name=i.sku_name,
            unit=UnitOfMeasure.value_of(i.unit),
            brands=i.brands,
        )
        for i in app_db.select_all(
            sqla.select(
                CulinarySkuModel.sku_uuid,
                CulinarySkuModel.sku_code,
                CulinarySkuModel.sku_name,
                CulinarySkuModel.unit,
                CulinarySkuModel.brands,
            ).where(CulinarySkuModel.market == market)
        )
    }


def _get_base_culinary_sku_query(site: str | None, additional_sku_fields: Collection[ColumnElement]) -> Select:
    market = context.get_request_context().market
    query = (
        sqla.select(
            CulinarySkuModel.sku_id,
            CulinarySkuModel.sku_code,
            CulinarySkuModel.sku_name,
            *additional_sku_fields,
            PurchasingCategoryModel.name.label("purchasing_category"),
            PurchasingSubcategoryModel.name.label("purchasing_subcategory"),
            (CommodityGroupModel.group_name if site else sqla.literal_column("NULL")).label("commodity_group"),
        )
        .where(CulinarySkuModel.market == market)
        .join(
            PurchasingCategoryModel,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
            isouter=True,
        )
        .join(
            PurchasingSubcategoryModel,
            onclause=(CulinarySkuModel.purchasing_subcategory_id == PurchasingSubcategoryModel.id),
            isouter=True,
        )
    )
    if site:
        query = query.join(
            IngredientSiteCommodityGroupModel,
            onclause=(
                (CulinarySkuModel.sku_code == IngredientSiteCommodityGroupModel.sku_code)
                & (IngredientSiteCommodityGroupModel.site == site)
            ),
            isouter=True,
        ).join(
            CommodityGroupModel,
            onclause=(IngredientSiteCommodityGroupModel.commodity_group_id == CommodityGroupModel.id),
            isouter=True,
        )
    if MasterReplenishmentModel.replenishment_type in additional_sku_fields:
        query = query.join(
            MasterReplenishmentModel,
            onclause=(
                (CulinarySkuModel.sku_code == MasterReplenishmentModel.sku_code)
                & (MasterReplenishmentModel.site == site)
            ),
            isouter=True,
        )
    return query


def get_culinary_sku_by_sku_codes(site: str, sku_codes: set[str]) -> list[CulinarySku]:
    query = _get_base_culinary_sku_query(
        site=site,
        additional_sku_fields={CulinarySkuModel.storage_location, MasterReplenishmentModel.replenishment_type},
    )
    return [
        CulinarySku(
            sku_id=item.sku_id,
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            storage_location=item.storage_location,
            purchasing_category=item.purchasing_category,
            commodity_group=item.commodity_group,
            replenishment_type=item.replenishment_type,
        )
        for item in app_db.select_all(query.where(CulinarySkuModel.sku_code.in_(sku_codes)))
    ]


def get_culinary_category_by_sku_codes(sku_codes: set[str]) -> dict[SKU_CODE, CATEGORY_NAME]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            CulinarySkuModel.sku_code,
            PurchasingCategoryModel.name,
        )
        .join(
            PurchasingCategoryModel,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
        )
        .where(CulinarySkuModel.market == market, CulinarySkuModel.sku_code.in_(sku_codes))
    )
    return {item.sku_code: item.name for item in app_db.select_all(query)}


def get_sku_name_by_sku_codes(sku_codes: Collection[str] | None = None) -> list[SkuCodeName]:
    market = context.get_request_context().market
    query = (
        sqla.select(CulinarySkuModel.sku_code, sqla.func.max(CulinarySkuModel.sku_name).label("sku_name"))
        .where(CulinarySkuModel.market == market)
        .group_by(CulinarySkuModel.sku_code)
    )
    if sku_codes:
        query = query.where(CulinarySkuModel.sku_code.in_(sku_codes))

    return [SkuCodeName(sku_code=item.sku_code, sku_name=item.sku_name) for item in app_db.select_all(query)]


def get_sku_meta(
    site: str | None, sku_codes: Collection[str] = None, ignore_category: IngredientCategory | None = None
) -> tuple[SkuMeta, ...]:
    query = _get_base_culinary_sku_query(
        site=site,
        additional_sku_fields={CulinarySkuModel.unit, CulinarySkuModel.brands, CulinarySkuModel.is_manufactured_sku},
    )
    if ignore_category:
        query = query.where(PurchasingCategoryModel.name.is_(None) | (PurchasingCategoryModel.name != ignore_category))
    if sku_codes is not None:
        query = query.where(CulinarySkuModel.sku_code.in_(sku_codes))
    res = app_db.select_all(query)
    return tuple(
        SkuMeta(
            sku_id=row.sku_id,
            sku_code=row.sku_code,
            sku_name=row.sku_name,
            unit=UnitOfMeasure.inexact_value_of(row.unit),
            category=row.purchasing_category,
            subcategory=row.purchasing_subcategory,
            commodity_group=row.commodity_group,
            brands=set(row.brands) if row.brands else set(),
            is_manufactured_sku=row.is_manufactured_sku,
        )
        for row in res
    )


def get_sku_codes_statuses_categories_by_name(brand: str, search_key: str = None) -> list[SkuCodeNameStatusCategory]:
    market = context.get_request_context().market
    brand_name = sqla.select(BrandModel.name).where(BrandModel.id == brand, BrandModel.market == market).subquery()
    query = (
        sqla.select(
            CulinarySkuModel.sku_name,
            CulinarySkuModel.sku_code,
            CulinarySkuModel.status,
            PurchasingCategoryModel.name.label("purchasing_category"),
        )
        .join(
            PurchasingCategoryModel,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
            isouter=True,
        )
        .where(CulinarySkuModel.market == market, brand_name == sqla.any_(CulinarySkuModel.brands))
    )
    if search_key:
        query.where(CulinarySkuModel.sku_name.ilike(f"%{search_key}%"))
    return [
        SkuCodeNameStatusCategory(
            sku_name=item.sku_name, sku_code=item.sku_code, sku_status=item.status, category=item.purchasing_category
        )
        for item in app_db.select_all(query)
    ]


def get_skus_details() -> list[SkuDetail]:
    query = sqla.select(
        CulinarySkuModel.sku_code,
        CulinarySkuModel.sku_uuid,
        CulinarySkuModel.sku_id,
        CulinarySkuModel.sku_name,
        PurchasingCategoryModel.name.label("purchasing_category"),
    ).join(
        PurchasingCategoryModel,
        onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
        isouter=True,
    )
    return [
        SkuDetail(
            sku_code=item.sku_code,
            sku_uuid=item.sku_uuid,
            sku_id=item.sku_id,
            sku_name=item.sku_name,
            purchasing_category=item.purchasing_category,
        )
        for item in app_db.select_all(query)
    ]
