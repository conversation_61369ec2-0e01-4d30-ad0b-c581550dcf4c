from typing import NamedTuple

from procurement.client.snowflake import snowflake_service


class StrategyManagerItem(NamedTuple):
    sku_code: str
    manager: str


def get_strategy_managers() -> set[StrategyManagerItem]:
    query = """
    SELECT FULL_SKU, MANAGER_STRATEGY
    FROM US_OPS_ANALYTICS.PROCUREMENT.PROCUREMENT_STRATEGY_CONTACTS
    WHERE (FULL_SKU, MANAGER_STRATEGY, UPDATED_AT) IN
    (SELECT FULL_SKU, MANAGER_STRATEGY, MAX(UPDATED_AT) AS UPDATED_AT
    FROM US_OPS_ANALYTICS.PROCUREMENT.PROCUREMENT_STRATEGY_CONTACTS
    GROUP BY FULL_SKU, MANAGER_STRATEGY)
    """
    return {StrategyManagerItem(sku_code=row[0], manager=row[1]) for row in snowflake_service.select_all(query)}
