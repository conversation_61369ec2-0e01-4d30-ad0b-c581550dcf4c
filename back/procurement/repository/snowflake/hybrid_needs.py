from collections.abc import Iterable
from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

from procurement.client.snowflake import snowflake_service
from procurement.core.dates.weeks import ScmWeek


class HybridNeedSnowflakeItem(NamedTuple):
    sku_code: str
    site: str
    day: datetime
    daily_need: Decimal
    status: str
    live_usage: Decimal


def get_hybrid_needs_from_snowflake(week: ScmWeek, site: str, brand: str) -> Iterable[HybridNeedSnowflakeItem]:
    query = """
    SELECT SKU_CODE, PROD_DATE, DAILY_NEED, SOURCE, LIVE_USAGE
    FROM US_OPS_ANALYTICS.PROCUREMENT.V_HYBRID_NEEDS
    WHERE HELLOFRESH_WEEK = %(week)s
    AND DC = %(site)s
    AND BRAND = %(brand)s;
    """
    return (
        HybridNeedSnowflakeItem(
            sku_code=row[0],
            site=site,
            day=row[1],
            daily_need=Decimal(str(row[2])),
            status=row[3],
            live_usage=Decimal(str(row[4])),
        )
        for row in snowflake_service.select_all(query, {"week": str(week), "site": site, "brand": brand})
    )
