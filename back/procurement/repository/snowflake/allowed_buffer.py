from collections.abc import Iterable

from procurement.client.snowflake import snowflake_service
from procurement.core.dates.weeks import ScmWeek
from procurement.data.dto.ordering.allowed_produce_buffer import AllowedProduceBuffer


def get_allowed_buffers_from_snowflake(start_week: ScmWeek, end_week: ScmWeek) -> Iterable[AllowedProduceBuffer]:
    query = """
    SELECT (YEAR * 100 + WEEK) as scm_week, BRAND, DC, SKU, TOTAL_BUFFER
    FROM US_OPS_ANALYTICS.PROCUREMENT.BUFFERS
    WHERE scm_week >= %(start_week)s
    AND scm_week <= %(end_week)s
    AND BRAND IS NOT NULL
    AND DC IS NOT NULL
    AND SKU IS NOT NULL
    AND TOTAL_BUFFER IS NOT NULL;
    """
    return (
        AllowedProduceBuffer(
            week=ScmWeek.from_number(row[0]), brand=row[1], site=row[2], sku_code=row[3], total_buffer=row[4]
        )
        for row in snowflake_service.select_all(query, {"start_week": int(start_week), "end_week": int(end_week)})
    )
