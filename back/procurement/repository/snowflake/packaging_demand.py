from collections.abc import Iterable

from procurement.client.snowflake import snowflake_service
from procurement.core.dates import ScmWeek
from procurement.data.dto.snowflake.packaging_demand import CanadaPackagingDemandDto


def get_packaging_demand_snowflake(week: ScmWeek) -> Iterable[CanadaPackagingDemandDto]:
    query = """
    WITH latest_records AS (
        SELECT
            *,
            RANK() OVER (
                PARTITION BY WEEK, REGION, ROUTE, SIZE, BRAND, IMT_DAY, COMPONENT
                ORDER BY INSERT_TIME DESC
            ) as rnk
        FROM CA_OPS_ANALYTICS.FINCHER.SKU_DEMAND_OUTPUT
        WHERE WEEK >= %(week)s
        AND SKU_CODE IS NOT NULL
    )
    SELECT
        BRAND,
        REGION,
        SKU_CODE,
        WEEK,
        IMT_DAY,
        SUM(QUANTITY)
    FROM latest_records
    WHERE rnk = 1
    GROUP BY 1, 2, 3, 4, 5
    """
    return (
        CanadaPackagingDemandDto(
            brand=row[0],
            site=row[1],
            sku_code=row[2],
            week=row[3],
            weekday=row[4],
            quantity=row[5],
        )
        for row in snowflake_service.select_all(query, {"week": str(week)})
    )
