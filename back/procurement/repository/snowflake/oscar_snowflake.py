import dataclasses
from collections.abc import Iterable

from procurement.client.snowflake import snowflake_service
from procurement.constants.hellofresh_constant import DEPLETION_WEEKS_AHEAD
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.managers.admin import brand_admin


@dataclasses.dataclass
class UsForecast:
    sku_code: str
    site: str
    scm_week: str
    quantity: int


@dataclasses.dataclass
class CanadaForecast:
    sku_code: str
    site: str
    scm_week: ScmWeek
    quantity: int
    day: str
    recipe: str
    recipe_name: str
    size: str
    picks: int


def get_forecasts(week: ScmWeek, brand: str, sites: list[str]) -> Iterable[UsForecast]:
    query = """
    SELECT PURCHASE_SKU_CODE, DC, SUM(FORECAST), HELLOFRESH_WEEK
    FROM US_OPS_ANALYTICS.FORECAST.MOST_RECENT_FORECAST
    WHERE HELLOFRESH_WEEK >= %(week)s
        AND HELLOFRESH_WEEK <= %(end_week)s
        AND DC IN (%(sites)s)
        AND country = %(country_code)s
    GROUP BY (PURCHASE_SKU_CODE, DC, HELLOFRESH_WEEK);
    """
    country_code = brand_admin.get_brands(week=week)[brand].country_code
    return (
        UsForecast(
            sku_code=row[0],
            site=row[1],
            quantity=utils.ceil_subone_integer(row[2]),
            scm_week=row[3],
        )
        for row in snowflake_service.select_all(
            query,
            {
                "week": str(week),
                "end_week": str(week + DEPLETION_WEEKS_AHEAD),
                "sites": sites,
                "country_code": country_code,
            },
        )
        if row[2] is not None
    )


def get_ca_staging_forecasts(week_from: ScmWeek, week_to: ScmWeek, sites: list[str]) -> Iterable[CanadaForecast]:
    query = """
    SELECT SKU_ID, DC, SUM(TOTAL_VOLUME), WEEK, DAY, SLOT, RECIPE_ID, SIZE, PICKS
    FROM CA_OPS_ANALYTICS.VIEWS.XPS_SKU_LEVEL_FORECAST
    WHERE WEEK >= %(week)s AND WEEK <= %(end_week)s AND DC IN (%(sites)s)
    GROUP BY SKU_ID, DC, WEEK, DAY, SLOT, RECIPE_ID, SIZE, PICKS
    """
    return (
        CanadaForecast(
            sku_code=row[0],
            site=row[1],
            quantity=utils.ceil_subone_integer(row[2]),
            scm_week=ScmWeek.from_str(row[3]),
            day=row[4],
            recipe=row[5],
            recipe_name=row[6],
            size=row[7],
            picks=int(row[8]),
        )
        for row in snowflake_service.select_all(
            query, {"week": str(week_from), "sites": sites, "end_week": str(week_to)}
        )
        if row[2] is not None
    )
