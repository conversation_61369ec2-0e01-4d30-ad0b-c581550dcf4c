from collections.abc import Iterable
from decimal import Decimal

from procurement.client.snowflake import snowflake_service
from procurement.data.dto.inventory.allocation_tool_price import AllocationPriceInput


def get_allocation_prices_from_snowflake(brand: str) -> Iterable[AllocationPriceInput]:
    query = """
    SELECT (YEAR * 100 + WEEK) as scm_week, BRAND, DC, SKU, UNIT_PRICE, AWARD_PERCENTAGE
    FROM US_OPS_ANALYTICS.PROCUREMENT.PROCUREMENT_RFP
    WHERE BRAND = %(brand)s
    AND STATUS IN ('Post Lock', 'Post Lock Network Shift', 'Locked')
    AND AWARD_PERCENTAGE IS NOT NULL
    AND UNIT_PRICE IS NOT NULL;
    """
    return (
        AllocationPriceInput(
            week=row[0],
            brand=row[1],
            site=row[2],
            sku_code=row[3],
            unit_price=Decimal(str(row[4])),
            award_percentage=Decimal(str(row[5])),
        )
        for row in snowflake_service.select_all(query, {"brand": brand})
    )
