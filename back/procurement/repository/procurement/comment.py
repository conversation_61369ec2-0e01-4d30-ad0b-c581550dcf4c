from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.constants.hellofresh_constant import Domain
from procurement.core import dates as date_util
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.procurement.comment import Comment, CommentPreView, CommentView
from procurement.data.models.constants import CommentType
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
)
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.social.comment import CommentModel
from procurement.repository.procurement.context import CommentContext
from procurement.services.database import app_db


def _to_dto(row: Row) -> Comment:
    return Comment(
        comment_id=row.id,
        domain=row.domain,
        brand=row.brand,
        site=row.site,
        region=row.site,
        sku_code=row.sku_code,
        sku_name=row.sku_name,
        resource_type=row.resource_type,
        comment=row.comment,
        week=row.week,
        last_updated=row.last_updated,
        updated_by=row.updated_by,
        po_number=row.resource_id,
        commodity_group_name=row.commodity_group_name,
        purchasing_category_name=row.purchasing_category_name,
    )


def get_sku_comments_log(domain: Domain, week: date_util.ScmWeek) -> list[Comment]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            CommentModel,
            CulinarySkuModel,
            PurchasingCategoryModel.name.label("purchasing_category_name"),
            CommodityGroupModel.group_name.label("commodity_group_name"),
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                CommentModel.resource_id == CulinarySkuModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .join(
            PurchasingCategoryModel,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
            isouter=True,
        )
        .join(
            IngredientSiteCommodityGroupModel,
            onclause=sqla.and_(
                CulinarySkuModel.sku_code == IngredientSiteCommodityGroupModel.sku_code,
                CommentModel.site == IngredientSiteCommodityGroupModel.site,
            ),
            isouter=True,
        )
        .join(
            CommodityGroupModel,
            onclause=(IngredientSiteCommodityGroupModel.commodity_group_id == CommodityGroupModel.id),
            isouter=True,
        )
        .where(
            CommentModel.domain == domain,
            CommentModel.week == week.to_number_format(),
            CommentModel.resource_type.in_([CommentType.SKU, CommentType.WH_SKU]),
        )
    )
    return [_to_dto(item) for item in app_db.select_all(query)]


def get_po_comments_log(domain: Domain, week: ScmWeek) -> list[Comment]:
    query = (
        sqla.select(
            CommentModel,
            CulinarySkuModel.sku_code.label("sku_code"),
            CulinarySkuModel.sku_name.label("sku_name"),
            sqla.sql.expression.null().label("purchasing_category_name"),
            sqla.sql.expression.null().label("commodity_group_name"),
        )
        .join_from(
            CommentModel,
            PurchaseOrderModel,
            onclause=sqla.and_(
                CommentModel.resource_id == PurchaseOrderModel.po_number,
                PurchaseOrderModel.week == str(week),
            ),
        )
        .join_from(
            PurchaseOrderModel,
            PurchaseOrderSkuModel,
            onclause=(PurchaseOrderSkuModel.po_uuid == PurchaseOrderModel.po_uuid),
        )
        .join_from(
            PurchaseOrderSkuModel,
            CulinarySkuModel,
            onclause=(PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid),
        )
        .where(
            CommentModel.domain == domain,
            CommentModel.week == week.to_number_format(),
            CommentModel.resource_type == CommentType.PO,
        )
    )
    return [_to_dto(item) for item in app_db.select_all(query)]


def upsert_comment(comment: dict[ColumnElement, Any]) -> int:
    comment[CommentModel.brand] = comment[CommentModel.brand] or ""
    query = psql_sqla.insert(CommentModel)
    query = (
        query.on_conflict_do_update(
            index_elements=[
                CommentModel.domain,
                CommentModel.resource_type,
                CommentModel.week,
                CommentModel.site,
                CommentModel.resource_id,
                CommentModel.brand,
            ],
            set_={
                CommentModel.comment: query.excluded.comment,
                CommentModel.last_updated: query.excluded.last_updated,
                CommentModel.updated_by: query.excluded.updated_by,
            },
        )
        .values(comment)
        .returning(CommentModel.id)
    )
    return app_db.apply_query(query)[0].id


def get_comments_view(comment_context: CommentContext) -> list[CommentView]:
    query = sqla.select(
        CommentModel.comment,
        CommentModel.last_updated,
        CommentModel.updated_by,
        CommentModel.site,
    ).where(
        CommentModel.domain == comment_context.domain,
        CommentModel.week == comment_context.week.to_number_format(),
        CommentModel.resource_type == comment_context.resource_type,
        CommentModel.brand == (comment_context.brand or ""),
        CommentModel.resource_id == comment_context.resource_id,
        CommentModel.site.in_(comment_context.sites),
    )
    return [
        CommentView(
            site=item.site,
            comment=item.comment,
            last_updated=item.last_updated,
            updated_by=item.updated_by,
        )
        for item in app_db.select_all(query)
    ]


def get_preview(comment_context: CommentContext) -> list[CommentPreView]:
    query = sqla.select(CommentModel.resource_id, CommentModel.site, CommentModel.week, CommentModel.brand).where(
        CommentModel.domain == comment_context.domain,
        CommentModel.week.in_(list(map(int, comment_context.weeks))),
        CommentModel.resource_type == comment_context.resource_type,
        CommentModel.brand == (comment_context.brand or ""),
        CommentModel.site.in_(comment_context.sites),
    )
    return [
        CommentPreView(
            site=item.site, resource_id=item.resource_id, week=ScmWeek.from_number(item.week), brand=item.brand
        )
        for item in app_db.select_all(query)
    ]


def delete_comment(comment_context: CommentContext) -> int:
    return app_db.apply_query(
        sqla.delete(CommentModel)
        .where(
            CommentModel.domain == comment_context.domain,
            CommentModel.week == comment_context.week.to_number_format(),
            CommentModel.resource_type == comment_context.resource_type,
            CommentModel.brand == (comment_context.brand or ""),
            CommentModel.resource_id == comment_context.resource_id,
            CommentModel.site == comment_context.site,
        )
        .returning(CommentModel.id)
    )[0].id
