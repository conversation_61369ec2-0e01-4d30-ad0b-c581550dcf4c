import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.dto.procurement.network_depletion import UserPreset
from procurement.data.models.social.network_depletion_preset import NetworkDepletionPresetModel
from procurement.services.database import app_db


def get_user_presets(user_id: int, market: str) -> list[UserPreset] | None:
    return [
        UserPreset(
            preset_id=item.id,
            name=item.name,
            brands=item.configuration["brands"],
            warehouses=item.configuration["warehouses"],
        )
        for item in app_db.select_all(
            sqla.select(
                NetworkDepletionPresetModel.id,
                NetworkDepletionPresetModel.name,
                NetworkDepletionPresetModel.configuration,
            ).where(NetworkDepletionPresetModel.user_id == user_id, NetworkDepletionPresetModel.market == market),
        )
    ]


def get_user_preset_by_id(user_id: int, preset_id: int, market: str) -> UserPreset | None:
    preset = app_db.select_one(
        sqla.select(
            NetworkDepletionPresetModel.id, NetworkDepletionPresetModel.name, NetworkDepletionPresetModel.configuration
        ).where(
            NetworkDepletionPresetModel.user_id == user_id,
            NetworkDepletionPresetModel.id == preset_id,
            NetworkDepletionPresetModel.market == market,
        ),
    )
    if preset:
        return UserPreset(
            preset_id=preset.id,
            name=preset.name,
            brands=preset.configuration["brands"],
            warehouses=preset.configuration["warehouses"],
        )
    return None


def update_presets(user_presets: list[UserPreset], user_id: int, market: str):
    query = psql_sqla.insert(NetworkDepletionPresetModel)
    query = query.on_conflict_do_update(
        index_elements=[
            NetworkDepletionPresetModel.id,
            NetworkDepletionPresetModel.user_id,
            NetworkDepletionPresetModel.market,
        ],
        set_={
            NetworkDepletionPresetModel.name: query.excluded.name,
            NetworkDepletionPresetModel.configuration: query.excluded.configuration,
        },
    ).values(
        [
            {
                NetworkDepletionPresetModel.id: preset.preset_id,
                NetworkDepletionPresetModel.name: preset.name,
                NetworkDepletionPresetModel.user_id: user_id,
                NetworkDepletionPresetModel.market: market,
                NetworkDepletionPresetModel.configuration: {"brands": preset.brands, "warehouses": preset.warehouses},
            }
            for preset in user_presets
        ]
    )
    app_db.apply_query(query)
