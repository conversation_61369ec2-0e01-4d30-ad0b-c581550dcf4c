from typing import Iterable, NamedTuple

from procurement.constants.hellofresh_constant import Domain
from procurement.core.dates import ScmWeek
from procurement.data.models.constants import CommentType


class CommentContext(NamedTuple):
    brand: str | None
    domain: Domain
    resource_type: CommentType
    week: ScmWeek | None = None
    weeks: tuple[ScmWeek, ...] | None = None
    site: str | None = None
    sites: Iterable[str] | None = None
    resource_id: str | None = None
