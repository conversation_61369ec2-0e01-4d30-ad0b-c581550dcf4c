from datetime import datetime, timedelta

import sqlalchemy as sqla

from procurement.core import config_utils
from procurement.core.cache_utils.factory import CACHES
from procurement.core.request_utils import context
from procurement.data.dto.procurement.parallel_sync_log import ParallelSyncLog
from procurement.data.models.social.parallel_sync_log import ParallelSyncLogModel, SyncStatus
from procurement.services.database import app_db


def get_by_name(name: str) -> ParallelSyncLog | None:
    if config_utils.is_ci:
        # Sync tests are polling job status every few seconds to check if it's
        # completed, cache would add delay to every sync test.
        return _query_by_name(name)
    return CACHES.job_step_info.get(name, lambda: _query_by_name(name))


def _query_by_name(name: str) -> ParallelSyncLog | None:
    log = app_db.select_one(
        sqla.select(ParallelSyncLogModel)
        .where(
            ParallelSyncLogModel.job_name == name,
            ParallelSyncLogModel.market == context.get_request_context().market,
        )
        .order_by(ParallelSyncLogModel.sync_time.desc())
    )
    return (
        ParallelSyncLog(
            id=log.id,
            job_name=log.job_name,
            sync_time=log.sync_time,
            sync_status=log.sync_status,
            parent_job_ids=log.parent_job_ids,
            warnings=log.warnings,
            global_parent_id=log.global_parent_id,
        )
        if log
        else None
    )


def get_child_jobs(job_id: int) -> list[ParallelSyncLog]:
    return [
        ParallelSyncLog(
            id=log.id,
            job_name=log.job_name,
            sync_time=log.sync_time,
            sync_status=log.sync_status,
            parent_job_ids=log.parent_job_ids,
            warnings=log.warnings,
            global_parent_id=log.global_parent_id,
        )
        for log in app_db.select_all(
            sqla.select(ParallelSyncLogModel).where(ParallelSyncLogModel.global_parent_id == job_id)
        )
    ]


def clean_up_parallel_sync_log():
    subquery = (
        sqla.select(sqla.func.max(ParallelSyncLogModel.id))
        .where(ParallelSyncLogModel.global_parent_id.is_(None))
        .group_by(ParallelSyncLogModel.job_name)
        .scalar_subquery()
    )

    app_db.apply_query(
        sqla.delete(ParallelSyncLogModel).where(
            ParallelSyncLogModel.sync_time < datetime.now() - timedelta(weeks=1),
            ParallelSyncLogModel.global_parent_id.is_not(None),
            ParallelSyncLogModel.global_parent_id.not_in(subquery),
        )
    )

    app_db.apply_query(
        sqla.delete(ParallelSyncLogModel).where(
            ParallelSyncLogModel.sync_time < datetime.now() - timedelta(weeks=1),
            ParallelSyncLogModel.global_parent_id.is_(None),
            ParallelSyncLogModel.id.not_in(subquery),
        )
    )


def insert_parallel_sync_logs(logs: list[dict]) -> None:
    app_db.apply_query(sqla.insert(ParallelSyncLogModel).values(logs))


def insert_parent_sync_log(log: dict) -> int:
    return app_db.apply_query(sqla.insert(ParallelSyncLogModel).values(log).returning(ParallelSyncLogModel.id))[0].id


def update_job_status(job_id: int, status: SyncStatus) -> None:
    app_db.apply_query(
        sqla.update(ParallelSyncLogModel)
        .where(ParallelSyncLogModel.id == job_id)
        .values({ParallelSyncLogModel.sync_status: status})
    )
