from typing import Any

import pytz
import sqlalchemy as sqla
import sqlalchemy.dialects.postgresql as psql
from pytz.tzinfo import BaseTzInfo
from sqlalchemy import ColumnElement

from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE
from procurement.data.dto.procurement.distribution_center import DcBobCode
from procurement.data.models.social.distribution_center import DcModel
from procurement.services.database import app_db


def upsert_dc(dc: dict[ColumnElement, Any]) -> None:
    query = psql.insert(DcModel).values(dc)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=DcModel.get_primary_key_columns(),
            set_=DcModel.get_excluded_columns(query),
        )
    )


def get_dc_names_and_bob_codes() -> list[DcBobCode]:
    return [
        DcBobCode(
            bob_code=item.bob_code,
            ot_name=item.name,
        )
        for item in app_db.select_all(sqla.select(DcModel.bob_code, DcModel.name))
    ]


def get_timezones(market: str | None = None) -> dict[BOB_CODE, BaseTzInfo]:
    market = market or context.get_request_context().market
    query = sqla.select(DcModel.bob_code, DcModel.tz).where(DcModel.market == market)
    return {r.bob_code: pytz.timezone(r.tz) for r in app_db.select_all(query)}
