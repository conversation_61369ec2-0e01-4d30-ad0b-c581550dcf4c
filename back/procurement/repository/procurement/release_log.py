from collections import defaultdict
from collections.abc import Sequence
from datetime import date

import sqlalchemy as sqla
import sqlalchemy.dialects.postgresql as psql
from dateutil.relativedelta import relativedelta
from sqlalchemy import Row

from procurement.core.exceptions.validation_errors import InputParamsValidationException
from procurement.data.dto.procurement.release_log import ReleaseLog, ReleaseLogFeature
from procurement.data.models.social.release_log import ReleaseLogModel
from procurement.services.database import app_db

PAGE_SIZE = 10


def get_release_logs_page(dc_user_only: bool, date_from: date | None) -> list[ReleaseLog]:
    query = sqla.select(ReleaseLogModel).order_by(sqla.desc(ReleaseLogModel.release_date), ReleaseLogModel.feature_key)
    if dc_user_only:
        query = query.where(ReleaseLogModel.dc_users_available)
    dates = _get_page_dates(dc_user_only, date_from)
    if not dates:
        return []
    query = query.where(ReleaseLogModel.release_date <= dates[0], ReleaseLogModel.release_date >= dates[-1])
    return _parse_db_rows(app_db.select_all(query))


def get_page_keys(dc_user_only: bool) -> list[date]:
    query = sqla.select(ReleaseLogModel.release_date.distinct()).order_by(sqla.desc(ReleaseLogModel.release_date))
    if dc_user_only:
        query = query.where(ReleaseLogModel.dc_users_available)
    all_dates = app_db.select_all(query)
    return [all_dates[i].release_date for i in range(0, len(all_dates), PAGE_SIZE)]


def get_latest_releases(dc_user_only: bool) -> list[ReleaseLog]:
    query = (
        sqla.select(ReleaseLogModel)
        .order_by(sqla.desc(ReleaseLogModel.release_date), ReleaseLogModel.feature_key)
        .where(ReleaseLogModel.release_date > date.today() - relativedelta(months=2))
    )
    if dc_user_only:
        query = query.where(ReleaseLogModel.dc_users_available)
    return _parse_db_rows(app_db.select_all(query))


def _get_page_dates(dc_user_only: bool, date_from: date | None):
    date_query = (
        sqla.select(ReleaseLogModel.release_date.distinct())
        .order_by(sqla.desc(ReleaseLogModel.release_date))
        .limit(PAGE_SIZE)
    )
    if dc_user_only:
        date_query = date_query.where(ReleaseLogModel.dc_users_available)
    if date_from:
        date_query = date_query.where(ReleaseLogModel.release_date <= date_from)

    return [record.release_date for record in app_db.select_all(date_query)]


def _parse_db_rows(raw_records: Sequence[Row]) -> list[ReleaseLog]:
    release_logs = defaultdict(list)
    for record in raw_records:
        feature = ReleaseLogFeature(
            feature_key=record.feature_key,
            feature_type=record.feature_type,
            description=record.description,
            dc_users_available=record.dc_users_available,
        )
        release_logs[record.release_date].append(feature)

    return [ReleaseLog(release_date=release_date, features=features) for release_date, features in release_logs.items()]


def create_release_log(features: list[ReleaseLogFeature], release_date: date) -> None:
    release_log_exists = app_db.select_scalar(
        sqla.select(sqla.exists().where(ReleaseLogModel.release_date == release_date))
    )
    if release_log_exists:
        raise InputParamsValidationException(f"A release log with the date {release_date} already exists.")
    for feature_key, feature in enumerate(features, 1):
        release_log_query = sqla.insert(ReleaseLogModel).values(
            feature_key=feature_key,
            feature_type=feature.feature_type,
            release_date=release_date,
            dc_users_available=feature.dc_users_available,
            description=feature.description,
        )
        app_db.apply_query(release_log_query)


def update_release_log(
    release_date: date,
    features: list[ReleaseLogFeature],
) -> None:
    release_log_exists = app_db.select_scalar(
        sqla.select(sqla.exists().where(ReleaseLogModel.release_date == release_date))
    )
    if not release_log_exists:
        raise InputParamsValidationException(f"Release log with release date {release_date} does not exist.")

    updated_feature_keys = []
    for feature_key, feature in enumerate(features, 1):
        upsert_query = (
            psql.insert(ReleaseLogModel)
            .values(
                feature_key=feature_key,
                feature_type=feature.feature_type,
                release_date=release_date,
                dc_users_available=feature.dc_users_available,
                description=feature.description,
            )
            .on_conflict_do_update(
                index_elements=["release_date", "feature_key"],
                set_={
                    "feature_type": feature.feature_type,
                    "dc_users_available": feature.dc_users_available,
                    "description": feature.description,
                },
            )
        )
        app_db.apply_query(upsert_query)
        updated_feature_keys.append(feature_key)

    delete_query = sqla.delete(ReleaseLogModel).where(
        ReleaseLogModel.release_date == release_date, ReleaseLogModel.feature_key.not_in(updated_feature_keys)
    )
    app_db.apply_query(delete_query)
