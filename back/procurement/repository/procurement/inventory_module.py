from datetime import date

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.typing import BRAND, SITE, SKU_CODE
from procurement.data.dto.procurement.inventory_module import (
    InventoryModuleComment,
    InventoryModuleCommentInput,
    InventoryModuleInHouseAdjustment,
    InventoryModuleInHouseAdjustmentInput,
)
from procurement.data.models.social.inventory_module import (
    InventoryModuleCommentModel,
    InventoryModuleInHouseAdjustmentModel,
)
from procurement.data.models.social.user import UserModel
from procurement.services.database import app_db


def get_comments_for_brand_and_site(brand: BRAND, site: SITE) -> list[InventoryModuleComment]:
    return [
        InventoryModuleComment(
            sku_code=item.sku_code,
            text=item.text,
            last_edited_by_email=item.email,
        )
        for item in app_db.select_all(
            sqla.select(
                InventoryModuleCommentModel.sku_code,
                InventoryModuleCommentModel.text,
                UserModel.email,
            )
            .join(UserModel, onclause=(UserModel.id == InventoryModuleCommentModel.last_edited_by))
            .where(InventoryModuleCommentModel.site == site, InventoryModuleCommentModel.brand == brand),
        )
    ]


def add_comment(comment: InventoryModuleCommentInput) -> None:
    query = psql_sqla.insert(InventoryModuleCommentModel)
    query = query.on_conflict_do_update(
        index_elements=[
            InventoryModuleCommentModel.sku_code,
            InventoryModuleCommentModel.site,
            InventoryModuleCommentModel.brand,
        ],
        set_={
            InventoryModuleCommentModel.last_edited_by: query.excluded.last_edited_by,
            InventoryModuleCommentModel.text: query.excluded.text,
        },
    ).values(
        {
            InventoryModuleCommentModel.sku_code: comment.sku_code,
            InventoryModuleCommentModel.site: comment.site,
            InventoryModuleCommentModel.brand: comment.brand,
            InventoryModuleCommentModel.last_edited_by: comment.last_edited_by_id,
            InventoryModuleCommentModel.text: comment.text,
        }
    )
    app_db.apply_query(query)


def delete_in_house_adjustment_by_brand_site_and_sku(brand: BRAND, site: SITE, sku_code: SKU_CODE) -> None:
    app_db.apply_query(
        sqla.delete(InventoryModuleInHouseAdjustmentModel).where(
            InventoryModuleInHouseAdjustmentModel.sku_code == sku_code,
            InventoryModuleInHouseAdjustmentModel.brand == brand,
            InventoryModuleInHouseAdjustmentModel.site == site,
            InventoryModuleInHouseAdjustmentModel.adjustment_date == date.today(),
        )
    )


def get_in_house_adjustment_for_brand_and_site(brand: BRAND, site: SITE) -> list[InventoryModuleInHouseAdjustment]:
    return [
        InventoryModuleInHouseAdjustment(
            sku_code=item.sku_code,
            value=item.in_house_adjustment,
        )
        for item in app_db.select_all(
            sqla.select(
                InventoryModuleInHouseAdjustmentModel.sku_code,
                InventoryModuleInHouseAdjustmentModel.in_house_adjustment,
            ).where(
                InventoryModuleInHouseAdjustmentModel.site == site,
                InventoryModuleInHouseAdjustmentModel.brand == brand,
                InventoryModuleInHouseAdjustmentModel.adjustment_date == date.today(),
            ),
        )
    ]


def add_in_house_adjustment(in_house_adjustment: InventoryModuleInHouseAdjustmentInput) -> None:
    query = psql_sqla.insert(InventoryModuleInHouseAdjustmentModel)
    query = query.on_conflict_do_update(
        index_elements=[
            InventoryModuleInHouseAdjustmentModel.sku_code,
            InventoryModuleInHouseAdjustmentModel.site,
            InventoryModuleInHouseAdjustmentModel.brand,
        ],
        set_={
            InventoryModuleInHouseAdjustmentModel.in_house_adjustment: query.excluded.in_house_adjustment,
            InventoryModuleInHouseAdjustmentModel.adjustment_date: date.today(),
        },
    ).values(
        {
            InventoryModuleInHouseAdjustmentModel.sku_code: in_house_adjustment.sku_code,
            InventoryModuleInHouseAdjustmentModel.site: in_house_adjustment.site,
            InventoryModuleInHouseAdjustmentModel.brand: in_house_adjustment.brand,
            InventoryModuleInHouseAdjustmentModel.in_house_adjustment: in_house_adjustment.in_house_adjustment,
            InventoryModuleInHouseAdjustmentModel.adjustment_date: date.today(),
        }
    )
    app_db.apply_query(query)
