import json
from datetime import datetime

import sqlalchemy as sqla

from procurement.core.exceptions.db_errors import ResourceNotFound
from procurement.data.dto.procurement.user_view_state import UserViewState
from procurement.data.models.social.user_view_state import UserViewStateModel
from procurement.services.database import app_db


def get_states(user_id: int, resource_id: str) -> list[UserViewState]:
    return [
        UserViewState(state_id=item.id, name=item.name)
        for item in app_db.select_all(
            sqla.select(UserViewStateModel.id, UserViewStateModel.name).where(
                UserViewStateModel.user_id == user_id, UserViewStateModel.resource == resource_id
            )
        )
    ]


def get_state_by_id(_id: int) -> UserViewState | None:
    state = app_db.select_one(
        sqla.select(UserViewStateModel.id, UserViewStateModel.name, UserViewStateModel.state).where(
            UserViewStateModel.id == _id
        )
    )
    if state:
        return UserViewState(state_id=state.id, name=state.name, state=json.loads(state.state))
    raise ResourceNotFound("User view state not found")


def add_state(user_id: int, resource: str, name: str, state: dict) -> int:
    state = app_db.apply_query(
        sqla.insert(UserViewStateModel)
        .values(
            {
                UserViewStateModel.user_id: user_id,
                UserViewStateModel.resource: resource,
                UserViewStateModel.name: name,
                UserViewStateModel.state: json.dumps(state),
                UserViewStateModel.last_usage: datetime.now(),
            }
        )
        .returning(UserViewStateModel.id)
    )[0]
    return state.id


def update_state(_id: int, state: dict | None = None, name: str | None = None) -> None:
    update_data = {}
    if state:
        update_data[UserViewStateModel.state] = json.dumps(state)
    if name:
        update_data[UserViewStateModel.name] = name
    update_data[UserViewStateModel.last_usage] = datetime.now()
    app_db.apply_query(sqla.update(UserViewStateModel).where(UserViewStateModel.id == _id).values(update_data))


def delete_state(_id: int) -> None:
    app_db.apply_query(sqla.delete(UserViewStateModel).where(UserViewStateModel.id == _id))
