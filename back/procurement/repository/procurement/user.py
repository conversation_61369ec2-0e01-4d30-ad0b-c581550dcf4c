from collections.abc import Iterable
from datetime import datetime
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.auth.permissions import DC_USER_PERMISSIONS
from procurement.core.typing import MARKET
from procurement.data.dto.procurement.user import Role, User, UserIdEmail
from procurement.data.models.social.user import (
    PermissionModel,
    PermissionRoleModel,
    RoleModel,
    UserModel,
    UserRoleModel,
)
from procurement.services.database import app_db


def get_user_by_id(user_id) -> User | None:
    user = app_db.select_one(_get_base_user_query().where(UserModel.id == user_id))
    return _build_user(user) if user else None


def get_by_email(email: str) -> User | None:
    user = app_db.select_one(_get_base_user_query().where(UserModel.email == email))
    return _build_user(user) if user else None


def _get_base_user_query() -> Select:
    return sqla.select(UserModel.id, UserModel.email, UserModel.first_name, UserModel.last_name, UserModel.picture)


def _build_user(user: Row) -> User:
    return User(
        id=user.id, email=user.email, first_name=user.first_name, last_name=user.last_name, picture=user.picture
    )


def get_roles_by_fullname(roles_full_names: list[str]) -> list[Role]:
    query = sqla.select(RoleModel.id, RoleModel.full_name).where(RoleModel.full_name.in_(roles_full_names))
    return [Role(id=item.id, full_name=item.full_name) for item in app_db.select_all(query)]


def remove_user_roles(user_id: int, market: str) -> None:
    app_db.apply_query(
        sqla.delete(UserRoleModel).where(UserRoleModel.user_id == user_id, UserRoleModel.market == market)
    )


def add_user_role(user_id: int, roles: Iterable[int], market: str) -> None:
    app_db.apply_query(
        sqla.insert(UserRoleModel).values(
            [
                {UserRoleModel.user_id: user_id, UserRoleModel.role_id: role_id, UserRoleModel.market: market}
                for role_id in roles
            ]
        )
    )


def add_dc_role(full_name: str, short_name: str) -> None:
    permissions = []
    for permission in DC_USER_PERMISSIONS:
        if not permission.is_read_only:
            permissions.append(permission.write)
        permissions.append(permission.read)

    permission_ids = get_permission_ids(permissions)

    with app_db.transaction() as transaction:
        role_id = app_db.apply_query(
            sqla.insert(RoleModel)
            .values({RoleModel.full_name: full_name, RoleModel.short_name: short_name})
            .returning(RoleModel.id),
            transaction=transaction,
        )[0].id
        data = [
            {PermissionRoleModel.role_id: role_id, PermissionRoleModel.permission_id: permission_id}
            for permission_id in permission_ids
        ]
        app_db.apply_query(sqla.insert(PermissionRoleModel).values(data), transaction=transaction)


def get_permission_ids(permissions: list[str]) -> list[int]:
    return [
        item.id
        for item in app_db.select_all(sqla.select(PermissionModel.id).where(PermissionModel.name.in_(permissions)))
    ]


def is_role_exist(name: str) -> bool:
    return app_db.select_scalar(sqla.exists(RoleModel).select().where(RoleModel.full_name == name))


def get_user_permissions(user_email: str, market: str) -> list[str]:
    query = (
        sqla.select(PermissionModel.name)
        .join(PermissionRoleModel, onclause=(PermissionModel.id == PermissionRoleModel.permission_id))
        .join(RoleModel, onclause=(PermissionRoleModel.role_id == RoleModel.id))
        .join(UserRoleModel, onclause=(RoleModel.id == UserRoleModel.role_id))
        .join(UserModel, onclause=(UserRoleModel.user_id == UserModel.id))
        .where(UserModel.email == user_email, UserRoleModel.market == market)
    )
    return [permission.name for permission in app_db.select_all(query)]


def get_user_emails() -> list[UserIdEmail]:
    query = sqla.select(UserModel.id, UserModel.email)
    return [UserIdEmail(id=item.id, email=item.email) for item in app_db.select_all(query)]


def upsert_user_by_email(user_data: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(UserModel).values(user_data)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[UserModel.email],
            set_={
                UserModel.forced_out: query.excluded.forced_out,
                UserModel.first_name: query.excluded.first_name,
                UserModel.last_name: query.excluded.last_name,
                UserModel.last_login: query.excluded.last_login,
                UserModel.picture: query.excluded.picture,
            },
        )
    )


def update_user_login(user_id: int, timestamp: datetime) -> None:
    app_db.apply_query(sqla.update(UserModel).where(UserModel.id == user_id).values({UserModel.last_login: timestamp}))


def get_role_names_list(user_id: int, market: str) -> list[str]:
    query = (
        sqla.select(RoleModel.short_name)
        .join(UserRoleModel, onclause=(RoleModel.id == UserRoleModel.role_id))
        .where(UserRoleModel.user_id == user_id, UserRoleModel.market == market)
        .order_by(RoleModel.priority)
    )
    return [item.short_name for item in app_db.select_all(query)]


def get_role_full_names_list(user_email: str, market: str) -> list[str]:
    query = (
        sqla.select(RoleModel.full_name)
        .join(UserRoleModel, onclause=(RoleModel.id == UserRoleModel.role_id))
        .join(UserModel, onclause=(UserRoleModel.user_id == UserModel.id))
        .where(UserModel.email == user_email, UserRoleModel.market == market)
        .order_by(RoleModel.priority)
    )
    return [item.full_name for item in app_db.select_all(query)]


def get_user_markets(user_id: int) -> list[MARKET]:
    return [
        item.market
        for item in app_db.select_all(
            sqla.select(UserRoleModel.market.distinct()).where(UserRoleModel.user_id == user_id)
        )
    ]
