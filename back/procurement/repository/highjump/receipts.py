import dataclasses
from collections.abc import Collection, Iterable
from datetime import datetime
from decimal import Decimal
from typing import NamedTuple

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select, Subquery

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.constants.ordering import HJ_CLOSED_STATUSES
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import PO_NUMBER, SKU_CODE, PoSku
from procurement.data.dto.highjump.receipts import HjReceiptItem
from procurement.data.dto.inventory.receipt_override import ReceiptData, ReceiptSku
from procurement.data.models.highjump.highjump import HJReceiptsModel
from procurement.data.models.inventory.grn import LegacyHjGoodsReceiptNoteModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.managers.datasync.framework import warnings
from procurement.services.database import app_db

from . import common

HJReceiptsSqla = LegacyHjGoodsReceiptNoteModel if killswitch.hj_grn_enabled else HJReceiptsModel


class HjReceiptContext(NamedTuple):
    scm_week: ScmWeek = None
    wh_ids: set[str] = None
    sku_codes: set[SKU_CODE] = None
    po_number: PO_NUMBER = None


@dataclasses.dataclass
class HjReceiptData:
    wh_id: str
    supplier_name: str
    po_number: str
    cases_received: int
    quantity_received: Decimal
    sku_code: str
    sku_name: str
    scm_week_raw: str
    status: str
    receipt_time_est: datetime | str
    unit: str
    market: str
    supplier_code: str


def get_hj_receipts_pos(scm_week: ScmWeek, wh_id) -> list[PO_NUMBER]:
    market = context.get_request_context().market
    query = (
        sqla.select(HJReceiptsSqla.po_number)
        .distinct()
        .where(
            HJReceiptsSqla.scm_week_raw == str(int(scm_week)),
            HJReceiptsSqla.wh_id == wh_id,
            HJReceiptsSqla.market == market,
        )
    )
    return [r.po_number for r in app_db.select_all(query)]


def get_hj_receipt_skus(scm_week: ScmWeek, po_number: str, wh_id: str, exclude_skus: list[str]) -> list[ReceiptSku]:
    market = context.get_request_context().market
    query = (
        sqla.select(HJReceiptsSqla.sku_code, HJReceiptsSqla.sku_name)
        .distinct()
        .join(
            CulinarySkuModel,
            onclause=(CulinarySkuModel.sku_code == HJReceiptsSqla.sku_code) & (CulinarySkuModel.market == market),
        )
        .where(
            HJReceiptsSqla.scm_week_raw == str(int(scm_week)),
            HJReceiptsSqla.wh_id == wh_id,
            HJReceiptsSqla.po_number == po_number,
            HJReceiptsSqla.market == market,
            HJReceiptsSqla.sku_code.not_in(exclude_skus),
        )
    )
    return [ReceiptSku(sku_code=r.sku_code, sku_name=r.sku_name) for r in app_db.select_all(query)]


def get_hj_receipt(wh_ids: Collection[str]) -> list[HjReceiptData]:
    query = """
    SELECT
        wh_id,
        Supplier_Name,
        PO_Number,
        Cases_Received,
        Quantity_Received,
        Units_per_Case,
        SKU_Number,
        SKU_Name,
        SCM_Week,
        status,
        Receipt_Time_Local,
        "Unit of Measure" as Unit_of_Measure,
        "Supplier_Id"
    FROM v_procurement_export_receipts
        WHERE wh_id IN :wh_ids
    """

    items = []
    market = context.get_request_context().market
    for row in common.select_by_wh_ids(query, wh_ids):
        if not ScmWeek.is_valid_number(row.SCM_Week):
            error_message = "Error on load HJ_receipt: incorrect scm_week: " + str(row.SCM_Week)
            warnings.notify_warning(error_message, sku_code=row.SKU_Number, po_number=row.PO_Number)
            continue
        unit = UnitOfMeasure.inexact_value_of(row.Unit_of_Measure)
        item = HjReceiptData(
            wh_id=row.wh_id,
            supplier_name=row.Supplier_Name,
            po_number=row.PO_Number,
            cases_received=round(row.Cases_Received) if row.Cases_Received else 0,
            quantity_received=utils.float_to_decimal(row.Quantity_Received) if row.Quantity_Received else 0,
            sku_code=row.SKU_Number,
            sku_name=row.SKU_Name,
            scm_week_raw=row.SCM_Week,
            status=row.status,
            receipt_time_est=common.parse_hj_datetime(row.Receipt_Time_Local),
            unit=unit.short_name if unit else row.Unit_of_Measure,
            market=market,
            supplier_code=row.Supplier_Id,
        )
        items.append(item)
    return items


def get_receipts(receipt_context: HjReceiptContext) -> list[HjReceiptItem]:
    query = sqla.select(
        HJReceiptsSqla.sku_code,
        HJReceiptsSqla.po_number,
        HJReceiptsSqla.quantity_received,
        HJReceiptsSqla.cases_received,
        HJReceiptsSqla.status,
        HJReceiptsSqla.receipt_time_est,
        HJReceiptsSqla.unit,
        HJReceiptsSqla.supplier_code,
    )
    if receipt_context.scm_week:
        query = query.where(HJReceiptsSqla.scm_week_raw == str(int(receipt_context.scm_week)))
    if receipt_context.wh_ids:
        query = query.where(HJReceiptsSqla.wh_id.in_(receipt_context.wh_ids))
    if receipt_context.sku_codes is not None:
        query = query.where(HJReceiptsSqla.sku_code.in_(receipt_context.sku_codes))
    if receipt_context.po_number:
        query = query.where(HJReceiptsSqla.po_number == receipt_context.po_number)
    return [build_hj_receipt_item(r) for r in app_db.select_all(query)]


def get_receipts_for_override(week: ScmWeek) -> list[ReceiptData]:
    market = context.get_request_context().market
    return [
        ReceiptData(
            bob_code=r.wh_id,
            sku_code=r.sku_code,
            sku_name=r.sku_name,
            po_number=r.po_number,
            supplier_name=r.supplier_name,
            quantity_received=r.quantity_received,
            cases_received=r.cases_received,
        )
        for r in app_db.select_all(
            sqla.select(
                HJReceiptsSqla.wh_id,
                HJReceiptsSqla.po_number,
                HJReceiptsSqla.sku_code,
                CulinarySkuModel.sku_name,
                HJReceiptsSqla.supplier_name,
                HJReceiptsSqla.quantity_received,
                HJReceiptsSqla.cases_received,
            )
            .join(
                CulinarySkuModel,
                onclause=(HJReceiptsSqla.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market),
            )
            .where(HJReceiptsSqla.scm_week_raw == str(int(week)))
        )
    ]


def get_received_by_receive_date(
    wh_id: str,
    receive_date_from: datetime,
    receive_date_to: datetime,
    po_sku_codes: Iterable[tuple[PO_NUMBER, SKU_CODE]] = None,
    sku_codes: Iterable[SKU_CODE] = None,
    weeks: list[ScmWeek] = None,
) -> list[HjReceiptItem]:
    query = sqla.select(
        HJReceiptsSqla.sku_code,
        HJReceiptsSqla.po_number,
        HJReceiptsSqla.quantity_received,
        HJReceiptsSqla.cases_received,
        HJReceiptsSqla.status,
        HJReceiptsSqla.receipt_time_est,
        HJReceiptsSqla.unit,
        HJReceiptsSqla.supplier_code,
    ).where(
        HJReceiptsSqla.receipt_time_est >= receive_date_from,
        HJReceiptsSqla.receipt_time_est < receive_date_to,
        HJReceiptsSqla.wh_id == wh_id,
    )
    if weeks is not None:
        query = query.where(HJReceiptsSqla.scm_week_raw.in_([str(int(w)) for w in weeks]))
    if utils.is_any_collection_empty(po_sku_codes, sku_codes):
        return []
    if po_sku_codes:
        po_sku_code = sqla.tuple_(HJReceiptsSqla.po_number, HJReceiptsSqla.sku_code)
        query = query.where(po_sku_code.in_(po_sku_codes))
    elif sku_codes:
        query = query.where(HJReceiptsSqla.sku_code.in_(sku_codes))
    return [build_hj_receipt_item(item) for item in app_db.select_all(query)]


def get_po_sku_filtered_receives(po_sku_filtering_query: Select) -> list[HjReceiptItem]:
    po_query = po_sku_filtering_query.subquery()
    query = sqla.select(
        HJReceiptsSqla.sku_code,
        HJReceiptsSqla.po_number,
        HJReceiptsSqla.quantity_received,
        HJReceiptsSqla.cases_received,
        HJReceiptsSqla.status,
        HJReceiptsSqla.receipt_time_est,
        HJReceiptsSqla.unit,
        HJReceiptsSqla.supplier_code,
    ).join(
        po_query,
        onclause=(HJReceiptsSqla.po_number == po_query.c.po_number) & (HJReceiptsSqla.sku_code == po_query.c.sku_code),
    )
    return [build_hj_receipt_item(item) for item in app_db.select_all(query)]


def build_hj_receipt_item(item: Row) -> HjReceiptItem:
    return HjReceiptItem(
        sku_code=item.sku_code,
        po_number=item.po_number,
        quantity_received=item.quantity_received,
        cases_received=item.cases_received,
        status=item.status,
        receipt_time_est=item.receipt_time_est,
        unit=item.unit,
        supplier_code=item.supplier_code,
    )


def get_received_weeks(wh_id: str, receive_date_from: datetime, receive_date_to: datetime) -> set[ScmWeek]:
    query = (
        sqla.select(HJReceiptsSqla.scm_week_raw)
        .distinct()
        .where(
            HJReceiptsSqla.receipt_time_est >= receive_date_from,
            HJReceiptsSqla.receipt_time_est < receive_date_to,
            HJReceiptsSqla.wh_id == wh_id,
        )
    )
    return {ScmWeek.from_number(int(item.scm_week_raw)) for item in app_db.select_all(query)}


def query_received(
    wh_id: str,
    receive_date_from: datetime,
    receive_date_to: datetime,
    fields: Iterable[ColumnElement],
    weeks: list[ScmWeek] = None,
) -> Select:
    """
    Returns distinct query for filtering by receiving date. Should be used only in repositories.
    """
    query = (
        sqla.select(*fields)
        .distinct()
        .where(
            HJReceiptsSqla.receipt_time_est >= receive_date_from,
            HJReceiptsSqla.receipt_time_est < receive_date_to,
            HJReceiptsSqla.wh_id == wh_id,
        )
    )
    if weeks is not None:
        query = query.where(HJReceiptsSqla.scm_week_raw.in_([str(int(w)) for w in weeks]))
    return query


def delete_non_grn_hj_receipts_by_po_sku(po_sku: list[tuple[PO_NUMBER, SKU_CODE]]) -> int:
    return app_db.apply_query(
        sqla.delete(HJReceiptsModel).where(sqla.tuple_(HJReceiptsModel.po_number, HJReceiptsModel.sku_code).in_(po_sku))
    )


def insert_non_grn_hj_receipts(data: Iterable[HjReceiptData]):
    data = list(map(dataclasses.asdict, data))
    app_db.apply_query(sqla.insert(HJReceiptsModel).values(data))


def get_unique_po_sku_hj_receipts(po_filter_query: Subquery) -> Subquery:
    return (
        sqla.select(HJReceiptsSqla.po_number, HJReceiptsSqla.sku_code)
        .distinct()
        .join(po_filter_query, onclause=(HJReceiptsSqla.po_number == po_filter_query.c.po_number))
        .where(HJReceiptsSqla.status.in_(HJ_CLOSED_STATUSES))
        .subquery()
    )


def get_received_hj_keys(
    weeks: Iterable[ScmWeek],
    wh_ids: Iterable[str],
    sku_codes: set[str] | None = None,
) -> set[PoSku]:
    query = (
        sqla.select(HJReceiptsSqla.po_number, HJReceiptsSqla.sku_code)
        .distinct()
        .where(
            HJReceiptsSqla.scm_week_raw.in_([str(int(w)) for w in weeks]),
            HJReceiptsSqla.wh_id.in_(wh_ids),
            HJReceiptsSqla.status.in_(HJ_CLOSED_STATUSES),
        )
    )
    if sku_codes is not None:
        query = query.where(HJReceiptsSqla.sku_code.in_(sku_codes))
    return {PoSku(row.po_number, row.sku_code) for row in app_db.select_all(query)}
