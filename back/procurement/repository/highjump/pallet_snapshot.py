import dataclasses
from collections.abc import Collection, Iterable
from datetime import date, datetime
from typing import NamedTuple

import sqlalchemy as sqla

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.data.dto.highjump.snapshots import DailyPalletSnapshot, WeeklyPalletSnapshot
from procurement.data.dto.inventory.inventory import Inventory
from procurement.data.models.highjump.highjump import HjPackagingPalletSnapshotModel, HjPalletSnapshotModel
from procurement.services.database import app_db

from .. import repository_utils
from . import common


class HjPalletSnapshotData(NamedTuple):
    po_number: str
    sku_code: str
    license_plate: str
    pallet_quantity: int
    wh_id: str
    expiration_date: date
    status: str
    location_id: str


class HjPackagingSnapshotKey(NamedTuple):
    sku_code: str
    wh_id: str


@dataclasses.dataclass(slots=True)
class HjPalletSnapshotAggregatedData:
    sku_code: str
    wh_id: str
    pallet_quantity: int
    expiration_date: date
    unique_license_plate_count: int


def get_sku_pallet_quantity(wh_ids: Iterable[str], week: ScmWeek) -> list[WeeklyPalletSnapshot]:
    query = sqla.select(
        HjPalletSnapshotModel.sku_code,
        HjPalletSnapshotModel.wh_id,
        HjPalletSnapshotModel.pallet_quantity,
        HjPalletSnapshotModel.expiration_date,
    ).where(HjPalletSnapshotModel.wh_id.in_(wh_ids), HjPalletSnapshotModel.week == str(week))
    return [
        WeeklyPalletSnapshot(
            sku_code=item.sku_code,
            wh_id=item.wh_id,
            pallet_quantity=item.pallet_quantity,
            expiration_date=item.expiration_date,
        )
        for item in app_db.select_all(query)
    ]


def get_hj_pallet_snapshot(
    wh_ids: Collection[str],
    category: str = None,
    available_only: bool = True,
) -> list[HjPalletSnapshotData]:
    query = """
    SELECT
        SKU, License_Plate, Pallet_Quantity, po_number, SCM_Week, wh_id, expiration_date, Pallet_Status, location_id
    FROM v_procurement_export_lps
    WHERE location_id not in (
        'LOST', 'CLOUD', 'ADJ LOC', 'ADD_ON_AS', 'AS_KIT_STG', 'AUTOSTORE_QC', 'AUTOSTORE_STG', 'AUTOSTORE',
        'RCPT CORRECTION LOC', 'PROD RTN', 'AB OUT', 'WIP', 'AB IN'
    )
        AND wh_id IN :wh_ids
    """
    if category:
        query += " AND SKU_Category = :category"

    if available_only:
        query += " AND Pallet_Status = 'A'"

    items = []
    for row in common.select_by_wh_ids(query, wh_ids, {"category": category}):
        try:
            expiration_date = datetime.strptime(row.expiration_date, "%m/%d/%y").date() if row.expiration_date else None
        except ValueError:
            expiration_date = None

        item = HjPalletSnapshotData(
            po_number=row.po_number,
            sku_code=row.SKU,
            license_plate=row.License_Plate,
            pallet_quantity=utils.ceil_subone_integer(row.Pallet_Quantity) if row.Pallet_Quantity else 0,
            wh_id=row.wh_id,
            expiration_date=expiration_date,
            status=row.Pallet_Status,
            location_id=row.location_id,
        )
        items.append(item)
    return items


def delete_hj_pallet_snapshot_by_week_wh_ids(snapshot_week: ScmWeek, wh_ids: Collection[str]) -> None:
    app_db.apply_query(
        sqla.delete(HjPalletSnapshotModel).where(
            HjPalletSnapshotModel.week == str(snapshot_week), HjPalletSnapshotModel.wh_id.in_(wh_ids)
        )
    )


def hj_pallet_snapshot_insert_many(data: list[HjPalletSnapshotAggregatedData], snapshot_week: ScmWeek) -> None:
    snapshot_week_str = str(snapshot_week)
    data = [
        {
            HjPalletSnapshotModel.week: snapshot_week_str,
            HjPalletSnapshotModel.wh_id: item.wh_id,
            HjPalletSnapshotModel.sku_code: item.sku_code,
            HjPalletSnapshotModel.pallet_quantity: item.pallet_quantity,
            HjPalletSnapshotModel.expiration_date: item.expiration_date,
            HjPalletSnapshotModel.unique_license_plate_count: item.unique_license_plate_count,
        }
        for item in data
    ]

    repository_utils.safe_bulk_insert_sqla(
        model=HjPalletSnapshotModel,
        data=data,
        keys=[
            HjPalletSnapshotModel.week,
            HjPalletSnapshotModel.wh_id,
            HjPalletSnapshotModel.sku_code,
            HjPalletSnapshotModel.expiration_date,
        ],
        preserve_fields=[HjPalletSnapshotModel.pallet_quantity, HjPalletSnapshotModel.unique_license_plate_count],
    )


def get_current_hj_pallets_inventory(wh_id: str, week: ScmWeek) -> list[Inventory]:
    query = sqla.select(
        HjPalletSnapshotModel.sku_code,
        HjPalletSnapshotModel.pallet_quantity,
        HjPalletSnapshotModel.expiration_date,
        HjPalletSnapshotModel.unique_license_plate_count,
    ).where(HjPalletSnapshotModel.wh_id == wh_id, HjPalletSnapshotModel.week == str(week))

    return [
        Inventory(
            sku_code=item.sku_code,
            quantity=item.pallet_quantity,
            expiration_date=item.expiration_date,
            license_count=item.unique_license_plate_count,
        )
        for item in app_db.select_all(query)
    ]


def insert_packaging_snapshot(snapshot: dict[HjPackagingSnapshotKey, int], snapshot_date: date) -> None:
    data = [
        {
            HjPackagingPalletSnapshotModel.snapshot_date: snapshot_date,
            HjPackagingPalletSnapshotModel.wh_id: key.wh_id,
            HjPackagingPalletSnapshotModel.sku_code: key.sku_code,
            HjPackagingPalletSnapshotModel.pallet_quantity: qty,
        }
        for key, qty in snapshot.items()
    ]
    app_db.apply_query(sqla.insert(HjPackagingPalletSnapshotModel).values(data))


def get_packaging_snapshots(wh_id: str, date_from: date, date_to: date) -> list[DailyPalletSnapshot]:
    query = sqla.select(
        HjPackagingPalletSnapshotModel.sku_code,
        HjPackagingPalletSnapshotModel.snapshot_date,
        HjPackagingPalletSnapshotModel.pallet_quantity,
    ).where(
        HjPackagingPalletSnapshotModel.snapshot_date >= date_from,
        HjPackagingPalletSnapshotModel.snapshot_date <= date_to,
        HjPackagingPalletSnapshotModel.wh_id == wh_id,
    )

    return [
        DailyPalletSnapshot(
            sku_code=item.sku_code, snapshot_date=item.snapshot_date, pallet_quantity=item.pallet_quantity
        )
        for item in app_db.select_all(query)
    ]


def get_latest_packaging_snapshot_date() -> date | None:
    return app_db.select_scalar(sqla.select(sqla.func.max(HjPackagingPalletSnapshotModel.snapshot_date)))


def delete_packaging_snapshot(snapshot_date: date, wh_ids: Collection[str]) -> None:
    app_db.apply_query(
        sqla.delete(HjPackagingPalletSnapshotModel).where(
            HjPackagingPalletSnapshotModel.snapshot_date == snapshot_date,
            HjPackagingPalletSnapshotModel.wh_id.in_(wh_ids),
        )
    )
