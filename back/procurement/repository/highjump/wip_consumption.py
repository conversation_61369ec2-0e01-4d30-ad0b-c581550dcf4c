from collections import defaultdict
from collections.abc import Collection
from datetime import datetime, time, timedelta
from decimal import Decimal
from typing import Any, NamedTuple

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.data.dto.highjump.wip_consumption import HjWipConsumption
from procurement.data.models.highjump.highjump import HjWipConsumptionModel
from procurement.managers.admin import brand_admin
from procurement.services.database import app_db, canada_hj


class HjWipConsumptionKey(NamedTuple):
    sku_code: str
    week: int
    wh_id: str
    destination_location: str
    tran_type: str
    tran_date: datetime


def get_hj_query_wip_consumption(brand: str, wh_ids: Collection[str]) -> list[dict[ColumnElement, Any]]:
    week_config = brand_admin.get_week_config(brand)
    current_week = ScmWeek.current_week(week_config=week_config)
    date_from = datetime.combine(current_week.get_first_day() - timedelta(days=2), time())
    date_to = date_from + timedelta(days=3, hours=4)
    query = """SET NOCOUNT ON; EXEC dbo.usp_hf_report_lp_consumption :wh_id;"""
    items = []
    for wh_id in wh_ids:
        wh_items: dict[HjWipConsumptionKey, Decimal] = defaultdict(Decimal)
        for row in canada_hj.select_all(query=query, params={"wh_id": wh_id}):
            tran_date = getattr(row, "Tran Date")
            if tran_date < date_from or date_to < tran_date:
                continue

            key = HjWipConsumptionKey(
                sku_code=getattr(row, "Item Number"),
                week=int(current_week),
                wh_id=wh_id,
                destination_location=getattr(row, "Destination Location"),
                tran_type=getattr(row, "Description"),
                tran_date=tran_date.date(),
            )
            wh_items[key] += Decimal(str(getattr(row, "Quantity")))

        items.extend(
            [
                {
                    HjWipConsumptionModel.sku_code: key.sku_code,
                    HjWipConsumptionModel.week: key.week,
                    HjWipConsumptionModel.wh_id: wh_id,
                    HjWipConsumptionModel.destination_location: key.destination_location,
                    HjWipConsumptionModel.tran_type: key.tran_type,
                    HjWipConsumptionModel.quantity: quantity,
                    HjWipConsumptionModel.tran_date: key.tran_date,
                }
                for key, quantity in wh_items.items()
            ]
        )
    return items


def hj_wip_consumption_insert_many(items: list[dict[ColumnElement, Any]]) -> None:
    query = psql_sqla.insert(HjWipConsumptionModel)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[
                HjWipConsumptionModel.week,
                HjWipConsumptionModel.wh_id,
                HjWipConsumptionModel.sku_code,
                HjWipConsumptionModel.destination_location,
                HjWipConsumptionModel.tran_type,
                HjWipConsumptionModel.tran_date,
            ],
            set_={HjWipConsumptionModel.quantity: query.excluded.quantity},
        ).values(items)
    )


def get_hj_wip_consumption(wh_id: str, week: ScmWeek) -> list[HjWipConsumption]:
    return [
        HjWipConsumption(
            sku_code=row.sku_code,
            tran_type=row.tran_type,
            destination_location=row.destination_location,
            quantity=row.quantity,
        )
        for row in app_db.apply_query(
            sqla.select(
                HjWipConsumptionModel.sku_code,
                HjWipConsumptionModel.tran_type,
                HjWipConsumptionModel.destination_location,
                sqla.func.sum(HjWipConsumptionModel.quantity).label("quantity"),
            )
            .group_by(
                HjWipConsumptionModel.sku_code,
                HjWipConsumptionModel.tran_type,
                HjWipConsumptionModel.destination_location,
            )
            .where(HjWipConsumptionModel.wh_id == wh_id, HjWipConsumptionModel.week == int(week))
        )
    ]
