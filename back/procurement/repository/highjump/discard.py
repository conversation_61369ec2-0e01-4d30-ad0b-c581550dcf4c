from collections.abc import Collection
from datetime import date, datetime
from typing import Any

import sqlalchemy as sqla
from sqlalchemy.orm import InstrumentedAttribute

from procurement.data.dto.highjump.discard import HjDiscard
from procurement.data.models.highjump.highjump import DiscardType, HjDiscardModel
from procurement.services.database import app_db

from . import common


def get_hj_query_discard(wh_ids: Collection[str]) -> list[dict[InstrumentedAttribute, Any]]:
    query = f"""
        SELECT item_number, wh_id, tran_type, tran_qty, end_tran_date
        FROM v_highjump_discards_imt
        WHERE tran_type in ({",".join(rf"'{dt}'" for dt in DiscardType)})
        AND location_id != 'LOST' AND wh_id IN :wh_ids;
    """  # nosec B608

    return [
        {
            HjDiscardModel.sku_code: delete_g_from_sku_code(row.item_number),
            HjDiscardModel.wh_id: row.wh_id,
            HjDiscardModel.tran_type: row.tran_type,
            HjDiscardModel.quantity: row.tran_qty,
            HjDiscardModel.discard_date: row.end_tran_date,
        }
        for row in common.select_by_wh_ids(query, wh_ids)
    ]


def delete_g_from_sku_code(sku_code: str) -> str:
    if sku_code.endswith("-G"):
        return sku_code[:-2]
    return sku_code


def delete_hj_discard(date_delete: date | datetime, wh_ids: Collection[str]) -> None:
    app_db.apply_query(
        sqla.delete(HjDiscardModel).where(
            HjDiscardModel.discard_date >= date_delete, HjDiscardModel.wh_id.in_(list(wh_ids))
        )
    )


def hj_discard_insert_many(items: list[dict[InstrumentedAttribute, Any]]) -> None:
    app_db.apply_query(sqla.insert(HjDiscardModel).values(items))


def get_hj_discards(
    wh_id: str,
    date_from: date | datetime,
    date_to: date | datetime,
    sku_code: str | None,
    tran_type: DiscardType | None = None,
) -> list[HjDiscard]:
    query = sqla.select(
        HjDiscardModel.sku_code,
        HjDiscardModel.tran_type,
        HjDiscardModel.discard_date,
        sqla.func.abs(sqla.func.sum(HjDiscardModel.quantity)).label("quantity"),
    ).where(
        HjDiscardModel.wh_id == wh_id,
        HjDiscardModel.discard_date < date_to,
        HjDiscardModel.discard_date >= date_from,
    )
    if sku_code is not None:
        query = query.where(HjDiscardModel.sku_code == sku_code)
    if tran_type:
        query = query.where(HjDiscardModel.tran_type == tran_type)
    return [
        HjDiscard(row.sku_code, wh_id, DiscardType(row.tran_type), row.discard_date, row.quantity)
        for row in app_db.apply_query(
            query.group_by(HjDiscardModel.sku_code, HjDiscardModel.tran_type, HjDiscardModel.discard_date)
        )
    ]
