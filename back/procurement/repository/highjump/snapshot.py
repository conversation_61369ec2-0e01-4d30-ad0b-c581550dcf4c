from collections.abc import Iterable
from datetime import date, datetime, timedelta

import sqlalchemy as sqla

from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.inventory import Inventory
from procurement.data.models.highjump.highjump import HJInvSnapshotModel
from procurement.repository.highjump.pallet_snapshot import HjPalletSnapshotAggregatedData
from procurement.services.database import app_db

HJ_INVENTORY_TTL = 120


def delete_hj_inv_snapshot(wh_id: str, date_delete: date) -> None:
    app_db.apply_query(
        sqla.delete(HJInvSnapshotModel).where(HJInvSnapshotModel.date == date_delete, HJInvSnapshotModel.wh_id == wh_id)
    )


def hj_inv_snapshot_insert_many(data: list[HjPalletSnapshotAggregatedData], dc_date: date) -> None:
    update_ts = datetime.now()
    data = [
        {
            HJInvSnapshotModel.sku_code: item.sku_code,
            HJInvSnapshotModel.quantity: item.pallet_quantity,
            HJInvSnapshotModel.wh_id: item.wh_id,
            HJInvSnapshotModel.date: dc_date,
            HJInvSnapshotModel.expiration_date: item.expiration_date,
            HJInvSnapshotModel.updated_ts: update_ts,
        }
        for item in data
    ]
    app_db.apply_query(sqla.insert(HJInvSnapshotModel).values(data))


def get_latest_inv_snapshot_day(wh_id: str) -> date | None:
    query = sqla.select(sqla.func.max(HJInvSnapshotModel.date).label("latest_date")).where(
        HJInvSnapshotModel.wh_id == wh_id
    )
    return app_db.select_scalar(query)


def get_inv_snapshots(
    wh_id: str, date_from: date, date_to: date, sku_codes: Iterable[SKU_CODE] = None
) -> list[Inventory]:
    query = (
        sqla.select(
            HJInvSnapshotModel.sku_code,
            HJInvSnapshotModel.date,
            HJInvSnapshotModel.expiration_date,
            sqla.func.sum(HJInvSnapshotModel.quantity).label("quantity"),
        )
        .where(
            HJInvSnapshotModel.date >= date_from, HJInvSnapshotModel.date <= date_to, HJInvSnapshotModel.wh_id == wh_id
        )
        .group_by(HJInvSnapshotModel.date, HJInvSnapshotModel.sku_code, HJInvSnapshotModel.expiration_date)
    )

    if sku_codes:
        query = query.where(HJInvSnapshotModel.sku_code.in_(sku_codes))

    return [
        Inventory(
            sku_code=item.sku_code,
            snapshot_timestamp=item.date,
            quantity=item.quantity,
            expiration_date=item.expiration_date.date() if item.expiration_date else None,
        )
        for item in app_db.select_all(query)
    ]


def delete_old_inv_snapshots() -> None:
    date_to = datetime.now() - timedelta(days=HJ_INVENTORY_TTL)
    app_db.apply_query(sqla.delete(HJInvSnapshotModel).where(HJInvSnapshotModel.updated_ts <= date_to))
