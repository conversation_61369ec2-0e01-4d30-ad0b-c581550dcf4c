from collections.abc import Collection
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.data.dto.highjump.wip import HjWip, HjWipExtended
from procurement.data.models.highjump.highjump import HjWipModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db

from ...core import utils
from . import common


def get_hj_query_wip(wh_ids: Collection[str]) -> list[dict[ColumnElement, Any]]:
    query = (
        "SELECT SKU, wh_id, SUM(Quantity) as Quantity FROM v_highjump_wip WHERE wh_id IN :wh_ids GROUP BY SKU, wh_id;"
    )
    return [
        {
            HjWipModel.sku_code: row.SKU,
            HjWipModel.wh_id: row.wh_id,
            HjWipModel.quantity: int(row.Quantity) if row.Quantity is not None else None,
        }
        for row in common.select_by_wh_ids(query, wh_ids)
    ]


def delete_hj_wip(wh_ids: Collection[str]) -> None:
    app_db.apply_query(sqla.delete(HjWipModel).where(HjWipModel.wh_id.in_(list(wh_ids))))


def get_units_on_floor(dc_object: DcConfig, sku_codes: set[str] = None) -> list[HjWip]:
    if dc_object.receiving_type.is_manual:
        return []
    query = sqla.select(HjWipModel.sku_code, HjWipModel.quantity).where(HjWipModel.wh_id == dc_object.high_jump_name)
    if sku_codes:
        query = query.where(HjWipModel.sku_code.in_(sku_codes))
    return [HjWip(sku_code=row.sku_code, quantity=row.quantity) for row in app_db.apply_query(query)]


def get_wip_inventory(wh_ids: Collection[str]) -> list[HjWipExtended]:
    if utils.is_any_collection_empty(wh_ids):
        return []
    query = sqla.select(HjWipModel.sku_code, HjWipModel.quantity, HjWipModel.wh_id).where(HjWipModel.wh_id.in_(wh_ids))
    return [
        HjWipExtended(sku_code=row.sku_code, quantity=row.quantity, wh_id=row.wh_id)
        for row in app_db.apply_query(query)
    ]


def hj_wip_insert_many(data: list[dict[ColumnElement, Any]]) -> None:
    app_db.apply_query(sqla.insert(HjWipModel).values(data))
