from collections import defaultdict
from collections.abc import Collection
from datetime import datetime

from sqlalchemy import Row

from procurement.client.googlesheets.googlesheet_utils import HJ_TIME_EST
from procurement.constants.hellofresh_constant import MARKET_CA
from procurement.core.request_utils import context
from procurement.services.database import HighjumpService, canada_hj, green_chef_hj, hj

__HJ_BY_WH_SIGN_MAP = {"G": green_chef_hj, "F": green_chef_hj}


def parse_hj_datetime(str_datetime: str) -> datetime | None:
    try:
        return datetime.strptime(str_datetime, HJ_TIME_EST) if str_datetime else None
    except ValueError:
        return None


def get_database_by_wh_id(wh_id: str) -> HighjumpService:
    if context.get_request_context().market == MARKET_CA:
        return canada_hj
    return __HJ_BY_WH_SIGN_MAP.get(wh_id[0], hj)


def map_wh_ids_to_database(wh_ids: Collection[str]) -> dict[HighjumpService, list[str]]:
    if context.get_request_context().market == MARKET_CA:
        return {canada_hj: list(wh_ids)}
    db_mapping = defaultdict(list)
    for wh_id in wh_ids:
        db_mapping[__HJ_BY_WH_SIGN_MAP.get(wh_id[0], hj)].append(wh_id)
    return db_mapping


def select_by_wh_ids(
    query: str, wh_ids: Collection[str], other_params: dict | None = None, *, include_wh_ids: bool = True
) -> list[Row]:
    other_params = other_params or {}
    result = []
    for hj_db, whs in map_wh_ids_to_database(wh_ids).items():
        params = {"wh_ids": whs} if include_wh_ids else {}
        params.update(other_params)
        for row in hj_db.select_all(query, params):
            result.append(row)

    return result
