from procurement.core.typing import SKU_CODE

from . import common


def get_hj_autostore_info_by_wh_id(wh_id: str) -> dict[SKU_CODE, int]:
    query = """
        SELECT SKU, SUM(Quantity) AS Quantity
        FROM v_highjump_autostore
        WHERE wh_id = :wh_id
        GROUP BY SKU
    """
    return {
        row.SKU: int(row.Quantity) for row in common.get_database_by_wh_id(wh_id).select_all(query, {"wh_id": wh_id})
    }
