from decimal import Decimal

import sqlalchemy as sqla

from procurement.core.request_utils import context
from procurement.data.dto.pimt.master_replenishment import MasterReplenishmentItem, MasterReplenishmentSku
from procurement.data.models.inventory.ingredient import PurchasingCategoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.pimt.replenishment import MasterReplenishmentModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def update_master_replenishment(data: list[dict]):
    repository_utils.safe_bulk_insert_sqla(
        model=MasterReplenishmentModel,
        data=data,
        keys=[MasterReplenishmentModel.site, MasterReplenishmentModel.sku_code],
        preserve_fields=[
            MasterReplenishmentModel.replenishment_type,
            MasterReplenishmentModel.shelf_life,
            MasterReplenishmentModel.status,
            MasterReplenishmentModel.market,
            MasterReplenishmentModel.max_supplier_lead_time,
            MasterReplenishmentModel.min_order_qty,
            MasterReplenishmentModel.request_for_proposal,
            MasterReplenishmentModel.market_factor,
            MasterReplenishmentModel.replenishment_buyer,
        ],
    )


def delete_master_repl_data():
    app_db.apply_query(sqla.delete(MasterReplenishmentModel))


def get_master_replenishment(sku_codes: set[str] | None = None) -> list[MasterReplenishmentItem]:
    market = context.get_request_context().market
    query = (
        sqla.select(MasterReplenishmentModel, PurchasingCategoryModel.name.label("purchasing_category"))
        .join(
            CulinarySkuModel,
            isouter=True,
            onclause=(
                (MasterReplenishmentModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)
            ),
        )
        .join(
            PurchasingCategoryModel,
            isouter=True,
            onclause=(PurchasingCategoryModel.id == CulinarySkuModel.purchasing_category_id),
        )
    )
    if sku_codes is not None:
        query = query.where(MasterReplenishmentModel.sku_code.in_(sku_codes))
    return [
        MasterReplenishmentItem(
            site=item.site,
            sku_code=item.sku_code,
            category=item.purchasing_category,
            status=item.status,
            replenishment_type=item.replenishment_type,
            shelf_life=item.shelf_life,
            request_for_proposal=item.request_for_proposal,
            max_supplier_lead_time=item.max_supplier_lead_time or 0,
            market=item.market,
            market_factor=item.market_factor or Decimal(),
            min_order_qty=item.min_order_qty or Decimal(),
            buyers={item.replenishment_buyer} if item.replenishment_buyer else set(),
        )
        for item in app_db.select_all(query)
    ]


def get_master_replenishment_skus() -> list[MasterReplenishmentSku]:
    query = sqla.select(MasterReplenishmentModel.sku_code, MasterReplenishmentModel.site).where(
        MasterReplenishmentModel.replenishment_type != ""
    )
    return [MasterReplenishmentSku(sku_code=item.sku_code, site=item.site) for item in app_db.select_all(query)]
