from datetime import date
from typing import Sequence

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.models.pimt.exceptions import ExceptionModel
from procurement.services.database import app_db


def insert_data(exception_date: date, stats: dict, market: str) -> None:
    query = psql_sqla.insert(ExceptionModel)
    query = query.on_conflict_do_update(
        index_elements=[ExceptionModel.exception_date, ExceptionModel.market],
        set_={ExceptionModel.stats.name: query.excluded.stats},
    )
    app_db.apply_query(
        query.values(
            {ExceptionModel.exception_date: exception_date, ExceptionModel.stats: stats, ExceptionModel.market: market}
        )
    )


def get_count_exceptions_data(
    first_date_of_week: date, last_date_of_week: date, market: str
) -> Sequence[sqla.Row[ExceptionModel]]:
    query = (
        sqla.select(ExceptionModel)
        .where(
            ExceptionModel.exception_date >= first_date_of_week,
            ExceptionModel.exception_date <= last_date_of_week,
            ExceptionModel.market == market,
        )
        .order_by(ExceptionModel.exception_date)
    )
    return app_db.select_all(query)
