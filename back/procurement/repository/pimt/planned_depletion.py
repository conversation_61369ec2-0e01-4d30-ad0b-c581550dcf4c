from collections.abc import Collection
from decimal import Decimal

import sqlalchemy as sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.pimt.pakcaging_safety_stock import PlannedDepletionKey
from procurement.data.models.pimt.planned_depletion import PlannedDepletionModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_planned_depletion(data: Collection[dict]):
    repository_utils.safe_bulk_insert_sqla(
        model=PlannedDepletionModel,
        data=data,
        keys=[
            PlannedDepletionModel.week,
            PlannedDepletionModel.region,
            PlannedDepletionModel.market,
            PlannedDepletionModel.demand_pipeline,
            PlannedDepletionModel.sku_code,
            PlannedDepletionModel.owner,
        ],
        preserve_fields=[PlannedDepletionModel.quantity],
    )


def delete_historical_data_from_planned_depl(weeks: set[int]) -> None:
    app_db.apply_query(
        sqla.delete(PlannedDepletionModel).where(
            PlannedDepletionModel.week.in_(weeks) | (PlannedDepletionModel.week < int(ScmWeek.current_week() - 10))
        )
    )


def get_planned_depletion(
    weeks: Collection[ScmWeek], packaging_regions: Collection[str], market: str | None = None
) -> dict[PlannedDepletionKey, Decimal]:
    market_ = market or context.get_request_context().market
    query = sqla.select(
        PlannedDepletionModel.week,
        PlannedDepletionModel.region,
        PlannedDepletionModel.demand_pipeline,
        PlannedDepletionModel.sku_code,
        PlannedDepletionModel.owner,
        PlannedDepletionModel.quantity,
    ).where(
        PlannedDepletionModel.week.in_(map(int, weeks)),
        PlannedDepletionModel.region.in_(packaging_regions),
        PlannedDepletionModel.market == market_,
    )
    return {
        PlannedDepletionKey(ScmWeek.from_number(r.week), r.region, r.demand_pipeline, r.sku_code, r.owner): r.quantity
        for r in app_db.select_all(query)
    }
