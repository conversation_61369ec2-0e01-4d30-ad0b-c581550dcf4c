from collections.abc import Iterable
from datetime import date, <PERSON>elta

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select, Subquery

from procurement.constants.hellofresh_constant import MANY_SUPPLIERS, InventoryInputType, InventoryState
from procurement.constants.protobuf import GrnDeliveryLineState
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.inventory.unified_inventory import (
    UnifiedExpiredInventory,
    UnifiedInventoryE2OpenExport,
    UnifiedInventoryUnits,
    UnifiedInventoryWhLastUpdate,
)
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.models.inventory.grn import GoodsReceiptNoteModel
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.data.models.pimt.partner import WarehouseModel
from procurement.repository.inventory import inventory
from procurement.repository.repository_utils import replace_zero
from procurement.services.database import app_db


def get_expiring_inventory_report_data(
    warehouses: Iterable[Warehouse], sku_code: str = None, available_inv_only: bool = True
) -> tuple[UnifiedExpiredInventory, ...]:
    statuses = (
        [InventoryState.AVAILABLE]
        if available_inv_only
        else [InventoryState.AVAILABLE, InventoryState.BOOKED_FOR_OUTBOUND, InventoryState.ON_HOLD]
    )
    po_subquery = _get_po_subquery()

    query = _filter_latest_records(
        sqla.select(
            UnifiedInventoryModel.sku_code,
            UnifiedInventoryModel.lot_code,
            UnifiedInventoryModel.wh_code,
            _case_quantity(po_subquery.c.case_size).label("cases"),
            UnifiedInventoryModel.expiration_date,
            inventory.unit_quantity(po_subquery.c.case_size).label("total"),
            UnifiedInventoryModel.inventory_status,
            po_subquery.c.po_number,
            sqla.func.coalesce(UnifiedInventoryModel.supplier, po_subquery.c.supplier, "").label("supplier"),
            po_subquery.c.source_bob_code,
            po_subquery.c.supplier_code,
            po_subquery.c.delivery_time_start.label("po_delivery_time_start"),
            sqla.func.coalesce(UnifiedInventoryModel.case_size, po_subquery.c.case_size, 0).label("case_size"),
            po_subquery.c.case_price,
            GoodsReceiptNoteModel.receipt_time_est.label("grn_delivery_time_start"),
        )
        .join(
            po_subquery,
            isouter=True,
            onclause=sqla.and_(
                po_subquery.c.order_number == UnifiedInventoryModel.order_number,
                po_subquery.c.sku_code == UnifiedInventoryModel.sku_code,
            ),
        )
        .join(
            GoodsReceiptNoteModel,
            isouter=True,
            onclause=sqla.and_(
                GoodsReceiptNoteModel.order_number == UnifiedInventoryModel.order_number,
                GoodsReceiptNoteModel.sku_code == UnifiedInventoryModel.sku_code,
                GoodsReceiptNoteModel.status == GrnDeliveryLineState.CLOSED,
                GoodsReceiptNoteModel.bob_code_source_tuple().in_(
                    ((wh.bob_code, wh.receiving_type.grn_source_name) for wh in warehouses)
                ),
            ),
        )
        .where(UnifiedInventoryModel.inventory_status.in_(statuses)),
        wh_codes=[wh.code for wh in warehouses],
    )

    return tuple(
        UnifiedExpiredInventory(
            sku_code=item.sku_code,
            lot_code=item.lot_code,
            wh_code=item.wh_code,
            cases=item.cases,
            inventory_expiration_date=item.expiration_date,
            total=item.total,
            state=item.inventory_status,
            po_number=item.po_number,
            supplier=(
                MANY_SUPPLIERS
                if item.supplier is None and item.source_bob_code and killswitch.transfer_orders_enabled
                else item.supplier
            ),
            supplier_code=item.supplier_code,
            po_delivery_time_start=item.po_delivery_time_start,
            case_size=item.case_size,
            case_price=item.case_price,
            grn_delivery_time_start=item.grn_delivery_time_start,
        )
        for item in app_db.select_all(query)
        if sku_code is None or item.sku_code == sku_code
        # IMPORTANT: DO NOT REPLACE SKU_CODE FILTRATION WITH DB FILTERING.
        # For some reason the DB builds very slow plan with SKU code filter (at least in Postgres 15)
    )


def _get_po_subquery() -> Subquery:
    market = context.get_request_context().market
    return (
        sqla.select(
            PurchaseOrderSkuModel.case_size,
            PurchaseOrderSkuModel.case_price,
            PurchaseOrderModel.delivery_time_start,
            PurchaseOrderModel.supplier_code,
            PurchaseOrderModel.source_bob_code,
            PurchaseOrderModel.po_number,
            PurchaseOrderModel.order_number,
            PurchaseOrderModel.is_sent,
            PurchaseOrderModel.deleted,
            CulinarySkuModel.sku_code,
            SupplierModel.name.label("supplier"),
        )
        .join(PurchaseOrderModel, onclause=(PurchaseOrderModel.po_uuid == PurchaseOrderSkuModel.po_uuid))
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(
            SupplierModel,
            isouter=True,
            onclause=sqla.and_(PurchaseOrderModel.supplier_code == SupplierModel.code, SupplierModel.market == market),
        )
        .subquery()
    )


def _case_quantity(case_size_column: ColumnElement) -> ColumnElement:
    return sqla.func.coalesce(
        UnifiedInventoryModel.case_quantity,
        (
            UnifiedInventoryModel.unit_quantity
            / replace_zero(sqla.func.coalesce(UnifiedInventoryModel.case_size, case_size_column, 0))
        ).cast(sqla.INTEGER),
        0,
    )


def get_unified_inventory_in_house(wh_codes: Iterable[str], week: ScmWeek | None = None) -> list[UnifiedInventoryUnits]:
    query = _filter_latest_records(_get_base_inventory_query(), wh_codes=wh_codes, week=week).where(
        UnifiedInventoryModel.inventory_status == InventoryState.AVAILABLE
    )

    return [
        UnifiedInventoryUnits(sku_code=item.sku_code, wh_code=item.wh_code, total=item.total)
        for item in app_db.select_all(query)
    ]


def _get_base_inventory_query() -> Select:
    group_fields = [UnifiedInventoryModel.sku_code, UnifiedInventoryModel.wh_code]
    po_subquery = _get_po_subquery()
    agg_fields = [sqla.func.sum(inventory.unit_quantity(po_subquery.c.case_size)).label("total")]
    query = (
        sqla.select(*group_fields, *agg_fields)
        .join(
            po_subquery,
            isouter=True,
            onclause=sqla.and_(
                UnifiedInventoryModel.order_number == po_subquery.c.order_number,
                UnifiedInventoryModel.sku_code == po_subquery.c.sku_code,
                ~po_subquery.c.deleted,
                po_subquery.c.is_sent,
            ),
        )
        .group_by(*group_fields)
    )
    return query


def get_max_last_update_time(wh_codes: Iterable[str]) -> list[UnifiedInventoryWhLastUpdate]:
    return [
        UnifiedInventoryWhLastUpdate(wh_code=item.wh_code, last_updated=item.last_updated.date())
        for item in app_db.select_all(_get_max_last_updated_by_wh_query(wh_codes))
    ]


# TODO use separate table for latest update time
def _get_max_last_updated_by_wh_query(wh_codes: Iterable[str] = None, week: ScmWeek | None = None) -> Select:
    query = (
        sqla.select(
            UnifiedInventoryModel.wh_code,
            UnifiedInventoryModel.inventory_type,
            sqla.func.max(UnifiedInventoryModel.snapshot_timestamp).label("last_updated"),
        )
        .join(
            WarehouseModel,
            onclause=sqla.and_(
                WarehouseModel.code == UnifiedInventoryModel.wh_code,
                WarehouseModel.inventory_type == UnifiedInventoryModel.inventory_type,
            ),
        )
        .group_by(UnifiedInventoryModel.wh_code, UnifiedInventoryModel.inventory_type)
    )
    if wh_codes is not None:
        query = query.where(UnifiedInventoryModel.wh_code.in_(wh_codes))
    if week:
        week = min(week, ScmWeek.current_week())
        query = query.where(
            UnifiedInventoryModel.snapshot_timestamp >= week.get_first_day(),
            UnifiedInventoryModel.snapshot_timestamp < week.get_last_production_day() + timedelta(days=1),
        )
    return query


def _filter_latest_records(query: Select, wh_codes: Iterable[str] = None, week: ScmWeek | None = None) -> Select:
    return query.where(
        sqla.tuple_(
            UnifiedInventoryModel.wh_code,
            UnifiedInventoryModel.inventory_type,
            UnifiedInventoryModel.snapshot_timestamp,
        ).in_(_get_max_last_updated_by_wh_query(wh_codes, week).scalar_subquery()),
    )


def get_unique_po_sku_inventory() -> Subquery:
    return _filter_latest_records(
        sqla.select(UnifiedInventoryModel.order_number, UnifiedInventoryModel.sku_code).distinct()
    ).subquery()


def get_e2_open_unified_inventory_for_export(wh_code: str, date_from: date) -> Iterable[UnifiedInventoryE2OpenExport]:
    query = sqla.select(
        UnifiedInventoryModel.sku_code,
        UnifiedInventoryModel.order_number,
        UnifiedInventoryModel.po_number,
        UnifiedInventoryModel.lot_code,
        UnifiedInventoryModel.expiration_date,
    ).where(
        UnifiedInventoryModel.wh_code == wh_code,
        UnifiedInventoryModel.snapshot_timestamp >= date_from,
        UnifiedInventoryModel.snapshot_timestamp < date_from + timedelta(days=1),
        UnifiedInventoryModel.inventory_type == InventoryInputType.E2OPEN,
    )

    return [
        UnifiedInventoryE2OpenExport(
            sku_code=item.sku_code,
            order_number=item.order_number,
            po_number=item.po_number,
            lot_code=item.lot_code,
            expiration_date=item.expiration_date,
        )
        for item in app_db.select_all(query)
    ]
