from datetime import date
from typing import Iterable, Sequence

import sqlalchemy as sqla

from procurement.core.typing import ORDER_NUMBER
from procurement.data.dto.pimt.goods_receipt_notification import GoodsReceiptNote
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.models.inventory.grn import GoodsReceiptNoteModel
from procurement.data.models.pimt.partner import WarehouseModel
from procurement.services.database import app_db


def get_grn_receive_date_by_pos(
    purchase_orders: Iterable[ORDER_NUMBER], warehouses: list[Warehouse]
) -> Sequence[sqla.Row[GoodsReceiptNoteModel]]:
    return app_db.select_all(
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.receipt_time_est.label("receive_date"),
        ).where(
            GoodsReceiptNoteModel.order_number.in_(purchase_orders),
            GoodsReceiptNoteModel.bob_code_source_tuple().in_(
                (wh.bob_code, wh.receiving_type.grn_source_name) for wh in warehouses
            ),
        )
    )


def get_grn_by_record_date(filter_date: date, warehouses: list[Warehouse]) -> list[GoodsReceiptNote]:
    query = (
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.units_received,
            GoodsReceiptNoteModel.receipt_time_est,
            WarehouseModel.name.label("wh_code"),
        )
        .join(WarehouseModel, onclause=(WarehouseModel.bob_code == GoodsReceiptNoteModel.bob_code))
        .where(
            GoodsReceiptNoteModel.update_ts.cast(sqla.DATE) == filter_date,
            GoodsReceiptNoteModel.bob_code_source_tuple().in_(
                (wh.bob_code, wh.receiving_type.grn_source_name) for wh in warehouses
            ),
        )
    )
    return [
        GoodsReceiptNote(
            sku_code=record.sku_code,
            order_number=record.order_number,
            units_received=record.units_received,
            receipt_time_est=record.receipt_time_est,
            wh_code=record.wh_code,
        )
        for record in app_db.select_all(query)
    ]
