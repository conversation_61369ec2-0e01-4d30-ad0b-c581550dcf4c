from datetime import datetime

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.replenishment_comment import ReplenishmentCommentItem
from procurement.data.models.pimt.replenishment_comment import ReplenishmentCommentModel
from procurement.services.database import app_db


def get_replenishment_comments_preview(region: str) -> list[SKU_CODE]:
    market = context.get_request_context().market
    query = sqla.select(ReplenishmentCommentModel.sku_code).where(
        ReplenishmentCommentModel.region == region, ReplenishmentCommentModel.market == market
    )
    return [item.sku_code for item in app_db.select_all(query)]


def get_replenishment_comment(region: str, sku_code: str) -> ReplenishmentCommentItem:
    query = sqla.select(
        ReplenishmentCommentModel.text, ReplenishmentCommentModel.last_updated, ReplenishmentCommentModel.updated_by
    ).where(
        ReplenishmentCommentModel.region == region,
        ReplenishmentCommentModel.market == context.get_request_context().market,
        ReplenishmentCommentModel.sku_code == sku_code,
    )
    item = app_db.select_one(query)
    return ReplenishmentCommentItem(text=item.text, last_updated=item.last_updated, updated_by=item.updated_by)


def upsert_replenishment_comments(region: str, sku_code: str, text: str) -> None:
    query = psql_sqla.insert(ReplenishmentCommentModel)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=ReplenishmentCommentModel.get_primary_key_columns(),
            set_={
                ReplenishmentCommentModel.text: query.excluded.text,
                ReplenishmentCommentModel.updated_by: query.excluded.updated_by,
                ReplenishmentCommentModel.last_updated: query.excluded.last_updated,
            },
        ).values(
            {
                ReplenishmentCommentModel.market: context.get_request_context().market,
                ReplenishmentCommentModel.region: region,
                ReplenishmentCommentModel.sku_code: sku_code,
                ReplenishmentCommentModel.text: text,
                ReplenishmentCommentModel.updated_by: context.get_request_context().user_info.email,
                ReplenishmentCommentModel.last_updated: datetime.now(),
            }
        )
    )


def delete_replenishment_comments(region: str, sku_code: str) -> None:
    market = context.get_request_context().market
    query = sqla.delete(ReplenishmentCommentModel).where(
        ReplenishmentCommentModel.market == market,
        ReplenishmentCommentModel.region == region,
        ReplenishmentCommentModel.sku_code == sku_code,
    )
    app_db.apply_query(query)
