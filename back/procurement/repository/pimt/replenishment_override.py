from datetime import datetime
from decimal import Decimal

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.request_utils import context
from procurement.data.dto.pimt.replenishment_override import ReplenishmentOverrideItem
from procurement.data.models.pimt.replenishment_override import ReplenishmentOverrideModel
from procurement.services.database import app_db


def get_replenishment_overrides(region: str, sku_codes: set[str] | None = None) -> list[ReplenishmentOverrideItem]:
    market = context.get_request_context().market
    query = sqla.select(ReplenishmentOverrideModel.sku_code, ReplenishmentOverrideModel.value).where(
        ReplenishmentOverrideModel.region == region,
        ReplenishmentOverrideModel.market == market,
    )
    if sku_codes is not None:
        query = query.where(ReplenishmentOverrideModel.sku_code.in_(sku_codes))
    return [ReplenishmentOverrideItem(override=item.value, sku_code=item.sku_code) for item in app_db.select_all(query)]


def upsert_replenishment_override(region: str, sku_code: str, value: Decimal) -> None:
    context_item = context.get_request_context()
    query = psql_sqla.insert(ReplenishmentOverrideModel)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=ReplenishmentOverrideModel.get_primary_key_columns(),
            set_={
                ReplenishmentOverrideModel.value: query.excluded.value,
                ReplenishmentOverrideModel.updated_by: query.excluded.updated_by,
                ReplenishmentOverrideModel.last_updated: query.excluded.last_updated,
            },
        ).values(
            {
                ReplenishmentOverrideModel.market: context_item.market,
                ReplenishmentOverrideModel.region: region,
                ReplenishmentOverrideModel.sku_code: sku_code,
                ReplenishmentOverrideModel.value: value,
                ReplenishmentOverrideModel.updated_by: context_item.user_info.email,
                ReplenishmentOverrideModel.last_updated: datetime.now(),
            }
        )
    )


def delete_replenishment_override(region: str, sku_code: str) -> None:
    market = context.get_request_context().market
    query = sqla.delete(ReplenishmentOverrideModel).where(
        ReplenishmentOverrideModel.market == market,
        ReplenishmentOverrideModel.region == region,
        ReplenishmentOverrideModel.sku_code == sku_code,
    )
    app_db.apply_query(query)
