from collections.abc import Iterable

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select, Subquery

from procurement.core.request_utils import context
from procurement.core.typing import SITE, SKU_CODE
from procurement.data.dto.pimt.sku import IngredientDetails
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
)
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.services.database import app_db


def get_ingredient_details(
    sku_codes: Iterable[SKU_CODE] = None, sites: Iterable[SITE] = None
) -> list[IngredientDetails]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            CulinarySkuModel.sku_code,
            sqla.func.max(CulinarySkuModel.brands).label("brands"),
            sqla.func.max(CulinarySkuModel.sku_name).label("sku_name"),
            sqla.func.max(CulinarySkuModel.status).label("status"),
            sqla.func.max(PurchasingCategoryModel.name).label("purchasing_category_name"),
            sqla.func.array_agg(CommodityGroupModel.group_name.distinct()).label("commodity_groups"),
        )
        .join(
            PurchasingCategoryModel,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
            isouter=True,
        )
        .where(CulinarySkuModel.market == market)
    )
    if sku_codes is not None:
        query = query.where(CulinarySkuModel.sku_code.in_(list(sku_codes)))
    else:
        inventory_skus = _get_pimt_sku_codes_query()
        query = query.join(inventory_skus, onclause=CulinarySkuModel.sku_code == inventory_skus.c.sku_code)

    query = _join_commodity_groups(query, CulinarySkuModel.sku_code, sites)
    return [
        IngredientDetails(
            sku_name=item.sku_name,
            sku_code=item.sku_code,
            brands=item.brands,
            status=item.status,
            purchasing_category=item.purchasing_category_name,
            commodity_groups=list(filter(None, item.commodity_groups)),
        )
        for item in app_db.select_all(
            query.group_by(CulinarySkuModel.sku_code).order_by(sqla.func.max(CulinarySkuModel.sku_name))
        )
    ]


def _join_commodity_groups(
    query: Select,
    sku_code_field: ColumnElement,
    sites: Iterable[str] | None,
) -> Select:
    join_on = sku_code_field == IngredientSiteCommodityGroupModel.sku_code
    if sites is not None:
        join_on &= IngredientSiteCommodityGroupModel.site.in_(list(sites))

    query = query.join(
        IngredientSiteCommodityGroupModel,
        isouter=True,
        onclause=join_on,
    ).join(
        CommodityGroupModel,
        onclause=(IngredientSiteCommodityGroupModel.commodity_group_id == CommodityGroupModel.id),
        isouter=True,
    )

    return query


def _get_pimt_sku_codes_query() -> Subquery:
    return sqla.select(UnifiedInventoryModel.sku_code).distinct().subquery()
