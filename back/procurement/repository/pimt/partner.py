import sqlalchemy as sqla

from procurement.constants.hellofresh_constant import InventoryInputType, WhReceivingType
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import BOB_CODE, SITE, SUPPLIER, WH_CODE
from procurement.data.dto.pimt.warehouse import Warehouse
from procurement.data.models.pimt.partner import WarehouseModel
from procurement.services.database import app_db


@request_cache
def get_all_warehouses(market: str, e2open_grn: bool = False) -> tuple[Warehouse, ...]:
    query = sqla.select(WarehouseModel).where(WarehouseModel.market == market).order_by(WarehouseModel.order)
    if e2open_grn:
        query = query.where(WarehouseModel.receiving_type.in_([WhReceivingType.E2OPEN_GRN, WhReceivingType.HJ_GRN]))
    return tuple(_to_dto(r) for r in app_db.select_all(query))


def get_old_inventory_warehouses(market: str) -> list[Warehouse]:
    query = sqla.select(WarehouseModel).where(
        WarehouseModel.inventory_type == InventoryInputType.GSHEET, WarehouseModel.market == market
    )
    return [_to_dto(r) for r in app_db.select_all(query)]


def get_hj_inventory_warehouses(market: str) -> list[Warehouse]:
    query = sqla.select(WarehouseModel).where(
        WarehouseModel.inventory_type == InventoryInputType.HJ, WarehouseModel.market == market
    )
    return [_to_dto(r) for r in app_db.select_all(query)]


def get_supplier_to_warehouse(market: str) -> dict[SUPPLIER, WH_CODE]:
    return {
        supplier: warehouse.code
        for warehouse in get_all_warehouses(market=market)
        for supplier in warehouse.ot_suppliers
    }


def add_warehouse(warehouse: Warehouse) -> None:
    warehouse_max_order = app_db.select_scalar(sqla.select(sqla.func.max(WarehouseModel.order))) or 0
    query = sqla.insert(WarehouseModel).values(
        {
            WarehouseModel.code: warehouse.code,
            WarehouseModel.name: warehouse.name,
            WarehouseModel.bob_code: warehouse.bob_code,
            WarehouseModel.ot_dcs: warehouse.ot_dcs,
            WarehouseModel.ot_suppliers: warehouse.ot_suppliers,
            WarehouseModel.order: warehouse_max_order + 1,
            WarehouseModel.region: warehouse.region,
            WarehouseModel.regional_dcs: warehouse.regional_dcs,
            WarehouseModel.receiving_type: warehouse.receiving_type,
            WarehouseModel.inventory_type: warehouse.inventory_type,
            WarehouseModel.hj_name: warehouse.hj_name,
            WarehouseModel.is_3rd_party: warehouse.is_3rd_party,
            WarehouseModel.market: warehouse.market,
            WarehouseModel.packaging_regions: warehouse.packaging_regions,
        }
    )
    app_db.apply_query(query)


def update_warehouse(warehouse: Warehouse) -> None:
    query = (
        sqla.update(WarehouseModel)
        .where(WarehouseModel.code == warehouse.code)
        .values(
            {
                WarehouseModel.name: warehouse.name,
                WarehouseModel.bob_code: warehouse.bob_code,
                WarehouseModel.ot_dcs: warehouse.ot_dcs,
                WarehouseModel.ot_suppliers: warehouse.ot_suppliers,
                WarehouseModel.region: warehouse.region,
                WarehouseModel.regional_dcs: warehouse.regional_dcs,
                WarehouseModel.receiving_type: warehouse.receiving_type,
                WarehouseModel.inventory_type: warehouse.inventory_type,
                WarehouseModel.hj_name: warehouse.hj_name,
                WarehouseModel.is_3rd_party: warehouse.is_3rd_party,
                WarehouseModel.packaging_regions: warehouse.packaging_regions,
            }
        )
    )
    app_db.apply_query(query)


def delete_warehouse(code: str, market: str) -> None:
    app_db.apply_query(sqla.delete(WarehouseModel).where(WarehouseModel.code == code, WarehouseModel.market == market))


def update_order(new_order: dict, market: str) -> None:
    app_db.apply_query(
        sqla.update(WarehouseModel)
        .where(WarehouseModel.code == sqla.bindparam("warehouse_code"), WarehouseModel.market == market)
        .values({WarehouseModel.order.name: sqla.bindparam("new_order")}),
        [{"warehouse_code": c, "new_order": o} for c, o in new_order.items()],
    )


def get_order(market: str) -> list[tuple[WH_CODE, int], ...]:
    whs = app_db.select_all(
        sqla.select(WarehouseModel.code, WarehouseModel.order).where(WarehouseModel.market == market)
    )
    return [(w.code, w.order) for w in whs]


def get_whs_by_bob_code(market: str) -> dict[BOB_CODE, WH_CODE]:
    whs = app_db.select_all(
        sqla.select(WarehouseModel.code, WarehouseModel.bob_code).where(WarehouseModel.market == market)
    )
    return {w.bob_code: w.code for w in whs}


def get_sites() -> set[SITE]:
    query = sqla.select(sqla.func.unnest(WarehouseModel.regional_dcs).label("dc")).distinct()
    return {r.dc for r in app_db.select_all(query)}


def _to_dto(row: WarehouseModel) -> Warehouse:
    return Warehouse(
        code=row.code,
        name=row.name,
        bob_code=row.bob_code,
        ot_dcs=row.ot_dcs,
        ot_suppliers=row.ot_suppliers,
        order=row.order,
        region=row.region,
        regional_dcs=row.regional_dcs,
        receiving_type=WhReceivingType(row.receiving_type),
        inventory_type=row.inventory_type,
        hj_name=row.hj_name,
        is_3rd_party=row.is_3rd_party,
        market=row.market,
        packaging_regions=row.packaging_regions,
    )
