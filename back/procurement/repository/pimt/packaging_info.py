from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.typing import SKU_CODE
from procurement.data.dto.pimt.packaging_info import PackagingInfo
from procurement.data.models.inventory.supplier_sku import SupplierSkuModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.pimt.packaging_info import PackagingInfoModel
from procurement.services.database import app_db


def upsert_packaging_info(packaging_info: dict[ColumnElement, Any]):
    query = psql_sqla.insert(PackagingInfoModel)
    query = query.on_conflict_do_update(
        index_elements=[PackagingInfoModel.supplier_sku_uuid],
        set_={
            PackagingInfoModel.cases_per_pallet: query.excluded.cases_per_pallet,
            PackagingInfoModel.units_per_pallet: query.excluded.units_per_pallet,
        },
    ).values(packaging_info)
    app_db.apply_query(query)


def get_packaging_info(skus: Iterable[SKU_CODE] = None) -> list[PackagingInfo]:
    query = (
        sqla.select(
            sqla.func.avg(PackagingInfoModel.cases_per_pallet).label("cases_per_pallet"),
            sqla.func.avg(PackagingInfoModel.units_per_pallet).label("units_per_pallet"),
            CulinarySkuModel.sku_code,
        )
        .join(SupplierSkuModel, onclause=(PackagingInfoModel.supplier_sku_uuid == SupplierSkuModel.supplier_sku_uuid))
        .join(CulinarySkuModel, onclause=(SupplierSkuModel.culinary_sku_uuid == CulinarySkuModel.sku_uuid))
        .group_by(CulinarySkuModel.sku_code)
    )
    if skus is not None:
        query = query.where(CulinarySkuModel.sku_code.in_(list(skus)))
    return [
        PackagingInfo(
            cases_per_pallet=round(item.cases_per_pallet),
            units_per_pallet=round(item.units_per_pallet),
            sku_code=item.sku_code,
        )
        for item in app_db.select_all(query)
    ]
