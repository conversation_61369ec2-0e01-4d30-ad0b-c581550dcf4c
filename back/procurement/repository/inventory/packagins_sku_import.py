from collections.abc import Collection

import sqlalchemy as sqla

from procurement.data.dto.inventory.packaging_sku_import import PackagingSkuImportData
from procurement.data.models.inventory.packaging_units_per_truck_load import PackagingSkuImportModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def upsert_packaging_sku_import_data(data: Collection[dict]):
    repository_utils.safe_bulk_insert_sqla(
        model=PackagingSkuImportModel,
        data=data,
        keys=[
            PackagingSkuImportModel.bob_code,
            PackagingSkuImportModel.sku_code,
        ],
        preserve_fields=[PackagingSkuImportModel.units_per_truck_load],
    )


def get_packaging_sku_import_data(pck_regions: set[str]) -> list[PackagingSkuImportData]:
    query = sqla.select(
        PackagingSkuImportModel.sku_code,
        PackagingSkuImportModel.units_per_truck_load,
        PackagingSkuImportModel.bob_code,
    ).where(PackagingSkuImportModel.bob_code.in_(pck_regions))

    return [
        PackagingSkuImportData(
            sku_code=item.sku_code, bob_code=item.bob_code, units_per_truck=item.units_per_truck_load
        )
        for item in app_db.select_all(query)
    ]
