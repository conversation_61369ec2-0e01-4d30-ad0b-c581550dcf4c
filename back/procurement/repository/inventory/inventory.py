from collections.abc import Collection, Iterable
from datetime import date, datetime, timedelta
from uuid import UUID

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select, Subquery, Tuple, label
from sqlalchemy.dialects import postgresql as psql

from procurement.constants.hellofresh_constant import HjInventoryState, InventoryInputType, InventoryState
from procurement.core import utils
from procurement.core.config_utils import killswitch
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, HJ_NAME
from procurement.data.dto.inventory.inventory import Inventory, InventoryUnitPrice
from procurement.data.dto.inventory.unified_inventory import UnifiedInventoryDto
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.admin import brand_admin, dc_admin
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.highjump import HjPalletSnapshotData
from procurement.services.database import app_db

SNAPSHOT_LIFETIME = 45


def get_inventory_records(
    bob_code: str, inventory_type: InventoryInputType, date_from: date, date_to: date
) -> list[Inventory]:
    po_subquery = _get_po_subquery()

    query = (
        sqla.select(
            UnifiedInventoryModel.sku_code,
            UnifiedInventoryModel.snapshot_timestamp,
            UnifiedInventoryModel.expiration_date,
            sqla.func.sum(
                sqla.func.coalesce(
                    UnifiedInventoryModel.unit_quantity,
                    UnifiedInventoryModel.case_quantity
                    * sqla.func.coalesce(UnifiedInventoryModel.case_size, po_subquery.c.case_size, 0),
                    0,
                )
            ).label("quantity"),
            sqla.func.count(UnifiedInventoryModel.lot_code.distinct()).label("license_count"),
        )
        .join(
            po_subquery,
            isouter=True,
            onclause=sqla.and_(
                po_subquery.c.order_number == UnifiedInventoryModel.order_number,
                po_subquery.c.sku_code == UnifiedInventoryModel.sku_code,
            ),
        )
        .group_by(
            UnifiedInventoryModel.sku_code,
            UnifiedInventoryModel.snapshot_timestamp,
            UnifiedInventoryModel.expiration_date,
        )
        .where(
            UnifiedInventoryModel.wh_code == bob_code,
            UnifiedInventoryModel.inventory_type == inventory_type,
            UnifiedInventoryModel.snapshot_timestamp >= date_from,
            UnifiedInventoryModel.snapshot_timestamp <= date_to,
            UnifiedInventoryModel.inventory_status == InventoryState.AVAILABLE,
        )
    )

    return [
        Inventory(
            sku_code=item.sku_code,
            snapshot_timestamp=item.snapshot_timestamp.date(),
            quantity=item.quantity,
            expiration_date=item.expiration_date,
            license_count=item.license_count,
        )
        for item in app_db.select_all(query)
    ]


def _get_po_subquery() -> Subquery:
    return (
        sqla.select(
            PurchaseOrderSkuModel.case_size,
            PurchaseOrderModel.order_number,
            CulinarySkuModel.sku_code,
            PurchaseOrderModel.supplier_code,
            PurchaseOrderModel.source_bob_code,
        )
        .join(PurchaseOrderModel, onclause=(PurchaseOrderModel.po_uuid == PurchaseOrderSkuModel.po_uuid))
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .where(
            ~PurchaseOrderModel.deleted,
            PurchaseOrderModel.is_sent,
        )
        .subquery()
    )


def get_unified_inventory(
    inventory_selector: dict[BOB_CODE, InventoryInputType],
    *,
    snapshot_day: date = None,
    sku_codes: Iterable[str] | None = None,
    multibrand_wh_code_mapping: dict[BOB_CODE, str] | None = None,
) -> list[UnifiedInventoryDto]:
    market = context.get_request_context().market
    po_subquery = _get_po_subquery()
    supplier_columns_in_order = [UnifiedInventoryModel.supplier, SupplierModel.name]
    if killswitch.transfer_orders_enabled:
        # Using bob_code here as supplier name until we know how to map inventory items to transfer order.
        # Ideally transferred inventory should have supplier, and should not fall back to ordering data.
        supplier_columns_in_order.append(po_subquery.c.source_bob_code)
    query = (
        sqla.select(
            UnifiedInventoryModel.wh_code,
            UnifiedInventoryModel.po_number,
            UnifiedInventoryModel.sku_code,
            UnifiedInventoryModel.lot_code,
            UnifiedInventoryModel.expiration_date,
            UnifiedInventoryModel.inventory_status,
            UnifiedInventoryModel.inventory_type,
            UnifiedInventoryModel.snapshot_timestamp,
            UnifiedInventoryModel.location_id,
            sqla.func.coalesce(*supplier_columns_in_order).label("supplier"),
            unit_quantity(po_subquery.c.case_size).label("units"),
        )
        .join(
            po_subquery,
            onclause=sqla.and_(
                UnifiedInventoryModel.order_number == po_subquery.c.order_number,
                UnifiedInventoryModel.sku_code == po_subquery.c.sku_code,
            ),
            isouter=True,
        )
        .join(
            SupplierModel,
            onclause=sqla.and_(
                po_subquery.c.supplier_code == SupplierModel.code,
                SupplierModel.market == market,
            ),
            isouter=True,
        )
        .where(
            Tuple(
                UnifiedInventoryModel.wh_code,
                UnifiedInventoryModel.inventory_type,
                UnifiedInventoryModel.snapshot_timestamp,
            ).in_(_get_latest_timestamps(inventory_selector, snapshot_day).scalar_subquery()),
            UnifiedInventoryModel.inventory_status != InventoryState.TOTAL,
            (
                (sqla.func.coalesce(UnifiedInventoryModel.case_quantity, 0) != 0)
                | (sqla.func.coalesce(UnifiedInventoryModel.unit_quantity, 0) != 0)
            ),
        )
    )

    if sku_codes:
        query = query.where(UnifiedInventoryModel.sku_code.in_(sku_codes))

    return [
        UnifiedInventoryDto(
            source=r.inventory_type,
            snapshot_timestamp=r.snapshot_timestamp,
            bob_code=multibrand_wh_code_mapping.get(r.wh_code, r.wh_code) if multibrand_wh_code_mapping else r.wh_code,
            po_number=r.po_number,
            sku_code=r.sku_code,
            units=r.units,
            status=r.inventory_status,
            lot_code=r.lot_code,
            location_id=r.location_id,
            expiration_date=r.expiration_date,
            supplier=r.supplier,
        )
        for r in app_db.select_all(query)
    ]


def delete_inventory_by_warehouse_snapshot_timestamp(
    wh_codes: Iterable[str], inventory_type: InventoryInputType, snapshot_timestamp: datetime
) -> None:
    app_db.apply_query(
        sqla.delete(UnifiedInventoryModel).where(
            UnifiedInventoryModel.wh_code.in_(wh_codes),
            UnifiedInventoryModel.inventory_type == inventory_type,
            UnifiedInventoryModel.snapshot_timestamp >= snapshot_timestamp.date(),
            UnifiedInventoryModel.snapshot_timestamp < snapshot_timestamp,
        )
    )


def delete_inventory_by_warehouse_snapshot_id(
    wh_codes: Iterable[str], inventory_type: InventoryInputType, snapshot_date: date, snapshot_id: UUID
) -> None:
    """
    Deletes all snapshots for the snapshot date if they don't match the snapshot id
    """
    app_db.apply_query(
        sqla.delete(UnifiedInventoryModel).where(
            UnifiedInventoryModel.wh_code.in_(wh_codes),
            UnifiedInventoryModel.inventory_type == inventory_type,
            UnifiedInventoryModel.snapshot_timestamp >= snapshot_date,
            UnifiedInventoryModel.snapshot_timestamp < (snapshot_date + timedelta(days=1)),
            UnifiedInventoryModel.snapshot_id != snapshot_id,
        )
    )


def update_hj_inventory_snapshot(brand: str, data: Collection[HjPalletSnapshotData], snapshot_timestamp: datetime):
    bob_codes_map = {
        dc.high_jump_name: dc.bob_code for dc in _get_sites_by_inventory_type(brand, InventoryInputType.HJ)
    }
    if not bob_codes_map or not data:
        return
    with app_db.transaction() as trns:
        app_db.apply_query(
            sqla.delete(UnifiedInventoryModel).where(
                UnifiedInventoryModel.wh_code.in_(bob_codes_map.values()),
                UnifiedInventoryModel.inventory_type == InventoryInputType.HJ,
                UnifiedInventoryModel.snapshot_timestamp >= snapshot_timestamp.date(),
                UnifiedInventoryModel.snapshot_timestamp < snapshot_timestamp,
            ),
            transaction=trns,
        )
        updates = _unify_hj_data(data, snapshot_timestamp, bob_codes_map)
        app_db.apply_query(
            psql.insert(UnifiedInventoryModel)
            .values(updates)
            .on_conflict_do_nothing(constraint=UnifiedInventoryModel.__table_args__[0]),
            transaction=trns,
        )


def _unify_hj_data(
    data: Collection[HjPalletSnapshotData], snapshot_timestamp: datetime, bob_codes_map: dict[HJ_NAME, BOB_CODE]
) -> list[dict]:
    return [
        {
            UnifiedInventoryModel.wh_code: bob_codes_map[it.wh_id],
            UnifiedInventoryModel.po_number: it.po_number,
            UnifiedInventoryModel.order_number: utils.get_order_number_from_po_number(it.po_number),
            UnifiedInventoryModel.sku_code: it.sku_code,
            UnifiedInventoryModel.lot_code: it.license_plate,
            UnifiedInventoryModel.unit_quantity: it.pallet_quantity,
            UnifiedInventoryModel.expiration_date: it.expiration_date,
            UnifiedInventoryModel.inventory_status: HjInventoryState.to_unified_state(it.status),
            UnifiedInventoryModel.inventory_type: InventoryInputType.HJ,
            UnifiedInventoryModel.snapshot_timestamp: snapshot_timestamp,
            UnifiedInventoryModel.imt_update_ts: snapshot_timestamp,
            UnifiedInventoryModel.location_id: it.location_id,
        }
        for it in data
        if it.wh_id in bob_codes_map
    ]


def _get_sites_by_inventory_type(brand: str, inventory_type: InventoryInputType) -> list[DcConfig]:
    week = ScmWeek.current_week(brand_admin.get_week_config(brand))
    return [dc for dc in dc_admin.get_enabled_sites(week, brand).values() if dc.inventory_type == inventory_type]


def delete_old_inventory_records(wh_codes: Iterable[str]) -> None:
    app_db.apply_query(
        sqla.delete(UnifiedInventoryModel).where(
            UnifiedInventoryModel.snapshot_timestamp < date.today() - timedelta(days=SNAPSHOT_LIFETIME),
            UnifiedInventoryModel.wh_code.in_(wh_codes),
        )
    )


def insert_inventory(updates: list[dict]) -> None:
    app_db.apply_query(sqla.insert(UnifiedInventoryModel).values(updates))


def upsert_inventory(updates: list[dict]) -> None:
    query = psql.insert(UnifiedInventoryModel)
    query = query.on_conflict_do_update(
        index_elements=[
            UnifiedInventoryModel.order_number,
            UnifiedInventoryModel.sku_code,
            UnifiedInventoryModel.wh_code,
            UnifiedInventoryModel.inventory_type,
            UnifiedInventoryModel.lot_code,
            UnifiedInventoryModel.location_id,
            UnifiedInventoryModel.inventory_status,
            UnifiedInventoryModel.expiration_date,
            UnifiedInventoryModel.snapshot_timestamp,
        ],
        set_={
            UnifiedInventoryModel.case_quantity: query.excluded.case_quantity,
            UnifiedInventoryModel.unit_quantity: query.excluded.unit_quantity,
        },
    )

    app_db.apply_query(query.values(updates))


def get_latest_inventory_records(bob_code: str, inventory_type: InventoryInputType) -> list[Inventory]:
    latest_date = get_max_date(bob_code, inventory_type)
    if not latest_date:
        return []
    return get_inventory_records(bob_code, inventory_type, latest_date, latest_date)


def get_max_date(bob_code: str, inventory_type: InventoryInputType) -> datetime:
    return app_db.select_scalar(
        sqla.select(sqla.func.max(UnifiedInventoryModel.snapshot_timestamp)).where(
            UnifiedInventoryModel.wh_code == bob_code, UnifiedInventoryModel.inventory_type == inventory_type
        )
    )


def unit_quantity(case_size_column: ColumnElement) -> ColumnElement:
    return sqla.func.coalesce(
        UnifiedInventoryModel.unit_quantity,
        UnifiedInventoryModel.case_quantity * case_size_column,
        0,
    )


def get_latest_inventory_timestamps(
    inventory_selector: dict[BOB_CODE, InventoryInputType],
) -> dict[tuple[BOB_CODE, InventoryInputType], datetime]:
    return {
        (row.wh_code, row.inventory_type): row.ts
        for row in app_db.select_all(_get_latest_timestamps(inventory_selector))
    }


def _get_latest_timestamps(inventory_selector: dict[BOB_CODE, InventoryInputType], snapshot_day: date = None) -> Select:
    if len(set(inventory_selector.values())) == 1:
        # that way performs better when it's only one type
        where_clause = sqla.and_(
            UnifiedInventoryModel.wh_code.in_(inventory_selector.keys()),
            UnifiedInventoryModel.inventory_type == next(iter(inventory_selector.values())),
        )
    else:
        where_clause = Tuple(UnifiedInventoryModel.wh_code, UnifiedInventoryModel.inventory_type).in_(
            inventory_selector.items()
        )
    query = (
        sqla.select(
            UnifiedInventoryModel.wh_code,
            UnifiedInventoryModel.inventory_type,
            sqla.func.max(UnifiedInventoryModel.snapshot_timestamp).label("ts"),
        )
        .where(where_clause)
        .group_by(UnifiedInventoryModel.wh_code, UnifiedInventoryModel.inventory_type)
    )
    if snapshot_day:
        query = query.where(
            UnifiedInventoryModel.snapshot_timestamp >= snapshot_day,
            UnifiedInventoryModel.snapshot_timestamp < (snapshot_day + timedelta(days=1)),
        )
    return query


def get_inventory_unit_costs(
    po_numbers: Iterable[str],
) -> list[InventoryUnitPrice]:
    unit_cost_column = sqla.case(
        (PurchaseOrderSkuModel.case_size == 0, 0),
        else_=(PurchaseOrderSkuModel.case_price / PurchaseOrderSkuModel.case_size),
    )

    query = (
        sqla.select(
            PurchaseOrderModel.po_number,
            CulinarySkuModel.sku_code,
            label("unit_cost", unit_cost_column),
        )
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .where(
            PurchaseOrderModel.po_number.in_(po_numbers),
        )
    )

    return [
        InventoryUnitPrice(
            po_number=item.po_number,
            sku_code=item.sku_code,
            cost=item.unit_cost,
        )
        for item in app_db.select_all(query)
    ]
