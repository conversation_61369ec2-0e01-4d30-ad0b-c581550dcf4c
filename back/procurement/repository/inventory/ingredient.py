import logging
from datetime import date, datetime, timedelta
from decimal import <PERSON>ima<PERSON>
from typing import Any, Type

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Connection, Row
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.request_utils.cache import request_cache
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.ingredient import CommodityGroup, KittingFlag, Meal, Mealkit, PkgData, SkuMeals
from procurement.data.dto.inventory.pull_put import ExportPullPut, PullPutDto
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
    PurchasingSubcategoryModel,
)
from procurement.data.models.inventory.inventory_pull_put import PullPutModel
from procurement.data.models.inventory.mealkit import MealkitIngredientModel, MealkitModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.repository import repository_utils
from procurement.services.database import app_db

logger = logging.getLogger(__name__)


def update_ingredients(
    update_data: list[dict[ColumnElement, Any]],
    transaction: Connection,
) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=IngredientModel,
        data=update_data,
        keys=[IngredientModel.sku_code],
        preserve_fields=[
            IngredientModel.pack_size_amount,
            IngredientModel.pack_size_unit,
            IngredientModel.brand,
            IngredientModel.storage_location,
            IngredientModel.allergens,
        ],
        transaction=transaction,
    )


def update_purchase_category_model(
    data: dict[ColumnElement, Any],
    model: Type[PurchasingCategoryModel | PurchasingSubcategoryModel],
    transaction: Connection = None,
) -> None:
    logger.debug("Update %s", model.__name__)
    # never too many records
    app_db.apply_query(
        psql_sqla.insert(model).values(data).on_conflict_do_nothing(),
        transaction=transaction,
    )


def get_or_create_purchasing_category_model_id(
    name: str, model: Type[PurchasingCategoryModel | PurchasingSubcategoryModel]
) -> int:
    with app_db.transaction() as trns:
        purchasing_category_id = get_category_model_id_by_name(name, model, trns)
        if not purchasing_category_id:
            update_purchase_category_model({model.name: name}, model, trns)
            purchasing_category_id = get_category_model_id_by_name(name, model, trns)
        return purchasing_category_id


def get_category_model_id_by_name(
    name: str, model: Type[PurchasingCategoryModel | PurchasingSubcategoryModel], transaction: Connection = None
) -> int | None:
    return app_db.select_scalar(
        sqla.select(model.id).where(model.name == name),
        transaction=transaction,
    )


def update_commodities(names: set[str]) -> None:
    # never too many records
    app_db.apply_query(
        psql_sqla.insert(CommodityGroupModel)
        .values([{CommodityGroupModel.group_name: name} for name in names])
        .on_conflict_do_nothing()
    )


def _clean_up_categories(
    model: Type[PurchasingCategoryModel | PurchasingSubcategoryModel], culinary_sku_category_id_field: ColumnElement
) -> None:
    unused_categories = (
        sqla.select(model.id.distinct())
        .join(
            CulinarySkuModel,
            onclause=(model.id == culinary_sku_category_id_field),
            isouter=True,
        )
        .where(CulinarySkuModel.sku_code.is_(None))
    )
    app_db.apply_query(sqla.delete(model).where(model.id.in_(unused_categories)))


def clean_up_categories() -> None:
    _clean_up_categories(PurchasingCategoryModel, CulinarySkuModel.purchasing_category_id)
    _clean_up_categories(PurchasingSubcategoryModel, CulinarySkuModel.purchasing_subcategory_id)


def clean_up_commodity_groups() -> None:
    unused_commodity_groups = (
        sqla.select(CommodityGroupModel.id)
        .join(
            IngredientSiteCommodityGroupModel,
            onclause=(CommodityGroupModel.id == IngredientSiteCommodityGroupModel.commodity_group_id),
            isouter=True,
        )
        .where(IngredientSiteCommodityGroupModel.sku_code.is_(None))
    )
    app_db.apply_query(sqla.delete(CommodityGroupModel).where(CommodityGroupModel.id.in_(unused_commodity_groups)))


def get_kitting_flags_by_weeks(pkg_site: str, brand: str, weeks: frozenset[ScmWeek]) -> list[KittingFlag]:
    query = (
        sqla.select(
            MealkitModel.scm_week,
            MealkitIngredientModel.sku_code,
            sqla.func.bool_or(MealkitIngredientModel.is_in_kitbox).label("is_in_kitbox"),
        )
        .join(MealkitIngredientModel, onclause=(MealkitModel.id == MealkitIngredientModel.mealkit_id))
        .where(
            MealkitModel.scm_week.in_(map(str, weeks)),
            MealkitModel.brand == brand,
            MealkitIngredientModel.dc_list.contains([pkg_site]),
        )
        .group_by(MealkitModel.scm_week, MealkitIngredientModel.sku_code)
    )
    return [
        KittingFlag(week=ScmWeek.from_str(item.scm_week), sku_code=item.sku_code, is_in_kitbox=item.is_in_kitbox)
        for item in app_db.select_all(query)
    ]


def get_commodity_groups() -> list[CommodityGroup]:
    return [
        CommodityGroup(id=item.id, group_name=item.group_name)
        for item in app_db.select_all(sqla.select(CommodityGroupModel))
    ]


def load_pkg_data(week: ScmWeek, site: str, brand: str) -> list[PkgData]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            IngredientModel.sku_code,
            MealkitIngredientModel.weight_amount,
            MealkitIngredientModel.weight_unit,
            IngredientModel.storage_location,
            IngredientModel.allergens,
            CulinarySkuModel.sku_id,
            CulinarySkuModel.sku_name,
            MealkitModel.code,
            MealkitModel.slot,
            MealkitModel.meal_name,
            MealkitModel.sub_recipe,
            MealkitIngredientModel.picks_2p,
            MealkitIngredientModel.picks_3p,
            MealkitIngredientModel.picks_4p,
            MealkitIngredientModel.picks_6p,
            PurchasingCategoryModel.name.label("purchasing_category"),
        )
        .join(MealkitIngredientModel, onclause=(IngredientModel.sku_code == MealkitIngredientModel.sku_code))
        .join(CulinarySkuModel, onclause=(IngredientModel.sku_code == CulinarySkuModel.sku_code))
        .join(MealkitModel, onclause=(MealkitIngredientModel.mealkit_id == MealkitModel.id))
        .join(
            PurchasingCategoryModel,
            isouter=True,
            onclause=(CulinarySkuModel.purchasing_category_id == PurchasingCategoryModel.id),
        )
        .where(
            MealkitModel.scm_week == str(week),
            MealkitIngredientModel.dc_list.contains([site]),
            MealkitModel.brand == brand,
            ~IngredientModel.is_receipt_bp_drop,
            CulinarySkuModel.market == market,
        )
    )
    return [
        PkgData(
            sku_code=item.sku_code,
            weight_amount=item.weight_amount,
            weight_unit=item.weight_unit,
            storage_location=item.storage_location,
            allergens=item.allergens,
            sku_id=item.sku_id,
            sku_name=item.sku_name,
            code=item.code,
            slot=item.slot,
            meal_name=item.meal_name,
            picks_2p=item.picks_2p,
            picks_3p=item.picks_3p,
            picks_4p=item.picks_4p,
            picks_6p=item.picks_6p,
            purcasing_category=item.purchasing_category,
            sub_recipe=item.sub_recipe,
        )
        for item in app_db.select_all(query)
    ]


@request_cache
def get_meals(week: ScmWeek, site: str, brand: str) -> list[Meal]:
    query = (
        sqla.select(
            MealkitModel.slot,
            MealkitIngredientModel.weight_amount,
            MealkitIngredientModel.weight_unit,
            MealkitIngredientModel.sku_code,
            MealkitIngredientModel.picks_2p,
            MealkitIngredientModel.picks_4p,
            MealkitIngredientModel.picks_6p,
        )
        .join(MealkitIngredientModel, isouter=True, onclause=(MealkitModel.id == MealkitIngredientModel.mealkit_id))
        .where(
            MealkitIngredientModel.dc_list.contains([site]),
            MealkitModel.scm_week == str(week),
            MealkitModel.brand == brand,
        )
    )
    return [
        Meal(
            slot=item.slot,
            sku_code=item.sku_code,
            picks_2p=item.picks_2p,
            picks_4p=item.picks_4p,
            picks_6p=item.picks_6p,
            weight_amount=Decimal(str(item.weight_amount or 0)),
            unit_of_measure=UnitOfMeasure.inexact_value_of(item.weight_unit),
        )
        for item in app_db.select_all(query)
    ]


def get_sku_meal_numbers(week: ScmWeek, site: str, brand: str, sku_codes: set[SKU_CODE] = None) -> list[SkuMeals]:
    query = (
        sqla.select(IngredientModel.sku_code, sqla.func.array_agg(MealkitModel.slot.distinct()).label("meal_numbers"))
        .join(MealkitIngredientModel, onclause=(IngredientModel.sku_code == MealkitIngredientModel.sku_code))
        .join(MealkitModel, onclause=(MealkitIngredientModel.mealkit_id == MealkitModel.id))
        .where(
            MealkitModel.scm_week == str(week),
            MealkitModel.brand == brand,
            MealkitIngredientModel.dc_list.contains([site]),
            ~IngredientModel.is_receipt_bp_drop,
        )
        .group_by(IngredientModel.sku_code)
    )

    if utils.is_any_collection_empty(sku_codes):
        return []
    if sku_codes:
        query = query.where(IngredientModel.sku_code.in_(sku_codes))

    return _build_sku_meals(app_db.select_all(query))


def _build_sku_meals(items: list[Row]) -> list[SkuMeals]:
    return [SkuMeals(sku_code=item.sku_code, meal_numbers=item.meal_numbers) for item in items]


def update_commodity_groups(site_sku_commodity: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=IngredientSiteCommodityGroupModel,
        data=site_sku_commodity,
        keys=[
            IngredientSiteCommodityGroupModel.sku_code,
            IngredientSiteCommodityGroupModel.site,
            IngredientSiteCommodityGroupModel.market,
        ],
        preserve_fields=[IngredientSiteCommodityGroupModel.commodity_group_id],
    )


def get_mealkit_by_week_and_brand(week: ScmWeek, brand: str, transaction: Connection) -> list[Mealkit]:
    return [
        Mealkit(id=item.id, slot=item.slot, code=item.code)
        for item in app_db.select_all(
            query=sqla.select(MealkitModel.id, MealkitModel.slot, MealkitModel.code).where(
                MealkitModel.scm_week == str(week), MealkitModel.brand == brand
            ),
            transaction=transaction,
        )
    ]


def delete_mealkit_by_week_and_brand(week: ScmWeek, brand, transaction: Connection) -> None:
    app_db.apply_query(
        query=sqla.delete(MealkitModel).where(MealkitModel.scm_week == str(week), MealkitModel.brand == brand),
        transaction=transaction,
    )


def insert_many_mealkit(updates: list[dict[ColumnElement, Any]], transaction: Connection) -> None:
    app_db.apply_query(query=sqla.insert(MealkitModel).values(updates), transaction=transaction)


def insert_many_mealkit_ingredient(updates: list[dict[ColumnElement, Any]], transaction: Connection) -> None:
    app_db.apply_query(query=sqla.insert(MealkitIngredientModel).values(updates), transaction=transaction)


def get_pull_put_week_by_id(item_id: int) -> ScmWeek:
    return ScmWeek.from_str(app_db.select_scalar(sqla.select(PullPutModel.week).where(PullPutModel.id == item_id)))


def get_pull_put(week, fetch_sku=False, brand=None, site=None, sku_codes=None) -> list[PullPutDto]:
    market = context.get_request_context().market
    fields = [PullPutModel]
    if fetch_sku:
        fields.extend([CulinarySkuModel.sku_name, CulinarySkuModel.unit])

    query = sqla.select(*fields).where(
        PullPutModel.week == str(week), PullPutModel.market == market, PullPutModel.deleted_ts.is_(None)
    )

    if fetch_sku:
        query = query.join(
            CulinarySkuModel,
            onclause=((PullPutModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)),
        )

    if brand:
        query = query.where(PullPutModel.brand == brand.upper())

    if site:
        query = query.where(PullPutModel.dc == site.upper())

    if sku_codes:
        query = query.where(PullPutModel.sku_code.in_(sku_codes))

    return [_build_pull_put_dto_item(item, fetch_culinary=fetch_sku) for item in app_db.select_all(query)]


def get_pull_put_items_for_export(export_date: date) -> list[PullPutDto]:
    market = context.get_request_context().market
    query = (
        sqla.select(PullPutModel, CulinarySkuModel.sku_name, CulinarySkuModel.unit)
        .join(
            CulinarySkuModel,
            onclause=((PullPutModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)),
        )
        .where(
            PullPutModel.market == market,
            PullPutModel.upd_tmst < export_date + timedelta(days=1),
            PullPutModel.upd_tmst >= export_date,
            PullPutModel.deleted_ts.is_(None),
        )
    )
    return [_build_pull_put_dto_item(item, fetch_culinary=True) for item in app_db.select_all(query)]


def _build_pull_put_dto_item(item: Row, fetch_culinary: bool = False) -> PullPutDto:
    return PullPutDto(
        inventory_id=item.id,
        dc=item.dc,
        brand=item.brand,
        user_email=item.user_email,
        sku_code=item.sku_code,
        sku_name=item.sku_name if fetch_culinary else None,
        qty=item.qty,
        comment=item.comment,
        week=ScmWeek.from_str(item.week),
        updated_at=item.upd_tmst,
        unit_of_measure=item.unit if fetch_culinary else None,
    )


def insert_many_pull_put(data) -> list[int]:
    return [item.id for item in app_db.apply_query(sqla.insert(PullPutModel).values(data).returning(PullPutModel.id))]


def delete_pull_put_by_id(record_id: int, user_email: str) -> None:
    app_db.apply_query(
        sqla.update(PullPutModel)
        .where(PullPutModel.id == record_id, PullPutModel.deleted_ts.is_(None))
        .values({PullPutModel.deleted_ts: datetime.now(), PullPutModel.deleted_by: user_email})
    )


def get_pull_put_by_id(record_id: int) -> PullPutDto | None:
    market = context.get_request_context().market
    item = app_db.select_one(
        sqla.select(PullPutModel, CulinarySkuModel.sku_name, CulinarySkuModel.unit)
        .join(
            CulinarySkuModel,
            onclause=((PullPutModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)),
        )
        .where(PullPutModel.id == record_id, PullPutModel.deleted_ts.is_(None))
    )
    return _build_pull_put_dto_item(item, fetch_culinary=True) if item else None


def edit_pull_put_item(record_id: int, new_qty: int, comment: str, update_timestamp: datetime) -> None:
    app_db.apply_query(
        sqla.update(PullPutModel)
        .where(PullPutModel.id == record_id)
        .values(
            {
                PullPutModel.qty: new_qty,
                PullPutModel.user_email: context.get_request_context().user_info.email,
                PullPutModel.source: "WEB_APP",
                PullPutModel.upd_tmst: update_timestamp,
                PullPutModel.comment: comment if comment != "" else None,
            }
        )
    )


def export_pull_put_items(week, site, brand) -> list[ExportPullPut]:
    market = context.get_request_context().market
    query = (
        sqla.select(PullPutModel.qty, CulinarySkuModel.sku_name)
        .join(
            CulinarySkuModel,
            onclause=((PullPutModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)),
        )
        .where(
            CulinarySkuModel.market == market,
            PullPutModel.week == str(week).upper(),
            PullPutModel.dc == site.upper(),
            PullPutModel.brand == brand.upper(),
            PullPutModel.market == market,
            PullPutModel.deleted_ts.is_(None),
        )
    )
    return [ExportPullPut(sku_name=item.sku_name, qty=item.qty) for item in app_db.select_all(query)]
