from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.weekly_snapshot import WeeklySnapshot
from procurement.data.models.inventory.weekly_snapshot import WeeklySnapshotModel
from procurement.services.database import app_db


def insert_weekly_snapshots(data: list[dict[ColumnElement, Any]]) -> None:
    app_db.apply_query(sqla.insert(WeeklySnapshotModel).values(data))


def delete_weekly_snapshots(wh_codes: list[str], market: str, week: ScmWeek) -> None:
    app_db.apply_query(
        sqla.delete(WeeklySnapshotModel).where(
            WeeklySnapshotModel.market == market,
            WeeklySnapshotModel.week == int(week),
            WeeklySnapshotModel.wh_code.in_(wh_codes),
        )
    )


def get_weekly_snapshots(market: str, bob_code: str, week: ScmWeek) -> list[WeeklySnapshot]:
    query = sqla.select(WeeklySnapshotModel.sku_code, WeeklySnapshotModel.quantity).where(
        WeeklySnapshotModel.market == market,
        WeeklySnapshotModel.wh_code == bob_code,
        WeeklySnapshotModel.week == int(week),
    )

    return [WeeklySnapshot(sku_code=item.sku_code, quantity=item.quantity) for item in app_db.select_all(query)]
