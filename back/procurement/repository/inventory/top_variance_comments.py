import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.inventory.top_variance_comments import TopVarianceComment, TopVarianceCommentInput
from procurement.data.models.inventory.top_variance_comments import TopVarianceCommentModel
from procurement.services.database import app_db


def get_top_variance_comments(brand: str, site: str, week: ScmWeek) -> list[TopVarianceComment]:
    market = context.get_request_context().market
    query = sqla.select(
        TopVarianceCommentModel.comment,
        TopVarianceCommentModel.last_edited_by,
        TopVarianceCommentModel.sku_code,
        TopVarianceCommentModel.po_number,
    ).where(
        TopVarianceCommentModel.market == market,
        TopVarianceCommentModel.brand == brand,
        TopVarianceCommentModel.site == site,
        TopVarianceCommentModel.week == int(week),
    )
    return [
        TopVarianceComment(
            sku_code=item.sku_code, po_number=item.po_number, comment=item.comment, last_edited_by=item.last_edited_by
        )
        for item in app_db.select_all(query)
    ]


def upsert_top_variance_comment(comment_item: TopVarianceCommentInput) -> None:
    market = context.get_request_context().market
    query = psql_sqla.insert(TopVarianceCommentModel).values(
        {
            TopVarianceCommentModel.market: market,
            TopVarianceCommentModel.week: int(comment_item.week),
            TopVarianceCommentModel.brand: comment_item.brand,
            TopVarianceCommentModel.site: comment_item.site,
            TopVarianceCommentModel.sku_code: comment_item.sku_code,
            TopVarianceCommentModel.po_number: comment_item.po_number,
            TopVarianceCommentModel.comment: comment_item.comment,
            TopVarianceCommentModel.last_edited_by: comment_item.last_edited_by,
        }
    )
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[
                TopVarianceCommentModel.market,
                TopVarianceCommentModel.week,
                TopVarianceCommentModel.brand,
                TopVarianceCommentModel.site,
                TopVarianceCommentModel.sku_code,
                TopVarianceCommentModel.po_number,
            ],
            set_={
                TopVarianceCommentModel.comment: query.excluded.comment,
                TopVarianceCommentModel.last_edited_by: query.excluded.last_edited_by,
            },
        ).returning(1)
    )
