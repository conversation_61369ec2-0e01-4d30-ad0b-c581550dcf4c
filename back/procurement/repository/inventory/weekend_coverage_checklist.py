from collections.abc import Iterable, Sequence
from datetime import datetime
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select

from procurement.constants.hellofresh_constant import DayOfWeek
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.inventory.wcc import (
    ChecklistCount,
    WeekendCoverageChecklistFullItem,
    WeekendCoverageChecklistItem,
)
from procurement.data.models.inventory.weekend_coverage_checklist import ChecklistStatus, WeekendCoverageChecklistModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.ordering.shipment import POShipmentModel
from procurement.data.models.social.user import UserModel
from procurement.services.database import app_db


def get_checklist_preview_counts(
    weeks: tuple[ScmWeek, ...], brand: str = None, sites: Iterable[str] = None, sku_code: str = None
) -> list[ChecklistCount]:
    market = context.get_request_context().market
    fields = [
        WeekendCoverageChecklistModel.brand,
        WeekendCoverageChecklistModel.site,
        WeekendCoverageChecklistModel.po_number,
        WeekendCoverageChecklistModel.sku_code,
    ]
    query = (
        sqla.select(*fields, sqla.func.count(WeekendCoverageChecklistModel.id).label("count"))
        .where(
            WeekendCoverageChecklistModel.week.in_(list(map(int, weeks))),
            WeekendCoverageChecklistModel.market == market,
        )
        .group_by(*fields)
    )
    if brand is not None:
        query = query.where(WeekendCoverageChecklistModel.brand == brand)
    if sites is not None:
        query = query.where(WeekendCoverageChecklistModel.site.in_(sites))
    if sku_code is not None:
        query = query.where(WeekendCoverageChecklistModel.sku_code == sku_code)
    return [
        ChecklistCount(
            brand=item.brand,
            site=item.site,
            po_number=item.po_number,
            sku_code=item.sku_code,
            count=item.count,
        )
        for item in app_db.apply_query(query)
    ]


def get_checklist_by_week(week: ScmWeek) -> list[WeekendCoverageChecklistFullItem]:
    return [
        _build_checklist_item(item)
        for item in app_db.select_all(
            _get_base_checklist_query().where(WeekendCoverageChecklistModel.week == week.to_number_format())
        )
    ]


def get_checklist_by_po_sku(po_number: str, sku_code: str) -> list[WeekendCoverageChecklistFullItem]:
    return [
        _build_checklist_item(item)
        for item in app_db.select_all(
            _get_base_checklist_query().where(
                WeekendCoverageChecklistModel.po_number == po_number, WeekendCoverageChecklistModel.sku_code == sku_code
            )
        )
    ]


def _build_checklist_item(item: Row) -> WeekendCoverageChecklistFullItem:
    return WeekendCoverageChecklistFullItem(
        po_number=item.po_number,
        sku_name=item.sku_name,
        po_landing_day=DayOfWeek(item.po_landing_day) if item.po_landing_day else None,
        production_day_affected=DayOfWeek(item.production_day_affected) if item.production_day_affected else None,
        to_check=item.to_check,
        contact_name_vendor_carrier=item.contact_name_vendor_carrier,
        email_phone=item.email_phone,
        back_up_vendor=item.back_up_vendor,
        id=item.id,
        brand=item.brand,
        site=item.site,
        sku_code=item.sku_code,
        updated_by=item.updated_by_email,
        status=ChecklistStatus(item.status) if item.status else None,
        comment=item.comment,
        carrier_name=item.carrier_name,
        shipping_method=item.shipping_method,
        fob_pick_up_date=item.fob_pick_up_date,
        week=ScmWeek.from_number(item.week),
    )


def _get_base_checklist_query() -> Select:
    market = context.get_request_context().market
    return (
        sqla.select(
            WeekendCoverageChecklistModel.id,
            WeekendCoverageChecklistModel.brand,
            WeekendCoverageChecklistModel.site,
            WeekendCoverageChecklistModel.po_number,
            WeekendCoverageChecklistModel.sku_code,
            WeekendCoverageChecklistModel.po_landing_day,
            WeekendCoverageChecklistModel.production_day_affected,
            WeekendCoverageChecklistModel.to_check,
            WeekendCoverageChecklistModel.contact_name_vendor_carrier,
            WeekendCoverageChecklistModel.email_phone,
            WeekendCoverageChecklistModel.back_up_vendor,
            WeekendCoverageChecklistModel.status,
            WeekendCoverageChecklistModel.comment,
            WeekendCoverageChecklistModel.fob_pick_up_date,
            WeekendCoverageChecklistModel.week,
            CulinarySkuModel.sku_name,
            UserModel.email.label("updated_by_email"),
            PurchaseOrderModel.shipping_method,
            POShipmentModel.carrier_name,
        )
        .join(UserModel, onclause=(WeekendCoverageChecklistModel.updated_by_id == UserModel.id))
        .join(PurchaseOrderModel, onclause=(WeekendCoverageChecklistModel.po_number == PurchaseOrderModel.po_number))
        .join(PurchaseOrderSkuModel)
        .join(
            CulinarySkuModel,
            onclause=(
                (PurchaseOrderSkuModel.sku_uuid == CulinarySkuModel.sku_uuid)
                & (WeekendCoverageChecklistModel.sku_code == CulinarySkuModel.sku_code)
            ),
        )
        .join(
            POShipmentModel,
            isouter=True,
            onclause=(WeekendCoverageChecklistModel.po_number == POShipmentModel.po_number),
        )
        .where(WeekendCoverageChecklistModel.market == market, ~PurchaseOrderModel.deleted, PurchaseOrderModel.is_sent)
    )


def insert_checklist(rows: Sequence[dict[ColumnElement, Any]]) -> list[Row[int]]:
    return app_db.apply_query(
        sqla.insert(WeekendCoverageChecklistModel).values(rows).returning(WeekendCoverageChecklistModel.id)
    )


def update_checklist_item(checklist_id: int, parameter_name: str, value: Any, user_id: int) -> None:
    app_db.apply_query(
        sqla.update(WeekendCoverageChecklistModel)
        .where(WeekendCoverageChecklistModel.id == checklist_id)
        .values(
            {
                getattr(WeekendCoverageChecklistModel, parameter_name): value,
                WeekendCoverageChecklistModel.updated_by_id: user_id,
                WeekendCoverageChecklistModel.last_updated: datetime.now(),
            }
        )
    )


def delete_checklist_item(checklist_id: int) -> None:
    app_db.apply_query(
        sqla.delete(WeekendCoverageChecklistModel).where(WeekendCoverageChecklistModel.id == checklist_id)
    )


def get_checklist_by_id(checklist_id: int) -> WeekendCoverageChecklistItem | None:
    item = app_db.select_one(
        sqla.select(
            WeekendCoverageChecklistModel.brand,
            WeekendCoverageChecklistModel.po_number,
            WeekendCoverageChecklistModel.week,
            WeekendCoverageChecklistModel.site,
            WeekendCoverageChecklistModel.sku_code,
        ).where(WeekendCoverageChecklistModel.id == checklist_id)
    )
    return (
        WeekendCoverageChecklistItem(
            brand=item.brand,
            site=item.site,
            week=ScmWeek.from_number(item.week),
            po_number=item.po_number,
            sku_code=item.sku_code,
        )
        if item
        else None
    )
