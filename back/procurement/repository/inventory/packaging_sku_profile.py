from typing import Iterable

import sqlalchemy as sqla
from sqlalchemy.dialects.postgresql import insert

from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.sku import SkuProfile
from procurement.data.models.inventory.packaging_sku_profile import PackagingSkuProfileModel
from procurement.services.database import app_db


def upsert_packaging_sku_profile(sku_profiles: list[dict]):
    if not sku_profiles:
        return

    query = insert(PackagingSkuProfileModel).values(sku_profiles)
    query = query.on_conflict_do_update(
        index_elements=PackagingSkuProfileModel.get_primary_key_columns(),
        set_=PackagingSkuProfileModel.get_excluded_columns(query),
    )
    app_db.apply_query(query)


def get_packaging_sku_profile(sku_codes: Iterable[SKU_CODE]) -> dict[SKU_CODE, SkuProfile]:
    market = context.get_request_context().market
    res = app_db.select_all(
        sqla.select(
            PackagingSkuProfileModel.sku_code,
            PackagingSkuProfileModel.size,
            PackagingSkuProfileModel.type,
            PackagingSkuProfileModel.profile,
        ).where(
            PackagingSkuProfileModel.sku_code.in_(sku_codes),
            PackagingSkuProfileModel.market == market,
        )
    )
    return {
        profile.sku_code: SkuProfile(
            size=profile.size,
            type=profile.type,
            profile=profile.profile,
        )
        for profile in res
    }
