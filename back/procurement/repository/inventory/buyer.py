from collections.abc import Collection

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row

from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.buyer_sku import Buyer, BuyerSkuInfo, BuyerSkus
from procurement.data.models.inventory.buyer_sku import BuyerSkuModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.social.user import UserModel
from procurement.repository import repository_utils as repo_utils
from procurement.services.database import app_db


def get_buyer_names() -> list[Buyer]:
    market = context.get_request_context().market
    subq = sqla.select(BuyerSkuModel.user_id).where(BuyerSkuModel.market == market).distinct().subquery()
    query = sqla.select(UserModel.id, UserModel.first_name, UserModel.last_name).join(
        subq, onclause=(UserModel.id == subq.c.user_id)
    )
    return [
        Buyer(id=item.id, first_name=item.first_name, last_name=item.last_name) for item in app_db.select_all(query)
    ]


def get_buyers(brand: str = None, site: str = None, sku_codes: Collection[SKU_CODE] = None) -> list[BuyerSkus]:
    market = context.get_request_context().market
    name_column = _full_name_column()
    query = (
        sqla.select(name_column, _sku_codes_column())
        .join(UserModel, onclause=(UserModel.id == BuyerSkuModel.user_id))
        .where(BuyerSkuModel.market == market)
        .group_by(name_column)
    )

    if brand:
        query = query.where(BuyerSkuModel.brand == brand)
    if site:
        query = query.where(BuyerSkuModel.site == site)
    if sku_codes:
        query = query.where(BuyerSkuModel.sku_code.in_(sku_codes))

    return _build_buyer_skus(app_db.select_all(query))


def _build_buyer_skus(items: list[Row]) -> list[BuyerSkus]:
    return [BuyerSkus(full_name=item.full_name, sku_codes=item.sku_codes) for item in items]


def get_buyers_by_sku_site(
    sites: Collection[str], brand: str = None, sku_codes: set[str] | None = None
) -> list[BuyerSkus]:
    market = context.get_request_context().market
    query = (
        sqla.select(_full_name_column(), BuyerSkuModel.site, _sku_codes_column())
        .join(UserModel, onclause=(UserModel.id == BuyerSkuModel.user_id))
        .where(BuyerSkuModel.site.in_(sites), BuyerSkuModel.market == market)
        .group_by(_full_name_column(), BuyerSkuModel.site)
    )
    if brand:
        query = query.where(BuyerSkuModel.brand == brand)
    if sku_codes is not None:
        query = query.where(BuyerSkuModel.sku_code.in_(sku_codes))

    return [
        BuyerSkus(full_name=item.full_name, site=item.site, sku_codes=item.sku_codes)
        for item in app_db.select_all(query)
    ]


def _full_name_column() -> ColumnElement:
    return sqla.func.concat(UserModel.first_name, " ", UserModel.last_name).label("full_name")


def _sku_codes_column() -> ColumnElement:
    return sqla.func.array_agg(BuyerSkuModel.sku_code).label("sku_codes")


def get_buyers_skus(buyer_id: int) -> list[BuyerSkuInfo]:
    market = context.get_request_context().market
    query = (
        sqla.select(BuyerSkuModel.site, BuyerSkuModel.brand, BuyerSkuModel.sku_code, CulinarySkuModel.sku_id)
        .join(
            CulinarySkuModel,
            isouter=True,
            onclause=((BuyerSkuModel.sku_code == CulinarySkuModel.sku_code) & (CulinarySkuModel.market == market)),
        )
        .where(BuyerSkuModel.user_id == buyer_id, BuyerSkuModel.market == market)
    )
    return [
        BuyerSkuInfo(site=item.site, brand=item.brand, sku_code=item.sku_code, sku_id=item.sku_id)
        for item in app_db.select_all(query)
    ]


def update_data(data: list[dict]) -> None:
    market = context.get_request_context().market
    app_db.apply_query(sqla.delete(BuyerSkuModel).where(BuyerSkuModel.market == market))
    repo_utils.safe_bulk_insert_sqla(
        BuyerSkuModel,
        data=data,
        keys=[BuyerSkuModel.sku_code, BuyerSkuModel.site, BuyerSkuModel.brand, BuyerSkuModel.market],
    )


def is_user_buyer(user_id: int) -> bool:
    return bool(app_db.select_scalar(sqla.select(repo_utils.make_value(1)).where(BuyerSkuModel.user_id == user_id)))
