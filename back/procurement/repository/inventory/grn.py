from collections.abc import Collection, Iterable
from datetime import datetime
from typing import Any, Type

import sqlalchemy as sqla
import sqlalchemy.dialects.postgresql as psql
from sqlalchemy import ColumnElement, Select, Subquery

from procurement.constants.hellofresh_constant import MARKET_US, GrnSource, SkuStatus
from procurement.constants.protobuf import GrnDeliveryLineState
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BOB_CODE, PO_SKU_KEY, SKU_CODE, SKU_NAME, PoSku
from procurement.data.dto.inventory.grn import GrnInput, GrnPo
from procurement.data.dto.inventory.receipt_override import ReceiptData
from procurement.data.models.inventory.grn import GoodsReceiptNoteModel, LegacyHjGoodsReceiptNoteModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.data.models.ordering.purchase_order import PurchaseOrderModel, PurchaseOrderSkuModel
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.managers.pimt import partners
from procurement.services.database import app_db


def upsert_grn(data: Collection[GrnInput], site_by_bob_code: Iterable[BOB_CODE]) -> None:
    updates = [
        {
            GoodsReceiptNoteModel.bob_code: (
                i.bob_code if i.bob_code in site_by_bob_code else utils.get_bob_code_from_po_number(i.po_number)
            ),
            GoodsReceiptNoteModel.po_number: i.po_number,
            GoodsReceiptNoteModel.order_number: i.order_number,
            GoodsReceiptNoteModel.sku_code: i.sku_code,
            GoodsReceiptNoteModel.week: int(i.week),
            GoodsReceiptNoteModel.units_received: i.units_received,
            GoodsReceiptNoteModel.cases_received: i.cases_received or (None if i.units_received else 0),
            GoodsReceiptNoteModel.receipt_time_est: i.receipt_time_est,
            GoodsReceiptNoteModel.status: i.status,
            GoodsReceiptNoteModel.unit: i.unit.short_name if i.unit else None,
            GoodsReceiptNoteModel.update_ts: i.update_ts,
            GoodsReceiptNoteModel.source: i.source,
        }
        for i in data
        if i.status >= GrnDeliveryLineState.OPEN or i.units_received
    ]
    _update_receipts(GoodsReceiptNoteModel, updates, {(i.po_number, i.sku_code) for i in data})


def upsert_legacy_grn_hj(data: Iterable[GrnInput], site_by_bob_code: dict[BOB_CODE, DcConfig]) -> None:
    updates = _map_to_legacy_format(
        [i for i in data if i.status >= GrnDeliveryLineState.OPEN or i.units_received], site_by_bob_code
    )
    _update_receipts(LegacyHjGoodsReceiptNoteModel, updates, {(i.po_number, i.sku_code) for i in data})


def _update_receipts(
    model: Type[GoodsReceiptNoteModel | LegacyHjGoodsReceiptNoteModel], updates: list[dict], all_keys: set[PO_SKU_KEY]
) -> None:
    with app_db.transaction() as trns:
        app_db.apply_query(
            sqla.delete(model).where(sqla.tuple_(model.po_number, model.sku_code).in_(all_keys)),
            transaction=trns,
        )
        if updates:
            query = psql.insert(model).values(updates)
            app_db.apply_query(
                query.on_conflict_do_update(
                    index_elements=model.get_primary_key_columns(),
                    set_=model.get_excluded_columns(query),
                ),
                transaction=trns,
            )


_GRN_LEGACY_STATUS_MAP = {
    GrnDeliveryLineState.RECEIVED: "Unloading",
    GrnDeliveryLineState.OPEN: "Unloading",
    GrnDeliveryLineState.REJECTED: "Rejected",
    GrnDeliveryLineState.CLOSED: "Closed",
    GrnDeliveryLineState.CANCELLED: "Cancelled",
}


def _map_to_legacy_format(data: Collection[GrnInput], site_by_bob_code: dict[BOB_CODE, DcConfig]) -> list[dict]:
    sku_names = _get_sku_names_map(data)
    pos = _get_legacy_po_data_map(data)
    res = []
    for grn_row in data:
        po_number = grn_row.po_number
        sku_code = grn_row.sku_code
        po = pos.get((po_number, sku_code))
        week = grn_row.week
        site = site_by_bob_code.get(grn_row.bob_code) or site_by_bob_code.get(
            utils.get_bob_code_from_po_number(grn_row.po_number)
        )
        case_size = po.case_size if po else 0
        cases_received = grn_row.cases_received or (grn_row.units_received / case_size if case_size else 0)
        res.append(
            {
                LegacyHjGoodsReceiptNoteModel.wh_id: site.high_jump_name if site else None,
                LegacyHjGoodsReceiptNoteModel.bob_code: grn_row.bob_code,
                LegacyHjGoodsReceiptNoteModel.supplier_name: po.supplier if po else None,
                LegacyHjGoodsReceiptNoteModel.po_number: po_number,
                LegacyHjGoodsReceiptNoteModel.cases_received: cases_received,
                LegacyHjGoodsReceiptNoteModel.quantity_received: grn_row.units_received,
                LegacyHjGoodsReceiptNoteModel.sku_code: sku_code,
                LegacyHjGoodsReceiptNoteModel.sku_name: sku_names.get(sku_code),
                LegacyHjGoodsReceiptNoteModel.scm_week_raw: str(int(week)),
                LegacyHjGoodsReceiptNoteModel.status: _GRN_LEGACY_STATUS_MAP.get(grn_row.status, str(grn_row.status)),
                LegacyHjGoodsReceiptNoteModel.receipt_time_est: grn_row.receipt_time_est,
                LegacyHjGoodsReceiptNoteModel.unit: grn_row.unit.short_name if grn_row.unit else None,
                LegacyHjGoodsReceiptNoteModel.update_ts: grn_row.update_ts,
                LegacyHjGoodsReceiptNoteModel.market: site.market,
            }
        )
    return res


def _get_sku_names_map(data: Iterable[GrnInput]) -> dict[SKU_CODE, SKU_NAME]:
    sku_codes = {d.sku_code for d in data}
    query = sqla.select(CulinarySkuModel.sku_code, CulinarySkuModel.sku_name).where(
        CulinarySkuModel.sku_code.in_(sku_codes)
    )
    return {sku.sku_code: sku.sku_name for sku in app_db.select_all(query)}


def _get_legacy_po_data_map(data: Iterable[GrnInput]) -> dict[PO_SKU_KEY, Any]:
    po_numbers = {d.po_number for d in data}
    query = (
        sqla.select(
            PurchaseOrderModel.po_number,
            CulinarySkuModel.sku_code,
            SupplierModel.name.label("supplier"),
            PurchaseOrderSkuModel.case_size,
        )
        .select_from(PurchaseOrderModel)
        .join(PurchaseOrderSkuModel)
        .join(CulinarySkuModel, onclause=(CulinarySkuModel.sku_uuid == PurchaseOrderSkuModel.sku_uuid))
        .join(
            SupplierModel,
            onclause=(PurchaseOrderModel.supplier_code == SupplierModel.code) & (SupplierModel.market == MARKET_US),
        )
        .where(PurchaseOrderModel.po_number.in_(po_numbers))
    )
    return {(po.po_number, po.sku_code): po for po in app_db.select_all(query)}


def get_grn_receive_data_by_week_bob_code_and_sku_code(
    weeks: Iterable[int], dc_objects: Iterable[DcConfig], sku_codes: set[str] | None = None
) -> list[GrnPo]:
    where_condition = [
        GoodsReceiptNoteModel.week.in_(weeks),
        GoodsReceiptNoteModel.bob_code_source_tuple().in_(
            ((dc_object.bob_code, dc_object.receiving_type.grn_source_name) for dc_object in dc_objects)
        ),
    ]
    if sku_codes:
        where_condition.append(GoodsReceiptNoteModel.sku_code.in_(sku_codes))
    query = sqla.select(
        GoodsReceiptNoteModel.po_number,
        GoodsReceiptNoteModel.order_number,
        GoodsReceiptNoteModel.sku_code,
        GoodsReceiptNoteModel.receipt_time_est,
        GoodsReceiptNoteModel.units_received,
        GoodsReceiptNoteModel.cases_received,
    ).where(*where_condition)
    return [
        GrnPo(
            order_number=item.order_number,
            sku_code=item.sku_code,
            receipt_time_est=item.receipt_time_est,
            units_received=item.units_received,
            cases_received=item.cases_received,
            po_number=item.po_number,
        )
        for item in app_db.select_all(query)
    ]


def get_grn_receive_data_by_receive_date_and_bob_code(
    receive_date_from: datetime, receive_date_to: datetime, dc_object: DcConfig, weeks: list[ScmWeek] = None
) -> list[GrnPo]:
    query = sqla.select(
        GoodsReceiptNoteModel.order_number,
        GoodsReceiptNoteModel.sku_code,
        GoodsReceiptNoteModel.receipt_time_est,
        GoodsReceiptNoteModel.units_received,
        GoodsReceiptNoteModel.cases_received,
    ).where(
        GoodsReceiptNoteModel.receipt_time_est >= receive_date_from,
        GoodsReceiptNoteModel.receipt_time_est < receive_date_to,
        GoodsReceiptNoteModel.bob_code == dc_object.bob_code,
        GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name,
    )
    if weeks is not None:
        query = query.where(GoodsReceiptNoteModel.week.in_(list(map(int, weeks))))
    grns = app_db.select_all(query)
    return [
        GrnPo(
            order_number=grn.order_number,
            sku_code=grn.sku_code,
            receipt_time_est=grn.receipt_time_est,
            units_received=grn.units_received,
            cases_received=grn.cases_received,
        )
        for grn in grns
    ]


def get_grn_received_weeks_by_receive_date(
    dc_object: DcConfig, date_from: datetime, date_to: datetime, weeks: list[ScmWeek] = None
) -> list[ScmWeek]:
    query = (
        sqla.select(GoodsReceiptNoteModel.week)
        .distinct()
        .where(
            GoodsReceiptNoteModel.bob_code == dc_object.bob_code,
            GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name,
            GoodsReceiptNoteModel.receipt_time_est >= date_from,
            GoodsReceiptNoteModel.receipt_time_est < date_to,
        )
    )
    if weeks is not None:
        query = query.where(GoodsReceiptNoteModel.week.in_(list(map(int, weeks))))
    return [ScmWeek.from_number(r.week) for r in app_db.select_all(query)]


def get_grn_received_items_by_receive_date_sqla(
    dc_object: DcConfig,
    date_from: datetime,
    date_to: datetime,
    fields: Collection[ColumnElement],
    weeks: list[ScmWeek] = None,
) -> Select:
    query = (
        sqla.select(*fields)
        .distinct()
        .where(
            GoodsReceiptNoteModel.bob_code == dc_object.bob_code,
            GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name,
            GoodsReceiptNoteModel.receipt_time_est >= date_from,
            GoodsReceiptNoteModel.receipt_time_est < date_to,
        )
    )
    if weeks is not None:
        query = query.where(GoodsReceiptNoteModel.week.in_(list(map(int, weeks))))
    return query


def get_grn_by_po_sku(order_number: str, sku_code: str, dc_object: DcConfig) -> list[GrnPo]:
    grns = app_db.select_all(
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.receipt_time_est,
            GoodsReceiptNoteModel.units_received,
            GoodsReceiptNoteModel.cases_received,
        ).where(
            GoodsReceiptNoteModel.order_number == order_number,
            GoodsReceiptNoteModel.sku_code == sku_code,
            GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name,
        )
    )
    return [
        GrnPo(
            order_number=grn.order_number,
            sku_code=grn.sku_code,
            receipt_time_est=grn.receipt_time_est,
            units_received=grn.units_received,
            cases_received=grn.cases_received,
        )
        for grn in grns
    ]


def get_grn_by_po_sku_query(dc_object: DcConfig, po_sku_filtering_query: Select) -> list[GrnPo]:
    po_query = po_sku_filtering_query.subquery()
    query = (
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.receipt_time_est,
            GoodsReceiptNoteModel.units_received,
            GoodsReceiptNoteModel.cases_received,
        )
        .select_from(po_query)
        .join(
            GoodsReceiptNoteModel,
            onclause=(
                (po_query.c.order_number == GoodsReceiptNoteModel.order_number)
                & (po_query.c.sku_code == GoodsReceiptNoteModel.sku_code)
                & (GoodsReceiptNoteModel.bob_code == dc_object.bob_code)
                & (GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name)
            ),
        )
    )
    return [
        GrnPo(
            order_number=grn.order_number,
            sku_code=grn.sku_code,
            receipt_time_est=grn.receipt_time_est,
            units_received=grn.units_received,
            cases_received=grn.cases_received,
        )
        for grn in app_db.select_all(query)
    ]


def get_unique_pimt_po_sku_grn_receipts(po_filter_query: Subquery) -> Subquery:
    receving_type_filter = [
        (wh.bob_code, wh.receiving_type.grn_source_name)
        for wh in partners.get_all_partners(e2open_grn=True)
        if wh.receiving_type.is_grn
    ]
    return (
        sqla.select(GoodsReceiptNoteModel.order_number, GoodsReceiptNoteModel.sku_code)
        .distinct()
        .join(po_filter_query, onclause=(GoodsReceiptNoteModel.order_number == po_filter_query.c.order_number))
        .where(GoodsReceiptNoteModel.status == GrnDeliveryLineState.CLOSED)
        .where(GoodsReceiptNoteModel.bob_code_source_tuple().in_(receving_type_filter))
        .subquery()
    )


def get_grn_receipt_skus(week: ScmWeek, po_number: str, dc_object: DcConfig, exclude_skus: list[str]) -> list[GrnPo]:
    market = context.get_request_context().market
    query = (
        sqla.select(
            GoodsReceiptNoteModel.order_number,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.receipt_time_est,
            GoodsReceiptNoteModel.units_received,
            GoodsReceiptNoteModel.cases_received,
            CulinarySkuModel.sku_name,
        )
        .distinct()
        .join(CulinarySkuModel, onclause=(GoodsReceiptNoteModel.sku_code == CulinarySkuModel.sku_code))
        .join(PurchaseOrderModel, onclause=(GoodsReceiptNoteModel.po_number == PurchaseOrderModel.po_number))
        .where(
            GoodsReceiptNoteModel.week == int(week),
            GoodsReceiptNoteModel.bob_code == dc_object.bob_code,
            GoodsReceiptNoteModel.source == dc_object.receiving_type.grn_source_name,
            GoodsReceiptNoteModel.po_number == po_number,
            GoodsReceiptNoteModel.sku_code.not_in(exclude_skus),
            CulinarySkuModel.market == market,
            CulinarySkuModel.status != SkuStatus.ARCHIVED,
        )
    )
    return [
        GrnPo(
            order_number=item.order_number,
            sku_code=item.sku_code,
            receipt_time_est=item.receipt_time_est,
            units_received=item.units_received,
            cases_received=item.cases_received,
            sku_name=item.sku_name,
            po_number=po_number,
        )
        for item in app_db.select_all(query)
    ]


def get_grn_receipts(week: ScmWeek, sites: list[DcConfig]) -> list[ReceiptData]:
    query = (
        sqla.select(
            GoodsReceiptNoteModel.bob_code,
            GoodsReceiptNoteModel.sku_code,
            GoodsReceiptNoteModel.po_number,
            GoodsReceiptNoteModel.units_received,
            GoodsReceiptNoteModel.cases_received,
            CulinarySkuModel.sku_name,
            PurchaseOrderModel.supplier,
        )
        .join(CulinarySkuModel, onclause=(GoodsReceiptNoteModel.sku_code == CulinarySkuModel.sku_code))
        .join(PurchaseOrderModel, onclause=(GoodsReceiptNoteModel.po_number == PurchaseOrderModel.po_number))
        .where(
            GoodsReceiptNoteModel.week == int(week),
            GoodsReceiptNoteModel.bob_code_source_tuple().in_(
                (site.bob_code, site.receiving_type.grn_source_name) for site in sites
            ),
            CulinarySkuModel.market == context.get_request_context().market,
        )
    )
    return [
        ReceiptData(
            bob_code=item.bob_code,
            sku_code=item.sku_code,
            sku_name=item.sku_name,
            po_number=item.po_number,
            supplier_name=item.supplier,
            quantity_received=item.units_received,
            cases_received=item.cases_received,
        )
        for item in app_db.select_all(query)
    ]


def get_received_keys(
    weeks: Iterable[ScmWeek],
    selectors: Iterable[tuple[BOB_CODE, GrnSource]],
    sku_codes: set[str] | None = None,
) -> set[PoSku]:
    query = (
        sqla.select(
            GoodsReceiptNoteModel.po_number,
            GoodsReceiptNoteModel.sku_code,
        )
        .distinct()
        .where(
            GoodsReceiptNoteModel.week.in_(map(int, weeks)),
            GoodsReceiptNoteModel.bob_code_source_tuple().in_(selectors),
            GoodsReceiptNoteModel.status == GrnDeliveryLineState.CLOSED,
        )
    )
    if sku_codes is not None:
        query = query.where(GoodsReceiptNoteModel.sku_code.in_(sku_codes))
    return {PoSku(row.po_number, row.sku_code) for row in app_db.select_all(query)}
