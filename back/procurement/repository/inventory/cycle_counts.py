from collections.abc import Iterable
from datetime import date

import sqlalchemy as sqla
from sqlalchemy import Column, Date, Select
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.cycle_counts import CycleCount, CycleCountInput
from procurement.data.dto.inventory.inventory import Inventory
from procurement.data.models.inventory.cycle_counts import CycleCountsModel
from procurement.services.database import app_db


def upsert_cycle_counts(cycle_counts: list[CycleCountInput]):
    if not cycle_counts:
        return
    query = psql_sqla.insert(CycleCountsModel)
    query = query.on_conflict_do_update(
        index_elements=[
            CycleCountsModel.cycle_count_day,
            CycleCountsModel.site,
            CycleCountsModel.sku_code,
            CycleCountsModel.brand,
            CycleCountsModel.scm_week,
        ],
        set_={CycleCountsModel.units: query.excluded.units},
    ).values(
        [
            {
                CycleCountsModel.cycle_count_day: cycle_count_item.cycle_count_day,
                CycleCountsModel.site: cycle_count_item.site,
                CycleCountsModel.sku_code: cycle_count_item.sku_code,
                CycleCountsModel.brand: cycle_count_item.brand,
                CycleCountsModel.units: cycle_count_item.units,
                CycleCountsModel.scm_week: int(cycle_count_item.scm_week),
                CycleCountsModel.date_of_count: cycle_count_item.date_for_comparison,
            }
            for cycle_count_item in cycle_counts
        ]
    )
    app_db.apply_query(query)


def get_cycle_counts(brand: str, site: str, date_from: date, date_to: date) -> list[CycleCount]:
    query = (
        sqla.select(
            CycleCountsModel.sku_code,
            CycleCountsModel.cycle_count_day,
            CycleCountsModel.units,
            _week_order(CycleCountsModel.cycle_count_day).label("rn"),
        )
        .where(
            CycleCountsModel.brand == brand,
            CycleCountsModel.site == site,
            CycleCountsModel.cycle_count_day >= date_from,
            CycleCountsModel.cycle_count_day <= date_to,
        )
        .subquery()
    )
    query = sqla.select(query).where(query.c.rn == 1)
    return [
        CycleCount(
            sku_code=cycle_count_item.sku_code,
            cycle_count_day=cycle_count_item.cycle_count_day,
            units=cycle_count_item.units,
        )
        for cycle_count_item in app_db.select_all(query)
    ]


def get_bod_cycle_counts_inventory(brand: str, site: str) -> list[Inventory]:
    return _get_latest_inventory(brand, site, CycleCountsModel.cycle_count_day)


def get_current_cycle_count_inventory(brand: str, site: str, sku_codes: Iterable[str] | None = None) -> list[Inventory]:
    return _get_latest_inventory(brand, site, CycleCountsModel.date_of_count, sku_codes)


def _get_max_date_query(brand: str, site: str, snapshot_date_column: Column) -> Select:
    return sqla.select(sqla.func.max(snapshot_date_column)).where(
        CycleCountsModel.brand == brand, CycleCountsModel.site == site
    )


def get_latest_snapshot_date(brand: str, site: str) -> date:
    return app_db.select_scalar(_get_max_date_query(brand, site, CycleCountsModel.cycle_count_day))


def _get_latest_inventory(
    brand: str, site: str, snapshot_date_column: Column, sku_codes: Iterable[str] | None = None
) -> list[Inventory]:
    query = sqla.select(
        CycleCountsModel.sku_code,
        sqla.func.cast(snapshot_date_column, Date).label("snapshot_day"),
        CycleCountsModel.units,
        _week_order(snapshot_date_column).label("rn"),
    ).where(
        CycleCountsModel.brand == brand,
        CycleCountsModel.site == site,
        _get_max_date_query(brand, site, snapshot_date_column).scalar_subquery() == snapshot_date_column,
    )

    if sku_codes:
        query = query.where(CycleCountsModel.sku_code.in_(sku_codes))

    query = query.subquery()
    query = sqla.select(query).where(query.c.rn == 1)
    return [
        Inventory(
            sku_code=item.sku_code,
            snapshot_timestamp=item.snapshot_day,
            quantity=item.units,
            expiration_date=None,
            license_count=None,
        )
        for item in app_db.select_all(query)
    ]


def _week_order(snapshot_date_column: Column) -> Column:
    return sqla.func.ROW_NUMBER().over(
        order_by=CycleCountsModel.scm_week.desc(),
        partition_by=(snapshot_date_column, CycleCountsModel.sku_code),
    )


def delete_cycle_counts_by_week(brand: str, site: str, week: ScmWeek):
    app_db.apply_query(
        sqla.delete(CycleCountsModel).where(
            CycleCountsModel.brand == brand,
            CycleCountsModel.site == site,
            CycleCountsModel.scm_week == int(week),
        )
    )
