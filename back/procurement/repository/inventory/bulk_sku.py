from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla
from sqlalchemy.orm import aliased

from procurement.constants.hellofresh_constant import ALL_BRANDS
from procurement.core.request_utils import context
from procurement.core.typing import BRAND, BULK_SKU_CODE, PACKAGED_SKU_CODE
from procurement.data.dto.inventory.bulk_skus import BulkSku, BulkSkuWithNames
from procurement.data.models.inventory.bulk_skus import BulkSkusModel, MasterBulkSkuModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.services.database import app_db


def get_bulk_skus(packaged_sku_codes: Iterable[PACKAGED_SKU_CODE] = None, brand: BRAND = None) -> Iterable[BulkSku]:
    query = sqla.select(
        BulkSkusModel.packaged_sku_code,
        BulkSkusModel.bulk_sku_code,
        MasterBulkSkuModel.bulk_sku_code.label("master_bulk_sku_code"),
        BulkSkusModel.pick_conversion,
        BulkSkusModel.brands,
    ).join(MasterBulkSkuModel, onclause=(BulkSkusModel.bulk_sku_code == MasterBulkSkuModel.bulk_sku_code), isouter=True)
    if packaged_sku_codes:
        query = query.where(BulkSkusModel.packaged_sku_code.in_(packaged_sku_codes))
    if brand:
        query = query.where(BulkSkusModel.brands.contains([brand]) | BulkSkusModel.brands.contains([ALL_BRANDS]))
    return [
        BulkSku(
            packaged_sku_code=item.packaged_sku_code,
            bulk_sku_code=item.bulk_sku_code,
            is_master_sku=bool(item.master_bulk_sku_code),
            pick_conversion=item.pick_conversion,
            brands=item.brands,
        )
        for item in app_db.select_all(query)
    ]


def get_bulk_skus_with_names() -> Iterable[BulkSkuWithNames]:
    packaged_sku = aliased(CulinarySkuModel)
    bulk_sku = aliased(CulinarySkuModel)
    market = context.get_request_context().market
    query = (
        (
            sqla.select(
                BulkSkusModel.packaged_sku_code,
                BulkSkusModel.bulk_sku_code,
                BulkSkusModel.pick_conversion,
                BulkSkusModel.brands,
                MasterBulkSkuModel.bulk_sku_code.label("master_bulk_sku_code"),
                packaged_sku.sku_name.label("packaged_sku_name"),
                bulk_sku.sku_name.label("bulk_sku_name"),
            )
            .where(packaged_sku.market == market, bulk_sku.market == market)
            .join(packaged_sku, onclause=(packaged_sku.sku_code == BulkSkusModel.packaged_sku_code), isouter=True)
        )
        .join(bulk_sku, onclause=(bulk_sku.sku_code == BulkSkusModel.bulk_sku_code), isouter=True)
        .join(MasterBulkSkuModel, onclause=(bulk_sku.sku_code == MasterBulkSkuModel.bulk_sku_code), isouter=True)
        .order_by(packaged_sku.sku_name)
    )
    return [
        BulkSkuWithNames(
            packaged_sku_code=item.packaged_sku_code,
            bulk_sku_code=item.bulk_sku_code,
            is_master_sku=bool(item.master_bulk_sku_code),
            pick_conversion=item.pick_conversion,
            brands=item.brands,
            bulk_sku_name=item.bulk_sku_name,
            packaged_sku_name=item.packaged_sku_name,
        )
        for item in app_db.select_all(query)
    ]


def upsert_bulk_skus(bulk_sku: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(BulkSkusModel)
    query = query.on_conflict_do_update(
        index_elements=BulkSkusModel.get_primary_key_columns(),
        set_={
            BulkSkusModel.brands: query.excluded.brands,
            BulkSkusModel.pick_conversion: query.excluded.pick_conversion,
        },
    ).values(bulk_sku)
    app_db.apply_query(query)


def insert_bulk_master_sku(bulk_master_sku: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(MasterBulkSkuModel)
    query = query.on_conflict_do_nothing(
        index_elements=MasterBulkSkuModel.get_primary_key_columns(),
    ).values(bulk_master_sku)
    app_db.apply_query(query)


def delete_bulk_sku(packaged_sku_code: PACKAGED_SKU_CODE, bulk_sku_code: BULK_SKU_CODE) -> None:
    app_db.apply_query(
        sqla.delete(BulkSkusModel).where(
            BulkSkusModel.packaged_sku_code == packaged_sku_code, BulkSkusModel.bulk_sku_code == bulk_sku_code
        )
    )
    existing_bulk_sku = app_db.select_one(
        sqla.select(BulkSkusModel).where(BulkSkusModel.bulk_sku_code == bulk_sku_code)
    )
    if not existing_bulk_sku:
        delete_bulk_master_sku(bulk_sku_code)


def delete_bulk_master_sku(bulk_sku_code: BULK_SKU_CODE):
    app_db.apply_query(sqla.delete(MasterBulkSkuModel).where(MasterBulkSkuModel.bulk_sku_code == bulk_sku_code))
