from collections.abc import Collection

import sqlalchemy as sqla

from procurement.data.dto.inventory.demand_pipeline import DemandPipelineSkuMapping
from procurement.data.models.forecast.demand_pipeline_sku_mapping import DemandPipelineSkuMappingModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def update_demand_pipeline_sku_mapping(data: Collection[dict]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=DemandPipelineSkuMappingModel,
        data=data,
        keys=[DemandPipelineSkuMappingModel.sku_code, DemandPipelineSkuMappingModel.market],
        preserve_fields=[DemandPipelineSkuMappingModel.sku_type, DemandPipelineSkuMappingModel.demand_pipeline],
    )


def get_packaging_demand_pipeline_sku_mapping(market: str) -> list[DemandPipelineSkuMapping]:
    query = sqla.select(
        DemandPipelineSkuMappingModel.sku_code,
        DemandPipelineSkuMappingModel.demand_pipeline,
        DemandPipelineSkuMappingModel.sku_type,
    ).where(DemandPipelineSkuMappingModel.market == market)
    return [
        DemandPipelineSkuMapping(sku_code=item.sku_code, demand_pipeline=item.demand_pipeline, sku_type=item.sku_type)
        for item in app_db.select_all(query)
    ]
