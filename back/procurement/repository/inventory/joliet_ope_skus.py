from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.data.models.inventory.joliet_ope_skus import JolietOpeSkuModel
from procurement.services.database import app_db


def insert_joliet_ope_skus(data: list[dict[ColumnElement, Any]]) -> None:
    app_db.apply_query(sqla.insert(JolietOpeSkuModel).values(data))


def delete_joliet_ope_skus() -> None:
    app_db.apply_query(sqla.delete(JolietOpeSkuModel))


def get_joliet_ope_skus() -> set[str]:
    return {item.sku_code for item in app_db.select_all(sqla.select(JolietOpeSkuModel.sku_code))}
