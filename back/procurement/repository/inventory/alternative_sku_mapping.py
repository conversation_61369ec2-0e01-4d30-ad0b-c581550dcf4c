from collections.abc import Collection
from typing import Type

import sqlalchemy as sqla

from procurement.core.request_utils import context
from procurement.data.dto.sku import AlternativeSkuMapping
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel, PackagingSkuModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def get_alternative_sku_mapping(
    model: Type[PackagingSkuModel] | Type[OrganicSkuModel],
) -> tuple[AlternativeSkuMapping, ...]:
    market = context.get_request_context().market
    return tuple(
        AlternativeSkuMapping(row.interchangable_sku_code, row.original_sku_code)
        for row in app_db.select_all(sqla.select(model).where(model.market == market))
    )


def delete_alternative_sku_mapping(
    mapping: Collection[AlternativeSkuMapping], model: Type[PackagingSkuModel] | Type[OrganicSkuModel]
):
    market = context.get_request_context().market
    mapping_pkeys = [(item.original_sku_code, item.interchangable_sku_code, market) for item in mapping]
    pkey = sqla.tuple_(model.original_sku_code, model.interchangable_sku_code, model.market)
    app_db.apply_query(sqla.delete(model).where(pkey.in_(mapping_pkeys)))


def add_alternative_sku_mapping(
    mapping: Collection[AlternativeSkuMapping], model: Type[PackagingSkuModel] | Type[OrganicSkuModel]
):
    market = context.get_request_context().market
    repository_utils.safe_bulk_insert_sqla(
        model=model,
        data=[
            {
                model.original_sku_code: item.original_sku_code,
                model.interchangable_sku_code: item.interchangable_sku_code,
                model.market: market,
            }
            for item in mapping
        ],
        keys=[model.original_sku_code, model.interchangable_sku_code, model.market],
    )
