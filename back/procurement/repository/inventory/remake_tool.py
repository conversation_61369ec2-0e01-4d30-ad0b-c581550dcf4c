from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.remake_tool import RemakeTool
from procurement.data.models.inventory.remake_tool import RemakeToolModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def get_remake_tool_data(week: ScmWeek, site: str, meals: frozenset[str]) -> list[RemakeTool]:
    query = sqla.select(
        RemakeToolModel.meal, RemakeToolModel.picks_2p, RemakeToolModel.picks_4p, RemakeToolModel.picks_6p
    ).where(RemakeToolModel.dc == site, RemakeToolModel.week == str(week), RemakeToolModel.meal.in_(meals))

    return [
        RemakeTool(meal=item.meal, picks_2p=item.picks_2p, picks_4p=item.picks_4p, picks_6p=item.picks_6p)
        for item in app_db.select_all(query)
    ]


def upsert_remake_tool_data(upsert_rt: list[dict[ColumnElement, Any]]) -> None:
    repository_utils.safe_bulk_insert_sqla(
        model=RemakeToolModel,
        data=upsert_rt,
        keys=[RemakeToolModel.meal, RemakeToolModel.week, RemakeToolModel.dc],
        preserve_fields=[RemakeToolModel.picks_2p, RemakeToolModel.picks_4p, RemakeToolModel.picks_6p],
    )
