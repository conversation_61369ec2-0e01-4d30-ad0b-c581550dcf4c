from typing import Any, <PERSON><PERSON><PERSON>le

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.data.dto.inventory.vendor_managed_inventory import VendorManagedInventory
from procurement.data.models.inventory.vendor_managed_inventory import VendorManagedInventoryModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


class _VmiKey(NamedTuple):
    sku_code: str
    region: str
    supplier_name: str


def upsert_vendor_managed_inventory(data: list[dict[ColumnElement, Any]]):
    _delete_removed_records(data)
    repository_utils.safe_bulk_insert_sqla(
        model=VendorManagedInventoryModel,
        data=data,
        keys=[
            VendorManagedInventoryModel.sku_code,
            VendorManagedInventoryModel.region,
            VendorManagedInventoryModel.supplier_name,
            VendorManagedInventoryModel.market,
        ],
        preserve_fields=[
            VendorManagedInventoryModel.units,
            VendorManagedInventoryModel.inbound,
            VendorManagedInventoryModel.outbound,
        ],
    )


def get_vendor_managed_inventory(market: str, packaging_regions: set[str]) -> list[VendorManagedInventory]:
    query = sqla.select(
        VendorManagedInventoryModel.region,
        VendorManagedInventoryModel.sku_code,
        VendorManagedInventoryModel.supplier_name,
        VendorManagedInventoryModel.units,
        VendorManagedInventoryModel.inbound,
        VendorManagedInventoryModel.outbound,
    ).where(
        VendorManagedInventoryModel.market == market,
        VendorManagedInventoryModel.region.in_(packaging_regions),
    )

    return [
        VendorManagedInventory(
            region=item.region,
            sku_code=item.sku_code,
            supplier_name=item.supplier_name,
            units=round(item.units or 0),
            inbound=round(item.inbound or 0),
            outbound=round(item.outbound or 0),
        )
        for item in app_db.select_all(query)
    ]


def _delete_removed_records(updates: list[dict[ColumnElement, Any]]) -> None:
    market = updates[0][VendorManagedInventoryModel.market]
    regions = {it[VendorManagedInventoryModel.region] for it in updates}
    existing_keys = _get_vmi_keys(market, regions)
    updated_keys = {
        _VmiKey(
            sku_code=it[VendorManagedInventoryModel.sku_code],
            region=it[VendorManagedInventoryModel.region],
            supplier_name=it[VendorManagedInventoryModel.supplier_name],
        )
        for it in updates
    }
    keys_to_delete = existing_keys - updated_keys
    if keys_to_delete:
        query = sqla.delete(VendorManagedInventoryModel).where(
            sqla.tuple_(
                VendorManagedInventoryModel.sku_code,
                VendorManagedInventoryModel.region,
                VendorManagedInventoryModel.supplier_name,
            ).in_(keys_to_delete),
            VendorManagedInventoryModel.market == market,
        )
        app_db.apply_query(query)


def _get_vmi_keys(market: str, regions: set[str]) -> set[_VmiKey]:
    query = sqla.select(
        VendorManagedInventoryModel.sku_code,
        VendorManagedInventoryModel.region,
        VendorManagedInventoryModel.supplier_name,
    ).where(
        VendorManagedInventoryModel.market == market,
        VendorManagedInventoryModel.region.in_(regions),
    )
    return {
        _VmiKey(
            sku_code=r.sku_code,
            region=r.region,
            supplier_name=r.supplier_name,
        )
        for r in app_db.select_all(query)
    }
