from collections.abc import Iterable
from datetime import date, datetime, timedelta

import sqlalchemy as sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.inventory.discarding import Discard
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.discard import DiscardModel
from procurement.services.database import app_db


def add_discard_data(discard_data: list[dict]):
    app_db.apply_query(sqla.insert(DiscardModel).values(discard_data))


def get_discards(
    week: ScmWeek, brand: str | None = None, site: str | None = None, sku_code: str | None = None
) -> list[Discard]:
    market = context.get_request_context().market
    filters = [DiscardModel.market == market, DiscardModel.week == str(week), DiscardModel.deleted_ts.is_(None)]
    if site:
        filters.append(DiscardModel.dc == site)
    if brand:
        filters.append(DiscardModel.brand == brand)
    if sku_code:
        filters.append(DiscardModel.sku == sku_code)
    query = sqla.select(DiscardModel).where(*filters)
    return [_build_discard_dto(record) for record in app_db.select_all(query)]


def get_discards_by_change_date(change_date: date) -> Iterable[Discard]:
    query = sqla.select(DiscardModel).where(
        DiscardModel.timestamp < change_date + timedelta(days=1),
        DiscardModel.timestamp >= change_date,
        DiscardModel.deleted_ts.is_(None),
    )
    return [_build_discard_dto(record) for record in app_db.select_all(query)]


def delete_imported_discard_data(week: ScmWeek, site: str) -> None:
    query = sqla.delete(DiscardModel).where(
        DiscardModel.week == str(week), DiscardModel.source == DataSource.GSHEET, DiscardModel.dc == site
    )
    app_db.apply_query(query)


def update_discard_item(discard_id: int, params: dict, user_email: str) -> None:
    params[DiscardModel.timestamp] = datetime.now()
    params[DiscardModel.updated_by] = user_email
    query = sqla.update(DiscardModel).where(DiscardModel.id == discard_id).values(params)
    app_db.apply_query(query)


def mark_discard_as_deleted(discard_id: int, deleted_by: str, deleted_ts: datetime):
    app_db.apply_query(
        sqla.update(DiscardModel)
        .where(DiscardModel.id == discard_id)
        .values({DiscardModel.deleted_by: deleted_by, DiscardModel.deleted_ts: deleted_ts})
    )


def get_discard_by_id(discard_id: int) -> Discard | None:
    entry = app_db.select_one(sqla.select(DiscardModel).where(DiscardModel.id == discard_id))
    return _build_discard_dto(entry) if entry else None


def _build_discard_dto(discard_item: DiscardModel) -> Discard:
    return Discard(
        id=discard_item.id,
        user=discard_item.user,
        timestamp=discard_item.timestamp,
        dc=discard_item.dc,
        sku=discard_item.sku,
        discarded_datetime=discard_item.discarded_datetime,
        quantity=discard_item.quantity,
        quality_instructions=discard_item.quality_instructions,
        reason=discard_item.reason,
        source=discard_item.source,
        week=discard_item.week,
        brand=discard_item.brand,
        comment=discard_item.comment,
        updated_by=discard_item.updated_by,
        deleted_by=discard_item.deleted_by,
        deleted_ts=discard_item.deleted_ts,
        market=discard_item.market,
    )
