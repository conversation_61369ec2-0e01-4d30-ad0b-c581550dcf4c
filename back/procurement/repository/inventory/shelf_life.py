from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.dto.inventory.shelf_life import ShelfLife
from procurement.data.models.inventory.shelf_life import ShelfLifeModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.services.database import app_db


def upsert_shelf_life(culinary_sku: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(ShelfLifeModel).values(culinary_sku)

    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=ShelfLifeModel.get_primary_key_columns(),
            set_=ShelfLifeModel.get_excluded_columns(query),
        )
    )


def get_shelf_life(sku_codes: list[str]) -> list[ShelfLife]:
    query = (
        sqla.select(
            ShelfLifeModel.shelf_life,
            CulinarySkuModel.sku_code.label("sku_code"),
        )
        .join(CulinarySkuModel, onclause=(ShelfLifeModel.sku_uuid == CulinarySkuModel.sku_uuid))
        .where(CulinarySkuModel.sku_code.in_(sku_codes))
    )
    return [ShelfLife(sku_code=item.sku_code, shelf_life_value=item.shelf_life) for item in app_db.select_all(query)]
