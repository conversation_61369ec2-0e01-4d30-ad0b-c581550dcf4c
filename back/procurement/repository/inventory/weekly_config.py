from collections import defaultdict
from collections.abc import Iterable
from datetime import datetime
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Connection
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek, ScmWeekConfig
from procurement.core.request_utils import context
from procurement.core.typing import BRAND, MARKET, SITE
from procurement.data.dto.config import Brand
from procurement.data.dto.inventory.site import Site
from procurement.data.dto.inventory.weekly_config import WeeklyConfig, WeeklyConfigIdWeek
from procurement.data.models.inventory.brand import BrandConfigModel, BrandModel
from procurement.data.models.inventory.site import SiteModel
from procurement.data.models.inventory.weekly_config import WeeklyConfigModel
from procurement.services.database import app_db


def get_latest_configs_up_to_week(
    week: ScmWeek, brand: str, only_enabled: bool, market: str = None
) -> list[WeeklyConfig]:
    market = market or context.get_request_context().market
    subquery = (
        sqla.select(
            sqla.func.max(WeeklyConfigModel.week),
            WeeklyConfigModel.site,
            WeeklyConfigModel.brand,
            WeeklyConfigModel.market,
        )
        .where(
            WeeklyConfigModel.week <= week.to_number_format(),
            WeeklyConfigModel.brand == brand,
            WeeklyConfigModel.market == market,
        )
        .group_by(WeeklyConfigModel.site, WeeklyConfigModel.brand, WeeklyConfigModel.market)
    )

    subquery_match_fields = sqla.tuple_(
        WeeklyConfigModel.week, WeeklyConfigModel.site, WeeklyConfigModel.brand, WeeklyConfigModel.market
    )
    query = (
        sqla.select(
            WeeklyConfigModel.id,
            WeeklyConfigModel.brand,
            WeeklyConfigModel.site,
            SiteModel.name.label("site_name"),
            SiteModel.sequence_number,
            WeeklyConfigModel.week,
            WeeklyConfigModel.enabled,
            WeeklyConfigModel.properties,
            SiteModel.timezone,
            SiteModel.dc_azure_role,
            WeeklyConfigModel.market,
        )
        .join(
            SiteModel,
            onclause=sqla.and_(
                WeeklyConfigModel.site == SiteModel.id,
                WeeklyConfigModel.brand == SiteModel.brand,
                WeeklyConfigModel.market == SiteModel.market,
            ),
        )
        .where(subquery_match_fields.in_(subquery))
        .order_by(SiteModel.sequence_number)
    )

    if only_enabled:
        query = query.where(WeeklyConfigModel.enabled)

    return [
        WeeklyConfig(
            id=item.id,
            brand=item.brand,
            site=item.site,
            site_name=item.site_name,
            sequence_number=item.sequence_number,
            week=ScmWeek.from_number(item.week),
            enabled=item.enabled,
            properties=item.properties,
            timezone=item.timezone,
            dc_azure_role=item.dc_azure_role,
            market=item.market,
        )
        for item in app_db.select_all(query)
    ]


def get_all_configs(week: ScmWeek, brand: BRAND, only_enabled=True, market: str = None) -> dict[str, WeeklyConfig]:
    return {
        config.site: config
        for config in get_latest_configs_up_to_week(week=week, brand=brand, only_enabled=only_enabled, market=market)
    }


def delete_configs(brand: str, site: str) -> None:
    market = context.get_request_context().market
    app_db.apply_query(
        sqla.delete(WeeklyConfigModel).where(
            WeeklyConfigModel.brand == brand, WeeklyConfigModel.site == site, WeeklyConfigModel.market == market
        )
    )

    delete_site(brand, site)


def delete_site(brand: str, site: str) -> None:
    app_db.apply_query(
        sqla.delete(SiteModel).where(
            SiteModel.brand == brand, SiteModel.id == site, SiteModel.market == context.get_request_context().market
        )
    )


def update_sequence(new_order: dict[SITE, str | int]):
    now = datetime.now().astimezone()

    app_db.apply_query(
        sqla.update(SiteModel)
        .where(SiteModel.market == context.get_request_context().market, SiteModel.id.in_(new_order.keys()))
        .values(
            {
                SiteModel.sequence_number: sqla.case(new_order, value=SiteModel.id),
                SiteModel.updated_tmst: now,
                SiteModel.user_id: context.get_request_context().user_info.user_id,
            }
        )
    )


def get_next_configs(week: ScmWeek, brand: str, site: str) -> list[WeeklyConfigIdWeek]:
    query = sqla.select(WeeklyConfigModel.week, WeeklyConfigModel.id).where(
        WeeklyConfigModel.brand == brand,
        WeeklyConfigModel.site == site,
        WeeklyConfigModel.week > int(week),
        WeeklyConfigModel.market == context.get_request_context().market,
    )
    return [WeeklyConfigIdWeek(id=item.id, week=ScmWeek.from_number(item.week)) for item in app_db.select_all(query)]


def bulk_delete_configs(config_ids: Iterable[int]) -> None:
    app_db.apply_query(sqla.delete(WeeklyConfigModel).where(WeeklyConfigModel.id.in_(config_ids)))


def get_brands() -> dict[MARKET, dict[ScmWeek, dict[BRAND, Brand]]]:
    """
    Returns full brand config for each week when any brand was changed in descending order (newer week -> older week).
    E.g. result
    {
        2022-W42: {"HF": "Hello Fresh", "EP": "Not Every Plate", "GC": "Green Chef"},
        ### EP name was changed
        ^
        |
        2022-W40: {"HF": "Hello Fresh", "EP": "Every Plate", "GC": "Green Chef"},
        ### new brand was added
        ^
        |
        2020-W32: {"HF": "Hello Fresh", "EP": "Every Plate"},
    }
    """
    all_brands = _select_brand_configs()
    if not all_brands:
        return {}
    brands_by_week = defaultdict(lambda: defaultdict(dict))
    for brand in all_brands:
        brands_by_week[brand.market][brand.config_week][brand.code] = brand

    res = {}
    for market, configs in brands_by_week.items():
        _fill_brand_week_configs(configs)
        ordered_brands = [
            (week, dict(sorted(brands.items(), key=lambda k: k[1].order))) for week, brands in configs.items()
        ]
        res[market] = dict(reversed(ordered_brands))
    return res


def get_all_brands_ordered(market: str) -> tuple[BRAND, ...]:
    return tuple(
        row.id
        for row in app_db.select_all(
            sqla.select(BrandModel.id).where(BrandModel.market == market).order_by(BrandModel.order)
        )
    )


def _fill_brand_week_configs(brands_by_week: dict[ScmWeek, dict[BRAND, Brand]]):
    """
    Propagates brand configs to be available on any future week (not only on week they were created)
    """
    by_week_iter = iter(brands_by_week.values())
    prev_brands = next(by_week_iter)
    for brands in by_week_iter:
        brands.update(entry for entry in prev_brands.items() if entry[0] not in brands)
        prev_brands = brands


def _select_brand_configs() -> list[Brand]:
    query = (
        sqla.select(
            BrandModel.id,
            BrandModel.name,
            BrandModel.market,
            BrandModel.order,
            BrandModel.consolidated,
            BrandModel.week_calendar_mon_shift,
            BrandModel.week_length,
            BrandModel.country_code,
            BrandConfigModel.week,
            BrandConfigModel.enabled,
        )
        .join(
            BrandConfigModel,
            onclause=((BrandConfigModel.brand == BrandModel.id) & (BrandConfigModel.market == BrandModel.market)),
        )
        .order_by(BrandConfigModel.week)
    )
    return [
        Brand(
            code=brand.id,
            name=brand.name,
            market=brand.market,
            enabled=brand.enabled,
            consolidated=brand.consolidated,
            order=brand.order,
            scm_week_config=ScmWeekConfig(calendar_mon_shift=brand.week_calendar_mon_shift, length=brand.week_length),
            config_week=ScmWeek.from_number(brand.week),
            country_code=brand.country_code,
        )
        for brand in app_db.select_all(query)
    ]


def delete_brand_config(week: ScmWeek, brand: str):
    app_db.apply_query(
        sqla.delete(BrandConfigModel).where(
            BrandConfigModel.brand == brand,
            BrandConfigModel.market == context.get_request_context().market,
            BrandConfigModel.week == int(week),
        )
    )


def upsert_brand_config(brand: Brand):
    with app_db.transaction() as transaction:
        upsert_brand(brand, transaction)
        query = psql_sqla.insert(BrandConfigModel)
        query = query.on_conflict_do_update(
            index_elements=[BrandConfigModel.brand, BrandConfigModel.market, BrandConfigModel.week],
            set_={BrandConfigModel.enabled: query.excluded.enabled},
        ).values(
            {
                BrandConfigModel.brand: brand.code,
                BrandConfigModel.market: brand.market,
                BrandConfigModel.week: int(brand.config_week),
                BrandConfigModel.enabled: brand.enabled,
            }
        )
        app_db.apply_query(query, transaction=transaction)


def upsert_brand(brand: Brand, transaction: Connection = None) -> None:
    query = psql_sqla.insert(BrandModel)
    query = query.on_conflict_do_update(
        index_elements=[BrandModel.id, BrandModel.market],
        set_={
            BrandModel.name: query.excluded.name,
            BrandModel.consolidated: query.excluded.consolidated,
            BrandModel.week_calendar_mon_shift: query.excluded.week_calendar_mon_shift,
            BrandModel.week_length: query.excluded.week_length,
            BrandModel.country_code: query.excluded.country_code,
        },
    ).values(
        {
            BrandModel.id: brand.code,
            BrandModel.market: brand.market,
            BrandModel.name: brand.name,
            BrandModel.consolidated: brand.consolidated,
            BrandModel.week_calendar_mon_shift: brand.scm_week_config.calendar_mon_shift,
            BrandModel.week_length: brand.scm_week_config.length,
            BrandModel.country_code: brand.country_code,
        }
    )
    app_db.apply_query(query, transaction=transaction)


def update_brands_order(brands: Iterable[BRAND]):
    new_orders = {brand: order for order, brand in enumerate(brands, start=1)}
    market = context.get_request_context().market
    app_db.apply_query(
        sqla.update(BrandModel)
        .where(BrandModel.market == market)
        .values({BrandModel.order: sqla.case(new_orders, value=BrandModel.id)})
    )


def get_all_sites() -> dict[BRAND, dict[SITE, Site]]:
    res = defaultdict(dict)

    for item in app_db.select_all(sqla.select(SiteModel.id, SiteModel.brand, SiteModel.dc_azure_role)):
        res[item.brand][item.id] = Site(id=item.id, brand=item.brand, dc_azure_role=item.dc_azure_role)
    return res


def upsert_site(data: dict[ColumnElement, Any], transaction: Connection) -> None:
    query = psql_sqla.insert(SiteModel).values(data)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=SiteModel.get_primary_key_columns(),
            set_={
                SiteModel.name: query.excluded.name,
                SiteModel.timezone: query.excluded.timezone,
                SiteModel.dc_azure_role: query.excluded.dc_azure_role,
            },
        ),
        transaction=transaction,
    )


def get_max_site_sequence() -> int:
    return app_db.select_scalar(sqla.select(sqla.func.coalesce(sqla.func.max(SiteModel.sequence_number), 0)))


def upsert_weekly_config(weekly_config: dict[ColumnElement, Any], transaction: Connection) -> None:
    query = psql_sqla.insert(WeeklyConfigModel).values(weekly_config)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[
                WeeklyConfigModel.site,
                WeeklyConfigModel.brand,
                WeeklyConfigModel.week,
                WeeklyConfigModel.market,
            ],
            set_={
                WeeklyConfigModel.properties: query.excluded.properties,
                WeeklyConfigModel.enabled: query.excluded.enabled,
                WeeklyConfigModel.user_id: query.excluded.user_id,
            },
        ),
        transaction=transaction,
    )
