from collections.abc import Iterable
from datetime import datetime
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Connection, Row, Select, Tuple

from procurement.core.dates import ScmWeek
from procurement.core.typing import PoSku
from procurement.data.dto.inventory.receiving import ReceivingItem
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.receive import ReceivingModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.repository.inventory.context import TimeRangeContext
from procurement.services.database import app_db


def get_received_weeks(site: str, receive_date_from: datetime, receive_date_to: datetime) -> set[ScmWeek]:
    query = sqla.select(ReceivingModel.week.distinct()).where(
        ReceivingModel.receive_timestamp >= receive_date_from,
        ReceivingModel.receive_timestamp < receive_date_to,
        ReceivingModel.dc == site,
    )
    return {ScmWeek.from_str(week) for week in {item.week for item in app_db.select_all(query)}}


def get_received_by_date(
    dc_object: DcConfig, receive_date_from: datetime, receive_date_to: datetime
) -> list[ReceivingItem]:
    query = _base_receiving_query().where(
        ReceivingModel.receive_timestamp >= receive_date_from,
        ReceivingModel.receive_timestamp < receive_date_to,
        ReceivingModel.dc == dc_object.sheet_name,
        ReceivingModel.brand == dc_object.brand,
    )
    return [_build_receiving_item(item) for item in app_db.select_all(query)]


def get_po_sku_filtered_receives(po_skus: Select) -> list[ReceivingItem]:
    po_query = po_skus.subquery()
    query = _base_receiving_query().join(
        po_query,
        onclause=(ReceivingModel.po == po_query.c.po_number) & (ReceivingModel.sku_code == po_query.c.sku_code),
    )
    return [_build_receiving_item(item) for item in app_db.select_all(query)]


def get_received_by_po_sku(po_number: str, sku_code: str) -> list[ReceivingItem]:
    query = _base_receiving_query().where(ReceivingModel.po == po_number, ReceivingModel.sku_code == sku_code)
    return [_build_receiving_item(item) for item in app_db.select_all(query)]


def _base_receiving_query() -> Select:
    return sqla.select(
        ReceivingModel.po,
        ReceivingModel.sku_code,
        ReceivingModel.receive_timestamp,
        ReceivingModel.case_count_one_total_units,
        ReceivingModel.case_count_two_total_units,
        ReceivingModel.case_count_three_total_units,
        ReceivingModel.case_size_one,
        ReceivingModel.case_size_two,
        ReceivingModel.case_size_three,
        ReceivingModel.total_cases_received,
    )


def _build_receiving_item(item: Row) -> ReceivingItem:
    return ReceivingItem(
        po_number=item.po,
        sku_code=item.sku_code,
        receive_timestamp=item.receive_timestamp,
        case_count_one_total_units=item.case_count_one_total_units,
        case_count_two_total_units=item.case_count_two_total_units,
        case_count_three_total_units=item.case_count_three_total_units,
        case_size_one=item.case_size_one,
        case_size_two=item.case_size_two,
        case_size_three=item.case_size_three,
        total_cases_received=item.total_cases_received,
    )


def query_received(
    fields: Iterable[ColumnElement], site: str, time_range_context: TimeRangeContext, weeks: list[ScmWeek] = None
) -> Select:
    """
    Returns distinct query for filtering by receiving date. Should be used only in repositories.
    """
    query = (
        sqla.select(*fields)
        .distinct()
        .where(
            ReceivingModel.receive_timestamp >= time_range_context.date_from,
            ReceivingModel.receive_timestamp < time_range_context.date_to,
            ReceivingModel.dc == site,
        )
    )
    if weeks is not None:
        query = query.where(ReceivingModel.week.in_(list(map(str, weeks))))
    return query


def get_received_keys(
    weeks: Iterable[ScmWeek], sites: Iterable[DcConfig], sku_codes: set[str] | None = None
) -> set[PoSku]:
    query = (
        sqla.select(ReceivingModel.po, ReceivingModel.sku_code)
        .distinct()
        .where(
            ReceivingModel.week.in_(map(str, weeks)),
            Tuple(ReceivingModel.brand, ReceivingModel.dc).in_((s.brand, s.sheet_name) for s in sites),
        )
    )
    if sku_codes is not None:
        query = query.where(ReceivingModel.sku_code.in_(sku_codes))
    return {PoSku(row.po, row.sku_code) for row in app_db.select_all(query)}


def get_received_data(week: ScmWeek, brand: str, site: str, sku_codes: Iterable[str] = None) -> list[ReceivingItem]:
    query = _base_receiving_query().where(
        ReceivingModel.week == str(week), ReceivingModel.dc == site.upper(), ReceivingModel.brand == brand.upper()
    )

    if sku_codes is not None:
        if not sku_codes:
            return []
        query = query.where(ReceivingModel.sku_code.in_(sku_codes))

    return [_build_receiving_item(item) for item in app_db.select_all(query)]


def add_receiving_data(data: list[dict[ColumnElement, Any]], transaction: Connection) -> None:
    app_db.apply_query(sqla.insert(ReceivingModel).values(data), transaction=transaction)


def remove_imported_receiving_data(week: ScmWeek, sites: list[str], transaction: Connection) -> None:
    query = sqla.delete(ReceivingModel).where(
        ReceivingModel.week == str(week), ReceivingModel.source == DataSource.GSHEET.name, ReceivingModel.dc.in_(sites)
    )
    app_db.apply_query(query, transaction=transaction)
