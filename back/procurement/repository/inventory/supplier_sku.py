import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.dto.sku import SupplierSkuRecord
from procurement.data.models.inventory.supplier_sku import SupplierSkuModel
from procurement.services.database import app_db


def update_supplier_sku(supplier_sku: SupplierSkuRecord):
    existing = app_db.select_one(
        sqla.select(SupplierSkuModel.last_updated).where(
            SupplierSkuModel.supplier_sku_uuid == supplier_sku.supplier_sku_uuid
        )
    )
    if existing and existing.last_updated <= supplier_sku.last_updated:
        return
    upsert_supplier_sku(supplier_sku)


def upsert_supplier_sku(supplier_sku: SupplierSkuRecord):
    query = psql_sqla.insert(SupplierSkuModel).values(
        {
            SupplierSkuModel.supplier_sku_uuid: supplier_sku.supplier_sku_uuid,
            SupplierSkuModel.culinary_sku_uuid: supplier_sku.culinary_sku_uuid,
            SupplierSkuModel.last_updated: supplier_sku.last_updated,
        }
    )
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=[SupplierSkuModel.supplier_sku_uuid],
            set_={
                SupplierSkuModel.culinary_sku_uuid: query.excluded.culinary_sku_uuid,
                SupplierSkuModel.last_updated: query.excluded.last_updated,
            },
        )
    )
