from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.data.dto.procurement.gsheets import DocInfo, GsheetMeta
from procurement.data.models.social.gsheet import GsheetAdminModel, GsheetMetaModel
from procurement.services.database import app_db


def get_meta(
    doc_codes: str = None, brand: str = None, required: str = None, project: str = "imt", market: str = None
) -> list[GsheetMeta]:
    market = market or context.get_request_context().market
    entities = sqla.select(GsheetMetaModel).where(GsheetMetaModel.market == market)
    if project:
        entities = entities.where(GsheetMetaModel.project == project)
    if doc_codes:
        entities = entities.where(GsheetMetaModel.doc_code.in_(doc_codes))
    if brand:
        entities = entities.where(GsheetMetaModel.brand == brand)
    if required is not None:
        entities = entities.where(GsheetMetaModel.required == required)
    entities = entities.order_by(GsheetMetaModel.order)
    return [
        GsheetMeta(
            doc_code=gsheet_meta.doc_code,
            brand=gsheet_meta.brand,
            name=gsheet_meta.name,
            id=gsheet_meta.id,
            weekly=gsheet_meta.weekly,
            title_template=gsheet_meta.title_template,
            visible=gsheet_meta.visible,
        )
        for gsheet_meta in app_db.select_all(entities)
    ]


def get_meta_by_id(meta_id) -> GsheetMeta:
    gsheet_meta = app_db.select_one(sqla.select(GsheetMetaModel).where(GsheetMetaModel.id == meta_id))
    return GsheetMeta(
        doc_code=gsheet_meta.doc_code,
        brand=gsheet_meta.brand,
        name=gsheet_meta.name,
        id=gsheet_meta.id,
        weekly=gsheet_meta.weekly,
        title_template=gsheet_meta.title_template,
        visible=gsheet_meta.visible,
    )


def get_docs(
    scm_weeks: Iterable[ScmWeek] = None, doc_codes: Iterable[str] = None, brand=None, project: str | None = "imt"
) -> list[DocInfo]:
    entities = (
        sqla.select(GsheetAdminModel.gsheet_id, GsheetAdminModel.meta_id, GsheetAdminModel.scm_week)
        .join(GsheetMetaModel, onclause=(GsheetAdminModel.meta_id == GsheetMetaModel.id))
        .where(GsheetMetaModel.market == context.get_request_context().market)
    )
    if project:
        entities = entities.where(GsheetMetaModel.project == project)
    if doc_codes:
        entities = entities.where(GsheetMetaModel.doc_code.in_(list(doc_codes)))
    if scm_weeks:
        entities = entities.where(GsheetAdminModel.scm_week.in_([str(w) for w in scm_weeks]) | ~GsheetMetaModel.weekly)
    if brand:
        entities = entities.where(GsheetMetaModel.brand == brand.upper())
    entities = entities.order_by(GsheetMetaModel.order)
    return [
        DocInfo(gsheet_id=doc.gsheet_id, meta_id=doc.meta_id, scm_week=doc.scm_week)
        for doc in app_db.select_all(entities)
    ]


def get_gsheet_admin(meta_id: int, scm_week: ScmWeek) -> int:
    return app_db.select_scalar(
        sqla.select(GsheetAdminModel.id).where(
            (GsheetAdminModel.meta_id == meta_id),
            sqla.or_(GsheetAdminModel.scm_week == str(scm_week), GsheetAdminModel.scm_week.is_(None)),
        )
    )


def get_doc_name(doc_model) -> str | None:
    doc_name = app_db.select_scalar(
        sqla.select(GsheetMetaModel.name).where(
            GsheetMetaModel.doc_code == doc_model.doc_code,
            GsheetMetaModel.market == context.get_request_context().market,
        )
    )
    return doc_name


def get_gsheet_id(doc_code: str, scm_week: ScmWeek, brand: str) -> str | None:
    return app_db.select_scalar(
        sqla.select(GsheetAdminModel.gsheet_id)
        .join(GsheetMetaModel, onclause=(GsheetAdminModel.meta_id == GsheetMetaModel.id))
        .where(
            GsheetMetaModel.brand == brand.upper() if brand else True,
            sqla.or_(GsheetAdminModel.scm_week == str(scm_week), ~GsheetMetaModel.weekly),
            GsheetMetaModel.doc_code == doc_code,
            GsheetMetaModel.market == context.get_request_context().market,
        )
    )


def insert_gsheet_admin(meta_id=None, scm_week=None, gsheet_id=None, user_id=None):
    return app_db.apply_query(
        sqla.insert(GsheetAdminModel)
        .values(
            {
                GsheetAdminModel.meta_id: meta_id,
                GsheetAdminModel.scm_week: str(scm_week),
                GsheetAdminModel.gsheet_id: gsheet_id,
                GsheetAdminModel.user_id: user_id,
            }
        )
        .returning(GsheetAdminModel.id)
    )[0].id


def delete_gsheet_url(meta_id: int, scm_week: ScmWeek = None):
    week = str(scm_week) if scm_week else None
    return app_db.apply_query(
        sqla.delete(GsheetAdminModel).where(
            (GsheetAdminModel.meta_id == meta_id),
            sqla.or_(GsheetAdminModel.scm_week == week, GsheetAdminModel.scm_week.is_(None)),
        )
    )


def update_gsheet_admin(entity_id, values: dict[ColumnElement, Any]):
    app_db.apply_query(sqla.update(GsheetAdminModel).where(GsheetAdminModel.id == entity_id).values(values))
