from typing import Any

import sqlalchemy as sqla
import sqlalchemy.dialects.postgresql as psql
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.work_order import WorkOrder
from procurement.data.models.inventory.work_order import WorkOrderModel
from procurement.services.database import app_db


def upsert_work_orders(work_order: list[dict[ColumnElement, Any]]) -> None:
    query = psql.insert(WorkOrderModel).values(work_order)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=WorkOrderModel.get_primary_key_columns(),
            set_=WorkOrderModel.get_excluded_columns(query),
        )
    )


def get_work_orders(hj_name: str, week: ScmWeek) -> list[WorkOrder]:
    return [
        WorkOrder(sku_code=item.sku_code, quantity=item.quantity)
        for item in app_db.select_all(
            sqla.select(WorkOrderModel.sku_code, WorkOrderModel.quantity).where(
                WorkOrderModel.high_jump_warehouse_id == hj_name, WorkOrderModel.menu_week == int(week)
            )
        )
    ]
