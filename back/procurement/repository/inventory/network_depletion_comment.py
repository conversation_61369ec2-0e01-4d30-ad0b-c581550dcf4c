from datetime import datetime

import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import SKU_CODE
from procurement.data.dto.inventory.network_depletion_comment import NetworkDepletionCommentItem
from procurement.data.models.inventory.network_depletion_comments import NetworkDepletionCommentModel
from procurement.services.database import app_db


def get_network_depletion_comments_preview(resource_id: str, week: ScmWeek) -> list[SKU_CODE]:
    market = context.get_request_context().market
    query = sqla.select(NetworkDepletionCommentModel.sku_code).where(
        NetworkDepletionCommentModel.resource_id == resource_id,
        NetworkDepletionCommentModel.market == market,
        NetworkDepletionCommentModel.week == week.to_number_format(),
    )
    return [item.sku_code for item in app_db.select_all(query)]


def get_network_depletion_comment(resource_id: str, week: ScmWeek, sku_code: str) -> NetworkDepletionCommentItem:
    query = sqla.select(
        NetworkDepletionCommentModel.text,
        NetworkDepletionCommentModel.last_updated,
        NetworkDepletionCommentModel.updated_by,
    ).where(
        NetworkDepletionCommentModel.resource_id == resource_id,
        NetworkDepletionCommentModel.week == week.to_number_format(),
        NetworkDepletionCommentModel.market == context.get_request_context().market,
        NetworkDepletionCommentModel.sku_code == sku_code,
    )
    item = app_db.select_one(query)
    return NetworkDepletionCommentItem(text=item.text, last_updated=item.last_updated, updated_by=item.updated_by)


def upsert_network_depletion_comments(resource_id: str, week: ScmWeek, sku_code: str, text: str) -> None:
    query = psql_sqla.insert(NetworkDepletionCommentModel)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=NetworkDepletionCommentModel.get_primary_key_columns(),
            set_={NetworkDepletionCommentModel.text: query.excluded.text},
        ).values(
            {
                NetworkDepletionCommentModel.market: context.get_request_context().market,
                NetworkDepletionCommentModel.resource_id: resource_id,
                NetworkDepletionCommentModel.week: week.to_number_format(),
                NetworkDepletionCommentModel.sku_code: sku_code,
                NetworkDepletionCommentModel.text: text,
                NetworkDepletionCommentModel.updated_by: context.get_request_context().user_info.email,
                NetworkDepletionCommentModel.last_updated: datetime.now(),
            }
        )
    )


def delete_network_depletion_comments(resource_id: str, week: ScmWeek, sku_code: str) -> None:
    market = context.get_request_context().market
    query = sqla.delete(NetworkDepletionCommentModel).where(
        NetworkDepletionCommentModel.market == market,
        NetworkDepletionCommentModel.resource_id == resource_id,
        NetworkDepletionCommentModel.week == week.to_number_format(),
        NetworkDepletionCommentModel.sku_code == sku_code,
    )
    app_db.apply_query(query)
