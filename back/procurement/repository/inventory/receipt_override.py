from collections.abc import Collection
from datetime import date, datetime, timedelta
from typing import Any, NamedTuple

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row, Select

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BRAND, PO_NUMBER, SITE, SKU_CODE, PoSku
from procurement.data.dto.inventory.receipt_override import (
    ReceiptOverride,
    ReceiptOverrideExtended,
    ReceiptOverrideValidationItem,
)
from procurement.data.models.inventory.receipt_override import ReceiptOverrideModel
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.services.database import app_db


class ReceiptOverrideKey(NamedTuple):
    week: ScmWeek
    brand: BRAND
    site: SITE
    po_number: PO_NUMBER


def get_receipt_override_skus(key: ReceiptOverrideKey) -> list[SKU_CODE]:
    market = context.get_request_context().market
    query = ReceiptOverrideModel.base_select(ReceiptOverrideModel.sku_code.distinct()).where(
        ReceiptOverrideModel.week == str(key.week),
        ReceiptOverrideModel.brand == key.brand,
        ReceiptOverrideModel.dc == key.site,
        ReceiptOverrideModel.po_number == key.po_number,
        ReceiptOverrideModel.market == market,
    )
    return [item.sku_code for item in app_db.select_all(query)]


def get_receipt_override_by_po_sku_query(brand: str, po_skus: Select) -> list[ReceiptOverride]:
    market = context.get_request_context().market
    po_query = po_skus.subquery()
    query = (
        ReceiptOverrideModel.base_select(
            ReceiptOverrideModel.po_number,
            ReceiptOverrideModel.sku_code,
            ReceiptOverrideModel.qty,
            ReceiptOverrideModel.cases,
            ReceiptOverrideModel.receiving_date,
        )
        .join(
            po_query,
            onclause=(
                (ReceiptOverrideModel.po_number == po_query.c.po_number)
                & (ReceiptOverrideModel.sku_code == po_query.c.sku_code)
            ),
        )
        .where(ReceiptOverrideModel.market == market, ReceiptOverrideModel.brand == brand)
    )
    return [_build_receipt_override_item(item) for item in app_db.select_all(query)]


def get_receipt_override_by_po_sku(po_number: str, sku_code: str) -> list[ReceiptOverride]:
    market = context.get_request_context().market
    query = (
        ReceiptOverrideModel.base_select(
            ReceiptOverrideModel.qty, ReceiptOverrideModel.cases, ReceiptOverrideModel.receiving_date
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                CulinarySkuModel.sku_code == ReceiptOverrideModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(
            ReceiptOverrideModel.po_number == po_number,
            ReceiptOverrideModel.sku_code == sku_code,
            ReceiptOverrideModel.market == market,
        )
    )
    return [
        ReceiptOverride(
            po_number=po_number, sku_code=sku_code, qty=item.qty, cases=item.cases, receiving_date=item.receiving_date
        )
        for item in app_db.select_all(query)
    ]


def get_valid_receipt_override_keys(site: str, brand: str, weeks: Collection[ScmWeek]) -> set[PoSku]:
    market = context.get_request_context().market
    query = ReceiptOverrideModel.base_select(ReceiptOverrideModel.po_number, ReceiptOverrideModel.sku_code).where(
        ReceiptOverrideModel.week.in_(list(map(str, weeks))),
        ReceiptOverrideModel.dc == site,
        ReceiptOverrideModel.brand == brand,
        ReceiptOverrideModel.market == market,
        ReceiptOverrideModel.qty != 0,
    )
    return {PoSku(row.po_number, row.sku_code) for row in app_db.select_all(query)}


def get_receipt_override_by_week_dc_brand(
    week: ScmWeek, site: str, brand: str, sku_codes: Collection[str] = None
) -> list[ReceiptOverride]:
    market = context.get_request_context().market
    query = (
        ReceiptOverrideModel.base_select(
            ReceiptOverrideModel.po_number,
            ReceiptOverrideModel.sku_code,
            ReceiptOverrideModel.qty,
            ReceiptOverrideModel.cases,
            ReceiptOverrideModel.receiving_date,
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                CulinarySkuModel.sku_code == ReceiptOverrideModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(
            ReceiptOverrideModel.week == str(week).upper(),
            ReceiptOverrideModel.dc == site.upper(),
            ReceiptOverrideModel.brand == brand,
            ReceiptOverrideModel.market == market,
        )
    )

    if sku_codes:
        query = query.where(ReceiptOverrideModel.sku_code.in_(sku_codes))

    return [_build_receipt_override_item(item) for item in app_db.select_all(query)]


def _build_receipt_override_item(item: Row) -> ReceiptOverride:
    return ReceiptOverride(
        po_number=item.po_number,
        sku_code=item.sku_code,
        qty=item.qty,
        cases=item.cases,
        receiving_date=item.receiving_date,
    )


def get_extended_receipt_overrides_by_week(week: ScmWeek) -> list[ReceiptOverrideExtended]:
    market = context.get_request_context().market

    query = (
        ReceiptOverrideModel.base_select(
            ReceiptOverrideModel.brand,
            ReceiptOverrideModel.dc,
            ReceiptOverrideModel.po_number,
            ReceiptOverrideModel.sku_code,
            ReceiptOverrideModel.qty,
            ReceiptOverrideModel.cases,
            ReceiptOverrideModel.receiving_date,
            ReceiptOverrideModel.user,
            ReceiptOverrideModel.upd_tmst,
            ReceiptOverrideModel.comment,
            CulinarySkuModel.sku_name,
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                CulinarySkuModel.sku_code == ReceiptOverrideModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(ReceiptOverrideModel.week == str(week).upper(), ReceiptOverrideModel.market == market)
    )

    return [_build_receipt_override_extended(item) for item in app_db.select_all(query)]


def get_receipt_override_by_date(by_date: date) -> list[ReceiptOverrideExtended]:
    market = context.get_request_context().market
    query = (
        ReceiptOverrideModel.base_select(
            ReceiptOverrideModel.po_number,
            ReceiptOverrideModel.sku_code,
            CulinarySkuModel.sku_name,
            ReceiptOverrideModel.brand,
            ReceiptOverrideModel.dc,
            ReceiptOverrideModel.qty,
            ReceiptOverrideModel.cases,
            ReceiptOverrideModel.receiving_date,
            ReceiptOverrideModel.user,
            ReceiptOverrideModel.upd_tmst,
            ReceiptOverrideModel.comment,
        )
        .join(
            CulinarySkuModel,
            onclause=sqla.and_(
                ReceiptOverrideModel.sku_code == CulinarySkuModel.sku_code,
                CulinarySkuModel.market == market,
            ),
        )
        .where(
            ReceiptOverrideModel.market == market,
            ReceiptOverrideModel.upd_tmst >= by_date,
            ReceiptOverrideModel.upd_tmst < by_date + timedelta(days=1),
        )
    )

    return [_build_receipt_override_extended(item) for item in app_db.select_all(query)]


def _build_receipt_override_extended(item: Row) -> ReceiptOverrideExtended:
    return ReceiptOverrideExtended(
        po_number=item.po_number,
        sku_code=item.sku_code,
        sku_name=item.sku_name,
        brand=item.brand,
        site=item.dc,
        qty=item.qty,
        receiving_date=item.receiving_date,
        user=item.user,
        upd_tmst=item.upd_tmst,
        cases=item.cases,
        comment=item.comment,
    )


def get_receipt_override_items_for_validation(week: ScmWeek) -> list[ReceiptOverrideValidationItem]:
    market = context.get_request_context().market
    query = ReceiptOverrideModel.base_select(
        ReceiptOverrideModel.brand,
        ReceiptOverrideModel.dc,
        ReceiptOverrideModel.po_number,
        ReceiptOverrideModel.sku_code,
    ).where(ReceiptOverrideModel.week == str(week), ReceiptOverrideModel.market == market)
    return [
        ReceiptOverrideValidationItem(brand=item.brand, site=item.dc, po_number=item.po_number, sku_code=item.sku_code)
        for item in app_db.select_all(query)
    ]


def add_receipt_override_data(data) -> None:
    app_db.apply_query(sqla.insert(ReceiptOverrideModel).values(data))


def update_receipt_override_item(key: ReceiptOverrideKey, sku_code: str, updates: dict[ColumnElement, Any]) -> None:
    market = context.get_request_context().market
    app_db.apply_query(
        sqla.update(ReceiptOverrideModel)
        .where(
            ReceiptOverrideModel.week == str(key.week),
            ReceiptOverrideModel.brand == key.brand.upper(),
            ReceiptOverrideModel.dc == key.site,
            ReceiptOverrideModel.po_number == key.po_number,
            ReceiptOverrideModel.sku_code == sku_code,
            ReceiptOverrideModel.market == market,
        )
        .values(updates)
    )


def delete_receipt_override_by_po_sku(user_email: str, key: ReceiptOverrideKey, sku_code: str) -> None:
    market = context.get_request_context().market
    app_db.apply_query(
        sqla.update(ReceiptOverrideModel)
        .where(
            ReceiptOverrideModel.week == str(key.week),
            ReceiptOverrideModel.brand == key.brand.upper(),
            ReceiptOverrideModel.dc == key.site,
            ReceiptOverrideModel.po_number == key.po_number,
            ReceiptOverrideModel.sku_code == sku_code,
            ReceiptOverrideModel.market == market,
        )
        .values({ReceiptOverrideModel.deleted_ts: datetime.now(), ReceiptOverrideModel.deleted_by: user_email})
    )
