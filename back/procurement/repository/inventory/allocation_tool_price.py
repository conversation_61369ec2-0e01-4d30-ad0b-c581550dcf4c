from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from procurement.core.dates import ScmWeek
from procurement.data.dto.inventory.allocation_tool_price import AllocationPrice
from procurement.data.models.inventory.allocation_price import AllocationPriceModel
from procurement.managers.admin.dc_admin import DcConfig
from procurement.services.database import app_db


def update_allocation_prices(updates: list[dict[ColumnElement, Any]]) -> None:
    app_db.apply_query(sqla.insert(AllocationPriceModel).values(updates))


def delete_allocation_prices(brand: str, weeks: set[int]) -> None:
    app_db.apply_query(
        sqla.delete(AllocationPriceModel).where(
            AllocationPriceModel.brand == brand,
            AllocationPriceModel.scm_week.in_(weeks),
        )
    )


def get_allocation_prices(
    week: ScmWeek, dc_obj: DcConfig, brand: str, sku_codes: set[str] | None = None
) -> list[AllocationPrice]:
    query = sqla.select(AllocationPriceModel.sku_code, AllocationPriceModel.price).where(
        AllocationPriceModel.scm_week == int(week),
        AllocationPriceModel.brand == brand,
        (AllocationPriceModel.site == dc_obj.sheet_name) | (AllocationPriceModel.site == dc_obj.bob_code),
    )
    if sku_codes is not None:
        query = query.where(AllocationPriceModel.sku_code.in_(sku_codes))
    return [AllocationPrice(sku_code=item.sku_code, cost=item.price) for item in app_db.select_all(query)]
