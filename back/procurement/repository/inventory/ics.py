from collections.abc import Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import Row
from sqlalchemy.dialects import postgresql as psql_sqla
from sqlalchemy.orm import InstrumentedAttribute

from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import ORDER_NUMBER, PO_NUMBER
from procurement.data.dto.inventory.ics import IcsTicket, IcsTicketPreview, ics_status_mapping
from procurement.data.models.inventory.ics import IcsTicketsModel
from procurement.services.database import app_db


def upsert_ics_tickets(data: list[dict[InstrumentedAttribute, Any]]) -> None:
    query = psql_sqla.insert(IcsTicketsModel).values(data)

    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=IcsTicketsModel.get_primary_key_columns(),
            set_={
                IcsTicketsModel.subject: query.excluded.subject,
                IcsTicketsModel.ticket_link: query.excluded.ticket_link,
                IcsTicketsModel.request_type: query.excluded.request_type,
                IcsTicketsModel.status: query.excluded.status,
                IcsTicketsModel.updated_at: query.excluded.updated_at,
                IcsTicketsModel.production_impact: query.excluded.production_impact,
            },
        )
    )


def get_ics_tickets_preview(bob_codes: list[str], weeks: Iterable[ScmWeek]) -> list[IcsTicketPreview]:
    market = context.get_request_context().market
    query = sqla.select(
        IcsTicketsModel.sku_code, IcsTicketsModel.po_number, IcsTicketsModel.bob_code, IcsTicketsModel.week
    ).where(
        IcsTicketsModel.week.in_(list(map(int, weeks))),
        IcsTicketsModel.bob_code.in_(bob_codes),
        IcsTicketsModel.market == market,
    )
    return [
        IcsTicketPreview(
            sku_code=item.sku_code,
            po_number=item.po_number,
            order_number=utils.get_order_number_from_po_number(item.po_number),
            bob_code=item.bob_code,
            week=ScmWeek.from_number(item.week),
        )
        for item in app_db.select_all(query)
    ]


def _to_dto(item: Row) -> IcsTicket:
    return IcsTicket(
        ticket_id=item.ticket_id,
        ticket_link=item.ticket_link,
        po_number=item.po_number,
        order_number=item.order_number,
        sku_code=item.sku_code,
        bob_code=item.bob_code,
        subject=item.subject,
        week=ScmWeek.from_number(item.week),
        production_impact=item.production_impact,
        status=ics_status_mapping.get(item.status),
        request_type=item.request_type,
        updated_at=item.updated_at,
    )


def get_ics_tickets(
    bob_codes: list[str], sku_code: str | None, po_number: PO_NUMBER | ORDER_NUMBER | None, week: ScmWeek
) -> list[IcsTicket]:
    market = context.get_request_context().market
    query = sqla.select(IcsTicketsModel).where(
        IcsTicketsModel.week == int(week),
        IcsTicketsModel.bob_code.in_(bob_codes),
        IcsTicketsModel.market == market,
    )
    if sku_code:
        query = query.where(IcsTicketsModel.sku_code == sku_code)
    if po_number:
        query = query.where(IcsTicketsModel.order_number == utils.get_order_number_from_po_number(po_number))
    return [_to_dto(item) for item in app_db.select_all(query.order_by(sqla.desc(IcsTicketsModel.status)))]
