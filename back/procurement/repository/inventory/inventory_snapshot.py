import functools
from collections.abc import Iterable
from datetime import date, datetime, time, timedelta

import sqlalchemy as sqla
from sqlalchemy import Connection
from sqlalchemy.dialects import postgresql as psql

from procurement.constants.hellofresh_constant import InventoryInputType
from procurement.core.typing import BOB_CODE
from procurement.data.dto.inventory.inventory_snapshots import (
    InventorySnapshot,
    InventorySnapshotExtended,
    LatestSnapshotByWh,
)
from procurement.data.models.inventory.inventory_snapshot import InventorySnapshotModel
from procurement.services.database import app_db


def upsert_snapshots(snapshots: list[dict], transaction: Connection = None) -> None:
    query = psql.insert(InventorySnapshotModel).values(snapshots)
    app_db.apply_query(
        query.on_conflict_do_update(
            index_elements=InventorySnapshotModel.get_primary_key_columns(),
            set_={
                InventorySnapshotModel.unit_quantity: InventorySnapshotModel.unit_quantity
                + query.excluded.unit_quantity
            },
        ),
        transaction=transaction,
    )


def delete_old_snapshot(
    new_snapshot_ts_by_wh: dict[tuple[BOB_CODE, InventoryInputType], datetime], transaction: Connection = None
) -> None:
    clause = functools.reduce(
        lambda a, b: a | b,
        (
            (InventorySnapshotModel.wh_code == wh)
            & (InventorySnapshotModel.inventory_type == i_type)
            & (InventorySnapshotModel.snapshot_ts >= datetime.combine(ts.date(), time()))
            & (InventorySnapshotModel.snapshot_ts < ts)
            for (wh, i_type), ts in new_snapshot_ts_by_wh.items()
        ),
    )
    app_db.apply_query(sqla.delete(InventorySnapshotModel).where(clause), transaction=transaction)


def get_snapshots(bob_code: str, date_from: date, date_to: date) -> list[InventorySnapshot]:
    query = sqla.select(
        InventorySnapshotModel.sku_code,
        InventorySnapshotModel.snapshot_ts,
        InventorySnapshotModel.unit_quantity,
        InventorySnapshotModel.expiration_date,
        InventorySnapshotModel.location_type,
    ).where(
        InventorySnapshotModel.wh_code == bob_code,
        InventorySnapshotModel.inventory_type == InventoryInputType.WMSL,
        InventorySnapshotModel.snapshot_ts >= date_from,
        InventorySnapshotModel.snapshot_ts < date_to + timedelta(days=1),
    )
    snapshots = app_db.select_all(query)
    return [
        InventorySnapshot(
            sku_code=snapshot.sku_code,
            snapshot_date=snapshot.snapshot_ts.date(),
            units=snapshot.unit_quantity or None,
            expiration_date=snapshot.expiration_date,
            location_type=snapshot.location_type,
        )
        for snapshot in snapshots
    ]


def get_latest_snapshots(
    bob_codes: Iterable[str], sku_codes: Iterable[str] | None = None
) -> list[InventorySnapshotExtended]:
    max_ts = app_db.select_scalar(
        sqla.select(sqla.func.max(InventorySnapshotModel.snapshot_ts)).where(
            InventorySnapshotModel.inventory_type == InventoryInputType.WMSL
        )
    )
    query = sqla.select(
        InventorySnapshotModel.sku_code,
        InventorySnapshotModel.snapshot_ts,
        InventorySnapshotModel.unit_quantity,
        InventorySnapshotModel.expiration_date,
        InventorySnapshotModel.wh_code,
        InventorySnapshotModel.location_type,
        InventorySnapshotModel.state,
        InventorySnapshotModel.location_id,
    ).where(
        InventorySnapshotModel.wh_code.in_(bob_codes),
        InventorySnapshotModel.inventory_type == InventoryInputType.WMSL,
        InventorySnapshotModel.snapshot_ts == max_ts,
    )

    if sku_codes:
        query = query.where(InventorySnapshotModel.sku_code.in_(sku_codes))

    snapshots = app_db.select_all(query)
    return [
        InventorySnapshotExtended(
            sku_code=snapshot.sku_code,
            snapshot_date=snapshot.snapshot_ts.date(),
            units=snapshot.unit_quantity,
            expiration_date=snapshot.expiration_date,
            bob_code=snapshot.wh_code,
            location_type=snapshot.location_type,
            state=snapshot.state,
            location_id=snapshot.location_id,
        )
        for snapshot in snapshots
    ]


def get_latest_snapshot_dates_by_wh() -> list[LatestSnapshotByWh]:
    query = sqla.select(
        InventorySnapshotModel.wh_code,
        InventorySnapshotModel.inventory_type,
        sqla.func.max(InventorySnapshotModel.snapshot_ts).label("max_snapshot_ts"),
    ).group_by(
        InventorySnapshotModel.wh_code,
        InventorySnapshotModel.inventory_type,
    )
    return [
        LatestSnapshotByWh(
            bob_code=row.wh_code, inventory_type=row.inventory_type, snapshot_date=row.max_snapshot_ts.date()
        )
        for row in app_db.select_all(query)
    ]
