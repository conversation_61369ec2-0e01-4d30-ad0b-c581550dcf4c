import sqlalchemy as sqla
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.data.dto.inventory.strategy_manager import StrategyManager
from procurement.data.models.inventory.strategy_manager import StrategyManagerModel
from procurement.services.database import app_db


def update_data(updates: list) -> None:
    if not updates:
        return
    app_db.apply_query(sqla.delete(StrategyManagerModel))
    query = psql_sqla.insert(StrategyManagerModel)
    query = query.on_conflict_do_nothing(
        index_elements=StrategyManagerModel.get_primary_key_columns(),
    ).values(updates)
    app_db.apply_query(query)


def get_strategy_managers(sku_codes: set[str] | None = None) -> list[StrategyManager]:
    query = sqla.select(StrategyManagerModel.sku_code, StrategyManagerModel.manager)
    if sku_codes is not None:
        query = query.where(StrategyManagerModel.sku_code.in_(sku_codes))
    return [StrategyManager(sku_code=item.sku_code, manager=item.manager) for item in app_db.select_all(query)]
