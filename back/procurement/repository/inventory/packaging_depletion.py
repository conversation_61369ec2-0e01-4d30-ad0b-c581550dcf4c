from collections.abc import Collection, Iterable
from typing import Any

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Select
from sqlalchemy.dialects import postgresql as psql_sqla

from procurement.core.dates import ScmWeek
from procurement.core.request_utils import context
from procurement.core.typing import BRAND, MARKE<PERSON>, SITE, SKU_CODE
from procurement.data.dto.inventory.packaging import (
    PackagingDemand,
    PackagingOverride,
    PackagingOverrideExtended,
    PackagingOverrideKey,
)
from procurement.data.models.inventory.packaging_demand import PackagingDemandModel
from procurement.data.models.inventory.packaging_override import PackagingOverrideModel
from procurement.repository import repository_utils
from procurement.services.database import app_db


def update_packaging_demand(data: list[dict[ColumnElement, Any]]) -> None:
    if not data:
        return
    data_keys = {
        (
            item[PackagingDemandModel.week],
            item[PackagingDemandModel.site],
            item[PackagingDemandModel.brand],
            item[PackagingDemandModel.sku_code],
            item[PackagingDemandModel.market],
        )
        for item in data
    }
    brands = set()
    weeks = set()
    for item in data:
        brands.add(item[PackagingDemandModel.brand])
        weeks.add(item[PackagingDemandModel.week])
    to_delete = [k for k in _get_demand_keys(brands, weeks) if k not in data_keys]
    if to_delete:
        _delete_demand(to_delete)
    repository_utils.safe_bulk_insert_sqla(
        model=PackagingDemandModel,
        data=data,
        keys=[
            PackagingDemandModel.week,
            PackagingDemandModel.site,
            PackagingDemandModel.brand,
            PackagingDemandModel.sku_code,
            PackagingDemandModel.market,
        ],
        preserve_fields=[PackagingDemandModel.demand_by_day],
    )


def get_packaging_demand(weeks: Iterable[ScmWeek], site: str, brand: str) -> list[PackagingDemand]:
    market = context.get_request_context().market
    query = sqla.select(
        PackagingDemandModel.week, PackagingDemandModel.sku_code, PackagingDemandModel.demand_by_day
    ).where(
        PackagingDemandModel.week.in_([int(w) for w in weeks]),
        PackagingDemandModel.site == site,
        PackagingDemandModel.brand == brand,
        PackagingDemandModel.market == market,
    )
    return [
        PackagingDemand(week=item.week, sku_code=item.sku_code, demand_by_day=item.demand_by_day)
        for item in app_db.select_all(query)
    ]


def get_packaging_demand_sku_codes_query(
    week: ScmWeek, site: str, alternative_skus: Collection[str] | None = None
) -> Select:
    market = context.get_request_context().market
    query = (
        sqla.select(PackagingDemandModel.sku_code)
        .distinct()
        .where(
            PackagingDemandModel.week == int(week),
            PackagingDemandModel.site == site,
            PackagingDemandModel.market == market,
        )
    )
    if alternative_skus:
        query = query.union(repository_utils.make_values(alternative_skus, ["sku_code"]).select()).select()
    return query


def _get_demand_keys(brands: set[str], weeks: set[int]) -> list[tuple[int, SITE, BRAND, SKU_CODE, MARKET]]:
    market = context.get_request_context().market
    query = sqla.select(
        PackagingDemandModel.week,
        PackagingDemandModel.site,
        PackagingDemandModel.brand,
        PackagingDemandModel.sku_code,
    ).where(
        PackagingDemandModel.week.in_(weeks),
        PackagingDemandModel.brand.in_(brands),
        PackagingDemandModel.market == market,
    )
    return [(item.week, item.site, item.brand, item.sku_code, market) for item in app_db.select_all(query)]


def _delete_demand(keys: list[tuple[int, SITE, BRAND, SKU_CODE, MARKET]]) -> None:
    app_db.apply_query(
        sqla.delete(PackagingDemandModel).where(
            sqla.tuple_(
                PackagingDemandModel.week,
                PackagingDemandModel.site,
                PackagingDemandModel.brand,
                PackagingDemandModel.sku_code,
                PackagingDemandModel.market,
            ).in_(keys)
        ),
    )


def get_packaging_overrides(weeks: Iterable[ScmWeek], site: str, brand: str) -> Iterable[PackagingOverrideExtended]:
    query = sqla.select(
        PackagingOverrideModel.week,
        PackagingOverrideModel.sku_code,
        PackagingOverrideModel.day,
        PackagingOverrideModel.on_hand_override,
        PackagingOverrideModel.incoming_override,
    ).where(
        PackagingOverrideModel.week.in_([int(w) for w in weeks]),
        PackagingOverrideModel.site == site,
        PackagingOverrideModel.brand == brand,
    )
    return [
        PackagingOverrideExtended(
            week=item.week,
            sku_code=item.sku_code,
            day=item.day,
            on_hand_override=item.on_hand_override,
            incoming_override=item.incoming_override,
        )
        for item in app_db.select_all(query)
    ]


def get_packaging_override(key: PackagingOverrideKey) -> PackagingOverride | None:
    override = app_db.select_one(
        sqla.select(PackagingOverrideModel.on_hand_override, PackagingOverrideModel.incoming_override).where(
            PackagingOverrideModel.week == int(key.week),
            PackagingOverrideModel.site == key.site,
            PackagingOverrideModel.brand == key.brand,
            PackagingOverrideModel.sku_code == key.sku_code,
            PackagingOverrideModel.day == key.day,
        )
    )
    return (
        PackagingOverride(override.on_hand_override, override.incoming_override)
        if override
        else PackagingOverride(None, None)
    )


def save_packaging_override(override: dict[ColumnElement, Any]) -> None:
    query = psql_sqla.insert(PackagingOverrideModel)
    query = query.on_conflict_do_update(
        index_elements=PackagingOverrideModel.get_primary_key_columns(),
        set_={
            PackagingOverrideModel.on_hand_override: query.excluded.on_hand_override,
            PackagingOverrideModel.incoming_override: query.excluded.incoming_override,
            PackagingOverrideModel.last_updated: query.excluded.last_updated,
            PackagingOverrideModel.updated_by: query.excluded.updated_by,
        },
    ).values(override)
    app_db.apply_query(query)
