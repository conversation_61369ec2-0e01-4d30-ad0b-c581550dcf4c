from collections.abc import Collection
from datetime import date
from decimal import Decimal
from typing import Type

import sqlalchemy as sqla
from sqlalchemy import ColumnElement, Row

from procurement.constants.hellofresh_constant import BRAND_GC, ProductionPlanType
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.data.dto.inventory.hybrid_needs import (
    HybridNeedsDto,
    HybridNeedsLiveUsage,
    HybridNeedsStatusDto,
    ShiftNeedsDto,
)
from procurement.data.models.inventory.hybrid_needs import (
    DeliveryDateNeedsModel,
    HybridNeedsIngredientsModel,
    HybridNeedsIngredientsShiftLevelModel,
    HybridNeedsIngredientsStatusModel,
    HybridNeedsLiveUsageModel,
)
from procurement.services.database import app_db


def get_ingredients_data_count(brand: str, site: str, week: ScmWeek) -> int:
    return app_db.select_scalar(
        sqla.select(sqla.func.count().label("record_count")).where(
            HybridNeedsIngredientsModel.scm_week == str(week),
            HybridNeedsIngredientsModel.brand == brand,
            HybridNeedsIngredientsModel.dc == site,
        )
    )


def remove_ingredients_data(brand: str, site: str, week: ScmWeek, except_skus: set[str]) -> None:
    app_db.apply_query(
        sqla.delete(HybridNeedsIngredientsModel).where(
            HybridNeedsIngredientsModel.scm_week == str(week),
            HybridNeedsIngredientsModel.brand == brand,
            HybridNeedsIngredientsModel.dc == site,
            HybridNeedsIngredientsModel.sku_code.not_in(except_skus),
        )
    )


def remove_delivery_date_needs_data(brand: str, site: str, week: ScmWeek, except_skus: set[str]) -> None:
    app_db.apply_query(
        sqla.delete(DeliveryDateNeedsModel).where(
            DeliveryDateNeedsModel.scm_week == str(week),
            DeliveryDateNeedsModel.brand == brand,
            DeliveryDateNeedsModel.dc == site,
            DeliveryDateNeedsModel.sku_code.not_in(except_skus),
        )
    )


def get_shift_level_data_count(brand: str, site: str, week: ScmWeek) -> int:
    return app_db.select_scalar(
        sqla.select(sqla.func.count().label("record_count")).where(
            HybridNeedsIngredientsShiftLevelModel.scm_week == str(week),
            HybridNeedsIngredientsShiftLevelModel.brand == brand,
            HybridNeedsIngredientsShiftLevelModel.dc == site,
        )
    )


def remove_shift_level_data(brand: str, site: str, week: ScmWeek, except_skus: set[str]) -> None:
    app_db.apply_query(
        sqla.delete(HybridNeedsIngredientsShiftLevelModel).where(
            HybridNeedsIngredientsShiftLevelModel.scm_week == str(week),
            HybridNeedsIngredientsShiftLevelModel.brand == brand,
            HybridNeedsIngredientsShiftLevelModel.dc == site,
            HybridNeedsIngredientsShiftLevelModel.sku_code.not_in(except_skus),
        )
    )


def get_hybrid_needs_status_by_week(site: str, brand: str, week: ScmWeek) -> list[HybridNeedsStatusDto]:
    ingredient_statuses = app_db.select_all(
        sqla.select(
            HybridNeedsIngredientsStatusModel.day,
            HybridNeedsIngredientsStatusModel.status,
            HybridNeedsIngredientsStatusModel.site,
        ).where(
            HybridNeedsIngredientsStatusModel.site == site,
            HybridNeedsIngredientsStatusModel.scm_week == str(week),
            HybridNeedsIngredientsStatusModel.brand == brand,
        )
    )
    return [
        HybridNeedsStatusDto(site=item.site, day=item.day, status=ProductionPlanType(item.status))
        for item in ingredient_statuses
    ]


def get_hybrid_needs_live_usages(site: str, brand: str, week: ScmWeek) -> list[HybridNeedsLiveUsage]:
    query = sqla.select(
        HybridNeedsLiveUsageModel.sku_code, HybridNeedsLiveUsageModel.day, HybridNeedsLiveUsageModel.value
    ).where(
        HybridNeedsLiveUsageModel.site == site,
        HybridNeedsLiveUsageModel.brand == brand,
        HybridNeedsLiveUsageModel.scm_week == int(week),
    )

    return [
        HybridNeedsLiveUsage(sku_code=item.sku_code, day=item.day, value=item.value)
        for item in app_db.select_all(query)
    ]


def get_hybrid_needs_by_week(
    site: str, brand: str, week: ScmWeek, sku_codes: Collection[str] = None
) -> list[HybridNeedsDto]:
    date_filter = HybridNeedsIngredientsModel.scm_week == str(week)
    return _get_hybrid_needs_data(site, brand, date_filter, sku_codes=sku_codes)


def get_hybrid_needs_by_date(site: str, brand: str, date_from: date, date_to: date) -> list[HybridNeedsDto]:
    date_filter = (HybridNeedsIngredientsModel.date >= date_from) & (HybridNeedsIngredientsModel.date < date_to)
    return _get_hybrid_needs_data(site, brand, date_filter=date_filter)


def _get_hybrid_needs_data(
    site: str, brand: str, date_filter: ColumnElement, sku_codes: Collection[str] = None
) -> list[HybridNeedsDto]:
    query = sqla.select(
        HybridNeedsIngredientsModel.sku_code,
        HybridNeedsIngredientsModel.date,
        HybridNeedsIngredientsModel.scm_week,
        HybridNeedsIngredientsModel.value,
    ).where(HybridNeedsIngredientsModel.dc == site, HybridNeedsIngredientsModel.brand == brand, date_filter)

    if utils.is_any_collection_empty(sku_codes):
        return []
    if sku_codes:
        query = query.where(HybridNeedsIngredientsModel.sku_code.in_(sku_codes))

    return _build_hn_dto(brand, app_db.select_all(query))


def _build_hn_dto(brand: str, records: list[Row]) -> list[HybridNeedsDto]:
    return [
        HybridNeedsDto(
            sku_code=item.sku_code,
            day=item.date,
            scm_week=ScmWeek.from_str(item.scm_week),
            value=_round_if_not_brand_gc(item.value, brand),
        )
        for item in records
    ]


def get_delivery_date_needs_by_date(site: str, brand: str, date_from: date, date_to: date) -> list[HybridNeedsDto]:
    query = sqla.select(
        DeliveryDateNeedsModel.sku_code,
        DeliveryDateNeedsModel.date,
        DeliveryDateNeedsModel.scm_week,
        DeliveryDateNeedsModel.value,
    ).where(
        DeliveryDateNeedsModel.dc == site,
        DeliveryDateNeedsModel.brand == brand,
        DeliveryDateNeedsModel.date >= date_from,
        DeliveryDateNeedsModel.date < date_to,
    )
    return _build_hn_dto(brand, app_db.select_all(query))


def get_hybrid_needs_shift_by_week(site: str, brand: str, week: ScmWeek) -> list[ShiftNeedsDto]:
    shifts = app_db.select_all(
        sqla.select(
            HybridNeedsIngredientsShiftLevelModel.sku_code,
            HybridNeedsIngredientsShiftLevelModel.date,
            HybridNeedsIngredientsShiftLevelModel.scm_week,
            HybridNeedsIngredientsShiftLevelModel.value_day,
            HybridNeedsIngredientsShiftLevelModel.value_night,
            HybridNeedsIngredientsShiftLevelModel.value_third,
        ).where(
            HybridNeedsIngredientsShiftLevelModel.dc == site,
            HybridNeedsIngredientsShiftLevelModel.brand == brand,
            HybridNeedsIngredientsShiftLevelModel.scm_week == str(week),
        )
    )
    return [
        ShiftNeedsDto(
            sku_code=item.sku_code,
            day=item.date,
            scm_week=ScmWeek.from_str(item.scm_week),
            value_day=_round_if_not_brand_gc(item.value_day, brand),
            value_night=_round_if_not_brand_gc(item.value_night, brand),
            value_third=_round_if_not_brand_gc(item.value_third, brand),
        )
        for item in shifts
    ]


def _round_if_not_brand_gc(value: Decimal, brand: str) -> UNITS:
    return value if brand == BRAND_GC else utils.ceil_subone_integer(value)


def get_latest_available_hn_week(site: str, brand: str) -> ScmWeek | None:
    return _get_latest_week(HybridNeedsIngredientsModel, site, brand)


def get_latest_available_delivery_date_needs_week(site: str, brand: str) -> ScmWeek | None:
    return _get_latest_week(DeliveryDateNeedsModel, site, brand)


def _get_latest_week(
    model: Type[HybridNeedsIngredientsModel | DeliveryDateNeedsModel], site: str, brand: str
) -> ScmWeek | None:
    week = app_db.select_scalar(
        sqla.select(sqla.func.max(model.scm_week).label("latest_week")).where(model.dc == site, model.brand == brand)
    )
    if week:
        return ScmWeek.from_str(week)
    return None
