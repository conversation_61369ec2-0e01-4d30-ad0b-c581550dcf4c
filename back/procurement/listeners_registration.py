from typing import Type

from procurement.core.config_utils import AppType, app_type, config, killswitch
from procurement.core.kafka.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KafkaHandlerRegistry
from procurement.core.pubsub.constants import redis_listener
from procurement.core.pubsub.events import BaseEvent, InvalidateCacheEvent, ResetKafkaTopicEvent
from procurement.core.pubsub.handlers import RedisHandler
from procurement.core.pubsub.listener import RedisHandlerRegistry
from procurement.managers import core_management
from procurement.managers.admin.dc_listener import DcHandler
from procurement.managers.imt.work_order.work_order_listener import WorkOrderHandler
from procurement.managers.inventory.inventory_listener import InventoryHandler
from procurement.managers.inventory.shelf_life_manager import Shelf<PERSON>ifeHandler
from procurement.managers.ordering.advance_shipping_notice import AdvanceShippingNoticeHandler
from procurement.managers.ordering.culinary_sku import CulinarySkuHandler, SupplierSkuHandler
from procurement.managers.ordering.manufactured_sku import ManufacturedSkuHandler
from procurement.managers.ordering.ot_listener import OtHandler
from procurement.managers.ordering.po_acknowledgement import POAcknowledgementHandler
from procurement.managers.ordering.purchase_qty_recommendation_listener import PurchaseQtyRecommendationHandler
from procurement.managers.ordering.shipment import BluJayAppointmentHandler, BluJayShipmentHandler
from procurement.managers.ordering.supplier_listener import SupplierHandler
from procurement.managers.ordering.supplier_splits_listener import SupplierSplitsHandler
from procurement.managers.ordering.transfer_order_listener import ToHandler
from procurement.managers.pimt.packaging_info import PackagingInfoHandler
from procurement.managers.receipt.grn_listener import GrnHandler


def register_kafka_message_handlers() -> KafkaHandlerRegistry:
    topics = config["kafka"]["topics"]["consume"]
    registry = KafkaHandlerRegistry()

    def _register_handler(enabled: bool, topic_name: str, handler: Type[KafkaHandler]):
        if enabled and topic_name in topics:
            registry.register_handler(key=topics[topic_name], handler=handler())

    _register_handler(killswitch.kafka_ot_enabled, "ot", OtHandler)
    _register_handler(killswitch.kafka_transfer_order_enabled, "transfer_order", ToHandler)
    _register_handler(killswitch.blujay_shipment_sync_enabled, "blujay_shipment", BluJayShipmentHandler)
    _register_handler(killswitch.grn_sync_enabled, "grn", GrnHandler)
    _register_handler(killswitch.culinary_sku_sync_enabled, "culinary_sku", CulinarySkuHandler)
    _register_handler(killswitch.blujay_appointment_sync_enabled, "blujay_appointment", BluJayAppointmentHandler)
    _register_handler(killswitch.supplier_sku_sync_enabled, "supplier_sku", SupplierSkuHandler)
    _register_handler(killswitch.packaging_info_sync_enabled, "packaging_info", PackagingInfoHandler)
    _register_handler(killswitch.po_acknowledgement_sync_enabled, "po_acknowledgement", POAcknowledgementHandler)
    _register_handler(
        killswitch.advance_shipping_notice_sync_enabled,
        "advance_shipping_notice",
        AdvanceShippingNoticeHandler,
    )
    _register_handler(killswitch.kafka_inv_snapshot_enabled, "inventory_snapshot", InventoryHandler)
    _register_handler(killswitch.kafka_supplier_enabled, "supplier", SupplierHandler)
    _register_handler(killswitch.kafka_dc_enabled, "dc", DcHandler)
    _register_handler(killswitch.kafka_work_order_tool_enabled, "work_order_tool", WorkOrderHandler)
    _register_handler(killswitch.kafka_manufactured_sku_enabled, "manufactured_sku", ManufacturedSkuHandler)
    _register_handler(killswitch.kafka_supplier_splits_consumption_enabled, "supplier_splits", SupplierSplitsHandler)
    _register_handler(
        killswitch.kafka_purchase_qty_recommendation_consumption_enabled,
        "purchase_qty_recommendation",
        PurchaseQtyRecommendationHandler,
    )
    _register_handler(killswitch.kafka_shelf_life_consumption_enabled, "shelf_life", ShelfLifeHandler)

    return registry


def register_redis_message_handlers():
    registry = RedisHandlerRegistry()

    def _register_handler(event: Type[BaseEvent], handler: RedisHandler):
        registry.register_handler(key=event, handler=handler)

    _register_handler(InvalidateCacheEvent, core_management.LocalCacheInvalidateHandler())
    if app_type == AppType.CONSUMER:
        _register_handler(ResetKafkaTopicEvent, core_management.KafkaResetOffsetHandler())

    if not registry.is_empty():
        redis_listener.register_registry(registry)
        redis_listener.run()
