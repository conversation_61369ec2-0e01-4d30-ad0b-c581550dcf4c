import logging
import os

import prometheus_client
from rq import Queue, worker
from rq_exporter.collector import <PERSON><PERSON><PERSON>ollector

from procurement.core.cache_utils.constants import broker
from procurement.core.exceptions.worker_exceptions_handlers import worker_custom_exception_handler
from procurement.core.metrics import PROMETHEUS_REGISTRY, ApplicationMetrics, init_registry
from procurement.data.models.social.parallel_sync_log import SyncStatus
from procurement.managers.datasync.framework import common, warnings

logger = logging.getLogger(__name__)


def _initialize_collector() -> None:
    collector = RQCollector(broker, ProcurementWorker, Queue)
    metrics_port = int(os.environ.get("WORKER_METRICS_PORT", 8001))
    init_registry()
    PROMETHEUS_REGISTRY.register(collector)

    prometheus_client.start_http_server(metrics_port, "0.0.0.0", PROMETHEUS_REGISTRY)  # nosec B104


class ProcurementWorker(worker.Worker):
    def __init__(self, *args, **kwargs):
        kwargs["exception_handlers"] = worker_custom_exception_handler

        super().__init__(*args, **kwargs)

    def handle_job_failure(self, job, queue, started_job_registry=None, exc_string=""):
        self.log.debug("Handling failed execution of job %s", job.id)

        if started_job_registry is None:
            started_job_registry = worker.StartedJobRegistry(job.origin, self.connection, job_class=self.job_class)

        error_message = f"The job failed unexpectedly. Exc: '{exc_string}'. Step name: {job.meta.get('name')}"
        logger.error(error_message)
        ApplicationMetrics.critical_exceptions(service="job", exception=error_message)

        if job.retries_left and job.retries_left > 0:
            super().handle_job_failure(
                job=job, exc_string=exc_string, queue=queue, started_job_registry=started_job_registry
            )
        else:
            warnings.notify_warning(error_message, job=job)
            common.set_job_status(job, SyncStatus.FAILED)
            self.handle_job_success(job, queue, started_job_registry)


_initialize_collector()
