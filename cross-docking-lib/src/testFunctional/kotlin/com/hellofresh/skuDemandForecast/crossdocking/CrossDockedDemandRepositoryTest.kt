package com.hellofresh.skuDemandForecast.crossdocking

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction.DUPLICATE
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction.REPLACE
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedKey.Companion.Sku
import com.hellofresh.skuDemandForecast.crossdocking.automatic.CrossdockedDemandRule
import com.hellofresh.skuDemandForecast.crossdocking.automatic.CrossdockedRule
import com.hellofresh.skuDemandForecast.crossdocking.automatic.RegexSkuCodeRule
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.crossdockinglib.schema.public_.Tables
import com.hellofresh.skudemandforecast.crossdockinglib.schema.public_.enums.CrossdockAction
import com.hellofresh.skudemandforecast.crossdockinglib.schema.public_.tables.records.CrossdockedDemandRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class CrossDockedDemandRepositoryTest {

    private val week30 = "2024-W30"
    private val week31 = "2024-W31"
    private val week32 = "2024-W32"
    private val week51 = "2024-W51"

    @AfterEach
    fun cleanUp() {
        dsl.deleteFrom(Tables.CROSSDOCKED_DEMAND).execute()
        dsl.deleteFrom(Tables.CROSSDOCKED_DEMAND_RULES).execute()
    }

    @Test
    fun `should fetch cross docked demand By key`() {
        val skuId = UUID.randomUUID()
        val dcFrom = "BX"

        val crossdockedDemandList = createListOfCrossedDemand(skuId, dcFrom)

        crossdockedDemandList.forEach { cd ->
            runBlocking { insertCrossDockedDemand(cd) }
        }

        runBlocking {
            val record = repository.fetchLastVersionByUserKey(
                dsl,
                skuId,
                dcFrom,
                week32,
            )

            // ordered by version is ensured
            assertEquals(2, record?.version)
        }
    }

    @Test
    fun `should return last version of cross docked demand by dc weekStart less than given week`() {
        val skuId = UUID.randomUUID()
        val dcFrom = "BX"

        val crossDockedDemand1 = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = week30,
            version = 1,
        )

        val crossDockedDemand2 = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = week32,
            version = 1,
        )

        val crossDockedDemand3 = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = week31,
            version = 1,
        )

        val crossDockedDemand4 = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = week31,
            version = 2,
        )

        insertCrossDockedDemand(crossDockedDemand1)
        insertCrossDockedDemand(crossDockedDemand2)
        insertCrossDockedDemand(crossDockedDemand3)
        insertCrossDockedDemand(crossDockedDemand4)

        runBlocking {
            val crossDockedDemands = repository.fetchWeekLookUp(
                dsl,
                dcFrom,
                week31,
            )
            assertEquals(1, crossDockedDemands.size)
            assertEquals(crossDockedDemand4, crossDockedDemands[0])
        }
    }

    @Test
    fun `should return the cross docked demand rules by dc weekStart less than given week`() {
        val regexString = "^[A-Z]{1,4}-00-[0-9]{1,6}-[0-9]\$"
        insertCrossDockedDemandRules(regexString)
        val dcFrom = "CH"

        val crossDockedDemand = CrossdockedDemandRule(
            dcFrom = dcFrom,
            weekStart = week51,
            dcTo = "CR",
            action = REPLACE,
            regexSkuCodeRule = RegexSkuCodeRule(regexString),
            disabled = false,
        )

        runBlocking {
            val crossDockedDemands = repository.fetchFromWeekLookUpRules(
                setOf(dcFrom),
                week31,
            )
            assertEquals(1, crossDockedDemands.size)
            assertEquals(crossDockedDemand, crossDockedDemands[0])
        }
    }

    @Test
    fun `version should be null when there is no exact week look up`() {
        val dcFrom = "BX"
        val week = week31

        val crossDockedDemand1 = createCrossdockedDemandTestData(
            skuId = UUID.randomUUID(),
            dcFrom = dcFrom,
            weekStart = week30,
            version = 1,
        )

        val crossDockedDemand2 = createCrossdockedDemandTestData(
            skuId = UUID.randomUUID(),
            dcFrom = dcFrom,
            weekStart = week,
            version = 1,
        )

        insertCrossDockedDemand(crossDockedDemand1)
        insertCrossDockedDemand(crossDockedDemand2)

        runBlocking {
            val crossDockedDemands = repository.fetchWeekLookUp(dcFrom, week)
            assertEquals(2, crossDockedDemands.size)
            assertEquals(
                crossDockedDemand1.copy(weekStart = week, version = null),
                crossDockedDemands.first { it.skuId == crossDockedDemand1.skuId },
            )
            assertEquals(crossDockedDemand2, crossDockedDemands.first { it.skuId == crossDockedDemand2.skuId })
        }
    }

    @Test
    fun `should return empty list when given dc doesn't exist`() {
        val skuId = UUID.randomUUID()
        val dcFrom = "BX"

        val crossDockedDemand1 = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = week30,
            version = 1,
        )

        runBlocking { insertCrossDockedDemand(crossDockedDemand1) }

        runBlocking {
            val crossDockedDemands = repository.fetchWeekLookUp(
                "VE",
                week31,
            )
            assertEquals(0, crossDockedDemands.size)
        }
    }

    @Test
    fun `should return no records when weekStart is greated than given week`() {
        val skuId = UUID.randomUUID()
        val dcFrom = "BX"

        val crossDockedDemand = createCrossdockedDemandTestData(
            skuId = skuId,
            dcFrom = dcFrom,
            weekStart = "2024-W27",
            version = 1,
        )

        insertCrossDockedDemand(crossDockedDemand)
        runBlocking {
            val crossDockedDemands = repository.fetchWeekLookUp(
                dcFrom,
                "2024-W26",
            )
            assertEquals(0, crossDockedDemands.size)
        }
    }

    @Test
    fun `cross docker demands are fetched from week`() {
        val dcFrom1 = "BX"
        val dcFrom2 = "TT"

        val sku1 = Sku(UUID.randomUUID(), UUID.randomUUID().toString())
        val crossDockedDemand1_1 = createCrossdockedDemandTestData(
            skuId = sku1.id,
            dcFrom = dcFrom1,
            weekStart = week31,
            version = 1,
        )
        val crossDockedDemand1_2 = crossDockedDemand1_1.copy(
            weekStart = week31,
            version = 2,
        )
        val crossDockedDemand1_3 = crossDockedDemand1_1.copy(
            weekStart = week32,
            version = 1,
        )

        val sku2 = Sku(UUID.randomUUID(), UUID.randomUUID().toString())
        val crossDockedDemand2_1 = createCrossdockedDemandTestData(
            skuId = sku2.id,
            dcFrom = dcFrom1,
            weekStart = week30,
            version = 1,
        )

        val sku3 = Sku(UUID.randomUUID(), UUID.randomUUID().toString())
        val crossDockedDemand3_1 = createCrossdockedDemandTestData(
            skuId = sku3.id,
            dcFrom = dcFrom2,
            weekStart = week32,
            version = 1,
        )
        val crossDockedDemand3_2 = crossDockedDemand3_1.copy(
            version = 2,
            userId = "NewUser",
        )

        insertCrossDockedDemand(crossDockedDemand1_1)
        insertCrossDockedDemand(crossDockedDemand1_2)
        insertCrossDockedDemand(crossDockedDemand1_3)
        insertCrossDockedDemand(crossDockedDemand2_1)
        insertCrossDockedDemand(crossDockedDemand3_1)
        insertCrossDockedDemand(crossDockedDemand3_2)

        runBlocking {
            val crossDockedDemands = repository.fetchFromWeekLookUp(setOf(dcFrom1, dcFrom2), week31)
            // Exact match week + latest version (with future weeks as well)
            assertEquals(
                crossDockedDemand1_2,
                CrossDockedDemands(
                    crossDockedDemands,
                    emptyList(),
                ).getCrossDockedDemand(
                    crossDockedDemand1_2.dcFrom,
                    week31,
                    sku1,
                ),
            )
            // Exact match week + latest version + previous weeks
            assertEquals(
                crossDockedDemand1_3,
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand1_2.dcFrom,
                    week32,
                    sku1,
                ),
            )
            // Match previous week (not exact wee matching) + latest version
            assertEquals(
                crossDockedDemand2_1,
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand2_1.dcFrom,
                    week31,
                    sku2,
                ),
            )
            // No exact match and non previous week
            assertNull(
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand3_2.dcFrom,
                    week31,
                    sku3,
                ),
            )
            // exact week match + latest version
            assertEquals(
                crossDockedDemand3_2,
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand3_2.dcFrom,
                    week32,
                    sku3,
                ),
            )
        }
    }

    @Test
    fun `disabled cross docked demands are also fetched from week`() {
        val dcFrom1 = "BX"
        val dcFrom2 = "TT"

        val sku1 = Sku(UUID.randomUUID(), UUID.randomUUID().toString())
        val crossDockedDemand1_1 = createCrossdockedDemandTestData(
            skuId = sku1.id,
            dcFrom = dcFrom1,
            weekStart = week30,
            version = 1,
        )
        val crossDockedDemand1_2 = crossDockedDemand1_1.copy(
            weekStart = week30,
            version = 2,
        )
        val crossDockedDemand1_3 = crossDockedDemand1_1.copy(
            weekStart = week32,
            version = 1,
            disabled = true,
        )

        insertCrossDockedDemand(crossDockedDemand1_1)
        insertCrossDockedDemand(crossDockedDemand1_2)
        insertCrossDockedDemand(crossDockedDemand1_3)

        runBlocking {
            val crossDockedDemands = repository.fetchFromWeekLookUp(setOf(dcFrom1, dcFrom2), week31)
            // Previous week match + latest version (with future weeks as well)
            assertEquals(
                crossDockedDemand1_2,
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand1_2.dcFrom,
                    week31,
                    sku1,
                ),
            )
            // Exact match week + latest version(disabled) + previous weeks
            assertNull(
                CrossDockedDemands(crossDockedDemands, emptyList()).getCrossDockedDemand(
                    crossDockedDemand1_2.dcFrom,
                    "2024-W34",
                    sku1,
                ),
            )
        }
    }

    private fun createListOfCrossedDemand(skuId: UUID, dcFrom: String): List<CrossDockedDemand> {
        val configurations = listOf(
            Triple(skuId, dcFrom, "2024-W25"),
            Triple(skuId, dcFrom, week30),
            Triple(skuId, dcFrom, week32),
        )

        val dataList = configurations.map { (skuId, dcFrom, weekStart) ->
            createCrossdockedDemandTestData(
                skuId = skuId,
                dcFrom = dcFrom,
                weekStart = weekStart,
            )
        }.toMutableList()

        dataList.add(
            createCrossdockedDemandTestData(
                skuId = skuId,
                dcFrom = dcFrom,
                version = 2,
                weekStart = week32,
            ),
        )
        dataList.add(
            createCrossdockedDemandTestData(
                skuId = skuId,
                dcFrom = dcFrom,
                version = 2,
                weekStart = "2024-W33",
            ),
        )
        dataList.add(
            createCrossdockedDemandTestData(
                skuId = skuId,
                dcFrom = "VB",
                dcTo = dcFrom,
                version = 2,
                weekStart = "2024-W33",
            ),
        )
        dataList.add(
            createCrossdockedDemandTestData(
                skuId = UUID.randomUUID(),
                dcFrom = dcFrom,
                weekStart = week32,
            ),
        )

        return dataList
    }

    @Suppress("LongParameterList")
    internal fun createCrossdockedDemandTestData(
        skuId: UUID = UUID.randomUUID(),
        version: Int = 1,
        dcFrom: String = "BX",
        dcTo: String = "VB",
        weekStart: String = "2024-W35",
        action: CrossDockAction = DUPLICATE,
        userId: String = "<EMAIL>",
        disabled: Boolean = false,
    ) =
        CrossDockedDemand(
            skuId,
            dcFrom,
            weekStart,
            version,
            dcTo,
            action,
            userId,
            disabled,
        )

    private fun insertCrossDockedDemand(crossDockedDemand: CrossDockedDemand) {
        dsl.batchInsert(
            CrossdockedDemandRecord().apply {
                skuId = crossDockedDemand.skuId
                dcFrom = crossDockedDemand.dcFrom
                weekStart = crossDockedDemand.weekStart
                version = crossDockedDemand.version
                dcTo = crossDockedDemand.dcTo
                action = when (crossDockedDemand.action) {
                    REPLACE -> CrossdockAction.REPLACE
                    DUPLICATE -> CrossdockAction.DUPLICATE
                }
                userId = crossDockedDemand.userId
                disabled = crossDockedDemand.disabled
            },
        ).execute()
    }

    private fun insertCrossDockedDemandRules(regexString: String) =
        dsl.query(
            "INSERT INTO crossdocked_demand_rules (dc_from, dc_to, week_start, rule, action, disabled)\n" +
                "VALUES ('CH', 'CR', '2024-W51', '${objectMapper.writeValueAsString(CrossdockedRule(regexString))}', 'REPLACE', false);\n",
        ).execute()

    companion object {
        private val dataSource = getMigratedDataSource()
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        lateinit var repository: CrossDockedDemandRepository

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            repository = CrossDockedDemandRepository(dsl)
        }
    }
}
