import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction.REPLACE
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemand
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemand.Companion
import java.util.UUID

fun Companion.default() =
    CrossDockedDemand(
        skuId = UUID.randomUUID(),
        dcFrom = "VE",
        weekStart = "2024-W43",
        version = 1,
        dcTo = "BX",
        action = REPLACE,
        userId = UUID.randomUUID().toString(),
        disabled = false,
    )
