package com.hellofresh.skuDemandForecast.crossdocking.automatic

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction

data class CrossdockedDemandRule(
    val dcFrom: String,
    val dcTo: String,
    val weekStart: String,
    val regexSkuCodeRule: RegexSkuCodeRule,
    val action: CrossDockAction,
    val disabled: Boolean,
) {
    companion object
}

data class RegexSkuCodeRule(private val regexString: String) {

    @Transient
    private val regex: Regex = Regex(regexString)

    fun matches(skuCode: String) = regex.containsMatchIn(skuCode)
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class CrossdockedRule(
    @JsonProperty("sku_regex")
    val skuRegex: String,
) {
    companion object
}
