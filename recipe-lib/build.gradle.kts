plugins {
    id("com.hellofresh.sdf.common-conventions")
    alias(libs.plugins.gradle.docker.compose)
    alias(libs.plugins.jooq)
    `test-functional`
    hellofresh.`test-fixtures`
}

group = "$group.${project.name}".replace("-", "")
description = "Recipe Database library"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib"))
    implementation(project(":lib:db"))
    implementation(project(":lib:models"))
    implementation(project(":lib:featureflags"))
    implementation(project(":lib:authserviceclient"))
    implementation(libs.coroutines.jdk8)
    implementation(libs.jackson.kotlin)
    implementation(libs.retrofit.jackson)
    implementation(libs.retrofit.core)
    implementation(libs.okhttp.core)
    implementation(libs.resilience4j.circuitbreaker)
    implementation(libs.resilience4j.retrofit)
    implementation(libs.resilience4j.retry)

    testFunctionalImplementation(libs.coroutines.core)
    testFunctionalImplementation(libs.flyway.core)
    testFunctionalImplementation(libs.jooq.core)
    testFunctionalImplementation(libs.jackson.kotlin)
    testFunctionalImplementation(libs.testcontainers.core) {
        // excluding junit 4
        exclude("junit", module = "junit")
    }
    testFunctionalImplementation(libs.testcontainers.junit)
    testFunctionalImplementation(libs.testcontainers.postgresql)
    testFunctionalImplementation(project(":lib:db"))
    testFunctionalImplementation(testFixtures(project(":lib")))
    testFunctionalImplementation(libs.wiremock)
    testFunctionalImplementation(libs.mockk)
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "recipe|anz_recipe|recipe_view|recipe_snapshot"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
