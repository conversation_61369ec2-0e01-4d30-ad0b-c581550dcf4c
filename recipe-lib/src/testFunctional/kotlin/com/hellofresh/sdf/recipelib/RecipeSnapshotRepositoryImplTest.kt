package com.hellofresh.skuDemandForecast.recipelib

import com.hellofresh.skudemandforecast.recipelib.schema.public_.tables.RecipeSnapshot.RECIPE_SNAPSHOT
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking

internal class RecipeSnapshotRepositoryImplTest : FunctionalTest() {
    @Test fun `dirty bit is updated`() {
        insertRecipeSnapshot(UploadedRecipe.default(), LocalDateTime.now().minusMinutes(10))
        with(dsl.selectFrom(RECIPE_SNAPSHOT).fetch()) {
            assertEquals(1, size)
            assertTrue(this.first().dirtyBit)
        }

        runBlocking { RecipeSnapshotRepository().resetDirtyBit(dsl, LocalDateTime.now()) }

        with(dsl.selectFrom(RECIPE_SNAPSHOT).fetch()) {
            assertEquals(1, size)
            assertFalse(this.first().dirtyBit)
        }
    }
}
