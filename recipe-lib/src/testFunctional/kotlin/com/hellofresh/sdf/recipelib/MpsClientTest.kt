package com.hellofresh.sdf.recipelib

import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import com.github.tomakehurst.wiremock.client.WireMock.verify
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import com.github.tomakehurst.wiremock.stubbing.Scenario
import com.hellofresh.skuDemandForecast.authserviceclient.model.ApplicationConfig
import com.hellofresh.skuDemandForecast.authserviceclient.service.AuthServiceClient
import com.hellofresh.skuDemandForecast.models.MenuResponse
import com.hellofresh.skuDemandForecast.models.RecipeSnapshots
import com.hellofresh.skuDemandForecast.recipelib.MpsClient
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import org.intellij.lang.annotations.Language
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

private const val WEEK = "2024-W06"
private const val PRODUCT_FAMILY = "mutual-menu"
private const val MENUS_URI = "/v2/DACH/menus/$WEEK"
private const val PICKLIST_URI = "/v2/IT/picklists"
private const val AUTHORIZATION = "Authorization"
private const val WEEKS = "weeks"

@WireMockTest
class MpsClientTest {

    private lateinit var mpsClient: MpsClient
    private val authorizationHeader = "MOCK_ACCESS_TOKEN"
    private val timeout = "PT1S"
    private val meterRegistry = SimpleMeterRegistry()
    private val authServiceClient = mockk<AuthServiceClient>()

    @BeforeEach
    fun setUp(wmRuntimeInfo: WireMockRuntimeInfo) {
        val applicationConfig = ApplicationConfig(
            authServiceUrl = "http://localhost:${wmRuntimeInfo.httpPort}",
            applicationName = "",
            clientId = "",
            clientSecret = "",
            userName = "username",
            password = "password",
            authClientTimeout = timeout,
        )
        mpsClient = MpsClient(applicationConfig, "http://localhost:${wmRuntimeInfo.httpPort}", meterRegistry, authServiceClient)
        coEvery { authServiceClient.getAuthToken() }.returns(authorizationHeader)
    }

    @Test
    fun `should get recipes for the given market`() {
        stubGetRecipeCall(200, AUTH_TOKEN_RESPONSE)
        val result = mpsClient.getRecipes("IT", setOf(WEEK), null)
        val recipeSnapshots: RecipeSnapshots = result?.body() ?: RecipeSnapshots(items = emptyList())

        verify(
            getRequestedFor(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader)),
        )
        assertEquals(2, recipeSnapshots.items.size)
        recipeSnapshots.items.first { it.skuCode == "PTN-00-005073-4" }.apply {
            assertEquals(PRODUCT_FAMILY, boxType)
            assertEquals("IT", country)
            assertEquals("IT", locale)
            assertEquals(0, p1)
            assertEquals(1, p2)
            assertEquals(0, p3)
            assertEquals(2, p4)
            assertEquals(0, p5)
            assertEquals(0, p6)
            assertEquals(1, slotNumber)
            assertEquals(WEEK, week)
        }

        recipeSnapshots.items.first { it.skuCode == "DRY-00-005170-1" }.apply {
            assertEquals(PRODUCT_FAMILY, boxType)
            assertEquals("IT", country)
            assertEquals("IT", locale)
            assertEquals(0, p1)
            assertEquals(1, p2)
            assertEquals(0, p3)
            assertEquals(0, p4)
            assertEquals(0, p5)
            assertEquals(0, p6)
            assertEquals(1, slotNumber)
            assertEquals(WEEK, week)
        }
    }

    @Test
    fun `should return null while fetching the recipes`() {
        stubGetRecipeCall(400)
        val result = mpsClient.getRecipes("IT", setOf(WEEK), null)
        val recipeSnapshots: RecipeSnapshots = result?.body() ?: RecipeSnapshots(items = emptyList())

        verify(
            getRequestedFor(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader)),
        )
        assertEquals(0, recipeSnapshots.items.size)
    }

    @Test
    fun `should retry with exponential backoff retries when request fails`() {
        createFailureScenario()

        val result = mpsClient.getRecipes("IT", setOf(WEEK), "HelloFresh")
        val recipeSnapshots: RecipeSnapshots = result?.body() ?: RecipeSnapshots(items = emptyList())

        verify(
            5,
            getRequestedFor(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader)),
        )
        assertEquals(2, recipeSnapshots.items.size)
    }

    @Test
    fun `should get menus for the given market`() {
        stubGetMenusCall(200, MENUS_RESPONSE)
        val result = mpsClient.getMenus("DACH", WEEK)
        val menus = result?.body() ?: MenuResponse(emptyList())

        verify(
            getRequestedFor(urlPathEqualTo(MENUS_URI))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader)),
        )
        assertEquals(1, menus.menus.size)
    }

    @Test
    fun `should return null while fetching the menus`() {
        stubGetMenusCall(400)
        val result = mpsClient.getMenus("DACH", WEEK)
        val menus = result?.body() ?: MenuResponse(emptyList())

        verify(
            getRequestedFor(urlPathEqualTo(MENUS_URI))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader)),
        )
        assertEquals(0, menus.menus.size)
    }

    private fun createFailureScenario() {
        val scenario = "Retry Scenario"

        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(
                    AUTHORIZATION,
                    equalTo(authorizationHeader),
                )
                .inScenario(scenario)
                .whenScenarioStateIs(Scenario.STARTED)
                .willReturn(aResponse().withStatus(412))
                .willSetStateTo("Attempt 1"),
        )

        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .inScenario(scenario)
                .whenScenarioStateIs("Attempt 1")
                .willReturn(aResponse().withStatus(412))
                .willSetStateTo("Attempt 2"),
        )

        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .inScenario(scenario)
                .whenScenarioStateIs("Attempt 2")
                .willReturn(aResponse().withStatus(412))
                .willSetStateTo("Attempt 3"),
        )

        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .inScenario(scenario)
                .whenScenarioStateIs("Attempt 3")
                .willReturn(
                    aResponse()
                        .withStatus(412),
                )
                .willSetStateTo("Attempt 4"),
        )

        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .inScenario(scenario)
                .whenScenarioStateIs("Attempt 4")
                .willReturn(aResponse().withStatus(200).withBody(AUTH_TOKEN_RESPONSE)),
        )
    }

    private fun stubGetRecipeCall(status: Int, body: String = "") {
        stubFor(
            get(urlPathEqualTo(PICKLIST_URI))
                .withQueryParam(WEEKS, equalTo(WEEK))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .willReturn(
                    aResponse()
                        .withStatus(status)
                        .withBody(body),
                ),
        )
    }

    private fun stubGetMenusCall(status: Int, body: String = "") {
        stubFor(
            get(urlPathEqualTo(MENUS_URI))
                .withHeader(AUTHORIZATION, equalTo(authorizationHeader))
                .willReturn(
                    aResponse()
                        .withStatus(status)
                        .withBody(body),
                ),
        )
    }
}

@Language("JSON")
val AUTH_TOKEN_RESPONSE = """
    {
    "items": [
        {
            "box_type": "mutual-menu",
            "country": "IT",
            "locale": "IT",
            "p1": 0,
            "p2": 1,
            "p3": 0,
            "p4": 2,
            "p5": 0,
            "p6": 0,
            "sku_code": "PTN-00-005073-4",
            "sku_name": "beef pork trim minced 85 cl / tritata manzo e maiale - 280g",
            "slot_item_name": "Bowl di carne stile Banh-Mi",
            "slot_number": 1,
            "week": "2024-W06"
        },
        {
            "box_type": "mutual-menu",
            "country": "IT",
            "locale": "IT",
            "p1": 0,
            "p2": 1,
            "p3": 0,
            "p4": 0,
            "p5": 0,
            "p6": 0,
            "sku_code": "DRY-00-005170-1",
            "sku_name": "rice jasmine / riso jasmine - 150g",
            "slot_item_name": "Bowl di carne stile Banh-Mi",
            "slot_number": 1,
            "week": "2024-W06"
        }
        ]
  }
"""

@Language("JSON")
val MENUS_RESPONSE = """
    {
    "menus": [
        {
            "id": "3338a767-14fb-434d-8400-846fbff6bb62",
            "region": "deat",
            "brand": "HelloFresh",
            "week": "2025-W17",
            "slots": [
                {
                    "id": "84c8701b-8065-4225-ab5f-d6a535e6e652",
                    "position": 1,
                    "group": {
                        "id": "49d48216-429f-4cea-9043-6859e5baa6f6",
                        "name": "mutual-menu",
                        "is_default": false
                    },
                    "item_id": "6c254d09-61fd-4bbc-97de-b9942cc0de15",
                    "type": "recipe",
                    "tags": [],
                    "version": "2b1c2c83d51a7efe27d7bc33b1fc036b",
                    "updated_at": "2025-01-24T16:32:06.526761Z",
                    "request_state": "ok",
                    "item": {
                        "id": "6c254d09-61fd-4bbc-97de-b9942cc0de15",
                        "variant_type": "core",
                        "linked_variants": [
                            {
                                "id": "d8bcf677-f00f-4f22-b1ae-2d2d2736609e",
                                "variant_type": "thermomix",
                                "linked_variants": []
                            }
                        ]
                    },
                    "category": null,
                    "items": [
                        {
                            "id": "6c254d09-61fd-4bbc-97de-b9942cc0de15"
                        }
                    ]
                },
                {
                    "id": "fd2e48e4-699c-471a-87c1-14b7898d6d18",
                    "position": 2,
                    "group": {
                        "id": "49d48216-429f-4cea-9043-6859e5baa6f6",
                        "name": "mutual-menu",
                        "is_default": false
                    },
                    "item_id": "d38efcaa-d711-493b-9cdc-a1c2a0b2dc16",
                    "type": "recipe",
                    "tags": [],
                    "version": "fb07fc702aba1b30ac9818a0bf59eea0",
                    "updated_at": "2025-01-24T16:32:07.20979Z",
                    "request_state": "ok",
                    "item": {
                        "id": "d38efcaa-d711-493b-9cdc-a1c2a0b2dc16",
                        "variant_type": "core",
                        "linked_variants": []
                    },
                    "category": null,
                    "items": [
                        {
                            "id": "d38efcaa-d711-493b-9cdc-a1c2a0b2dc16"
                        }
                    ]
                }
            ],
            "is_past": false,
            "status": "planned"
        }
    ]
}
"""
