package com.hellofresh.skuDemandForecast.recipelib

import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

internal class RecipeViewRepositoryImplTest : FunctionalTest() {

    @Test fun `fetches the recipes from both RECIPE and RECIPE_SNAPSHOT tables based on mps countries`() {
        val importedRecipe = UploadedRecipe.random().copy(sourceId = null)
        val mpsRecipe = UploadedRecipe.random().copy(sourceId = null)
        runBlocking {
            recipeRepository.upsert(importedRecipe, dsl)
            insertRecipeSnapshot(mpsRecipe, LocalDateTime.now().minusMinutes(100))

            val fetched = with(RecipeViewRepository(dsl)) {
                fetch(fetchUpdatedCountryWeek(), setOf(CountryWeek(mpsRecipe.country, mpsRecipe.week)))
            }

            assertEquals(2, fetched.size)
        }
    }

    @Test fun `no country is enabled`() {
        val importedRecipe = UploadedRecipe.random().copy(sourceId = null)
        val mpsRecipe = UploadedRecipe.random().copy(sourceId = null)
        runBlocking {
            recipeRepository.upsert(importedRecipe, dsl)
            insertRecipeSnapshot(mpsRecipe, LocalDateTime.now().minusMinutes(100))

            val fetched = with(RecipeViewRepository(dsl)) {
                fetch(fetchUpdatedCountryWeek(), setOf())
            }

            assertEquals(1, fetched.size)
            assertEquals(importedRecipe.recipeIndex, fetched.first().recipeIndex)
        }
    }

    @Test fun `Ignore the recipes in RECIPE table if corresponding country is mps enabled`() {
        val importedRecipe = UploadedRecipe.random().copy(sourceId = null)
        val mpsRecipe = UploadedRecipe.random().copy(sourceId = null)
        runBlocking {
            recipeRepository.upsert(importedRecipe, dsl)
            insertRecipeSnapshot(mpsRecipe, LocalDateTime.now().minusMinutes(100))

            val fetched = with(RecipeViewRepository(dsl)) {
                fetch(
                    fetchUpdatedCountryWeek(),
                    setOf(
                        CountryWeek(mpsRecipe.country, mpsRecipe.week),
                        CountryWeek(importedRecipe.country, importedRecipe.week),
                    ),
                )
            }

            assertEquals(1, fetched.size)
            assertEquals(mpsRecipe.recipeIndex, fetched.first().recipeIndex)
        }
    }
}
