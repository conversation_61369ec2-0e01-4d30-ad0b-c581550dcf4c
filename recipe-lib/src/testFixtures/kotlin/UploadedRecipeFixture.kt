@file:Suppress("MagicNumber")

package com.hellofresh.skuDemandForecast.recipelib

import java.util.UUID
import kotlin.random.Random

fun UploadedRecipe.Companion.default() = UploadedRecipe(
    recipeIndex = 1,
    week = "2023-W46",
    family = "test-product-family",
    locale = "DE",
    country = "DE",
    skus = listOf(
        SkuRecipe(
            "DRY-00-90514-5",
            mapOf(1 to 2),
        ),
    ),
    sourceId = null,
    brand = "HF",
)

fun UploadedRecipe.Companion.random() =
    with(Random(System.nanoTime())) {
        UploadedRecipe(
            recipeIndex = nextInt(0, 1000),
            week = "2023-W46",
            family = "test-product-family",
            locale = UUID.randomUUID().toString(),
            country = UUID.randomUUID().toString(),
            skus = listOf(
                SkuRecipe(
                    "DRY-00-90514-5",
                    mapOf(1 to 2),
                ),
            ),
            sourceId = UUID.randomUUID(),
            brand = "HF",
        )
    }
