package com.hellofresh.skuDemandForecast.recipelib

import org.jooq.TableField
import org.jooq.impl.DSL

fun buildCountryWeekCondition(
    countryField: TableField<*, String>,
    weekField: TableField<*, String>,
    countryWeek: List<CountryWeek>,
) = countryWeek.map { (country, week) ->
    countryField.equalIgnoreCase(country)
        .and(weekField.equalIgnoreCase(week))
}.run {
    reduceOrNull { c1, c2 -> c1.or(c2) } ?: DSL.trueCondition()
}
