package com.hellofresh.skuDemandForecast.recipelib

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skudemandforecast.recipelib.schema.public_.tables.Recipe.RECIPE
import com.hellofresh.skudemandforecast.recipelib.schema.public_.tables.records.RecipeRecord
import java.time.LocalDateTime
import kotlinx.coroutines.future.await
import org.jooq.JSONB
import org.jooq.impl.DSL

class RecipeRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : RecipeRepository {
    private val fetchModifiedRecipes = "fetch-modified-recipes"
    private val fetchRecipes = "fetch-recipes"
    private val resetRecipeDirtyBit = "reset-recipe-dirty-bit"
    private val fetchExistingRecipe = "fetch-existing-recipes"
    private val insertRecipe = "upsert-recipes"

    override suspend fun fetch(countryWeek: List<CountryWeek>): List<UploadedRecipe> =
        if (countryWeek.isNotEmpty()) {
            dslContext.withTagName(fetchRecipes)
                .selectFrom(RECIPE)
                .where(
                    buildCountryWeekCondition(
                        RECIPE.COUNTRY,
                        RECIPE.WEEK,
                        countryWeek,
                    ),
                )
                .fetchAsync()
                .await()
                .map(::toUploadedRecipe)
        } else {
            emptyList()
        }

    override suspend fun fetch(index: Int, country: String, week: String, brand: String): UploadedRecipe? =
        dslContext
            .withTagName(fetchExistingRecipe)
            .selectFrom(RECIPE)
            .where(
                DSL.row(RECIPE.RECIPE_INDEX, RECIPE.WEEK, RECIPE.COUNTRY, RECIPE.BRAND)
                    .`in`(DSL.row(index, week, country, brand)),
            )
            .fetchAsync()
            .await()
            .map(::toUploadedRecipe).firstOrNull()

    override suspend fun resetDirtyBit(
        transactionalContext: MetricsDSLContext,
        timestamp: LocalDateTime
    ) {
        transactionalContext.withTagName(resetRecipeDirtyBit)
            .update(RECIPE)
            .set(RECIPE.DIRTY_BIT, false)
            .where(
                RECIPE.DIRTY_BIT.isTrue
                    .and(RECIPE.UPDATED_AT.lt(timestamp)),
            )
            .executeAsync().await()
    }

    override suspend fun fetchUpdatedCountryWeek(): List<CountryWeek> =
        dslContext.withTagName(fetchModifiedRecipes)
            .select(
                RECIPE.COUNTRY,
                RECIPE.WEEK,
            ).distinctOn(RECIPE.COUNTRY, RECIPE.WEEK)
            .from(RECIPE)
            .where(RECIPE.DIRTY_BIT.isTrue)
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    CountryWeek(
                        country = record.get(RECIPE.COUNTRY),
                        week = record.get(RECIPE.WEEK),
                    )
                }
            }.await()

    override suspend fun upsert(uploadedRecipe: UploadedRecipe, tx: MetricsDSLContext) {
        dslContext
            .withTagName(insertRecipe)
            .insertInto(RECIPE)
            .columns(
                RECIPE.RECIPE_INDEX,
                RECIPE.WEEK,
                RECIPE.COUNTRY,
                RECIPE.LOCALE,
                RECIPE.PRODUCT_FAMILY,
                RECIPE.BRAND,
                RECIPE.SOURCE_ID,
                RECIPE.VALUE,
            )
            .values(
                uploadedRecipe.recipeIndex,
                uploadedRecipe.week,
                uploadedRecipe.country,
                uploadedRecipe.locale,
                uploadedRecipe.family,
                uploadedRecipe.brand,
                uploadedRecipe.sourceId,
                JSONB.valueOf(
                    objectMapper.writeValueAsString(toRecipeValue(uploadedRecipe.skus.map { it.toRecipeSku() })),
                ),
            )
            .onDuplicateKeyUpdate()
            .set(RECIPE.RECIPE_INDEX, uploadedRecipe.recipeIndex)
            .set(RECIPE.WEEK, uploadedRecipe.week)
            .set(RECIPE.COUNTRY, uploadedRecipe.country)
            .set(RECIPE.LOCALE, uploadedRecipe.locale)
            .set(RECIPE.PRODUCT_FAMILY, uploadedRecipe.family)
            .set(RECIPE.BRAND, uploadedRecipe.brand)
            .set(RECIPE.SOURCE_ID, uploadedRecipe.sourceId)
            .set(
                RECIPE.VALUE,
                JSONB.valueOf(
                    objectMapper.writeValueAsString(toRecipeValue(uploadedRecipe.skus.map { it.toRecipeSku() })),
                ),
            )
            .set(RECIPE.DIRTY_BIT, true)
            .executeAsync()
            .await()
    }

    private fun toRecipeValue(skus: List<SkuRecipe>) =
        RecipeValue(
            skus.map { sku -> SkuPicks(sku.skuCode, sku.picks.map { Pick(it.key, it.value) }) }
        )

    private fun toUploadedRecipe(record: RecipeRecord) =
        with(record) {
            val recipeValue = objectMapper.readValue<RecipeValue>(value.data())
            UploadedRecipe(
                recipeIndex = recipeIndex,
                week = week,
                family = productFamily,
                locale = locale,
                country = country,
                skus = recipeValue.skus.map { sku ->
                    SkuRecipe(
                        skuCode = sku.skuCode,
                        picks = sku.picks.associateBy({ it.peopleCount }) { it.picks }.toMap(),
                    )
                },
                sourceId = sourceId,
                brand = record.brand,
            )
        }

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
