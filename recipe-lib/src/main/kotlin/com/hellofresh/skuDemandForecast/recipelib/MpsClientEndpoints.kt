package com.hellofresh.skuDemandForecast.recipelib

import com.hellofresh.skuDemandForecast.models.MenuResponse
import com.hellofresh.skuDemandForecast.models.RecipeSnapshots
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Path
import retrofit2.http.Query

interface MpsClientEndpoints {
    @GET("/v2/{market}/picklists")
    fun getPicklistByMarketAndWeeks(
        @Path("market") market: String,
        @Query("weeks") week: Set<String>,
        @Query("brands") brand: String?,
        @Header("Authorization") token: String
    ): Call<RecipeSnapshots>

    @GET("/v2/{market}/menus/{week}")
    fun getMenusByMarketAndWeeks(
        @Path("market") market: String,
        @Path("week") week: String,
        @Header("Authorization") token: String
    ): Call<MenuResponse>
}
