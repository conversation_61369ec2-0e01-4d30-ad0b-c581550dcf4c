package com.hellofresh.skuDemandForecast.recipelib

import java.util.UUID

data class UploadedRecipe(
    val recipeIndex: Int,
    val week: String,
    val family: String,
    val locale: String?,
    val country: String,
    val skus: List<SkuRecipe>,
    val sourceId: UUID?,
    val brand: String,
    val dcCode: String? = null,
) {
    companion object
}

data class SkuRecipe(val skuCode: String, val picks: Map<Int, Int>)
fun SkuRecipe.toRecipeSku() =
    SkuRecipe(
        this.skuCode,
        this.picks
    )
