package com.hellofresh.skuDemandForecast.recipelib

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import java.time.LocalDateTime

interface RecipeRepository {
    suspend fun fetch(countryWeek: List<CountryWeek>): List<UploadedRecipe>
    suspend fun resetDirtyBit(transactionalContext: MetricsDSLContext, timestamp: LocalDateTime)
    suspend fun fetchUpdatedCountryWeek(): List<CountryWeek>
    suspend fun fetch(index: Int, country: String, week: String, brand: String): UploadedRecipe?
    suspend fun upsert(uploadedRecipe: UploadedRecipe, tx: MetricsDSLContext)
}
