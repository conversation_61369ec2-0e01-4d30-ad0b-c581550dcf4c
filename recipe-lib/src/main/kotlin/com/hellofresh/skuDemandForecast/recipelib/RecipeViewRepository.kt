package com.hellofresh.skuDemandForecast.recipelib

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skudemandforecast.recipelib.schema.public_.Tables.RECIPE_VIEW
import com.hellofresh.skudemandforecast.recipelib.schema.public_.tables.records.RecipeViewRecord
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.impl.DSL

private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

class RecipeViewRepository(
    private val dslContext: MetricsDSLContext,
) {
    private val fetchModifiedRecipes = "fetch-modified-recipes-view"
    private val fetchRecipes = "fetch-recipes-view"

    suspend fun fetch(countryWeek: List<CountryWeek>, mpsEnabledCountryWeeks: Set<CountryWeek>): List<UploadedRecipe> =
        if (countryWeek.isNotEmpty()) {
            dslContext.withTagName(fetchRecipes)
                .selectFrom(RECIPE_VIEW)
                .where(
                    DSL.row(RECIPE_VIEW.COUNTRY, RECIPE_VIEW.WEEK, RECIPE_VIEW.SOURCE).`in`(
                        countryWeek.map { (country, week) ->
                            val source = if (mpsEnabledCountryWeeks.contains(
                                    CountryWeek(
                                        country,
                                        week,
                                    ),
                                )
                            ) {
                                "MPS"
                            } else {
                                "RECIPE"
                            }
                            DSL.row(country, week, source)
                        },
                    ),
                )
                .fetchAsync()
                .await()
                .map(::toUploadedRecipe)
        } else {
            emptyList()
        }

    suspend fun fetchUpdatedCountryWeek(): List<CountryWeek> =
        dslContext.withTagName(fetchModifiedRecipes)
            .select(
                RECIPE_VIEW.COUNTRY,
                RECIPE_VIEW.WEEK,
                RECIPE_VIEW.SOURCE,
            ).distinctOn(RECIPE_VIEW.COUNTRY, RECIPE_VIEW.WEEK)
            .from(RECIPE_VIEW)
            .where(
                RECIPE_VIEW.DIRTY_BIT.isTrue,
            )
            .fetchAsync()
            .thenApply {
                it.map { (country, week, _) -> CountryWeek(country, week) }
            }.await()

    companion object : Logging
}

private fun toUploadedRecipe(record: RecipeViewRecord) =
    with(record) {
        val recipeValue = objectMapper.readValue<RecipeValue>(value.data())
        UploadedRecipe(
            recipeIndex = recipeIndex,
            week = week,
            family = productFamily,
            locale = locale,
            country = country,
            skus = recipeValue.skus.map { sku ->
                SkuRecipe(
                    skuCode = sku.skuCode,
                    picks = sku.picks.associateBy({ it.peopleCount }) { it.picks }.toMap(),
                )
            },
            sourceId = sourceId,
            brand = record.brand,
            dcCode = record.dcCode
        )
    }
