package com.hellofresh.skuDemandForecast.recipelib

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.recipelib.schema.public_.Tables.RECIPE_SNAPSHOT
import java.time.LocalDateTime
import kotlinx.coroutines.future.await

class RecipeSnapshotRepository {
    private val resetRecipeDirtyBit = "reset-recipe-snapshot-dirty-bit"
    suspend fun resetDirtyBit(transactionalContext: MetricsDSLContext, timestamp: LocalDateTime) {
        transactionalContext.withTagName(resetRecipeDirtyBit)
            .update(RECIPE_SNAPSHOT)
            .set(RECIPE_SNAPSHOT.DIRTY_BIT, false)
            .where(
                RECIPE_SNAPSHOT.DIRTY_BIT.isTrue
                    .and(RECIPE_SNAPSHOT.UPDATED_AT.lt(timestamp)),
            )
            .executeAsync().await()
    }
}
