package com.hellofresh.skudemandforecast.model.time

import com.google.protobuf.Timestamp
import com.google.protobuf.util.Timestamps
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset

fun Timestamp.toOffsetDateTime(): OffsetDateTime = Instant.ofEpochSecond(
    seconds,
    nanos.toLong()
).atOffset(ZoneOffset.UTC)

fun Timestamp.toOffsetDateTime(zoneId: ZoneId): OffsetDateTime =
    this.toOffsetDateTime().atZoneSameInstant(zoneId).toOffsetDateTime()

fun Timestamp.toNullableOffSetDateTime() =
    if (this.isNotDefaultValues()) this.toOffsetDateTime() else null

fun Timestamp.toNullableOffSetDateTime(zoneId: ZoneId) =
    this.toNullableOffSetDateTime()?.atZoneSameInstant(zoneId)?.toOffsetDateTime()

fun Timestamp.isNotDefaultValues() =
    this != Timestamp.getDefaultInstance() &&
        this != Timestamps.MIN_VALUE &&
        this != Timestamps.MAX_VALUE &&
        this != Timestamps.EPOCH
