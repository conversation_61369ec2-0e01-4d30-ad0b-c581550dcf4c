package com.hellofresh.skuDemandForecast.model.substitution

data class RecipeSkuSubstitution(
    val recipeIndex: Int,
    val week: String,
    val country: String,
    val brand: String,
    val dcCode: String,
    val peopleCount: Int,
    val value: String,
) {
    companion object
}

data class RecipeWithSkuSubstitutionDetail(
    val recipeIndex: Int,
    val week: String,
    val country: String,
    val brand: String,
    val dcCode: String,
    val peopleCount: Int,
    val skuSubstitutions: List<RecipeSubstitutionDetail>,
) {
    companion object
}
