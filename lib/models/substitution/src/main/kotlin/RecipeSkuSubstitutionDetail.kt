package com.hellofresh.skuDemandForecast.model.substitution

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.time.LocalDate
import java.util.UUID

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class RecipeSubstitutionDetail(
    val skuOut: RecipeSkuOutDemand,
    val skuIn: List<RecipeSkuInDemand>,
) {
    companion object
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class RecipeSkuInDemand(
    val skuId: UUID,
    val skuCode: String,
    val parentSkuId: UUID?,
    val parentSkuCode: String?,
    val date: LocalDate,
    val fullDay: Boolean = false,
    val picks: Int,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class RecipeSkuOutDemand(
    val skuId: UUID,
    val skuCode: String,
    val parentSkuId: UUID?,
    val parentSkuCode: String?,
    val fullDay: Boolean = false,
    val picks: Int,
)
