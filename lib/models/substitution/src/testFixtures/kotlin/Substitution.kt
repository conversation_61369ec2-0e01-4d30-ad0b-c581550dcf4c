package com.hellofresh.skuDemandForecast.model.substitution

import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID

internal const val DEFAULT_SKU_OUT_QTY = 50
fun SubstitutionDetail.Companion.random() = SubstitutionDetail(
    skuOut = SkuDemand(
        skuId = UUID.randomUUID(),
        demand = listOf(
            DemandByDate(
                LocalDate.now(),
                DEFAULT_SKU_OUT_QTY,
            ),
        ),
    ),
    skuIn = listOf(
        SkuDemand(
            UUID.randomUUID(),
            listOf(DemandByDate(LocalDate.now(), DEFAULT_SKU_OUT_QTY / 2)),
        ),
        SkuDemand(
            UUID.randomUUID(),
            listOf(DemandByDate(LocalDate.now(), DEFAULT_SKU_OUT_QTY / 2)),
        ),
    ),
)

@Suppress("MagicNumber")
fun Substitution.Companion.default(
    dcCode: String = "DC",
    subOutQty: Int? = 10,
    subOutPicks: Int = 1,
    subInQty: Int? = 20,
    subInPicks: Int = 1,
) = run {
    val skuIdOut = UUID.randomUUID()
    val today = LocalDate.now(UTC)
    Substitution(
        subId = UUID.randomUUID(),
        skuIdOut = skuIdOut,
        fromDate = today,
        toDate = today.plusDays(7),
        dcCode = dcCode,
        substitutionDetail = SubstitutionDetail(
            skuOut = SkuDemand(
                skuId = skuIdOut,
                demand = (0..7L).map { i ->
                    DemandByDate(today.plusDays(i), subOutQty, subOutQty == null)
                },
                picks = subOutPicks,
            ),
            skuIn = listOf(
                SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = (0..7L).map { i ->
                        DemandByDate(today.plusDays(i), subInQty, subInQty == null)
                    },
                    picks = subInPicks,
                ),
            ),
        ),
        reference = "ref",
        authorName = "fixture",
        authorEmail = "fix@fixture",
        lastEdited = OffsetDateTime.now(UTC),
        version = 0,
        sourceId = null,
        disabled = false,
    )
}
