import com.hellofresh.skuDemandForecast.model.substitution.DemandByDate
import com.hellofresh.skuDemandForecast.model.substitution.SkuDemand
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class SubstitutionDetailTest {
    @Test
    fun `should be able to create substitution detail with valid full day flag and picks`() {
        val substitutionDetail = SubstitutionDetail(
            skuOut = SkuDemand(
                skuId = UUID.randomUUID(),
                demand = listOf(
                    DemandByDate(
                        date = LocalDate.now(),
                        qty = null,
                        fullDay = true,
                    ),
                ),
                picks = 1,
            ),
            skuIn = listOf(
                SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(DemandByDate(LocalDate.now(), 10, false)),
                    picks = null,
                ),
                SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(DemandByDate(LocalDate.now(), null, true)),
                    picks = 1,
                ),
            ),
        )
        assertNull(substitutionDetail.skuOut.demand.first().qty)
        assertEquals(1, substitutionDetail.skuOut.picks)
        assertTrue(substitutionDetail.skuOut.demand.first().fullDay)
        assertEquals(2, substitutionDetail.skuIn.size)
    }

    @Test
    fun `should be able to create substitution detail with default full day flag and picks`() {
        val substitutionDetail = SubstitutionDetail(
            skuOut = SkuDemand(
                skuId = UUID.randomUUID(),
                demand = listOf(
                    DemandByDate(
                        date = LocalDate.now(),
                        qty = 20,
                    ),
                ),
            ),
            skuIn = listOf(
                SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(DemandByDate(LocalDate.now(), 10)),
                ),
                SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(DemandByDate(LocalDate.now(), 10)),
                ),
            ),
        )
        assertEquals(20, substitutionDetail.skuOut.demand.first().qty)
        assertNull(substitutionDetail.skuOut.picks)
        assertFalse(substitutionDetail.skuOut.demand.first().fullDay)
        assertEquals(2, substitutionDetail.skuIn.size)
    }

    @Test
    fun `should throw an exception if the picks does not exist when full day is true - skuOut`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = 20,
                            fullDay = true,
                        ),
                    ),
                    picks = null,
                ),
                skuIn = emptyList()
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuOut, then the skuOut picks must exist."
            )
        )
    }

    @Test
    fun `should throw an exception if the full day is selected and the qty exist - skuOut`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = 20,
                            fullDay = true,
                        ),
                    ),
                    picks = 1,
                ),
                skuIn = emptyList()
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuOut date, qty must be null."
            )
        )
    }

    @Test
    fun `should throw an exception if the full day is not selected and the qty does not exist - skuOut`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = null,
                            fullDay = false,
                        ),
                    ),
                    picks = 1,
                ),
                skuIn = emptyList()
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuOut date, qty must be null."
            )
        )
    }

    @Test
    fun `should throw an exception if the picks does not exist when full day is true - skuIn`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = 20,
                            fullDay = false,
                        ),
                    ),
                    picks = null,
                ),
                skuIn = listOf(
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), 10, false)),
                        picks = null,
                    ),
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), 10, true)),
                        picks = null,
                    ),
                ),
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuIn, then the skuIn picks must exist."
            )
        )
    }

    @Test
    fun `should throw an exception if the full day is selected and the qty exist - skuIn`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = 20,
                            fullDay = false,
                        ),
                    ),
                    picks = null,
                ),
                skuIn = listOf(
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), 10, false)),
                        picks = null,
                    ),
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), 10, true)),
                        picks = 1,
                    ),
                ),
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuIn date, qty must be null."
            )
        )
    }

    @Test
    fun `should throw an exception if the full day is not selected and the qty does not exist - skuIn`() {
        val exception = assertThrows<IllegalStateException> {
            SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = UUID.randomUUID(),
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = 20,
                            fullDay = false,
                        ),
                    ),
                    picks = null,
                ),
                skuIn = listOf(
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), null, false)),
                        picks = null,
                    ),
                    SkuDemand(
                        skuId = UUID.randomUUID(),
                        demand = listOf(DemandByDate(LocalDate.now(), 10, true)),
                        picks = 1,
                    ),
                ),
            )
        }
        assertTrue(
            exception.message!!.contains(
                "If there is a full day selected for skuIn date, qty must be null."
            )
        )
    }
}
