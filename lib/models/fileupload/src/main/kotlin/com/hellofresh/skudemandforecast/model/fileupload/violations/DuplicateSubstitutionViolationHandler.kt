package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.SUBSTITUTION

class SubstitutionDuplicateViolationHandler : ViolationHandlersPerFile {

    private data class DuplicateSubstitutionKey(
        val reference: String,
        val dcCode: String,
        val date: String,
        val skuCodeSubOut: String,
        val skuCodeSubIn: String,
    ) {
        constructor(record: Row) : this(
            record[Substitution.REFERENCE_HEADER],
            record[Substitution.DC_HEADER],
            record[Substitution.DATE_HEADER],
            record[Substitution.SKU_CODE_SUB_OUT_HEADER],
            record[Substitution.SKU_CODE_SUB_IN_HEADER],
        )
    }

    private val existingSubstitutionDuplicateSubstitutionKeys = mutableSetOf<DuplicateSubstitutionKey>()

    override fun handle(data: ProcessingUnit): Violation =
        if (data.fileType == SUBSTITUTION) {
            checkSubstitutionKeyViolation(data)
        } else {
            noViolation
        }

    private fun checkSubstitutionKeyViolation(data: ProcessingUnit): Violation {
        val duplicateSubstitutionKey = DuplicateSubstitutionKey(data.record)
        val noSubInKey = duplicateSubstitutionKey.copy(skuCodeSubIn = Substitution.NO_SUBSTITUTION_CODE)

        return if (duplicateSubstitutionKey.skuCodeSubOut == duplicateSubstitutionKey.skuCodeSubIn) {
            DuplicatedSubstitutionSkuInOutCodes(data.record.recordNumber.toInt())
        } else if (existingSubstitutionDuplicateSubstitutionKeys.contains(duplicateSubstitutionKey) || existingSubstitutionDuplicateSubstitutionKeys.contains(
                noSubInKey,
            )
        ) {
            DuplicateSubstitutionViolation(data.record.recordNumber.toInt())
        } else {
            existingSubstitutionDuplicateSubstitutionKeys.add(duplicateSubstitutionKey)
            existingSubstitutionDuplicateSubstitutionKeys.add(noSubInKey)
            noViolation
        }
    }
}

data class DuplicateSubstitutionViolation(
    val lineNumber: Int,
    override val message: String = "Duplicate Substitution"
) : SevereViolation(lineNumber)

data class DuplicatedSubstitutionSkuInOutCodes(
    val lineNumber: Int,
    override val message: String = "Same Sku In and Out substitution"
) : SevereViolation(lineNumber)
