package com.hellofresh.skudemandforecast.model.fileupload

import com.hellofresh.skudemandforecast.model.fileupload.FileType.DEMAND
import com.hellofresh.skudemandforecast.model.fileupload.FileType.RECIPE
import com.hellofresh.skudemandforecast.model.fileupload.FileType.SUBSTITUTION
import com.hellofresh.skudemandforecast.model.fileupload.violations.SevereViolation
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation
import com.hellofresh.skudemandforecast.model.fileupload.violations.WarnViolation

enum class UploadFileType {
    RECIPE, DEMAND, SUBSTITUTION, UNKNOWN
}

sealed interface ProcessFileResult {
    val parsedFile: ParsedFile?
    val dc: List<String>
    val weeks: List<String>
    val violations: List<Violation>

    fun hasSevereViolations(): Boolean

    class Valid(
        override val parsedFile: ParsedFile,
        override val dc: List<String>,
        override val weeks: List<String>,
        override val violations: List<WarnViolation>
    ) : ProcessFileResult {
        override fun hasSevereViolations() = false
    }

    class Invalid(
        override val parsedFile: ParsedFile?,
        override val dc: List<String>,
        override val weeks: List<String>,
        override val violations: List<Violation>,
    ) : ProcessFileResult {

        override fun hasSevereViolations() = violations.any { it is SevereViolation }
    }
}

fun ParsedFile?.toUploadFileType() =
    this?.type?.let {
        when (this.type) {
            DEMAND -> UploadFileType.DEMAND
            RECIPE -> UploadFileType.RECIPE
            SUBSTITUTION -> UploadFileType.SUBSTITUTION
        }
    } ?: UploadFileType.UNKNOWN
