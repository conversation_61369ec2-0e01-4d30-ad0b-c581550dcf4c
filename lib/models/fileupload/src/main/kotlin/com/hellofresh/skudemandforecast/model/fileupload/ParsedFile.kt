package com.hellofresh.skudemandforecast.model.fileupload

import com.hellofresh.skudemandforecast.model.fileupload.FileType.DEMAND
import com.hellofresh.skudemandforecast.model.fileupload.FileType.RECIPE
import com.hellofresh.skudemandforecast.model.fileupload.FileType.SUBSTITUTION
import java.time.LocalDate
import org.apache.commons.csv.CSVRecord

sealed interface ParsedFile {
    val mandatoryColumns: List<String>
    val optionalColumns: Map<String, String?>
    val data: List<Row>
    val type: FileType
    val pKey: List<String>

    val allHeaders: List<String>
    fun data(records: List<CSVRecord>): List<Row> = records
        .map { csvRecord ->
            Row(
                recordNumber = csvRecord.recordNumber + 1,
                mandatory = mandatoryColumns.associateWith { csvRecord[it] },
                optional = optionalColumns.mapValues { (name, default) ->
                    if (csvRecord.isMapped(name) && csvRecord.isSet(name)) {
                        csvRecord[name]?.trim() ?: default
                    } else {
                        default
                    }
                },
            )
        }

    class Recipe(records: List<CSVRecord>) : ParsedFile {
        override val mandatoryColumns = Companion.mandatoryColumns
        override val optionalColumns = Companion.optionalColumns
        override val data = data(records)
        override val type: FileType = RECIPE
        override val allHeaders: List<String> = Companion.allHeaders
        override val pKey: List<String> = listOf(
            INDEX_HEADER,
            WEEK_HEADER,
            FAMILY_HEADER,
            LOCALE_HEADER,
            COUNTRY_HEADER,
        )

        companion object {
            const val WEEK_HEADER: String = "week.value"
            const val COUNTRY_HEADER = "iso2Code.value"
            const val LOCALE_HEADER = "locale"
            const val INDEX_HEADER = "index"
            const val SKU_HEADER = "skus.mapping.sku.value"
            const val FAMILY_HEADER = "family"
            const val NAME_HEADER = "name"
            const val SKU_NAME_HEADER = "skus.mapping.name"
            const val SKU_PICKS1_HEADER = "skus.mapping.picks.1"
            const val SKU_PICKS2_HEADER = "skus.mapping.picks.2"
            const val SKU_PICKS3_HEADER = "skus.mapping.picks.3"
            const val SKU_PICKS4_HEADER = "skus.mapping.picks.4"
            const val SKU_PICKS5_HEADER = "skus.mapping.picks.5"
            const val SKU_PICKS6_HEADER = "skus.mapping.picks.6"

            val optionalColumns = mapOf(
                LOCALE_HEADER to null,
            )
            val allHeaders: List<String> = listOf(
                WEEK_HEADER, COUNTRY_HEADER, FAMILY_HEADER, INDEX_HEADER, NAME_HEADER,
                SKU_HEADER, SKU_NAME_HEADER, SKU_PICKS1_HEADER, SKU_PICKS2_HEADER,
                SKU_PICKS3_HEADER, SKU_PICKS4_HEADER, SKU_PICKS5_HEADER, SKU_PICKS6_HEADER,
                LOCALE_HEADER,
            )

            val mandatoryColumns: List<String> = allHeaders - optionalColumns.keys
            val doubleColumnDataTypes = setOf(
                INDEX_HEADER,
            )
            val intColumnDataTypes = setOf(
                SKU_PICKS1_HEADER,
                SKU_PICKS2_HEADER,
                SKU_PICKS3_HEADER,
                SKU_PICKS4_HEADER,
                SKU_PICKS5_HEADER,
                SKU_PICKS6_HEADER,
            )
        }
    }

    class Demand(records: List<CSVRecord>) : ParsedFile {
        override val mandatoryColumns = Companion.mandatoryColumns
        override val optionalColumns = Companion.optionalColumns
        override val data = data(records)
        override val type: FileType = DEMAND
        override val allHeaders: List<String> = Companion.allHeaders
        override val pKey: List<String> =
            listOf(
                MEAL_NUMBER_HEADER,
                WEEK_HEADER,
                DAY_HEADER,
                PRODUCT_FAMILY_HEADER,
                COUNTRY_HEADER,
                DC_HEADER,
                SERVINGS_HEADER,
                LOCALE_HEADER,
            )

        companion object {
            const val WEEK_HEADER: String = "week"
            const val DC_HEADER: String = "dc"
            const val DAY_HEADER: String = "day"
            const val LOCALE_HEADER: String = "locale"
            const val BOX_NAME_HEADER: String = "box_name"
            const val MEAL_NUMBER_HEADER = "meal_number"
            const val MEALS_TO_DELIVER_HEADER = "meals_to_deliver"
            const val COUNTRY_HEADER = "country"
            const val SIZE_HEADER = "size"
            const val PRODUCT_FAMILY_HEADER = "product_family"
            const val SERVINGS_HEADER = "servings"

            val optionalColumns = mapOf(
                LOCALE_HEADER to null,
                BOX_NAME_HEADER to null,
            )
            val allHeaders: List<String> = listOf(
                WEEK_HEADER, DAY_HEADER, COUNTRY_HEADER, DC_HEADER,
                PRODUCT_FAMILY_HEADER, SERVINGS_HEADER, SIZE_HEADER, MEAL_NUMBER_HEADER, MEALS_TO_DELIVER_HEADER,
                LOCALE_HEADER, BOX_NAME_HEADER,
            )
            val mandatoryColumns: List<String> = allHeaders - optionalColumns.keys
            val doubleColumnDataTypes = setOf(
                MEAL_NUMBER_HEADER,
                MEALS_TO_DELIVER_HEADER,
                SIZE_HEADER,
            )
        }
    }

    class Substitution(records: List<CSVRecord>) : ParsedFile {
        override val mandatoryColumns = Companion.mandatoryColumns
        override val optionalColumns = emptyMap<String, String>()
        override val data = data(records)
        override val type: FileType = SUBSTITUTION
        override val allHeaders: List<String> = Companion.allHeaders
        override val pKey: List<String> =
            listOf(
                REFERENCE_HEADER,
                DC_HEADER,
                DATE_HEADER,
                SKU_CODE_SUB_OUT_HEADER,
                SKU_CODE_SUB_IN_HEADER,
            )

        companion object {

            const val NO_SUBSTITUTION_CODE = "No Substitution"

            const val REFERENCE_HEADER: String = "Reference"
            const val DC_HEADER: String = "DC"
            const val DATE_HEADER: String = "Date"
            const val SKU_CODE_SUB_OUT_HEADER: String = "SKU Code subbed out"
            const val SKU_SUB_OUT_QTY_HEADER: String = "QTY subbed out"
            const val SKU_CODE_SUB_IN_HEADER: String = "SKU Code subbed in"
            const val SKU_SUB_IN_QTY_HEADER: String = "QTY subbed in"

            val allHeaders: List<String> = listOf(
                REFERENCE_HEADER,
                DC_HEADER,
                DATE_HEADER,
                SKU_CODE_SUB_OUT_HEADER,
                SKU_SUB_OUT_QTY_HEADER,
                SKU_CODE_SUB_IN_HEADER,
                SKU_SUB_IN_QTY_HEADER,
            )
            val mandatoryColumns: List<String> = allHeaders
            val doubleColumnDataTypes = setOf(
                SKU_SUB_OUT_QTY_HEADER,
                SKU_SUB_IN_QTY_HEADER,
            )

            fun getDateHeader(row: Row) = kotlin.runCatching { LocalDate.parse(row[DATE_HEADER]) }.getOrNull()
        }
    }
}

data class Row(val recordNumber: Long, val mandatory: Map<String, String>, val optional: Map<String, String?>) {
    val all = mandatory + optional
    operator fun get(name: String) = mandatory[name]!!
}
