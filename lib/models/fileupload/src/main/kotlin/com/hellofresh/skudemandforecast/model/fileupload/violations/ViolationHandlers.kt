@file:Suppress("UnusedClass", "unused")

package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skuDemandForecast.models.Country
import com.hellofresh.skuDemandForecast.models.CountryMarketMapping
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Recipe
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.DEMAND
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.RECIPE
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.SUBSTITUTION
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.UNKNOWN
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.BlankCell
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DecimalNumbers
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DifferentProductFamilyInADemandFile
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DifferentProductFamilyInARecipeFile
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DuplicateRows
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DuplicateSkuInARecipe
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidDataTypeFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidDateFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidDayFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidNumberOfColumns
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidWeekFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.NegativeNumbers
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.UnknownDC
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.UnknownOrUnauthorizedCountryCode
import com.hellofresh.skudemandforecast.model.skuspecification.SkuCodeLookUp
import java.time.DayOfWeek
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.primaryConstructor
import org.apache.logging.log4j.kotlin.Logging

interface ViolationHandler {
    fun handle(data: ProcessingUnit): Violation

    companion object : Logging
}

sealed interface ViolationHandlersPerFile : ViolationHandler {
    companion object {
        fun createInstances(
            dcs: Set<String>,
            market: String,
            skuLookLookUp: SkuCodeLookUp,
            violationsHandlersPerFile: List<ViolationHandlersPerFile> = emptyList()
        ): List<ViolationHandlersPerFile> =
            ViolationHandlersPerFile::class.sealedSubclasses
                .filter { it.primaryConstructor?.parameters?.size ?: 0 == 0 }
                .map { it.objectInstance ?: it.createInstance() }
                .plus(violationsHandlersPerFile)
                .plus(UnknownDcHandler(dcs))
                .plus(UnauthorizedCountryCodeForMarketHandler(market))
                .plus(SkuCodeViolationHandler(skuLookLookUp))
                .plus(DifferentProductFamilyDemandFileHandler())
    }
}

object DataTypeVerificationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val isValid = when (data.fileType) {
            DEMAND -> checkDataType(data, Demand.doubleColumnDataTypes) { it.toDoubleOrNull() }
            RECIPE -> checkDataType(data, Recipe.doubleColumnDataTypes) { it.toDoubleOrNull() } &&
                checkDataType(data, Recipe.intColumnDataTypes) { it.toIntOrNull() }

            SUBSTITUTION -> checkDataType(data, Substitution.doubleColumnDataTypes) { it.toDoubleOrNull() }

            UNKNOWN -> true
        }

        return if (isValid) noViolation else InvalidDataTypeFormat(data.record.recordNumber.toInt())
    }

    private fun checkDataType(data: ProcessingUnit, headers: Set<String>, conversion: (String) -> Number?): Boolean =
        headers.all { header -> conversion(data.record[header]) != null }
}

object NegativeNumbersViolationHandler : ViolationHandlersPerFile {

    @Suppress("UnnecessaryParentheses")
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.all.values.any { (it?.toDoubleOrNull() ?: 0.0) < 0 }) {
            NegativeNumbers(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

data object DecimalNumbersViolationHandler : ViolationHandlersPerFile {

    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.all.values.any { (it?.toDoubleOrNull() ?: 0.0) % 1 != 0.0 }) {
            DecimalNumbers(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

class InvalidNumberOfColumnsViolationHandler : ViolationHandlersPerFile {
    var size: Int? = null
    override fun handle(data: ProcessingUnit): Violation =
        if (size != null && size != data.record.all.size) {
            InvalidNumberOfColumns(data.record.recordNumber.toInt())
        } else {
            size = data.record.all.size
            noViolation
        }
}

object BlankCellViolationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.mandatory.values.any { it.isBlank() }) {
            BlankCell(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

class DuplicateRowsViolationHandler : ViolationHandlersPerFile {
    val records = mutableSetOf<String>()
    override fun handle(data: ProcessingUnit): Violation {
        // CsvRecord's hashcode is based on the record number and the values of the record
        val record = data.record.all.values.joinToString()
        if (records.contains(record)) {
            return DuplicateRows(data.record.recordNumber.toInt())
        } else {
            records.add(record)
        }
        return noViolation
    }
}

object InvalidDayHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation =
        if (data.fileType == DEMAND) {
            val day = data.record[Demand.DAY_HEADER]
            if (DayOfWeek.entries.map { it.name }.contains(day.uppercase())) {
                noViolation
            } else {
                InvalidDayFormat(data.record.recordNumber.toInt())
            }
        } else {
            noViolation
        }
}

object InvalidDateHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation =
        if (data.fileType == SUBSTITUTION) {
            val date = Substitution.getDateHeader(data.record)
            if (date != null) {
                noViolation
            } else {
                InvalidDateFormat(data.record.recordNumber.toInt())
            }
        } else {
            noViolation
        }
}

class DuplicateSkuInARecipeHandler : ViolationHandlersPerFile {

    private data class Key(
        val index: Int,
        val country: String,
        val week: String,
        val locale: String?,
        val sku: String,
    ) {
        constructor(record: Row) : this(
            record[Recipe.INDEX_HEADER].toInt(),
            record[Recipe.COUNTRY_HEADER],
            record[Recipe.WEEK_HEADER],
            record.optional[Recipe.LOCALE_HEADER],
            record[Recipe.SKU_HEADER],
        )
    }

    private val recipeToSku = mutableSetOf<Key>()
    override fun handle(data: ProcessingUnit): Violation {
        if (data.fileType != RECIPE) {
            return noViolation
        }

        return if (!recipeToSku.contains(Key(data.record))) {
            recipeToSku.add(Key(data.record))
            noViolation
        } else {
            DuplicateSkuInARecipe(data.record.recordNumber.toInt())
        }
    }
}

class DifferentProductFamilyDemandFileHandler : ViolationHandlersPerFile {
    private data class Key(
        val index: Int,
        val country: String,
        val week: String,
        val locale: String?
    ) {
        constructor(record: Row) : this(
            record[Demand.MEAL_NUMBER_HEADER].toInt(),
            record[Demand.COUNTRY_HEADER],
            record[Demand.WEEK_HEADER],
            record.optional[Demand.LOCALE_HEADER]
        )
    }

    private val seenRecords = mutableMapOf<Key, Pair<String, Int>>()
    override fun handle(data: ProcessingUnit): Violation {
        if (data.fileType != DEMAND) {
            return noViolation
        }

        val record = data.record
        val key = Key(record)
        val currentFamily = record[Demand.PRODUCT_FAMILY_HEADER]
        val currentLineNumber = record.recordNumber.toInt()

        val existingEntry = seenRecords[key]

        return if (existingEntry != null && existingEntry.first != currentFamily) {
            DifferentProductFamilyInADemandFile(currentLineNumber)
        } else {
            seenRecords[key] = Pair(currentFamily, currentLineNumber)
            noViolation
        }
    }
}

class DifferentProductFamilyRecipeFileHandler : ViolationHandlersPerFile {
    private data class Key(
        val index: Int,
        val country: String,
        val week: String,
        val locale: String?
    ) {
        constructor(record: Row) : this(
            record[Recipe.INDEX_HEADER].toInt(),
            record[Recipe.COUNTRY_HEADER],
            record[Recipe.WEEK_HEADER],
            record.optional[Recipe.LOCALE_HEADER]
        )
    }

    private val seenRecords = mutableMapOf<Key, Pair<String, Int>>()
    override fun handle(data: ProcessingUnit): Violation {
        if (data.fileType != RECIPE) {
            return noViolation
        }

        val record = data.record
        val key = Key(record)
        val currentFamily = record[Recipe.FAMILY_HEADER]
        val currentLineNumber = record.recordNumber.toInt()

        val existingEntry = seenRecords[key]

        return if (existingEntry != null && existingEntry.first != currentFamily) {
            DifferentProductFamilyInARecipeFile(currentLineNumber)
        } else {
            seenRecords[key] = Pair(currentFamily, currentLineNumber)
            noViolation
        }
    }
}

object InvalidWeekHandler : ViolationHandlersPerFile {
    private val weekRegex = Regex("20[0-9]{2}-W[0-9]{2}")
    override fun handle(data: ProcessingUnit): Violation {
        // CsvRecord's hashcode is based on the record number and the values of the record
        val isValid = when (data.fileType) {
            DEMAND -> isValid(demandWeekHeader(data))
            RECIPE -> isValid(recipeWeekHeader(data))
            SUBSTITUTION, UNKNOWN -> true
        }

        return if (isValid) {
            noViolation
        } else {
            InvalidWeekFormat(data.record.recordNumber.toInt())
        }
    }

    fun demandWeekHeader(data: ProcessingUnit) = data.record[Demand.WEEK_HEADER]
    fun recipeWeekHeader(data: ProcessingUnit) = data.record[Recipe.WEEK_HEADER]
    fun isValid(week: String) = weekRegex.matches(week)
}

class UnknownDcHandler(private val dcs: Set<String>) : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val dc = when (data.fileType) {
            DEMAND -> data.record[Demand.DC_HEADER]
            SUBSTITUTION -> data.record[Substitution.DC_HEADER]
            RECIPE, UNKNOWN -> null
        }
        return if (dc != null && (
                dc.isBlank() ||
                    dcs.none { it.equals(dc, ignoreCase = true) }
                )
        ) {
            UnknownDC(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
    }
}

class UnauthorizedCountryCodeForMarketHandler(private val market: String) : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val countryFromFile =
            when (data.fileType) {
                RECIPE -> data.record[Recipe.COUNTRY_HEADER]
                DEMAND -> data.record[Demand.COUNTRY_HEADER]
                SUBSTITUTION, UNKNOWN -> null
            }

        return if (countryFromFile != null &&
            (
                Country.entries.none { it.name == countryFromFile.uppercase() } ||
                    !CountryMarketMapping.mapping[Country(countryFromFile)].equals(market, ignoreCase = true)
                )
        ) {
            UnknownOrUnauthorizedCountryCode(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
    }
}
