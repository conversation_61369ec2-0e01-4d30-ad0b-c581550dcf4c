package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.fileupload.FileType

sealed class ReaderViolation : SevereViolation() {

    abstract override val message: String

    // Headers in upload files not matching reference CSVs
    data class HeadersNotMatchingAnyFileType(
        override val message: String = "Headers not matching any file type",
    ) : ReaderViolation()

    // Missing Headers in upload file
    data class HeadersNotMatching(
        val fileType: FileType,
        val missingHeaders: Set<String>,
    ) : ReaderViolation() {
        override val message: String = "Headers missing for file type: $fileType - Headers: $missingHeaders"
    }

    // Delimiter extraction from upload file headers failed
    data class DelimiterExtractionFailed(
        override val message: String = "Delimiter extraction failed",
    ) : ReaderViolation()

    // File which does not match CSV format is uploaded
    data class NotACsv(
        override val message: String = "Not matching CSV format",
    ) : ReaderViolation()
}
