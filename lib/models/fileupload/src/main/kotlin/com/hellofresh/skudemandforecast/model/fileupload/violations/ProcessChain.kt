package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Recipe
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult.Invalid
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult.Valid
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType
import com.hellofresh.skudemandforecast.model.fileupload.toUploadFileType

internal class ProcessChain(
    private val violationHandlers: List<ViolationHandler>,
    private val dcConfigs: List<DistributionCenter>
) {

    internal fun process(file: ParsedFile, fileName: String): ProcessFileResult {
        val weeks = mutableSetOf<String>()
        val dcs = mutableSetOf<String>()
        val violation = mutableListOf<Violation>()
        val dcConfigMap = dcConfigs.associateBy { it.dcCode }
        file.data.map { record ->

            aggregateDcAndWeeks(file, record, dcs, weeks, dcConfigMap)

            // For a line it doesn't make sense to check further violations
            violationHandlers.forEach { v ->
                val res = v.handle(ProcessingUnit(fileName, file.toUploadFileType(), record))
                if (res !is NoViolation) {
                    violation.add(res)
                    return@map
                } else if (res is WarnViolation) {
                    violation.add(res)
                }
            }
        }

        return createProcessResult(
            parsedFile = file,
            dc = dcs.filter { it.isNotBlank() },
            weeks = weeks.filter { it.isNotBlank() },
            violations = violation
                .groupBy { it.javaClass.simpleName }
                .values
                .map { it.first().add(it) },
        )
    }

    private fun aggregateDcAndWeeks(
        file: ParsedFile,
        record: Row,
        dcs: MutableSet<String>,
        weeks: MutableSet<String>,
        dcConfigMap: Map<String, DistributionCenter>
    ) {
        when (file) {
            is Demand -> {
                dcs.add(record[Demand.DC_HEADER])
                weeks.add(record[Demand.WEEK_HEADER])
            }

            is Recipe -> weeks.add(record[Recipe.WEEK_HEADER])
            is Substitution -> {
                val dc = record[Substitution.DC_HEADER]
                dcs.add(dc)
                dcConfigMap[dc]?.also { dcConfig ->
                    Substitution.getDateHeader(record)?.also { date ->
                        weeks.add(DcWeek(date, dcConfig.productionStart).value)
                    }
                }
            }
        }
    }

    companion object {

        fun createProcessResult(
            parsedFile: ParsedFile,
            dc: List<String>,
            weeks: List<String>,
            violations: List<Violation>,
        ) = if (violations.any { it !is NoViolation }) {
            Invalid(
                parsedFile = parsedFile,
                dc = dc.filter { it.isNotBlank() },
                weeks = weeks.filter { it.isNotBlank() },
                violations = violations,
            )
        } else {
            Valid(
                parsedFile = parsedFile,
                dc = dc.filter { it.isNotBlank() },
                weeks = weeks.filter { it.isNotBlank() },
                violations = violations.filterIsInstance<WarnViolation>().toList(),
            )
        }
    }
}

data class ProcessingUnit(val fileName: String, val fileType: UploadFileType, val record: Row)
