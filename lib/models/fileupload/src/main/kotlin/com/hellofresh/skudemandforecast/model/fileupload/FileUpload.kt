package com.hellofresh.skudemandforecast.model.fileupload

import java.time.OffsetDateTime
import java.util.UUID

data class FileUpload(
    val id: UUID,
    val dcs: Set<String>,
    val market: String,
    val weeks: Set<String>,
    val fileName: String,
    val content: ByteArray,
    val info: List<String>,
    val errors: List<String>,
    val fileSource: FileSource,
    val fileType: FileType,
    val fileStatus: FileStatus,
    val authorName: String?,
    val authorEmail: String,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
) {
    @Suppress("ComplexMethod")
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FileUpload

        if (id != other.id) return false
        if (dcs != other.dcs) return false
        if (market != other.market) return false
        if (weeks != other.weeks) return false
        if (fileName != other.fileName) return false
        if (!content.contentEquals(other.content)) return false
        if (info != other.info) return false
        if (errors != other.errors) return false
        if (fileSource != other.fileSource) return false
        if (fileType != other.fileType) return false
        if (fileStatus != other.fileStatus) return false
        if (authorName != other.authorName) return false
        if (authorEmail != other.authorEmail) return false
        if (createdAt != other.createdAt) return false
        return updatedAt == other.updatedAt
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + dcs.hashCode()
        result = 31 * result + market.hashCode()
        result = 31 * result + weeks.hashCode()
        result = 31 * result + fileName.hashCode()
        result = 31 * result + content.contentHashCode()
        result = 31 * result + info.hashCode()
        result = 31 * result + errors.hashCode()
        result = 31 * result + fileSource.hashCode()
        result = 31 * result + fileType.hashCode()
        result = 31 * result + fileStatus.hashCode()
        result = 31 * result + (authorName?.hashCode() ?: 0)
        result = 31 * result + authorEmail.hashCode()
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + updatedAt.hashCode()
        return result
    }

    companion object
}

enum class FileType {
    RECIPE,
    DEMAND,
    SUBSTITUTION
}

enum class FileSource {
    ORDERING,
    INVENTORY,
    MPS,
    SCO,
    LOCAL_D4
}

enum class FileStatus {
    REJECTED,
    PENDING,
    CONSUMED,
    CREATED,
    CREATED_WITH_ERROR,
    PROCESSED,
    PROCESSED_WITH_ERROR,
    ERROR
}
