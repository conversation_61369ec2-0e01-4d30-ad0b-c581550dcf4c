package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult.Invalid
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult.Valid
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.BlankCell
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DecimalNumbers
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DuplicateRows
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidDataTypeFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidWeekFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.NegativeNumbers
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.junit.jupiter.api.Test

@Suppress("MaxLineLength")
class ProcessChainTest {

    @Test fun `return 1 violation if 1 line has several violations`() {
        val result = ProcessChain(
            listOf(
                NegativeNumbersViolationHandler,
                BlankCellViolationHandler,
                InvalidWeekHandler,
            ),
            emptyList(),
        ).process(
            ParsedFile.Recipe(
                records(
                    """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-45;GB;GB; ;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                    """.trimIndent(),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(1, (result as Invalid).violations.size)
    }

    @Test fun `return violations for each line`() {
        val lines = mapOf(
            NegativeNumbersViolationHandler to "2023-W45;GB;GB;PROTEIN;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
            BlankCellViolationHandler to "2023-W45;GB;GB;    ;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
            InvalidWeekHandler to "2023-45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
            null to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
        )
        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(lines.keys.toList().filterNotNull(), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + lines.values.joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(
            setOf(NegativeNumbers::class, BlankCell::class, InvalidWeekFormat::class),
            (result as Invalid).violations.map { it::class }.toSet(),
        )
    }

    @Test fun `return valid when duplicate rows violations`() {
        val line = "2023-W45;GB;GB;PROTEIN;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0"

        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(listOf(DuplicateRowsViolationHandler()), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + listOf(line, line).joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Valid }
        with(result as Valid) {
            assertEquals(
                setOf(DuplicateRows::class),
                violations.map { it::class }.toSet(),
            )
        }
    }

    @Test fun `return valid when decimal demand violations`() {
        val line = "2023-W45;GB;GB;PROTEIN;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1.5;0;2.5;0;0"

        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(listOf(DecimalNumbersViolationHandler), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + listOf(line),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Valid }
        with(result as Valid) {
            assertEquals(
                setOf(DecimalNumbers::class),
                violations.map { it::class }.toSet(),
            )
        }
    }

    @Test fun `return violations for each line of uploaded recipe file for invalid index`() {
        val lines = mapOf(
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;invalidIndex;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
        )
        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(lines.keys.toList(), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + lines.values.joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(
            InvalidDataTypeFormat::class,
            (result as Invalid).violations.map { it::class }.first(),
        )
    }

    @Test fun `return violations for each line of uploaded recipe file for invalid picks`() {
        val lines = mapOf(
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;invalidPick1;1;0;2;0;0",
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;invalidPick2;0;2;0;0",
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;invalidPick3;2;0;0",
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;invalidPick4;0;0",
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;invalidPick5;0",
            DataTypeVerificationHandler to "2023-W45;GB;GB;PROTEIN;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;invalidPick6",
        )
        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(lines.keys.toList(), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + lines.values.joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(
            InvalidDataTypeFormat::class,
            (result as Invalid).violations.map { it::class }.first(),
        )
    }

    @Test fun `return violations for each line of uploaded demand file for invalid picks`() {
        val lines = mapOf(
            DataTypeVerificationHandler to "2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;invalidSize;21;12",
            DataTypeVerificationHandler to "2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;2;invalidMealNumber;12",
            DataTypeVerificationHandler to "2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;2;21;invalidMealToDeliver",
        )
        val header =
            """week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver"""
        val result = ProcessChain(lines.keys.toList(), emptyList()).process(
            ParsedFile.Demand(
                records(
                    header + '\n' + lines.values.joinToString("\n"),
                    ParsedFile.Demand.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(
            InvalidDataTypeFormat::class,
            (result as Invalid).violations.map { it::class }.first(),
        )
    }

    @Test fun `return invalid result when there are invalid and warning violations`() {
        val line = "2023-W45;GB;GB;PROTEIN;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0"
        val negativeViolation = NegativeNumbersViolationHandler to "2023-W45;GB;GB;PROTEIN;-33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0"

        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(
            listOf(DuplicateRowsViolationHandler(), negativeViolation.first),
            emptyList(),
        ).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + listOf(line, line, negativeViolation).joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        with(result as Invalid) {
            assertEquals(
                setOf(DuplicateRows::class, NegativeNumbers::class),
                violations.map { it::class }.toSet(),
            )
        }
    }

    @Test fun `violations line should also count header`() {
        val lines = mapOf(
            BlankCellViolationHandler to "2023-W45;GB;GB;    ;33;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0",
        )
        val header =
            """week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6"""
        val result = ProcessChain(lines.keys.toList(), emptyList()).process(
            ParsedFile.Recipe(
                records(
                    header + '\n' + lines.values.joinToString("\n"),
                    ParsedFile.Recipe.allHeaders,
                ),
            ),
            "test",
        )

        assertTrue { result is Invalid }
        assertEquals(
            2,
            (result as Invalid).violations.first().lineNumbers.first(),
        )
    }
}

fun records(records: String, headers: List<String>): List<CSVRecord> {
    val csvFormat: CSVFormat = CSVFormat.DEFAULT.builder()
        .setAllowMissingColumnNames(true)
        .setSkipHeaderRecord(true)
        .setDelimiter(';')
        .setHeader(*headers.toTypedArray())
        .setIgnoreHeaderCase(true)
        .setTrim(true)
        .build()

    return CSVParser(records.reader(), csvFormat.builder().build()).records
}
