package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Recipe
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.SUBSTITUTION
import com.hellofresh.skudemandforecast.model.fileupload.toUploadFileType
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.DuplicateSkuInARecipe
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.InvalidDateFormat
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.UnknownDC
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation.UnknownOrUnauthorizedCountryCode
import java.time.LocalDate
import kotlin.test.assertEquals
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ViolationsTest {

    @Test
    fun `date validation handler`() {
        val row1 = Row(1, mapOf(Substitution.DATE_HEADER to LocalDate.now().toString()), emptyMap())
        val row2 = Row(2, mapOf(Substitution.DATE_HEADER to "12-23-1232"), emptyMap())

        val violations = listOf(row1, row2)
            .map {
                InvalidDateHandler.handle(
                    ProcessingUnit("test", SUBSTITUTION, it),
                )
            }

        assertEquals(1, violations.count { it is NoViolation })
        assertEquals(1, violations.count { it is InvalidDateFormat })
        assertEquals(row2.recordNumber, violations.first { it is InvalidDateFormat }.lineNumbers.first().toLong())
    }

    @ParameterizedTest
    @MethodSource("argForDuplicateSkuInARecipe")
    fun testDuplicateSkuInARecipe(
        name: String,
        recordStr: String,
        constructor: (List<CSVRecord>) -> ParsedFile,
        hasViolation: Boolean,
    ) {
        val handler = DuplicateSkuInARecipeHandler()
        val file = constructor(parse(recordStr).records)
        val violations = file.data
            .map {
                handler.handle(
                    ProcessingUnit("test", file.toUploadFileType(), it),
                )
            }

        if (hasViolation) {
            assertTrue(violations.any { it is DuplicateSkuInARecipe }, name)
        } else {
            assertTrue(violations.none { it is DuplicateSkuInARecipe }, name)
        }
    }

    @ParameterizedTest
    @MethodSource("argForUnknownDcTest")
    fun `test unknown DC in a file`(
        name: String,
        recordStr: String,
        constructor: (List<CSVRecord>) -> ParsedFile,
        dcs: Set<String>,
        hasViolation: Boolean,
    ) {
        val handler = UnknownDcHandler(dcs)
        val file = constructor(parse(recordStr).records)
        val violations = file.data
            .map {
                handler.handle(
                    ProcessingUnit("test", file.toUploadFileType(), it),
                )
            }

        if (hasViolation) {
            assertTrue(violations.any { it is UnknownDC }, name)
        } else {
            assertTrue(violations.none { it is UnknownDC }, name)
        }
    }

    @ParameterizedTest
    @MethodSource("argForUnknownCountryTest")
    fun `test unknown country in a file`(
        name: String,
        recordStr: String,
        constructor: (List<CSVRecord>) -> ParsedFile,
        countryCode: String,
        hasViolation: Boolean,
    ) {
        val handler = UnauthorizedCountryCodeForMarketHandler(countryCode)
        val file = constructor(parse(recordStr).records)
        val violations = file.data
            .map {
                handler.handle(
                    ProcessingUnit("test", file.toUploadFileType(), it),
                )
            }

        if (hasViolation) {
            assertTrue(violations.any { it is UnknownOrUnauthorizedCountryCode }, name)
        } else {
            assertTrue(violations.none { it is UnknownOrUnauthorizedCountryCode }, name)
        }
    }

    companion object {

        val csvFormat: CSVFormat = CSVFormat.DEFAULT.builder()
            .setAllowMissingColumnNames(true)
            .setSkipHeaderRecord(true)
            .setDelimiter(';')
            .setIgnoreHeaderCase(true)
            .setTrim(true)
            .build()

        fun parse(records: String) = CSVParser(
            records.reader(),
            csvFormat.builder().setHeader(
                *records.split("\n").first().trim().split(";").toTypedArray(),
            ).build(),
        )

        @Suppress("MaxLineLength")
        @JvmStatic
        fun argForUnknownDcTest() = listOf(
            Arguments.of(
                "All DCs are present",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;DE;BV;DE;mutual-menu;mutual-menu;6;2;21;12

                """.trimIndent(),
                ::Demand,
                setOf("VE", "BV"),
                false,
            ),

            Arguments.of(
                "DC check is case insensitive",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;DE;vE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;DE;bV;DE;mutual-menu;mutual-menu;6;2;21;12

                """.trimIndent(),
                ::Demand,
                setOf("ve", "bv"),
                false,
            ),

            Arguments.of(
                "One of the DC is missing",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;DE;BV;DE;mutual-menu;mutual-menu;6;2;21;12
                """.trimIndent(),
                ::Demand,
                setOf("VE"),
                true,
            ),
        )

        @Suppress("MaxLineLength")
        @JvmStatic
        fun argForUnknownCountryTest() = listOf(
            Arguments.of(
                "Valid country code in demand file",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;DE;VE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;DE;BV;DE;mutual-menu;mutual-menu;6;2;21;12

                """.trimIndent(),
                ::Demand,
                "DACH",
                false,
            ),
            Arguments.of(
                "Valid country code in recipe file",
                """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-W45;GB;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                """.trimIndent(),
                ::Recipe,
                "GB",
                false,
            ),
            Arguments.of(
                "Country code check is case insensitive in demand file",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;DE;vE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;DE;bV;DE;mutual-menu;mutual-menu;6;2;21;12

                """.trimIndent(),
                ::Demand,
                "dach",
                false,
            ),

            Arguments.of(
                "Invalid country code in demand file",
                """
                    week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                    2023-W30;Sunday;BX;VE;DE;mutual-menu;mutual-menu;6;2;21;12
                    2023-W30;Sunday;BX;BV;DE;mutual-menu;mutual-menu;6;2;21;12
                """.trimIndent(),
                ::Demand,
                "DACH",
                true,
            ),
            Arguments.of(
                "Invalid country code in recipe file",
                """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-W45;INVALID;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                """.trimIndent(),
                ::Recipe,
                "GB",
                true,
            ),
        )

        @Suppress("MaxLineLength")
        @JvmStatic
        fun argForDuplicateSkuInARecipe() = listOf(
            Arguments.of(
                "No violation if only 1 line",
                """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-W45;GB;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                """.trimIndent(),
                ::Recipe,
                false,
            ),

            Arguments.of(
                "No violation if recipe is repeated for a different locale",
                """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-W45;GB;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                    2023-W45;GB;IL;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                """.trimIndent(),
                ::Recipe,
                false,
            ),

            Arguments.of(
                "Sku repeated in a recipe",
                """
                    week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                    2023-W45;GB;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;0;1;0;2;0;0
                    2023-W45;GB;GB;classic-box;3;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;1;2;3;2;0;0
                """.trimIndent(),
                ::Recipe,
                true,
            ),
        )
    }
}
