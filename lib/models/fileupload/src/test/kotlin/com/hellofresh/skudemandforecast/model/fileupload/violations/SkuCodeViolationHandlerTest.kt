package com.hellofresh.skudemandforecast.model.fileupload.violations

import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.model.fileupload.UploadFileType.SUBSTITUTION
import com.hellofresh.skudemandforecast.model.skuspecification.SkuCodeLookUp
import com.hellofresh.skudemandforecast.model.skuspecification.SkuSpecification
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class SkuCodeViolationHandlerTest {

    @Test
    fun `sku code validation handler`() {
        val row1 = Row(
            1,
            mapOf(
                Substitution.DC_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_OUT_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_IN_HEADER to UUID.randomUUID().toString(),
            ),
            emptyMap(),
        )
        val row2 = Row(
            2,
            mapOf(
                Substitution.DC_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_OUT_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_IN_HEADER to UUID.randomUUID().toString(),
            ),
            emptyMap(),
        )

        val skuCodeLookUp = mockk<SkuCodeLookUp>()

        every {
            skuCodeLookUp.getSkuId(row1[Substitution.DC_HEADER], row1[Substitution.SKU_CODE_SUB_OUT_HEADER])
        } returns mockk<SkuSpecification>()
        every {
            skuCodeLookUp.getSkuId(row1[Substitution.DC_HEADER], row1[Substitution.SKU_CODE_SUB_IN_HEADER])
        } returns mockk<SkuSpecification>()

        every {
            skuCodeLookUp.getSkuId(row2[Substitution.DC_HEADER], row2[Substitution.SKU_CODE_SUB_OUT_HEADER])
        } returns mockk<SkuSpecification>()
        every { skuCodeLookUp.getSkuId(row2[Substitution.DC_HEADER], row2[Substitution.SKU_CODE_SUB_IN_HEADER]) } returns null

        val violations = listOf(row1, row2)
            .map {
                SkuCodeViolationHandler(skuCodeLookUp).handle(
                    ProcessingUnit("test", SUBSTITUTION, it),
                )
            }

        assertEquals(1, violations.count { it is NoViolation })
        assertEquals(1, violations.count { it is UnknownSkuCode })
        assertEquals(
            row2.recordNumber,
            violations.filterIsInstance<UnknownSkuCode>().first().lineNumbers.first().toLong(),
        )
    }

    @Test
    fun `no substitution for sku in code is accepted`() {
        val row = Row(
            1,
            mapOf(
                Substitution.DC_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_OUT_HEADER to UUID.randomUUID().toString(),
                Substitution.SKU_CODE_SUB_IN_HEADER to Substitution.NO_SUBSTITUTION_CODE,
            ),
            emptyMap(),
        )

        val skuCodeLookUp = mockk<SkuCodeLookUp>()

        every {
            skuCodeLookUp.getSkuId(row[Substitution.DC_HEADER], row[Substitution.SKU_CODE_SUB_OUT_HEADER])
        } returns mockk<SkuSpecification>()

        val violations = listOf(row)
            .map {
                SkuCodeViolationHandler(skuCodeLookUp).handle(
                    ProcessingUnit("test", SUBSTITUTION, it),
                )
            }

        assertEquals(1, violations.count { it is NoViolation })
    }
}
