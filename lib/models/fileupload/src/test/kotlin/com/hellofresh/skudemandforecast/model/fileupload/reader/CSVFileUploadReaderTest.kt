package com.hellofresh.skudemandforecast.model.fileupload.reader

import com.hellofresh.skudemandforecast.model.fileupload.FileType
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Substitution
import com.hellofresh.skudemandforecast.model.fileupload.violations.ReaderViolation.HeadersNotMatching
import java.io.File
import java.nio.file.Files
import java.util.Random
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class CSVFileUploadReaderTest {

    @Test
    fun `returns accepted while processing file succefully`() {
        val forecast = readFileContent("fileuploads/forecastfile.csv")
        val recipe = readFileContent("fileuploads/recipedetails.csv")

        with(CSVFileUploadReader.toCsvRecords(forecast, null)) {
            assertEquals(FileType.DEMAND, parsedFile?.type)
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertEquals(2, parsedFile?.data?.minOfOrNull { it.recordNumber })
            assertTrue(errors.isEmpty())
        }
        with(CSVFileUploadReader.toCsvRecords(recipe, null)) {
            assertEquals(FileType.RECIPE, parsedFile?.type)
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertEquals(2, parsedFile?.data?.minOfOrNull { it.recordNumber })
            assertTrue(errors.isEmpty())
        }
    }

    @Test
    fun `returns accepted regardless of the file header order`() {
        val demand = readFileContent("fileuploads/forecastfile_different_header_order.csv")

        with(CSVFileUploadReader.toCsvRecords(demand, null)) {
            assertEquals(FileType.DEMAND, parsedFile?.type)
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertTrue(errors.isEmpty())
        }
    }

    @Test
    fun `returns accepted while processing file succefully with known file type`() {
        val forecast = readFileContent("fileuploads/forecastfile.csv")

        with(CSVFileUploadReader.toCsvRecords(forecast, FileType.DEMAND)) {
            assertEquals(FileType.DEMAND, parsedFile?.type)
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertEquals(2, parsedFile?.data?.minOfOrNull { it.recordNumber })
            assertTrue(errors.isEmpty())
        }
    }

    @Test
    fun `returns bad request if filetype doesnt match headers`() {
        val demand = readFileContent("fileuploads/forecastfile.csv")

        with(CSVFileUploadReader.toCsvRecords(demand, FileType.RECIPE)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `return bad request if file type is unknown or corrupted`() {
        val badContent = ByteArray(10).apply { Random().nextBytes(this) }
        with(CSVFileUploadReader.toCsvRecords(badContent, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `return bad request if file type has wrong delimiter`() {
        val recipe = readFileContent("fileuploads/recipedetails_wrong_delimiter.csv")
        with(CSVFileUploadReader.toCsvRecords(recipe, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
        val substitution = readFileContent("fileuploads/substitutions_wrong_delimiter.csv")
        with(CSVFileUploadReader.toCsvRecords(substitution, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `return missing headers error for known file type`() {
        // Missing week, dc
        with(
            CSVFileUploadReader.toCsvRecords(
                """
            day;country;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
                """.trimIndent().toByteArray(),
                FileType.DEMAND,
            ),
        ) {
            assertNull(parsedFile)
            assertTrue(errors.first() is HeadersNotMatching)
            assertTrue(errors.first().message.contains(FileType.DEMAND.name))
            assertTrue(errors.first().message.contains("dc"))
            assertTrue(errors.first().message.contains("week"))
        }
    }

    @Test
    fun `return missing headers error for best match file type`() {
        // Missing family;index;skus.mapping.sku.value;
        with(
            CSVFileUploadReader.toCsvRecords(
                """
                week.value;iso2Code.value;locale;name;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
                """.trimIndent().toByteArray(),
                null,
            ),
        ) {
            assertNull(parsedFile)
            assertTrue(errors.first() is HeadersNotMatching)
            assertTrue(errors.first().message.contains("family"))
            assertTrue(errors.first().message.contains("index"))
            assertTrue(errors.first().message.contains("skus.mapping.sku.value"))
        }
    }

    @Test
    fun `return bad request if file type has wrong headers`() {
        val forecast = readFileContent("fileuploads/forecastfile_wrong_headers.csv")
        val recipe = readFileContent("fileuploads/recipedetails_wrong_headers.csv")
        val substitutions = readFileContent("fileuploads/substitutions_wrong_headers.csv")

        with(CSVFileUploadReader.toCsvRecords(forecast, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
        with(CSVFileUploadReader.toCsvRecords(recipe, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
        with(CSVFileUploadReader.toCsvRecords(substitutions, null)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `returns accepted while processing substitution file succefully`() {
        val substitution = readFileContent("fileuploads/substitutions.csv")

        with(CSVFileUploadReader.toCsvRecords(substitution, null)) {
            assertEquals(FileType.SUBSTITUTION, parsedFile?.type)
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertEquals(3, parsedFile?.data?.count())
            with(parsedFile?.data?.first { it.recordNumber == 2L }!!) {
                assertEquals("2024-09-19", all[Substitution.DATE_HEADER])
                assertEquals("supplier cannot deliver", all[Substitution.REFERENCE_HEADER])
                assertEquals("SPI-00-12112-5", all[Substitution.SKU_CODE_SUB_OUT_HEADER])
                assertEquals("10", all[Substitution.SKU_SUB_OUT_QTY_HEADER])
                assertEquals("SPI-00-12111-5", all[Substitution.SKU_CODE_SUB_IN_HEADER])
                assertEquals("30", all[Substitution.SKU_SUB_IN_QTY_HEADER])
            }
            with(parsedFile?.data?.first { it.recordNumber == 3L }!!) {
                assertEquals("2024-09-20", all[Substitution.DATE_HEADER])
                assertEquals("supplier cannot deliver", all[Substitution.REFERENCE_HEADER])
            }
            with(parsedFile?.data?.first { it.recordNumber == 4L }!!) {
                assertEquals("2024-09-19", all[Substitution.DATE_HEADER])
                assertEquals("Jupiter #6798", all[Substitution.REFERENCE_HEADER])
            }
            assertTrue(errors.isEmpty())
        }
    }

    private fun readFileContent(fileName: String): ByteArray =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            Files.readAllBytes(it.toPath())
        }
}
