package com.hellofresh.skudemandforecast.model.fileupload

import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import java.util.UUID
import kotlin.random.Random
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser

@Suppress("MagicNumber", "SpreadOperator")
fun Demand.Companion.generateRandom(size: Int): Demand = with(Random(seed = System.nanoTime())) {
    val records = (0 until size).map { _ ->
        val data = mapOf(
            WEEK_HEADER to "2024-W${nextInt(9)}${nextInt(9)}",
            DAY_HEADER to "MONDAY",
            COUNTRY_HEADER to "DE",
            DC_HEADER to "VE",
            PRODUCT_FAMILY_HEADER to UUID.randomUUID().toString(),
            SERVINGS_HEADER to "${nextInt(10)}",
            SIZE_HEADER to "${nextInt(10)}",
            MEAL_NUMBER_HEADER to "${nextInt(40)}",
            MEALS_TO_DELIVER_HEADER to "${nextInt(10000)}",
            LOCALE_HEADER to "DE",
            BOX_NAME_HEADER to UUID.randomUUID().toString(),
        )

        CSVParser.parse(
            data.values.joinToString(separator = ","),
            CSVFormat.Builder.create()
                .setHeader(*data.keys.toTypedArray())
                .setDelimiter(',')
                .build(),
        )
            .getRecords()[0]
    }
    return Demand(records)
}
