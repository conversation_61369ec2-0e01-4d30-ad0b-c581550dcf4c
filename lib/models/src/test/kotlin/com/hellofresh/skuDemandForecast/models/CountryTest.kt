package com.hellofresh.skuDemandForecast.models

import com.hellofresh.skuDemandForecast.models.Country.DE
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFails

internal class CountryTest {
    @Test fun `valid strings are translated to valid country`() {
        assertEquals(DE, Country("DE"))
    }

    @Test fun `valid lowercase strings are translated to valid country`() {
        assertEquals(DE, Country("de"))
    }

    @Test fun `unknown strings are translated to UnknownDc`() {
        assertFails { Country("XZ") }
    }
}
