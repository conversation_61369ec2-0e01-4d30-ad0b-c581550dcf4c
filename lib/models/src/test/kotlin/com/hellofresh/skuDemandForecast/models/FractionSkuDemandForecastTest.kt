package com.hellofresh.skuDemandForecast.models

import kotlin.random.Random
import kotlin.test.Test
import kotlin.test.assertEquals

class FractionSkuDemandForecastTest {

    @Test fun `Qty sum adds correctly`() = with(Random) {
        val val1 = nextInt(0, Int.MAX_VALUE / 2)
        val val2 = nextInt(0, Int.MAX_VALUE / 2)
        val qty1 = Qty(val1)
        val qty2 = Qty(val2)

        assertEquals(Qty(val1 + val2), qty1 + qty2)
    }
}
