package com.hellofresh.skuDemandForecast.models

import java.time.Instant
import kotlin.test.assertEquals
import org.apache.kafka.streams.processor.api.Record
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class KafkaExtTest {
    @Test fun `instantTimestamp() gives exception if timestamp is in seconds`() {
        val r = Record("key", "value", Instant.now().epochSecond)
        assertThrows<IllegalStateException> { r.instantTimestamp() }
    }

    @Test fun `instantTimestamp() gives correct time in Instant`() {
        val expected = Instant.now().toEpochMilli()
        val r = Record("key", "value", expected)
        // We can't compare Instant as Kafka's lib has different resolution(no nanos)
        assertEquals(expected, r.instantTimestamp().toEpochMilli())
    }
}
