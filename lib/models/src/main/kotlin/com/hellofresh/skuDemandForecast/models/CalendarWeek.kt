package com.hellofresh.skuDemandForecast.models

import com.fasterxml.jackson.annotation.JsonValue
import java.time.LocalDate
import java.time.temporal.WeekFields

/**
 * CalendarWeek follows the ISO-8601 definition, where a week starts on Monday
 * and the first week has a minimum of 4 days.
 *
 * The string representation of a the week follows HelloFresh standards
 * (${year}-W${week}), e.g.: 2021-W22.
 *
 * @see https://en.wikipedia.org/wiki/ISO_week_date
 */
data class CalendarWeek(private val date: LocalDate) : Comparable<CalendarWeek> {

    private val weekFields = WeekFields.ISO
    val weekNo = date[weekFields.weekOfYear()]
    val year = date[weekFields.weekBasedYear()]
    private val value = "$year-W${"%02d".format(weekNo)}"

    /**
     * Returns the next calendar week based on the current calendar week.
     */
    fun next() = CalendarWeek(date.plusWeeks(1))

    /**
     * Returns the next [n] calendar week based on the current calendar week.
     */
    fun next(n: Long) = (0 until n).map { CalendarWeek(date.plusWeeks(it)) }

    /**
     * Returns the next [n] calendar week based on the current calendar week.
     */
    fun next(n: Int) = (0 until n).map { CalendarWeek(date.plusWeeks(it.toLong())) }

    @JsonValue
    fun value() = value

    override fun compareTo(other: CalendarWeek) = this.date.compareTo(other.date)

    companion object {

        /**
         * Returns the [CalendarWeek] for LocalDate.now().
         */
        fun now() = CalendarWeek(LocalDate.now())

        /**
         * Returns the next [n] calendar weeks based starting from [now].
         */
        fun next(n: Int) = now().next(n)

        /**
         * Tries to construct CalendarWeek from a random string.
         */
        fun extractFromString(s: String): CalendarWeek? {
            val localDate = localDateFromYearWeek(s)
            return if (localDate != null) {
                CalendarWeek(localDate)
            } else {
                null
            }
        }
    }
}
