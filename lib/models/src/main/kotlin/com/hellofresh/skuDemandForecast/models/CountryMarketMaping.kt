package com.hellofresh.skuDemandForecast.models

private const val DACH = "DACH"
private const val BENELUXFR = "BENELUXFR"
private const val DKSE = "DKSE"
const val UK_MARKET = "GB"

object CountryMarketMapping {

    val mapping: Map<Country, String> = mapOf(
        Country.DE to DACH,
        Country.CH to DACH,
        Country.NL to BENELUXFR,
        Country.BE to BENELUXFR,
        Country.LU to BENELUXFR,
        Country.FR to "FR",
        Country.IT to "IT",
        Country.GB to UK_MARKET,
        Country.CA to "CA",
        Country.US to "US",
        Country.DK to DKSE,
        Country.SE to DKSE,
        Country.JP to "JP",
        Country.IE to "IE",
        Country.ES to "ES",
        Country.NZ to "NZ",
        Country.AU to "AU",
    )
}

/**
 * Only DACH dc's are added here.
 */
object DcToCountryMapper {
    val country: Map<String, String> = mapOf(
        "AT" to "AT",
        "KO" to "CH",
        "SB" to "DE",
        "BY" to "DE",
        "EK" to "DE",
        "CR" to "DE",
        "XC" to "DE",
        "FB" to "DE",
        "NI" to "DE",
        "BN" to "DE",
        "BX" to "DE",
        "MD" to "DE",
        "CH" to "DE",
        "VB" to "DE",
        "TZ" to "DE",
        "NG" to "DE",
        "VE" to "DE",
        "VF" to "DE",
        "SS" to "DE",
    )
}
