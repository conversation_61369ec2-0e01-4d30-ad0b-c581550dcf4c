package com.hellofresh.skuDemandForecast.models

import com.google.type.Date
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters.nextOrSame
import java.time.temporal.WeekFields
import kotlin.text.RegexOption.IGNORE_CASE

fun calculateDate(
    productionWeekStr: String,
    productionStart: DayOfWeek,
    dayOfWeek: DayOfWeek,
    currentWeekProduction: Boolean
): LocalDate? {
    val productionWeek = localDateFromYearWeek(productionWeekStr)
    if (productionWeek != null) {
        return calculateDate(productionWeek, productionStart, dayOfWeek, currentWeekProduction)
    }
    return null
}

fun localDateFromYearWeek(yearWeek: String): LocalDate? = weekRE.matchEntire(yearWeek)?.groupValues?.let {
    LocalDate.now()
        .withYear(it[1].toInt())
        .with(WeekFields.ISO.weekOfYear(), it[2].toLong())
}

fun productionWeekFromYearWeek(yearWeek: String): ProductionWeek? = weekRE.matchEntire(yearWeek)?.groupValues?.let {
    ProductionWeek(
        it[1].toInt(),
        it[2].toInt(),
    )
}

internal val weekRE = """.*(\d{4})-W(\d{2}).*""".toRegex(IGNORE_CASE)

private fun calculateDate(
    productionWeek: LocalDate,
    productionStart: DayOfWeek,
    dayOfWeek: DayOfWeek,
    currentWeekProduction: Boolean
): LocalDate {
    val productionStartDate = if (currentWeekProduction) {
        productionWeek.with(productionStart)
    } else {
        productionWeek.minusWeeks(1).with(productionStart)
    }
    return productionStartDate.with(nextOrSame(dayOfWeek))
}

fun LocalDate.protoDate(): Date = Date.newBuilder()
    .setYear(year)
    .setMonth(monthValue)
    .setDay(dayOfMonth)
    .build()
