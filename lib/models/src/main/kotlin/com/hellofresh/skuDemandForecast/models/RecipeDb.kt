package com.hellofresh.skuDemandForecast.models.db

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class RecipeValue(val skus: List<SkuPicks>) {
    companion object
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class SkuPicks(
    val skuCode: String,
    val picks: List<Pick>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class Pick(
    val peopleCount: Int,
    val picks: Int,
) {
    companion object
}
