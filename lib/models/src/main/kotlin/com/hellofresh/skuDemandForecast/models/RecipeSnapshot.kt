package com.hellofresh.skuDemandForecast.models

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

data class RecipeSnapshots(
    val items: List<RecipeSnapshot>
)

@JsonNaming(SnakeCaseStrategy::class)
data class RecipeSnapshot(
    val boxType: String,
    val country: String,
    val locale: String?,
    val dcs: List<String>?,
    val p1: Int,
    val p2: Int,
    val p3: Int,
    val p4: Int,
    val p5: Int,
    val p6: Int,
    val skuCode: String,
    val skuName: String,
    val slotItemName: String,
    val slotNumber: Int,
    val week: String
) {
    companion object
}
