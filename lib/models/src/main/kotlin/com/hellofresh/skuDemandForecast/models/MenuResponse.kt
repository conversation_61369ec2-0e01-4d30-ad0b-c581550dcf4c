package com.hellofresh.skuDemandForecast.models

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

data class MenuResponse(
    val menus: List<Menu>
)

data class Menu(
    val region: String,
    val slots: List<Slot>
)

@JsonNaming(SnakeCaseStrategy::class)
data class Slot(
    val position: Int,
    val itemId: String?,
)
