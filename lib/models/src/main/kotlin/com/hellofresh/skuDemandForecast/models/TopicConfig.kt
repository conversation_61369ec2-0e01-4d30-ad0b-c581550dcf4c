package com.hellofresh.skuDemandForecast.models

import com.hellofresh.service.Config

class TopicConfig private constructor() {
    companion object {
        private val configMap = Config().load("topic-versions")

        fun getVersionedTopic(name: String): String = "$name.v${getTopicVersion(name)}"

        fun getTopicVersion(name: String): Int {
            val topicVersionStr = configMap[name]
            require(
                topicVersionStr != null,
            ) { "Topic $name requires a version. Add it to topic-versions.properties first." }
            val topicVersionInt = topicVersionStr.toString().toIntOrNull()
            require(topicVersionInt != null) { "Version $topicVersionStr cannot be cast to Int for topic $name" }
            return topicVersionInt
        }
    }
}
