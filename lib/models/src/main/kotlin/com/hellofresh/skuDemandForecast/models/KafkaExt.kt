package com.hellofresh.skuDemandForecast.models

import java.time.Instant
import org.apache.kafka.streams.processor.api.Record

/**
 * Returns [Record] timestamp as [Instant].
 *
 * The function throws [IllegalStateException] if the timestamp is determined
 * to be in seconds instead of milliseconds. The check uses the fact that the
 * timestamp which is lower than a tenth of the current timestamp is more than
 * 40 years old. Only a timestamp in seconds could have such lower value in our
 * business domain.
 */
@Suppress("MagicNumber")
fun <K, V> Record<K, V>.instantTimestamp(): Instant = this.timestamp().let {
    check(it > Instant.now().toEpochMilli() / 10) {
        "Record timestamp is in seconds: $it, key: ${this.key()}"
    }

    Instant.ofEpochMilli(it)
}
