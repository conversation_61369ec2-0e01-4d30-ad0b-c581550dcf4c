package com.hellofresh.skuDemandForecast.lib

import com.hellofresh.metrics.HelloFreshMeterRegistry
import com.hellofresh.sdf.checks.CheckResult
import com.hellofresh.sdf.checks.HealthChecks
import com.hellofresh.sdf.checks.StartUpChecks
import com.hellofresh.service.Config
import com.hellofresh.service.MetricsServer
import com.hellofresh.service.UncaughtException
import com.hellofresh.service.toDuration
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.prometheus.PrometheusMeterRegistry
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.system.exitProcess
import org.apache.logging.log4j.kotlin.Logging

private val applicationLock = AtomicBoolean(false)
private const val TRIBE_NAME = "procurement"

open class Application(
    val config: Config,
    meterRegistry: MeterRegistry? = null,
) : Logging {
    val meterRegistry = meterRegistry ?: HelloFreshMeterRegistry(
        "${TRIBE_NAME}_${config["project.name"]}",
        config["application.name"],
        config["prometheus.descriptions"].toBoolean(),
        config["prometheus.scrapeInterval"].toDuration(),
    ).also {
        with({ CheckResult("MeterRegistry", !it.isClosed) }) {
            StartUpChecks.add(this)
            HealthChecks.add(this)
        }
    }

    init {
        // Uncaught exceptions in child threads are by default printed and the
        // machinery continues. This is not good enough for us since we are
        // running many threads and any uncaught exception in any of them means
        // that something went terribly wrong, hence, we rethrow it in the main
        // thread so that the application can die.
        Thread.setDefaultUncaughtExceptionHandler(UncaughtException.Handler)
    }

    /** Start a [MetricsServer] in the background. */
    fun runStatusServer(): MetricsServer? {
        if (config.getOrNull("statusServer.enabled")?.toBoolean() == false) {
            return null
        }

        return if (meterRegistry is PrometheusMeterRegistry) {
            MetricsServer(config, meterRegistry::scrape)
        } else {
            logger.warn("No metrics server will be started because meter registry is not a Prometheus meter registry")
            null
        }
    }

    /** Starts the [application]. */
    inline fun <R> run(application: () -> R): R =
        try {
            checkLock()
            application()
        } catch (all: Throwable) {
            logger.error(all)
            exitProcess(1)
        }

    /**
     * Checks the [applicationLock] and ensures that it's not already locked.
     */
    @PublishedApi
    internal fun checkLock() {
        check(applicationLock.compareAndSet(false, true)) {
            "Another application is already running, only one application can be executed at once."
        }
    }
}

/**
 * Construct new [Application] instance and bootstrap the system, afterwards
 * execute the given [block].
 */
inline fun runApplication(
    config: Config = Config.load(),
    meterRegistry: MeterRegistry? = null,
    block: Application.() -> Unit,
) {
    Application(config, meterRegistry).apply(block)
}
