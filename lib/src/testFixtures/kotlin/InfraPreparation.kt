@file:Suppress("StringLiteralDuplication")

import com.hellofresh.skuDemandForecast.db.DatabaseConfig
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import javax.sql.DataSource
import org.flywaydb.core.Flyway
import org.postgresql.Driver
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName

private const val POSTGRES_IMAGE = "repo.tools-k8s.hellofresh.io/postgres:15.3-alpine"

object InfraPreparation {
    private const val DB_USERNAME = "sdf"
    private const val DB_NAME = "sdf"
    private const val DB_PASSWORD = "123456"

    private val network = Network.newNetwork()
    private var postgresImage = DockerImageName
        .parse(POSTGRES_IMAGE)
        .asCompatibleSubstituteFor("postgres")
    private val postgres = PostgreSQLContainer(postgresImage)
        .withDatabaseName(DB_NAME)
        .withUsername(DB_USERNAME)
        .withPassword(DB_PASSWORD)
        .withNetwork(network)

    private lateinit var dataSource: DataSource

    fun startPostgresAndRunMigrations(
        nestedFolderCount: Int = 1
    ): PostgreSQLContainer<*> {
        postgres.start()
        Flyway.configure()
            .dataSource(postgres.jdbcUrl, postgres.username, postgres.password)
            .locations(
                "filesystem:${(1..nestedFolderCount).joinToString(
                    "/"
                ) { ".." }}/sku-demand-forecast-db/src/main/resources/db/migration/schema"
            )
            .placeholders(
                mapOf(
                    "sdf" to "'123456'",
                    "DB_OUTBOX_USERNAME" to "sdf_outbox",
                    "DB_OUTBOX_PASSWORD" to "'123456'",
                    "DB_USERNAME" to "sdf",
                    "DB_PASSWORD" to "'123456'",
                ),
            )
            .load()
            .migrate()
        return postgres
    }

    fun startPostgres(): PostgreSQLContainer<*> {
        if (!postgres.isRunning) {
            postgres.start()
        }
        return postgres
    }

    fun getMigratedDataSource(
        schema: String = "public",
        nestedFolderCount: Int = 1
    ): DataSource {
        if (!postgres.isRunning) {
            startPostgresAndRunMigrations(nestedFolderCount)
            dataSource = getDataSource(schema)
        }
        return dataSource
    }

    fun getDataSourceConfig(
        nestedFolderCount: Int = 1
    ): DatabaseConfig {
        if (!postgres.isRunning) {
            startPostgresAndRunMigrations(nestedFolderCount)
        }
        return DatabaseConfig(
            "testContainer",
            "${postgres.host}:${postgres.firstMappedPort}",
            postgres.username,
            postgres.password,
        )
    }

    fun getDataSource(schema: String = "public"): DataSource = HikariConfig()
        .also {
            it.jdbcUrl = postgres.jdbcUrl
            it.username = postgres.username
            it.password = postgres.password
            it.schema = schema
            it.driverClassName = Driver::class.java.name
        }.let { HikariDataSource(it) }
}
