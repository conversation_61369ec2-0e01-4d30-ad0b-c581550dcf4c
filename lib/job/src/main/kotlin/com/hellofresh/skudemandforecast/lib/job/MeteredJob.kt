package com.hellofresh.skudemandforecast.lib.job

import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Timer
import java.time.Duration
import java.time.Instant
import org.apache.logging.log4j.kotlin.Logging

class MeteredJob(meterRegistry: MeterRegistry, private val jobName: String, private val fn: suspend () -> Unit) {

    private val jobDuration = Timer.builder("import_duration")
        .description("Record the elapsed time of the execution of the job")
        .tags(tags(jobName))
        .publishPercentileHistogram()
        .register(meterRegistry)

    private val successMetrics = Counter.builder("component_success")
        .description("count of successful runs of a job")
        .tags(tags(jobName))
        .register(meterRegistry)

    private val failureMetrics = Counter.builder("component_failure")
        .description("count of failed runs of a job")
        .tags(tags(jobName))
        .register(meterRegistry)

    suspend fun execute() {
        kotlin.runCatching { recordSuspended(jobDuration, fn) }
            .onSuccess { successMetrics.increment() }
            .onFailure {
                logger.error("Error in $jobName job", it)
                failureMetrics.increment()
            }
    }

    private suspend fun <T> recordSuspended(timer: Timer, fn: suspend () -> T): T {
        val startTime = Instant.now()
        return try {
            logger.info("Starting $jobName.")
            fn()
        } finally {
            logger.info("Finished $jobName.")
            timer.record(Duration.between(startTime, Instant.now()))
        }
    }

    companion object : Logging {
        fun tags(jobName: String) = listOf(Tag.of("name", jobName))
    }
}
