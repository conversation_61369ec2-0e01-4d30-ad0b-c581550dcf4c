package com.hellofresh.sdf.checks

import com.hellofresh.sdf.shutdown.shutdownNeeded
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.newFixedThreadPoolContext
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

val StartUpChecks = Checks("startUpProbe")
val HealthChecks = Checks("healthChecks")

fun interface HealthCheck : Check {
    suspend fun isHealthy(): CheckResult
    override suspend fun check() = isHealthy()
}

fun interface Check {
    suspend fun check(): CheckResult
}

data class CheckResult(val name: String, val result: Boolean) {

    companion object {
        fun failed(name: String) = CheckResult(name, false)
    }
}

private const val MAX_PARALLELISM = 3

class Checks(private val name: String, parallelism: Int = MAX_PARALLELISM) : Check, Logging {
    @OptIn(DelicateCoroutinesApi::class)
    private val coroutineDispatcher = shutdownNeeded { newFixedThreadPoolContext(parallelism, "Checks-$name") }

    // thread safe access is provided with mutex on the list
    private val checkList = mutableListOf<Check>()

    fun add(fn: Check): Checks {
        synchronized(checkList) {
            checkList.add(fn)
        }
        return this
    }

    override suspend fun check(): CheckResult =
        withContext(coroutineDispatcher) {
            synchronized(checkList) {
                checkList.map {
                    async {
                        runCatching {
                            it.check().result.also { result ->
                                if (result) {
                                    logger.debug("Checks: $name - ${it.javaClass.name} PASS")
                                } else {
                                    logger.warn("Checks: $name - ${it.javaClass.name} FAILED")
                                }
                            }
                        }.recoverCatching {
                            logger.warn("Unable to perform check", it)
                            false
                        }
                    }
                }
            }.awaitAll()
                .map { it.getOrDefault(false) }
                .reduceOrNull(Boolean::and)
                ?.let { CheckResult(name, it) }
                ?: CheckResult(name, true)
        }
}
