package com.hellofresh.sdf.checks

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.logging.log4j.kotlin.Logging

class AsyncWarmupCheck(private val warmupCheck: WarmupCheck) : Check {
    private var startUpReady = false

    constructor(name: String, warmUp: Warmup) : this(Warmup<PERSON>heck(name, warmUp))

    override suspend fun check() = CheckResult(warmupCheck.name, startUpReady)

    fun fireAndForget() {
        CoroutineScope(Dispatchers.IO)
            .launch {
                startUpReady = warmupCheck.check().result
            }
    }

    companion object : Logging
}

class WarmupCheck(val name: String, private val warmUp: Warmup) : Check {
    override suspend fun check() =
        runCatching {
            warmUp.run()
        }.onSuccess {
            logger.info("Warmup $name Done")
        }.onFailure {
            logger.error("Warmup $name failed", it)
        }.let { CheckResult("Warmup: $name", true) }

    companion object : Logging
}

fun Checks.fireAndAddWarmUp(asyncWarmupCheck: AsyncWarmupCheck): Checks {
    asyncWarmupCheck.fireAndForget()
    this.add(asyncWarmupCheck)
    return this
}

fun interface Warmup {

    suspend fun run()
}
