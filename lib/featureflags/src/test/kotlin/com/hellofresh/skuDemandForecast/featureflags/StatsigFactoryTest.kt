package com.hellofresh.skuDemandForecast.featureflags

import com.hellofresh.skuDemandForecast.featureflags.Context.COUNTRY
import com.hellofresh.skuDemandForecast.featureflags.Context.DC
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag.CrossDocking
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag.MpsImport
import kotlin.reflect.KClass
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

class StatsigFactoryTest {

    val defaultLocalFileOverridesPath = "/feature-flags-overrides.json"

    private val overridesStatsigFactory = getFactory(defaultLocalFileOverridesPath)

    private fun getFactory(localFileOverridesPath: String?, hfTier: String = "local"): StatsigFactory =
        StatsigFactory(
            "",
            "",
            true,
            hfTier = hfTier,
            localFileOverridesPath = localFileOverridesPath?.let {
                StatsigFactoryTest::class.java.getResource(localFileOverridesPath)?.path
            },
        )

    @Test fun `market evaluation`() {
        val market = "DACH"
        val param = UserFactory("any-id").createUser(setOf(ContextData(MARKET, market)))
        assertEquals(market, param.custom!!["market"]!!.toString())
    }

    @Test fun `country evaluation`() {
        val country = "DE"
        val param = UserFactory("any-id").createUser(setOf(ContextData(COUNTRY, country)))
        assertEquals(country, param.country)
    }

    @Test fun `dc evaluation`() {
        val dc = "VE"
        val param = UserFactory("any-id").createUser(setOf(ContextData(DC, dc)))
        assertEquals(dc, param.custom!!["dc_code"]!!.toString())
    }

    @Test fun `default value is returned when there arent overrides in offline mode`() {
        val statsigFactory = getFactory(hfTier = "local", localFileOverridesPath = null)
        assertFalse(
            statsigFactory
                .featureFlagClient.isEnabledFor(MpsImport(emptySet())),
        )
        assertNull(
            statsigFactory
                .dynamicConfigClient.getConfig(MPSConfig(emptySet())),
        )
        assertFalse(
            statsigFactory
                .featureFlagClient.isEnabledFor(CrossDocking(emptySet())),
        )

        assertNull(
            statsigFactory
                .dynamicConfigClient.getConfig(TestDynamicConfig()),
        )
    }

    @Test fun `overrides matches context for flag`() {
        assertTrue(overridesStatsigFactory.featureFlagClient.isEnabledFor(MpsImport(MARKET, "FLAG_MARKET")))
        assertTrue(overridesStatsigFactory.featureFlagClient.isEnabledFor(MpsImport(COUNTRY, "FLAG_COUNTRY")))
    }

    @Test fun `default is returned when flag overrides doesnt match`() {
        assertFalse(overridesStatsigFactory.featureFlagClient.isEnabledFor(MpsImport(MARKET, "WHATEVER_MARKET")))
        assertFalse(overridesStatsigFactory.featureFlagClient.isEnabledFor(MpsImport(COUNTRY, "WHATEVER_COUNTRY")))
    }

    @Test fun `overrides matches context for dynamic config`() {
        assertEquals(
            "2025-W22",
            overridesStatsigFactory.dynamicConfigClient.getConfig(MPSConfig(MARKET, "CONFIG_MARKET"))?.parsedWeekStart,
        )
        assertEquals(
            "2025-W22",
            overridesStatsigFactory.dynamicConfigClient.getConfig(
                MPSConfig(
                    COUNTRY,
                    "CONFIG_COUNTRY",
                ),
            )?.parsedWeekStart,
        )
    }

    @Test fun `empty config  is returned when dynamic config overrides doesnt match`() {
        assertNull(overridesStatsigFactory.dynamicConfigClient.getConfig(MPSConfig(MARKET, "WHATEVER_MARKET")))
        assertNull(overridesStatsigFactory.dynamicConfigClient.getConfig(MPSConfig(COUNTRY, "WHATEVER_COUNTRY")))
    }

    @Test fun `flag and config overrides just used in local model`() {
        val statsigFactory = getFactory(localFileOverridesPath = defaultLocalFileOverridesPath, hfTier = "live")
        assertFalse(statsigFactory.featureFlagClient.isEnabledFor(MpsImport(MARKET, "FLAG_MARKET")))
        assertFalse(statsigFactory.featureFlagClient.isEnabledFor(MpsImport(COUNTRY, "FLAG_COUNTRY")))

        assertNull(
            statsigFactory.dynamicConfigClient.getConfig(MPSConfig(MARKET, "CONFIG_MARKET")),
        )
        assertNull(
            statsigFactory.dynamicConfigClient.getConfig(MPSConfig(COUNTRY, "CONFIG_COUNTRY")),
        )
    }

    private class TestDynamicConfig(
        override val contextData: Set<ContextData>,
    ) : DynamicConfig<String> {

        override val name: String = "TestDynamicConfig"
        override val valueKClass: KClass<String> = String::class

        constructor() : this(emptySet())
    }
}
