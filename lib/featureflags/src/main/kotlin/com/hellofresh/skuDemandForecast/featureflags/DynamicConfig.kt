package com.hellofresh.skuDemandForecast.featureflags

import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig.MpsConfigValue
import kotlin.reflect.KClass
import org.apache.logging.log4j.kotlin.Logging

/**
 * Dynamic Config for MPS Imports
 */
const val MPS_CONFIG = "mps-config"

interface DynamicConfig<T : Any> {
    val name: String
    val contextData: Set<ContextData>
    val valueKClass: KClass<T>

    data class MPSConfig(
        override val contextData: Set<ContextData>,
    ) : DynamicConfig<MpsConfigValue> {
        override val name: String = MPS_CONFIG
        override val valueKClass: KClass<MpsConfigValue> = MpsConfigValue::class

        constructor(context: Context, contextValue: String) : this(setOf(ContextData(context, contextValue)))

        data class MpsConfigValue(private val weekStart: String) {
            val parsedWeekStart: String? =
                if (weekStart.matches(weekRegex)) {
                    weekStart
                } else {
                    logger.error("Reading wrong week format from MPS Dynamic Config: $weekStart")
                    null
                }

            companion object {
                private val weekRegex = Regex("20[0-9]{2}-W[0-9]{2}")
            }
        }

        companion object : Logging
    }
}
