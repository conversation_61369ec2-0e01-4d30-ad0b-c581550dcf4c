package com.hellofresh.skuDemandForecast.featureflags.override

import com.hellofresh.skuDemandForecast.featureflags.ContextData
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigFactory.Companion.objectMapper
import org.apache.logging.log4j.kotlin.Logging

class FeatureFlagOverrides(
    featureFlags: List<FeatureFlagOverride>,
    dynamicConfigs: List<DynamicConfigOverride>
) {

    private val featureFlagsByName = featureFlags.associateBy { it.name }
    private val dynamicConfigsByName = dynamicConfigs.associateBy { it.name }

    fun checkFlag(flag: FeatureFlag): Boolean? =
        featureFlagsByName[flag.name]?.let {
            logger.info("Evaluating Flag Override for: $flag")
            checkContexts(it.contextData, flag.contextData)
                .also { logger.info("Flag Override Result: $it") }
        }

    fun <T : Any> getConfig(dynamicConfig: DynamicConfig<T>): T? =
        runCatching {
            dynamicConfigsByName[dynamicConfig.name]
                ?.let { override ->
                    override.takeIf {
                        logger.info("Evaluating Dynamic Config Override for: $dynamicConfig")
                        checkContexts(it.contextData, dynamicConfig.contextData)
                    }
                        ?.let { objectMapper.convertValue(it.value, dynamicConfig.valueKClass.java) }
                        .also { logger.info("Dynamic Config Override Result: $it") }
                }
        }.onFailure { logger.error("Couldn't use dynamic config from override: ${dynamicConfig.name}", it) }
            .getOrNull()

    private fun checkContexts(overrideContext: Set<ContextData>, requestedContext: Set<ContextData>): Boolean =
        requestedContext.any { overrideContext.contains(it) }

    override fun toString() =
        "FeatureFlagsOverrides: flags: ${featureFlagsByName.values}, dynamicConfigs: ${dynamicConfigsByName.values}"

    companion object : Logging {
        val empty = FeatureFlagOverrides(emptyList(), emptyList())
    }
}

data class FeatureFlagOverride(
    val name: String,
    val contextData: Set<ContextData>
)

data class DynamicConfigOverride(
    val name: String,
    val contextData: Set<ContextData>,
    val value: Map<String, Any>
)
