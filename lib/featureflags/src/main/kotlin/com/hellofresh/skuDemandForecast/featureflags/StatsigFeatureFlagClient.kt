package com.hellofresh.skuDemandForecast.featureflags

import com.hellofresh.skuDemandForecast.featureflags.override.FeatureFlagOverrides
import com.statsig.sdk.Statsig
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging

interface StatsigFeatureFlagClient {
    fun isEnabledFor(featureFlag: FeatureFlag, default: Boolean? = null): Boolean
}

class StatsigFeatureFlagClientImpl internal constructor(
    private val userFactory: UserFactory,
    private val featureFlagOverrides: FeatureFlagOverrides
) : StatsigFeatureFlagClient {

    override fun isEnabledFor(featureFlag: FeatureFlag, default: Boolean?): Boolean =
        featureFlagOverrides.checkFlag(featureFlag)
            ?: run {
                if (Statsig.isInitialized()) {
                    runBlocking {
                        Statsig.getFeatureGate(
                            gateName = featureFlag.name,
                            user = userFactory.createUser(featureFlag.contextData),
                        ).value
                    }
                } else {
                    default ?: error("Statsig is not yet initialized")
                }
            }

    companion object : Logging
}
