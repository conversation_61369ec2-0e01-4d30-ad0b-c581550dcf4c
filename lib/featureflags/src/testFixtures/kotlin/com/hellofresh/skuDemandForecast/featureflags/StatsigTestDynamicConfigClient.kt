package com.hellofresh.skuDemandForecast.featureflags

import kotlin.reflect.cast

class StatsigTestDynamicConfigClient(
    var dynamicConfigs: Map<DynamicConfig<*>, Any> = emptyMap()
) : DynamicConfigClient {

    override fun <T : Any> getConfig(dynamicConfig: DynamicConfig<T>): T? =
        dynamicConfigs[dynamicConfig]?.let { dynamicConfig.valueKClass.cast(it) }

    companion object {
        val empty = StatsigTestDynamicConfigClient(emptyMap())
    }
}
