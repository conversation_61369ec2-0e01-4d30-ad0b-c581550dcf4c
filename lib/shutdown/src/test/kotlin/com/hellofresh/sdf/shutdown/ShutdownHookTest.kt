package com.hellofresh.sdf.shutdown

import kotlin.test.Test
import kotlin.test.assertEquals

class ShutdownHookTest {

    @Test fun `shutdown hooks are executed in LIFO order`() {
        val orderOfAddition = listOf<Int>(1, 2, 3, 4)
        val orderOfExecution = mutableListOf<Int>()
        orderOfAddition.forEach {
            shutdownNeeded { AutoCloseable { orderOfExecution.add(it) } }
        }
        executeShutdown()
        assertEquals(orderOfAddition.reversed(), orderOfExecution)
    }
}
