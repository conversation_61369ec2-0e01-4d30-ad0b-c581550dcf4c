plugins {
    id("com.hellofresh.sdf.kotlin-conventions")
    hellofresh.`test-fixtures`
}

dependencies {

    api(libs.kafka.clients)
    api(libs.hellofresh.service)
    api(project(":lib:logging"))
    api(project(":lib:checks"))
    api(libs.coroutines.core)
    api(libs.resilience4j.retry)
    api(libs.resilience4j.kotlin)
    api(libs.kafka.avro.serializer)
    api(libs.kotlin.reflect)

    testImplementation(libs.mockk)

    testFixturesApi(libs.kafka.clients)
    testFixturesApi(libs.hellofresh.schemaregistry)
    testFixturesApi(libs.testcontainers.core)
    testFixturesApi(libs.testcontainers.kafka)
}
