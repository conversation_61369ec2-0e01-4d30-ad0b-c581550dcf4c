package com.hellofresh.sdf.lib.kafka.processor

import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.Duration
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.KafkaException
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.errors.RecordDeserializationException
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.logging.log4j.Level
import org.junit.jupiter.api.Test

class CoroutinesProcessorTest {
    private val kafkaConsumerMock: KafkaConsumer<String, String> = mockk(relaxed = true)
    private val pollConfig = PollConfig(
        kotlin.time.Duration.parse("PT1M"),
        10,
        kotlin.time.Duration.parse("PT10S"),
        DeserializationExceptionStrategyType.IGNORE_EXCEPTIONS.name,
    )
    private val meterRegistry = SimpleMeterRegistry()

    @Test
    fun `exceptions in poll will close the CoroutinesProcessor`() {
        every {
            kafkaConsumerMock.poll(any<Duration>())
        }.throws(KafkaException("Some exception"))

        val coroutinesProcessor = spyk(
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumer = kafkaConsumerMock,
                meterRegistry = meterRegistry,
                handleDeserializationException = IgnoreExceptions,
                process = { },
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
            ),
        )

        runBlocking {
            coroutinesProcessor.run()
        }
        verify { kafkaConsumerMock.poll(any<Duration>()) }
        verify { kafkaConsumerMock.close() }
    }

    @Test
    fun `deserialization exceptions not handled with deserializer will execute the strategy`() {
        every {
            kafkaConsumerMock.poll(any<Duration>())
        }.throws(RecordDeserializationException(TopicPartition("any", 1), 0, "message", null))

        var called = false

        runBlocking {
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumer = kafkaConsumerMock,
                meterRegistry = meterRegistry,
                handleDeserializationException = {
                    called = true
                    throw it
                },
                process = { },
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
            ).run()
        }
        verify(atLeast = 1) { kafkaConsumerMock.poll(any<Duration>()) }
        assert(called) { "Handling the deserialization exception did not called." }
    }

    @Test
    fun `deserialization exceptions handled with deserializer will execute the strategy`() {
        val deserializationException = RecordDeserializationException(TopicPartition("any", 1), 0, "message", null)
        val headers = RecordHeaders()
        ErrorHandlingDeserializer.processDeserializationException("topic", false, headers, deserializationException)
        val consumerRecord = mockk<ConsumerRecord<String, String>>(relaxed = true)
        every { consumerRecord.headers() } returns headers
        every { consumerRecord.value() } returns null
        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns ConsumerRecords(mapOf(deserializationException.topicPartition() to listOf(consumerRecord)))

        var called = false

        runBlocking {
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumer = kafkaConsumerMock,
                meterRegistry = meterRegistry,
                handleDeserializationException = {
                    called = true
                    throw it
                },
                process = { },
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
            ).run()
        }
        verify(atLeast = 1) { kafkaConsumerMock.poll(any<Duration>()) }
        assert(called) { "Handling the deserialization exception did not called." }
    }

    @Test
    fun `records are returned when ignoring deserialization errors`() {
        val messageValue = "message"
        val deserializationException = RecordDeserializationException(TopicPartition("any", 1), 0, messageValue, null)
        val headers = RecordHeaders()
        ErrorHandlingDeserializer.processDeserializationException("topic", false, headers, deserializationException)
        val consumerRecord = mockk<ConsumerRecord<String, String>>(relaxed = true)
        every { consumerRecord.headers() } returns headers
        every { consumerRecord.value() } returns null

        val consumerRecord2 = mockk<ConsumerRecord<String, String>>(relaxed = true)
        every { consumerRecord2.value() } returns messageValue

        val processMock = mockk<(ConsumerRecords<String, String>) -> Unit>()
        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = processMock,
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
        )
        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns ConsumerRecords(
            mapOf(deserializationException.topicPartition() to listOf(consumerRecord, consumerRecord2))
        ) andThenAnswer {
            coroutinesProcessor.close()
            ConsumerRecords(
                emptyMap(),
            )
        }

        runBlocking {
            coroutinesProcessor.run()

            verify(atLeast = 1) { kafkaConsumerMock.poll(any<Duration>()) }

            verify(atLeast = 1, timeout = 15000L) {
                processMock.invoke(
                    withArg {
                        assertEquals(consumerRecord2, it.first())
                    },
                )
            }
            coroutinesProcessor.close()
        }
    }

    @Test
    fun `calls commitAsync on the Kafka consumer if processing is successful`() {
        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = { },
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
        )

        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns ConsumerRecords(
            mapOf(mockk<TopicPartition>() to listOf(mockk<ConsumerRecord<String, String>>(relaxed = true)))
        ) andThenAnswer {
            coroutinesProcessor.close()
            ConsumerRecords.empty()
        }

        runBlocking {
            coroutinesProcessor.run()
        }

        verify { kafkaConsumerMock.poll(any<Duration>()) }
        verify { kafkaConsumerMock.commitAsync() }
    }

    @Test
    fun `metrics are recoded`() {
        every {
            kafkaConsumerMock.poll(any<Duration>())
        }.throws(RecordDeserializationException(TopicPartition("any", 1), 0, "message", null))

        runBlocking {
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumer = kafkaConsumerMock,
                meterRegistry = meterRegistry,
                handleDeserializationException = LogRecordAndFail(Level.ERROR, meterRegistry),
                process = { },
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
            ).run()
        }

        verify(atLeast = 1) { kafkaConsumerMock.poll(any<Duration>()) }
        assert(true) { "The coroutine processor did not exit." }

        val errorCounter = meterRegistry.get(DeserializationExceptionStrategy.ERROR_METRIC_NAME).counter().count()
        assertEquals(1, errorCounter.toInt(), "Did not get the metric error count, when there is an exception.")
    }

    @Test
    fun `processor is healthy if no errors`() {
        val consumerRecords = ConsumerRecords<String, String>(emptyMap())
        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns consumerRecords

        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = { },
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
        )

        runBlocking {
            assertFalse(runBlocking { coroutinesProcessor.isHealthy().result })

            launch { coroutinesProcessor.run() }

            assertTrue(runBlocking { coroutinesProcessor.isHealthy().result })
            coroutinesProcessor.close()
        }
    }

    @Test
    fun `still calls commitAsync if processing fails with first poll but is successful on the next poll when IgnoreAndContinueProcessing is used`() {
        val processMock = mockk<suspend (ConsumerRecords<String, String>) -> Unit>()
        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = processMock,
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(meterRegistry, "X"),
        )

        // first call throws an exception and the next returns successfully
        coEvery { processMock.invoke(any()) } throws IllegalArgumentException() andThenAnswer { }

        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns ConsumerRecords(
            mapOf(mockk<TopicPartition>() to listOf(mockk<ConsumerRecord<String, String>>(relaxed = true)))
        )

        // when
        runBlocking { coroutinesProcessor.pollAndProcessRecords(Duration.ofSeconds(1)) }
        // then
        verify(exactly = 0) { kafkaConsumerMock.commitAsync() }

        // when calling again fetchAndProcessRecords
        runBlocking { coroutinesProcessor.pollAndProcessRecords(Duration.ofSeconds(1)) }
        // then
        verify { kafkaConsumerMock.commitAsync() }
    }
}
