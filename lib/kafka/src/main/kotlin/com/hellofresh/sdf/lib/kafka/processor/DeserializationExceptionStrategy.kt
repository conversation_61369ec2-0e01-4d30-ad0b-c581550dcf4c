package com.hellofresh.sdf.lib.kafka.processor

import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategy.Companion.ERROR_METRIC_NAME
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.FAIL
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.IGNORE_EXCEPTIONS
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.LOG_ERROR_FAIL
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.LOG_RECORD_FAIL
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.LOG_RECORD_IGNORE
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.kotlin.Logging

fun interface DeserializationExceptionStrategy : (Exception) -> Unit {
    companion object : Logging {

        const val ERROR_METRIC_NAME = "deserialization_error"

        fun create(type: String, meterRegistry: MeterRegistry) =
            create(DeserializationExceptionStrategyType.valueOf(type), meterRegistry)

        fun create(type: DeserializationExceptionStrategyType, meterRegistry: MeterRegistry) =
            when (type) {
                IGNORE_EXCEPTIONS -> IgnoreExceptions
                LOG_ERROR_IGNORE -> LogAndIgnoreExceptions(Level.ERROR)
                LOG_RECORD_IGNORE -> LogRecordAndIgnoreExceptions(Level.ERROR, meterRegistry)
                FAIL -> Fail
                LOG_ERROR_FAIL -> LogAndFail(Level.ERROR)
                LOG_RECORD_FAIL -> LogRecordAndFail(Level.ERROR, meterRegistry)
            }
    }
}

enum class DeserializationExceptionStrategyType {
    IGNORE_EXCEPTIONS,
    LOG_ERROR_IGNORE,
    LOG_RECORD_IGNORE,
    FAIL,
    LOG_ERROR_FAIL,
    LOG_RECORD_FAIL
}

val IgnoreExceptions = DeserializationExceptionStrategy {}

class LogAndIgnoreExceptions(private val logLevel: Level) : DeserializationExceptionStrategy {
    override fun invoke(p1: Exception) {
        DeserializationExceptionStrategy.logger.log(logLevel, p1)
    }
}

class LogRecordAndIgnoreExceptions(private val logLevel: Level, val registry: MeterRegistry) : DeserializationExceptionStrategy {
    private val errorMetric = Counter.builder(ERROR_METRIC_NAME).register(registry)
    override fun invoke(p1: Exception) {
        DeserializationExceptionStrategy.logger.log(logLevel, p1)
        errorMetric.increment()
    }
}

val Fail = DeserializationExceptionStrategy { throw it }

class LogAndFail(private val logLevel: Level) : DeserializationExceptionStrategy {
    override fun invoke(p1: Exception) {
        DeserializationExceptionStrategy.logger.log(logLevel, p1)
        throw p1
    }
}

class LogRecordAndFail(private val logLevel: Level, val registry: MeterRegistry) : DeserializationExceptionStrategy {
    private val errorMetric = Counter.builder(ERROR_METRIC_NAME).register(registry)
    override fun invoke(p1: Exception) {
        DeserializationExceptionStrategy.logger.log(logLevel, p1)
        errorMetric.increment()
        throw p1
    }
}
