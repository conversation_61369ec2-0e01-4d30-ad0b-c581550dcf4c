package com.hellofresh.sdf.lib.kafka

import com.hellofresh.service.Config
import com.hellofresh.service.Tier
import java.nio.file.Files
import java.nio.file.Paths
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock
import kotlin.io.path.notExists
import org.apache.kafka.clients.CommonClientConfigs
import org.apache.kafka.common.config.SaslConfigs
import org.apache.kafka.common.config.SslConfigs
import org.apache.kafka.common.security.plain.PlainLoginModule

private const val AIVEN_DOMAIN = ".aivencloud.com"
private val firstRun: AtomicBoolean = AtomicBoolean(true)
private val lock = ReentrantLock()

fun Config.loadKafkaConfigurations(): Map<String, String> {
    if (tier.isLocal) {
        return all
    }
    return this.addAuthConfig().all
}

fun Config.addAuthConfig() = if (tier.isLocal) {
    this
} else {
    listOf(
        SaslConfigs.SASL_JAAS_CONFIG to plainLogin(aivenUsername, aivenPassword),
        SaslConfigs.SASL_MECHANISM to "PLAIN",
        CommonClientConfigs.SECURITY_PROTOCOL_CONFIG to "SASL_SSL",
        SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG to "",
        SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG to getAivenSslTruststoreLocation(tier),
        SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG to "${tier.value}$AIVEN_DOMAIN",
    ).toMap().let {
        Config(
            all = it + this.all,
            profiles = this.profiles,
            tier = this.tier,
        )
    }
}

internal fun getAivenSslTruststoreLocation(tier: Tier): String {
    val resource = "kafka/${tier.value}$AIVEN_DOMAIN/trust-store.jks"
    val truststorePath = Paths.get(System.getProperty("java.io.tmpdir"), resource.replace('/', '.'))
    if (truststorePath.notExists()) {
        val url = checkNotNull(ClassLoader.getSystemResource(resource)) {
            "Could not find required Aiven trust store: $resource. Aborting"
        }
        if (firstRun.compareAndSet(true, false)) {
            lock.withLock {
                url.openStream().use { Files.copy(it, truststorePath) }
            }
        }
    }
    return truststorePath.toString()
}

private fun plainLogin(userName: String, password: String) =
    "${PlainLoginModule::class.java.name} required username=\"$userName\" password=\"$password\";"

private val Config.aivenUsername: String
    get() = get("aiven.username") { get("project.name").let { if (profiles.contains("qa")) "$it-qa" else it } }

private val Config.aivenPassword: String
    get() = get("aiven.password")
