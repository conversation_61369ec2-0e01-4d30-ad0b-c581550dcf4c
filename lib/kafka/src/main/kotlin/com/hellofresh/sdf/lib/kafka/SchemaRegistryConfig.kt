package com.hellofresh.sdf.lib.kafka

import com.hellofresh.service.Config
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClientConfig

fun loadSchemaRegistryClientConfig(config: Config) = mapOf(
    SchemaRegistryClientConfig.BASIC_AUTH_CREDENTIALS_SOURCE to "USER_INFO",
    SchemaRegistryClientConfig.USER_INFO_CONFIG
        to "${config["schema.registry.aiven.username"]}:${config["schema.registry.aiven.password"]}",
)
