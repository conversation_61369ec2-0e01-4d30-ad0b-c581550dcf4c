package com.hellofresh.skuDemandForecast.db

import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import org.jooq.impl.DSL.selectFrom
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class DBConfigurationTest : AbstractDbTest() {

    @Test
    fun `jooq dsl context can perform queries`() {
        val jooqDslContext = DBConfiguration.jooqDslContext(databaseConfig, true, 1, SimpleMeterRegistry())

        jooqDslContext.insertInto(table).values(1).execute()

        val count = jooqDslContext.fetchCount(selectFrom(table).where(idField.eq(1)))

        assertEquals(1, count)
    }
}
