package com.hellofresh.skuDemandForecast.db.metrics

import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tags
import io.micrometer.core.instrument.Timer
import java.util.Locale
import org.jooq.ExecuteContext
import org.jooq.ExecuteListener
import org.jooq.Scope
import org.jooq.TransactionContext
import org.jooq.TransactionListener

internal const val TAG_TYPE_KEY = "type"
internal const val QUERY_TAG_TYPE_VALUE = "query"

internal const val TRANSACTION_TAG_TYPE_VALUE = "transaction"

internal class JooqListener(
    private val registry: MeterRegistry,
    private val defaultTags: Tags,
) : ExecuteListener, TransactionListener {

    private var timer: Timer.Sample? = null

    override fun executeStart(ctx: ExecuteContext) {
        startTimer(ctx.getTags())
    }

    override fun beginStart(ctx: TransactionContext) {
        startTimer(ctx.getTags())
    }

    private fun startTimer(tags: Tags) {
        tags.find { it.key == NAME_TAG_KEY }
            ?.run {
                timer = Timer.start(registry)
            }
    }

    override fun executeEnd(ctx: ExecuteContext) {
        stopTimerIfStillRunning(ctx)
    }

    override fun commitEnd(ctx: TransactionContext) {
        stopTimerIfStillRunning(ctx)
    }

    override fun rollbackEnd(ctx: TransactionContext) {
        stopTimerIfStillRunning(ctx)
    }

    private fun stopTimerIfStillRunning(ctx: TransactionContext) {
        stopTimerIfStillRunning(
            ctx,
            TRANSACTION_TAG_TYPE_VALUE,
            "none",
            ctx.cause(),
        )
    }

    private fun stopTimerIfStillRunning(ctx: ExecuteContext) {
        stopTimerIfStillRunning(
            ctx,
            QUERY_TAG_TYPE_VALUE,
            ctx.type().name.lowercase(Locale.getDefault()),
            ctx.exception(),
        )
    }

    private fun stopTimerIfStillRunning(scope: Scope, type: String, subType: String, exception: Exception?) {
        timer?.stop(
            Timer.builder("jooq")
                .description("Execution time of JOOQ calls")
                .tags(scope.getTags())
                .tag("status", exception?.let { "ko" } ?: "ok")
                .tag(TAG_TYPE_KEY, type)
                .tag("subType", subType)
                .tags(defaultTags)
                .register(registry),
        )
    }

    private fun Scope.getTags() =
        this.configuration().data().getOrDefault(CONFIGURATION_METRICS_TAGS_KEY, Tags.empty()) as Tags
}
