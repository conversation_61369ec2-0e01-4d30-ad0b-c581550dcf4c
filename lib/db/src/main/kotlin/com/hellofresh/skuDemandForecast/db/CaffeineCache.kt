package com.hellofresh.skuDemandForecast.db

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Scheduler
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics
import java.time.Duration
import java.util.concurrent.TimeUnit.MINUTES
import org.apache.logging.log4j.kotlin.Logging

class CaffeineCache<K, V>(
    meterRegistry: MeterRegistry,
    ttl: Duration,
    cacheSize: Long,
    val loadFunction: suspend (K) -> Map<K, V> = { emptyMap() }
) {

    private val hitCounter = Counter.builder("filter_hit_counter")
        .description("Record the number of times the record filter was able to filter a record")
        .register(meterRegistry)

    private val missCounter = Counter.builder("filter_miss_counter")
        .description("Record the number of times the record filter was not able to filter a record")
        .register(meterRegistry)

    private val cache = Caffeine.newBuilder()
        .scheduler(Scheduler.systemScheduler())
        .expireAfterWrite(ttl.toMinutes(), MINUTES)
        .maximumSize(cacheSize)
        .recordStats()
        .build<K, V>().also {
            CaffeineCacheMetrics.monitor(meterRegistry, it, "db-cache")
        }

    fun put(key: K, value: V) {
        cache.put(key, value)
    }

    suspend fun get(key: K, localLoadFunction: suspend (K) -> V): V? = cache.getIfPresent(key)
        .also { hitCounter.increment() }
        ?: let {
            missCounter.increment()
            localLoadFunction(key)?.also { put(key, it) }
        }

    suspend fun get(key: K): V? = cache.getIfPresent(key)
        .also { hitCounter.increment() }
        ?: let {
            missCounter.increment()
            loadFunction(key).forEach { (k, v) ->
                logger.info("adding to cache: $k, $v")
                put(k, v)
            }
            cache.getIfPresent(key)
        }

    companion object : Logging
}
