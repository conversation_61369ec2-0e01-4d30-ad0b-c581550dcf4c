package com.hellofresh.skuDemandForecast.db

import com.hellofresh.sdf.checks.HealthChecks
import com.hellofresh.sdf.checks.StartUpChecks
import com.hellofresh.sdf.shutdown.shutdownHook
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicLong
import org.jooq.SQLDialect.POSTGRES
import org.jooq.conf.Settings
import org.jooq.impl.DefaultConfiguration
import org.postgresql.Driver

private const val CLIENT_DB_TIMEOUT_IN_MILLISECONDS = 25000

object DBConfiguration {
    fun jooqDslContext(
        databaseConfig: DatabaseConfig,
        readOnly: <PERSON>ole<PERSON>,
        parallelism: Int,
        meterRegistry: MeterRegistry,
    ): MetricsDSLContext {
        val dataSource = HikariDataSource(
            HikariConfig().apply {
                driverClassName = Driver::class.qualifiedName
                metricRegistry = meterRegistry
                jdbcUrl = "jdbc:postgresql://${databaseConfig.hostName}/sdf"
                username = databaseConfig.userName
                password = databaseConfig.password
                isReadOnly = readOnly
                maximumPoolSize = parallelism
                validate()
            },
        ).also {
            shutdownHook(it)
        }

        return MetricsDSLContext(
            databaseConfig.configName,
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(createThreadPool(parallelism, databaseConfig))
                setSettings(Settings().withQueryTimeout(CLIENT_DB_TIMEOUT_IN_MILLISECONDS))
            },
            meterRegistry,
        ).also {
            StartUpChecks.add(it)
            HealthChecks.add(it)
        }
    }

    private fun createThreadPool(
        parallelism: Int,
        databaseConfig: DatabaseConfig,
    ) =
        Executors.newFixedThreadPool(
            parallelism,
            object : ThreadFactory {
                private val count: AtomicLong = AtomicLong(0)
                override fun newThread(r: Runnable): Thread =
                    Executors.defaultThreadFactory().newThread(r)
                        .also {
                            it.name = "database-query-${databaseConfig.configName}-thread-${count.getAndIncrement()}"
                        }
            },
        ).also {
            shutdownHook(AutoCloseable { it.shutdownNow() })
        }
}

data class DatabaseConfig(
    val configName: String,
    val hostName: String,
    val userName: String,
    val password: String
)
