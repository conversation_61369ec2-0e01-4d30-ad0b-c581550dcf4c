package com.hellofresh.skuDemandForecast.db

import kotlin.getOrDefault
import kotlin.onFailure
import kotlin.runCatching
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

object DbDslCheck : Logging {

    fun isHealthy(dsl: DSLContext): Boolean =
        runCatching {
            dsl.fetch("SELECT 1")
            true
        }.onFailure {
            logger.error("DB DSL check failed", it)
        }.getOrDefault(false)
}
