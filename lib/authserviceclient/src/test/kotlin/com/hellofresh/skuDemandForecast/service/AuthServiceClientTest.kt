package com.hellofresh.skuDemandForecast.service

import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.post
import com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo
import com.github.tomakehurst.wiremock.client.WireMock.verify
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import com.hellofresh.skuDemandForecast.authserviceclient.model.ApplicationConfig
import com.hellofresh.skuDemandForecast.authserviceclient.service.AuthServiceClient
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import org.intellij.lang.annotations.Language
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@WireMockTest
class AuthServiceClientTest {

    private lateinit var authServiceClient: AuthServiceClient
    private val clientId = "client_id"
    private val clientSecret = "client_secret"
    private val serviceUserName = "service_user_name"
    private val serviceUserPassword = "service_password"
    private val appName = "test_app"
    private val authHeader = "Basic Y2xpZW50X2lkOmNsaWVudF9zZWNyZXQ="
    private val timeout = "PT5S"

    @BeforeEach
    fun setUp(wmRuntimeInfo: WireMockRuntimeInfo) {
        val applicationConfig = ApplicationConfig(
            authServiceUrl = "http://localhost:${wmRuntimeInfo.httpPort}",
            applicationName = appName,
            clientId = clientId,
            clientSecret = clientSecret,
            userName = serviceUserName,
            password = serviceUserPassword,
            authClientTimeout = timeout,
        )
        authServiceClient = AuthServiceClient(SimpleMeterRegistry(), applicationConfig)

        stubFor(
            post(urlEqualTo("/token"))
                .withFormParam("username", equalTo(serviceUserName))
                .withFormParam("password", equalTo(serviceUserPassword))
                .withFormParam("grant_type", equalTo("password"))
                .withHeader("Authorization", equalTo(authHeader))
                .willReturn(
                    aResponse()
                        .withStatus(200)
                        .withBody(AUTH_TOKEN_RESPONSE),
                ),
        )
    }

    @Test
    fun `should get auth token`() {
        val result = authServiceClient.getAuthToken()

        verify(
            /* requestPatternBuilder = */
            postRequestedFor(urlEqualTo("/token"))
                .withFormParam("username", equalTo(serviceUserName))
                .withFormParam("password", equalTo(serviceUserPassword))
                .withFormParam("grant_type", equalTo("password"))
                .withHeader("Authorization", equalTo(authHeader)),
        )
        assertEquals("Bearer access_token", result)
    }
}

@Language("JSON")
val AUTH_TOKEN_RESPONSE = """
    {
        "access_token": "access_token",
        "expires_in": 2629743,
        "issued_at": 1683540769,
        "refresh_token": "refresh_token",
        "token_type": "Bearer",
        "user_data": {
            "id": "9fec2e8a-f377-4257-8e7e-7363070e5556",
            "username": "inventory",
            "email": "<EMAIL>",
            "user_id": "015735295339",
            "country": "de",
            "blocked": false,
            "metadata": {},
            "source_system": {
                "String": "service",
                "Valid": true
            },
            "roles": []
        }
    }
"""
