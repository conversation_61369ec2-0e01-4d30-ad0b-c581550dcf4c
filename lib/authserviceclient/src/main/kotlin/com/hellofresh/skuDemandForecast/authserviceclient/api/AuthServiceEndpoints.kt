package com.hellofresh.skuDemandForecast.authserviceclient.api

import com.hellofresh.skuDemandForecast.authserviceclient.model.TokenResponse
import retrofit2.Call
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface AuthServiceEndpoints {

    @FormUrlEncoded
    @POST("token")
    fun getAuthToken(
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("grant_type") grantType: String = "password",
    ): Call<TokenResponse>
}
