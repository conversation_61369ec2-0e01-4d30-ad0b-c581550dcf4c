package com.hellofresh.skuDemandForecast.authserviceclient.retrofit

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.authserviceclient.model.ApplicationConfig
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.retrofit.CircuitBreakerCallAdapter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.binder.okhttp3.OkHttpMetricsEventListener
import java.time.Duration
import okhttp3.Credentials
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.HttpException
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.create

val converter: JacksonConverterFactory = JacksonConverterFactory.create(
    jacksonObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false),
)

/**
 * Transform [interface][T] into an HTTP client based on [Retrofit] annotations
 * on the contained functions with the supplied [config] and the given
 * [meterRegistry].
 *
 * The [interface][T] may be private and hidden inside another class. Reflection
 * is used and it will be made accessible.
 *
 * @throws IllegalArgumentException if [T] is not an interface or extends
 *   another interface (Retrofit prefers composition over inheritance).
 */
inline fun <reified T> retrofit(meterRegistry: MeterRegistry, applicationConfig: ApplicationConfig): T =
    Retrofit.Builder().apply {
        addConverterFactory(converter)
        addCallAdapterFactory(
            CircuitBreakerCallAdapter.of(
                CircuitBreaker.ofDefaults("appName.circuit_breaker"),
            ),
        )
        baseUrl(applicationConfig.authServiceUrl)
        client(createClient(meterRegistry, applicationConfig))
        validateEagerly(true)
    }.build().create()

fun createClient(meterRegistry: MeterRegistry, applicationConfig: ApplicationConfig) =
    OkHttpClient.Builder().apply {
        addInterceptor(createAuthInterceptor(applicationConfig.clientId, applicationConfig.clientSecret))
        callTimeout(Duration.parse(applicationConfig.authClientTimeout))
        eventListener(
            OkHttpMetricsEventListener.builder(meterRegistry, "http.outgoing")
                .tag(Tag.of("application", applicationConfig.applicationName))
                .build(),
        )
    }.build()

private fun createAuthInterceptor(username: String, password: String): Interceptor =
    Interceptor { chain ->
        val authorizedRequest = chain
            .request()
            .newBuilder()
            .addHeader("Authorization", Credentials.basic(username, password))
            .build()
        chain.proceed(authorizedRequest)
    }

/**
 * Unwrap the [Response.body] if the [Response] is OK.
 *
 * @return [T] if [Response] is OK.
 * @throws HttpException in all other cases.
 */
fun <T : Any> Response<T>.unwrapOrThrow(): T =
    if (this.isSuccessful) {
        checkNotNull(body())
    } else {
        throw HttpException(this)
    }

/**
 * Unwrap the [Response.body] and [transform] it if the [Response] is OK.
 *
 * @return [R] if [Response] is OK.
 * @throws HttpException in all other cases.
 */
inline fun <T : Any, R : Any> Response<T>.unwrapOrThrow(transform: (T) -> R): R =
    unwrapOrThrow().let(transform)
