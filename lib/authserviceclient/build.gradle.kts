plugins {
    id("com.hellofresh.sdf.kotlin-conventions")
    hellofresh.`test-fixtures`
}

dependencies {
    api(project(":lib:logging"))
    api(libs.jackson.kotlin)
    api(libs.jackson.jsr310)
    implementation(project(":lib"))
    implementation(libs.retrofit.jackson)
    implementation(libs.retrofit.core)
    implementation(libs.okhttp.core)
    implementation(libs.resilience4j.circuitbreaker)
    implementation(libs.resilience4j.retrofit)
    implementation(libs.resilience4j.retry)

    testImplementation(libs.wiremock)
}
