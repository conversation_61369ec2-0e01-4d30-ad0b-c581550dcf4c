import com.hellofresh.service.Config
import com.hellofresh.skuDemandForecast.lib.s3.FileObject
import com.hellofresh.skuDemandForecast.lib.s3.S3FileService
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.Instant
import kotlin.reflect.full.declaredFunctions
import kotlin.reflect.full.declaredMemberFunctions
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.javaField
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response

class S3FileServiceTest {
    private val bucket = "test-bucket"
    private val key = "test-key"
    private val prefix = "test-prefix"
    private lateinit var config: Config
    private lateinit var s3Client: S3Client
    private lateinit var s3FileService: S3FileService

    @BeforeEach
    fun setUp() {
        config = mockk()
        s3Client = mockk()
        every { config.tier.isLocal } returns false
        s3FileService = S3FileService(config)
        setS3Client(s3FileService, s3Client) // inject the mocked s3Client using reflection
    }

    @AfterEach
    fun tearDown() {
        clearMocks(config, s3Client)
    }

    @Test
    fun `fetchObjectContent should return InputStream`() {
        val inputStream = mockk<ResponseInputStream<GetObjectResponse>>()

        every {
            s3Client.getObject(
                match<GetObjectRequest> {
                    it.bucket() == bucket && it.key() == key
                },
            )
        } returns inputStream

        val result = s3FileService.fetchObjectContent(bucket, key)

        assertEquals(inputStream, result)
    }

    @Test
    fun `fetchObjectSummaries should return list of keys`() {
        val lastModified = Instant.now()
        val summary1 = software.amazon.awssdk.services.s3.model.S3Object.builder()
            .key("test-key-1")
            .lastModified(lastModified).build()

        val summary2 =
            software.amazon.awssdk.services.s3.model.S3Object.builder()
                .key("test-key-2")
                .lastModified(lastModified).build()

        val result = mockk<ListObjectsV2Response>()
        every { result.contents() } returns listOf(summary1, summary2)
        every { s3Client.listObjectsV2(any<software.amazon.awssdk.services.s3.model.ListObjectsV2Request>()) } returns result

        val summaries = s3FileService.fetchObjectSummaries(bucket, prefix)

        val sampleData = listOf(
            FileObject(lastModified = lastModified, key = "test-key-1"),
            FileObject(lastModified = lastModified, key = "test-key-2"),
        )
        assertEquals(sampleData, summaries)
        verify { s3Client.listObjectsV2(any<software.amazon.awssdk.services.s3.model.ListObjectsV2Request>()) }
    }

    @Test
    fun `local initialization should configure local S3 client`() {
        every { config.tier.isLocal } returns true
        every { config["aws.s3.host"] } returns "http://localhost:8001"

        s3FileService = S3FileService(config)

        val s3Client = getS3Client(s3FileService)
        assertNotNull(s3Client)
        verify { config["aws.s3.host"] }
    }

    @Test
    fun `non-local initialization should configure default S3 client`() {
        every { config.tier.isLocal } returns false

        s3FileService = S3FileService(config)

        val s3Client = getS3Client(s3FileService)
        assertNotNull(s3Client)
    }

    @Test
    fun `should verify method structure of S3FileService`() {
        val methods = S3FileService::class.declaredMemberFunctions.map { it.name }

        assertTrue("fetchObjectContent" in methods)
        assertTrue("fetchObjectSummaries" in methods)
        assertTrue("localS3Client" in methods)
        assertTrue("createS3Client" in methods)
    }

    @Test
    fun `should verify property structure of S3FileService`() {
        val properties = S3FileService::class.declaredMemberProperties.map { it.name }

        assertTrue("config" in properties)
        assertTrue("s3Client" in properties)
    }

    @Test
    fun `should verify fetchObjectContent method parameters`() {
        val fetchObjectContentMethod = S3FileService::class.declaredFunctions.find { it.name == "fetchObjectContent" }
        assertNotNull(fetchObjectContentMethod, "fetchObjectContent method not found")

        val parameters = fetchObjectContentMethod.parameters
        assertEquals(
            3,
            parameters.size,
            "fetchObjectContent should have 3 parameters including the instance",
        )

        val expectedParameterTypes = listOf(String::class, String::class)
        for (i in 1 until parameters.size) {
            assertEquals(
                expectedParameterTypes[i - 1],
                parameters[i].type.classifier,
                "Parameter type mismatch at index $i",
            )
        }
    }

    private fun setS3Client(s3FileService: S3FileService, s3Client: S3Client) {
        val s3ClientField = S3FileService::class.declaredMemberProperties.find { it.name == "s3Client" }
        s3ClientField?.javaField?.isAccessible = true
        s3ClientField?.javaField?.set(s3FileService, s3Client)
    }

    private fun getS3Client(s3FileService: S3FileService): S3Client? {
        val s3ClientField = S3FileService::class.declaredMemberProperties.find { it.name == "s3Client" }
        s3ClientField?.javaField?.isAccessible = true
        return s3ClientField?.javaField?.get(s3FileService) as S3Client?
    }
}
