plugins {
    id("com.hellofresh.sdf.kotlin-conventions")
}

group = "$group.${project.name}"

dependencies {
    implementation(libs.aws.sdk.s3)
    implementation(libs.aws.sdk.sts)
    implementation(libs.hellofresh.service)

    testImplementation(libs.mockk)
    testImplementation(libs.kotlin.reflect)
    testImplementation(libs.aws.s3.mock)
}

tasks.test {
    jvmArgs("-XX:+EnableDynamicAgentLoading")
}
