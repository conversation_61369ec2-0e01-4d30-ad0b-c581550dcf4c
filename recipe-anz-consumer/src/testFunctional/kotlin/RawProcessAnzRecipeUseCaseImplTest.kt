package com.hellofresh.skuDemandForecast.anzRecipeConsumer

import com.hellofresh.skuDemandForecast.anzRecipeConsumer.schema.public_.Tables.RAW_RECIPE
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzRecipe
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType.CREATED
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals

private const val SKU_CODE = "PHF-00-90514-5"
class RawProcessAnzRecipeUseCaseImplTest : FunctionalTest() {
    @Test
    fun `should create the raw recipe when the create event is received`() {
        val anzRecipe = AnzRecipe.Companion.default(SKU_CODE)
        runBlocking {
            rawAnzRecipeRepository.save(anzRecipe, dsl)
            val rawRecipeRecord = dsl.selectFrom(RAW_RECIPE).fetch().first()
            assertEquals(anzRecipe.recipeIndex, rawRecipeRecord.recipeIndex)
            assertEquals(anzRecipe.country, rawRecipeRecord.country)
            assertEquals(anzRecipe.week, rawRecipeRecord.week)
            assertEquals(anzRecipe.brand, rawRecipeRecord.brand)
            assertEquals(CREATED.name, rawRecipeRecord.eventType)
            val skuPicks = objectMapper.readValue(rawRecipeRecord.value.toString(), RecipeValue::class.java).skus
                .first()
            assertEquals(SKU_CODE, skuPicks.skuCode)
            skuPicks.picks.first().apply {
                assertEquals(2, peopleCount)
                assertEquals(1, picks)
            }
        }
    }
}
