package com.hellofresh.skuDemandForecast.anzRecipeConsumer

import InfraPreparation.getMigratedDataSource
import KafkaInfraPreparation.createKafkaProducer
import KafkaInfraPreparation.kafka
import KafkaInfraPreparation.startKafkaAndCreateTopics
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType.LOG_ERROR_FAIL
import com.hellofresh.sdf.lib.kafka.processor.PollConfig
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.schema.public_.Tables.RAW_RECIPE
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.schema.public_.Tables.RECIPE
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.ProcessAnzRecipeUseCaseImpl
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.ProcessRecipeSubstitutionsUseCase
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.ProcessRecipeSubstitutionsUseCaseImpl
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.PRODUCT_SPECIFICATION_TOPIC
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.RecipeAnzConsumer
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.DcConfig
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RawAnzRecipeRepository
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RawAnzRecipeRepositoryImpl
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RecipeSkuSubstitutionRepository
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RecipeSkuSubstitutionRepositoryImpl
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepository
import com.hellofresh.skuDemandForecast.recipelib.RecipeRepositoryImpl
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.FRIDAY
import java.time.Duration
import java.time.ZoneId
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import kotlin.time.toKotlinDuration
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {
    @BeforeTest
    fun cleanup() {
        dsl.deleteFrom(RECIPE).execute()
        dsl.deleteFrom(RAW_RECIPE).execute()
    }

    @AfterEach
    fun cleanupAfter() {
        captured.clear()
    }

    companion object {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
        lateinit var topicProducer: KafkaProducer<String, ByteArray>
        lateinit var consumerConfig: Map<String, String>
        val captured = mutableMapOf<String, ByteArray>()
        lateinit var app: RecipeAnzConsumer
        private val dataSource = getMigratedDataSource()
        lateinit var dsl: MetricsDSLContext
        lateinit var anzRecipeUseCase: ProcessAnzRecipeUseCaseImpl
        lateinit var rawAnzRecipeRepository: RawAnzRecipeRepository
        lateinit var substitutionUseCase: ProcessRecipeSubstitutionsUseCase
        lateinit var substitutionRepository: RecipeSkuSubstitutionRepository
        private lateinit var recipeRepository: RecipeRepository

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            startKafkaAndCreateTopics(listOf(PRODUCT_SPECIFICATION_TOPIC))

            topicProducer = createKafkaProducer(
                Serdes.String().serializer(),
                ByteArraySerializer(),
            )

            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "sku-demand-forecast.anzRecipeConsumer.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )

            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
                LOG_ERROR_FAIL.name,
            )
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            val registry = SimpleMeterRegistry()
            dsl = DSL.using(dbConfiguration).withMetrics(registry)
            rawAnzRecipeRepository = RawAnzRecipeRepositoryImpl()
            recipeRepository = RecipeRepositoryImpl(dsl)
            substitutionRepository = RecipeSkuSubstitutionRepositoryImpl()
            substitutionUseCase = ProcessRecipeSubstitutionsUseCaseImpl(
                substitutionRepository,
                mapOf(
                    "SY" to DcConfig("SY", ZoneId.systemDefault(), FRIDAY),
                    "MB" to DcConfig("MB", ZoneId.systemDefault(), FRIDAY),
                ),
            )
            anzRecipeUseCase = ProcessAnzRecipeUseCaseImpl(rawAnzRecipeRepository, recipeRepository, registry, substitutionUseCase, dsl)
            app = RecipeAnzConsumer(
                SimpleMeterRegistry(),
                consumerConfig = consumerConfig,
                pollConfig = pollConfig,
                schemaRegistryClient = ProductSpecificationAvro.schemaRegistryClient,
                processAnzRecipeUseCase = anzRecipeUseCase,
            )
        }
    }
}
