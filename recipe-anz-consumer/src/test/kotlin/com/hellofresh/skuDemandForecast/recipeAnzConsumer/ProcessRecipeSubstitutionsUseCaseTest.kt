package com.hellofresh.skuDemandForecast.recipeAnzConsumer

import com.hellofresh.skuDemandForecast.anzRecipeConsumer.default
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.withSubstitution
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.withSubstitutions
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.withSubstitutionsIngredientId
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.Country
import com.hellofresh.skuDemandForecast.models.RecipeIndex
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzRecipe
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.toRecipeSubstitutionKey
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType.UPDATED
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSkuSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.SubIn
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.SubOut
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.DcConfig
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RecipeSkuSubstitutionRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifySequence
import io.mockk.mockk
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProcessRecipeSubstitutionsUseCaseTest {

    private val repository = mockk<RecipeSkuSubstitutionRepository>()
    private val tx = mockk<MetricsDSLContext>()
    private val useCase = ProcessRecipeSubstitutionsUseCaseImpl(
        repository,
        mapOf(
            "SY" to DcConfig("SY", ZoneId.systemDefault(), FRIDAY),
            "MB" to DcConfig("MB", ZoneId.systemDefault(), FRIDAY),
        ),
    )

    @BeforeEach
    fun setup() {
        coEvery { repository.upsert(any(), any()) } returns Unit
        coEvery { repository.delete(any(), any()) } returns Unit
    }

    @Test
    fun `it upserts one sku substitution for one dc`() {
        val anzRecipe = AnzRecipe.withSubstitution(
            recipeSkuCodeAndQty = listOf(Pair("sku-code2", 1)),
            substitutedSkusQtyDc = listOf(Triple("sku-code1", 2, "SY")),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution = listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn(
                                    "sku-code1",
                                    anzRecipe.substitutions.first().skuId,
                                    2,
                                    localDateOfSub(anzRecipe, 0)
                                )
                            ),
                            SubOut("sku-code2", anzRecipe.skus.first().skuId, 1),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it deletes substitutions in no substutions in recipe`() {
        val anzRecipe = AnzRecipe.default("skuCode1")

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerify {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
        }
        coVerify(inverse = true) {
            repository.upsert(any(), any())
        }
    }

    @Test
    fun `it upserts one sku substitution for every dc`() {
        val anzRecipe = AnzRecipe.withSubstitution(
            recipeSkuCodeAndQty = listOf(Pair("sku-code2", 1)),
            substitutedSkusQtyDc = listOf(
                Triple("sku-code1", 2, "SY"),
                Triple("sku-code3", 2, "MB"),
            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn(
                                    "sku-code1",
                                    anzRecipe.substitutions.first().skuId,
                                    2,
                                    localDateOfSub(anzRecipe, 0)
                                )
                            ),
                            SubOut("sku-code2", anzRecipe.skus.first().skuId, 1),
                        ),
                    ),
                ),
                tx
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "MB",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn("sku-code3", anzRecipe.substitutions[1].skuId, 2, localDateOfSub(anzRecipe, 1))
                            ),
                            SubOut("sku-code2", anzRecipe.skus.first().skuId, 1),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it upserts many sku substitution for one dc`() {
        val anzRecipe = AnzRecipe.withSubstitution(
            recipeSkuCodeAndQty = listOf(Pair("sku-code1", 2)),
            substitutedSkusQtyDc = listOf(
                Triple("sku-code2", 1, "SY"),
                Triple("sku-code3", 1, "SY"),
            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn(
                                    "sku-code2",
                                    anzRecipe.substitutions.first().skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 0)
                                ),
                                SubIn(
                                    "sku-code3",
                                    anzRecipe.substitutions[1].skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 1)
                                ),
                            ),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it upserts many sku substituions for every dc`() {
        val anzRecipe = AnzRecipe.withSubstitution(
            recipeSkuCodeAndQty = listOf(Pair("sku-code1", 2)),
            substitutedSkusQtyDc = listOf(
                Triple("sku-code2", 1, "SY"),
                Triple("sku-code3", 1, "SY"),
                Triple("sku-code2", 1, "MB"),
                Triple("sku-code3", 1, "MB"),

            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey()
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn(
                                    "sku-code2",
                                    anzRecipe.substitutions.first().skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 0)
                                ),
                                SubIn(
                                    "sku-code3",
                                    anzRecipe.substitutions[1].skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 1)
                                ),
                            ),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "MB",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn(
                                    "sku-code2",
                                    anzRecipe.substitutions[2].skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 2)
                                ),
                                SubIn(
                                    "sku-code3",
                                    anzRecipe.substitutions[3].skuId,
                                    1,
                                    localDateOfSub(anzRecipe, 3)
                                ),
                            ),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it upserts one sku substituions for many dates`() {
        val plusOne = Date().toInstant().plus(1, ChronoUnit.DAYS)
        val substitution1UUID = UUID.randomUUID().toString()
        val substitution2UUID = UUID.randomUUID().toString()
        val anzRecipe = AnzRecipe.withSubstitutions(
            recipeSkuCodeAndQty = listOf(Pair("sku-code1", 2)),
            substitutions = listOf(
                AnzSubstitution("sku-code2", substitution1UUID, 1, 2, 10, Date(), "SY"),
                AnzSubstitution("sku-code3", substitution2UUID, 1, 2, 10, Date.from(plusOne), "SY"),
            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn("sku-code2", substitution1UUID, 2, localDateOfSub(anzRecipe, 0)),
                                SubIn("sku-code3", substitution2UUID, 2, localDateOfSub(anzRecipe, 1))
                            ),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it upserts all subbed out skus`() {
        val substitution1UUID = UUID.randomUUID().toString()
        val substitution2UUID = UUID.randomUUID().toString()
        val anzRecipe = AnzRecipe.withSubstitutionsIngredientId(
            recipeSkuCodeAndQty = listOf(Triple("sku-code1", 2, 1), Triple("sku-code2", 2, 2)),
            substitutions = listOf(
                AnzSubstitution("sku-code3", substitution1UUID, 1, 2, 10, Date(), "SY"),
                AnzSubstitution("sku-code4", substitution2UUID, 2, 2, 10, Date(), "SY"),
            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey()
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(SubIn("sku-code3", substitution1UUID, 2, LocalDate.now())),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                        RecipeSkuSubstitution(
                            listOf(SubIn("sku-code4", substitution2UUID, 2, LocalDate.now())),
                            SubOut("sku-code2", anzRecipe.skus[1].skuId, 2),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    @Test
    fun `it upserts many sku substitutions for every date`() {
        val plusOne = Date().toInstant().plus(1, ChronoUnit.DAYS)
        val sub1UUID = UUID.randomUUID().toString()
        val sub2UUID = UUID.randomUUID().toString()
        val sub3UUID = UUID.randomUUID().toString()
        val anzRecipe = AnzRecipe.withSubstitutions(
            recipeSkuCodeAndQty = listOf(Pair("sku-code1", 2)),
            substitutions = listOf(
                AnzSubstitution("sku-code2", sub1UUID, 1, 2, 10, Date(), "SY"),
                AnzSubstitution("sku-code3", sub2UUID, 1, 2, 10, Date.from(plusOne), "SY"),
                AnzSubstitution("sku-code2", sub3UUID, 1, 2, 10, Date(), "MB"),
            ),
        )

        runBlocking { useCase(anzRecipe, UPDATED, tx) }

        coVerifySequence {
            repository.delete(
                tx,
                anzRecipe.toRecipeSubstitutionKey(),
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "SY",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(
                                SubIn("sku-code2", sub1UUID, 2, LocalDate.now()),
                                SubIn("sku-code3", sub2UUID, 2, LocalDate.now().plusDays(1))
                            ),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
            repository.upsert(
                RecipeSubstitution(
                    recipeIndex = RecipeIndex(100),
                    country = Country("AU"),
                    week = "2024-W10",
                    brand = "HF",
                    dcCode = "MB",
                    peopleCount = 1,
                    fromDate = LocalDate.of(2024, 3, 1),
                    toDate = LocalDate.of(2024, 3, 7),
                    recipeSkuSubstitution =
                    listOf(
                        RecipeSkuSubstitution(
                            listOf(SubIn("sku-code2", sub3UUID, 2, LocalDate.now())),
                            SubOut("sku-code1", anzRecipe.skus.first().skuId, 2),
                        ),
                    ),
                ),
                tx
            )
        }
    }

    private fun localDateOfSub(anzRecipe: AnzRecipe, subPosition: Int): LocalDate =
        anzRecipe.substitutions[subPosition]
            .date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate()
}
