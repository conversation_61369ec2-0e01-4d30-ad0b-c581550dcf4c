---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  app:
    #Scaling not supported, replica must be 1
    replicaCount: 1
    deploymentStrategy:
      type: RollingUpdate
    containerPorts:
      http: 8080
      http-actuator: 8081
      jmx: '@jmxPort@'
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '1000Mi'
        cpu: '300m'
      limits:
        memory: '1000Mi'
        cpu: '1'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    env:
      HF_DB_PASSWORD: 'vault:@tier@/key-value/data/rds#DB_PASSWORD'
      HF_DB_USERNAME: 'vault:@tier@/key-value/data/rds#DB_USERNAME'
      HF_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/sku-demand-forecast#password'
      HF_SCHEMA_REGISTRY_AIVEN_USERNAME: 'sku-demand-forecast'
      HF_SCHEMA_REGISTRY_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/sku-demand-forecast#password'

    hpa:
      enabled: false
    spotInstance:
      preferred: true
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      initialDelaySeconds: 10
      timeoutSeconds: 3
      failureThreshold: 15
      periodSeconds: 10
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 60
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    terminationGracePeriodSeconds: 60

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
