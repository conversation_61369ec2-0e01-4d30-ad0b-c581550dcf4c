package com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.schema.public_.Tables.RAW_RECIPE
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzRecipe
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.toRecipeSku
import com.hellofresh.skuDemandForecast.recipelib.SkuRecipe
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB

class RawAnzRecipeRepositoryImpl : RawAnzRecipeRepository {
    private val saveRawRecipes = "save-raw-recipes"

    @Suppress("TooGenericExceptionCaught")
    override suspend fun save(rawRecipe: AnzRecipe, tx: MetricsDSLContext) {
        try {
            tx
                .withTagName(saveRawRecipes)
                .insertInto(RAW_RECIPE)
                .columns(
                    RAW_RECIPE.RECIPE_INDEX,
                    RAW_RECIPE.WEEK,
                    RAW_RECIPE.COUNTRY,
                    RAW_RECIPE.BRAND,
                    RAW_RECIPE.PEOPLE_COUNT,
                    RAW_RECIPE.EVENT_TYPE,
                    RAW_RECIPE.VALUE
                )
                .values(
                    rawRecipe.recipeIndex,
                    rawRecipe.week,
                    rawRecipe.country,
                    rawRecipe.brand,
                    rawRecipe.peopleCount,
                    rawRecipe.eventType.name,
                    JSONB.valueOf(
                        objectMapper.writeValueAsString(toRecipeValue(rawRecipe.skus.map { it.toRecipeSku() }))
                    )
                )
                .onDuplicateKeyUpdate()
                .set(RAW_RECIPE.RECIPE_INDEX, rawRecipe.recipeIndex)
                .set(RAW_RECIPE.WEEK, rawRecipe.week)
                .set(RAW_RECIPE.COUNTRY, rawRecipe.country)
                .set(RAW_RECIPE.BRAND, rawRecipe.brand)
                .set(RAW_RECIPE.PEOPLE_COUNT, rawRecipe.peopleCount)
                .set(RAW_RECIPE.EVENT_TYPE, rawRecipe.eventType.name)
                .set(
                    RAW_RECIPE.VALUE,
                    JSONB.valueOf(
                        objectMapper.writeValueAsString(toRecipeValue(rawRecipe.skus.map { it.toRecipeSku() }))
                    )
                )
                .executeAsync()
                .await()
        } catch (exception: Exception) {
            logger.error(
                "Error occurred while persisting raw recipe records $exception"
            )
        }
    }
    private fun toRecipeValue(skus: List<SkuRecipe>) =
        RecipeValue(
            skus.map { sku -> SkuPicks(sku.skuCode, sku.picks.map { Pick(it.key, it.value) }) }
        )
    companion object : Logging {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
