package com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.anzRecipeConsumer.schema.public_.Tables.RECIPE_SKU_SUBSTITUTION
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSubstitutionKey
import kotlinx.coroutines.future.await
import org.jooq.JSONB

class RecipeSkuSubstitutionRepositoryImpl : RecipeSkuSubstitutionRepository {

    private val upsertRecipeSubstitution = "upsert-recipe-substitution"
    override suspend fun upsert(recipeSubstitution: RecipeSubstitution, dslContext: MetricsDSLContext) {
        dslContext
            .withTagName(upsertRecipeSubstitution)
            .insertInto(RECIPE_SKU_SUBSTITUTION)
            .columns(
                RECIPE_SKU_SUBSTITUTION.RECIPE_INDEX,
                RECIPE_SKU_SUBSTITUTION.WEEK,
                RECIPE_SKU_SUBSTITUTION.COUNTRY,
                RECIPE_SKU_SUBSTITUTION.BRAND,
                RECIPE_SKU_SUBSTITUTION.DC_CODE,
                RECIPE_SKU_SUBSTITUTION.PEOPLE_COUNT,
                RECIPE_SKU_SUBSTITUTION.VALUE,
                RECIPE_SKU_SUBSTITUTION.FROM_DATE,
                RECIPE_SKU_SUBSTITUTION.TO_DATE,
            ).values(
                recipeSubstitution.recipeIndex.value,
                recipeSubstitution.week,
                recipeSubstitution.country.toString(),
                recipeSubstitution.brand,
                recipeSubstitution.dcCode,
                recipeSubstitution.peopleCount,
                JSONB.valueOf(objectMapper.writeValueAsString(recipeSubstitution.recipeSkuSubstitution)),
                recipeSubstitution.fromDate,
                recipeSubstitution.toDate,
            ).onDuplicateKeyUpdate()
            .set(RECIPE_SKU_SUBSTITUTION.RECIPE_INDEX, recipeSubstitution.recipeIndex.value)
            .set(RECIPE_SKU_SUBSTITUTION.WEEK, recipeSubstitution.week)
            .set(RECIPE_SKU_SUBSTITUTION.COUNTRY, recipeSubstitution.country.toString())
            .set(RECIPE_SKU_SUBSTITUTION.BRAND, recipeSubstitution.brand)
            .set(RECIPE_SKU_SUBSTITUTION.DC_CODE, recipeSubstitution.dcCode)
            .set(RECIPE_SKU_SUBSTITUTION.PEOPLE_COUNT, recipeSubstitution.peopleCount)
            .set(RECIPE_SKU_SUBSTITUTION.FROM_DATE, recipeSubstitution.fromDate)
            .set(RECIPE_SKU_SUBSTITUTION.TO_DATE, recipeSubstitution.toDate)
            .set(
                RECIPE_SKU_SUBSTITUTION.VALUE,
                JSONB.valueOf(objectMapper.writeValueAsString(recipeSubstitution.recipeSkuSubstitution)),
            )
            .executeAsync()
            .await()
    }

    override suspend fun delete(tx: MetricsDSLContext, recipeSubstitutionKey: RecipeSubstitutionKey) {
        tx.deleteFrom(
            RECIPE_SKU_SUBSTITUTION,
        )
            .where(
                listOf(
                    RECIPE_SKU_SUBSTITUTION.RECIPE_INDEX.eq(recipeSubstitutionKey.recipeIndex.value),
                    RECIPE_SKU_SUBSTITUTION.WEEK.eq(recipeSubstitutionKey.week),
                    RECIPE_SKU_SUBSTITUTION.COUNTRY.eq(recipeSubstitutionKey.country.toString()),
                    RECIPE_SKU_SUBSTITUTION.BRAND.eq(recipeSubstitutionKey.brand),
                    RECIPE_SKU_SUBSTITUTION.PEOPLE_COUNT.eq(recipeSubstitutionKey.peopleCount),
                ),
            )
            .executeAsync()
            .await()
    }

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
