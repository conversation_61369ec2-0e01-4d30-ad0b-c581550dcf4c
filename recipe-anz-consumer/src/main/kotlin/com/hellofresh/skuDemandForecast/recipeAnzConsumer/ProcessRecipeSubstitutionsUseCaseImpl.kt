package com.hellofresh.skuDemandForecast.recipeAnzConsumer

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.Country
import com.hellofresh.skuDemandForecast.models.RecipeIndex
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzRecipe
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzSku
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.toRecipeSubstitutionKey
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType.CREATED
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType.UPDATED
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSkuSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.SubIn
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.SubOut
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.DcConfig
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.repository.RecipeSkuSubstitutionRepository
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.LocalDate
import java.util.Date
import org.apache.logging.log4j.kotlin.Logging

class ProcessRecipeSubstitutionsUseCaseImpl(
    private val substitutionRepository: RecipeSkuSubstitutionRepository,
    private val dcConfigs: Map<String, DcConfig>
) : ProcessRecipeSubstitutionsUseCase {

    override suspend fun invoke(anzRecipe: AnzRecipe, eventType: RecipeEventType, tx: MetricsDSLContext) {
        if (eventType == CREATED) {
            error("Created event not supported for substitutions for Recipe: $anzRecipe")
        }
        val skusByIngredientId = anzRecipe.skus.associateBy { it.mealKitIngredientId }
        val substitutionByDc = anzRecipe.substitutions.groupBy { it.dc }

        substitutionRepository.delete(tx, anzRecipe.toRecipeSubstitutionKey())

        if (eventType == UPDATED) {
            insertUpdatedSubstitutions(substitutionByDc, skusByIngredientId, anzRecipe, tx)
        }
    }

    private suspend fun insertUpdatedSubstitutions(
        substitutionByDc: Map<String, List<AnzSubstitution>>,
        skusByIngredientId: Map<Int, AnzSku>,
        anzRecipe: AnzRecipe,
        tx: MetricsDSLContext
    ) {
        substitutionByDc.forEach { (dc, subs) ->
            val subsByIngredientId = subs.groupBy { it.mealKitIngredientId }
            val list = mutableListOf<RecipeSkuSubstitution>()
            subsByIngredientId.forEach { (mealKitIngredientId, subsForMealKitId) ->
                if (skusByIngredientId.containsKey(mealKitIngredientId)) {
                    val subIns = subsForMealKitId.map {
                        SubIn(
                            it.skuCode,
                            it.skuId,
                            it.quantity,
                            substitutionDate(dc, it.date),
                        )
                    }
                    list.add(
                        RecipeSkuSubstitution(
                            subIns,
                            subOutSku(mealKitIngredientId, skusByIngredientId, anzRecipe),
                        ),
                    )
                }
            }
            if (list.isNotEmpty()) {
                val productionStartEndDate = productionStartEndDate(dc, anzRecipe.week)
                if (productionStartEndDate != null) {
                    val substitution = RecipeSubstitution(
                        RecipeIndex(anzRecipe.recipeIndex),
                        anzRecipe.week,
                        Country(anzRecipe.country),
                        brand = anzRecipe.brand,
                        dcCode = dc,
                        peopleCount = anzRecipe.peopleCount,
                        fromDate = productionStartEndDate.first,
                        toDate = productionStartEndDate.second,
                        recipeSkuSubstitution = list,
                    )
                    substitutionRepository.upsert(substitution, tx)
                }
            }
        }
    }

    private fun subOutSku(
        mealKitIngredientId: Int,
        skusByIngredientId: Map<Int, AnzSku>,
        anzRecipe: AnzRecipe
    ): SubOut {
        val subOutSku = skusByIngredientId[mealKitIngredientId]
            ?: error("Meal kit ingredient id does not match for subbed out sku for recipe $anzRecipe")
        val picks = subOutSku.picks[anzRecipe.peopleCount]
            ?: error("Picks not present for subbed out sku for recipe $anzRecipe")
        val subOut = SubOut(subOutSku.skuCode, subOutSku.skuId, picks)
        return subOut
    }

    private fun productionStartEndDate(dc: String, week: String): Pair<LocalDate, LocalDate>? {
        val dcConfig = dcConfigs[dc]
        if (dcConfig != null) {
            val dcWeek = DcWeek(week)
            return dcWeek.getStartDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId) to
                dcWeek.getLastDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId)
        } else {
            logger.warn("DC config not present for DC $dc in DC config.")
            return null
        }
    }

    private fun substitutionDate(dc: String, date: Date): LocalDate {
        val zoneId = dcConfigs[dc]?.zoneId
            ?: error("Zone Id not present for DC $dc in DC config.")
        return date.toInstant().atZone(zoneId).toLocalDate()
    }

    companion object : Logging
}
