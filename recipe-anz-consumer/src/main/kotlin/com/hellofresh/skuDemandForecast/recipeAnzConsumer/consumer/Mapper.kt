package com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer

import com.hellofresh.skuDemandForecast.models.Country
import com.hellofresh.skuDemandForecast.models.RecipeIndex
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeSubstitutionKey
import com.hellofresh.skuDemandForecast.recipelib.SkuRecipe
import com.hellofresh.skuDemandForecast.recipelib.UploadedRecipe

private const val UNKNOWN_PRODUCT_FAMILY = "UNKNOWN"
private const val UNKNOWN_LOCALE = ""

fun AnzRecipe.toUploadedRecipe() =
    UploadedRecipe(
        recipeIndex = this.recipeIndex,
        week = this.week,
        country = this.country,
        skus = this.skus.map { it.toRecipeSku() },
        brand = this.brand,
        family = UNKNOWN_PRODUCT_FAMILY,
        sourceId = null,
        locale = UNKNOWN_LOCALE,
    )

fun AnzSku.toRecipeSku() =
    SkuRecipe(
        this.skuCode,
        this.picks
    )

fun AnzRecipe.toRecipeSubstitutionKey() =
    RecipeSubstitutionKey(
        RecipeIndex(this.recipeIndex),
        this.week,
        Country(this.country),
        this.brand,
        this.peopleCount
    )
