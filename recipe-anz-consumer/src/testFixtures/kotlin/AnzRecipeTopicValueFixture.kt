package com.hellofresh.skuDemandForecast.anzRecipeConsumer

import kotlin.random.Random

@Suppress("MagicNumber")
fun AnzRecipeTopicValue.Companion.default() = with(Random(System.nanoTime())) {
    AnzRecipeTopicValue(
        index = 1000,
        size = 2,
        week = "2024-W10",
        brand = "HF",
        country = "AU",
        skus = listOf(
            AnzSkuTopicValue(skuCode = "SPI-000-0000", peopleCount = 2, qty = 1),
            AnzSkuTopicValue(skuCode = "SPI-000-0001", peopleCount = 2, qty = 1),
        ),
    )
}
