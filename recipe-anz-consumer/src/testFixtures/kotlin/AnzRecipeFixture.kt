package com.hellofresh.skuDemandForecast.anzRecipeConsumer

import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzRecipe
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzSku
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.consumer.AnzSubstitution
import com.hellofresh.skuDemandForecast.recipeAnzConsumer.model.RecipeEventType.CREATED
import java.util.Date
import java.util.UUID

private const val WEEK = "2024-W10"

fun AnzRecipe.Companion.default(skuCode: String) = AnzRecipe(
    recipeIndex = 100,
    country = "AU",
    week = WEEK,
    brand = "HF",
    peopleCount = 2,
    skus = listOf(
        AnzSku(
            skuCode,
            UUID.randomUUID().toString(),
            mapOf(2 to 1),
            1,
        ),
    ),
    eventType = CREATED,
    substitutions = emptyList(),
)

fun AnzRecipe.Companion.withSubstitution(
    recipeSkuCodeAndQty: List<Pair<String, Int>>,
    substitutedSkusQtyDc: List<Triple<String, Int, String>>
) = AnzRecipe(
    recipeIndex = 100,
    country = "AU",
    week = WEEK,
    brand = "HF",
    peopleCount = 1,
    skus = recipeSkuCodeAndQty.map {
        AnzSku(
            it.first,
            UUID.randomUUID().toString(),
            mapOf(1 to it.second),
            1,
        )
    },
    eventType = CREATED,
    substitutions = substitutedSkusQtyDc.map {
        AnzSubstitution(it.first, UUID.randomUUID().toString(), 1, it.second, 2, date = Date(), it.third)
    },
)

fun AnzRecipe.Companion.withSubstitutions(
    recipeSkuCodeAndQty: List<Pair<String, Int>>,
    substitutions: List<AnzSubstitution>
) = AnzRecipe(
    recipeIndex = 100,
    country = "AU",
    week = WEEK,
    brand = "HF",
    peopleCount = 1,
    skus = recipeSkuCodeAndQty.map {
        AnzSku(
            it.first,
            UUID.randomUUID().toString(),
            mapOf(1 to it.second),
            1,
        )
    },
    eventType = CREATED,
    substitutions = substitutions,
)

fun AnzRecipe.Companion.withSubstitutionsIngredientId(
    recipeSkuCodeAndQty: List<Triple<String, Int, Int>>,
    substitutions: List<AnzSubstitution>
) = AnzRecipe(
    recipeIndex = 100,
    country = "AU",
    week = WEEK,
    brand = "HF",
    peopleCount = 1,
    skus = recipeSkuCodeAndQty.map {
        AnzSku(
            it.first,
            UUID.randomUUID().toString(),
            mapOf(1 to it.second),
            it.third,
        )
    },
    eventType = CREATED,
    substitutions = substitutions,
)
