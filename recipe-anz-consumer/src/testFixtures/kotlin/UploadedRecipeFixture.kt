package com.hellofresh.skuDemandForecast.anzRecipeConsumer

import com.hellofresh.skuDemandForecast.recipelib.SkuRecipe
import com.hellofresh.skuDemandForecast.recipelib.UploadedRecipe
import kotlin.random.Random

@Suppress("MagicNumber")
fun UploadedRecipe.Companion.default() = with(Random(System.nanoTime())) {
    UploadedRecipe(
        recipeIndex = 1000,
        week = "2024-W10",
        family = "test-product-family",
        locale = "",
        country = "AU",
        skus = listOf(
            SkuRecipe(
                "SKU1",
                mapOf(1 to 2, 2 to 4),
            )
        ),
        sourceId = null,
        brand = "HF",
    )
}
