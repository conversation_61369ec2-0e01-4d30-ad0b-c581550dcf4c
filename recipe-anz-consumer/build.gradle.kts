@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

group = "com.hellofresh.skuDemandForecast.anzRecipeConsumer"
description = "Consume recipe topic for ANZ"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib"))
    implementation(project(":lib:kafka"))
    implementation(project(":lib:db"))
    implementation(project(":lib:models"))
    implementation(project(":lib:models:distribution-center"))
    implementation(project(":recipe-lib"))
    api(libs.jackson.kotlin)
    api(libs.jackson.jsr310)
    api(libs.jooq.coroutine)
    api(libs.hellofresh.schemaregistry)
    api(libs.kafka.schema.registry.client)

    testImplementation(libs.mockk)
    testImplementation(testFixtures(project(":lib")))
    testImplementation(testFixtures(project(":lib:kafka")))
    testFunctionalImplementation(project(":recipe-lib"))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "recipe|raw_recipe|recipe_sku_substitution|dc_config"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
