@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skuDemandForecast.api.substitution

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.api.FunctionalTest
import com.hellofresh.skuDemandForecast.api.generated.model.AddSubstitutionRequest
import com.hellofresh.skuDemandForecast.api.generated.model.DemandByDate
import com.hellofresh.skuDemandForecast.api.generated.model.EditSubstitutionRequestWithVersion
import com.hellofresh.skuDemandForecast.api.generated.model.RetrieveDetailSubstitutionResponse
import com.hellofresh.skuDemandForecast.api.generated.model.RetrieveOverviewSubstitutionResponse
import com.hellofresh.skuDemandForecast.api.generated.model.RetrieveOverviewSubstitutionResponses
import com.hellofresh.skuDemandForecast.api.generated.model.SkuDemand
import com.hellofresh.skuDemandForecast.api.generated.model.SubstitutionDetail
import com.hellofresh.skuDemandForecast.api.generated.model.SubstitutionKey
import com.hellofresh.skuDemandForecast.api.ktor.JwtCredentials
import com.hellofresh.skuDemandForecast.api.ktor.configureJwtAuth
import com.hellofresh.skuDemandForecast.api.ktor.customJackson
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested

@DisplayName("Substitution Api Test")
class SubstitutionApiTest : FunctionalTest() {
    @Nested
    @DisplayName("When retrieving substitution with version")
    inner class Retrieve {
        @Test fun `should return ok status while retrieving substitutions with version for valid request`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest().copy(
                    fromDate = LocalDate.parse("2023-01-01"),
                    toDate = LocalDate.parse("2023-01-08"),
                )
                createSubstitution(createSubstitutionRequest = substitutionRequest)
                retrieveSubstitutionsWithVersion().apply {
                    val substitutions = objectMapper.readValue<List<RetrieveOverviewSubstitutionResponses>>(
                        bodyAsText(),
                    )
                    assertNotNull(substitutions)
                    assertTrue(substitutions.size == 1)
                    assertEquals(
                        substitutionRequest.substitutionDetail,
                        substitutions[0].substitutions[0].substitutionDetail
                    )
                    assertEquals(HttpStatusCode.OK, status)
                }
            }
        }

        @Test fun `should retrieve a substitution with version for the given request`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                createSubstitution("VE", substitutionRequest).apply {
                    val substitutionKey = objectMapper.readValue<SubstitutionKey>(bodyAsText())
                    retrieveSubstitutionWithVersion(substitutionKey.id).apply {
                        val retrieveDetailSubstitutionResponses = objectMapper.readValue<List<RetrieveDetailSubstitutionResponse>>(
                            bodyAsText(),
                        )
                        assertEquals(HttpStatusCode.OK, this.status)
                        assertEquals(
                            substitutionRequest.substitutionDetail,
                            retrieveDetailSubstitutionResponses.first().substitutionDetail,
                        )
                        retrieveDetailSubstitutionResponses.first().apply {
                            assertNull(substitutionDetail.skuOut.picks)
                            assertFalse(substitutionDetail.skuOut.demand.first().fullDay!!)
                            assertNull(substitutionDetail.skuIn.first().picks)
                            assertFalse(substitutionDetail.skuIn.first().demand.first().fullDay!!)
                        }
                    }
                }
            }
        }

        @Test fun `should return not found status while retrieving a given substitution with version for valid request but no data`() {
            runBlocking {
                retrieveSubstitutionWithVersion(UUID.randomUUID()).apply {
                    assertEquals("[]", bodyAsText())
                    assertEquals(HttpStatusCode.OK, status)
                }
            }
        }

        @Test fun `should return unauthorized status while retrieving a given substitution with version for invalid jwt token`() {
            runBlocking {
                val response = retrieveSubstitutionWithVersion(UUID.randomUUID(), false)
                assertEquals(HttpStatusCode.Unauthorized, response.status)
            }
        }

        @Test fun `should return bad request status while retrieving substitutions with version for invalid request`() {
            runBlocking {
                retrieveSubstitutionsWithVersion(params = mapOf("to" to "")).apply {
                    assertEquals(HttpStatusCode.InternalServerError, status)
                }
            }
        }

        @Test fun `should return unauthorized status while retrieving substitutions with version for invalid jwt token`() {
            runBlocking {
                retrieveSubstitutionsWithVersion(authenticationEnabled = false).apply {
                    assertEquals(HttpStatusCode.Unauthorized, status)
                }
            }
        }

        @Test fun `should return ok status while retrieving substitutions with version for valid request but no data`() {
            runBlocking {
                retrieveSubstitutionsWithVersion(params = mapOf("from" to "2021-01-01", "to" to "2021-01-08")).apply {
                    val substitutions = objectMapper.readValue<List<List<RetrieveOverviewSubstitutionResponse>>>(
                        bodyAsText(),
                    )
                    assertTrue(substitutions.isEmpty())
                    assertEquals(HttpStatusCode.OK, status)
                }
            }
        }
    }

    @Nested
    @DisplayName("When creating substitution with version")
    inner class Creation {
        @Test fun `should create substitution for the given request`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                createSubstitution(createSubstitutionRequest = substitutionRequest)
                retrieveSubstitutionsWithVersion(
                    params = mapOf(
                        "from" to substitutionRequest.fromDate.toString(),
                        "to" to substitutionRequest.toDate.toString(),
                    ),
                ).apply {
                    val substitutions = objectMapper.readValue<List<RetrieveOverviewSubstitutionResponses>>(
                        bodyAsText(),
                    )
                    assertTrue(substitutions.isNotEmpty())
                    assertEquals(
                        substitutionRequest.substitutionDetail.skuOut.skuId,
                        substitutions[0].substitutions[0].skuIdOut,
                    )
                    assertEquals(substitutionRequest.reference, substitutions[0].substitutions[0].reference)
                    assertEquals(HttpStatusCode.OK, status)
                }
            }
        }

        @Test fun `should return bad request status while creating substitutions for invalid request - empty skuOut`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                val response = createSubstitution(
                    "VE",
                    substitutionRequest.copy(
                        substitutionDetail = SubstitutionDetail(
                            skuOut = SkuDemand("", emptyList()),
                            skuIn = substitutionRequest.substitutionDetail.skuIn,
                        ),
                    ),
                )
                assertEquals(HttpStatusCode.BadRequest, response.status)
            }
        }

        @Test fun `should return bad request status while creating substitutions where skuOut has empty picks with fullday enabled`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                val response = createSubstitution(
                    "VE",
                    substitutionRequest.copy(
                        substitutionDetail = SubstitutionDetail(
                            skuOut = substitutionRequest.substitutionDetail.skuOut.copy(
                                skuId = substitutionRequest.substitutionDetail.skuOut.skuId,
                                demand = listOf(DemandByDate(LocalDate.now(), 10, true)),
                                picks = null,
                            ),
                            skuIn = substitutionRequest.substitutionDetail.skuIn,
                        ),
                    ),
                )
                assertEquals(HttpStatusCode.BadRequest, response.status)
            }
        }

        @Test fun `should return bad request status while creating substitutions where skuIn has empty picks with fullday enabled`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                val response = createSubstitution(
                    "VE",
                    substitutionRequest.copy(
                        substitutionDetail = SubstitutionDetail(
                            skuOut = substitutionRequest.substitutionDetail.skuOut,
                            skuIn = listOf(
                                SkuDemand(
                                    skuId = substitutionRequest.substitutionDetail.skuIn.first().skuId,
                                    demand = listOf(DemandByDate(LocalDate.now(), 10, true)),
                                    picks = null,
                                ),
                            ),
                        ),
                    ),
                )
                assertEquals(HttpStatusCode.BadRequest, response.status)
            }
        }

        @Test fun `should return unauthorized status while creating substitutions for invalid jwt token`() {
            runBlocking {
                createSubstitution("VE", createSubstitutionRequest(), false).apply {
                    assertEquals(HttpStatusCode.Unauthorized, status)
                }
            }
        }
    }

    @Nested
    @DisplayName("When editing substitution with version")
    inner class Edit {
        @Test fun `should edit substitution for the given request`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                createSubstitution("VE", substitutionRequest).apply {
                    val substitutionKey = objectMapper.readValue<SubstitutionKey>(bodyAsText())
                    val substitutionEditRequest = createSubstitutionEditRequestWithVersion(substitutionKey.version)
                    editSubstitutionWithVersion(substitutionKey.id, substitutionEditRequest)
                    retrieveSubstitutionsWithVersion(
                        params = mapOf(
                            "from" to substitutionRequest.fromDate.toString(),
                            "to" to substitutionRequest.toDate.toString(),
                        ),
                    ).apply {
                        val substitutions = objectMapper
                            .readValue<List<RetrieveOverviewSubstitutionResponses>>(bodyAsText())
                        assertNotNull(substitutions)
                        assertTrue(substitutions.isNotEmpty())
                        with(substitutions[0].substitutions.maxByOrNull { it.lastEdited }!!) {
                            assertEquals(substitutionEditRequest.substitutionDetail.skuOut.skuId, this.skuIdOut)
                            assertEquals(substitutionRequest.reference, this.reference)
                        }
                        assertEquals(HttpStatusCode.OK, status)
                    }
                }
            }
        }

        @Test fun `should return not found for a missing substitution id`() {
            runBlocking {
                val substitutionEditRequest = createSubstitutionEditRequestWithVersion(0)
                editSubstitutionWithVersion(UUID.randomUUID(), substitutionEditRequest)
                    .apply {
                        assertTrue(bodyAsText().isEmpty())
                        assertEquals(HttpStatusCode.NotFound, status)
                    }
            }
        }

        @Test fun `should return bad request status while editing substitutions for invalid request - empty skuOut`() {
            runBlocking {
                val substitutionRequest = createSubstitutionRequest()
                val createResponse = createSubstitution(createSubstitutionRequest = substitutionRequest)
                val substitutionKey = objectMapper.readValue<SubstitutionKey>(createResponse.bodyAsText())

                val editResponse = editSubstitutionWithVersion(
                    substitutionKey.id,
                    EditSubstitutionRequestWithVersion(
                        version = substitutionKey.version,
                        disabled = false,
                        substitutionDetail = SubstitutionDetail(
                            skuOut = SkuDemand("", emptyList()),
                            skuIn = substitutionRequest.substitutionDetail.skuIn,
                        ),
                    ),
                )
                assertEquals(HttpStatusCode.BadRequest, editResponse.status)
            }
        }

        @Test fun `should return unauthorized status while editing substitution for invalid jwt token`() {
            runBlocking {
                editSubstitutionWithVersion(
                    UUID.randomUUID(),
                    createSubstitutionEditRequestWithVersion(0),
                    false
                ).apply {
                    assertEquals(HttpStatusCode.Unauthorized, status)
                }
            }
        }
    }

    private fun retrieveSubstitutionsWithVersion(
        dcCode: String = "VE",
        params: Map<String, String> = mapOf("from" to "2023-01-01", "to" to "2023-01-08"),
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/demand/$dcCode/substitution/v1",
            params = params,
            authenticationEnabled = authenticationEnabled,
        )

    private fun createSubstitution(
        dcCode: String = "VE",
        createSubstitutionRequest: AddSubstitutionRequest,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/demand/$dcCode/substitution/v1",
            method = HttpMethod.Post,
            jsonBody = objectMapper.writeValueAsString(createSubstitutionRequest),
            authenticationEnabled = authenticationEnabled,
        )

    private fun editSubstitutionWithVersion(
        substitutionId: UUID,
        editSubstitutionRequest: EditSubstitutionRequestWithVersion,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/substitution/$substitutionId/v1",
            method = HttpMethod.Put,
            jsonBody = objectMapper.writeValueAsString(editSubstitutionRequest),
            authenticationEnabled = authenticationEnabled,
        )

    private fun retrieveSubstitutionWithVersion(
        substitutionId: UUID,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/substitution/$substitutionId/v1",
            authenticationEnabled = authenticationEnabled,
        )

    fun createSubstitutionRequest() =
        AddSubstitutionRequest(
            fromDate = LocalDate.now(),
            toDate = LocalDate.now(),
            substitutionDetail = createSubstitutionDetailRequest(),
            reference = "test-reference",
        )

    fun createSubstitutionEditRequestWithVersion(version: Int, disabled: Boolean = false) =
        EditSubstitutionRequestWithVersion(
            version = version,
            disabled = disabled,
            substitutionDetail = createSubstitutionDetailRequest(),
        )

    private fun createSubstitutionDetailRequest() = SubstitutionDetail(
        skuOut = SkuDemand(
            skuId = UUID.randomUUID().toString(),
            demand = listOf(
                DemandByDate(
                    LocalDate.now(),
                    10,
                ),
            ),
        ),
        skuIn = listOf(
            SkuDemand(
                UUID.randomUUID().toString(),
                listOf(DemandByDate(LocalDate.now(), 10)),
            ),
            SkuDemand(
                UUID.randomUUID().toString(),
                listOf(DemandByDate(LocalDate.now(), 15)),
            ),
        ),
    )

    fun clientRequest(
        path: String = "/demand/VE/substitution",
        params: Map<String, String> = emptyMap(),
        method: HttpMethod = HttpMethod.Get,
        jsonBody: String? = null,
        authenticationEnabled: Boolean = true,
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                routing {
                    this.substitution(
                        substitutionService,
                        timeout,
                    ).install(ContentNegotiation) { customJackson() }
                }
            }
            response = client.request(path) {
                this.method = method
                jsonBody?.let { setBody(it) }
                params.forEach { (key, value) -> parameter(key, value) }
                header(HttpHeaders.ContentType, Application.Json.toString())
                if (authenticationEnabled) {
                    this.addAuthHeader("test-email", "test-author-name")
                }
            }
        }
        return response
    }
}
