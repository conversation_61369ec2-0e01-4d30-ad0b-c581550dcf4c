@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skuDemandForecast.api

import InfraPreparation.getMigratedDataSource
import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.api.crossdock.CrossDockedApiRepository
import com.hellofresh.skuDemandForecast.api.crossdock.CrossDockedApiRepositoryImpl
import com.hellofresh.skuDemandForecast.api.crossdock.CrossdockedDemandService
import com.hellofresh.skuDemandForecast.api.demand.DemandRepository
import com.hellofresh.skuDemandForecast.api.demand.DemandRepositoryJooqImpl
import com.hellofresh.skuDemandForecast.api.generated.model.UploadViolationError
import com.hellofresh.skuDemandForecast.api.ktor.JwtCredentials
import com.hellofresh.skuDemandForecast.api.ktor.configureJwtAuth
import com.hellofresh.skuDemandForecast.api.ktor.customJackson
import com.hellofresh.skuDemandForecast.api.schema.public_.Tables
import com.hellofresh.skuDemandForecast.api.substitution.SubstitutionService
import com.hellofresh.skuDemandForecast.api.uploads.HF_FILE_SOURCE_HEADER
import com.hellofresh.skuDemandForecast.api.uploads.HF_FILE_TIMESTAMP_HEADER
import com.hellofresh.skuDemandForecast.api.uploads.ImportsRepository
import com.hellofresh.skuDemandForecast.api.uploads.UploadedFile
import com.hellofresh.skuDemandForecast.api.uploads.UploadsRepository
import com.hellofresh.skuDemandForecast.api.uploads.UploadsRepositoryImpl
import com.hellofresh.skuDemandForecast.api.uploads.UploadsService
import com.hellofresh.skuDemandForecast.api.uploads.uploads
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemand
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemandRepository
import com.hellofresh.skuDemandForecast.crossdocking.automatic.CrossdockedRule
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestDynamicConfigClient
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.Tables.DC_CONFIG
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.lib.substitution.repo.SubstitutionRepository
import com.hellofresh.skudemandforecast.model.fileupload.FileSource.INVENTORY
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.Tables.SKU_SPECIFICATION
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.tables.records.SkuSpecificationRecord
import com.hellofresh.skudemandforecast.skuspecification.repo.SkuSpecificationRepository
import com.hellofresh.skudemandforecast.skuspecification.repo.SkuSpecificationRepositoryImpl
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.headers
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.auth.AuthScheme
import io.ktor.http.auth.HttpAuthHeader
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import io.ktor.utils.io.core.buildPacket
import io.ktor.utils.io.core.writeFully
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.clearAllMocks
import java.io.File
import java.nio.file.Files
import java.time.Duration
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.Random
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {
    val jwtSecret = "testSecret"
    val authorEmail = "test-user-email"
    val authorName = "test-user-name"
    private val marketParamName = "market"
    private val countryParamName = "country"
    private val jwksURI = "https://test.com"

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.AGGREGATED_DEMAND).execute()
        dsl.deleteFrom(Tables.SKU_SUBSTITUTION).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dsl.deleteFrom(Tables.IMPORTS).execute()
        clearAllMocks()
    }

    internal fun HttpRequestBuilder.addAuthHeader(authorEmail: String, authorName: String) =
        this.header(
            HttpHeaders.Authorization,
            HttpAuthHeader.Single(
                AuthScheme.Bearer,
                buildJwtToken {
                    this.withClaim("email", authorEmail)
                    this.withClaim("metadata", mapOf("name" to authorName))
                },
            ),
        )

    private fun buildJwtToken(jwtBuilderConf: JWTCreator.Builder.() -> Unit) =
        JWT.create().withClaim("sub", UUID.randomUUID().toString())
            .also(jwtBuilderConf)
            .sign(Algorithm.HMAC256(jwtSecret))

    internal fun createUploadedFile(fileName: String, market: String = "DACH"): UploadedFile =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            UploadedFile(
                fileName,
                OffsetDateTime.now(),
                market,
                Files.readAllBytes(it.toPath()),
                INVENTORY,
            )
        }

    internal fun createRandomUploadFile(contentSize: Int = 10, offsetDateTime: OffsetDateTime = OffsetDateTime.now()) =
        UploadedFile(
            "upload_random_file_${Random().nextInt()}.csv",
            offsetDateTime,
            UUID.randomUUID().toString(),
            ByteArray(contentSize).apply { Random().nextBytes(this) },
            INVENTORY,
        )

    fun uploadsRequest(
        market: String,
        files: List<UploadedFile>,
        uploadsService: UploadsService,
        source: String = "",
        country: String = "DE",
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                routing {
                    this.uploads(uploadsService, timeout).install(ContentNegotiation) { customJackson() }
                }
            }
            response = client.post("/uploads") {
                parameter(countryParamName, country)
                parameter(marketParamName, market)
                setBody(
                    MultiPartFormDataContent(
                        formData {
                            headers {
                                append(HF_FILE_SOURCE_HEADER, source)
                            }
                            files.forEach { uploadedFile ->
                                this.appendInput(
                                    key = uploadedFile.name,
                                    headers = Headers.build {
                                        append(
                                            HttpHeaders.ContentDisposition,
                                            "filename=${uploadedFile.name}",
                                        )
                                        append(
                                            HttpHeaders.ContentEncoding,
                                            ContentType.Text.CSV.contentType,
                                        )
                                        uploadedFile.timestamp.also {
                                            append(
                                                HF_FILE_TIMESTAMP_HEADER,
                                                it.toEpochSecond().toString(),
                                            )
                                        }
                                    },
                                    size = uploadedFile.content.size.toLong(),
                                ) { buildPacket { writeFully(uploadedFile.content) } }
                            }
                        },
                    ),
                )
                addAuthHeader(authorEmail, authorName)
            }
        }
        return response
    }

    fun getUploadedFileRequest(
        uploadsService: UploadsService,
        market: String? = null
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                routing {
                    this.uploads(uploadsService, timeout).install(ContentNegotiation) { customJackson() }
                }
            }
            val url = if (market == null) "/uploads" else "/uploads?market=$market"
            response = client.get(url) {
                addAuthHeader(authorEmail, authorName)
            }
        }
        return response
    }

    internal fun assertUploadsBadRequest(
        uploadedFiles: List<UploadedFile>,
        uploadsService: UploadsService,
        message: String = "Bad request",
        source: String = ""
    ) {
        assertUploadsByHttpStatus(uploadedFiles, uploadsService, HttpStatusCode.BadRequest, message, source)
    }

    internal fun assertUploadsAccepted(
        uploadedFiles: List<UploadedFile>,
        uploadsService: UploadsService,
        message: String = "Accepted",
        source: String = "",
        country: String = "DE",
    ) {
        assertUploadsByHttpStatus(uploadedFiles, uploadsService, HttpStatusCode.Accepted, message, source, country)
    }

    @Suppress("LongParameterList")
    private fun assertUploadsByHttpStatus(
        uploadedFiles: List<UploadedFile>,
        uploadsService: UploadsService,
        httpStatus: HttpStatusCode,
        message: String = "Bad request",
        source: String = "",
        country: String = "DE"
    ) {
        uploadsRequest(uploadedFiles.firstOrNull()?.market ?: "", uploadedFiles, uploadsService, source, country)
            .apply {
                assertEquals(httpStatus, status)
                readUploadResultsBodyError(this)?.let {
                    assertTrue(it.isNotEmpty(), message)
                }
            }
    }

    private fun readUploadResultsBodyError(httpResponse: HttpResponse) =
        runCatching {
            objectMapper.readValue<UploadViolationError>(runBlocking { httpResponse.bodyAsText() }).errors
        }.getOrNull()

    internal fun createDcConfig(dcCode: String, market: String = "DACH") = DcConfigRecord().apply {
        this.dcCode = dcCode
        this.market = market
        zoneId = "Europe/Berlin"
        enabled = true
        productionStart = "FRIDAY"
    }

    internal fun createSkuSpecification(
        skuCode: String = UUID.randomUUID().toString(),
        market: String = "DACH"
    ) = SkuSpecificationRecord().apply {
        this.id = UUID.randomUUID()
        this.market = market
        this.code = skuCode
        this.packaging = ""
    }

    @Suppress("LongParameterList")
    internal fun createCrossdockedDemandTestData(
        skuId: UUID = UUID.randomUUID(),
        version: Int = 1,
        dcFrom: String = "BX",
        dcTo: String = "VB",
        weekStart: String = "2024-W35",
        action: CrossDockAction = CrossDockAction.DUPLICATE,
        userId: String = "<EMAIL>",
    ) =
        CrossDockedDemand(
            skuId,
            dcFrom,
            weekStart,
            version,
            dcTo,
            action,
            userId,
            false,
        )

    internal fun createCrossdockedDemandRule(regexString: String) =
        dsl.query(
            "INSERT INTO crossdocked_demand_rules (dc_from, dc_to, week_start, rule, action, disabled)\n" +
                "VALUES ('CH', 'CR', '2024-W50', '${objectMapper.writeValueAsString(CrossdockedRule(regexString))}', 'REPLACE', false);\n",
        ).execute()

    fun sampleAggregatedDemandData(skuId1: UUID, skuId2: UUID) =
        listOf(
            mapOf(
                "skuId" to skuId1,
                "skuCode" to "PHF-00-10135-1",
                "productionDate" to LocalDate.parse("2024-12-16"),
                "originalDemand" to 100L,
                "totalQuantity" to 100L,
            ),
            mapOf(
                "skuId" to skuId2,
                "skuCode" to "PHF-00-10135-2",
                "productionDate" to LocalDate.parse("2024-12-17"),
                "originalDemand" to 150L,
                "totalQuantity" to 150L,
            ),
            mapOf(
                "skuId" to UUID.randomUUID(),
                "skuCode" to "PHF-01-10135-2",
                "productionDate" to LocalDate.parse("2024-12-16"),
                "originalDemand" to 250L,
                "totalQuantity" to 250L,
            )
        )

    @Suppress("LongParameterList")
    internal fun createAggregatedDemandTestData(
        skuId: UUID = UUID.randomUUID(),
        dcCode: String = "BX",
        skuCode: String = "PHF-00-10135-3",
        productionDate: LocalDate = LocalDate.now(),
        originalDemand: Long = 0,
        totalQuantity: Long = 0,
    ) = run {
        with(Tables.AGGREGATED_DEMAND) {
            dsl.insertInto(Tables.AGGREGATED_DEMAND)
                .columns(
                    SKU_PARENT_ID,
                    SKU_ID,
                    SKU_CODE,
                    DC_CODE,
                    PRODUCTION_DATE,
                    RECIPE_BREAKDOWN,
                    SOURCE_IDS,
                    ORIGINAL_DEMAND,
                    TOTAL_QUANTITY,
                    DEMAND_DETAILS,
                    PUBLISHED,
                )
                .values(
                    skuId,
                    skuId,
                    skuCode,
                    dcCode,
                    productionDate,
                    JSONB.valueOf("{}"),
                    null,
                    originalDemand,
                    totalQuantity,
                    JSONB.valueOf("{}"),
                    true
                ).execute()
        }
    }

    companion object {
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
        val timeout: Duration = Duration.ofSeconds(10)
        lateinit var dsl: MetricsDSLContext
        lateinit var demandRepo: DemandRepository
        lateinit var substitutionRepository: SubstitutionRepository
        lateinit var substitutionService: SubstitutionService
        lateinit var crossdockedDemandRepo: CrossDockedApiRepository
        lateinit var crossdockedDemandService: CrossdockedDemandService
        lateinit var uploadsRepository: UploadsRepository
        lateinit var importsRepository: ImportsRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var skuSpecificationRepository: SkuSpecificationRepository
        lateinit var uploadsService: UploadsService

        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(5))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            demandRepo = DemandRepositoryJooqImpl(dsl)
            demandRepo = DemandRepositoryJooqImpl(dsl)
            substitutionRepository = SubstitutionRepository(dsl)
            substitutionService = SubstitutionService(substitutionRepository, demandRepo)
            crossdockedDemandRepo = CrossDockedApiRepositoryImpl()
            dcConfigService = DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
            crossdockedDemandService = CrossdockedDemandService(
                crossdockedDemandRepo,
                CrossDockedDemandRepository(dsl),
                DemandRepositoryJooqImpl(dsl),
                dsl,
                StatsigTestFeatureFlagClient(
                    setOf(
                        FeatureFlag.AutomaticCrossDocking(MARKET, "CH"),
                    ),
                ),
                dcConfigService,
            )
            uploadsRepository = UploadsRepositoryImpl()
            importsRepository = ImportsRepository(dsl)
            skuSpecificationRepository = SkuSpecificationRepositoryImpl(dsl)
            uploadsService = UploadsService(
                uploadsRepository,
                importsRepository,
                dcConfigService,
                skuSpecificationRepository,
                StatsigTestFeatureFlagClient(setOf()),
                StatsigTestDynamicConfigClient.empty,
                dsl,
                dsl
            )
        }
    }
}
