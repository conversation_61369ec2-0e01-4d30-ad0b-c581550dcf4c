package com.hellofresh.skuDemandForecast.api.crossdock

import com.hellofresh.skuDemandForecast.api.FunctionalTest
import com.hellofresh.skuDemandForecast.api.schema.public_.Tables.CROSSDOCKED_DEMAND
import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.CrossdockedDemandRecord
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockAction
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemand
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

@Suppress("StringLiteralDuplication")
class CrossDockedApiRepositoryImplTest : FunctionalTest() {

    @AfterEach
    fun cleanUp() {
        dsl.deleteFrom(CROSSDOCKED_DEMAND).execute()
    }

    @Test
    fun `should create new crossed demand record`() {
        val crossdockedDemand = createCrossdockedDemandTestData(
            weekStart = "2024-W25",
        ).copy(
            version = null,
        )

        val key = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand).first()

        val skuSubstitutionRecord = dsl.fetchOne(CROSSDOCKED_DEMAND)!!

        // then
        assertEquals(skuSubstitutionRecord.skuId, key.skuId)
        assertEquals(skuSubstitutionRecord.skuId, crossdockedDemand.skuId)
        assertEquals(skuSubstitutionRecord.version, 1)
        assertEquals(skuSubstitutionRecord.dcFrom, crossdockedDemand.dcFrom)
        assertEquals(skuSubstitutionRecord.weekStart, crossdockedDemand.weekStart)
    }

    @Test
    fun `should not upsert cross docking demand to a dc that already exists from`() {
        val crossdockedDemand1 = createCrossdockedDemandTestData(
            weekStart = "2024-W25",
        )
        val keys1 = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand1)

        val crossdockedDemand2 = crossdockedDemand1.copy(
            weekStart = "2024-W29",
            dcFrom = "12",
            dcTo = crossdockedDemand1.dcFrom,
        )

        assertTrue(keys1.isNotEmpty())

        val keys2 = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand2)
        assertTrue(keys2.isEmpty())
    }

    @Test
    fun `should upsert given week and future crossed demand record`() {
        val crossdockedDemand1 = createCrossdockedDemandTestData(
            weekStart = "2024-W25",
        )

        val crossdockedDemand2 = crossdockedDemand1.copy(
            weekStart = "2024-W27",
            version = 2,
        )

        val crossdockedDemand3 = crossdockedDemand1.copy(
            weekStart = "2024-W29",
            version = 3,
        )

        val key1 = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand1).first()
        val key2 = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand2).first()
        val key3 = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(dsl, crossdockedDemand3).first()

        val crossDockedDemand = crossdockedDemand1.copy(
            version = key1.version,
            dcTo = "12",
            action = CrossDockAction.entries.first { it != crossdockedDemand1.action },
            disabled = !crossdockedDemand1.disabled,
            userId = "newUser",
        )
        val keys = crossdockedDemandRepo.upsertCrossDockedDemandWeeks(
            dsl,
            crossDockedDemand,
        )

        val skuSubstitutionRecords = dsl.fetch(CROSSDOCKED_DEMAND)

        // then
        assertEquals(3, keys.size)

        assertCrossDocking(crossDockedDemand, key1, keys, skuSubstitutionRecords)
        assertCrossDocking(crossDockedDemand, key2, keys, skuSubstitutionRecords)
        assertCrossDocking(crossDockedDemand, key3, keys, skuSubstitutionRecords)
    }

    private fun assertCrossDocking(
        crossDockedDemand: CrossDockedDemand,
        keyVersion1: CrossdockedDemandKey,
        keysUpdated: List<CrossdockedDemandKey>,
        records: List<CrossdockedDemandRecord>
    ) {
        val keyVersion2 = keysUpdated.first { it.weekStart == keyVersion1.weekStart }
        val record = records.filter { it.weekStart == keyVersion2.weekStart }.maxBy { it.version }

        assertEquals(keyVersion1.version + 1, keyVersion2.version)
        assertEquals(keyVersion1.weekStart, keyVersion2.weekStart)
        assertEquals(crossDockedDemand.dcTo, record.dcTo)
        assertEquals(crossDockedDemand.action.name, record.action.name)
        assertEquals(crossDockedDemand.userId, record.userId)
        assertEquals(crossDockedDemand.disabled, record.disabled)
    }
}
