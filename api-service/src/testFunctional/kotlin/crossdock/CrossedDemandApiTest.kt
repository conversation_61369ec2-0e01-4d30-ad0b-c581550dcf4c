@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skuDemandForecast.api.crossdock

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.api.FunctionalTest
import com.hellofresh.skuDemandForecast.api.generated.model.CreateCrossDockedDemandRequest
import com.hellofresh.skuDemandForecast.api.generated.model.CrossDockedDemand
import com.hellofresh.skuDemandForecast.api.generated.model.CrossDockedDemandInsertResponse
import com.hellofresh.skuDemandForecast.api.generated.model.CrossDockingAction.DUPLICATE
import com.hellofresh.skuDemandForecast.api.generated.model.ErrorResponse
import com.hellofresh.skuDemandForecast.api.generated.model.UpdateCrossDockedDemandRequest
import com.hellofresh.skuDemandForecast.api.ktor.JwtCredentials
import com.hellofresh.skuDemandForecast.api.ktor.configureJwtAuth
import com.hellofresh.skuDemandForecast.api.ktor.customJackson
import com.hellofresh.skuDemandForecast.api.schema.public_.Tables.CROSSDOCKED_DEMAND
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested

@DisplayName("CrossedDemand Api Test")
class CrossedDemandApiTest : FunctionalTest() {

    @AfterEach
    fun clearDb() {
        dsl.deleteFrom(CROSSDOCKED_DEMAND).execute()
    }

    @Nested
    @DisplayName("creating crossed demand from /demand/crossdock POST request")
    inner class Creation {
        @Test fun `should create crossed Demand from request`() {
            runBlocking {
                val skuId = UUID.randomUUID()
                val crossedDemandRequest = createCrossedDemandRequest(skuId = skuId)
                val responseCrossedDemand = createCrossedDemand(crossedDemandRequest, true)

                assertEquals(crossedDemandRequest.skuId, responseCrossedDemand.key?.skuId)
                assertEquals(crossedDemandRequest.dcFrom, responseCrossedDemand.key?.dcFrom)
                assertEquals(crossedDemandRequest.weekStart, responseCrossedDemand.key?.weekStart)
                assertEquals(1, responseCrossedDemand.key?.version)
            }
        }

        @Test fun `should create crossed Demand with version 2`() {
            runBlocking {
                val skuId = UUID.randomUUID()
                val dcFrom = "BX"
                val weekStart = "2024-W30"
                val dcTo = "VB"

                val crossedDemandRequest1 = createCrossedDemandRequest(
                    skuId = skuId,
                    dcFrom = dcFrom,
                    weekStart = weekStart,
                    dcTo = dcTo,
                )
                val responseCrossedDemand1 = createCrossedDemand(crossedDemandRequest1, true)

                val responseCrossedDemand2 = updateCrossedDemand(
                    UpdateCrossDockedDemandRequest(
                        skuId = skuId,
                        dcFrom = dcFrom,
                        weekStart = weekStart,
                        dcTo = "TA",
                        action = crossedDemandRequest1.action,
                        version = responseCrossedDemand1.key!!.version,
                        disabled = false,
                    ),
                )

                assertEquals(1, responseCrossedDemand1.key?.version)
                assertEquals(2, responseCrossedDemand2.key?.version)
            }
        }
    }

    @Test fun `should get crossed Demand from by dc and week`() {
        runBlocking {
            val skuId = UUID.randomUUID()
            val dcFrom = "BX"
            val weekStart = "2024-W30"
            val dcTo = "VB"
            val crossedDemandRequest1 = createCrossedDemandRequest(
                skuId = skuId,
                dcFrom = dcFrom,
                weekStart = weekStart,
                dcTo = dcTo,
            )

            val createCrossedDemandResponse = createCrossedDemand(crossedDemandRequest1, true)
            val updateCrossedDemandResponse = updateCrossedDemand(
                UpdateCrossDockedDemandRequest(
                    skuId = skuId,
                    dcFrom = dcFrom,
                    weekStart = weekStart,
                    dcTo = "TA",
                    action = crossedDemandRequest1.action,
                    version = createCrossedDemandResponse.key!!.version,
                    disabled = false,
                ),
                true,
            )

            val response = getCrossedDemand(dcFrom, weekStart, true)

            assertEquals(updateCrossedDemandResponse.key?.skuId, response.first().skuId)
            assertEquals(updateCrossedDemandResponse.key?.dcFrom, response.first().dcFrom)
            assertEquals(updateCrossedDemandResponse.key?.weekStart, response.first().weekStart)
            assertEquals(updateCrossedDemandResponse.key?.version, response.first().version)
        }
    }

    @Test fun `should get crossed Demand with auto-crossdocked from by dc and week`() {
        runBlocking {
            val skuId = UUID.randomUUID()
            val skuId2 = UUID.randomUUID()
            val skuId3 = UUID.randomUUID()
            val dcFrom = "CH"
            val weekStart = "2024-W51"
            val dcTo = "CR"
            val crossedDemandRequest1 = createCrossedDemandRequest(
                skuId = skuId,
                dcFrom = dcFrom,
                weekStart = weekStart,
                dcTo = dcTo,
            )

            // prepare DC_CONFIG mockk data
            dsl.query(
                "INSERT INTO dc_config (dc_code, production_start, market, zone_id, global_dc, enabled) " +
                    "VALUES ('$dcFrom', 'FRIDAY', 'DACH', 'Europe/Berlin', NULL, true)"
            )
                .execute()

            val regexString = "^[A-Z]{1,4}-00-[0-9]{1,6}-[0-9]\$"
            createCrossdockedDemandRule(regexString)

            val aggregatedDemands = sampleAggregatedDemandData(skuId2, skuId3)
            aggregatedDemands.forEach { demand ->
                createAggregatedDemandTestData(
                    skuId = demand["skuId"] as UUID,
                    dcCode = dcFrom,
                    skuCode = demand["skuCode"] as String,
                    productionDate = demand["productionDate"] as LocalDate,
                    originalDemand = demand["originalDemand"].toString().toLong(),
                    totalQuantity = demand["totalQuantity"].toString().toLong(),
                )
            }

            createCrossedDemand(crossedDemandRequest1, true)

            val response = getCrossedDemand(dcFrom, weekStart, true)

            assertEquals(3, response.size)
            assertEquals(1, response.filter { it.isEditable }.size) // 1 from crossDocked
            assertEquals(2, response.filter { !it.isEditable }.size) // 2 from autoCrossDocked

            val firstAutoDocked = response.first { it.skuId == skuId2 }
            val secondAutoDocked = response.first { it.skuId == skuId2 }

            assertEquals(false, firstAutoDocked.isEditable)
            assertEquals(dcFrom, firstAutoDocked.dcFrom)
            assertEquals(null, firstAutoDocked.version)
            assertEquals("2024-W50", firstAutoDocked.weekStart)

            assertEquals(false, secondAutoDocked.isEditable)
            assertEquals(dcFrom, secondAutoDocked.dcFrom)
            assertEquals(null, secondAutoDocked.version)
            assertEquals("2024-W50", secondAutoDocked.weekStart)
        }
    }

    @Test fun `should update by requested fields with a new version plus 1`() {
        runBlocking {
            val skuId = UUID.randomUUID()
            val dcFrom = "BX"
            val weekStart = "2024-W30"

            // populate data to test
            val createResponse = createCrossedDemand(
                createCrossedDemandRequest(
                    skuId = skuId,
                    dcFrom = dcFrom,
                    dcTo = "CC",
                    weekStart = weekStart,
                ),
            )

            val updateRequest1 = UpdateCrossDockedDemandRequest(
                skuId = skuId,
                dcFrom = dcFrom,
                dcTo = "VB",
                weekStart = weekStart,
                action = DUPLICATE,
                version = createResponse.key!!.version,
                disabled = true,
            )
            val deleteResponse1 = updateCrossedDemand(updateRequest1)

            // after deleting old record, version number expected to increase to 2 and have same skuId, dcFrom
            assertEquals(2, deleteResponse1.key?.version)
            assertEquals(skuId, deleteResponse1.key?.skuId)
            assertEquals(dcFrom, deleteResponse1.key?.dcFrom)

            // create a 2nd delete request and version is expected to increase to 3 and have same skuId, dcFrom
            val updateRequest2 = updateRequest1.copy(version = deleteResponse1.key!!.version)
            val updateResponse2 = updateCrossedDemand(updateRequest2)

            assertEquals(3, updateResponse2.key?.version)
            assertEquals(skuId, updateResponse2.key?.skuId)
            assertEquals(dcFrom, updateResponse2.key?.dcFrom)

            // Test sending duplicate request with same version
            val response3 = updateCrossedDemandRequest(updateRequest2)
            val updateResponse3 = objectMapper.readValue<ErrorResponse>(
                response3.bodyAsText(),
            )
            val expectedReason = "Version already updated, please take the latest version and update again."

            assertEquals(expectedReason, updateResponse3.reason)
        }
    }

    @Test fun `should not create cross docked demand to an existing dc with cross docker demand from`() {
        runBlocking {
            val skuId = UUID.randomUUID()
            val dcFrom = "BX"
            val weekStart = "2024-W30"

            val cdRequest1 = createCrossedDemandRequest(
                skuId = skuId,
                dcFrom = dcFrom,
                dcTo = "TT",
                weekStart = weekStart,
            )
            createCrossedDemand(cdRequest1)

            val cdRequest2 = createCrossedDemandRequest(
                skuId = skuId,
                dcFrom = "WW",
                dcTo = dcFrom,
                weekStart = weekStart,
            )

            val createCrossedDemand = createCrossedDemand(cdRequest2)

            assertNull(createCrossedDemand.key)
            assertNotNull(createCrossedDemand.message)
        }
    }

    private suspend fun createCrossedDemand(
        createCrossedDemandRequest: CreateCrossDockedDemandRequest,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/demand/crossdock",
            method = HttpMethod.Post,
            jsonBody = objectMapper.writeValueAsString(createCrossedDemandRequest),
            authenticationEnabled = authenticationEnabled,
        ).let {
            assertEquals(HttpStatusCode.Created, it.status)
            objectMapper.readValue<CrossDockedDemandInsertResponse>(
                it.bodyAsText(),
            )
        }

    private suspend fun updateCrossedDemand(
        deleteRequest: UpdateCrossDockedDemandRequest,
        authenticationEnabled: Boolean = true,
    ) =
        updateCrossedDemandRequest(deleteRequest, authenticationEnabled)
            .let {
                assertEquals(HttpStatusCode.OK, it.status)
                objectMapper.readValue<CrossDockedDemandInsertResponse>(
                    it.bodyAsText(),
                )
            }

    private fun updateCrossedDemandRequest(
        deleteRequest: UpdateCrossDockedDemandRequest,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/demand/crossdock",
            method = HttpMethod.Put,
            jsonBody = objectMapper.writeValueAsString(deleteRequest),
            authenticationEnabled = authenticationEnabled,
        )

    private suspend fun getCrossedDemand(
        dcCode: String,
        weekStart: String,
        authenticationEnabled: Boolean = true,
    ) =
        clientRequest(
            path = "/demand/crossdock/$dcCode?week=$weekStart",
            method = HttpMethod.Get,
            authenticationEnabled = authenticationEnabled,
        ).let {
            assertEquals(HttpStatusCode.OK, it.status)
            objectMapper.readValue<List<CrossDockedDemand>>(
                it.bodyAsText(),
            )
        }

    fun createCrossedDemandRequest(
        skuId: UUID,
        dcFrom: String = "BX",
        dcTo: String = "VB",
        weekStart: String = "2024-W35"
    ) = CreateCrossDockedDemandRequest(
        skuId = skuId,
        dcFrom = dcFrom,
        dcTo = dcTo,
        weekStart = weekStart,
        action = DUPLICATE,
        disabled = false
    )

    fun clientRequest(
        path: String = "/demand/crossdock",
        params: Map<String, String> = emptyMap(),
        method: HttpMethod = HttpMethod.Get,
        jsonBody: String? = null,
        authenticationEnabled: Boolean = true,
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                routing {
                    this.crossdockedDemand(
                        crossdockedDemandService,
                        timeout,
                    ).install(ContentNegotiation) { customJackson() }
                }
            }
            response = client.request(path) {
                this.method = method
                jsonBody?.let { setBody(it) }
                params.forEach { (key, value) -> parameter(key, value) }
                header(HttpHeaders.ContentType, Application.Json.toString())
                if (authenticationEnabled) {
                    this.addAuthHeader("<EMAIL>", "test-author-name")
                }
            }
        }
        return response
    }
}
