package com.hellofresh.skuDemandForecast.api.uploads

import com.fasterxml.jackson.core.type.TypeReference
import com.hellofresh.skuDemandForecast.api.FunctionalTest
import com.hellofresh.skuDemandForecast.api.generated.model.RetrieveUploadedFileDetailsResponse
import com.hellofresh.skuDemandForecast.api.generated.model.UploadViolation
import com.hellofresh.skuDemandForecast.api.schema.public_.Tables.FILE_UPLOADS
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileSource.INVENTORY
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileStatus.CONSUMED
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileStatus.PROCESSED_WITH_ERROR
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileStatus.valueOf
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileType.DEMAND
import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.FileUploadsRecord
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig.MpsConfigValue
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestDynamicConfigClient
import com.hellofresh.skuDemandForecast.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.skudemandforecast.model.fileupload.FileStatus
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.utils.io.core.toByteArray
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource

private const val INVENTORY_SOURCE = "INVENTORY"

@DisplayName("Uploads Api Test")
@Suppress("StringLiteralDuplication")
class UploadsApiTest : FunctionalTest() {

    private val uploadedFileName = "uploads/forecastfile.csv"
    private val uploadedFileNameForUK = "uploads/forecastfileforUK.csv"

    @Test fun `uploaded files are processed`() {
        val uploadsRequestSlot = slot<UploadsRequest>()
        val uploadsService = mockk<UploadsService>()
        coEvery { uploadsService.save(capture(uploadsRequestSlot)) } returns emptyList()

        val forecast = createUploadedFile(uploadedFileName)
        val recipe = createUploadedFile("uploads/recipedetails.csv")
        assertUploadsAccepted(listOf(forecast, recipe), uploadsService)
        coVerify(exactly = 1) { uploadsService.save(any()) }

        val uploadsRequest = uploadsRequestSlot.captured
        assertEquals(authorName, uploadsRequest.loggedInUserInfo.userName)
        assertEquals(authorEmail, uploadsRequest.loggedInUserInfo.userEmail)
        assertEquals(2, uploadsRequest.files.size)
        uploadsRequest.files.first { it.name == forecast.name }.also { uploadedFile ->
            assertEquals(forecast.name, uploadedFile.name)
            assertNotNull(uploadedFile.timestamp)
            assertEquals(forecast.content.size, uploadedFile.content.size)
        }
        uploadsRequest.files.first { it.name == recipe.name }.also { uploadedFile ->
            assertEquals(recipe.name, uploadedFile.name)
            assertNotNull(uploadedFile.timestamp)
            assertEquals(recipe.content.size, uploadedFile.content.size)
        }
    }

    @ParameterizedTest
    @ValueSource(
        booleans = [true, false],
    )
    fun `recipe uploads are denied if mps imports is enabled`(mpsEnabled: Boolean) {
        val dcConfig = createDcConfig("VE")
        dsl.batchInsert(
            listOf(
                dcConfig,
            ),
        ).execute()
        val forecast = createUploadedFile(uploadedFileName).copy(market = dcConfig.market)
        val recipe = createUploadedFile("uploads/recipedetails.csv").copy(market = dcConfig.market)
        val uploadsService = UploadsService(
            uploadsRepository,
            importsRepository,
            dcConfigService,
            skuSpecificationRepository,
            if (mpsEnabled) {
                StatsigTestFeatureFlagClient(setOf(FeatureFlag.MpsImport(MARKET, dcConfig.market)))
            } else {
                StatsigTestFeatureFlagClient(emptySet())
            },
            StatsigTestDynamicConfigClient.empty,
            dsl,
            dsl
        )

        if (mpsEnabled) {
            assertUploadsBadRequest(listOf(forecast, recipe), uploadsService)
        } else {
            assertUploadsAccepted(listOf(forecast, recipe), uploadsService)
        }

        val getUploadedFileResponse = getUploadedFileRequest(uploadsService, dcConfig.market)

        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponse.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertTrue(
            if (mpsEnabled) {
                retrieveUploadedFileDetailsResponse.isEmpty()
            } else {
                retrieveUploadedFileDetailsResponse.isNotEmpty()
            },
        )
    }

    @ParameterizedTest
    @CsvSource(
        // File Week is 2023-W45
        value = [
            "2024-W41, true",
            "2023-W46, true",
            "2023-W45, false",
            "2023-W44, false",
            "2022-W01, false",
            "null, false",
        ],
        nullValues = ["null"],
    )
    fun `recipe uploads are denied for weeks later than mps config start week`(mpsWeekStart: String?, valid: Boolean) {
        val dcConfig = createDcConfig("VE")
        dsl.batchInsert(listOf(dcConfig)).execute()

        val recipe = createUploadedFile("uploads/recipedetails.csv").copy(market = dcConfig.market)
        val uploadsService = UploadsService(
            uploadsRepository,
            importsRepository,
            dcConfigService,
            skuSpecificationRepository,
            StatsigTestFeatureFlagClient(setOf(FeatureFlag.MpsImport(MARKET, dcConfig.market))),
            StatsigTestDynamicConfigClient(
                mpsWeekStart?.let {
                    mapOf(DynamicConfig.MPSConfig(MARKET, dcConfig.market) to MpsConfigValue(mpsWeekStart))
                } ?: emptyMap(),
            ),
            dsl,
            dsl
        )

        if (valid) {
            assertUploadsAccepted(listOf(recipe), uploadsService)
        } else {
            assertUploadsBadRequest(listOf(recipe), uploadsService)
        }
    }

    @ParameterizedTest
    @CsvSource(
        // File Week is 2025-W14
        value = [
            "2026-W41, true", // 2025-W14 < 2026-W41 - true
            "2025-W46, true", // 2025-W14 < 2025-W46 - true
            "2025-W15, true", // 2025-W14 < 2025-W46 - true
            "2025-W14, false", // 2025-W14 < 2025-W14 - false
            "2024-W44, false", // 2025-W14 < 2024-W44 - false
            "2023-W01, false", // 2025-W14 < 2023-W01 - false
            "null, true",
        ],
        nullValues = ["null"],
    )
    fun `demand uploads are denied for weeks greater than mps config start week`(
        mpsWeekStart: String?,
        valid: Boolean
    ) {
        val dcConfig = createDcConfig("VE")
        dsl.batchInsert(listOf(dcConfig)).execute()

        val demandFile = createUploadedFile("uploads/mps-configured-forecastfile.csv").copy(market = dcConfig.market)
        val uploadsService = UploadsService(
            uploadsRepository,
            importsRepository,
            dcConfigService,
            skuSpecificationRepository,
            StatsigTestFeatureFlagClient(setOf(FeatureFlag.MpsImport(MARKET, dcConfig.market))),
            StatsigTestDynamicConfigClient(
                mpsWeekStart?.let {
                    mapOf(DynamicConfig.MPSConfig(MARKET, dcConfig.market) to MpsConfigValue(mpsWeekStart))
                } ?: emptyMap(),
            ),
            dsl,
            dsl
        )

        if (valid) {
            assertUploadsAccepted(listOf(demandFile), uploadsService)
        } else {
            assertUploadsBadRequest(listOf(demandFile), uploadsService)
        }
    }

    @Test
    fun `recipe uploads are denied when weekStart from mps config can not be parsed`() {
        val dcConfig = createDcConfig("VE")
        dsl.batchInsert(listOf(dcConfig)).execute()

        val recipe = createUploadedFile("uploads/recipedetails.csv").copy(market = dcConfig.market)
        val uploadsService = UploadsService(
            uploadsRepository,
            importsRepository,
            dcConfigService,
            skuSpecificationRepository,
            StatsigTestFeatureFlagClient(setOf(FeatureFlag.MpsImport(MARKET, dcConfig.market))),
            StatsigTestDynamicConfigClient(
                mapOf(DynamicConfig.MPSConfig(MARKET, dcConfig.market) to MpsConfigValue(UUID.randomUUID().toString())),
            ),
            dsl,
            dsl
        )

        assertUploadsBadRequest(listOf(recipe), uploadsService)
    }

    @Test fun `should be able to get uploaded file details`() {
        dsl.batchInsert(
            listOf(
                createDcConfig("VE"),
            ),
        ).execute()
        val testFile = createUploadedFile(uploadedFileName)
        val response = uploadsRequest(
            testFile.market,
            listOf(testFile),
            uploadsService,
            INVENTORY_SOURCE,
        )
        assertEquals(HttpStatusCode.Accepted, response.status)
        val getUploadedFileResponse = getUploadedFileRequest(uploadsService, "DACH")
        assertEquals(HttpStatusCode.OK, getUploadedFileResponse.status)
        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponse.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertEquals(1, retrieveUploadedFileDetailsResponse.size)
        retrieveUploadedFileDetailsResponse.first().apply {
            assertNotNull(this.fileId)
            assertEquals(testFile.name, fileName)
            assertEquals(listOf("VE"), dcs)
            assertEquals(listOf("2023-W30"), weeks)
            assertEquals("DEMAND", type)
            assertEquals("test-user-name", importedBy.authorName)
            assertEquals("test-user-email", importedBy.authorEmail)
            assertEquals(0, errors?.size)
            assertEquals("PENDING", status)
        }
    }

    @ParameterizedTest
    @CsvSource("CONSUMED", "CREATED", "CREATED_WITH_ERROR")
    fun `intermediate statuses are returned as PENDING`(fileStatus: FileStatus) {
        dsl.batchInsert(
            listOf(
                createDcConfig("VE"),
                FileUploadsRecord(
                    UUID.randomUUID(),
                    arrayOf("VE"),
                    arrayOf("2023-W50"),
                    "test.csv",
                    "test-content".toByteArray(),
                    arrayOf(),
                    INVENTORY,
                    DEMAND,
                    valueOf(fileStatus.name),
                    "authorName",
                    "<EMAIL>",
                    OffsetDateTime.now(),
                    OffsetDateTime.now(),
                    "DACH",
                    arrayOf(),
                ),
            ),
        ).execute()

        val getUploadedFileResponse = getUploadedFileRequest(uploadsService, "DACH")
        assertEquals(HttpStatusCode.OK, getUploadedFileResponse.status)
        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponse.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertEquals(1, retrieveUploadedFileDetailsResponse.size)
        retrieveUploadedFileDetailsResponse.first().apply {
            assertEquals("DEMAND", type)
            assertEquals("PENDING", status)
        }
    }

    @Test fun `should return the uploaded file status PROCESSED_WITH_ERROR`() {
        dsl.batchInsert(
            listOf(
                createDcConfig("VE"),
                FileUploadsRecord(
                    UUID.randomUUID(),
                    arrayOf("VE"),
                    arrayOf("2023-W50"),
                    "test.csv",
                    "test-content".toByteArray(),
                    arrayOf("error"),
                    INVENTORY,
                    DEMAND,
                    PROCESSED_WITH_ERROR,
                    "authorName",
                    "<EMAIL>",
                    OffsetDateTime.now(),
                    OffsetDateTime.now(),
                    "DACH",
                    arrayOf(),
                ),
            ),
        ).execute()

        val getUploadedFileResponse = getUploadedFileRequest(uploadsService, "DACH")
        assertEquals(HttpStatusCode.OK, getUploadedFileResponse.status)
        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponse.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertEquals(1, retrieveUploadedFileDetailsResponse.size)
        retrieveUploadedFileDetailsResponse.first().apply {
            assertEquals("DEMAND", type)
            assertEquals("PROCESSED WITH ERROR", status)
        }
    }

    @Test fun `info and error fields are returned in api`() {
        val errorList = arrayOf("error")
        val infoList = arrayOf("info")
        dsl.batchInsert(
            listOf(
                createDcConfig("VE"),
                FileUploadsRecord(
                    UUID.randomUUID(),
                    arrayOf("VE"),
                    arrayOf("2023-W50"),
                    "test.csv",
                    "test-content".toByteArray(),
                    errorList,
                    INVENTORY,
                    DEMAND,
                    CONSUMED,
                    "authorName",
                    "<EMAIL>",
                    OffsetDateTime.now(),
                    OffsetDateTime.now(),
                    "DACH",
                    infoList,
                ),
            ),
        ).execute()

        val getUploadedFileResponse = getUploadedFileRequest(uploadsService, "DACH")
        assertEquals(HttpStatusCode.OK, getUploadedFileResponse.status)
        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponse.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertEquals(1, retrieveUploadedFileDetailsResponse.size)
        retrieveUploadedFileDetailsResponse.first().apply {
            assertEquals(errorList.toSet() + infoList.toSet(), errors?.toSet())
        }
    }

    @Test fun `should be able to get uploaded file details only for the given market`() {
        dsl.batchInsert(
            listOf(createDcConfig("VE"), createDcConfig("BV", "GB")),
        ).execute()
        val testFileOne = createUploadedFile(uploadedFileName)
        val responseOne = uploadsRequest(testFileOne.market, listOf(testFileOne), uploadsService, INVENTORY_SOURCE)
        assertEquals(HttpStatusCode.Accepted, responseOne.status)
        val testFileTwo = createUploadedFile(uploadedFileNameForUK, "GB")
        val responseTwo = uploadsRequest(
            testFileTwo.market,
            listOf(testFileTwo),
            uploadsService,
            INVENTORY_SOURCE,
            "GB",
        )
        assertEquals(HttpStatusCode.Accepted, responseTwo.status)
        val getUploadedFileResponseForDachMarket = getUploadedFileRequest(uploadsService, "DACH")
        assertEquals(HttpStatusCode.OK, getUploadedFileResponseForDachMarket.status)
        val retrieveUploadedFileDetailsResponse = runBlocking {
            objectMapper.readValue(
                getUploadedFileResponseForDachMarket.bodyAsText(),
                object : TypeReference<List<RetrieveUploadedFileDetailsResponse>>() {},
            )
        }
        assertEquals(1, retrieveUploadedFileDetailsResponse.size)
        retrieveUploadedFileDetailsResponse.first().apply {
            assertNotNull(this.fileId)
            assertEquals(testFileOne.name, fileName)
            assertEquals(listOf("VE"), dcs)
            assertEquals(listOf("2023-W30"), weeks)
            assertEquals("DEMAND", type)
            assertEquals("test-user-name", importedBy.authorName)
            assertEquals("test-user-email", importedBy.authorEmail)
            assertEquals(0, errors?.size)
            assertEquals("PENDING", status)
        }
    }

    @Test fun `upload fails when processing result fails`() {
        val uploadsService = mockk<UploadsService>()
        coEvery { uploadsService.save(any()) } returns
            listOf(
                UploadViolation(
                    fileName = "test-error",
                    violation = "test-violation",
                    lines = listOf(1),
                ),
            )

        assertUploadsBadRequest(listOf(createRandomUploadFile()), uploadsService)
    }

    @Test fun `uploaded file timestamp header is used when received from request`() {
        val uploadsRequestSlot = slot<UploadsRequest>()
        val uploadsService = mockk<UploadsService>()
        coEvery { uploadsService.save(capture(uploadsRequestSlot)) } returns emptyList()
        val testFile = createRandomUploadFile().copy(
            timestamp = OffsetDateTime.now(UTC).minusSeconds(100),
        )

        assertUploadsAccepted(listOf(testFile), uploadsService)
        coVerify(exactly = 1) { uploadsService.save(any()) }

        val uploadsRequest = uploadsRequestSlot.captured
        assertEquals(1, uploadsRequest.files.size)
        uploadsRequest.files.first { it.name == testFile.name }.also { uploadedFile ->
            assertEquals(testFile.name, uploadedFile.name)
            assertEquals(testFile.timestamp.toEpochSecond(), uploadedFile.timestamp.toEpochSecond())
            assertEquals(testFile.content.size, uploadedFile.content.size)
        }
    }

    @Test fun `uploads fails when maximum number of files is exceeded`() {
        val uploadsService = mockk<UploadsService>()
        assertUploadsBadRequest(
            (1..MAXIMUM_ALLOWED_FILES + 1).map { createRandomUploadFile() },
            uploadsService,
        )
        coVerify(exactly = 0) { uploadsService.save(any()) }
    }

    @Test fun `uploads fails when maximum file size is exceeded`() {
        val uploadsService = mockk<UploadsService>()
        assertUploadsBadRequest(
            listOf(createRandomUploadFile(contentSize = MAXIMUM_BYTES_FILE_SIZE + 1)),
            uploadsService,
        )
        coVerify(exactly = 0) { uploadsService.save(any()) }
    }

    @Test fun `uploads fails when no files found in request`() {
        val uploadsService = mockk<UploadsService>()
        assertUploadsBadRequest(emptyList(), uploadsService)
        coVerify(exactly = 0) { uploadsService.save(any()) }
    }

    @ParameterizedTest
    @CsvSource("ORDERING", INVENTORY_SOURCE)
    fun `uploaded file source header is used when received from request`(source: String) {
        dsl.batchInsert(createDcConfig("VE")).execute()
        val testFile = createUploadedFile(uploadedFileName)
        val response = uploadsRequest(testFile.market, listOf(testFile), uploadsService, source)
        assertEquals(HttpStatusCode.Accepted, response.status)
        val dbFile = dsl.selectFrom(FILE_UPLOADS)
            .where(FILE_UPLOADS.FILE_NAME.eq(testFile.name))
            .fetch().first()
        assertEquals(source, dbFile.uploadedFileSource.name)
        assertEquals("DACH", dbFile.market)
    }
}
