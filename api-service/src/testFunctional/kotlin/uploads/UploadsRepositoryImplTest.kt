package com.hellofresh.skuDemandForecast.api.uploads

import com.hellofresh.skuDemandForecast.api.FunctionalTest
import com.hellofresh.skuDemandForecast.api.schema.public_.Tables.FILE_UPLOADS
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileSource
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileStatus
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileType
import com.hellofresh.skudemandforecast.model.fileupload.FileSource.INVENTORY
import com.hellofresh.skudemandforecast.model.fileupload.FileStatus.PENDING
import com.hellofresh.skudemandforecast.model.fileupload.FileType.DEMAND
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import com.hellofresh.skudemandforecast.model.fileupload.default
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class UploadsRepositoryImplTest : FunctionalTest() {

    @Test
    fun `should save the uploaded files`() {
        // when
        val fileUploadOne = FileUpload.default()
        val fileUploadTwo = FileUpload.default()
        runBlocking {
            uploadsRepository.saveUploads(listOf(fileUploadOne, fileUploadTwo), dsl)
            val fileUploadResults = dsl.selectFrom(
                FILE_UPLOADS,
            ).toList()

            // then
            val fileUploadResult = fileUploadResults.first()
            assertEquals(2, fileUploadResults.size)
            assertEquals(setOf("VE").toTypedArray().first(), fileUploadResult.dcs.first())
            assertEquals(setOf("2023-W41").toTypedArray().first(), fileUploadResult.weeks.first())
            assertEquals("upload.csv", fileUploadResult.fileName)
            assertEquals(String("uploaded-forecast-file-content".toByteArray()), String(fileUploadResult.content))
            assertEquals("test-author-name", fileUploadResult.authorName)
            assertEquals("test-author-email", fileUploadResult.authorEmail)
            assertEquals(fileUploadOne.errors, fileUploadResult.errors.toList())
            assertEquals(fileUploadOne.info, fileUploadResult.info.toList())
            assertEquals(FileSource.INVENTORY, fileUploadResult.uploadedFileSource)
            assertEquals(FileType.DEMAND, fileUploadResult.uploadedFileType)
            assertEquals(FileStatus.PENDING, fileUploadResult.uploadedFileStatus)
        }
    }

    @Test
    fun `should fetch the latest uploaded file details`() {
        // when
        val fileUploadOne = FileUpload.default()
        val fileUploadTwo = FileUpload.default()
        runBlocking {
            uploadsRepository.saveUploads(listOf(fileUploadOne, fileUploadTwo), dsl)
            val fileUploadResults = uploadsRepository.getUploadedFileDetails("DACH", dsl)
            // then
            val fileUploadResult = fileUploadResults.first()
            assertEquals(2, fileUploadResults.size)
            assertEquals(setOf("VE").toTypedArray().first(), fileUploadResult.dcs.first())
            assertEquals(setOf("2023-W41").toTypedArray().first(), fileUploadResult.weeks.first())
            assertEquals("upload.csv", fileUploadResult.name)
            assertEquals("test-author-name", fileUploadResult.authorName)
            assertEquals("test-author-email", fileUploadResult.authorEmail)
            assertEquals(fileUploadOne.errors, fileUploadResult.errors.toList())
            assertEquals(fileUploadOne.info, fileUploadResult.info.toList())
            assertEquals(INVENTORY, fileUploadResult.source)
            assertEquals(DEMAND, fileUploadResult.type)
            assertEquals(PENDING, fileUploadResult.status)
        }
    }
}
