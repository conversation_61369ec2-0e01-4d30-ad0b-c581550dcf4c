package substitution

import com.hellofresh.skuDemandForecast.api.generated.model.DemandByDate
import com.hellofresh.skuDemandForecast.api.generated.model.SkuDemand
import com.hellofresh.skuDemandForecast.api.generated.model.SubstitutionDetail
import com.hellofresh.skuDemandForecast.api.substitution.isValid
import io.mockk.mockk
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test

class ValidationTest {

    @Test
    fun `substitution details is not valid if sku out has empty fields`() {
        val substitutionDetail = createValidSubstitutionDetail()

        assertFalse(
            substitutionDetail.copy(
                skuOut = substitutionDetail.skuOut.copy(
                    skuId = "",
                ),
            ).isValid(),
        )

        assertFalse(
            substitutionDetail.copy(
                skuOut = substitutionDetail.skuOut.copy(
                    demand = emptyList(),
                ),
            ).isValid(),
        )
    }

    @Test
    fun `substitution details is valid when has all valid fields`() {
        assertTrue(
            createValidSubstitutionDetail()
                .isValid(),
        )
    }
}

private fun createValidSubstitutionDetail() =
    SubstitutionDetail(
        skuOut = SkuDemand(
            skuId = "inOut",
            demand = listOf(mockk<DemandByDate>()),
        ),
        skuIn = listOf(
            SkuDemand(
                "inId",
                listOf(mockk<DemandByDate>()),
            ),
        ),
    )
