package uploads

import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileSource
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileStatus
import com.hellofresh.skuDemandForecast.api.schema.public_.enums.FileType
import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.FileUploadsRecord
import com.hellofresh.skuDemandForecast.api.uploads.toImport
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class RepositoryMapperTest {
    @Test
    fun `should return empty list if the errors is null`() {
        val fileId = UUID.randomUUID()
        val fileUploadsRecord = FileUploadsRecord(
            fileId,
            setOf(UUID.randomUUID().toString()).toTypedArray(),
            setOf(UUID.randomUUID().toString()).toTypedArray(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString().toByteArray(),
            null,
            FileSource.INVENTORY,
            FileType.DEMAND,
            FileStatus.PROCESSED,
            "author",
            "email",
            OffsetDateTime.now(ZoneOffset.UTC),
            OffsetDateTime.now(ZoneOffset.UTC),
            "market",
            null,
        )

        val fileUpload = fileUploadsRecord.toImport()

        assertEquals(emptyList(), fileUpload.errors)
        assertEquals(emptyList(), fileUpload.info)

        assertEquals(fileId, fileUpload.id)
        assertEquals("author", fileUpload.authorName)
        assertEquals("email", fileUpload.authorEmail)
    }
}
