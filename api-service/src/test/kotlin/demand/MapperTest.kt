package com.hellofresh.skuDemandForecast.api.demand

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.AggregatedDemandRecord
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.AggregatedDemand
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.default
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDocking
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.DUPLICATE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockingAction.REPLACE
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.CrossDockings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekitting
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Prekittings
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitution
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.details.Substitutions
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.getRecipeBreakDownWithSubs
import com.hellofresh.skudemandforecast.demandcalculator.aggregatedemand.random
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.jooq.JSONB

val objectMapper = ObjectMapper().findAndRegisterModules()

class MapperTest {
    @Test fun `empty list is mapped to empty list`() {
        assertTrue { listOf<AggregatedDemandRecord>().toResponse().isEmpty() }
    }

    @Test fun `recipe quantities are summed`() {
        val aggregatedDemand = AggregatedDemand.random().copy(
            recipeBreakdowns = listOf(
                RecipeBreakdownLine.default(1, 10),
                RecipeBreakdownLine.default(2, 20),
                RecipeBreakdownLine.default(3, 30),
            ),
            substitutions = Substitutions(
                subIn = listOf(Substitution(20)),
                subOut = listOf(Substitution(10)),
            ),
            prekittings = Prekittings(
                prekittingIn = listOf(Prekitting(15)),
                prekittingOut = listOf(Prekitting(5))
            ),
            crossDockings = CrossDockings(
                crossDockingsIn = listOf(CrossDocking(dcCode = "XX", REPLACE, 25)),
                crossDockingsOut = listOf(CrossDocking(dcCode = "YY", DUPLICATE, 15)),
            ),
        )
        val dbRecords: List<AggregatedDemandRecord> = listOf(
            aggregatedDemand,
        ).map { it.toRecord() }

        val response = dbRecords.toResponse()

        assertEquals(1, response.size)
        assertEquals(aggregatedDemand.totalQty, 90)
        assertEquals(aggregatedDemand.totalQty, dbRecords.first().totalQuantity)
        assertEquals(aggregatedDemand.totalRecipeBreakDownQty(), dbRecords.first().originalDemand)
        assertEquals(aggregatedDemand.getRecipeBreakDownWithSubs().values.sum().toInt(), response[0].qty)
        assertEquals(aggregatedDemand.totalRecipeBreakDownQty().toInt(), response[0].originalDemand)
    }
}

fun AggregatedDemand.toRecord() = AggregatedDemandRecord(
    parentSku?.id,
    sku.id,
    sku.code,
    dcCode,
    productionDate,
    JSONB.valueOf(
        objectMapper.writeValueAsString(this.getRecipeBreakDownWithSubs()),
    ),
    LocalDateTime.now(),
    LocalDateTime.now(),
    this.totalRecipeBreakDownQty(),
    null,
    this.totalQty,
    null,
    false,
)
