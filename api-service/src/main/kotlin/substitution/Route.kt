package com.hellofresh.skuDemandForecast.api.substitution

import com.hellofresh.skuDemandForecast.api.demand.mapToErrorResponse
import com.hellofresh.skuDemandForecast.api.generated.model.AddSubstitutionRequest
import com.hellofresh.skuDemandForecast.api.generated.model.EditSubstitutionRequestWithVersion
import com.hellofresh.skuDemandForecast.api.generated.model.RetrieveOverviewSubstitutionResponses
import com.hellofresh.skuDemandForecast.api.generated.model.SubstitutionDetail
import com.hellofresh.skuDemandForecast.api.generated.model.SubstitutionKey
import com.hellofresh.skuDemandForecast.api.ktor.getOrThrow
import com.hellofresh.skuDemandForecast.api.ktor.loggedInUserInfo
import com.hellofresh.skuDemandForecast.api.user.LoggedInUserInfo
import com.hellofresh.skudemandforecast.lib.substitution.CreateSubstitution
import com.hellofresh.skudemandforecast.lib.substitution.EditSubstitution
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import java.time.Duration
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.time.withTimeout

private const val DEMAND_SUBSTITUTION_PATH = "/demand/{dcCode}/substitution/v1"
private const val SUBSTITUTION_PATH = "/substitution/{substitutionId}/v1"
fun Routing.substitution(
    substitutionService: SubstitutionService,
    timeout: Duration
) = authenticate {
    post(DEMAND_SUBSTITUTION_PATH) {
        runCatching {
            createSubstitutionRequest(call)
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
        }.onSuccess { createSubstitution ->
            runCatching {
                withTimeout(timeout) {
                    substitutionService.substitutionRepository.createSubstitution(createSubstitution)
                }
            }.onSuccess { result ->
                call.respond(HttpStatusCode.OK, result)
            }.onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
        }
    }

    put(SUBSTITUTION_PATH) {
        runCatching {
            editSubstitutionRequest(call)
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
        }.onSuccess { editSubstitution ->
            runCatching {
                withTimeout(timeout) {
                    substitutionService.substitutionRepository.editSubstitution(editSubstitution)
                        ?.let {
                            SubstitutionKey(
                                editSubstitution.substitutionId,
                                editSubstitution.version,
                            )
                        }
                }
            }.onSuccess { result ->
                result?.let { call.respond(HttpStatusCode.OK, result) }
                    ?: call.respond(HttpStatusCode.NotFound)
            }.onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
        }
    }

    get(DEMAND_SUBSTITUTION_PATH) {
        retrieveSubstitutionsWithVersion(this, timeout, substitutionService)
    }

    get(SUBSTITUTION_PATH) {
        retrieveSubstitutionWithVersion(this, timeout, substitutionService)
    }
}

private fun ApplicationCall.substitutionId() = parameters.getOrThrow("substitutionId")

internal suspend fun createSubstitutionRequest(applicationCall: ApplicationCall): CreateSubstitution {
    val loggedInUserInfo: LoggedInUserInfo = applicationCall.loggedInUserInfo()
    val dcCode: String = applicationCall.parameters.getOrThrow("dcCode")
    return applicationCall.receive<AddSubstitutionRequest>()
        .let {
            require(it.substitutionDetail.isValid()) { "Invalid Substitution Request" }
            it.toSubstitution(dcCode, loggedInUserInfo)
        }
}

private suspend fun retrieveSubstitutionsWithVersion(
    context: RoutingContext,
    timeout: Duration,
    substitutionService: SubstitutionService,
) {
    context.runCatching {
        val dcCode = this.call.parameters.getOrThrow("dcCode")
        val from = this.call.request.queryParameters.getOrThrow("from").let { LocalDate.parse(it) }
        val to = this.call.request.queryParameters.getOrThrow("to").let { LocalDate.parse(it) }
        withTimeout(timeout) {
            substitutionService.retrieveSubstitutionsWithVersion(dcCode, from, to)
        }
    }.onFailure { exception ->
        context.call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
    }.fold(
        { substitutions ->
            context.call.respond(
                HttpStatusCode.OK,
                substitutions.map {
                    RetrieveOverviewSubstitutionResponses(
                        substitutions = it.map { substitution ->
                            substitution.toSubstitutionOverviewResponse()
                        },
                    )
                },
            )
        },
        { exception ->
            context.call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(exception))
        },
    )
}

private suspend fun retrieveSubstitutionWithVersion(
    context: RoutingContext,
    timeout: Duration,
    substitutionService: SubstitutionService,
) {
    context.runCatching {
        val subId = call.substitutionId()
        withTimeout(timeout) {
            substitutionService.retrieveSubstitutionWithVersion(UUID.fromString(subId))
        }
    }.onFailure { exception ->
        context.call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
    }.fold(
        { substitutions ->
            context.call.respond(
                HttpStatusCode.OK,
                substitutions.map { substitution ->
                    substitution.toSubstitutionDetailResponse()
                },
            )
        },
        { exception ->
            context.call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(exception))
        },
    )
}

internal fun SubstitutionDetail.isValid(): Boolean =
    !(skuOut.skuId.isEmpty() || skuOut.demand.isEmpty())

private suspend fun editSubstitutionRequest(applicationCall: ApplicationCall): EditSubstitution {
    val loggedInUserInfo = applicationCall.loggedInUserInfo()
    val substitutionId: UUID = UUID.fromString(applicationCall.substitutionId())
    return applicationCall.receive<EditSubstitutionRequestWithVersion>()
        .let {
            require(it.substitutionDetail.isValid()) { "Invalid Substitution Request" }
            it.toEditSubstitution(substitutionId, loggedInUserInfo)
        }
}
