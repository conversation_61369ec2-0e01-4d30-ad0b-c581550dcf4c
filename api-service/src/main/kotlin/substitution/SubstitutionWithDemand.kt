package com.hellofresh.skuDemandForecast.api.substitution

import com.hellofresh.skuDemandForecast.model.substitution.SkuDemand
import com.hellofresh.skuDemandForecast.model.substitution.Substitution

data class SubstitutionWithDemand(
    val substitution: Substitution,
    val skuOut: SkuDemand,
    val skuIn: List<SkuDemand>,
) {
    constructor(substitution: Substitution) :
        this(substitution, substitution.substitutionDetail.skuOut, substitution.substitutionDetail.skuIn)
}
