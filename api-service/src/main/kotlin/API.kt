package com.hellofresh.skuDemandForecast.api

import com.hellofresh.sdf.shutdown.shutdownNeeded
import com.hellofresh.service.Config
import com.hellofresh.skuDemandForecast.api.crossdock.CrossDockedApiRepositoryImpl
import com.hellofresh.skuDemandForecast.api.crossdock.CrossdockedDemandService
import com.hellofresh.skuDemandForecast.api.crossdock.crossdockedDemand
import com.hellofresh.skuDemandForecast.api.demand.DemandRepositoryJooqImpl
import com.hellofresh.skuDemandForecast.api.demand.demand
import com.hellofresh.skuDemandForecast.api.docs.docs
import com.hellofresh.skuDemandForecast.api.ktor.JwtCredentials
import com.hellofresh.skuDemandForecast.api.ktor.KtorServer
import com.hellofresh.skuDemandForecast.api.ktor.configureJwtAuth
import com.hellofresh.skuDemandForecast.api.ktor.configureSerialization
import com.hellofresh.skuDemandForecast.api.substitution.SubstitutionService
import com.hellofresh.skuDemandForecast.api.substitution.substitution
import com.hellofresh.skuDemandForecast.api.uploads.ImportsRepository
import com.hellofresh.skuDemandForecast.api.uploads.UploadsRepositoryImpl
import com.hellofresh.skuDemandForecast.api.uploads.UploadsService
import com.hellofresh.skuDemandForecast.api.uploads.uploads
import com.hellofresh.skuDemandForecast.crossdocking.CrossDockedDemandRepository
import com.hellofresh.skuDemandForecast.featureflags.StatsigFactory
import com.hellofresh.skuDemandForecast.lib.Application
import com.hellofresh.skuDemandForecast.lib.StatusServer
import com.hellofresh.skuDemandForecast.lib.runApplication
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.lib.substitution.repo.SubstitutionRepository
import com.hellofresh.skudemandforecast.skuspecification.repo.SkuSpecificationRepositoryImpl
import io.ktor.server.routing.routing
import io.micrometer.core.instrument.MeterRegistry
import java.time.Duration

internal const val HTTP_PORT = 8080
private const val HTTP_ACTUATOR = 8081
private const val POOL_SIZE = 5
private const val API_TIMEOUT_MINS = 30L

@Suppress("LongMethod")
suspend fun main() {
    runApplication {
        val ctx = jooqDslContext(
            "readWrite",
            dataSource(config, meterRegistry) {
                isReadOnly = false
                maximumPoolSize = POOL_SIZE
            },
            POOL_SIZE,
            meterRegistry,
        )

        runStatusServer(meterRegistry)

        val replicaDslContext = jooqDslContext(
            "readOnly",
            replicaDataSource(config, meterRegistry) {
                maximumPoolSize = POOL_SIZE
            },
            POOL_SIZE,
            meterRegistry,
        )
        val statsigFactory = statsigFactory()
        val demandRepository = DemandRepositoryJooqImpl(ctx)
        val dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(replicaDslContext))
        val crossdockedService = CrossdockedDemandService(
            CrossDockedApiRepositoryImpl(),
            CrossDockedDemandRepository(ctx),
            DemandRepositoryJooqImpl(ctx),
            ctx,
            statsigFactory.featureFlagClient,
            dcConfigService = dcConfigService,
        )
        KtorServer.start(
            HTTP_PORT,
            meterRegistry,
            listOf {
                configureJwtAuth(getJwtCredentials(config), config["jwt.enabled"].toBoolean())
                routing {
                    docs()
                    configureSerialization(
                        this.crossdockedDemand(
                            crossdockedService,
                            Duration.ofMinutes(API_TIMEOUT_MINS),
                        ),
                    )
                    configureSerialization(
                        this.demand(
                            demandRepository,
                            Duration.ofMinutes(API_TIMEOUT_MINS),
                        ),
                    )
                    configureSerialization(
                        this.substitution(
                            SubstitutionService(SubstitutionRepository(ctx), demandRepository),
                            Duration.ofMinutes(API_TIMEOUT_MINS),
                        ),
                    )
                    configureSerialization(
                        this.uploads(
                            UploadsService(
                                UploadsRepositoryImpl(),
                                ImportsRepository(replicaDslContext),
                                dcConfigService = dcConfigService,
                                SkuSpecificationRepositoryImpl(replicaDslContext),
                                statsigFactory.featureFlagClient,
                                statsigFactory.dynamicConfigClient,
                                ctx,
                                replicaDslContext,
                            ),
                            Duration.ofMinutes(API_TIMEOUT_MINS),
                        ),
                    )
                }
            },
        )
    }
}

private fun getJwtCredentials(config: Config) = JwtCredentials(
    config["auth.service.jwt.secret.key"],
    config["application.name"],
    config["azure.issuer"],
    config["azure.client.id"],
    config["azure.jwks.uri"],
)

private fun runStatusServer(meterRegistry: MeterRegistry) {
    StatusServer.run(
        meterRegistry,
        HTTP_ACTUATOR,
    )
}

private fun Application.statsigFactory() = shutdownNeeded {
    StatsigFactory(
        sdkKey = config["statsig.sdk.key"],
        userId = config["application.name"],
        isOffline = config.getOrNull("statsig.offline")?.toBoolean() ?: false,
        hfTier = config["tier"],
    )
}
