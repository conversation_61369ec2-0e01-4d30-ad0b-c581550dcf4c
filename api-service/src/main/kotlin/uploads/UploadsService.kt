package com.hellofresh.skuDemandForecast.api.uploads

import com.hellofresh.skuDemandForecast.api.generated.model.UploadViolation
import com.hellofresh.skuDemandForecast.api.uploads.violations.ViolationHandlersPerUploadEvent
import com.hellofresh.skuDemandForecast.api.user.LoggedInUserInfo
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfigClient
import com.hellofresh.skuDemandForecast.featureflags.FeatureFlag
import com.hellofresh.skuDemandForecast.featureflags.StatsigFeatureFlagClient
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.fileupload.FileSource
import com.hellofresh.skudemandforecast.model.fileupload.FileStatus.PENDING
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFile.processFileContent
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileContent
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult.Invalid
import com.hellofresh.skudemandforecast.model.fileupload.ViolationHandlersConfig
import com.hellofresh.skudemandforecast.model.fileupload.violations.Violation
import com.hellofresh.skudemandforecast.model.skuspecification.SkuCodeLookUp
import com.hellofresh.skudemandforecast.skuspecification.repo.SkuSpecificationRepository
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

@Suppress("LongParameterList")
class UploadsService(
    private val uploadsRepository: UploadsRepository,
    private val importsRepository: ImportsRepository,
    private val dcConfigService: DcConfigService,
    private val skuSpecificationRepository: SkuSpecificationRepository,
    private val featureFlagClient: StatsigFeatureFlagClient,
    private val dynamicConfigClient: DynamicConfigClient,
    private val metricsDSLContext: MetricsDSLContext,
    private val readMetricsDSLContext: MetricsDSLContext,
) {

    suspend fun save(uploadsRequest: UploadsRequest): List<UploadViolation> {
        logger.info("Uploaded files ${uploadsRequest.files.joinToString(separator = ", ", postfix = ".")}")
        val violationHandlers = ViolationHandlersPerUploadEvent.createInstances(
            uploadsRequest,
            featureFlagClient,
            dynamicConfigClient,
        )
        val dcs = dcConfigService.dcConfigurations.values.filter { it.market == uploadsRequest.market }
        val skuCodeLookUp = fetchSkusLookUp(dcs)
        val processed = uploadsRequest.files.map {
            it to
                processFileContent(
                    ProcessFileContent(it.name, it.content, null), dcs, it.market, skuCodeLookUp,
                    ViolationHandlersConfig(preViolationHandlers = violationHandlers),
                )
        }

        // We have the file if not severe
        if (!processed.hasSevereViolations()) {
            val toSave = processed.mapNotNull { (file, res) ->
                file.toPendingFileUpload(uploadsRequest.loggedInUserInfo, res)
            }.also { logger.info("Saving ${it.size} upload files") }
            uploadsRepository.saveUploads(toSave, metricsDSLContext)
        }

        val uploadViolations = processed.uploadViolations
        logger.info("Found ${uploadViolations.size} upload violations, [Violations: ${processed.prettyPrint()}]")

        return uploadViolations
    }

    private suspend fun fetchSkusLookUp(dcs: List<DistributionCenter>): SkuCodeLookUp =
        skuSpecificationRepository.fetchSkuCodeLookUp(dcs)

    suspend fun getImportDetails(market: String): List<Import> =
        if (isEnabledMpsImports(market, featureFlagClient)) {
            importsRepository.getImportDetails(market)
        } else {
            uploadsRepository.getUploadedFileDetails(market, readMetricsDSLContext)
        }

    suspend fun getFile(fileId: UUID): Pair<String, ByteArray>? = uploadsRepository.getFile(
        fileId,
        readMetricsDSLContext,
    )

    companion object : Logging {

        fun isEnabledMpsImports(market: String, featureFlagClient: StatsigFeatureFlagClient) =
            featureFlagClient.isEnabledFor(FeatureFlag.MpsImport(MARKET, market))
    }
}

fun List<Pair<UploadedFile, ProcessFileResult>>.hasSevereViolations() =
    any { (_, res) -> res.hasSevereViolations() }

val List<Pair<UploadedFile, ProcessFileResult>>.uploadViolations
    get() = flatMap { (file, res) ->
        if (res is Invalid) {
            res.violations.map { it.toUploadViolation(file.name) }
        } else {
            listOf()
        }
    }

private fun UploadedFile.toPendingFileUpload(
    loggedInUserInfo: LoggedInUserInfo,
    processFileResult: ProcessFileResult,
): FileUpload? = processFileResult.parsedFile?.type?.let { type ->
    FileUpload(
        id = UUID.randomUUID(),
        dcs = processFileResult.dc.toSet(),
        market = this.market,
        weeks = processFileResult.weeks.toSet(),
        fileName = this.name,
        content = this.content,
        info = processFileResult.violations.map { it.prettyPrint() },
        errors = emptyList(),
        fileSource = FileSource.valueOf(source.name),
        fileType = type,
        fileStatus = PENDING,
        authorName = loggedInUserInfo.userName,
        authorEmail = loggedInUserInfo.userEmail,
        createdAt = this.timestamp,
        updatedAt = this.timestamp,
    )
}

fun Violation.toUploadViolation(fileName: String) = UploadViolation(
    fileName = fileName,
    violation = message,
    lines = lineNumbers,
)

fun Violation.prettyPrint() =
    "${message.replace("X", "")} - ${lineNumbers.joinToString(separator = ", ")}"

private fun ProcessFileResult.prettyPrint() =
    "[${
        violations.joinToString(separator = ", ") { it.prettyPrint() }
    }]"

private fun List<Pair<UploadedFile, ProcessFileResult>>.prettyPrint() =
    "[${
        joinToString(separator = ", ") {
            "${it.first.name} - " + it.second.prettyPrint()
        }
    }]"
