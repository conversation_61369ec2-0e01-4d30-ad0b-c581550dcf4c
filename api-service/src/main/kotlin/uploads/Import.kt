package com.hellofresh.skuDemandForecast.api.uploads

import com.hellofresh.skudemandforecast.model.fileupload.FileSource
import com.hellofresh.skudemandforecast.model.fileupload.FileStatus
import com.hellofresh.skudemandforecast.model.fileupload.FileType
import java.time.OffsetDateTime
import java.util.UUID

data class Import(
    val id: UUID,
    val dcs: Set<String>,
    val market: String,
    val weeks: Set<String>,
    val name: String,
    val info: List<String>,
    val errors: List<String>,
    val source: FileSource,
    val type: FileType,
    val status: FileStatus,
    val authorName: String?,
    val authorEmail: String,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
) {

    companion object
}
