package com.hellofresh.skuDemandForecast.api.uploads

import com.hellofresh.skuDemandForecast.api.schema.public_.Tables.IMPORTS_VIEW
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import kotlinx.coroutines.future.await

class ImportsRepository(private val dslContext: MetricsDSLContext) {
    suspend fun getImportDetails(market: String): List<Import> =
        dslContext.withTagName("fetch-import-details")
            .selectFrom(IMPORTS_VIEW)
            .apply {
                where(
                    IMPORTS_VIEW.MARKET.eq(market)
                )
            }
            .orderBy(IMPORTS_VIEW.UPDATED_AT.desc())
            .fetchAsync()
            .thenApply { result ->
                result.map { it.toImport() }
            }
            .await()
}
