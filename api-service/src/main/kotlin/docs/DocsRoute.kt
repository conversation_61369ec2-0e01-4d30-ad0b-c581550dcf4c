package com.hellofresh.skuDemandForecast.api.docs

import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get

private const val DOC_PATH = "openapi.yaml"
fun Routing.docs() {
    authenticate {
        get("/docs") {
            ClassLoader.getSystemClassLoader().getResourceAsStream(DOC_PATH)
                ?.bufferedReader()
                ?.let { call.respondText(it.readText(), ContentType.Any, HttpStatusCode.OK) }
                ?: call.respond(HttpStatusCode.NotFound)
        }
        swaggerUI(path = "swagger", swaggerFile = DOC_PATH)
    }
}
