package com.hellofresh.skuDemandForecast.api.demand

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.api.generated.model.ErrorResponse
import com.hellofresh.skuDemandForecast.api.generated.model.SkuDemandDate
import com.hellofresh.skuDemandForecast.api.objectMapper
import com.hellofresh.skuDemandForecast.api.schema.public_.tables.records.AggregatedDemandRecord
import org.jooq.JSONB

fun List<AggregatedDemandRecord>.toResponse(): List<SkuDemandDate> =
    this.map {
        SkuDemandDate(
            skuId = it.skuId.toString(),
            date = it.productionDate,
            qty = it.recipeBreakdown.toRecipeMap().values.sum(),
            originalDemand = it.originalDemand.toInt(),
        )
    }

fun JSONB.toRecipeMap() = objectMapper.readValue<Map<String, Int>>(data())

fun mapToErrorResponse(throwable: Throwable) = ErrorResponse(throwable.message)
