package com.hellofresh.skuDemandForecast.api.demand

import com.hellofresh.skuDemandForecast.api.ktor.getAllOrDefault
import com.hellofresh.skuDemandForecast.api.ktor.getOrThrow
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import java.time.Duration
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.time.withTimeout

fun Routing.demand(
    demandRepository: DemandRepository,
    timeout: Duration
) = authenticate {
    get("/demand/{dcCode}") {
        kotlin.runCatching {
            val dcCode = call.parameters.getOrThrow("dcCode")
            val from = this.call.parameters.getOrThrow("from").let { LocalDate.parse(it) }
            val to = this.call.parameters.getOrThrow("to").let { LocalDate.parse(it) }
            val skuIds = this.call.parameters.getAllOrDefault("skuId").map(UUID::fromString).toSet()
            DemandRequest(dcCode, from, to, skuIds)
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
        }.onSuccess { req ->
            kotlin.runCatching {
                withTimeout(timeout) {
                    with(req) {
                        demandRepository.fetchDemand(dcCode, from, to, skuIds).toResponse()
                    }
                }
            }.onSuccess { result ->
                call.respond(HttpStatusCode.OK, result)
            }.onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
        }
    }
}
