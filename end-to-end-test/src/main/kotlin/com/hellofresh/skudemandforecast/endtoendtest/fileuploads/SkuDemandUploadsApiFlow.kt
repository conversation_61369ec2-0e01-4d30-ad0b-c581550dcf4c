package com.hellofresh.skudemandforecast.endtoendtest.fileuploads

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.assertions.AggregatedDemandAssertion
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.LocalDate
import java.time.ZoneId
import java.util.Properties
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import org.awaitility.core.ConditionTimeoutException

const val TOPIC_DEMAND_TOPIC_NAME = "public.demand.sku-demand-forecast.v2"
const val UNKNOWN_BRAND = "UNKNOWN_BRAND"

class SkuDemandUploadsApiFlow(
    private val dslContext: MetricsDSLContext,
    private val fileUploadHandler: FileUploadHandler,
    brokerProperties: Properties,
) : FunctionalTest() {

    private val assertion = AggregatedDemandAssertion("E2E_Api_upload_Flow", dslContext, brokerProperties)

    suspend fun verifyForecastUploadsProcessing() {
        try {
            val skuId = UUID.randomUUID()
            val parentId = UUID.randomUUID()
            val parentCode = "SPI-000-0003"

            persistDistributionCenter(dslContext, DC_CODE, MARKET, ZoneId.of(ZONE_ID))
            persistSkuSpecificationRecord(
                listOf(
                    Sku(skuId, SKU_CODE),
                ),
                Sku(parentId, parentCode),
                dslContext,
                MARKET,
            )
            val zoneId = ZoneId.of(ZONE_ID)
            val date = LocalDate.now(zoneId).plusWeeks(1)
            val week = DcWeek(date, date.dayOfWeek).value

            fileUploadHandler.uploadFiles(week, date.dayOfWeek)

            assertion.assertAggregatedDemand(skuId, DC_CODE, date, EXPECTED_TOTAL_QUANTITY)
        } catch (e: ConditionTimeoutException) {
            logger.warn("Failed to assert the sku demand uploads api results.")
            throw AssertionError("Failed to assert the sku demand uploads api results.", e)
        }
    }

    companion object : Logging
}
