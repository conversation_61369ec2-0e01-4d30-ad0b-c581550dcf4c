package com.hellofresh.skudemandforecast.endtoendtest.kafka

import java.time.Duration
import java.util.Properties
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.serialization.Serde
import org.apache.logging.log4j.kotlin.Logging
import org.awaitility.Awaitility.await
import org.awaitility.core.ConditionTimeoutException

private const val ASSERTION_TIMEOUT_IN_SECONDS = 720L
private const val KAFKA_POLLING_TIMEOUT_IN_SECONDS = 5L

class KafkaSource<K, V>(
    private val topic: String,
    properties: Properties,
    serdeKey: Serde<K>,
    serdeValue: Serde<V>
) : Logging {
    private val consumer = KafkaConsumer(
        properties,
        serdeKey.deserializer(),
        serdeValue.deserializer(),
    )

    private val groupId = properties[ConsumerConfig.GROUP_ID_CONFIG]

    init {
        logger.info("Kafka source consumer $topic-$groupId")
        val partitions = consumer.partitionsFor(topic).map { TopicPartition(topic, it.partition()) }
        consumer.assign(partitions)
        consumer.endOffsets(partitions).forEach { logger.info("Partition Assigned: ${it.key}, Offset:. ${it.value}") }
        consumer.seekToBeginning(partitions)
    }

    fun assertEventually(
        expected: Map<K, V>,
        assertion: (V, V) -> Boolean = { v1, v2 -> v1 == v2 },
        timeout: Duration = Duration.ofSeconds(ASSERTION_TIMEOUT_IN_SECONDS),
    ) {
        logger.info("Checking final demand results in the kafka topic: $topic-$groupId")
        val allRecords = mutableMapOf<K, V>()
        try {
            await()
                .atMost(timeout)
                .until { assertRecords(allRecords, expected, assertion) }
            logger.info("Final demand results verified in the kafka topic: $topic-$groupId")
        } catch (e: ConditionTimeoutException) {
            logger.warn("Failed to assert. $topic-$groupId [\n\tallRecords=$allRecords\n\texpected=$expected]")
            throw AssertionError("Failed to assert the expected kafka messages: $topic-$groupId", e)
        }
    }

    private fun assertRecords(
        allRecords: MutableMap<K, V>,
        expected: Map<K, V>,
        assertion: (V, V) -> Boolean
    ): Boolean {
        val timeout = Duration.ofSeconds(KAFKA_POLLING_TIMEOUT_IN_SECONDS)
        val records = consumer.poll(timeout)
        allRecords.putAll(records.map { it.key() to it.value() })
        logger.info { "$topic-$groupId records consumer ${allRecords.count()}." }
        return if (allRecords.keys.containsAll(expected.keys)) {
            val actualValues = allRecords.filter { expected.keys.contains(it.key) }

            logger.info {
                "$topic-$groupId: Expected published demand value = ${expected.values.toSet()} , " +
                    "actual published demand values = $actualValues."
            }
            expected.all { (key, value) -> assertion(value, actualValues[key]!!) }
        } else {
            false
        }
    }
}
