package com.hellofresh.skudemandforecast.endtoendtest.fileuploads

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.tables.records.SkuSpecificationRecord
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

const val ASSERTION_TIMEOUT_IN_SECONDS = 300L
val timeout: Duration = Duration.ofSeconds(ASSERTION_TIMEOUT_IN_SECONDS)
const val CHECK_INTERVAL = 5L

open class FunctionalTest {
    fun persistSkuSpecificationRecord(
        skus: List<Sku>,
        parentSku: Sku? = null,
        dataSource: MetricsDSLContext,
        market: String,
    ): List<SkuSpecificationRecord> {
        val records = skus
            .map { (id, code, packaging) ->
                SkuSpecificationRecord(
                    id,
                    parentSku?.id,
                    code,
                    LocalDateTime.now(),
                    null,
                    market.lowercase(),
                    null,
                    packaging ?: "packaging"
                )
            }
            .toMutableList()

        if (parentSku != null) {
            records.add(
                SkuSpecificationRecord(
                    parentSku.id,
                    null,
                    parentSku.code,
                    LocalDateTime.now(),
                    null,
                    market.lowercase(),
                    null,
                    "packaging"
                )
            )
        }

        dataSource.batchMerge(records).execute()
        return records
    }

    fun persistDistributionCenter(
        dataSource: MetricsDSLContext,
        code: String,
        market: String,
        zoneId: ZoneId
    ) =
        DcConfigRecord()
            .apply {
                dcCode = code
                productionStart = LocalDate.now(zoneId).dayOfWeek.name
                this.market = market
                this.zoneId = zoneId.id
                enabled = true
            }.also {
                dataSource.batchInsert(it).execute()
            }
}
