package com.hellofresh.skudemandforecast.endtoendtest.fileuploads

import com.hellofresh.skudemandforecast.endtoendtest.CsvFileGenerator
import com.hellofresh.skudemandforecast.endtoendtest.api.FileUploadsHttpClient
import java.time.DayOfWeek
import kotlin.test.assertEquals
import org.apache.logging.log4j.kotlin.Logging

private const val DEMAND_FILE = "demandfile"
private const val RECIPE_FILE = "recipefile"
const val DC_CODE = "SP"
const val MARKET = "ES"
private const val COUNTRY = "ES"
private const val LOCALE = ""
const val ZONE_ID = "Europe/Madrid"
const val SKU_CODE = "PTN-10-27697-4"
const val EXPECTED_TOTAL_QUANTITY = 10L

class FileUploadHandler(
    private val fileUploadsHttpClient: FileUploadsHttpClient,
) {

    fun uploadFiles(week: String, dayOfWeek: DayOfWeek) {
        runCatching {
            val demandFile = CsvFileGenerator.generateDemandCsv(
                dcCode = DC_CODE,
                country = COUNTRY,
                locale = LOCALE,
                week = week,
                dayOfWeek = dayOfWeek,
                demand = EXPECTED_TOTAL_QUANTITY,
                recipeIndex = 21,
            )
            val recipeFile = CsvFileGenerator.generateRecipeCsv(
                country = COUNTRY,
                locale = LOCALE,
                skuCode = SKU_CODE,
                week = week,
                picks = 1,
                recipeIndex = 21,
            )
            val demandUploadResults = fileUploadsHttpClient.uploadFile(
                MARKET,
                COUNTRY,
                demandFile,
                DEMAND_FILE,
            )
            val recipeUploadResults = fileUploadsHttpClient.uploadFile(
                MARKET,
                COUNTRY,
                recipeFile,
                RECIPE_FILE,
            )
            assertEquals("File uploaded successfully", demandUploadResults)
            assertEquals("File uploaded successfully", recipeUploadResults)
        }.onFailure { logger.warn("Sku Demand Uploads Api error.", it) }
    }

    companion object : Logging
}
