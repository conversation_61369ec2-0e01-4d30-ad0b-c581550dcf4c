package com.hellofresh.skudemandforecast.endtoendtest.api

import okhttp3.MultipartBody
import retrofit2.Call
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

interface FileUploadsEndpoints {
    @Multipart
    @POST("/uploads")
    fun uploadFiles(
        @Query("market") market: String = "GB",
        @Query("country") country: String = "UK",
        @Part file: MultipartBody.Part,
        @Header("Authorization") token: String
    ): Call<Void>
}
