package com.hellofresh.skudemandforecast.endtoendtest

import com.hellofresh.sdf.lib.kafka.loadKafkaConfigurations
import com.hellofresh.service.Application
import com.hellofresh.service.runApplication
import com.hellofresh.skuDemandForecast.db.DBConfiguration
import com.hellofresh.skuDemandForecast.db.DatabaseConfig
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.api.FileUploadsHttpClient
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.FileUploadHandler
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.SkuDemandUploadsApiFlow
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.AGGREGATED_DEMAND
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.DC_CONFIG
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.FILE_UPLOADS
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.FRACTION_SKU_DEMAND
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.IMPORTS
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.RECIPE
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.RECIPE_SNAPSHOT
import com.hellofresh.skudemandforecast.endtoendtest.schema.public_.Tables.SKU_SPECIFICATION
import java.util.Properties
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

private const val READ_WRITE_DB_POOL_SIZE = 2
private fun Application.readWriteDslContext() =
    DBConfiguration.jooqDslContext(
        databaseConfig = this.databaseConfig("readWrite"),
        readOnly = false,
        parallelism = READ_WRITE_DB_POOL_SIZE,
        meterRegistry = meterRegistry,
    )

private fun Application.databaseConfig(name: String) =
    DatabaseConfig(
        configName = name,
        hostName = config["db.host"],
        userName = config["db.username"],
        password = config["db.password"],
    )

suspend fun main() = runApplication {
    runTest()
}

suspend fun Application.runTest() {
    val dslContext = readWriteDslContext()
    val awsLocalHost = config["aws.local.host"]
    val brokerProperties = Properties().apply {
        putAll(config.loadKafkaConfigurations())
    }
    try {
        kotlinx.coroutines.withContext(Dispatchers.IO) {
            launch {
                SkuDemandUploadsApiFlow(
                    dslContext,
                    FileUploadHandler(FileUploadsHttpClient(config["sku.demand.forecast.api.server"])),
                    brokerProperties,
                ).verifyForecastUploadsProcessing()
            }
            launch {
                SkuDemandDoveDachImportFlow(dslContext, brokerProperties).execute()
            }
            launch {
                SkuDemandUKImportFlow(awsLocalHost, dslContext, brokerProperties).execute()
            }
            launch {
                SkuDemandNordicImportFlow(awsLocalHost, dslContext, brokerProperties).execute()
            }
            launch {
                SkuDemandANZImportFlow(dslContext, brokerProperties).execute()
            }
        }
    } finally {
        cleanUp(dslContext)
    }
}

fun cleanUp(dslContext: MetricsDSLContext) {
    dslContext.deleteFrom(DC_CONFIG).execute()
    dslContext.deleteFrom(SKU_SPECIFICATION).execute()
    dslContext.deleteFrom(DEMAND).execute()
    dslContext.deleteFrom(RECIPE).execute()
    dslContext.deleteFrom(RECIPE_SNAPSHOT).execute()
    dslContext.deleteFrom(IMPORTS).execute()
    dslContext.deleteFrom(FILE_UPLOADS).execute()
    dslContext.deleteFrom(FRACTION_SKU_DEMAND).execute()
    dslContext.deleteFrom(AGGREGATED_DEMAND).execute()
}
