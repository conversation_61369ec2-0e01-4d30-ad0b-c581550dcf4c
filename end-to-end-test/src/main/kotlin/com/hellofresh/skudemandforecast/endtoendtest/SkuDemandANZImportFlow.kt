package com.hellofresh.skudemandforecast.endtoendtest

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.assertions.AggregatedDemandAssertion
import com.hellofresh.skudemandforecast.endtoendtest.assertions.ExpectedDemandSku
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.FunctionalTest
import com.hellofresh.skudemandforecast.endtoendtest.kafka.KafkaANZDemandProtoProducer
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.Properties
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

private const val DC_CODE = "SY"
private const val MARKET = "AU"
private const val SKU_CODE1 = "BAK-10-00777-1"
private const val EXPECTED_DEMAND = 600L

class SkuDemandANZImportFlow(
    private val dslContext: MetricsDSLContext,
    brokerProperties: Properties,
) : FunctionalTest() {

    private val kafkaANZDemandProtoProducer = KafkaANZDemandProtoProducer(brokerProperties)

    private val assertion = AggregatedDemandAssertion("E2E_ANZ_Flow", dslContext, brokerProperties)

    fun execute() {
        logger.info("Starting ANZ Import Flow")

        persistDistributionCenter(dslContext, code = DC_CODE, market = MARKET, zoneId = ZoneOffset.UTC)
        val sku1 = Sku(UUID.randomUUID(), SKU_CODE1)
        persistSkuSpecificationRecord(listOf(sku1), null, dslContext, MARKET)

        val date = LocalDate.parse("2025-04-09")

        // ANZ Demand Topic
        pushDemandMessage()

        // Recipe coming from wiremock MPS api
        assertion.assertAggregatedDemand(
            listOf(
                ExpectedDemandSku(sku1.id, DC_CODE, date, EXPECTED_DEMAND),
            ),
        )

        logger.info("Ending ANZ Import Flow")
    }

    @Suppress("LongParameterList")
    private fun pushDemandMessage() {
        kafkaANZDemandProtoProducer.produceDemandData()
    }

    companion object : Logging
}
