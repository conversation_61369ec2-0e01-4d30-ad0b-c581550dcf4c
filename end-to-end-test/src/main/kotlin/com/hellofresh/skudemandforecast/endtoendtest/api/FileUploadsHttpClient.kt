package com.hellofresh.skudemandforecast.endtoendtest.api

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.github.resilience4j.retry.Retry
import io.github.resilience4j.retry.RetryConfig
import java.io.File
import java.time.Duration
import java.util.UUID
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.MultipartBody.Part.Companion.createFormData
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import org.apache.logging.log4j.kotlin.Logging
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory

private const val MAX_RETRIES = 1
private const val WAIT_DURATION_BETWEEN_RETRIES_MS = 1000L
private const val DEFAULT_TIMEOUT_MILLIS: Long = 300000L
private const val PRECONDITION_FAILED_HTTP_CODE = 412
private const val EMAIL = "email"
private const val METADATA = "metadata"
private const val NAME = "name"
private const val TEXT_CSV = "text/csv"
private const val JWT_SECRET = "hello"
private const val AUTHOR_EMAIL = "test-user-email"
private const val AUTHOR_NAME = "test-user-name"
private const val ISSUER = "issuer"
private const val CLIENT_ID = "test"

class FileUploadsHttpClient(serviceUrl: String) {
    private val client = createEndpoints(
        serviceUrl,
        createHttpClient(),
        objectMapper(),
    )
    private val retryConfig = RetryConfig.custom<Any>()
        .maxAttempts(MAX_RETRIES)
        .waitDuration(Duration.ofMillis(WAIT_DURATION_BETWEEN_RETRIES_MS))
        .build()
    private val retry = Retry.of("testApplication.retry", retryConfig)

    fun uploadFile(market: String, country: String, file: File, fileName: String): String =
        retry.executeCallable {
            val token = buildJwtToken(JWT_SECRET) {
                this.withClaim(EMAIL, AUTHOR_EMAIL)
                this.withClaim(METADATA, mapOf(NAME to AUTHOR_NAME))
            }
            val response = client.uploadFiles(
                market,
                country,
                createMultipartBodyPart(fileName, file),
                "Bearer $token",
            ).execute()
            when {
                response.isSuccessful -> "File uploaded successfully"
                response.code() == PRECONDITION_FAILED_HTTP_CODE -> {
                    logger.warn(
                        "Bad request response while uploading the demand / recipe file, response = $response",
                    )
                    ""
                }

                else -> {
                    logger.error(
                        "Error occurred while getting the uploading the demand / recipe file, response = $response",
                    )
                    ""
                }
            }
        }

    private fun createHttpClient(
        readTimeout: Duration = Duration.ofMillis(DEFAULT_TIMEOUT_MILLIS),
        connectTimeout: Duration = Duration.ofMillis(DEFAULT_TIMEOUT_MILLIS),
    ): OkHttpClient = OkHttpClient.Builder()
        .readTimeout(readTimeout)
        .connectTimeout(connectTimeout)
        .build()

    private fun objectMapper(): ObjectMapper {
        val objectMapper = jacksonObjectMapper()
        objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.ALWAYS)
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        return objectMapper
    }

    private fun createEndpoints(
        baseUrl: String,
        okHttpClient: OkHttpClient,
        objectMapper: ObjectMapper,
    ): FileUploadsEndpoints = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(okHttpClient)
        .addConverterFactory(JacksonConverterFactory.create(objectMapper))
        .build()
        .create(FileUploadsEndpoints::class.java)

    private fun buildJwtToken(
        jwtSecret: String,
        jwtBuilderConf: JWTCreator.Builder.() -> Unit
    ): String =
        JWT.create()
            .withIssuer(ISSUER)
            .withAudience(CLIENT_ID)
            .withClaim("sub", UUID.randomUUID().toString())
            .also(jwtBuilderConf)
            .sign(Algorithm.HMAC256(jwtSecret))

    private fun createMultipartBodyPart(name: String, file: File): MultipartBody.Part =
        createFormData(name, name, file.asRequestBody(TEXT_CSV.toMediaTypeOrNull()))

    companion object : Logging
}
