package com.hellofresh.skudemandforecast.endtoendtest

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.assertions.AggregatedDemandAssertion
import com.hellofresh.skudemandforecast.endtoendtest.assertions.ExpectedDemandSku
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.FunctionalTest
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.io.File
import java.net.URI
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.time.format.DateTimeFormatter
import java.util.Properties
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

private const val DC_CODE = "BV"
private const val MARKET = "GB"
private const val COUNTRY = "GB"
private const val LOCALE = "GB"

private const val SCO_RECIPE_DEMAND = 23L
private const val SCO_RECIPE_PICKS = 2L
private const val SCO_RECIPE_INDEX = 11
private const val SCO_SKU_CODE = "PHF-11-11111-1"

@Suppress("UnusedPrivateProperty")
private const val SCO_EXPECTED_DEMAND = SCO_RECIPE_DEMAND * SCO_RECIPE_PICKS

private const val D4_RECIPE_DEMAND = 17L
private const val D4_RECIPE_PICKS = 4L
private const val D4_RECIPE_INDEX = 22
private const val D4_SKU_CODE = "PHF-22-22222-2"

@Suppress("UnusedPrivateProperty")
private const val D4_EXPECTED_DEMAND = D4_RECIPE_DEMAND * D4_RECIPE_PICKS

private const val BUCKET = "hf-bi-dwh-uploader-e2e"
private const val SCO_S3_PREFIX = "sco_ordering_file"
private const val D4_S3_PREFIX = "gb_recipe_forecast"
private const val QUEUE_URL = "http://sqs.eu-west-1.localhost.localstack.cloud:4566/000000000000/hf_ip_demand_import_notifications_queue_e2e"

class SkuDemandUKImportFlow(
    awsS3Host: String,
    private val dslContext: MetricsDSLContext,
    brokerProperties: Properties,
) : FunctionalTest() {

    private val sqsClient = SqsClient.builder()
        .endpointOverride(URI.create(awsS3Host))
        .region(Region.EU_WEST_1)
        .credentialsProvider(AnonymousCredentialsProvider.create())
        .build()

    private val s3Client = S3Client.builder()
        .endpointOverride(URI.create(awsS3Host))
        .forcePathStyle(true)
        .region(Region.EU_WEST_1)
        .credentialsProvider(AnonymousCredentialsProvider.create())
        .build()

    private val assertion = AggregatedDemandAssertion("E2E_UK_Flow", dslContext, brokerProperties)

    fun execute() {
        logger.info("Starting UK ImportFlow")

        persistDistributionCenter(dslContext, code = DC_CODE, market = MARKET, zoneId = UTC)
        val sku1 = Sku(UUID.randomUUID(), SCO_SKU_CODE)
        val sku2 = Sku(UUID.randomUUID(), D4_SKU_CODE)
        persistSkuSpecificationRecord(listOf(sku1, sku2), null, dslContext, MARKET)

        val today = LocalDate.now(UTC)
        val dayOfWeek = today.dayOfWeek
        val scoDate = today.plusWeeks(1)
        val scoWeek = DcWeek(scoDate, today.dayOfWeek).value
        val d4Date = today.plusWeeks(2)
        val d4Week = DcWeek(d4Date, today.dayOfWeek).value
        uploadS3Demand(scoWeek, d4Week, dayOfWeek)

        assertion.assertAggregatedDemand(
            listOf(
                ExpectedDemandSku(sku1.id, DC_CODE, scoDate, SCO_EXPECTED_DEMAND),
                ExpectedDemandSku(sku2.id, DC_CODE, d4Date, D4_EXPECTED_DEMAND),
            ),
        )

        logger.info("Ending UK ImportFlow")
    }

    private fun uploadS3Demand(scoWeek: String, d4Week: String, dayOfWeek: DayOfWeek) {
        val sco = CsvFileGenerator.generateDemandCsv(
            dcCode = DC_CODE,
            country = COUNTRY,
            locale = LOCALE,
            week = scoWeek,
            dayOfWeek = dayOfWeek,
            demand = SCO_RECIPE_DEMAND,
            recipeIndex = SCO_RECIPE_INDEX,
        )
        // SCO should have priority
        val d4 = CsvFileGenerator.generateDemandCsv(
            listOf(
                DemandCsvLine(
                    dcCode = DC_CODE,
                    country = COUNTRY,
                    locale = LOCALE,
                    week = scoWeek,
                    dayOfWeek = dayOfWeek,
                    demand = SCO_RECIPE_DEMAND * 100L,
                    recipeIndex = SCO_RECIPE_INDEX,
                ),
                DemandCsvLine(
                    dcCode = DC_CODE,
                    country = COUNTRY,
                    locale = LOCALE,
                    week = d4Week,
                    dayOfWeek = dayOfWeek,
                    demand = D4_RECIPE_DEMAND,
                    recipeIndex = D4_RECIPE_INDEX,
                ),
            ),
        )

        val scoKey = uploadS3(sco, SCO_S3_PREFIX)
        val d4Key = uploadS3(d4, D4_S3_PREFIX)
        sendSqs(scoKey)
        sendSqs(d4Key)
    }

    private fun uploadS3(file: File, keyPrefix: String): String {
        val key = "$keyPrefix/${file.name}"
        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(BUCKET)
                .key(key)
                .build(),
            RequestBody.fromFile(file),
        )

        s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(BUCKET)
                .key(key)
                .build(),
        ).also { uploadedFile ->
            logger.info("Uploaded content file: ${file.name}: ${uploadedFile.readAllBytes().toString(Charsets.UTF_8)}")
        }
        return key
    }

    private fun sendSqs(key: String) {
        sqsClient.sendMessage(
            SendMessageRequest.builder()
                .queueUrl(QUEUE_URL)
                .messageBody(
                    """
                        {
                        "Type":"Notification",
                        "Subject":"Amazon S3 Notification",
                        "Message":"{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"$BUCKET\"},\"object\":{\"key\":\"$key\"}}}]}",
                        "Timestamp":"${DateTimeFormatter.ISO_DATE_TIME.format(OffsetDateTime.now(UTC))}"
                        }
                    """.trimIndent(),
                ).build(),
        )
        logger.info("Sending SQS message to: $QUEUE_URL")
    }

    companion object : Logging
}
