package com.hellofresh.skudemandforecast.endtoendtest

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.assertions.AggregatedDemandAssertion
import com.hellofresh.skudemandforecast.endtoendtest.assertions.ExpectedDemandSku
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.FunctionalTest
import com.hellofresh.skudemandforecast.endtoendtest.kafka.KafkaDoveDemandProtoProducer
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.Properties
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

private const val DC_CODE = "BX"
private const val MARKET = "DACH"
private const val LOCALE = "DE"
private const val SKU_CODE1 = "PHF-99-00000-1"
private const val SKU_CODE2 = "PHF-99-00000-2"
private const val DOVE_RECIPE_ID1 = "R11"
private const val DOVE_RECIPE_ID2 = "R22"
private const val DEFAULT_PEOPLE_COUNT = 2
private const val KITTING_ALLOWED_PACKAGING = "KITTING_PACKAGING"

private const val RECIPE_DEMAND1 = 14
private const val RECIPE_PICKS1 = 2L
private const val EXPECTED_DEMAND1 = RECIPE_DEMAND1 * RECIPE_PICKS1 * 4 * 2

private const val RECIPE_DEMAND2 = 45
private const val RECIPE_PICKS2 = 3L
private const val EXPECTED_DEMAND2 = RECIPE_DEMAND2 * RECIPE_PICKS2

class SkuDemandDoveDachImportFlow(
    private val dslContext: MetricsDSLContext,
    brokerProperties: Properties,
) : FunctionalTest() {

    private val kafkaDoveDemandProtoProducer = KafkaDoveDemandProtoProducer(brokerProperties)

    private val assertion = AggregatedDemandAssertion("E2E_Dove_Dach_Flow", dslContext, brokerProperties)

    fun execute() {
        logger.info("Starting Dove Dach Import Flow")

        persistDistributionCenter(dslContext, code = DC_CODE, market = MARKET, zoneId = ZoneOffset.UTC)
        val sku1 = Sku(UUID.randomUUID(), SKU_CODE1)
        val sku2 = Sku(UUID.randomUUID(), SKU_CODE2, KITTING_ALLOWED_PACKAGING)
        persistSkuSpecificationRecord(listOf(sku1, sku2), null, dslContext, MARKET)

        val date = LocalDate.now(ZoneOffset.UTC).plusWeeks(1)
        val assemblyTime = date.atTime(LocalTime.NOON).atZone(ZoneOffset.UTC)
        val kittingTime = assemblyTime.minusDays(1)
        val week = DcWeek(date, date.dayOfWeek)

        // DOVE Demand Topic
        pushDemandMessageForMultipleDates(DOVE_RECIPE_ID1, date, week, RECIPE_DEMAND1, assemblyTime, kittingTime)
        pushDemandMessageForMultipleDates(
            DOVE_RECIPE_ID1,
            date.plusDays(1),
            week,
            RECIPE_DEMAND1,
            assemblyTime,
            kittingTime
        )
        pushDemandMessage(DOVE_RECIPE_ID2, date, week, RECIPE_DEMAND2, assemblyTime, kittingTime)

        // Recipe coming from wiremock MPS api

        assertion.assertAggregatedDemand(
            listOf(
                ExpectedDemandSku(sku1.id, DC_CODE, assemblyTime.toLocalDate(), EXPECTED_DEMAND1),
                ExpectedDemandSku(sku1.id, DC_CODE, assemblyTime.toLocalDate().plusDays(1), EXPECTED_DEMAND1),
                ExpectedDemandSku(sku1.id, DC_CODE, assemblyTime.toLocalDate().plusDays(2), EXPECTED_DEMAND1),
                ExpectedDemandSku(sku2.id, DC_CODE, kittingTime.toLocalDate(), EXPECTED_DEMAND2),
            ),
        )

        logger.info("Ending Dove Dach Import Flow")
    }

    @Suppress("LongParameterList")
    private fun pushDemandMessage(
        doveRecipeId: String,
        date: LocalDate,
        week: DcWeek,
        mealsToDeliver: Int,
        assemblyTime: ZonedDateTime,
        kittingTime: ZonedDateTime
    ) {
        kafkaDoveDemandProtoProducer.produceDemandData(
            DC_CODE,
            doveRecipeId,
            date,
            week,
            DEFAULT_PEOPLE_COUNT,
            mealsToDeliver,
            LOCALE,
            assemblyTime,
            kittingTime
        )
    }

    @Suppress("LongParameterList")
    private fun pushDemandMessageForMultipleDates(
        doveRecipeId: String,
        date: LocalDate,
        week: DcWeek,
        mealsToDeliver: Int,
        assemblyTime: ZonedDateTime,
        kittingTime: ZonedDateTime
    ) {
        kafkaDoveDemandProtoProducer.produceDemandDataForMultipleDates(
            DC_CODE,
            doveRecipeId,
            date,
            week,
            DEFAULT_PEOPLE_COUNT,
            mealsToDeliver,
            LOCALE,
            assemblyTime,
            kittingTime
        )
    }

    companion object : Logging
}
