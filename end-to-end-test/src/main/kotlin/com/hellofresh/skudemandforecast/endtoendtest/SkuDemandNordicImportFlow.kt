package com.hellofresh.skudemandforecast.endtoendtest

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.endtoendtest.assertions.AggregatedDemandAssertion
import com.hellofresh.skudemandforecast.endtoendtest.fileuploads.FunctionalTest
import com.hellofresh.skudemandforecast.endtoendtest.model.Sku
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.io.File
import java.net.URI
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.time.format.DateTimeFormatter
import java.util.Properties
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

private const val DC_CODE = "MO"
private const val MARKET = "DKSE"
private const val COUNTRY = "SE"
private const val LOCALE = "NO"
private const val SKU_CODE = "PHF-00-10135-2"

private const val NORDIC_BUCKET = "hf-nordics-e2e"
private const val KEY_PREFIX = "standardized/forecasting/inventory_planner_recipe_forecast"
private const val QUEUE_URL = "http://sqs.eu-west-1.localhost.localstack.cloud:4566/000000000000/hf_ip_demand_import_notifications_queue_e2e"

private const val RECIPE_DEMAND = 16L
private const val RECIPE_PICKS = 3L

@Suppress("UnusedPrivateProperty")
private const val EXPECTED_DEMAND = RECIPE_DEMAND * RECIPE_PICKS

class SkuDemandNordicImportFlow(
    awsLocalHost: String,
    private val dslContext: MetricsDSLContext,
    brokerProperties: Properties,
) : FunctionalTest() {

    private val s3Client = S3Client.builder()
        .endpointOverride(URI.create(awsLocalHost))
        .forcePathStyle(true)
        .region(Region.EU_WEST_1)
        .credentialsProvider(AnonymousCredentialsProvider.create())
        .build()

    private val sqsClient = SqsClient.builder()
        .endpointOverride(URI.create(awsLocalHost))
        .region(Region.EU_WEST_1)
        .credentialsProvider(AnonymousCredentialsProvider.create())
        .build()

    private val assertion = AggregatedDemandAssertion("E2E_Nordic_Flow", dslContext, brokerProperties)

    fun execute() {
        logger.info("Starting Nordic Import Flow")

        persistDistributionCenter(dslContext, code = DC_CODE, market = MARKET, zoneId = UTC)
        val sku = Sku(UUID.randomUUID(), SKU_CODE)
        persistSkuSpecificationRecord(listOf(sku), null, dslContext, MARKET)

        val date = LocalDate.now(UTC).plusWeeks(1)
        val week = DcWeek(date, date.dayOfWeek).value

        val demand = CsvFileGenerator.generateDemandCsv(
            DC_CODE,
            COUNTRY,
            LOCALE,
            week,
            date.dayOfWeek,
            RECIPE_DEMAND,
        )

        val key = uploadS3(demand)

        sendSqs(key)

        assertion.assertAggregatedDemand(sku.id, DC_CODE, date, EXPECTED_DEMAND)

        logger.info("Ending Nordic Import Flow")
    }

    private fun sendSqs(key: String) {
        sqsClient.sendMessage(
            SendMessageRequest.builder()
                .queueUrl(QUEUE_URL)
                .messageBody(
                    """
                        {
                        "Type":"Notification",
                        "Subject":"Amazon S3 Notification",
                        "Message":"{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"$NORDIC_BUCKET\"},\"object\":{\"key\":\"$key\"}}}]}",
                        "Timestamp":"${DateTimeFormatter.ISO_DATE_TIME.format(OffsetDateTime.now(UTC))}"
                        }
                    """.trimIndent(),
                ).build(),
        )
        logger.info("Sending SQS message to: $QUEUE_URL")
    }

    private fun uploadS3(file: File): String {
        val key = KEY_PREFIX + "_" + file.name
        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(NORDIC_BUCKET)
                .key(key)
                .build(),
            RequestBody.fromFile(file),
        )

        s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(NORDIC_BUCKET)
                .key(key)
                .build(),
        ).also { uploadedFile ->
            logger.info("Uploaded content file: ${file.name}: ${uploadedFile.readAllBytes().toString(Charsets.UTF_8)}")
        }

        return key
    }

    companion object : Logging
}
