package com.hellofresh.skudemandforecast.lib.substitution.repo

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.model.substitution.Substitution
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import com.hellofresh.skudemandforecast.lib.substitution.CreateSubstitution
import com.hellofresh.skudemandforecast.lib.substitution.EditSubstitution
import com.hellofresh.skudemandforecast.lib.substitution.schema.public_.Tables.SKU_SUBSTITUTION
import com.hellofresh.skudemandforecast.lib.substitution.schema.public_.tables.records.SkuSubstitutionRecord
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB
import org.jooq.impl.DSL.inline

private const val DEFAULT_VERSION = 0

class SubstitutionRepository(private val dslContext: MetricsDSLContext) {

    suspend fun createSubstitution(createSubstitution: CreateSubstitution) = dslContext.createSubstitution(
        createSubstitution,
    )

    suspend fun retrieveSubstitutionWithVersion(
        substitutionIdParam: UUID
    ): List<Substitution> =
        dslContext.withTagName(retrieveSubstitutionTag)
            .selectFrom(SKU_SUBSTITUTION)
            .where(
                SKU_SUBSTITUTION.ID.eq(substitutionIdParam),
            ).fetchAsync()
            .thenApply {
                it.map { record -> record.toSubstitution() }
            }.await()

    suspend fun retrieveSubstitutionsWithVersion(
        dcCode: String,
        from: LocalDate,
        to: LocalDate,
    ): List<List<Substitution>> =
        dslContext.withTagName(retrieveAllSubstitutionTag)
            .selectFrom(SKU_SUBSTITUTION)
            .where(
                SKU_SUBSTITUTION.DC_CODE.eq(dcCode)
                    .and(SKU_SUBSTITUTION.FROM_DATE.ge(from))
                    .and(SKU_SUBSTITUTION.TO_DATE.le(to)),
            ).fetchAsync()
            .thenApply {
                it.groupBy { substitutionRecord ->
                    substitutionRecord.id
                }.map { (_, substitutionRecords) ->
                    substitutionRecords.map { substitutionRecord ->
                        substitutionRecord.toSubstitution()
                    }
                }
            }.await()

    suspend fun retrieveSubstitution(
        substitutionFieldsKey: SubstitutionFieldsKey
    ) = dslContext.retrieveSubstitution(substitutionFieldsKey)

    suspend fun editSubstitution(editSubstitution: EditSubstitution): SubstitutionKey? = dslContext.editSubstitution(
        editSubstitution,
    )

    companion object : Logging {

        private const val createSubstitutionTag = "create-substitution"
        private const val updateSubstitutionTag = "update-substitution"
        private const val retrieveSubstitutionTag = "retrieve-substitution-with-version"
        private const val retrieveAllSubstitutionTag = "retrieve-all-substitution-with-version"

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        fun createID(substitutionFieldsKey: SubstitutionFieldsKey) =
            UUID.nameUUIDFromBytes(
                objectMapper.writeValueAsBytes(substitutionFieldsKey),
            )

        suspend fun MetricsDSLContext.createSubstitution(createSubstitution: CreateSubstitution): SubstitutionKey {
            val skuSubstitutionInId = createID(
                SubstitutionFieldsKey(
                    createSubstitution.reference,
                    createSubstitution.dcCode,
                    createSubstitution.fromDate,
                    createSubstitution.toDate,
                    createSubstitution.substitutionDetail.skuOut.skuId,
                ),
            )

            val substitutionDetailJson = objectMapper.writeValueAsString(createSubstitution.substitutionDetail)
            withTagName(createSubstitutionTag)
                .insertInto(SKU_SUBSTITUTION).columns(
                    SKU_SUBSTITUTION.ID,
                    SKU_SUBSTITUTION.VERSION,
                    SKU_SUBSTITUTION.DISABLED,
                    SKU_SUBSTITUTION.SKU_ID_OUT,
                    SKU_SUBSTITUTION.DC_CODE,
                    SKU_SUBSTITUTION.FROM_DATE,
                    SKU_SUBSTITUTION.TO_DATE,
                    SKU_SUBSTITUTION.AUTHOR_NAME,
                    SKU_SUBSTITUTION.AUTHOR_EMAIL,
                    SKU_SUBSTITUTION.VALUE,
                    SKU_SUBSTITUTION.REFERENCE,
                    SKU_SUBSTITUTION.SOURCE_ID,
                ).values(
                    skuSubstitutionInId,
                    DEFAULT_VERSION,
                    createSubstitution.disabled,
                    createSubstitution.substitutionDetail.skuOut.skuId,
                    createSubstitution.dcCode,
                    createSubstitution.fromDate,
                    createSubstitution.toDate,
                    createSubstitution.authorName,
                    createSubstitution.authorEmail,
                    JSONB.valueOf(substitutionDetailJson),
                    createSubstitution.reference,
                    createSubstitution.sourceId,
                ).executeAsync()
                .await()
            return SubstitutionKey(skuSubstitutionInId, DEFAULT_VERSION)
        }

        suspend fun MetricsDSLContext.retrieveSubstitution(
            substitutionFieldsKey: SubstitutionFieldsKey
        ): Substitution? {
            val skuSubstitutionInId = createID(substitutionFieldsKey)
            return withTagName("fetch-substitution-by-fields")
                .selectFrom(
                    select()
                        .distinctOn(SKU_SUBSTITUTION.ID)
                        .from(SKU_SUBSTITUTION)
                        .where(SKU_SUBSTITUTION.ID.eq(skuSubstitutionInId))
                        .orderBy(SKU_SUBSTITUTION.ID, SKU_SUBSTITUTION.VERSION.desc()),
                )
                .fetchAsync()
                .thenApply {
                    it.firstOrNull()?.into(SkuSubstitutionRecord())?.toSubstitution()
                }.await()
        }

        suspend fun MetricsDSLContext.editSubstitution(editSubstitution: EditSubstitution): SubstitutionKey? =
            withTagName(updateSubstitutionTag)
                .insertInto(SKU_SUBSTITUTION)
                .columns(
                    SKU_SUBSTITUTION.ID,
                    SKU_SUBSTITUTION.VERSION,
                    SKU_SUBSTITUTION.DISABLED,
                    SKU_SUBSTITUTION.SKU_ID_OUT,
                    SKU_SUBSTITUTION.DC_CODE,
                    SKU_SUBSTITUTION.FROM_DATE,
                    SKU_SUBSTITUTION.TO_DATE,
                    SKU_SUBSTITUTION.AUTHOR_NAME,
                    SKU_SUBSTITUTION.AUTHOR_EMAIL,
                    SKU_SUBSTITUTION.VALUE,
                    SKU_SUBSTITUTION.REFERENCE,
                    SKU_SUBSTITUTION.SOURCE_ID,
                ).select(
                    select(
                        SKU_SUBSTITUTION.ID,
                        inline(editSubstitution.version + 1),
                        inline(editSubstitution.disabled),
                        inline(editSubstitution.substitutionDetail.skuOut.skuId),
                        SKU_SUBSTITUTION.DC_CODE,
                        SKU_SUBSTITUTION.FROM_DATE,
                        SKU_SUBSTITUTION.TO_DATE,
                        inline(editSubstitution.authorName),
                        inline(editSubstitution.authorEmail),
                        inline(JSONB.valueOf(objectMapper.writeValueAsString(editSubstitution.substitutionDetail))),
                        SKU_SUBSTITUTION.REFERENCE,
                        inline(editSubstitution.sourceId),
                    ).from(SKU_SUBSTITUTION)
                        .where(
                            SKU_SUBSTITUTION.ID.eq(editSubstitution.substitutionId)
                                .and(SKU_SUBSTITUTION.VERSION.eq(editSubstitution.version)),
                        ),
                ).returning()
                .fetchAsync()
                .thenApply {
                    it.getOrNull(0)?.let { record -> SubstitutionKey(record.id, record.version) }
                }.await()

        private fun SkuSubstitutionRecord.toSubstitution() = Substitution(
            subId = this.id,
            skuIdOut = this.skuIdOut,
            fromDate = this.fromDate,
            toDate = this.toDate,
            dcCode = this.dcCode,
            substitutionDetail = objectMapper.readValue(this.value.data(), SubstitutionDetail::class.java),
            reference = this.reference,
            authorName = this.authorName,
            authorEmail = this.authorEmail,
            lastEdited = this.createdAt,
            version = this.version,
            sourceId = this.sourceId,
            disabled = this.disabled,
        )

        fun CreateSubstitution.toFieldsKey() =
            SubstitutionFieldsKey(
                reference = reference,
                dcCode = dcCode,
                fromDate = fromDate,
                toDate = toDate,
                skuIdOut = substitutionDetail.skuOut.skuId,
            )
    }
}

data class SubstitutionFieldsKey(
    val reference: String,
    val dcCode: String,
    val fromDate: LocalDate,
    val toDate: LocalDate,
    val skuIdOut: UUID,
)

data class SubstitutionKey(val id: UUID, val version: Int)
