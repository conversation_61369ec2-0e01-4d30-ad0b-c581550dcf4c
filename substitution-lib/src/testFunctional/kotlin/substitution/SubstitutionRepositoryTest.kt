package substitution

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.model.substitution.DemandByDate
import com.hellofresh.skuDemandForecast.model.substitution.SkuDemand
import com.hellofresh.skuDemandForecast.model.substitution.SubstitutionDetail
import com.hellofresh.skudemandforecast.lib.substitution.CreateSubstitution
import com.hellofresh.skudemandforecast.lib.substitution.EditSubstitution
import com.hellofresh.skudemandforecast.lib.substitution.repo.SubstitutionRepository
import com.hellofresh.skudemandforecast.lib.substitution.repo.SubstitutionRepository.Companion.toFieldsKey
import com.hellofresh.skudemandforecast.lib.substitution.schema.public_.Tables.SKU_SUBSTITUTION
import com.hellofresh.skudemandforecast.lib.substitution.schema.public_.tables.records.SkuSubstitutionRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class SubstitutionRepositoryTest {
    private val authorEmail = "test-user-email"
    private val authorName = "test-user-name"
    private val testAuthorUpdatedName = "test-author-updated-name"
    private val testAuthorUpdatedEmail = "test-author-updated-email"
    private val datasource = getMigratedDataSource()
    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dsl = DSL.using(dbConfig).withMetrics(SimpleMeterRegistry())

    private val substitutionRepo = SubstitutionRepository(dsl)

    @AfterEach
    fun clear() {
        dsl.deleteFrom(SKU_SUBSTITUTION).execute()
    }

    @Test
    fun `should create new substitutions`() {
        // when
        val substitution = createSubstitution()
        runBlocking {
            substitutionRepo.createSubstitution(substitution)
            val skuSubstitutionRecord = dbConfig.dsl().selectFrom(
                SKU_SUBSTITUTION,
            ).toList().first()

            // then
            assertEquals(authorName, skuSubstitutionRecord.authorName)
            assertEquals(authorEmail, skuSubstitutionRecord.authorEmail)

            assertSubstitutionRecord(skuSubstitutionRecord, substitution)
        }
    }

    @Test
    fun `should create new substitutions with fullday and picks parameters`() {
        // when
        val substitution = createSubstitution(
            skuOutQty = null,
            skuInQty = null,
            skuOutFullDay = true,
            skuInFullDay = true,
            skuOutPicks = 1,
            skuInPicks = 2,
        )
        runBlocking {
            substitutionRepo.createSubstitution(substitution)
            val skuSubstitutionRecord = dbConfig.dsl().selectFrom(
                SKU_SUBSTITUTION,
            ).toList().first()

            // then
            assertEquals(authorName, skuSubstitutionRecord.authorName)
            assertEquals(authorEmail, skuSubstitutionRecord.authorEmail)

            assertSubstitutionRecord(skuSubstitutionRecord, substitution)
        }
    }

    @Test
    fun `should create only one new substitution even when create substitution called multiple times`() {
        // when
        val substitution = createSubstitution()
        runBlocking {
            assertThrows<Exception> {
                substitutionRepo.createSubstitution(substitution)
                substitutionRepo.createSubstitution(substitution)
            }
            val skuSubstitutionRecordCount = dbConfig.dsl().selectFrom(
                SKU_SUBSTITUTION,
            ).count()

            // then
            assertEquals(1, skuSubstitutionRecordCount)
        }
    }

    @Test
    fun `should update an existing substitution creating a new version`() = runBlocking {
        // when
        val substitution = createSubstitution()
        val substitutionKey = substitutionRepo.createSubstitution(substitution)
        val newSubstitution = createSubstitution(skuOutQty = 100, skuInQty = 200, disabled = true)
        substitutionRepo.editSubstitution(
            EditSubstitution(
                substitutionId = substitutionKey.id,
                version = substitutionKey.version,
                substitutionDetail = newSubstitution.substitutionDetail,
                authorName = testAuthorUpdatedName,
                authorEmail = testAuthorUpdatedEmail,
                sourceId = null,
                disabled = newSubstitution.disabled,
            ),
        )
        val skuSubstitutionRecords = dbConfig.dsl().selectFrom(
            SKU_SUBSTITUTION,
        ).toList()

        val skuSubstitutionRecordV0 = skuSubstitutionRecords.first { it.version == 0 }
        val skuSubstitutionRecordV1 = skuSubstitutionRecords.first { it.version == 1 }

        // then
        assertEquals(testAuthorUpdatedName, skuSubstitutionRecordV1.authorName)
        assertEquals(testAuthorUpdatedEmail, skuSubstitutionRecordV1.authorEmail)

        assertFalse(skuSubstitutionRecordV0.disabled)
        assertTrue(skuSubstitutionRecordV1.disabled)

        assertSubstitutionRecord(skuSubstitutionRecordV0, substitution)
        assertSubstitutionRecord(skuSubstitutionRecordV1, newSubstitution)
    }

    @Test
    fun `should be able to create a substitution with same substitution detail after deleting the substitution`() = runBlocking {
        // when
        val substitution = createSubstitution()
        val substitutionKey = substitutionRepo.createSubstitution(substitution)
        val newSubstitution = createSubstitution(skuOutQty = 100, skuInQty = 200, disabled = true)
        substitutionRepo.editSubstitution(
            EditSubstitution(
                substitutionId = substitutionKey.id,
                version = substitutionKey.version,
                substitutionDetail = newSubstitution.substitutionDetail,
                authorName = testAuthorUpdatedName,
                authorEmail = testAuthorUpdatedEmail,
                sourceId = null,
                disabled = newSubstitution.disabled,
            ),
        )
        val createAnotherSubstitution = substitution.copy(reference = "test-reference-new")
        val anotherSubstitution = substitutionRepo.createSubstitution(createAnotherSubstitution) // create again sub
        val skuSubstitutionRecords = dbConfig.dsl().selectFrom(
            SKU_SUBSTITUTION,
        ).toList()

        val skuSubstitutionRecordV0 = skuSubstitutionRecords.first {
            it.version == anotherSubstitution.version && it.id == anotherSubstitution.id
        }

        // then
        assertFalse(skuSubstitutionRecordV0.disabled)
        assertSubstitutionRecord(skuSubstitutionRecordV0, createAnotherSubstitution, "test-reference-new")
    }

    @Test
    fun `update an on existing substitution return empty key`() {
        // when
        val substitution = createSubstitution()
        runBlocking {
            val substitutionKeyPersisted = substitutionRepo.editSubstitution(
                EditSubstitution(
                    substitutionId = UUID.randomUUID(),
                    version = 0,
                    substitutionDetail = substitution.substitutionDetail,
                    authorName = testAuthorUpdatedName,
                    authorEmail = testAuthorUpdatedEmail,
                    sourceId = null,
                    disabled = substitution.disabled,
                ),
            )
            assertEquals(0, dbConfig.dsl().selectFrom(SKU_SUBSTITUTION).toList().size)
            assertNull(substitutionKeyPersisted)
        }
    }

    @Test
    fun `update same substitution version twice should fail`() {
        // when
        val substitution = createSubstitution()
        runBlocking {
            val substitutionKey = substitutionRepo.createSubstitution(substitution)
            substitutionRepo.editSubstitution(
                EditSubstitution(
                    substitutionId = substitutionKey.id,
                    version = substitutionKey.version,
                    substitutionDetail = substitution.substitutionDetail,
                    authorName = testAuthorUpdatedName,
                    authorEmail = testAuthorUpdatedEmail,
                    sourceId = null,
                    disabled = substitution.disabled,
                ),
            )

            assertThrows<Exception> {
                substitutionRepo.editSubstitution(
                    EditSubstitution(
                        substitutionId = substitutionKey.id,
                        version = substitutionKey.version,
                        substitutionDetail = substitution.substitutionDetail,
                        authorName = testAuthorUpdatedName,
                        authorEmail = testAuthorUpdatedEmail,
                        sourceId = null,
                        disabled = substitution.disabled,
                    ),
                )
            }

            assertEquals(2, dbConfig.dsl().selectFrom(SKU_SUBSTITUTION).toList().size)
        }
    }

    @Test
    fun `should get all the existing substitutions with version`() {
        // when
        val substitutionOne = createSubstitution()
        val substitutionTwo = createSubstitution()
        runBlocking {
            val substitutionOneKey = substitutionRepo.createSubstitution(substitutionOne)
            val substitutionTwoKey = substitutionRepo.createSubstitution(substitutionTwo)
            val skuSubstitutionRecords = substitutionRepo.retrieveSubstitutionsWithVersion(
                dcCode = "VE",
                from = LocalDate.now(),
                to = LocalDate.now().plusDays(6),
            )

            val skuSubstitutionRecordV0 = skuSubstitutionRecords.asSequence().flatten()
                .filter { it.subId == substitutionOneKey.id }.first()
            val skuSubstitutionRecordV1 = skuSubstitutionRecords.asSequence().flatten()
                .filter { it.subId == substitutionTwoKey.id }.first()

            // then
            assertEquals(substitutionOneKey.version, skuSubstitutionRecordV0.version)
            assertEquals(substitutionTwoKey.version, skuSubstitutionRecordV1.version)
            assertFalse(skuSubstitutionRecordV1.disabled)
            assertFalse(skuSubstitutionRecordV1.disabled)
        }
    }

    @Test
    fun `should get a list of substitutions with version for the given substitution id`() {
        // when
        val substitution = createSubstitution()
        runBlocking {
            val substitutionKey = substitutionRepo.createSubstitution(substitution)
            val newSubstitution = createSubstitution(skuOutQty = 100, skuInQty = 200)
            substitutionRepo.editSubstitution(
                EditSubstitution(
                    substitutionId = substitutionKey.id,
                    version = substitutionKey.version,
                    substitutionDetail = newSubstitution.substitutionDetail,
                    authorName = testAuthorUpdatedName,
                    authorEmail = testAuthorUpdatedEmail,
                    sourceId = null,
                    disabled = newSubstitution.disabled,
                ),
            )
            val skuSubstitutionRecords = substitutionRepo.retrieveSubstitutionWithVersion(
                substitutionKey.id,
            )

            assertEquals(2, skuSubstitutionRecords.size)

            val skuSubstitutionV0 = skuSubstitutionRecords.first { it.version == 0 }
            val skuSubstitutionV1 = skuSubstitutionRecords.first { it.version == 1 }

            // then
            assertEquals(skuSubstitutionV0.reference, substitution.reference)
            assertEquals(skuSubstitutionV1.reference, newSubstitution.reference)
            assertFalse(skuSubstitutionV0.disabled)
            assertFalse(skuSubstitutionV1.disabled)
        }
    }

    @Test
    fun `should be able to create substitution with multiple skus`() {
        // given
        val skuInId1 = UUID.randomUUID()
        val skuInId2 = UUID.randomUUID()
        val substitution = createSubstitution(
            skuIdsIn = listOf(skuInId1, skuInId2),
        )

        runBlocking {
            // when
            substitutionRepo.createSubstitution(substitution)
            val skuSubstitutionRecord = dbConfig.dsl().selectFrom(
                SKU_SUBSTITUTION,
            ).toList().first()
            val substitutionDetail = objectMapper
                .readValue(
                    skuSubstitutionRecord.value.data(),
                    SubstitutionDetail::class.java,
                )

            // then
            assertEquals(skuInId1, substitutionDetail.skuIn[0].skuId)
            assertEquals(skuInId2, substitutionDetail.skuIn[1].skuId)
        }
    }

    @Test
    fun `should get latest substitution version using ID fields`() {
        // when
        val substitutionOneV1Create = createSubstitution()
        val substitutionTwo = createSubstitution()
        runBlocking {
            val substitutionOneV1 = substitutionRepo.createSubstitution(substitutionOneV1Create)
            val editSubstitutionOneV2 = EditSubstitution(
                substitutionId = substitutionOneV1.id,
                version = substitutionOneV1.version,
                substitutionDetail = substitutionOneV1Create.substitutionDetail,
                authorName = substitutionOneV1Create.authorName,
                authorEmail = substitutionOneV1Create.authorEmail,
                sourceId = UUID.randomUUID(),
                disabled = substitutionOneV1Create.disabled,
            )
            val substitutionOneV2 = substitutionRepo.editSubstitution(editSubstitutionOneV2)!!

            substitutionRepo.createSubstitution(substitutionTwo)

            val skuSubstitutionResult = substitutionRepo.retrieveSubstitution(substitutionOneV1Create.toFieldsKey())!!

            assertEquals(substitutionOneV2.id, skuSubstitutionResult.subId)
            assertEquals(substitutionOneV1Create.substitutionDetail.skuOut.skuId, skuSubstitutionResult.skuIdOut)
            assertEquals(substitutionOneV1Create.fromDate, skuSubstitutionResult.fromDate)
            assertEquals(substitutionOneV1Create.toDate, skuSubstitutionResult.toDate)
            assertEquals(substitutionOneV1Create.dcCode, skuSubstitutionResult.dcCode)
            assertEquals(editSubstitutionOneV2.substitutionDetail, skuSubstitutionResult.substitutionDetail)
            assertEquals(substitutionOneV1Create.reference, skuSubstitutionResult.reference)
            assertEquals(editSubstitutionOneV2.authorName, skuSubstitutionResult.authorName)
            assertEquals(editSubstitutionOneV2.authorEmail, skuSubstitutionResult.authorEmail)
            assertEquals(substitutionOneV2.version, skuSubstitutionResult.version)
            assertEquals(editSubstitutionOneV2.sourceId, skuSubstitutionResult.sourceId)
            assertEquals(editSubstitutionOneV2.disabled, skuSubstitutionResult.disabled)
        }
    }

    private fun assertSubstitutionRecord(
        skuSubstitutionRecord: SkuSubstitutionRecord,
        substitution: CreateSubstitution,
        testReference: String = "test-reference",
    ) {
        assertEquals("VE", skuSubstitutionRecord.dcCode)
        assertEquals(LocalDate.now(), skuSubstitutionRecord.fromDate)
        assertEquals(LocalDate.now().plusDays(6), skuSubstitutionRecord.toDate)
        assertEquals(testReference, skuSubstitutionRecord.reference)
        assertEquals(substitution.substitutionDetail.skuOut.skuId, skuSubstitutionRecord.skuIdOut)
        assertEquals(
            substitution.substitutionDetail,
            objectMapper.readValue(
                skuSubstitutionRecord.value.toString(),
                SubstitutionDetail::class.java,
            ),
        )
    }

    @Suppress("LongParameterList")
    fun createSubstitution(
        skuIdOut: UUID = UUID.randomUUID(),
        skuOutQty: Int? = 10,
        skuInQty: Int? = 50,
        version: Int = 0,
        skuIdsIn: List<UUID> = MutableList(4) { UUID.randomUUID() },
        disabled: Boolean = false,
        skuOutFullDay: Boolean = false,
        skuInFullDay: Boolean = false,
        skuOutPicks: Int? = null,
        skuInPicks: Int? = null,
    ) =
        CreateSubstitution(
            fromDate = LocalDate.now(),
            toDate = LocalDate.now().plusDays(6),
            dcCode = "VE",
            substitutionDetail = SubstitutionDetail(
                skuOut = SkuDemand(
                    skuId = skuIdOut,
                    demand = listOf(
                        DemandByDate(
                            date = LocalDate.now(),
                            qty = skuOutQty,
                            fullDay = skuOutFullDay,
                        ),
                    ),
                    picks = skuOutPicks,
                ),
                skuIn = skuIdsIn.map { id ->
                    SkuDemand(
                        id,
                        listOf(
                            DemandByDate(
                                LocalDate.now(),
                                skuInQty,
                                skuInFullDay,
                            ),
                        ),
                        skuInPicks,
                    )
                },
            ),
            reference = "test-reference",
            authorName = authorName,
            authorEmail = authorEmail,
            sourceId = null,
            version = version,
            disabled = disabled,
        )

    companion object {
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
