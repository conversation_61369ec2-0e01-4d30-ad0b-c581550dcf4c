services:
  database:
    image: public.ecr.aws/bitnami/postgresql:15.3.0
    environment:
      - POSTGRES_USER=inventory_management_us
      - POSTGRES_PASSWORD=inventory_management_us
      - POSTGRES_DB=inventory_management_us
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/database:/var/lib/postgresql/data
    ports:
      - 5432:5432
    shm_size: 256m

  cache:
    image: public.ecr.aws/bitnami/redis:5.0.10
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - 6379:6379

  hj:
    image: mcr.microsoft.com/azure-sql-edge
    ports:
      - 1433:1433
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=My!StrongPassword

  consumer:
    depends_on:
      - database
      - cache
    build: back
    command: "python consumer.py"
    environment:
      APP_TYPE: CONSUMER
      CONFIG: >
        {
          "database": { "host": "procurement-db", "port": "5432" },
          "cache": { "host": "cache", "port": 6379 },
        }

  prometheus:
    image: prom/prometheus
    ports:
      - 9090:9090
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:7.3.4
    ports:
      - 3000:3000

  zookeeper:
    image: confluentinc/cp-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - ./volumes/zookeeper-data:/var/lib/zookeeper/data
      - ./volumes/zookeeper-log:/var/lib/zookeeper/log
    ports:
      - 22181:2181

  kafka:
    image: confluentinc/cp-kafka
    depends_on:
      - zookeeper
    ports:
      - 29092:29092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - ./volumes/kafka:/var/lib/kafka/data
