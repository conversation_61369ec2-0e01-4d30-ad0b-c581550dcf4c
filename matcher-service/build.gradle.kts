@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    hellofresh.`test-integration`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}
group = "$group.${project.name}".replace("-", "")
description = "a service to match demand in SDF with processed demand in CIF"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(project(":lib:models"))
    implementation(project(":lib"))
    implementation(project(":lib:db"))
    implementation(project(":lib:job"))
    implementation(project(":distribution-center-lib"))
    implementation(libs.hellofresh.service)
    implementation(libs.coroutines.jdk8)
    implementation(libs.jackson.kotlin)
    implementation(libs.jackson.jsr310)
    implementation(libs.retrofit.jackson)
    implementation(libs.retrofit.core)
    implementation(libs.resilience4j.retrofit)
    implementation(libs.resilience4j.retry)
    implementation(libs.ktor.server.auth.jwt)

    testImplementation(libs.wiremock)
    testImplementation(libs.mockk)

    testIntegrationImplementation(testFixtures(project(":lib")))
    testIntegrationImplementation(testFixtures(project(":files-consumer")))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "aggregated_demand|file_uploads|imports|imports_view|file_source|file_type|file_status"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
