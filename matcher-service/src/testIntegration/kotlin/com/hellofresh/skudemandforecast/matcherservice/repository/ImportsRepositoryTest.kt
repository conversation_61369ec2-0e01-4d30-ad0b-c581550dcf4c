package com.hellofresh.skudemandforecast.matcherservice.repository

import com.hellofresh.skudemandforecast.matcherservice.FunctionalTest
import com.hellofresh.skudemandforecast.matcherservice.schema.Tables.IMPORTS
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.PROCESSED as DbProcessed
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileType
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ImportsRepositoryTest : FunctionalTest() {
    @Test
    fun `updates imports in created status to processed`() {
        val importsRecord = importsRecord()
            .apply {
                this.type = FileType.RECIPE
                this.status = FileStatus.CREATED
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        val importRecords2 = importsRecord()
            .apply {
                this.type = FileType.DEMAND
                this.status = FileStatus.CREATED_WITH_ERROR
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        runBlocking {
            dsl.batchInsert(importsRecord, importRecords2).execute()

            importsRepositoryImpl.markFileAsProcessed(dsl, setOf(importsRecord.id, importRecords2.id))

            val files = dsl.selectFrom(
                IMPORTS,
            ).fetch()

            assertEquals(2, files.size)

            with(files.first { it.id == importsRecord.id }) {
                assertEquals(FileType.RECIPE, type)
                assertEquals(DbProcessed, status)
            }

            with(files.first { it.id == importRecords2.id }) {
                assertEquals(FileType.DEMAND, type)
                assertEquals(FileStatus.PROCESSED_WITH_ERROR, status)
            }
        }
    }
}
