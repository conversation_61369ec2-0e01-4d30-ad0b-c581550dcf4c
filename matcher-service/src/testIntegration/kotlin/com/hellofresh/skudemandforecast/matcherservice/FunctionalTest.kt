@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skudemandforecast.matcherservice

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.matcherservice.repository.AggregatedDemandRepository
import com.hellofresh.skudemandforecast.matcherservice.repository.FileUploadsRepository
import com.hellofresh.skudemandforecast.matcherservice.repository.FileUploadsRepositoryImpl
import com.hellofresh.skudemandforecast.matcherservice.repository.ImportsRepositoryImpl
import com.hellofresh.skudemandforecast.matcherservice.repository.RecipeBreakdownLine
import com.hellofresh.skudemandforecast.matcherservice.schema.Tables
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileSource.INVENTORY
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileSource.MPS
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileType.DEMAND
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileType.RECIPE
import com.hellofresh.skudemandforecast.matcherservice.schema.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.matcherservice.schema.tables.records.ImportsRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    val jsonObjectMapper = jacksonObjectMapper().findAndRegisterModules()

    val recipeBreakdowns = listOf(
        RecipeBreakdownLine.default(1, 10),
        RecipeBreakdownLine.default(2, 20),
        RecipeBreakdownLine.default(3, 20),
        RecipeBreakdownLine.default(4, 50),
    ).groupBy { it.recipeIndex }.mapValues { it.value.sumOf { it.qty } }
    fun fileUploadsRecord() = FileUploadsRecord().apply {
        id = UUID.randomUUID()
        dcs = setOf(UUID.randomUUID().toString()).toTypedArray()
        weeks = setOf(UUID.randomUUID().toString()).toTypedArray()
        fileName = UUID.randomUUID().toString()
        content = UUID.randomUUID().toString().toByteArray()
        errors = listOf(UUID.randomUUID().toString()).toTypedArray()
        uploadedFileSource = INVENTORY
        uploadedFileType = DEMAND
        uploadedFileStatus = CREATED
        authorName = "author"
        authorEmail = "email"
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = OffsetDateTime.now(UTC)
        market = "market"
    }

    fun importsRecord() = ImportsRecord().apply {
        id = UUID.randomUUID()
        dcs = setOf(UUID.randomUUID().toString()).toTypedArray()
        weeks = setOf(UUID.randomUUID().toString()).toTypedArray()
        name = UUID.randomUUID().toString()
        errors = listOf(UUID.randomUUID().toString()).toTypedArray()
        source = MPS
        type = RECIPE
        status = CREATED
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = OffsetDateTime.now(UTC)
        market = "market"
    }

    fun RecipeBreakdownLine.Companion.default(recipeIndex: Int = 100, qty: Long = 1000) =
        RecipeBreakdownLine(recipeIndex, "country", "locale", "prodFamily", "brand", 2, 1, qty)

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.IMPORTS).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dsl.deleteFrom(Tables.AGGREGATED_DEMAND).execute()
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var aggregatedDemandRepository: AggregatedDemandRepository
        lateinit var meterRegistry: SimpleMeterRegistry
        private val dataSource = getMigratedDataSource()
        lateinit var dcConfigService: DcConfigService
        lateinit var fileUploadsRepository: FileUploadsRepository
        lateinit var importsRepositoryImpl: FileUploadsRepository

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            meterRegistry = SimpleMeterRegistry()
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())

            aggregatedDemandRepository = AggregatedDemandRepository(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
            fileUploadsRepository = FileUploadsRepositoryImpl()
            importsRepositoryImpl = ImportsRepositoryImpl()
        }
    }
}
