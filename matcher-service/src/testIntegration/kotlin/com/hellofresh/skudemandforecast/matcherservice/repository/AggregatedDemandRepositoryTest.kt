package com.hellofresh.skudemandforecast.matcherservice.repository

import com.hellofresh.skudemandforecast.matcherservice.FunctionalTest
import com.hellofresh.skudemandforecast.matcherservice.repository.AggregatedDemandRepository.Companion.toSourceType
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.CREATED_WITH_ERROR
import com.hellofresh.skudemandforecast.matcherservice.schema.tables.records.AggregatedDemandRecord
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class AggregatedDemandRepositoryTest : FunctionalTest() {
    @Test
    fun `fetch aggregated demand from pending sources`() {
        val fileUploadsRecord = fileUploadsRecord().apply {
            uploadedFileStatus = CREATED
        }

        val importRecord = importsRecord().apply {
            status = CREATED_WITH_ERROR
        }

        val aggregatedDemandRecord = AggregatedDemandRecord().apply {
            skuParentId = null
            skuId = UUID.randomUUID()
            skuCode = UUID.randomUUID().toString()
            dcCode = "DC"
            productionDate = importRecord.createdAt.toLocalDate()
            recipeBreakdown = JSONB.valueOf(jsonObjectMapper.writeValueAsString(recipeBreakdowns))
            originalDemand = 100
            sourceIds = arrayOf(fileUploadsRecord.id, importRecord.id)
        }

        dsl.batchInsert(fileUploadsRecord, importRecord, aggregatedDemandRecord).execute()

        val aggregatedDemands = runBlocking { aggregatedDemandRepository.getPendingDemand() }

        assertEquals(1, aggregatedDemands.size)
        val aggregatedDemand = aggregatedDemands.first()

        assertEquals(aggregatedDemandRecord.dcCode, aggregatedDemand.dcCode)
        assertEquals(aggregatedDemandRecord.skuId, aggregatedDemand.skuId)
        assertEquals(aggregatedDemandRecord.productionDate, aggregatedDemand.date)

        assertEquals(2, aggregatedDemand.sources.size)
        with(aggregatedDemand.sources.first { it.id == fileUploadsRecord.id }) {
            assertEquals(fileUploadsRecord.uploadedFileSource.toSourceType(), type)
            assertEquals(fileUploadsRecord.updatedAt.toLocalDate(), updatedAt.toLocalDate())
        }

        with(aggregatedDemand.sources.first { it.id == importRecord.id }) {
            assertEquals(importRecord.source.toSourceType(), type)
            assertEquals(importRecord.updatedAt.toLocalDate(), updatedAt.toLocalDate())
        }
    }

    @Test
    fun `aggregated demand from non pending sources is filtered`() {
        val fileUploadsRecords = FileStatus.entries.filter { !pendingStatus.contains(it) }
            .map {
                fileUploadsRecord().apply {
                    uploadedFileStatus = it
                }
            }

        val importRecord = importsRecord().apply {
            status = CREATED_WITH_ERROR
        }

        val aggregatedDemandRecord = AggregatedDemandRecord().apply {
            skuParentId = null
            skuId = UUID.randomUUID()
            skuCode = UUID.randomUUID().toString()
            dcCode = "DC"
            productionDate = importRecord.createdAt.toLocalDate()
            recipeBreakdown = JSONB.valueOf(jsonObjectMapper.writeValueAsString(recipeBreakdowns))
            originalDemand = 100
            sourceIds = fileUploadsRecords.map { it.id }.toSet().plus(importRecord.id).toTypedArray()
        }

        dsl.batchInsert(fileUploadsRecords).execute()
        dsl.batchInsert(importRecord, aggregatedDemandRecord).execute()

        val aggregatedDemands = runBlocking { aggregatedDemandRepository.getPendingDemand() }

        assertEquals(1, aggregatedDemands.size)
        val aggregatedDemand = aggregatedDemands.first()

        assertEquals(aggregatedDemandRecord.dcCode, aggregatedDemand.dcCode)
        assertEquals(aggregatedDemandRecord.skuId, aggregatedDemand.skuId)
        assertEquals(aggregatedDemandRecord.productionDate, aggregatedDemand.date)

        assertEquals(1, aggregatedDemand.sources.size)
        with(aggregatedDemand.sources.first()) {
            assertEquals(importRecord.id, id)
            assertEquals(importRecord.source.toSourceType(), type)
            assertEquals(importRecord.updatedAt.toLocalDate(), updatedAt.toLocalDate())
        }
    }

    @Test
    fun `aggregated demand from old sources is filtered`() {
        val pastMinutes = 10L
        val fileUploadsRecord = fileUploadsRecord().apply {
            uploadedFileStatus = CREATED
            createdAt = OffsetDateTime.now(ZoneOffset.UTC).minusMinutes(pastMinutes + 1)
            updatedAt = OffsetDateTime.now(ZoneOffset.UTC).minusHours(pastMinutes + 1)
        }
        val importRecord = importsRecord().apply {
            status = CREATED_WITH_ERROR
        }

        val aggregatedDemandRecord = AggregatedDemandRecord().apply {
            skuParentId = null
            skuId = UUID.randomUUID()
            skuCode = UUID.randomUUID().toString()
            dcCode = "DC"
            productionDate = importRecord.createdAt.toLocalDate()
            recipeBreakdown = JSONB.valueOf(jsonObjectMapper.writeValueAsString(recipeBreakdowns))
            originalDemand = 100
            sourceIds = arrayOf(fileUploadsRecord.id, importRecord.id)
        }

        dsl.batchInsert(fileUploadsRecord, importRecord, aggregatedDemandRecord).execute()

        val aggregatedDemands = runBlocking { aggregatedDemandRepository.getPendingDemand(pastMinutes.toInt()) }

        assertEquals(1, aggregatedDemands.size)
        val aggregatedDemand = aggregatedDemands.first()

        assertEquals(aggregatedDemandRecord.dcCode, aggregatedDemand.dcCode)
        assertEquals(aggregatedDemandRecord.skuId, aggregatedDemand.skuId)
        assertEquals(aggregatedDemandRecord.productionDate, aggregatedDemand.date)

        assertEquals(1, aggregatedDemand.sources.size)
        with(aggregatedDemand.sources.first()) {
            assertEquals(importRecord.id, id)
            assertEquals(importRecord.source.toSourceType(), type)
            assertEquals(importRecord.updatedAt.toLocalDate(), updatedAt.toLocalDate())
        }
    }

    @Test
    fun `just aggregated demand from last week is returned`() {
        val importRecord = importsRecord().apply {
            status = CREATED_WITH_ERROR
        }

        val aggregatedDemandRecord = AggregatedDemandRecord().apply {
            skuParentId = null
            skuId = UUID.randomUUID()
            skuCode = UUID.randomUUID().toString()
            dcCode = "DC"
            productionDate = LocalDate.now(ZoneOffset.UTC).minusDays(DEMAND_DAYS_BEFORE + 1L)
            recipeBreakdown = JSONB.valueOf(jsonObjectMapper.writeValueAsString(recipeBreakdowns))
            originalDemand = 100
            sourceIds = arrayOf(importRecord.id)
        }

        dsl.batchInsert(importRecord, aggregatedDemandRecord).execute()

        val aggregatedDemands = runBlocking { aggregatedDemandRepository.getPendingDemand() }

        assertEquals(0, aggregatedDemands.size)
    }
}
