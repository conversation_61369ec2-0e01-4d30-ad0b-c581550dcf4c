package com.hellofresh.skudemandforecast.matcherservice.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.unnest
import com.hellofresh.skudemandforecast.matcherservice.repository.SourceType.FILE_UPLOADS
import com.hellofresh.skudemandforecast.matcherservice.repository.SourceType.IMPORTS
import com.hellofresh.skudemandforecast.matcherservice.schema.Tables.AGGREGATED_DEMAND
import com.hellofresh.skudemandforecast.matcherservice.schema.Tables.IMPORTS_VIEW
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileSource
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.matcherservice.schema.enums.FileStatus.CREATED_WITH_ERROR
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DatePart
import org.jooq.JSONB
import org.jooq.impl.DSL
import org.jooq.impl.SQLDataType

val pendingStatus = setOf(CREATED, CREATED_WITH_ERROR)

const val DEMAND_DAYS_BEFORE = 7
private val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

class AggregatedDemandRepository(private val readDslContext: MetricsDSLContext) {

    private val sourceId = DSL.field("sourceId", IMPORTS_VIEW.ID.type)
    private val unnestAggregatedDemand =
        DSL.select(
            AGGREGATED_DEMAND.DC_CODE,
            AGGREGATED_DEMAND.SKU_ID,
            AGGREGATED_DEMAND.PRODUCTION_DATE,
            AGGREGATED_DEMAND.RECIPE_BREAKDOWN,
            unnest(AGGREGATED_DEMAND.SOURCE_IDS).`as`(sourceId),
        ).from(AGGREGATED_DEMAND)
            .where(
                AGGREGATED_DEMAND.PRODUCTION_DATE.ge(
                    DSL.localDateSub(
                        DSL.currentLocalDate(),
                        DEMAND_DAYS_BEFORE,
                        DatePart.DAY,
                    ),
                ),
            ).asTable("unnest_agg_demand")

    suspend fun getPendingDemand(minutesInPast: Int = 10): List<AggregatedDemand> =
        readDslContext.withTagName("fetch-aggregated-pending-demand")
            .select(
                unnestAggregatedDemand.field(AGGREGATED_DEMAND.DC_CODE),
                unnestAggregatedDemand.field(AGGREGATED_DEMAND.SKU_ID),
                unnestAggregatedDemand.field(AGGREGATED_DEMAND.PRODUCTION_DATE),
                unnestAggregatedDemand.field(AGGREGATED_DEMAND.RECIPE_BREAKDOWN),
                IMPORTS_VIEW.ID,
                IMPORTS_VIEW.STATUS,
                IMPORTS_VIEW.SOURCE,
                IMPORTS_VIEW.UPDATED_AT,
            ).from(unnestAggregatedDemand)
            .join(IMPORTS_VIEW).on(IMPORTS_VIEW.ID.eq(unnestAggregatedDemand.field(sourceId)))
            .where(IMPORTS_VIEW.STATUS.`in`(pendingStatus))
            .and(
                IMPORTS_VIEW.UPDATED_AT.gt(
                    DSL.localDateTimeSub(
                        DSL.currentLocalDateTime(),
                        minutesInPast,
                        DatePart.MINUTE,
                    ).cast(SQLDataType.OFFSETDATETIME),
                ),
            )
            .fetchAsync()
            .thenApply { result ->
                result.groupBy {
                    AggregatedDemandKey(
                        dcCode = it.get(AGGREGATED_DEMAND.DC_CODE),
                        skuId = it.get(AGGREGATED_DEMAND.SKU_ID),
                        date = it.get(AGGREGATED_DEMAND.PRODUCTION_DATE),
                    )
                }.map { (key, records) ->
                    AggregatedDemand(
                        dcCode = key.dcCode,
                        skuId = key.skuId,
                        date = key.date,
                        demandQty = records.first().get(
                            AGGREGATED_DEMAND.RECIPE_BREAKDOWN,
                        ).toRecipeMap().values.sum().toLong(),
                        sources = records.map {
                            Source(
                                it.get(IMPORTS_VIEW.ID),
                                it.get(IMPORTS_VIEW.SOURCE).toSourceType(),
                                it.get(IMPORTS_VIEW.UPDATED_AT),
                            )
                        },
                    )
                }
            }.await()

    private fun JSONB.toRecipeMap() = objectMapper.readValue<Map<String, Int>>(data())

    companion object {
        fun FileSource.toSourceType() =
            when (this) {
                FileSource.ORDERING, FileSource.INVENTORY -> FILE_UPLOADS
                FileSource.MPS, FileSource.SCO, FileSource.LOCAL_D4 -> IMPORTS
            }
    }
}

data class AggregatedDemandKey(
    val dcCode: String,
    val skuId: UUID,
    val date: LocalDate,
)

data class AggregatedDemand(
    val dcCode: String,
    val skuId: UUID,
    val date: LocalDate,
    val demandQty: Long,
    val sources: List<Source>,
)

data class Source(
    val id: UUID,
    val type: SourceType,
    val updatedAt: OffsetDateTime,
)

enum class SourceType {
    FILE_UPLOADS,
    IMPORTS
}
