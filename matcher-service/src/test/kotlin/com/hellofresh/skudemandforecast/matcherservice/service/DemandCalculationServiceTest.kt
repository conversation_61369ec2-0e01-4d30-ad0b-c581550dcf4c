package com.hellofresh.skudemandforecast.matcherservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.github.tomakehurst.wiremock.client.MappingBuilder
import com.github.tomakehurst.wiremock.client.WireMock.aResponse
import com.github.tomakehurst.wiremock.client.WireMock.equalTo
import com.github.tomakehurst.wiremock.client.WireMock.get
import com.github.tomakehurst.wiremock.client.WireMock.reset
import com.github.tomakehurst.wiremock.client.WireMock.stubFor
import com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo
import com.github.tomakehurst.wiremock.junit5.WireMockTest
import com.github.tomakehurst.wiremock.matching.MatchResult
import com.github.tomakehurst.wiremock.stubbing.Scenario
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertNull
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import wiremock.org.hamcrest.Matchers

@WireMockTest
class DemandCalculationServiceTest {

    private lateinit var demandCalculationService: DemandCalculationService

    private val emailAddress = "<EMAIL>"
    private val jwtSecret = "jwtTestSecret"
    private val applicationName = "matcher-test"
    private val market = "MARKET"
    private val weeks = setOf("W1,W2")

    @BeforeEach
    fun setUp(wmRuntimeInfo: WireMockRuntimeInfo) {
        demandCalculationService =
            DemandCalculationService(
                applicationName, emailAddress, jwtSecret, "http://localhost:${wmRuntimeInfo.httpPort}",
                SimpleMeterRegistry(),
            )
    }

    @AfterEach
    fun afterEach() {
        reset()
    }

    @Test
    fun `should get demands for the given market`() {
        val demand1 = Demand(
            dcCode = "D1",
            date = LocalDate.of(2024, 9, 25),
            skuId = UUID.randomUUID(),
            quantity = 100,
        )
        val demand2 = Demand(
            dcCode = "D1",
            date = LocalDate.of(2024, 8, 12),
            skuId = UUID.randomUUID(),
            quantity = 500,
        )
        val expectedDemands = listOf(demand1, demand2)
        val expectedDemandCalculationResponse =
            DemandCalculationResponse(OffsetDateTime.now(ZoneOffset.UTC), expectedDemands)
        val token = demandCalculationService.createJwtToken()

        stubGetDemandCalculationClient(market, weeks, expectedDemandCalculationResponse, token, 200)

        val demands = demandCalculationService.getCalculationDemands(market, weeks, token)!!

        assertEquals(expectedDemands.toSet(), demands.toSet())
    }

    @Test
    fun `should return null when client receives error`() {
        val token = demandCalculationService.createJwtToken()
        val weeks = setOf("W1", "W2")
        stubGetDemandCalculationClient(
            market,
            weeks,
            DemandCalculationResponse(OffsetDateTime.now(), emptyList()),
            token,
            500,
        )

        val demands = demandCalculationService.getCalculationDemands(market, weeks, token)

        assertNull(demands)
    }

    @Test
    fun `should at least retry once when getting demand`() {
        val demands = listOf(
            Demand(
                dcCode = "D1",
                date = LocalDate.of(2024, 9, 25),
                skuId = UUID.randomUUID(),
                quantity = 100,
            ),
        )
        val market = "MARKETX"
        val token = demandCalculationService.createJwtToken()
        val scenario = "retry"
        stubGetDemandCalculationClient(
            market,
            weeks,
            DemandCalculationResponse(OffsetDateTime.now(), emptyList()),
            token,
            500,
        ) {
            inScenario(scenario)
                .whenScenarioStateIs(Scenario.STARTED).willSetStateTo("VALID")
        }

        val expectedDemandCalculationResponse = DemandCalculationResponse(
            OffsetDateTime.now(ZoneOffset.UTC),
            demands,
        )
        stubGetDemandCalculationClient(
            market,
            weeks,
            expectedDemandCalculationResponse,
            token,
            200,
        ) {
            inScenario(scenario).whenScenarioStateIs("VALID")
        }

        val demandResult = demandCalculationService.getCalculationDemands(market, weeks, token)!!

        assertEquals(expectedDemandCalculationResponse.demands.toSet(), demandResult.toSet())
    }

    @SuppressWarnings("LongParameterList")
    private fun stubGetDemandCalculationClient(
        market: String,
        weeks: Set<String>,
        demandCalculationResponse: DemandCalculationResponse,
        token: String,
        status: Int = 200,
        timestamp: OffsetDateTime? = null,
        mappingBuilderBlock: MappingBuilder.() -> MappingBuilder = { this }
    ) {
        stubFor(
            get(urlPathEqualTo(demandPath(market)))
                .let(mappingBuilderBlock)
                .withHeader("Authorization", equalTo(token))
                .let { builder ->
                    timestamp
                        ?.let {
                            builder.withQueryParam(
                                "timestamp",
                                equalTo(timestamp.serialize()),
                            )
                        } ?: builder
                }
                .andMatching { request ->
                    MatchResult.of(
                        Matchers.equalTo(request.queryParameter("weeks").values().toSet()).matches(weeks),
                    )
                }
                .willReturn(
                    aResponse()
                        .withStatus(status)
                        .withBody(objectMapper.writeValueAsString(demandCalculationResponse)),
                ),
        )
    }

    private fun OffsetDateTime?.serialize() = this?.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)

    private fun demandPath(market: String) = DEMAND_CALCULATION_PATH.replace("{market}", market)

    companion object {
        private val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
