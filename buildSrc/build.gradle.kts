val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

plugins {
    // Support convention plugins written in Kotlin. Convention plugins are build scripts in 'src/main'
    // that automatically become available as plugins in the main build.
    `kotlin-dsl`
}

repositories {
    // Use the plugin portal to apply community plugins in convention plugins.
    gradlePluginPortal()
    mavenCentral()
}

dependencies {
    // buildSrc in combination with this plugin ensures that the version set here
    // will be set to the same for all other Kotlin dependencies / plugins in the project.
    add("implementation", libs.findLibrary("kotlin-gradle").get())
    // https://kotlinlang.org/docs/all-open-plugin.html
    // contains also https://kotlinlang.org/docs/all-open-plugin.html#spring-support
    // The all-open compiler plugin adapts Kotlin to the requirements of those frameworks and makes classes annotated
    // with a specific annotation and their members open without the explicit open keyword.
    add("implementation", libs.findLibrary("kotlin-allopen").get())
    // https://kotlinlang.org/docs/no-arg-plugin.html
    // contains also https://kotlinlang.org/docs/no-arg-plugin.html#jpa-support
    // The no-arg compiler plugin generates an additional zero-argument constructor for classes
    // with a specific annotation.
    add("implementation", libs.findLibrary("kotlin-noarg").get())
    // HelmChartGen dependencies
    add("implementation", libs.findLibrary("jackson-kotlin").get())
    add("implementation", libs.findLibrary("jackson-yaml").get())
    // Docker Jib dependency
    add("implementation", libs.findLibrary("jib-gradle").get())
    // Bundles
    add("compileOnly", libs.findBundle("main-compileOnly").get())
    add("testImplementation", libs.findBundle("test-implementation").get())
    add("testRuntimeOnly", libs.findBundle("test-runtime").get())
}

gradlePlugin {
    plugins.register("helm") {
        id = "hellofresh.helm"
        implementationClass = "com.hellofresh.gradle.helm.HelmPlugin"
    }

    plugins.register("testFixtures") {
        id = "hellofresh.test-fixtures"
        implementationClass = "com.hellofresh.gradle.TestFixturesPlugin"
    }
}

kotlinDslPluginOptions {
    jvmTarget.set(libs.findVersion("java").get().requiredVersion)
    jvmTarget.finalizeValueOnRead()
}
