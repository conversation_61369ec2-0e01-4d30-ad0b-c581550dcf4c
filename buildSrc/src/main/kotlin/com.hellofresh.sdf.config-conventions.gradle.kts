@file:Suppress("detekt:all")
import com.hellofresh.gradle.hasOrPut
import com.hellofresh.gradle.hasThen
import com.hellofresh.gradle.loadRealRootProperties
import com.hellofresh.gradle.putCaseFormats
import com.hellofresh.gradle.putCaseLowerUpper
import com.hellofresh.gradle.realRootDir

realRootDir(".git")?.let { gitDir ->
    val headFile = gitDir.resolve("HEAD")
    if (!headFile.exists()) {
        throw GradleException(
            "Could not find '.git/HEAD', make sure the project is within a valid git repository."
        )
    }

    val head = headFile.readText().trim()
    val id = if (!head.startsWith("ref: ")) {
        head
    } else {
        val refPath = head.substringAfter("ref: ")
        val refFile = gitDir.resolve(refPath)
        if (!refFile.exists()) throw GradleException("Could not find '.git/$refPath', is this an empty git repository?")
        refFile.readText().trim()
    }

    extra["commitId"] = id
    extra["shortCommitId"] = id.take(12)
} ?: logger.warn("Could not find '.git' directory anywhere")

loadRealRootProperties("project")?.also { projectProperties ->
    projectProperties.forEach { extra[it.key.toString()] = it.value }

    val projectKey: String by extra
    extra.putCaseLowerUpper("projectKey", projectKey)

    val projectName: String by extra
    extra.putCaseFormats("projectName", projectName)

    extra["applicationId"] = "${if (name.startsWith(projectKey)) "" else "$projectKey-"}$name"

    extra["applicationName"] = name
    extra.putCaseFormats("applicationName", name)

    extra.hasOrPut("slackAlertChannel") { "proj-$projectName-alerts" }
    extra.hasOrPut("slackReleaseChannel") { "proj-$projectName-releases" }

    extra.hasThen("projectDisplayName") { k, v -> extra[k] = v.toString().trim('"') }
    extra.hasThen("tiers") { k, v -> extra[k] = v.toString().split(',') }

    extra["dockerTag"] = "$projectName-${extra["shortCommitId"]}"
    extra["dockerRepository"] = "${extra["dockerRegistry"]}/$projectName"
    extra["dockerImage"] = "${extra["dockerRepository"]}:${extra["dockerTag"]}"
} ?: logger.warn("Could not find 'project.properties' file anywhere")
