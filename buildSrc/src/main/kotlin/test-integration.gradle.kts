package hellofresh

import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.register

plugins {
    java
}

val testIntegration by sourceSets.registering {
    compileClasspath += sourceSets.main.get().output.plus(configurations.testRuntimeClasspath.get())
    runtimeClasspath += output + compileClasspath
}

listOf("Implementation", "RuntimeOnly", "CompileOnly").forEach { configSuffix ->
    configurations.named("${testIntegration.name}$configSuffix") {
        extendsFrom(configurations.getByName("test$configSuffix"))
    }
}

val integrationTestTask = tasks.register<Test>(testIntegration.name) {
    group = "verification"
    description = "Runs integration tests."
    shouldRunAfter(tasks.test)
    tasks["check"].dependsOn(this)
    testClassesDirs = testIntegration.get().output.classesDirs
    classpath = testIntegration.get().runtimeClasspath
    useJUnitPlatform()
}

tasks.check { dependsOn(integrationTestTask) }
