package com.hellofresh.gradle

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.internal.project.ProjectInternal
import org.gradle.api.plugins.JavaTestFixturesPlugin
import org.gradle.internal.component.external.model.TestFixturesSupport.TEST_FIXTURES_FEATURE_NAME
import org.gradle.kotlin.dsl.apply
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.kotlin

class TestFixturesPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        val project = target as ProjectInternal
        with(project) {
            pluginManager.apply(JavaTestFixturesPlugin::class)
            dependencies {
                add("${TEST_FIXTURES_FEATURE_NAME}CompileOnly", "org.jetbrains:annotations:24.0.1")
                add("${TEST_FIXTURES_FEATURE_NAME}Implementation", platform(kotlin("bom", "1.6.20")))
            }
        }
    }
}
