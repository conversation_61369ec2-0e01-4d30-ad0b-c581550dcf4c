package com.hellofresh.gradle

import org.apache.tools.ant.filters.ReplaceTokens
import org.gradle.api.file.CopySpec
import org.gradle.kotlin.dsl.filter

@Suppress("UNCHECKED_CAST")
fun <T : CopySpec> T.replaceTokens(properties: Map<String, Any?>, extensions: Set<String>? = null): T =
    filesMatching(
        (extensions ?: setOf("html", "json", "properties", "toml", "xml", "yaml", "yml")).map { "**/*.$it" }
    ) {
        filter(ReplaceTokens::class, mapOf("tokens" to properties.mapValues { it.value.toString() }))
    } as T
