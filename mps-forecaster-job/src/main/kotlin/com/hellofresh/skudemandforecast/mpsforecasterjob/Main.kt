package com.hellofresh.skudemandforecast.mpsforecasterjob

import com.hellofresh.sdf.shutdown.shutdownNeeded
import com.hellofresh.service.toDuration
import com.hellofresh.skuDemandForecast.db.DBConfiguration
import com.hellofresh.skuDemandForecast.db.DatabaseConfig
import com.hellofresh.skuDemandForecast.lib.Application
import com.hellofresh.skuDemandForecast.lib.StatusServer
import com.hellofresh.skuDemandForecast.lib.runApplication
import com.hellofresh.skudemandforecast.lib.job.KrontabScheduler
import com.hellofresh.skudemandforecast.lib.job.MeteredJob
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.DcConfigRepositoryJooqImpl
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.DemandRepositoryImpl
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.MpsFractionSkuDemandRepositoryImpl
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.RecipeSnapshotRepositoryImpl
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.SkuRepositoryBuilder
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeUnit.MINUTES

private const val HTTP_PORT = 8081
private const val READONLY_DB_POOL_SIZE = 5
private const val READ_WRITE_DB_POOL_SIZE = 5

private val singleThreadJobExecutor = ThreadPoolExecutor(
    1,
    1,
    Long.MAX_VALUE,
    TimeUnit.HOURS,
    ArrayBlockingQueue(1, true),
    CallerRunsPolicy(),
).also { shutdownNeeded { AutoCloseable { it.shutdownNow() } } }

private fun Application.readOnlyDslContext() =
    DBConfiguration.jooqDslContext(
        databaseConfig = this.databaseConfig("readonly"),
        readOnly = true,
        parallelism = READONLY_DB_POOL_SIZE,
        meterRegistry = meterRegistry,
    )

private fun Application.readWriteDslContext() =
    DBConfiguration.jooqDslContext(
        databaseConfig = this.databaseConfig("readWrite"),
        readOnly = false,
        parallelism = READ_WRITE_DB_POOL_SIZE,
        meterRegistry = meterRegistry,
    )

private fun Application.databaseConfig(name: String) =
    DatabaseConfig(
        configName = name,
        hostName = config["db.host"],
        userName = config["db.username"],
        password = config["db.password"],
    )

fun main() {
    runApplication {
        StatusServer.run(meterRegistry, HTTP_PORT)

        val scheduler = shutdownNeeded {
            KrontabScheduler(
                period = this.config["job.time_period"].toDuration().toMinutes().toInt(),
                timeUnit = MINUTES,
                executor = singleThreadJobExecutor,
            )
        }

        val readDslContext = readOnlyDslContext()
        val readWriteDslContext = readWriteDslContext()
        val recipeRepository = RecipeSnapshotRepositoryImpl(readWriteDslContext)
        val demandRepository = DemandRepositoryImpl(readWriteDslContext)
        val dcConfigRepository = DcConfigRepositoryJooqImpl(readDslContext)
        val fractionSkuDemandRepository = MpsFractionSkuDemandRepositoryImpl()
        val mpsForecasterService = MpsForecasterService(
            readWriteDslContext,
            recipeRepository,
            demandRepository,
            { dsl -> SkuRepositoryBuilder(dsl) },
            dcConfigRepository,
            fractionSkuDemandRepository,
        )

        scheduler.schedule {
            MeteredJob(meterRegistry, "mps-forecaster-job", mpsForecasterService::process).execute()
        }
    }
}
