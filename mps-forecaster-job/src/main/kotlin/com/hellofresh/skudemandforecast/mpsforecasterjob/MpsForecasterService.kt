package com.hellofresh.skudemandforecast.mpsforecasterjob

import com.hellofresh.service.Config.Companion.logger
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ErrorPerLine
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ErrorsPerFile
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.FractionSkuDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedFileError.NO_DEMAND_ASSOCIATED_WITH_RECIPE
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedFileError.NO_RECIPE_ASSOCIATED_WITH_DEMAND
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedFileError.SKU_NOT_FOUND
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedForecastData
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.RecipeDemandKey
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedRecipe
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.calculateDate
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.getCurrentDcWeek
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.merge
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.DcConfigRepository
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.DemandRepository
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.FractionSkuDemandRepository
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.RecipeRepository
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.SkuRepository
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.TestOnly
import org.jetbrains.annotations.VisibleForTesting

const val DELAY_PROCESSED_SECONDS = 270
const val DAYS_IN_A_WEEK = 7

/** Only reset dirty bit for rows older than this minutes from now **/
const val DIRTY_BIT_RESET_LAG_MINS = 4L

/**
 * MPS enabled countries
 */
private val MPS_ENABLED_COUNTRIES = listOf("GB", "SE", "IT", "IE", "ES", "FR", "NL", "BE", "NU")

@Suppress("LongParameterList")
class MpsForecasterService(
    private val readWriteDslContext: MetricsDSLContext,
    private val recipeRepository: RecipeRepository,
    private val demandRepository: DemandRepository,
    private val skuRepositoryBuilder: suspend (MetricsDSLContext) -> SkuRepository,
    private val dcConfigRepository: DcConfigRepository,
    private val fractionSkuDemandRepository: FractionSkuDemandRepository,
) : Logging {
    suspend fun process() {
        logger.debug("Starting to process demand & recipe records.")
        val processStartTime = LocalDateTime.now(ZoneOffset.UTC)
        val skuRepository = skuRepositoryBuilder(readWriteDslContext)
        val latestRecipeCountryWeek = recipeRepository.fetchAllByCountryWeek()
        val latestDemandCountryWeek = demandRepository.fetchUpdatedCountryWeek()
        val countryWeeks = latestRecipeCountryWeek.merge(latestDemandCountryWeek).filter {
            !MPS_ENABLED_COUNTRIES.contains(it.country)
        }

        val recipes = recipeRepository.fetch(countryWeeks)
        val demand = demandRepository.fetch(countryWeeks)
        val dcConfigMap = dcConfigRepository.fetch().associateBy { it.dcCode }
        val (forecastSkuDemands, _) = prepareNewForecastDemands(recipes, demand, skuRepository, dcConfigMap)

        readWriteDslContext.withTagName("forecaster-job-save")
            .transaction { config ->
                val transactionalContext = readWriteDslContext.withMeteredConfiguration(config)
                runBlocking {
                    if (forecastSkuDemands.isNotEmpty()) {
                        val dcDates = forecastSkuDemands
                            .map { it.dcCode to it.demandDate }
                            .toSet()
                            .flatMap { (dc, date) -> dcDatesInWeek(dc, date, dcConfigMap) }
                            .toSet()

                        fractionSkuDemandRepository.save(transactionalContext, forecastSkuDemands, dcDates)
                        logger.info("Total number of fraction sku demands created = ${forecastSkuDemands.size}")
                    }
                    val timeToResetDirtyBit = processStartTime.minusMinutes(DIRTY_BIT_RESET_LAG_MINS)
                    recipeRepository.resetDirtyBit(transactionalContext, timeToResetDirtyBit)
                }
            }
        logger.debug("Completed processing demand & recipe records.")
    }

    companion object : Logging
}

suspend fun prepareNewForecastDemands(
    recipesParam: List<UploadedRecipe>,
    demandsParam: List<UploadedDemand>,
    skuRepository: SkuRepository,
    dcConfigMap: Map<String, DcConfig>
): Pair<List<FractionSkuDemand>, List<ErrorsPerFile>> {
    val recipeMap = recipesParam.groupBy {
        RecipeDemandKey(it.recipeIndex, it.week, it.family, it.country, it.locale, it.brand)
    }
    val demandMap = demandsParam.groupBy {
        RecipeDemandKey(it.recipeIndex, it.week, it.family, it.country, it.locale, it.brand)
    }

    val (matchedEntries, unmatched) = outerJoin(recipeMap, demandMap)
        .entries
        .partition { (_, pair) -> pair.first != null && pair.second != null }

    val processed = matchedEntries
        .flatMap { (key, pair) ->
            val (recipes, demands) = pair
            checkNotNull(recipes)
            checkNotNull(demands)

            match(key, recipes, demands, skuRepository, dcConfigMap)
        }

    val unmatchedErrors = unmatched.flatMap { (_, pair) ->

        val (recipes, demands) = pair
        recipes?.map {
            logger.warn("Unable to match recipe: $it")
            ErrorPerLine(it.sourceId, NO_DEMAND_ASSOCIATED_WITH_RECIPE)
        } ?: demands!!.map {
            logger.warn("Unable to match demand: $it")
            ErrorPerLine(it.sourceId, NO_RECIPE_ASSOCIATED_WITH_DEMAND)
        }
    }

    return processed
        .aggregateBrands() to
        ErrorsPerFile.createList(
            processed
                .filter { it.errors.error != null && it.errors.fileSourceId != null }
                .map { it.errors } + unmatchedErrors,
        )
}

// Aggregates demand for the primary key between different brands
private fun List<ProcessedForecastData>.aggregateBrands(): List<FractionSkuDemand> =
    this.filter { it.fractionSkuDemand != null }
        .groupBy { it.fractionSkuDemand!!.toPrimaryKey() }
        .map { (key, processedDemand) ->
            // same primary key
            val qty = processedDemand.groupBy { it.key.brand }
                .map { (brand, groupedByBrands) ->
                    if (groupedByBrands.size > 1) {
                        // TODO: Change to error when the data is fixed
                        logger.warn(
                            """There are duplicate keys in the mps fraction demand for brand $brand
                                and keys ${groupedByBrands.map { it.fractionSkuDemand?.toPrimaryKey() }}
                            """.trimIndent()
                        )
                    }
                    groupedByBrands.first()
                }
                .sumOf { it.fractionSkuDemand!!.qty }

            FractionSkuDemand(
                skuId = key.skuId,
                dcCode = key.dcCode,
                demandDate = key.demandDate,
                country = key.country,
                locale = key.locale,
                productFamily = key.productFamily,
                recipeIndex = key.recipeIndex,
                qty = qty,
            )
        }

private suspend fun match(
    key: RecipeDemandKey,
    recipes: List<UploadedRecipe>,
    demands: List<UploadedDemand>,
    skuRepository: SkuRepository,
    dcConfigMap: Map<String, DcConfig>
): List<ProcessedForecastData> {
    val skuToPickList = recipes.flatMap { recipe ->
        recipe.skus.map {
            it.skuCode to Pair(it.picks, recipe.sourceId)
        }
    }

    return demands.groupBy { it.day to it.dcCode }
        .flatMap { (pair, demandList) ->
            val (day, dcCode) = pair

            val peopleCountToQty =
                demandList.groupBy { it.peopleCount }
                    .map { (people, demands) -> people to demands.sumOf { it.mealsToDeliver } }
                    .toMap()

            skuToPickList.map { (skuCode, picks) ->
                val (pickList, fileId) = picks

                val fetchedSkuId = skuRepository.fetchSkuId(dcCode, skuCode)
                if (fetchedSkuId == null) {
                    logger.error(
                        "Sku code from uploaded file does not exist, fileId = $fileId, skucode = $skuCode",
                    )
                    return@map ProcessedForecastData(
                        key = key,
                        errors = ErrorPerLine(fileId, SKU_NOT_FOUND),
                    )
                }

                ProcessedForecastData(
                    key = key,
                    fractionSkuDemand = FractionSkuDemand(
                        skuId = fetchedSkuId,
                        dcCode = dcCode,
                        demandDate = convertDayToDate(day, dcCode, key.week, dcConfigMap)!!,
                        country = key.country,
                        locale = key.locale ?: "",
                        productFamily = key.family,
                        recipeIndex = key.recipeIndex,
                        qty = pickList.map { (peopleCount, qtyToPick) ->
                            qtyToPick * (peopleCountToQty[peopleCount] ?: 0)
                        }.sum().toLong(),
                    ),
                    errors = ErrorPerLine(fileSourceId = fileId, null),
                )
            }
        }
}

@VisibleForTesting
fun dcDatesInWeek(dcCode: String, date: LocalDate, dcConfigMap: Map<String, DcConfig>): List<DcDate> {
    val productionStart: DayOfWeek = dcConfigMap[dcCode.uppercase()]?.productionStart!!
    val week = DcWeek(date, productionStart)
    val firstDayInWeek = week.getStartDateInDcWeek(productionStart, dcConfigMap[dcCode.uppercase()]?.zoneId!!)

    return (0 until DAYS_IN_A_WEEK).map {
        DcDate(dcCode, firstDayInWeek.plusDays(it.toLong()))
    }
}

private fun convertDayToDate(
    day: DayOfWeek,
    dcCode: String,
    week: String,
    dcConfigMap: Map<String, DcConfig>
): LocalDate? {
    val productionStart: DayOfWeek = dcConfigMap[dcCode.uppercase()]?.productionStart!!
    val currentWeekProduction: Boolean = getCurrentDcWeek(productionStart).equals(week)
    val localDate = calculateDate(
        week,
        productionStart,
        day,
        currentWeekProduction,
    )
    if (localDate == null) {
        logger.error("Couldn't calculate date for [$week], [$day], [$dcCode]")
    }
    return localDate
}

private fun <K, V1, V2> outerJoin(m1: Map<K, V1>, m2: Map<K, V2>): Map<K, Pair<V1?, V2?>> =
    buildMap {
        m1.forEach { (k, v1) ->
            this[k] = v1 to m2[k]
        }
        (m2.keys - m1.keys).forEach { k ->
            this[k] = null to m2[k]
        }
    }

@Suppress("UnnecessaryParentheses")
@TestOnly
fun missingRecipes(
    recipesParam: List<UploadedRecipe>,
    demandsParam: List<UploadedDemand>,
): Map<UUID, MutableList<Int>> {
    val recipeIndicesInRecipe = recipesParam.groupBy { it.recipeIndex }
        .mapValues { (_, list) -> list.mapNotNull { it.sourceId }.toSet() }
    val recipeIndicesInDemand = demandsParam.groupBy { it.recipeIndex }
        .mapValues { (_, list) -> list.mapNotNull { it.sourceId }.toSet() }

    val missingRecipeIds = (recipeIndicesInRecipe - recipeIndicesInDemand.keys) +
        (recipeIndicesInDemand - recipeIndicesInRecipe.keys)

    return buildMap {
        missingRecipeIds.forEach { (recipeId, list) ->
            list.forEach { fileId ->
                if (containsKey(fileId)) {
                    get(fileId)?.add(recipeId)
                } else {
                    put(fileId, mutableListOf(recipeId))
                }
            }
        }
    }
}
