package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.CountryWeek
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.Demand.DEMAND
import java.time.DayOfWeek
import kotlinx.coroutines.future.await

private const val COUNTRY_WEEK_QUERY_CHUNK_SIZE = 20

class DemandRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : DemandRepository {
    private val fetchModifiedDemands = "fetch-modified-demands"
    private val fetchDemands = "fetch-demands"

    override suspend fun fetch(countryWeeks: List<CountryWeek>): List<UploadedDemand> =
        if (countryWeeks.isNotEmpty()) {
            countryWeeks.chunked(COUNTRY_WEEK_QUERY_CHUNK_SIZE).map { countryWeek ->
                dslContext.withTagName(fetchDemands)
                    .selectFrom(DEMAND)
                    .where(
                        buildCountryWeekCondition(
                            DEMAND.COUNTRY,
                            DEMAND.WEEK,
                            countryWeek,
                        ),
                    )
                    .fetchAsync()
                    .await()
                    .map {
                        with(it) {
                            UploadedDemand(
                                recipeIndex = recipeIndex,
                                week = week,
                                family = productFamily,
                                locale = locale,
                                country = country,
                                day = DayOfWeek.valueOf(day.uppercase()),
                                dcCode = dcCode,
                                peopleCount = peopleCount,
                                mealsToDeliver = mealsToDeliver,
                                sourceId = sourceId,
                                brand = brand,
                            )
                        }
                    }
            }.flatten()
        } else {
            emptyList()
        }

    override suspend fun fetchUpdatedCountryWeek(): List<CountryWeek> =
        dslContext.withTagName(fetchModifiedDemands)
            .select(
                DEMAND.COUNTRY,
                DEMAND.WEEK,
            ).distinctOn(DEMAND.COUNTRY, DEMAND.WEEK)
            .from(DEMAND)
            .where(DEMAND.DIRTY_BIT.isTrue)
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    CountryWeek(
                        country = record.get(DEMAND.COUNTRY),
                        week = record.get(DEMAND.WEEK),
                    )
                }
            }.await()
}
