package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.CountryWeek
import org.jooq.TableField
import org.jooq.impl.DSL

fun buildCountryWeekCondition(
    countryField: TableField<*, String>,
    weekField: TableField<*, String>,
    countryWeek: List<CountryWeek>,
) = countryWeek.map { (country, week) ->
    countryField.equalIgnoreCase(country)
        .and(weekField.equalIgnoreCase(week))
}.run {
    reduceOrNull { c1, c2 -> c1.or(c2) } ?: DSL.trueCondition()
}
