package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.CountryWeek
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedRecipe
import java.time.LocalDateTime

interface RecipeRepository {
    suspend fun fetch(countryWeek: List<CountryWeek>): List<UploadedRecipe>
    suspend fun resetDirtyBit(transactionalContext: MetricsDSLContext, timestamp: LocalDateTime)
    suspend fun fetchAllByCountryWeek(): List<CountryWeek>
}
