package com.hellofresh.skudemandforecast.mpsforecasterjob

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedFileError.NO_DEMAND_ASSOCIATED_WITH_RECIPE
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.ProcessedFileError.NO_RECIPE_ASSOCIATED_WITH_DEMAND
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.RecipeDemandKey
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.SkuRecipe
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedRecipe
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.SkuRepository
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.util.UUID
import kotlin.random.Random.Default.nextInt
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import random
import randomString

internal class PrepareForecastDemandTest {

    @Test fun `ignores the RecipeDemandKey which are not matching`() {
        val keys = buildSet { repeat(100) { add(RecipeDemandKey.random()) } }

        // some will match
        val matchedKeys = buildSet { repeat(5) { add(keys.random()) } }
        val unmatchedKeys = keys - matchedKeys

        val recipes = matchedKeys.map(::randomRecipeFrom)
        val demand = matchedKeys.map(::randomDemandFrom)

        val skuRepo = SkuRepository {
            recipes.flatMap { it.skus }.map { it.skuCode }.toSet()
                .zip(demand.map { it.dcCode }.toSet())
                .associateWith { UUID.randomUUID() }
        }

        val dcRepo = demand.map { it.dcCode }.toSet()
            .associateWith { DcConfig.random().copy(dcCode = it) }

        val (fractionDemand, _) = runBlocking {
            prepareNewForecastDemands(
                recipes,
                demand,
                skuRepo,
                dcRepo,
            )
        }

        assertEquals(matchedKeys.size, fractionDemand.size)
        val demandForNonMatchingKeys = unmatchedKeys.flatMap { key ->
            fractionDemand.filter {
                it.country == key.country &&
                    it.locale == key.locale &&
                    it.recipeIndex == key.recipeIndex &&
                    key.family == it.productFamily
            }
        }

        assertEquals(0, demandForNonMatchingKeys.size)
    }

    @Test fun `prepares forecast for each sku and day`() {
        val key = RecipeDemandKey.random()
        val sku1 = "sku-code-${randomString(4)}"
        val sku2 = "sku-code-${randomString(4)}"
        val week = "2023-W40"

        val recipeSku1 = randomRecipeFrom(key).copy(
            skus = listOf(
                SkuRecipe(
                    "${randomString(3)}-00-${nextInt(5)}-1",
                    mapOf(1 to 2, 2 to 4),
                ),
            ),
            week = week,
        )

        val recipeSku2 = randomRecipeFrom(key).copy(
            skus = listOf(
                SkuRecipe(
                    "${randomString(3)}-00-${nextInt(5)}-1",
                    mapOf(1 to 10, 2 to 20),
                ),
            ),
            week = week,
        )

        val sampleDemand = randomDemandFrom(key).copy(week = week)

        val demands = buildList {
            add(sampleDemand.copy(day = MONDAY, mealsToDeliver = 100, peopleCount = 1))
            add(sampleDemand.copy(day = TUESDAY, mealsToDeliver = 100, peopleCount = 2))
            add(sampleDemand.copy(day = WEDNESDAY, mealsToDeliver = 100, peopleCount = 2))
        }

        val skuRepo = SkuRepository {
            mapOf(
                Pair(sampleDemand.dcCode, sku1) to UUID.randomUUID(),
                Pair(sampleDemand.dcCode, sku2) to UUID.randomUUID(),
            )
        }

        val dcRepo = mapOf(
            sampleDemand.dcCode to DcConfig.random().copy(
                dcCode = sampleDemand.dcCode,
            ),
        )

        val (fractionDemands, _) = runBlocking {
            prepareNewForecastDemands(
                listOf(recipeSku1, recipeSku2),
                demands,
                skuRepo,
                dcRepo,
            )
        }

        assertEquals(6, fractionDemands.size) // number of days * number of sku
    }

    @Test fun `returns error if some demand or recipes are not matched`() {
        val keys = buildSet { repeat(100) { add(RecipeDemandKey.random()) } }

        // some will match
        val matchedKeys = buildSet { repeat(5) { add(keys.random()) } }
        val unmatchedKeys = keys - matchedKeys

        val unmatchedDemandKeys = buildSet { repeat(5) { add(unmatchedKeys.random()) } }
        val unmatchedRecipesKeys = buildSet {
            repeat(10) {
                val key = unmatchedKeys.random()
                if (key !in unmatchedDemandKeys) {
                    add(key)
                }
            }
        }

        val unmatchedDemand = unmatchedDemandKeys.map(::randomDemandFrom)
        val unmatchedRecipe = unmatchedRecipesKeys.map(::randomRecipeFrom)

        val recipes = matchedKeys.map(::randomRecipeFrom) + unmatchedRecipe
        val demand = matchedKeys.map(::randomDemandFrom) + unmatchedDemand

        val skuRepo = SkuRepository {
            recipes.flatMap { it.skus }.map { it.skuCode }.toSet()
                .zip(demand.map { it.dcCode }.toSet())
                .associateWith { UUID.randomUUID() }
        }

        val dcRepo = demand.map { it.dcCode }.toSet().associateWith {
            DcConfig.random().copy(dcCode = it)
        }

        val (fractionDemand, errors) = runBlocking {
            prepareNewForecastDemands(
                recipes,
                demand,
                skuRepo,
                dcRepo,
            )
        }

        assertTrue(errors.isNotEmpty())

        assertEquals(
            unmatchedDemand.map { it.sourceId }.toSet(),
            errors.filter { it.errors.contains(NO_RECIPE_ASSOCIATED_WITH_DEMAND) }
                .map { it.fileSourceId }
                .toSet(),
        )

        assertEquals(
            unmatchedRecipe.map { it.sourceId }.toSet(),
            errors.filter { it.errors.contains(NO_DEMAND_ASSOCIATED_WITH_RECIPE) }
                .map { it.fileSourceId }
                .toSet(),
        )
        assertEquals(matchedKeys.size, fractionDemand.size)
    }
}

fun randomDemandFrom(key: RecipeDemandKey) = UploadedDemand.random().copy(
    recipeIndex = key.recipeIndex,
    week = key.week,
    country = key.country,
    family = key.family,
    locale = key.locale,
    brand = key.brand,
)

fun randomRecipeFrom(key: RecipeDemandKey) = UploadedRecipe.random().copy(
    recipeIndex = key.recipeIndex,
    week = key.week,
    country = key.country,
    family = key.family,
    locale = key.locale,
    brand = key.brand,
)
