package com.hellofresh.skudemandforecast.mpsforecasterjob

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcConfig
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedRecipe
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.DcConfigRepository
import com.hellofresh.skudemandforecast.mpsforecasterjob.repository.SkuRepository
import default
import io.mockk.coEvery
import io.mockk.mockk
import java.time.DayOfWeek
import java.time.DayOfWeek.THURSDAY
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import random

internal class MpsForecasterServiceTest {
    private val mockSkuRepository = mockk<SkuRepository>()
    private val mockDcConfigRepository = mockk<DcConfigRepository>()
    private val dcConfig = DcConfig(
        dcCode = "VE",
        market = "DACH",
        productionStart = DayOfWeek.MONDAY,
        zoneId = ZoneId.of("Europe/Berlin"),
        globalDc = null,
        enabled = true,
    )

    @Test
    fun `should return new fraction demands for matched uploaded recipe & demand`() {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default()
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(
            recipe.recipeIndex,
            newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex,
        )
        assertEquals(
            demand.recipeIndex,
            newForecastDemands.first
                { it.recipeIndex == 1 }.recipeIndex,
        )
    }

    @Test
    fun `should return empty list of new fraction demands when the uploaded recipe and demand are not matching`() {
        val recipe = UploadedRecipe.Companion.default().copy(
            recipeIndex = 10,
        )
        val demand = UploadedDemand.Companion.default().copy(
            recipeIndex = 20,
        )
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )
        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertTrue(newForecastDemands.isEmpty())
    }

    @Test
    fun `should returns multiple new fraction demands when the uploaded recipe and demand are matching`() {
        val recipe = UploadedRecipe.Companion.default()
        val demand = UploadedDemand.Companion.default()
        val recipeOne = UploadedRecipe.Companion.default().copy(
            recipeIndex = 10,
        )
        val recipeNotMatching = UploadedRecipe.Companion.default().copy(
            recipeIndex = 100,
        )
        val demandOne = UploadedDemand.Companion.default().copy(
            recipeIndex = 10,
        )
        val demandNotMatching = UploadedDemand.Companion.default().copy(
            recipeIndex = 200,
        )
        val uploadedRecipe = listOf(recipe, recipeOne, recipeNotMatching)
        val uploadedDemand = listOf(demand, demandOne, demandNotMatching)
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )
        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertEquals(2, newForecastDemands.size)
        assertEquals(recipe.recipeIndex, newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex)
        assertEquals(demand.recipeIndex, newForecastDemands.first { it.recipeIndex == 1 }.recipeIndex)
        assertEquals(recipeOne.recipeIndex, newForecastDemands.first { it.recipeIndex == 10 }.recipeIndex)
        assertEquals(demandOne.recipeIndex, newForecastDemands.first { it.recipeIndex == 10 }.recipeIndex)
    }

    @Test
    fun `missing recipeIds are collected`() {
        val recipes = buildList { repeat(5) { add(UploadedRecipe.random()) } }
        val demand = buildList { add(UploadedDemand.random()) }
        val output = missingRecipes(recipes, demand)
        assertTrue { output.isNotEmpty() }

        val expected = recipes.mapNotNull { it.sourceId } + demand.mapNotNull { it.sourceId }
        assertEquals(expected.sorted(), output.keys.sorted())
    }

    @Test
    fun `dcDatesInWeek gets all the days in a production week`() {
        val dc = "VE"
        val date = LocalDate.now()
        val dcConfig = DcConfig.random().copy(
            dcCode = dc,
            productionStart = THURSDAY,
        )

        val dcDates = dcDatesInWeek(dc, date, dcConfigMap = mapOf(dc to dcConfig))
        assertEquals(7, dcDates.size)
        assertTrue { dcDates.contains(DcDate(dc, date)) }
        assertEquals(7, dcDates.map { it.date.dayOfWeek }.toSet().size)
        assertEquals(THURSDAY, dcDates.minOf { it.date }.dayOfWeek)
    }

    @Test
    fun `recipe and demand for different brands shouldn't be matched`() {
        val recipe = UploadedRecipe.default().copy(brand = "HF")
        val demand = UploadedDemand.default().copy(brand = "EP")
        val uploadedRecipe = listOf(recipe)
        val uploadedDemand = listOf(demand)
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        coEvery { mockDcConfigRepository.fetch() } returns listOf(
            dcConfig,
        )

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertEquals(0, newForecastDemands.size)
    }

    @Test
    fun `fraction demand of different brands are aggregated`() {
        val demandHF = UploadedDemand.default().copy(brand = "HF")
        val recipeHF = UploadedRecipe.default().copy(brand = "HF")

        val demandEP = UploadedDemand.default().copy(
            brand = "EP",
            mealsToDeliver = 100,
        )
        val recipeEP = UploadedRecipe.default().copy(brand = "EP")

        val uploadedRecipe = listOf(recipeEP, recipeHF)
        val uploadedDemand = listOf(demandEP, demandHF)
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        val (newForecastDemands, _) = runBlocking {
            prepareNewForecastDemands(
                uploadedRecipe,
                uploadedDemand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertEquals(1, newForecastDemands.size)
        assertEquals(110, newForecastDemands.first().qty)
    }

    @Test
    fun `errors in null source ids are ignored`() {
        val recipes = buildList { repeat(5) { add(UploadedRecipe.random().copy(sourceId = null)) } }
        val demand = buildList { add(UploadedDemand.random().copy(sourceId = null)) }
        coEvery { mockSkuRepository.fetchSkuId(any(), any()) } returns UUID.randomUUID()

        // Recipes will mismatch as they
        val (newForecastDemands, errors) = runBlocking {
            prepareNewForecastDemands(
                recipes,
                demand,
                mockSkuRepository,
                mapOf(dcConfig.dcCode to dcConfig),
            )
        }
        assertTrue { newForecastDemands.isEmpty() }

        // errors with null fileIds are ignored
        assertTrue { errors.isEmpty() }
    }
}
