@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileSource.INVENTORY
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileType.DEMAND
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.MpsFractionSkuDemandRecord
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.RecipeSnapshotRecord
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.SkuSpecificationRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.random.Random.Default.nextInt
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    fun fileUploadsRecord() = FileUploadsRecord().apply {
        id = UUID.randomUUID()
        dcs = setOf(UUID.randomUUID().toString()).toTypedArray()
        weeks = setOf(UUID.randomUUID().toString()).toTypedArray()
        fileName = UUID.randomUUID().toString()
        content = UUID.randomUUID().toString().toByteArray()
        errors = listOf(UUID.randomUUID().toString()).toTypedArray()
        uploadedFileSource = INVENTORY
        uploadedFileType = DEMAND
        uploadedFileStatus = CREATED
        authorName = "author"
        authorEmail = "email"
        createdAt = OffsetDateTime.now(UTC)
        market = "market"
    }

    @Suppress("LongParameterList")
    fun demandRecord(
        dirtyBitParam: Boolean = true,
        sourceIdParam: UUID,
        updatedAtParam: LocalDateTime = LocalDateTime.now(UTC).minusMinutes(5),
        mealNumberParam: Int = 1,
        productFamilyParam: String = "test-product-family" + nextInt(),
        dayParam: String = "MONDAY",
        peopleCountParam: Int = 1,
        mealsToDeliverParam: Int = 1,
    ) = DemandRecord().apply {
        recipeIndex = mealNumberParam
        week = "2023-W47"
        productFamily = productFamilyParam
        locale = "DE"
        country = "DE"
        day = dayParam
        dcCode = "VE"
        peopleCount = peopleCountParam
        mealsToDeliver = mealsToDeliverParam
        dirtyBit = dirtyBitParam
        sourceId = sourceIdParam
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = updatedAtParam
    }

    @Suppress("MagicNumber", "LongParameterList")
    fun recipeRecord(
        updatedAtParam: LocalDateTime = LocalDateTime.now(UTC).minusMinutes(5),
        indexParam: Int = 1,
        familyParam: String = "test-product-family" + nextInt(),
        skuCode1Param: String = "SPI-1234",
        skuCode2Param: String? = null,
        countryParam: String = "DE",
    ) = RecipeSnapshotRecord().apply {
        val sku2 = skuCode2Param?.let {
            listOf(
                SkuPicks(
                    skuCode2Param,
                    listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                ),
            )
        } ?: emptyList()

        recipeIndex = indexParam
        week = "2023-W47"
        productFamily = familyParam
        locale = "DE"
        country = countryParam
        value = JSONB.jsonb(
            objectMapper.writeValueAsString(
                RecipeValue(
                    listOf(
                        SkuPicks(
                            skuCode1Param,
                            listOf(Pick(1, 1), Pick(2, 2), Pick(3, 3), Pick(4, 4)),
                        ),
                    ) + sku2,
                ),
            ),
        )
        createdAt = OffsetDateTime.now(UTC)
        updatedAt = updatedAtParam
    }

    @Suppress("LongParameterList")
    fun fractionSkuDemandRecord(
        skuIdParam: UUID = UUID.randomUUID(),
        demandDateParam: LocalDate = LocalDate.now(),
        dcCodeParam: String = "VE",
        countryParam: String = "DE",
        localeParam: String = "DE",
        recipeIndexParam: Int = 1,
        productFamilyParam: String = "test-product-family",
        qtyParam: Long = 10,
    ) = MpsFractionSkuDemandRecord().apply {
        skuId = skuIdParam
        demandDate = demandDateParam
        dcCode = dcCodeParam
        country = countryParam
        locale = localeParam
        recipeIndex = recipeIndexParam
        productFamily = productFamilyParam
        qty = qtyParam
        createdAt = LocalDateTime.now()
        updatedAt = LocalDateTime.now()
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.RECIPE_SNAPSHOT).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dsl.deleteFrom(Tables.MPS_FRACTION_SKU_DEMAND).execute()
    }

    companion object {
        lateinit var dcRecord: DcConfigRecord
        lateinit var skuSpecificationRecord: SkuSpecificationRecord
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        lateinit var demandRepository: DemandRepository
        lateinit var recipeRepository: RecipeRepository
        lateinit var fractionSkuDemandRepository: FractionSkuDemandRepository
        lateinit var skuRepositoryBuilder: SkuRepository
        lateinit var dcConfigRepository: DcConfigRepository
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcRecord = createDcConfigRecord()
            skuSpecificationRecord = createSkuSpecificationRecord()
            dsl.batchInsert(dcRecord, skuSpecificationRecord, createSkuSpecificationRecord("SPI-1235")).execute()
            demandRepository = DemandRepositoryImpl(dsl)
            recipeRepository = RecipeSnapshotRepositoryImpl(dsl)
            fractionSkuDemandRepository = MpsFractionSkuDemandRepositoryImpl()
            skuRepositoryBuilder = runBlocking { SkuRepositoryBuilder(dsl) }
            dcConfigRepository = DcConfigRepositoryJooqImpl(dsl)
        }

        private fun createDcConfigRecord() =
            DcConfigRecord().apply {
                dcCode = "VE"
                market = "DACH"
                productionStart = "MONDAY"
                zoneId = "UTC"
                enabled = true
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
            }

        private fun createSkuSpecificationRecord(code: String = "SPI-1234") =
            SkuSpecificationRecord().apply {
                id = UUID.randomUUID()
                parentId = null
                this.code = code
                market = "dach"
                this.packaging = ""
                createdAt = LocalDateTime.now()
                updatedAt = LocalDateTime.now()
            }

        @AfterAll
        @JvmStatic
        fun cleanUp() {
            dsl.deleteFrom(Tables.DC_CONFIG).execute()
        }
    }
}
