package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.DcDate
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.FractionSkuDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.Tables.MPS_FRACTION_SKU_DEMAND
import java.time.LocalDate
import kotlin.random.Random
import kotlinx.coroutines.runBlocking
import mpsFractionSkuDemand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

internal class FractionSkuDemandRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should be able to save fraction sku demand`() {
        val existingFractionSkuDemandRecord = fractionSkuDemandRecord()
        runBlocking {
            dsl.batchInsert(existingFractionSkuDemandRecord).execute()
            val fractionSkuDemand = FractionSkuDemand.Companion.mpsFractionSkuDemand(
                skuIdParam = existingFractionSkuDemandRecord.skuId,
                qtyParam = 100,
            )
            fractionSkuDemandRepository.save(dsl, listOf(fractionSkuDemand), listOf())
            val deletedRecords = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND)
                .where(MPS_FRACTION_SKU_DEMAND.QTY.eq(10)).fetch()
            assertTrue(deletedRecords.isEmpty())
            val result = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND)
                .fetch().first()
            assertEquals(100, result.qty)
        }
    }

    @Test
    fun `should be able to save multiple fraction sku demand`() {
        runBlocking {
            val fractionSkuDemandOne = FractionSkuDemand.Companion.mpsFractionSkuDemand(
                qtyParam = 100,
            )
            val fractionSkuDemandTwo = FractionSkuDemand.Companion.mpsFractionSkuDemand(
                qtyParam = 200,
            )
            val fractionSkuDemandThree = FractionSkuDemand.Companion.mpsFractionSkuDemand(
                qtyParam = 400,
            )
            fractionSkuDemandRepository.save(
                dsl,
                listOf(
                    fractionSkuDemandOne,
                    fractionSkuDemandTwo,
                    fractionSkuDemandThree,
                ),
                listOf(),
            )
            val createdRecords = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND)
                .fetch()
            assertEquals(3, createdRecords.size)
            assertEquals(fractionSkuDemandOne.skuId, createdRecords.first { it.qty == 100L }.skuId)
            assertEquals(fractionSkuDemandTwo.skuId, createdRecords.first { it.qty == 200L }.skuId)
            assertEquals(fractionSkuDemandThree.skuId, createdRecords.first { it.qty == 400L }.skuId)
        }
    }

    @Test
    fun `should make demand 0 for the dates in a dc, but not other dcs`() {
        runBlocking {
            // Create demand for dcs in 2 different date but same country.
            val date1 = LocalDate.now()
            val date2 = LocalDate.now().minusDays(1)
            val date3 = LocalDate.now().plusDays(1)

            val country = "SE"
            val anyDC = "ANY-DC"
            val dc = "MO"
            val random = Random(System.nanoTime())

            // demand for a random dc in the country and for MO dc
            fractionSkuDemandRepository.save(
                dsl,
                listOf(
                    FractionSkuDemand.Companion.mpsFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date1,
                        dcCodeParam = anyDC,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.mpsFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date1,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.mpsFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date2,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                    FractionSkuDemand.Companion.mpsFractionSkuDemand(
                        countryParam = country,
                        demandDateParam = date3,
                        dcCodeParam = dc,
                        recipeIndexParam = random.nextInt(10000),
                    ),
                ),

                listOf(),
            )

            val fractionSkuDemandMO = FractionSkuDemand.Companion.mpsFractionSkuDemand(
                countryParam = country,
                qtyParam = 100,
                dcCodeParam = dc,
                demandDateParam = date1,
            )

            fractionSkuDemandRepository.save(
                dsl,
                listOf(fractionSkuDemandMO),
                listOf(date1, date2, date3).map { DcDate(dc, it) },
            )

            val createdRecords = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND)
                .fetch()

            assertEquals(
                100L,
                createdRecords.first {
                    it.dcCode == dc && it.demandDate == date1 && it.recipeIndex == fractionSkuDemandMO.recipeIndex
                }.qty,
            )

            assertEquals(
                0L,
                createdRecords.first {
                    it.dcCode == dc && it.demandDate != date1
                }.qty,
            )

            // Demand for other DCs are not 0 in the same country
            createdRecords
                .filter { it.dcCode != dc }
                .forEach { assertTrue { it.qty != 0L } }
        }
    }
}
