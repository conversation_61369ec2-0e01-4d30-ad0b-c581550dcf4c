package com.hellofresh.skudemandforecast.mpsforecasterjob.repository

import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.mpsforecasterjob.DELAY_PROCESSED_SECONDS
import com.hellofresh.skudemandforecast.mpsforecasterjob.MpsForecasterService
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.FractionSkuDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedRecipe
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.Tables.FILE_UPLOADS
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileStatus
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileStatus.CREATED_WITH_ERROR
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.enums.FileType
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.MpsFractionSkuDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.MpsFractionSkuDemand.MPS_FRACTION_SKU_DEMAND
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.RecipeSnapshot.RECIPE_SNAPSHOT
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.MpsFractionSkuDemandRecord
import default
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random
import kotlinx.coroutines.runBlocking
import mpsFractionSkuDemand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import toDemandRecord
import toRecipeRecord

@Suppress("StringLiteralDuplication")
class ForecasterServiceFunctionalTest : FunctionalTest() {
    private val testProductFamily = "test-product-family"

    @Test
    fun `should be able to match recipe & demand and produce fraction sku demands`() {
        val recipeFileUploadsRecord = fileUploadsRecord()
        val recipeRecord = recipeRecord(
            indexParam = 1,
            familyParam = testProductFamily,
        )
        val demandFileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
        )
        runBlocking {
            dsl.batchInsert(recipeFileUploadsRecord, demandFileUploadsRecord, recipeRecord, demandRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()
            val fractionSkuDemands = dsl.selectFrom(
                MPS_FRACTION_SKU_DEMAND,
            ).fetch()
            assertEquals(1, fractionSkuDemands.size)
            assertEquals(1, fractionSkuDemands.first().qty)
        }
    }

    @Test
    fun `should be able to match recipe & multiple demand & different day and produce fraction sku demands`() {
        val recipeFileUploadsRecord = fileUploadsRecord()
        val recipeRecord = recipeRecord(
            indexParam = 1,
            familyParam = testProductFamily,
        )
        val demandFileUploadsRecord = fileUploadsRecord()
        val demandRecordOne = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
            mealsToDeliverParam = 10,
            peopleCountParam = 1,
            dayParam = "MONDAY",
        )
        val demandRecordTwo = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
            mealsToDeliverParam = 20,
            peopleCountParam = 2,
            dayParam = "TUESDAY",
        )
        val demandRecordThree = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
            mealsToDeliverParam = 30,
            peopleCountParam = 4,
            dayParam = "WEDNESDAY",
        )
        runBlocking {
            dsl.batchInsert(
                recipeFileUploadsRecord,
                demandFileUploadsRecord,
                recipeRecord,
                demandRecordOne,
                demandRecordTwo,
                demandRecordThree,
            ).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()
            val fractionSkuDemands = dsl.selectFrom(
                MpsFractionSkuDemand.MPS_FRACTION_SKU_DEMAND,
            ).fetch()
            assertEquals(3, fractionSkuDemands.size)
            assertEquals(10, fractionSkuDemands.first { it.qty == 10L }.qty)
            assertEquals(40, fractionSkuDemands.first { it.qty == 40L }.qty)
            assertEquals(120, fractionSkuDemands.first { it.qty == 120L }.qty)
        }
    }

    @Test
    fun `should not create fraction sku demand if the recipe and demand does not match`() {
        val recipeFileUploadsRecord = fileUploadsRecord().apply {
            uploadedFileType = FileType.RECIPE
            errors = null
        }
        val recipeRecord = recipeRecord(
            indexParam = 100,
            familyParam = testProductFamily,
        )
        val demandFileUploadsRecord = fileUploadsRecord().apply {
            uploadedFileType = FileType.DEMAND
            errors = null
        }
        val demandRecord = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
        )
        runBlocking {
            dsl.batchInsert(recipeFileUploadsRecord, demandFileUploadsRecord, recipeRecord, demandRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()
            val fractionSkuDemands = dsl.selectFrom(MpsFractionSkuDemand.MPS_FRACTION_SKU_DEMAND).fetch()
            assertEquals(0, fractionSkuDemands.size)

            // Error is set in the original file source
            val filesWithError = dsl.select(FILE_UPLOADS.ID, FILE_UPLOADS.UPLOADED_FILE_TYPE, FILE_UPLOADS.ERRORS)
                .from(FILE_UPLOADS)
                .where(FILE_UPLOADS.ERRORS.isNotNull)
                .and(FILE_UPLOADS.UPLOADED_FILE_STATUS.eq(FileStatus.CREATED_WITH_ERROR))
                .fetch()

            assertTrue {
                setOf(
                    recipeFileUploadsRecord.id,
                    demandFileUploadsRecord.id,
                ).containsAll(filesWithError.map { it.component1() })
            }

            filesWithError.forEach {
                val err = it.component3()
                assertEquals(2, err.size)
                assertTrue {
                    if (it.component2() == FileType.DEMAND) {
                        err.contains("Missing recipe indices: ${demandRecord.recipeIndex}")
                    } else {
                        err.contains("Missing recipe indices: ${recipeRecord.recipeIndex}")
                    }
                }
            }
        }
    }

    @Test
    fun `should create fraction sku demand if there is no recipe and demand records pending to be processed`() {
        val recipeRecord = recipeRecord(
            indexParam = 1,
            familyParam = testProductFamily,
        )
        val demandFileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(
            dirtyBitParam = false,
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
        )
        runBlocking {
            dsl.batchInsert(demandFileUploadsRecord, recipeRecord, demandRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()
            val fractionSkuDemandsCount = dsl.fetchCount(MPS_FRACTION_SKU_DEMAND)
            assertEquals(1, fractionSkuDemandsCount)
        }
    }

    @Test
    fun `should persist unknown skuid error in file uploads when the given skucode is not present`() {
        val recipeFileUploadsRecord = fileUploadsRecord()
        val recipeRecord = recipeRecord(
            indexParam = 1,
            familyParam = testProductFamily,
            skuCode1Param = "NON-EXISTING-SKU-CODE",
        )
        val demandFileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
        )
        runBlocking {
            dsl.batchInsert(recipeFileUploadsRecord, demandFileUploadsRecord, recipeRecord, demandRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()
            val fractionSkuDemands = dsl.selectFrom(
                MpsFractionSkuDemand.MPS_FRACTION_SKU_DEMAND,
            ).fetch()
            assertEquals(0, fractionSkuDemands.size)
            val errors = dsl.selectFrom(
                FILE_UPLOADS,
            ).where(FILE_UPLOADS.ID.eq(recipeFileUploadsRecord.id)).fetch().first().errors

            assertEquals(1, errors.size)
//            assertTrue(errors.contains("Sku code not found on 1 number of lines"))
        }
    }

    @Test fun `remove demand when sku is removed from a recipe`() {
        val recipeFileUploadsRecord = fileUploadsRecord()
        val recipeRecord = recipeRecord(
            indexParam = 1,
            familyParam = testProductFamily,
            skuCode1Param = "SPI-1234",
            skuCode2Param = "SPI-1235",
        )

        val demandFileUploadsRecord = fileUploadsRecord()
        val demandRecord = demandRecord(
            sourceIdParam = demandFileUploadsRecord.id,
            mealNumberParam = 1,
            productFamilyParam = testProductFamily,
        )
        runBlocking {
            dsl.batchInsert(recipeFileUploadsRecord, demandFileUploadsRecord, recipeRecord, demandRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()

            val fractionDemand = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND).fetch()
            assertEquals(
                2,
                fractionDemand.filter { it.recipeIndex == recipeRecord.recipeIndex }.size,
            )

            dsl.deleteFrom(RECIPE_SNAPSHOT)
                .where(RECIPE_SNAPSHOT.RECIPE_INDEX.eq(recipeRecord.recipeIndex))
                .execute()

            dsl.batchInsert(
                recipeRecord(
                    indexParam = 1,
                    familyParam = testProductFamily,
                    skuCode1Param = "SPI-1234",
                    skuCode2Param = null,
                ),
            ).execute()

            forecasterService.process()
        }
    }

    @Test fun `fetches sku data at every run`() {
        val counter = AtomicInteger(0)
        runBlocking {
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { _ ->
                    counter.getAndIncrement()
                    SkuRepository {
                        mapOf()
                    }
                },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )

            val numberOfRuns = 5
            repeat(numberOfRuns) { forecasterService.process() }
            assertEquals(numberOfRuns, counter.get())
        }
    }

    @Test
    fun `does not update older files status when there are errors`() {
        val recipeFileUploadsRecord = fileUploadsRecord()
            .apply {
                this.uploadedFileType = FileType.RECIPE
                this.uploadedFileStatus = CREATED
                this.updatedAt = OffsetDateTime.now(UTC).minusSeconds(DELAY_PROCESSED_SECONDS + 10L)
            }

        val demandFileUploadsRecord = fileUploadsRecord()
            .apply {
                this.uploadedFileType = FileType.DEMAND
                this.uploadedFileStatus = CREATED_WITH_ERROR
                this.updatedAt = OffsetDateTime.now(UTC).minusSeconds(DELAY_PROCESSED_SECONDS + 20L)
            }

        runBlocking {
            dsl.batchInsert(recipeFileUploadsRecord, demandFileUploadsRecord).execute()
            val forecasterService = MpsForecasterService(
                readWriteDslContext = dsl,
                recipeRepository = recipeRepository,
                demandRepository = demandRepository,
                skuRepositoryBuilder = { skuRepositoryBuilder },
                dcConfigRepository = dcConfigRepository,
                fractionSkuDemandRepository = fractionSkuDemandRepository,
            )
            forecasterService.process()

            val files = dsl.selectFrom(
                FILE_UPLOADS,
            ).fetch()

            assertEquals(2, files.size)

            with(files.first { it.id == recipeFileUploadsRecord.id }) {
                assertEquals(FileType.RECIPE, uploadedFileType)
                assertEquals(CREATED, uploadedFileStatus)
            }

            with(files.first { it.id == demandFileUploadsRecord.id }) {
                assertEquals(FileType.DEMAND, uploadedFileType)
                assertEquals(CREATED_WITH_ERROR, uploadedFileStatus)
            }
        }
    }

    @Test fun `demand is set 0 for multiple production weeks`() {
        // "2023-W46" + "2023-W47" + "2023-W48" -> 2023-11-06 - 2023-11-27

        val skuCode = skuSpecificationRecord.code
        val skuId = skuSpecificationRecord.id

        val demandToInsert = with(FractionSkuDemand.mpsFractionSkuDemand()) {
            val startDate = LocalDate.of(2023, 11, 6)
            (0 until 21L).map {
                copy(
                    skuId = skuId,
                    demandDate = startDate.plusDays(it),
                    qty = Random(System.nanoTime()).nextLong(100, 200),
                )
            }.map {
                MpsFractionSkuDemandRecord(
                    it.skuId,
                    it.dcCode,
                    it.demandDate,
                    it.country,
                    it.locale,
                    it.productFamily,
                    it.recipeIndex,
                    it.qty,
                    LocalDateTime.now(),
                    LocalDateTime.now(),
                )
            }
        }

        dsl.batchInsert(demandToInsert).execute()
        val recipeWeek1 = UploadedRecipe.default(skuCode).copy(week = "2023-W46").toRecipeRecord()
        val recipeWeek2 = UploadedRecipe.default(skuCode).copy(week = "2023-W47").toRecipeRecord()
        val recipeWeek3 = UploadedRecipe.default(skuCode).copy(week = "2023-W48").toRecipeRecord()

        val demandWeek1 = UploadedDemand.default().copy(week = "2023-W46", brand = "UNKNOWN").toDemandRecord()
        val demandWeek2 = UploadedDemand.default().copy(week = "2023-W47", brand = "UNKNOWN").toDemandRecord()
        val demandWeek3 = UploadedDemand.default().copy(week = "2023-W48", brand = "UNKNOWN").toDemandRecord()

        val uploadedRecipe = listOf(recipeWeek1, recipeWeek2, recipeWeek3)
        val uploadedDemand = listOf(demandWeek1, demandWeek2, demandWeek3)

        dsl.batchInsert(uploadedRecipe).execute()
        dsl.batchInsert(uploadedDemand).execute()

        val forecasterService = MpsForecasterService(
            readWriteDslContext = dsl,
            recipeRepository = recipeRepository,
            demandRepository = demandRepository,
            skuRepositoryBuilder = { skuRepositoryBuilder },
            dcConfigRepository = dcConfigRepository,
            fractionSkuDemandRepository = fractionSkuDemandRepository,
        )

        runBlocking { forecasterService.process() }

        val records = dsl.selectFrom(MPS_FRACTION_SKU_DEMAND).fetch()
        val (zeroQty, nonZeroQty) = records.partition { it.qty == 0L }
        assertEquals(3, nonZeroQty.size)
        assertEquals(18, zeroQty.size)

        val daysWithDemand = uploadedDemand.map {
            DcWeek(it.week).getDate(
                DayOfWeek.valueOf(dcRecord.productionStart.uppercase()),
                ZoneId.of(dcRecord.zoneId),
                DayOfWeek.valueOf(it.day),
            )
        }

        nonZeroQty.forEach {
            assertTrue { daysWithDemand.contains(it.demandDate) }
        }

        zeroQty.forEach {
            assertFalse { daysWithDemand.contains(it.demandDate) }
        }
    }
}
