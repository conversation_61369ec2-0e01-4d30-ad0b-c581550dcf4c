@file:Suppress("MagicNumber")

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.CountryWeek
import kotlin.random.Random

const val ALPHABETS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

fun randomString(length: Int) = with(Random(System.nanoTime())) {
    (0 until length).map { ALPHABETS[nextInt(25)] }.joinToString(separator = "")
}

fun CountryWeek.Companion.random() = with(Random(System.nanoTime())) {
    CountryWeek(
        country = randomString(2),
        week = "2023-W${nextInt(10, 52)}",
    )
}
