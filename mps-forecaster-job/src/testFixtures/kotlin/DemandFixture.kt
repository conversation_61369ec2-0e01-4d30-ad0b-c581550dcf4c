@file:Suppress("MagicNumber")

import com.hellofresh.skudemandforecast.mpsforecasterjob.model.UploadedDemand
import com.hellofresh.skudemandforecast.mpsforecasterjob.schema.public_.tables.records.DemandRecord
import java.time.DayOfWeek
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.random.Random

fun UploadedDemand.Companion.default() = UploadedDemand(
    recipeIndex = 1,
    week = "2023-W46",
    family = "test-product-family",
    locale = "DE",
    country = "DE",
    day = DayOfWeek.MONDAY,
    dcCode = "VE",
    peopleCount = 1,
    mealsToDeliver = 10,
    sourceId = UUID.randomUUID(),
    brand = "HF",
)

fun UploadedDemand.Companion.random() = with(Random(System.nanoTime())) {
    UploadedDemand(
        recipeIndex = nextInt(1000),
        week = "2023-W${nextInt(10, 52)}",
        family = "test-product-family-${randomString(6)}",
        locale = randomString(2),
        country = randomString(2),
        day = DayOfWeek.MONDAY,
        dcCode = randomString(2),
        peopleCount = nextInt(5),
        mealsToDeliver = nextInt(1000),
        sourceId = UUID.randomUUID(),
        brand = randomString(6).uppercase()
    )
}

fun UploadedDemand.toDemandRecord() = DemandRecord().apply {
    recipeIndex = <EMAIL>
    week = <EMAIL>
    productFamily = family
    locale = <EMAIL>
    country = <EMAIL>
    day = <EMAIL>
    dcCode = <EMAIL>
    peopleCount = <EMAIL>
    mealsToDeliver = <EMAIL>
    dirtyBit = true
    sourceId = <EMAIL>
    createdAt = OffsetDateTime.now()
    brand = <EMAIL>
}
