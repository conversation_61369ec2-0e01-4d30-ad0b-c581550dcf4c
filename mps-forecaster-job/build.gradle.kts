@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    hellofresh.`test-integration`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

group = "$group.${project.name}".replace("-", "")
description = "Process the MPS recipe and demand records via background jobs"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib"))
    implementation(project(":lib:db"))
    implementation(project(":lib:job"))
    implementation(project(":lib:models"))
    implementation(project(":lib:models:distribution-center"))
    implementation(project(":lib:logging"))
    implementation(libs.coroutines.jdk8)
    implementation(libs.hellofresh.service)
    implementation(libs.jooq.core)
    implementation(libs.krontab)
    implementation(libs.jackson.databind)
    implementation(libs.jackson.kotlin)

    testImplementation(libs.awaitility)

    testFixturesImplementation(libs.jackson.databind)
    testFixturesImplementation(libs.jackson.kotlin)

    testImplementation(libs.mockk)
    testIntegrationImplementation(libs.coroutines.core)
    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.jooq.core)
    testIntegrationImplementation(libs.jackson.kotlin)
    testIntegrationImplementation(libs.testcontainers.core) {
        // excluding junit 4
        exclude("junit", module = "junit")
    }
    testIntegrationImplementation(libs.testcontainers.junit)
    testIntegrationImplementation(libs.testcontainers.postgresql)
    testIntegrationImplementation(project(":lib:db"))
    testIntegrationImplementation(testFixtures(project(":lib")))
    testIntegrationImplementation(testFixtures(project(":mps-forecaster-job")))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "demand|recipe_snapshot|file_uploads|file_status|file_source|file_type|sku_specification|dc_config|mps_fraction_sku_demand"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
