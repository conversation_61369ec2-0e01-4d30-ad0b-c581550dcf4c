.PHONY: help build clean aws-docker-login check-docker-deps check-env docker-build core-up \
core-down local-up local-down \
local-down-cleanup clean-docker-volumes wait-core-containers kafka-mirror \
check-env set-env set-local-env set-staging-env set-live-env set-docker-env \
print-topics show-lag

.DEFAULT_GOAL := build
SHELL = bash

VERSIONS_FILE=lib/models/src/main/resources/topic-versions.properties
DISTRIBUTION_CENTER=$(shell s=`grep -v "^\#" "$(VERSIONS_FILE)" | grep csku-inventory-forecast.intermediate.distribution-center | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
SKU_SPECIFICATION_TOPIC=$(shell s=`grep -v "^\#" "$(VERSIONS_FILE)" | grep csku-inventory-forecast.intermediate.sku-specification | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
DEMAND_PARTITIONED_TOPIC=$(shell s=`grep -v "^\#" "$(VERSIONS_FILE)" | grep fraction-sku-demand-forecasts | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
DAILY_FORECAST_TOPIC=$(shell s=`grep -v "^\#" "$(VERSIONS_FILE)" | grep daily-forecasts | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
RECIPE_TOPIC=$(shell s=`grep -v "^\#" "$(VERSIONS_FILE)" | grep recipes | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
OUTPUT_PUBLIC_DEMAND_TOPIC=$(shell s=`grep -v "^\#" "aggregator-job/src/main/resources/topic-versions.properties" | grep public.demand.sku-demand-forecast | sed "s/=/.v/" | tr -d "\n" | tr -d "|"`; echo $$s)
PUBLIC_EMPTY_TOPIC=public.empty.v1
SKU_DEMAND_FORECAST=public.demand.sku-demand-forecast.v2
RECIPE_DEMAND_TOPIC=public.production-demand.v1
ANZ_DEMAND_TOPIC=public.demand.recipe-demand-forecast.v1

MANUAL_TOPICS=$(DISTRIBUTION_CENTER)|$(SKU_TOPIC)|$(SKU_SPECIFICATION_TOPIC)|$(OUTPUT_PUBLIC_DEMAND_TOPIC)|${PUBLIC_EMPTY_TOPIC}|${SKU_DEMAND_FORECAST}|${RECIPE_DEMAND_TOPIC}|${ANZ_DEMAND_TOPIC}
ALL_TOPICS=${MANUAL_TOPICS}|$(DAILY_FORECAST_TOPIC)|$(RECIPE_TOPIC)|$(DEMAND_PARTITIONED_TOPIC)

DOCKER_COMPOSE=docker compose -f docker-compose.yaml --env-file .env-docker
AWS_LOGIN=aws ecr get-login-password --region eu-west-1 --profile sso-hf-it-developer | docker login --username AWS --password-stdin https://489198589229.dkr.ecr.eu-west-1.amazonaws.com

env=local # by default

############################################################
#                   Target Dependencies                    #
#           Used as dependency by other targets            #
############################################################

print-topics:
	@echo "ALL_TOPICS: $(ALL_TOPICS)"
	@echo "MANUAL_TOPICS: $(MANUAL_TOPICS)"
	@echo

check-env:
ifndef HF_DEMAND_FORECAST_KAFKA_PASSWORD_LIVE
	$(error HF_DEMAND_FORECAST_KAFKA_PASSWORD_LIVE is undefined)
endif
ifndef HF_DEMAND_FORECAST_KAFKA_PASSWORD_STAGING
	$(error HF_DEMAND_FORECAST_KAFKA_PASSWORD_STAGING is undefined)
endif
ifndef HF_DEMAND_FORECAST_KAFKA_PASSWORD_READONLY_LIVE
	$(error HF_DEMAND_FORECAST_KAFKA_PASSWORD_READONLY_LIVE is undefined)
endif
ifndef HF_DEMAND_FORECAST_KAFKA_PASSWORD_READONLY_STAGING
	$(error HF_DEMAND_FORECAST_KAFKA_PASSWORD_READONLY_STAGING is undefined)
endif
ifndef HF_KAFKA_TRUSTSTORE_PASSWORD
	$(error HF_KAFKA_TRUSTSTORE_PASSWORD is undefined)
endif

check-aws-creds:
ifndef HF_DEMAND_FORECAST_AWS_ACCESS_KEY_ID
	$(error HF_DEMAND_FORECAST_AWS_ACCESS_KEY_ID is undefined)
endif
ifndef HF_DEMAND_FORECAST_AWS_SECRET_ACCESS_KEY
	$(error HF_DEMAND_FORECAST_AWS_SECRET_ACCESS_KEY is undefined)
endif
ifndef HF_DEMAND_FORECAST_AWS_SESSION_TOKEN
	$(error HF_DEMAND_FORECAST_AWS_SESSION_TOKEN is undefined)
endif

aws-docker-login:
	@$(AWS_LOGIN)

set-docker-env:
	@echo ALL_TOPICS="$(ALL_TOPICS)" > .env-docker
	@echo MANUAL_TOPICS="$(MANUAL_TOPICS)" >> .env-docker
	@echo SKU_SPECIFICATION_TOPIC="$(SKU_SPECIFICATION_TOPIC)" >> .env-docker
	@echo DB_NAME=sdf >> .env-docker
	@echo DB_USERNAME=sdf >> .env-docker
	@echo DB_PASSWORD=123456 >> .env-docker
	@echo DB_PORT=5432 >> .env-docker
	@echo DB_OUTBOX_USERNAME=worker >> .env-docker
	@echo DB_OUTBOX_PASSWORD=654321 >> .env-docker

set-env:
	@./docker/dev/set-env.sh $(env)

set-live-env: check-env
	@./docker/dev/set-env.sh live

set-staging-env: check-env
	@./docker/dev/set-env.sh staging

set-local-env:
	@./docker/dev/set-env.sh local


check-docker-deps:
	@echo "docker: $$(which docker || echo 'not found')"
	@echo "docker compose: $$(which docker compose || echo 'not found')"

wait-core-containers:
	@./docker/dev/wait-container.sh -c sdf-broker

############################################################
#                        Gradle Tasks                      #
#       Provides convenience when running Gradle tasks     #
############################################################

clean:
	@./gradlew clean

build:
	@./gradlew clean build

docker-build:
	@HF_TIER=local ./gradlew build   -Phf.docker.registry= \
                       -Phf.docker.repository= \
                       -Phf.docker.tag=local \
                       -Phf.docker.default=local \
                       jibDockerBuild -x test -x testFunctional -x detekt -x testIntegration

build-mirror-plugins:
	@./gradlew tools:kafka-mirror-plugin:shadowJar

build-scripts:
	@./gradlew tools:scripts:shadowJar


############################################################
#                   Environment Creation                   #
# Facilitates the creation and destruction of environments #
############################################################

# core-up starts only the core containers for a local environment.
# It's also possible to start a specific core component by using the
core-up: check-docker-deps aws-docker-login build-mirror-plugins set-docker-env
	$(DOCKER_COMPOSE) up -d zookeeper broker kafdrop postgres db-migration aws-service wiremock kafka-db-sink
	@make wait-core-containers

e2e-core-up: check-docker-deps set-docker-env
	$(DOCKER_COMPOSE) up -d zookeeper broker postgres db-migration aws-service wiremock
	@make wait-core-containers

# core-down stops all core containers that are currently running and clean their
# volumes.
core-down: check-docker-deps
	@$(DOCKER_COMPOSE) down --volumes

# local-up creates a complete local environment from scratch with all
# necessary containers.
# container flag: make core-up container=<container_name>
local-up: check-docker-deps docker-build core-up
	@$(DOCKER_COMPOSE) up -d $(container)

# local-down stops all local environment containers that are currently running.
local-down: core-down

# kafka-mirror mirrors specific topics from a remote Kafka cluster to a local
# Kafka cluster.
# Usage:
# make kafka-mirror topic="<topic_name>" filter="<a list of strings separated by space>" env=(local|staging|live)
kafka-mirror: build-mirror-plugins check-docker-deps core-up set-staging-env set-live-env
	@echo "Running Kafka Mirror. Press Ctrl+C to stop mirroring."
	@docker exec --env-file=.env-$(env) sdf-broker sh -c '/usr/src/kafka-mirror.sh "$(topic)" "$(filter)"'

# kafka-mirror-all mirrors all topics from a remote Kafka cluster to
# a local Kafka cluster.
# make kafka-mirror-all filter="<a list of stings separated by space>" env=(local|staging|live)
kafka-mirror-all: build-mirror-plugins check-docker-deps core-up set-staging-env set-live-env
	@echo "Running Kafka Mirror. Press Ctrl+C to stop mirroring."
	@docker exec --env-file=.env-$(env) sdf-broker sh -c '/usr/src/kafka-mirror.sh "$(ALL_TOPICS)" "$(filter)"'

############################################################
#                      Utilities                           #
############################################################

clean-docker-volumes:
	@docker volume prune

# e.g. make kafkacat topic=<topic_name> env=(local|staging|live) formatter='part-%p;%T;%o;%k\n'(optional)
kafkacat: set-local-env set-staging-env set-live-env
	@./tools/scripts/src/main/shell/kafkacat $(env) $(topic) "$(formatter)"

# e.g. make kafkacat topic=<topic_name> env=(local|staging|live) filePath=<file_path> partition=<partition_id>
# filePath is the path of a file relative to the mounted directory /usr/src/, e.g:
# /usr/src/${filePath}
# partition is optional
# IMPORTANT THIS TASK IS ALSO USED IN UPDATE DC TOPIC GITHUB ACTION. BE CARE WHEN MODIFIED
kafkacat-producer: set-env
	@./tools/scripts/src/main/shell/kafkacat-producer $(env) $(topic) $(filePath) $(partition)

# Consumes protobuf topics and output it in the stdout giving the OPTION to
# filter data with AND and OR, e.g:
# - make proto-consumer bootstrap-servers=localhost:29092 topic=public.sku-demand-forecast.v1beta5 filter-and=1a1b5e01-1326-4521-b774-47584624f78f,VE
# - make proto-consumer bootstrap-servers=localhost:29092 topic=public.sku-demand-forecast.v1beta5 filter-or=1a1b5e01-1326-4521-b774-47584624f78f,VE
# - make proto-consumer bootstrap-servers=localhost:29092 topic=public.sku-demand-forecast.v1beta5 filter-and=2021-01-01 filter-or=1a1b5e01-1326-4521-b774-47584624f78f,VE
proto-consumer: build-scripts
	@java -jar ./tools/scripts/build/libs/scripts-all.jar --bootstrap-servers=$(bootstrap-servers) --topic="$(topic)" --filter-and="$(filter-and)" --filter-or="$(filter-or)"

ps:
	@watch -n 1 $(DOCKER_COMPOSE) ps

# make logs app=<container_name or id>
logs:
	@$(DOCKER_COMPOSE) logs -f $(app)


# make show-lag group=scm.inventory.qa.1617696419 env=live
show-lag:
	@docker exec --env-file=.env-$(env) sdf-broker sh -c \
	'kafka-consumer-groups --command-config=/tmp/consumer.properties --bootstrap-server $$BROKER_ADDRESS --group $(group) --describe' \
	| awk '$$6>0 || $$6=="-" {print $$0}'

run-e2e-test:
	@HF_TIER=local ./gradlew :end-to-end-test:exec

e2e-apps-up:
	@make set-docker-env
	@make docker-build
	@make check-docker-deps
	@make e2e-core-up
	@$(DOCKER_COMPOSE) up --no-recreate -d sdf-api-service sdf-demand-import-job sdf-recipe-import-job sdf-files-consumer sdf-forecaster-job sdf-demand-calculator sdf-demand-anz-consumer

