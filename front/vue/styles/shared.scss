* {
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: #323232;
}

::-webkit-scrollbar-thumb {
  background: #545454;
}

.v-application {
  font-family: $body-font-family, sans-serif !important;
}

.v-application--wrap {
  background-color: $body-bg-dark;
}

html {
  overflow-y: auto !important;
  overscroll-behavior-x: none;
}

.bold {
  font-weight: 500;
}

.rotate-90 {
  transform: rotate(90deg);
}

.theme--dark.v-card {
  background-color: #424242 !important;
}

.cached {
  color: #cecece;
  margin-bottom: 5px;
}

.switch-mode {
  margin-left: 10px;

  label {
    font-size: 14px;
    white-space: nowrap;
  }
  .v-input--selection-controls {
    margin-top: 0;
    margin-left: 12px;
    margin-right: 15px;
  }
}

#app {
  min-height: 100vh;
}

#loading {
  width: 300px;
}

.app-navigation {
  button span {
    font-size: 12px;
    text-transform: capitalize;
    text-shadow: 1px 1px 0 rgba(#000, 0.7);
  }
}

.picker-toolbar {
  padding: 12px 10px 0 0;
}

.picker-select {
  width: 136px;
  margin-left: 10px;

  &--wide {
    width: 180px;
  }

  .v-text-field.v-text-field--solo .v-input__control {
    min-height: 36px;
    font-size: 14px;
  }
}

.multiple-select {
  width: auto;
  overflow: hidden;
  
  .v-select__selections {
    overflow-x: auto;
    flex-wrap: nowrap !important;

    .v-chip {
       flex: 0 0 auto;
    }
  }
}

.btn-site {
  font-weight: normal;
}

#app .menu-group.v-list-group--sub-group {
  .v-list-item__icon:first-child {
    margin-right: 0;
  }

  .v-list-group__header {
    padding-left: 15px;
  }
}

.chip {
  color: #fff;
  border-radius: 10px;
  font-size: 12px;
  padding: 2px 12px;
  white-space: nowrap;
  overflow: hidden;
  background-color: #555;
}

.jobs-badge-dot .v-badge__wrapper {
  margin-top: -5px;

  span {
    height: 8px;
    width: 8px;
    border-radius: 50%;
  }
}

.job-in-progress {
  .v-badge__badge {
    animation: dot 2s infinite;
  }
}

.late-delivery {
  background-color: #fb8c00;
  color: #fff;
}

.custom-filter {
  padding: 0 10px;

  label {
    font-size: 14px;
    font-weight: 500;
  }

  .v-input {
    .v-icon {
      opacity: 0.5;
    }
  }

  .v-input--is-label-active {
    .v-icon {
      opacity: 1;
    }

    .primary--text {
      color: #2196f3 !important;
    }
  }
}

.appointment-filter {
  label {
    color: #ffb222 !important;
  }
}

.po-status-filter {
  padding: 8px 10px;

  .v-input--selection-controls {
    margin: 0 0 6px;
  }
  .ag-high-attention-text label {
    color: var(--ag-high-attention-bg-color) !important;
  }

  .ag-attention-text label {
    color: var(--ag-attention-bg-color) !important;
  }
}

.case-size-mismatch-filter {
  padding: 8px 10px;

  .v-input--selection-controls {
    margin: 0 0 6px;
  }

  .ag-case-size-mismatch-text label {
    color: var(--ag-attention-bg-color) !important;
  }
}

.cell-expired {
  background-color: rgba(148, 21, 21, 0.5);
}

.cell-over-budget {
  background-color: rgb(234, 51, 247);
  color: #fff;
}

.cell-blue {
  background-color: #517fd9;
  color: #fff
}

.cell-orange {
  background-color: #cb7304;
  color: #fff
}

.text-blue {
  color: rgb(121, 157, 228)
}

.v-btn--plain:not(.v-btn--active):not(.v-btn--loading):not(:focus):not(:hover) .v-btn__content {
  opacity: 0.85;
}