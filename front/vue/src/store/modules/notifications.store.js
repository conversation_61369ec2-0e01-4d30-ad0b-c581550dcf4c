export default {
  namespaced: true,
  state: {
    notifications: []
  },
  actions: {
    addNotification({ commit }, payload) {
      const { text, isError = false, isPermanent = false, details } = payload
      const id = Math.random().toString(36)
      const notification = { id, text, isError, details }

      commit('ADD_NOTIFICATION', notification)
      if (!isPermanent) {
        setTimeout(() => commit('REMOVE_NOTIFICATION', notification), 6000)
      }
    },
    removeNotification({ commit }, notification) {
      commit('REMOVE_NOTIFICATION', notification)
    }
  },
  mutations: {
    ADD_NOTIFICATION(state, item) {
      state.notifications.push(item)
    },
    REMOVE_NOTIFICATION(state, item) {
      state.notifications = state.notifications.filter(({ id }) => id !== item.id)
    }
  },
  getters: {
    notifications(state) {
      let bottom = 0
      return state.notifications.map((item, index) => {
        bottom += index === 0 ? 0 : 75

        return {
          ...item,
          style: {
            bottom: `${bottom}px`
          }
        }
      })
    }
  }
}
