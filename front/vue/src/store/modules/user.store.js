import Vue from 'vue'
import { viewStateApi } from '@/api/view-state'
import { permissionsConfig } from '@/config/permissions.config'

const state = {
  user: null,
  userState: {},
  viewStateList: [],
  viewStateHashMap: {}
}

const actions = {
  hasAccess({ getters }, requiredRoles) {
    // note: vuex action returns Promise
    return getters.roles.some((role) => requiredRoles.includes(role))
  },
  async fetchViewStateList({ commit }, type) {
    if (!type) return

    const params = { resource: type }
    const { data } = await viewStateApi.getByType(params)

    commit('putViewStateList', data)
  },
  async fetchViewStateById({ commit }, id) {
    if (!id) return false

    const response = await viewStateApi.getById(id)
    if (!response) return false

    const { data } = response
    if (!data) return false

    const { state: viewState } = data
    commit('putViewState', { id, viewState })
    return true
  },
  async addState({ dispatch }, payload) {
    const { resource } = payload

    try {
      const id = await viewStateApi.add(payload)

      if (id) {
        await dispatch('fetchViewStateList', resource)
        dispatch('Notifications/addNotification', { text: 'State is saved' }, { root: true })
        return id
      }
      return null
    } catch (e) {}
    return null
  },
  async updateState({ dispatch, commit }, payload) {
    const { id, data, resource } = payload

    // cache viewState
    const viewState = data.state
    if (viewState) {
      commit('putViewState', { id, viewState })
    }

    try {
      await viewStateApi.update(id, data)
      await dispatch('fetchViewStateList', resource)
      dispatch('Notifications/addNotification', { text: 'State is updated' }, { root: true })
    } catch (e) {}
  },
  async deleteState({ dispatch }, payload) {
    const { id, resource } = payload

    try {
      await viewStateApi.delete(id)
      await dispatch('fetchViewStateList', resource)
      dispatch('Notifications/addNotification', { text: 'State is deleted' }, { root: true })
    } catch (e) {}
  }
}

const mutations = {
  putUser(state, data) {
    state.user = data
  },
  putViewStateList(state, data) {
    state.viewStateList = data
  },
  putViewState(state, { id, viewState }) {
    Vue.set(state.viewStateHashMap, id, viewState)
  },
  removeViewState(state, id) {
    Vue.delete(state.viewStateHashMap, id)
  }
}

const getters = {
  roles: (state) => {
    return state.user ? state.user.roles : []
  },
  permissions: (state) => {
    return state.user ? state.user.userPermissions.permissions : []
  },
  userAvailableSites: (state) => {
    return (state.user && state.user.userPermissions.availableSites) || []
  },
  userAvailableBrands: (state) => {
    return (state.user && state.user.brands) || []
  },
  userWithoutPermission: (state, getters) =>
    state.user && !getters.permissions.includes(permissionsConfig.general.read),
  userName: (state) => (state.user && state.user.email ? state.user.email.split('@')[0] : '')
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
