import HttpService from '@/services/http.service'
import { buyerResource } from '@/config/resource.config'

export async function getBuyers() {
  try {
    const { data } = await HttpService.get(buyerResource.buyers)

    return data
  } catch (e) {
    return []
  }
}

export async function getBuyerBoards(id, week) {
  const apiUrl = buyerResource.boards(id)
  const params = { week }

  try {
    const { data } = await HttpService.get(apiUrl, params)

    return data
  } catch (e) {
    return {}
  }
}

export async function getBuyerCriticalItems(week) {
  try {
    const { data } = await HttpService.get(buyerResource.buyerCriticalItems, { week })

    return Object.values(data)[0]
  } catch (e) {
    return []
  }
}
