import HttpService from '@/services/http.service'
import { commonResource, imtFormsResource } from '@/config/resource.config'
import { getDefaultApiGetResponse } from '@/utils'

const receiptOverridePath = 'receipt-override'

async function getSKUs(resource, params) {
  const isParams = Object.values(params).every((param) => param)

  if (!isParams) return []

  const url = imtFormsResource.skus(resource)

  try {
    const { data } = await HttpService.get(url, params)

    return data.map((sku, index) => ({ label: sku, value: index }))
  } catch (e) {
    return []
  }
}

async function getPOs(resource, params) {
  const isParams = Object.values(params).every((param) => param)

  if (!isParams) return []

  const url = imtFormsResource.pos(resource)

  try {
    const { data } = await HttpService.get(url, params)

    return data.map((poNumber, index) => ({ label: poNumber, value: index }))
  } catch (e) {
    return []
  }
}

async function getSkusInfo(resource, params, isCode) {
  const isParams = Object.values(params).every((param) => param)

  if (!isParams) return []

  const url = imtFormsResource.skus(resource)

  try {
    const { data } = await HttpService.get(url, params)

    return data.map(({ sku_code: code, sku_name: name }) => ({
      label: (isCode && code) || name,
      value: (isCode && name) || code
    }))
  } catch (e) {
    return []
  }
}

export const manualFormsApi = {
  pullPut: {
    get(params) {
      return HttpService.get(imtFormsResource.pullPut, params).catch(() => false)
    },
    validate(payload) {
      const path = `${imtFormsResource.pullPut}validate/`

      return HttpService.post(path, payload)
    },
    submit(payload) {
      return HttpService.post(imtFormsResource.pullPut, payload)
    },
    edit(payload) {
      return HttpService.patch(imtFormsResource.pullPut, payload)
    },
    deleteRecord(payload) {
      return HttpService.delete(imtFormsResource.pullPut, {}, payload)
    },
    skus(params) {
      return getSKUs('pull-put', params)
    }
  },
  receiptOverride: {
    get(params) {
      return HttpService.get(imtFormsResource.receiptOverride, params).catch(() => false)
    },
    validate(payload) {
      const path = `${imtFormsResource.receiptOverride}validate/`

      return HttpService.post(path, payload)
    },
    submit(payload) {
      return HttpService.post(imtFormsResource.receiptOverride, payload)
    },
    edit(payload) {
      return HttpService.patch(imtFormsResource.receiptOverride, payload)
    },
    deleteRecord(payload) {
      return HttpService.delete(imtFormsResource.receiptOverride, {}, payload)
    },
    skus(week, data) {
      const { brand, dc, poNumber } = data
      const params = {
        week,
        brand,
        dc,
        po_number: poNumber
      }
      return getSkusInfo(receiptOverridePath, params)
    },
    skuCodes(week, data) {
      const { brand, dc, poNumber } = data
      const params = {
        week,
        brand,
        dc,
        po_number: poNumber
      }
      const isCode = true

      return getSkusInfo(receiptOverridePath, params, isCode)
    },
    pos(week, data) {
      const { brand, dc } = data
      const params = {
        week,
        brand,
        dc
      }

      return getPOs(receiptOverridePath, params)
    }
  },
  poVoid: {
    get(params) {
      return HttpService.get(imtFormsResource.poVoid, params).catch(() => false)
    },
    validate(payload) {
      const path = `${imtFormsResource.poVoid}validate/`

      return HttpService.post(path, payload)
    },
    edit(payload) {
      return HttpService.patch(imtFormsResource.poVoid, payload)
    },
    submit(payload) {
      return HttpService.post(imtFormsResource.poVoid, payload)
    },
    deleteRecord(payload) {
      return HttpService.delete(imtFormsResource.poVoid, {}, payload)
    },
    skus(week, data) {
      const { brand, dc, poNumber } = data
      const params = {
        week,
        brand,
        dc,
        po_number: poNumber
      }
      return getSKUs('po-void', params)
    },
    pos(week, data) {
      const { brand, dc } = data
      const params = {
        week,
        brand,
        dc
      }

      return getPOs('po-void', params)
    }
  },
  weekendCoverageChecklist: {
    get(params) {
      return HttpService.get(imtFormsResource.weekendCoverageChecklist, params).catch(() => false)
    },
    edit(id, payload, params) {
      const path = `${imtFormsResource.weekendCoverageChecklist}${id}/`

      return HttpService.patch(path, payload, params)
    },
    deleteRecord(id, params) {
      const path = `${imtFormsResource.weekendCoverageChecklist}${id}/`

      return HttpService.delete(path, params)
    },
    validate(payload, params) {
      const path = `${imtFormsResource.weekendCoverageChecklist}validate/`

      return HttpService.post(path, payload, params)
    },
    submit(payload, params) {
      return HttpService.post(imtFormsResource.weekendCoverageChecklist, payload, params)
    },
    async getCoveragePoNumbers(week) {
      if (!week) return []

      try {
        const { data } = await HttpService.get(commonResource.pos(week))
        return data.map(({ poNumber, shipMethod }, i) => ({
          label: poNumber,
          value: i,
          shipMethod
        }))
      } catch (e) {
        return []
      }
    },
    async getCoverageSKUNames(week, data) {
      const { poNumber } = data

      if (!week || !poNumber) return []

      try {
        const { data } = await HttpService.get(commonResource.skuNames(week, poNumber))

        return data.map((skuName, i) => ({ label: skuName, value: i }))
      } catch (e) {
        return []
      }
    },
    getCoveragePreview(params) {
      return HttpService.get(`${imtFormsResource.weekendCoverageChecklist}preview/`, params).catch(
        () => false
      )
    },
    getCoverageData(params) {
      const { poNumber, skuCode } = params
      const path = `${imtFormsResource.weekendCoverageChecklist}preview/${poNumber}/${skuCode}/`

      return HttpService.get(path).catch(() => false)
    }
  },
  discard: {
    get(params) {
      return HttpService.get(imtFormsResource.discard, params).catch(() => false)
    },
    validate(payload, params) {
      const path = `${imtFormsResource.discard}validate/`

      return HttpService.post(path, payload, params)
    },
    submit(payload, params) {
      return HttpService.post(imtFormsResource.discard, payload, params)
    },
    edit(payload) {
      return HttpService.patch(imtFormsResource.discard, payload)
    },
    deleteRecord(payload) {
      return HttpService.delete(imtFormsResource.discard, {}, payload)
    },
    skus(params) {
      return getSKUs('discard', params)
    }
  }
}
