import HttpService from '@/services/http.service'
import { commonResource } from '@/config/resource.config'
import { getDefaultApiGetResponse } from '@/utils'

export const gsheetsApi = {
  get(params, type) {
    return HttpService.get(commonResource.gsheets(type), params).catch(getDefaultApiGetResponse)
  },
  delete(data, type) {
    return HttpService.delete(commonResource.gsheets(type), {}, data)
  },
  edit(data, type) {
    return HttpService.post(commonResource.gsheets(type), data)
  }
}
