import HttpService from '@/services/http.service'
import { pimtAdminResource } from '@/config/resource.config'
import { getDefaultApiGetResponse } from '@/utils'

export const adminPimtApi = {
  threePWs: {
    get(type) {
      return HttpService.get(pimtAdminResource[type]).catch(getDefaultApiGetResponse)
    },
    submit(type, payload, params) {
      return HttpService.post(pimtAdminResource[type], payload, params)
    },
    edit(partnerCode, payload, params) {
      const path = `${pimtAdminResource.partners}${partnerCode}/`

      return HttpService.patch(path, payload, params)
    },
    delete(partnerCode) {
      const path = `${pimtAdminResource.partners}${partnerCode}/`

      return HttpService.delete(path)
    }
  },
  metrics: {
    get(params) {
      return HttpService.get(pimtAdminResource.metrics, params).catch(getDefaultApiGetResponse)
    }
  }
}
