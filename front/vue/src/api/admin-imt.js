import HttpService from '@/services/http.service'
import { adminResource } from '@/config/resource.config'
import { getDefaultApiGetResponse } from '@/utils'

export const adminImtApi = {
  get(type, params) {
    return HttpService.get(adminResource[type], params).catch(getDefaultApiGetResponse)
  },
  submit(type, payload, params) {
    return HttpService.post(adminResource[type], payload, params)
  },
  delete(type, params) {
    return HttpService.delete(adminResource[type], params)
  },
  getBrandsConfig(week) {
    return HttpService.get(adminResource.brandConfig(week)).catch(getDefaultApiGetResponse)
  },
  createOrUpdateBrandsConfig(week, payload) {
    return HttpService.post(adminResource.brandConfig(week), payload)
  },
  getSupplierNames(params) {
    return HttpService.get(adminResource.supplierNames, params).catch(getDefaultApiGetResponse)
  }
}
