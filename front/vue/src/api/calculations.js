import HttpService from '@/services/http.service'
import { calculationsResource } from '@/config/resource.config'
import { getDefaultApiGetResponse } from '@/utils'

export const calculationsApi = {
  dailyNeed: {
    get(params) {
      return HttpService.get(calculationsResource.expectedDailyNeed, params).catch(
        getDefaultApiGetResponse
      )
    }
  },
  grossNeeds: {
    get(params) {
      return HttpService.get(calculationsResource.perpetualGrossNeeds, params).catch(
        getDefaultApiGetResponse
      )
    }
  }
}
