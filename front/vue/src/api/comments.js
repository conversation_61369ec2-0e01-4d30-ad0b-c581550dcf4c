import HttpService from '@/services/http.service'
import { commentResource } from '@/config/resource.config'

export default {
  common: {
    async getComment(params) {
      const isParams = Object.values(params).every((value) => value)

      if (!isParams) return {}

      const { domain, brand, site, week, resourceId, resourceType } = params

      try {
        const apiUrl = commentResource.comment(domain, resourceType)
        const payload = { brand, sites: site, week, resourceId }
        const { data } = await HttpService.get(apiUrl, payload)

        return data?.[site] || {}
      } catch (e) {
        return {}
      }
    },
    async putComment(params) {
      const { domain, brand, site, week, resourceId, comment, resourceType } = params
      const apiUrl = commentResource.comment(domain, resourceType)
      const payload = { brand, site, week, resourceId, comment }

      return HttpService.post(apiUrl, payload)
    },
    async deleteComment(params) {
      const { domain, brand, site, week, resourceId, resourceType } = params
      const apiUrl = commentResource.comment(domain, resourceType)
      const payload = { brand, site, week, resourceId }

      return HttpService.delete(apiUrl, payload)
    }
  },
  replenishment: {
    async getComment(params) {
      try {
        const { data } = await HttpService.get(commentResource.replenishmentComment, params)

        return data || {}
      } catch (e) {
        return {}
      }
    },
    async putComment(params) {
      return HttpService.post(commentResource.replenishmentComment, params)
    },
    async deleteComment(params) {
      return HttpService.delete(commentResource.replenishmentComment, params)
    },
    async previewComments(params) {
      try {
        const { data } = await HttpService.get(commentResource.replenishmentCommentPreview, params)

        return data || []
      } catch (e) {
        return []
      }
    }
  },
  networkDepl: {
    async getComment(params) {
      try {
        const { data } = await HttpService.get(commentResource.networkDeplComment, params)

        return data || {}
      } catch (e) {
        return {}
      }
    },
    async putComment(params) {
      return HttpService.post(commentResource.networkDeplComment, params)
    },
    async deleteComment(params) {
      return HttpService.delete(commentResource.networkDeplComment, params)
    },
    async previewComments(params) {
      try {
        const { data } = await HttpService.get(commentResource.networkDeplCommentPreview, params)

        return data || []
      } catch (e) {
        return []
      }
    }
  }
}

export const getCommentsLog = async (domain, week, type) => {
  if (!week) return []

  const payload = { week }

  try {
    const apiUrl = commentResource.commentLog(domain, type)
    const { data } = await HttpService.get(apiUrl, payload)

    return data
  } catch (e) {
    return []
  }
}

export const getCommentsPreview = async (domain, params) => {
  const isParams = Object.values(params).every((value) => value)

  if (!isParams) return {}

  const { brand, sites, week, resourceType } = params
  const payload = { brand, week, sites }

  try {
    const apiUrl = commentResource.commentPreview(domain, resourceType)
    const { data } = await HttpService.get(apiUrl, payload)

    return data
  } catch (e) {
    return {}
  }
}
