<template>
  <div>
    <ViewToolbar :title="pageTitle">
      <ReloadButton v-if="hasUpdates" @reload="onReload" />
      <ExportButton :export-title="exportTitle" :grid="grid" />
      <ParallelSync @sync="onSync" />
      <Warnings :items="warnings" />
      <StateControls ref="stateControls" :resource="gridStateName" :grid-params="grid" />
    </ViewToolbar>

    <!-- put here everything you want to hide when Fullscreen mode is on -->
    <div v-show="!isFullScreen" ref="content">
      <FiltersBar>
        <template #left>
          <BrandFilter
            v-model="current.brand"
            :brands="available.brandsWithDetails"
            :disabled="isLoading || !current.week"
          />
          <SiteFilter
            v-model="current.site"
            :sites="available.sites"
            :disabled="isLoading || !current.week"
          />
          <v-btn color="#f44336" dark class="btn-picker ml-2" @click="clearRemakeData">
            <v-icon left> mdi-delete </v-icon>
            Clear Remake Data
          </v-btn>
        </template>
        <template #right>
          <WeekFilter v-model="current.week" />
        </template>
      </FiltersBar>
      <v-container fluid>
        <div v-if="isCached" class="cached">Getting from cache...</div>

        <template v-if="isData && (!isLoading || isEdit)">
          <div class="remake-grid">
            <div class="remake-select">
              <v-select
                id="remake-select"
                v-model="day"
                label=""
                color="white"
                :items="days"
                item-text="text"
                hide-details
                dense
                @change="getData"
              />
            </div>
            <ag-grid-vue
              key="remake-grid"
              class="ag-theme-alpine-dark"
              :style="{ height: remakeGridHeight }"
              :column-defs="remakeScheme"
              :row-data="remakeRows"
              :default-col-def="gridDefaultColDefRemake"
              :grid-options="remakeOptions"
              :process-data-from-clipboard="processDataFromClipboard"
              @grid-ready="onRemakeGridReady"
              @cell-value-changed="onCellValueChanged"
            />
          </div>
        </template>

        <v-progress-linear v-if="isLoading && !isCached" color="primary" indeterminate />
      </v-container>

      <CommentFormDialog ref="commentFormDialog" type="common" />
      <CoverageViewDialog ref="coverageViewDialog" />
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          id="ing-depl-grid"
          key="ing-depl-grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="ingredientDepletionRows"
          :default-col-def="gridDefaultColDef"
          :grid-options="gridOptions"
        />
      </template>

      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import axios from 'axios'
  import { toFormattedMoney } from '@/modules/grid/cells'
  import { setOnlyNumbers, sortAlphaNumeric, transposeMatrix } from '@/modules/grid/helpers'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import FiltersBar, { BrandFilter, SiteFilter, WeekFilter } from '@/components/filters'
  import ParallelSync from '@/components/util/ParallelSync'
  import ReloadButton from '@/components/util/ReloadButton'
  import Warnings from '@/components/util/Warnings'
  import StateControls from '@/components/util/StateControls'
  import ExportButton from '@/components/util/ExportButton'
  import { AgCellWithFlags } from '@/modules/grid/components'
  import { AgAppointmentFilter, AgCaseSizeMismatchFilter } from '@/modules/grid/filters'
  import CommentFormDialog from '@/components/shared/CommentFormDialog'
  import CoverageViewDialog from '@/components/shared/CoverageViewDialog'
  import { AgGridVue } from 'ag-grid-vue'
  import { imtApi } from '@/api/imt'
  import { commentColumnContext } from '@/modules/dashboards'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useGridScheme,
    useGridState,
    useCommonComments,
    useImtDashboardData,
    useLayout,
    usePermissions,
    useCoverageData,
    useReload
  } from '@/hooks'
  import { computed, ref } from 'vue'
  import { permissionsConfig } from '@/config/permissions.config'
  import { imtResource } from '@/config/resource.config'
  import { imtSchemas } from '@/schemas'

  export default {
    components: {
      ParallelSync,
      ReloadButton,
      Warnings,
      StateControls,
      ViewToolbar,
      FiltersBar,
      BrandFilter,
      SiteFilter,
      WeekFilter,
      ExportButton,
      AgGridVue,
      AgCellWithFlags,
      AgAppointmentFilter,
      AgCaseSizeMismatchFilter,
      toFormattedMoney,
      CommentFormDialog,
      CoverageViewDialog
    },
    setup() {
      const { hasPermission } = usePermissions(permissionsConfig.imtRemakeTool.read)
      const { route, refs } = useContext()
      const { current, available, actions } = useFilter({ isDashboard: true })
      const weekdaysConfig = {}
      const days = computed(() => weekdaysConfig[`${current.week}-${current.brand}`])
      const isFactorBrand = computed(() => current.brand === 'FJ')
      const remakeGridHeight = computed(() => (isFactorBrand.value ? '74px' : '142px'))
      const { busOn } = useEventBus()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { grid, defaultGridOptions, defaultDetailGridOptions, defaultGridColDef } = useGrid({
        refs
      })
      const {
        rows,
        warnings,
        isLoading,
        isCached,
        isDataCached,
        resetCache,
        loadDashboardData,
        setData
      } = useImtDashboardData({ current })
      const { loadCommentsByType, mapDashboardRowsAndComments } = useCommonComments({
        grid,
        current,
        available
      })
      const { storePrevGridColState, gridStateName } = useGridState({ route, refs })
      const { mapMasterRowsAndCoverageData, mapDetailRowsAndCoverageData, loadCoverageData } =
        useCoverageData({
          grid,
          current
        })
      const { schemas } = useGridScheme(imtSchemas.remakeTool, {
        current,
        refs
      })
      const isCommentsEditable = hasPermission(permissionsConfig.imtComment.write)
      const readonly = !hasPermission(permissionsConfig.imtRemakeTool.write)
      const day = ref('Wednesday 1')

      async function loadWeekdays() {
        const { week, brand } = current
        const { data } = await imtApi.ingredientsDepletion.weekdays({ week, brand })
        const cacheKey = `${week}-${brand}`

        weekdaysConfig[cacheKey] = Object.values(data)
      }

      async function getData() {
        storePrevGridColState()

        const shouldLoadData = !isDataCached()

        if (!shouldLoadData) return

        isLoading.value = true

        const responses = await Promise.all([
          loadDashboardData(imtResource.remakeTool, {
            week: current.week,
            dc: current.site,
            brand: current.brand,
            day: day.value
          }),
          loadWeekdays(),
          loadCoverageData(current.week)
        ])

        const [{ remake = {}, ingredientDepletion = [] } = {}, coverageData] = responses
        const rowsMappedWithCoverageData = mapMasterRowsAndCoverageData(
          ingredientDepletion,
          coverageData,
          true
        )

        setData({ remake, ingredientDepletion: rowsMappedWithCoverageData })
        isLoading.value = false
      }

      async function getPoStatusDataBySkuCode(inputParams) {
        const { brand, site, week } = this.current
        const {
          data: { sku: skuCode }
        } = inputParams

        const params = {
          week,
          dc: site,
          brand,
          skuCode
        }

        const responses = await Promise.all([
          imtApi.poStatus.getContextualPo(params),
          loadCoverageData(current.week),
          loadCommentsByType('po')
        ])
        const [rows, coverageData, comments] = responses
        const mappedRows = mapDashboardRowsAndComments(rows, comments)
        const mappedRowsAndCoverageData = mapDetailRowsAndCoverageData(mappedRows, coverageData)

        inputParams.successCallback(mappedRowsAndCoverageData)
      }

      const { hasUpdates, onSync, onReload } = useReload({
        syncCb: resetCache,
        reloadCb: getData
      })

      // subscribers
      busOn('filter-changed', getData)

      actions.changeFilter()

      return {
        isFullScreen,
        gridHeight,
        current,
        available,
        defaultGridOptions,
        defaultDetailGridOptions,
        defaultGridColDef,
        grid,
        gridStateName,
        rows,
        day,
        isLoading,
        isCached,
        warnings,
        getPoStatusDataBySkuCode,
        getData,
        setData,
        readonly,
        isCommentsEditable,
        schemas,
        hasUpdates,
        onSync,
        onReload,
        days,
        isFactorBrand,
        remakeGridHeight
      }
    },
    data() {
      return {
        accumulatingData: false,
        isEdit: false
      }
    },
    computed: {
      pageTitle() {
        return `Remake Tool ${this.current.week} ${this.current.site} ${this.current.brand}`
      },
      exportTitle() {
        const [, weekNumber] = this.current.week.split('-')

        return `RemakeTool_${weekNumber}_${this.current.site}_${this.current.brand}`
      },
      remakeOptions() {
        return {
          ...this.defaultGridOptions,
          suppressContextMenu: true,
          headerHeight: 30,
          statusBar: null
        }
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          masterDetail: true,
          detailCellRendererParams: {
            detailGridOptions: {
              ...this.defaultDetailGridOptions,
              defaultColDef: this.defaultGridColDef,
              columnDefs: this.schemas.poStatus,
              getContextMenuItems: (params) =>
                commentColumnContext({
                  params,
                  filter: this.current,
                  dialog: this.$refs.commentFormDialog,
                  extraParams: {
                    prohibitedTypes: ['sku']
                  },
                  isCommentsEditable: this.isCommentsEditable
                })
            },
            getDetailRowData: this.getPoStatusDataBySkuCode.bind(this)
          }
        }
      },
      gridDefaultColDefRemake() {
        return {
          ...this.defaultGridColDef,
          editable: !this.readonly
        }
      },
      gridDefaultColDef() {
        return {
          ...this.defaultGridColDef
        }
      },
      currentData() {
        return this.rows
      },
      ingredientDepletionRows() {
        return (this.currentData && this.currentData.ingredientDepletion) || []
      },
      remakeRows() {
        if (!this.currentData || !this.currentData.remake) return []

        return this.isFactorBrand
          ? [this.populateEntries('Remakes')]
          : [this.populateEntries('2P'), this.populateEntries('4P'), this.populateEntries('6P')]
      },
      remakeScheme() {
        return this.currentData && this.currentData.remake
          ? [
              {
                headerName: '',
                field: 'first',
                pinned: 'left',
                width: 162,
                minWidth: 162,
                cellClass: 'grid-main-column',
                suppressHeaderMenuButton: true,
                resizable: false,
                editable: false,
                sortable: false,
                filter: false
              },
              ...Object.keys(this.currentData.remake)
                .sort(sortAlphaNumeric)
                .map((name) => {
                  return {
                    field: name,
                    colId: 'remake_tool_col' + name,
                    minWidth: 70,
                    suppressHeaderMenuButton: true,
                    sortable: false,
                    resizable: true,
                    filter: false,
                    valueSetter(params) {
                      setOnlyNumbers(params, { onlyPositive: false, zeroOnEmpty: true })
                    }
                  }
                })
            ]
          : []
      },
      // ------------
      // overwrite mixin computed props
      isRequiredParamsExist() {
        return this.current.brand && this.current.week && this.current.site && this.day
      },
      isData() {
        return (
          this.remakeRows.length && this.remakeScheme.length && this.ingredientDepletionRows.length
        )
      }
      // ------------
      // end overwriting
    },
    methods: {
      clearRemakeData() {
        Object.entries(this.currentData.remake).forEach(([key, value]) => {
          value.picks_2p = 0
          value.picks_4p = 0
          value.picks_6p = 0
        })
        this.sendData()
      },
      populateEntries(rowName) {
        const obj = {
          first: rowName
        }

        Object.entries(this.currentData.remake).forEach(([key, value]) => {
          const rowKey = `picks_${this.isFactorBrand ? '2p' : rowName.toLowerCase()}`

          obj[key] = value[rowKey]
        })

        return obj
      },
      processDataFromClipboard(params) {
        const { data } = params
        return data.length < 3 ? data : transposeMatrix(data)
      },
      onRemakeGridReady(params) {
        params.api.sizeColumnsToFit()
      },
      onCellValueChanged(params) {
        params.api?.autoSizeColumns([params.column.colId])

        if (this.accumulatingData) return

        this.waitAndSendData()
        this.accumulatingData = true
      },
      async waitAndSendData() {
        const accumulatingTimeout = 500

        return setTimeout(this.sendData.bind(this), accumulatingTimeout)
      },
      async sendData() {
        this.accumulatingData = false

        if (!this.isRequiredParamsExist) return

        const sendData = this.prepareDataToSend()
        this.isLoading = true
        this.isEdit = true

        try {
          const response = await axios.post(imtResource.remakeTool, sendData, {
            params: {
              week: this.current.week,
              dc: this.current.site,
              brand: this.current.brand,
              day: this.day
            }
          })

          this.setData(response.data.data[this.current.site])
        } catch (e) {}

        this.isLoading = false
        this.isEdit = false
      },
      prepareDataToSend() {
        return Object.keys(this.currentData.remake).reduce((acc, key) => {
          const key2P = 'picks_2p'
          const key4P = 'picks_4p'
          const key6P = 'picks_6p'
          const value2P = this.remakeRows[0][key]
          const value4P = this.remakeRows[1]?.[key] || 0
          const value6P = this.remakeRows[2]?.[key] || 0

          acc[key] = {
            [key2P]: value2P,
            [key4P]: value4P,
            [key6P]: value6P
          }

          return acc
        }, {})
      }
    }
  }
</script>

<style lang="scss">
  .remake-grid {
    position: relative;
  }

  .remake-select {
    width: 150px;
    position: absolute;
    left: 10px;
    top: -4px;
    z-index: 2;

    .v-input__slot {
      &:before,
      &:after {
        display: none;
      }
    }
  }
</style>
