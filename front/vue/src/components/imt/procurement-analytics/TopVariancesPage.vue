<template>
  <div class="wrapper">
    <ViewToolbar :title="pageTitle">
      <ReloadButton v-if="hasUpdates" @reload="onReload" />
      <ExportButton :export-title="exportTitle" :excel-tab-title="excelTabTitle" :grid="grid" />
      <ParallelSync @sync="onSync" />
      <Warnings :items="warnings" />
      <v-tooltip bottom color="black">
        <template #activator="{ on }">
          <v-btn
            id="expandCollapseRows"
            height="38"
            width="42"
            icon
            tile
            :disabled="!isData"
            @click="expandCollapseRows()"
            v-on="on"
          >
            <v-icon size="20">
              {{ isRowsExpanded ? 'mdi-collapse-all-outline' : 'mdi-expand-all-outline' }}
            </v-icon>
          </v-btn>
        </template>
        <span>{{ isRowsExpanded ? 'Collapse Rows' : 'Expand Rows' }}</span>
      </v-tooltip>
      <StateControls
        ref="stateControls"
        :resource="gridStateName"
        :grid-params="grid"
        :add-reset-filter-btn="false"
      />
      <FilterOptions v-model="available.filterOptions" :grid-params="grid" />
    </ViewToolbar>

    <!-- put here everything you want to hide when Fullscreen mode is on -->
    <div v-show="!isFullScreen" ref="content">
      <FiltersBar>
        <template #left>
          <BrandFilter
            v-model="current.brand"
            :brands="available.brandsWithDetails"
            :disabled="consolidatedFilter.isConsolidatedView || isLoading || !current.week"
          />
          <SiteFilter
            v-model="current.site"
            :sites="available.sites"
            :disabled="consolidatedFilter.isConsolidatedView || isLoading || !current.week"
          />
          <ConsolidatedFilter
            v-model="consolidatedFilter.consolidatedBy.brands"
            label="Brand Consolidated View"
            :disabled="consolidatedFilter.consolidatedBy.sites || !current.week"
          />
          <ConsolidatedFilter
            v-model="consolidatedFilter.consolidatedBy.sites"
            label="Site Consolidated View"
            :disabled="consolidatedFilter.consolidatedBy.brands || !current.week"
          />
        </template>
        <template #right>
          <WeekFilter v-model="current.week" />
        </template>
      </FiltersBar>
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <div v-if="isCached" class="cached">Getting from cache...</div>
      <v-progress-linear v-if="isLoading && !isCached" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
          @cell-value-changed="updateComment"
        />
      </template>

      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>

    <ContextualPoStatusDialog ref="contextualPoStatusDialog" />
  </div>
</template>

<script>
  import { nextTick, ref } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import FiltersBar, {
    BrandFilter,
    SiteFilter,
    WeekFilter,
    ConsolidatedFilter
  } from '@/components/filters'
  import ParallelSync from '@/components/util/ParallelSync'
  import ReloadButton from '@/components/util/ReloadButton'
  import Warnings from '@/components/util/Warnings'
  import StateControls from '@/components/util/StateControls'
  import FilterOptions from '@/components/util/FilterOptions'
  import ExportButton from '@/components/util/ExportButton'
  import { AgLogTooltip, AgChevron } from '@/modules/grid/components'
  import ContextualPoStatusDialog from '@/components/shared/ContextualPoStatusDialog.vue'
  import { imtResource } from '@/config/resource.config'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useGridScheme,
    useGridState,
    useImtDashboardData,
    useLayout,
    useConsolidatedFilter,
    useReload,
    useNotifications
  } from '@/hooks'
  import { imtSchemas } from '@/schemas'
  import { cellWidths } from '@/modules/grid/constants'
  import { imtApi } from '@/api/imt'

  export default {
    components: {
      AgGridVue,
      AgLogTooltip,
      AgChevron,
      ContextualPoStatusDialog,
      ParallelSync,
      ReloadButton,
      Warnings,
      StateControls,
      FilterOptions,
      ViewToolbar,
      FiltersBar,
      BrandFilter,
      SiteFilter,
      WeekFilter,
      ConsolidatedFilter,
      ExportButton
    },
    setup() {
      const { route, refs } = useContext()
      const { addNotification } = useNotifications()
      const { busOn } = useEventBus()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { current, available, actions } = useFilter({
        isDashboard: true
      })
      const consolidatedFilter = useConsolidatedFilter()
      const { grid, defaultGridOptions, defaultGridColDef, getTreeModeColDef } = useGrid({
        refs
      })
      const { storePrevGridColState, gridStateName } = useGridState({ route, refs })
      const {
        rows,
        warnings,
        isData,
        isLoading,
        isCached,
        isDataCached,
        resetCache,
        loadDashboardData,
        setData
      } = useImtDashboardData({ current, consolidatedFilter })

      const { schemas } = useGridScheme(imtSchemas.topVariances, {
        rows,
        isData,
        consolidatedFilter,
        getPoStatusData
      })
      const isRowsExpanded = ref(false)
      const collapseRowsAfterFilterReset = ref(false)

      function populateTreeData(rows) {
        return rows.reduce((acc, cur, parentIndex) => {
          const { skuCode, skuName: path, pos, site, brand } = cur
          const isParentOfGroup = pos?.length
          const parentKey = `${path}-${skuCode}-${site}-${brand}`

          acc.push({
            ...cur,
            id: parentIndex,
            isParent: isParentOfGroup,
            path: consolidatedFilter.isConsolidatedView ? [parentKey] : [path]
          })

          if (isParentOfGroup) {
            pos.forEach((item, childIndex) => {
              const childKey = `${item.skuCode}-${item.poNumber}-${site}-${brand}`

              acc.push({
                ...item,
                path: consolidatedFilter.isConsolidatedView
                  ? [parentKey, childKey]
                  : [path, childKey],
                site,
                brand,
                isParent: false,
                id: `${parentIndex}_${childIndex}`
              })
            })
          }

          return acc
        }, [])
      }

      async function getData() {
        storePrevGridColState()
        isRowsExpanded.value = false

        const shouldLoadData = !isDataCached()

        if (!shouldLoadData) return

        isLoading.value = true
        const dashboardData = await loadDashboardData(imtResource.topVariances)
        const rowsToRender = populateTreeData(dashboardData)

        setData(rowsToRender)

        warnings.value && warnings.value.forEach((text) => addNotification({ text, isError: true }))
        nextTick(() => (isLoading.value = false))
      }

      function expandCollapseRows() {
        if (!grid && !grid.value) return

        if (!isRowsExpanded.value) {
          grid.value.api.expandAll()
        } else {
          grid.value.api.collapseAll()
        }

        isRowsExpanded.value = !isRowsExpanded.value
      }

      async function updateComment(event) {
        const { data, node } = event
        const isChildRow = !!node.level

        const params = {
          week: current.week,
          brand: data.brand,
          dc: data.site
        }

        const body = {
          skuCode: data.skuCode,
          poNumber: data.poNumber.includes(' ') ? null : data.poNumber,
          comment: data.comment.trim()
        }

        try {
          const commentData = await imtApi.topVariances.editComment({ body, params })
          const updatedRow = { ...data, ...commentData }

          if (isChildRow) {
            grid.value.api.applyTransaction({
              update: [updatedRow]
            })
          } else {
            node.setData(updatedRow)
          }
          addNotification({ text: 'The record is updated' })
        } catch (e) {
          undoComment(event)
          throw e
        }
      }

      function undoComment(params) {
        const { data, oldValue } = params
        data.comment = oldValue

        grid.value.api.refreshCells({ force: true })
      }

      function getPoStatusData(inputParams) {
        const {
          node: {
            data: { skuCode, site, brand: _brand }
          }
        } = inputParams
        const dc = consolidatedFilter.isConsolidatedView ? site : current.site
        const brand = consolidatedFilter.isConsolidatedView ? _brand : current.brand
        const params = {
          week: current.week,
          dc,
          brand,
          skuCode,
          includeBulkSkus: true,
          includeOrgCvSkus: true,
          includePackagedSkus: true
        }

        refs.contextualPoStatusDialog.open({
          params,
          url: imtResource.poStatus
        })
      }

      const { hasUpdates, onSync, onReload } = useReload({
        syncCb: resetCache,
        reloadCb: getData
      })

      // subscribers
      busOn('filter-changed', getData)
      actions.changeFilter()

      return {
        isFullScreen,
        gridHeight,
        current,
        available,
        defaultGridOptions,
        defaultGridColDef,
        getTreeModeColDef,
        grid,
        gridStateName,
        rows,
        isData,
        isLoading,
        isCached,
        warnings,
        isRowsExpanded,
        expandCollapseRows,
        schemas,
        consolidatedFilter,
        hasUpdates,
        onSync,
        onReload,
        updateComment
      }
    },
    computed: {
      pageTitle() {
        return `Top Variances View ${this.current.week}`
      },
      excelTabTitle() {
        return `TopVariances${
          this.consolidatedFilter.isConsolidatedView ? 'Consolidated' : `${this.current.site}`
        }`
      },
      exportTitle() {
        const [, weekNumber] = this.current.week.split('-')

        return `TopVariances_${weekNumber}_${
          this.consolidatedFilter.isConsolidatedView
            ? 'Consolidated'
            : `${this.current.site}_${this.current.brand}`
        }`
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          treeData: true,
          getDataPath: (data) => data.path,
          getRowId: ({ data: { id } }) => id,
          autoGroupColumnDef: {
            headerName: 'Purchase Order #',
            field: 'poNumber',
            cellClass: ({ data }) => ['text-left', data.pos && !data.pos.length && 'text-blue'],
            width: cellWidths.xl,
            pinned: 'left'
          }
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef,
          ...this.getTreeModeColDef(this.available.filterOptions)
        }
      }
    }
  }
</script>

<style lang="scss">
  .wrapper {
    .ag-row-group {
      .ag-group-value {
        font-weight: bold;
      }
    }

    .ag-row-group-leaf-indent {
      margin-left: 0px !important;
    }

    .ag-cell-last-left-pinned {
      .ag-row-group-indent-1 {
        padding-left: 0 !important;
      }
    }
    .ag-header-cell {
      border: 0 !important;
    }
  }
</style>
