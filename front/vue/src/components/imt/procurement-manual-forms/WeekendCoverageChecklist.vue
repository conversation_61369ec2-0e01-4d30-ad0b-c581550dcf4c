<template>
  <div class="form-page">
    <ViewToolbar :title="pageTitle" />
    <div v-show="!isFullScreen" ref="content">
      <FiltersBar>
        <template #left>
          <v-btn
            v-if="!isAddMode && !readonly && !isLoading"
            color="primary"
            dark
            class="btn-picker"
            :disabled="!current.week"
            @click="onAddMode"
          >
            <v-icon left> mdi-plus </v-icon>
            Add Records
          </v-btn>

          <v-btn v-if="isAddMode" color="grey" dark class="btn-picker" @click="turnOffAddMode">
            <v-icon left> mdi-close </v-icon>
            Cancel
          </v-btn>

          <v-btn
            v-if="isAddMode"
            :disabled="!records.length"
            :loading="isValidating"
            color="warning"
            dark
            class="btn-picker"
            @click="validate"
          >
            <v-icon left> mdi-check </v-icon>
            Validate Records
          </v-btn>
        </template>
        <template #right>
          <WeekFilter v-model="current.week" :disabled="isAddMode" />
        </template>
      </FiltersBar>
    </div>

    <SubmitDialog
      :is-dialog="isValidationDialogOpen"
      :error-obj="errorObj"
      :records="records"
      :is-records-validated="isRecordsValidated"
      @submit="submit"
      @close="closeValidationDialog"
    />

    <!-- put here only grid -->
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isAddMode && !isLoading">
        <ag-grid-vue
          :key="1"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
          @cell-value-changed="edit"
        />
      </template>
      <template v-if="isAddMode">
        <ag-grid-vue
          :key="2"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.addRecords"
          :row-data="addRecordsRows"
          :default-col-def="addRecordsColDef"
          :grid-options="addRecordsGridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading && !isAddMode" dense color="secondary">
        No data
      </v-alert>
    </v-container>

    <ContextualPoStatusDialog ref="contextualPoStatusDialog" />
    <ContextualTicketsDialog ref="contextualTicketsDialog" />
  </div>
</template>

<script>
  import { ref, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import {
    AgFormTooltip,
    AgRowActions,
    AgDateInput,
    AgAutocomplete,
    AgTicket
  } from '@/modules/grid/components'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import FiltersBar, { WeekFilter } from '@/components/filters'
  import SubmitDialog from '@/components/shared/FormSubmitDialog'
  import ContextualPoStatusDialog from '@/components/shared/ContextualPoStatusDialog.vue'
  import ContextualTicketsDialog from '@/components/shared/ContextualTicketsDialog.vue'
  import {
    useEventBus,
    useLayout,
    useFilter,
    useContext,
    useGrid,
    useManualForms,
    usePermissions,
    useGridScheme,
    useNotifications,
    useIcsTickets
  } from '@/hooks'
  import { permissionsConfig } from '@/config/permissions.config'
  import { imtFormsResource } from '@/config/resource.config'
  import { manualFormsApi } from '@/api/manual-forms'
  import { getManualFormsContextMenuItems } from '@/modules/dashboards'
  import { defaultWeekendCoverageRecord } from '@/modules/procurement-forms'
  import { resizeColumnsIfNeeded } from '@/modules/grid/helpers'
  import { formsSchemas } from '@/schemas'
  import { debounce } from '@/utils'

  export default {
    components: {
      ViewToolbar,
      AgGridVue,
      AgFormTooltip,
      AgDateInput,
      AgTicket,
      AgRowActions,
      FiltersBar,
      WeekFilter,
      SubmitDialog,
      ContextualPoStatusDialog,
      ContextualTicketsDialog,
      AgAutocomplete
    },
    setup() {
      const { hasPermission } = usePermissions(permissionsConfig.weekendCoverageChecklist.read)
      const { addNotification } = useNotifications()
      const { refs, route } = useContext()
      const { busOn } = useEventBus()
      const { gridHeight, isFullScreen } = useLayout(refs)
      const { current, available, actions: filterActions } = useFilter({ isDashboard: true })
      const { grid, defaultGridOptions, defaultGridColDef, scrollToNodeByQueryParams } = useGrid({
        refs
      })
      const {
        isAddMode,
        isValidating,
        records,
        isRecordsValidated,
        isValidationDialogOpen,
        addRecordsRows,
        errorObj,
        turnOnAddMode,
        turnOffAddMode,
        handleErrors,
        clearErrors,
        closeValidationDialog,
        onCellKeyDown
      } = useManualForms(defaultWeekendCoverageRecord, grid)
      const isLoading = ref(false)
      const readonlyWeek = ref(false)
      const rows = ref([])
      const isData = computed(() => rows.value.length)
      const readonly = computed(
        () => readonlyWeek.value || !hasPermission(permissionsConfig.weekendCoverageChecklist.write)
      )
      const poNumbers = ref([])
      const actions = [{ tooltipName: 'Delete', callback: deleteRecord, icon: 'mdi-delete' }]
      const {
        weekendCoverageChecklist: { getCoverageSKUNames }
      } = manualFormsApi

      const { schemas } = useGridScheme(formsSchemas.weekendCoverageChecklist, {
        readonly,
        current,
        actions,
        poNumbers,
        getCoverageSKUNames,
        getPoStatusData,
        getTicketsData
      })

      const { loadTickets, mapRowsAndIcsTickets } = useIcsTickets({
        available,
        current,
        getPreviewForAllBrands: true
      })

      async function getData(silentReload = false) {
        if (!current.week) return

        const params = {
          week: current.week
        }
        isLoading.value = !silentReload

        const responses = await Promise.all([
          manualFormsApi.weekendCoverageChecklist.get(params),
          loadTickets()
        ])

        const [{ data: formData }, ticketsData] = responses

        if (!formData) {
          isLoading.value = false
          addNotification({ text: 'Server Error', isError: true })
          return
        }

        const mappedRowsWithTicketsData = mapRowsAndIcsTickets(formData.data, ticketsData)

        readonlyWeek.value = formData.readonlyWeek
        rows.value = mappedRowsWithTicketsData
        isLoading.value = false
      }

      async function getPoNumbers() {
        if (!current.week) return

        const {
          weekendCoverageChecklist: { getCoveragePoNumbers }
        } = manualFormsApi

        poNumbers.value = await getCoveragePoNumbers(current.week)
      }

      function getPoStatusData({
        node: {
          data: { poNumber, skuCode }
        }
      }) {
        const params = { poNumber, skuCode }

        refs.contextualPoStatusDialog.open({
          params,
          url: imtFormsResource.weekendCoverageChecklistPoStatus
        })
      }

      function getTicketsData({
        node: {
          data: { poNumber, brand, site, skuCode }
        }
      }) {
        const params = {
          week: current.week,
          poNumber,
          skuCode,
          brand,
          sites: site
        }

        refs.contextualTicketsDialog.open(params)
      }

      function onAddMode() {
        turnOnAddMode()
        getPoNumbers()
      }

      function buildRequestData() {
        return records.value.map((record, rowId) => {
          const {
            poNumber = '',
            skuName,
            poLandingDay,
            productionDayAffected,
            toCheck,
            contactNameVendorCarrier,
            emailPhone,
            backUpVendor,
            fobPickUpDate
          } = record

          return {
            rowId,
            poNumber,
            skuName,
            poLandingDay,
            productionDayAffected,
            toCheck,
            contactNameVendorCarrier,
            emailPhone,
            backUpVendor,
            fobPickUpDate
          }
        })
      }

      async function validate() {
        grid.value.api.stopEditing()
        grid.value.api.clearRangeSelection()

        const params = { week: current.week }
        const data = buildRequestData()

        isValidating.value = true
        clearErrors()

        try {
          await manualFormsApi.weekendCoverageChecklist.validate(data, params)

          isRecordsValidated.value = true
          isValidationDialogOpen.value = true
        } catch (e) {
          const response = e.response.data
          const isRecordsErrors = response.data

          if (isRecordsErrors) {
            handleErrors(response)
          } else {
            throw e
          }
        } finally {
          isValidating.value = false
        }
      }

      async function edit(props) {
        const {
          data: { id },
          colDef: { field },
          newValue,
          oldValue
        } = props

        if (newValue === oldValue) {
          return
        }

        const params = { week: current.week }
        const payload = {
          [field]: newValue || null
        }

        try {
          await manualFormsApi.weekendCoverageChecklist.edit(id, payload, params)
          await getData(true)
          addNotification({ text: 'The record is updated' })
        } catch (e) {
          getData(true)
        }
      }

      async function submit() {
        const params = { week: current.week }
        const payload = buildRequestData()

        try {
          await manualFormsApi.weekendCoverageChecklist.submit(payload, params)
          addNotification({ text: 'Coverage checklist item has been successfully submitted' })
          isValidationDialogOpen.value = false
          isAddMode.value = false
          getData()
        } catch (e) {
          const response = e.response.data
          const isRecordsErrors = response.data

          if (isRecordsErrors) {
            handleErrors(response)
          } else {
            throw e
          }
        } finally {
          isRecordsValidated.value = false
        }
      }

      async function deleteRecord(_, record) {
        if (confirm('Are you sure you want to delete this item?')) {
          const { id } = record
          const params = { week: current.week }

          try {
            await manualFormsApi.weekendCoverageChecklist.deleteRecord(id, params)
            rows.value = rows.value.filter((row) => row.id !== id)
            addNotification({ text: 'The record has been successfully deleted' })
          } catch (e) {}
        }
      }

      // subscribers
      busOn('grid-ready', () => {
        const { query } = route

        resizeColumnsIfNeeded(grid)
        scrollToNodeByQueryParams(query)
      })
      busOn('filter-changed', () => {
        isAddMode.value ? getPoNumbers() : getData()
      })

      busOn(
        'grid-size-changed',
        debounce(() => isAddMode.value && resizeColumnsIfNeeded(grid))
      )

      filterActions.changeFilter()

      return {
        grid,
        gridHeight,
        isFullScreen,
        current,
        available,
        isLoading,
        rows,
        getData,
        isData,
        defaultGridOptions,
        defaultGridColDef,
        isAddMode,
        isValidating,
        records,
        isRecordsValidated,
        isValidationDialogOpen,
        addRecordsRows,
        errorObj,
        onAddMode,
        turnOffAddMode,
        closeValidationDialog,
        schemas,
        readonly,
        onCellKeyDown,
        validate,
        edit,
        submit
      }
    },
    computed: {
      pageTitle() {
        return `Weekend Coverage Checklist ${this.readonly ? '(View Only)' : ''}`
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          tooltipShowDelay: 0,
          tooltipMouseTrack: true,
          getContextMenuItems: (params) => getManualFormsContextMenuItems(params),
          statusBar: null
        }
      },
      addRecordsGridOptions() {
        return {
          ...this.defaultGridOptions,
          tooltipShowDelay: 0,
          tooltipMouseTrack: true,
          onCellKeyDown: this.onCellKeyDown.bind(this),
          getContextMenuItems: (params) => getManualFormsContextMenuItems(params),
          statusBar: null,
          rowClassRules: {
            'ag-row-error': ({ data }) => data.error
          }
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      },
      addRecordsColDef() {
        return {
          ...this.defaultGridColDef,
          editable: true,
          tooltipComponent: 'AgFormTooltip',
          suppressHeaderMenuButton: true,
          sortable: false,
          tooltipValueGetter: ({ value }) => ({
            value
          }),
          cellClass: (params) => {
            const {
              data: { error },
              colDef: { field }
            } = params

            return error && error[field] ? 'form-cell-error' : ''
          }
        }
      }
    }
  }
</script>

<style lang="scss">
  .btn-picker {
    margin-left: 12px;
  }
</style>
