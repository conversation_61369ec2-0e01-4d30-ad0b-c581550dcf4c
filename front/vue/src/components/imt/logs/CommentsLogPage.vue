<template>
  <div class="form-page">
    <div ref="content">
      <ViewToolbar :title="pageTitle" />
      <FiltersBar>
        <template #left>
          <CommentLogFilter v-model="current.commentLogType" :types="available.commentLogTypes" />
        </template>
        <template #right>
          <WeekFilter v-model="current.week" :highlight-week="false" />
        </template>
      </FiltersBar>
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          :key="1"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="scheme"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  const mainDashboardPath = '/imt/dashboards/po-status-view'

  import { AgGridVue } from 'ag-grid-vue'
  import { AgLogTooltip } from '@/modules/grid/components'
  import FiltersBar, { WeekFilter, CommentLogFilter } from '@/components/filters'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import {
    useFilter,
    useContext,
    useGrid,
    useLayout,
    useEventBus,
    useImtDashboardData,
    usePermissions,
    useGridScheme
  } from '@/hooks'
  import { permissionsConfig } from '@/config/permissions.config'
  import { getCommentsLog } from '@/api/comments'
  import { getDefaultContextMenuItems, getPoCommentLogContext } from '@/modules/dashboards'
  import { imtSchemas } from '@/schemas'
  import { debounce } from '@/utils'
  import { resizeColumnsIfNeeded } from '@/modules/grid/helpers'

  export default {
    name: 'PoCommentsLog',
    components: {
      FiltersBar,
      CommentLogFilter,
      WeekFilter,
      ViewToolbar,
      AgGridVue,
      AgLogTooltip
    },
    setup() {
      usePermissions(permissionsConfig.imtCommentLog.read)
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { current, available } = useFilter({ isDashboard: true, isWeekConfigNeeded: false })
      const { defaultGridOptions, defaultGridColDef, grid } = useGrid({ refs })
      const { gridHeight } = useLayout(refs)
      const { rows, isData, isLoading, setData } = useImtDashboardData({ current })
      const { schemas } = useGridScheme(imtSchemas.commentsLog)

      async function getData() {
        if (!current.week) return

        isLoading.value = true

        const logsData =
          current.commentLogType === 'PO'
            ? await getCommentsLog('imt', current.week, 'po')
            : await getCommentsLog('imt', current.week, 'sku')

        setData(logsData)

        isLoading.value = false
      }

      getData()
      busOn('grid-ready', () => resizeColumnsIfNeeded(grid))
      busOn(
        'grid-size-changed',
        debounce(() => resizeColumnsIfNeeded(grid))
      )
      busOn('filter-changed', getData)

      return {
        current,
        available,
        defaultGridOptions,
        defaultGridColDef,
        gridHeight,
        isLoading,
        isData,
        rows,
        schemas
      }
    },
    computed: {
      pageTitle() {
        return `${this.isPoCommentsLog ? 'PO' : 'SKU'} Comments Log`
      },
      isPoCommentsLog() {
        return this.current.commentLogType === 'PO'
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          tooltipShowDelay: 0,
          tooltipMouseTrack: true,
          getContextMenuItems: (params) => {
            if (this.isPoCommentsLog) {
              return getPoCommentLogContext(params, this.current.week, mainDashboardPath)
            } else return getDefaultContextMenuItems(params)
          }
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      },
      scheme() {
        return this.isPoCommentsLog ? this.schemas.poLogsScheme : this.schemas.skuLogsScheme
      }
    }
  }
</script>
