<template>
  <div class="release-card">
    <div class="release-date">
      Release Date: <strong>{{ item.releaseDate }}</strong>
    </div>
    <div :class="{ 'content-with-actions': showActions }">
      <div class="release-group" v-for="group in groups" :key="group.title">
        <div class="release-group-title">
          {{ group.title }}
        </div>
        <ul>
          <li v-for="(feature, index) in group.features" :key="index">
            <div v-html="feature" class="release-feature" />
          </li>
        </ul>
      </div>
      <div v-if="showActions" class="release-actions">
        <v-tooltip slot="append" top>
          <template #activator="{ on }">
            <v-btn v-on="on" @click="$emit('edit')" color="primary" class="mx-2" fab dark small>
              <v-icon dark> mdi-pencil </v-icon>
            </v-btn>
          </template>
          <span>Edit</span>
        </v-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
  import { computed, toRefs } from 'vue'
  import { featureTypesMap } from '@/components/admin/logs/featureTypes'

  export default {
    props: {
      item: {
        type: Object,
        required: true
      },
      showActions: {
        type: Boolean,
        default: false
      },
      isDcUser: {
        type: Boolean,
        default: false
      }
    },
    setup(props) {
      const { item, isDcUser } = toRefs(props)
      const groupsMap = computed(() =>
        item.value.features.reduce((acc, cur) => {
          const { dcUsersAvailable, description, type } = cur

          if (isDcUser.value && !dcUsersAvailable) return acc
          if (acc[type]) {
            acc[type].features.push(description)
          } else {
            acc[type] = {
              title: featureTypesMap[type].name,
              order: featureTypesMap[type].index,
              features: [description]
            }
          }

          return acc
        }, {})
      )
      const groups = computed(() =>
        Object.values(groupsMap.value).sort((a, b) => a.order - b.order)
      )

      return {
        groups
      }
    }
  }
</script>

<style lang="scss" scoped>
  .release-card {
    background-color: #222;
    border-radius: 5px;
    padding: 16px 16px 24px 16px;
  }

  .release-date {
    font-size: 12px;
    text-align: right;
  }

  .content-with-actions {
    position: relative;
    padding-right: 80px;
  }

  .release-group {
    & + & {
      margin-top: 20px;
    }

    &-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }

  .release-actions {
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
  }
</style>

<style lang="scss">
  .release-feature p {
    margin: 0;
    line-height: 20px;
    font-size: 14px;
    min-height: 20px;
  }

  .release-log-compact {
    .release-card {
      padding: 15px 15px 22px 15px;
    }

    .release-feature p {
      font-size: 12px;
      color: #aaa;
    }

    .release-group-title {
      font-size: 12px;
    }

    ul li::marker {
      color: #aaa !important;
    }

    code {
      color: #eee !important;
    }
  }
</style>
