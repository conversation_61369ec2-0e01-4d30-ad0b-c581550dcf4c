<template>
  <v-dialog v-model="isDialog">
    <v-card>
      <!-- put here only grid -->
      <v-container fluid>
        <v-progress-linear v-if="isLoading" indeterminate color="primary" />
        <template v-if="!isLoading">
          <ag-grid-vue
            ref="$grid"
            class="ag-theme-alpine-dark"
            :column-defs="schemas.main"
            :style="{ height: gridHeight }"
            :row-data="rows"
            :default-col-def="gridColDef"
            :grid-options="gridOptions"
          />
        </template>
      </v-container>
      <v-card-actions ref="content">
        <v-spacer />
        <v-btn color="grey" @click="close"> Close </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import { ref, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import { useEventBus, useContext, useGrid, useGridScheme } from '@/hooks'
  import { inventorySchemas } from '@/schemas'
  import { inventoryApi } from '@/api/inventory'
  import { inventoryResource } from '@/config/resource.config'
  import { getDefaultContextMenuItems } from '@/modules/dashboards'

  export default {
    components: {
      AgGridVue
    },
    setup() {
      const gridId = 'contextual-forecast-dialog'
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs, gridId })
      const isLoading = ref(true)
      const rows = ref([])
      const isData = computed(() => rows.value.length)
      const { schemas } = useGridScheme(inventorySchemas.forecastDialog, { isData })
      const isDialog = ref(false)
      const gridHeight = '40vh'

      async function getData(params) {
        if (!params) return

        isLoading.value = true

        const { data } = await inventoryApi.getInventoryDropdownData(
          inventoryResource.replenishmentForecasts,
          params
        )

        rows.value = data || []
        isLoading.value = false
      }

      function open(params) {
        isDialog.value = true

        return getData(params)
      }

      function close() {
        isDialog.value = false
      }

      // subscribers
      busOn(`grid-ready:${gridId}`, () => {
        grid.value.api.sizeColumnsToFit()
      })

      return {
        grid,
        gridHeight,
        isLoading,
        rows,
        defaultGridOptions,
        defaultGridColDef,
        isDialog,
        open,
        close,
        schemas
      }
    },
    computed: {
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          getContextMenuItems: (params) => getDefaultContextMenuItems(params)
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>
