<template>
  <v-dialog v-model="isDialog">
    <v-card>
      <!-- put here only grid -->
      <v-container fluid>
        <v-progress-linear v-if="isLoading" indeterminate color="primary" />
        <template v-if="!isLoading">
          <ag-grid-vue
            ref="$grid"
            class="ag-theme-alpine-dark"
            :column-defs="schemas.main"
            :style="{ height: gridHeight }"
            :row-data="rows"
            :default-col-def="gridColDef"
            :grid-options="gridOptions"
          />
        </template>
      </v-container>
      <v-card-actions ref="content">
        <v-spacer />
        <v-btn color="grey" @click="close"> Close </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import { ref, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import { useEventBus, useContext, useGrid, useGridScheme } from '@/hooks'
  import { imtSchemas } from '@/schemas'
  import { imtApi } from '@/api/imt'
  import { getDefaultContextMenuItems } from '@/modules/dashboards'
  import { inventoryApi } from '@/api/inventory'

  export default {
    components: {
      AgGridVue
    },
    setup() {
      const gridId = 'contextual-discards-dialog'
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs, gridId })
      const isLoading = ref(true)
      const rows = ref([])
      const isData = computed(() => rows.value.length)
      const { schemas } = useGridScheme(imtSchemas.discardsDialog, { isData })
      const isDialog = ref(false)
      const gridHeight = '40vh'

      async function getData(params, type) {
        if (!params) return

        isLoading.value = true

        const fetch = type.includes('donations')
          ? type.includes('imt')
            ? imtApi.ingredientsDepletion.donations
            : inventoryApi.donations
          : imtApi.discards.getDiscards

        const { data } = await fetch(params)

        rows.value = data || []
        isLoading.value = false
      }

      function open(params, type) {
        isDialog.value = true
        getData(params, type)
      }

      function close() {
        isDialog.value = false
      }

      // subscribers
      busOn(`grid-ready:${gridId}`, () => {
        grid.value.api.sizeColumnsToFit()
      })

      return {
        grid,
        gridHeight,
        isLoading,
        rows,
        defaultGridOptions,
        defaultGridColDef,
        isDialog,
        open,
        close,
        schemas
      }
    },
    computed: {
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          getContextMenuItems: (params) => getDefaultContextMenuItems(params)
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>

<style></style>
