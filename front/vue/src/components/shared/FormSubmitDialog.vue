<template>
  <v-dialog :value="isDialog" width="500">
    <v-card id="form-submit">
      <v-card-text>
        <div class="validation-info">
          <!-- error scenario -->
          <template v-if="errorObj">
            <div v-if="errorObj" class="validation-info--title">
              {{ errorObj.msg }}
            </div>
            <template v-if="errorObj.data">
              <p>
                You have entered
                <b class="validation-info--error"
                  >{{ errorObj.data.length }} invalid {{ pluralizeRecord(errorObj.data.length) }}</b
                >.
              </p>
              <p>
                Those rows are highlighted in the table. <br />
                Fix them and try again.
              </p>
            </template>
          </template>
          <!-- success scenario -->
          <p v-else>
            You are going to submit
            <b class="validation-info--success"
              >{{ records.length }} {{ pluralizeRecord(records.length) }}</b
            >
          </p>
          <div v-if="warningsCount">
            <h4 class="mb-2">Warnings</h4>
            <ul class="validation-warnings mb-4">
              <li v-for="(discardRate, index) in discardRateWarnings" :key="index">
                {{ discardRate.count }} {{ pluralizeRecord(discardRate.count) }} with discard rate
                greater than
                <b class="validation-info--warning">{{ discardRate.percentage }}%</b>
              </li>
              <li v-if="warningsByTypes.RETIRED_SKU">
                {{ warningsByTypes.RETIRED_SKU }} {{ pluralizeRecord(warningsByTypes.RETIRED_SKU) }}
                <span class="validation-info--error"> contain Retired SKUs </span>
              </li>
            </ul>
            <span class="caption">(SKU cells with warnings are highlighted in the table)</span>
          </div>
        </div>
      </v-card-text>

      <v-divider />

      <v-card-actions>
        <v-spacer />
        <v-btn
          v-if="isRecordsValidated"
          :disabled="isLoading"
          :loading="isLoading"
          @click="submit"
          color="primary"
        >
          Submit
        </v-btn>
        <v-btn color="grey" @click="close"> Cancel </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import { watch, ref, toRefs, computed } from 'vue'

  export default {
    props: {
      isDialog: {
        type: Boolean,
        default: false
      },
      isRecordsValidated: {
        type: Boolean,
        default: false
      },
      errorObj: {
        type: Object,
        default: () => {}
      },
      records: {
        type: Array,
        default: () => []
      },
      warningsCount: {
        type: Number
      },
      warningsByTypes: {
        type: Object
      }
    },
    setup(props, { emit }) {
      const { isRecordsValidated, warningsByTypes } = toRefs(props)
      const discardRateWarnings = computed(() =>
        Object.entries(warningsByTypes.value).reduce((acc, [type, count]) => {
          if (type.includes('DISCARD_RATE')) {
            const [_, percentage] = type.split(':')

            acc.push({ count, percentage })
          }

          return acc
        }, [])
      )
      const isLoading = ref(false)
      const submit = () => {
        isLoading.value = true
        emit('submit')
      }
      const close = () => emit('close')
      watch(isRecordsValidated, (flag) => {
        if (!flag) {
          isLoading.value = false
        }
      })

      function pluralizeRecord(count) {
        return count > 1 ? 'records' : 'record'
      }

      return { isLoading, submit, close, pluralizeRecord, discardRateWarnings }
    }
  }
</script>

<style lang="scss">
  .validation-info {
    padding-top: 40px;
    font-size: 16px;

    &--title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 20px;
    }

    &--error {
      color: #d67474;
    }

    &--success {
      color: #53a960;
    }

    &--warning {
      color: orange;
    }
  }

  .validation-warnings {
    li {
      font-size: 14px;
    }
  }
</style>
