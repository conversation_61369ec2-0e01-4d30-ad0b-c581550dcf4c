<template>
  <div>
    <v-snackbar
      v-for="item in notifications"
      :id="item.id"
      :key="item.id"
      :value="true"
      left
      timeout="-1"
      :style="item.style"
    >
      <div class="d-flex align-center">
        <v-icon v-if="item.isError" color="error" class="mr-3">mdi-alert</v-icon>
        <div class="snackbar-text">{{ item.text }}</div>
      </div>

      <template v-slot:action>
        <v-btn
          v-if="isDetailsAvailable(item.details)"
          @click="openDetailsDialog(item.details)"
          color="warning"
          text
        >
          Details
        </v-btn>
        <v-btn color="primary" text @click="removeNotification(item)">Close</v-btn>
      </template>
    </v-snackbar>

    <v-dialog
      v-model="isDetailsDialog"
      fullscreen
      hide-overlay
      scrollable
      transition="dialog-bottom-transition"
      class="details-dialog"
    >
      <v-card>
        <v-toolbar dark color="#FF8F00" class="details-toolbar">
          <v-btn class="ml-2" icon dark @click="closeDetailsDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title> Details </v-toolbar-title>
        </v-toolbar>
        <v-card-text>
          <div class="details-log">
            <div class="details-item">{{ details }}</div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useNotifications } from '@/hooks'

  const { notifications, removeNotification } = useNotifications()
  const isDetailsDialog = ref(false)
  const details = ref('')

  function openDetailsDialog(text) {
    details.value = text
    isDetailsDialog.value = true
  }

  function closeDetailsDialog() {
    details.value = ''
    isDetailsDialog.value = false
  }

  function isDetailsAvailable(details) {
    return Boolean(details)
  }
</script>

<style>
  .details-toolbar {
    flex: none;
  }
</style>
