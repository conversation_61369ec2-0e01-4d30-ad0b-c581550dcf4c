<template>
  <v-dialog v-model="isDialog">
    <v-card class="bulk-sku-data" ref="table">
      <!-- put here only grid -->
      <v-container fluid>
        <v-progress-linear v-if="isLoading" indeterminate color="primary" />
        <template v-if="!isLoading">
          <ag-grid-vue
            ref="$grid"
            class="ag-theme-alpine-dark"
            :column-defs="schemas.main"
            :style="{ height: gridHeight }"
            :row-data="rows"
            :default-col-def="gridColDef"
            :grid-options="gridOptions"
          />
        </template>
      </v-container>
      <v-card-actions ref="content">
        <v-spacer />
        <v-btn color="grey" @click="close"> Close </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  import { ref, onUnmounted, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useGridScheme,
    usePermissions,
    useCommonComments
  } from '@/hooks'
  import { debounce } from '@/utils'
  import { imtSchemas } from '@/schemas'
  import { useNamespacedGetters } from 'vuex-composition-helpers'
  import { permissionsConfig } from '@/config/permissions.config'
  import { commentTypes } from '@/config/app.config'
  import { commentColumnContext } from '@/modules/dashboards'

  export default {
    components: {
      AgGridVue
    },
    props: {
      consolidatedFilter: {
        type: Object,
        default: () => {}
      },
      weekdays: {
        type: Object,
        default: () => {}
      }
    },
    setup(props) {
      const gridId = 'contextual-bulk-sku-dialog'
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { hasPermission } = usePermissions()
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs, gridId })
      const { current, available } = useFilter()
      const { country } = useNamespacedGetters('Common', ['country'])
      const isCommentsEditable = hasPermission(permissionsConfig[`${current.domain}Comment`].write)
      const activeCommentTypes = ['sku']
      const isLoading = ref(false)
      const rows = ref([])
      const isData = computed(() => rows.value.length)
      const { loadCommentsByType, mapDashboardRowsAndComments } = useCommonComments({
        activeCommentTypes,
        grid,
        current,
        available,
        consolidatedFilter: props.consolidatedFilter
      })

      const { schemas } = useGridScheme(imtSchemas.ingredientsDepletion, {
        isData,
        rows,
        current,
        available,
        weekdaysConfig: props.weekdays,
        consolidatedFilter: props.consolidatedFilter,
        isDailyColumnsVisible: true,
        isBulkSkuModal: true,
        country
      })
      const isDialog = ref(false)
      const gridHeight = '40vh'

      async function getDataAndComments(params) {
        if (!params) return

        isLoading.value = true

        const {
          data: { packagedItems }
        } = params

        if (!packagedItems) return

        const comments = loadCommentsByType(commentTypes.sku)

        rows.value = mapDashboardRowsAndComments(packagedItems, comments)
        isLoading.value = false
      }

      function open(params) {
        isDialog.value = true

        return getDataAndComments(params)
      }

      function close() {
        isDialog.value = false
      }

      // subscribers
      busOn(`grid-ready:${gridId}`, () => {
        refs.table.$el.addEventListener('wheel', (e) => e.stopPropagation())
      })

      onUnmounted(() => refs.table && refs.table.$el.removeEventListener('wheel'))

      return {
        grid,
        gridHeight,
        isLoading,
        rows,
        defaultGridOptions,
        defaultGridColDef,
        isDialog,
        open,
        close,
        current,
        schemas,
        isCommentsEditable
      }
    },
    computed: {
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          getContextMenuItems: (params) =>
            commentColumnContext({
              params,
              filter: this.current,
              dialog: this.$parent.$refs.commentFormDialog,
              extraParams: {
                prohibitedTypes: [commentTypes.po]
              },
              isCommentsEditable: this.isCommentsEditable
            })
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>

<style>
  .bulk-sku-data {
    padding: 20px;
  }
</style>
