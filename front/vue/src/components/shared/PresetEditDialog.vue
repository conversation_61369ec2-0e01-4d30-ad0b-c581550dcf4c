<template>
  <v-dialog value="true" max-width="760px" @input="close">
    <v-form id="new-config" ref="form" v-model="isFormValid">
      <v-card>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12" sm="12">
                <span class="headline white--text">Edit preset</span>
              </v-col>
              <v-col cols="12" sm="8">
                <v-text-field
                  v-model="nameModel"
                  label="Name"
                  :text="nameModel"
                  :rules="[rules.required, rules.maxNameLength]"
                  required
                />
              </v-col>
            </v-row>
            <section>
              <article>
                <div v-for="(brand, index) in brandsModel" :key="index">
                  <div class="title mb-2">{{ brand.name }}</div>
                  <ul class="d-flex flex-wrap">
                    <li v-for="site in brand.sites" :key="site.name">
                      <v-btn
                        @click="site.selected = !site.selected"
                        :class="{ 'preset--selected': site.selected }"
                        :disabled="!enabledBrandsToPick.includes(brand.name)"
                        small
                        height="40"
                        >{{ site.name }}</v-btn
                      >
                    </li>
                  </ul>
                </div>
              </article>

              <article v-if="warehousesModel.length">
                <div class="title mb-2">Warehouses</div>
                <ul class="d-flex flex-wrap">
                  <li v-for="warehouse in warehousesModel" :key="warehouse.name">
                    <v-btn
                      @click="warehouse.selected = !warehouse.selected"
                      :class="{ 'preset--selected': warehouse.selected }"
                      small
                      height="40"
                      >{{ warehouse.name }}</v-btn
                    >
                  </li>
                </ul>
              </article>
            </section>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn color="grey" @click="close">Cancel</v-btn>
          <v-btn color="primary" @click="reset"> Reset </v-btn>
          <v-btn
            color="success"
            :disabled="!isFormValid || isProcessing"
            :loading="isProcessing"
            @click="save"
            >Save</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
  import { ref, computed } from 'vue'
  import { inventoryApi } from '@/api/inventory'
  import { useNotifications } from '@/hooks'

  export default {
    props: {
      formPresets: {
        type: Object
      },
      preset: {
        type: Object
      }
    },
    setup(props, { emit }) {
      const MAX_NAME_LENGTH = 12
      const { addNotification } = useNotifications()
      const isFormValid = ref(false)
      const isProcessing = ref(false)
      const rules = ref({
        required: (value) => !!value || 'Required',
        maxNameLength: (value) =>
          value.length <= MAX_NAME_LENGTH || `${MAX_NAME_LENGTH} characters is allowed`
      })

      function isSiteSelected(brand, site) {
        return props.preset.brands[brand]?.includes(site)
      }

      const nameModel = ref(props.preset.name)
      const brandsModel = ref(
        Object.entries(props.formPresets.brands).map(([brand, obj]) => {
          const { name, weekConfig, sites } = obj
          const mappedSites = sites.map((site) => ({
            name: site,
            selected: isSiteSelected(brand, site)
          }))

          return {
            shortName: brand,
            name,
            sites: mappedSites,
            weekConfig
          }
        })
      )
      const warehousesModel = ref(
        props.formPresets.warehouses.map((name) => ({
          name,
          selected: props.preset.warehouses.includes(name)
        }))
      )

      const enabledBrandsToPick = computed(() => {
        const firstBrandEntityWithSelectedSite = brandsModel.value.find(({ sites }) =>
          sites.some(({ selected }) => selected)
        )

        if (firstBrandEntityWithSelectedSite) {
          const { weekConfig } = firstBrandEntityWithSelectedSite

          return brandsModel.value
            .filter((item) => item.weekConfig === weekConfig)
            .map(({ name }) => name)
        }

        return brandsModel.value.map(({ name }) => name)
      })

      function close() {
        emit('close')
      }
      function reset() {
        nameModel.value = props.preset.name
        brandsModel.value.forEach(({ sites }) => {
          sites.forEach((item) => {
            item.selected = false
          })
        })
        warehousesModel.value.forEach((item) => {
          item.selected = false
        })
      }
      async function save() {
        isProcessing.value = true

        const brands = brandsModel.value.reduce((acc, cur) => {
          const { shortName, sites } = cur
          const sitesSelected = sites.filter(({ selected }) => selected).map(({ name }) => name)

          if (sitesSelected.length) {
            acc[shortName] = sitesSelected
          }

          return acc
        }, {})

        const warehouses = warehousesModel.value
          .filter(({ selected }) => selected)
          .map(({ name }) => name)

        const item = {
          presetId: props.preset.id,
          presetName: nameModel.value,
          brands,
          warehouses
        }

        const payload = [item]

        try {
          await inventoryApi.presets.save(payload)
          emit('save', item)
          addNotification({ text: `"${nameModel.value}" is updated` })
          close()
        } catch ({ message: details = 'Server Error' }) {
          addNotification({
            text: 'Update is failed due to server problem',
            isError: true,
            details
          })
        }

        isProcessing.value = false
      }

      return {
        nameModel,
        brandsModel,
        warehousesModel,
        enabledBrandsToPick,
        isFormValid,
        isProcessing,
        rules,
        close,
        reset,
        save
      }
    }
  }
</script>

<style lang="scss" scoped>
  ul {
    list-style: none;
    padding: 0 0 12px;

    button {
      margin: 0 12px 12px 0;
    }
  }
</style>
