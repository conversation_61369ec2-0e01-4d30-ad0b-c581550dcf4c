<template>
  <div id="multi-week-selector">
    <v-menu
      v-model="menu"
      :close-on-content-click="!multi"
      close-delay="200"
      offset-y
      tile
      transition="v-menu-transition"
      @input="onMenuChange"
      content-class="muiti-week-selector"
    >
      <template #activator="{ on }">
        <v-icon class="week-prepend-icon">mdi-calendar</v-icon>
        <div v-on="on" class="week-activator" role="button" aria-label="week selector" tabindex="0">
          <div v-if="multi">
            <span class="week-input" v-if="value.from">
              <label class="week-label">Week From : Week To</label>
              <span>{{ value.from }}</span> </span
            ><span v-else class="week-placeholder">Week From</span>
            <span class="range-separator bold">:</span>
            <span v-if="value.to">{{ value.to }}</span
            ><span v-else class="week-placeholder">Week To</span>
          </div>
          <div v-else>
            <span class="week-input" v-if="value">
              <label class="week-label">Select Week</label>
              <span>{{ value }}</span> </span
            ><span v-else class="week-placeholder">Select Week</span>
          </div>
        </div>
      </template>
      <div class="week-popover">
        <div class="week-content">
          <div class="week-scroll-area">
            <div class="weeks-group" v-for="(group, index) in groups" :key="index">
              <div class="the-quarter">{{ group.quarter }}</div>
              <div>
                <v-btn
                  v-for="(week, index) in group.weeks"
                  :class="{
                    'the-week--ongoing': week.ongoing,
                    'the-week--range': week.inRange,
                    'the-week--selected': week.selected
                  }"
                  :disabled="week.disabled"
                  :key="index"
                  @click="onWeekClick(week)"
                  @mouseover="handleHover(week)"
                  @mouseleave="handleUnhover"
                  class="the-week"
                  width="30"
                  >{{ week.title }}</v-btn
                >
              </div>
            </div>
          </div>
          <div v-if="rangeLimit" class="week-range-details">
            <v-icon>mdi-information-outline</v-icon>
            <span
              >max range selection: <b>{{ rangeLimit }} weeks</b></span
            >
          </div>
        </div>
      </div>
    </v-menu>
  </div>
</template>

<script>
  import { ref, computed } from 'vue'
  import { createNamespacedHelpers } from 'vuex-composition-helpers'
  import { scrollToSelectedOrOngoingItem } from '@/utils'

  export default {
    props: {
      value: [String, Object],
      multi: {
        type: Boolean,
        default: false
      },
      rangeLimit: {
        type: Number,
        default: 0
      },
      highlightWeek: {
        type: Boolean,
        default: true
      }
    },
    setup(props, { emit }) {
      const menu = ref(false)
      const hovered = ref(null)
      const hoverTimeout = ref(null)
      const { useGetters } = createNamespacedHelpers('Filters')
      const { ongoingWeek, quartersWithWeeks, allWeeks } = useGetters([
        'ongoingWeek',
        'quartersWithWeeks',
        'allWeeks'
      ])

      const selectedWeeks = computed(() =>
        props.multi
          ? [getIndex(props.value.from), getIndex(props.value.to)]
          : [getIndex(props.value)]
      )

      const hoverSelection = computed(() => {
        if (!props.multi || !hovered.value || !props.value.from || props.value.to) return null

        const [from] = selectedWeeks.value

        return { from, to: hovered.value }
      })

      const groups = computed(() => {
        const [from, to] = selectedWeeks.value
        let counter = 1

        return Object.entries(quartersWithWeeks.value).reduce((acc, [quarter, theWeeks]) => {
          const weeks = theWeeks.map((item) => {
            const index = counter++
            const title = formatWeek(item)
            const selected = selectedWeeks.value.includes(index)
            const ongoing = props.highlightWeek && ongoingWeek.value === item
            const disabled = Boolean(
              props.multi &&
                props.rangeLimit &&
                from &&
                !to &&
                index > from &&
                isWeekOutOfRange(from, index)
            )

            const inRange =
              (props.multi && from && to && index > from && index < to) ||
              (hoverSelection.value &&
                hoverSelection.value.from <= index &&
                hoverSelection.value.to >= index)

            return {
              value: item,
              title,
              selected,
              ongoing,
              index,
              inRange,
              disabled
            }
          })

          const group = { quarter, weeks }

          return acc.concat(group)
        }, [])
      })

      function onWeekClick(week) {
        const { value, index } = week

        if (props.multi) {
          const [from, to] = selectedWeeks.value

          if (!from && !to) {
            return emit('input', { from: value, to: null })
          }

          if (index === from && !to) {
            return emit('input', { from: null, to: null })
          }

          if (index === from || index === to) {
            return emit('input', { from: value, to: null })
          }

          if (index < from) {
            return emit('input', { from: value, to: null })
          }

          if (from && !to) {
            closeMenu()
            return emit('input', { from: props.value.from, to: value })
          }

          if (from && to) {
            return emit('input', { from: value, to: null })
          }
        }

        return emit('input', value)
      }

      function closeMenu() {
        menu.value = false
      }

      function formatWeek(week) {
        const [_, part2] = week.split('-')

        return part2
      }

      function getIndex(value) {
        return allWeeks.value.indexOf(value) + 1
      }

      function isWeekOutOfRange(fromIndex, currentIndex) {
        const diff = currentIndex - fromIndex

        return diff > props.rangeLimit - 1
      }

      function onMenuChange() {
        if (!menu.value) return

        scrollToSelectedOrOngoingItem()
      }

      function handleHover(week) {
        if (hoverTimeout.value) {
          clearTimeout(hoverTimeout.value)
        }

        hovered.value = week.index
      }

      function handleUnhover() {
        hoverTimeout.value = setTimeout(() => {
          hovered.value = null
        }, 150)
      }

      return {
        groups,
        menu,
        quartersWithWeeks,
        onMenuChange,
        onWeekClick,
        handleHover,
        handleUnhover
      }
    }
  }
</script>

<style lang="scss" scoped>
  $bgColor: #1e1e1e;
  $disabledColor: rgba(255, 255, 255, 0.5);
  $tintedWhite: rgba(255, 255, 255, 0.7);

  #multi-week-selector {
    display: flex;
    margin: 17px 0 21px;
  }

  .muiti-week-selector {
    box-shadow: none;
  }

  .week {
    &-activator {
      flex: 1;
      font-size: 16px;
      color: white;
      background-color: #424242;
      height: 32px;
      display: flex;
      align-items: center;
      border-radius: 0px;
      padding: 0;
      white-space: nowrap;
      cursor: pointer;
      border-bottom: 1px solid $tintedWhite;
      box-shadow: none;
      margin-left: 9px;

      &:hover {
        border-bottom-color: white;
      }

      i {
        margin-left: 15px;
      }

      &--disabled {
        color: $disabledColor;
        cursor: default;

        i {
          color: $disabledColor !important;
        }
      }
    }

    &-input {
      position: relative;
    }

    &-label {
      position: absolute;
      left: 0;
      right: auto;
      color: $tintedWhite;
      transform: translateY(-18px) scale(0.75);
      transform-origin: top left;
    }

    &-popover {
      padding-top: 10px;
    }

    &-content {
      width: 394px;
      background-color: #424242;
      border-radius: 5px;
      padding: 15px 6px 15px 20px;
    }

    &-scroll-area {
      padding-right: 20px;
      max-height: 390px;
      overflow-y: auto;
    }

    &-range-details {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 15px;
      margin-right: 30px;

      .v-icon {
        margin-right: 5px;
        font-size: 15px;
      }

      span {
        font-size: 14px;
        position: relative;
        top: -1px;
      }
    }

    &-placeholder {
      display: inline-block;
      height: 23px;
      color: $tintedWhite;
    }

    &-prepend-icon {
      margin-bottom: 2px;
    }
  }

  .weeks-group + .weeks-group {
    padding-top: 14px;
    margin-top: 16px;
    border-top: 1px solid #7c7c7c;
  }

  .the-quarter {
    margin-bottom: 2px;
    font-weight: 600;
    font-size: 14px;
  }

  .the-week {
    margin: 5px 5px 0 0;

    &:nth-child(5n) {
      margin-right: 0;
    }

    &--ongoing {
      background-color: #089d89 !important;
    }

    &--range {
      background-color: #516681 !important;
    }

    &--selected {
      background-color: #1866c0 !important;
    }
  }

  .range-separator {
    padding: 0 6px;
  }
</style>
