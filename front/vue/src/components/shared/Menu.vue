<template>
  <div>
    <v-menu
      v-for="item in showAvailableTabs(items)"
      :key="item.name"
      :close-on-content-click="false"
      dark
      transition="v-menu-transition"
      close-delay="100"
      offset-y
      open-on-hover
    >
      <template #activator="{ on }">
        <v-btn big text tile v-on="on">
          {{ item.name }}
        </v-btn>
      </template>

      <v-card class="mx-auto pt-2 pb-2" min-width="200" max-width="270">
        <v-list-group
          v-for="group in showAvailableGroups(item.groups)"
          :key="group.path"
          :value="true"
          no-action
          sub-group
          class="menu-group"
        >
          <template #prependIcon>
            <v-icon x-small class="icon-align"> fa-angle-up </v-icon>
          </template>

          <template #activator>
            <v-list-item-title>{{ group.name }}</v-list-item-title>
          </template>

          <v-list-item
            v-for="link in group.items"
            :id="getFullPath(item.path, group.path, link.path)"
            :key="link.path"
            :to="getFullPath(item.path, group.path, link.path)"
            @click="triggerEvent(item.name, link.name)"
            link
          >
            <v-list-item-title v-text="link.name" />
          </v-list-item>
        </v-list-group>
      </v-card>
    </v-menu>
  </div>
</template>

<script>
  import { menuScheme } from '@/config/app.config'
  import { computed } from 'vue'
  import { usePermissions } from '@/hooks'
  import { isAnalyticsEnabled, trackEvent } from '@/modules/analytics'

  export default {
    setup() {
      const { hasPermission } = usePermissions()

      const items = computed(() => {
        return menuScheme
          .filter(({ isHidden }) => !isHidden)
          .map((item) => ({
            ...item,
            groups: item.groups.map((group) => ({
              ...group,
              items: group.items.filter(({ permissions, isHidden }) => {
                const isAccessibleForCurrentUser =
                  permissions && hasPermission(permissions) && !isHidden

                return isAccessibleForCurrentUser
              })
            }))
          }))
      })

      const showAvailableGroups = (menuGroups) => menuGroups.filter((group) => group.items.length)

      const showAvailableTabs = (tabs) =>
        tabs.filter((tab) => showAvailableGroups(tab.groups).length)

      function getFullPath(...args) {
        return args.reduce((acc, cur) => `${acc}/${cur}`, '')
      }

      function triggerEvent(groupName, itemName) {
        const currentDashboardName = this.$route.meta.title
        const menuItemName = `${groupName} | ${itemName}`

        if (groupName !== 'Admin' && currentDashboardName !== menuItemName) {
          isAnalyticsEnabled() &&
            trackEvent({
              event: 'click_menu',
              label: 'Menu click',
              menuItemName
            })
        }
      }

      return {
        getFullPath,
        items,
        showAvailableGroups,
        showAvailableTabs,
        triggerEvent
      }
    }
  }
</script>

<style lang="scss">
  .menu-group.v-list-group--sub-group {
    margin: 0 8px;
    background-color: #333;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 3px 1px rgba(0, 0, 0, 0.2);

    & + & {
      margin-top: 8px;
    }

    a {
      color: #dadada;
    }

    .v-list-group__header {
      padding-left: 10px;
      height: 32px;
      min-height: 32px;
      font-weight: bold;
      color: #424242;
      cursor: default;

      &:hover:before {
        opacity: 0;
      }

      .v-list-item__icon {
        margin: 10px 0;
        min-width: 14px;
      }

      .v-list-item__icon {
        color: #c1c1c1 !important;
        cursor: pointer;
      }

      .v-list-item__title {
        font-size: 10px;
        text-shadow: 1px 1px 0 rgba(#000, 0.6);
        color: #c1c1c1;
      }
    }

    .v-list-item__icon:first-child {
      margin-right: 0;
    }

    .v-list-group__items > .v-list-item {
      padding-left: 40px !important;
      min-height: 32px;
      border-top: 1px solid #2b2b2b;

      &:hover:before {
        opacity: 0.1;
      }

      &.v-list-item--active {
        cursor: default;

        &:hover:before {
          opacity: 0.24;
        }
      }

      .v-list-item__title {
        font-size: 12px;
        font-weight: normal;
      }
    }
  }
</style>
