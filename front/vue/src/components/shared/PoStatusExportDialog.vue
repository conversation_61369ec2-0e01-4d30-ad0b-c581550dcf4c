<template>
  <div>
    <v-dialog v-model="isExportModal" width="620px">
      <v-progress-linear v-if="isDownloadInProgress" indeterminate color="primary" />
      <v-card id="export-pop-up">
        <v-card-title>Generate PO export</v-card-title>
        <v-card-text class="download-card-content">
          <h3 class="popup-subtitle">Select brands:</h3>
          <v-row class="d-flex flex-row">
            <v-col v-for="(brand, index) in enhancedBrandList" :key="index">
              <v-checkbox
                v-model="exportBrands"
                :value="brand"
                :disabled="isBrandSelectionDisabled(brand)"
                :label="brand"
              />
            </v-col>
          </v-row>

          <hr />
          <h3 class="popup-subtitle">Select PO filter:</h3>
          <v-radio-group v-model="selectedPoFilter" row>
            <v-radio
              v-for="(poFilter, index) in poFilterTypes"
              :key="index"
              :value="poFilter"
              :label="poFilter"
            />
          </v-radio-group>
          <hr />
          <h3 class="popup-subtitle">Select time period:</h3>
          <v-radio-group v-model="selectedExportPeriod" row>
            <v-radio
              v-for="(period, index) in periodsList"
              :key="index"
              :value="period"
              :label="capitalizeFirstLetter(period)"
            />
          </v-radio-group>
          <hr />
          <template v-if="selectedExportPeriod === DAILY_EXPORT_NAME">
            <v-row class="d-flex align-center mt-3">
              <v-col cols="12" md="6">
                <v-menu
                  v-model="isDailyExportMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="formattedDailyExportDate"
                      label="Select Date"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    v-model="dailyExportDate"
                    required
                    no-title
                    color="primary"
                    header-color="primary"
                    @input="isDailyExportMenu = false"
                  />
                </v-menu>
              </v-col>
            </v-row>
          </template>
          <template v-if="selectedExportPeriod === WEEKLY_EXPORT_NAME">
            <v-row class="d-flex align-center mt-3">
              <v-col cols="12" md="6">
                <WeekSelector v-model="weeklyExportWeek" />
              </v-col>
            </v-row>
          </template>
          <template v-if="selectedExportPeriod === WEEKLY_RANGE_NAME">
            <v-row class="d-flex align-center mt-3">
              <v-col cols="12" md="6">
                <WeekSelector v-model="weeksRange" multi :rangeLimit="5" />
              </v-col>
            </v-row>
          </template>
          <template v-if="selectedExportPeriod === MONTHLY_EXPORT_NAME">
            <v-row class="d-flex align-center mt-3">
              <v-col cols="12" md="6">
                <v-menu
                  v-model="isMonthlyExportMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="formattedMonthlyExportDate"
                      label="Select Month"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    id="month-picker"
                    v-model="monthlyExportDate"
                    required
                    no-title
                    color="primary"
                    header-color="primary"
                    type="month"
                    @input="isMonthlyExportMenu = false"
                  />
                </v-menu>
              </v-col>
            </v-row>
          </template>
          <template v-if="selectedExportPeriod === CUSTOM_EXPORT_NAME">
            <v-row class="d-flex align-center mt-3">
              <v-col cols="12" md="6">
                <v-menu
                  v-model="isPeriodFromMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="formattedPeriodFrom"
                      label="Date From"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    v-model="periodFrom"
                    required
                    no-title
                    color="primary"
                    header-color="primary"
                    @input="isPeriodFromMenu = false"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" md="6">
                <v-menu
                  v-model="isPeriodToMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="formattedPeriodTo"
                      label="Date To"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    v-model="periodTo"
                    required
                    color="primary"
                    header-color="primary"
                    no-title
                    @input="isPeriodToMenu = false"
                  />
                </v-menu>
              </v-col>
              <v-col cols="12" v-if="!!customPeriodWarning">
                <v-icon color="error" class="mr-3"> mdi-alert </v-icon>
                <div class="period-warning">
                  {{ customPeriodWarning }}
                </div>
              </v-col>
            </v-row>
          </template>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn color="grey" @click="closeExportPopup"> Close </v-btn>
          <v-btn color="primary" :disabled="isGenerateReportDisabled" @click="downloadFile"
            >Generate</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import { computed, ref } from 'vue'
  import { useFilter, useNotifications } from '@/hooks'
  import { formatDate, getDayDifference, tokens } from '@/modules/date'
  import { areTwoArraysWithSameValues, capitalizeFirstLetter, filterEmptyProperties } from '@/utils'
  import { getCurrentTimestamp } from '@/modules/date'
  import { downloadPoExport } from '@/api/global'
  import WeekSelector from '../shared/PoStatusWeekSelector.vue'

  const ALL_BRANDS = 'All'
  const MAX_CUSTOM_PERIOD_DURATION_IN_DAYS = 120
  const DAILY_EXPORT_NAME = 'daily'
  const WEEKLY_EXPORT_NAME = 'weekly'
  const WEEKLY_RANGE_NAME = 'week range'
  const MONTHLY_EXPORT_NAME = 'monthly'
  const CUSTOM_EXPORT_NAME = 'custom'
  const periodsList = [
    DAILY_EXPORT_NAME,
    WEEKLY_EXPORT_NAME,
    WEEKLY_RANGE_NAME,
    MONTHLY_EXPORT_NAME,
    CUSTOM_EXPORT_NAME
  ]
  const ALL_POS = 'All POs'
  const RECEIVED_POS_ONLY = 'Received POs only'
  const ALL_POS_WITH_TRANSFERS = 'All POs + TOs'
  const poFilterTypes = [ALL_POS_WITH_TRANSFERS, ALL_POS, RECEIVED_POS_ONLY]
  const poFilterRequestOptions = {
    [ALL_POS_WITH_TRANSFERS]: 'allPosWithTransfers',
    [ALL_POS]: 'allPos',
    [RECEIVED_POS_ONLY]: 'receivedPos'
  }

  export default {
    props: {
      exportModal: {
        type: Boolean,
        default: false
      }
    },
    components: {
      WeekSelector
    },
    setup(props, { emit }) {
      const { available } = useFilter()
      const { addNotification } = useNotifications()

      const selectedBrandsList = ref([])
      const selectedExportPeriod = ref(DAILY_EXPORT_NAME)
      const selectedPoFilter = ref(ALL_POS)
      const isDownloadInProgress = ref(false)
      const isDailyExportMenu = ref(false)
      const isMonthlyExportMenu = ref(false)
      const isPeriodFromMenu = ref(false)
      const isPeriodToMenu = ref(false)

      const dailyExportDate = ref('')
      const weeklyExportWeek = ref('')
      const weeklyExportQuarter = ref('')
      const monthlyExportDate = ref('')
      const periodFrom = ref('')
      const periodTo = ref('')
      const weeksRange = ref({
        from: '',
        to: ''
      })
      const enhancedBrandList = computed(() => [ALL_BRANDS, ...available.brands])
      const exportBrands = computed({
        get: () => selectedBrandsList.value,
        set: (newValue) => {
          if (
            areTwoArraysWithSameValues(newValue, available.brands) ||
            newValue.includes(ALL_BRANDS)
          ) {
            return (selectedBrandsList.value = [ALL_BRANDS])
          }

          return (selectedBrandsList.value = newValue)
        }
      })
      const formattedDailyExportDate = computed(() =>
        formatDate(dailyExportDate.value, tokens.date.default)
      )
      const formattedMonthlyExportDate = computed(() =>
        formatDate(monthlyExportDate.value, tokens.date.monthYear)
      )
      const formattedPeriodFrom = computed(() => formatDate(periodFrom.value, tokens.date.default))
      const formattedPeriodTo = computed(() => formatDate(periodTo.value, tokens.date.default))
      const weekOptions = computed(() => available.quartersWithWeeks[weeklyExportQuarter.value])
      const isExportModal = computed({
        get: () => props.exportModal,
        set: (value) => emit('update:exportModal', value)
      })
      const isAnyEntitiesSelected = computed(() => exportBrands.value.length)
      const isGenerateReportDisabled = computed(() => {
        if (isDownloadInProgress.value || !isAnyEntitiesSelected.value) {
          return true
        }

        switch (selectedExportPeriod.value) {
          case DAILY_EXPORT_NAME:
            return !dailyExportDate.value
          case MONTHLY_EXPORT_NAME:
            return !monthlyExportDate.value
          case CUSTOM_EXPORT_NAME:
            return !isCustomPeriodValid.value
          default:
            return false
        }
      })

      const customPeriodWarning = computed(() => {
        if (periodFrom.value && periodTo.value) {
          if (periodTo.value < periodFrom.value) {
            return '"To" date cannot be before "From" date'
          } else if (
            getDayDifference(periodFrom.value, periodTo.value) > MAX_CUSTOM_PERIOD_DURATION_IN_DAYS
          ) {
            return `Selected period exceeds ${MAX_CUSTOM_PERIOD_DURATION_IN_DAYS} days, please adjust it.`
          }
        }
        return ''
      })

      const isCustomPeriodValid = computed(
        () => periodFrom.value && periodTo.value && !customPeriodWarning.value
      )

      function isBrandSelectionDisabled(brand) {
        return brand !== ALL_BRANDS && exportBrands.value.includes(ALL_BRANDS)
      }

      function getRequestParamsForExport() {
        return {
          brands: exportBrands.value.filter((brand) => brand !== ALL_BRANDS).join() || undefined
        }
      }

      async function downloadFile() {
        isDownloadInProgress.value = true

        const requestParams = getRequestParamsForExport()
        requestParams.poFilter = poFilterRequestOptions[selectedPoFilter.value]

        switch (selectedExportPeriod.value) {
          case DAILY_EXPORT_NAME:
            requestParams.date = dailyExportDate.value
            break
          case WEEKLY_EXPORT_NAME:
            requestParams.week = weeklyExportWeek.value
            break
          case MONTHLY_EXPORT_NAME:
            requestParams.month = monthlyExportDate.value
            break
          case CUSTOM_EXPORT_NAME:
            requestParams.dateFrom = periodFrom.value
            requestParams.dateTo = periodTo.value
            break
          case WEEKLY_RANGE_NAME:
            requestParams.weekFrom = weeksRange.value.from
            requestParams.weekTo = weeksRange.value.to
            break
        }

        const params = filterEmptyProperties(requestParams)

        try {
          await downloadPoExport(params, 'imt')
        } catch ({ message: details = 'Server Error' }) {
          addNotification({
            text: `Can't download report`,
            isError: true,
            details
          })
        }

        isDownloadInProgress.value = false
        closeExportPopup()
      }

      function cleanUpInputtedData() {
        dailyExportDate.value = ''
        weeklyExportWeek.value = ''
        weeklyExportQuarter.value = ''
        monthlyExportDate.value = ''
        periodFrom.value = ''
        periodTo.value = ''
      }

      function closeExportPopup() {
        cleanUpInputtedData()
        isExportModal.value = false
      }

      return {
        available,
        selectedBrandsList,
        selectedExportPeriod,
        isDailyExportMenu,
        isPeriodFromMenu,
        isPeriodToMenu,
        isDownloadInProgress,
        isMonthlyExportMenu,
        dailyExportDate,
        monthlyExportDate,
        enhancedBrandList,
        formattedDailyExportDate,
        formattedMonthlyExportDate,
        weekOptions,
        weeklyExportWeek,
        weeklyExportQuarter,
        formattedPeriodFrom,
        formattedPeriodTo,
        downloadFile,
        periodsList,
        periodFrom,
        periodTo,
        isExportModal,
        isBrandSelectionDisabled,
        customPeriodWarning,
        isGenerateReportDisabled,
        closeExportPopup,
        exportBrands,
        capitalizeFirstLetter,
        getCurrentTimestamp,
        poFilterTypes,
        selectedPoFilter,
        weeksRange,

        DAILY_EXPORT_NAME,
        WEEKLY_EXPORT_NAME,
        WEEKLY_RANGE_NAME,
        MONTHLY_EXPORT_NAME,
        CUSTOM_EXPORT_NAME
      }
    }
  }
</script>

<style lang="scss">
  .popup-subtitle {
    padding-top: 15px;
  }
  .period-warning {
    display: inline-block;
    color: #ff7a73;
    font-size: 16px;
  }
</style>
