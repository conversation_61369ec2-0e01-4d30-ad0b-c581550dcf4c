<template>
  <v-col>
    <v-text-field
      :color="gsheet.color"
      :error="gsheet.error"
      :success="gsheet.success"
      :hint="gsheet.errorMessage"
      :label="gsheet.name"
      :loading="gsheet.loading"
      :prepend-icon="inputPrependIcon(gsheet.success)"
      :disabled="readonly || gsheet.loading"
      :value="gsheet.url"
      :rules="[rules.gsheetUrlValidation]"
      @change="preValidationCheck(gsheet, $event)"
      @click:prepend="redirect(gsheet)"
    >
      <v-icon slot="append" :color="gsheet.color" @click="validate(gsheet)">
        {{ gsheet.icon }}
      </v-icon>
    </v-text-field>
  </v-col>
</template>

<script>
  import { ref } from 'vue'
  export default {
    name: 'GsheetInput',
    props: {
      gsheet: {
        type: Object,
        default: () => ({})
      },
      preValidationCheck: {
        type: Function,
        default: () => {}
      },
      validate: {
        type: Function,
        default: () => {}
      },
      readonly: {
        type: Boolean,
        default: false
      }
    },
    setup() {
      const gsheetIdPattern = /^([a-zA-Z0-9-_]+)$/
      const gsheetPattern = /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/
      const rules = ref({
        gsheetUrlValidation: (value) => {
          return (
            (!!value &&
              (gsheetIdPattern.test(value) ||
                (value.match(gsheetPattern) &&
                  gsheetIdPattern.test(value.match(gsheetPattern)[1])))) ||
            'Gsheet URL is not valid'
          )
        }
      })

      function inputPrependIcon(isValid) {
        return isValid ? 'mdi-send' : ''
      }

      function redirect(gsheet) {
        return gsheet.color === 'success' && window.open(gsheet.url, '_blank')
      }

      return {
        inputPrependIcon,
        redirect,
        rules
      }
    }
  }
</script>

<style lang="scss">
  .btn-sync {
    margin-right: 10px;

    .v-icon {
      font-size: 18px;
    }

    span {
      text-transform: initial;
      font-size: 12px;
      line-height: 12px;
      padding-left: 3px;
      letter-spacing: 0px;
    }
  }

  .theme--dark.btn-sync.v-btn.v-btn--disabled:not(.v-btn--flat):not(.v-btn--text):not(
      .v-btn--outlined
    ) {
    background-color: #1866c0 !important;
    color: #fff !important;

    .v-icon {
      color: #fff !important;
    }
  }
</style>
