<template>
  <div>
    <ViewToolbar :title="pageTitle">
      <ReloadButton v-if="hasUpdates" @reload="onReload" />
      <ExportButton :export-title="exportTitle" :grid="grid" />
      <ParallelSync @sync="onSync" />
      <StateControls ref="stateControls" :resource="gridStateName" :grid-params="grid" />
    </ViewToolbar>

    <div v-show="!isFullScreen" ref="content">
      <!-- put here everything you want to hide when Fullscreen mode is on -->
      <FiltersBar>
        <template #left>
          <BrandFilter
            v-model="current.brand"
            :brands="available.brandsWithDetails"
            :disabled="isLoading || !current.week"
          />
          <SiteFilter
            v-model="current.site"
            :sites="available.sites"
            :disabled="isLoading || !current.week"
          />
        </template>
        <template #right>
          <WeekFilter v-model="current.week" />
        </template>
      </FiltersBar>
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import { computed, ref, nextTick } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import StateControls from '@/components/util/StateControls'
  import ParallelSync from '@/components/util/ParallelSync'
  import ReloadButton from '@/components/util/ReloadButton'
  import ExportButton from '@/components/util/ExportButton'
  import FiltersBar, { BrandFilter, SiteFilter, WeekFilter } from '@/components/filters'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useLayout,
    useGridState,
    useGridScheme,
    useReload
  } from '@/hooks'
  import { templateData } from '@/mocks/templateData'
  import dashboardScheme from '@/schemas/starter'

  export default {
    components: {
      ViewToolbar,
      ParallelSync,
      ReloadButton,
      ExportButton,
      StateControls,
      FiltersBar,
      BrandFilter,
      SiteFilter,
      WeekFilter,
      AgGridVue
    },
    setup() {
      const { route, refs } = useContext()
      const { busOn } = useEventBus()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { current, available, actions } = useFilter({ isDashboard: true })
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs })
      const { schemas } = useGridScheme(dashboardScheme, { isData })
      const { storePrevGridColState, gridStateName } = useGridState({ route, refs })

      const rows = ref([])
      const isData = computed(() => rows.value.length)
      const isLoading = ref(false)

      async function getData() {
        storePrevGridColState()

        isLoading.value = true
        // todo: replace mocked response with actual api call
        rows.value = templateData
        nextTick(() => (isLoading.value = false))
      }

      const { hasUpdates, onSync, onReload } = useReload({
        syncCb: resetCache,
        reloadCb: getData
      })

      // subscribers
      busOn('filter-changed', getData)

      actions.changeFilter()

      return {
        isFullScreen,
        gridHeight,
        current,
        available,
        defaultGridOptions,
        defaultGridColDef,
        grid,
        gridStateName,
        rows,
        isData,
        isLoading,
        schemas,
        hasUpdates,
        onSync,
        onReload
      }
    },
    computed: {
      pageTitle() {
        // todo: change the page title
        return `New Dashboard ${this.current.week} ${this.current.site} ${this.current.brand}`
      },
      exportTitle() {
        const [, weekNumber] = this.current.week.split('-')

        return `New Dashboard_${weekNumber}_${this.current.site}_${this.current.brand}`
      },
      gridOptions() {
        return {
          // todo: provide custom grid options if needed
          ...this.defaultGridOptions
        }
      },
      gridColDef() {
        return {
          // todo: provide custom grid column definitions if needed
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>

<style scoped></style>
