<template>
  <div id="week-picker" class="picker-select">
    <v-select
      v-model="week"
      :items="weeks"
      :disabled="disabled"
      label="Week"
      color="primary"
      hide-details
      solo
      @click="ensureSelectedItemVisible"
    >
      <template slot="item" slot-scope="{ item }">
        <span :style="getWeekColor(item)">{{ item }}</span>
      </template>
    </v-select>
  </div>
</template>

<script>
  import { computed } from 'vue'
  import { createNamespacedHelpers } from 'vuex-composition-helpers'
  import { ensureSelectedItemVisible } from '@/utils'

  export default {
    props: {
      value: String,
      weeks: {
        type: Array,
        default: () => []
      },
      highlightWeek: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    setup(props) {
      const { useActions, useGetters } = createNamespacedHelpers('Filters')
      const { changeWeek } = useActions(['changeWeek'])
      const { ongoingWeek } = useGetters(['ongoingWeek'])

      const week = computed({
        get() {
          return props.value
        },
        set(value) {
          changeWeek(value)
        }
      })
      const getWeekColor = (name) => {
        const color = props.highlightWeek && name === ongoingWeek.value ? '#00BFA5' : '#fff'

        return { color }
      }

      return { week, ensureSelectedItemVisible, getWeekColor }
    }
  }
</script>
