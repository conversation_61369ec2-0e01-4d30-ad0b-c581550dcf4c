<template>
  <div id="quarter-picker" class="picker-select">
    <v-select
      v-model="quarter"
      :items="quarters"
      :disabled="disabled"
      label="Quarter"
      color="primary"
      hide-details
      solo
      @click="ensureSelectedItemVisible"
    />
  </div>
</template>

<script>
  import { computed } from 'vue'
  import { createNamespacedHelpers } from 'vuex-composition-helpers'
  import { ensureSelectedItemVisible } from '@/utils'

  export default {
    props: {
      value: String,
      quarters: {
        type: Array,
        default: () => []
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    setup(props, { emit }) {
      const { useActions } = createNamespacedHelpers('Filters')
      const { changeQuarter } = useActions(['changeQuarter'])

      const quarter = computed({
        get() {
          return props.value
        },
        set(value) {
          changeQuarter(value)
          emit('change', value)
        }
      })

      return { quarter, ensureSelectedItemVisible }
    }
  }
</script>
