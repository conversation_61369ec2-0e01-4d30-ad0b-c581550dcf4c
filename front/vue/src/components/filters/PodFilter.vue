<template>
  <v-btn-toggle v-model="podIndex" mandatory class="ml-2">
    <v-btn
      v-for="(podItem, i) in pods"
      :key="i"
      :disabled="disabled"
      height="39"
      text
      class="btn-site"
    >
      {{ podItem }}
    </v-btn>
  </v-btn-toggle>
</template>

<script>
  import { computed, toRefs } from 'vue'
  import { createNamespacedHelpers } from 'vuex-composition-helpers'

  export default {
    props: {
      value: String,
      pods: {
        type: Array,
        default: () => []
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    setup(props) {
      const { useActions } = createNamespacedHelpers('Filters')
      const { changePod } = useActions(['changePod'])

      const { pods, value: model } = toRefs(props)
      const podIndex = computed({
        get() {
          return pods.value.indexOf(model.value)
        },
        set(index) {
          changePod(pods.value[index])
        }
      })

      return { podIndex }
    }
  }
</script>
