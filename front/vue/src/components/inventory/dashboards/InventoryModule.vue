<template>
  <div>
    <ViewToolbar :title="pageTitle">
      <ReloadButton v-if="hasUpdates" @reload="onReload" />
      <ExportButton :export-title="exportTitle" :excel-tab-title="excelTabTitle" :grid="grid" />
      <ParallelSync @sync="onSync" />
      <StateControls ref="stateControls" :resource="gridStateName" :grid-params="grid" />
    </ViewToolbar>

    <div v-show="!isFullScreen" ref="content">
      <!-- put here everything you want to hide when Fullscreen mode is on -->
      <FiltersBar>
        <template #left>
          <BrandFilter
            v-model="current.brand"
            :brands="available.brandsWithDetails"
            :disabled="consolidatedFilter.isConsolidatedView || isLoading"
          />
          <SiteFilter
            v-model="current.site"
            :sites="available.sites"
            :disabled="consolidatedFilter.isConsolidatedView || isLoading"
          />
          <ConsolidatedFilter
            v-model="consolidatedFilter.consolidatedBy.brands"
            label="Brand Consolidated View"
            :disabled="consolidatedFilter.consolidatedBy.sites"
          />
          <ConsolidatedFilter
            v-model="consolidatedFilter.consolidatedBy.sites"
            label="Site Consolidated View"
            :disabled="consolidatedFilter.consolidatedBy.brands"
          />
        </template>
      </FiltersBar>
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <div v-if="isCached" class="cached">Getting from cache...</div>
      <v-progress-linear v-if="isLoading && !isCached" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>

    <ContextualInventoryDialog
      ref="contextualInventoryDialog"
      :consolidated-filter="consolidatedFilter"
    />
  </div>
</template>

<script>
  import { nextTick, reactive, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import StateControls from '@/components/util/StateControls'
  import FiltersBar, { BrandFilter, SiteFilter, ConsolidatedFilter } from '@/components/filters'
  import ParallelSync from '@/components/util/ParallelSync'
  import ReloadButton from '@/components/util/ReloadButton'
  import ExportButton from '@/components/util/ExportButton'
  import ContextualInventoryDialog from '@/components/shared/ContextualInventoryDialog.vue'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useLayout,
    useGridScheme,
    useImtDashboardData,
    useGridState,
    useResetWeek,
    useConsolidatedFilter,
    useReload,
    useNotifications
  } from '@/hooks'
  import { inventorySchemas } from '@/schemas'
  import { inventoryResource } from '@/config/resource.config'
  import { AgAppointmentFilter, AgCaseSizeMismatchFilter } from '@/modules/grid/filters'
  import { inventoryApi } from '@/api/inventory'
  import { imtApi } from '@/api/imt'
  import { AgChevron } from '@/modules/grid/components'

  export default {
    components: {
      ViewToolbar,
      StateControls,
      FiltersBar,
      ExportButton,
      ReloadButton,
      ParallelSync,
      ContextualInventoryDialog,
      AgGridVue,
      AgAppointmentFilter,
      AgCaseSizeMismatchFilter,
      AgChevron,
      SiteFilter,
      BrandFilter,
      ConsolidatedFilter
    },
    setup() {
      const { busOn } = useEventBus()
      const { addNotification } = useNotifications()
      const { route, refs } = useContext()
      const { changeWeekToOngoing, isOngoingWeek } = useResetWeek()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { current: filter, available, actions } = useFilter({ isDashboard: true })
      const {
        grid,
        defaultGridOptions,
        defaultDetailGridOptions,
        defaultGridColDef,
        updateCellsWithinRow
      } = useGrid({ refs })
      const consolidatedFilter = useConsolidatedFilter()
      const current = reactive({
        week: computed(() => filter.ongoingWeek),
        site: computed(() => filter.site),
        brand: computed(() => filter.brand),
        prevWeek: computed(() => filter.prevWeek)
      })
      const poStatusWeeks = computed(() => {
        if (!rows.value.length) return []

        return Object.keys(rows.value[0].weekly)
      })

      async function onNoteChanged(params) {
        grid.value.api.stopEditing()

        const {
          data: { sku, lastEditedBy },
          newValue,
          oldValue
        } = params

        if (!(newValue || oldValue) || newValue === oldValue) return

        const payload = {
          brand: current.brand,
          site: current.site,
          skuCode: sku,
          comment: newValue
        }

        try {
          const { lastEditedBy, comment } = await inventoryApi.updateNote(payload)
          const dataToUpdate = {
            lastEditedBy,
            notes: comment
          }

          updateCellsWithinRow(params, dataToUpdate)
          addNotification({ text: 'The note is updated' })
        } catch (e) {
          const prevNotesData = {
            lastEditedBy,
            notes: oldValue
          }

          updateCellsWithinRow(params, prevNotesData)
          addNotification({ text: 'Note update problem', isError: true })
        }
      }

      async function onAdjustmentChanged(params) {
        grid.value.api.stopEditing()

        const {
          data,
          newValue,
          oldValue,
          colDef: { colId }
        } = params
        const dataWithUnitsInHouseAdjustment = (value) => ({
          ...data,
          weekly: {
            ...data.weekly,
            [current.week]: {
              ...data.weekly[current.week],
              unitsInHouseAdjustment: value
            }
          }
        })
        const inputValueToNumber = parseInt(newValue)

        if ((!newValue && oldValue == null) || inputValueToNumber === oldValue) {
          data.weekly[current.week][colId] = oldValue
          return
        }

        const payload = {
          brand: current.brand,
          site: current.site,
          data: {
            ...dataWithUnitsInHouseAdjustment(
              Number.isNaN(inputValueToNumber) ? newValue || null : inputValueToNumber
            )
          }
        }

        try {
          const data = await inventoryApi.updateAdjustment(payload)

          updateCellsWithinRow(params, data)
          addNotification({ text: 'In house adjustment successfully changed' })
        } catch (e) {
          updateCellsWithinRow(params, dataWithUnitsInHouseAdjustment(oldValue))
          addNotification({
            text: 'There was a problem changing in house adjustment. Please try again',
            isError: true
          })
        }
      }

      const {
        isData,
        rows,
        isLoading,
        isCached,
        isDataCached,
        resetCache,
        setData,
        loadDashboardData,
        warnings
      } = useImtDashboardData({ current, consolidatedFilter })
      const { storePrevGridColState, gridStateName } = useGridState({ route, refs })
      const { schemas } = useGridScheme(inventorySchemas.inventoryModule, {
        isData,
        rows,
        current,
        consolidatedFilter,
        onNoteChanged,
        onAdjustmentChanged,
        getInventoryData
      })

      async function getData() {
        storePrevGridColState()

        const shouldLoadData = !isDataCached()

        if (!shouldLoadData) return

        isLoading.value = true

        const response = await loadDashboardData(inventoryResource.inventoryModule)

        setData(response)
        warnings.value && warnings.value.forEach((text) => addNotification({ text, isError: true }))
        nextTick(() => (isLoading.value = false))
      }

      async function getPoStatusDataBySkuCode(inputParams) {
        const skuCode = inputParams.node.data.sku
        const dc = consolidatedFilter.isConsolidatedView ? inputParams.node.data.dc : current.site
        const brand = consolidatedFilter.isConsolidatedView
          ? inputParams.node.data.brand
          : current.brand
        const params = {
          week: poStatusWeeks.value.join(','),
          dc,
          brand,
          skuCode
        }
        const rows = await imtApi.poStatus.getContextualPo(params)

        inputParams.successCallback(rows)
      }

      async function getInventoryData({
        node: {
          data: { sku, brand, dc }
        }
      }) {
        const params = {
          week: current.week,
          sku,
          includeWarehouseInventory: true,
          ...(consolidatedFilter.isConsolidatedView
            ? { brand, site: dc }
            : { brand: current.brand, site: current.site })
        }

        refs.contextualInventoryDialog.open({ params, url: inventoryResource.siteUnifiedInventory })
      }

      const { hasUpdates, onSync, onReload } = useReload({
        syncCb: resetCache,
        reloadCb: getData
      })

      busOn('filter-changed', getData)

      if (isOngoingWeek && current.week) {
        actions.changeFilter()
      } else {
        changeWeekToOngoing()
      }

      return {
        isFullScreen,
        gridHeight,
        current,
        available,
        defaultGridOptions,
        defaultDetailGridOptions,
        defaultGridColDef,
        grid,
        rows,
        isData,
        isLoading,
        isCached,
        gridStateName,
        schemas,
        getPoStatusDataBySkuCode,
        consolidatedFilter,
        hasUpdates,
        onSync,
        onReload
      }
    },
    computed: {
      pageTitle() {
        return `Inventory Module ${
          this.consolidatedFilter.isConsolidatedView ? 'Consolidated' : ''
        } ${this.current.week}`
      },
      excelTabTitle() {
        return `InventoryModule_${
          this.consolidatedFilter.isConsolidatedView ? 'Consolidated' : `${this.current.site}`
        }`
      },
      exportTitle() {
        return `InventoryModule_${
          this.consolidatedFilter.isConsolidatedView
            ? 'Consolidated'
            : `${this.current.site}_${this.current.brand}`
        }`
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          masterDetail: true,
          detailCellRendererParams: {
            detailGridOptions: {
              ...this.defaultDetailGridOptions,
              defaultColDef: this.defaultGridColDef,
              columnDefs: this.schemas.poStatus
            },
            getDetailRowData: this.getPoStatusDataBySkuCode.bind(this)
          }
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>

<style scoped></style>
