<template>
  <div>
    <ViewToolbar :title="pageTitle">
      <ReloadButton v-if="hasUpdates" @reload="onReload" />
      <ExportButton :export-title="exportTitle" :grid="grid" />
      <ParallelSync @sync="onSync" />
      <Warnings :items="warnings" />
      <StateControls ref="stateControls" :resource="gridStateName" :grid-params="grid" />
    </ViewToolbar>

    <!-- put here everything you want to hide when Fullscreen mode is on -->
    <div v-show="!isFullScreen" ref="content"></div>

    <!-- put here only grid -->
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import { ref, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import ParallelSync from '@/components/util/ParallelSync'
  import ReloadButton from '@/components/util/ReloadButton'
  import Warnings from '@/components/util/Warnings'
  import StateControls from '@/components/util/StateControls'
  import ExportButton from '@/components/util/ExportButton'
  import {
    useContext,
    useGrid,
    useGridScheme,
    useGridState,
    useLayout,
    usePermissions,
    useReload
  } from '@/hooks'
  import { permissionsConfig } from '@/config/permissions.config'
  import { inventorySchemas } from '@/schemas'
  import { inventoryApi } from '@/api/inventory'

  export default {
    // extend current context with common grid-related configuration
    components: {
      ParallelSync,
      ReloadButton,
      Warnings,
      StateControls,
      ViewToolbar,
      ExportButton,
      AgGridVue
    },
    setup() {
      usePermissions(permissionsConfig.unifiedInventory.read)
      const { route, refs } = useContext()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs })
      const { storePrevGridColState, gridStateName } = useGridState({ route, refs })

      const rows = ref([])
      const warnings = ref([])
      const isLoading = ref(false)
      const isData = computed(() => rows.value.length)
      const { schemas } = useGridScheme(inventorySchemas.unifiedInventory, { isData })

      async function getData() {
        storePrevGridColState()
        isLoading.value = true
        const { data, warnings = [] } = await inventoryApi.getUnifiedInventory()
        rows.value = data
        warnings.value = warnings
        isLoading.value = false
      }

      const { hasUpdates, onSync, onReload } = useReload({
        reloadCb: getData
      })

      getData()

      return {
        isFullScreen,
        gridHeight,
        defaultGridOptions,
        defaultGridColDef,
        grid,
        gridStateName,
        schemas,
        rows,
        isData,
        isLoading,
        warnings,
        hasUpdates,
        onSync,
        onReload
      }
    },
    computed: {
      gridOptions() {
        return {
          ...this.defaultGridOptions
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      },
      pageTitle() {
        return 'Unified Inventory Module'
      },
      exportTitle() {
        return 'UnifiedInventory'
      }
    }
  }
</script>

<style scoped></style>
