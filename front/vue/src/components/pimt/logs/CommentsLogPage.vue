<template>
  <div class="form-page">
    <div ref="content">
      <ViewToolbar :title="pageTitle" />
      <FiltersBar>
        <template #left>
          <CommentLogFilter v-model="current.commentLogType" :types="available.commentLogTypes" />
        </template>
        <template #right>
          <WeekFilter v-model="current.week" :highlight-week="false" />
        </template>
      </FiltersBar>
    </div>

    <!-- put here only grid -->
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          :key="1"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="schemas.main"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import { ref, computed } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import { AgLogTooltip } from '@/modules/grid/components'
  import FiltersBar, { WeekFilter, CommentLogFilter } from '@/components/filters'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import {
    useFilter,
    useContext,
    useGrid,
    useLayout,
    useEventBus,
    useGridScheme,
    usePermissions
  } from '@/hooks'
  import { pimtApi } from '@/api/pimt'
  import { permissionsConfig } from '@/config/permissions.config'
  import { getDefaultContextMenuItems } from '@/modules/dashboards'
  import { pimtSchemas } from '@/schemas'
  import { debounce } from '@/utils'
  import { resizeColumnsIfNeeded } from '@/modules/grid/helpers'

  export default {
    name: 'PimtCommentsLog',
    components: {
      FiltersBar,
      CommentLogFilter,
      WeekFilter,
      ViewToolbar,
      AgGridVue,
      AgLogTooltip
    },
    setup() {
      usePermissions(permissionsConfig.imtCommentLog.read)
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { current, available } = useFilter({ isDashboard: true, isWeekConfigNeeded: false })
      const { defaultGridOptions, defaultGridColDef, grid } = useGrid({ refs })
      const { gridHeight } = useLayout(refs)

      const rows = ref([])
      const isLoading = ref(false)
      const isData = computed(() => rows.value.length)
      const isPoCommentLog = computed(() => current.commentLogType === 'PO')
      const { schemas } = useGridScheme(pimtSchemas.commentsLog, {
        isPoCommentLog
      })

      async function getData() {
        if (!current.week) return

        isLoading.value = true

        const commentLogType = current.commentLogType.toLowerCase()

        const { data: comments } = await pimtApi.getPoCommentLog(current.week, commentLogType)
        const { data: partners } = await pimtApi.getPartners()
        const partnersMap = partners.reduce(
          (acc, { partnerCode, name }) => ({ ...acc, [partnerCode]: name }),
          {}
        )

        comments.forEach((comment) => (comment.site = partnersMap[comment.site]))
        rows.value = comments

        isLoading.value = false
      }

      getData()

      // subscribers
      busOn('grid-ready', () => resizeColumnsIfNeeded(grid))
      busOn(
        'grid-size-changed',
        debounce(() => resizeColumnsIfNeeded(grid))
      )
      busOn('filter-changed', getData)

      return {
        current,
        available,
        defaultGridOptions,
        defaultGridColDef,
        gridHeight,
        isLoading,
        isData,
        rows,
        schemas
      }
    },
    computed: {
      pageTitle() {
        return 'Comments Log'
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          tooltipShowDelay: 0,
          tooltipMouseTrack: true,
          getContextMenuItems: (params) => getDefaultContextMenuItems(params)
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      }
    }
  }
</script>
