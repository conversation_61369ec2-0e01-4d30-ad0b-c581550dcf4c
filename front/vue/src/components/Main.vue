<template>
  <div class="home">
    <ViewToolbar :title="pageTitle" :is-expand-btn="false" />
    <v-alert v-if="!isBrowserChrome" dense tile color="warning" icon="mdi-alert">
      The recommended browser for this application is Google Chrome.
    </v-alert>
    <div class="content" :class="{ 'alert-is-active': !isBrowserChrome }">
      <div class="home-top" :class="{ 'full-height': !isUserBuyer }">
        <ReleaseLogHome />
        <HomeTiles />
      </div>
      <div v-if="isUserBuyer" class="home-bottom">
        <BuyerCriticalItems />
      </div>
    </div>
  </div>
</template>

<script>
  import { computed } from 'vue'
  import { useNamespacedGetters, useNamespacedState } from 'vuex-composition-helpers'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import HomeTiles from '@/components/util/HomeTiles'
  import ReleaseLogHome from '@/components/shared/ReleaseLogHome'
  import BuyerCriticalItems from '@/components/buyer/dashboards/BuyerCriticalItemsPage'

  export default {
    name: 'Main',
    components: {
      ViewToolbar,
      HomeTiles,
      ReleaseLogHome,
      BuyerCriticalItems
    },
    setup() {
      const { user } = useNamespacedState('User', ['user'])
      const { country } = useNamespacedGetters('Common', ['country'])
      const isBrowserChrome = computed(
        () => /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
      )

      const userName = computed(() => (user.value && user.value.name) || 'User')
      const pageTitle = computed(
        () => `${userName.value}, Welcome to Inventory Management ${country.value}`
      )
      const isUserBuyer = computed(() => (user.value && user.value.isBuyer) || false)

      return {
        isBrowserChrome,
        pageTitle,
        isUserBuyer
      }
    }
  }
</script>

<style lang="scss">
  .home {
    height: calc(100vh - 36px);
  }

  .content {
    height: calc(100vh - 76px);
    display: flex;
    flex-direction: column;

    &.alert-is-active {
      height: calc(100vh - 132px);
    }
  }

  .home-top {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    height: 100%;
  }

  .home-top-wrapper {
    display: flex;
    height: 100%;
  }

  .home-bottom {
    height: 360px;
  }

  .full-height {
    height: 100%;
  }
</style>
