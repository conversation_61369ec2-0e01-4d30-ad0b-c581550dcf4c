<template>
  <v-container class="site-group-container">
    <v-row v-for="(row, i) in params.grid" :key="i" no-gutters>
      <v-col v-for="(item, y) in row" :key="y">
        <v-tooltip v-if="item.toolTip" bottom>
          <template #activator="{ on, attrs }">
            <v-card
              :class="itemClass(item)"
              outlined
              tile
              :style="getStyle(item)"
              v-bind="attrs"
              v-on="on"
            >
              <span :style="getTextStyle(item)"> {{ item.value }}</span>
            </v-card>
          </template>
          <span>{{ item.toolTip }}</span>
        </v-tooltip>
        <v-card v-if="!item.toolTip" :class="itemClass(item)" outlined tile :style="getStyle(item)">
          <span :style="getTextStyle(item)">{{ item.value }}</span>
          <v-btn
            v-if="item.isExpandable"
            icon
            tile
            height="22"
            width="22"
            @click="toggleColumnsVisibility"
          >
            <v-icon size="20">
              {{ expandIcon }}
            </v-icon>
          </v-btn>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  export default {
    name: 'GridGroupHeaderComponent',
    data: function () {
      return {
        isGroupExpanded: false
      }
    },
    computed: {
      expandIcon() {
        return this.isGroupExpanded ? 'mdi-chevron-left' : 'mdi-chevron-right'
      }
    },
    methods: {
      toggleColumnsVisibility() {
        this.isGroupExpanded = !this.params.columnGroup.isExpanded()

        return this.params.setExpanded(this.isGroupExpanded)
      },
      getStyle(item) {
        let style = {}
        if (item.height) {
          style['min-height'] = item.height + 'px'
        }
        return style
      },
      getTextStyle(item) {
        let style = {}
        let align = item.align
        if (align === 'right') {
          style['margin-left'] = 'auto'
          style['padding-right'] = '1%'
        } else if (align === 'left') {
          style['margin-right'] = 'auto'
          style['padding-left'] = '1%'
        }
        return style
      },
      itemClass({ main, requireHighAttention, cssClass = '' }) {
        const groupClass = (main && 'main-site-group-item') || 'site-group-item'
        const highAttentionClass = (requireHighAttention && 'high-attention') || ''

        return `${groupClass} ${highAttentionClass} ${cssClass}`
      }
    }
  }
</script>

<style lang="scss">
  .site-group-container {
    padding: 0;
  }
  .site-group-item {
    @extend .ag-theme-alpine-dark !optional;
    font-weight: 500;
    font-size: 12px;
    color: #fff;
    overflow: hidden;
    white-space: normal;
    vertical-align: middle;
    justify-content: center;
    align-items: center;
    display: flex;
  }
  .theme--dark.v-card.main-site-group-item {
    @extend .site-group-item;
    background-color: #1866c0 !important;
    font-weight: bold;
    text-align: center;
    font-size: 11px;
    line-height: 11px;
    padding: 0 2px;
  }

  .theme--dark.v-card.high-attention {
    background-color: var(--ag-high-attention-bg-color) !important;
  }
</style>
