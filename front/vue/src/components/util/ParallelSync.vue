<template>
  <div>
    <v-menu
      open-on-hover
      close-delay="100"
      nudge-left="20"
      offset-y
      tile
      min-width="200"
      transition="v-menu-transition"
    >
      <template #activator="{ on }">
        <v-badge dot overlap class="badge-dot" :color="color" :class="isLoading && 'sync-loading'">
          <v-btn icon tile height="38" width="42" v-on="on">
            <v-icon size="20"> mdi-cached </v-icon>
          </v-btn>
        </v-badge>
      </template>
      <div class="sync-popover">
        <div class="sync-info">
          <div v-if="successSyncTime" class="sync-text">
            <v-icon class="icon-align" small> mdi-timer </v-icon> Last updated at
            <b>{{ successSyncTime }}</b>
          </div>
          <div v-if="failedSyncTime" class="sync-text">
            <v-icon class="icon-align" small> mdi-timer </v-icon> Last run at
            <b>{{ failedSyncTime }}</b> was failed
          </div>
          <div v-if="!isFresh && !isLoading && time" class="sync-text">
            <v-icon class="icon-align" small> mdi-alert-circle-outline </v-icon> Data is
            <b>not fresh</b>
          </div>

          <div v-if="isNeverStarted" class="sync-text">
            <v-icon class="icon-align" small> mdi-alert-circle-outline </v-icon> Sync is
            <b>not started</b> yet
          </div>

          <div v-if="errorsCount || warningsCount" class="sync-count">
            <span v-if="errorsCount"
              ><v-icon class="icon-align" x-small color="#f44336">mdi-moon-full</v-icon>
              <b>{{ errorsCount }}</b> {{ errorNoun }}</span
            >
            <span v-if="warningsCount"
              ><v-icon class="icon-align" x-small color="#FF8F00">mdi-moon-full</v-icon>
              <b>{{ warningsCount }}</b> {{ warningNoun }}</span
            >
          </div>

          <div class="sync-actions">
            <v-btn v-if="logs.length" class="mb-2" @click="openLogs"> Show logs </v-btn>
            <v-btn v-if="!isLoading" color="#7e57c2" @click="updateStocks"> Refresh data </v-btn>
          </div>
        </div>
      </div>
    </v-menu>

    <!-- logs popup -->
    <v-dialog v-model="isDialog" scrollable width="900px">
      <v-card id="logs-info">
        <v-card-actions>
          <div class="sync-count ml-4">
            <span v-if="isLoading"
              ><v-icon class="icon-align" x-small color="#259ae0">mdi-moon-full</v-icon> Sync is in
              progress
            </span>
            <span v-else class="mr-2">
              <span v-if="time"
                ><v-icon class="icon-align" small>mdi-timer</v-icon> {{ time }}</span
              >
            </span>

            <span v-if="errorsCount"
              ><v-icon class="icon-align" x-small color="#f44336">mdi-moon-full</v-icon>
              <b>{{ errorsCount }}</b> {{ errorNoun }}</span
            >
            <span v-if="warningsCount"
              ><v-icon class="icon-align" x-small color="#FF8F00">mdi-moon-full</v-icon>
              <b>{{ warningsCount }}</b> {{ warningNoun }}</span
            >
          </div>
          <v-spacer />
          <v-btn class="mr-2" icon dark @click="closeLogs">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-actions>
        <v-card-text>
          <v-treeview open-all multiple-active hoverable :items="logs">
            <template #prepend="{ item }">
              <v-icon
                class="icon-align"
                :class="item.status === STATUS_ENUM.IN_PROGRESS && 'dot-animated'"
                x-small
                :color="getColorByJob(item)"
              >
                mdi-moon-full
              </v-icon>
            </template>
            <template #append="{ item }">
              <v-btn
                v-if="item.message"
                :color="getColorByJob(item)"
                x-small
                @click="openJobDetails(item)"
              >
                details
              </v-btn>
            </template>
          </v-treeview>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- logs details popup -->
    <v-dialog
      v-model="isDetailsOpened"
      fullscreen
      hide-overlay
      scrollable
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar dark :color="getColorByJob(job)" class="sync-toolbar">
          <v-btn class="ml-2" icon dark @click="isDetailsOpened = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title>{{ job.name }}</v-toolbar-title>
        </v-toolbar>
        <v-card-text>
          <div class="jobs-log">
            <div v-for="(item, index) of job.message" :key="index" class="log-item">
              <div v-if="item.message"><b>Message:</b> {{ item.message }}</div>
              <div v-if="item.error"><b>Error:</b> {{ item.error }}</div>
              <div v-if="item.line"><b>Line:</b> {{ item.line }}</div>
              <div v-if="item.dc"><b>DC:</b> {{ item.dc }}</div>
              <div v-if="item.tab"><b>Tab:</b> {{ item.tab }}</div>
              <div v-if="item.value"><b>Source:</b> {{ item.value }}</div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  import axios from 'axios'
  import { mapGetters } from 'vuex'
  import { formatDate } from '@/modules/date'
  import { commonResource } from '@/config/resource.config'
  import { useFilter } from '@/hooks'

  const PULL_TIMEOUT = {
    checkStart: 20000,
    checkEnd: 10000
  }

  export default {
    setup() {
      const { current } = useFilter()

      return {
        domain: current.domain
      }
    },
    data() {
      return {
        timer: null,
        isUnspecified: true,
        isDialog: false,
        isLoading: false,
        isFresh: false,
        isDetailsOpened: false,
        lastSync: {},
        job: {},
        STATUS_ENUM: {
          IN_PROGRESS: 'in_progress',
          SUCCESS: 'success',
          FAILED: 'failed',
          QUEUED: 'queued'
        }
      }
    },
    computed: {
      ...mapGetters({
        week: ['Filters/week']
      }),
      syncParams() {
        return this.domain === 'imt' ? { week: this.week } : {}
      },
      apiUrl() {
        return commonResource.sync(this.domain)
      },
      syncApiUrl() {
        return commonResource.syncJob(this.domain)
      },
      color() {
        switch (true) {
          case this.isUnspecified:
            return 'transparent'
          case this.isNeverStarted:
            return '#888'
          case this.isLoading:
            return '#259ae0'
          case this.isError:
            return '#f44336'
          case !this.isFresh:
            return '#b975c5'
          case this.isWarning:
            return '#FF8F00'
          default:
            return '#5aa519'
        }
      },
      errorsCount() {
        return this.lastSync.errors
      },
      warningsCount() {
        return this.lastSync.warnings
      },
      errorNoun() {
        return this.errorsCount && this.errorsCount > 1 ? 'Errors' : 'Error'
      },
      warningNoun() {
        return this.warningsCount && this.warningsCount > 1 ? 'Warnings' : 'Warning'
      },
      isNeverStarted() {
        return !this.isLoading && !this.isFresh && !this.time && !this.logs.length
      },
      isError() {
        return !!this.errorsCount
      },
      isWarning() {
        return !!this.warningsCount
      },
      time() {
        return formatDate(this.lastSync.time)
      },
      successSyncTime() {
        return !this.isError && this.time
      },
      failedSyncTime() {
        return this.isError && this.time
      },
      logs() {
        return this.lastSync.jobs ? this.mapLogs(this.lastSync.jobs) : []
      }
    },
    watch: {
      week() {
        this.isUnspecified = true
        this.sync()
        this.pull()
      }
    },
    mounted() {
      this.sync()
      this.pull()
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    methods: {
      openLogs() {
        this.isDialog = true
      },
      closeLogs() {
        this.isDialog = false
      },
      mapLogs(logs = []) {
        return logs.map((log) => {
          const item = {
            name: log.job_name,
            status: log.status,
            message: log.warning
          }

          item.children = log.dependants.length ? this.mapLogs(log.dependants) : []

          return item
        })
      },
      pull(checkEnd = false) {
        const timeout = checkEnd ? PULL_TIMEOUT.checkEnd : PULL_TIMEOUT.checkStart

        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }

        this.timer = setInterval(this.sync.bind(this), timeout)
      },
      reset() {
        this.lastSync = {}
        this.isFresh = false
      },
      async updateStocks() {
        this.isLoading = true

        await axios.post(this.apiUrl, this.syncParams)
        this.sync()
      },
      async sync() {
        let response

        try {
          response = await axios.get(this.syncApiUrl, { params: this.syncParams })
        } catch (e) {}

        this.isUnspecified = false
        this.reset()

        if (!(response && response.data && response.data.data)) {
          this.isLoading = false
          return
        }

        const { is_fresh: isFresh, last_sync: lastSync } = response.data.data

        this.isFresh = isFresh
        this.lastSync = lastSync

        const { in_progress: isInProgress = false } = lastSync
        const syncWasInProgress = this.isLoading

        if (isInProgress) {
          this.isLoading = true

          if (!syncWasInProgress) {
            this.pull(true) // check sync end
          }
          return
        }

        this.isLoading = false

        if (syncWasInProgress) {
          this.$emit('sync')
          this.pull() // get back to regular pulling
        }
      },
      openJobDetails(job) {
        this.job = job
        this.isDetailsOpened = true
      },
      getColorByJob(job) {
        const { status, message } = job

        if (message && status !== this.STATUS_ENUM.FAILED) return '#FF8F00'

        switch (status) {
          case this.STATUS_ENUM.IN_PROGRESS:
            return '#259ae0'
          case this.STATUS_ENUM.FAILED:
            return '#f44336'
          case this.STATUS_ENUM.QUEUED:
            return '#999'
          default:
            return '#5aa519'
        }
      }
    }
  }
</script>

<style lang="scss">
  .sync {
    &-popover {
      padding-top: 10px;
    }

    &-info {
      padding: 15px;
      border-radius: 5px;
      background-color: rgba(0, 0, 0, 0.9);
    }

    &-loading {
      .v-badge__badge {
        animation: dot 2s infinite;
      }
    }

    &-status {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 2px;
      top: -4px;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      font-size: 10px;
      line-height: 10px;
      background-color: #f44336;
      color: #fff;
    }

    &-text {
      font-size: 12px;
      margin-bottom: 10px;
    }

    &-actions {
      display: flex;
      flex-direction: column;
    }

    &-toolbar {
      flex-grow: 0;
    }

    &-count {
      margin-bottom: 15px;

      span {
        font-size: 13px;
        margin-bottom: 5px;
      }

      span + span {
        margin-left: 5px;
      }
    }
  }

  .badge-dot .v-badge__wrapper {
    margin-left: -5px;
    margin-top: 6px;

    span {
      height: 8px;
      width: 8px;
      border-radius: 50%;
    }
  }

  .v-treeview-node__root {
    border: 1px solid rgba(0, 0, 0, 0.2);
  }

  .dot-animated {
    animation: dot 2s infinite;
  }

  .jobs-log {
    margin: 20px 5px;
  }

  .log-item {
    font-size: 13px;
    line-height: 16px;
    padding-top: 20px;
    margin-bottom: 20px;

    b {
      padding-right: 5px;
    }
  }

  .log-item + .log-item {
    border-top: 1px solid #5f5f5f;
  }

  .logs-status-row {
    padding: 30px 0 10px;
    color: #fff;
  }

  .icon-align {
    position: relative;
    top: -1px;
  }

  @keyframes dot {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
</style>
