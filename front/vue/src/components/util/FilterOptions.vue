<template>
  <div>
    <v-menu
      open-on-hover
      close-delay="100"
      :close-on-content-click="false"
      nudge-left="20"
      offset-y
      tile
      min-width="240"
      transition="v-menu-transition"
    >
      <template #activator="{ on }">
        <v-btn id="filters" :disabled="!gridParams" icon tile height="38" width="42" v-on="on">
          <v-icon size="20"> mdi-filter-menu-outline </v-icon>
        </v-btn>
      </template>
      <div class="filter-options-popover">
        <div class="filter-options-info">
          <section>
            <div class="filter-options-title mb-2">Actions:</div>
            <v-btn class="mb-2" color="primary" small @click="resetFilter">
              <v-icon left> mdi-filter-remove-outline </v-icon>
              Reset filters
            </v-btn>
          </section>
          <section>
            <div class="filter-options-title">Options:</div>
            <v-radio-group v-model="treeFilter" dense class="filter-options-radio">
              <v-radio label="Filter parent level only" value="parentOnly"></v-radio>
              <v-radio label="Filter all levels" value="default"></v-radio>
            </v-radio-group>
          </section>
        </div>
      </div>
    </v-menu>
  </div>
</template>

<script>
  import { computed } from 'vue'
  import { createNamespacedHelpers } from 'vuex-composition-helpers'

  export default {
    props: {
      value: {
        type: Object,
        required: true
      },
      gridParams: Object
    },
    setup(props) {
      const { useActions } = createNamespacedHelpers('Filters')
      const { changeFilterOptions } = useActions(['changeFilterOptions'])

      function resetFilter() {
        props.gridParams.api.setFilterModel(null)
      }

      function applyFilter() {
        props.gridParams.api.onFilterChanged()
      }

      const treeFilter = computed({
        get() {
          return props.value.treeFilter
        },
        set(value) {
          changeFilterOptions({
            ...props.value,
            treeFilter: value
          })
          applyFilter()
        }
      })

      return { treeFilter, resetFilter }
    }
  }
</script>

<style lang="scss">
  .filter-options {
    &-popover {
      padding-top: 10px;

      .v-radio {
        margin-bottom: 4px !important;
      }

      .v-label {
        color: #fff;
        font-size: 13px;
      }
    }

    &-info {
      padding: 1px 15px 0 15px;
      border-radius: 5px;
      background-color: rgba(0, 0, 0, 0.9);
    }

    &-title {
      letter-spacing: 0.01em;
      border-bottom: 1px solid #626161;
      font-size: 14px;
      padding-top: 5px;
      padding-bottom: 2px;
    }

    .v-input--selection-controls {
      margin-top: 12px;
    }
  }
</style>
