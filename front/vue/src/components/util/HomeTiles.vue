<template>
  <div class="home-tales" :class="{ 'blur-the-bottom': isScrollAndNotEnd }">
    <div class="area-with-scroll" ref="scrollContainer" @scroll="updateScrollPosition">
      <div class="subtitle-1 mb-1">
        <span class="mr-2">Application overview</span><v-icon small> mdi-school </v-icon>
      </div>
      <masonry :cols="{ default: 4, 1920: 3, 1540: 2 }" :gutter="10">
        <router-link v-for="(tile, index) in tiles" :key="index" :to="tile.path" class="h-tile">
          <div class="h-tile-top">
            <v-chip small :color="tile.color">{{ tile.name }}</v-chip>
            <div class="h-tile-category">{{ tile.category }}</div>
          </div>
          <div class="h-tile-content">{{ tile.description }}</div>
        </router-link>
      </masonry>
    </div>
  </div>
</template>

<script>
  import { dashboardList } from '@/config/tiles.config'
  import { computed } from 'vue'
  import router from '@/router'
  import { usePermissions, useContext, useScrollChecker } from '@/hooks'
  export default {
    setup() {
      const { refs } = useContext()
      const { isScrollAndNotEnd, updateScrollPosition } = useScrollChecker(refs)

      const { hasPermission } = usePermissions()

      const tiles = computed(() => {
        return dashboardList.filter(
          ({ permissions, isHidden }) => !isHidden && hasPermission(permissions)
        )
      })

      const navigateTo = (path) => {
        router.push(path)
      }

      return {
        navigateTo,
        tiles,
        isScrollAndNotEnd,
        updateScrollPosition
      }
    }
  }
</script>

<style lang="scss" scoped>
  .home-tales {
    flex: 1;
    height: 100%;
    position: relative;
  }

  .h-tile {
    display: block;
    background-color: #222;
    border-radius: 5px;
    padding: 15px;
    text-decoration: none;
    color: #fff;
    font-size: 12px;
    margin-bottom: 10px;

    .v-chip {
      cursor: pointer;
    }
  }

  .h-tile-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #313231;
    margin-bottom: 10px;
  }

  .h-tile-category {
    font-weight: bold;
  }

  .h-tile-content {
    font-size: 12px;
    color: #aaa;
  }
</style>
