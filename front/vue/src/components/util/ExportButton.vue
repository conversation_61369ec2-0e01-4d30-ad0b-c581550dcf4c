<template #export-btn>
  <div class="text-center">
    <v-menu offset-y :close-on-content-click="false" nudge-left="9" open-on-hover>
      <template #activator="{ on }">
        <v-btn
          id="exportFile"
          height="38"
          width="42"
          icon
          tile
          :disabled="isExportBtnDisabled || !grid"
          v-on="on"
        >
          <v-icon size="20"> mdi-table-arrow-right </v-icon>
        </v-btn>
      </template>
      <v-list class="export-list">
        <v-list-item
          class="export-item"
          v-for="(item, index) in exportOptions"
          @click="item.action"
          :key="index"
        >
          <v-list-item-title class="export-title">
            {{ item.title }}
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
  import { toRefs } from 'vue'

  export default {
    props: {
      exportTitle: {
        type: String,
        default: ''
      },
      excelTabTitle: {
        type: String,
        default: ''
      },
      grid: {
        type: Object,
        default: () => ({
          api: {},
          columnApi: {},
          type: {}
        })
      },
      isExportBtnDisabled: {
        type: Boolean,
        default: false
      },
      exportParams: {
        type: Object,
        default: () => {}
      }
    },
    setup(props) {
      const { grid, exportParams, exportTitle, excelTabTitle } = toRefs(props)

      const exportOptions = [
        {
          title: '.csv',
          action: exportAsCsv
        },
        {
          title: '.xlsx',
          action: exportAsExcel
        }
      ]

      function exportAsCsv() {
        grid.value &&
          grid.value.api.exportDataAsCsv({
            ...exportParams.value,
            fileName: `${exportTitle.value}.csv`
          })
      }
      function exportAsExcel() {
        grid.value &&
          grid.value.api.exportDataAsExcel({
            ...exportParams.value,
            fileName: `${exportTitle.value}.xlsx`,
            sheetName: excelTabTitle.value || exportTitle.value
          })
      }

      return {
        exportAsCsv,
        exportAsExcel,
        exportOptions
      }
    }
  }
</script>
