<template>
  <v-alert v-if="isReleaseWarningActive" dense class="mb-0 rounded-0" type="info">
    <v-row align="center">
      <v-col> New app version is available: {{ releaseVersion }}. Please, refresh the page </v-col>
      <v-col class="shrink">
        <v-btn small @click="refreshPage"> Refresh </v-btn>
      </v-col>
    </v-row>
  </v-alert>
</template>

<script>
  import { wsEvents } from '@/config/ws.config'
  import { ref } from 'vue'
  import { createNamespacedHelpers, useNamespacedMutations } from 'vuex-composition-helpers'
  import { useSockets } from '@/hooks'
  import envConfig from '@/config/env.config'

  const { useState } = createNamespacedHelpers('Common')

  export default {
    name: 'ReleaseWarning',
    setup() {
      const { isReleaseWarningActive } = useState(['isReleaseWarningActive'])
      const { showReleaseWarning } = useNamespacedMutations('Common', ['showReleaseWarning'])
      const { subscribeToSocketsEvent } = useSockets()

      const releaseVersion = ref('')

      if (envConfig.WEBSOCKET_ENABLED) {
        subscribeToSocketsEvent(wsEvents.release, ({ version }) => getReleaseVersion(version))
      }

      function getReleaseVersion(version) {
        releaseVersion.value = version
        showReleaseWarning()
      }

      function refreshPage() {
        window.location.reload(true)
      }

      return {
        isReleaseWarningActive,
        releaseVersion,
        refreshPage
      }
    }
  }
</script>
