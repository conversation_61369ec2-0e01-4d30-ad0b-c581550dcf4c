<template>
  <v-dialog v-model="isFeatureDialog" max-width="700px">
    <v-form id="feature-dialog" ref="featureForm" v-model="isFeatureFormValid">
      <v-card>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col md="5">
                <v-select
                  v-model="editedItem.type"
                  :items="featureTypes"
                  :rules="[rules.required]"
                  label="Feature Type"
                  item-text="value"
                  item-value="key"
                  required
                />
              </v-col>
              <v-spacer />
              <v-col class="col-auto">
                <v-checkbox v-model="editedItem.dcUsersAvailable" label="Is visible for DC user" />
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <tiptap-vuetify
                  v-model="editedItem.description"
                  :extensions="extensions"
                  placeholder="Write something …"
                />
              </v-col>
            </v-row>
            <v-row>
              <v-spacer />
              <v-col class="col-auto">
                <v-btn color="grey" @click="isFeatureDialog = false">Cancel</v-btn>
                <v-btn
                  color="primary"
                  :disabled="!isFeatureFormValid"
                  @click="onFeatureSubmit"
                  class="ml-2"
                  >Save</v-btn
                >
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-form>
  </v-dialog>
</template>

<script>
  import { ref } from 'vue'
  import { useEditor } from '@/hooks'
  import { featureTypes } from './featureTypes'

  const { TiptapVuetify, extensions } = useEditor()

  export default {
    components: {
      TiptapVuetify
    },
    setup(_, { emit }) {
      const isFeatureDialog = ref(false)
      const isFeatureFormValid = ref(false)
      const rules = ref({
        required: (value) => !!value || 'Required'
      })
      const defaultItem = {
        type: 'NEW',
        description: '',
        dcUsersAvailable: true
      }
      const editedItem = ref({ ...defaultItem })

      function create() {
        isFeatureDialog.value = true
        editedItem.value = { ...defaultItem }
      }

      function edit(payload) {
        isFeatureDialog.value = true
        editedItem.value = { ...payload }
      }

      function onFeatureSubmit() {
        emit('change', editedItem.value)
        isFeatureDialog.value = false
      }

      return {
        isFeatureDialog,
        isFeatureFormValid,
        rules,
        featureTypes,
        editedItem,
        extensions,
        onFeatureSubmit,
        create,
        edit
      }
    }
  }
</script>

<style lang="scss">
  .release-dialog-edit {
    background: #303030;
  }

  .content-view {
    background-color: #333;
    padding: 10px;
    margin: 6px 0;
    border-radius: 5px;

    p {
      margin: 0;
      line-height: 20px;
      font-size: 12px;
      min-height: 20px;
    }
  }
</style>
