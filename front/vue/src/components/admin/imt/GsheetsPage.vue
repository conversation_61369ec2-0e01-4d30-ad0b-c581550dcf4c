<template>
  <div>
    <div ref="container" class="view-container">
      <div class="view-actions">
        <ViewToolbar :title="pageTitle">
          <v-btn
            class="btn-sync"
            dark
            x-small
            rounded
            :color="statusColor"
            :disabled="readonly"
            @click="refreshAll()"
          >
            <v-icon>{{ statusIcon }}</v-icon>
            <span>{{ statusText }}</span>
          </v-btn>
        </ViewToolbar>
        <FiltersBar>
          <template #left>
            <BrandFilter
              v-model="current.brand"
              :brands="available.brandsWithDetails"
              :disabled="isLoading || !current.week"
            />
          </template>
          <template #right>
            <WeekFilter v-model="current.week" />
          </template>
        </FiltersBar>
      </div>
    </div>

    <v-container>
      <div v-for="(gsheetsData, type) of gsheets" :key="type">
        <v-row>
          <v-col cols="8">
            <h5 class="display-5">
              {{ capitalizeFirstLetter(type) }}
            </h5>
          </v-col>
          <v-spacer />
        </v-row>
        <v-row v-for="gsheet in gsheetsData" :key="gsheet.id">
          <v-col>
            <v-text-field
              :color="gsheet.color"
              :error="gsheet.error"
              :success="gsheet.success"
              :hint="gsheet.errorMessage"
              :label="gsheet.name"
              :loading="gsheet.loading"
              :prepend-icon="inputPrependIcon(gsheet.success)"
              :disabled="readonly || gsheet.loading"
              :value="gsheet.url"
              :rules="[rules.gsheetUrlValidation]"
              :ref="gsheet.metaId"
              @change="onInputChange(gsheet, $event)"
              @click:prepend="redirect(gsheet)"
            >
              <v-icon slot="append" :color="gsheet.color" @click="validate(gsheet)">
                {{ gsheet.icon }}
              </v-icon>
            </v-text-field>
          </v-col>
        </v-row>
      </div>
    </v-container>
  </div>
</template>

<script>
  import { ref, toRefs } from 'vue'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import FiltersBar, { BrandFilter, WeekFilter } from '@/components/filters'
  import { usePermissions, useFilter, useContext, useEventBus, useGsheets } from '@/hooks'
  import { gsheetsApi } from '@/api/gsheets'
  import { permissionsConfig } from '@/config/permissions.config'
  import { capitalizeFirstLetter } from '@/utils'

  export default {
    name: 'AdminGSheet',
    components: {
      ViewToolbar,
      FiltersBar,
      BrandFilter,
      WeekFilter
    },
    setup() {
      const { hasPermission } = usePermissions(permissionsConfig.adminGsheet.read)
      const { refs } = useContext()
      const { current, available, actions } = useFilter({ isDashboard: true })
      const { busOn } = useEventBus()
      const gsheets = ref({})
      const {
        rules,
        statusBar,
        prepareState,
        redirect,
        validate,
        inputPrependIcon,
        preValidationCheck,
        refreshAll
      } = useGsheets({
        refs,
        gsheets,
        current
      })
      const { statusIcon, statusColor, statusText } = toRefs(statusBar)
      const isLoading = ref(false)
      const readonly = !hasPermission(permissionsConfig.adminGsheet.write)

      function getData() {
        gsheets.value = {}
        getGSheetInfo()
      }

      function setGsheets(gsheets, type) {
        return gsheets.reduce((acc, gsheet) => {
          acc.push({
            ...prepareState(gsheet),
            ...gsheet,
            type
          })
          return acc
        }, [])
      }

      async function getGSheetInfo() {
        if (current.week) {
          isLoading.value = true

          const {
            data: { general, weekly }
          } = await gsheetsApi.get({
            week: current.week,
            brand: current.brand
          })

          gsheets.value = {
            general: setGsheets(general, 'general'),
            weekly: setGsheets(weekly, 'weekly')
          }

          isLoading.value = false
        }
      }

      async function onInputChange(gsheet, gsheetUrlFromInput) {
        ;(await preValidationCheck(gsheet, gsheetUrlFromInput)) && getData()
      }

      busOn('filter-changed', getData)
      actions.changeFilter()

      return {
        hasPermission,
        current,
        available,
        actions,
        gsheets,
        isLoading,
        readonly,
        capitalizeFirstLetter,
        redirect,
        validate,
        rules,
        refreshAll,
        inputPrependIcon,
        onInputChange,
        statusIcon,
        statusColor,
        statusText
      }
    },
    computed: {
      pageTitle() {
        return (
          `GSheet ${this.current.week} ${this.current.brand}` +
          (this.readonly ? ` (view only)` : ``)
        )
      }
    }
  }
</script>

<style lang="scss">
  .btn-sync {
    margin-right: 10px;

    .v-icon {
      font-size: 18px;
    }

    span {
      text-transform: initial;
      font-size: 12px;
      line-height: 12px;
      padding-left: 3px;
      letter-spacing: 0px;
    }
  }

  .theme--dark.btn-sync.v-btn.v-btn--disabled:not(.v-btn--flat):not(.v-btn--text):not(
      .v-btn--outlined
    ) {
    background-color: #1866c0 !important;
    color: #fff !important;

    .v-icon {
      color: #fff !important;
    }
  }
</style>
