<template>
  <div>
    <div ref="content">
      <ViewToolbar :title="pageTitle" />
      <v-col class="d-flex align-center flex-grow-1">
        <v-btn color="primary" dark class="mb-2" @click="addItem">
          <v-icon left> mdi-plus </v-icon>Add new SKU
        </v-btn>
      </v-col>
      <v-dialog v-model="isDialog" max-width="700px">
        <v-form id="new-config" ref="form" v-model="isFormValid">
          <v-card>
            <v-card-title>
              <span class="headline">{{ formTitle }}</span>
            </v-card-title>
            <v-card-text>
              <v-container>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="editedItem.packagedSkuCode"
                      label="Packaged SKU Code"
                      :rules="[rules.required, rules.invalidPackagedSkuCode]"
                      :disabled="isEdit"
                      required
                    />
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="editedItem.bulkSkuCode"
                      label="Bulk SKU Code"
                      :rules="[rules.required, rules.invalidBulkSkuCode]"
                      :disabled="isEdit"
                      required
                    />
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-select
                      v-model="editedItem.brands"
                      label="Brands"
                      :items="availableBrandsList"
                      item-value="value"
                      item-text="value"
                      :rules="[rules.required]"
                      append-icon=""
                      required
                      multiple
                      @blur="validateSelectedBrands"
                    />
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model.number="editedItem.pickConversion"
                      type="number"
                      step="1"
                      label="Pick Conversions"
                      :rules="[rules.onlyNumbers]"
                      required
                    />
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-checkbox v-model="editedItem.isMasterSku" label="Bulk Master SKU" />
                  </v-col>
                </v-row>
              </v-container>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn color="grey" @click="close">Cancel</v-btn>
              <v-btn color="primary" :disabled="!isFormValid" @click="save">Save</v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </div>
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="scheme"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import { computed, ref, nextTick } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import { AgRowActions } from '@/modules/grid/components'
  import { toFormattedArray, toLocalNumber, toSwitchChip } from '@/modules/grid/cells'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import {
    useEventBus,
    useFilter,
    useContext,
    useGrid,
    useLayout,
    usePermissions,
    useNotifications
  } from '@/hooks'
  import { getDefaultContextMenuItems } from '@/modules/dashboards'
  import { adminSkusApi } from '@/api/admin-skus'
  import { permissionsConfig } from '@/config/permissions.config'
  import { debounce } from '@/utils'
  import { resizeColumnsIfNeeded, cellWidths } from '@/modules/grid/helpers'

  export default {
    components: {
      ViewToolbar,
      AgRowActions,
      AgGridVue
    },
    setup() {
      usePermissions(permissionsConfig.adminRegion.read)
      const { addNotification } = useNotifications()
      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { isFullScreen, gridHeight } = useLayout(refs)
      const { current, available, actions } = useFilter({ isDashboard: true })
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs })

      const DEFAULT_BRAND_OPTION = 'All Brands'

      const rows = ref([])
      const isLoading = ref(false)
      const defaultItem = ref({
        packagedSkuCode: '',
        bulkSkuCode: '',
        brands: [DEFAULT_BRAND_OPTION],
        isMasterSku: false,
        pickConversion: 1
      })
      const editedItem = ref({
        ...defaultItem.value
      })
      const rules = ref({
        required: (value) => !!(value && value.length) || 'Required',
        invalidPackagedSkuCode: (value) => (value && !!value.trim()) || 'Invalid Packaged Sku Code',
        invalidBulkSkuCode: (value) => (value && !!value.trim()) || 'Invalid Bulk Sku Code',
        onlyNumbers: (value) =>
          (!isNaN(parseFloat(value)) && isFinite(value) && parseInt(value, 10) == value) ||
          'Must be an integer'
      })
      const isDialog = ref(false)
      const isFormValid = ref(false)
      const editedIndex = ref(-1)
      const isEdit = computed(() => editedIndex.value !== -1)
      const isData = computed(() => rows.value.length)

      function addItem() {
        editedIndex.value = -1
        editedItem.value = Object.assign({}, defaultItem.value)
        isDialog.value = true

        nextTick(() => refs.form.resetValidation())
      }

      function editItem(index, data) {
        editedIndex.value = index
        editedItem.value = { ...data }
        isDialog.value = true

        nextTick(() => refs.form.resetValidation())
      }

      async function save() {
        if (editedIndex.value > -1) {
          await editSku()
        } else {
          await createSku()
        }

        close()
      }
      async function editSku() {
        const payload = { ...editedItem.value }

        await adminSkusApi.edit(payload)
        await getData()
        addNotification({ text: 'Item has been successfully edited' })
      }
      async function createSku() {
        const payload = { ...editedItem.value }

        await adminSkusApi.submit(payload)
        await getData()
        addNotification({ text: 'Item has been successfully created' })
      }
      async function deleteItem(_, item) {
        const { bulkSkuName, packagedSkuCode, bulkSkuCode } = item

        if (
          confirm(
            `Removing ${
              bulkSkuName ?? 'this sku'
            } will impact on inventory data calculations on different views within IMT. Are you sure you want to delete this Bulk SKU?`
          )
        ) {
          const payload = {
            packagedSkuCode,
            bulkSkuCode
          }

          await adminSkusApi.delete(payload)
          addNotification({ text: 'Item has been successfully deleted' })
          await getData()
        }
      }

      function close() {
        isDialog.value = false
      }

      function validateSelectedBrands() {
        const isAllBrandsSelected = editedItem.value.brands.includes(DEFAULT_BRAND_OPTION)

        editedItem.value.brands = isAllBrandsSelected
          ? [DEFAULT_BRAND_OPTION]
          : editedItem.value.brands
      }

      async function getData() {
        isLoading.value = true

        const { data: response } = await adminSkusApi.get()

        rows.value = response

        // wait for refs defining

        nextTick(() => (isLoading.value = false))
      }

      // subscribers
      busOn('grid-ready', () => resizeColumnsIfNeeded(grid))
      busOn(
        'grid-size-changed',
        debounce(() => resizeColumnsIfNeeded(grid))
      )
      busOn('filter-changed', getData)

      actions.changeFilter()

      return {
        isFullScreen,
        gridHeight,
        current,
        available,
        defaultGridOptions,
        defaultGridColDef,
        grid,
        rows,
        isData,
        isLoading,
        getData,
        addItem,
        deleteItem,
        editItem,
        defaultItem,
        editedItem,
        isDialog,
        isFormValid,
        isEdit,
        save,
        rules,
        close,
        validateSelectedBrands,
        DEFAULT_BRAND_OPTION
      }
    },
    computed: {
      pageTitle() {
        return 'Bulk SKUs'
      },
      formTitle() {
        const prefix = this.isEdit ? 'Edit' : 'Add new'

        return `${prefix} Packaged - Bulk SKU Mapping`
      },
      availableBrandsList() {
        return [this.DEFAULT_BRAND_OPTION].concat(this.available.allBrands)
      },
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          getContextMenuItems: (params) => getDefaultContextMenuItems(params)
        }
      },
      gridColDef() {
        return {
          ...this.defaultGridColDef
        }
      },
      scheme() {
        if (!this.isData) return []

        return [
          {
            headerName: 'Packaged SKU Code',
            field: 'packagedSkuCode',
            width: cellWidths.l
          },
          {
            headerName: 'Packaged SKU Name',
            field: 'packagedSkuName',
            width: cellWidths.xxl
          },
          {
            headerName: 'Bulk SKU Code',
            field: 'bulkSkuCode',
            width: cellWidths.l
          },
          {
            headerName: 'Bulk SKU Name',
            field: 'bulkSkuName',
            width: cellWidths.xxl
          },
          {
            headerName: 'Brands',
            field: 'brands',
            valueFormatter: toFormattedArray,
            width: cellWidths.m
          },
          {
            headerName: 'Pick Conversions',
            field: 'pickConversion',
            valueFormatter: toLocalNumber,
            width: cellWidths.s
          },
          {
            headerName: 'Bulk Master SKU',
            field: 'isMasterSku',
            cellClass: 'text-center',
            cellRenderer: toSwitchChip,
            width: cellWidths.s
          },
          {
            headerName: 'Actions',
            valueGetter: () => {
              return [
                { tooltipName: 'Edit', callback: this.editItem, icon: 'mdi-pencil' },
                { tooltipName: 'Delete', callback: this.deleteItem, icon: 'mdi-delete' }
              ]
            },
            cellClass: ({ data: { packagedSkuCode } }) => `${packagedSkuCode}-actions-bar`,
            cellRenderer: 'AgRowActions',
            pinned: 'right',
            width: cellWidths.s
          }
        ]
      }
    }
  }
</script>

<style scoped></style>
