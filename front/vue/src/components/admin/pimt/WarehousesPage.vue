<template>
  <div class="pimt-partner-list">
    <div ref="content">
      <ViewToolbar :title="pageTitle" />
      <v-col class="d-flex align-center flex-grow-1">
        <v-btn v-if="!readonly" color="primary" dark class="mb-2" @click="addConfig">
          <v-icon left> mdi-plus </v-icon>Add new config
        </v-btn>

        <SequenceControl
          :items="partnersOrder"
          :scheme="sequenceScheme"
          @on-sequence-change="submitOrder"
        >
          Partner Sequence
        </SequenceControl>
      </v-col>

      <v-dialog v-model="isDialog" max-width="700px">
        <v-form id="new-config" ref="form" v-model="isFormValid">
          <v-card>
            <v-card-title>
              <span class="headline">{{ formTitle }}</span>
            </v-card-title>
            <v-card-text>
              <v-container>
                <v-row>
                  <v-col cols="12" md="6" sd="6">
                    <v-text-field
                      v-model="editedItem.partnerCode"
                      label="GSheet Name"
                      :rules="[rules.required, rules.duplicateGsheetName]"
                      :disabled="isEdit"
                      required
                    />
                  </v-col>
                  <v-col cols="12" md="6" sd="6">
                    <v-text-field
                      v-model="editedItem.name"
                      label="Full Name"
                      :rules="[rules.required]"
                      required
                    />
                  </v-col>
                  <v-col cols="12" md="6" sd="6">
                    <v-text-field
                      v-model="editedItem.bobCode"
                      label="Bob Code"
                      :rules="[rules.duplicateBobCode]"
                    />
                  </v-col>
                  <v-col cols="12" md="6" sd="6">
                    <v-text-field v-model="editedItem.hjName" label="HJ Name" />
                  </v-col>
                  <v-col cols="12">
                    <v-combobox
                      v-model="editedItem.otDcs"
                      label="Ordering Tool Names"
                      :rules="[rules.required]"
                      append-icon=""
                      required
                      chips
                      deletable-chips
                      multiple
                    />
                  </v-col>
                  <v-col cols="12">
                    <SupplierAutocomplete v-model="editedItem.otSuppliers" />
                  </v-col>
                  <v-col cols="12" md="4" sd="4">
                    <v-select
                      v-model="editedItem.region"
                      :items="regions"
                      :rules="[rules.required]"
                      required
                      label="Region"
                    />
                  </v-col>
                  <v-col cols="12" md="4" sd="4">
                    <v-select
                      v-model="editedItem.regionalDCs"
                      :items="regionalDCs"
                      :rules="[rules.required]"
                      required
                      multiple
                      label="Regional DCs"
                    />
                  </v-col>
                  <v-col cols="12" md="4" sd="4">
                    <v-select
                      v-model="editedItem.packagingRegions"
                      :items="partnerPackagingRegions"
                      multiple
                      label="Packaging Region"
                    />
                  </v-col>
                  <v-col cols="12" md="4" sd="4">
                    <v-select
                      v-model="editedItem.receivingType"
                      :items="receivingTypes"
                      :rules="[rules.required]"
                      required
                      label="Receiving Type"
                    />
                  </v-col>
                  <v-col cols="12" md="4" sd="4">
                    <v-select
                      v-model="editedItem.inventoryType"
                      :items="inventoryTypes"
                      :rules="[rules.required]"
                      required
                      label="Inventory Type"
                    />
                  </v-col>
                  <v-col cols="12" md="4">
                    <v-checkbox v-model="editedItem.is3rdParty" label="Is 3rd Party" />
                  </v-col>
                </v-row>
              </v-container>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn color="grey" @click="close">Cancel</v-btn>
              <v-btn color="primary" :disabled="!isFormValid" @click="save">Save</v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </div>
    <v-container fluid>
      <v-progress-linear v-if="isLoading" indeterminate color="primary" />
      <template v-if="isData && !isLoading">
        <ag-grid-vue
          ref="$grid"
          :key="1"
          class="ag-theme-alpine-dark"
          :style="{ height: gridHeight }"
          :column-defs="scheme"
          :row-data="rows"
          :default-col-def="gridColDef"
          :grid-options="gridOptions"
        />
      </template>
      <v-alert v-if="!isData && !isLoading" dense color="secondary"> No data </v-alert>
    </v-container>
  </div>
</template>

<script>
  import { ref, computed, nextTick } from 'vue'
  import { AgGridVue } from 'ag-grid-vue'
  import { AgRowActions } from '@/modules/grid/components'
  import { toFormattedArray } from '@/modules/grid/cells'
  import ViewToolbar from '@/components/util/ViewToolbar'
  import SequenceControl from '@/components/admin/SequenceControl'
  import SupplierAutocomplete from '@/components/shared/SupplierAutocomplete'
  import {
    useLayout,
    useGrid,
    useEventBus,
    usePermissions,
    useContext,
    useNotifications
  } from '@/hooks'
  import { getManualFormsContextMenuItems } from '@/modules/dashboards'
  import { adminPimtApi } from '@/api/admin-pimt'
  import { permissionsConfig } from '@/config/permissions.config'
  import { debounce } from '@/utils'
  import { resizeColumnsIfNeeded } from '@/modules/grid/helpers'

  export default {
    components: {
      AgGridVue,
      AgRowActions,
      SequenceControl,
      ViewToolbar,
      SupplierAutocomplete
    },
    setup() {
      const { hasPermission } = usePermissions(permissionsConfig.pimtPartners.read)
      const { addNotification } = useNotifications()

      const gridId = 'admin-pimt-3pws'

      const { refs } = useContext()
      const { busOn } = useEventBus()
      const { gridHeight } = useLayout(refs)
      const { grid, defaultGridOptions, defaultGridColDef } = useGrid({ refs, gridId })

      const readonly = !hasPermission(permissionsConfig.pimtPartners.write)
      const rows = ref([])
      const isLoading = ref(false)
      const isData = computed(() => rows.value.length)
      const regionalDCs = ref([])
      const partnersOrder = ref([])
      const partnerPackagingRegions = ref([])
      const regions = ['Northeast', 'South', 'West', 'Southeast', 'Southwest']
      const receivingTypes = ['N/A', 'E2OPEN_GRN', 'HJ_GRN']
      const inventoryTypes = ['E2Open', 'HighJump', 'Gsheet']
      const defaultItem = ref({
        partnerCode: '',
        name: '',
        bobCode: '',
        otDcs: [],
        otSuppliers: [],
        order: '',
        region: '',
        regionalDCs: [],
        packagingRegions: [],
        receivingType: '',
        inventoryType: '',
        is3rdParty: false,
        hjName: ''
      })
      const editedItem = ref({
        ...defaultItem.value
      })
      const isDialog = ref(false)
      const isFormValid = ref(false)
      const rules = ref({
        required: (value) => !!(value && value.length) || 'Required',
        duplicateGsheetName: (value) => {
          return (
            isEdit.value ||
            !rows.value.find(({ partnerCode }) => partnerCode === value) ||
            'Gsheet Name already exists'
          )
        },
        duplicateBobCode: (value) => {
          if (!value) return true

          const validCodeMatchesOnEdit = 1
          const isFieldValid = isEdit.value
            ? rows.value.filter(({ bobCode }) => bobCode === value).length <= validCodeMatchesOnEdit
            : !rows.value.find(({ bobCode }) => bobCode === value)

          return isFieldValid || 'Bob code is not unique'
        }
      })
      const editedIndex = ref(-1)
      const isEdit = computed(() => editedIndex.value !== -1)

      async function getData() {
        isLoading.value = true

        const configData = await Promise.all([
          adminPimtApi.threePWs.get('partners'),
          adminPimtApi.threePWs.get('order')
        ])

        const [partnersData, partnersOrderData] = configData

        rows.value = partnersData.data
        partnersOrder.value = partnersOrderData.data
        isLoading.value = false
      }

      async function addConfig() {
        editedIndex.value = -1
        editedItem.value = Object.assign({}, defaultItem.value)
        isDialog.value = true

        await nextTick()
        refs.form.resetValidation()
      }

      async function edit(index) {
        editedIndex.value = index
        editedItem.value = Object.assign({}, rows.value[index])
        isDialog.value = true

        await nextTick()
        refs.form.resetValidation()
      }

      async function save() {
        if (editedIndex.value > -1) {
          await editRegion()
        } else {
          await addRegion()
        }

        close()
      }
      async function editRegion() {
        const {
          partnerCode,
          name,
          otDcs,
          otSuppliers,
          receivingType,
          inventoryType,
          is3rdParty,
          hjName,
          region,
          regionalDCs,
          packagingRegions,
          bobCode
        } = editedItem.value

        const payload = {
          name,
          otDcs,
          otSuppliers,
          receivingType,
          inventoryType,
          is3rdParty,
          hjName,
          region,
          regionalDCs,
          ...(packagingRegions ? { packagingRegions } : {}),
          bobCode
        }

        await adminPimtApi.threePWs.edit(partnerCode, payload)
        await getData()
        addNotification({ text: 'Item has been successfully edited' })
      }
      async function addRegion() {
        const payload = { ...editedItem.value }

        await adminPimtApi.threePWs.submit('partners', payload)
        await getData()
        addNotification({ text: 'Item has been successfully created' })
      }
      async function deleteRecord(_, item) {
        const { partnerCode } = item

        if (confirm(`Are you sure you want to delete ${partnerCode} configuration?`)) {
          await adminPimtApi.threePWs.delete(partnerCode)
          addNotification({ text: 'Item has been successfully deleted' })
          await getData()
        }
      }

      function close() {
        isDialog.value = false
      }

      async function submitOrder(payload) {
        await adminPimtApi.threePWs.submit('order', payload)

        getData()
      }

      async function getRegions() {
        const data = await Promise.all([
          adminPimtApi.threePWs.get('regions'),
          adminPimtApi.threePWs.get('packagingRegions')
        ])

        const [{ data: regionalDCsData }, { data: packagingRegions }] = data

        regionalDCs.value = regionalDCsData
        partnerPackagingRegions.value = packagingRegions
      }

      getData()
      getRegions()

      // subscribers
      busOn(`grid-ready:${gridId}`, () => resizeColumnsIfNeeded(grid))
      busOn(
        `grid-size-changed:${gridId}`,
        debounce(() => resizeColumnsIfNeeded(grid))
      )
      busOn('on-sequence-change', getData)

      return {
        gridHeight,
        grid,
        defaultGridOptions,
        defaultGridColDef,
        readonly,
        getData,
        rows,
        regionalDCs,
        regions,
        partnerPackagingRegions,
        receivingTypes,
        inventoryTypes,
        partnersOrder,
        isLoading,
        isData,
        isEdit,
        isDialog,
        isFormValid,
        editedItem,
        close,
        rules,
        addConfig,
        edit,
        save,
        deleteRecord,
        submitOrder
      }
    },
    computed: {
      gridOptions() {
        return {
          ...this.defaultGridOptions,
          getContextMenuItems: (params) => getManualFormsContextMenuItems(params),
          tooltipShowDelay: 0,
          tooltipMouseTrack: true
        }
      },
      gridColDef() {
        return {
          editable: false,
          sortable: false,
          resizable: false,
          filter: false,
          menuTabs: []
        }
      },
      scheme() {
        return [
          {
            headerName: 'GSheet Name',
            field: 'partnerCode'
          },
          {
            headerName: 'Full Site Name',
            field: 'name'
          },
          {
            headerName: 'Bob Code',
            field: 'bobCode'
          },
          {
            headerName: 'Ordering Tool Names',
            field: 'otDcs',
            tooltipField: 'otDcs',
            valueFormatter: toFormattedArray
          },
          {
            headerName: 'Supplier Names',
            field: 'otSuppliers',
            tooltipValueGetter: toFormattedArray,
            valueFormatter: toFormattedArray
          },
          {
            headerName: 'Region',
            field: 'region'
          },
          {
            headerName: 'Regional DCs',
            field: 'regionalDCs',
            valueFormatter: toFormattedArray
          },
          {
            headerName: 'Packaging Region',
            field: 'packagingRegions',
            valueFormatter: toFormattedArray
          },
          {
            headerName: 'Receiving Type',
            field: 'receivingType'
          },
          {
            headerName: 'Inventory Type',
            field: 'inventoryType'
          },
          {
            headerName: 'Actions',
            cellClass: ({ data: { partnerCode } }) => `${partnerCode}-edit-button`,
            valueGetter: () => {
              return [
                { tooltipName: 'Edit', callback: this.edit, icon: 'mdi-pencil' },
                { tooltipName: 'Delete', callback: this.deleteRecord, icon: 'mdi-delete' }
              ]
            },
            cellRenderer: 'AgRowActions',
            pinned: 'right',
            width: 100,
            hide: this.readonly
          }
        ]
      },
      sequenceScheme() {
        return [
          {
            field: 'sequenceNumber',
            hide: true
          },
          {
            headerName: 'Partner',
            field: 'partnerCode',
            rowDrag: true
          }
        ]
      },
      pageTitle() {
        return 'Warehouses' + (this.readonly ? ' (view only)' : '')
      },
      formTitle() {
        const prefix = this.isEdit ? 'Edit' : 'New'

        return `${prefix} Warehouse Config`
      }
    }
  }
</script>

<style lang="scss">
  .pimt-partner-list {
    .v-data-table-header {
      background-color: #7e57c2;

      th {
        color: #fff !important;
        font-weight: normal;
      }
    }
  }

  .disabled-item {
    color: grey !important;
  }
</style>
