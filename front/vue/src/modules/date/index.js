import moment from 'moment'

export const tokens = {
  timestamp: {
    default: 'MM/DD/YYYY h:mm A',
    secondary: 'MM/DD/YYYY HH:mm'
  },
  date: {
    default: 'MM/DD/YYYY',
    short: 'MM/DD',
    iso: 'YYYY-MM-DD',
    year: 'YYYY',
    withDay: 'dddd - MM/DD/YYYY',
    isoWithoutSeparators: 'YYYYMMDD',
    monthYear: 'MMMM-YYYY'
  },
  time: {
    default: 'h:mm A',
    secondary: 'H:mm'
  }
}

export const validators = {
  date: /^((0?[1-9]|1[012])[- /.](0?[1-9]|[12][0-9]|3[01])[- /.](19|20)?[0-9]{4})*$/,
  isoDate: /\d{4}-\d{2}-\d{2}/,
  amPm: /^([01]?\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/
}

export const daysWithinImtWeek = [
  'Monday',
  'Tuesday',
  'Wednesday 1',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday 2',
  'No Impact'
]

export const formatDate = (value, token = tokens.timestamp.default) =>
  value && moment(value).format(token)

export const createDate = (token = tokens.timestamp.default) => {
  const dateNow = moment()

  return formatDate(dateNow, token)
}

export const getCurrentTimestamp = () => ({
  date: createDate(tokens.date.iso),
  time: createDate(tokens.time.secondary)
})

export const toAmPmTime = (time24) => {
  return time24 && moment(time24, tokens.time.secondary).format(tokens.time.default)
}

export const to24Time = (time12) => {
  return time12 && moment(time12, tokens.time.default).format(tokens.time.secondary)
}

export const formatDateWithoutSpaces = () => {
  return createDate(tokens.date.isoWithoutSeparators)
}

export const getDayDifference = (dateFrom, dateTo) => {
  if (!(dateFrom && dateTo)) return null

  return Math.abs(moment(dateFrom).diff(moment(dateTo), 'days', false))
}

export const compareDates = (date1, date2) => {
  const momentDate1 = moment(formatDate(date1, tokens.date.default))
  const momentDate2 = moment(formatDate(date2, tokens.date.default))

  return moment(momentDate1).diff(momentDate2)
}

export const dateFilterComparator = (date1, date2) => compareDates(date2, date1)

export const isDateAfter = (date1, date2) => moment(date1).isAfter(date2)

export const getWeekNumber = (scmWeek) => (scmWeek ? Number(scmWeek.split('W')[1]) : '')

export const revertWeek = (scmWeek) => {
  const [year, week] = scmWeek.split('-')

  return `${year}-${week}`
}

export const getYesterdayDate = () => {
  return formatDate(moment().subtract(1, 'day'), tokens.date.default)
}
