import store from '@/store'
import router from '@/router'
import { columnsWithComments } from '@/modules/dashboards'
import { validators } from '@/modules/date'
import { cellWidths, defaultNumberFilterOptions } from '@/modules/grid/constants'
import { defaultBrand } from '@/config/app.config'
import { getNestedValue } from '@/utils'

export { cellWidths, defaultNumberFilterOptions }

export const transposeMatrix = (m) => m[0].map((x, i) => m.map((x) => x[i]))

export const sortAlphaNumeric = (a, b) => {
  if (!a) return b ? 1 : 0

  return a.localeCompare(b, undefined, {
    numeric: true,
    sensitivity: 'base'
  })
}

export const isColumnsFitViewport = (grid) => {
  if (!grid) return true

  const {
    columnApi: {
      api: {
        columnModel: { bodyWidth: allColumnsWidth, scrollWidth: gridWidth }
      }
    }
  } = grid

  return gridWidth ? gridWidth <= allColumnsWidth : true
}

export const resizeColumnsIfNeeded = (grid) => {
  if (!grid.value) return
  if (!isColumnsFitViewport(grid.value)) {
    grid.value.api.sizeColumnsToFit()
  }
}

export const getValueFromParams = (params) => {
  const {
    data,
    colDef: { field, valueGetter }
  } = params

  if (valueGetter) {
    return valueGetter(params)
  }

  return data ? getNestedValue(data, field) : null
}

export const createColumn = (props) => ({
  width: cellWidths.m, // default value for width
  cellClass: 'text-right', // most common cell class
  colId: props.field || props.colId,
  ...props
})

export const setBlankCellFor1Level = (id, width) => ({
  colId: `blank_${id}`,
  headerClass: 'cell-blank',
  cellClass: 'cell-blank',
  width: width || cellWidths.blank,
  minWidth: width || cellWidths.blank,
  headerName: '',
  suppressMovable: true,
  suppressNavigable: true,
  editable: false
})

export const setBlankCell = (id, width, groupId = '', initialHide = false) => ({
  width: width || cellWidths.blank,
  minWidth: width || cellWidths.blank,
  headerName: '',
  headerClass: 'cell-blank',
  groupId,
  children: [
    {
      colId: `blank_${id}`,
      headerClass: 'cell-blank',
      cellClass: 'cell-blank',
      width: width || cellWidths.blank,
      minWidth: width || cellWidths.blank,
      headerName: '',
      suppressMovable: true,
      suppressNavigable: true,
      editable: false,
      initialHide
    }
  ]
})

export const setBlankCellFor3Level = (id, width) => ({
  width: width || cellWidths.blank,
  minWidth: width || cellWidths.blank,
  headerName: '',
  headerClass: 'cell-blank',
  marryChildren: true,
  children: [
    {
      headerClass: 'cell-blank',
      cellClass: 'cell-blank',
      width: width || cellWidths.blank,
      minWidth: width || cellWidths.blank,
      headerName: '',
      suppressMovable: true,
      suppressNavigable: true,
      editable: false,
      children: [
        {
          colId: `blank_${id}`,
          headerClass: 'cell-blank',
          cellClass: 'cell-blank',
          width: width || cellWidths.blank,
          minWidth: width || cellWidths.blank,
          headerName: '',
          suppressMovable: true,
          suppressNavigable: true,
          editable: false
        }
      ]
    }
  ]
})

export const formatMoneyAndLocalNumber = (value) =>
  value || value === 0
    ? '$ ' +
      Number(parseFloat(value).toFixed(2)).toLocaleString('en', {
        minimumFractionDigits: 2
      })
    : ''

export const clearRangeCells = (params) => {
  if (params.api.isDestroyed()) return

  const cellRanges = params.api.getCellRanges()
  const clearCells = (start, end, columns) => {
    for (let i = start; i <= end; i++) {
      const rowNode = params.api.getRowNode(i)
      columns.forEach((column) => rowNode.setDataValue(column, ''))
    }
  }

  cellRanges.forEach((cells) => {
    const colIds = cells.columns.map((col) => col.colId)
    const startRowIndex = Math.min(cells.startRow.rowIndex, cells.endRow.rowIndex)
    const endRowIndex = Math.max(cells.startRow.rowIndex, cells.endRow.rowIndex)

    clearCells(startRowIndex, endRowIndex, colIds)
  })
}

export const setOnlyNumbers = (params, config = {}) => {
  const defaultOptions = {
    couldBeEmpty: true,
    onlyPositive: true,
    zeroOnEmpty: false
  }
  const options = { ...defaultOptions, ...config }
  const {
    colDef: { field },
    data,
    newValue,
    oldValue
  } = params

  const isEmpty = newValue === null || (typeof newValue === 'string' && newValue.trim() === '')

  if (isEmpty || isNaN(newValue)) {
    if (options.couldBeEmpty) {
      data[field] = options.zeroOnEmpty ? 0 : null
    } else {
      data[field] = oldValue
    }
    return
  }

  data[field] = options.onlyPositive ? Math.abs(newValue) : +newValue
}

export const setValidTime = (params, couldBeEmpty = true) => {
  const {
    colDef: { field },
    data,
    newValue,
    oldValue
  } = params

  if (!newValue) {
    data[field] = couldBeEmpty ? '' : oldValue
    return
  }

  const isValid = newValue.match(validators.amPm)

  if (isValid) {
    data[field] = newValue
    return
  }

  // set "null" in case value is present and not valid
  data[field] = null
}

export const setValidDate = (params, couldBeEmpty = true) => {
  const {
    colDef: { field },
    data,
    newValue,
    oldValue
  } = params

  if (!newValue) {
    data[field] = couldBeEmpty ? '' : oldValue
    return
  }

  const isValidStored = newValue.match(validators.isoDate)

  if (isValidStored) {
    data[field] = newValue
    return
  }

  const isValidDisplayed = newValue.match(validators.date)

  if (isValidDisplayed) {
    const [month, day, year] = newValue.split('/')
    data[field] = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    return
  }

  // set "null" in case value is present and not valid
  data[field] = null
}

export const completeShipMethod = (params, poNumbers) => {
  const { data, api, node } = params
  const { poNumber } = data
  const key = 'shipMethod'

  if (poNumber) {
    const poNumberContext = poNumbers.value.find(({ label }) => label === poNumber)
    data[key] = poNumberContext ? poNumberContext[key] : null
  } else {
    data[key] = null
  }

  api.refreshCells({
    columns: [key],
    rowNodes: [node],
    force: true
  })
}

export const isColumn = (params, columnName) => {
  return keyToCompare(params) === columnName
}

export const getCommentColumnByParams = (params) => {
  return columnsWithComments.find(({ field }) => isColumn(params, field))
}

export const isSingleComment = (params) => {
  const data = params.node ? params.node.data : params.data
  const { isComment } = data

  return isComment
}

export const isCommentWithinField = (params, columnWithComment = '') => {
  const data = params.node ? params.node.data : params.data

  if (!data) return false

  const { comments } = data

  return comments && comments.includes(columnWithComment || keyToCompare(params))
}

export const isCommentWithinChildRows = (params) => {
  const {
    node: { allChildrenCount, expanded, childrenAfterFilter },
    column: { colId: columnName }
  } = params

  return (
    allChildrenCount &&
    !expanded &&
    childrenAfterFilter.some((child) => isCommentWithinField(child, columnName))
  )
}

export const isAddedByCoverageForm = (params) => {
  const data = params.node ? params.node.data : params.data

  if (!data) return false

  const { isCoverageItem } = data
  const { cellWithCoverageData } = params

  return isCoverageItem && cellWithCoverageData
}

export const commentContextOption = (context, params, dialog) => {
  const { domain, brand, site, week, resourceField, resourceType, field, isSingleNode } = context
  const {
    node: { data }
  } = params
  const isComment = isCommentWithinField(params)
  const resourceId = data[resourceField]
  const isMultiBrand = brand.includes(',')

  return {
    name: isComment ? 'Edit Comment' : 'Add Comment',
    action: async () => {
      const requestParams = {
        domain,
        site,
        brand: brand || defaultBrand,
        week: data.week || week,
        resourceType,
        resourceId
      }

      if (!dialog) return
      if (isMultiBrand) {
        return dialog.addNotification({
          text: 'Comments can not be added to the aggregated row. Please expand on the Brand field and add the comment in the brand specific row.',
          isError: true
        })
      }

      const status = await dialog.open(requestParams, isComment)

      if (!status) return

      const updateNode = (node) => {
        if (status === 'edited') {
          node.data.comments = [field, ...node.data.comments]
        }
        if (status === 'deleted') {
          node.data.comments = node.data.comments.filter((key) => key !== field)
        }
      }

      const rowNodes = []

      if (isSingleNode) {
        updateNode(params.node)
        rowNodes.push(params.node)
      } else {
        params.api.forEachLeafNode((rowNode) => {
          const isSameResourceFieldValue =
            params.node.data[resourceField] === rowNode.data[resourceField]

          if (isSameResourceFieldValue) {
            updateNode(rowNode)
            rowNodes.push(rowNode)
          }
        })
      }

      params.api.redrawRows({ rowNodes })
    }
  }
}

export const jumpToDashboardOption = (params, currentWeek, path) => {
  return {
    name: 'Jump to Dashboard',
    icon: '<img style="margin-left:5px" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAHCAYAAADEUlfTAAAAJUlEQVR42mNgAILy8vL/DLgASBKnApgkVgXIkhgKiNKJ005s4gDLbCZBiSxfygAAAABJRU5ErkJggg==" />',
    action: async () => {
      const {
        node: { data }
      } = params
      const { brand, site, week, sku, skuCode, poNumber } = data
      let url = `${path}?sku=${sku || skuCode}`

      if (poNumber) {
        url = `${url}&poNumber=${poNumber}`
      }

      store.dispatch('Filters/changeBrand', brand)
      store.dispatch('Filters/changeSite', site)
      store.dispatch('Filters/changeWeek', week || currentWeek)

      router.push(url)
    }
  }
}

export const isGridAvailable = (params) => {
  return params && !params.api.isDestroyed()
}

const keyToCompare = (params) => {
  if (!params.column) return ''

  const {
    column: {
      colId,
      colDef: { field }
    }
  } = params

  return colId === 'ag-Grid-AutoColumn' ? field : colId
}

export const isCaseSizeMismatch = ({ data: { caseSize, caseSizeReceived, poStatus } = {} }) => {
  return caseSize !== caseSizeReceived && poStatus.includes('Received')
}

export const isNullOrUndefined = (value) => value === null || value === undefined
