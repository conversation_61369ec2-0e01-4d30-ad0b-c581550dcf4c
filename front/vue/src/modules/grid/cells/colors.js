import { chipColors, poStatusChipColors } from '@/modules/grid/constants'
import stringToCamelCase from '@/utils/string-to-camelcase'

export const getPoStatusChipColor = (params) => {
  const { value } = params
  const key = stringToCamelCase(value)

  if (key.includes('inProgress')) return chipColors.inProgressHj

  return poStatusChipColors[key] || chipColors.default
}

export const getPoStatusColor = (params) => {
  switch (params.value?.toLowerCase()) {
    case 'supplier accepted with changes':
      return chipColors.invalidText
    case 'supplier rejected':
      return chipColors.defaultText
    case 'invalid':
      return chipColors.invalidText
    default:
      return chipColors.defaultText
  }
}

export const getPoTypeChipColor = (params) => {
  const { value } = params
  const poTypeColors = {
    inbound: chipColors.accurate,
    outbound: chipColors.outbound
  }

  return (value && poTypeColors[value.toLowerCase()]) || chipColors.default
}

export const getChipColorForJobStatus = (params) => {
  const status = (params && params.value) || params || null

  if (!status) return chipColors.default

  switch (true) {
    case status.startsWith('success'):
      return chipColors.accurate
    case status.startsWith('failed'):
      return chipColors.highAttention
    case status.startsWith('Attention Needed'):
      return chipColors.attention
    case status.startsWith('in progress'):
      return chipColors.jobInProgress
    default:
      return chipColors.default
  }
}

export const getClosingStockStatusColor = (params) => {
  switch (true) {
    case params.value >= 0:
      return chipColors.accurate
    case params.value < 0:
      return chipColors.highAttention
    default:
      return chipColors.default
  }
}

export const getCoverageStatusColor = (params) => {
  switch (true) {
    case params.value.startsWith('In Progress'):
      return chipColors.attention
    case params.value.startsWith('Completed'):
      return chipColors.accurate
    case params.value.startsWith('Delayed'):
      return chipColors.highAttention
    case params.value.startsWith('No Response'):
      return chipColors.highAttention
    case params.value.startsWith('Other'):
      return chipColors.na
    default:
      return chipColors.default
  }
}

export const getInventoryStatusColor = (params) => {
  const { value = '' } = params
  const text = value.toUpperCase()

  switch (true) {
    case text.startsWith('ACTIVE'):
      return chipColors.accurate
    case text.startsWith('LOST'):
      return chipColors.highAttention
    case text.startsWith('ON HOLD'):
      return chipColors.attention
    case text.startsWith('AVAILABLE'):
      return chipColors.green
    case text.startsWith('BOOKED FOR OUTBOUND'):
      return chipColors.inProgressHj
    case text.startsWith('TOTAL'):
      return chipColors.trackArrived
    case text.startsWith('UNSPECIFIED'):
      return chipColors.invalidText
    case text.startsWith('UNAVAILABLE'):
      return chipColors.invalidText
    default:
      return chipColors.default
  }
}

export const getChipColorForIcsTicketId = (params) => {
  if (!params) return ''

  const {
    data: { status }
  } = params

  if (status.startsWith('Open') || status.startsWith('In Progress')) {
    return chipColors.attention
  }

  return chipColors.default
}

export const getRowBackgroundStyleForLiner = (name = '', isParent = false) => {
  const colors = {
    WIN: '87, 178, 209',
    SPR: '110, 198, 78',
    SUP: '236, 98, 70',
    SUM: '248, 145, 27'
  }

  const match = Object.entries(colors).find(([key]) => name.includes(key))

  return {
    background: match ? `rgba(${match[1]}, ${isParent ? '0.6' : '0.5'})` : 'transparent'
  }
}
