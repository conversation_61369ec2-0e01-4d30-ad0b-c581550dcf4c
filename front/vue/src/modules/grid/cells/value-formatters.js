import moment from 'moment'
import { tokens, toAmPmTime } from '@/modules/date'
import { multiplyDecimals } from '@/utils'

const currencyFormatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD'
})

export function toFormattedMoney({ value }) {
  if (!value && value !== 0) {
    return ''
  }

  return currencyFormatter.format(value)
}

export const toMoneyRange = ({ value }) => {
  if (!value) return ''

  const { min, max } = value

  if (!min && min !== 0) {
    return ''
  }

  const minFormatted = toFormattedMoney({ value: min })

  if (min === max) {
    return minFormatted
  }

  const maxFormatted = toFormattedMoney({ value: max })

  return `${minFormatted} - ${maxFormatted}`
}

export const toFormattedArray = ({ value }) => (Array.isArray(value) ? value.join(', ') : value)

export const toFormattedDate =
  (token = tokens.timestamp.default) =>
  ({ value }) =>
    value && moment(value).format(token)

export const toLocalNumber = (params) => {
  return params.value && Number.isInteger(params.value)
    ? params.value.toLocaleString()
    : params.value?.toString()
}

export const toLocalNumberForDailyNeeds = (params) => {
  return params.value === null ? 'N/A' : toLocalNumber(params)
}

export const toTime = (params) => {
  if (params.value === null) return 'Invalid Time'
  if (params.value === '') return ''

  return toAmPmTime(params.value)
}

export const toPercent = ({ value = null }) =>
  value !== null ? multiplyDecimals(value, 100) + '%' : ''
