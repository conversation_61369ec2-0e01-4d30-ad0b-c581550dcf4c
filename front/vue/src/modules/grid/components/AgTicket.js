import Vue from 'vue'
import { isNullOrUndefined } from '../helpers'
import AgChevron from '@/modules/grid/components/AgChevron'

export default Vue.extend({
  template: `
    <span class="ag-cell-with-ticket">
      <AgChevron v-if="isExpandable" :component-params="cellParams"/>
      <div v-else>{{formattedValue}}</div>
      <v-btn
        v-if="isVisible"
        class="ticket-icon"
        v-ripple="false"
        icon
        small
        plain
        @click="clickHandler">
        <v-icon small class="rotate-90">
          mdi-ticket-confirmation-outline
        </v-icon>
      </v-btn>
    </span>
  `,
  props: ['componentParams'],
  components: {
    AgChevron
  },
  computed: {
    cellParams() {
      return this.componentParams || this.params
    },
    isVisible() {
      if (isNullOrUndefined(this.cellParams.value)) return false

      return this.cellParams.data.icsTickets
    },
    formattedValue() {
      return this.cellParams.valueFormatter
        ? this.cellParams.valueFormatter(this.cellParams)
        : this.cellParams.value
    },
    isExpandable() {
      return this.cellParams.isExpandable
    }
  },
  methods: {
    clickHandler() {
      return this.cellParams.getTicketsData(this.cellParams)
    }
  }
})
