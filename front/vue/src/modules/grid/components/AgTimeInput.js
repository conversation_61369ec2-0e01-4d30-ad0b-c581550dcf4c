import Vue, { nextTick } from 'vue'
import { to24Time, toAmPmTime } from '@/modules/date'

export default Vue.extend({
  data() {
    return {
      value: '',
      timeFormatted: '',
      menu: false
    }
  },
  watch: {
    value(val) {
      this.timeFormatted = this.formatTime(val)
    }
  },
  created() {
    this.value = this.params.value
  },
  mounted() {
    nextTick(() => {
      this.menu = true
      if (this.$refs.input) {
        this.$refs.input.focus()
        this.$refs.input.select()
      }
    })
  },
  methods: {
    getValue() {
      return this.value
    },
    formatTime(time) {
      return time ? toAmPmTime(time) : ''
    },
    parseTime() {
      if (!this.timeFormatted) {
        this.value = ''
        return
      }

      const isValid = this.timeFormatted.match(/^(0?[1-9]|1[012])(:[0-5]\d) [APap][mM]$/)

      if (!isValid) return

      this.value = to24Time(this.timeFormatted)
    },
    close() {
      this.menu = false
      this.params.api.stopEditing()
    }
  },
  template: `<v-menu
    ref="menu"
    v-model="menu"
    nudge-top="-2"
    :close-on-content-click="false"
    :close-on-click="false"
    transition="scale-transition"
    offset-y
    max-width="290px"
    min-width="auto"
  >
    <template v-slot:activator="{ on, attrs }">
      <input
        v-model="timeFormatted"
        type="text"
        ref="input"
        maxlength="8"
        placeholder="HH:MM AM/PM"
        @input="parseTime()"
        class="form-input"
      />
    </template>
    <v-time-picker
      v-if="menu"
      v-model="value"
      ampm-in-title
      flat
      color="primary"
    >
    <div class="grid-input-close">
      <v-btn
        class="mx-2"
        fab
        dark
        x-small
        color="white"
        @click="close"
      >
        <v-icon dark color="primary">
          mdi-close
        </v-icon>
      </v-btn>
    </div>
    </v-time-picker>
  </v-menu>`
})
