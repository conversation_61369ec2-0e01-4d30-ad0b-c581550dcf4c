export function getCookie(name) {
  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'))

  return match && match[2]
}

export function deleteCookie(name) {
  document.cookie = `${name}= ; expires = Thu, 01 Jan 1970 00:00:00 GMT`
}

export function setCookie(name, value, expirationInHours) {
  const now = new Date()
  now.setTime(now.getTime() + expirationInHours * 3600 * 1000)
  document.cookie = `${name}=${value}; expires=${now.toUTCString()}; path=/`
}
