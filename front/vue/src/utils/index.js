import forceFileDownload from './force-file-download'

export { forceFileDownload }

export const isProd = process.env.NODE_ENV === 'production'

export const isObj = (value) => typeof value === 'object' && value !== null
export const isEmptyObj = (obj) => Object.keys(obj).length === 0 && obj.constructor === Object
export const filterOutEmptyValuesFromObj = (obj) =>
  Object.entries(obj).reduce((acc, [key, value]) => (value ? { ...acc, [key]: value } : acc), {})

export function multiplyDecimals(a, b, precision = 2) {
  const multiplier = 1000000

  return +((a * multiplier * (b * multiplier)) / (multiplier * multiplier)).toFixed(precision)
}

export const debounce = (fn, delay = 500) => {
  let timeout

  return (...args) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

export function throttle(func, delay = 500) {
  let lastCall = 0
  let timeoutId

  return function (...args) {
    const now = new Date().getTime()

    clearTimeout(timeoutId)

    if (now - lastCall < delay) {
      timeoutId = setTimeout(() => {
        lastCall = now
        func.apply(this, args)
      }, delay - (now - lastCall))
    } else {
      lastCall = now
      func.apply(this, args)
    }
  }
}

export const getQueryStringParams = () => {
  const query = window.location.href.split('?')[1]

  return query
    ? (/^[?#]/.test(query) ? query.slice(1) : query).split('&').reduce((params, param) => {
        let [key, value] = param.split('=')
        params[key] = value ? decodeURIComponent(value.replace(/\+/g, ' ')) : ''
        return params
      }, {})
    : {}
}

export const cleanUpQueryParams = () => {
  const [path] = window.location.hash.split('?')

  history.pushState({}, null, `/${path}`)
}

export const capitalizeFirstLetter = (string) => {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

export const getUserName = (email) => {
  return email.split('@')[0]
}

export const areTwoArraysWithSameValues = (firstArray = [], secondArray = []) => {
  if (firstArray.length !== secondArray.length) return false

  return firstArray.every((item) => secondArray.includes(item))
}

export const getDefaultApiGetResponse = () => ({
  data: [],
  warnings: [],
  headers: {},
  pageKeys: []
})

export const ensureSelectedItemVisible = () => {
  // wait for content to render

  setTimeout(() => {
    const $el = document.querySelector('.menuable__content__active')
    const activeItem = $el.querySelector('.v-list-item--active')

    $el.style.display = 'block'

    // scroll only after element is present and visible

    activeItem &&
      requestAnimationFrame(() => {
        activeItem.scrollIntoView({ block: 'center', scrollBehavior: 'smooth' })
      })
  }, 0)
}

export const scrollToSelectedOrOngoingItem = () => {
  setTimeout(() => {
    const $el = document.querySelector('.week-content')
    const $selected = $el.querySelector('.the-week--selected')
    const $ongoing = $el.querySelector('.the-week--ongoing')
    const activeItem = $selected || $ongoing

    if (activeItem) {
      requestAnimationFrame(() => {
        activeItem.scrollIntoView({ block: 'center', scrollBehavior: 'smooth' })
      })
    }
  }, 0)
}

export const filterEmptyProperties = (obj) => {
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v != null))
}

/**
 * Retrieves a nested value from an object given a dot-separated path string.
 *
 * @param {Object} obj - The object from which to retrieve the value.
 * @param {string} path - A dot-separated string representing the path to the desired value (e.g., 'weekly.param').
 * @returns {*} The value found at the specified path, or `null` if the path does not exist.
 *
 * @example
 * const data = { weekly: { param: 42 } };
 * const value = getNestedValue(data, 'weekly.param'); // returns 42
 *
 * @example
 * const data = { weekly: { param: 42 } };
 * const value = getNestedValue(data, 'monthly.param'); // returns null
 */
export const getNestedValue = (obj, path) =>
  path.split('.').reduce((acc, key) => (acc ? acc[key] : null), obj)
