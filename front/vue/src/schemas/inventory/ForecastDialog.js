import { cellWidths, createColumn } from '@/modules/grid/helpers'
import { toLocalNumber } from '@/modules/grid/cells'

export default {
  main({ isData }) {
    if (!isData.value) return []

    return [
      createColumn({
        headerName: 'DC',
        field: 'site',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'SKU',
        field: 'skuCode',
        cellClass: 'text-left',
        width: cellWidths.l
      }),
      createColumn({
        headerName: 'SCM Week',
        field: 'week',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Forecast',
        field: 'forecast',
        filter: 'agNumberColumnFilter',
        cellClass: 'text-left',
        width: cellWidths.m,
        valueFormatter: toLocalNumber
      })
    ]
  }
}
