import { getYesterdayDate, createDate, tokens, getWeekNumber } from '@/modules/date'
import { toLocalNumber, toSupplementNeed } from '@/modules/grid/cells'
import { cellWidths, setBlankCell, createColumn } from '@/modules/grid/helpers'
import { getUnitsInHouseAdjustmentColor } from '@/modules/dashboards'
import poStatus from '../poStatus/poContextual'

export default {
  poStatus,
  main({
    isData,
    rows,
    current,
    consolidatedFilter,
    onNoteChanged,
    onAdjustmentChanged,
    getInventoryData
  }) {
    if (!isData.value) return []

    const combinedGroup = {
      headerName: '',
      headerClass: 'cell-no-separator',
      children: [
        createColumn({
          headerName: 'DC',
          field: 'dc',
          pinned: 'right',
          width: cellWidths.xs,
          cellClass: 'text-left',
          hide: !consolidatedFilter.isConsolidatedView,
          lockVisible: true
        }),
        createColumn({
          headerName: 'Brand',
          field: 'brand',
          pinned: 'right',
          width: cellWidths.xs,
          cellClass: 'text-left',
          hide: !consolidatedFilter.isConsolidatedView,
          lockVisible: true
        })
      ]
    }

    const materialSummaryGroup = {
      headerName: 'Material Summary',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'SKU',
          field: 'sku',
          pinned: 'left',
          width: cellWidths.l,
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'SKU Name',
          field: 'skuName',
          pinned: 'left',
          width: cellWidths.xxxl,
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Category',
          field: 'category',
          columnGroupShow: 'open',
          pinned: 'left',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Storage Location',
          field: 'storageLocation',
          columnGroupShow: 'open',
          width: cellWidths.xl,
          pinned: 'left',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Buyer',
          field: 'buyer',
          columnGroupShow: 'open',
          width: cellWidths.l,
          pinned: 'left',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Commodity Group',
          field: 'commodityGroup',
          columnGroupShow: 'open',
          width: cellWidths.xl,
          pinned: 'left',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Replenishment SKU',
          field: 'isReplenishmentSku',
          valueGetter: ({ data: { isReplenishmentSku } }) => (isReplenishmentSku ? 'Y' : null),
          columnGroupShow: 'open',
          width: cellWidths.xl,
          pinned: 'left',
          cellClass: 'text-left'
        })
      ]
    }

    const availableInventorySummary = {
      headerName: 'Available Inventory Summary',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Weeks on Hand',
          field: 'weeksOnHand',
          cellClass: 'text-center'
        }),
        createColumn({
          headerName: 'Pallets On Hand',
          field: 'palletsOnHand',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Units in House',
          field: 'unitsInHouseHj',
          filter: 'agNumberColumnFilter',
          width: cellWidths.m,
          cellRenderer: 'AgChevron',
          cellClass: 'text-left',
          cellRendererParams: {
            isChevronVisible: () => !!getInventoryData,
            getData: getInventoryData,
            valueFormatter: toLocalNumber
          }
        }),
        createColumn({
          headerName: 'Supplement Need',
          field: 'supplementNeed',
          width: cellWidths.xm,
          cellRenderer: toSupplementNeed,
          cellClass: 'text-center'
        }),
        createColumn({
          headerName: 'Supplement Need Quantity',
          field: 'supplementNeedQuantity',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'DC Expiring Inventory (1-90 Days)',
          field: 'dcExpiringInventory',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: '10 Days from Expiration',
          field: 'tenDaysFromExpiration',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        })
      ]
    }

    const partnerInventoryGroup = {
      headerName: '3PW Inventory',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Units in House (Regional)',
          field: 'unitsInHouse',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Units in House (Network)',
          field: 'unitsInHouseNetwork',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: '3PW Outbound',
          field: 'threePWOutbound',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: '3PW Expiring Inventory (1-90 Days)',
          field: 'threePWExpiringInventory',
          filter: 'agNumberColumnFilter',
          width: cellWidths.xm,
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Weeks On Hand',
          field: 'threePWWeeksOnHand',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: '10 Days from Expiration',
          field: 'threePWTenDaysFromExpiration',
          cellClass: 'text-center',
          valueFormatter: toLocalNumber
        })
      ]
    }

    const weeklyGroup = Object.keys(rows.value[0].weekly)
      .map((week, index) => {
        return week === current.week
          ? {
              headerName: `${week} (Current Week)`,
              headerClass: 'cell-no-separator',
              marryChildren: true,
              children: [
                createColumn({
                  headerName: `High Jump Snapshot - ${getYesterdayDate()}`,
                  colId: 'unitsInHouseHJYesterday',
                  valueGetter: ({ data }) => data && data.weekly[week]['unitsInHouseHJYesterday'],
                  filter: 'agNumberColumnFilter',
                  width: cellWidths.xm,
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Start of Day Units in House Snapshot (HJ)',
                  colId: 'startOfDayUnitsInHouseHJ',
                  valueGetter: ({ data }) => data && data.weekly[week]['startOfDayUnitsInHouseHJ'],
                  filter: 'agNumberColumnFilter',
                  width: cellWidths.xm,
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Inbound - Awaiting Delivery',
                  colId: `inbound_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['inbound'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Undelivered - Past Due POs',
                  colId: `undeliveredPastDuePos_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['undeliveredPastDuePos'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: `Received - ${createDate(tokens.date.default)}`,
                  colId: 'received',
                  valueGetter: ({ data }) => data && data.weekly[week]['received'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Units Expiring',
                  colId: `unitsExpiring_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['unitsExpiring'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: `ROW Needs ${current.prevWeek}`,
                  colId: `rowNeedsPreviousWeek_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['rowNeedsPreviousWeek'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Production Needs - Rest of Week for Current Week',
                  colId: 'productionNeedsRestOfWeekForCurrentWeek',
                  valueGetter: ({ data }) =>
                    data && data.weekly[week]['productionNeedsRestOfWeekForCurrentWeek'],
                  width: cellWidths.xl,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Remaining Inventory',
                  colId: `remainingInventory_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['remainingInventory'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Units in House (Adjustment)',
                  colId: `unitsInHouseAdjustment`,
                  valueGetter: ({ data }) => data && data.weekly[week]['unitsInHouseAdjustment'],
                  valueSetter: (params) => {
                    params.data.weekly[current.week].unitsInHouseAdjustment = params.newValue
                    return true
                  },
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  cellStyle: (params) => getUnitsInHouseAdjustmentColor(params, current.week),
                  valueFormatter: toLocalNumber,
                  editable: true,
                  onCellValueChanged: onAdjustmentChanged
                })
              ]
            }
          : {
              headerName: `${week}`,
              headerClass: 'cell-no-separator',
              marryChildren: true,
              children: [
                createColumn({
                  headerName: 'Start of Week Inventory',
                  colId: `startOfWeekInventory_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['startOfWeekInventory'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Inbound - Awaiting Delivery',
                  colId: `inbound_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['inbound'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Units Expiring',
                  colId: `unitsExpiring_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['unitsExpiring'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: `W${getWeekNumber(week)} Forecast`,
                  colId: `productionNeedsFullWeek_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['productionNeedsFullWeek'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                }),
                createColumn({
                  headerName: 'Remaining Inventory',
                  colId: `remainingInventory_${index}`,
                  valueGetter: ({ data }) => data && data.weekly[week]['remainingInventory'],
                  width: cellWidths.xm,
                  filter: 'agNumberColumnFilter',
                  valueFormatter: toLocalNumber
                })
              ]
            }
      })
      .reduce(
        (prev, current, index, arr) =>
          index === arr.length - 1
            ? [...prev, current]
            : [...prev, current, setBlankCell('weekly_' + index)],
        []
      )
    const notesGroup = {
      headerName: '',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Notes',
          field: 'notes',
          width: cellWidths.xxxl,
          editable: true,
          cellClass: 'ag-cell-editable',
          onCellValueChanged: onNoteChanged
        }),
        createColumn({
          headerName: 'Last Edited By',
          field: 'lastEditedBy',
          width: cellWidths.xxl,
          cellClass: 'text-left'
        })
      ]
    }

    return [
      combinedGroup,
      materialSummaryGroup,
      setBlankCell(1),
      availableInventorySummary,
      setBlankCell(2),
      partnerInventoryGroup,
      setBlankCell(3),
      ...weeklyGroup,
      setBlankCell(4),
      notesGroup
    ]
  }
}
