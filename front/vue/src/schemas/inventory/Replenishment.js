import { toLocal<PERSON>umber, toColor<PERSON>hip, toFormattedMoney } from '@/modules/grid/cells'
import { cellWidths, setBlankCell, createColumn } from '@/modules/grid/helpers'
import { chipColors } from '@/modules/grid/constants'
import poStatus from '../poStatus/poContextual'
import envConfig from '@/config/env.config'

const getChipColorForOverride = (params) => {
  const { data } = params
  const isOverride = data.totalOnHandOverride !== null

  if (isOverride) {
    return chipColors.override
  }

  return 'transparent'
}

const overrideValueSetter = (params) => {
  const { data, newValue, oldValue } = params

  if (Number.isNaN(+newValue)) {
    return false
  }

  if (newValue === '') {
    data.totalOnHandOverride = null
    return true
  }

  if (oldValue === +newValue) {
    return false
  }

  data.totalOnHandOverride = +newValue

  return true
}

const maxMoqIoqValueGetter = ({ data }) => {
  if (!data) return null

  const { maxMoq, maxIoq } = data
  const formatValue = (value) => (value ? value.toLocaleString() : '-')

  return `${formatValue(maxMoq)} / ${formatValue(maxIoq)}`
}

const maxMoqIoqFilterValueGetter = (params) => {
  if (!params.data) return null

  const { maxMoq, maxIoq } = params.data

  return (maxMoq || 0) + (maxIoq || 0)
}

export default {
  poStatus,
  main({
    isData,
    sortedAvailableWeeks: availableWeeks,
    getInventoryData,
    getUndeliveredPoData,
    getForecastData,
    current,
    topoRecommendationEnabled
  }) {
    if (!isData.value) return []

    const getEowNetInventoryClass = (params) => {
      if (params.value <= 0) return 'ag-high-attention'

      const {
        colDef: { headerName }
      } = params

      return params.data.endOfWeekBufferFlags?.[headerName] ? 'ag-moderate-attention' : ''
    }
    const getWeeklyForecastClass = (params) => {
      const { data, colDef } = params
      const { highForecastWeeks } = data
      const { headerName } = colDef
      const [week] = headerName.split(' ')

      return highForecastWeeks[week] ? 'ag-attention' : ''
    }
    const getChipColorForInbounds = (params) => {
      if (!params) return

      const {
        data: { deliveryWeek },
        colDef: { headerName, field }
      } = params
      const isDeliveryWeekMatch = deliveryWeek?.includes(headerName)
      const isTotalInbounds = deliveryWeek && field === 'totalInbounds'

      if (topoRecommendationEnabled.value && (isDeliveryWeekMatch || isTotalInbounds))
        return chipColors.eodAttentionNeeded

      return 'transparent'
    }
    const isChevronVisible = (params) => params.value > 0
    const getChevronCellClass = (params) =>
      params.value > 0 ? 'd-flex justify-space-between align-center' : ''
    const availableWeeksCount = availableWeeks.value.length

    const overviewGroup = {
      headerName: 'Overview',
      headerClass: 'cell-no-separator ag-header-pink',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'SKU',
          field: 'sku',
          pinned: 'left',
          width: cellWidths.l,
          cellClass: 'text-left',
          cellRenderer: 'agGroupCellRenderer'
        }),
        createColumn({
          headerName: 'SKU Name',
          field: 'skuName',
          pinned: 'left',
          width: cellWidths.xxxl,
          cellClass: 'text-left',
          cellRenderer: 'AgCellWithFlags',
          cellRendererParams: {
            type: 'replenishment',
            current,
            isExpandable: true,
            getData: getInventoryData
          },
          lockVisible: true
        }),
        createColumn({
          headerName: '3PW Replenishment Type',
          headerClass: 'ag-header-pink',
          field: 'replenishmentType',
          width: cellWidths.xl,
          cellClass: 'text-left',
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Buyer',
          headerClass: 'ag-header-pink',
          field: 'buyer',
          columnGroupShow: 'open',
          width: cellWidths.xl,
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Strategy Manager',
          headerClass: 'ag-header-pink',
          field: 'strategyManager',
          columnGroupShow: 'open',
          width: cellWidths.xxl,
          cellClass: 'text-left',
          tooltipValueGetter(params) {
            const value = params.data['strategyManager'] || ''
            const list = value.split(',')

            return list.length > 2 ? value : null
          },
          valueGetter(params) {
            return params.data['strategyManager']?.split(',')
          }
        }),
        createColumn({
          headerName: 'Replenishment Buyer',
          headerClass: 'ag-header-pink',
          field: 'replenishmentBuyer',
          columnGroupShow: 'open',
          width: cellWidths.xl,
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Category',
          headerClass: 'ag-header-pink',
          field: 'category',
          cellClass: 'text-left',
          columnGroupShow: 'open',
          width: cellWidths.xm
        }),
        createColumn({
          headerName: 'Sub-Category',
          headerClass: 'ag-header-pink',
          field: 'subCategory',
          cellClass: 'text-left',
          columnGroupShow: 'open',
          width: cellWidths.xm
        }),
        createColumn({
          headerName: 'Brand',
          headerClass: 'ag-header-pink',
          field: 'brand',
          cellClass: 'text-left',
          columnGroupShow: 'open',
          width: cellWidths.xm
        })
      ]
    }

    const topoGroup = {
      headerName: 'TOPO',
      headerClass: 'cell-no-separator ag-header-magenta',
      children: [
        createColumn({
          headerName: 'Purchase Qty Recommendation',
          headerClass: 'ag-header-magenta',
          field: 'purchaseQtyRecommendation',
          width: cellWidths.sl,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Ordering Week',
          headerClass: 'ag-header-magenta',
          field: 'orderingWeek',
          width: cellWidths.l,
          cellClass: 'text-left',
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Delivery Week',
          headerClass: 'ag-header-magenta',
          field: 'deliveryWeek',
          tooltipValueGetter(params) {
            const value = params.data['deliveryWeek'] || []
            const stringValue = value.join(', ')

            return value.length ? stringValue : null
          },
          width: cellWidths.l,
          cellClass: 'text-left',
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Projected Shortage Week',
          headerClass: 'ag-header-magenta',
          field: 'projectedShortageWeek',
          width: cellWidths.sl,
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Projected Shortage Qty',
          headerClass: 'ag-header-magenta',
          field: 'projectedShortageQty',
          width: cellWidths.sl,
          filter: 'agNumberColumnFilter',
          columnGroupShow: 'open'
        })
      ]
    }

    const commonInventoryTargetsColumns = [
      createColumn({
        headerName: 'Shelf Life (days)',
        field: 'shelfLife',
        filter: 'agNumberColumnFilter',
        width: cellWidths.m,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Max PO Lead Time (Weeks)',
        field: 'leadTime',
        width: cellWidths.l,
        filter: 'agNumberColumnFilter'
      })
    ]

    const inventoryTargetsGroup = {
      headerName: 'Inventory Targets',
      headerClass: 'cell-no-separator',
      children: envConfig.TOPO_SECTION_ENABLED
        ? [
            ...commonInventoryTargetsColumns,
            createColumn({
              headerName: 'Max MOQ/IOQ',
              valueGetter: maxMoqIoqValueGetter,
              width: cellWidths.xm,
              filter: 'agNumberColumnFilter',
              filterValueGetter: maxMoqIoqFilterValueGetter
            })
          ]
        : commonInventoryTargetsColumns
    }

    const forecastGroup = {
      headerName: 'Forecasts',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: `${current.week} ROWN`,
          field: `inWeekRowNeed`,
          columnGroupShow: 'open',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber,
          cellClass: getWeeklyForecastClass
        }),
        ...availableWeeks.value.map((week, index) =>
          createColumn({
            headerName: week,
            colId: `forecast_${index}`,
            valueGetter: ({ data }) => data && data.forecasts[week],
            columnGroupShow: 'open',
            width: cellWidths.m,
            filter: 'agNumberColumnFilter',
            valueFormatter: toLocalNumber,
            cellClass: getWeeklyForecastClass
          })
        ),
        createColumn({
          headerName: `${availableWeeksCount} Wk Forecast`,
          field: 'totalForecast',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          cellRenderer: 'AgChevron',
          cellRendererParams: {
            isChevronVisible: isChevronVisible,
            getWrapperClass: getChevronCellClass,
            valueFormatter: toLocalNumber,
            getData: getForecastData
          }
        }),
        createColumn({
          headerName: `Avg Weekly Demand`,
          field: 'averageWeeklyDemand',
          columnGroupShow: 'open',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        })
      ]
    }

    const onHandGroup = {
      headerName: 'On Hand',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Total On Hand',
          field: 'totalOnHand',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Total DC',
          field: 'totalInventoryCore',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber,
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Total 3PL',
          field: 'totalInventoryTpl',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber,
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Total 3PW',
          field: 'totalInventoryWarehouse',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber,
          columnGroupShow: 'open'
        }),
        createColumn({
          headerName: 'Vendor Managed Inventory',
          field: 'vendorManagedInventory',
          width: cellWidths.l,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Inv WOS',
          field: 'totalInventoryWos',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Expired Inventory',
          field: 'expiredInventory',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Soon to Expire Inventory (<60 Days)',
          field: 'expiringSoonInventory',
          width: cellWidths.xl,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        })
      ]
    }

    const inventoryCostingGroup = {
      headerName: 'Inventory Costing',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Total Cost',
          field: 'totalCost',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-left',
          width: cellWidths.l,
          valueFormatter: toFormattedMoney
        }),
        createColumn({
          headerName: 'DC Cost',
          field: 'dcCost',
          filter: 'agNumberColumnFilter',
          columnGroupShow: 'open',
          cellClass: 'text-left',
          width: cellWidths.m,
          valueFormatter: toFormattedMoney
        }),
        createColumn({
          headerName: '3PL Cost',
          field: 'tplCost',
          filter: 'agNumberColumnFilter',
          columnGroupShow: 'open',
          cellClass: 'text-left',
          width: cellWidths.m,
          valueFormatter: toFormattedMoney
        }),
        createColumn({
          headerName: '3PW Cost',
          field: 'tpwCost',
          filter: 'agNumberColumnFilter',
          columnGroupShow: 'open',
          cellClass: 'text-left',
          width: cellWidths.m,
          valueFormatter: toFormattedMoney
        })
      ]
    }

    const onOrderGroup = {
      headerName: 'On Order',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        ...availableWeeks.value.map((week, index) =>
          createColumn({
            headerName: week,
            colId: `inbound_${index}`,
            valueGetter: ({ data }) => data && data.inbounds[week],
            cellRenderer: toColorChip,
            cellRendererParams: {
              getChipColor: getChipColorForInbounds,
              formatChipValue: toLocalNumber
            },
            columnGroupShow: 'open',
            cellClass: 'text-center',
            width: cellWidths.m,
            filter: 'agNumberColumnFilter',
            valueFormatter: toLocalNumber
          })
        ),
        createColumn({
          headerName: 'Total On Order',
          field: 'totalInbounds',
          cellRenderer: toColorChip,
          cellRendererParams: {
            getChipColor: getChipColorForInbounds,
            formatChipValue: toLocalNumber
          },
          cellClass: 'text-center',
          width: cellWidths.m,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber
        }),
        createColumn({
          headerName: 'Total Undelivered',
          field: 'totalUndelivered',
          columnGroupShow: 'open',
          width: cellWidths.xm,
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          cellRenderer: 'AgChevron',
          cellRendererParams: {
            isChevronVisible,
            getWrapperClass: getChevronCellClass,
            valueFormatter: toLocalNumber,
            getData: getUndeliveredPoData
          }
        })
      ]
    }

    const overrideGroup = {
      headerName: 'Override',
      headerClass: 'cell-no-separator override-header',
      marryChildren: true,
      children: [
        createColumn({
          headerName: 'Total on Hand',
          headerClass: 'override-header',
          field: 'totalOnHandOverride',
          width: cellWidths.xm,
          filter: 'agNumberColumnFilter',
          cellRenderer: toColorChip,
          cellRendererParams: {
            getChipColor: getChipColorForOverride,
            formatChipValue: toLocalNumber
          },
          cellClass: 'text-center',
          valueFormatter: toLocalNumber,
          valueSetter: overrideValueSetter,
          editable: true
        })
      ]
    }

    const eowInventoryGroup = {
      headerName: 'EOW Net Inventory',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        ...availableWeeks.value.map((week, index) =>
          createColumn({
            headerName: week,
            colId: `eow_inventory_${index}`,
            cellClass: getEowNetInventoryClass,
            valueGetter: ({ data }) => data && data.endOfWeek[week],
            width: cellWidths.m,
            filter: 'agNumberColumnFilter',
            valueFormatter: toLocalNumber
          })
        )
      ]
    }

    const eowWosGroup = {
      headerName: 'EOW WOS',
      headerClass: 'cell-no-separator',
      marryChildren: true,
      children: [
        ...availableWeeks.value.map((week, index) =>
          createColumn({
            headerName: week,
            colId: `eow_wos_${index}`,
            valueGetter: ({ data }) => data && data.endOfWeekWos[week],
            columnGroupShow: week === current.week ? null : 'open',
            width: cellWidths.m,
            filter: 'agNumberColumnFilter',
            valueFormatter: toLocalNumber
          })
        )
      ]
    }

    return [
      overviewGroup,
      setBlankCell(1),
      ...(envConfig.TOPO_SECTION_ENABLED ? [topoGroup, setBlankCell(9)] : []),
      inventoryTargetsGroup,
      setBlankCell(2),
      forecastGroup,
      setBlankCell(3),
      onHandGroup,
      setBlankCell(4),
      inventoryCostingGroup,
      setBlankCell(5),
      onOrderGroup,
      setBlankCell(6),
      overrideGroup,
      setBlankCell(7),
      eowInventoryGroup,
      setBlankCell(8),
      eowWosGroup
    ]
  }
}
