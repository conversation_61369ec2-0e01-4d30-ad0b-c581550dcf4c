import { getChipColorForWoh } from '@/modules/dashboards'
import { toColorChip, toLocalNumber } from '@/modules/grid/cells'
import { chipColors } from '@/modules/grid/constants'
import { cellWidths, setBlankCell, createColumn, isNullOrUndefined } from '@/modules/grid/helpers'

export default {
  main({ isData, consolidatedFilter, rows, getPoStatusData, current }) {
    if (!isData.value) return []

    const isPoModalAvailable = (params) => {
      const {
        node: { level }
      } = params

      return level === 0
    }

    const skuCodeTooltipValueGetter = (params) => {
      return params.value && params.value.length > 1 ? params.value : null
    }

    const getChipColorForBoh = (
      { data: { weekly }, colDef: { colId } } = { data: { weekly: {} }, colDef: { colId: '' } }
    ) => {
      const transparentColor = 'transparent'
      const weeklyValues = Object.values(weekly)
      const [field, weeklyIndexStr] = colId.split('_')
      const previousWeekIndex = Number(weeklyIndexStr) - 1

      if (previousWeekIndex < 0 || previousWeekIndex >= weeklyValues.length) {
        return transparentColor
      }

      const { hfOwnedPlannedDepletion = null, vendorManagedPlannedDepletion = null } =
        weeklyValues[previousWeekIndex] || {}
      const depletionValue = field.includes('hf')
        ? hfOwnedPlannedDepletion
        : vendorManagedPlannedDepletion

      if (depletionValue && depletionValue > 0) {
        return chipColors.attention
      }

      return transparentColor
    }

    const combinedGroup = {
      headerName: '',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'PIMT Region',
          colId: 'pimtRegion',
          field: 'pimtRegion',
          pinned: 'right',
          width: cellWidths.m,
          hide: !consolidatedFilter.isConsolidatedView,
          lockVisible: true
        }
      ]
    }

    const overviewGroup = {
      headerName: 'Packaging Summary',
      headerClass: 'cell-no-separator',
      children: [
        createColumn({
          headerName: 'Region',
          field: 'packagingRegion',
          pinned: 'left',
          width: cellWidths.s,
          hide: true,
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Ownership',
          field: 'ownership',
          pinned: 'left',
          width: cellWidths.xl,
          cellClass: 'text-left',
          tooltipField: 'ownership'
        }),
        createColumn({
          headerName: 'Demand Pipeline / SKU Name',
          field: 'itemName',
          pinned: 'left',
          width: cellWidths.xxl,
          cellClass: 'text-left',
          cellRenderer: 'AgChevron',
          cellRendererParams: {
            isChevronVisible: isPoModalAvailable,
            getData: getPoStatusData
          }
        }),
        createColumn({
          headerName: 'SKU',
          field: 'skuCode',
          pinned: 'left',
          width: cellWidths.xl,
          cellClass: 'text-left',
          columnGroupShow: 'open',
          tooltipValueGetter: skuCodeTooltipValueGetter,
          valueGetter(params) {
            return params.data['skuCode'].replace(/\s+/g, '').split(',')
          }
        }),
        createColumn({
          headerName: 'Category',
          field: 'category',
          pinned: 'left',
          width: cellWidths.m,
          columnGroupShow: 'open',
          cellClass: 'text-left'
        }),
        createColumn({
          headerName: 'Units/TL',
          field: 'unitsPerTruckLoad',
          pinned: 'left',
          width: cellWidths.m,
          valueFormatter: toLocalNumber,
          cellClass: 'text-left',
          columnGroupShow: 'open'
        })
      ]
    }

    const hfOwnedPlannedDepletionField = {
      hfOwnedPlannedDepletion: createColumn({
        headerName: 'HF Owned Planned Depletion',
        width: cellWidths.sl,
        cellClass: 'text-right',
        valueFormatter: toLocalNumber
      })
    }

    const weeklyFields = (week) => {
      return {
        hfOwnedBoh: createColumn({
          headerName: 'HF Owned BOH',
          width: cellWidths.xm,
          cellClass: 'text-center',
          cellRenderer: toColorChip,
          cellRendererParams: {
            getChipColor: getChipColorForBoh,
            formatChipValue: toLocalNumber
          }
        }),
        vendorOwnedBoh: createColumn({
          headerName: 'Vendor Managed BOH',
          width: cellWidths.xm,
          cellClass: 'text-center',
          cellRenderer: toColorChip,
          cellRendererParams: {
            getChipColor: getChipColorForBoh,
            formatChipValue: toLocalNumber
          }
        }),
        ...(current.week === week ? {} : hfOwnedPlannedDepletionField),
        vendorManagedPlannedDepletion: createColumn({
          headerName: 'Vendor Managed Planned Depletion',
          width: cellWidths.sl,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        liveDemand: createColumn({
          headerName: 'Live Demand',
          width: cellWidths.xm,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        incomingPos: createColumn({
          headerName: `Incoming PO's to SS`,
          width: cellWidths.xm,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        outboundPos: createColumn({
          headerName: `Outbound PO's from SS to DCs`,
          width: cellWidths.xm,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        vendorPosToDc: createColumn({
          headerName: 'Vendor POs to DC',
          width: cellWidths.xm,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        truckLoads: createColumn({
          headerName: `TL's`,
          width: cellWidths.xm,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        }),
        weeksOnHand: createColumn({
          headerName: 'WOH',
          width: cellWidths.xm,
          cellClass: 'text-center',
          cellRenderer: toColorChip,
          cellRendererParams: {
            getChipColor: getChipColorForWoh,
            formatChipValue: toLocalNumber
          }
        })
      }
    }

    const weekItems = rows.value[0].weekly
    const weeklyColumns = Object.keys(weekItems).map((week, index) => {
      const children = Object.keys(weeklyFields(week)).map((field) => {
        const colId = field + '_' + index

        return {
          ...weeklyFields(week)[field],
          colId,
          filter: 'agNumberColumnFilter',
          valueFormatter: toLocalNumber,
          valueGetter: (row) => row.node.data.weekly[week][field]
        }
      })

      return {
        headerName: week,
        children,
        groupId: week
      }
    })

    const weeklyGroup = weeklyColumns.reduce(
      (prev, current, index) => [
        ...prev,
        setBlankCell('daily_' + index, cellWidths.blank, `weekly_group_${index}`),
        current
      ],
      []
    )

    return [combinedGroup, overviewGroup, ...weeklyGroup]
  }
}
