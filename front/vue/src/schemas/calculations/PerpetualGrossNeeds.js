import { cellWidths, setBlankCell } from '@/modules/grid/helpers'
import { toLocalNumber } from '@/modules/grid/cells'
import { formatDate, tokens } from '@/modules/date'

export default {
  main({ isData, availableDays, getColorForDayCell }) {
    if (!isData.value) return []

    const dailyColumns = availableDays.value.map((day, index) => {
      return {
        headerName: '',
        headerClass: 'cell-no-separator',
        children: [
          {
            headerName: formatDate(day, tokens.date.default),
            colId: `daily_${index}`,
            valueGetter: ({ data }) => data && data.days[day],
            cellClass: ({ data }) => ['text-right', !data && 'cell-bold'],
            cellStyle: (params) => getColorForDayCell(params),
            width: 100,
            aggFunc: ({ values }) => values.reduce((acc, cur) => acc + (cur ?? 0), 0),
            valueFormatter: toLocalNumber
          }
        ]
      }
    })

    const summaryGroup = {
      headerName: 'Summary',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'SKU',
          rowGroup: true,
          field: 'sku',
          headerClass: 'cell-center',
          cellClass: ['cell-blank', 'cell-covered'],
          pinned: 'left',
          width: cellWidths.l,
          hide: true
        },
        {
          headerName: 'SKU Name',
          field: 'skuName',
          cellClass: ({ data }) => data && data.week && ['cell-blank', 'cell-covered'],
          aggFunc: ({ values }) => values.length && values[0],
          pinned: 'left',
          width: cellWidths.xxxl
        },
        {
          headerName: 'SCM Week',
          field: 'week',
          pinned: 'left',
          aggFunc: () => 'All Current SCM Weeks',
          cellClass: ({ data }) => !data && 'cell-bold',
          cellStyle: (params) => getColorForDayCell(params),
          width: 155
        },
        {
          headerName: 'Sum of All Gross Needs',
          field: 'sum',
          filter: 'agNumberColumnFilter',
          pinned: 'left',
          aggFunc: 'sum',
          width: cellWidths.m,
          cellClass: ({ data }) => ['text-right', !data && 'cell-bold'],
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Category',
          field: 'category',
          columnGroupShow: 'open',
          cellClass: ({ data }) => data && data.week && ['cell-blank', 'cell-covered'],
          aggFunc: ({ values }) => values.length && values[0],
          width: cellWidths.s,
          pinned: 'left'
        },
        {
          headerName: 'Commodity Group',
          field: 'commodityGroup',
          columnGroupShow: 'open',
          cellClass: ({ data }) => data && data.week && ['cell-blank', 'cell-covered'],
          aggFunc: ({ values }) => values.length && values[0],
          width: cellWidths.l,
          pinned: 'left'
        },
        {
          headerName: 'Buyer',
          field: 'buyer',
          columnGroupShow: 'open',
          cellClass: ({ data }) => data && data.week && ['cell-blank', 'cell-covered'],
          aggFunc: ({ values }) => values.length && values[0],
          width: cellWidths.l,
          pinned: 'left'
        }
      ]
    }

    const dailyGroup = dailyColumns.reduce((prev, current) => [...prev, current], [])

    return [summaryGroup, setBlankCell(1), ...dailyGroup]
  }
}
