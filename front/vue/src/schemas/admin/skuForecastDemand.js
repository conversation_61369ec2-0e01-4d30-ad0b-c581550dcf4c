import { cellWidths, createColumn } from '@/modules/grid/helpers'
import { toLocalNumber } from '@/modules/grid/cells'

export default {
  main({ isData }) {
    if (!isData.value) return []

    return [
      createColumn({
        headerName: 'SKU',
        field: 'skuCode',
        cellClass: 'text-left',
        width: cellWidths.l
      }),
      createColumn({
        headerName: 'Week',
        field: 'week',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Day',
        field: 'day',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Site',
        field: 'site',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Brand',
        field: 'brand',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Quantity',
        field: 'quantity',
        filter: 'agNumberColumnFilter',
        width: cellWidths.m,
        valueFormatter: toLocalNumber
      })
    ]
  }
}
