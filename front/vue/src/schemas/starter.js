import { cellWidths, setBlankCell } from '@/modules/grid/helpers'

export default {
  main({ isData }) {
    if (!isData.value) return []

    // todo: configure your own group and column defenitions
    const summaryGroup = {
      headerName: 'Pinned group ',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'SKU',
          colId: 'sku',
          field: 'sku',
          pinned: 'left',
          width: cellWidths.l
        },
        {
          headerName: 'SKU Name',
          colId: 'skuName',
          field: 'skuName',
          pinned: 'left',
          width: cellWidths.xxxl
        }
      ]
    }

    const anotherGroup = {
      headerName: 'Another group ',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'Col 1',
          colId: 'col1',
          field: 'col1',
          width: cellWidths.m
        },
        {
          headerName: 'Col 2',
          colId: 'col2',
          field: 'col2',
          width: cellWidths.m
        },
        {
          headerName: 'Col 3',
          colId: 'col3',
          field: 'col3',
          width: cellWidths.m
        },
        {
          headerName: 'Col 4',
          colId: 'col4',
          field: 'col4',
          width: cellWidths.m
        },
        {
          headerName: 'Col 5',
          colId: 'col5',
          field: 'col5',
          width: cellWidths.m
        }
      ]
    }

    return [summaryGroup, setBlankCell(1), anotherGroup]
  }
}
