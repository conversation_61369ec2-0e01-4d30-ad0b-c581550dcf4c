import { daysWithinImtWeek, getWeekNumber } from '@/modules/date'
import { toD<PERSON>ta, toSupplementNeed, toLocalNumber } from '@/modules/grid/cells'
import { cellWidths, setBlankCell, sortAlphaNumeric } from '@/modules/grid/helpers'
import { AgRecipesTooltip } from '@/modules/grid/components'

export default {
  main({ current }) {
    const nextWeekNumber = getWeekNumber(current.nextWeek)
    const combinedGroup = {
      headerName: '',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'Site',
          field: 'site',
          pinned: 'right',
          width: cellWidths.xs
        },
        {
          headerName: 'Brand',
          field: 'brand',
          pinned: 'right',
          width: cellWidths.xs
        }
      ]
    }
    const summaryGroup = {
      headerName: 'Ingredient Summary',
      children: [
        {
          headerName: 'SKU',
          field: 'sku',
          pinned: 'left',
          width: cellWidths.l
        },
        {
          headerName: 'SKU Name',
          field: 'skuName',
          pinned: 'left',
          width: cellWidths.xxxl,
          tooltipField: 'skuName'
        },
        {
          headerName: 'Category',
          field: 'category',
          columnGroupShow: 'open',
          width: cellWidths.s,
          pinned: 'left'
        },
        {
          headerName: 'Commodity Group',
          field: 'commodityGroup',
          columnGroupShow: 'open',
          width: cellWidths.s,
          pinned: 'left'
        },
        {
          headerName: 'Impacted Recipes',
          field: 'impactedRecipes',
          columnGroupShow: 'open',
          pinned: 'left',
          width: cellWidths.s,
          tooltipField: 'impactedRecipes',
          tooltipComponent: AgRecipesTooltip,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            debounceMs: 500,
            comparator: sortAlphaNumeric
          },
          valueGetter(params) {
            return params.data['impactedRecipes'].split('-')
          }
        }
      ]
    }
    const unitsGroup = {
      headerName: 'Units Needed',
      children: [
        {
          headerName: 'Plan',
          field: 'plan',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Forecast - OSCAR',
          field: 'forecastOscar',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Delta',
          field: 'delta',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          cellRenderer: toDelta,
          width: cellWidths.m
        }
      ]
    }
    const statusGroup = {
      headerName: 'Status',
      children: [
        {
          headerName: 'Supplement Need',
          field: 'sn',
          width: cellWidths.xl,
          cellClass: 'text-center',
          cellRenderer: toSupplementNeed,
          comparator(a, b) {
            return daysWithinImtWeek.indexOf(a) - daysWithinImtWeek.indexOf(b)
          }
        }
      ]
    }
    const overviewGroup = {
      headerName: 'Weekly Overview',
      children: [
        {
          headerName: 'Units Needed',
          field: 'weekly.unitsNeeded',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units Ordered',
          field: 'weekly.unitsOrdered',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units Received',
          field: 'weekly.unitsReceived',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units in House (HJ)',
          field: 'weekly.unitsInHouseHj',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'ROW Need',
          field: 'weekly.rowNeed',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units in House - ROW Need',
          field: 'weekly.unitsInHouseMinusRowNeed',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: `Week ${nextWeekNumber} Forecast`,
          field: 'weekly.nextWeekForecast',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        }
      ]
    }

    return [
      combinedGroup,
      summaryGroup,
      setBlankCell(1),
      unitsGroup,
      setBlankCell(2),
      statusGroup,
      setBlankCell(3),
      overviewGroup
    ]
  }
}
