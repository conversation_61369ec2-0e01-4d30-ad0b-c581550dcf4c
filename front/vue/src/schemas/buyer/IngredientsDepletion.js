import { daysWithinImtWeek, getWeekNumber } from '@/modules/date'
import {
  toD<PERSON><PERSON>,
  toSupplementNeed,
  toLocalNumber,
  toFormattedMoney,
  toPercent,
  getPoStatusChipColor,
  toColorChip,
  toFormattedDate
} from '@/modules/grid/cells'
import { cellWidths, setBlankCell, sortAlphaNumeric } from '@/modules/grid/helpers'
import { getChipColorForCaseSizeReceived, getChipColorForCasePrice } from '@/modules/dashboards'
import { AgRecipesTooltip } from '@/modules/grid/components'
import { tokens, dateFilterComparator } from '@/modules/date'
import envConfig from '@/config/env.config'

export default {
  main({ current, week }) {
    const nextWeekNumber = getWeekNumber(current.nextWeek)
    const combinedGroup = {
      headerName: '',
      headerClass: 'cell-no-separator',
      children: [
        {
          headerName: 'Site',
          field: 'site',
          pinned: 'right',
          width: cellWidths.xs
        },
        {
          headerName: 'Brand',
          field: 'brand',
          pinned: 'right',
          width: cellWidths.xs
        }
      ]
    }
    const summaryGroup = {
      headerName: 'Ingredient Summary',
      children: [
        {
          headerName: 'SKU',
          field: 'sku',
          pinned: 'left',
          width: cellWidths.l,
          cellRenderer: 'agGroupCellRenderer'
        },
        {
          headerName: 'SKU Name',
          field: 'skuName',
          pinned: 'left',
          width: cellWidths.xxxl,
          tooltipField: 'skuName',
          cellRenderer: 'AgCellWithFlags',
          cellRendererParams: { current, week }
        },
        {
          headerName: 'Category',
          field: 'category',
          columnGroupShow: 'open',
          width: cellWidths.s,
          pinned: 'left'
        },
        {
          headerName: 'Commodity Group',
          field: 'commodityGroup',
          columnGroupShow: 'open',
          width: cellWidths.s,
          pinned: 'left'
        },
        {
          headerName: 'Impacted Recipes',
          field: 'impactedRecipes',
          columnGroupShow: 'open',
          pinned: 'left',
          width: cellWidths.s,
          tooltipField: 'impactedRecipes',
          tooltipComponent: AgRecipesTooltip,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            debounceMs: 500,
            comparator: sortAlphaNumeric
          },
          valueGetter(params) {
            return params.data['impactedRecipes'].split('-')
          }
        }
      ]
    }
    const statusGroup = {
      headerName: 'Status',
      children: [
        {
          headerName: 'Supplement Need',
          field: 'sn',
          width: cellWidths.xl,
          cellClass: 'text-center',
          cellRenderer: toSupplementNeed,
          comparator(a, b) {
            return daysWithinImtWeek.indexOf(a) - daysWithinImtWeek.indexOf(b)
          }
        }
      ]
    }
    const unitsGroup = {
      headerName: 'Units Needed',
      children: [
        {
          headerName: 'Plan',
          field: 'plan',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Forecast - OSCAR',
          field: 'forecastOscar',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Delta',
          field: 'delta',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-center',
          cellRenderer: toDelta,
          width: cellWidths.m
        }
      ]
    }
    const overviewGroup = {
      headerName: 'Weekly Overview',
      children: [
        {
          headerName: 'Units Needed',
          field: 'weekly.unitsNeeded',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units Ordered',
          field: 'weekly.unitsOrdered',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units Received',
          field: 'weekly.unitsReceived',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units in House (HJ)',
          field: 'weekly.unitsInHouseHj',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'ROW Need',
          field: 'weekly.rowNeed',
          filter: 'agNumberColumnFilter',
          width: cellWidths.s,
          cellClass: 'text-right',
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units in House - ROW Need',
          field: 'weekly.unitsInHouseMinusRowNeed',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: `Week ${nextWeekNumber} Forecast`,
          field: 'weekly.nextWeekForecast',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: `Units in House - ROW Need - Week ${nextWeekNumber} Forecast`,
          field: 'weekly.unitsInHouseMinRowNeedMinForecast',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.xl,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Units Scheduled to be Produced by Auto-Bagger',
          field: 'weekly.unitsToProduceByAutobagger',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.xl,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Inventory',
          field: 'weekly.inventory',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Discards',
          field: 'weekly.discards',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.s,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Pulls',
          field: 'weekly.pulls',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.s,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Total On Hand',
          field: 'weekly.totalOnHand',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.s,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Total On Hand - Production Need',
          field: 'weekly.onHandMinProductionNeeds',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.xl,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'In-Progress (HJ)',
          field: 'weekly.inProgressHj',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Awaiting Delivery',
          field: 'weekly.awaitingDelivery',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.s,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Not Delivered',
          field: 'weekly.notDelivered',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.m,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Buffer Quantity (if all is delivered)',
          field: 'weekly.bufferQuantity',
          filter: 'agNumberColumnFilter',
          cellClass: 'text-right',
          width: cellWidths.xl,
          valueFormatter: toLocalNumber
        },
        {
          headerName: 'Buffer % (if all is delivered)',
          field: 'weekly.bufferPercent',
          filter: 'agNumberColumnFilter',
          width: cellWidths.xl,
          cellClass: 'text-right',
          valueFormatter: toPercent
        }
      ]
    }

    return [
      combinedGroup,
      summaryGroup,
      setBlankCell(1),
      unitsGroup,
      setBlankCell(2),
      statusGroup,
      setBlankCell(3),
      overviewGroup
    ]
  },
  poStatus({ current }) {
    return [
      {
        headerName: 'Supplier',
        field: 'supplier'
      },
      {
        headerName: 'PO #',
        field: 'poNumber',
        cellRenderer: 'AgCellWithFlags',
        cellRendererParams: { current }
      },
      {
        headerName: 'SKU',
        field: 'sku',
        width: cellWidths.l
      },
      {
        headerName: 'SKU Name',
        field: 'skuName',
        width: cellWidths.xxxl
      },
      {
        headerName: 'Scheduled Delivery Date',
        field: 'scheduledDeliveryDate',
        filter: 'agMultiColumnFilter',
        valueFormatter: toFormattedDate(tokens.date.default),
        filterParams: {
          filters: [
            {
              filter: 'agDateColumnFilter',
              filterParams: {
                comparator: dateFilterComparator,
                inRangeInclusive: true
              }
            },
            {
              filter: 'agSetColumnFilter',
              filterParams: {
                valueFormatter: toFormattedDate(tokens.date.default)
              }
            }
          ]
        },
        sort: 'asc',
        width: cellWidths.xm
      },
      {
        headerName: 'PO Status',
        field: 'poStatus',
        width: cellWidths.xxlm,
        cellClass: 'text-center',
        cellRenderer: toColorChip,
        cellRendererParams: {
          getChipColor: getPoStatusChipColor
        }
      },
      {
        headerName: 'Receiving Variance (units)',
        field: 'receiveVariance',
        filter: 'agNumberColumnFilter',
        width: cellWidths.m,
        cellClass: 'text-right',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Order Size',
        field: 'orderSize',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Case Price',
        field: 'casePrice',
        filter: 'agNumberColumnFilter',
        valueFormatter: toFormattedMoney,
        cellRenderer: toColorChip,
        cellRendererParams: {
          getChipColor: getChipColorForCasePrice
        }
      },
      {
        headerName: 'Case Size',
        field: 'caseSize',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Case Size Received',
        field: 'caseSizeReceived',
        filter: 'agNumberColumnFilter',
        cellRenderer: toColorChip,
        cellRendererParams: {
          getChipColor: getChipColorForCaseSizeReceived,
          formatChipValue: toLocalNumber
        },
        filter: 'agMultiColumnFilter',
        filterParams: {
          filters: [
            {
              filter: 'AgCaseSizeMismatchFilter'
            },
            {
              filter: 'agNumberColumnFilter'
            }
          ]
        }
      },
      {
        headerName: 'Quantity Ordered',
        field: 'quantityOrdered',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Quantity Received',
        field: 'quantityReceived',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Cases Received',
        field: 'casesReceived',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      },
      {
        headerName: 'Date Received',
        field: 'dateReceived',
        cellClass: 'text-left',
        width: cellWidths.xm,
        valueFormatter: toFormattedDate(tokens.timestamp.secondary),
        filter: 'agMultiColumnFilter',
        filterParams: {
          filters: [
            {
              filter: 'agDateColumnFilter',
              filterParams: {
                comparator: dateFilterComparator,
                inRangeInclusive: true
              }
            },
            {
              filter: 'agSetColumnFilter',
              filterParams: {
                valueFormatter: toFormattedDate(tokens.timestamp.secondary)
              }
            }
          ]
        }
      },
      {
        headerName: 'Total Price PO',
        field: 'totalPrice',
        filter: 'agNumberColumnFilter',
        cellClass: 'text-right',
        valueFormatter: toFormattedMoney
      },
      {
        headerName: 'Total Price Received',
        field: 'totalPriceReceived',
        filter: 'agNumberColumnFilter',
        cellClass: 'text-right',
        valueFormatter: toFormattedMoney
      },
      {
        headerName: 'Emergency Reason',
        field: 'emergencyReason',
        width: cellWidths.l
      },
      {
        headerName: 'Ship Method',
        field: 'shipMethod',
        width: cellWidths.m
      },
      {
        headerName: 'PHF Delivery % of Forecast',
        field: 'forecastDeliveryPercent',
        width: cellWidths.l,
        valueFormatter: toPercent
      }
    ]
  }
}
