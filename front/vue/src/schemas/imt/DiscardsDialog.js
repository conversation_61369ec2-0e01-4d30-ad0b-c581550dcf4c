import { cellWidths, createColumn } from '@/modules/grid/helpers'
import { toFormattedDate, toLocalNumber } from '@/modules/grid/cells'
import { tokens } from '@/modules/date'

export default {
  main({ isData }) {
    if (!isData.value) return []

    return [
      createColumn({
        headerName: 'Site',
        field: 'site',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'SKU',
        field: 'skuCode',
        cellClass: 'text-left',
        width: cellWidths.l
      }),
      createColumn({
        headerName: 'Discard Date',
        field: 'discardDate',
        cellClass: 'text-left',
        width: cellWidths.l,
        valueFormatter: toFormattedDate(tokens.date.default)
      }),
      createColumn({
        headerName: 'Quantity',
        field: 'quantity',
        filter: 'agNumberColumnFilter',
        width: cellWidths.m,
        valueFormatter: toLocalNumber
      }),
      createColumn({
        headerName: 'Transaction Type',
        field: 'tranType',
        width: cellWidths.xl,
        cellClass: 'text-left'
      })
    ]
  }
}
