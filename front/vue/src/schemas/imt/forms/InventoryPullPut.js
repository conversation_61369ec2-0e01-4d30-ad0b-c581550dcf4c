import {
  defaultSelectParams,
  siteEditorParams,
  siteTooltipParams
} from '@/modules/procurement-forms'
import { setOnlyNumbers, cellWidths } from '@/modules/grid/helpers'
import { toFormattedDate } from '@/modules/grid/cells'
import { dateFilterComparator } from '@/modules/date'
import { manualFormsApi } from '@/api/manual-forms'

export default {
  main({ readonly, inventoryOptions, actions }) {
    return [
      {
        headerName: 'Brand',
        field: 'brand',
        width: cellWidths.xs
      },
      {
        headerName: 'DC',
        field: 'dc',
        width: cellWidths.xs
      },
      {
        headerName: 'Type',
        field: 'type',
        width: cellWidths.m,
        editable: !readonly.value,
        cellClass: (!readonly.value && 'ag-cell-editable') || '',
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: { values: inventoryOptions },
        singleClickEdit: true
      },
      {
        headerName: 'SKU #',
        field: 'skuCode',
        width: cellWidths.m
      },
      {
        headerName: 'SKU Name',
        field: 'skuName',
        width: cellWidths.xxxl
      },
      {
        headerName: 'Quantity',
        field: 'qty',
        width: cellWidths.m,
        editable: !readonly.value,
        cellClass: (!readonly.value && 'ag-cell-editable') || '',
        cellEditor: 'agNumberCellEditor',
        cellEditorParams: {
          min: 1,
          precision: 0,
          placeholder: 'Enter a number greater then 0'
        },
        valueSetter: (params) =>
          setOnlyNumbers(params, {
            couldBeEmpty: false
          })
      },
      {
        headerName: 'Last Edited By',
        field: 'userEmail',
        width: cellWidths.xl
      },
      {
        headerName: 'Edited At',
        field: 'updatedAt',
        width: cellWidths.m,
        sort: 'desc',
        valueFormatter: toFormattedDate(),
        filter: 'agMultiColumnFilter',
        filterParams: {
          filters: [
            {
              filter: 'agSetColumnFilter',
              filterParams: {
                valueFormatter: toFormattedDate()
              }
            },
            {
              filter: 'agDateColumnFilter',
              filterParams: {
                comparator: dateFilterComparator,
                inRangeInclusive: true
              }
            }
          ]
        }
      },
      {
        headerName: 'UOM',
        field: 'unitOfMeasure',
        width: cellWidths.xs
      },
      {
        headerName: 'Comments',
        field: 'comment',
        width: cellWidths.xl,
        editable: !readonly.value,
        cellClass: (!readonly.value && 'ag-cell-editable') || '',
        cellEditorParams: {
          placeholder: 'Optional. E.g. “Transferred to NW - 12/29”'
        }
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        hide: readonly.value,
        valueGetter: () => actions,
        cellRenderer: 'AgRowActions',
        width: cellWidths.xs
      }
    ]
  },
  addRecords({ available, current, inventoryOptions }) {
    return [
      {
        headerName: 'Brand',
        field: 'brand',
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: () => defaultSelectParams(available.brands),
        tooltipComponentParams: {
          hints: [`Valid brands: ${available.brands.join(', ')}`]
        }
      },
      {
        headerName: 'Site',
        field: 'dc',
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: (params) => siteEditorParams(params, available.sitesByGroup),
        tooltipComponentParams: (params) => siteTooltipParams(params, available.sitesByGroup)
      },
      {
        headerName: 'Type',
        field: 'type',
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: () => defaultSelectParams(inventoryOptions),
        tooltipComponentParams: {
          hints: [`Valid types: ${inventoryOptions.join(', ')}`]
        }
      },
      {
        headerName: 'SKU Name',
        field: 'skuName',
        tooltipComponentParams: {
          hints: ['Make sure that the SKU Name matches one of the registered values']
        },
        cellEditor: 'AgAutocomplete',
        cellEditorParams: {
          placeholder: 'Start typing SKU Name',
          getOptions: manualFormsApi.pullPut.skus,
          isApiSearch: true
        }
      },
      {
        headerName: 'Quantity',
        field: 'qty',
        cellEditor: 'agNumberCellEditor',
        cellEditorParams: {
          min: 1,
          precision: 0,
          placeholder: 'Enter a number greater then 0'
        },
        valueSetter: (params) => setOnlyNumbers(params)
      },
      {
        headerName: 'Comment',
        field: 'comment',
        minWidth: 140,
        cellEditorParams: {
          placeholder: 'Optional. E.g. “Transferred to NW - 12/29”'
        }
      },
      {
        field: 'error',
        hide: true
      }
    ]
  }
}
