import { cellWidths, createColumn } from '@/modules/grid/helpers'
import {
  getChipColorForIcsTicketId,
  toColorChip,
  toFormattedDate,
  toLink,
  toTicketProductionImpact
} from '@/modules/grid/cells'
import { dateFilterComparator, tokens } from '@/modules/date'

export default {
  main() {
    return [
      createColumn({
        headerName: 'Ticket ID',
        field: 'ticketId',
        cellClass: 'text-center',
        cellRenderer: toColorChip,
        cellRendererParams: {
          getChipColor: getChipColorForIcsTicketId
        },
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Ticket Link',
        field: 'ticketLink',
        cellClass: 'text-left',
        cellRenderer: toLink,
        width: cellWidths.xl
      }),
      createColumn({
        headerName: 'Request Type',
        field: 'requestType',
        width: cellWidths.xl,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Production Impact',
        field: 'productionImpact',
        cellRenderer: toTicketProductionImpact,
        width: cellWidths.x,
        cellClass: 'text-center'
      }),
      createColumn({
        headerName: 'Status',
        field: 'status',
        width: cellWidths.m,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Subject',
        field: 'subject',
        tooltipField: 'subject',
        cellClass: 'text-left',
        width: cellWidths.xx
      }),
      createColumn({
        headerName: 'PO #',
        field: 'poNumber',
        tooltipField: 'poNumber',
        cellClass: 'text-left',
        width: cellWidths.l
      }),
      createColumn({
        headerName: 'SKU',
        field: 'skuCode',
        cellClass: 'text-left',
        width: cellWidths.l
      }),
      createColumn({
        headerName: 'Bob Code',
        field: 'bobCode',
        cellClass: 'text-left',
        width: cellWidths.m
      }),
      createColumn({
        headerName: 'Week',
        field: 'week',
        width: cellWidths.m,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Updated At',
        field: 'updatedAt',
        filter: 'agMultiColumnFilter',
        cellClass: 'text-left',
        valueFormatter: toFormattedDate(tokens.date.default),
        filterParams: {
          filters: [
            {
              filter: 'agSetColumnFilter',
              filterParams: {
                valueFormatter: toFormattedDate(tokens.date.default)
              }
            },
            {
              filter: 'agDateColumnFilter',
              filterParams: {
                comparator: dateFilterComparator,
                inRangeInclusive: true
              }
            }
          ]
        },
        width: cellWidths.xm
      })
    ]
  }
}
