import { toLocalNumber } from '@/modules/grid/cells'
import { cellWidths, sortAlphaNumeric, createColumn } from '@/modules/grid/helpers'

export default {
  main({ isFactorBrand, current }) {
    const commonGroup = [
      createColumn({
        headerName: 'SKU Name',
        field: 'skuName',
        width: cellWidths.xxxl,
        cellRenderer: 'AgCellWithFlags',
        cellRendererParams: { current },
        cellClass: ({ data }) => (data.isComment ? 'ag-cell-with-comment' : '')
      }),
      createColumn({
        headerName: 'HF Full SKU',
        field: 'sku',
        width: cellWidths.xl,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Purchasing Category',
        field: 'purchasingCategory',
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Full SKU + SKU Name',
        field: 'fullSkuPlusSkuName',
        width: cellWidths.lCodePlusSkuName,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Weight Amount',
        field: 'weightAmount',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      }),
      createColumn({
        headerName: 'Weight Unit',
        field: 'weightUnit',
        width: cellWidths.xl,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Storage Location',
        field: 'storageLocation',
        width: cellWidths.xxl,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Allergens',
        field: 'allergens',
        cellClass: 'text-left'
      })
    ]

    const defaultScheme = [
      createColumn({
        headerName: 'Recipe Code',
        field: 'code',
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Meal #',
        field: 'mealNumber',
        width: cellWidths.s,
        cellClass: 'text-left',
        comparator: sortAlphaNumeric
      }),
      createColumn({
        headerName: 'Recipe Name',
        field: 'mealName',
        width: cellWidths.x,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'HF 5-Digit SKU',
        field: 'skuId',
        filter: 'agNumberColumnFilter',
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Picks - 2-Person',
        field: 'picks2p',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      }),
      createColumn({
        headerName: 'Picks - 3-Person',
        field: 'picks3p',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      }),
      createColumn({
        headerName: 'Picks - 4-Person',
        field: 'picks4p',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      }),
      createColumn({
        headerName: 'Picks - 6-Person',
        field: 'picks6p',
        filter: 'agNumberColumnFilter',
        valueFormatter: toLocalNumber
      }),
      ...commonGroup
    ]

    const factorScheme = [
      createColumn({
        headerName: 'Recipe Code',
        field: 'mealNumber',
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'Recipe Name',
        field: 'mealName',
        width: cellWidths.x,
        cellClass: 'text-left'
      }),
      createColumn({
        headerName: 'HF 5-Digit SKU',
        field: 'skuId',
        filter: 'agNumberColumnFilter',
        cellClass: 'text-left'
      }),
      ...commonGroup,
      createColumn({
        headerName: 'Sub Recipe ID',
        field: 'code',
        cellClass: 'text-left',
        width: cellWidths.xm
      }),
      createColumn({
        headerName: 'Sub Recipe',
        field: 'subRecipe',
        cellClass: 'text-left',
        width: cellWidths.x
      })
    ]

    return [...(isFactorBrand.value ? factorScheme : defaultScheme)]
  }
}
