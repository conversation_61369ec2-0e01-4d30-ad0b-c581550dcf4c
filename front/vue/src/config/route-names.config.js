export const routeNames = {
  imt: {
    ingredientsDepletion: 'imt::ing-depl',
    packagingDepletion: 'imt::pack-depl',
    poStatus: 'imt::po-status',
    remakeTool: 'imt::remake-tool',
    productionNeedIngredients: 'imt::production-need-ingridients',
    productionKitGuide: 'imt::production-kit-guide',
    topVariances: 'imt::top-variances'
  },
  forms: {
    inventoryPullPut: 'forms::pull-put',
    poVoid: 'forms::po-void',
    receiptOverride: 'forms::receipt-override',
    weekendCoverageChecklist: 'forms::weekend-coverage-checklist',
    discardForm: 'forms::discard-form'
  },
  logs: {
    imtComments: 'logs::imt-comments',
    pimtComments: 'logs::pimt-comments'
  },
  pimt: {
    opsMain: 'pimt::ops-main',
    expiringInventoryReport: 'pimt::exp-inventory',
    ambientSafetyStock: 'pimt::ambient-safety-stock',
    metrics: 'pimt::metrics'
  },
  calc: {
    expectedDailyNeed: 'calc::expected-daily-need',
    perpetualGrossNeeds: 'calc::perpetual-gross-needs'
  },
  inventory: {
    inventoryModule: 'inventory::inventory-module',
    networkDepletion: 'inventory::network-depletion',
    unifiedInventory: 'inventory::unified-inventory',
    replenishment: 'inventory::replenishment'
  },
  admin: {
    gSheetImt: 'admin::gsheet-imt',
    gSheetPimt: 'admin::gsheet-pimt',
    brands: 'admin::brands',
    sites: 'admin::sites',
    warehouses: 'admin::warehouses',
    syncJobs: 'admin::sync-jobs',
    bulkSkus: 'admin::bulk-skus',
    releaseLog: 'admin::release-log',
    skuForecastDemand: 'admin::sku-forecast-demand'
  },
  buyer: {
    dashboard: 'buyer::dashboard'
  }
}
