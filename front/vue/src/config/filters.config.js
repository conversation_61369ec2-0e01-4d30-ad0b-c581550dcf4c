import { siteTypes, warehousesBrand } from '@/config/app.config'
import { routeNames } from '@/config/route-names.config'
import envConfig from './env.config'

const { imt, inventory, buyer, forms, calc } = routeNames

export const filtersConfig = {
  [inventory.inventoryModule]: {
    sites: {
      includedOnly: [siteTypes.hj, siteTypes.threePl]
    }
  },
  [imt.ingredientsDepletion]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [imt.remakeTool]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [imt.productionNeedIngredients]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [imt.productionKitGuide]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [imt.poStatus]: {
    brands: {
      included: [warehousesBrand]
    }
  },
  [buyer.dashboard]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [forms.inventoryPullPut]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [forms.weekendCoverageChecklist]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [forms.discardForm]: {
    sites: {
      excluded: [siteTypes.hjDiscard]
    }
  },
  [calc.expectedDailyNeed]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  },
  [calc.perpetualGrossNeeds]: {
    sites: {
      excluded: [siteTypes.fulfilment]
    }
  }
}
