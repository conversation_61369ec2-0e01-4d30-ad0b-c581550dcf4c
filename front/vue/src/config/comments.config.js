import { isObj } from '@/utils'
import { defaultBrand } from '@/config/app.config'
import { getCommentColumnByParams } from '@/modules/grid/helpers'

export const commentTypes = {
  common: 'common',
  replenishment: 'replenishment',
  networkDepl: 'networkDepl'
}

export const commentsConfig = {
  [commentTypes.common]: {
    getRequestParams(params) {
      const { current, data } = params
      const columnContext = getCommentColumnByParams(params)

      if (!(current && columnContext)) return null

      const { resourceType, resourceField } = columnContext
      const domain = current.domain || 'imt'
      const site =
        domain === 'imt'
          ? data.site || data.dc || current.site
          : data.dc || data.whCode || data.region || current.region

      return {
        domain,
        brand: data.brand || current.brand || defaultBrand,
        week: params.week || data.week || current.week,
        site,
        resourceType,
        resourceId: data[resourceField]
      }
    }
  },
  [commentTypes.replenishment]: {
    getRequestParams(params) {
      const {
        current: { site: region },
        data: { sku: skuCode }
      } = params

      return {
        region,
        skuCode
      }
    }
  },
  [commentTypes.networkDepl]: {
    getRequestParams(params) {
      const {
        current: { week },
        currentPresetId,
        data: { sku: skuCode }
      } = params

      return {
        skuCode,
        presetId: isObj(currentPresetId) ? currentPresetId.value : currentPresetId,
        week
      }
    }
  }
}
