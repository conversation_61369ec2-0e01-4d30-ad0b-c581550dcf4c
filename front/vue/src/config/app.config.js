import envConfig from '@/config/env.config'
import { permissionsConfig } from './permissions.config'

export const apiUrlV2 = envConfig.API_URL && envConfig.API_URL.replace('/v1', '/v2')
export const serviceEmail = '<EMAIL>'

export const menuScheme = [
  {
    name: 'Admin',
    path: 'admin',
    groups: [
      {
        name: 'IMT',
        path: 'imt',
        items: [
          {
            name: 'GSheets',
            path: 'gsheets',
            permissions: permissionsConfig.adminGsheet.read
          },
          {
            name: 'Brands',
            path: 'brands',
            permissions: permissionsConfig.adminGsheet.read
          },
          {
            name: 'Sites',
            path: 'sites',
            permissions: permissionsConfig.adminRegion.read
          }
        ]
      },
      {
        name: 'PIMT',
        path: 'pimt',
        items: [
          {
            name: 'GSheets',
            path: 'gsheets',
            permissions: permissionsConfig.adminGsheet.read
          },
          {
            name: 'Warehouses',
            path: 'warehouses',
            permissions: permissionsConfig.pimtPartners.read
          }
        ]
      },
      {
        name: 'Jobs',
        path: 'jobs',
        items: [
          {
            name: 'Synchronization Jobs',
            path: 'sync-jobs',
            permissions: permissionsConfig.adminJobs.read
          },
          {
            name: 'OPE Demand Data',
            path: 'sku-demand',
            permissions: permissionsConfig.skuForecastDemand.read
          }
        ]
      },
      {
        name: 'Skus',
        path: 'skus',
        items: [
          {
            name: 'Bulk SKUs',
            path: 'bulk-skus',
            permissions: permissionsConfig.adminRegion.read
          }
        ]
      },
      {
        name: 'Logs',
        path: 'logs',
        items: [
          {
            name: 'Release log',
            path: 'release-log',
            permissions: permissionsConfig.adminReleaseLog.read
          }
        ]
      }
    ]
  },
  {
    name: 'Buyer',
    path: 'buyer',
    groups: [
      {
        name: 'Dashboards',
        path: 'dashboards',
        items: [
          {
            name: 'Buyer Dashboard',
            path: 'buyer-dashboard',
            permissions: permissionsConfig.imtBuyer.read
          }
        ]
      }
    ]
  },
  {
    name: 'PIMT',
    path: 'pimt',
    groups: [
      {
        name: 'Dashboards',
        path: 'dashboards',
        items: [
          {
            name: 'OPs Main Dashboard',
            path: 'ops-main-dashboard',
            permissions: permissionsConfig.pimtOpsMainDashboard.read
          },
          {
            name: 'Ambient Safety Stock',
            path: 'ambient-safety-stock',
            permissions: permissionsConfig.ambientSafetyStock.read
          },
          {
            name: 'Expiring Inventory Report',
            path: 'expiring-inventory-report',
            permissions: permissionsConfig.pimtAgingInventoryTool.read
          },
          {
            name: 'Metrics',
            path: 'metrics',
            permissions: permissionsConfig.pimtExceptionMetrics.read
          }
        ]
      },
      {
        name: 'Logs',
        path: 'logs',
        items: [
          {
            name: 'Comments Log',
            path: 'comments',
            permissions: permissionsConfig.imtCommentLog.read
          }
        ]
      }
    ]
  },
  {
    name: 'IMT',
    path: 'imt',
    groups: [
      {
        name: 'Dashboards',
        path: 'dashboards',
        items: [
          {
            name: 'Ingredients Depletion',
            path: 'ingredients-depletion',
            permissions: permissionsConfig.imtIngredientDepletion.read
          },
          {
            name: 'Packaging Depletion',
            path: 'packaging-depletion',
            permissions: permissionsConfig.general.read
          },
          {
            name: 'PO Status View',
            path: 'po-status-view',
            permissions: permissionsConfig.imtPoStatus.read
          },
          {
            name: 'Remake Tool',
            path: 'remake-tool',
            permissions: permissionsConfig.imtRemakeTool.read
          },
          {
            name: 'Production Need - Ingredients',
            path: 'production-need-ingredients',
            permissions: permissionsConfig.imtProductionNeed.read
          },
          {
            name: 'Production Kit Guide',
            path: 'production-kit-guide',
            permissions: permissionsConfig.imtProductionKitGuide.read
          }
        ]
      },
      {
        name: 'Procurement Analytics',
        path: 'procurement-analytics',
        items: [
          {
            name: 'Top Variances',
            path: 'top-variances',
            permissions: permissionsConfig.imtIngredientDepletion.read
          }
        ]
      },
      {
        name: 'Procurement Manual Forms',
        path: 'procurement-manual-forms',
        items: [
          {
            name: 'Inventory Pull/Put',
            path: 'inventory-pull-put',
            permissions: permissionsConfig.pullPutForm.read
          },
          {
            name: 'PO Void',
            path: 'po-void',
            permissions: permissionsConfig.poVoidForm.read
          },
          {
            name: 'Receipt Override',
            path: 'receipt-override',
            permissions: permissionsConfig.receiptOverrideForm.read
          },
          {
            name: 'Weekend Coverage Checklist',
            path: 'weekend-coverage-checklist',
            permissions: permissionsConfig.weekendCoverageChecklist.read
          }
        ]
      },
      {
        name: 'Fulfillment Site Manual Forms',
        path: 'fulfillment-forms',
        items: [
          {
            name: 'Discard Form',
            path: 'discard-form',
            permissions: permissionsConfig.discard.read
          }
        ]
      },
      {
        name: 'Logs',
        path: 'logs',
        items: [
          {
            name: 'Comments Log',
            path: 'comments',
            permissions: permissionsConfig.imtCommentLog.read
          }
        ]
      }
    ]
  },
  {
    name: 'Inventory',
    path: 'inventory',
    groups: [
      {
        name: 'Dashboards',
        path: 'dashboards',
        items: [
          {
            name: 'Inventory Module',
            path: 'inventory-module',
            permissions: permissionsConfig.inventoryModule.read
          },
          {
            name: 'Network Depletion Module',
            path: 'network-depletion-module',
            permissions: permissionsConfig.inventoryNetworkDepletion.read
          },
          {
            name: 'Unified Inventory Module',
            path: 'unified-inventory',
            permissions: permissionsConfig.unifiedInventory.read
          },
          {
            name: 'Risk Analysis & Replenishment',
            path: 'replenishment',
            permissions: permissionsConfig.replenishment.read
          }
        ]
      }
    ]
  },
  {
    name: 'Calculations',
    path: 'calculations',
    groups: [
      {
        name: 'Dashboards',
        path: 'dashboards',
        items: [
          {
            name: 'Expected Daily Need',
            path: 'expected-daily-need',
            permissions: permissionsConfig.imtInventoryV2.read
          },
          {
            name: 'Perpetual Gross Needs',
            path: 'perpetual-gross-needs',
            permissions: permissionsConfig.imtInventoryV2.read
          }
        ]
      }
    ]
  }
]

export const defaultBrand = 'HF'

export const filterTypes = {
  brand: 'brand',
  site: 'site',
  period: 'period',
  region: 'region',
  pod: 'pod'
}

export const siteTypes = {
  threePl: 'threePl',
  fulfilment: 'fulfilment',
  hj: 'hj',
  hjAutostore: 'hjAutostore',
  hjDiscard: 'hjDiscard'
}

export const authEnum = {
  accessToken: 'access_token',
  refreshToken: 'refresh_token',
  authError: 'auth_error'
}

export const commentTypes = {
  sku: 'sku',
  po: 'po'
}

export const warehousesBrand = {
  Warehouses: {
    brandName: 'Warehouses',
    consolidated: false,
    sites: []
  }
}

export const respondTimeout = 35000 // 35 sec
export const debugRespondTimeout = 40000 // 40 sec
