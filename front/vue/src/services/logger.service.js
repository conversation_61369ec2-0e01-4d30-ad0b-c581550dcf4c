import envConfig from '@/config/env.config'
import Node<PERSON>sLogger from '@/services/nodejs-logger.service'
import httpStatusEnum from '@/api/http-status.enum'

let instance
const logLevels = ['info', 'error']

/**
 * Loggers service
 * @constructor
 */
class Logger {
  constructor() {
    if (!instance) {
      this.transports = []
      this.init()
      instance = this
    }
    return instance
  }

  init() {
    if (envConfig.APP_DEBUG) {
      this.transports.push(console)
    }

    if (envConfig.IS_PROD) {
      this.transports.push(new NodeJsLogger())
    }
  }
  shouldLog(method) {
    return logLevels.includes(method)
  }
  send(method, input) {
    try {
      if (this.shouldLog(method)) {
        this.transports.forEach((transport) => {
          const logMethod = transport[method]

          if (!logMethod) return

          const shouldStringify = !!transport.info
          const data = shouldStringify ? JSON.stringify(input) : input

          return logMethod(data)
        })
      }
    } catch (e) {
      console.error(e)
    }
  }
  info(input) {
    return this.send('info', input)
  }
  error(input) {
    const { error: { response, config } = {} } = input

    // don't process unauthorized error
    if (response && response.status === httpStatusEnum.UNAUTHORIZED_CODE) return
    if (config) {
      const { url, data, method } = config

      input.request = { url, data, method }
    }

    return this.send('error', input)
  }
}

export default new Logger()
