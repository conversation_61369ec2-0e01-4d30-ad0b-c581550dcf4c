import axios from 'axios'

class HttpService {
  get(path, params, headers = {}) {
    return axios
      .get(path, { params, ...headers })
      .then(({ data: responseData = {}, headers: responseHeaders = {} }) => {
        return {
          data: responseData.data || responseData,
          warnings: responseData.warnings || [],
          headers: responseHeaders,
          pageKeys: responseData.pageKeys || []
        }
      })
  }

  post(path, payload, params, extraParams = {}) {
    return axios
      .post(path, payload, { params, ...extraParams })
      .then((response) => response.data.data || response.data)
  }

  put(path, payload, params) {
    return axios
      .put(path, payload, { params })
      .then((response) => response.data.data || response.data)
  }

  patch(path, payload, params) {
    return axios
      .patch(path, payload, { params })
      .then((response) => response.data.data || response.data)
  }

  delete(path, params = {}, data = {}) {
    return axios
      .delete(path, { params, data })
      .then((response) => response.data.data || response.data)
  }
}

export default new HttpService()
