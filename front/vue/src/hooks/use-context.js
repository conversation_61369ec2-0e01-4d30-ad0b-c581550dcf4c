import { getCurrentInstance } from 'vue'
/**
 *
 * @typedef {Object} IContext
 * @property {route: Object} IContext.route - route
 * @property {refs: Object} IContext.refs - refs
 * @property {attrs: Object} IContext.attrs - refs
 */

/**
 * Hook returns component instance context
 * @returns {IContext} context object
 * */
export function useContext() {
  const { proxy: instance } = getCurrentInstance()
  const { $route: route, $refs: refs, $attrs: attrs } = instance

  return { route, refs, attrs }
}
