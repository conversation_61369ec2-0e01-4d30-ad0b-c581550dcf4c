import { io } from 'socket.io-client'
import envConfig from '@/config/env.config'
import {
  SOCKETS_CONNECT_ERROR_EVENT,
  SOCKETS_CONNECT_EVENT,
  SOCKETS_ARE_CONNECTED_MESSAGE,
  SOCKETS_ARE_DISABLED_MESSAGE,
  SOCKETS_ARE_NOT_AUTHENTICATED_MESSAGE,
  SOCKETS_CONNECTION_ERROR_MESSAGE,
  SOCKETS_INSTANCE_NOT_INITIALIZED_MESSAGE
} from '@/modules/sockets/messages'

/**
 * Hook that introduces ways to work with websockets
 * @returns {{
 *   sockets: Object,
 *   initSockets: (loginData: { email: string, token: string }) => void,
 *   subscribeToSocketsEvent: (eventName: string, fn: () => void),
 *   unsubscribeFromSocketsEvent: (eventName: string)
 * }}
 * object with methods to subscribe or unsubscribe from socket events
 * */

const sockets = {}

export function useSockets() {
  const initSockets = async (loginData) => {
    if (!envConfig.WEBSOCKET_ENABLED) {
      console.log(SOCKETS_ARE_DISABLED_MESSAGE)
      return
    }

    if (!loginData) {
      console.log(SOCKETS_ARE_NOT_AUTHENTICATED_MESSAGE)
      return
    }

    sockets.instance = io(`${envConfig.WEB_SERVER_URL || window.location.origin}/v1`, {
      autoConnect: false,
      transports: ['websocket'],
      reconnectionDelay: 5000,
      withCredentials: true,
      query: {
        ...loginData
      }
    })

    sockets.instance.on(SOCKETS_CONNECT_EVENT, () => console.log(SOCKETS_ARE_CONNECTED_MESSAGE))

    sockets.instance.on(SOCKETS_CONNECT_ERROR_EVENT, (error) =>
      console.log(SOCKETS_CONNECTION_ERROR_MESSAGE, error.message)
    )

    sockets.instance.connect()
  }

  const subscribeToSocketsEvent = (eventName, fn) => {
    if (!envConfig.WEBSOCKET_ENABLED) {
      console.log(SOCKETS_ARE_DISABLED_MESSAGE)
      return
    }
    if (!sockets.instance) {
      console.log(SOCKETS_INSTANCE_NOT_INITIALIZED_MESSAGE)
      return
    }

    sockets.instance.on(eventName, fn)
  }

  const unsubscribeFromSocketsEvent = (eventName) => {
    if (!envConfig.WEBSOCKET_ENABLED) {
      console.log(SOCKETS_ARE_DISABLED_MESSAGE)
      return
    }
    if (!sockets.instance) {
      console.log(SOCKETS_INSTANCE_NOT_INITIALIZED_MESSAGE)
      return
    }

    sockets.instance.removeAllListeners(eventName)
  }

  return {
    sockets,
    initSockets,
    subscribeToSocketsEvent,
    unsubscribeFromSocketsEvent
  }
}
