import { createNamespacedHelpers } from 'vuex-composition-helpers'
import { reactive, watch } from 'vue'
import { filterTypes } from '@/config/app.config'
import { useEventBus } from '@/hooks'
import { isEmptyObj } from '@/utils'
import { IAvailable, ICurrent } from './index'

const { useGetters, useActions } = createNamespacedHelpers('Filters')

/**
 *
 * @typedef {Object} IActions
 * @property {({ commit: (mutation: String, value: any) => void }, brand: String) => void} changeBrand
 * @property {({ commit: (mutation: String, value: any) => void }, commentLogType: String) => void} changeCommentLogType
 * @property {({ commit: (mutation: String, value: any) => void }, pod: String) => void} changePod
 * @property {({ commit: (mutation: String, value: any) => void }, quarter: String) => void} changeQuarter
 * @property {({ commit: (mutation: String, value: any) => void }, site: String) => void} changeSite
 * @property {({ commit: (mutation: String, value: any) => void }, week: String) => void} changeWeek
 * @property {({ commit: (mutation: String, value: any) => void }, region: String) => void} changeRegion
 * @property {({ commit: (mutation: String, value: any) => void }, domain: String) => void} changeDomain
 * @property {({ commit: (mutation: String, value: any) => void }, domain: String) => void} changeFilterOptions
 * @property {Function} changeFilter
 * @property {Function} resetWeekConfig
 */

/**
 *
 * @typedef {Object} IAppFilter
 * @property {IAvailable} available - all possible filters data
 * @property {ICurrent} current - currently selected filters data
 * @property {IActions} actions - filters actions
 */

/**
 *
 * @typedef {Object} IFilterParams
 * @property {Boolean} isDashboard - is dashboard flag
 * @property {Boolean} isWeekConfigNeeded - if need to load week config
 */

/**
 * Hook with methods and properties to work with filters across all app
 * @param {IFilterParams} object - filter params
 * @returns {IAppFilter} object with filters data
 * */

export function useFilter(
  { isDashboard, isWeekConfigNeeded = true } = {
    isDashboard: false,
    isWeekConfigNeeded: true
  }
) {
  const { busEmit } = useEventBus()
  const {
    brand,
    brandFullName,
    brandsWithSites,
    site,
    sitesByType,
    week,
    defaultWeek,
    prevWeek,
    nextWeek,
    ongoingWeek,
    quarter,
    pod,
    commentLogType,
    year,
    region,
    domain,
    brands,
    allBrands,
    brandsWithDetails,
    currentSites,
    sitesByGroup,
    weeks,
    allWeeks,
    quarters,
    quartersWithWeeks,
    pods,
    commentLogTypes,
    years,
    regions,
    filterOptions
  } = useGetters([
    'brand',
    'brandFullName',
    'brandsWithSites',
    'site',
    'sitesByType',
    'week',
    'defaultWeek',
    'prevWeek',
    'nextWeek',
    'ongoingWeek',
    'quarter',
    'pod',
    'commentLogType',
    'year',
    'region',
    'domain',
    'brands',
    'allBrands',
    'brandsWithDetails',
    'currentSites',
    'sitesByGroup',
    'weeks',
    'allWeeks',
    'quarters',
    'quartersWithWeeks',
    'pods',
    'commentLogTypes',
    'years',
    'regions',
    'filterOptions'
  ])

  const {
    changeBrand,
    changeCommentLogType,
    changePod,
    changeQuarter,
    changeSite,
    changeWeek,
    changeRegion,
    changeDomain,
    fetchWeekConfig,
    resetWeekConfig,
    changeFilterOptions
  } = useActions([
    'changeBrand',
    'changeCommentLogType',
    'changePod',
    'changeQuarter',
    'changeSite',
    'changeWeek',
    'changeRegion',
    'changeDomain',
    'fetchWeekConfig',
    'resetWeekConfig',
    'changeFilterOptions'
  ])

  const available = reactive({
    brands,
    brandsWithDetails,
    brandsWithSites,
    allBrands,
    sites: currentSites,
    sitesByGroup,
    sitesByType,
    quarters,
    quartersWithWeeks,
    weeks,
    allWeeks,
    filters: filterTypes,
    pods,
    commentLogTypes,
    years,
    regions,
    filterOptions
  })

  const current = reactive({
    brand,
    brandFullName,
    site,
    quarter,
    week,
    defaultWeek,
    ongoingWeek,
    prevWeek,
    nextWeek,
    pod,
    commentLogType,
    year,
    region,
    domain
  })

  async function changeFilter() {
    const { week } = current

    if (!isDashboard) return
    if (isWeekConfigNeeded) {
      if (!week) return
      if (isEmptyObj(brandsWithSites.value)) return fetchWeekConfig(week)
    }

    return busEmit('filter-changed')
  }

  watch(current, changeFilter)

  const actions = {
    changeBrand,
    changeCommentLogType,
    changePod,
    changeQuarter,
    changeSite,
    changeWeek,
    changeRegion,
    changeDomain,
    changeFilter,
    resetWeekConfig,
    changeFilterOptions
  }

  return { available, actions, current }
}
