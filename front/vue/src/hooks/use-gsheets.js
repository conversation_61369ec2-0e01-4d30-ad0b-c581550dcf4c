import { gsheetsApi } from '@/api/gsheets'
import { ref, computed, reactive, Ref } from 'vue'
import { ICurrent } from './index'

/**
 * @typedef {{ statusIcon: String, statusColor: String, statusText: String }} IGsheetStatusBar
 */

/**
 * @typedef {{ color: String, icon: String, error: Boolean, errorMessage: String, success: Boolean, loading: <PERSON>olean }} IGsheetState
 */

/**
 * Hook to manage gsheets
 * @param {Object} useGsheetsParams
 * @param {Object} useGsheetsParams.refs - dashboard refs
 * @param {Ref<Object>} useGsheetsParams.gsheets - gsheets data
 * @param {ICurrent} useGsheetsParams.current - currently selected filters data
 * @param {String} useGsheetsParams.type - gsheet type
 * @returns {{
 *   rules: Ref<Object>,
 *   statusBar: IGsheetStatusBar,
 *   prepareState: IGsheetState,
 *   validate: (gsheet: Object) => Promise<Boolean>
 *   preValidationCheck: (gsheet: Object, gsheetUrlFromInput: String) => Promise<Boolean>
 *   redirect: (gsheet: Object) => Window
 *   inputPrependIcon: (isValid: Boolean) => String
 *   refreshAll: () => Promise<void>
 * }}
 * Object that contains properties and methods to manage gsheets data
 * */

export function useGsheets({ refs, gsheets, current = { week: '', quarter: '' }, type = '' }) {
  const defaultGsheetProps = {
    color: null,
    icon: null,
    error: false,
    errorMessage: null,
    success: false,
    loading: false
  }
  const validGsheetProps = {
    color: 'success',
    icon: 'mdi-check',
    success: true,
    error: false
  }
  const invalidGsheetProps = {
    color: 'red',
    icon: 'mdi-alert-circle',
    success: false,
    error: true
  }
  const gsheetIdPattern = /^([a-zA-Z0-9-_]+)$/
  const gsheetPattern = /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/
  const rules = ref({
    gsheetUrlValidation: (value) => {
      return (
        (!!value &&
          (gsheetIdPattern.test(value) ||
            (value.match(gsheetPattern) && gsheetIdPattern.test(value.match(gsheetPattern)[1])))) ||
        'Gsheet URL is not valid'
      )
    }
  })

  function prepareState(gsheet) {
    if (!gsheet.url)
      return {
        ...defaultGsheetProps
      }

    if (gsheet.status === 'INVALID')
      return {
        ...defaultGsheetProps,
        ...invalidGsheetProps,
        errorMessage: gsheet.errorMessage
      }

    return {
      ...defaultGsheetProps,
      ...validGsheetProps
    }
  }

  async function preValidationCheck(gsheet, gsheetUrlFromInput) {
    // If gsheet URL is the same as URL from input, validating is redundant

    if (gsheet.url === gsheetUrlFromInput) return false

    // If URL from input is undefined and gsheet url exists, run delete method

    const [currentInputRef] = type ? refs[gsheet.metaId][0].$children : refs[gsheet.metaId]

    if (!gsheetUrlFromInput && gsheet.url) {
      if (refs[gsheet.metaId]) {
        currentInputRef?.resetValidation()
      }

      Object.assign(gsheet, prepareState({}), { url: '', loading: true })

      await deleteGsheet(gsheet.metaId, current.week)

      gsheet.loading = false
      return false
    }

    // Checking if gsheet URL is valid

    if (currentInputRef && !currentInputRef.valid) return false

    // URL is valid, but it still might be only the gsheet ID, so we need to adjust URL accordingly

    gsheet.url = gsheetIdPattern.test(gsheetUrlFromInput)
      ? `https://docs.google.com/spreadsheets/d/${gsheetUrlFromInput}`
      : gsheetUrlFromInput

    return await validate(gsheet)
  }

  async function validate(gsheet) {
    Object.assign(gsheet, { ...defaultGsheetProps, loading: true })

    try {
      await editGsheet(gsheet)

      Object.assign(gsheet, { ...validGsheetProps })
    } catch (error) {
      Object.assign(gsheet, {
        ...invalidGsheetProps,
        errorMessage: error.response.data.msg,
        loading: false
      })
      return false
    } finally {
      gsheet.loading = false
    }

    return true
  }

  function redirect(gsheet) {
    return gsheet.color === 'success' && window.open(gsheet.url, '_blank')
  }

  function inputPrependIcon(isValid) {
    return isValid ? 'mdi-send' : ''
  }

  async function deleteGsheet(id) {
    const params = type
      ? {
          quarter: current.quarter
        }
      : {
          meta_id: id,
          week: current.week
        }

    await gsheetsApi.delete(params, type)
  }

  async function editGsheet(gsheet) {
    const { url, metaId } = gsheet

    const params = type
      ? {
          url,
          quarter: gsheet.metaId
        }
      : {
          url,
          meta_id: metaId,
          week: current.week
        }

    await gsheetsApi.edit(params, type)
  }

  async function refreshAll() {
    if (Array.isArray(gsheets.value)) {
      gsheets.value.forEach((sheet) => {
        sheet.url && validate(sheet)
      })
    } else {
      Object.keys(gsheets.value).forEach((type) => {
        const gsheetsByType = gsheets.value[type]

        gsheetsByType.forEach((sheet) => {
          sheet.url && validate(sheet)
        })
      })
    }
  }

  const statusIcon = computed(() => {
    if (!gsheets.value) return ''

    const docs = Array.isArray(gsheets.value)
      ? gsheets.value
      : Object.keys(gsheets.value).reduce((acc, cur) => [...acc, ...gsheets.value[cur]], [])

    const docsWithErrors = docs.filter((sheet) => sheet.error)
    const isEveryDocFilledAndValid = docs.every((g) => g.success)
    if (docsWithErrors.length) {
      return 'mdi-alert-circle'
    } else if (isEveryDocFilledAndValid) {
      return 'mdi-check-all'
    } else {
      return 'mdi-alert'
    }
  })

  const statusColor = computed(() => {
    switch (statusIcon.value) {
      case 'mdi-alert-circle':
        return '#f44336'
      case 'mdi-check-all':
        return '#138424'
      case 'mdi-alert':
        return '#c18f00'
      default:
        return ''
    }
  })

  const statusText = computed(() => {
    switch (statusIcon.value) {
      case 'mdi-check-all':
        return 'All docs are valid'
      case 'mdi-alert-circle':
        return 'Invalid documents'
      default:
        return 'Revalidate all'
    }
  })

  const statusBar = reactive({ statusIcon, statusColor, statusText })

  return {
    rules,
    statusBar,
    prepareState,
    validate,
    preValidationCheck,
    redirect,
    inputPrependIcon,
    refreshAll
  }
}
