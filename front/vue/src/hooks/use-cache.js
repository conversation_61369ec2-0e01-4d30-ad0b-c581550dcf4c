import { ref, set, computed } from 'vue'
/**
 *
 * @typedef {Object} ICache
 * @property {(data: Array<Object>) => void} ICache.putToCache - puts dashboard data to cache
 * @property {() => Object} ICache.getCache - gets dashboard data from cache
 * @property {() => void} ICache.resetCache - clears the cache
 */

/**
 * Hook with methods to put data to cache and get data from cache
 * @param {Array<String>} dataKeys - keys to store data in cache
 * @returns {ICache} object with cache operating methods
 * */
export function useCache(dataKeys) {
  const cache = ref({})
  const timeouts = ref({})
  const key = computed(() => dataKeys.value.reduce((acc, cur) => (acc ? `${acc}_${cur}` : cur), ''))
  function putToCache(data) {
    set(cache.value, key.value, data)
    timeouts.value[key.value] = Date.now()
  }

  function getCache() {
    const cacheTimeout = 60000
    const cachedData = cache.value[key.value]
    const previousTimestamp = timeouts.value[key.value]
    const isCacheValid = previousTimestamp ? Date.now() - previousTimestamp < cacheTimeout : false

    return isCacheValid && cachedData
  }

  function resetCache() {
    cache.value = {}
  }

  return { putToCache, getCache, resetCache }
}
