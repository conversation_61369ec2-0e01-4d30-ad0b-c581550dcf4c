import axios from 'axios'
import { ref, reactive, computed, toRefs, set, toRaw, Ref, ComputedRef as Computed } from 'vue'
import { ICurrent } from './index'
import { IConsolidatedFilter } from './use-consolidated-filter'

const RESPONSE_TIMEOUT = 50000

/**
 * Hook to manage IMT Dashboards data
 * @param {Object} useImtDashboardDataParams
 * @param {ICurrent} useImtDashboardDataParams.current
 * @param {IConsolidatedFilter} useImtDashboardDataParams.consolidatedFilter
 * @returns {{
 *   rows: Ref<Object>,
 *   warnings: Ref<String[]>
 *   isData: Computed<Boolean>
 *   isLoading: Ref<Boolean>
 *   isCached: Ref<Boolean>
 *   isDataCached: () => Object[]
 *   resetCache: () => void
 *   loadDashboardData: (baseUrl: String, params: Object) => Object[]
 *   setData: (data: Object[]) => void
 * }}
 * Object that contains properties and methods to manage IMT Dashboard data
 * */

export function useImtDashboardData({
  current: filter,
  consolidatedFilter = reactive({}),
  customCacheKey = computed(() => '')
}) {
  const { week, brand, site } = toRefs(filter)
  const { isConsolidatedView = ref(false), brands: consolidatedBrands = ref([]) } =
    toRefs(consolidatedFilter)
  const timeouts = ref({})
  const serverData = ref({})
  const warnings = ref([])
  const isLoading = ref(false)
  const isCached = ref(false)
  const requestParams = reactive({
    brand,
    dc: site,
    week
  })

  const dataKey = computed(() => {
    if (customCacheKey.value) return customCacheKey.value

    return isConsolidatedView?.value
      ? [week.value, ...consolidatedBrands.value].join('-')
      : Object.values(requestParams).join('-')
  })
  const rows = computed(() => serverData.value[dataKey.value] || [])
  const isData = computed(() => rows.value.length)
  const isRemakeData = computed(() => rows.value.ingredientDepletion)

  function isDataCached() {
    const cacheTimeout = 60000
    const previousTimestamp = timeouts.value[dataKey.value]
    const isCacheValid = previousTimestamp ? Date.now() - previousTimestamp < cacheTimeout : false
    const isValidData = isCacheValid && (isData.value || isRemakeData.value)

    if (isValidData) {
      // show chaching message for 500ms
      isCached.value = true
      isLoading.value = true
      setTimeout(() => {
        isCached.value = false
        isLoading.value = false
      }, 500)
    }

    return isValidData
  }

  function setData(data) {
    if (data) {
      set(serverData.value, dataKey.value, data)
      timeouts.value[dataKey.value] = Date.now()
    }
  }

  function resetCache() {
    timeouts.value = {}
  }

  async function loadDashboardData(baseUrl, params) {
    warnings.value = []

    if (isConsolidatedView.value) {
      return loadAllItems(baseUrl, params)
    }

    const dataWithinSite = await loadSingleItem(baseUrl, params)

    return dataWithinSite[site.value] || dataWithinSite
  }

  async function loadSingleItem(baseUrl, inputParams) {
    const params = inputParams || toRaw(requestParams) || {}
    const site = params.dc || params.site
    const emptyResponse = site
      ? {
          [site]: []
        }
      : []

    let response

    try {
      response = await axios.get(baseUrl, { params, timeout: RESPONSE_TIMEOUT })
    } catch (e) {}

    if (!response) return emptyResponse

    warnings.value = response.data.warnings

    return response.data.data || emptyResponse
  }

  async function loadAllItems(baseUrl, additionalParams = {}) {
    const responses = await Promise.all(
      consolidatedBrands.value.map((brand) => {
        const params = {
          week: additionalParams?.week || week.value,
          brand
        }

        return loadSingleItem(baseUrl, params)
      })
    )

    return responses
      .map((item, index) => {
        const brand = consolidatedBrands.value[index]

        return Array.isArray(item)
          ? item
          : Object.keys(item).reduce((acc, dc) => {
              // extend rows with "brand", "dc" fields;
              const itemWithBrandAndRegion = item[dc].map((row) => ({
                ...row,
                brand,
                dc
              }))

              return acc.concat(itemWithBrandAndRegion) // merge all DCs lists
            }, [])
      })
      .reduce((acc, cur) => acc.concat(cur), []) // merge all brands lists
  }

  return {
    rows,
    warnings,
    isData,
    isLoading,
    isCached,
    isDataCached,
    resetCache,
    loadDashboardData,
    setData
  }
}
