import Vue, { onUnmounted } from 'vue'

/**
 *@instance
 */
const bus = new Vue()

/**
 *
 * @typedef {Object} IEventBus
 * @property {(eventName: string, data: (Object|Array)) => void} busEmit - emits event to event bus
 * @property {(eventName: string, handler: Function) => void} busOn - executes function every time passed event fires
 * @property {(eventName: string, handler: Function) => void} busOff - clean up all subscriptions
 * @property {(eventName: string, handler: Function) => void} busOnce - executes function only once when passed event fires
 */

/**
 * Hook with methods to work with Event Bus
 * @returns {IEventBus} object with event bus methods
 */

export function useEventBus() {
  function busEmit(eventName, data) {
    bus.$emit(eventName, data)
  }

  function busOn(eventName, handler) {
    bus.$on(eventName, handler)
    onUnmounted(() => bus.$off(eventName, handler))
  }

  function busOff() {
    bus.$off()
  }

  function busOnce(eventName, handler) {
    bus.$once(eventName, handler)
  }

  return { busEmit, busOn, busOff, busOnce }
}
