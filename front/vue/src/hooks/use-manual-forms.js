import { ref, computed, Ref, ComputedRef as Computed } from 'vue'
import { clearRangeCells } from '@/modules/grid/helpers'

/**
 * Hook with methods and properties to work with manual forms
 * @param {Object} addRecordsDefaultItem - default item for current manual form
 * @param {Object} grid - grid object
 * @param {Number} emptyRowsCount - number of empty rows in add mode
 * @returns {{
 *   isAddMode: Ref<Boolean>,
 *   isValidating: Ref<Boolean>,
 *   records: Computed<Object[]>,
 *   isRecordsValidated: Ref<Boolean>,
 *   isValidationDialogOpen: Ref<Boolean>,
 *   addRecordsRows: Ref<Object[]>,
 *   errorObj: Object,
 *   turnOnAddMode: () => void,
 *   turnOffAddMode: () => void,
 *   handleErrors: (response: Object) => void,
 *   clearErrors: () => void,
 *   closeValidationDialog: () => void,
 *   onCellKeyDown: (params: Object) => void
 * }}
 * Object with properties and methods for manual forms
 * */

export function useManualForms(addRecordsDefaultItem, grid, emptyRowsCount = 150) {
  const isAddMode = ref(false)
  const isValidating = ref(false)
  const isRecordsValidated = ref(false)
  const isValidationDialogOpen = ref(false)
  const addRecordsRows = ref([])
  const errorObj = ref(null)

  const records = computed(() => {
    return addRecordsRows.value
      .map((row, index) => ({
        uiRowId: index,
        ...row,
        id: index,
        skuName: row.skuName?.label || row.skuName
      }))
      .filter((row) =>
        Object.keys(row).some(
          (key) => key !== 'uiRowId' && key !== 'id' && key !== 'error' && row[key]
        )
      )
  })

  function configureNewRecordsRows() {
    const emptyRecord = Object.assign({}, addRecordsDefaultItem)
    addRecordsRows.value = [...Array(emptyRowsCount)].map(() => ({ ...emptyRecord }))
  }

  function turnOnAddMode() {
    configureNewRecordsRows()
    isAddMode.value = true
  }

  function turnOffAddMode() {
    clearErrors()
    isAddMode.value = false
  }

  function closeValidationDialog() {
    isValidationDialogOpen.value = false
    isRecordsValidated.value = false
  }

  function handleErrors(response) {
    if (!response) return

    const { error = '', data = [] } = response
    errorObj.value = { msg: error, data }

    isValidationDialogOpen.value = true

    // highlight invalid rows
    const rowNodes = errorObj.value.data.map(({ row: rowIndex, cols }) => {
      const rowNode = grid.value.api.getDisplayedRowAtIndex(rowIndex)
      rowNode.data.error = cols

      return rowNode
    })

    grid.value.api.redrawRows({ rowNodes })
  }

  function clearErrors() {
    if (!errorObj.value) return

    const rowNodes = errorObj.value.data.map(({ row: rowIndex }) => {
      const rowNode = grid.value.api.getDisplayedRowAtIndex(rowIndex)
      rowNode.data.error = null

      return rowNode
    })

    errorObj.value = null
    grid.value.api.redrawRows({ rowNodes })
  }

  function onCellKeyDown(params) {
    const keyPress = params.event.keyCode

    if (keyPress === 8 || keyPress === 46) {
      clearRangeCells(params)
    }
  }

  return {
    isAddMode,
    isValidating,
    records,
    isRecordsValidated,
    isValidationDialogOpen,
    addRecordsRows,
    errorObj,
    turnOnAddMode,
    turnOffAddMode,
    handleErrors,
    clearErrors,
    closeValidationDialog,
    onCellKeyDown
  }
}
