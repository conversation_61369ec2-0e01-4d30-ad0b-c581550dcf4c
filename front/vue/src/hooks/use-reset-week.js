import { createNamespacedHelpers } from 'vuex-composition-helpers'

/**
 * Hook with reset week properties
 * @returns {{ changeWeekToOngoing: () => void, isOngoingWeek: boolean}} Object with flag
 * to check if current week is ongoing week and action to change current week to ongoing
 * */

export function useResetWeek() {
  const { useActions, useGetters } = createNamespacedHelpers('Filters')

  const { changeWeekToOngoing } = useActions(['changeWeekToOngoing'])
  const { week, ongoingWeek } = useGetters(['week', 'ongoingWeek'])

  const isOngoingWeek = week.value === ongoingWeek.value

  return { changeWeekToOngoing, isOngoingWeek }
}
