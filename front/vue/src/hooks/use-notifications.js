import { createNamespacedHelpers } from 'vuex-composition-helpers'

const { useGetters, useActions } = createNamespacedHelpers('Notifications')

/**
 * Represents a notification object.
 * @typedef {Object} INotification
 * @property {String} text - The text message of the notification.
 * @property {String} details - Additional details about the notification.
 * @property {boolean} [isError=false] - Indicates if the notification is an error. Defaults to false.
 * @property {boolean} [isPermanent=false] - Indicates if the notification should persist and not automatically disappear. Defaults to false.
 */

/**
 * Provides composition API functions for managing notifications in a Vuex store.
 * @returns {{
 *   notifications: Computed<INotification[]>,
 *   addNotification: (notification: INotification) => void,
 *   removeNotification: (notification: INotification) => void,
 * }}
 */
export function useNotifications() {
  const { notifications } = useGetters(['notifications'])

  const { addNotification, removeNotification } = useActions([
    'addNotification',
    'removeNotification'
  ])

  return { notifications, addNotification, removeNotification }
}
