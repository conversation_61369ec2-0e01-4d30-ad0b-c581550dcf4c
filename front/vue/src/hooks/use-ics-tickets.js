import { getTicketsPreview } from '@/api/ics-tickets'
import envConfig from '@/config/env.config'
import { inventoryResource } from '@/config/resource.config'
import { computed, reactive, ref, toRefs } from 'vue'

export function useIcsTickets({
  current,
  available,
  consolidatedFilter = reactive({}),
  additionalTicketsParams = computed(() => ({})),
  getPreviewForAllBrands = false,
  getPreviewByPresetId = false
}) {
  const { isConsolidatedView = ref(false), brands: consolidatedBrands = ref([]) } =
    toRefs(consolidatedFilter)

  async function loadBrandTickets(inputParams = {}) {
    const filters = {
      ...current,
      ...additionalTicketsParams.value,
      ...inputParams
    }
    const { brand, site, week } = filters
    const sites = (isConsolidatedView.value ? available.sitesByGroup[brand] : [site]).join()

    const params = {
      brand,
      sites,
      week
    }

    const data = await getTicketsPreview(params)

    return {
      [brand]: data
    }
  }

  async function loadPresetTickets(params) {
    const data = await getTicketsPreview(params, inventoryResource.networkDepletionIcsTickets)

    return data
  }

  async function loadAllBrandsTickets() {
    const responses = await Promise.all(
      (getPreviewForAllBrands ? available.brands : consolidatedBrands.value).map((brand) => {
        const params = {
          week: additionalTicketsParams.value?.week || current.week,
          brand
        }

        return loadBrandTickets(params)
      })
    )

    return responses.reduce((acc, cur) => ({ ...acc, ...cur }), {})
  }

  function loadTickets(inputParams = {}) {
    if (getPreviewByPresetId) return loadPresetTickets(inputParams)

    return getPreviewForAllBrands || isConsolidatedView.value
      ? loadAllBrandsTickets()
      : loadBrandTickets()
  }

  function mapRowsAndIcsTickets(rows = [], tickets) {
    return rows.map((row) => {
      return {
        ...row,
        icsTickets: setTicketsForRow(row, tickets)
      }
    })
  }

  function setTicketsForRow(row, tickets) {
    // Handle single or comma-separated values for brands and sites
    const brands = row.brand
      ? row.brand.includes(',')
        ? row.brand.split(',').map((b) => b.trim())
        : [row.brand]
      : [current.brand]
    const sites =
      row.dc || row.site
        ? (row.dc || row.site).includes(',')
          ? (row.dc || row.site).split(',').map((s) => s.trim())
          : [row.dc || row.site]
        : [current.site]

    const week = row.week || current.week
    const skuCode = row.sku
    const [orderNumber] = row.poNumber?.split('_') || []

    const isRowWithTickets = brands.some((brand) => {
      const brandTickets = tickets[brand]
      if (!brandTickets) return false

      const weekTickets = brandTickets[week]
      if (!weekTickets) return false

      return sites.some((site) => {
        const siteTickets = weekTickets[site]
        if (!siteTickets) return false

        return siteTickets.some((ticket) => {
          if (orderNumber && !ticket.skuCode) {
            return ticket.orderNumber === orderNumber
          }

          if (orderNumber && skuCode) {
            return ticket.orderNumber === orderNumber && ticket.skuCode === skuCode
          }

          if (skuCode) {
            return ticket.skuCode === skuCode
          }

          return ticket.orderNumber === orderNumber
        })
      })
    })

    return isRowWithTickets
  }

  return {
    loadTickets,
    mapRowsAndIcsTickets
  }
}
