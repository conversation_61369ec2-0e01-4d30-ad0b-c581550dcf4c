import Vue from 'vue'
import { LicenseManager } from 'ag-grid-enterprise'

import App from './App'
import router from '@/router'
import store from './store'

import vuetify from '@/plugins/vuetify'
import { initGTM } from '@/plugins/gtm'
import { initMasonry } from '@/plugins/masonry'
import { initTiptapVuetify } from '@/plugins/tiptap-vuetify'
import { initAxiosInterceptors } from '@/services/interceptors.service'
import { initRouterGuards } from '@/services/router-guards.service'
import { ErrorService } from '@/services/error.service'
import envConfig from '@/config/env.config'
import { isProd } from '@/utils'

Vue.config.productionTip = false
if (isProd) {
  ErrorService.initHandlers()
}
LicenseManager.setLicenseKey(envConfig.AG_GRID_KEY)
initAxiosInterceptors()
initRouterGuards()
if (envConfig.UI_METRICS_ENABLED) {
  initGTM()
}
initTiptapVuetify()
initMasonry()

new Vue({
  router,
  store,
  vuetify,
  render: (h) => h(App)
}).$mount('#app')
