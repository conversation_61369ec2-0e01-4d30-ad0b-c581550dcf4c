{"name": "nest-server", "version": "1.0.0", "description": "Serves static files from VueJS application and makes Websocket, Socket.IO, Redis work togather", "author": "<PERSON><PERSON><PERSON> <andri<PERSON>.<EMAIL>>", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"src/**/*.ts\" --fix"}, "dependencies": {"@nestjs/common": "~11.0.2", "@nestjs/config": "~4.0.0", "@nestjs/core": "~11.0.2", "@nestjs/platform-express": "~11.0.2", "@nestjs/platform-socket.io": "~11.0.2", "@nestjs/serve-static": "^5.0.2", "@nestjs/websockets": "~11.0.2", "@socket.io/redis-adapter": "^8.2.1", "axios": "^1.8.2", "compression": "^1.7.4", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.1.0", "shelljs": "^0.8.5"}, "devDependencies": {"@nestjs/cli": "^10.3.9", "@nestjs/schematics": "^10.1.2", "@types/express": "^4.17.13", "@types/node": "^18.1.0", "@typescript-eslint/eslint-plugin": "3.10.1", "@typescript-eslint/parser": "3.10.1", "eslint": "^7.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.24.2", "prettier": "^2.4.1", "ts-loader": "^6.2.2", "ts-node": "^8.10.2", "tsconfig-paths": "^3.11.0", "tslint": "5.20.1", "typescript": "^4.5.4"}}