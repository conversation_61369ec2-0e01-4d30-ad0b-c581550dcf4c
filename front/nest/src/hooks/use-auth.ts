import axios from 'axios'
import { appConfig } from '../app.config'

export const checkAuth = async (token) => {
  if (!token) return false

  const resourceMe = `${appConfig.apiUrl}/api/v2/me/`
  const options = {
    headers: { Authorization: `Bearer ${token}` }
  }

  try {
    const response = await axios.get(resourceMe, options)
    const {
      data: { data }
    } = response
    const appAccessPermission = 'general:r'

    return data.userPermissions.permissions.includes(appAccessPermission)
  } catch (e) {
    return false
  }
}
