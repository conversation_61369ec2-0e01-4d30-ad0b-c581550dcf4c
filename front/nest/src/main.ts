import * as compression from 'compression'
import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { connectToRedisAdapter } from './adapters/redis-io.adapter'
import { isProd } from './utils'
import { useIndexCache } from './middleware/index-cache'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  const port = parseInt(process.env.SERVER_PORT)

  app.use(compression())
  app.use(useIndexCache)

  await connectToRedisAdapter(app)

  if (!isProd) app.enableCors()

  await app.listen(port)
}

bootstrap()
