import { isProd } from './utils'

interface AppConfig {
  redisAdapterEnabled: boolean
  envConfig: EnvConfig
  assetsCacheTime: number
  apiUrl: string
}

interface EnvConfig {
  cache: CacheConfig
}

interface CacheConfig {
  host: string
  port: number
  password?: string
  db: number
}

const localRedisConfig: CacheConfig = {
  host: 'localhost',
  port: 6379,
  db: 1
}

const parseConfig = () => {
  try {
    return JSON.parse(process.env.CONFIG)
  } catch (e) {
    return {}
  }
}

const envConfig: EnvConfig = isProd
  ? parseConfig()
  : {
      cache: localRedisConfig
    }

const getApiUrl = () => {
  const configApiUrl = process.env.VUE_APP_API_URL
  const fallbackApiUrl = 'http://localhost:8000'

  return configApiUrl || fallbackApiUrl
}

export const appConfig: AppConfig = {
  redisAdapterEnabled: true,
  envConfig,
  apiUrl: getApiUrl(),
  assetsCacheTime: 60 * 60 * 1000 * 24 * 7 // 7 days
}
