package com.hellofresh.skudemandforecast.fileconsumer.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.filesconsumer.schema.Tables.FILE_UPLOADS
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus as FileStatusDb
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus.CONSUMED
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus.PENDING
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileType.DEMAND
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileType.RECIPE
import com.hellofresh.skudemandforecast.filesconsumer.schema.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.model.fileupload.FileSource
import com.hellofresh.skudemandforecast.model.fileupload.FileStatus
import com.hellofresh.skudemandforecast.model.fileupload.FileType
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import java.time.OffsetDateTime
import java.util.UUID

object FileUploadsRepository {

    fun MetricsDSLContext.fetchPendingFiles(fileBatchSize: Int, seekFile: SeekFile? = null): List<FileUpload> =
        withTagName("fetch-pending-files-seek")
            .selectFrom(FILE_UPLOADS)
            .where(FILE_UPLOADS.UPLOADED_FILE_STATUS.eq(PENDING))
            .orderBy(FILE_UPLOADS.CREATED_AT.asc(), FILE_UPLOADS.ID)
            .apply {
                seekFile?.also { seek(seekFile.createdAt, seekFile.id) }
            }
            .limit(fileBatchSize)
            .fetch { record -> record.toFileUpload() }

    fun FileUploadsRecord.toFileUpload() =
        FileUpload(
            id = this.id,
            dcs = this.dcs.toSet(),
            weeks = this.weeks.toSet(),
            fileName = this.fileName,
            content = this.content,
            info = this.info?.toList() ?: emptyList(),
            errors = this.errors?.toList() ?: emptyList(),
            fileSource = FileSource.valueOf(this.uploadedFileSource.name),
            fileType = FileType.valueOf(this.uploadedFileType.name),
            fileStatus = FileStatus.valueOf(this.uploadedFileStatus.name),
            authorName = this.authorName,
            authorEmail = this.authorEmail,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            market = this.market,
        )

    fun MetricsDSLContext.updateFilesToConsumed(fileUploads: List<FileUpload>) {
        withTagName("update-file-status-consumed")
            .update(FILE_UPLOADS)
            .set(FILE_UPLOADS.UPLOADED_FILE_STATUS, CONSUMED)
            .where(
                FILE_UPLOADS.ID.`in`(fileUploads.map { file -> file.id })
                    .let { condition ->
                        fileUploads.filter { it.fileType == FileType.DEMAND }.maxOfOrNull { it.createdAt }
                            ?.let { maxDemandCreatedAt ->
                                condition.or(
                                    FILE_UPLOADS.UPLOADED_FILE_TYPE.eq(DEMAND)
                                        .and(FILE_UPLOADS.UPLOADED_FILE_STATUS.eq(PENDING))
                                        .and(FILE_UPLOADS.CREATED_AT.le(maxDemandCreatedAt)),
                                )
                            } ?: condition
                    }.let { condition ->
                        fileUploads.filter { it.fileType == FileType.RECIPE }.maxOfOrNull { it.createdAt }
                            ?.let { maxRecipeCreatedAt ->
                                condition.or(
                                    FILE_UPLOADS.UPLOADED_FILE_TYPE.eq(RECIPE)
                                        .and(FILE_UPLOADS.UPLOADED_FILE_STATUS.eq(PENDING))
                                        .and(FILE_UPLOADS.CREATED_AT.le(maxRecipeCreatedAt)),
                                )
                            } ?: condition
                    },
            ).execute()
    }

    fun MetricsDSLContext.updateFileStatus(fileUploads: List<FileUpload>, fileStatus: FileStatusDb) {
        withTagName("update-file-status")
            .update(FILE_UPLOADS)
            .set(FILE_UPLOADS.UPLOADED_FILE_STATUS, fileStatus)
            .where(
                FILE_UPLOADS.ID.`in`(fileUploads.map { file -> file.id }),
            ).execute()
    }
}

data class SeekFile(val id: UUID, val createdAt: OffsetDateTime)
