package com.hellofresh.skudemandforecast.fileconsumer.repository

import com.hellofresh.skudemandforecast.fileconsumer.FunctionalTest
import com.hellofresh.skudemandforecast.model.fileupload.FileType
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.default
import com.hellofresh.skudemandforecast.model.fileupload.reader.CSVFileUploadReader.toCsvRecords
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class FileConsumerRepositoryTest : FunctionalTest() {

    @Test
    fun `data from latest file for dc and week is selected`() {
        val now = OffsetDateTime.now(UTC)
        val dcCode1 = "T1"
        val dcCode2 = "T2"
        val dcCode3 = "T3"
        val week30 = "2023-W30"
        val week31 = "2023-W31"
        val demandFile = DemandFile(
            fileUpload = FileUpload.default().copy(
                id = UUID.randomUUID(),
                createdAt = now,
            ),
            toCsvRecords(
                demandCsv(
                    dcCode1,
                    week30,
                    10,
                    listOf("$week31;Sunday;DE;$dcCode2;DE;mutual-menu;mutual-menu;6;2;21;11"),
                ).toByteArray(),
                FileType.DEMAND
            ).parsedFile as Demand,
        )
        val olderDemandFile = DemandFile(
            FileUpload.default().copy(
                id = UUID.randomUUID(),
                createdAt = now,
            ),
            toCsvRecords(
                demandCsv(
                    dcCode1,
                    week30,
                    100,
                    listOf("$week31;Sunday;DE;$dcCode3;DE;mutual-menu;mutual-menu;6;2;21;100"),
                ).toByteArray(),
                FileType.DEMAND
            ).parsedFile as Demand,
        )

        val calculateLatestDemand = FileConsumerRepository.calculateLatestDemand(listOf(demandFile, olderDemandFile))

        assertEquals(3, calculateLatestDemand.size)
        calculateLatestDemand.first { it.dc == dcCode1 && it.week == week30 }
            .also {
                assertEquals(demandFile, it.demandFile)
                assertEquals(10, it.rows.single()[Demand.MEALS_TO_DELIVER_HEADER].toInt())
            }
        calculateLatestDemand.first { it.dc == dcCode2 && it.week == week31 }
            .also {
                assertEquals(demandFile, it.demandFile)
                assertEquals(11, it.rows.single()[Demand.MEALS_TO_DELIVER_HEADER].toInt())
            }
        calculateLatestDemand.first { it.dc == dcCode3 && it.week == week31 }
            .also {
                assertEquals(olderDemandFile, it.demandFile)
                assertEquals(100, it.rows.single()[Demand.MEALS_TO_DELIVER_HEADER].toInt())
            }
    }
}
