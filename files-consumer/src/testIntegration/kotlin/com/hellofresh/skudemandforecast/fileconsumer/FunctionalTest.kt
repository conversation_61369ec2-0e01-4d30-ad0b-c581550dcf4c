@file:Suppress("StringLiteralDuplication")

package com.hellofresh.skudemandforecast.fileconsumer

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.Tables.DC_CONFIG
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.fileconsumer.repository.NULL_LOCALE_VALUE
import com.hellofresh.skudemandforecast.filesconsumer.schema.Tables
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileSource
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileType
import com.hellofresh.skudemandforecast.filesconsumer.schema.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.filesconsumer.schema.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.filesconsumer.schema.tables.records.RecipeRecord
import com.hellofresh.skudemandforecast.lib.substitution.schema.public_.Tables.SKU_SUBSTITUTION
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Recipe
import com.hellofresh.skudemandforecast.model.fileupload.Row
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.Tables.SKU_SPECIFICATION
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.tables.records.SkuSpecificationRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeAll

internal const val FILE_BATCH_SIZE = 10

open class FunctionalTest {

    @Suppress("LongParameterList")
    fun demandCsv(
        dcCode: String = "XX",
        week: String = "2023-W45",
        mealsToDeliver: Int = 10,
        extraLines: List<String> = emptyList(),
    ) = demandCsv(
        dcCode = dcCode,
        week = week,
        mealsToDeliver = "$mealsToDeliver",
        extraLines = extraLines,
    )

    @Suppress("LongParameterList")
    fun demandCsv(
        dcCode: String = "XX",
        week: String = "2023-W45",
        mealsToDeliver: String = "10",
        mealNumber: String = "21",
        size: String = "2",
        extraLines: List<String> = emptyList(),
        locale: String? = "DE",
        boxName: String? = "mutual-menu"
    ) = """
week;day;country;dc;locale;box_name;product_family;servings;size;meal_number;meals_to_deliver
$week;Sunday;DE;$dcCode;${locale ?: ""};${boxName ?: ""};mutual-menu;6;$size;$mealNumber;$mealsToDeliver
${extraLines.joinToString(separator = "\n")}
    """.trimIndent()

    @Suppress("MaxLineLength", "LongParameterList")
    fun recipeCsv(
        dcCode: String = "XX",
        week: String = "2023-W45",
        skusMappingPicks1: Long = 3,
        recipeIndex: String = "3",
        locale: String? = "GB",
        extraLines: List<String> = emptyList(),
    ) = """
week.value;iso2Code.value;locale;family;index;name;skus.mapping.sku.value;skus.mapping.name;skus.mapping.picks.1;skus.mapping.picks.2;skus.mapping.picks.3;skus.mapping.picks.4;skus.mapping.picks.5;skus.mapping.picks.6
$week;$dcCode;${locale ?: ""};classic-box;$recipeIndex;Pronto Bacon Linguine;PTN-10-27697-4;aSkuName;$skusMappingPicks1;1;0;2;0;0
${extraLines.joinToString(separator = "\n")}
    """.trimIndent()

    @Suppress("MaxLineLength", "LongParameterList")
    fun substitutionCsv(
        reference: String = "REF",
        dcCode: String = "XX",
        date: LocalDate,
        skuOutCode: String,
        skuOutQty: Int,
        skuInCode: String,
        skuInQty: Int,
        extraLines: List<String> = emptyList(),
    ) = """
        Reference;DC;Date;SKU Code subbed out;QTY subbed out;SKU Code subbed in;QTY subbed in
        $reference;$dcCode;$date;$skuOutCode;$skuOutQty;$skuInCode;$skuInQty
        ${extraLines.joinToString(separator = "\n")}
    """.trimIndent()

    fun saveUpload(fileUpload: FileUpload) {
        dsl.batchInsert(
            FileUploadsRecord().apply {
                this.id = fileUpload.id
                this.dcs = fileUpload.dcs.toTypedArray()
                this.market = fileUpload.market
                this.weeks = fileUpload.weeks.toTypedArray()
                this.fileName = fileUpload.fileName
                this.content = fileUpload.content
                this.errors = fileUpload.info.toTypedArray()
                this.errors = fileUpload.errors.toTypedArray()
                this.uploadedFileSource = FileSource.valueOf(fileUpload.fileSource.name)
                this.uploadedFileType = FileType.valueOf(fileUpload.fileType.name)
                this.uploadedFileStatus = FileStatus.valueOf(fileUpload.fileStatus.name)
                this.authorName = fileUpload.authorName
                this.authorEmail = fileUpload.authorEmail
                this.createdAt = fileUpload.createdAt
            },
        )
            .execute()
    }

    fun persistSkuSpecificationRecord(
        skuIdAndCodes: List<Pair<UUID, String>>,
        market: String
    ): List<SkuSpecificationRecord> =
        skuIdAndCodes
            .map { (id, code) -> SkuSpecificationRecord(id, null, code, LocalDateTime.now(), null, market, null, "") }
            .toMutableList()
            .also { records ->
                dsl.batchInsert(records).execute()
            }

    fun persistDistributionCenter(code: String, market: String) =
        DcConfigRecord()
            .apply {
                dcCode = code
                productionStart = MONDAY.name
                this.market = market
                zoneId = ZoneOffset.UTC.id
                enabled = true
                this.globalDc = globalDc
            }.also {
                dsl.batchInsert(it).execute()
            }

    fun assertRecipe(sourceId: UUID, row: Row, recipeRecord: RecipeRecord, dirtyBit: Boolean) {
        Assertions.assertEquals(sourceId, recipeRecord.sourceId)
        Assertions.assertEquals(dirtyBit, recipeRecord.dirtyBit)
        Assertions.assertEquals(row[Recipe.INDEX_HEADER].toDouble().toInt(), recipeRecord.recipeIndex)
        Assertions.assertEquals(row[Recipe.WEEK_HEADER], recipeRecord.week)
        Assertions.assertEquals(row[Recipe.FAMILY_HEADER], recipeRecord.productFamily)
        Assertions.assertEquals(row.optional[Recipe.LOCALE_HEADER] ?: NULL_LOCALE_VALUE, recipeRecord.locale)
        Assertions.assertEquals(row[Recipe.COUNTRY_HEADER], recipeRecord.country)
        with(objectMapper.readValue<RecipeValue>(recipeRecord.value.data()).skus.first()) {
            Assertions.assertEquals(row[Recipe.SKU_HEADER], this.skuCode)
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS1_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 1 }?.picks ?: 0,
            )
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS2_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 2 }?.picks ?: 0,
            )
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS3_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 3 }?.picks ?: 0,
            )
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS4_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 4 }?.picks ?: 0,
            )
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS5_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 5 }?.picks ?: 0,
            )
            Assertions.assertEquals(
                row[Recipe.SKU_PICKS6_HEADER].toInt(),
                this.picks.firstOrNull { it.peopleCount == 6 }?.picks ?: 0,
            )
        }
    }

    fun assertDemand(
        sourceId: UUID,
        row: Row,
        demandRecord: DemandRecord,
        dirtyBit: Boolean = true,
        mealsToDeliver: Int? = null
    ) {
        Assertions.assertEquals(dirtyBit, demandRecord.dirtyBit)
        Assertions.assertEquals(row[Demand.MEAL_NUMBER_HEADER].toDouble().toInt(), demandRecord.recipeIndex)
        Assertions.assertEquals(row[Demand.PRODUCT_FAMILY_HEADER], demandRecord.productFamily)
        Assertions.assertEquals(row.optional[Demand.LOCALE_HEADER] ?: NULL_LOCALE_VALUE, demandRecord.locale)
        Assertions.assertEquals(row[Demand.COUNTRY_HEADER], demandRecord.country)
        Assertions.assertEquals(row[Demand.DAY_HEADER], demandRecord.day)
        Assertions.assertEquals(row[Demand.WEEK_HEADER], demandRecord.week)
        Assertions.assertEquals(row[Demand.DC_HEADER], demandRecord.dcCode)
        Assertions.assertEquals(row[Demand.SIZE_HEADER].toDouble().toInt(), demandRecord.peopleCount)
        Assertions.assertEquals(
            mealsToDeliver ?: row[Demand.MEALS_TO_DELIVER_HEADER].toDouble().toInt(),
            demandRecord.mealsToDeliver,
        )
        Assertions.assertEquals(sourceId, demandRecord.sourceId)
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.RECIPE).execute()
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(SKU_SUBSTITUTION).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
    }

    companion object {

        internal val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
        }
    }
}
