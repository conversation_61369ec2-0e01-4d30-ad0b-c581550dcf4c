package com.hellofresh.skudemandforecast.fileconsumer.repository

import com.hellofresh.skudemandforecast.fileconsumer.FILE_BATCH_SIZE
import com.hellofresh.skudemandforecast.fileconsumer.FunctionalTest
import com.hellofresh.skudemandforecast.fileconsumer.repository.FileUploadsRepository.fetchPendingFiles
import com.hellofresh.skudemandforecast.fileconsumer.repository.FileUploadsRepository.toFileUpload
import com.hellofresh.skudemandforecast.fileconsumer.repository.FileUploadsRepository.updateFilesToConsumed
import com.hellofresh.skudemandforecast.filesconsumer.schema.Tables
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus.CONSUMED
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileStatus.PENDING
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileType.DEMAND
import com.hellofresh.skudemandforecast.filesconsumer.schema.enums.FileType.RECIPE
import com.hellofresh.skudemandforecast.filesconsumer.schema.tables.records.FileUploadsRecord
import com.hellofresh.skudemandforecast.model.fileupload.FileUpload
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import randomFileUploadsRecord

class FileUploadsRepositoryTest : FunctionalTest() {

    @Test
    fun `returns files with PENDING status`() {
        // when

        val fileUploadRecords = FileStatus.values().map {
            randomFileUploadsRecord().apply { uploadedFileStatus = it }
        }

        dsl.batchInsert(fileUploadRecords).execute()

        val pendingFiles = dsl.fetchPendingFiles(FILE_BATCH_SIZE)

        assertEquals(1, pendingFiles.size)
        assertFile(fileUploadRecords.first { it.uploadedFileStatus == PENDING }, pendingFiles.first()) {
            assertEquals(com.hellofresh.skudemandforecast.model.fileupload.FileStatus.PENDING, it.fileStatus)
        }
    }

    @Test
    fun `updates status to CONSUMED for given files and existing older PENDING files`() {
        // when

        val alreadyProcessedFile = randomFileUploadsRecord().apply {
            uploadedFileStatus = CONSUMED
            uploadedFileType = RECIPE
        }
        val olderPendingFile1 = randomFileUploadsRecord().apply {
            uploadedFileStatus = PENDING
            uploadedFileType = DEMAND
        }
        val olderPendingFile2 = randomFileUploadsRecord().apply { uploadedFileStatus = PENDING }

        val currentFile1 = randomFileUploadsRecord().apply {
            uploadedFileStatus = PENDING
            uploadedFileType = RECIPE
        }
        val currentFile2 = randomFileUploadsRecord().apply {
            uploadedFileStatus = PENDING
            uploadedFileType = DEMAND
        }

        val expectedRecords = listOf(
            alreadyProcessedFile,
            olderPendingFile1,
            olderPendingFile2,
            currentFile1,
            currentFile2,
        )

        dsl.batchInsert(expectedRecords).execute()

        dsl.updateFilesToConsumed(listOf(currentFile1.toFileUpload(), currentFile2.toFileUpload()))

        val fileUploadRecords = dsl.selectFrom(Tables.FILE_UPLOADS).fetch()

        expectedRecords.forEach { expectedRecord ->
            fileUploadRecords.first { it.id == expectedRecord.id }.also {
                assertEquals(CONSUMED, it.uploadedFileStatus)
            }
        }
    }

    @Test
    fun `returns files in batches with PENDING status`() {
        val now = OffsetDateTime.now(UTC)
        val fileUploadRecords = (1..FILE_BATCH_SIZE * 2L).map {
            randomFileUploadsRecord().apply {
                uploadedFileStatus = PENDING
                createdAt = now.plusMinutes(100).minusMinutes(it)
            }
        }

        dsl.batchInsert(fileUploadRecords).execute()
        val expectedFilesOrder = fileUploadRecords.sortedWith(
            compareBy<FileUploadsRecord> {
                it.createdAt
            }.thenComparing(compareBy { it.id })
        )

        val pendingFiles = dsl.fetchPendingFiles(FILE_BATCH_SIZE)

        assertEquals(FILE_BATCH_SIZE, pendingFiles.size)
        assertTrue(
            pendingFiles.all { it.fileStatus == com.hellofresh.skudemandforecast.model.fileupload.FileStatus.PENDING }
        )
        assertEquals(expectedFilesOrder.first().id, pendingFiles.first().id)

        val pendingFiles2 = dsl.fetchPendingFiles(
            FILE_BATCH_SIZE,
            pendingFiles.last().let { SeekFile(it.id, it.createdAt) }
        )

        assertEquals(FILE_BATCH_SIZE, pendingFiles2.size)
        assertTrue(
            pendingFiles2.all { it.fileStatus == com.hellofresh.skudemandforecast.model.fileupload.FileStatus.PENDING }
        )
        assertEquals(expectedFilesOrder.last().id, pendingFiles2.last().id)

        assertTrue(
            dsl.fetchPendingFiles(FILE_BATCH_SIZE, pendingFiles2.last().let { SeekFile(it.id, it.createdAt) }).isEmpty()
        )
    }

    private fun assertFile(
        fileUploadsRecord: FileUploadsRecord,
        fileUpload: FileUpload,
        assertBlock: (FileUpload) -> Unit
    ) {
        assertEquals(fileUpload.dcs, fileUploadsRecord.dcs.toSet())
        assertEquals(fileUpload.weeks, fileUploadsRecord.weeks.toSet())
        assertEquals(fileUpload.fileName, fileUploadsRecord.fileName)
        assertEquals(String(fileUpload.content), String(fileUploadsRecord.content))
        assertEquals(fileUpload.fileSource.name, fileUploadsRecord.uploadedFileSource.name)
        assertEquals(fileUpload.fileType.name, fileUploadsRecord.uploadedFileType.name)
        assertEquals(fileUpload.fileStatus.name, fileUploadsRecord.uploadedFileStatus.name)
        assertEquals(fileUpload.authorName, fileUploadsRecord.authorName)
        assertEquals(fileUpload.authorEmail, fileUploadsRecord.authorEmail)
        assertEquals(
            fileUpload.createdAt.truncatedTo(ChronoUnit.SECONDS).atZoneSameInstant(UTC),
            fileUploadsRecord.createdAt.truncatedTo(ChronoUnit.SECONDS).atZoneSameInstant(UTC),
        )

        assertBlock(fileUpload)
    }
}
