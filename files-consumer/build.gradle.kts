plugins {
    id("com.hellofresh.sdf.application-conventions")
    hellofresh.`test-integration`
    hellofresh.`test-fixtures`
    `test-functional`
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

group = "$group.${project.name}".replace("-", "")
description = "Read upload files and persist demand and recipe data"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib:models"))
    implementation(project(":lib:models:fileupload"))
    implementation(project(":lib:job"))
    implementation(project(":lib"))
    implementation(project(":lib:metrics"))
    implementation(project(":lib:db"))
    implementation(project(":distribution-center-lib"))
    implementation(project(":substitution-lib"))
    implementation(project(":sku-specification-lib"))
    implementation(libs.hellofresh.service)
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.jdk8)
    implementation(libs.jooq.coroutine)

    testFixturesImplementation(libs.jooq.core)

    testIntegrationImplementation(project(":lib:models"))
    testIntegrationImplementation(project(":lib:job"))
    testIntegrationImplementation(project(":lib:metrics"))
    testIntegrationImplementation(project(":lib:db"))
    testIntegrationImplementation(project(":substitution-lib"))
    testIntegrationImplementation(testFixtures(project(":files-consumer")))
    testIntegrationImplementation(testFixtures(project(":lib")))
    testIntegrationImplementation(testFixtures(project(":lib:models:fileupload")))
    testIntegrationImplementation(testFixtures(project(":lib:models:distribution-center")))
    testIntegrationImplementation(libs.jackson.kotlin)
    testIntegrationImplementation(libs.jackson.jsr310)
    testIntegrationImplementation(libs.awaitility)
    testIntegrationImplementation(libs.mockk)
    testIntegrationImplementation(libs.coroutines.core)
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }

                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "file_uploads|file_status|file_source|file_type|recipe|demand"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
