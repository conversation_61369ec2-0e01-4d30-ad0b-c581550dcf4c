package com.hellofresh.skudemandforecast.distributionCenter

import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepository
import com.hellofresh.skudemandforecast.lib.job.KrontabScheduler
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.distributioncenter.default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import java.time.Duration
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit.MINUTES
import kotlin.test.Test
import org.awaitility.Awaitility
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows

class DcConfigServiceTest {

    private val repo = mockk<DcRepository>(relaxed = true)
    private val scheduler = mockk<KrontabScheduler>(relaxed = true)

    @BeforeEach
    fun clear() {
        unmockkAll()
    }

    @Test fun `repositoy returns error`() {
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
        )
        coEvery { repo.fetchDcConfigurations() }.throws(IllegalStateException("Invalid ZoneID"))
        assertThrows<IllegalStateException> { dcConfig.dcConfigurations }
    }

    @Test fun `fetch on demand is truly on demand`() {
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = KrontabScheduler(1, MINUTES, Executors.newSingleThreadExecutor()),
        )

        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenter.default())
        dcConfig.dcConfigurations
        dcConfig.fetchOnDemand()
        dcConfig.dcConfigurations
        coVerify(exactly = 2) { repo.fetchDcConfigurations() }
    }

    @Test fun `scheduler repo update is executed`() {
        val dcConfigService = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            jobTimePeriodSeconds = 2,
        )
        val expectedLastUpdateDcCode = "EXPECTED_LAST_DC"

        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenter.default()) andThen listOf(
            DistributionCenter.default().copy(dcCode = "EXPECTED_LAST_DC"),
        )
        Awaitility
            .await()
            .pollInterval(Duration.ofSeconds(1))
            .atMost(Duration.ofSeconds(5))
            .until {
                dcConfigService.dcConfigurations[expectedLastUpdateDcCode] != null
            }
    }

    @Test fun `multiple get calls on dcConfigurations trigger demand retrieval only once`() {
        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenter.default())
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = scheduler,
        )

        dcConfig.dcConfigurations
        dcConfig.dcConfigurations
        dcConfig.dcConfigurations

        coVerify(exactly = 1) { repo.fetchDcConfigurations() }
    }
}
