package com.hellofresh.sdf.distributioncenter

import InfraPreparation.getMigratedDataSource
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.Tables
import com.hellofresh.skudemandforecast.distribution_center_lib.schema.public_.tables.records.DcConfigRecord
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.unmockkAll
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach

class DcConfigServiceFunctionalTest {

    @Test fun smokeTest() {
        val expected = (0..3).map {
            DcConfigRecord().apply {
                dcCode = UUID.randomUUID().toString()
                productionStart = FRIDAY.name
                market = UUID.randomUUID().toString()
                zoneId = ZoneOffset.UTC.id
                globalDc = UUID.randomUUID().toString()
                enabled = true
                createdAt = LocalDateTime.now(ZoneOffset.UTC)
                updatedAt = LocalDateTime.now(ZoneOffset.UTC)
            }
        }
        dsl.batchInsert(expected).execute()
        assertEquals(
            expected.associateBy { it.dcCode }
                .mapValues {
                    with(it.value) {
                        DistributionCenter(
                            dcCode,
                            DayOfWeek.valueOf(productionStart),
                            market,
                            ZoneId.of(zoneId),
                            globalDc,
                            enabled,
                        )
                    }
                },
            DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
                .dcConfigurations,
        )
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
        }

        @BeforeEach
        fun clear() {
            dsl.deleteFrom(Tables.DC_CONFIG).execute()
            unmockkAll()
        }
    }
}
