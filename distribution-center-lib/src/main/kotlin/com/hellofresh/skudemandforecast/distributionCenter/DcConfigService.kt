package com.hellofresh.skudemandforecast.distributionCenter

import com.hellofresh.sdf.shutdown.shutdownNeeded
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepository
import com.hellofresh.skudemandforecast.lib.job.KrontabScheduler
import com.hellofresh.skudemandforecast.lib.job.MeteredJob
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.TimeUnit.SECONDS
import kotlinx.coroutines.runBlocking

private const val POOL_SIZE = 1

/**
 * Reads `DC_CONFIG` table in a background thread and caches the result. By
 * default, it uses the [KrontabScheduler] to schedule the read from  the
 * [DcRepositoryImpl]. However, this could be overwritten by the caller.
 *
 * **The class is threadsafe.**
 */

class DcConfigService(
    private val meterRegistry: MeterRegistry,
    private val repo: DcRepository,
    jobTimePeriodSeconds: Int = 60,
    private val scheduler: KrontabScheduler = shutdownNeeded {
        KrontabScheduler(
            jobTimePeriodSeconds,
            SECONDS,
            ThreadPoolExecutor(
                POOL_SIZE,
                POOL_SIZE,
                Long.MAX_VALUE,
                HOURS,
                ArrayBlockingQueue(POOL_SIZE, true),
                CallerRunsPolicy(),
            ),
        )
    }
) {

    /**
     * Assignment and read operations are atomic hence volatile is enough to make
     * the critical section threadsafe
     */
    @Volatile
    private var _dcConfigurations: Map<String, DistributionCenter> = emptyMap()

    init {
        scheduler.schedule {
            MeteredJob(meterRegistry, "dcConfigService") { _dcConfigurations = loadDcConfigurations() }.execute()
        }
    }

    /**
     * Mapping of DC Configurations. Note that this mapping is
     * refreshed by a background task periodically. Use [fetchOnDemand] if stale
     * data is not favourable.
     */
    val dcConfigurations: Map<String, DistributionCenter>
        get() = readDcConfigurations()

    /**
     * Fetch the mapping of DC code to Timezone from the source. This performs
     * a DB query. Use [dcTimezones] if cached data is enough for the use.
     */
    fun fetchOnDemand(): Map<String, DistributionCenter> = loadDcConfigurations()

    /**
     * Reads dc configurations if present and fetch them from db if needed
     */
    private fun readDcConfigurations() =
        _dcConfigurations.ifEmpty {
            loadDcConfigurations()
        }

    private fun loadDcConfigurations() = runBlocking {
        repo.fetchDcConfigurations()
            .associateBy { it.dcCode }
            .also {
                _dcConfigurations = it
            }
    }
}
