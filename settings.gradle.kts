@file:Suppress("UnstableApiUsage")

import java.net.URI


plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.8.0"
}

dependencyResolutionManagement {
    repositories {
        mavenCentral()
        maven("https://packages.confluent.io/maven/")
        maven("https://artifactory.tools-k8s.hellofresh.io/artifactory/maven-local/")
    }
}

buildCache {
    buildCache {
        local {
            isEnabled = true
        }

        remote(HttpBuildCache::class.java) {
            isEnabled = true
            url = URI("https://artifactory.tools-k8s.hellofresh.io/artifactory/gradle-build-caches/sku-demand-forecast/")
            credentials {
                username = System.getenv("ARTIFACTORY_USERNAME")
                password = System.getenv("ARTIFACTORY_PASSWORD")
            }
            // Only push to the remote cache when running in CI
            isPush = System.getenv("CI") == "true"
            println("Build cache push is enabled: $isPush")
        }
    }
}

include(
    "api-service",
    "demand-anz-consumer",
    "demand-calculator",
    "demand-import-job",
    "demand-matcher-job",
    "sku-specification-lib",
    "substitution-lib",
    "distribution-center-lib",
    "distribution-center-service",
    "files-consumer",
    "forecaster-job",
    "functional-tests",
    "kafka-db-sink",
    "lib",
    "lib:authserviceclient",
    "lib:checks",
    "lib:shutdown",
    "lib:db",
    "lib:featureflags",
    "lib:job",
    "lib:kafka",
    "lib:logging",
    "lib:metrics",
    "lib:models",
    "lib:models:date-util",
    "lib:models:fileupload",
    "lib:models:sku-specification",
    "lib:models:substitution",
    "lib:models:distribution-center",
    "lib:s3",
    "lib:slack",
    "matcher-service",
    "mps-forecaster-job",
    "recipe-anz-consumer",
    "recipe-import-job",
    "recipe-lib",
    "cross-docking-lib",
    "sku-demand-forecast-db",
    "tools:kafka-mirror-plugin",
    "tools:scripts",
    "end-to-end-test",
)
