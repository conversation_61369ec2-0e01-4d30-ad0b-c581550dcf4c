@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    hellofresh.`test-integration`
    alias(libs.plugins.jooq)
}

group = "$group.demandImportJob"
description = "Imports recipe demand forecast"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(project(":lib"))
    implementation(project(":lib:job"))
    implementation(project(":lib:db"))
    implementation(project(":lib:s3"))
    implementation(project(":lib:db"))
    implementation(project(":lib:kafka"))
    implementation(project(":lib:models:fileupload"))
    implementation(project(":lib:models:date-util"))
    implementation(project(":lib:slack"))
    implementation(project(":distribution-center-lib"))
    implementation(project(":lib:authserviceclient"))
    implementation(project(":recipe-lib"))
    implementation(project(":lib:featureflags"))

    implementation(libs.jackson.jsr310)
    implementation(libs.aws.sdk.sqs)
    implementation(libs.aws.sdk.sts)
    implementation(libs.coroutines.core)
    implementation(libs.hellofresh.schemaregistry)
    implementation(libs.hellofresh.service)
    implementation(libs.retrofit.jackson)
    implementation(libs.retrofit.core)
    implementation(libs.resilience4j.retrofit)

    testImplementation(libs.mockk)
    testImplementation(testFixtures(project(":lib")))
    testImplementation(testFixtures(project(":lib:kafka")))
    testImplementation(testFixtures(project(":lib:models:fileupload")))
    testFunctionalImplementation(testFixtures(project(":lib:featureflags")))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "demand|file_uploads|file_type|file_source|file_status|imports"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
