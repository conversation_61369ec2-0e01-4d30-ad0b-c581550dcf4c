import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ImportsRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should fetch only SCO type imported files`() {
        val importsRecord = importsRecord()
            .apply {
                this.source = FileSource.LOCAL_D4
                this.createdAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        val importRecords2 = importsRecord()
            .apply {
                this.source = FileSource.SCO
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        val importRecords3 = importsRecord()
            .apply {
                this.source = FileSource.MPS
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        val importRecords4 = importsRecord()
            .apply {
                this.source = FileSource.INVENTORY
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        runBlocking {
            dsl.batchInsert(importsRecord, importRecords2, importRecords3, importRecords4).execute()

            val files = importRepositoryImpl.fetchScoImportDetails()

            assertEquals(1, files.size)

            with(files.first { it.id == importRecords2.id }) {
                assertEquals(FileSource.SCO.name, source.name)
            }
        }
    }

    @Test
    fun `should return empty list there is no data at all`() {
        runBlocking {
            val files = importRepositoryImpl.fetchScoImportDetails()
            assertEquals(0, files.size)
        }
    }

    @Test
    fun `should return empty list when there are no SCO type imported files`() {
        val importsRecord = importsRecord()
            .apply {
                this.source = FileSource.MPS
                this.createdAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        val importRecords2 = importsRecord()
            .apply {
                this.source = FileSource.INVENTORY
                this.updatedAt = OffsetDateTime.now(ZoneOffset.UTC)
            }

        runBlocking {
            dsl.batchInsert(importsRecord, importRecords2).execute()

            val files = importRepositoryImpl.fetchScoImportDetails()

            assertEquals(0, files.size)
        }
    }
}
