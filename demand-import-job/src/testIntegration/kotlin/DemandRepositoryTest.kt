import InfraPreparation.getMigratedDataSource
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.demandImportJob.repository.DemandRepositoryImpl
import com.hellofresh.skudemandforecast.demandImportJob.repository.toDemandRecord
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import com.hellofresh.skudemandforecast.model.fileupload.generateRandom
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import junit.framework.TestCase.assertNull
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll

class DemandRepositoryTest {
    @BeforeTest
    fun cleanup() {
        dsl.deleteFrom(DEMAND).execute()
    }

    @Test fun `demand is inserted`() {
        val input = Demand.generateRandom(10)
        val sourceId = UUID.randomUUID()
        val demandImportedRecord = input.toDemandRecord(sourceId, { DemandRecord() }, DEMAND)

        DemandRepositoryImpl().upsertDemands(dsl, demandImportedRecord)

        val dbRecords = dsl.selectFrom(DEMAND).fetch()
        assertEquals(input.data.size, dbRecords.size)
        dbRecords.forEach { dbRecord ->
            with(dbRecord) {
                input.data.any {
                    recipeIndex == it[Demand.MEAL_NUMBER_HEADER].toDouble().toInt() &&
                        week == it[Demand.WEEK_HEADER] &&
                        productFamily == it[Demand.PRODUCT_FAMILY_HEADER] &&
                        country == it[Demand.COUNTRY_HEADER] &&
                        locale == it.optional[Demand.LOCALE_HEADER] &&
                        day == it[Demand.DAY_HEADER] &&
                        dcCode == it[Demand.DC_HEADER] &&
                        peopleCount == it[Demand.SIZE_HEADER].toDouble().toInt() &&
                        brand == "UNKNOWN" &&
                        mealsToDeliver == it[Demand.MEALS_TO_DELIVER_HEADER].toInt() &&
                        this.sourceId == sourceId &&
                        dirtyBit == true
                }
            }
        }
    }

    @Test fun `demand is deleted by week`() {
        val input = Demand.generateRandom(20)
        val demandImportedRecord = input.toDemandRecord(UUID.randomUUID(), { DemandRecord() }, DEMAND)

        DemandRepositoryImpl().upsertDemands(dsl, demandImportedRecord)
        val inserted = dsl.selectFrom(DEMAND).fetch()

        val demandsWithWeekToBeDeleted = inserted.take(2).map { it.week }
        DemandRepositoryImpl().deleteUnMatchedDemands(
            dsl,
            demandsToBeDeleted = inserted.filter { it.week in demandsWithWeekToBeDeleted }
        )
        assertTrue {
            dsl.selectFrom(DEMAND).fetch().none {
                it.week in demandsWithWeekToBeDeleted
            }
        }
    }

    @Test fun `demand is deleted by dc`() {
        val input = Demand.generateRandom(20)
        val demandImportedRecord = input.toDemandRecord(UUID.randomUUID(), { DemandRecord() }, DEMAND)

        DemandRepositoryImpl().upsertDemands(dsl, demandImportedRecord)
        val inserted = dsl.selectFrom(DEMAND).fetch()

        val demandsWithDcCodeToBeDeleted = inserted.take(2).map { it.dcCode }
        DemandRepositoryImpl().deleteUnMatchedDemands(
            dsl,
            demandsToBeDeleted = inserted.filter { it.dcCode in demandsWithDcCodeToBeDeleted }
        )
        assertTrue {
            dsl.selectFrom(DEMAND).fetch().none {
                it.dcCode in demandsWithDcCodeToBeDeleted
            }
        }
    }

    @Test fun `demand is deleted by country`() {
        val input = Demand.generateRandom(20)
        val demandImportedRecord = input.toDemandRecord(UUID.randomUUID(), { DemandRecord() }, DEMAND)

        DemandRepositoryImpl().upsertDemands(dsl, demandImportedRecord)
        val inserted = dsl.selectFrom(DEMAND).fetch()

        val demandsWithCountryToBeDeleted = inserted.take(2).map { it.country }
        DemandRepositoryImpl().deleteUnMatchedDemands(
            dsl,
            demandsToBeDeleted = inserted.filter { it.country in demandsWithCountryToBeDeleted }
        )
        assertTrue {
            dsl.selectFrom(DEMAND).fetch().none {
                it.country in demandsWithCountryToBeDeleted
            }
        }
    }

    @Test fun `demand is inserted for DACH`() {
        val input = Demand.generateRandom(1)
        val sourceId = UUID.randomUUID()
        val demandRecord = input.toDemandRecord(sourceId, { DemandRecord() }, DEMAND)

        DemandRepositoryImpl().upsert(dsl, demandRecord.first().toDemand())

        val upsertedDemand = DemandRepositoryImpl().fetchDemand(dsl, demandRecord.first().toDemand())

        assertNotNull(upsertedDemand)
        demandRecord.first().apply {
            assertEquals(recipeIndex, upsertedDemand.recipeIndex)
            assertEquals(day, upsertedDemand.day)
            assertEquals(week, upsertedDemand.week)
            assertEquals(country, upsertedDemand.country)
            assertEquals(productFamily, upsertedDemand.productFamily)
            assertEquals(locale, upsertedDemand.locale)
            assertEquals(peopleCount, upsertedDemand.peopleCount)
            assertEquals(brand, upsertedDemand.brand)
            assertEquals(mealsToDeliver, upsertedDemand.mealsToDeliver)
            assertEquals(dcCode, upsertedDemand.dcCode)
            assertTrue(upsertedDemand.dirtyBit)
        }
    }

    @Test fun `demand is upserted for DACH`() {
        val input = Demand.generateRandom(1)
        val sourceId = UUID.randomUUID()
        val demandRecord = input.toDemandRecord(sourceId, { DemandRecord() }, DEMAND)
        DemandRepositoryImpl().upsert(dsl, demandRecord.first().toDemand())
        val createdDemand = DemandRepositoryImpl().fetchDemand(dsl, demandRecord.first().toDemand())

        assertNotNull(createdDemand)
        demandRecord.first().apply {
            assertEquals(recipeIndex, createdDemand.recipeIndex)
            assertEquals(day, createdDemand.day)
            assertEquals(week, createdDemand.week)
            assertEquals(country, createdDemand.country)
            assertEquals(productFamily, createdDemand.productFamily)
            assertEquals(locale, createdDemand.locale)
            assertEquals(peopleCount, createdDemand.peopleCount)
            assertEquals(brand, createdDemand.brand)
            assertEquals(mealsToDeliver, createdDemand.mealsToDeliver)
            assertEquals(dcCode, createdDemand.dcCode)
            assertTrue(createdDemand.dirtyBit)
        }

        DemandRepositoryImpl().upsert(dsl, demandRecord.first().toDemand().copy(mealsToDeliver = 20000))
        val upsertedDemand = DemandRepositoryImpl().fetchDemand(dsl, demandRecord.first().toDemand())
        assertEquals(20000, upsertedDemand?.mealsToDeliver)
    }

    @Test fun `demand is deleted for DACH`() {
        val input = Demand.generateRandom(1)
        val sourceId = UUID.randomUUID()
        val demandRecord = input.toDemandRecord(sourceId, { DemandRecord() }, DEMAND)
        DemandRepositoryImpl().upsert(dsl, demandRecord.first().toDemand())
        DemandRepositoryImpl().delete(dsl, demandRecord.map { it.toDemand() })
        val createdDemand = DemandRepositoryImpl().fetchDemand(dsl, demandRecord.first().toDemand())
        assertNull(createdDemand)
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
        }
    }
}
