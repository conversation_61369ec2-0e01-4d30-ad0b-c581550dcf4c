import com.hellofresh.cif.slack.SlackMessage
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.IMPORTS
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.LOCAL_D4
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.SCO
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileType.DEMAND as FileTypeDemand
import com.hellofresh.skudemandforecast.demandImportJob.service.s3.S3FileDetails
import io.mockk.coEvery
import io.mockk.slot
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

const val BUCKET = "test-bucket"

class LocalD4ImportServiceTest : FunctionalTest() {

    @Test
    fun `should process D4 demand files where there is no sco demand exists`() {
        val demandImportedFileName = "d4demandfile.csv"
        val distributionCenter = persistDistributionCenter()
        val timestamp = Instant.now()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(IMPORTS_PREFIX + demandImportedFileName).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )
        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(1, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName, importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_30), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demand = demands.first()
        assertEquals(21, demand.recipeIndex)
        assertEquals(PRODUCT_FAMILY, demand.productFamily)
        assertEquals(COUNTRY_GB, demand.country)
        assertEquals(distributionCenter.dcCode, demand.dcCode)
        assertEquals(DAY_SUNDAY, demand.day)
        assertEquals(LOCAL_GB, demand.locale)
        assertEquals(WEEK_30, demand.week)
        assertEquals(12, demand.mealsToDeliver)
        assertTrue(demand.dirtyBit)
    }

    @Test
    fun `should not process D4 demand files when the demand records already exist`() {
        val demandImportedFileName = "d4demandalreadyexist.csv"
        val distributionCenter = persistDistributionCenter()
        val timestamp = Instant.now()
        val demandImportedRecord = demandRecord(dirtyBitParam = false)
        dsl.batchInsert(demandImportedRecord).execute()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(IMPORTS_PREFIX + demandImportedFileName).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )
        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(0, imports.size)
        assertEquals(1, demands.size)

        val demand = demands.first()
        assertEquals(1, demand.recipeIndex)
        assertEquals(PRODUCT_FAMILY, demand.productFamily)
        assertEquals(COUNTRY_GB, demand.country)
        assertEquals(distributionCenter.dcCode, demand.dcCode)
        assertEquals(DAY_SUNDAY, demand.day)
        assertEquals(LOCAL_GB, demand.locale)
        assertEquals(WEEK_30, demand.week)
        assertEquals(1, demand.mealsToDeliver)
        assertFalse(demand.dirtyBit)
    }

    @ParameterizedTest(name = "should not process Local D4 demand files: {2}")
    @MethodSource("invalidLocalD4FileScenarios")
    @Suppress("UnusedParameter") // testCaseDescription
    fun `should not process invalid D4 demand files`(
        demandImportedFileName: String,
        expectedErrorMessage: String,
        testCaseDescription: String
    ) {
        val distributionCenter = persistDistributionCenter()
        val slackMessageSlot = slot<SlackMessage>()
        val timestamp = Instant.now()
        coEvery {
            slackClient.sendMessageAsync(capture(slackMessageSlot), "test")
        } returns Unit

        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(
            IMPORTS_PREFIX + demandImportedFileName,
        ).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )

        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val importsCount = dsl.fetchCount(IMPORTS)
        val demandsCount = dsl.fetchCount(DEMAND)

        assertEquals(1, importsCount)
        assertEquals(0, demandsCount)
        assertEquals("Unable to import Local D4 file.", slackMessageSlot.captured.header)
        assertTrue(slackMessageSlot.captured.mainText.contains(demandImportedFileName))
        assertTrue(slackMessageSlot.captured.detailsText?.contains(expectedErrorMessage)!!)
        assertEquals("Inventory Planner", slackMessageSlot.captured.userName)
    }

    @Test
    fun `should process D4 demand files with duplicate rows`() {
        val demandImportedFileName = "d4demandfile-duplicaterows.csv"
        val distributionCenter = persistDistributionCenter()
        val timestamp = Instant.now()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(IMPORTS_PREFIX + demandImportedFileName).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )
        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(1, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName, importedDemandFile.name)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demand = demands.first()
        assertEquals(24, demand.mealsToDeliver)
    }

    @Test
    fun `should process D4 demand files and skip the existing sco demand records`() {
        val demandImportedFileName = "d4demandfile-withsco.csv"
        val distributionCenter = persistDistributionCenter()
        val scoImportsRecord = importsRecord(sourceParam = SCO)
        val timestamp = Instant.now()
        val demandImportedRecord = demandRecord()
        dsl.batchInsert(scoImportsRecord, demandImportedRecord).execute()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(IMPORTS_PREFIX + demandImportedFileName).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )

        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).where(IMPORTS.SOURCE.eq(LOCAL_D4)).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(2, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName, importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_30, WEEK_31), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demandFromSco = demands.first { it.week == WEEK_30 }
        val demandFromLocalD4 = demands.first { it.week == WEEK_31 }
        demandFromSco.apply {
            assertEquals(1, recipeIndex)
            assertEquals(productFamily, productFamily)
            assertEquals(COUNTRY_GB, country)
            assertEquals(distributionCenter.dcCode, dcCode)
            assertEquals(DAY_SUNDAY, day)
            assertEquals(LOCAL_GB, locale)
            assertEquals(WEEK_30, week)
            assertEquals(1, mealsToDeliver)
            assertTrue(dirtyBit)
        }

        demandFromLocalD4.apply {
            assertEquals(21, recipeIndex)
            assertEquals(productFamily, productFamily)
            assertEquals(COUNTRY_GB, country)
            assertEquals(distributionCenter.dcCode, dcCode)
            assertEquals(DAY_SUNDAY, day)
            assertEquals(LOCAL_GB, locale)
            assertEquals(WEEK_31, week)
            assertEquals(12, mealsToDeliver)
            assertTrue(dirtyBit)
        }
    }

    @Test
    fun `should upsert the latest local D4 demands from the latest D4 file`() {
        val demandImportedFileName = "d4demandfile-upsertdemands.csv"
        val demandImportedRecord = demandRecord()
        val distributionCenter = persistDistributionCenter()
        val timestamp = Instant.now()
        dsl.batchInsert(demandImportedRecord).execute()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent(IMPORTS_PREFIX + demandImportedFileName).inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )
        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(1, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName, importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_30), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demandFromLocalD4 = demands.first()
        demandFromLocalD4.apply {
            assertEquals(1, recipeIndex)
            assertEquals(productFamily, productFamily)
            assertEquals(COUNTRY_GB, country)
            assertEquals(distributionCenter.dcCode, dcCode)
            assertEquals(DAY_SUNDAY, day)
            assertEquals(LOCAL_GB, locale)
            assertEquals(WEEK_30, week)
            assertEquals(1000, mealsToDeliver)
            assertTrue(dirtyBit)
        }
    }

    @Test
    fun `while processing the local D4 files should replace the locale NI with GB`() {
        val demandImportedFileName = "d4demandfile-with-NI-locale.csv"
        val distributionCenter = persistDistributionCenter()
        val timestamp = Instant.now()
        coEvery {
            mockS3Service.fetchObjectContent(
                any(),
                any(),
            )
        } returns readFileContent("$IMPORTS_PREFIX/$demandImportedFileName").inputStream()
        coEvery { s3FileProcessor.processContent(any(), any()) } returns getParsedDemandFile(
            demandImportedFileName,
            distributionCenter,
        )
        val s3FileDetails = S3FileDetails(
            bucket = BUCKET,
            name = demandImportedFileName,
            timestamp = timestamp,
        )
        runBlocking { localD4ImportService.process(s3FileDetails) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(5, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName, importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_30), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        demands.forEach { demand ->
            assertEquals(LOCAL_GB, demand.locale)
        }
    }

    companion object {
        @JvmStatic
        fun invalidLocalD4FileScenarios() = listOf(
            Arguments.of("d4demand-invalid-file.csv", "Headers missing for file type", "Invalid or no CSV file"),
            Arguments.of("d4demand-invalid-file-negative.csv", "Negative demand", "Invalid negative value"),
            Arguments.of(
                "d4demand-different-product-family.csv",
                "Forecast has different product families for the same recipe index, week, country, locale",
                "Different product family",
            ),
        )
    }
}
