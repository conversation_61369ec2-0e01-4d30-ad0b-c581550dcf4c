@file: Suppress("StringLiteralDuplication")

import com.hellofresh.cif.slack.SlackMessage
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.IMPORTS
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.LOCAL_D4
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileType.DEMAND as FileTypeDemand
import com.hellofresh.skudemandforecast.demandImportJob.service.NORDICS_MARKET
import com.hellofresh.skudemandforecast.demandImportJob.service.s3.S3FileDetails
import io.mockk.coEvery
import io.mockk.slot
import java.time.Instant
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val IMPORTS_PATH = "imports/"
private const val S3_BUCKET = "hf-nordics"

class NordicsImportServiceTest : FunctionalTest() {

    private val slackMessageSlot = slot<SlackMessage>()

    @Test
    fun `should process nordics D4 demand files`() {
        val distributionCenter = persistDistributionCenter(NORDICS_DC, NORDICS_MARKET)
        val demandImportedFileName = "nordics/valid_demand_file.csv"
        val fileContentInByteArray = readFileContent("$IMPORTS_PATH$demandImportedFileName")
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns fileContentInByteArray.inputStream()
        coEvery {
            s3FileProcessor.processContent(demandImportedFileName, fileContentInByteArray)
        } returns getParsedDemandFile(demandImportedFileName, distributionCenter, NORDICS_MARKET)
        runBlocking { nordicsImportService.process(S3FileDetails(S3_BUCKET, demandImportedFileName, Instant.now())) }

        val imports = dsl.selectFrom(IMPORTS).where(IMPORTS.MARKET.eq(NORDICS_MARKET)).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(1, imports.size)
        assertEquals(1, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName.split("/").last(), importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_45), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demand = demands.first()
        assertEquals(21, demand.recipeIndex)
        assertEquals(COUNTRY_SE, demand.country)
        assertEquals(distributionCenter.dcCode, demand.dcCode)
        assertEquals(DAY_FRIDAY, demand.day)
        assertEquals(LOCALE_SE, demand.locale)
        assertEquals(WEEK_45, demand.week)
        assertEquals(2, demand.mealsToDeliver)
        assertTrue(demand.dirtyBit)
    }

    @Test
    fun `should not process D4 demand files when the demand records already exist`() {
        val distributionCenter = persistDistributionCenter(NORDICS_DC, NORDICS_MARKET)
        val demandImportedRecord = nordicsDemandRecord(dirtyBitParam = false)
        dsl.batchInsert(demandImportedRecord).execute()
        val demandImportedFileName = "nordics/valid_demand_file_already_exists.csv"
        val fileContentInByteArray = readFileContent("$IMPORTS_PATH$demandImportedFileName")
        coEvery {
            s3FileProcessor.processContent(demandImportedFileName, fileContentInByteArray)
        } returns getParsedDemandFile(demandImportedFileName, distributionCenter, NORDICS_MARKET)
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns fileContentInByteArray.inputStream()
        runBlocking { nordicsImportService.process(S3FileDetails(S3_BUCKET, demandImportedFileName, Instant.now())) }

        val imports = dsl.selectFrom(IMPORTS).fetch()
        val demands = dsl.selectFrom(DEMAND).fetch()

        assertEquals(0, imports.size)
        assertEquals(1, demands.size)

        val demand = demands.first()
        assertEquals(1, demand.recipeIndex)
        assertEquals(PRODUCT_FAMILY_CLASSIC, demand.productFamily)
        assertEquals(COUNTRY_SE, demand.country)
        assertEquals(distributionCenter.dcCode, demand.dcCode)
        assertEquals(DAY_FRIDAY, demand.day)
        assertEquals(LOCALE_SE, demand.locale)
        assertEquals(WEEK_45, demand.week)
        assertEquals(1, demand.mealsToDeliver)
        assertFalse(demand.dirtyBit)
    }

    @ParameterizedTest(name = "{2}")
    @MethodSource("invalidNordicsD4FileScenarios")
    @Suppress("UnusedParameter") // testCaseDescription
    fun `should not process invalid nordics D4 demand files`(
        invalidFile: String,
        expectedErrorMessage: String,
        testCaseDescription: String
    ) {
        val distributionCenter = persistDistributionCenter(NORDICS_DC, NORDICS_MARKET)

        coEvery { slackClient.sendMessageAsync(capture(slackMessageSlot), "test") } returns Unit

        val fileContentInByteArray = readFileContent("$IMPORTS_PATH$invalidFile")
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns fileContentInByteArray.inputStream()
        coEvery {
            s3FileProcessor.processContent(invalidFile, fileContentInByteArray)
        } returns getParsedDemandFile(invalidFile, distributionCenter, NORDICS_MARKET)

        runBlocking { nordicsImportService.process(S3FileDetails(S3_BUCKET, invalidFile, Instant.now())) }

        val demandsCount = dsl.fetchCount(DEMAND)
        val importsCount = dsl.fetchCount(IMPORTS)

        assertEquals(1, importsCount)
        assertEquals(0, demandsCount)
        assertEquals("Nordics Local D4 file, Unable to import.", slackMessageSlot.captured.header)
        assertTrue(slackMessageSlot.captured.mainText.contains(invalidFile))
        assertTrue(slackMessageSlot.captured.detailsText!!.contains(expectedErrorMessage))
        assertEquals("Inventory Planner", slackMessageSlot.captured.userName)
    }

    @Test
    fun `should upsert the latest local D4 demands from the latest D4 file`() {
        val demandImportedFileName = "nordics/valid_demand_file_upsertdemands.csv"
        val demandImportedRecord = nordicsDemandRecord()
        val distributionCenter = persistDistributionCenter(NORDICS_DC, NORDICS_MARKET)
        dsl.batchInsert(demandImportedRecord).execute()
        val fileContentInByteArray = readFileContent("$IMPORTS_PATH$demandImportedFileName")
        coEvery {
            s3FileProcessor.processContent(demandImportedFileName, fileContentInByteArray)
        } returns getParsedDemandFile(demandImportedFileName, distributionCenter, NORDICS_MARKET)
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns fileContentInByteArray.inputStream()
        runBlocking { nordicsImportService.process(S3FileDetails(S3_BUCKET, demandImportedFileName, Instant.now())) }

        val demands = dsl.selectFrom(DEMAND).fetch()
        val imports = dsl.selectFrom(IMPORTS).fetch()

        assertEquals(1, imports.size)
        assertEquals(1, demands.size)

        val importedDemandFile = imports.first()
        assertEquals(demandImportedFileName.split("/").last(), importedDemandFile.name)
        assertEquals(setOf(distributionCenter.dcCode), importedDemandFile.dcs.toSet())
        assertEquals(setOf(WEEK_45), importedDemandFile.weeks.toSet())
        assertEquals(FileTypeDemand, importedDemandFile.type)
        assertEquals(LOCAL_D4, importedDemandFile.source)

        val demandFromLocalD4 = demands.first()
        demandFromLocalD4.apply {
            assertEquals(1, recipeIndex)
            assertEquals(productFamily, productFamily)
            assertEquals(COUNTRY_SE, country)
            assertEquals(distributionCenter.dcCode, dcCode)
            assertEquals(DAY_FRIDAY, day)
            assertEquals(LOCALE_SE, locale)
            assertEquals(WEEK_45, week)
            assertEquals(1000, mealsToDeliver)
            assertTrue(dirtyBit)
        }
    }

    companion object {
        @JvmStatic
        fun invalidNordicsD4FileScenarios() = listOf(
            Arguments.of(
                "nordics/invalid_demand_file.csv",
                "Headers missing for file type",
                "should not process D4 demand files where there is invalid or no csv file"
            ),
            Arguments.of(
                "nordics/invalid_file_negative_number.csv",
                "Negative demand",
                "should not process D4 demand files where there is invalid negative value and add entry in imports with rejected status"
            ),
            Arguments.of(
                "nordics/invalid_demand_file_with_different_product_family.csv",
                "Forecast has different product families for the same recipe index, week, country, locale",
                "should not process D4 demand files where there different product family and add entry in imports with rejected status"
            )
        )
    }
}
