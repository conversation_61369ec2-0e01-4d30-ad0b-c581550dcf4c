import com.fasterxml.jackson.module.kotlin.readValue
import com.google.protobuf.Timestamp
import com.google.type.Date
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand.DemandSegment
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand.DemandSegment.ProductionSchedule
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand.EventType
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand.RecipeSpecification
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemandKey
import com.hellofresh.skuDemandForecast.models.DemandDetail
import com.hellofresh.skuDemandForecast.models.DemandDetails
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.demandImportJob.service.kafka.PUBLIC_DEMAND_TOPIC
import io.mockk.coEvery
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.ProducerRecord
import org.jooq.JSONB
import org.junit.jupiter.api.Test
import org.testcontainers.shaded.org.awaitility.Awaitility

private const val WEDNESDAY = "Wednesday"

@Suppress("LongParameterList")
class DemandConsumerTest : FunctionalTest() {
    private val createRecipeId = UUID.randomUUID()
    private val updateRecipeId = UUID.randomUUID()
    private val deleteRecipeId = UUID.randomUUID()

    @Test
    fun `should consume to create demand from dach demand topic`() {
        val key = createKey(createRecipeId)
        val date = LocalDate.of(2025, 2, 12)
        val assemblyTime = date.atStartOfDay()
        val kittingTime = date.atStartOfDay().plusHours(1)
        createDbRecord(1, 15, 8, 280, assemblyTime, kittingTime)

        val demand1 = createDemand(100, 2, assemblyTime, kittingTime)
        val demand2 = createDemand(200, 4, assemblyTime, kittingTime)

        // demand3 already exists with 280 demands, will not be added
        val demand3 = createDemand(280, 8, assemblyTime, kittingTime)

        val value = createValue(
            createRecipeId,
            EventType.EVENT_TYPE_CREATED,
            15,
            dcValue = "VE",
            demandList = listOf(demand1, demand1, demand2, demand2, demand3, demand3),
        )

        coEvery { recipeSlotCache.get(any()) } returns 1
        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key, value)).get()

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = dsl.selectFrom(Tables.DEMAND).fetch()

            Awaitility
                .await()
                .atMost(java.time.Duration.ofSeconds(50))
                .until {
                    records = dsl
                        .selectFrom(Tables.DEMAND).fetch()
                    records.count() == 3
                }.let {
                    assertEquals(3, records.count())

                    val first = records.filter {
                        it.recipeIndex == 1
                    }.first { it.peopleCount == 2 }

                    val second = records.filter {
                        it.recipeIndex == 1
                    }.first { it.peopleCount == 4 }

                    val third = records.filter {
                        it.recipeIndex == 1
                    }.first { it.peopleCount == 8 }

                    assertEquals(200, first.mealsToDeliver)
                    assertEquals("2025-W15", first.week)
                    assertEquals(WEDNESDAY, first.day)
                    assertDemandDetails(first.mealsToDeliver, assemblyTime, kittingTime, first)

                    assertEquals(400, second.mealsToDeliver)
                    assertEquals(WEDNESDAY, second.day)
                    assertDemandDetails(second.mealsToDeliver, assemblyTime, kittingTime, second)

                    assertEquals(280, third.mealsToDeliver)
                    assertEquals(WEDNESDAY, third.day)
                    assertDemandDetails(third.mealsToDeliver, assemblyTime, kittingTime, third, 1)
                }
            app.cancel()
        }
    }

    @Test
    fun `should update demand from dach demand topic`() {
        val key = createKey(updateRecipeId)
        val date = LocalDate.of(2025, 2, 12)
        val assemblyTime = date.atStartOfDay()
        val kittingTime = date.atStartOfDay().plusHours(1)
        createDbRecord(2, 15, 6, 250, assemblyTime, kittingTime)

        val demand1 = createDemand(250, 6, assemblyTime, kittingTime) // this will be ignored
        val demand2 = createDemand(250, 4, assemblyTime, kittingTime)
        val demand3 = createDemand(450, 6, assemblyTime, kittingTime) // this will be updated

        val value = createValue(
            updateRecipeId,
            EventType.EVENT_TYPE_UPDATED,
            15,
            dcValue = "VE",
            demandList = listOf(demand1, demand2, demand3),
        )

        coEvery { recipeSlotCache.get(any()) } returns 2

        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key, value)).get()

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }
            Thread.sleep(3000)

            var records = dsl.selectFrom(Tables.DEMAND).fetch()

            Awaitility
                .await()
                .atMost(java.time.Duration.ofSeconds(3000))
                .until {
                    records = dsl.selectFrom(Tables.DEMAND).fetch()
                    records.count() == 2
                }.let {
                    assertEquals(2, records.count())

                    val first = records.filter {
                        it.recipeIndex == 2
                    }.first { it.peopleCount == 4 }

                    val second = records.filter {
                        it.recipeIndex == 2
                    }.first { it.peopleCount == 6 }

                    assertEquals(2, first.recipeIndex)
                    assertEquals(250, first.mealsToDeliver)
                    assertEquals("2025-W15", first.week)
                    assertEquals("classic-box", first.productFamily)
                    assertEquals(WEDNESDAY, first.day)
                    assertDemandDetails(first.mealsToDeliver, assemblyTime, kittingTime, first, 1)

                    assertEquals(700, second.mealsToDeliver)
                    assertEquals(6, second.peopleCount)
                    assertEquals(WEDNESDAY, second.day)
                    assertDemandDetails(second.mealsToDeliver, assemblyTime, kittingTime, second, 2)
                }
            app.cancel()
        }
    }

    @Test
    fun `should delete demand from dach demand topic when receiving the DELETE event type`() {
        val key = createKey(deleteRecipeId)
        val date = LocalDate.of(2025, 2, 12)
        val assemblyTime = date.atStartOfDay()
        val kittingTime = date.atStartOfDay().plusHours(1)
        createDbRecord(2, 15, 6, 250, assemblyTime, kittingTime)
        createDemand(250, 6, assemblyTime, kittingTime) // this will be deleted

        val value = createValue(
            deleteRecipeId,
            EventType.EVENT_TYPE_DELETED,
            15,
            dcValue = "VE",
            demandList = emptyList(),
        )

        coEvery { recipeSlotCache.get(any()) } returns 2

        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key, value)).get()

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }
            Thread.sleep(3000)
            Awaitility
                .await()
                .atMost(java.time.Duration.ofSeconds(50))
                .until {
                    val records = dsl.selectFrom(Tables.DEMAND).fetch()
                    records.isEmpty()
                }
            app.cancel()
        }
    }

    @Test
    fun `consumer should continue to work with a wrong dc country map`() {
        val key = createKey(updateRecipeId)
        val date = LocalDate.of(2025, 2, 12)
        val assemblyTime = date.atStartOfDay()
        val kittingTime = date.atStartOfDay().plusHours(1)
        val demand1 = createDemand(50, 6, assemblyTime, kittingTime)
        val demand2 = createDemand(150, 4, assemblyTime, kittingTime)
        val value = createValue(
            updateRecipeId,
            EventType.EVENT_TYPE_UPDATED,
            15,
            dcValue = "FR", // invalid dcValue
            demandList = listOf(demand1, demand2),
        )

        val key2 = createKey(UUID.randomUUID())
        val demand3 = createDemand(350, 6, assemblyTime, kittingTime)
        val value2 = createValue(
            updateRecipeId,
            EventType.EVENT_TYPE_CREATED,
            15,
            dcValue = "BX",
            demandList = listOf(demand3),
        )

        coEvery { recipeSlotCache.get(any()) } returns 3

        // this message will not be stored due to wrong dc country map
        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key, value)).get()

        // consumer will catch error and continue to work and store this value2 message
        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key2, value2)).get()

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }
            var records = dsl.selectFrom(Tables.DEMAND).fetch()

            Awaitility
                .await()
                .atMost(java.time.Duration.ofSeconds(300))
                .until {
                    records = dsl.selectFrom(Tables.DEMAND).fetch()
                    records.count() == 1
                }.let {
                    assertEquals(1, records.count())

                    with(records.first()) {
                        assertEquals(3, recipeIndex)
                        assertEquals("DE", country)
                        assertEquals(6, peopleCount)
                        assertEquals(350, mealsToDeliver)
                        assertDemandDetails(mealsToDeliver, assemblyTime, kittingTime, this, 1)
                    }
                }
            app.cancel()
        }
    }

    @Test
    fun `should discard old weeks before week start from statsig`() {
        val key = createKey(createRecipeId)
        val date = LocalDate.of(2025, 2, 12)
        val assemblyTime = date.atStartOfDay()
        val kittingTime = date.atStartOfDay().plusHours(1)

        val demand1 = createDemand(100, 2, assemblyTime, kittingTime)
        val demand2 = createDemand(200, 4, assemblyTime, kittingTime)

        val value = createValue(
            createRecipeId,
            EventType.EVENT_TYPE_CREATED,
            13,
            dcValue = "VE",
            demandList = listOf(demand1, demand2),
        )

        coEvery { recipeSlotCache.get(any()) } returns 1
        topicProducer.send(ProducerRecord(PUBLIC_DEMAND_TOPIC, key, value)).get()

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = dsl.selectFrom(Tables.DEMAND).fetch()

            Awaitility
                .await()
                .atMost(java.time.Duration.ofSeconds(50))
                .until {
                    records = dsl
                        .selectFrom(Tables.DEMAND).fetch()
                    records.count() == 0
                }.let {
                    assertEquals(0, records.count())
                }
            app.cancel()
        }
    }

    private fun assertDemandDetails(
        mealsToDeliver: Int,
        assemblyTime: LocalDateTime,
        kittingTime: LocalDateTime,
        demandRecord: DemandRecord,
        size: Int = 2,
    ) {
        with(objectMapper.readValue<DemandDetails>(demandRecord.demandDetails!!.data())) {
            assertEquals(size, demandDetails.size)
            assertEquals(mealsToDeliver, demandDetails.sumOf { it.qty })
            assertEquals(assemblyTime, demandDetails.first().assemblyTime)
            assertEquals(kittingTime, demandDetails.first().kittingTime)
        }
    }

    private fun createKey(recipeId: UUID): RecipeDemandKey =
        with(RecipeDemandKey.newBuilder()) {
            this.recipeId = recipeId.toString()
            productionDate = with(Date.newBuilder()) {
                day = 12
                month = 2
                year = 2025
                build()
            }
            dc = "VE"
            build()
        }

    private fun createValue(
        recipeId: UUID,
        recipeEventType: EventType,
        weekValue: Int = 15,
        demandList: List<DemandSegment>,
        dcValue: String,
    ): RecipeDemand =
        with(RecipeDemand.newBuilder()) {
            eventType = recipeEventType
            eventTime = Timestamp.getDefaultInstance()
            market = "DACH"
            dc = dcValue
            demandType = RecipeDemand.DemandType.DEMAND_TYPE_FORECAST
            recipe = with(RecipeSpecification.newBuilder()) {
                year = 2025
                week = weekValue
                this.recipeId = recipeId.toString()
                build()
            }
            addAllDemand(demandList)
            build()
        }

    private fun createDbRecord(
        recipeIndexValue: Int,
        weekValue: Int,
        peopleCountValue: Int,
        mealsToDeliverValue: Int,
        assemblyTime: LocalDateTime,
        kittingTime: LocalDateTime,
    ) {
        DemandRecord()
            .apply {
                recipeIndex = recipeIndexValue
                week = "2025-W$weekValue"
                productFamily = "classic-box"
                country = "DE"
                dcCode = "VE"
                locale = "DE"
                peopleCount = peopleCountValue
                day = WEDNESDAY.lowercase()
                    .replaceFirstChar { it.uppercase() }
                mealsToDeliver = mealsToDeliverValue
                brand = "UNKNOWN"
                demandDetails = JSONB.valueOf(
                    objectMapper.writeValueAsString(
                        DemandDetails(
                            listOf(
                                DemandDetail(
                                    qty = mealsToDeliverValue,
                                    type = null,
                                    assemblyTime = assemblyTime,
                                    kittingTime = kittingTime,
                                ),
                            ),
                        ),
                    ),
                )
            }.also {
                dsl.batchInsert(it).execute()
            }
    }

    private fun createDemand(
        demand: Int = 100,
        servingSize: Int = 2,
        assemblyTime: LocalDateTime,
        kittingTime: LocalDateTime,
    ): DemandSegment {
        val protoAssembly = with(assemblyTime.atZone(UTC)) {
            Timestamp.newBuilder()
                .setSeconds(toEpochSecond())
                .setNanos(nano)
                .build()
        }
        val protoKitting = with(kittingTime.atZone(UTC)) {
            Timestamp.newBuilder()
                .setSeconds(toEpochSecond())
                .setNanos(nano)
                .build()
        }

        return DemandSegment.newBuilder()
            .setLocale("DE")
            .setDemand(demand)
            .setServingSize(servingSize)
            .setSchedule(
                ProductionSchedule.newBuilder()
                    .setAssemblyTime(protoAssembly)
                    .setKittingTime(protoKitting)
                    .build(),
            )
            .build()
    }
}
