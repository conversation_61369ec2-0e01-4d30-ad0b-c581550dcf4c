job.time_period_minutes=1

slack.webhook=noHost

db.host=localhost:5432
db.username=sdf
db.password=123456

db.readonly.host=localhost:5432
db.readonly.username=sdf
db.readonly.password=123456
statsig.offline=true
statsig.sdk.key=local

bootstrap.servers=localhost:29092

aws.s3.host=http://localhost:4566
s3.bucket.postfix=-staging
aws.sqs.host=http://localhost:4566
demand.aws.sqs.url=http://sqs.eu-west-1.localhost.localstack.cloud:4566/000000000000/hf_ip_demand_import_notifications_queue_local
menu.planning.service=http://localhost:1919
user.auth.service=http://localhost:1919
user.auth.service.client.id=
user.auth.service.client.secret=
user.auth.service.user.name=
user.auth.service.user.password=
recipe.slot.cache.refresh.period.minutes=360
