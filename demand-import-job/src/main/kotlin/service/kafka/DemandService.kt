@file:Suppress("TooManyFunctions")

package com.hellofresh.skudemandforecast.demandImportJob.service.kafka

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemand
import com.hellofresh.proto.stream.productionDemand.v1.RecipeDemandKey
import com.hellofresh.skuDemandForecast.db.CaffeineCache
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.featureflags.Context.MARKET
import com.hellofresh.skuDemandForecast.featureflags.ContextData
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfig.MPSConfig
import com.hellofresh.skuDemandForecast.featureflags.DynamicConfigClient
import com.hellofresh.skuDemandForecast.models.DcToCountryMapper
import com.hellofresh.skuDemandForecast.models.Demand
import com.hellofresh.skuDemandForecast.models.DemandDetail
import com.hellofresh.skuDemandForecast.models.DemandDetails
import com.hellofresh.skudemandforecast.demandImportJob.repository.DemandRepository
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.demandImportJob.service.RecipeSlotCacheBuilder.RecipeIdSlotIdKey
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.time.toOffsetDateTime
import java.time.LocalDate
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

private val dcCodeToLocaleMap = mapOf(
    "VE" to listOf("DE", "CH"),
    "BX" to listOf("DE", "CH"),
    "CR" to listOf("DE", "CH"),
    "CH" to listOf("DE", "CH"),
)

class DemandService(
    private val writeDsl: MetricsDSLContext,
    private val recipeSlotCache: CaffeineCache<RecipeIdSlotIdKey, Int>,
    private val demandRepository: DemandRepository<DemandRecord>,
    private val dcConfigService: DcConfigService,
    private val dynamicConfigClient: DynamicConfigClient,
) {
    suspend fun process(consumerRecords: ConsumerRecords<RecipeDemandKey, RecipeDemand>) {
        runCatching {
            val demands: List<DemandWithEventType> = consumerRecords.flatMap { record ->
                val key = record.key()
                val demand = record.value()

                val locales = if (demand.demandList.isNotEmpty()) {
                    demand.demandList.map { it.locale }.distinct()
                } else {
                    dcCodeToLocaleMap[demand.dc] ?: listOf("DE", "CH")
                }

                locales.flatMap { locale ->
                    runCatching {
                        val distributionCenter = requireNotNull(
                            dcConfigService.dcConfigurations[key.dc],
                        ) { "Dc Code not found: ${key.dc}" }

                        val cacheKey = RecipeIdSlotIdKey(distributionCenter.market, key.recipeId, locale)
                        val recipeIndex =
                            requireNotNull(recipeSlotCache.get(cacheKey)) {
                                throw IllegalArgumentException("Recipe ID Mapping key $cacheKey} not found in cache")
                            }

                        mapToDemand(key, recipeIndex, demand, distributionCenter, locale)
                            .map { mappedDemand -> DemandWithEventType(demand.eventType, mappedDemand) }
                    }.getOrElse {
                        logger.warn("Error mapping demands", it)
                        emptyList()
                    }
                }
            }
            logger.info("Total DACH demand messages received for processing = ${demands.size}")
            demands.forEach {
                processDemand(it)
            }
        }.onFailure {
            logger.warn("Error persisting demand records", it)
        }
    }

    data class DemandWithEventType(
        val eventType: RecipeDemand.EventType,
        val demand: Demand,
    )

    private suspend fun processDemand(demandWithEventType: DemandWithEventType) {
        writeDsl
            .withTagName("demand-import")
            .transactionAsync { conf ->
                val tx = writeDsl.withMeteredConfiguration(conf)
                when (demandWithEventType.eventType) {
                    RecipeDemand.EventType.EVENT_TYPE_CREATED -> createDemand(tx, demandWithEventType.demand)
                    RecipeDemand.EventType.EVENT_TYPE_UPDATED -> {
                        updateDemand(tx, demandWithEventType.demand)
                    }

                    RecipeDemand.EventType.EVENT_TYPE_DELETED -> {
                        deleteDemand(tx, demandWithEventType.demand)
                    }

                    RecipeDemand.EventType.EVENT_TYPE_UNSPECIFIED, RecipeDemand.EventType.UNRECOGNIZED -> logger.warn(
                        "unspecified or unrecognized demand event received for the " +
                            "demand = ${demandWithEventType.demand}",
                    )
                }
            }.await()
    }

    private fun createDemand(tx: MetricsDSLContext, demand: Demand) {
        val existingDemand = fetchDemand(tx, demand)
        if (existingDemand == null) {
            demandRepository.upsert(tx, demand)
        } else {
            val demandCopy = existingDemand.copy(
                mealsToDeliver = demand.mealsToDeliver,
                demandDetails = demand.demandDetails,
            )
            demandRepository.upsert(tx, demandCopy)
        }
    }

    private fun updateDemand(tx: MetricsDSLContext, demand: Demand) {
        val existingDemand = fetchDemand(tx, demand)
        if (existingDemand != null) {
            val updatedDemand = existingDemand.copy(
                mealsToDeliver = demand.mealsToDeliver,
                demandDetails = demand.demandDetails,
            )
            demandRepository.upsert(tx, updatedDemand)
        } else {
            logger.warn(
                "Demand create / update event received - demand does not exist, hence creating it " +
                    "recipe-index = ${demand.recipeIndex}, country = ${demand.country}, week = ${demand.week}, " +
                    "dc code = ${demand.dcCode}, day = ${demand.day}, locale = ${demand.locale}",
            )
            createDemand(tx, demand)
        }
    }

    private fun deleteDemand(tx: MetricsDSLContext, demand: Demand) {
        val existingDemands = fetchDemandForDelete(tx, demand)
        if (existingDemands.isNotEmpty()) {
            demandRepository.delete(tx, existingDemands)
        } else {
            logger.warn(
                "Demand delete event received - demand does not exist, " +
                    "recipe-index = ${demand.recipeIndex}, country = ${demand.country}, week = ${demand.week}, " +
                    "brand = ${demand.brand}, day = ${demand.day}, locale = ${demand.locale}",
            )
        }
    }

    private fun fetchDemand(tx: MetricsDSLContext, demand: Demand): Demand? =
        demandRepository.fetchDemand(tx, demand)?.let {
            mapDemandRecordToDemand(it)
        }

    private fun fetchDemandForDelete(tx: MetricsDSLContext, demand: Demand): List<Demand> =
        demandRepository.fetchDemandForDelete(tx, demand).map {
            mapDemandRecordToDemand(it)
        }

    private fun mapDemandRecordToDemand(
        record: DemandRecord
    ): Demand = with(record) {
        Demand(
            recipeIndex = recipeIndex,
            week = week,
            productFamily = productFamily,
            country = country,
            locale = locale,
            day = day,
            dcCode = dcCode,
            peopleCount = peopleCount,
            mealsToDeliver = mealsToDeliver,
            brand = brand,
            sourceId = sourceId,
            demandDetails = demandDetails?.let {
                objectMapper.readValue<DemandDetails>(it.data())
            },
        )
    }

    private fun mapToDemand(
        key: RecipeDemandKey,
        position: Int,
        recipeDemandValue: RecipeDemand,
        distributionCenter: DistributionCenter,
        locale: String,
    ): List<Demand> {
        val week = getDcWeek(recipeDemandValue)
        val country = getCountry(recipeDemandValue)
        val dayOfWeek = LocalDate.of(
            key.productionDate.year,
            key.productionDate.month,
            key.productionDate.day,
        ).dayOfWeek.name.lowercase().replaceFirstChar { it.uppercase() }

        val mpsConfig = getMpsConfig(distributionCenter)
        val weekStart = mpsConfig?.parsedWeekStart
        val isBeforeCutOffWeek = mpsConfig != null && weekStart != null && week < weekStart
        val demands = if (recipeDemandValue.demandList.isEmpty() && !isBeforeCutOffWeek) {
            listOf(
                Demand(
                    recipeIndex = position,
                    week = week,
                    productFamily = "classic-box",
                    country = country,
                    locale = locale,
                    day = dayOfWeek,
                    dcCode = key.dc,
                    peopleCount = 0,
                    mealsToDeliver = 0,
                    brand = "UNKNOWN",
                    sourceId = null,
                    demandDetails = null,
                ),
            )
        } else {
            if (isBeforeCutOffWeek) {
                logger.warn(
                    "Week $week is before week start for ${distributionCenter.market} ${mpsConfig?.parsedWeekStart}",
                )
                emptyList()
            } else {
                recipeDemandValue.demandList
                    .filter { it.locale == locale }
                    .groupBy {
                        it.servingSize
                    }.map { (peopleCount, demands) ->
                        Demand(
                            recipeIndex = position,
                            week = week,
                            productFamily = "classic-box",
                            country = country,
                            locale = locale,
                            day = dayOfWeek,
                            dcCode = key.dc,
                            peopleCount = peopleCount,
                            mealsToDeliver = demands.sumOf { it.demand },
                            brand = "UNKNOWN",
                            sourceId = null,
                            demandDetails = toDemandDetails(demands, distributionCenter),
                        )
                    }
            }
        }
        return demands
    }

    private fun toDemandDetails(
        demands: List<RecipeDemand.DemandSegment>,
        distributionCenter: DistributionCenter
    ) =
        DemandDetails(
            demands.map { demand ->
                DemandDetail(
                    qty = demand.demand,
                    type = null,
                    assemblyTime = demand.schedule.assemblyTime
                        .toOffsetDateTime(distributionCenter.zoneId)
                        .toLocalDateTime(),
                    kittingTime = demand.schedule.kittingTime
                        .toOffsetDateTime(distributionCenter.zoneId)
                        .toLocalDateTime(),
                )
            },
        )

    private fun getMpsConfig(distributionCenter: DistributionCenter) =
        dynamicConfigClient.getConfig(
            MPSConfig(
                setOf(
                    ContextData(
                        MARKET,
                        distributionCenter.market,
                    ),
                ),
            ),
        )

    private fun getCountry(recipeDemandValue: RecipeDemand) =
        DcToCountryMapper.country[recipeDemandValue.dc] ?: run {
            val message = "${recipeDemandValue.dc}, Event type: ${recipeDemandValue.eventType}, " +
                "Week: ${recipeDemandValue.recipe.year}-W${recipeDemandValue.recipe.week}"
            throw IllegalArgumentException("Country not found for dc: $message")
        }

    private fun getDcWeek(recipeDemandValue: RecipeDemand) = DcWeek(
        year = recipeDemandValue.recipe.year,
        week = recipeDemandValue.recipe.week,
    ).toString()

    companion object : Logging {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
    }
}
