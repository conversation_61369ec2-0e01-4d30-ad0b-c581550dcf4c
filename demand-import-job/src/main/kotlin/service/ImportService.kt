package com.hellofresh.skudemandforecast.demandImportJob.service

import com.hellofresh.skuDemandForecast.models.DemandKey
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.model.fileupload.violations.SevereViolation
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Table
import org.jooq.UpdatableRecord

private const val MAX_NUMBER_OF_LINES_TO_SHOW = 20

open class ImportService {
    fun getDemandsToBeUpsertedAndDeleted(
        demandsFromFile: List<DemandRecord>,
        existingDemands: List<DemandRecord>
    ): DemandDetails {
        val existingDemandsMap = existingDemands.associateBy { createKey(it, Tables.DEMAND) }

        // Find new and modified demands
        val newAndModifiedDemands = demandsFromFile.filter { demandFromFile ->
            val existingDemand = existingDemandsMap[createKey(demandFromFile, Tables.DEMAND)]
            existingDemand == null || demandFromFile.mealsToDeliver != existingDemand.mealsToDeliver
        }

        // Find demands to be deleted (present in existingDemands but not in demandsFromFile)
        val demandsFromFileKeys = demandsFromFile.map { createKey(it, Tables.DEMAND) }.toSet()
        val demandsToBeDeleted = existingDemands.filter { existingDemand ->
            createKey(existingDemand, Tables.DEMAND) !in demandsFromFileKeys
        }
        return DemandDetails(newAndModifiedDemands, demandsToBeDeleted)
    }

    fun prepareErrorMessage(file: ImportedFile) = file.processFileResult.violations
        .filterIsInstance<SevereViolation>()
        .joinToString(",") { violation ->
            val lineNumbers = violation.lineNumbers
            val lineText = if (lineNumbers.size > MAX_NUMBER_OF_LINES_TO_SHOW) {
                "${lineNumbers.take(
                    MAX_NUMBER_OF_LINES_TO_SHOW
                ).joinToString(",")} ... more (total error lines = ${lineNumbers.size})"
            } else {
                lineNumbers.joinToString(",")
            }
            "$lineText - ${violation.message}"
        }

    private fun <R : UpdatableRecord<R>> createKey(demand: R, table: Table<R>): DemandKey =
        DemandKey(
            demand.getValue(table.field("recipe_index", Int::class.java)),
            demand.getValue(table.field("week", String::class.java)),
            demand.getValue(table.field("day", String::class.java)),
            demand.getValue(table.field("product_family", String::class.java)),
            demand.getValue(table.field("country", String::class.java)),
            demand.getValue(table.field("dc_code", String::class.java)),
            demand.getValue(table.field("people_count", Int::class.java)),
            demand.getValue(table.field("locale", String::class.java)),
            demand.getValue(table.field("brand", String::class.java)),
        )

    companion object : Logging
}
