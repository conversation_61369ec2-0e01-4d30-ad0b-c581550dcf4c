package com.hellofresh.skudemandforecast.demandImportJob.service.s3

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Notification(
    @JsonProperty("Type")
    val type: String? = null,
    @JsonProperty("MessageId")
    val messageId: String? = null,
    @JsonProperty("TopicArn")
    val topicArn: String? = null,
    @JsonProperty("Subject")
    val subject: String? = null,
    @JsonProperty("Message")
    val message: String? = null,
    @JsonProperty("Timestamp")
    val timestamp: String? = null,
    @JsonProperty("SignatureVersion")
    val signatureVersion: String? = null,
    @JsonProperty("Signature")
    val signature: String? = null,
    @JsonProperty("SigningCertURL")
    val signingCertURL: String? = null,
    @JsonProperty("UnsubscribeURL")
    val unsubscribeURL: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Message(
    @JsonProperty("Records")
    val records: List<S3Record>? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class S3Record(
    val eventVersion: String? = null,
    val eventSource: String? = null,
    val awsRegion: String? = null,
    val eventTime: String? = null,
    val eventName: String? = null,
    val userIdentity: UserIdentity? = null,
    val requestParameters: RequestParameters? = null,
    val responseElements: ResponseElements? = null,
    val s3: S3? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class UserIdentity(
    val principalId: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RequestParameters(
    val sourceIPAddress: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ResponseElements(
    @JsonProperty("x-amz-request-id")
    val requestId: String? = null,
    @JsonProperty("x-amz-id-2")
    val id2: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class S3(
    val s3SchemaVersion: String? = null,
    val configurationId: String? = null,
    val bucket: Bucket? = null,
    val `object`: S3Object? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Bucket(
    val name: String? = null,
    val ownerIdentity: OwnerIdentity? = null,
    val arn: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class OwnerIdentity(
    val principalId: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class S3Object(
    val key: String? = null,
    val size: Long? = null,
    val eTag: String? = null,
    val sequencer: String? = null
)
