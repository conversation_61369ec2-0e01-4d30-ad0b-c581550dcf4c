package com.hellofresh.skudemandforecast.demandImportJob.service

import com.hellofresh.skuDemandForecast.lib.s3.S3FileService
import com.hellofresh.skudemandforecast.demandImportJob.repository.ImportRepository
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.LOCAL_D4
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.SCO
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.fileupload.ProcessFileResult
import com.hellofresh.skudemandforecast.model.fileupload.ViolationHandlersConfig
import com.hellofresh.skudemandforecast.model.fileupload.violations.DuplicateRowsViolationHandler
import java.time.Instant
import org.apache.logging.log4j.kotlin.Logging

const val BUCKET_UK = "hf-bi-dwh-uploader"
const val SCO_S3_PREFIX = "sco_ordering_file"
const val D4_S3_PREFIX = "gb_recipe_forecast"

@Suppress("LongParameterList")
abstract class S3Importer(
    private val s3Service: S3FileService,
    private val prefix: String,
    private val numberOfFiles: Int,
    private val s3BucketPostfix: String,
    private val dcConfigService: DcConfigService,
    private val importRepository: ImportRepository,
    private val fileSource: FileSource,
    private val market: String,
) {
    @get:Synchronized
    @set:Synchronized
    lateinit var lastFileTime: Instant
    private val s3FileProcessor = S3FileProcessor(dcConfigService, market)

    suspend fun fetchLatestFiles(): List<ImportedFile> {
        val allObjects = s3Service
            .fetchObjectSummaries(getBucketForMarket(market) + s3BucketPostfix, prefix)
            .sortedByDescending { it.lastModified }

        val latest = allObjects.take(numberOfFiles)
            .groupBy { it.key }
            .map { (_, files) -> files.maxBy { it.lastModified } }

        return if (!this::lastFileTime.isInitialized) {
            val existingImport = importRepository.fetchLastImportByFileSource(fileSource, market)
            if (existingImport?.fileTimestamp == null) {
                latest
            } else {
                latest.filter { it.lastModified > existingImport.fileTimestamp.toInstant() }
            }
        } else {
            latest.filter { it.lastModified > lastFileTime }
        }.map { (key, lastModified) ->
            val allBytes = s3Service.fetchObjectContent(
                getBucketForMarket(market) + s3BucketPostfix,
                key,
            ).readAllBytes()

            ImportedFile(
                key,
                lastModified,
                s3FileProcessor.processContent(key, allBytes),
            )
        }.also { files ->
            if (files.isNotEmpty()) {
                lastFileTime = files.maxOf { it.fileTimestamp }
                logger.info("S3 Importer $prefix Last File Time: $lastFileTime")
            }
        }
    }

    private fun getBucketForMarket(market: String): String {
        val marketBucketMap: Map<String, String> = mapOf(
            GB_MARKET to BUCKET_UK,
        )
        return marketBucketMap[market] ?: BUCKET_UK
    }

    companion object : Logging {
        val defaultViolationHandlersConfig = ViolationHandlersConfig(
            excludeViolationHandlers = listOf(DuplicateRowsViolationHandler::class),
        )
    }
}

class S3ScoImporter(
    s3Service: S3FileService,
    s3BucketPostfix: String,
    dcConfigService: DcConfigService,
    importRepository: ImportRepository,
) :
    S3Importer(
        s3Service,
        SCO_S3_PREFIX,
        2,
        s3BucketPostfix,
        dcConfigService,
        importRepository,
        SCO,
        GB_MARKET,
    )

class S3D4Importer(
    s3Service: S3FileService,
    s3BucketPostfix: String,
    dcConfigService: DcConfigService,
    importRepository: ImportRepository,
) :
    S3Importer(
        s3Service,
        D4_S3_PREFIX,
        1,
        s3BucketPostfix,
        dcConfigService,
        importRepository,
        LOCAL_D4,
        GB_MARKET,
    )

data class ImportedFile(
    val key: String,
    val fileTimestamp: Instant,
    val processFileResult: ProcessFileResult
)
