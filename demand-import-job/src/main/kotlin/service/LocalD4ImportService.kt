package com.hellofresh.skudemandforecast.demandImportJob.service

import com.hellofresh.cif.slack.SlackClient
import com.hellofresh.cif.slack.SlackMessage
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.lib.s3.S3FileService
import com.hellofresh.skudemandforecast.demandImportJob.repository.DemandRepository
import com.hellofresh.skudemandforecast.demandImportJob.repository.ImportRepository
import com.hellofresh.skudemandforecast.demandImportJob.repository.toDemandRecord
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.LOCAL_D4
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileStatus.CREATED
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileStatus.REJECTED
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import com.hellofresh.skudemandforecast.demandImportJob.service.s3.S3FileDetails
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging

@Suppress("LongMethod", "LongParameterList")
class LocalD4ImportService(
    private val dsl: MetricsDSLContext,
    private val demandRepository: DemandRepository<DemandRecord>,
    private val importRepository: ImportRepository,
    private val slackClient: SlackClient,
    private val slackChannelName: String,
    private val s3FileService: S3FileService,
    private val s3FileProcessor: S3FileProcessor,
) : Logging, ImportService() {
    private val importD4DemandFiles = "import-d4-demand-files"

    suspend fun process(s3FileDetails: S3FileDetails) {
        val scoImportDetails = importRepository.fetchScoImportDetails()
        val scoWeeks = scoImportDetails.flatMap { it.weeks }.toSet()
        kotlin.runCatching {
            val demandFile = s3FileService.fetchObjectContent(s3FileDetails.bucket, s3FileDetails.name)
            val file = ImportedFile(
                s3FileDetails.name,
                s3FileDetails.timestamp,
                s3FileProcessor.processContent(
                    s3FileDetails.name,
                    demandFile.readAllBytes(),
                ),
            )
            if (file.processFileResult.hasSevereViolations()) {
                handleError(file)
                importRepository.insert(
                    dsl,
                    file.toImportMetadata(GB_MARKET).first,
                    LOCAL_D4,
                    REJECTED,
                )
                return
            } else if (file.processFileResult.violations.isNotEmpty()) {
                val message = file.processFileResult.violations.joinToString(
                    ",",
                ) { "${it.lineNumbers} - ${it.message}" }
                logger.error(
                    "LocalD4 service - Importing files with warnings ${file.key} - ${file.fileTimestamp}, because: $message.",
                )
            }

            dsl.withTagName(importD4DemandFiles)
                .transactionAsync { conf ->
                    logger.info("Processing ${file.key} - ${file.fileTimestamp}")

                    val tx = dsl.withMeteredConfiguration(conf)
                    val (import, demandDcWeeks) = file.toImportMetadata(GB_MARKET)
                    val demand = file.processFileResult.parsedFile as ParsedFile.Demand
                    val demandRecords = demand
                        .toDemandRecord(import.id, { DemandRecord() }, DEMAND)
                        .filter { it.week !in scoWeeks }

                    logger.info("Total number of demand records: ${demandRecords.size}")

                    val existingDemandRecords = demandRepository.fetchDemands(
                        tx,
                        demandDcWeeks,
                    ).filter { demandRecord ->
                        demandRecord.week !in scoWeeks
                    }
                    val (newAndModifiedDemands, demandsToBeDeleted) = getDemandsToBeUpsertedAndDeleted(
                        demandRecords,
                        existingDemandRecords,
                    )

                    if (newAndModifiedDemands.isNotEmpty()) {
                        demandRepository.deleteUnMatchedDemands(
                            tx,
                            demandsToBeDeleted = demandsToBeDeleted,
                        )
                        demandRepository.upsertDemands(tx, newAndModifiedDemands)
                        importRepository.insert(tx, import, LOCAL_D4, CREATED)
                    }
                }.await()
        }.onFailure {
            logger.error("Error occurred while processing the D4 demand files", it)
        }.onSuccess {
            logger.info("Successfully imported D4 file: ${s3FileDetails.name}")
        }
    }

    private fun handleError(file: ImportedFile) {
        val message = prepareErrorMessage(file)
        val errorMessage = "LocalD4 service - unable to import file ${file.key} - ${file.fileTimestamp}, because: $message."
        logger.error(errorMessage)
        logger.warn(errorMessage)
        val slackMessage = SlackMessage(
            header = "Unable to import Local D4 file.",
            mainText = "*${file.key} - ${file.fileTimestamp}*",
            detailsText = errorMessage,
        )
        slackClient.sendMessageAsync(slackMessage, slackChannelName)
    }

    companion object : Logging
}
