package com.hellofresh.skudemandforecast.demandImportJob.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.Demand
import com.hellofresh.skuDemandForecast.models.DemandKey
import com.hellofresh.skudemandforecast.demandImportJob.model.DemandDcWeek
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.DEMAND
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB
import org.jooq.Table
import org.jooq.UpdatableRecord
import org.jooq.impl.DSL
import org.jooq.impl.DSL.row

private const val DC_CODE = "dc_code"
private const val COUNTRY = "country"
private const val RECIPE_INDEX = "recipe_index"
private const val WEEK = "week"
private const val PRODUCT_FAMILY = "product_family"
private const val LOCALE = "locale"
private const val DAY = "day"
private const val BRAND = "brand"
private const val PEOPLE_COUNT = "people_count"

interface DemandRepository<R : UpdatableRecord<R>> {
    fun fetchDemand(
        tx: MetricsDSLContext,
        demand: Demand,
    ): R?

    fun fetchDemandForDelete(
        tx: MetricsDSLContext,
        demand: Demand,
    ): List<R>

    fun upsert(
        tx: MetricsDSLContext,
        demand: Demand,
    )

    fun delete(
        tx: MetricsDSLContext,
        demand: List<Demand>,
    )

    fun fetchDemands(
        tx: MetricsDSLContext,
        demandDcWeeks: Set<DemandDcWeek>,
    ): List<R>

    fun fetchDemandsWithKey(
        tx: MetricsDSLContext,
        demandKeys: Set<DemandKey>,
    ): List<R>

    fun upsertDemands(tx: MetricsDSLContext, demands: List<R>)

    fun deleteUnMatchedDemands(
        tx: MetricsDSLContext,
        demandsToBeDeleted: List<R>
    )
}

abstract class BaseDemandRepository<R : UpdatableRecord<R>>(
    private val demandTable: Table<R>
) : DemandRepository<R>, Logging {

    override fun fetchDemand(tx: MetricsDSLContext, demand: Demand): R? {
        val condition = row(
            demandTable.field(RECIPE_INDEX, Int::class.java),
            demandTable.field(WEEK, String::class.java),
            demandTable.field(PRODUCT_FAMILY, String::class.java),
            demandTable.field(COUNTRY, String::class.java),
            demandTable.field(LOCALE, String::class.java),
            demandTable.field(DAY, String::class.java),
            demandTable.field(DC_CODE, String::class.java),
            demandTable.field(PEOPLE_COUNT, Int::class.java),
            demandTable.field(BRAND, String::class.java),
        ).eq(
            demand.recipeIndex,
            demand.week,
            demand.productFamily,
            demand.country,
            demand.locale,
            demand.day,
            demand.dcCode,
            demand.peopleCount,
            demand.brand,
        )
        return tx
            .withTagName("fetch-existing-demand")
            .selectFrom(demandTable)
            .where(DSL.or(condition))
            .fetch().firstOrNull()
    }

    override fun fetchDemandForDelete(tx: MetricsDSLContext, demand: Demand): List<R> {
        val condition = row(
            demandTable.field(RECIPE_INDEX, Int::class.java),
            demandTable.field(WEEK, String::class.java),
            demandTable.field(PRODUCT_FAMILY, String::class.java),
            demandTable.field(COUNTRY, String::class.java),
            demandTable.field(LOCALE, String::class.java),
            demandTable.field(DAY, String::class.java),
            demandTable.field(DC_CODE, String::class.java),
            demandTable.field(BRAND, String::class.java),
        ).eq(
            demand.recipeIndex,
            demand.week,
            demand.productFamily,
            demand.country,
            demand.locale,
            demand.day,
            demand.dcCode,
            demand.brand,
        )
        return tx
            .withTagName("fetch-existing-demand-for-delete")
            .selectFrom(demandTable)
            .where(DSL.or(condition))
            .fetch()
    }

    override fun upsert(tx: MetricsDSLContext, demand: Demand) {
        tx
            .withTagName("upsert-demand")
            .insertInto(demandTable)
            .columns(
                demandTable.field(RECIPE_INDEX, Int::class.java),
                demandTable.field(WEEK, String::class.java),
                demandTable.field(DAY, String::class.java),
                demandTable.field(PRODUCT_FAMILY, String::class.java),
                demandTable.field(COUNTRY, String::class.java),
                demandTable.field(DC_CODE, String::class.java),
                demandTable.field(PEOPLE_COUNT, Int::class.java),
                demandTable.field(LOCALE, String::class.java),
                demandTable.field(BRAND, String::class.java),
                demandTable.field("meals_to_deliver", Int::class.java),
                demandTable.field("demand_details", JSONB::class.java),
                demandTable.field("dirty_bit", Boolean::class.java),
            )
            .values(
                demand.recipeIndex,
                demand.week,
                demand.day,
                demand.productFamily,
                demand.country,
                demand.dcCode,
                demand.peopleCount,
                demand.locale,
                demand.brand,
                demand.mealsToDeliver,
                JSONB.jsonbOrNull(objectMapper.writeValueAsString(demand.demandDetails)),
                true,
            )
            .onConflict(
                demandTable.field(RECIPE_INDEX),
                demandTable.field(WEEK),
                demandTable.field(DAY),
                demandTable.field(PRODUCT_FAMILY),
                demandTable.field(COUNTRY),
                demandTable.field(DC_CODE),
                demandTable.field(PEOPLE_COUNT),
                demandTable.field(LOCALE),
                demandTable.field(BRAND),
            )
            .doUpdate()
            .set(demandTable.field("meals_to_deliver", Int::class.java), demand.mealsToDeliver)
            .set(
                demandTable.field("demand_details", JSONB::class.java),
                JSONB.jsonbOrNull(objectMapper.writeValueAsString(demand.demandDetails)),
            )
            .set(demandTable.field("dirty_bit", Boolean::class.java), true)
            .execute()
    }

    override fun delete(tx: MetricsDSLContext, demandsToBeDeleted: List<Demand>) {
        if (demandsToBeDeleted.isNotEmpty()) {
            logger.info(
                "Number of demand records to be deleted = ${demandsToBeDeleted.size}",
            )
            val conditions = demandsToBeDeleted.map { demand ->
                row(
                    demandTable.field(RECIPE_INDEX, Int::class.java),
                    demandTable.field(WEEK, String::class.java),
                    demandTable.field(PRODUCT_FAMILY, String::class.java),
                    demandTable.field(COUNTRY, String::class.java),
                    demandTable.field(LOCALE, String::class.java),
                    demandTable.field(DAY, String::class.java),
                    demandTable.field(DC_CODE, String::class.java),
                    demandTable.field(BRAND, String::class.java),
                ).eq(
                    demand.recipeIndex,
                    demand.week,
                    demand.productFamily,
                    demand.country,
                    demand.locale,
                    demand.day,
                    demand.dcCode,
                    demand.brand,
                )
            }
            tx
                .withTagName("delete-demand")
                .deleteFrom(demandTable)
                .where(conditions)
                .execute()
        }
    }

    override fun fetchDemands(
        tx: MetricsDSLContext,
        demandDcWeeks: Set<DemandDcWeek>,
    ): List<R> {
        val conditions = demandDcWeeks.map { demandDcWeek ->
            row(
                demandTable.field(DC_CODE, String::class.java),
                demandTable.field("week", String::class.java),
            ).eq(
                demandDcWeek.dcCode,
                demandDcWeek.week,
            )
        }

        return tx
            .withTagName("fetch-imported-demands")
            .selectFrom(demandTable)
            .where(DSL.or(conditions))
            .fetch()
    }

    override fun fetchDemandsWithKey(
        tx: MetricsDSLContext,
        demandKeys: Set<DemandKey>,
    ): List<R> {
        val conditions = demandKeys.map { demandDcWeek ->
            row(
                demandTable.field("recipe_index", Int::class.java),
                demandTable.field("week", String::class.java),
                demandTable.field("product_family", String::class.java),
                demandTable.field("country", String::class.java),
                demandTable.field("locale", String::class.java),
                demandTable.field("day", String::class.java),
                demandTable.field(DC_CODE, String::class.java),
                demandTable.field("people_count", Int::class.java),
                demandTable.field("brand", String::class.java),
            ).eq(
                demandDcWeek.recipeIndex,
                demandDcWeek.week,
                demandDcWeek.productFamily,
                demandDcWeek.country,
                demandDcWeek.locale,
                demandDcWeek.day,
                demandDcWeek.dcCode,
                demandDcWeek.peopleCount,
                demandDcWeek.brand,
            )
        }

        return tx
            .withTagName("fetch-imported-demands-with-key")
            .selectFrom(demandTable)
            .where(DSL.or(conditions))
            .fetch()
    }

    override fun upsertDemands(tx: MetricsDSLContext, demands: List<R>) {
        if (demands.isNotEmpty()) {
            logger.info("Number of imported demand records to be merged = ${demands.size}")
            tx.withTagName("upserting-new-imported-demands")
                .batchMerge(demands)
                .execute()
        }
    }

    override fun deleteUnMatchedDemands(
        tx: MetricsDSLContext,
        demandsToBeDeleted: List<R>
    ) {
        if (demandsToBeDeleted.isNotEmpty()) {
            logger.info(
                "Number of demand records to be deleted = ${demandsToBeDeleted.size}",
            )
            tx.withTagName("batch-delete-for-imported-demands").batchDelete(demandsToBeDeleted).execute()
        }
    }

    companion object : Logging {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
    }
}

class DemandRepositoryImpl : BaseDemandRepository<DemandRecord>(DEMAND)
