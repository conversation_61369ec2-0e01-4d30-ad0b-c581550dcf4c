package com.hellofresh.skudemandforecast.demandImportJob.repository

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.demandImportJob.model.Import
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.Tables.IMPORTS
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource as DbFileSource
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.SCO
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileStatus as DbFileStatus
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileType
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.ImportsRecord
import com.hellofresh.skudemandforecast.model.fileupload.FileSource
import kotlinx.coroutines.future.await

interface ImportRepository {
    fun insert(
        tx: MetricsDSLContext,
        details: Import,
        fileSource: DbFileSource,
        status: DbFileStatus
    )
    suspend fun fetchScoImportDetails(): List<Import>
    suspend fun fetchLastImportByFileSource(fileSource: DbFileSource, market: String): Import?
}

class ImportRepositoryJooqImpl(private val dslContext: MetricsDSLContext) : ImportRepository {
    override fun insert(
        tx: MetricsDSLContext,
        details: Import,
        fileSource: DbFileSource,
        status: DbFileStatus
    ) {
        tx.batchInsert(details.toImportRecord(fileSource, status)).execute()
    }

    override suspend fun fetchScoImportDetails(): List<Import> =
        dslContext.withTagName("fetch-sco-imports-details")
            .selectFrom(IMPORTS)
            .where(
                IMPORTS.SOURCE.eq(SCO),
            )
            .fetchAsync()
            .thenApply { result ->
                result.map { it.toImport() }
            }
            .await()

    override suspend fun fetchLastImportByFileSource(fileSource: DbFileSource, market: String): Import? =
        dslContext.withTagName("fetch-latest-import-details")
            .selectFrom(IMPORTS)
            .where(IMPORTS.SOURCE.eq(fileSource))
            .and(IMPORTS.MARKET.eq(market))
            .and(IMPORTS.FILE_TIMESTAMP.isNotNull)
            .orderBy(IMPORTS.CREATED_AT.desc())
            .limit(1)
            .fetchAsync()
            .thenApply { result ->
                result.map { it.toImport() }.firstOrNull()
            }
            .await()
}

fun Import.toImportRecord(fileSource: DbFileSource, status: DbFileStatus): ImportsRecord = ImportsRecord().apply {
    id = <EMAIL>
    dcs = <EMAIL>()
    weeks = <EMAIL>()
    name = <EMAIL>
    errors = <EMAIL>()
    source = fileSource
    type = FileType.DEMAND
    this.status = status
    market = <EMAIL>
    errors = <EMAIL>()
    fileTimestamp = <EMAIL>
}

fun ImportsRecord.toImport() =
    Import(
        id = this.id,
        name = this.name,
        dcs = this.dcs.toSet(),
        weeks = this.weeks.toSet(),
        market = this.market,
        source = FileSource.valueOf(this.source.name),
        type = com.hellofresh.skudemandforecast.model.fileupload.FileType.valueOf(this.type.name),
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        errors = this.errors.toList(),
        fileTimestamp = this.fileTimestamp
    )
