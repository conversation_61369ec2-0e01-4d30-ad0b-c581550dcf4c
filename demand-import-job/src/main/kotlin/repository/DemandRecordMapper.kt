package com.hellofresh.skudemandforecast.demandImportJob.repository

import com.hellofresh.skuDemandForecast.models.DemandKey
import com.hellofresh.skudemandforecast.model.fileupload.ParsedFile.Demand
import java.util.UUID
import org.jooq.Table
import org.jooq.UpdatableRecord

private const val NI_LOCALE = "NI"
private const val GB_LOCALE = "GB"

fun <R : UpdatableRecord<R>> Demand.toDemandRecord(
    sourceId: UUID,
    recordFactory: () -> R,
    table: Table<R>,
): List<R> = this.data.map {
    if (it.optional[Demand.LOCALE_HEADER] == NI_LOCALE) {
        it.copy(optional = it.optional.toMutableMap().apply { put(Demand.LOCALE_HEADER, GB_LOCALE) }.toMap())
    } else {
        it
    }
}.groupBy {
    DemandKey(
        recipeIndex = it[Demand.MEAL_NUMBER_HEADER].toDouble().toInt(),
        week = it[Demand.WEEK_HEADER],
        productFamily = it[Demand.PRODUCT_FAMILY_HEADER],
        country = it[Demand.COUNTRY_HEADER],
        locale = it.optional[Demand.LOCALE_HEADER],
        day = it[Demand.DAY_HEADER],
        dcCode = it[Demand.DC_HEADER],
        peopleCount = it[Demand.SIZE_HEADER].toDouble().toInt(),
        brand = "UNKNOWN",
    )
}.map { (key, rows) ->
    recordFactory().apply {
        set(table.field("dirty_bit", Boolean::class.java), true)
        set(table.field("recipe_index", Int::class.java), key.recipeIndex)
        set(table.field("product_family", String::class.java), key.productFamily)
        set(table.field("week", String::class.java), key.week)
        set(table.field("country", String::class.java), key.country)
        set(table.field("locale", String::class.java), key.locale ?: "")
        set(table.field("day", String::class.java), key.day)
        set(table.field("dc_code", String::class.java), key.dcCode)
        set(table.field("people_count", Int::class.java), key.peopleCount)
        set(
            table.field("meals_to_deliver", Int::class.java),
            rows.sumOf { it[Demand.MEALS_TO_DELIVER_HEADER].toDouble().toInt() }
        )
        set(table.field("source_id", UUID::class.java), sourceId)
        set(table.field("brand", String::class.java), key.brand)
    }
}
