package com.hellofresh.skudemandforecast.demandImportJob.service

import com.hellofresh.skudemandforecast.demandImportJob.service.s3.S3NotificationMessageParser
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class S3NotificationMessageParserTest {

    private val parser = S3NotificationMessageParser()

    @ParameterizedTest
    @MethodSource("provideNordicsDemandFileSQSMessage")
    fun `test getFileDetails returns correct file name`(s3EventSQSMessage: String, expectedFileName: String) {
        val result = parser.getS3FileDetails(s3EventSQSMessage)
        assertEquals(expectedFileName, result.name)
    }

    companion object {
        @JvmStatic
        fun provideNordicsDemandFileSQSMessage(): Stream<Arguments> {
            val json1 = """
                {
                  "Type": "Notification",
                  "MessageId": "d19c0e83-23d4-51c6-a425-51411c62fe3e",
                  "TopicArn": "arn:aws:sns:eu-west-1:1234567:hf_nordics_ip_recipe_forecast_topic_staging",
                  "Subject": "Amazon S3 Notification",
                  "Message": "{\"Records\":[{\"eventVersion\":\"2.1\",\"eventSource\":\"aws:s3\",\"awsRegion\":\"eu-west-1\",\"eventTime\":\"2024-11-28T09:52:54.118Z\",\"eventName\":\"ObjectCreated:Put\",\"userIdentity\":{\"principalId\":\"AWS:AROA6K4FMOAP7ZHSXRYTK:<EMAIL>\"},\"requestParameters\":{\"sourceIPAddress\":\"************\"},\"responseElements\":{\"x-amz-request-id\":\"231PJB98HBNVNCTA\",\"x-amz-id-2\":\"Z+8FMVa00UspSC3T74KzlniWQ993Dq3p8537Cpa/gVQPMPp4fyzMddjkSCTHEbdxQ/BlbDE23QjHTVDutc1AWQShiOlEkbNN\"},\"s3\":{\"s3SchemaVersion\":\"1.0\",\"configurationId\":\"tf-s3-topic-20241111144949559700000005\",\"bucket\":{\"name\":\"hf-nordics-staging\",\"ownerIdentity\":{\"principalId\":\"A13H6PT4GJ3OU5\"},\"arn\":\"arn:aws:s3:::hf-nordics-staging\"},\"object\":{\"key\":\"standardized/forecasting/inventory_planner_recipe_forecast/demand_2025-W03.csv\",\"size\":152,\"eTag\":\"003c5887d4fe9e70aa7f896338b26e9d\",\"sequencer\":\"0067483D7619755520\"}}}]}",
                  "Timestamp": "2024-11-28T09:52:54.536Z",
                  "SignatureVersion": "1",
                  "Signature": "QihrXGlp2F5x5zxQJz4bzKc82Kx8zS6XFV6aY0ic5mQPk7HbUEd5knx/SjmkoJJLpn3NyRqURUoM4KDJvyWZaogdG0Y3HKj7UOLUEoGe/OjiRs+sHPW7qw9/a2X89QSwv1JxZyFfXwGkg6SKFrYWOvroHZI3zjn0zIByrHPeizUSNNBXwHVtn42hAE0tQtRVwJnjFL+GAooXSdB2TwF+h4W/C0of/Ig/6GJrDUHjkBIXi3IhfXxV1QtOei9PYNnriYusSR4ZxRUfPrYg9WpBvaQ5g58W08QH53XtE+Mr23XF5wdgcPFNrZwzJEIb1Paasi2m8epJTkaxHRNDNcq9nw==",
                  "SigningCertURL": "https://sns.eu-west-1.amazonaws.com/SimpleNotificationService-9c6465fa7f48f5cacd23014631ec1136.pem",
                  "UnsubscribeURL": "https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:1234567:hf_nordics_ip_recipe_forecast_topic_staging:edfe03e0-7210-47b2-8901-eab96e8273e1"
                }
            """.trimIndent()

            val json2 = """
                {
                  "Type": "Notification",
                  "MessageId": "d19c0e83-23d4-51c6-a425-51411c62fe3e",
                  "TopicArn": "arn:aws:sns:eu-west-1:1234567:hf_nordics_ip_recipe_forecast_topic_staging",
                  "Subject": "Amazon S3 Notification",
                  "Timestamp": "2024-11-28T09:52:54.536Z",
                  "SignatureVersion": "1",
                  "Signature": "QihrXGlp2F5x5zxQJz4bzKc82Kx8zS6XFV6aY0ic5mQPk7HbUEd5knx/SjmkoJJLpn3NyRqURUoM4KDJvyWZaogdG0Y3HKj7UOLUEoGe/OjiRs+sHPW7qw9/a2X89QSwv1JxZyFfXwGkg6SKFrYWOvroHZI3zjn0zIByrHPeizUSNNBXwHVtn42hAE0tQtRVwJnjFL+GAooXSdB2TwF+h4W/C0of/Ig/6GJrDUHjkBIXi3IhfXxV1QtOei9PYNnriYusSR4ZxRUfPrYg9WpBvaQ5g58W08QH53XtE+Mr23XF5wdgcPFNrZwzJEIb1Paasi2m8epJTkaxHRNDNcq9nw==",
                  "SigningCertURL": "https://sns.eu-west-1.amazonaws.com/SimpleNotificationService-9c6465fa7f48f5cacd23014631ec1136.pem",
                  "UnsubscribeURL": "https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:1234567:hf_nordics_ip_recipe_forecast_topic_staging:edfe03e0-7210-47b2-8901-eab96e8273e1"
                }
            """.trimIndent()

            return Stream.of(
                Arguments.of(json1, "standardized/forecasting/inventory_planner_recipe_forecast/demand_2025-W03.csv"),
                Arguments.of(json2, "")
            )
        }
    }
}
