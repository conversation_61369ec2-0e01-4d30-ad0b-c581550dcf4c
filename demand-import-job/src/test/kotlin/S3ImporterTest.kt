package com.hellofresh.skudemandforecast.demandImportJob.service

import com.hellofresh.skuDemandForecast.lib.s3.FileObject
import com.hellofresh.skuDemandForecast.lib.s3.S3FileService
import com.hellofresh.skudemandforecast.demandImportJob.model.Import
import com.hellofresh.skudemandforecast.demandImportJob.repository.ImportRepository
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.LOCAL_D4
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.enums.FileSource.SCO
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.model.fileupload.FileSource
import com.hellofresh.skudemandforecast.model.fileupload.FileType
import io.mockk.coEvery
import io.mockk.mockk
import java.io.InputStream
import java.time.Instant
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking

class S3ImporterTest {

    private val dcConfigService = mockk<DcConfigService>(relaxed = true)
    private val importRepository = mockk<ImportRepository>(relaxed = true)

    @Test fun `fetchLatestFiles sort by timestamp`() {
        val mockS3Service = mockk<S3FileService>()
        val prefix = "test"
        val numberOfFiles = 3
        val testObject = object : S3Importer(
            mockS3Service,
            prefix,
            numberOfFiles,
            "",
            dcConfigService,
            importRepository,
            LOCAL_D4,
            GB_MARKET,
        ) {}

        val mockResponse = (0..100).map {
            FileObject(UUID.randomUUID().toString(), Instant.now().plusSeconds(it.toLong()))
        }

        coEvery { mockS3Service.fetchObjectSummaries(any(), any()) } returns mockResponse
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns InputStream.nullInputStream()

        runBlocking {
            val actual = testObject.fetchLatestFiles()
            assertTrue { testObject.lastFileTime == mockResponse.maxOf { it.lastModified } }
            assertEquals(numberOfFiles, actual.size)

            val newFileObject = FileObject(UUID.randomUUID().toString(), Instant.now().plusSeconds(10000))
            coEvery { mockS3Service.fetchObjectSummaries(any(), any()) } returns
                mockResponse + newFileObject

            // only 1 file has timestamp greater than the lastRuntime
            val latestFiles = testObject.fetchLatestFiles()
            assertEquals(1, latestFiles.size)
            assertEquals(newFileObject.key, latestFiles.first().key)
            assertEquals(newFileObject.lastModified, latestFiles.first().fileTimestamp)
            assertTrue { testObject.lastFileTime == latestFiles.first().fileTimestamp }
            assertEquals(newFileObject.lastModified, latestFiles.first().fileTimestamp)
        }
    }

    @Test fun `fetchLatestFiles returns latest file with the timestamp`() {
        val mockS3Service = mockk<S3FileService>()
        val prefix = "test"
        val numberOfFiles = 3
        val testObject = object : S3Importer(
            mockS3Service,
            prefix,
            numberOfFiles,
            "",
            dcConfigService,
            importRepository,
            LOCAL_D4,
            GB_MARKET,
        ) {}
        val name = UUID.randomUUID().toString()
        val latestTime = Instant.now().plusSeconds(100L)
        val mockResponse = (0..100).map {
            FileObject(name, Instant.now().minusSeconds(it.toLong()))
        } + listOf(FileObject(name, latestTime))

        coEvery { mockS3Service.fetchObjectSummaries(any(), any()) } returns mockResponse
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns InputStream.nullInputStream()

        runBlocking {
            val actual = testObject.fetchLatestFiles()
            assertEquals(1, actual.size)
            assertEquals(latestTime, actual.first().fileTimestamp)
        }
    }

    @Test fun `fetchLatestFiles filters based on last processed file timestamp`() {
        val mockS3Service = mockk<S3FileService>()
        val prefix = "test"
        val numberOfFiles = 4
        val testObject = object : S3Importer(
            mockS3Service,
            prefix,
            numberOfFiles,
            "",
            dcConfigService,
            importRepository,
            LOCAL_D4,
            GB_MARKET,
        ) {}

        val initialLastFileTime = Instant.now().minusSeconds(200)
        testObject.lastFileTime = initialLastFileTime

        val mockResponse = (0..1).map {
            FileObject(UUID.randomUUID().toString(), Instant.now().minusSeconds(3600))
        }

        val existingImport = Import(
            id = UUID.randomUUID(),
            name = "test",
            dcs = setOf("dc1"),
            market = "market1",
            weeks = setOf("week1"),
            source = FileSource.SCO,
            type = FileType.DEMAND,
            errors = listOf(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            fileTimestamp = OffsetDateTime.now(),
        )

        coEvery { mockS3Service.fetchObjectSummaries(any(), any()) } returns mockResponse
        coEvery { mockS3Service.fetchObjectContent(any(), any()) } returns InputStream.nullInputStream()
        coEvery { importRepository.fetchLastImportByFileSource(SCO, "GB") } returns existingImport

        runBlocking {
            val actual = testObject.fetchLatestFiles()
            val filterTimestamp = existingImport.fileTimestamp!!.toInstant()
            val expectedFiles = mockResponse.filter { it.lastModified > filterTimestamp }

            assertEquals(expectedFiles.size, actual.size)
            assertTrue { actual.all { it.fileTimestamp > filterTimestamp } }

            // newer files than the ones in imports table
            val mockResponseWithNewerFiles = (0..1).map {
                FileObject(UUID.randomUUID().toString(), Instant.now().plusSeconds(3600))
            }
            val mockResponseWithOlderFiles = (0..1).map {
                FileObject(UUID.randomUUID().toString(), Instant.now().minusSeconds(3600))
            }
            coEvery { mockS3Service.fetchObjectSummaries(any(), any()) } returns
                mockResponseWithOlderFiles + mockResponseWithNewerFiles

            val newActual = testObject.fetchLatestFiles()
            assertEquals(2, newActual.size) // only return the newer files which has timestamp more that the last import
        }
    }
}
