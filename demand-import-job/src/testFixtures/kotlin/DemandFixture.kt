import com.hellofresh.skuDemandForecast.models.Demand
import com.hellofresh.skudemandforecast.demandImportJob.schema.public_.tables.records.DemandRecord

fun DemandRecord.toDemand() = Demand(
    recipeIndex = recipeIndex,
    week = week,
    country = country,
    peopleCount = peopleCount,
    brand = brand,
    dcCode = dcCode,
    day = day.uppercase(),
    mealsToDeliver = mealsToDeliver,
    productFamily = productFamily,
    locale = locale,
    sourceId = null,
    demandDetails = null,
)
