from datetime import datetime

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import SiteConfigsCA
from automated_tests.data.models.supplier import TestSupplier
from automated_tests.data.test_data import TestData
from automated_tests.services.kafka.kafka_tester import KafkaTester
from automated_tests.services.kafka.supplier_sync.supplier_kafka import KafkaSupplierTopic, SupplierKafkaDataForProduce
from procurement.data.models.ordering.supplier import SupplierModel
from procurement.managers.kafka_utils import AVRO_TIME_FORMAT


@pytest.mark.skip("Unkip when secrets will be provided GD-5661")
def test_supplier_kafka_topic():
    """
    Test Supplier Kafka topic.
    IF market != CA or US then message should be ignored

    Test steps:
    1.Generate required data
    2.Send message to kafka topic (2 messages)
    3.Check that data in supplier are equal to data from set up
    """
    site_config = SiteConfigsCA.random_value()
    supplier = TestSupplier.generate_supplier()
    supplier_kafka_data = SupplierKafkaDataForProduce.generate_supplier_message(
        supplier=supplier, site_config=site_config
    )
    supplier_kafka_data_random_market = SupplierKafkaDataForProduce.generate_supplier_message(
        supplier=supplier, site_config=site_config, market=data_generator.generate_string()
    )
    with TestData.root_data_canada():
        supplier_data = KafkaTester(KafkaSupplierTopic, [SupplierModel]).publish_and_read(
            data=[supplier_kafka_data, supplier_kafka_data_random_market]
        )
        assert len(supplier_data["supplier"]) == 1
        for row in supplier_data["supplier"]:
            assert row.id == supplier_kafka_data.id
            assert row.name == supplier_kafka_data.name
            assert row.code == supplier_kafka_data.code
            assert row.legal_name == supplier_kafka_data.legal_name
            assert row.market == supplier_kafka_data.market
            assert row.last_updated == datetime.strptime(supplier_kafka_data.updated_at, AVRO_TIME_FORMAT)
