from decimal import Decimal

from automated_tests.data.constants.base_constants import SiteConfigsCA
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_acknowledgement import TestPOAcknowledgementLineItems
from automated_tests.data.test_data import TestData
from automated_tests.services.kafka.kafka_tester import KafkaTester
from automated_tests.services.kafka.po_acknowledgement_sync.po_acknowledgement_kafka import (
    PoAcknowledgementKafkaDataForProduce,
    PoAcknowledgementKafkaTopic,
)
from procurement.constants.hellofresh_constant import PoAckUnitOfMeasure
from procurement.data.models.ordering.po_acknowledgement import POAcknowledgementLineItemsModel


def test_po_acknowledgement_kafka_topic():
    """
    Test PO Acknowledgement kafka topic.

    Test steps:
    1.Generate required data
    2.Send message to kafka topic
    3.Check that data in po_acknowledgement is equal to data from the setup
    """
    site_config = SiteConfigsCA.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(po=po)
    po_ack_kafka_data = PoAcknowledgementKafkaDataForProduce.generate_po_acknowledge_message(
        po_acknowledgement=po_acknowledgement
    )
    with TestData.root_data_canada():
        po_acknowledgement_data = KafkaTester(
            PoAcknowledgementKafkaTopic, [POAcknowledgementLineItemsModel]
        ).publish_and_read(data=[po_ack_kafka_data])
        assert len(po_acknowledgement_data["po_acknowledgement_line_items"]) == 1
        for row in po_acknowledgement_data["po_acknowledgement_line_items"]:
            assert row.po_uuid == po_ack_kafka_data.purchase_order_id
            assert row.order_number == po_ack_kafka_data.purchase_order_number
            assert row.sku_code == po_ack_kafka_data.sku_code
            assert row.sku_id == po_acknowledgement.first_line().sku.sku_id
            assert row.state == po_ack_kafka_data.state
            assert row.number_of_pallets == po_ack_kafka_data.pallets_quantity
            assert row.unit_of_measure == PoAckUnitOfMeasure.CASE
            assert row.size == po_ack_kafka_data.order_size
            assert row.packing_size == Decimal(po_ack_kafka_data.case_packaging_size)
            assert row.promised_date == po_ack_kafka_data.promised_time
