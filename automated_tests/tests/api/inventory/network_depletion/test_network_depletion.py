import dataclasses
import random
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from http import HTTPStatus

import pytest
import pytz

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    Brands,
    <PERSON>OfWeek,
    EmergencyReason,
    MultiMarketUsers,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsCA,
    SiteConfigsFactor,
    Sites,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_14,
    DAY_FORMAT,
    PREVIOUS_WEEK,
    TOMORROW_DATE,
    TOMORROW_DATETIME,
    US_EASTERN_TIMEZONE,
    YESTERDAY_DATE,
    YESTERDAY_DATETIME,
)
from automated_tests.data.constants.error_messages_constants import (
    REQUEST_IS_INVALID,
    SITES_WITH_DIFFERENT_WEEK_CONFIGS_CANNOT_BE_SELECTED,
)
from automated_tests.data.constants.ing_depletion_aggregated_view_constants import (
    CRITICAL_DELIVERY_PRIORITY_MAPPING,
    SUPPLEMENT_NEED_PRIORITY_MAPPING,
)
from automated_tests.data.models.allocation_price import TestAllocationPrice
from automated_tests.data.models.allowed_produce_buffer import TestAllowedProduceBuffer
from automated_tests.data.models.bulk_sku import TestBulkSkus
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.highjump import TestHJDiscard, TestHJPalletSnapshot, TestHJReceipts, TestHJWip
from automated_tests.data.models.hybrid_needs import TestHybridNeeds, TestHybridNeedsStatus
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.manufactured_sku import TestManufacturedSku, TestManufacturedSkuPart
from automated_tests.data.models.network_depletion_preset import TestNetworkDepletionPreset
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.raw_highjump.hj_autostore import TestHjAutostore
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.staged_inventory import TestStagedInventory
from automated_tests.data.models.work_order import TestWorkOrder
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import (
    aggregated_and_network_depletion_steps,
    ingredient_depletion_steps,
    po_status_steps,
)
from automated_tests.services.api.inventory.network_depletion_client import NetworkDepletionModuleClient
from automated_tests.utils import comparisons, datetime_utils
from procurement.constants.hellofresh_constant import MARKET_CA, MARKET_US, ReceiveInputType, WhReceivingType
from procurement.constants.ordering import CLOSED, IN_PROGRESS_HJ
from procurement.data.models.highjump.highjump import DiscardType


@pytest.mark.parametrize(
    "site_configs, warehouses",
    [
        ([SiteConfigs.NJ_HF.value], None),
        ([SiteConfigsFactor.GA_FJ.value], None),
        ([SiteConfigs.NJ_HF.value, SiteConfigs.CT_EP.value], None),
        ([SiteConfigs.NJ_HF.value], [Warehouses.SOUTHWEST.value]),
        (None, [Warehouses.SOUTHWEST.value, Warehouses.CASTELLINI.value]),
    ],
)
def test_preset_creation(site_configs, warehouses):
    """
    Test that preset can be saved

    Test steps:
    1. Generate required test data (site configs with different brands, warehouses if needed,
                                    data for network depletion board)
    2. Send a GET request to the network_depletion_endpoint -> 200, no records expected
    3. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    4. Send a GET request to the presets_endpoint -> 200
    5. Assert returned preset with passed data
    """
    all_site_configs = [SiteConfigs.NJ_HF.value, SiteConfigs.CT_EP.value, SiteConfigsFactor.GA_FJ.value]

    preset_id = 1
    preset_new_name = "New name"
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in all_site_configs))
        .with_sites(*(site_config.site for site_config in all_site_configs))
        .with_site_configs(*all_site_configs)
        .with_users(Users.test_user.value)
        .with_warehouses(Warehouses.SOUTHWEST.value, Warehouses.CASTELLINI.value)
    ):
        res = NetworkDepletionModuleClient().edit_preset(preset_id, preset_new_name, site_configs, warehouses)
        assert res.status_code == HTTPStatus.CREATED

        preset_form_res = NetworkDepletionModuleClient().get_preset_form(CURRENT_WEEK)
        res_data = {
            preset["presetId"]: preset
            for preset in base_steps.process_response_and_extract_data(preset_form_res)["presets"]
        }
        if site_configs:
            for site_config in site_configs:
                assert res_data[preset_id]["brands"][site_config.brand.code] == [site_config.site.code]
        if warehouses:
            assert res_data[preset_id]["warehouses"] == [warehouse.code for warehouse in warehouses]
        assert res_data[preset_id]["presetName"] == preset_new_name


def test_preset_creation_with_id_more_than_3():
    """
    Test that preset can't be saved with preset id more than 3

    Test steps:
    1. Generate required test data (site config, warehouse)
    2. Send a POST request to the presets_endpoint with preset_id=4, name, site_configs, warehouses-> 400
    3. Assert error msg
    """
    site_config = SiteConfigs.NJ_HF.value
    sku = TestSku.generate_sku()
    warehouse = Warehouses.CASTELLINI.value
    preset_id = 4
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_warehouses(warehouse)
    ):
        res = NetworkDepletionModuleClient().edit_preset(preset_id, "Preset 5", [site_config], [warehouse])
        assert res.status_code == HTTPStatus.BAD_REQUEST
        assert res.json()["msg"] == REQUEST_IS_INVALID


def test_preset_creation_with_different_week_configs():
    """
    Test that preset can not be saved

    Test steps:
    1. Generate required test data (created 2 site configs with two different brands, data for network depletion board)
    2. Send a GET request to the network_depletion_endpoint -> 200, no records expected
    3. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 400
    4. Assert returned error
    """
    site_configs = [SiteConfigs.CT_EP.value, SiteConfigsFactor.GA_FJ.value]
    preset_id = 1
    preset_new_name = "New name"
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(site_config.site for site_config in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
    ):
        res = NetworkDepletionModuleClient().edit_preset(preset_id, preset_new_name, site_configs)
        assert res.status_code == HTTPStatus.BAD_REQUEST
        assert res.json()["error"] == SITES_WITH_DIFFERENT_WEEK_CONFIGS_CANNOT_BE_SELECTED


def test_ingredient_summary():
    """
    Test values in Ingredient Summary section. All values choose any (next) value from child Skus,
    except 'Impacted Recipes', 'Brands' and 'Sites'.

    Test steps:
    1. Generate required test data (created 2 site configs, 2 warehouses, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    3. Send a GET request to the network_depletion_endpoint -> 200
    4. Check if the data matches the expected values from the response
    5. Check Sku name comment on NDM
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(SiteConfigs.CT_EP.value, week=PREVIOUS_WEEK)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    warehouses = Warehouses.random_non_repetitive_values(2)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    inventories = [
        TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse) for warehouse in warehouses
    ]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    commodity_group = data_generator.generate_string()
    buyer_skus = [TestBuyerSku.generate_buyer_sku(sku=sku, site_config=site_config) for site_config in site_configs]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=warehouses)
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    comments = [data_generator.generate_string() for _ in range(2)]
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_purchasing_categories(purchasing_category)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_commodity_group(commodity_group, site_config_hf.site.code, sku)
        .with_buyer_sku(*buyer_skus)
        .with_purchase_orders(po)
        .with_warehouses(*warehouses)
        .with_pimt_inventory(*inventories)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(*oscars)
    ):
        network_depletion_res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(
            response=network_depletion_res, expected_length=1, element_index=0
        )
        ing_depl_data = data["childItems"]
        assert data["brand"] == ", ".join(
            sorted(set([child["brand"] for child in ing_depl_data if child.get("brand")]))
        )
        assert data["site"] == ", ".join(sorted(set([child["site"] for child in ing_depl_data])))
        assert data["sku"] == sku.sku_code
        assert data["skuName"] == sku.sku_name
        assert data["category"] == purchasing_category.ingredient_category
        assert data["commodityGroup"] == commodity_group
        assert data["buyer"] == next(buyer_sku.user.full_name for buyer_sku in buyer_skus)
        assert data["unitOfMeasure"] == sku.unit
        assert data["impactedRecipes"] == ", ".join(sorted(set(mealkit.slot for mealkit in mealkits)))
        for comment in comments:
            post_request = NetworkDepletionModuleClient().add_or_edit_sku_comment_on_ndm(
                sku=sku, preset_id=preset.id, comment=comment
            )
            assert post_request.status_code == HTTPStatus.CREATED

            get_request = NetworkDepletionModuleClient().get_sku_comment_on_ndm(sku=sku, preset_id=preset.id)
            actual_data = base_steps.process_response_and_extract_data(response=get_request, expected_length=3)
            actual_updated_at = datetime.strptime(actual_data["lastUpdated"], DATE_TIME_FORMAT_1)
            expected_updated_at = datetime_utils.get_datetime_for_specific_timezone(
                timezone=pytz.timezone(US_EASTERN_TIMEZONE)
            )
            assert comparisons.datetime_is_close(actual_updated_at, expected_updated_at, deviation=timedelta(minutes=1))
            assert actual_data["lastUpdatedBy"] == Users.test_user.value.email
            assert actual_data["value"] == comment

        delete_request = NetworkDepletionModuleClient().delete_sku_comment_on_ndm(sku=sku, preset_id=preset.id)
        assert delete_request.status_code == HTTPStatus.NO_CONTENT


def test_units_needed_section():
    """
    Test values in Units Needed section. All values in this calculate as sum of child skus, except "delta" value,
    its calculate same as on Ing Depl.

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, hj_name="HF01", week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(
        site_config_hf, brand=Brands.EP.value, hj_name="HF11", site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    hybrid_needs = [
        TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=config) for config in site_configs
    ]
    hybrid_need_statuses = [
        TestHybridNeedsStatus.generate_hybrid_needs_statuses(site_config=config) for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_hybrid_needs(*(item for sublist in hybrid_needs for item in sublist))
        .with_oscar_forecast(*oscars)
        .with_hybrid_needs_status(*(item for sublist in hybrid_need_statuses for item in sublist))
        .with_network_depletion_presets(preset)
    ):
        network_depletion_res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(
            response=network_depletion_res, expected_length=1, element_index=0
        )
        ing_depl_data = data["childItems"]

        sum_hybrid_needs_quantity = sum([hybrid_need.quantity for sublist in hybrid_needs for hybrid_need in sublist])
        sum_oscar = sum(oscar.value for oscar in oscars)

        assert data["plan"] == sum(child["plan"] for child in ing_depl_data if child.get("brand"))
        assert data["plannedProduction"] == sum(
            child["plannedProduction"] for child in ing_depl_data if child.get("brand")
        )
        assert data["rowForecast"] == sum(child["rowForecast"] for child in ing_depl_data if child.get("brand"))
        assert data["forecastOscar"] == sum(child["forecastOscar"] for child in ing_depl_data if child.get("brand"))
        assert data["delta"] == round((1 - float(sum_oscar) / sum_hybrid_needs_quantity), 4)


def test_status_section():
    """
    Test values in Units Needed section. In the 'supplement needs' and 'supplement needs hj' field should be
    the earliest weekday from children rows or no impact. In the 'critical_delivery' next priority ->
    production will stop, scheduled delivery necessary, no impact.

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(
        site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=config) for config in site_configs]
    hybrid_needs_hf = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config_hf)
    hybrid_needs_ep_next_day = TestHybridNeeds.generate_hybrid_needs(
        sku=sku, site_config=site_config_ep, day=TOMORROW_DATETIME
    )
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_oscar_forecast(*(item for sublist in oscars for item in sublist))
        .with_hybrid_needs(hybrid_needs_hf, hybrid_needs_ep_next_day)
        .with_network_depletion_presets(preset)
    ):
        network_depletion_res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(
            response=network_depletion_res, expected_length=1, element_index=0
        )
        ing_depl_data = data["childItems"]

        status_fields_with_priority_mapping = {
            "sn": SUPPLEMENT_NEED_PRIORITY_MAPPING,
            "supplementNeedHj": SUPPLEMENT_NEED_PRIORITY_MAPPING,
            "criticalDelivery": CRITICAL_DELIVERY_PRIORITY_MAPPING,
        }
        for field_name, priority_mapping in status_fields_with_priority_mapping.items():
            expected_status = aggregated_and_network_depletion_steps.get_expected_status_according_to_priority(
                children_liners=ing_depl_data, mapping=priority_mapping, field_name=field_name
            )
            assert data[field_name] == expected_status, f"incorrect status in {field_name}"


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_weekly_overview_section(receiving_type):
    """
    Test values in Weekly Overview section, except "Pulls" field.
    UnitsInHouseMinusRowNeed, UnitsInHouseMinRowNeedMinForecast, StartOfTheDayInventoryMinusUnitsInHouseHj,
    BufferPercent values calculate as on Ing Depl. UnitsInHouseHj, HjDiscards, UnitsOnProductionFloor
    takes any value from child skus, the last columns calculate as sum of child skus. Allocation Price column calculates
    as: the 'min' takes min price value from all children rows, the 'max' takes the max price value from all children
    rows

    Test steps:
    1. Generate required test data (created 2 site configs, 2 warehouses, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=receiving_type, hj_name="HF01", week=PREVIOUS_WEEK
    )
    site_config_ep = dataclasses.replace(
        site_config_hf, receiving_type=receiving_type, brand=Brands.EP.value, site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_config_ep_another_hj_name = dataclasses.replace(
        site_config_ep, site=Sites.GA_EP.value, hj_name="HF11", week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep, site_config_ep_another_hj_name]
    sku = TestSku.generate_sku()
    warehouses = Warehouses.random_non_repetitive_values(2)
    po_for_inventory = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    inventories = [
        TestPimtUnifiedInventory.generate_inventory_by_po(po=po_for_inventory, warehouse=warehouse)
        for warehouse in warehouses
    ]
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    po_not_delivered = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, delivery_time_start=CURRENT_DATE - timedelta(days=10)
        )
        for config in site_configs
    ]
    po_autobagger = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config, supplier_name="Autobagger - " + config.site.code)
        for config in site_configs
    ]
    po_in_progress_hj = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, delivery_time_start=CURRENT_DATE + timedelta(days=10)
        )
        for config in site_configs
    ]
    hj_receipt_in_progress_hj = [
        TestHJReceipts.generate_hj_receipt(
            site_config=config, status=IN_PROGRESS_HJ, receipt_time_est=YESTERDAY_DATE, po=po, quantity_received=0
        )
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hj_receipt = [
        TestHJReceipts.generate_hj_receipt(
            site_config=config,
            po=po,
        )
        for config, po in zip(site_configs, pos)
    ]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=config) for config in site_configs]
    receipt_overrides = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config_hf)
    hj_discards = [
        TestHJDiscard.generate_discard(sku=sku, site_config=site_config, tran_type=DiscardType.DISCARD)
        for site_config in site_configs
    ]
    hj_wip_discard = TestHJDiscard.generate_discard(
        sku=sku, site_config=site_config_hf, tran_type=DiscardType.WIP_DISCARD
    )
    hj_wip_1 = TestHJWip(sku=sku, hj_name=site_config_hf.hj_name, quantity=data_generator.random_int())
    hj_wip_2 = TestHJWip(sku=sku, hj_name=site_config_ep_another_hj_name.hj_name, quantity=data_generator.random_int())
    hj_autostore = TestHjAutostore(sku=sku, quantity=data_generator.random_int(), wh_id=site_config_hf.hj_name)
    hybrid_needs_prev_week_available_dates = [
        date
        for date in [PREVIOUS_WEEK.get_last_day() + timedelta(days=1), PREVIOUS_WEEK.get_last_day() + timedelta(days=2)]
        if date >= CURRENT_DATE
    ] or PREVIOUS_WEEK.week_days()
    hybrid_needs = [TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=sc) for sc in site_configs]
    hybrid_needs_prev_week = [
        TestHybridNeeds.generate_hybrid_needs(
            sku=sku,
            site_config=site_config_hf,
            week=PREVIOUS_WEEK,
            day=random.choice(hybrid_needs_prev_week_available_dates),
        )
    ]
    inventory_pull_put = [
        TestInventoryPullPut.generate_inventory_pull_put(site_config=config, sku=sku) for config in site_configs
    ]
    discard = [TestDiscard.generate_discard(site_config=config, sku=sku) for config in site_configs]
    hj_donation_discard = [
        TestHJDiscard.generate_discard(site_config=sc, sku=sku, tran_type=DiscardType.DONATION) for sc in site_configs
    ]
    price_hf = TestAllocationPrice.generate_allocation_price(
        sku=sku, site_config=site_config_hf, price=data_generator.random_int(1)
    )
    price_ep = TestAllocationPrice.generate_allocation_price(
        sku=sku, site_config=site_config_ep, price=data_generator.random_int(2)
    )
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=warehouses)
    with (
        TestData(None)
        .with_brands(site_config_hf.brand, site_config_ep.brand)
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(mealkit_ingredient[0], mealkit_ingredient[1])
        .with_purchase_orders(*pos, *po_in_progress_hj, *po_autobagger, *po_not_delivered, po_for_inventory)
        .with_oscar_forecast(*[item for sublist in oscars for item in sublist])
        .with_receipt_override(*receipt_overrides)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_hj_discard(*hj_discards, hj_wip_discard, *hj_donation_discard)
        .with_hj_wip(hj_wip_1, hj_wip_2)
        .with_hybrid_needs(*hybrid_needs, *hybrid_needs_prev_week)
        .with_inventory_pull_put(*inventory_pull_put)
        .with_hj_autostore(hj_autostore)
        .with_hj_receipts(*hj_receipt, *hj_receipt_in_progress_hj)
        .with_discard(*discard)
        .with_warehouses(*warehouses)
        .with_pimt_inventory(*inventories)
        .with_network_depletion_presets(preset)
        .with_allocation_price(price_ep, price_hf)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        all_ing_depl_data = data["childItems"]
        ing_depl_data_tpw = [item for item in all_ing_depl_data if item.get("brand") == ""]
        ing_depl_data_not_tpw = [item for item in all_ing_depl_data if item.get("brand") != ""]
        weekly = data["weekly"]

        assert weekly["unitsInHouseMinusRowNeed"] == weekly["unitsInHouseHj"] - weekly["rowNeed"]
        assert (
            weekly["unitsInHouseMinRowNeedMinForecast"]
            == weekly["unitsInHouseMinusRowNeed"] - weekly["nextWeekForecast"]
        )
        assert (
            weekly["startOfTheDayInventoryMinusUnitsInHouseHj"]
            == weekly["startOfTheDayInventory"] - weekly["unitsInHouseHj"]
        )
        assert weekly["bufferPercent"] == float(round(weekly["bufferQuantity"] / weekly["unitsNeeded"], 4))
        assert weekly["hjDiscards"] == sum(
            0 if CURRENT_DATE.strftime(DAY_FORMAT) == DayOfWeek.WEDNESDAY else hj_discard.quantity
            for hj_discard in hj_discards
        )
        assert weekly["unitsInHouseHj"] == (
            sum(child["weekly"]["unitsInHouseHj"] for child in ing_depl_data_tpw) + hj_pallet_snapshot.pallet_quantity
        )
        if receiving_type == ReceiveInputType.HIGH_JUMP:
            expected_units_on_prod_floor = sum([hj_wip.quantity for hj_wip in [hj_wip_1, hj_wip_2]])
        else:
            expected_units_on_prod_floor = sum(
                child["weekly"]["unitsOnProductionFloor"] for child in ing_depl_data_not_tpw
            )
        assert weekly["unitsOnProductionFloor"] == expected_units_on_prod_floor
        assert weekly["unitsNeeded"] == sum(child["weekly"]["unitsNeeded"] for child in ing_depl_data_not_tpw)
        assert weekly["unitsOrdered"] == sum(child["weekly"]["unitsOrdered"] for child in all_ing_depl_data)
        assert weekly["unitsReceived"] == sum(child["weekly"]["unitsReceived"] for child in all_ing_depl_data)
        assert weekly["rowNeed"] == sum(child["weekly"]["rowNeed"] for child in ing_depl_data_not_tpw)
        assert weekly["awaitingDelivery"] == sum(child["weekly"]["awaitingDelivery"] for child in ing_depl_data_not_tpw)
        assert weekly["bufferQuantity"] == sum(child["weekly"]["bufferQuantity"] for child in ing_depl_data_not_tpw)
        assert weekly["discards"] == sum(child["weekly"]["discards"] for child in ing_depl_data_not_tpw)
        assert weekly["donations"] == sum(child["weekly"]["donations"] for child in ing_depl_data_not_tpw)
        assert weekly["inProgressHj"] == sum(child["weekly"]["inProgressHj"] for child in ing_depl_data_not_tpw)
        assert weekly["inventory"] == sum(child["weekly"]["inventory"] for child in ing_depl_data_not_tpw)
        assert weekly["notDelivered"] == sum(child["weekly"]["notDelivered"] for child in ing_depl_data_not_tpw)
        assert weekly["onHandMinProductionNeeds"] == sum(
            child["weekly"]["onHandMinProductionNeeds"] for child in ing_depl_data_not_tpw
        )
        assert weekly["previousWeekRowNeed"] == sum(
            child["weekly"]["previousWeekRowNeed"] for child in ing_depl_data_not_tpw
        )
        assert weekly["startOfTheDayInventory"] == sum(
            child["weekly"]["startOfTheDayInventory"] for child in ing_depl_data_not_tpw
        )
        assert weekly["totalOnHand"] == sum(child["weekly"]["totalOnHand"] for child in ing_depl_data_not_tpw)
        assert weekly["unitsToProduceByAutobagger"] == sum(
            child["weekly"]["unitsToProduceByAutobagger"] for child in ing_depl_data_not_tpw
        )
        assert weekly["hjAutostoreInventory"] is None
        assert weekly["hjAutostoreInventoryPlusHjInHouse"] is None
        assert weekly["allocationPrice"] == {"min": str(price_hf.price), "max": str(price_ep.price)}


def test_weekly_pulls_value():
    """
    Test "Pulls" value in Weekly Overview section. Pulls value calculates as sum of child skus.

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(
        site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    # inventory_pull_put.quantity is negative to set "pulls" value
    inventory_pull_put = [
        TestInventoryPullPut.generate_inventory_pull_put(
            site_config=config, sku=sku, quantity=-data_generator.random_int()
        )
        for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_inventory_pull_put(*inventory_pull_put)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(*oscars)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        ing_depl_data = data["childItems"]
        weekly = data["weekly"]

        assert weekly["pulls"] == sum(child["weekly"]["pulls"] for child in ing_depl_data)


def test_bulk_sku_values_section():
    """
    Test that all values in the Bulk Sku section are calculated properly. All values calculate as sum of child skus,
    except "delta" value (as on Ing Depl).

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP, hj_name="HF01", week=PREVIOUS_WEEK
    )
    site_config_ep = dataclasses.replace(
        site_config_hf, brand=Brands.EP.value, hj_name="HF11", site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep]
    sku, bulk_sku = TestSku.generate_skus(sku_quantity=2)
    warehouse = Warehouses.random_value()
    po_for_inventory = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_for_inventory, warehouse=warehouse)
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=bulk_sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        sku=bulk_sku,
        site_config=site_config_hf,
    )
    bulk_skus = TestBulkSkus.generate_bulk_skus(packaged_sku=sku, bulk_sku=bulk_sku, brands=[site_config_hf.brand])
    bulk_skus.brands = [site_config.brand.code for site_config in site_configs]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    hj_discards = [
        TestHJDiscard.generate_discard(sku=bulk_sku, site_config=config, tran_type=DiscardType.DISCARD)
        for config in site_configs
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku, bulk_sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, po_for_inventory)
        .with_receipt_override(*receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_bulk_skus(bulk_skus)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
        .with_hj_discard(*hj_discards)
        .with_oscar_forecast(*oscars)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        all_ing_depl_data = data["childItems"]
        ing_depl_data_tpw = [item for item in all_ing_depl_data if item.get("brand") == ""]
        ing_depl_data_not_tpw = [item for item in all_ing_depl_data if item.get("brand") != ""]
        bulk_values = data["bulkValues"]

        assert bulk_values["bulkUnitsOrdered"] == sum(
            child["bulkValues"]["bulkUnitsOrdered"] for child in all_ing_depl_data
        )
        assert bulk_values["bulkUnitsReceived"] == sum(
            child["bulkValues"]["bulkUnitsReceived"] for child in all_ing_depl_data
        )
        assert bulk_values["bulkDiscards"] == sum(
            child["bulkValues"]["bulkDiscards"] for child in all_ing_depl_data if child["brand"] != ""
        )
        assert bulk_values["delta"] == sum(child["bulkValues"]["delta"] for child in all_ing_depl_data)

        assert bulk_values["bulkUnitsInHj"] == (
            sum(child["bulkValues"]["bulkUnitsInHj"] for child in ing_depl_data_not_tpw)
            + sum(child["bulkValues"]["bulkUnitsInHj"] for child in ing_depl_data_tpw)
        )


def test_work_order_section_on_ndm():
    """
    Test that all values in the Work Order section are calculated properly. All values calculate as sum of child skus.

    Test steps:
    1. Generate required test data (created 2 site configs for factor brand, data for Network Depletion board)
    2. Make API call to network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_configs = SiteConfigsFactor.random_non_repetitive_values(qty=2)
    full_recipe_sku, sub_recipe_sku = TestSku.generate_skus(sku_quantity=2)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=next(iter(site_configs)).brand, week=CURRENT_WEEK_FACTOR)
    ingredient = TestIngredient(sku=sub_recipe_sku, brand=next(iter(site_configs)).brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit=mealkit, ingredient=ingredient
    )
    oscars = [
        TestOscar.generate_oscar(sku=sub_recipe_sku, site_config=site_config, week=CURRENT_WEEK_FACTOR)
        for site_config in site_configs
    ]
    hj_pallet_snapshot = [
        TestHJPalletSnapshot.generate_hj_pallet_snapshot(
            sku=sub_recipe_sku, site_config=site_config, week=CURRENT_WEEK_FACTOR
        )
        for site_config in site_configs
    ]
    receipt_pos = [
        TestPurchaseOrder.generate_po_with_sku(sku=sub_recipe_sku, site=site_config, week=CURRENT_WEEK_FACTOR)
        for site_config in site_configs
    ]
    pos_is_sent = [
        TestPurchaseOrder.generate_po_with_sku(sku=sub_recipe_sku, site=site_config, week=CURRENT_WEEK_FACTOR)
        for site_config in site_configs
    ]
    hj_receipts = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, week=CURRENT_WEEK_FACTOR)
        for po, site_config in zip(receipt_pos, site_configs)
    ]
    man_sku_full_receipt = TestManufacturedSku.generate_manufactured_sku(sku=full_recipe_sku)
    man_sku_sub_receipt = TestManufacturedSkuPart.generate_manufactured_sku_part(
        sku_root=full_recipe_sku, sku_part=sub_recipe_sku
    )
    work_orders = [
        TestWorkOrder.generate_work_order(
            site_config=site_config, sku=sub_recipe_sku, recipe_sku=full_recipe_sku, week=CURRENT_WEEK_FACTOR
        )
        for site_config in site_configs
    ]
    staged_inventories = [
        TestStagedInventory.generate_staged_inventory(
            site_config=site_config, sub_recipe_id=sub_recipe_sku, week=CURRENT_WEEK_FACTOR
        )
        for site_config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(next(iter(site_configs)).brand)
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(full_recipe_sku, sub_recipe_sku)
        .with_mealkit(mealkit)
        .with_ingredients(ingredient)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*receipt_pos, *pos_is_sent)
        .with_hj_receipts(*hj_receipts)
        .with_manufactured_sku(man_sku_full_receipt)
        .with_manufactured_sku_part(man_sku_sub_receipt)
        .with_work_order(*work_orders)
        .with_staged_inventory(*staged_inventories)
        .with_hj_palette_snapshot(*hj_pallet_snapshot)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(*oscars)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id, week=CURRENT_WEEK_FACTOR)
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        child_items = api_data["childItems"]
        work_order_agg_values = api_data["workOrder"]

        assert work_order_agg_values["requiredLbs"] == sum(child["workOrder"]["requiredLbs"] for child in child_items)
        assert work_order_agg_values["stagedLbs"] == sum(child["workOrder"]["stagedLbs"] for child in child_items)
        assert work_order_agg_values["bufferedForecast"] == round(
            sum(child["workOrder"]["bufferedForecast"] for child in child_items), 2
        )
        assert work_order_agg_values["woRowNeed"] == round(
            sum(child["workOrder"]["woRowNeed"] for child in child_items), 2
        )
        assert work_order_agg_values["stagedPlusHjMinusReceived"] == (
            sum(child["workOrder"]["stagedPlusHjMinusReceived"] for child in child_items)
        )
        assert work_order_agg_values["hjMinusWoRowNeed"] == round(
            sum(child["workOrder"]["hjMinusWoRowNeed"] for child in child_items), 2
        )
        assert work_order_agg_values["hjPlusToBeDeliveredMinusWoRowNeed"] == round(
            sum(child["workOrder"]["hjPlusToBeDeliveredMinusWoRowNeed"] for child in child_items), 2
        )


def test_buffer_analysis_section():
    """
    Test that all values in the Buffer Analysis section are calculated properly.

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP, hj_name="HF01"
    )
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, hj_name="HF11", site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config_hf)
    allowed_buffer = [
        TestAllowedProduceBuffer.generate_allowed_buffer(site_config=config, sku=sku) for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_oscar_forecast(*oscars)
        .with_receipt_override(*receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_purchasing_category(PurchasingCategories.PRODUCE, sku)
        .with_network_depletion_presets(preset)
        .with_allowed_buffer(*allowed_buffer)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        weekly_data = data["weekly"]
        ing_depl_data = data["childItems"]
        buffer_analysis = data["bufferAnalysis"]
        assert Decimal(str(buffer_analysis["poBuffer"])) == max(
            Decimal(str(child["bufferAnalysis"]["poBuffer"])) for child in ing_depl_data
        )
        comparisons.assert_numbers_equal(
            buffer_analysis["bufferQuantityNeeded"],
            (buffer_analysis["allowedBuffer"] * weekly_data["unitsNeeded"] - weekly_data["bufferQuantity"]),
            delta=1,
        )
        assert buffer_analysis["caseTotal"] == round(
            buffer_analysis["bufferQuantityNeeded"] / buffer_analysis["caseYield"]
        )
        expected_total_cost = buffer_analysis["caseTotal"] * buffer_analysis["caseCost"]
        assert buffer_analysis["totalCost"] == round(expected_total_cost, 2)
        priced_pos = (
            (isku.case_price / isku.case_size, (ipo, isku)) for ipo in pos for isku in ipo.line_items.values()
        )
        top_price_po, top_price_sku = (
            min(priced_pos) if buffer_analysis["bufferQuantityNeeded"] >= 0 else max(priced_pos)
        )[1]
        assert buffer_analysis["supplier"] == top_price_po.supplier_name
        assert buffer_analysis["caseYield"] == top_price_sku.case_size
        assert Decimal(str(buffer_analysis["caseCost"])) == top_price_sku.case_price
        assert Decimal(str(buffer_analysis["allowedBuffer"])) == next(
            filter(None, (Decimal(str(child["bufferAnalysis"]["allowedBuffer"])) for child in ing_depl_data))
        )


def test_daily_section():
    """
    Test that all values in the Daily section are calculated properly. StatusInWeek and Status values calculate based
    on priority. Inventory (except Monday 1), OnHand, EodInventoryProduction, EodInventoryOrder,
    TotalOnHand and TotalProductionNeed fields calculate as on Ing Depl, the other columns calculate as sum of child
    skus.

    Test steps:
    1. Generate required test data (created 2 site configs, data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP, hj_name="HF01", week=PREVIOUS_WEEK
    )
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, hj_name="HF11", site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)
    grn = TestGrn.generate_grn(po=inbound_po, warehouse=warehouse)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=inbound_po, warehouse=warehouse)
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    po_in_progress_hj = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, delivery_time_start=CURRENT_DATE + timedelta(days=10)
        )
        for config in site_configs
    ]
    po_received = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config, delivery_time_start=YESTERDAY_DATE)
        for config in site_configs
    ]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    inventory_pull_put = [
        TestInventoryPullPut.generate_inventory_pull_put(site_config=config, sku=sku) for config in site_configs
    ]
    discard = [TestDiscard.generate_discard(site_config=config, sku=sku) for config in site_configs]
    hj_receipt_in_progress_hj = [
        TestHJReceipts.generate_hj_receipt(
            site_config=config,
            status=IN_PROGRESS_HJ,
            # receipt_time_est - 5h because of est timezone
            receipt_time_est=YESTERDAY_DATETIME - timedelta(hours=5),
            po=po,
        )
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hj_receipt_closed = [
        TestHJReceipts.generate_hj_receipt(site_config=config, status=CLOSED, po=po)
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hybrid_needs = [TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=config) for config in site_configs]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, *po_in_progress_hj, *po_received, inbound_po)
        .with_oscar_forecast(*oscars)
        .with_receipt_override(*receipt_override)
        .with_warehouses(warehouse)
        .with_inventory_pull_put(*inventory_pull_put)
        .with_discard(*discard)
        .with_hj_receipts(*hj_receipt_in_progress_hj, *hj_receipt_closed)
        .with_hybrid_needs(*hybrid_needs)
        .with_grn(grn)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        ing_depl_data = data["childItems"]
        daily_data = data["daily"]

        # check the columns according to Ing Depl dashboard
        ingredient_depletion_steps.check_daily_status_in_week(daily_data)
        ingredient_depletion_steps.check_daily_status(daily_data)
        ingredient_depletion_steps.check_daily_total_production_need(daily_data)
        aggregated_and_network_depletion_steps.check_inventory_in_daily_section(daily_data, ing_depl_data)
        ingredient_depletion_steps.check_daily_total_on_hand(daily_data)
        assert (
            data["endOfWeekInventory"]
            == daily_data[list(daily_data)[-1]]["totalOnHand"] - daily_data[list(daily_data)[-1]]["totalProductionNeed"]
        )

        for day, daily_values in daily_data.items():
            assert (
                daily_values["eodInventoryOrder"]
                == daily_values["eodInventoryProduction"] + daily_values["unitsOnOrder"]
            )
            assert daily_values["eodInventoryProduction"] == daily_values["onHand"] - daily_values["productionNeeds"]
            assert (
                daily_values["onHand"]
                == daily_values["inventory"] + daily_values["unitsDelivered"] - daily_values["discards"]
            )
            assert daily_values["discards"] == sum(child["daily"][day]["discards"] for child in ing_depl_data)
            assert daily_values["inventory"] == sum(child["daily"][day]["inventory"] for child in ing_depl_data)
            assert daily_values["productionNeeds"] == sum(
                child["daily"][day]["productionNeeds"] for child in ing_depl_data
            )
            assert daily_values["totalProductionNeed"] == sum(
                child["daily"][day]["totalProductionNeed"] for child in ing_depl_data
            )
            assert (
                daily_values["totalOnHandMinusTotalProductionNeed"]
                == daily_values["totalOnHand"] - daily_values["totalProductionNeed"]
            )
            assert daily_values["unitsDelivered"] == sum(
                child["daily"][day]["unitsDelivered"] for child in ing_depl_data
            )
            assert daily_values["unitsOnOrder"] == sum(child["daily"][day]["unitsOnOrder"] for child in ing_depl_data)


def test_sorting_by_brand():
    """
    Test sorting sku names by brand on Network Depletion View.
    The hierarchy is the following: EP SKUs, GC SKUs, HF SKUs

    Test steps:
    1. Generate required test data (created 3 site configs (HF, EP, GC), data for Network Depletion board)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check if the data matches the expected values from the response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(
        site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value, week=PREVIOUS_WEEK
    )
    site_config_gc = dataclasses.replace(
        site_config_hf, brand=Brands.GC.value, site=Sites.NJ_GC.value, week=PREVIOUS_WEEK
    )
    site_configs = [site_config_hf, site_config_ep, site_config_gc]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    commodity_group = data_generator.generate_string()
    buyer_skus = [TestBuyerSku.generate_buyer_sku(sku=sku, site_config=site_config) for site_config in site_configs]
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config_gc, week=CURRENT_WEEK)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_purchasing_categories(purchasing_category)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_commodity_group(commodity_group, site_config_hf.site.code, sku)
        .with_buyer_sku(*buyer_skus)
        .with_oscar_forecast(oscar)
        .with_network_depletion_presets(preset)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=1)
        actual_brand_name_order = [item["brand"] for item in data]
        assert actual_brand_name_order == [
            f"{site_config_ep.brand.code}, {site_config_gc.brand.code}, {site_config_hf.brand.code}"
        ]


def test_pos_present_in_inventory():
    """
    Test unitsOrdered and unitsReceived columns.

    Test steps:
    1. Generate required test data (site config, data for Network Depletion board, inbound pos, inventory)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check unitsOrdered and unitsReceived matches the passed data
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    po_1, po_2 = TestPurchaseOrder.generate_inbound_pos(pos_quantity=2, sku=sku, warehouse=warehouse)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_1, warehouse=warehouse)
    grn_1, grn_2 = [TestGrn.generate_grn(po=po, warehouse=warehouse) for po in [po_1, po_2]]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config], warehouses=[warehouse])
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_grn(grn_1, grn_2)
        .with_purchase_orders(po_1, po_2)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(oscar)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        data = base_steps.process_response_and_extract_data(response=res, element_index=0)
        assert data["weekly"]["unitsOrdered"] == sum(po.first_line().qty for po in [po_1, po_2])
        assert data["weekly"]["unitsReceived"] == sum(grn.units_received for grn in [grn_1, grn_2])


def test_sku_listing_considering_demand_on_ndm():
    """
    Test that SKUs without demand are included

    Test steps:
    1. Generate required test data (created site config, warehouse, 4 SKUs, mealkit, ingredient for sku_with_demand
    and mealkit_ingredient, po for second sku, inventory for third sku and pull/put inventory for forth sku)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check that returned the records for all skus were returned
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    skus = sku_with_demand, sku_without_demand, sku_without_demand_2, sku_without_demand_3 = TestSku.generate_skus(
        sku_quantity=4
    )
    warehouse = Warehouses.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku_without_demand)
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        site_config=site_config, sku=sku_without_demand_2
    )
    pull_put_inventory = TestInventoryPullPut.generate_inventory_pull_put(
        site_config=site_config, sku=sku_without_demand_3
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku_with_demand, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config], warehouses=[warehouse])
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK) for sku in skus]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_warehouses(warehouse)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(*oscars)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_inventory_pull_put(pull_put_inventory)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        data = base_steps.process_response_and_extract_data(response=res, expected_length=4)
        assert sorted([item["sku"] for item in data]) == sorted([sku.sku_code for sku in skus])


def test_child_rows_records_on_ndm_considering_demands():
    """
    Test that child row is added if it has any forecast, incoming POs, pull/put inventory, inventory.
    Parent row is shown only if any child row had forecast values.

    Test steps:
    1.Generate required data (add 4 sites, for 1 site add forecast, for second site add po, for third site add pull/
    put inventory and add for forth site inventory)
    2.Make API for ndm -> 200
    3.Check that 4 children rows are present
    """
    site_config_hf_nj = dataclasses.replace(SiteConfigs.NJ_HF.value)
    site_config_tx_hf = dataclasses.replace(SiteConfigs.TX_HF.value)
    site_config_ep_ct = dataclasses.replace(SiteConfigs.CT_EP.value)
    site_config_ep_ga = dataclasses.replace(SiteConfigs.GA_EP.value)
    site_configs = [site_config_hf_nj, site_config_tx_hf, site_config_ep_ct, site_config_ep_ga]
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config_hf_nj.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config_hf_nj.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=None)
    oscar_for_hf_nj = TestOscar.generate_oscar(sku=sku, site_config=site_config_hf_nj, week=CURRENT_WEEK)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_tx_hf)
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(site_config=site_config_ep_ct, sku=sku)
    pull_put_inventory = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config_ep_ga, sku=sku)
    with (
        TestData(None)
        .with_brands(site_config_hf_nj.brand, site_config_ep_ct.brand)
        .with_sites(*(sc.site for sc in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(oscar_for_hf_nj)
        .with_inventory_pull_put(pull_put_inventory)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset.id)
        api_data = base_steps.process_response_and_extract_data(response=res, expected_length=1, element_index=0)
        assert len(api_data["childItems"]) == 4


def test_daily_depletion_for_warehouses():
    """
    Test Daily Depletion values for warehouses. The data set up for Monday 1 and for current date.

    Test steps:
    1. Generate required test data (site config, data for Network Depletion board, inbound pos, inventory)
    2. Send a POST request to the presets_endpoint with preset_id, preset_new_name, site_configs, warehouses-> 200
    2. Send a GET request to the network_depletion_endpoint -> 200
    3. Check that data are equal to expected
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    dates = [datetime.combine(CURRENT_WEEK.get_first_day() - timedelta(days=2), datetime.min.time()), CURRENT_DATETIME]
    inbound_po_is_sent = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)
    inbound_po = [
        TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse, delivery_time_start=date)
        for date in dates
    ]
    grn = [
        TestGrn.generate_grn(po=po, warehouse=warehouse, receipt_time_est=date) for po, date in zip(inbound_po, dates)
    ]
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=inbound_po_is_sent, warehouse=warehouse)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config], warehouses=[warehouse])
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(inbound_po_is_sent, *inbound_po)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
        .with_grn(*grn)
        .with_oscar_forecast(oscar)
    ):
        res = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        api_data = base_steps.process_response_and_extract_data(response=res, element_index=0)
        daily_data_warehouse = next(
            iter([row["daily"] for row in api_data["childItems"] if row["site"] == warehouse.code])
        )
        ingredient_depletion_steps.check_daily_units_on_order(daily_data_warehouse, [inbound_po_is_sent])
        ingredient_depletion_steps.check_daily_units_delivered_for_warehouse(daily_data_warehouse, [*grn])
        ingredient_depletion_steps.check_daily_eod_inventory_prod_for_warehouse(daily_data_warehouse)
        ingredient_depletion_steps.check_daily_total_on_hand(daily_data_warehouse)
        assert daily_data_warehouse[next(iter(daily_data_warehouse))]["inventory"] == 0
        for daily_val in daily_data_warehouse.values():
            assert daily_val["eodInventoryOrder"] == daily_val["eodInventoryProduction"] + daily_val["unitsOnOrder"]
            assert daily_val["onHand"] == daily_val["inventory"] + daily_val["unitsDelivered"] - daily_val["discards"]


def test_inventory_dropdown_on_ndm():
    """
    Test case checks Inventory dropdown on Network Depletion Page.

    Test steps:
    1. Generate required data (site config, warehouse, data for Network Depletion board, inventories)
    2. Make an API call to inventory endpoint -> 200
    3. Check that column values on the Inventory dropdown meet with the API response data
    """
    site_config = SiteConfigs.random_value()
    warehouse = Warehouses.random_value()
    sku = TestSku.generate_sku()
    po_1 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    po_2 = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    inventory_wh = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po_1, warehouse=warehouse, expiration_date=CURRENT_DATE
    )
    inventory_site = TestPimtUnifiedInventory.generate_inventory_by_site_config(
        po=po_2, site_config=site_config, expiration_date=TOMORROW_DATE
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config], warehouses=[warehouse])
    purchasing_category = TestSkuCategory(sku, PurchasingCategories.random_value())
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory_wh, inventory_site)
        .with_network_depletion_presets(preset)
        .with_purchase_orders(po_1, po_2)
        .with_purchasing_categories(purchasing_category)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion_inventory_dropdown(preset_id=preset.id, sku=sku)
        actual_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)
        for data in actual_data:
            assert data["skuCode"] == sku.sku_code
            assert data["skuName"] == sku.sku_name
            assert set(data["brands"].split(", ")) == set(sku.brands)
            assert data["category"] == purchasing_category.ingredient_category
            if data["site"] == site_config.bob_code:
                inventory = inventory_site
                po = po_2
                assert data["source"] == site_config.inventory_type
                assert data["siteName"] == site_config.site.name
            else:
                inventory = inventory_wh
                po = po_1
                assert data["source"] == warehouse.inventory_type
                assert data["site"] == warehouse.code
                assert data["siteName"] == warehouse.warehouse_name

            assert data["expirationDate"] == str(inventory.expiration_date)
            assert data["quantity"] == inventory.case_quantity * po.first_line().case_size
            assert data["status"] == inventory.inventory_status
            assert data["locationId"] == inventory.location_id
            assert data["lot"] == inventory.lot
            assert data["snapshotTimestamp"] == inventory.snapshot_timestamp.strftime(DATE_TIME_FORMAT_14)
            assert data["supplier"] == po.supplier_name
            assert data["poNumber"] == po.po_number


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_po_dropdown_on_ndm(receiving_type):
    """
    Test case checks PO dropdown on DC Inventory -> Network Depletion Page.
    Test steps:
    1. Generate required data (site configs with 2 brands, warehouse, data for Network Depletion board, set
    receiving_type to have High_Jump and Manual forms)
    2. Make an API call to receive data from the PO dropdown
    3. Check that column values on the PO dropdown meet with the API response data
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, receiving_type=receiving_type)
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    po_hf = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config_hf,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_ep = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config_ep,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
        emergency_reason="Emergency reason",
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = [po_ep, po_hf, inbound_po]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    hj_receipt_hf = TestHJReceipts.generate_hj_receipt(po=po_hf, site_config=site_config_hf)
    hj_receipt_ep = TestHJReceipts.generate_hj_receipt(po=po_ep, site_config=site_config_ep)
    receiving_hf = TestReceiving.generate_receiving(site_config=site_config_hf, po=po_hf)
    receiving_ep = TestReceiving.generate_receiving(site_config=site_config_ep, po=po_ep)
    po_shipment_hf = TestPOShipment.generate_po_shipment(po=po_hf)
    po_shipment_ep = TestPOShipment.generate_po_shipment(po=po_ep)
    grn = TestGrn.generate_grn(
        po=inbound_po, warehouse=warehouse, units_received=inbound_po.first_line().case_size + random.randint(100, 200)
    )
    with (
        TestData(None)
        .with_brands(*(sc.brand for sc in site_configs))
        .with_sites(*(sc.site for sc in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(*pos)
        .with_hj_receipts(hj_receipt_ep, hj_receipt_hf)
        .with_receiving(receiving_ep, receiving_hf)
        .with_network_depletion_presets(preset)
        .with_po_shipment(po_shipment_ep, po_shipment_hf)
        .with_warehouses(warehouse)
        .with_grn(grn)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion_po_dropdown(preset_id=preset.id, sku=sku)
        actual_data = {
            data["poNumber"]: data
            for data in base_steps.process_response_and_extract_data(response=response, expected_length=3)
        }
        for po in pos:
            assert actual_data[po.po_number]["supplier"] == po.supplier_name
            assert actual_data[po.po_number]["poNumber"] == po.po_number
            assert actual_data[po.po_number]["sku"] == sku.sku_code
            assert actual_data[po.po_number]["skuName"] == sku.sku_name
            assert actual_data[po.po_number]["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
            assert actual_data[po.po_number]["receiveVariance"] == abs(
                actual_data[po.po_number]["quantityOrdered"] - actual_data[po.po_number]["quantityReceived"]
            )
            assert actual_data[po.po_number]["orderSize"] == po.first_line().order_size
            assert actual_data[po.po_number]["casePrice"] == float(po.first_line().case_price)
            assert actual_data[po.po_number]["caseSize"] == po.first_line().case_size

            assert Decimal(str(actual_data[po.po_number]["caseSizeReceived"])) == round(
                Decimal(actual_data[po.po_number]["quantityReceived"])
                / Decimal(actual_data[po.po_number]["casesReceived"]),
                3,
            )
            assert actual_data[po.po_number]["quantityOrdered"] == po.first_line().qty
            assert round(Decimal(str(actual_data[po.po_number]["totalPriceReceived"])), 2) == round(
                Decimal(str(actual_data[po.po_number]["quantityReceived"]))
                * (po.first_line().case_price / po.first_line().case_size),
                2,
            )
            assert actual_data[po.po_number]["emergencyReason"] == po.emergency_reason
            assert actual_data[po.po_number]["shipMethod"] == po.shipping_method

            po_mapping = {
                po_hf.po_number: (hj_receipt_hf, receiving_hf, po_shipment_hf),
                po_ep.po_number: (hj_receipt_ep, receiving_ep, po_shipment_ep),
            }
            if po.po_number in po_mapping:
                hj_receipt, receiving, po_shipment = po_mapping[po.po_number]
            if po == po_hf or po == po_ep:
                assert actual_data[po.po_number]["appointmentTime"] == po_shipment.appointment_time.strftime(
                    DATE_TIME_FORMAT_1
                )
                assert actual_data[po.po_number]["quantityReceived"] == po_status_steps.get_quantity_received(
                    receiving_type=receiving_type, hj_receipt=hj_receipt, receiving=receiving
                )
                assert actual_data[po.po_number]["casesReceived"] == po_status_steps.get_cases_received(
                    receiving_type=receiving_type, hj_receipt=hj_receipt, receiving=receiving
                )
                assert actual_data[po.po_number]["dateReceived"] == po_status_steps.get_date_received(
                    receiving_type=receiving_type, hj_receipt=hj_receipt, receiving=receiving
                )
                assert actual_data[po.po_number]["totalPrice"] == float(po.first_line().total_price)
            else:
                assert actual_data[po.po_number]["totalPrice"] == round(
                    actual_data[po.po_number]["casesReceived"] * actual_data[po.po_number]["casePrice"], 2
                )
                assert Decimal(str(actual_data[po.po_number]["casesReceived"])) == round(
                    Decimal(str(actual_data[po.po_number]["quantityReceived"])) / actual_data[po.po_number]["caseSize"]
                )
                assert actual_data[po.po_number]["quantityReceived"] == grn.units_received
                assert actual_data[po.po_number]["dateReceived"] == grn.receipt_time_est.strftime(DATE_TIME_FORMAT_1)


def test_configured_presets_for_ca_us_markets():
    """
    Test case checks configured presets for US and CA markets.
    Test steps:
    1. Generate required test data with site_configs, presets for CA and US markets
    2. Make an API request to get Preset form for each market -> 200
    3. Check there are appropriate brands and sites for each preset form
    """
    site_config_ca, site_config_us = SiteConfigsCA.get_all_values(), SiteConfigs.get_all_values()
    site_configs = [*site_config_ca, *site_config_us]
    preset_us = TestNetworkDepletionPreset.create_preset(site_configs=site_config_us, market=MARKET_US)
    preset_ca = TestNetworkDepletionPreset.create_preset(site_configs=site_config_ca, market=MARKET_CA)
    markets = MARKET_US, MARKET_CA
    with (
        TestData(None)
        .with_site_configs(*site_configs)
        .with_users(MultiMarketUsers.test_user.value)
        .with_network_depletion_presets(preset_us, preset_ca)
    ):
        for market in markets:
            preset_from_response = NetworkDepletionModuleClient().get_preset_form(week=CURRENT_WEEK, market=market)
            response_data = base_steps.process_response_and_extract_data(response=preset_from_response)["presets"][0][
                "brands"
            ]
            brands = [brand for brand in response_data.keys()]
            sites = [site for site in response_data.values()]
            expected_brands = set(
                [site_config.brand.code for site_config in site_configs if site_config.market.code == market]
            )
            expected_sites = [
                site_config.site.code for site_config in site_configs if site_config.market.code == market
            ]

            assert sorted(brands) == sorted(expected_brands)
            assert sorted(item for sublist in sites for item in sublist) == sorted(expected_sites)


def test_donations_dropdown_values_on_ndm():
    """
    Test case checks Donations value on Weekly section for Network Depletion.
    Donations column is generated when transaction type is Donations.
    Test steps:
    1. Generate required test data with hj_discards with Donations and Wip Discards tran_types to check that Donations
    value is taken only from hj_discards with Donations tran_type, preset, oscar
    2. Make an API call to NDM endpoint -> 200, extract values, check donations column value
    3. Make an API call to Donations dropdown endpoint -> 200, extract values and check them to be as expected
    """
    is_wednesday = CURRENT_DATE.strftime(DAY_FORMAT) == DayOfWeek.WEDNESDAY
    week = PREVIOUS_WEEK if is_wednesday else CURRENT_WEEK
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=week)
    site_config_ep = dataclasses.replace(SiteConfigs.CT_EP.value, week=week)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(site_config.brand, week=week) for site_config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=site_config.brand) for site_config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    hj_donation_discard_hf = TestHJDiscard.generate_discard(
        site_config=site_config_hf, sku=sku, tran_type=DiscardType.DONATION
    )
    hj_donation_discard_ep = TestHJDiscard.generate_discard(
        site_config=site_config_ep, sku=sku, tran_type=DiscardType.DONATION
    )
    hj_discard = [TestHJDiscard.generate_discard(site_config=sc, sku=sku) for sc in site_configs]
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config_hf, week=week)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(sc.brand for sc in site_configs))
        .with_sites(*(sc.site for sc in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_hj_discard(hj_donation_discard_hf, hj_donation_discard_ep, *hj_discard)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(oscar)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id, week=week)
        actual_weekly_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["weekly"]
        assert actual_weekly_data["donations"] == sum(
            [hj_donation_discard_hf.quantity, hj_donation_discard_ep.quantity]
        )

        donation_dropdown_response = NetworkDepletionModuleClient().get_network_depletion_donation_dropdown(
            site_configs=site_configs, sku=sku, week=week
        )
        donation_data = base_steps.process_response_and_extract_data(
            response=donation_dropdown_response, expected_length=2
        )
        api_data_by_site = {site["site"]: site for site in donation_data}
        for data in donation_data:
            if data["site"] == site_config_hf.site.code:
                api_data = api_data_by_site[site_config_hf.site.code]
                donation_value = hj_donation_discard_hf
            else:
                api_data = api_data_by_site[site_config_ep.site.code]
                donation_value = hj_donation_discard_ep

            assert api_data["discardDate"] == donation_value.discard_date.strftime(DATE_TIME_FORMAT_1)
            assert api_data["quantity"] == donation_value.quantity
            assert api_data["skuCode"] == donation_value.sku.sku_code
            assert api_data["tranType"] == donation_value.tran_type
