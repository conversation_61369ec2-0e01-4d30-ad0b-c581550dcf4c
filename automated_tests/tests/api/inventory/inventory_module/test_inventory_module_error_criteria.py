from automated_tests.data.constants.base_constants import SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, PREVIOUS_WEEK
from automated_tests.data.constants.error_messages_constants import MISSED_MOCK_PLAN_CALCULATION_WARNING_MESSAGE
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.inventory.inventory_module_client import InventoryModuleClient
from procurement.core.dates import ScmWeek


def test_error_criteria_with_mock_plan():
    """
    Test that warnings messages are not present if mock plans set up on Inventory Module dashboard.
    Mock Plan calculation records should exist for previous week + 6 weeks. If they are absent or at least one,
    warning message "Mock plan calculation missed for weeks {week}" should appear.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    mock_plans = TestMockPlanCalculation.generate_mock_plans_for_several_weeks(site_config=site_config, weeks=6)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_mock_plan_calculation(*mock_plans)
    ):
        response = InventoryModuleClient().get_inventory_module(site_config=site_config, week=CURRENT_WEEK)
        assert response.status_code == 200, response.text
        warning_messages = response.json()["warnings"]
        for mock_plan_week in mock_plans:
            assert MISSED_MOCK_PLAN_CALCULATION_WARNING_MESSAGE.format(week=mock_plan_week.week) not in warning_messages


def test_error_criteria_without_mock_plan():
    """
    Test that warning messages are present if mock plan not set up on Inventory Module dashboard.
    Mock Plan calculation records should exist for previous week + 1 weeks. If they are absent or at least one,
    warning message "Mock plan calculation missed for weeks {week}" should appear.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    weeks = list(ScmWeek.range(PREVIOUS_WEEK, PREVIOUS_WEEK + 1))
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
    ):
        response = InventoryModuleClient().get_inventory_module(site_config=site_config, week=CURRENT_WEEK)
        assert response.status_code == 200, response.text
        actual_warning_messages = response.json()["warnings"]
        for week in weeks:
            assert MISSED_MOCK_PLAN_CALCULATION_WARNING_MESSAGE.format(week=week) in actual_warning_messages
