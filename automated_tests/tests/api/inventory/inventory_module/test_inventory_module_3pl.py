import dataclasses
from datetime import timedelta

import pytest

from automated_tests.data.constants.base_constants import PurchasingCategories, SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    TOMORROW_DATE,
    YESTERDAY_DATE,
    YESTERDAY_DATETIME,
)
from automated_tests.data.models.cycle_counts import TestCycleCounts
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.ingredient import TestIngredient
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.inventory.inventory_module_client import InventoryModuleClient
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import InventoryInputType, ReceiveInputType


def test_inventory_module_columns_3pl_cycle_count():
    """
    Test columns (Units in House, DC Expiring Inventory (1-90 Days), HighJump Snapshot (Yesterday),
    Start of Day Units in House Snapshot (HJ), palletsOnHand, Units Expiring) with cycle count inv_type and 3pl site.

    Test steps:
    1.Generate required data (data for inv module, cycle_count_units_in_house with date_of_count=cur_date and
    cycle_count_day=yesterday, cycle_count_start_of_day_units with date_of_count=yesterday and cycle_count_day=cur_date)
    2.Make API call to inventory_module endpoint -> 200
    3.Assert columns are equal to expected calculations.
    Expected calculations:
    Units in House = Sum of Units for the latest Date of Count
    Start of Day Units in House Snapshot (HJ) = Sum of Units for the latest Production BOH
    DC Expiring Inventory (1-90 Days), HighJump Snapshot (Yesterday) and Units Expiring = 0
    palletsOnHand is blank
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.is_3pl = True
    site_config.inventory_type = InventoryInputType.CYCLE_COUNT
    warehouse = dataclasses.replace(
        WarehouseUtils.get_warehouse_by_site_config(site_config=site_config), code=site_config.bob_code
    )
    po = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)
    ingredient = TestIngredient(sku, site_config.brand)
    forecast = TestOscar.generate_oscar(sku, site_config=site_config)
    cycle_count_units_in_house = TestCycleCounts.generate_cycle_counts(
        site_config=site_config, sku=sku, date_of_count=CURRENT_DATE, cycle_count_day=YESTERDAY_DATETIME
    )
    cycle_count_start_of_day_units = TestCycleCounts.generate_cycle_counts(
        site_config=site_config, sku=sku, date_of_count=YESTERDAY_DATE, cycle_count_day=CURRENT_DATETIME
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_ingredients(ingredient)
        .with_oscar_forecast(forecast)
        .with_cycle_counts(cycle_count_units_in_house, cycle_count_start_of_day_units)
    ):
        response = InventoryModuleClient().get_inventory_module(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        current_week_data = actual_data["weekly"][CURRENT_WEEK_STR]
        assert actual_data["palletsOnHand"] is None
        assert actual_data["unitsInHouseHj"] == cycle_count_units_in_house.units
        assert actual_data["dcExpiringInventory"] == 0
        assert current_week_data["startOfDayUnitsInHouseHJ"] == cycle_count_start_of_day_units.units
        assert current_week_data["unitsInHouseHJYesterday"] == 0
        assert current_week_data["unitsExpiring"] == 0


@pytest.mark.parametrize(
    "inventory_type, wh_inventory_type",
    [[InventoryInputType.GSHEET, InventoryInputType.GSHEET], [InventoryInputType.E2OPEN, InventoryInputType.E2OPEN]],
)
def test_inventory_module_3pl_manual_e2_open(inventory_type, wh_inventory_type):
    """
    Test columns (Units in House, DC Expiring Inventory (1-90 Days), HighJump Snapshot (Yesterday),
    Start of Day Units in House Snapshot (HJ), palletsOnHand, Units Expiring) with cycle count inv_type and 3pl site.

    Test steps:
    1.Generate required data (data for inv module, unified_inventories)
    2.Make API call to inventory_module endpoint -> 200
    3.Assert columns are equal to expected calculations.
    Expected calculations:
    Units in House = Sum of Qty with latest timestamp
    DC Expiring Inventory (1-90 Days) = Sum of with latest timestamp and expiration date > today and =< 90
    Start of Day Units in House Snapshot (HJ) = Sum of Qty with latest timestamp and expiration date > today
    Pallets on Hand and HighJump Snapshot (Yesterday) are blank
    Units Expiring = Sum of Qty with latest timestamp and expiry date <= Final Date of Production Week + 9
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.is_3pl = True
    site_config.inventory_type = inventory_type
    warehouse = dataclasses.replace(
        WarehouseUtils.get_warehouse_by_site_config(site_config=site_config), code=site_config.bob_code
    )
    po = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)
    ingredient = TestIngredient(sku, site_config.brand)
    forecast = TestOscar.generate_oscar(sku, site_config=site_config)
    inventory_yesterday_snapshot = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        warehouse=warehouse,
        expiration_date=TOMORROW_DATE,
        snapshot_timestamp=YESTERDAY_DATETIME,
        inventory_type=wh_inventory_type,
    )
    inventory_expiration_current_date = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        warehouse=warehouse,
        expiration_date=CURRENT_DATE,
        snapshot_timestamp=CURRENT_DATE,
        inventory_type=wh_inventory_type,
    )
    inventory_expiration_date_90_days = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        warehouse=warehouse,
        expiration_date=CURRENT_DATE + timedelta(days=90),
        snapshot_timestamp=CURRENT_DATE,
        inventory_type=wh_inventory_type,
    )
    inventory_expiration_date_91_days = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        warehouse=warehouse,
        expiration_date=CURRENT_DATE + timedelta(days=91),
        snapshot_timestamp=CURRENT_DATE,
        inventory_type=wh_inventory_type,
    )
    inventory_expiration_date_9_days = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        warehouse=warehouse,
        expiration_date=CURRENT_WEEK.get_last_day() + timedelta(days=9),
        snapshot_timestamp=CURRENT_DATE,
        inventory_type=wh_inventory_type,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_ingredients(ingredient)
        .with_oscar_forecast(forecast)
        .with_pimt_inventory(
            inventory_yesterday_snapshot,
            inventory_expiration_current_date,
            inventory_expiration_date_90_days,
            inventory_expiration_date_91_days,
            inventory_expiration_date_9_days,
        )
        .with_purchasing_category(PurchasingCategories.PRODUCE, sku)
    ):
        response = InventoryModuleClient().get_inventory_module(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        current_week_data = actual_data["weekly"][CURRENT_WEEK_STR]
        case_size = po.first_line().case_size
        assert actual_data["palletsOnHand"] is None
        assert actual_data["dcExpiringInventory"] == case_size * (
            inventory_expiration_current_date.case_quantity
            + inventory_expiration_date_90_days.case_quantity
            + inventory_expiration_date_9_days.case_quantity
        )

        assert actual_data["unitsInHouseHj"] == case_size * (
            inventory_expiration_current_date.case_quantity
            + inventory_expiration_date_90_days.case_quantity
            + inventory_expiration_date_91_days.case_quantity
            + inventory_expiration_date_9_days.case_quantity
        )

        assert current_week_data["startOfDayUnitsInHouseHJ"] == case_size * (
            inventory_expiration_current_date.case_quantity
            + inventory_expiration_date_90_days.case_quantity
            + inventory_expiration_date_91_days.case_quantity
            + inventory_expiration_date_9_days.case_quantity
        )

        assert current_week_data["unitsExpiring"] == inventory_expiration_date_9_days.case_quantity * case_size
        assert current_week_data["unitsInHouseHJYesterday"] == 0


@pytest.mark.parametrize(
    "inventory_type, wh_inventory_type",
    [[InventoryInputType.GSHEET, InventoryInputType.GSHEET], [InventoryInputType.E2OPEN, InventoryInputType.E2OPEN]],
)
def test_received_column_3pl_manual_and_e2open(inventory_type, wh_inventory_type):
    """
    Test Received column in current week section.

    Test steps:
    1.Generate required data (site config with manual receiving_type and parametrized inv_type (manual and e2open),
    warehouse, sku, ingredient, forecast, inventory and 3 receiving)
    2.Make API call inventory_module endpoint -> 200
    3.Assert that received column calculates as expected
    Expected calculation:
    Received = Sum of all received inventory where received date is within SCM Week dates AND received date > Timestamp
    """
    site_config = SiteConfigs.random_value()
    site_config.is_3pl = True
    site_config.receiving_type = ReceiveInputType.MANUAL
    site_config.inventory_type = inventory_type
    warehouse = dataclasses.replace(
        WarehouseUtils.get_warehouse_by_site_config(site_config=site_config), code=site_config.bob_code
    )
    sku = TestSku.generate_sku()
    po, po_2 = TestPurchaseOrder.generate_inbound_pos(pos_quantity=2, sku=sku, warehouse=warehouse)
    ingredient = TestIngredient(sku, site_config.brand)
    forecast = TestOscar.generate_oscar(sku, site_config=site_config)
    inventory_timestamp_yesterday_date = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po, warehouse=warehouse, inventory_type=wh_inventory_type, snapshot_timestamp=YESTERDAY_DATE
    )
    receiving_current_day = TestReceiving.generate_receiving(
        po=po, site_config=site_config, receive_timestamp=CURRENT_DATE
    )
    receiving_yesterday = TestReceiving.generate_receiving(
        po=po_2, site_config=site_config, receive_timestamp=YESTERDAY_DATE
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po, po_2)
        .with_ingredients(ingredient)
        .with_oscar_forecast(forecast)
        .with_pimt_inventory(inventory_timestamp_yesterday_date)
        .with_receiving(receiving_yesterday, receiving_current_day)
    ):
        response = InventoryModuleClient().get_inventory_module(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        current_week_data = actual_data["weekly"][CURRENT_WEEK_STR]
        assert (
            current_week_data["received"]
            == receiving_current_day.case_count_one_total_units * receiving_current_day.case_size_one
        )
