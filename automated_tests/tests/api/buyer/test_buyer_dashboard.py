import math
import random
from decimal import Decimal

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import NO_IMPACT
from automated_tests.data.constants.base_constants import (
    EmergencyReason,
    PoStatuses,
    PurchasingCategories,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_FORMAT_1,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    YESTERDAY_DATETIME,
)
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import Test<PERSON>car
from automated_tests.data.models.highjump import TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import ingredient_depletion_steps
from automated_tests.services.api.buyer.buyer_client import BuyerClient
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.constants.ordering import IN_PROGRESS_HJ


@pytest.mark.parametrize("week", [CURRENT_WEEK, NEXT_WEEK])
def test_ingredient_depl_values_on_buyer(week):
    """
    Test case checks Ingredient depletion table values on Buyer dashboard
    Test steps:
    1. Generate required data (all data needed for setting ingredient depl), parametrize week to check values for both,
    add 2 skus , one of which should have status No impact (only skus which status isn't NO IMPACT should be shown)
    2. Make an API request and extract data for Ingredient depletion
    3. Check all column values are as expected
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku, sku_not_included = TestSku.generate_skus(sku_quantity=2)
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=YESTERDAY_DATETIME, week=week
    )
    po_autobagger = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, supplier_name="Autobagger - " + site_config.site.code, week=week
    )
    po_in_progress_hj = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, week=week)
    po_received = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, week=week)
    pos = [po_not_delivered, po_in_progress_hj, po_autobagger, po_received]
    (
        po_not_delivered.first_line().qty,
        po_in_progress_hj.first_line().qty,
        po_autobagger.first_line().qty,
        po_received.first_line().qty,
    ) = [data_generator.random_int() for _ in range(4)]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand, week=week)
    ingredients = [TestIngredient(sku=sku_item, brand=site_config.brand) for sku_item in [sku, sku_not_included]]
    mealkit_ingredients = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for ingredient in ingredients
    ]
    hj_receipt_in_progress_hj = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, status=IN_PROGRESS_HJ, po=po_in_progress_hj, week=week, quantity_received=0
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_received, week=week)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config, week=week)
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    oscars = TestOscar.generate_oscar_for_several_weeks(
        site_config=site_config, sku=sku, value=data_generator.random_int(6), week=week
    )
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config, week=week)
    receipt_override = TestReceiptOverride.generate_receipt_override(
        site_config=site_config,
        po=po_received,
        week=week,
    )
    discard = TestDiscard.generate_discard(site_config=site_config, sku=sku, week=week)
    inventory_pull_put = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku, week=week)
    mock_plan_calculation = TestMockPlanCalculation.generate_mock_plan(site_config=site_config)
    commodity_group = data_generator.generate_string()
    category = PurchasingCategories.PRODUCE
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku, sku_not_included)
        .with_purchase_orders(*pos)
        .with_ingredients(*ingredients)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(*mealkit_ingredients)
        .with_hybrid_needs(*hybrid_needs)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
        .with_purchasing_category(category, sku)
        .with_oscar_forecast(*oscars)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_receipt_override(receipt_override)
        .with_hj_receipts(hj_receipt_in_progress_hj, hj_receipt)
        .with_discard(discard)
        .with_mock_plan_calculation(mock_plan_calculation)
        .with_inventory_pull_put(inventory_pull_put)
        .with_buyer_sku(buyer_sku)
    ):
        response = BuyerClient().get_buyer(week=week)
        response_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)[str(week)][
            "depletion"
        ]
        actual_data, weekly_data = response_data[0], response_data[0]["weekly"]
        assert len(response_data) == 1, "Sku which has status NO Impact should not be shown on Buyer dashboard"

        ing_depl_response = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=week)
        ing_depl_data = base_steps.process_response_and_extract_data(
            response=ing_depl_response, additional_keys=[site_config.site.code], expected_length=2
        )

        expected_oscar_value = next(oscar.value for oscar in oscars if oscar.week == week)
        hybrid_need_quantities = sum(hybrid_need.quantity for hybrid_need in hybrid_needs)
        next_week_plan_val = sum(
            [round(weight * oscar.value) for weight, oscar in zip(mock_plan_calculation.weights, oscars)]
        )

        if week == CURRENT_WEEK:
            exp_plan_value = hybrid_need_quantities
            exp_delta = 1 - math.ceil(expected_oscar_value) / hybrid_need_quantities
            exp_units_in_house_hj = hj_pallet_snapshot.pallet_quantity
            exp_row_need = sum(hn.quantity for hn in hybrid_needs if hn.date >= CURRENT_DATE)
        else:
            exp_plan_value = next_week_plan_val
            exp_delta = 1 - math.ceil(expected_oscar_value) / next_week_plan_val
            exp_units_in_house_hj = 0
            exp_row_need = next_week_plan_val

        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == category
        assert actual_data["commodityGroup"] == commodity_group
        assert actual_data["impactedRecipes"] == mealkit.slot
        assert actual_data["plan"] == exp_plan_value
        assert actual_data["forecastOscar"] == expected_oscar_value
        assert Decimal("%.4f" % (actual_data["delta"])) == Decimal("%.4f" % exp_delta)
        assert actual_data["sn"] == next(
            item
            for item in (ingredient_depletion_steps.get_expected_supplement_need(data) for data in ing_depl_data)
            if item != NO_IMPACT
        )
        assert weekly_data["unitsNeeded"] == expected_oscar_value
        assert weekly_data["unitsOrdered"] == sum(po.first_line().qty for po in pos)
        assert weekly_data["unitsReceived"] == receipt_override.quantity
        assert weekly_data["unitsInHouseHj"] == exp_units_in_house_hj
        assert weekly_data["rowNeed"] == exp_row_need
        assert weekly_data["unitsInHouseMinusRowNeed"] == weekly_data["unitsInHouseHj"] - weekly_data["rowNeed"]
        assert weekly_data["nextWeekForecast"] == expected_oscar_value
        assert (
            weekly_data["unitsInHouseMinRowNeedMinForecast"]
            == weekly_data["unitsInHouseHj"] - weekly_data["rowNeed"] - weekly_data["nextWeekForecast"]
        )
        assert weekly_data["unitsToProduceByAutobagger"] == po_autobagger.first_line().qty
        assert weekly_data["inventory"] == inventory_pull_put.quantity
        assert weekly_data["discards"] == discard.quantity
        assert weekly_data["pulls"] == 0
        assert (
            weekly_data["totalOnHand"]
            == weekly_data["unitsReceived"] + weekly_data["inventory"] - weekly_data["discards"] - weekly_data["pulls"]
        )
        assert weekly_data["onHandMinProductionNeeds"] == weekly_data["totalOnHand"] - weekly_data["unitsNeeded"]
        assert weekly_data["inProgressHj"] == po_in_progress_hj.first_line().qty
        assert weekly_data["awaitingDelivery"] == po_autobagger.first_line().qty
        assert weekly_data["notDelivered"] == po_not_delivered.first_line().qty
        assert (
            weekly_data["bufferQuantity"]
            == weekly_data["onHandMinProductionNeeds"]
            + weekly_data["inProgressHj"]
            + weekly_data["awaitingDelivery"]
            + weekly_data["notDelivered"]
        )
        assert weekly_data["bufferPercent"] == round(weekly_data["bufferQuantity"] / weekly_data["unitsNeeded"], 4)
        assert actual_data["brand"] == site_config.brand.code
        assert actual_data["site"] == site_config.site.code


def test_po_week_dashboard_on_buyer():
    """
    Test case checks PO week dashboard on Buyer.
    Test steps:
    1. Generate required test data with site_config, sku, buyer_sku, pos with received_under, receiver_over,
    received_accurate, not_delivered_past_due statuses, hj_receipts
    2. Make an API call and extract data
    3. Check all column values are as expected.
    Expected result: Only pos that have received_under, receiver_over, not_delivered_past_due statuses should be shown
    on the table.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_received_over, po_received_under, po_received_accurate = TestPurchaseOrder.generate_pos(
        quantity=3,
        site=site_config,
        sku=sku,
        delivery_time_start=CURRENT_DATETIME,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, delivery_time_start=YESTERDAY_DATETIME, emergency_reason=EmergencyReason.SAFETY_STOCK
    )
    pos = [po_received_over, po_received_under, po_not_delivered]
    hj_receipt_under = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        receipt_time_est=CURRENT_DATETIME,
        po=po_received_under,
        quantity_received=int(po_received_under.first_line().qty - random.randint(1, 100)),
    )
    hj_receipt_over = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        receipt_time_est=CURRENT_DATETIME,
        po=po_received_over,
        quantity_received=int(po_received_over.first_line().qty + random.randint(1, 100)),
    )
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    category = PurchasingCategories.PRODUCE
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(*pos, po_received_accurate)
        .with_purchasing_category(category, sku)
        .with_buyer_sku(buyer_sku)
        .with_hj_receipts(hj_receipt_under, hj_receipt_over)
        .with_oscar_forecast(oscar)
    ):
        response = BuyerClient().get_buyer(week=CURRENT_WEEK)
        actual_data = {
            data["poNumber"]: data
            for data in base_steps.process_response_and_extract_data(response=response, expected_length=2)[
                CURRENT_WEEK_STR
            ]["poStatus"]
        }
        assert len(actual_data) == 3, (
            "There should be shown 3 records for the pos that have statuses received_over, "
            "received_under, not_delivered_past_due."
        )
        expected_statuses = [PoStatuses.RECEIVED_OVER, PoStatuses.RECEIVED_UNDER, PoStatuses.NOT_DELIVERED_PAST_DUE]
        for po, status in zip(pos, expected_statuses):
            assert actual_data[po.po_number]["supplier"] == po.supplier_name
            assert actual_data[po.po_number]["poNumber"] == po.po_number
            assert actual_data[po.po_number]["sku"] == sku.sku_code
            assert actual_data[po.po_number]["skuName"] == sku.sku_name
            assert actual_data[po.po_number]["category"] == category
            assert actual_data[po.po_number]["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
            assert actual_data[po.po_number]["poStatus"] == status
            assert actual_data[po.po_number]["orderSize"] == po.first_line().order_size
            assert Decimal(str(actual_data[po.po_number]["casePrice"])) == Decimal(po.first_line().case_price)
            assert actual_data[po.po_number]["caseSize"] == po.first_line().case_size
            assert actual_data[po.po_number]["quantityOrdered"] == po.first_line().qty
            po_mapping = {
                po_received_over.po_number: hj_receipt_over,
                po_received_under.po_number: hj_receipt_under,
            }
            if po.po_number in po_mapping:
                hj_receipt = po_mapping[po.po_number]
            if po == po_received_under or po == po_received_over:
                assert actual_data[po.po_number]["quantityReceived"] == hj_receipt.quantity_received
                assert actual_data[po.po_number]["casesReceived"] == hj_receipt.cases_received
                assert actual_data[po.po_number]["receiveVariance"] == abs(
                    actual_data[po.po_number]["quantityOrdered"] - actual_data[po.po_number]["quantityReceived"]
                )
                assert actual_data[po.po_number]["caseSizeReceived"] == round(
                    hj_receipt.quantity_received / hj_receipt.cases_received, 3
                )
                assert round(Decimal(str(actual_data[po.po_number]["totalPriceReceived"])), 2) == round(
                    hj_receipt.quantity_received * (po.first_line().case_price / po.first_line().case_size),
                    2,
                )
            else:
                assert actual_data[po.po_number]["quantityReceived"] == 0
                assert actual_data[po.po_number]["casesReceived"] == 0
                assert actual_data[po.po_number]["caseSizeReceived"] == 0
                assert actual_data[po.po_number]["receiveVariance"] == 0
                assert actual_data[po.po_number]["totalPriceReceived"] == 0
            assert Decimal(str(actual_data[po.po_number]["totalPrice"])) == Decimal(po.first_line().total_price)
            assert actual_data[po.po_number]["emergencyReason"] == po.emergency_reason
            assert actual_data[po.po_number]["forecastDeliveryPercent"] == round(
                (
                    max(actual_data[po.po_number]["quantityOrdered"], actual_data[po.po_number]["quantityReceived"])
                    / int(oscar.value)
                ),
                4,
            )
            assert actual_data[po.po_number]["brand"] == site_config.brand.code
            assert actual_data[po.po_number]["site"] == site_config.site.code


def test_po_scheduled_delivery_today_dashboard_on_buyer():
    """
    Test case checks PO Scheduled for Delivery Today dashboard on Buyer.
    Test steps:
    1. Generate required test data with site_config, sku, po, hj_receipt, buyer_sku, set receiving type is HJ and
    Quantity Received < Quantity Ordered to be sure PO Status is Received-Under
    2. Make an API call and extract data
    3. Check all column values are as expected.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config,
        sku=sku,
        delivery_time_start=CURRENT_DATETIME,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, sku=sku, po=po, quantity_received=int(po.first_line().qty - random.randint(1, 100))
    )
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    category = PurchasingCategories.PRODUCE
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_purchasing_category(category, sku)
        .with_buyer_sku(buyer_sku)
        .with_hj_receipts(hj_receipt)
        .with_oscar_forecast(oscar)
    ):
        response = BuyerClient().get_buyer(week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)[
            CURRENT_WEEK_STR
        ]["poStatusToday"][0]
        assert actual_data["supplier"] == po.supplier_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == category
        assert actual_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["poStatus"] == PoStatuses.RECEIVED_UNDER
        assert actual_data["receiveVariance"] == abs(actual_data["quantityOrdered"] - actual_data["quantityReceived"])
        assert actual_data["orderSize"] == po.first_line().order_size
        assert Decimal(str(actual_data["casePrice"])) == Decimal(po.first_line().case_price)
        assert actual_data["caseSize"] == po.first_line().case_size
        assert actual_data["caseSizeReceived"] == round(hj_receipt.quantity_received / hj_receipt.cases_received, 3)
        assert actual_data["quantityOrdered"] == po.first_line().qty
        assert actual_data["quantityReceived"] == hj_receipt.quantity_received
        assert actual_data["casesReceived"] == hj_receipt.cases_received
        assert Decimal(str(actual_data["totalPrice"])) == Decimal(po.first_line().total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            hj_receipt.quantity_received * (po.first_line().case_price / po.first_line().case_size),
            2,
        )
        assert actual_data["emergencyReason"] == po.emergency_reason
        assert actual_data["forecastDeliveryPercent"] == round(
            (max(actual_data["quantityOrdered"], actual_data["quantityReceived"]) / int(oscar.value)), 4
        )
        assert actual_data["brand"] == site_config.brand.code
        assert actual_data["site"] == site_config.site.code
