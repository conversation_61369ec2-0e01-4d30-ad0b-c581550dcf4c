import random

from automated_tests.data.constants.base_constants import Warehouses
from automated_tests.data.models.supplier import TestSupplier
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.pimt.warehouses_client import WarehousesClient
from automated_tests.services.api.api_steps import base_steps


def test_warehouse_creation():
    """
    Test adding new PIMT warehouse
    Test steps:
    1.Generate test data for the warehouse
    2.Prepare the test environment
    3.Send a POST request to the warehouse_endpoint -> 200
    4.Send a GET request to the warehouse_endpoint -> 200
    5.Assert that the GET response matches the POSTed data.
    """
    warehouse = Warehouses.random_value()
    supplier = TestSupplier.generate_supplier(name=warehouse.ot_suppliers[0])
    with TestData.root_data().with_supplier(supplier):
        try:
            post_response = WarehousesClient().add_warehouse_config(
                wh_code=warehouse.code,
                name=warehouse.warehouse_name,
                ot_dcs=warehouse.ot_dcs,
                ot_suppliers=[supplier.name],
                region=warehouse.region,
                regional_dcs=warehouse.regional_dcs,
                packaging_regions=warehouse.packaging_regions,
                receiving_type=warehouse.receiving_type,
                inventory_type=warehouse.inventory_type,
                is_3rd_party=warehouse.is_3rd_party,
                bob_code=warehouse.bob_code,
                hj_name=warehouse.hj_name,
            )
            assert post_response.status_code == 200, post_response
            get_response = WarehousesClient().get_warehouses()
            processed_resp_data = base_steps.process_response_and_extract_data(get_response, element_index=0)
            assert processed_resp_data["bobCode"] == warehouse.bob_code
            assert processed_resp_data["hjName"] == warehouse.hj_name
            assert processed_resp_data["inventoryType"] == warehouse.inventory_type
            assert processed_resp_data["is3rdParty"] == warehouse.is_3rd_party
            assert processed_resp_data["name"] == warehouse.warehouse_name
            assert processed_resp_data["otDcs"] == warehouse.ot_dcs
            assert processed_resp_data["otSuppliers"] == [supplier.name]
            assert processed_resp_data["partnerCode"] == warehouse.code
            assert processed_resp_data["receivingType"] == warehouse.receiving_type
            assert processed_resp_data["region"] == warehouse.region
            assert processed_resp_data["regionalDCs"] == warehouse.regional_dcs
            assert processed_resp_data["packagingRegions"] == warehouse.packaging_regions
        finally:
            WarehousesClient().delete_warehouse_config(warehouse.code)


def test_warehouse_editing():
    """
    Test editing the existing PIMT warehouse
    Test steps:
    1.Generate test data for the two warehouses
    2.Prepare the test environment with
    3.Send a PATCH request to the warehouse_endpoint with the updated data -> 200
    4.Send a GET request to the warehouse_endpoint -> 200
    5.Assert that the GET response matches the updated data
    """
    warehouse_to_edit = Warehouses.UNCOMMON.value
    warehouse_with_another_data = Warehouses.CASTELLINI.value
    supplier = TestSupplier.generate_supplier(name=warehouse_with_another_data.ot_suppliers[0])
    with TestData.root_data().with_warehouses(warehouse_to_edit).with_supplier(supplier):
        patch_response = WarehousesClient().edit_warehouse_config(
            wh_code_to_edit=warehouse_to_edit.code,
            new_name=warehouse_with_another_data.warehouse_name,
            new_ot_dcs=warehouse_with_another_data.ot_dcs,
            new_ot_suppliers=[supplier.name],
            new_region=warehouse_with_another_data.region,
            new_regional_dcs=warehouse_with_another_data.regional_dcs,
            new_packaging_regions=warehouse_with_another_data.packaging_regions,
            new_receiving_type=warehouse_with_another_data.receiving_type,
            new_inventory_type=warehouse_with_another_data.inventory_type,
            new_is_3rd_party=warehouse_with_another_data.is_3rd_party,
            new_bob_code=warehouse_with_another_data.bob_code,
            new_hj_name=warehouse_with_another_data.hj_name,
        )
        assert patch_response.status_code == 200, patch_response
        get_response = WarehousesClient().get_warehouses()
        processed_resp_data = base_steps.process_response_and_extract_data(get_response, element_index=0)
        assert processed_resp_data["partnerCode"] == warehouse_to_edit.code
        assert processed_resp_data["bobCode"] == warehouse_with_another_data.bob_code
        assert processed_resp_data["hjName"] == warehouse_with_another_data.hj_name
        assert processed_resp_data["inventoryType"] == warehouse_with_another_data.inventory_type
        assert processed_resp_data["is3rdParty"] == warehouse_with_another_data.is_3rd_party
        assert processed_resp_data["name"] == warehouse_with_another_data.warehouse_name
        assert processed_resp_data["otDcs"] == warehouse_with_another_data.ot_dcs
        assert processed_resp_data["otSuppliers"] == [supplier.name]
        assert processed_resp_data["receivingType"] == warehouse_with_another_data.receiving_type
        assert processed_resp_data["region"] == warehouse_with_another_data.region
        assert processed_resp_data["regionalDCs"] == warehouse_with_another_data.regional_dcs
        assert processed_resp_data["packagingRegions"] == warehouse_with_another_data.packaging_regions


def test_warehouse_removing():
    """
    Test deleting the existing PIMT warehouse
    Test steps:
    1.Generate test data for the warehouse
    2.Prepare the test environment
    3.Send a DELETE request to the warehouse_endpoint with the warehouse code -> 204
    4.Send a GET request to the warehouse_endpoint -> 200
    5.Assert that the GET response returns 0 warehouses
    """
    warehouse_to_remove = Warehouses.random_value()
    with TestData.root_data().with_warehouses(warehouse_to_remove):
        delete_response = WarehousesClient().delete_warehouse_config(warehouse_to_remove.code)
        assert delete_response.status_code == 204, delete_response
        get_response = WarehousesClient().get_warehouses()
        base_steps.process_response_and_extract_data(response=get_response, expected_length=0)


def test_warehouse_change_sequence():
    """
    Test changing the existing PIMT warehouse sequence
    Test steps:
    1.Generate test data for the warehouses
    2.Prepare the test environment
    3.Retrieve the sequence of all warehouses and shuffle it
    4.Send a POST request with the shuffled sequence to the warehouse_order_endpoint -> 200
    5.Send a GET request to the warehouse_order_endpoint -> 200
    6.Verify that the sequence returned in the GET response matches the shuffled sequence
    """
    warehouses = Warehouses.get_all_values()
    with TestData.root_data().with_warehouses(*warehouses):
        current_sequence = []
        for warehouse in warehouses:
            current_sequence.append({"partnerCode": warehouse.code, "sequenceNumber": warehouse.warehouse_order})

        sequence_numbers = [warehouse["sequenceNumber"] for warehouse in current_sequence]
        random.shuffle(sequence_numbers)
        payload = []
        for i, warehouse in enumerate(current_sequence):
            payload.append({"partnerCode": warehouse["partnerCode"], "sequenceNumber": sequence_numbers[i]})

        post_response = WarehousesClient().set_warehouses_order(payload)
        assert post_response.status_code == 200, post_response
        get_response = WarehousesClient().get_warehouses_order()
        data_of_get_response = base_steps.process_response_and_extract_data(
            response=get_response, expected_length=len(warehouses)
        )
        assert data_of_get_response == payload
