from http import HTTPStatus

from dateutil.relativedelta import relativedelta

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import ReleaseLogTypes, SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, TOMORROW_DATE, YESTERDAY_DATE
from automated_tests.data.constants.error_messages_constants import (
    RELEASE_LOG_DOES_NOT_EXIST,
    RELEASE_LOG_WITH_THE_DATE_ALREADY_EXISTS,
)
from automated_tests.data.models.release_log import TestReleaseLog
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.logs.release_log_client import <PERSON><PERSON>og<PERSON><PERSON>
from automated_tests.services.api.api_steps import base_steps


def test_release_log_creation():
    """
    Test release log creation.

    Test steps:
    1.Generate required data (site configs, release log features with 3 different types: NEW, ONGOING, FIX)
    2.Add release log features for current date using post method
    3.Assert that the response status code after adding release log features is HTTPStatus.CREATED.
    4.Assert that the features in the get response match the expected release log features.
    """
    site_configs = SiteConfigs.random_value()
    with TestData(None).with_site_configs(site_configs).with_users(Users.test_user.value):
        release_log_features = [
            {"dcUsersAvailable": True, "description": data_generator.generate_string(), "type": ReleaseLogTypes.NEW},
            {
                "dcUsersAvailable": True,
                "description": data_generator.generate_string(),
                "type": ReleaseLogTypes.ONGOING,
            },
            {"dcUsersAvailable": True, "description": data_generator.generate_string(), "type": ReleaseLogTypes.FIX},
        ]
        post_response = ReleaseLogClient().add_release_log(features=release_log_features, release_date=CURRENT_DATE)
        assert post_response.status_code == HTTPStatus.CREATED

        get_response = ReleaseLogClient().get_release_log()
        actual_data = base_steps.process_response_and_extract_data(response=get_response, element_index=0)
        features_actual_data = actual_data["features"]
        assert actual_data["releaseDate"] == str(CURRENT_DATE)
        for expected_feature, actual_feature in zip(release_log_features, features_actual_data):
            assert actual_feature["type"] == expected_feature["type"]
            assert actual_feature["description"] == expected_feature["description"]
            assert actual_feature["dcUsersAvailable"] is expected_feature["dcUsersAvailable"]


def test_edit_release_log():
    """
    Test release log editing.

    Test steps:
    1.Generate required data (site configs, release log, data for editing)
    2.Using GET method check the order of release logs by type (the first fix, the seconds new)
    3.Swap the records and update the first records with PUT method -> 200
    4.Get the first record that previously was the second one and check that this record was updated
    """
    site_configs = SiteConfigs.random_value()
    release_log_fix = TestReleaseLog.generate_release_log(feature_key=1, feature_type=ReleaseLogTypes.FIX)
    release_log_new = TestReleaseLog.generate_release_log(feature_key=2, feature_type=ReleaseLogTypes.NEW)
    description_to_edit = data_generator.generate_string()
    feature_type_to_edit = ReleaseLogTypes.ONGOING
    dc_user_to_edit = False
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_fix, release_log_new)
    ):
        get_response = ReleaseLogClient().get_release_log()
        actual_data = base_steps.process_response_and_extract_data(response=get_response, element_index=0)["features"]
        assert actual_data[0]["type"] == release_log_fix.feature_type
        assert actual_data[1]["type"] == release_log_new.feature_type

        release_log_features = [
            {"dcUsersAvailable": dc_user_to_edit, "description": description_to_edit, "type": feature_type_to_edit},
            {
                "dcUsersAvailable": release_log_fix.dc_user_available,
                "description": release_log_fix.description,
                "type": release_log_fix.feature_type,
            },
        ]
        put_response = ReleaseLogClient().edit_release_log(features=release_log_features, release_date=CURRENT_DATE)
        assert put_response.status_code == HTTPStatus.OK

        get_response = ReleaseLogClient().get_release_log()
        actual_data = base_steps.process_response_and_extract_data(response=get_response, element_index=0)["features"][
            0
        ]
        assert actual_data["dcUsersAvailable"] == dc_user_to_edit
        assert actual_data["description"] == description_to_edit
        assert actual_data["type"] == feature_type_to_edit


def test_release_logs_for_dc_user():
    """
    Test release log for DC User. If flag "dcUsersAvailable" is False then this record should be unavailable for DC User

    Test steps:
    1.Generate required data (site configs, release log with dcUsersAvailable true, and another one with false, dc user)
    2.Make API call to release_log endpoint
    3.Check that for DC User response returns only 1 records that have dcUsersAvailable flag - true
    """
    site_configs = SiteConfigs.random_value()
    release_log_available_dc_user = TestReleaseLog.generate_release_log(feature_key=1, dc_user_available=False)
    release_log_non_available_dc_user = TestReleaseLog.generate_release_log(feature_key=2, dc_user_available=True)
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_users(Users.dc_user.value)
        .with_release_log(release_log_available_dc_user, release_log_non_available_dc_user)
    ):
        get_response = ReleaseLogClient(Users.dc_user.value).get_latest_release_log()
        actual_data = base_steps.process_response_and_extract_data(response=get_response, element_index=0)["features"]
        assert len(actual_data) == 1
        assert next(iter(actual_data))["dcUsersAvailable"] is True


def test_latest_release_log():
    """
    Test latest release log endpoint. This endpoint should return records that match with next condition:
    release_date > today() - 2 month

    Test steps:
    1.Generate required data (site configs, release logs for cur date, for 1 month ago and for 2 months ago)
    2.Make API call to latest_release_log endpoint -> 200
    3.Assert that response returns only 2 records (for current_date and for 1 month ago)
    """
    site_configs = SiteConfigs.random_value()
    release_log_current_date = TestReleaseLog.generate_release_log(feature_key=1, release_date=CURRENT_DATE)
    release_log_one_month_ago_date = TestReleaseLog.generate_release_log(
        feature_key=2, release_date=CURRENT_DATE - relativedelta(months=1)
    )
    release_log_two_month_ago_date = TestReleaseLog.generate_release_log(
        feature_key=3, release_date=CURRENT_DATE - relativedelta(months=2)
    )
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_current_date, release_log_one_month_ago_date, release_log_two_month_ago_date)
    ):
        get_response = ReleaseLogClient().get_latest_release_log()
        actual_data = base_steps.process_response_and_extract_data(response=get_response, expected_length=2)
        actual_dates = [(actual_data[row]["releaseDate"]) for row in range(len(actual_data))]
        assert actual_dates == [
            str(release_log_current_date.release_date),
            str(release_log_one_month_ago_date.release_date),
        ]


def test_get_date_from_option():
    """
    Test date from parameter in GET request. This parameter filter logs with the next logic: release_date <= date_from

    1.Generate required data (site config, release logs with current date, tomorrow date and yesterday date)
    2.Make API call to release_log endpoint with date from = current date parameter -> 200
    3.Check that response return 2 records with where release_date <= date_from
    """
    site_configs = SiteConfigs.random_value()
    release_log_current_date = TestReleaseLog.generate_release_log(feature_key=1, release_date=CURRENT_DATE)
    release_log_tomorrow_date = TestReleaseLog.generate_release_log(feature_key=2, release_date=TOMORROW_DATE)
    release_log_yesterday_date = TestReleaseLog.generate_release_log(feature_key=3, release_date=YESTERDAY_DATE)
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_current_date, release_log_tomorrow_date, release_log_yesterday_date)
    ):
        get_response = ReleaseLogClient().get_release_log(date_from=CURRENT_DATE)
        actual_data = base_steps.process_response_and_extract_data(response=get_response, expected_length=2)
        actual_dates = [(actual_data[row]["releaseDate"]) for row in range(len(actual_data))]
        assert actual_dates == [
            str(release_log_current_date.release_date),
            str(release_log_yesterday_date.release_date),
        ]


def test_error_message_release_log_record_already_exist():
    """
    Test error message that release log record already exist. Post method returns an error if a record for that date
    already exists

    Test steps:
    1.Generate required data(site_config, release_log_current_date)
    2.Add one more record using post method for the current date
    3.Assert that the response status code after adding record is HTTPStatus.BAD_REQUEST and error message
    "A release log with the date {date} already exists." is present
    """
    site_configs = SiteConfigs.random_value()
    release_log_current_date = TestReleaseLog.generate_release_log(feature_key=1, release_date=CURRENT_DATE)
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_current_date)
    ):
        release_log_features = [
            {
                "dcUsersAvailable": True,
                "description": data_generator.generate_string(),
                "type": ReleaseLogTypes.random_value(),
            }
        ]
        post_response = ReleaseLogClient().add_release_log(features=release_log_features, release_date=CURRENT_DATE)
        assert post_response.status_code == HTTPStatus.BAD_REQUEST
        assert post_response.json()["error"] == RELEASE_LOG_WITH_THE_DATE_ALREADY_EXISTS.format(date=str(CURRENT_DATE))


def test_error_release_log_record_does_not_exist():
    """
    Test error message that release log doest exist. Put method returns an error that record does not exist

    Test steps:
    1.Generate required data(site_config)
    2.Update not existing record with put method
    3.Assert that the response status code after editing record is HTTPStatus.BAD_REQUEST and error message
    "Release log with release date {date} does not exist." is present
    """
    site_configs = SiteConfigs.random_value()
    with TestData(None).with_site_configs(site_configs).with_users(Users.test_user.value):
        release_log_features = [
            {
                "dcUsersAvailable": True,
                "description": data_generator.generate_string(),
                "type": ReleaseLogTypes.random_value(),
            }
        ]
        put_response = ReleaseLogClient().edit_release_log(features=release_log_features, release_date=CURRENT_DATE)
        assert put_response.status_code == HTTPStatus.BAD_REQUEST
        assert put_response.json()["error"] == RELEASE_LOG_DOES_NOT_EXIST.format(date=str(CURRENT_DATE))
