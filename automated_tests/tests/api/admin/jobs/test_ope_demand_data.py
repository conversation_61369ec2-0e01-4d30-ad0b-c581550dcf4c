import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import MultiMarketUsers, SiteConfigs, SiteConfigsCA
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_WEEK
from automated_tests.data.models.forecast_upload import TestForecastUploadItem
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.forecast_push_data import ForecastPushDataClient
from automated_tests.services.api.api_steps import base_steps


@pytest.mark.parametrize("site_config", [SiteConfigs.random_value(), SiteConfigsCA.random_value()])
def test_ope_demand_data_values(site_config):
    """
    Test expected OPE SKU Demand values for US and CA.

    Test steps:
    1. Generate required data (site_config, sku, forecast upload data)
    2. Make API call to forecast_push_data endpoint -> 200
    3. Assert that values from response are equal to forecast_upload data
    """
    sku = TestSku.generate_sku()
    forecast_upload = TestForecastUploadItem.generate_forecasts_upload(
        site_config=site_config, cache_data={(sku.sku_code, CURRENT_DATE): {CURRENT_WEEK: data_generator.random_int()}}
    )
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(MultiMarketUsers.test_user.value)
        .with_forecast_upload(forecast_upload)
    ):
        response = ForecastPushDataClient().get_forecast_push_data(site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        cached_quantity = forecast_upload.cache_data[(sku.sku_code, CURRENT_DATE)][CURRENT_WEEK]
        assert actual_data["skuCode"] == sku.sku_code
        assert actual_data["week"] == CURRENT_WEEK
        assert actual_data["day"] == str(CURRENT_DATE)
        assert actual_data["site"] == site_config.site.code
        assert actual_data["brand"] == site_config.brand.code
        assert actual_data["quantity"] == cached_quantity
