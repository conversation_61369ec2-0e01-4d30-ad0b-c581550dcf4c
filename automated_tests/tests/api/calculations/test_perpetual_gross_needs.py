from datetime import timedelta

import pytest

from automated_tests.data.constants.base_constants import PurchasingCategories, SiteConfigs, SiteConfigsFactor, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_FORMAT_2,
    DEFAULT_WEEKS_AHEAD,
    FACTOR_WEEK_CONFIG,
    FACTOR_WEEK_LENGTH,
    NEXT_WEEK,
    NEXT_WEEK_FACTOR,
    YESTERDAY_DATE,
)
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.calculations.perpetual_gross_needs_client import PerpetualGrossNeedsClient
from procurement.constants.hellofresh_constant import (
    PRODUCTION_TYPE_ASSEMBLY,
    PRODUCTION_TYPE_KITTING,
    ReceiveInputType,
)
from procurement.core.dates import ScmWeek, Weekday


@pytest.mark.parametrize(
    "receive_type, is_3pl",
    [
        pytest.param(ReceiveInputType.HIGH_JUMP, False, id="test_core_sites"),
        pytest.param(ReceiveInputType.MANUAL, True, id="test_3pl"),
    ],
)
def test_perpetual_gross_needs_current_week(receive_type, is_3pl):
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receive_type
    site_config.is_3pl = is_3pl
    # we need prev week data, so we set up site to be available from prev week
    site_config.week = ScmWeek.from_date(YESTERDAY_DATE)
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    hybrid_needs_current_week = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    # we add mock plan for current week to ensure it won't be used for the current week calculations
    mock_plan_calculation_current_week = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=CURRENT_WEEK
    )
    commodity_group = generate_string()
    purchasing_category = PurchasingCategories.random_value()
    # we add oscar for current week to ensure it won't be used for the current week calculations
    oscar_current_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_hybrid_needs(*hybrid_needs_current_week)
        .with_mock_plan_calculation(mock_plan_calculation_current_week)
        .with_buyer_sku(buyer_sku)
        .with_commodity_group(group=commodity_group, site=site_config.site.code, sku=sku)
        .with_purchasing_category(purchasing_category, sku=sku)
        .with_oscar_forecast(oscar_current_week)
    ):
        response = PerpetualGrossNeedsClient().get_perpetual_gross_needs(site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == purchasing_category
        assert actual_data["commodityGroup"] == commodity_group
        assert actual_data["buyer"] == buyer_sku.user.full_name
        expected_gross_needs_current_week = {
            str(hybrid_need.date): hybrid_need.quantity for hybrid_need in hybrid_needs_current_week
        }
        actual_gross_needs_current_week = actual_data["weeks"][CURRENT_WEEK_STR]
        assert (
            actual_gross_needs_current_week["days"] == expected_gross_needs_current_week
        ), "Gross needs for current week should be equal to the hybrid needs of the current week"
        assert actual_gross_needs_current_week["sum"] == sum(
            expected_gross_needs_current_week.values()
        ), "Sum of gross needs for the current week should be equal to the sum of hybrid needs for this week"


@pytest.mark.parametrize(
    "is_in_kitbox",
    [
        pytest.param(True, id="test_core_sites_kitting"),
        pytest.param(False, id="test_core_sites_assembly"),
    ],
)
def test_perpetual_gross_needs_next_week_core_site(is_in_kitbox):
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config.is_3pl = False
    # we need prev week data, so we set up site to be available from prev week
    site_config.week = ScmWeek.from_date(YESTERDAY_DATE)
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    # we add hybrid needs for current week to check that it won't be overlapped with forecast for the next week
    hybrid_needs_current_week = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    # we add hybrid needs for next week to check that it won't be used for next week calculations
    hybrid_needs_next_week = TestHybridNeeds.generate_hybrid_needs_for_several_days(
        sku=sku, site_config=site_config, week=NEXT_WEEK
    )
    mock_plan_calculation_next_week_kitting = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=NEXT_WEEK, production_type=PRODUCTION_TYPE_KITTING
    )
    mock_plan_calculation_next_week_assembly = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=NEXT_WEEK, production_type=PRODUCTION_TYPE_ASSEMBLY
    )
    oscar_next_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK)
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand, week=NEXT_WEEK)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit, ingredient, [site_config.site], is_in_kitbox=is_in_kitbox
    )
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_hybrid_needs(*hybrid_needs_current_week, *hybrid_needs_next_week)
        .with_mock_plan_calculation(mock_plan_calculation_next_week_kitting, mock_plan_calculation_next_week_assembly)
        .with_oscar_forecast(oscar_next_week)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
    ):
        response = PerpetualGrossNeedsClient().get_perpetual_gross_needs(site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        actual_gross_needs_next_week = actual_data["weeks"][str(NEXT_WEEK)]
        # if is_in_kitbox= True for mealkit_ingredient, we have to take weights
        # from the mock plan with production_type=kitting, otherwise with production_type=assembly
        mock_plan_to_use = (
            mock_plan_calculation_next_week_kitting if is_in_kitbox else mock_plan_calculation_next_week_assembly
        )
        expected_gross_needs_next_week = [round(oscar_next_week.value * weight) for weight in mock_plan_to_use.weights]
        actual_gross_needs_next_week_daily = list(actual_gross_needs_next_week["days"].values())
        assert (
            actual_gross_needs_next_week_daily == expected_gross_needs_next_week
        ), "Gross needs for the next week should be equal to oscar * weight from the mock plan"
        assert actual_gross_needs_next_week["sum"] == sum(
            expected_gross_needs_next_week
        ), "Sum of gross needs for the next week should be equal to the sum of daily values"


def test_perpetual_gross_needs_future_week_factor():
    """
    Test that Factor should use forecast and mock plan for future weeks, when HN is not present

    Test steps:
    1.Generate required data (Factor site_config, oscar_forecast and mock_plan for future week)
    2.Make a get request to the expected_daily_need_endpoint
    3.Check values for the next week are calculated correctly (round(mock_plan_value * oscar_next_week.value))
    """
    site_config = SiteConfigsFactor.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config.is_3pl = True
    sku = TestSku.generate_sku()
    mock_plan_calculation_next_week = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=NEXT_WEEK_FACTOR
    )
    oscar_next_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK_FACTOR)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_mock_plan_calculation(mock_plan_calculation_next_week)
        .with_oscar_forecast(oscar_next_week)
    ):
        response = PerpetualGrossNeedsClient().get_perpetual_gross_needs(site_config=site_config, week=NEXT_WEEK_FACTOR)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        next_week_data = actual_data["weeks"][str(NEXT_WEEK_FACTOR)]["days"]
        for actual_value, mock_plan_value in zip(next_week_data.values(), mock_plan_calculation_next_week.weights):
            assert actual_value == round(mock_plan_value * oscar_next_week.value)


def test_perpetual_gross_needs_week_extension_for_factor():
    """
    Test PGN week extension for Factor.
    Example:
    Today is Monday 11/8 the forecast period to be displayed is 11/3-12/15.
    On Tuesday 11/9 the forecast period includes 11/3-12/22 (including an additional SCM week from the updated forecast)
    On Wednesday 11/10 the forecast period to be displayed is 11/3-12/22.
    On Thursday 11/11 the forecast period to be displayed is 11/10-12/22.

    Test steps:
    1. Generate required data for set up the board (site config (HJ), sku, HN, oscar for several weeks)
    2.Make API call to expected_daily_need endpoint -> 200
    3.Check that weeks are correctly extended
    """
    start_week = ScmWeek.from_date(CURRENT_DATE - timedelta(days=1), FACTOR_WEEK_CONFIG)
    site_config = SiteConfigsFactor.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(
        sku=sku, site_config=site_config, week=start_week, days_qty=FACTOR_WEEK_LENGTH
    )
    oscars = TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=site_config, week=start_week)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(*oscars)
    ):
        response = PerpetualGrossNeedsClient().get_perpetual_gross_needs(site_config=site_config, week=start_week)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        last_week = start_week + DEFAULT_WEEKS_AHEAD
        if CURRENT_DATE.weekday() in {Weekday.THU, Weekday.FRI}:
            last_week += 1
        last_day = (last_week.get_last_day(FACTOR_WEEK_CONFIG) + timedelta(days=1)).strftime(DATE_FORMAT_2)
        assert max(actual_data["weeks"][str(last_week)]["days"].keys()) == last_day
