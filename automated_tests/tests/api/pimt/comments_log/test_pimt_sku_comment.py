from datetime import datetime, <PERSON><PERSON><PERSON>
from http import HTTPStatus

import pytz

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import PurchasingCategories, Users, Warehouses
from automated_tests.data.constants.date_time_constants import (
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_2,
    US_EASTERN_TIMEZONE,
)
from automated_tests.data.constants.sync_constants import Projects
from automated_tests.data.models.comment import TestComment
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.comment_client import PimtCommentsLogClient
from automated_tests.utils import comparisons, datetime_utils
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import WhReceivingType


def test_pimt_comments_log_sku_comments_values():
    """
    Test case checks Sku comments values on PIMT -> Comments Log dashboard
    Test cases:
    1. Generate required test data (warehouse, sku, inbound po, site config, grn, comments, commodity_group, purchasing
       category)
    2. Make an API call with get request and extract data
    3. Check the data from the api call to be as expected
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
    )
    grn = TestGrn.generate_grn(po=inbound_po, warehouse=warehouse)
    comment = data_generator.generate_string()
    comment_value = TestComment.generate_sku_comment(
        site=warehouse.code, brand=site_config.brand.code, sku=sku, user=user, comment=comment, domain=Projects.PIMT
    )
    commodity_group = data_generator.generate_string()
    purchasing_category = PurchasingCategories.random_value()
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_comments(comment_value)
        .with_grn(grn)
        .with_commodity_group(commodity_group, warehouse.code, sku)
        .with_purchasing_category(purchasing_category, sku)
    ):
        response = PimtCommentsLogClient().get_pimt_comments_log_sku_comments()
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        actual_updated_at = datetime.strptime(actual_data["lastUpdated"], DATE_TIME_FORMAT_1)
        expected_updated_at = datetime_utils.get_datetime_for_specific_timezone(
            timezone=pytz.timezone(US_EASTERN_TIMEZONE)
        )

        assert actual_data["id"] == 1
        assert actual_data["region"] == warehouse.code
        assert actual_data["brand"] == site_config.brand.code
        assert actual_data["skuCode"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["commodityGroup"] == commodity_group
        assert actual_data["purchasingCategory"] == purchasing_category
        assert actual_data["comment"] == comment
        assert actual_data["lastUpdatedBy"] == user.email
        assert comparisons.datetime_is_close(actual_updated_at, expected_updated_at, deviation=timedelta(minutes=1))


def test_sku_comment():
    """
    Test case checks sku comments on PIMT -> PO Status dashboard
    Test steps:
    1. Generate required test data (warehouse, user, site config, inbound po, grn, sku, comments)
    2. Make an API call with a post request to add a comment for sku -> 201
    3. Make an API call with a post request to edit the previous comment -> 201
    4. Make an API call with a get request and extract data
    5. Check the data from the api call to be as expected
    6. Make an API call with delete request -> 204
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
    )
    grn = TestGrn.generate_grn(po=inbound_po, warehouse=warehouse)
    comment_text, comment_to_edit = [data_generator.generate_string() for _ in range(2)]
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_purchasing_category(sku=sku, category=PurchasingCategories.random_value())
        .with_grn(grn)
    ):
        for comment in [comment_text, comment_to_edit]:
            post_request = PimtCommentsLogClient().add_or_edit_sku_comment(
                site_config=site_config, sku=sku, comment=comment, warehouse=warehouse
            )
            assert post_request.status_code == HTTPStatus.CREATED

            get_request = PimtCommentsLogClient().get_sku_comment(site_config=site_config, sku=sku, warehouse=warehouse)
            actual_data = base_steps.process_response_and_extract_data(
                response=get_request, additional_keys=[warehouse.code], expected_length=3
            )

            actual_updated_at = datetime.strptime(actual_data["lastUpdated"], DATE_TIME_FORMAT_2)
            expected_updated_at = datetime_utils.get_datetime_for_specific_timezone(
                timezone=pytz.timezone(US_EASTERN_TIMEZONE)
            )
            assert comparisons.datetime_is_close(actual_updated_at, expected_updated_at, deviation=timedelta(minutes=1))
            assert actual_data["lastUpdatedBy"] == user.email
            assert actual_data["value"] == comment

        delete_response = PimtCommentsLogClient().delete_sku_comment(
            site_config=site_config, warehouse=warehouse, sku=sku
        )
        assert delete_response.status_code == HTTPStatus.NO_CONTENT


def test_sku_comment_max_length():
    """
    Test case checks sku comment max length is 4000.
    Test steps:
    1. Generate required test data (warehouse, user, site config, inbound po, grn, sku, comment with 4001 symbols)
    2. Make an API call with post request -> 400
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
    )
    grn = TestGrn.generate_grn(po=inbound_po, warehouse=warehouse)
    comment = data_generator.generate_string(str_length=4001)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(user)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_grn(grn)
    ):
        post_request = PimtCommentsLogClient().add_or_edit_sku_comment(
            site_config=site_config, sku=sku, comment=comment, warehouse=warehouse
        )
        assert post_request.status_code == HTTPStatus.BAD_REQUEST
