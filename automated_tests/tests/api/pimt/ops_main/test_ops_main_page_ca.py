import dataclasses

from automated_tests.data.constants.base_constants import BrandsCA, UsersCA, WarehousesCA
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, TOMORROW_DATE
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.pimt_ops_main_client import PimtOpsMainClient
from procurement.constants.hellofresh_constant import MARKET_CA, InventoryInputType, InventoryState


def test_enabled_e2_open_for_ca_market():
    """
    Test case checks Available, Units on Hold, Units Booked for Outbound, Total on Hand, Expired Inventory column values
    for CA market with enabled E2Open
    Test steps:
    1. Generate required test data(sku warehouse with E2Open inv type,inbound po, inventories for current and next days)
    2. Make an API request -> 200
    3. Check column values to be as expected.
    Expected calculation:
    Total on Hand: SUM of all Inventory.Quantity * Case Size (PO) IF Inventory.State IN (Available, Booked for Outbound,
     On Hold) AND Inventory.Expiry Date > Today
    Available :SUM of all Inventory.Quantity * Case Size (PO) IF Inventory.State = Available
    Units on Hold: SUM of all Inventory.Quantity * Case Size (PO) IF Inventory.State = On Hold
    Units Booked for Outbound: SUM of all Inventory.Quantity * Case Size (PO) IF Inventory.State = Booked for Outbound
    Expired Inventory: SUM of all Inventory.Quantity * Case Size (PO) IF Inventory.State IN (Available, Booked for
     Outbound, On Hold) AND Inventory.Expiry Date <= Today
    In-House: SUM of all Inventory.Quantity * Case Size (PO) IF Inventory Notification.State IN (Available, Booked for
     Outbound, On Hold) AND Expiry Date > Today
    """
    sku = TestSku.generate_sku(market=MARKET_CA)
    warehouse = dataclasses.replace(WarehousesCA.random_value(), inventory_type=InventoryInputType.E2OPEN)
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    inventory_available = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, expiration_date=CURRENT_DATE, inventory_status=InventoryState.AVAILABLE
    )
    inventory_available_tomorrow = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, expiration_date=TOMORROW_DATE, inventory_status=InventoryState.AVAILABLE
    )
    inventory_on_hold = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, expiration_date=CURRENT_DATE, inventory_status=InventoryState.ON_HOLD
    )
    inventory_on_hold_tomorrow = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, expiration_date=TOMORROW_DATE, inventory_status=InventoryState.ON_HOLD
    )
    inventory_booked_for_outbound = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po,
        warehouse=warehouse,
        expiration_date=CURRENT_DATE,
        inventory_status=InventoryState.BOOKED_FOR_OUTBOUND,
    )
    inventory_booked_for_outbound_tomorrow = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po,
        warehouse=warehouse,
        expiration_date=TOMORROW_DATE,
        inventory_status=InventoryState.BOOKED_FOR_OUTBOUND,
    )
    inventory_total = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, inventory_status=InventoryState.TOTAL
    )
    inventory_unspecified = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, warehouse=warehouse, inventory_status=InventoryState.UNSPECIFIED
    )
    with (
        TestData(None)
        .with_brands(BrandsCA.HF.value)
        .with_users(UsersCA.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_pimt_inventory(
            inventory_available,
            inventory_on_hold,
            inventory_booked_for_outbound,
            inventory_total,
            inventory_unspecified,
            inventory_available_tomorrow,
            inventory_on_hold_tomorrow,
            inventory_booked_for_outbound_tomorrow,
        )
    ):
        response = PimtOpsMainClient().get_pimt_ops_main(market=MARKET_CA)
        actual_data = base_steps.process_response_and_extract_data(response=response, expected_length=3)["total"]
        case_size, case_price = inbound_po.first_line().case_size, inbound_po.first_line().case_price
        expected_available_today = inventory_available.case_quantity * case_size
        expected_available_tomorrow = inventory_available_tomorrow.case_quantity * case_size
        expected_on_hold_today = inventory_on_hold.case_quantity * case_size
        expected_on_hold_tomorrow = inventory_on_hold_tomorrow.case_quantity * case_size
        expected_booked_today = inventory_booked_for_outbound.case_quantity * case_size
        expected_booked_tomorrow = inventory_booked_for_outbound_tomorrow.case_quantity * case_size
        expected_in_house = (
            inventory_available_tomorrow.case_quantity
            + inventory_booked_for_outbound_tomorrow.case_quantity
            + inventory_on_hold_tomorrow.case_quantity
        ) * case_price
        assert (
            actual_data["totalOnHand"]["unitsCount"]
            == expected_available_tomorrow + expected_on_hold_tomorrow + expected_booked_tomorrow
        )
        assert actual_data["available"] == expected_available_today + expected_available_tomorrow
        assert actual_data["unitsOnHold"] == expected_on_hold_today + expected_on_hold_tomorrow
        assert actual_data["unitsBookedForOutbound"] == expected_booked_today + expected_booked_tomorrow
        assert (
            actual_data["expiredInventory"] == expected_available_today + expected_on_hold_today + expected_booked_today
        )
        assert actual_data["totalOnHand"]["cost"] == float(expected_in_house)
