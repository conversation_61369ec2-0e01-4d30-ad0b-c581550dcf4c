import dataclasses

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import WhRegionApiInput
from automated_tests.data.constants.base_constants import (
    EmergencyReason,
    PoStatuses,
    ShippingMethods,
    SiteConfigs,
    SkuNames,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_1,
    NEXT_WEEK,
    NEXT_WEEK_STR,
)
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.packaging_demand import TestPackagingDemand
from automated_tests.data.models.packaging_long_term_forecast import (
    TestDemandPipelineSkuMapping,
    TestPackagingLongTermForecast,
)
from automated_tests.data.models.packaging_sku_import import TestPackagingSkuImport
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.vendor_managed_inventory import TestVendorManagedInventory
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.ambient_safety_stock_client import AmbientSafetyStockClient
from automated_tests.utils import comparisons
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import BRAND_HF, PoType, ReceiveInputType, WhReceivingType
from procurement.managers.pimt.packaging_safety_stock.context import REGIONAL_TOTAL_OWNERSHIP, OWNED_BY_TEMPlATE

_HF_OWNED = OWNED_BY_TEMPlATE.format(BRAND_HF)


def test_region_demand_combination_in_ambient_safety_stock():
    """
    Test region and demand pipeline combinations on Ambient Safety Stock.

    This test checks next cases where:
    1. Warehouses in the same region have the same packaging region but different demand pipelines, resulting in
     separate records.
    2. Warehouses in the same region have different packaging regions but the same demand pipelines, also resulting in
     separate records.

    Test Steps:
    1. Generate required data (3 warehouses in the same region (two of the warehouses share the same packaging region
    (UNCOMMON, FIRST_CHOICE in test), 3 SKUs, 3 demand pipelines, with two SKUs sharing the same demand pipeline)
    2. Make an API call to ambient_safety_stock endpoint -> 200.
    3. Check that the response contains 3 separate records.
    """
    warehouse_1 = dataclasses.replace(Warehouses.UNCOMMON.value, region=WhRegionApiInput.WEST)
    warehouse_2 = dataclasses.replace(Warehouses.FIRST_CHOICE.value, region=WhRegionApiInput.WEST)
    warehouse_3 = dataclasses.replace(Warehouses.DREISBACH.value, region=WhRegionApiInput.WEST)
    warehouses = [warehouse_1, warehouse_2, warehouse_3]
    skus = sku, sku_2, sku_3 = TestSku.generate_skus(sku_quantity=3)
    packaging_sku_imports = [
        TestPackagingSkuImport.generate_packaging_sku_import(bob_code=warehouse.packaging_regions[0], sku=sku)
        for warehouse, sku in zip(warehouses, skus)
    ]
    pipeline = data_generator.generate_string()
    demand_pipeline_sku_1 = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku)
    demand_pipeline_sku_2 = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku_2, demand_pipeline=pipeline)
    demand_pipeline_sku_3 = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku_3, demand_pipeline=pipeline)
    with (
        TestData.root_data()
        .with_warehouses(*warehouses)
        .with_skus(sku, sku_2, sku_3)
        .with_packaging_sku_import(*packaging_sku_imports)
        .with_demand_pipeline(demand_pipeline_sku_1, demand_pipeline_sku_2, demand_pipeline_sku_3)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse_1)
        base_steps.process_response_and_extract_data(response=response, expected_length=3)


def test_supplier_level_in_ambient_safety_stock():
    """
    Test supplier level in Ambient Safety stock for packaging summary, current week and next week sections.

    Test steps:
    1.Generate required data (warehouse, sku, demand pipeline, packaging sku import, forecasts, pos, inventories)
    2.Make API call to ambient_safety_stock_endpoint -> 200
    3.Check that values are equal to expected calculations.
    Expected calculations for Current week:
    hfOwnedBoh = inventory.case_quantity * po.case_size for HF Ownership and 0 for vendor ownership
    vendorOwnedBoh = vendor_managed_inventory.units for vendor Ownership and 0 for HF Ownership
    liveDemand = sum of all packaging demand for current week
    incomingPos =  po.qty
    outboundPos = outbound_po.qty
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / units tl
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / (liveDemand + sum of forecasts (range
    next_week, next_week + 3)) / (len(forecasts) + 1)

    Expected calculations for next week (calculation for further week are the same):
    hfOwnedBoh = Previous week’s BOH + Previous week’s Incoming POs IF Vendor POs to DC >= Live Demand else Vendor POs
    to DC < Live Demand THEN HF Owned BOH = Previous week's BOH + Previous week's Incoming POs to SS -
    (Live Demand - Vendor POs to DC). In this test we check the condition when Vendor POs to DC >= Live Demand
    liveDemand = next week forecast
    incomingPos = po.qty for next week
    outboundPos = outbound_po.qty for next week
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / units/TL
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / sum of forecasts (range next_week,
    next_week + 4)) / (len(forecasts)
    """
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    packaging_sku_import = TestPackagingSkuImport.generate_packaging_sku_import(
        bob_code=warehouse.packaging_regions[0], sku=sku
    )
    demand_pipeline = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku)
    po_inventory_1, po_inventory_2, incoming_po = TestPurchaseOrder.generate_inbound_pos(
        pos_quantity=3, sku=sku, warehouse=warehouse
    )
    vendor_po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    po_outbound = TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse)
    incoming_po_next_week = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK)
    outbound_po_next_week = TestPurchaseOrder.generate_outbound_purchase_order_with_sku(
        sku=sku, warehouse=warehouse, week=NEXT_WEEK
    )
    vendor_po_next_week = TestPurchaseOrder.generate_po_with_sku(site=site_config, week=NEXT_WEEK, sku=sku)
    inventory_po_1 = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_inventory_1, warehouse=warehouse)
    inventory_po_2 = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_inventory_2, warehouse=warehouse)
    # create current week forecasts in range (next week, next_week + 2)
    forecasts_for_current_week = []
    for i in range(3):
        forecasts_for_current_week.append(
            TestPackagingLongTermForecast.generate_packaging_long_term_forecast(
                site_config=site_config, demand_pipeline=demand_pipeline.demand_pipeline, week=NEXT_WEEK + i
            )
        )
    # create next week forecasts in range (next week, next_week + 4)
    forecasts_for_next_week = [*forecasts_for_current_week]
    for i in range(3, 5):
        forecasts_for_next_week.append(
            TestPackagingLongTermForecast.generate_packaging_long_term_forecast(
                site_config=site_config, demand_pipeline=demand_pipeline.demand_pipeline, week=NEXT_WEEK + i
            )
        )
    total_forecasts = [
        *forecasts_for_next_week,
        TestPackagingLongTermForecast.generate_packaging_long_term_forecast(
            site_config=site_config, demand_pipeline=demand_pipeline.demand_pipeline, week=NEXT_WEEK + 5
        ),
    ]
    vendor_managed_inventory = TestVendorManagedInventory.generate_vendor_managed_inventory(
        region="".join(warehouse.packaging_regions),
        supplier_name=vendor_po.supplier_name,
        sku=sku,
        inbound=100,
        outbound=200,
    )
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(
            vendor_po,
            po_outbound,
            incoming_po,
            po_inventory_1,
            po_inventory_2,
            incoming_po_next_week,
            outbound_po_next_week,
            vendor_po_next_week,
        )
        .with_packaging_sku_import(packaging_sku_import)
        .with_packaging_demand(packaging_demand)
        .with_pimt_inventory(inventory_po_1, inventory_po_2)
        .with_long_term_packaging_forecast(*total_forecasts)
        .with_demand_pipeline(demand_pipeline)
        .with_vendor_managed_inventory(vendor_managed_inventory)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        hf_owned_item = [value for value in actual_data["items"] if value["ownership"] == _HF_OWNED][0]
        vendor_owned_item = next(
            value
            for value in actual_data["items"]
            if value["ownership"] == OWNED_BY_TEMPlATE.format(vendor_po.supplier_name)
        )
        supplier_level_values = {_HF_OWNED: hf_owned_item, vendor_po.supplier_name + " Owned": vendor_owned_item}
        # current packaging summary data
        for ownership, values in supplier_level_values.items():
            assert values["packagingRegion"] == warehouse.packaging_regions[0]
            assert values["category"] == demand_pipeline.sku_type
            assert values["ownership"] == ownership
            assert values["itemName"] == sku.sku_name
            assert values["skuCode"] == sku.sku_code
            assert values["unitsPerTruckLoad"] == packaging_sku_import.units_per_truck_load

            # current week calculations
            current_week_values = values["weekly"][CURRENT_WEEK_STR]
            average_forecast = (
                current_week_values["liveDemand"] + sum(value.forecast for value in forecasts_for_current_week)
            ) / (len(forecasts_for_current_week) + 1)
            expected_hf_owned = (inventory_po_1.case_quantity * po_inventory_1.first_line().case_size) + (
                inventory_po_2.case_quantity * po_inventory_2.first_line().case_size
            )
            expected_inventory = current_week_values["hfOwnedBoh"] + current_week_values["vendorOwnedBoh"]
            assert current_week_values["hfOwnedBoh"] == (expected_hf_owned if ownership == _HF_OWNED else 0)
            assert current_week_values["vendorOwnedBoh"] == (
                0 if ownership == _HF_OWNED else vendor_managed_inventory.units + vendor_managed_inventory.inbound
            )
            assert current_week_values["liveDemand"] == sum(packaging_demand.demand_by_day)
            if ownership == _HF_OWNED:
                assert current_week_values["incomingPos"] == incoming_po.first_line().qty
            else:
                assert current_week_values["incomingPos"] == 0
            assert current_week_values["truckLoads"] == round(
                (expected_inventory + current_week_values["incomingPos"]) / packaging_sku_import.units_per_truck_load, 1
            )
            assert current_week_values["weeksOnHand"] == round(
                (expected_inventory + current_week_values["incomingPos"]) / average_forecast,
                1,
            )

            # check values for next week data
            next_week_values = values["weekly"][NEXT_WEEK_STR]
            average_next_week_forecast = (sum(value.forecast for value in forecasts_for_next_week)) / (
                len(forecasts_for_next_week)
            )
            expected_inventory_next_week = next_week_values["hfOwnedBoh"] + next_week_values["vendorOwnedBoh"]
            assert next_week_values["hfOwnedBoh"] == (
                (
                    current_week_values["hfOwnedBoh"]
                    + current_week_values["incomingPos"]
                    - current_week_values["outboundPos"]
                )
                if ownership == _HF_OWNED
                else -vendor_managed_inventory.outbound
            )
            assert next_week_values["vendorOwnedBoh"] == (
                current_week_values["vendorOwnedBoh"]
                - next_week_values["vendorPosToDc"]
                - current_week_values["vendorManagedPlannedDepletion"]
                - current_week_values["outboundPos"]
            )
            assert next_week_values["liveDemand"] == total_forecasts[0].forecast
            if ownership == _HF_OWNED:
                assert next_week_values["incomingPos"] == incoming_po_next_week.first_line().qty
                assert next_week_values["outboundPos"] == outbound_po_next_week.first_line().qty
                comparisons.assert_numbers_equal(
                    next_week_values["truckLoads"],
                    (expected_inventory_next_week + next_week_values["incomingPos"])
                    / packaging_sku_import.units_per_truck_load,
                    delta=0.1,
                )
            comparisons.assert_numbers_equal(
                next_week_values["weeksOnHand"],
                (expected_inventory_next_week + next_week_values["incomingPos"]) / average_next_week_forecast,
                delta=0.1,
            )


@pytest.mark.skip("Skip until Safety Stock logic finalized")
def test_vendor_po_to_dc_on_supplier_level_in_ambient_safety_stock():
    """
    Test Vendor PO to DC column on supplier level in Ambient Safety Stock for current and next weeks

    Test steps:
    1.Generate required data (2 warehouses for 1 region, site_config, 2 po outbound for 2 warehouses for current and
    next weeks, 2 vendor inventories)
    2.Make API call to ambient_safety_stock endpoint -> 200
    3.Check that Vendor PO to DC column calculates as expected

    Expected calculations for current week:
    vendorPosToDc for HF ownership = sum of all outbound pos.qty of vendors
    vendorPosToDc for vendor ownership = outbound po.qty for specific vendor
    Expected calculations for next week:
    vendorPosToDc for HF ownership = sum of all outbound pos.qty of vendors for next week
    vendorPosToDc for vendor ownership = outbound po.qty for specific vendor for next week
    """
    warehouse = dataclasses.replace(Warehouses.UNCOMMON.value)
    warehouse_2 = dataclasses.replace(Warehouses.FIRST_CHOICE.value)
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    packaging_sku_import = TestPackagingSkuImport.generate_packaging_sku_import(
        bob_code=warehouse.packaging_regions[0], sku=sku
    )
    demand_pipeline = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku)
    vendor_inventory = TestVendorManagedInventory.generate_vendor_managed_inventory(
        region="".join(warehouse.packaging_regions), sku=sku
    )
    vendor_po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, supplier_name=vendor_inventory.supplier_name
    )
    vendor_po_next_week = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, supplier_name=vendor_inventory.supplier_name, week=NEXT_WEEK
    )

    # these POs should not be added to "Vendor POs to DC" value as this supplier is also configured as a WH
    wh_vendor_managed_inventory = TestVendorManagedInventory.generate_vendor_managed_inventory(
        region="".join(warehouse.packaging_regions), supplier_name=warehouse.ot_suppliers[0], sku=sku
    )
    wh_vendor_po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, supplier_name=wh_vendor_managed_inventory.supplier_name
    )
    wh_vendor_po_next_week = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, supplier_name=wh_vendor_managed_inventory.supplier_name, week=NEXT_WEEK
    )
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse, warehouse_2)
        .with_skus(sku)
        .with_purchase_orders(vendor_po, vendor_po_next_week, wh_vendor_po, wh_vendor_po_next_week)
        .with_packaging_sku_import(packaging_sku_import)
        .with_packaging_demand(packaging_demand)
        .with_demand_pipeline(demand_pipeline)
        .with_vendor_managed_inventory(vendor_inventory, wh_vendor_managed_inventory)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["items"]
        for row in actual_data:
            if row["ownership"] == _HF_OWNED:
                expected_vendor_pos_current = 0
                expected_vendor_pos_next = 0
            elif row["ownership"] == OWNED_BY_TEMPlATE.format(wh_vendor_managed_inventory.supplier_name):
                expected_vendor_pos_current = wh_vendor_po.first_line().qty
                expected_vendor_pos_next = wh_vendor_po_next_week.first_line().qty
            else:
                expected_vendor_pos_current = vendor_po.first_line().qty
                expected_vendor_pos_next = vendor_po_next_week.first_line().qty
            assert row["weekly"][CURRENT_WEEK_STR]["vendorPosToDc"] == expected_vendor_pos_current
            assert row["weekly"][NEXT_WEEK_STR]["vendorPosToDc"] == expected_vendor_pos_next


@pytest.mark.skip("Skip until Safety Stock logic finalized")
def test_demand_level_data_in_ambient_safety_stock():
    """
    Test Ambient Safety Stock data on demand level.

    1.Generate required data(warehouse, site_config, 2 skus, pos for inventory, 2 incoming pos, outbound pos for current
     and next weeks, packaging demands for 2 skus, packaging sku import, demand pipelines, forecasts)
    2.Make API call to ambient_safety_stock endpoint -> 200
    3.Check that data for packaging summary, current week, and next week sections are equal to expected calculations.
    Expected calculations for Current week:
    hfOwnedBoh = sum of all hfOwnedBoh on supplier level for each sku
    vendorOwnedBoh = sum of all vendorOwnedBoh on supplier level for each sku
    liveDemand = sum of all packaging demand on supplier level for each sku
    incomingPos =  sum of all po.qty on supplier level for each sku
    outboundPos = sum of all outbound po.qty on supplier level for each sku
    vendorPosToDc = next value of outbound_pos.qty
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / average units_tl
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / (liveDemand + sum of forecasts
     / (len(forecasts) + 1)

    Expected calculations for next week (calculation for further week are the same):
    hfOwnedBoh = Previous week’s BOH on demand level value + Previous week’s Incoming POs on demand level value
    vendorOwnedBoh = Previous Week’s Vendor Managed BOH + sum Vendor Managed BOH on supplier level
    liveDemand = next week forecast
    incomingPos = sum po.qty for next week
    outboundPos = sum outbound_po.qty for next week
    vendorPosToDc = next value of outbound_pos.qty for next week
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / average units_TL
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / sum of forecasts / (len(forecasts)
    """
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    skus = sku_1, sku_2 = TestSku.generate_skus_with_specific_sku_names(
        sku_names=SkuNames.random_non_repetitive_values(qty=2)
    )
    packaging_skus_import = [
        TestPackagingSkuImport.generate_packaging_sku_import(bob_code="".join(warehouse.packaging_regions), sku=sku)
        for sku in skus
    ]
    po_inventories = [TestPurchaseOrder.generate_po_with_sku(warehouse=warehouse, sku=sku) for sku in skus]
    vendor_managed_inventory = [
        TestVendorManagedInventory.generate_vendor_managed_inventory(region=warehouse.packaging_regions[0], sku=sku)
        for sku in skus
    ]
    pos_outbound_cur_week = [
        TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse) for sku in skus
    ]
    vendor_pos_curr_week = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, supplier_name=inv.supplier_name)
        for inv, sku in zip(vendor_managed_inventory, skus)
    ]
    incoming_pos_cur_week = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, warehouse=warehouse, week=CURRENT_WEEK) for sku in skus
    ]
    incoming_pos_next_week = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK) for sku in skus
    ]
    pos_outbound_next_week = [
        TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK)
        for sku in skus
    ]
    vendor_pos_next_week = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=site_config, supplier_name=inv.supplier_name, week=NEXT_WEEK
        )
        for inv, sku in zip(vendor_managed_inventory, skus)
    ]
    inventories = [
        TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse) for po in po_inventories
    ]
    pipeline = data_generator.generate_string()
    demand_pipeline_sku = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku_1, demand_pipeline=pipeline)
    demand_pipeline_sku_2 = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku_2, demand_pipeline=pipeline)
    long_term_forecasts = [
        TestPackagingLongTermForecast.generate_packaging_long_term_forecast(
            site_config=site_config, demand_pipeline=demand_pipeline_sku.demand_pipeline, week=week
        )
        for week in [NEXT_WEEK, NEXT_WEEK + 1]
    ]
    packaging_demand_sku = TestPackagingDemand.generate_packaging_demand(sku=sku_1, site_config=site_config)
    packaging_demand_sku_2 = TestPackagingDemand.generate_packaging_demand(sku=sku_2, site_config=site_config)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(*skus)
        .with_purchase_orders(
            *pos_outbound_cur_week,
            *po_inventories,
            *pos_outbound_next_week,
            *incoming_pos_next_week,
            *incoming_pos_cur_week,
            *vendor_pos_curr_week,
            *vendor_pos_next_week,
        )
        .with_packaging_sku_import(*packaging_skus_import)
        .with_packaging_demand(packaging_demand_sku, packaging_demand_sku_2)
        .with_pimt_inventory(*inventories)
        .with_long_term_packaging_forecast(*long_term_forecasts)
        .with_demand_pipeline(demand_pipeline_sku, demand_pipeline_sku_2)
        .with_vendor_managed_inventory(*vendor_managed_inventory)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse)
        actual_demand_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        # check Packaging Summary demand level
        packaging_summary_items = actual_demand_data["items"]
        assert actual_demand_data["packagingRegion"] == warehouse.packaging_regions[0]
        assert actual_demand_data["category"] == next(item["category"] for item in packaging_summary_items)
        assert actual_demand_data["ownership"] == REGIONAL_TOTAL_OWNERSHIP
        assert actual_demand_data["itemName"] == demand_pipeline_sku.demand_pipeline
        assert sorted(actual_demand_data["skuCode"].split(", ")) == sorted(sku.sku_code for sku in skus)
        assert actual_demand_data["unitsPerTruckLoad"] == min(
            item["unitsPerTruckLoad"] for item in packaging_summary_items
        )

        average_units_tl = sum(value.units_per_truck_load for value in packaging_skus_import) / len(
            packaging_skus_import
        )
        # check current week section on demand level
        cur_week_demand_data = actual_demand_data["weekly"][CURRENT_WEEK_STR]
        sum_forecasts = sum(value.forecast for value in long_term_forecasts)
        average_forecast_sku_2 = (sum(packaging_demand_sku_2.demand_by_day) + sum_forecasts) / (
            len(long_term_forecasts) + 1
        )
        average_forecast_sku_1 = (sum(packaging_demand_sku.demand_by_day) + sum_forecasts) / (
            len(long_term_forecasts) + 1
        )
        total_inventory = cur_week_demand_data["hfOwnedBoh"] + cur_week_demand_data["vendorOwnedBoh"]
        assert cur_week_demand_data["hfOwnedBoh"] == sum(
            item["weekly"][CURRENT_WEEK_STR]["hfOwnedBoh"] for item in actual_demand_data["items"]
        )
        assert cur_week_demand_data["vendorOwnedBoh"] == sum(
            item["weekly"][CURRENT_WEEK_STR]["vendorOwnedBoh"] for item in actual_demand_data["items"]
        )
        assert cur_week_demand_data["liveDemand"] == sum(packaging_demand_sku_2.demand_by_day) + sum(
            packaging_demand_sku.demand_by_day
        )
        assert cur_week_demand_data["incomingPos"] == sum(value.first_line().qty for value in incoming_pos_cur_week)
        assert cur_week_demand_data["outboundPos"] == sum(value.first_line().qty for value in pos_outbound_cur_week)
        assert cur_week_demand_data["vendorPosToDc"] == sum(po.first_line().qty for po in vendor_pos_curr_week)
        assert cur_week_demand_data["truckLoads"] == round(
            (total_inventory + cur_week_demand_data["incomingPos"]) / average_units_tl, 1
        )
        assert cur_week_demand_data["weeksOnHand"] == round(
            (total_inventory + cur_week_demand_data["outboundPos"] - cur_week_demand_data["incomingPos"])
            / (average_forecast_sku_1 + average_forecast_sku_2),
            1,
        )

        # check next week section on demand level
        next_week_demand_data = actual_demand_data["weekly"][str(NEXT_WEEK)]
        average_forecast = sum_forecasts / len(long_term_forecasts)
        total_inventory_next_week = next_week_demand_data["hfOwnedBoh"] + next_week_demand_data["vendorOwnedBoh"]
        assert (
            next_week_demand_data["hfOwnedBoh"]
            == cur_week_demand_data["hfOwnedBoh"] + cur_week_demand_data["incomingPos"]
        )
        assert next_week_demand_data["vendorOwnedBoh"] == sum(
            item["weekly"][str(NEXT_WEEK)]["vendorOwnedBoh"] for item in actual_demand_data["items"]
        )
        assert next_week_demand_data["liveDemand"] == next(iter(long_term_forecasts)).forecast
        assert next_week_demand_data["incomingPos"] == sum(value.first_line().qty for value in incoming_pos_next_week)
        assert next_week_demand_data["outboundPos"] == sum(value.first_line().qty for value in pos_outbound_next_week)
        assert next_week_demand_data["vendorPosToDc"] == sum(po.first_line().qty for po in vendor_pos_next_week)
        assert next_week_demand_data["truckLoads"] == round(
            (total_inventory_next_week + next_week_demand_data["incomingPos"]) / average_units_tl, 1
        )
        assert next_week_demand_data["weeksOnHand"] == round(
            (total_inventory_next_week + next_week_demand_data["outboundPos"] - next_week_demand_data["incomingPos"])
            / average_forecast,
            1,
        )


@pytest.mark.skip("Skip until Safety Stock logic finalized")
def test_demand_level_for_compound_skus():
    """
    Test Ambient Safety Stock data on demand level for compound SKUs.

    1.Set up warehouse, site_config, 2 skus, pos for inventory, 2 incoming pos, outbound pos for current and next weeks,
    packaging demands for 2 skus, packaging sku import, demand pipelines, forecasts
    2.Make API call to ambient_safety_stock endpoint -> 200
    3.Check that data for packaging summary, current wee, and next week sections are equal to expected calculations.
    Expected calculations for Current week:
    hfOwnedBoh = min of hfOwnedBoh on supplier level
    vendorOwnedBoh = min of vendorOwnedBoh on supplier level
    liveDemand = min packaging demand on supplier level
    incomingPos =  min of po.qty on supplier level
    outboundPos = min of  outbound po.qty on supplier level
    vendorPosToDc = next value of outbound_pos.qty on supplier level
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / min units tl
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / (min liveDemand + sum of forecasts)
     / (len(forecasts) + 1)

    Expected calculations for next week (calculation for further week are the same):
    hfOwnedBoh = Previous week’s BOH + Previous week’s Incoming POs
    vendorOwnedBoh = Previous Week’s Vendor Managed BOH + min Vendor Managed BOH
    liveDemand = next week forecast
    incomingPos = min po.qty for next week on supplier level
    outboundPos =  min outbound_po.qty for next week on supplier level
    vendorPosToDc = next value of outbound_pos.qty for next week
    truckLoads = (hfOwnedBoh + vendorOwnedBoh + incomingPos) / min units_TL
    weeksOnHand = (hfOwnedBoh + vendorOwnedBoh + outboundPos - incomingPos) / (sum of forecasts / (len(forecasts))
    """
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    skus = sku_1, sku_2 = TestSku.generate_skus(sku_quantity=2)
    incoming_pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, warehouse=warehouse) for sku in skus]
    inventory_pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, warehouse=warehouse) for sku in skus]
    pos_outbound = [
        TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse) for sku in skus
    ]
    pos_incoming_next_week = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK) for sku in skus
    ]
    pos_outbound_next_week = [
        TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK)
        for sku in skus
    ]
    inventories = [
        TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse) for po in inventory_pos
    ]
    packaging_sku_import = [
        TestPackagingSkuImport.generate_packaging_sku_import(bob_code="".join(warehouse.packaging_regions), sku=sku)
        for sku in skus
    ]
    demand_pipeline = data_generator.generate_string()
    demand_pipelines = [
        TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku, demand_pipeline=demand_pipeline) for sku in skus
    ]
    long_term_forecasts = [
        TestPackagingLongTermForecast.generate_packaging_long_term_forecast(
            site_config=site_config, demand_pipeline=demand_pipeline, week=week
        )
        for week in [NEXT_WEEK, NEXT_WEEK + 1]
    ]
    vendor_managed_inventories = [
        TestVendorManagedInventory.generate_vendor_managed_inventory(
            region="".join(warehouse.packaging_regions), sku=sku
        )
        for po, sku in zip(pos_outbound, skus)
    ]
    packaging_demand_sku_1 = TestPackagingDemand.generate_packaging_demand(sku=sku_1, site_config=site_config)
    packaging_demand_sku_2 = TestPackagingDemand.generate_packaging_demand(sku=sku_2, site_config=site_config)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(*skus)
        .with_purchase_orders(
            *pos_outbound, *inventory_pos, *incoming_pos, *pos_outbound_next_week, *pos_incoming_next_week
        )
        .with_packaging_sku_import(*packaging_sku_import)
        .with_packaging_demand(packaging_demand_sku_1, packaging_demand_sku_2)
        .with_pimt_inventory(*inventories)
        .with_long_term_packaging_forecast(*long_term_forecasts)
        .with_demand_pipeline(*demand_pipelines)
        .with_vendor_managed_inventory(*vendor_managed_inventories)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse)
        actual_demand_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

        # Check current week demand level data
        cur_week_demand_data = actual_demand_data["weekly"][CURRENT_WEEK_STR]
        min_units_tl = min(value.units_per_truck_load for value in packaging_sku_import)
        max_demand = max(sum(packaging_demand_sku_1.demand_by_day), sum(packaging_demand_sku_2.demand_by_day))
        sum_forecasts = sum(value.forecast for value in long_term_forecasts)
        total_inventory = cur_week_demand_data["hfOwnedBoh"] + cur_week_demand_data["vendorOwnedBoh"]
        assert cur_week_demand_data["hfOwnedBoh"] == min(
            [
                item["weekly"][CURRENT_WEEK_STR]["hfOwnedBoh"]
                for item in actual_demand_data["items"]
                if item["weekly"][CURRENT_WEEK_STR]["hfOwnedBoh"] != 0
            ]
        )
        assert cur_week_demand_data["vendorOwnedBoh"] == min(
            [
                item["weekly"][CURRENT_WEEK_STR]["vendorOwnedBoh"]
                for item in actual_demand_data["items"]
                if item["weekly"][CURRENT_WEEK_STR]["vendorOwnedBoh"] != 0
            ]
        )
        assert cur_week_demand_data["liveDemand"] == max_demand
        assert cur_week_demand_data["incomingPos"] == min(po.first_line().qty for po in incoming_pos)
        assert cur_week_demand_data["outboundPos"] == min(po.first_line().qty for po in pos_outbound)
        assert cur_week_demand_data["vendorPosToDc"] == 0
        assert cur_week_demand_data["truckLoads"] == round(
            (total_inventory + cur_week_demand_data["incomingPos"]) / min_units_tl, 1
        )
        assert cur_week_demand_data["weeksOnHand"] == round(
            (total_inventory + cur_week_demand_data["outboundPos"] - cur_week_demand_data["incomingPos"])
            / ((max_demand + sum_forecasts) / (len(long_term_forecasts) + 1)),
            1,
        )

        # Check next week demand level data
        next_week_demand_data = actual_demand_data["weekly"][str(NEXT_WEEK)]
        average_forecasts = sum_forecasts / len(long_term_forecasts)
        next_week_total_inventory = next_week_demand_data["hfOwnedBoh"] + next_week_demand_data["vendorOwnedBoh"]
        assert (
            next_week_demand_data["hfOwnedBoh"]
            == cur_week_demand_data["hfOwnedBoh"]
            + cur_week_demand_data["incomingPos"]
            - cur_week_demand_data["outboundPos"]
        )
        vendor_owned_items = [
            item for item in actual_demand_data["items"] if item["weekly"][str(NEXT_WEEK)]["vendorOwnedBoh"] != 0
        ]
        assert next_week_demand_data["vendorOwnedBoh"] == min(
            [item["weekly"][str(NEXT_WEEK)]["vendorOwnedBoh"] for item in vendor_owned_items]
        )
        assert next_week_demand_data["liveDemand"] == next(iter(long_term_forecasts)).forecast
        assert next_week_demand_data["incomingPos"] == min(po.first_line().qty for po in pos_incoming_next_week)
        assert next_week_demand_data["outboundPos"] == min(po.first_line().qty for po in pos_outbound_next_week)
        assert next_week_demand_data["vendorPosToDc"] == 0
        assert next_week_demand_data["truckLoads"] == round(
            (next_week_total_inventory + next_week_demand_data["incomingPos"]) / min_units_tl, 1
        )
        assert next_week_demand_data["weeksOnHand"] == round(
            (next_week_total_inventory + next_week_demand_data["outboundPos"] - next_week_demand_data["incomingPos"])
            / average_forecasts,
            1,
        )


def test_po_dropdown_on_ambient_safety_stock():
    """
    Test PO pop-up on Ambient Safety Stock.
    PO Type = Inbound --> anything to the 3PW
    PO Type = Outbound --> anything where supplier = 3PW or site = DC/3PL (in our case DC)

    Test steps:
    1. Generate required test data with sku, site_config, warehouse, pos, packaging_sku_import, demand_pipeline, grn,
    po_shipment
    2. Make an API call to PO Status endpoint -> 200
    3. Check all values from the API call are as expected
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.GRN
    warehouse = dataclasses.replace(
        WarehouseUtils.get_warehouse_by_site_config(site_config=site_config), receiving_type=WhReceivingType.E2OPEN_GRN
    )
    po_outbound = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_inbound = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
        emergency_reason=EmergencyReason.CHARITY,
        shipping_method=ShippingMethods.FREIGHT_ON_BOARD,
    )
    pos = [po_inbound, po_outbound]
    packaging_sku_import = TestPackagingSkuImport.generate_packaging_sku_import(
        bob_code=warehouse.packaging_regions[0], sku=sku
    )
    demand_pipeline = TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku)
    grn_outbound = TestGrn.generate_grn_by_site_config(
        po=po_outbound,
        site_config=site_config,
        units_received=po_outbound.first_line().qty + data_generator.random_int(),
        cases_received=data_generator.random_int(),
    )
    grn_inbound = TestGrn.generate_grn(
        po=po_inbound,
        warehouse=warehouse,
        units_received=po_inbound.first_line().qty + data_generator.random_int(),
        cases_received=data_generator.random_int(),
    )
    po_shipment_outbound = TestPOShipment.generate_po_shipment(po=po_outbound)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(*pos)
        .with_packaging_sku_import(packaging_sku_import)
        .with_po_shipment(po_shipment_outbound)
        .with_demand_pipeline(demand_pipeline)
        .with_grn(grn_inbound, grn_outbound)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock_po_status(
            warehouse=warehouse, demand_pipeline=demand_pipeline.demand_pipeline
        )
        actual_dropdown_data = {
            data["poNumber"]: data
            for data in base_steps.process_response_and_extract_data(response=response, expected_length=2)
        }
        for po in pos:
            dropdown_data = actual_dropdown_data[po.po_number]
            po_type = PoType.OUTBOUND if po == po_outbound else PoType.INBOUND
            grn = grn_outbound if po == po_outbound else grn_inbound
            if po == po_outbound:
                assert dropdown_data["appointmentTime"] == po_shipment_outbound.appointment_time.isoformat()
                assert dropdown_data["totalPrice"] == float(po.first_line().total_price)
            else:
                assert dropdown_data["totalPrice"] == round(
                    dropdown_data["casesReceived"] * dropdown_data["casePrice"], 2
                )
            assert dropdown_data["supplier"] == po.supplier_name
            assert dropdown_data["poNumber"] == po.po_number
            assert dropdown_data["sku"] == sku.sku_code
            assert dropdown_data["skuName"] == sku.sku_name
            assert dropdown_data["poStatus"] == PoStatuses.RECEIVED_OVER
            assert dropdown_data["poType"] == po_type
            assert dropdown_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
            assert dropdown_data["receiveVariance"] == abs(
                dropdown_data["quantityOrdered"] - dropdown_data["quantityReceived"]
            )
            assert dropdown_data["orderSize"] == po.first_line().order_size
            assert dropdown_data["casePrice"] == float(po.first_line().case_price)
            assert dropdown_data["caseSize"] == po.first_line().case_size
            assert dropdown_data["caseSizeReceived"] == round(
                dropdown_data["quantityReceived"] / dropdown_data["casesReceived"], 3
            )
            assert dropdown_data["quantityOrdered"] == po.first_line().qty
            assert dropdown_data["quantityReceived"] == grn.units_received
            assert dropdown_data["casesReceived"] == grn.cases_received
            assert dropdown_data["dateReceived"] == grn.receipt_time_est.strftime(DATE_TIME_FORMAT_1)
            assert round(dropdown_data["totalPriceReceived"], 2) == round(
                dropdown_data["quantityReceived"] * (dropdown_data["casePrice"] / dropdown_data["caseSize"]), 2
            )
            assert dropdown_data["emergencyReason"] == po.emergency_reason
            assert dropdown_data["shipMethod"] == po.shipping_method
