import dataclasses

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    EP,
    FJ,
    HF,
    EmergencyReason,
    PackagingCategory,
    ReceiveInputType,
    ShippingMethods,
    SiteConfigs,
    Sizes,
    Users,
    WhReceivingType,
)
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.packaging_long_term_forecast import TestDemandPipelineSkuMapping
from automated_tests.data.models.packaging_sku_import import TestPackagingSkuImport
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import packaging_depletion_steps as depl_steps
from automated_tests.services.api.pimt.ambient_safety_stock_client import AmbientSafetyStockClient
from automated_tests.utils.warehouse_utils import WarehouseUtils


def generate_sku_names_for_brand(brand):
    return [
        f"{brand} {PackagingCategory.BOX}, NEW {Sizes.SMALL.value.upper()} - 1 Unit",
        f"{brand} {PackagingCategory.BOX}, {Sizes.SMALL.value.upper()} - 1 Unit",
        f"{brand} {PackagingCategory.BOX}, {Sizes.LARGE.value.upper()}, Standard - 1 Unit",
        f"{brand} {PackagingCategory.BOX}, {Sizes.MEDIUM.value.upper()}, Standard - 1 Unit",
        f"{brand} {Sizes.XSMALL.value} {PackagingCategory.SEPARATOR}",
    ]


def test_ambient_safety_stock_sku_grouping():
    """
    Tests the grouping of SKUs in Ambient Safety Stock based on liner groups.

    Test steps:
    1. Generate test data with SKUs, some associated with liner groups.
    2. Make an API call to the ambient_safety_stock endpoint.
    3. Extract the actual data from the response.
    4. For each group in the response:
       - If a liner group is present, verify that the items within that group
         have the expected liner group.
       - If no liner group is present, verify that the items within that
         group do not have a liner group.
    5. Ensure all generated SKUs are present in the response.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.GRN
    warehouse = dataclasses.replace(
        WarehouseUtils.get_warehouse_by_site_config(site_config=site_config), receiving_type=WhReceivingType.E2OPEN_GRN
    )

    # Generate SKUs with liner groups
    skus_with_liner_group_data = depl_steps.generate_skus_for_liners()
    skus_with_liner_group = list(skus_with_liner_group_data.values())

    # Generate SKUs without explicit liner groups
    packaging_categories_wo_box = PackagingCategory.get_all_values(exclude=[PackagingCategory.BOX])
    skus_without_liner_group = []
    sku_names_data = [f"HF {Sizes.SMALL.value} {category}" for category in packaging_categories_wo_box]
    for brand in [HF, EP, FJ]:
        sku_names_data.extend(generate_sku_names_for_brand(brand))
    for sku_name in sku_names_data:
        skus_without_liner_group.append(TestSku.generate_sku(sku_name=sku_name))

    all_skus = skus_with_liner_group + skus_without_liner_group
    all_demand_pipelines = [
        TestDemandPipelineSkuMapping.generate_demand_pipeline(sku=sku, demand_pipeline=sku.sku_name) for sku in all_skus
    ]
    packaging_sku_imports = [
        TestPackagingSkuImport.generate_packaging_sku_import(bob_code=warehouse.packaging_regions[0], sku=sku)
        for sku in all_skus
    ]
    pos = [
        TestPurchaseOrder.generate_po_with_sku(
            site=site_config,
            sku=sku,
            emergency_reason=EmergencyReason.random_value(),
            shipping_method=ShippingMethods.random_value(),
        )
        for sku in all_skus
    ]
    grns = [
        TestGrn.generate_grn_by_site_config(
            site_config=site_config,
            po=po,
            units_received=po.first_line().qty + data_generator.random_int(),
            cases_received=data_generator.random_int(),
        )
        for po in pos
    ]
    pos_shipments = [TestPOShipment.generate_po_shipment(po=po) for po in pos]

    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(*all_skus)
        .with_packaging_sku_import(*packaging_sku_imports)
        .with_demand_pipeline(*all_demand_pipelines)
        .with_purchase_orders(*pos)
        .with_po_shipment(*pos_shipments)
        .with_grn(*grns)
    ):
        response = AmbientSafetyStockClient().get_ambient_safety_stock(warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, expected_length=len(all_skus))
        all_sku_codes = {sku.sku_code for sku in all_skus}
        processed_sku_codes = set()

        for group in actual_data:
            liner_group_name = group.get("linerGroup")
            items = group["items"]

            for item in items:
                processed_sku_codes.add(item["skuCode"])
                if liner_group_name:
                    expected_liner_group_for_sku = (
                        f"Liner - {item['itemName'].split(', ')[1].upper()} - {item['itemName'].split(', ')[2].upper()}"
                        if "liner" in item["itemName"].lower()
                        else None
                    )
                    assert (
                        expected_liner_group_for_sku.upper() == liner_group_name.upper()
                    ), f"SKU '{item['itemName']}' should have liner group '{expected_liner_group_for_sku}'."
                else:
                    assert item["linerGroup"] is None, f"SKU '{item['itemName']}' should not have a liner group."

        assert processed_sku_codes == all_sku_codes
