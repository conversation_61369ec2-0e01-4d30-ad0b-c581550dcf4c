from automated_tests.data.constants.base_constants import UsersCA, WarehousesCA
from automated_tests.data.constants.date_time_constants import NEXT_WEEK
from automated_tests.data.models.canada_forecast import TestCanadaForecast
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.expiring_inventory_client import ExpiringInventoryClient
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import MARKET_CA


def test_next_week_used_and_forecast_value_in_expiring_inventory_report_ca():
    """
    Test Next Week Used And Forecast values in expiring inventory report for Canada

    Test steps:
    1.Generate required data (sku, warehouse, site config for CA, po, inventory, inventory planner values for Next week)
    2.Make API call to expiring_inventory_report -> 200
    3.Check that nextWeekUsed is equal to next week and forecasts value is equal to sum values from inv_planner
    """
    sku = TestSku.generate_sku(market=MARKET_CA)
    warehouse = WarehousesCA.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse)
    canada_forecasts_next_week = TestCanadaForecast.generate_canada_forecast_for_several_days(
        site_config=site_config, sku=sku, week=NEXT_WEEK
    )
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_pimt_inventory(inventory)
        .with_canada_forecast(*canada_forecasts_next_week)
        .with_warehouses(warehouse)
    ):
        response = ExpiringInventoryClient().get_expiring_inventory_report(market=MARKET_CA)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        assert actual_data["nextWeekUsed"] == str(NEXT_WEEK)
        assert actual_data["forecast"] == sum(forecast.value for forecast in canada_forecasts_next_week)
