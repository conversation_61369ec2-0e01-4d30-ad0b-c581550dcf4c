from datetime import datetime, timed<PERSON>ta
from http import HTTPStatus

import pytz

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_2,
    US_EASTERN_TIMEZONE,
)
from automated_tests.data.constants.sync_constants import Projects
from automated_tests.data.models.comment import TestComment
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.comment_logs_client import ImtCommentsLogClient
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.utils import comparisons, datetime_utils


def test_imt_comments_logs_po_comment_values():
    """
    Test IMT comments log po comments all values

    Test steps:
    1. Generate required data for set up all columns(site_config, sku, po, comment)
    2. Make API call to comments_logs_po_comment endpoint -> 200
    3. Check that all values are equal to expected
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    comment = data_generator.generate_string()
    user = Users.test_user.value
    comment_data = TestComment.generate_po_comment(
        site=site_config.site.code, brand=site_config.brand.code, po=po, user=user, comment=comment, domain=Projects.IMT
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_comments(comment_data)
    ):
        response = ImtCommentsLogClient().get_po_comments()
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        actual_updated_at = datetime.strptime(actual_data["lastUpdated"], DATE_TIME_FORMAT_1)
        expected_updated_at = datetime_utils.get_datetime_for_specific_timezone(
            timezone=pytz.timezone(US_EASTERN_TIMEZONE)
        )
        assert actual_data["id"] == 1
        assert actual_data["site"] == site_config.site.code
        assert actual_data["skuCode"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["comment"] == comment
        assert comparisons.datetime_is_close(actual_updated_at, expected_updated_at, deviation=timedelta(minutes=1))
        assert actual_data["lastUpdatedBy"] == user.email
        assert actual_data["brand"] == site_config.brand.code


def test_imt_po_comment():
    """
    Test IMT po comments

    Test steps:
    1. Generate required data for set up all columns(site_config, sku, po)
    2. Make post API call to imt_po_comment endpoint -> 201
    3. Check that all values are equal to expected
    4. Make post API call to imt_po_comment endpoint again for editing comment -> 201
    5. Check that all values are equal to expected
    6. Make delete API call to imt_po_comment endpoint -> 204
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    original_comment, edited_comment = [data_generator.generate_string() for _ in range(2)]
    user = Users.test_user.value
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
    ):
        for comment in [original_comment, edited_comment]:
            post_response = ImtPoStatusClient().add_or_edit_po_comment(site_config=site_config, po=po, comment=comment)
            assert post_response.status_code == HTTPStatus.CREATED

            get_response = ImtPoStatusClient().get_po_comment(site_config=site_config, po=po)
            actual_data = base_steps.process_response_and_extract_data(
                response=get_response, additional_keys=[site_config.site.code], expected_length=3
            )
            actual_updated_at = datetime.strptime(actual_data["lastUpdated"], DATE_TIME_FORMAT_2)
            expected_updated_at = datetime_utils.get_datetime_for_specific_timezone(
                timezone=pytz.timezone(US_EASTERN_TIMEZONE)
            )
            assert comparisons.datetime_is_close(actual_updated_at, expected_updated_at, deviation=timedelta(minutes=1))
            assert actual_data["lastUpdatedBy"] == user.email
            assert actual_data["value"] == comment

        delete_response = ImtPoStatusClient().delete_po_comment(site_config=site_config, po=po)
        assert delete_response.status_code == HTTPStatus.NO_CONTENT


def test_comment_max_length():
    """
    Test max length comment. The max length should be 4000 symbols.

    Test steps:
    1. Generate required data for set up all columns(site_config, sku, po)
    2. Make post API call to imt_po_comment endpoint with comment length 4001 symbol -> 400
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    comment = data_generator.generate_string(4001)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
    ):
        post_response = ImtPoStatusClient().add_or_edit_po_comment(site_config=site_config, po=po, comment=comment)
        assert post_response.status_code == HTTPStatus.BAD_REQUEST
