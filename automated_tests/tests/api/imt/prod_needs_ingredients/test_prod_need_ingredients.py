import random
from datetime import datetime
from decimal import Decimal

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    CURRENT_WEEK_SECOND_DAY,
    DATE_FORMAT_2,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    PRODUCTION_WEEK_LENGTH,
    YESTERDAY_DATE,
)
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.hybrid_needs import (
    TestHybridNeeds,
    TestHybridNeedsLiveUsage,
    TestHybridNeedsShiftLevel,
    TestHybridNeedsStatus,
)
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.calculations.expected_daily_need_client import ExpectedDailyNeedClient
from automated_tests.services.api.calculations.perpetual_gross_needs_client import PerpetualGrossNeedsClient
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from automated_tests.services.api.imt.prod_need_ingredients_client import ProdNeedIngredientsClient
from procurement.constants.hellofresh_constant import PRODUCTION_TYPE_KITTING, ProductionPlanType


def test_prod_need_ingredients_current_week():
    """
    Test case checks all values on prod need ingredients dashboard for the current week
    1. Set up data for prod need ingredients dashboard
    2. Check that daily count for current week is based on hybrid needs.
    3. Check that total is equal to the sum of all Daily Values
    4. Check that ROW Need should is equal to the sum of all Daily Values where day >= today
    5. Check that delta is equal (1 - Oscar Forecast / Total) * 100 %
    6. Check that buyer is equal to the buyer from buyer sku
    """
    site_config = SiteConfigs.random_value()
    brand, site = site_config.brand, site_config.site
    sku = TestSku.generate_sku()
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand)
    ingredient = TestIngredient(sku, brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(oscar)
        .with_buyer_sku(buyer_sku)
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site.code], element_index=0
        )
        expected_daily_count = {
            str(hybrid_need.date): {
                "value": hybrid_need.quantity,
                "valueDay": None,
                "valueNight": None,
                "valueThird": None,
            }
            for hybrid_need in hybrid_needs
        }
        actual_data_daily = actual_data["daily"]
        assert actual_data_daily == expected_daily_count, "Daily count for current week has to be based on hybrid needs"
        assert actual_data["total"] == sum(
            value["value"] for value in actual_data_daily.values()
        ), "Total should be equal to the sum of all Daily Values"
        actual_data_daily_except_yesterday = [
            actual_data_daily[date]
            for date in actual_data_daily
            if datetime.strptime(date, DATE_FORMAT_2).date() > YESTERDAY_DATE
        ]
        assert actual_data["rowNeed"] == sum(
            value["value"] for value in actual_data_daily_except_yesterday
        ), "ROW Need should be equal to the sum of all Daily Values where day >= today"
        assert actual_data["buyers"] == buyer_sku.user.full_name, "Buyer should be equal to the buyer from buyer sku"
        expected_delta = float(round((1 - oscar.value / actual_data["total"]), 4))
        assert actual_data["delta"] == expected_delta


@pytest.mark.parametrize(
    "is_in_kitbox", [pytest.param(True, id="is_in_kitbox_true"), pytest.param(False, id="is_in_kitbox_false")]
)
def test_prod_need_ingredients_next_week(is_in_kitbox):
    site_config = SiteConfigs.random_value()
    brand, site = site_config.brand, site_config.site
    sku = TestSku.generate_sku()
    mealkit_next_week = TestMealkit.generate_mealkit_by_brand(brand=brand, week=NEXT_WEEK)
    ingredient = TestIngredient(sku=sku, brand=brand)
    mealkit_ingredient_next_week_kitted = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit_next_week, ingredient, is_in_kitbox=is_in_kitbox
    )
    # we add hybrid needs to ensure that they are not used for next week calculations
    hybrid_needs_next_week = TestHybridNeeds.generate_hybrid_needs_for_several_days(
        sku=sku, site_config=site_config, week=NEXT_WEEK
    )
    oscar_next_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK)
    mock_plan_calculation_next_week_assembly = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=NEXT_WEEK
    )
    mock_plan_calculation_next_week_kitting = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config,
        week=NEXT_WEEK,
        production_type=PRODUCTION_TYPE_KITTING,
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit_next_week)
        .with_mealkit_ingredient(mealkit_ingredient_next_week_kitted)
        .with_hybrid_needs(*hybrid_needs_next_week)
        .with_oscar_forecast(oscar_next_week)
        .with_mock_plan_calculation(
            mock_plan_calculation_next_week_assembly,
            mock_plan_calculation_next_week_kitting,
        )
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=NEXT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site.code], element_index=0
        )
        expected_daily_count = [
            {"value": round(weight * oscar_next_week.value), "valueDay": None, "valueNight": None, "valueThird": None}
            for weight in (
                mock_plan_calculation_next_week_kitting if is_in_kitbox else mock_plan_calculation_next_week_assembly
            ).weights
        ]
        assert (
            list(actual_data["daily"].values()) == expected_daily_count
        ), "Daily count for the next week has to be based on mock plan calculation * oscar"


def test_status_and_shift_level_columns():
    """
    Test status (Plan/Forecast) and shift level columns (Day Shift, Night Shift, 3rd Shift) on Production Needs board

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Make API calls
    4.Check that API response values meets with expected
    Expected calculations:
    Plan status -> "Plan" if day < current day, Forecast status -> "Forecast" id day > current day
    Day Shift, Night Shift, 3rd Shift = Shift-Level Hybrid Needs.value_day, value_night and value_third respectively
    """
    site_config = SiteConfigs.random_value()
    brand, site = site_config.brand, site_config.site
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=brand)
    ingredient = TestIngredient(sku=sku, brand=brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    hybrid_needs_status = TestHybridNeedsStatus.generate_hybrid_needs_statuses(site_config=site_config)
    hybrid_need_shift_level = TestHybridNeedsShiftLevel.generate_hybrid_needs_shift_levels_for_several_days(
        sku=sku, site_config=site_config
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(oscar)
        .with_hybrid_needs_status(*hybrid_needs_status)
        .with_hybrid_needs_shift_level(*hybrid_need_shift_level)
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        actual_data_daily = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site.code], element_index=0
        )["daily"]
        daily_header_response = ProdNeedIngredientsClient().get_production_need_daily_headers(
            site_config=site_config, week=CURRENT_WEEK
        )
        actual_daily_header_response = base_steps.process_response_and_extract_data(
            response=daily_header_response, expected_length=PRODUCTION_WEEK_LENGTH
        )

        for date, value in actual_daily_header_response.items():
            if datetime.strptime(date, DATE_FORMAT_2).date() > CURRENT_DATE:
                assert value["planType"] == ProductionPlanType.PROJECTED
            else:
                assert value["planType"] == ProductionPlanType.ACTUAL

        expected_daily_shift_level = {
            str(hybrid_need.day): {
                "valueDay": hybrid_need.value_day,
                "valueNight": hybrid_need.value_night,
                "valueThird": hybrid_need.value_third,
            }
            for hybrid_need in hybrid_need_shift_level
        }
        for day, data in actual_data_daily.items():
            assert data["valueDay"] == expected_daily_shift_level[day]["valueDay"]
            assert data["valueNight"] == expected_daily_shift_level[day]["valueNight"]
            assert data["valueThird"] == expected_daily_shift_level[day]["valueThird"]


def test_handling_negative_values():
    """
    Test case checks handling negative values on IMT views.
    Test steps:
    1. Generate required test data (site_config, sku, mealkit, ingredient, mealkit_ingredient, hybrid needs for the
    first and the second days of the week(for the second day we set quantity to have negative value))
    2. Make an API call and extract data for the Prod Need Ingredient dashboard
    3. Check that the Plan column value from the response == hybrid needs quantity for the second day and is negative
    4. Make an API call for Ingredient depletion and check Plan column value is the sum of hybrid needs quantities
    5. Make an API call for Expected Daily Need and Perpetual Gross Needs dashboards - negative values should be
    converted to 0 for daily depletion calculations.
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(
        sku=sku, day=CURRENT_WEEK.get_first_day(), site_config=site_config, qty=data_generator.random_int(3)
    )
    hybrid_needs_second_day = TestHybridNeeds.generate_hybrid_needs(
        sku=sku, site_config=site_config, day=CURRENT_WEEK_SECOND_DAY, qty=-(data_generator.random_int(2))
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(hybrid_needs, hybrid_needs_second_day)
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        actual_daily_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["daily"]
        assert actual_daily_data[str(CURRENT_WEEK_SECOND_DAY)]["value"] == hybrid_needs_second_day.quantity

        response_ing_depl = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data_ing_depl = base_steps.process_response_and_extract_data(
            response=response_ing_depl, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data_ing_depl["plan"] == sum([hybrid_needs_second_day.quantity, hybrid_needs.quantity])

        response_exp_daily_need = ExpectedDailyNeedClient().get_expected_daily_need(site_config=site_config)
        actual_exp_daily_need_data = base_steps.process_response_and_extract_data(
            response=response_exp_daily_need, element_index=0
        )["days"]
        actual_data_daily = {data["day"]: data for data in actual_exp_daily_need_data}
        assert actual_data_daily[str(CURRENT_WEEK_SECOND_DAY)]["dailyNeeds"] == 0

        response_pgn = PerpetualGrossNeedsClient().get_perpetual_gross_needs(site_config=site_config)
        actual_weekly_pgn_data = base_steps.process_response_and_extract_data(response=response_pgn, element_index=0)[
            "weeks"
        ]
        second_day_data = actual_weekly_pgn_data[str(CURRENT_WEEK)]["days"][str(CURRENT_WEEK_SECOND_DAY)]
        assert second_day_data == 0


@pytest.mark.parametrize("live_row_need", [data_generator.random_int(4), 0])
def test_live_consumption_live_row_need_column_values(live_row_need):
    """
    Test case checks Live Consumption, Live Row Need columns values on Production Need Ingredients.
    Test steps:
    1. Generate required test data with site_config, sku, mealkit, ingredient, mealkit_ingredient, hybrid_needs,
    PROJECTED hn status for Current Date, hn live usage (parametrize live_row_need value to check 2 cases for
    LiveRowNeed column)
    2. Make an API request -> 200
    3. Check Live Consumption is equal to the sum of live_usage.value when day=today and hn status = 'Projected'
    and check Live Row Need is equal (RowNeed - LiveConsumption) if live_usage.value < hybrid needs.quantities, else 0
    """
    site_config = SiteConfigs.random_value()
    brand = site_config.brand
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand)
    ingredient = TestIngredient(sku, brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    hn_quantities = sum(hn.quantity for hn in hybrid_needs if hn.date > YESTERDAY_DATE)
    hn_status_for_current_date = TestHybridNeedsStatus.generate_hybrid_needs_status(
        site_config=site_config, status=ProductionPlanType.PROJECTED
    )
    hybrid_needs_live_usage = TestHybridNeedsLiveUsage.generate_hybrid_needs_live_usage_for_several_days(
        sku=sku, site_config=site_config
    )
    for usage in hybrid_needs_live_usage:
        if live_row_need != 0:
            usage.value = hn_quantities - live_row_need
        else:
            usage.value = data_generator.random_int(4)
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_hybrid_needs_status(hn_status_for_current_date)
        .with_hybrid_needs_live_usage(*hybrid_needs_live_usage)
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["liveConsumption"] == sum(
            hn.value for hn in hybrid_needs_live_usage if hn.day == CURRENT_DATE
        )
        assert actual_data["liveRowNeed"] == live_row_need


def test_rounded_daily_values_up_to_1():
    """
    Test case checks daily values (forecast * weight) are rounded up to 1 if 0<value<1
    Test steps:
    1. Generate required test data with site_config, mealkit, sku, ingredient, mealkit_ingredient, oscar with (0, 1)
    value, mock plan calculation
    2. Make an API call -> 200, extract response data
    3. Check all daily values are rounded up to 1
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    oscar = TestOscar.generate_oscar(
        sku=sku, site_config=site_config, week=CURRENT_WEEK, value=Decimal(random.uniform(0.001, 0.5))
    )
    mock_plan_calculation = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config,
        week=CURRENT_WEEK,
        weights=[0.1] * len(CURRENT_WEEK.production_days()),
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_oscar_forecast(oscar)
        .with_mock_plan_calculation(mock_plan_calculation)
    ):
        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        actual_daily_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["daily"]
        actual_daily_values = [actual_daily_data[date] for date in actual_daily_data]
        for record in range(len(actual_daily_values)):
            assert actual_daily_values[record]["value"] == 1, "All daily values should be rounded up to 1"
