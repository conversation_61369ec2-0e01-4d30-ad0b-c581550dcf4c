import dataclasses
import random
from datetime import datetime, timedelta
from decimal import Decimal
from http import HTTPStatus

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    PO_CASE_PRICE_FLAG_PERCENTAGE_THRESHOLD,
    RECEIVED_STATUSES,
    Brands,
    EmergencyReason,
    PoStatuses,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsCA,
    Sites,
    UnitMeasureASN,
    UnitOfMeasurePoAcknowledgement,
    Users,
    UsersCA,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_4,
    NEXT_WEEK,
    NEXT_WEEK_FIRST_DAY,
    TOMORROW_DATETIME,
    YESTERDAY_DATETIME,
)
from automated_tests.data.constants.ordering_constants import HJReceiptStatuses
from automated_tests.data.models.advanced_shipping_notice import TestAdvanceShippingNotice
from automated_tests.data.models.allocation_price import TestAllocationPrice
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.po import TestPurchaseOrder, TestTransferOrderSku
from automated_tests.data.models.po_acknowledgement import TestPOAcknowledgementLineItems
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.po_void import TestPoVoid
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import po_status_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.utils import comparisons
from procurement.constants.hellofresh_constant import (
    CHARITY_EMERGENCY_REASON,
    FUTURE_WEEK_PULL_REASONS,
    MARKET_CA,
    InventoryInputType,
    POAcknowledgementState,
    ReceiveInputType,
)
from procurement.constants.ordering import CANCELLED, CLOSED
from procurement.data.models.ordering.advance_shipping_notice import AdvanceShippingNoticeState


def test_po_status_view_main_values_except_supplier_portal_section():
    """
    Test Po status values except Supplier Portal section.
    Quantity_received, cases_received, date_received columns calculates considering receiving_type (in this test is
    random receiving_type (HJ, MANUAL, GRN or WMSL)).
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = random.choice(list(ReceiveInputType))
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    receiving = TestReceiving.generate_receiving(po=po, site_config=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        po=po,
        site_config=site_config,
        units_received=po.first_line().case_size + random.randint(100, 200),
    )
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(purchasing_category)
        .with_skus(sku)
        .with_receiving(receiving)
        .with_grn(grn)
        .with_oscar_forecast(oscar)
    ):
        response = ImtPoStatusClient().get_po_status(po.week, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["supplier"] == po.supplier_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == purchasing_category.ingredient_category
        assert actual_data["purchasingUnit"] == sku.unit
        assert actual_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["receiveVariance"] == abs(
            actual_data["quantityOrdered"] - actual_data["quantityReceived"]
        ), "Receiving variance should be difference between ordered and received units"
        assert actual_data["loadNumber"] == po_shipment.load_number
        assert actual_data["appointmentTime"] == po_shipment.appointment_time.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["carrierName"] == po_shipment.carrier_name
        assert actual_data["originLocation"] == f"{po_shipment.locality}, {po_shipment.administrative_area}"
        assert actual_data["palletCount"] == po_shipment.pallet_count
        assert actual_data["orderSize"] == po.first_line().order_size
        assert actual_data["orderUnit"] == po.first_line().case_unit
        assert actual_data["casePrice"] == float(po.first_line().case_price)

        assert actual_data["caseSize"] == po.first_line().case_size
        assert actual_data["caseSizeReceived"] == round(
            actual_data["quantityReceived"] / actual_data["casesReceived"], 3
        )
        assert actual_data["quantityOrdered"] == po.first_line().qty
        assert actual_data["quantityReceived"] == po_status_steps.get_quantity_received(
            receiving_type=site_config.receiving_type, hj_receipt=hj_receipt, receiving=receiving, grn=grn
        )
        assert actual_data["casesReceived"] == po_status_steps.get_cases_received(
            receiving_type=site_config.receiving_type, hj_receipt=hj_receipt, receiving=receiving, grn=grn, po=po
        )
        assert actual_data["dateReceived"] == po_status_steps.get_date_received(
            receiving_type=site_config.receiving_type, hj_receipt=hj_receipt, receiving=receiving, grn=grn
        )
        assert actual_data["totalPrice"] == float(po.first_line().total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            Decimal(str(actual_data["quantityReceived"] * (actual_data["casePrice"] / actual_data["caseSize"]))),
            2,
        )
        assert actual_data["emergencyReason"] == po.emergency_reason
        assert actual_data["buyers"] == buyer_sku.user.full_name
        assert actual_data["poBuyer"] == buyer_sku.user.email
        assert actual_data["shipMethod"] == po.shipping_method
        assert actual_data["orderUnit"] == po.first_line().case_unit
        assert actual_data["forecastDeliveryPercent"] == round(
            (max(actual_data["quantityOrdered"], actual_data["quantityReceived"]) / int(oscar.value)), 4
        )


@pytest.mark.parametrize(
    "po_acknowledgement_units_of_measure",
    [UnitOfMeasurePoAcknowledgement.CASE, UnitOfMeasurePoAcknowledgement.UNIT],
)
def test_imt_po_status_supplier_portal_section_asn_measure_not_unit(po_acknowledgement_units_of_measure):
    """
    Test Supplier Portal section values excluding UnitMeasureASN.unit calculations.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po, unit_of_measure=po_acknowledgement_units_of_measure
    )
    advance_shipping_notice = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po, unit_of_measure=UnitMeasureASN.random_value(exclude=[UnitMeasureASN.unit])
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po)
        .with_skus(sku)
        .with_po_acknowledgement_line_items(po_acknowledgement)
        .with_advance_shipping_notice(advance_shipping_notice)
    ):
        response = ImtPoStatusClient().get_po_status(po.week, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        if po_acknowledgement.unit_of_measure == UnitOfMeasurePoAcknowledgement.CASE:
            expected_proposed_quantity_cases = po_acknowledgement.size
            expected_proposed_units_per_case = po_acknowledgement.packing_size
            expected_proposed_quantity_units = (
                actual_data["proposedUnitsPerCase"] * actual_data["proposedQuantityCases"]
            )
        else:
            expected_proposed_quantity_cases = ""
            expected_proposed_units_per_case = ""
            expected_proposed_quantity_units = po_acknowledgement.size
        assert actual_data["proposedQuantityCases"] == expected_proposed_quantity_cases
        assert actual_data["proposedUnitsPerCase"] == expected_proposed_units_per_case
        assert actual_data["proposedQuantityUnits"] == expected_proposed_quantity_units
        assert actual_data["proposedDeliveryDate"] == po_acknowledgement.promised_date.strftime(DATE_TIME_FORMAT_4)
        assert actual_data["asnShipmentDate"] == advance_shipping_notice.shipment_time.strftime(DATE_FORMAT_2)
        assert actual_data["asnPlannedDeliveryTime"] == advance_shipping_notice.planned_delivery_time.strftime(
            DATE_FORMAT_2
        )
        assert actual_data["asnShippedQuantityCases"] == advance_shipping_notice.size
        assert actual_data["asnCaseCount"] == advance_shipping_notice.packing_size
        assert actual_data["asnUnitsOfMeasure"] == UnitMeasureASN(advance_shipping_notice.unit_of_measure).name
        assert (
            actual_data["asnShippedQuantityUnits"]
            == actual_data["asnShippedQuantityCases"] * actual_data["asnCaseCount"]
        )


def test_po_status_supplier_portal_section_asn_measure_is_unit():
    """
    Test Supplier Portal section values with UnitMeasureASN.unit.
    """
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config,
    )
    advance_shipping_notice = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po, unit_of_measure=UnitMeasureASN.unit
    )
    with (
        TestData.root_data()
        .with_purchase_orders(po)
        .with_skus(po.first_line().sku)
        .with_advance_shipping_notice(advance_shipping_notice)
    ):
        response = ImtPoStatusClient().get_po_status(po.week, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["asnShippedQuantityUnits"] == advance_shipping_notice.size
        assert actual_data["asnUnitsOfMeasure"] == UnitMeasureASN(advance_shipping_notice.unit_of_measure).name


def test_po_status_n_a_void():
    """
    Test Po status N/A - PO Void
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po_void_1 = TestPurchaseOrder.generate_po_with_sku(site=site_config, order_size=0)
    po_void_2 = TestPurchaseOrder.generate_po_with_sku(site=site_config, emergency_reason=CHARITY_EMERGENCY_REASON)
    po_void_3 = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    po_void = TestPoVoid.generate_po_void(site_config=site_config, po=po_void_3)

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po_void_1, po_void_2, po_void_3)
        .with_skus(po_void_1.first_line().sku, po_void_2.first_line().sku, po_void_3.first_line().sku)
        .with_po_void(po_void)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=3
        )

        for actual_po in actual_data:
            assert actual_po["poStatus"] == PoStatuses.N_A_PO_VOID


def test_po_statuses_highjump():
    """
    Test po statuses with HJ receiving type (Received - Accurate, Received - Over, Received - Under, Delivery Rejected,
    Receipt Cancelled, Not Delivered - Past Due, In Progress HJ, Received - Partial Rejection)
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()

    po_is_sent = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=datetime.now() - timedelta(days=10)
    )
    po_received_accurate_autobagger = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, supplier_name="Autobagger - " + site_config.site.code
    )
    po_received_accurate = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_received_over = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_received_under = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_in_progress_hj = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_delivery_rejected = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_receipt_cancelled = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_partial_rejection = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_list = [
        po_is_sent,
        po_not_delivered,
        po_in_progress_hj,
        po_received_accurate_autobagger,
        po_received_accurate,
        po_received_over,
        po_received_under,
        po_delivery_rejected,
        po_receipt_cancelled,
        po_partial_rejection,
    ]

    hj_receipt_in_progress = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        status=HJReceiptStatuses.random_value(
            exclude=[
                HJReceiptStatuses.REJECTED,
                HJReceiptStatuses.CLOSED,
                HJReceiptStatuses.CANCELLED,
                HJReceiptStatuses.CLOSED_PARTIAL_REJECTION,
            ]
        ),
        po=po_in_progress_hj,
    )
    hj_receipt_received_accurate = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        po=po_received_accurate,
        status=HJReceiptStatuses.CLOSED,
        quantity_received=int(po_received_accurate.first_line().qty),
    )
    hj_receipt_received_over = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        po=po_received_over,
        status=HJReceiptStatuses.CLOSED,
        quantity_received=int(po_received_over.first_line().qty + random.randint(1, 100)),
    )
    hj_receipt_received_under = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        po=po_received_under,
        status=HJReceiptStatuses.CLOSED,
        quantity_received=int(po_received_under.first_line().qty - random.randint(1, 100)),
    )
    hj_receipt_delivery_rejected = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, status=HJReceiptStatuses.REJECTED, po=po_delivery_rejected
    )
    hj_receipt_cancelled = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, status=HJReceiptStatuses.CANCELLED, po=po_receipt_cancelled
    )
    hj_receipt_partial_rejection = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, status=HJReceiptStatuses.CLOSED_PARTIAL_REJECTION, po=po_partial_rejection
    )
    hj_receipt_received_second = dataclasses.replace(
        hj_receipt_received_over, receipt_time_est=hj_receipt_received_over.receipt_time_est + timedelta(days=3)
    )

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(*po_list)
        .with_hj_receipts(
            hj_receipt_in_progress,
            hj_receipt_received_accurate,
            hj_receipt_received_under,
            hj_receipt_received_over,
            hj_receipt_delivery_rejected,
            hj_receipt_cancelled,
            hj_receipt_partial_rejection,
            hj_receipt_received_second,
        )
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=response, additional_keys=[site_config.site.code], expected_length=len(po_list)
            )
        }

        assert actual_data[po_delivery_rejected.po_number]["poStatus"] == PoStatuses.DELIVERY_REJECTED
        assert actual_data[po_in_progress_hj.po_number]["poStatus"] == PoStatuses.IN_PROGRESS_HJ_FORMAT.format(
            hj_receipt_in_progress.status
        )
        assert actual_data[po_is_sent.po_number]["poStatus"] == PoStatuses.IS_SENT_PENDING_ACCEPTANCE
        assert actual_data[po_not_delivered.po_number]["poStatus"] == PoStatuses.NOT_DELIVERED_PAST_DUE
        assert actual_data[po_receipt_cancelled.po_number]["poStatus"] == PoStatuses.RECEIPT_CANCELLED
        assert actual_data[po_received_accurate.po_number]["poStatus"] == PoStatuses.RECEIVED_ACCURATE
        assert actual_data[po_received_accurate_autobagger.po_number]["poStatus"] == PoStatuses.RECEIVED_ACCURATE
        assert actual_data[po_received_over.po_number]["poStatus"] == PoStatuses.RECEIVED_OVER
        assert actual_data[po_received_under.po_number]["poStatus"] == PoStatuses.RECEIVED_UNDER
        assert actual_data[po_partial_rejection.po_number]["poStatus"] == PoStatuses.RECEIVED_PARTIAL_REJECTION

        assert actual_data[po_received_over.po_number][
            "dateReceived"
        ] == hj_receipt_received_second.receipt_time_est.strftime(DATE_TIME_FORMAT_1)


def test_po_status_received_manual():
    """
    Test po statuses for Manual Receiving Input Type (Received-Accurate, Not Delivered-Past Due, Received-Over,
    Delivery Rejected, Received-Under)
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.MANUAL
    sku = TestSku.generate_sku()
    po_received_accurate, po_received_over, po_received_under = TestPurchaseOrder.generate_pos(
        quantity=3, site=site_config, sku=sku
    )

    receiving_accurate = TestReceiving.generate_receiving(
        po=po_received_accurate,
        site_config=site_config,
        case_count_one_total_units=po_received_accurate.first_line().order_size,
        case_size_one=int(po_received_accurate.first_line().case_size),
    )
    receiving_over = TestReceiving.generate_receiving(
        po=po_received_over,
        site_config=site_config,
        case_count_one_total_units=po_received_over.first_line().order_size,
        case_count_two_total_units=data_generator.random_int(),
        case_size_one=int(po_received_over.first_line().case_size),
        case_size_two=data_generator.random_int(),
    )
    receiving_under = TestReceiving.generate_receiving(
        po=po_received_under,
        site_config=site_config,
        # Here is dividing by 2 because we need a value less than PO order size and greater than 0
        case_count_one_total_units=int(po_received_under.first_line().order_size / 2),
        case_size_one=int(po_received_under.first_line().case_size) - data_generator.random_int(),
    )
    po_delivery_rejected = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    po_past = TestPurchaseOrder.generate_po_with_sku(site=site_config, delivery_time_start=YESTERDAY_DATETIME, sku=sku)
    receiving_delivery_rejected = TestReceiving.generate_receiving(
        po=po_delivery_rejected,
        site_config=site_config,
        case_count_one_total_units=data_generator.random_int(),
        case_size_one=random.randint(-100, -1),
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_received_accurate, po_received_over, po_received_under, po_delivery_rejected, po_past)
        .with_receiving(receiving_accurate, receiving_over, receiving_under, receiving_delivery_rejected)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=5
        )
        actual_data = {po["poNumber"]: po for po in response_data}
        assert actual_data[po_received_accurate.po_number]["poStatus"] == PoStatuses.RECEIVED_ACCURATE
        assert actual_data[po_received_over.po_number]["poStatus"] == PoStatuses.RECEIVED_OVER
        assert actual_data[po_received_under.po_number]["poStatus"] == PoStatuses.RECEIVED_UNDER
        assert actual_data[po_delivery_rejected.po_number]["poStatus"] == PoStatuses.DELIVERY_REJECTED
        assert actual_data[po_past.po_number]["poStatus"] == PoStatuses.NOT_DELIVERED_PAST_DUE


def test_po_status_asn_records():
    """
    Test Po statuses (Shipped, Partially Shipped, Invalid, On Hold and Cancelled)
    in the case when Advanced Shipping Notice record exist.

    Test steps:
    1.Set up test data
    2.Make API call to po status endpoint -> 200
    4.Check that POs have correct status
    Expected calculation:
    IF exists ASN record for PO-SKU combination:
    IF ASN.shipping_state == SHIPPING_STATE_SHIPPED: THEN "Shipped"
    IF ASN.shipping_state == SHIPPING_STATE_PARTIALLY_SHIPPED: THEN "Partially Shipped"
    IF ASN.shipping_state == SHIPPING_STATE_UNSPECIFIED: THEN “Invalid”
    IF ASN.shipping_state == SHIPPING_STATE_ON_HOLD: THEN “Supplier Accepted with Changes”
    IF ASN.shipping_state == SHIPPING_STATE_CANCELLED: THEN “Supplier Cancelled”
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po_shipped, po_partially_shipped, po_invalid, po_on_hold, po_cancelled = TestPurchaseOrder.generate_pos(
        quantity=5, site=site_config, sku=sku
    )
    asn_shipped = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_shipped, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_SHIPPED
    )
    asn_partially_shipped = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_partially_shipped, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_PARTIALLY_SHIPPED
    )
    asn_unspecified = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_invalid, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_UNSPECIFIED
    )
    asn_on_hold = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_on_hold, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_ON_HOLD
    )
    asn_cancelled = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_cancelled, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_CANCELLED
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_shipped, po_partially_shipped, po_invalid, po_on_hold, po_cancelled)
        .with_advance_shipping_notice(asn_shipped, asn_partially_shipped, asn_unspecified, asn_on_hold, asn_cancelled)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=5
        )
        actual_data = {po["poNumber"]: po for po in response_data}
        assert actual_data[po_shipped.po_number]["poStatus"] == PoStatuses.SHIPPED
        assert actual_data[po_partially_shipped.po_number]["poStatus"] == PoStatuses.PARTIALLY_SHIPPED
        assert actual_data[po_invalid.po_number]["poStatus"] == PoStatuses.INVALID
        assert actual_data[po_on_hold.po_number]["poStatus"] == PoStatuses.SUPPLIER_ACCEPTED_WITH_CHANGES
        assert actual_data[po_cancelled.po_number]["poStatus"] == PoStatuses.SUPPLIER_CANCELLED


def test_po_status_po_acknowledgement():
    """
    Test po statuses when PO Acknowledgment is available (Supplier Accepted, Supplier Accepted with Changes,
    Supplier Rejected, Invalid)
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    (
        po_supplier_accepted,
        po_supplier_accepted_with_changes,
        po_supplier_rejected,
        po_invalid,
        po_is_sent,
    ) = TestPurchaseOrder.generate_pos(quantity=5, site=site_config, sku=sku)

    po_acknowledgement_accepted = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_supplier_accepted, state=POAcknowledgementState.ACCEPTED
    )
    po_acknowledgement_accepted_with_changes = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_supplier_accepted_with_changes, state=POAcknowledgementState.ACCEPTED_WITH_CHANGES
    )
    po_acknowledgement_rejected = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_supplier_rejected, state=POAcknowledgementState.REJECTED
    )
    po_acknowledgement_unspecified = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_invalid, state=POAcknowledgementState.UNSPECIFIED
    )
    po_acknowledgement_open = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_is_sent, state=POAcknowledgementState.OPEN
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(
            po_supplier_accepted, po_supplier_accepted_with_changes, po_supplier_rejected, po_invalid, po_is_sent
        )
        .with_po_acknowledgement_line_items(
            po_acknowledgement_accepted,
            po_acknowledgement_accepted_with_changes,
            po_acknowledgement_rejected,
            po_acknowledgement_unspecified,
            po_acknowledgement_open,
        )
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=5
        )
        actual_data = {po["poNumber"]: po for po in response_data}
        assert actual_data[po_supplier_accepted.po_number]["poStatus"] == PoStatuses.SUPPLIER_ACCEPTED
        assert (
            actual_data[po_supplier_accepted_with_changes.po_number]["poStatus"]
            == PoStatuses.SUPPLIER_ACCEPTED_WITH_CHANGES
        )
        assert actual_data[po_supplier_rejected.po_number]["poStatus"] == PoStatuses.SUPPLIER_REJECTED
        assert actual_data[po_invalid.po_number]["poStatus"] == PoStatuses.INVALID
        assert actual_data[po_is_sent.po_number]["poStatus"] == PoStatuses.IS_SENT_PENDING_ACCEPTANCE


def test_po_status_priorities_po_void_first():
    """
    Test PO status priorities
    Statuses should be prioritized in order:
    N_A_PO_VOID -> manual or HJ po status -> asn po status -> acknowledgment po status -> po status is_sent

    Test steps:
    1. For this test there is no need to set manual or hj receiving type, we want it to be randomised
    2. Add voided po, hj receipt, po acknowledgment and asn
    2. Check that po status is N_A_PO_VOID
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po_void = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, order_size=0, delivery_time_start=datetime.now() - timedelta(days=10), sku=sku
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        status=HJReceiptStatuses.random_value(
            exclude=[HJReceiptStatuses.REJECTED, HJReceiptStatuses.CLOSED, HJReceiptStatuses.CANCELLED]
        ),
        po=po_void,
    )
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_void, state=random.choice(list(POAcknowledgementState))
    )
    asn_shipped = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_void, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_SHIPPED
    )

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_void)
        .with_po_acknowledgement_line_items(po_acknowledgement)
        .with_hj_receipts(hj_receipt)
        .with_advance_shipping_notice(asn_shipped)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_status = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["poStatus"]
        assert actual_status == PoStatuses.N_A_PO_VOID


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.MANUAL, ReceiveInputType.HIGH_JUMP])
def test_po_status_priorities_hj_first(receiving_type):
    """
    Test PO status priorities
    Statuses should be prioritized in order:
    N_A_PO_VOID -> manual or HJ po status -> asn po status -> acknowledgment po status -> po status is_sent

    Test steps:
    1. Set receiving type to HJ or Manual
    2. Add hj receipt (status not delivered past due), po acknowledgment and asn
    2. Check that po status is NOT_DELIVERED_PAST_DUE
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receiving_type
    sku = TestSku.generate_sku()
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, delivery_time_start=CURRENT_DATETIME - timedelta(days=10), sku=sku
    )
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po_not_delivered, state=random.choice(list(POAcknowledgementState))
    )
    asn_shipped = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_not_delivered, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_SHIPPED
    )

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_not_delivered)
        .with_po_acknowledgement_line_items(po_acknowledgement)
        .with_advance_shipping_notice(asn_shipped)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_status = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["poStatus"]
        assert actual_status == PoStatuses.NOT_DELIVERED_PAST_DUE


def test_po_status_priorities_asn_first():
    """
    Test PO status priorities
    Statuses should be prioritized in order:
    N_A_PO_VOID -> manual or HJ po status -> asn po status -> acknowledgement po status -> po status is_sent

    Test steps:
    1. Set receiving type to HJ
    2. Add po, po acknowledgement and asn with state SHIPPING_STATE_SHIPPED
    2. Check that po status is SHIPPED
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=po, state=random.choice(list(POAcknowledgementState))
    )
    asn_shipped = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po, shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_SHIPPED
    )

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_po_acknowledgement_line_items(po_acknowledgement)
        .with_advance_shipping_notice(asn_shipped)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_status = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["poStatus"]
        assert actual_status == PoStatuses.SHIPPED


def test_case_received_quantity_received_future_week():
    """
    Test case checks the columns Cases Received, Quantity Received in IMT → Po Status View dashboard for future weeks.
    Test steps:
    1. Generate required test data setting week for po to be NEXT WEEK
    2. Set up the test data using the TestData context manager
    2. Make an API call and extract data from the response
    3. Check that Case Received and Quantity Received columns values from API are as expected.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=random.choice(FUTURE_WEEK_PULL_REASONS),
        week=NEXT_WEEK,
        delivery_time_start=NEXT_WEEK_FIRST_DAY,
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    user = Users.test_user.value

    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(site_config)
        .with_users(user)
        .with_purchase_orders(po)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
        .with_skus(sku)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        assert actual_data["casesReceived"] == hj_receipt.cases_received
        assert actual_data["quantityReceived"] == hj_receipt.quantity_received


@pytest.mark.parametrize(
    "user",
    [
        Users.qa_buyer.value,
        Users.dc_user.value,
        Users.dc_universal_user.value,
        Users.dc_management_user.value,
        Users.procurement_user.value,
        Users.procurement_leadership.value,
        Users.procurement_manager.value,
        Users.helloconnect_user.value,
    ],
)
def test_po_financial_export_permission(user):
    """
    Test case checks user permissions to Generate Financial export on the Po Status View dashboard.
    Test steps:
    1. Generate required test data for the PO Status View dashboard
    2. Parametrize users to check permission for all non admin users
    3. Make an API call with the financial-export endpoint
    4. Check status code shows success and text from the API response is not empty.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
    ):
        response = ImtPoStatusClient(user=user).get_po_status_financial_export_data(
            site_config=site_config, week=CURRENT_WEEK
        )
        assert response.status_code == 200
        assert len(response.text) > 0, "Response data should not be empty"


def test_asn_info_ignores_for_canada_po_status():
    """
    Test Po Status Asn values are ignored for Canada.

    Test steps:
    1.Generate required data (site configs for CA, sku, po ,asn)
    2.Make API call to po_status endpoint -> 200
    3.Assert that ASN columns are None
    """
    site_config = SiteConfigsCA.random_value()
    site_config.inventory_type = InventoryInputType.WMSL
    sku = TestSku.generate_sku(market=MARKET_CA)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    asn = TestAdvanceShippingNotice.generate_advance_shipping_notice(po=po)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_advance_shipping_notice(asn)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        asn_expected_none_columns = [
            "asnShipmentDate",
            "asnPlannedDeliveryTime",
            "asnShippedQuantityCases",
            "asnUnitsOfMeasure",
            "asnCaseCount",
            "asnShippedQuantityUnits",
            "asnRequiresHighAttention",
            "asnRequiresAttention",
        ]
        for asn_value in asn_expected_none_columns:
            assert actual_data[asn_value] is None


def test_grn_case_received_delivery_rejected_status_for_imt_po_status():
    """
    Test case checks Cases Received column value when setting grn cases_received and Delivery Rejected status.
    Test steps:
    1. Generate required test data for imt po_status dashboard(site_config, sku, po, grn with cases_received, set
    units_received to be 0 , so PO Status should be Delivery Rejected)
    2. Make an API call and extract data
    3. Check Cases Received column value is equal to grn.cases_received value and PO status is Delivery Rejected.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    site_config.receiving_type = ReceiveInputType.GRN
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        po=po, site_config=site_config, cases_received=data_generator.random_int(), units_received=0
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po)
        .with_skus(sku)
        .with_grn(grn)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["casesReceived"] == grn.cases_received
        assert actual_data["poStatus"] == PoStatuses.DELIVERY_REJECTED


def test_total_price_received_and_case_size_received_with_receipt_override():
    """
    Test Total Price Received and Case Size Received columns with hj override. If we have override values, then data
    will be taken from manual override form.

    Test steps:
    1.Generate required data (site config, sku, po, receipt_override, hj_receipt)
    2.Make API call to po_status endpoint -> 200
    3.Check that totalPriceReceived and caseSizeReceived are calculated as expected
    Expected calculations:
    totalPriceReceived = (qty_received * (case_price (po) / case_size_received)) if case_size_received else 0
    (from manual override form)
    caseSizeReceived = qty_received / cases_received (both from manual override form)
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, status=CLOSED)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po)
        .with_hj_receipts(hj_receipt)
        .with_skus(sku)
        .with_receipt_override(receipt_override)
    ):
        response = ImtPoStatusClient().get_po_status(po.week, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        expected_case_size_received = round(receipt_override.quantity / receipt_override.cases, 3)
        expected_total_price_received = round(
            receipt_override.quantity * (po.first_line().case_price / po.first_line().case_size), 2
        )

        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == expected_total_price_received
        assert actual_data["caseSizeReceived"] == expected_case_size_received


def test_receive_date_for_cancelled_po():
    """
    Test Date Received column with Cancelled Po.
    If PO is cancelled (hj.receipt status = cancelled) then dateReceived = hj_receipt.receipt_time_est

    Test steps:
    1.Generate required data (site config, sku, 2 pos, 2 hj_receipts)
    2.Make API call to po_status endpoint -> 200
    3.Check that dateReceived is calculated as expected
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_cancelled = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=TOMORROW_DATETIME
    )
    hj_receipt_canceled = TestHJReceipts.generate_hj_receipt(
        po=po_cancelled, site_config=site_config, status=CANCELLED, receipt_time_est=YESTERDAY_DATETIME
    )
    po_closed = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME)
    hj_receipt_closed = TestHJReceipts.generate_hj_receipt(
        po=po_closed, site_config=site_config, status=CLOSED, receipt_time_est=YESTERDAY_DATETIME
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_closed, po_cancelled)
        .with_hj_receipts(hj_receipt_canceled, hj_receipt_closed)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )
        actual_data = {po["poNumber"]: po for po in response_data}
        assert actual_data[po_cancelled.po_number]["dateReceived"] == hj_receipt_canceled.receipt_time_est.strftime(
            DATE_TIME_FORMAT_1
        )
        assert actual_data[po_closed.po_number]["dateReceived"] == hj_receipt_closed.receipt_time_est.strftime(
            DATE_TIME_FORMAT_1
        )


def test_unreceived_pos_for_e2o_enabled_3pls_po_statuses():
    """
    Test case checks po statuses, Quantity Received, Cases received column values for received/unreceived pos
    Test steps:
    1. Generate required test data with site_config (GRN receiving type, 3pl sites), sku, pos with received and
    N/A - PO Void statuses, po_void, receipt_override for 2 pos
    2. Make an API call to Po Status endpoint -> 200 and extract data
    3. Check po_statuses are in [N_A_PO_VOID, Received], and Quantity Received, Cases received column values are taken
    from appropriate receipt_override data
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.GRN
    site_config.is_3pl = True
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_void = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_void_data = TestPoVoid.generate_po_void(site_config=site_config, po=po_void)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po)
    receipt_override_void = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_void)
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po, po_void)
        .with_po_void(po_void_data)
        .with_receipt_override(receipt_override, receipt_override_void)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )
        actual_data = {po["poNumber"]: po for po in response_data}

        assert actual_data[po.po_number]["quantityReceived"] == receipt_override.quantity
        assert actual_data[po.po_number]["casesReceived"] == receipt_override.cases
        assert actual_data[po.po_number]["poStatus"] in RECEIVED_STATUSES
        assert actual_data[po_void.po_number]["quantityReceived"] == receipt_override_void.quantity
        assert actual_data[po_void.po_number]["casesReceived"] == receipt_override_void.cases
        assert actual_data[po_void.po_number]["poStatus"] == PoStatuses.N_A_PO_VOID


def test_expanded_in_progress_hj_statuses():
    """
    Test case checks expanded In Progress HJ statuses.
    Test steps:
    1. Generate required test data with site_config, sku, pos, hj_receipts with CHECKED_IN, UNLOADING,
    QC_SUPERVISOR_NEEDED, READY_TO_PRINT_LABELS, BUILDING_PALLETS, PRINTING_LABELS statuses
    2. Make an API call to IMT -> Po status View endpoint -> 200
    3. Check po statuses from the API call meet appropriate statuses.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    statuses = [
        HJReceiptStatuses.CHECKED_IN,
        HJReceiptStatuses.UNLOADING,
        HJReceiptStatuses.QC_SUPERVISOR_NEEDED,
        HJReceiptStatuses.READY_TO_PRINT_LABELS,
        HJReceiptStatuses.BUILDING_PALLETS,
        HJReceiptStatuses.PRINTING_LABELS,
    ]
    po_list = TestPurchaseOrder.generate_pos(sku=sku, site=site_config, quantity=len(statuses))
    hj_receipts = [
        TestHJReceipts.generate_hj_receipt(site_config=site_config, status=status, po=po)
        for status, po in zip(statuses, po_list)
    ]

    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(*po_list)
        .with_hj_receipts(*hj_receipts)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=response, additional_keys=[site_config.site.code], expected_length=len(po_list)
            )
        }
        for po, status in zip(po_list, statuses):
            assert actual_data[po.po_number]["poStatus"] == PoStatuses.IN_PROGRESS_HJ_FORMAT.format(status)


def test_case_price_flag_on_po_status():
    """
    Test case checks Case Price flag on IMT -> Po status View
    Expected result: Flag should be highlighted in pink(value is True) only if po.unit_price > allocation_price by 15 %.
    Test steps:
    1. Generate required test data with site_config, sku, allocation_data, po records (to be higher and lower than
    allocation_price by 15 %.)
    2. Make an API call -> 200, extract data
    3. Check casePriceFlag is True only if po.unit_price > allocation_price by 15 %, False otherwise
    4. Check when multi week is enabled which includes site's week, api call is done successfully
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    allocation_data = TestAllocationPrice.generate_allocation_price(sku=sku, site_config=site_config)
    po_record_1, po_record_2 = TestPurchaseOrder.generate_pos(quantity=2, sku=sku, site=site_config)
    po_record_1.first_line().case_price = (
        allocation_data.price * PO_CASE_PRICE_FLAG_PERCENTAGE_THRESHOLD + data_generator.random_int()
    )
    po_record_1.first_line().case_size = 1
    po_record_2.first_line().case_price = allocation_data.price
    po_record_2.first_line().case_size = 1
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po_record_1, po_record_2)
        .with_skus(sku)
        .with_allocation_price(allocation_data)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )
        actual_po_data = {po["poNumber"]: po for po in actual_data}
        assert actual_po_data[po_record_1.po_number]["casePriceFlag"] is True
        assert actual_po_data[po_record_2.po_number]["casePriceFlag"] is False
        response_multi_week = ImtPoStatusClient().get_po_status(
            *(CURRENT_WEEK + week for week in range(-3, 2)), site_config=site_config
        )
        assert response_multi_week.status_code == HTTPStatus.OK


def test_transfer_order_status_on_po_status_view():
    """
    Test case checks added Transfer Order (TO) values on PO Status view.
    All TO line items should be shown with their corresponding supplier and receiving data based on supplier.
    Test steps:
    1. Generate required test data with po with transfer_orders, suppliers
    2. Make an API request to Transfer Orders endpoint -> 200, extract values
    3. Check all values to be as expected
    """
    site_config, original_site = SiteConfigs.random_non_repetitive_values(2)
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    original_pos = TestPurchaseOrder.generate_pos(quantity=2, site=original_site, sku=sku)
    transfer_order = TestPurchaseOrder.generate_transfer_order(original_pos=original_pos, site=site_config)
    receipt_supplier_1 = TestHJReceipts.generate_hj_receipt(
        po=transfer_order,
        site_config=site_config,
        supplier_code=str(original_pos[0].supplier.code),
        quantity_received=int(transfer_order.transfer_line_items[0].qty),
        cases_received=transfer_order.transfer_line_items[0].order_size,
    )
    receipt_supplier_2 = TestHJReceipts.generate_hj_receipt(
        po=transfer_order, site_config=site_config, supplier_code=str(original_pos[1].supplier.code)
    )
    expected_receipts = {int(it.supplier_code): it for it in (receipt_supplier_1, receipt_supplier_2)}
    expected_transfer_items = transfer_order.get_transfer_line_items_by_supplier_code()
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(*original_pos, transfer_order)
        .with_skus(sku)
        .with_hj_receipts(*expected_receipts.values())
    ):
        to_status_response = ImtPoStatusClient().get_to_dropdown_data(po=transfer_order)
        response_data = base_steps.process_response_and_extract_data(response=to_status_response, expected_length=2)
        actual_to_data = {it["supplier"]: it for it in response_data}
        for po in original_pos:
            supplier = po.supplier
            expected_to_item: TestTransferOrderSku = expected_transfer_items[supplier.code]
            receipt = expected_receipts[supplier.code]
            actual_to_item = actual_to_data[supplier.name]
            assert actual_to_item["supplier"] == supplier.name
            assert actual_to_item["orderSize"] == expected_to_item.order_size
            assert actual_to_item["orderUnit"] == expected_to_item.case_unit
            comparisons.assert_numbers_equal(actual_to_item["casePrice"], expected_to_item.case_price)
            comparisons.assert_numbers_equal(actual_to_item["caseSize"], expected_to_item.case_size)
            comparisons.assert_numbers_equal(actual_to_item["quantityOrdered"], expected_to_item.qty)
            comparisons.assert_numbers_equal(actual_to_item["totalPrice"], expected_to_item.total_price)
            expected_status = (
                PoStatuses.RECEIVED_UNDER
                if receipt.quantity_received < expected_to_item.qty
                else (
                    PoStatuses.RECEIVED_ACCURATE
                    if receipt.quantity_received == expected_to_item.qty
                    else PoStatuses.RECEIVED_OVER
                )
            )
            assert actual_to_item["poStatus"] == expected_status
            assert actual_to_item["quantityReceived"] == receipt.quantity_received
            assert actual_to_item["casesReceived"] == receipt.cases_received
            comparisons.assert_numbers_equal(
                actual_to_item["totalPriceReceived"],
                Decimal(receipt.quantity_received) * (expected_to_item.case_price / expected_to_item.case_size),
            )
