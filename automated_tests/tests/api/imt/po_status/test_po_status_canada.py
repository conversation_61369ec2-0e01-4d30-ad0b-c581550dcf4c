from automated_tests.data.constants.base_constants import PoStatuses, SiteConfigsCA, UsersCA
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from procurement.constants.hellofresh_constant import MARKET_CA, InventoryInputType


def test_is_sent_status_canada():
    """
    Test that “Is Sent” is shown instead of "Is Sent - Pending Accepted" for Canada market

    Test steps:
    1.Generate required data (site configs for CA, sku, po)
    2.Make API call to po_status endpoint -> 200
    3.Check that po status is "Is Sent"
    """
    site_config = SiteConfigsCA.random_value()
    site_config.inventory_type = InventoryInputType.WMSL
    sku = TestSku.generate_sku(market=MARKET_CA)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert response_data["poStatus"] == PoStatuses.IS_SENT
