from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    PurchasingCategories,
    SiteConfigs,
    SiteConfigsCA,
    SiteConfigsFactor,
    Users,
    UsersCA,
)
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, CURRENT_WEEK_FACTOR
from automated_tests.data.models.canada_forecast import TestCanadaForecastRecipe
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.manufactured_sku import (
    TestManufacturedSku,
    TestManufacturedSkuPart,
    TestManufacturedSkuWeek,
)
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.production_kit_guide import ProductionKitGuideClient
from procurement.constants.hellofresh_constant import MARKET_CA
from procurement.core import utils


def test_production_kit_guide_values():
    """
    Test all fields on Production Kit Guide Dashboard.

    Test steps:
    1.Generate required test data (site_config, sku, po, category, mealkit, ingredient)
    2.Make API call to production_kit_guide endpoint -> 200
    3.Check if the data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    category = PurchasingCategories.random_value()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient.generate_ingredients(
        sku=sku,
        brand=site_config.brand,
        allergens=data_generator.generate_string(),
        storage_location=data_generator.generate_string(),
    )
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchasing_category(category, sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_skus(sku)
    ):
        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["code"] == str(mealkit.code)
        assert actual_data["mealNumber"] == mealkit.slot
        assert actual_data["mealName"] == mealkit.meal_name
        assert actual_data["skuId"] == utils.get_sku_id_from_sku_code(sku.sku_code)
        assert actual_data["picks2p"] == mealkit_ingredient.picks_2p
        assert actual_data["picks3p"] == mealkit_ingredient.picks_3p
        assert actual_data["picks4p"] == mealkit_ingredient.picks_4p
        assert actual_data["picks6p"] == mealkit_ingredient.picks_6p
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["purchasingCategory"] == category
        assert actual_data["fullSkuPlusSkuName"] == "-".join([sku.sku_code, sku.sku_name])
        assert actual_data["weightUnit"] == mealkit_ingredient.weight_unit
        assert actual_data["weightAmount"] == mealkit_ingredient.weight_amount
        assert actual_data["storageLocation"] == ingredient.storage_location
        assert actual_data["allergens"] == ingredient.allergens
        assert actual_data["subRecipe"] == mealkit.sub_recipe


def test_production_kit_guide_values_factor():
    """
    Test all fields on Production Kit Guide Dashboard for Factor brand.

    Test steps:
    1.Generate required test data
    2.Make API call to production_kit_guide endpoint -> 200
    3.Check if the data matches the expected values from the response
    """
    site_config = SiteConfigsFactor.random_value()
    sku_full_receipt = TestSku.generate_sku(prefix="REC")
    sku_sub_receipt = TestSku.generate_sku()
    sku_sub_receipt_2 = TestSku.generate_sku()
    category = PurchasingCategories.random_value()
    man_sku_full_receipt = TestManufacturedSku.generate_manufactured_sku(sku=sku_full_receipt)
    man_sku_full_receipt_2 = TestManufacturedSku.generate_manufactured_sku(sku=sku_sub_receipt)
    man_sku_sub_receipt = TestManufacturedSkuPart.generate_manufactured_sku_part(
        sku_root=sku_full_receipt, sku_part=sku_sub_receipt, is_manufactured=True
    )
    man_sku_sub_receipt_2 = TestManufacturedSkuPart.generate_manufactured_sku_part(
        sku_root=sku_sub_receipt, sku_part=sku_sub_receipt_2, is_manufactured=False
    )
    man_sku_week = TestManufacturedSkuWeek.generate_manufactured_week(
        manufactured_sku=sku_full_receipt, week=CURRENT_WEEK_FACTOR
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchasing_category(category, sku_sub_receipt_2)
        .with_skus(sku_full_receipt, sku_sub_receipt, sku_sub_receipt_2)
        .with_manufactured_sku(man_sku_full_receipt, man_sku_full_receipt_2)
        .with_manufactured_sku_part(man_sku_sub_receipt, man_sku_sub_receipt_2)
        .with_manufactured_sku_week(man_sku_week)
    ):
        response = ProductionKitGuideClient().get_production_kit_guide(
            site_config=site_config, week=CURRENT_WEEK_FACTOR
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["code"] == sku_sub_receipt.sku_code
        assert actual_data["subRecipe"] == sku_sub_receipt.sku_name
        assert actual_data["mealNumber"] == sku_full_receipt.sku_code
        assert actual_data["mealName"] == sku_full_receipt.sku_name
        assert actual_data["skuId"] == utils.get_sku_id_from_sku_code(sku_sub_receipt_2.sku_code)
        assert actual_data["sku"] == sku_sub_receipt_2.sku_code
        assert actual_data["skuName"] == sku_sub_receipt_2.sku_name
        assert actual_data["purchasingCategory"] == category
        assert actual_data["fullSkuPlusSkuName"] == "-".join([sku_sub_receipt_2.sku_code, sku_sub_receipt_2.sku_name])
        assert actual_data["weightUnit"] == ",".join(man_sku_sub_receipt.unit_of_measure)
        assert actual_data["weightAmount"] == float(man_sku_sub_receipt_2.quantity * man_sku_sub_receipt.quantity)
        assert actual_data["storageLocation"] is None
        assert actual_data["allergens"] is None
        assert actual_data["picks2p"] is None
        assert actual_data["picks3p"] is None
        assert actual_data["picks4p"] is None
        assert actual_data["picks6p"] is None


def test_pkg_values_for_canada():
    """
    Test all values of Canada PKG record

    Test steps:
    1. Add Canada Site to the config
    2. Add PKG data to Canada recipie table
    3. Check all values in the PKG response
    """
    site_config = SiteConfigsCA.random_value()
    sku = TestSku.generate_sku(market=MARKET_CA)
    category = PurchasingCategories.random_value()
    canada_recipie = TestCanadaForecastRecipe.generate_canada_forecast_recipe(
        site_config=site_config, sku=sku, week=CURRENT_WEEK
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_purchasing_category(category, sku)
        .with_canada_forecast_recipe(canada_recipie)
    ):
        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["code"] == canada_recipie.recipe
        assert actual_data["mealNumber"] == canada_recipie.recipe
        assert actual_data["mealName"] == canada_recipie.recipe_name
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["purchasingCategory"] == category
        assert actual_data["fullSkuPlusSkuName"] == "-".join([sku.sku_code, sku.sku_name])
        assert actual_data["subRecipe"] is None
        assert actual_data["weightUnit"] is None
        assert actual_data["weightAmount"] is None
        assert actual_data["storageLocation"] is None
        assert actual_data["allergens"] is None
        assert actual_data["picks2p"] == canada_recipie.picks
        assert actual_data["picks3p"] == 0
        assert actual_data["picks4p"] == canada_recipie.picks
        assert actual_data["picks6p"] == canada_recipie.picks
