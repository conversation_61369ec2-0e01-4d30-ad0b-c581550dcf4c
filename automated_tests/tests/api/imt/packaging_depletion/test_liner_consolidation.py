import random

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    <PERSON><PERSON>,
    PurchasingCategories,
    Seasons,
    SiteConfigs,
    SiteConfigsFactor,
    SiteConfigsGC,
    Sizes,
    Users,
)
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, CURRENT_WEEK_FACTOR
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.highjump import TestHJPackagingPalletSnapshot
from automated_tests.data.models.packaging_demand import TestPackagingDemand
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import packaging_depletion_steps as depl_steps
from automated_tests.services.api.imt.packaging_depletion import PackagingDepletionClient
from procurement.constants.hellofresh_constant import ReceiveInputType


def test_liner_groups():
    """
    Test filling of liner groups.

    Test steps:
    1.Generate required test data
    To create the liner group the name of SKU name should start with "Liner" and contain the season and size,
    for this purpose the sku is generated by `generate_skus_for_liners` function. `generate_skus_for_liners` function
    as default returns the dict, where the keys are liner groups (16 unique Parent Liner groups) and the values
    are generated SKUs for each liner group.
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation: Liner group should contain the SKUs where SKU name contains size and season according
    to liner’s group
    """
    site_config = SiteConfigs.random_value()
    skus_with_liner_group = depl_steps.generate_skus_for_liners()
    skus = list(skus_with_liner_group.values())
    pos = [TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku) for sku in skus]
    packaging_demands = [
        TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config) for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(*packaging_demands)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(response=response)[site_config.site.code]
        processed_liner_groups = []
        liner_groups = [liner_group for liner_group in skus_with_liner_group.keys()]
        for liner_group in liner_groups:
            for liner in actual_data:
                if liner["skuName"].lower() == liner_group.lower():
                    assert liner["liners"][0]["sku"] == skus_with_liner_group[liner_group].sku_code
                    processed_liner_groups.append(liner_group)

        unprocessed_liner_groups = [
            liner_group for liner_group in liner_groups if liner_group not in processed_liner_groups
        ]
        if unprocessed_liner_groups:
            raise AssertionError(f"Liner Groups: {unprocessed_liner_groups} processed incorrectly")


def test_liner_groups_not_created():
    """
    Test that liner groups not create. To create the liner group the name of SKU name should start with "Liner" and
    contain the season and size.

    Test steps:
    1.Generate required test data
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation: Liner groups and liners -> None
    """
    site_config = SiteConfigs.random_value()
    skus = TestSku.generate_skus_with_specific_sku_names(
        sku_names=[f"Liner, {Sizes.random_value()}", f"Liner, {Seasons.random_value()}"]
    )
    pos = [TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku) for sku in skus]
    packaging_demands = [
        TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config) for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(*packaging_demands)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, expected_length=2, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["linerGroup"] is None
        assert actual_data["liners"] is None


def test_ingredient_summary_values():
    """
    Test that columns in the ingredient summary section calculate in the right way.

    Test steps:
    1.Generate required test data
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation: SKU -> None by default, SKU name -> equal to liner group, Category -> equal to "Packaging",
    Commodity group defines as "Liner" by default, Buyer -> equal to full name user.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku(sku_name=depl_steps.random_sku_name_for_liners())
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    purchasing_category = PurchasingCategories.PACKAGING.value
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_buyer_sku(buyer_sku)
        .with_purchasing_category(purchasing_category, sku)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["sku"] is None, "Sku should be None by default"
        assert actual_data["skuName"] == actual_data["linerGroup"]
        assert actual_data["category"] == purchasing_category
        assert actual_data["commodityGroup"] == "Liner"
        assert actual_data["buyer"] == buyer_sku.user.full_name


def test_units_short_and_weekly_overview_values():
    """
    Test that columns in the status and weekly overview sections calculate in the right way.

    Test steps:
    1.Generate required test data
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation:
    Units Short -> None by default
    All columns in weekly overview section -> None by default
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku(sku_name=depl_steps.random_sku_name_for_liners())
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        weekly_overview_data = actual_data["weeklyOverview"]

        assert actual_data["unitsShort"] is None, "Units Short column should be None by default"
        assert all(
            value is None for value in weekly_overview_data.values()
        ), "Weekly overview values should be None by default"


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_daily_needs_liners_values_except_status(receiving_type):
    """
    Test that columns in daily needs section calculate in the right way.

    Test steps:
    1.Generate required test data
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation:
    EOD Inventory -> Computed On Hand* + Computed Incoming POs*  - Demand
    On Hand (Projected), Demand, Incoming POs -> SUM Parts
    On Hand, On Hand (Override), Incoming POs (Override) -> SUM Parts, but ignoring empty values
    On Hand (HJ) - On Hand (Projected) -> the difference On Hand (HJ) - On Hand (Projected)
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receiving_type
    sku_name = depl_steps.random_sku_name_for_liners()
    sku_with_part_a = TestSku.generate_sku(sku_name=sku_name + " Part A")
    sku_with_part_b = TestSku.generate_sku(sku_name=sku_name + " Part B")
    sku_non_parts = TestSku.generate_sku(sku_name=sku_name)
    skus = [sku_with_part_a, sku_with_part_b, sku_non_parts]
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config) for sku in skus]
    packaging_demands = [
        TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config) for sku in skus
    ]
    packaging_snapshots = [
        TestHJPackagingPalletSnapshot.generate_packaging_snapshot(site_config=site_config, sku=sku) for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(*packaging_demands)
        .with_hj_packaging_palette_snapshot(*packaging_snapshots)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        liners_data = actual_data["liners"]
        daily_needs_data = actual_data["dailyNeeds"]

        depl_steps.check_on_hand_min_on_hand_projected(daily_needs_data)
        depl_steps.check_eod_inventory(daily_needs_data)
        liner_children = depl_steps.calculate_liner_children_depending_on_parts(liners_data, len(daily_needs_data))
        for day in range(len(daily_needs_data)):
            on_hand = [child[day]["onHand"] for child in liner_children if child[day]["onHand"] is not None]
            assert daily_needs_data[day]["onHand"] == (sum(on_hand) if on_hand else None)
            on_hand_override = [
                child[day]["onHandOverride"] for child in liner_children if child[day]["onHandOverride"]
            ]
            assert daily_needs_data[day]["onHandOverride"] == (sum(on_hand_override) if on_hand_override else None)
            assert daily_needs_data[day]["demand"] == sum(child[day]["demand"] or 0 for child in liner_children)
            assert daily_needs_data[day]["incoming"] == sum(child[day]["incoming"] or 0 for child in liner_children)
            incoming_override = [
                child[day]["incomingOverride"] for child in liner_children if child[day]["incomingOverride"]
            ]
            assert daily_needs_data[day]["incomingOverride"] == (sum(incoming_override) if incoming_override else None)
            assert daily_needs_data[day]["onHandProjected"] == sum(
                child[day]["onHandProjected"] or 0 for child in liner_children
            )


@pytest.mark.parametrize(
    "demand",
    [[0, 0, 0, 0, 0, 0, 0, 0], data_generator.generate_list_with_random_int_in_range(size=8, random_range=(30, 40))],
)
def test_status_and_supplement_need_columns(demand):
    """
    Test status and supplement need columns.

    Test steps:
    1.Generate required test data
    2.Make API call
    3.Extract actual data from the response
    4.Check if the actual data matches the expected values.
    Expected calculation:
    Status -> IF any child SKU status = “Supplement Needed” ELSE status from last child (“No Impact” or “Day Passed”)
    To ensure that we will have at least one day for each case we add parameters:
    *all no impact/day passed = no impact/day passed (for this we set up all demands 0)
    *one no impact/day passed + one supplement needed = supplement needed  (for this we set up demands for current day
    > pallet.quantity (on hand))
    Supplement  Need -> IF any production day for any of the child SKUs is flagged as "Supplement Needed",
    THEN populate with the first production day for which you see the flag.
    IF no production days are flagged as "Supplement Need", THEN this column should be populated with "No Impact"
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku_name = depl_steps.random_sku_name_for_liners()
    sku_with_part_a = TestSku.generate_sku(sku_name=f"{sku_name} Part A")
    sku_with_part_b = TestSku.generate_sku(sku_name=f"{sku_name} Part B")
    skus = [sku_with_part_a, sku_with_part_b]
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config) for sku in skus]
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku_with_part_a, site_config=site_config, demand_by_day=[0, 0, 0, 0, 0, 0, 0, 0]
    )
    packaging_demand_2 = TestPackagingDemand.generate_packaging_demand(
        sku=sku_with_part_b, site_config=site_config, demand_by_day=demand
    )
    packaging_snapshots = [
        TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
            site_config=site_config, sku=sku, pallet_quantity=random.randint(10, 20)
        )
        for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(packaging_demand, packaging_demand_2)
        .with_hj_packaging_palette_snapshot(*packaging_snapshots)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        daily_needs_data = actual_data["dailyNeeds"]

        depl_steps.check_supplement_need(daily_needs_data, actual_data)
        depl_steps.check_status(daily_needs_data=daily_needs_data)


def test_that_green_chef_liners_are_considered_for_liner_consolidation():
    """
    Test liner consolidation for Green Chef.
    The SKUs that start with "GC, Liner" are considered for liner consolidation (in addition to "Liner")

    Test steps:
    1.Generate required test data (site config, sku with sku_name "GC, Liner" + random size and season, po, pack_demand)
    2.Make API call to packaging_depletion endpoint -> 200
    3.Check that sku_name is equal to name of liner group and len of liners is equal to 1 as expected
    """
    site_config = SiteConfigsGC.random_value()
    sku = TestSku.generate_sku(sku_name=f"{Brands.GC.name}, {depl_steps.random_sku_name_for_liners()}")
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchasing_category(PurchasingCategories.PACKAGING, sku)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["skuName"] == actual_data["linerGroup"]
        assert len(actual_data["liners"]) == 1


def test_that_factor_liners_are_considered_for_liner_consolidation():
    """
    Test liner consolidation for Factor.
    The SKUs that start with "Factor, Liner" are considered for liner consolidation (in addition to "Liner")

    Test steps:
    1.Generate required test data
    (site config, sku with sku_name "Factor, Liner" + random size and season, po, pack_demand)
    2.Make API call to packaging_depletion endpoint -> 200
    3.Check that sku_name is equal to name of liner group and len of liners is equal to 1 as expected
    """
    site_config = SiteConfigsFactor.random_value()
    site_config.week = CURRENT_WEEK_FACTOR
    sku = TestSku.generate_sku(sku_name=f"Factor, {depl_steps.random_sku_name_for_liners()}")
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku, week=site_config.week)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku, site_config=site_config, week=site_config.week
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchasing_category(PurchasingCategories.PACKAGING, sku)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
    ):
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=site_config.week)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["skuName"] == actual_data["linerGroup"]
        assert len(actual_data["liners"]) == 1
