from decimal import Decimal
from http import HTTPStatus

import pytest

from automated_tests.data.constants.base_constants import (
    <PERSON>list<PERSON>tatus,
    DayOfWeek,
    EmergencyReason,
    PoStatuses,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsGC,
    SkuNames,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    PREVIOUS_WEEK,
    WEEK_BEFORE_PREVIOUS,
)
from automated_tests.data.constants.error_messages_constants import (
    FOB_DATE_CANT_BE_SAVED_FOR_NON_FREIGHT_ON_BOARD_ORDERS_ERROR_MSG,
    FOB_PICK_UP_DATE_CANT_BE_SET,
    MANDATORY_ERROR_MESSAGE,
    ONLY_LAST_PREVIOUS_WEEK_IS_AVAILABLE_FOR_EDIT,
    PO_NUMBER_NOT_FOUNT_IN_SELECTED_WEEK_ERROR_MESSAGE,
    SKU_NAME_IS_NOT_FOUNT_IN_PO_ERROR_MESSAGE,
)
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.organic_sku import TestOrganicSku
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.weekend_coverage_checklist import TestWcc
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.weekend_coverage_checklist_form import WeekendCoverageChecklistClient
from procurement.constants.hellofresh_constant import ReceiveInputType


@pytest.mark.parametrize(
    "shipping_method", [ShippingMethods.FREIGHT_ON_BOARD, ShippingMethods.CROSSDOCK, ShippingMethods.VENDOR_DELIVERED]
)
def test_insert_weekend_checklist_data(shipping_method):
    """
    Test insert data in WCC form

    1.Generate the test data
    2.Prepare the test environment
    3.Make API call
    4.Assert the data from post and get responses
    Expected calculations:
    if po.shipping_method == ShippingMethods.FREIGHT_ON_BOARD -> carrierName.po_shipment.carrier_name else ""
    when po.shipping_method == ShippingMethods.FREIGHT_ON_BOARD -> we can pass the fobPickUpDate to the form, otherwise
    it returns error message (that`s why when ShippingMethods = VENDOR_DELIVERED fobPickUpDate set up as None)
    """
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, shipping_method=shipping_method)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    sku_name = po.first_line().sku.sku_name
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_po_shipment(po_shipment)
        .with_users(Users.test_user.value)
    ):
        weekend_checklist_items = [
            {
                "rowId": 0,
                "poNumber": po.po_number,
                "skuName": sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
                "toCheck": weekend_checklist_coverage.to_check,
                "contactNameVendorCarrier": weekend_checklist_coverage.contact_name_vendor_carrier,
                "emailPhone": weekend_checklist_coverage.email_phone,
                "backUpVendor": weekend_checklist_coverage.back_up_vendor,
                "fobPickUpDate": (
                    weekend_checklist_coverage.fob_pick_up_date
                    if shipping_method in [ShippingMethods.FREIGHT_ON_BOARD, ShippingMethods.CROSSDOCK]
                    else None
                ),
            }
        ]
        try:
            post_response = WeekendCoverageChecklistClient().add_weekend_checklist_data(
                week=CURRENT_WEEK, weekend_checklist_items=weekend_checklist_items
            )
            assert post_response.status_code == HTTPStatus.OK, post_response.text

            response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
            actual_data = base_steps.process_response_and_extract_data(
                response=response, additional_keys=["data"], element_index=0
            )
            assert actual_data["brand"] == po.brand
            assert actual_data["site"] == site_config.site.code
            assert actual_data["poNumber"] == po.po_number
            assert actual_data["skuCode"] == po.first_line().sku.sku_code
            assert actual_data["skuName"] == sku_name
            assert actual_data["poLandingDay"] == weekend_checklist_coverage.po_landing_day
            assert actual_data["productionDayAffected"] == weekend_checklist_coverage.production_day_affected
            assert actual_data["toCheck"] == weekend_checklist_coverage.to_check
            assert actual_data["contactNameVendorCarrier"] == weekend_checklist_coverage.contact_name_vendor_carrier
            assert actual_data["emailPhone"] == weekend_checklist_coverage.email_phone
            assert actual_data["backUpVendor"] == weekend_checklist_coverage.back_up_vendor
            assert actual_data["updatedBy"] == weekend_checklist_coverage.updated_by.email
            assert actual_data["poStatus"] == PoStatuses.IS_SENT_PENDING_ACCEPTANCE
            assert actual_data["shipMethod"] == po.shipping_method
            if po.shipping_method == ShippingMethods.FREIGHT_ON_BOARD:
                assert actual_data["carrierName"] == po_shipment.carrier_name
                assert actual_data["fobPickUpDate"] == weekend_checklist_coverage.fob_pick_up_date
            elif po.shipping_method == ShippingMethods.CROSSDOCK:
                assert actual_data["carrierName"] == ""
                assert actual_data["fobPickUpDate"] == weekend_checklist_coverage.fob_pick_up_date
            else:
                assert actual_data["carrierName"] == ""
                assert actual_data["fobPickUpDate"] is None
        finally:
            actual_data_id = actual_data["id"]
            WeekendCoverageChecklistClient().delete_weekend_checklist_data(
                weekend_checklist_id=actual_data_id, week=CURRENT_WEEK
            )


def test_editing_recorded_data():
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    user = Users.test_user.value
    po_landing_day = DayOfWeek.random_value(exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY])
    production_day_affected = DayOfWeek.random_value(exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY])
    comment = to_check = back_up_vendor = generate_string(10)
    contact_name_vendor_carrier = user.full_name
    email_phone = user.email
    status = ChecklistStatus.random_value()
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(user)
    ):
        checklist_items = [
            {
                "rowId": 0,
                "poNumber": po.po_number,
                "skuName": po.first_line().sku.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
                "toCheck": weekend_checklist_coverage.to_check,
                "contactNameVendorCarrier": weekend_checklist_coverage.contact_name_vendor_carrier,
                "emailPhone": weekend_checklist_coverage.email_phone,
                "backUpVendor": weekend_checklist_coverage.back_up_vendor,
            }
        ]

        checklist_items_patch = {
            "status": status,
            "comment": comment,
            "poLandingDay": po_landing_day,
            "productionDayAffected": production_day_affected,
            "toCheck": to_check,
            "contactNameVendorCarrier": contact_name_vendor_carrier,
            "emailPhone": email_phone,
            "backUpVendor": back_up_vendor,
        }
        try:
            client = WeekendCoverageChecklistClient()
            client.add_weekend_checklist_data(week=CURRENT_WEEK, weekend_checklist_items=checklist_items)
            response = client.get_weekend_checklist_data(week=CURRENT_WEEK)
            response_data = base_steps.process_response_and_extract_data(
                response=response, additional_keys=["data"], element_index=0
            )
            actual_data_id = response_data["id"]
            for item_name, item_value in checklist_items_patch.items():
                client.edit_weekend_checklist_data(
                    week=CURRENT_WEEK,
                    weekend_checklist_items={item_name: item_value},
                    weekend_checklist_id=actual_data_id,
                )
            response = client.get_weekend_checklist_data(week=CURRENT_WEEK)
            actual_data = base_steps.process_response_and_extract_data(
                response=response, additional_keys=["data"], element_index=0
            )
            assert actual_data["poLandingDay"] == po_landing_day
            assert actual_data["productionDayAffected"] == production_day_affected
            assert actual_data["toCheck"] == to_check
            assert actual_data["contactNameVendorCarrier"] == contact_name_vendor_carrier
            assert actual_data["emailPhone"] == email_phone
            assert actual_data["backUpVendor"] == back_up_vendor
            assert actual_data["status"] == status
            assert actual_data["comment"] == comment
        finally:
            actual_data_id = actual_data["id"]
            WeekendCoverageChecklistClient().delete_weekend_checklist_data(
                weekend_checklist_id=actual_data_id, week=CURRENT_WEEK
            )


def test_error_messages_for_mandatory_fields():
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
    ):
        weekend_checklist_items = [
            {"rowId": 0, "poNumber": "", "skuName": "", "poLandingDay": "", "productionDayAffected": ""}
        ]
        post_validate_response = WeekendCoverageChecklistClient().validate_weekend_checklist_data(
            week=CURRENT_WEEK, weekend_checklist_items=weekend_checklist_items
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=post_validate_response, status_code=HTTPStatus.BAD_REQUEST, element_index=0
        )["cols"]

        for actual_data_key in actual_data.keys():
            assert actual_data[actual_data_key] == MANDATORY_ERROR_MESSAGE


def test_invalid_messages():
    """
    Test invalid messages on WCC dashboard.

    In the first case we set up the sku_1 and po_1 , but not load po_1 through TestData, the message
    "PO Number is not found in selected week" should appear.
    In the second case we set up sku_1 and po_2, the message "SKU Name is not found in this PO" should appear, as
    for creating po_2 use sku_2.
    In the third case we set up sku_3 and po_3 and pass in the po_3.shipping_method = "Vendor Delivered", the message
    "'FOB Pick Up Date' can't be set for non 'Freight on Board' or 'Crossdock' shipping order" should appear,
     as only for "Freight On Board" shipping method allow us to set up date for fobPickUpDate.
    """
    site_config = SiteConfigs.random_value()
    sku_1, sku_2, sku_3 = TestSku.generate_skus_with_specific_sku_names(
        sku_names=SkuNames.random_non_repetitive_values(qty=3)
    )
    po_sku_1, po_sku_2, po_sku_3 = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku,
            site=site_config,
            shipping_method=ShippingMethods.VENDOR_DELIVERED if sku == sku_3 else None,
        )
        for sku in [sku_1, sku_2, sku_3]
    ]
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po_sku_1, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(sku_1, sku_2, sku_3)
        .with_purchase_orders(po_sku_2, po_sku_3)
        .with_users(Users.test_user.value)
    ):
        weekend_checklist_items = [
            {
                "rowId": 0,
                "poNumber": po_sku_1.po_number,
                "skuName": sku_1.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
            },
            {
                "rowId": 0,
                "poNumber": po_sku_2.po_number,
                "skuName": sku_1.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
            },
            {
                "rowId": 0,
                "poNumber": po_sku_3.po_number,
                "skuName": sku_3.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
                "fobPickUpDate": weekend_checklist_coverage.fob_pick_up_date,
            },
        ]
        post_validate_response = WeekendCoverageChecklistClient().validate_weekend_checklist_data(
            week=CURRENT_WEEK, weekend_checklist_items=weekend_checklist_items
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=post_validate_response, status_code=HTTPStatus.BAD_REQUEST, expected_length=3
        )

        actual_data_po = actual_data[0]["cols"]["poNumber"]
        assert actual_data_po == PO_NUMBER_NOT_FOUNT_IN_SELECTED_WEEK_ERROR_MESSAGE

        actual_data_sku_name = actual_data[1]["cols"]["skuName"]
        assert actual_data_sku_name == SKU_NAME_IS_NOT_FOUNT_IN_PO_ERROR_MESSAGE

        actual_data_fob_pick_up_date = actual_data[2]["cols"]["fobPickUpDate"]
        assert actual_data_fob_pick_up_date == FOB_PICK_UP_DATE_CANT_BE_SET


def test_autocomplete_pos_endpoint():
    site_config = SiteConfigs.random_value()
    pos = TestPurchaseOrder.generate_pos(quantity=3, site=site_config)
    skus = [po.first_line().sku for po in pos]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_users(Users.test_user.value)
    ):
        response = WeekendCoverageChecklistClient().get_weekend_checklist_pos_data(week=CURRENT_WEEK)
        actual_data = [
            po["poNumber"] for po in base_steps.process_response_and_extract_data(response=response, expected_length=3)
        ]
        list_po_numbers = [pos[0].po_number, pos[1].po_number, pos[2].po_number]
        assert sorted(actual_data) == sorted(list_po_numbers)


def test_autocomplete_sku_name_by_po_number_endpoint():
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
    ):
        response = WeekendCoverageChecklistClient().get_weekend_checklist_sku_names_by_pos(
            week=CURRENT_WEEK, po_number=po.po_number
        )
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        assert actual_data == po.first_line().sku.sku_name


def test_preview_by_po_number_and_sku_code_endpoint():
    """Items added via Weekend Checklist are visible in PO Status view.
    Preview endpoint by po_number and sku_code return the data from weekend coverage checklist form."""
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
    ):
        try:
            weekend_checklist_items = [
                {
                    "rowId": 0,
                    "poNumber": po.po_number,
                    "skuName": sku.sku_name,
                    "poLandingDay": weekend_checklist_coverage.po_landing_day,
                    "productionDayAffected": weekend_checklist_coverage.production_day_affected,
                    "toCheck": weekend_checklist_coverage.to_check,
                    "contactNameVendorCarrier": weekend_checklist_coverage.contact_name_vendor_carrier,
                    "emailPhone": weekend_checklist_coverage.email_phone,
                    "backUpVendor": weekend_checklist_coverage.back_up_vendor,
                }
            ]
            WeekendCoverageChecklistClient().add_weekend_checklist_data(
                week=CURRENT_WEEK, weekend_checklist_items=weekend_checklist_items
            )
            response = WeekendCoverageChecklistClient().get_weekend_checklist_preview_by_po_sku(
                po_number=po.po_number, sku_code=sku.sku_code
            )
            actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

            assert actual_data["brand"] == po.brand
            assert actual_data["site"] == site_config.site.code
            assert actual_data["poNumber"] == po.po_number
            assert actual_data["skuCode"] == sku.sku_code
            assert actual_data["skuName"] == sku.sku_name
            assert actual_data["poLandingDay"] == weekend_checklist_coverage.po_landing_day
            assert actual_data["productionDayAffected"] == weekend_checklist_coverage.production_day_affected
            assert actual_data["toCheck"] == weekend_checklist_coverage.to_check
            assert actual_data["contactNameVendorCarrier"] == weekend_checklist_coverage.contact_name_vendor_carrier
            assert actual_data["emailPhone"] == weekend_checklist_coverage.email_phone
            assert actual_data["backUpVendor"] == weekend_checklist_coverage.back_up_vendor
            assert actual_data["updatedBy"] == weekend_checklist_coverage.updated_by.email
            assert actual_data["poStatus"] == PoStatuses.IS_SENT_PENDING_ACCEPTANCE

        finally:
            actual_data_id = actual_data["id"]
        WeekendCoverageChecklistClient().delete_weekend_checklist_data(
            weekend_checklist_id=actual_data_id, week=CURRENT_WEEK
        )


def test_delete_recorded_data():
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
    ):
        weekend_checklist_items = [
            {
                "rowId": 0,
                "poNumber": po.po_number,
                "skuName": po.first_line().sku.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
            }
        ]
        post_response = WeekendCoverageChecklistClient().add_weekend_checklist_data(
            week=CURRENT_WEEK, weekend_checklist_items=weekend_checklist_items
        )
        assert post_response.status_code == HTTPStatus.OK, post_response.text

        response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        actual_data_id = base_steps.process_response_and_extract_data(
            response=response, additional_keys=["data"], element_index=0
        )["id"]

        delete_response = WeekendCoverageChecklistClient().delete_weekend_checklist_data(
            weekend_checklist_id=actual_data_id, week=CURRENT_WEEK
        )
        assert delete_response.status_code == HTTPStatus.OK, response.text
        actual_data = delete_response.json()["data"]
        assert actual_data is None


@pytest.mark.parametrize(
    "shipping_method", [ShippingMethods.FREIGHT_ON_BOARD, ShippingMethods.CROSSDOCK, ShippingMethods.VENDOR_DELIVERED]
)
def test_fob_pick_up_date_field(shipping_method):
    """
    Test inserting date to the FOB pick up date field in WCC form

    Test steps[1] (FREIGHT_ON_BOARD, CROSSDOCK):
    1.Generate the test data (site config, po, wcc with no fob_pick_up_date)
    2.Make get API call -> fobPickUpDate is none
    3.Make post API call with new fobPickUpDate -> 200
    4.Make get API call -> fobPickUpDate equals to set fobPickUpDate

    Test steps[2] (VENDOR_DELIVERED):
    1.Generate the test data (site config, po, wcc with no fob_pick_up_date)
    2.Make get API call -> fobPickUpDate is none
    3.Make post API call with new fobPickUpDate -> 400
    4.Assert error message with FOB_DATE_CANT_BE_SAVED_FOR_NON_FREIGHT_ON_BOARD_ORDERS_ERROR_MSG
    5.Assert fobPickUpDate is still none
    """
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, shipping_method=shipping_method)
    wcc = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config, fob_pick_up_date=None)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
        .with_weekend_coverage_checklist(wcc)
    ):
        response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=["data"], element_index=0
        )
        assert response_data["fobPickUpDate"] is None
        response = WeekendCoverageChecklistClient().set_weekend_checklist_fob_pick_up_date(
            id_=response_data["id"], week=CURRENT_WEEK, fob_pick_up_date=CURRENT_DATE
        )
        get_response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        response_data = base_steps.process_response_and_extract_data(
            response=get_response, additional_keys=["data"], element_index=0
        )
        if shipping_method in [ShippingMethods.CROSSDOCK, ShippingMethods.FREIGHT_ON_BOARD]:
            assert response.status_code == HTTPStatus.OK
            assert response_data["fobPickUpDate"] == CURRENT_DATE.strftime(DATE_FORMAT_2)
        else:
            assert response.status_code == HTTPStatus.BAD_REQUEST
            assert response_data["fobPickUpDate"] is None
            assert response.json()["error"] == FOB_DATE_CANT_BE_SAVED_FOR_NON_FREIGHT_ON_BOARD_ORDERS_ERROR_MSG


def test_wcc_form_restricted_week_modification_without_imt_modify_old_form_v1_permission():
    """
    Test that users without permission IMT_MODIFY_OLD_FORM_V1 aren’t allowed to modify weeks,
    older than the previous one

    Here we use procurement_user because this user doesn't have IMT_MODIFY_OLD_FORM_V1 permission

    Test steps:
    1.Generate the test data (site config (site_config.week = PREVIOUS_WEEK - 1), po, po shipment,
    wcc but don't add to the db)
    2.Make post API call with week=PREVIOUS_WEEK - 1 -> 400,
    3.Assert that ONLY_LAST_PREVIOUS_WEEK_IS_AVAILABLE_FOR_EDIT error message returned
    4.Make post API call with week=PREVIOUS_WEEK -> 200
    """
    site_config = SiteConfigs.random_value()
    site_config.week = WEEK_BEFORE_PREVIOUS
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config,
        shipping_method=ShippingMethods.FREIGHT_ON_BOARD,
        week=PREVIOUS_WEEK,
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    sku = po.first_line().sku
    user = Users.procurement_user.value
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_po_shipment(po_shipment)
        .with_users(user, Users.test_user.value)
    ):
        weekend_checklist_items = [
            {
                "rowId": 0,
                "poNumber": po.po_number,
                "skuName": sku.sku_name,
                "poLandingDay": weekend_checklist_coverage.po_landing_day,
                "productionDayAffected": weekend_checklist_coverage.production_day_affected,
            }
        ]
        try:
            response = WeekendCoverageChecklistClient(user=user).add_weekend_checklist_data(
                week=WEEK_BEFORE_PREVIOUS, weekend_checklist_items=weekend_checklist_items
            )
            assert response.status_code == HTTPStatus.BAD_REQUEST
            assert response.json()["error"] == ONLY_LAST_PREVIOUS_WEEK_IS_AVAILABLE_FOR_EDIT
            response = WeekendCoverageChecklistClient(user=user).add_weekend_checklist_data(
                week=PREVIOUS_WEEK, weekend_checklist_items=weekend_checklist_items
            )
            assert response.status_code == HTTPStatus.OK
        finally:
            actual_data_id = base_steps.process_response_and_extract_data(response=response, element_index=0)["id"]
            WeekendCoverageChecklistClient().delete_weekend_checklist_data(
                weekend_checklist_id=actual_data_id, week=PREVIOUS_WEEK
            )


def test_cv_org_mapping_for_wcc_preview():
    """
    Test include CV-ORG mappings for weekend WCC.

    Test steps:
    1.Generate required data (organic sku, one po and wcc with organic_sku.original_sku_code and second po and wcc
    with organic_sku.interchangable_sku_code)
    2.Make API call on weekend_checklist_preview endpoint -> 200
    3.Check that response return correct combination of brand.code, site.code and sku.code (for cv and org skus) with
    corresponding pos
    """
    site_config = SiteConfigsGC.random_value()
    organic_sku = TestOrganicSku.generate_organic_sku()
    po_cv = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=organic_sku.original_sku)
    po_org = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=organic_sku.interchangable_sku)
    wcc_cv = TestWcc.generate_weekend_coverage_checklist(po=po_cv, site_config=site_config)
    wcc_org = TestWcc.generate_weekend_coverage_checklist(po=po_org, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(organic_sku.interchangable_sku, organic_sku.original_sku)
        .with_organic_skus(organic_sku)
        .with_purchase_orders(po_org, po_cv)
        .with_weekend_coverage_checklist(wcc_cv, wcc_org)
    ):
        response = WeekendCoverageChecklistClient().get_weekend_coverage_preview(week=CURRENT_WEEK)
        response_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)
        for sku in [organic_sku.original_sku, organic_sku.interchangable_sku]:
            assert (
                response_data[f"{site_config.brand.code}-{site_config.site.code}-{sku.sku_code}"][po_org.po_number] == 1
            )
            assert (
                response_data[f"{site_config.brand.code}-{site_config.site.code}-{sku.sku_code}"][po_cv.po_number] == 1
            )


def test_added_po_popup_on_wcc():
    """
    Test case checks PO popup on WCC.
    Test steps:
    1. Generate required test data with site_config, sku, po, wcc, hj_receipt, po_shipment
    2. Make an API call to WCC PO Status endpoint -> 200, extract data
    3. Check values from the API response are as expected.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    wcc = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_weekend_coverage_checklist(wcc)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
        .with_oscar_forecast(oscar)
    ):
        response = WeekendCoverageChecklistClient().get_po_status_on_wcc(po=po, sku=sku)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        assert actual_data["supplier"] == po.supplier_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["poStatus"] == PoStatuses.RECEIVED_UNDER
        assert actual_data["receiveVariance"] == actual_data["quantityOrdered"] - actual_data["quantityReceived"]
        assert actual_data["appointmentTime"] == po_shipment.appointment_time.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["orderSize"] == po.first_line().order_size
        assert actual_data["casePrice"] == float(po.first_line().case_price)
        assert actual_data["caseSize"] == po.first_line().case_size
        assert actual_data["caseSizeReceived"] == round(
            actual_data["quantityReceived"] / actual_data["casesReceived"], 3
        )
        assert actual_data["quantityOrdered"] == po.first_line().qty
        assert actual_data["quantityReceived"] == hj_receipt.quantity_received
        assert actual_data["casesReceived"] == hj_receipt.cases_received
        assert actual_data["dateReceived"] == hj_receipt.receipt_time_est.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["totalPrice"] == float(po.first_line().total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            hj_receipt.quantity_received * (po.first_line().case_price / po.first_line().case_size),
            2,
        )
        assert actual_data["emergencyReason"] == po.emergency_reason
        assert actual_data["shipMethod"] == po.shipping_method
        assert actual_data["forecastDeliveryPercent"] == round(
            (max(actual_data["quantityOrdered"], actual_data["quantityReceived"]) / int(oscar.value)), 4
        )
