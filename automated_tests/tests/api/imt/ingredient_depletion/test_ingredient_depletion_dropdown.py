import random
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from automated_tests.data.constants.base_constants import (
    AZ_BOB_CODE,
    EmergencyReason,
    PoStatuses,
    ShippingMethods,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_4,
    TOMORROW_DATETIME,
)
from automated_tests.data.models.bulk_sku import TestBulkSkus
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.utils import comparisons
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.constants.ordering import CANCELLED, CLOSED


def test_ingredient_depletion_dropdown_high_jump():
    site_config = SiteConfigs.random_value()
    site_config.is_hj_autostore = True
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_received_under = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        delivery_time_start=CURRENT_DATETIME + timedelta(days=10),
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
        order_size=random.randint(100, 1000),
        case_size=Decimal(random.randint(100, 1000)),
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        status=CLOSED,
        receipt_time_est=CURRENT_DATETIME,
        po=po_received_under,
        quantity_received=random.randint(1, 100),
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po_received_under)

    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po_received_under)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
    ):
        res = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=res, additional_keys=[site_config.site.code], element_index=0
        )

        # PO status should be Received-Under because receiving type is HJ and Quantity Received < Quantity Ordered
        assert actual_data["poStatus"] == PoStatuses.RECEIVED_UNDER
        assert actual_data["receiveVariance"] == actual_data["quantityOrdered"] - actual_data["quantityReceived"]
        assert actual_data["quantityReceived"] == hj_receipt.quantity_received
        assert actual_data["casesReceived"] == hj_receipt.cases_received
        assert actual_data["dateReceived"] == hj_receipt.receipt_time_est.strftime(DATE_TIME_FORMAT_1)

        assert actual_data["appointmentTime"] == po_shipment.appointment_time.isoformat()
        assert actual_data["caseSize"] == po_received_under.first_line().case_size
        assert actual_data["emergencyReason"] == po_received_under.emergency_reason
        assert actual_data["orderSize"] == po_received_under.first_line().order_size
        assert actual_data["poNumber"] == po_received_under.po_number
        assert actual_data["quantityOrdered"] == po_received_under.first_line().qty
        assert actual_data["shipMethod"] == po_received_under.shipping_method
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["supplier"] == po_received_under.supplier_name
        assert actual_data["totalPrice"] == float(po_received_under.first_line().total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            Decimal(str(actual_data["quantityReceived"]))
            * (po_received_under.first_line().case_price / po_received_under.first_line().case_size),
            2,
        )
        assert actual_data["casePrice"] == float(po_received_under.first_line().case_price)
        assert actual_data["scheduledDeliveryDate"] == po_received_under.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["caseSizeReceived"] == round(
            actual_data["quantityReceived"] / actual_data["casesReceived"], 3
        )


def test_ingredient_depletion_dropdown_manual():
    site_config = SiteConfigs.random_value()
    site_config.is_hj_autostore = True
    site_config.receiving_type = ReceiveInputType.MANUAL
    sku = TestSku.generate_sku()
    po_is_sent = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        delivery_time_start=CURRENT_DATETIME + timedelta(days=10),
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po_is_sent)

    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po_is_sent)
        .with_po_shipment(po_shipment)
    ):
        res = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=res, additional_keys=[site_config.site.code], element_index=0
        )

        # PO status should be Is Sent - Pending Acceptance because PO is not voided, is not received,
        # ASN record is not available, PO Acknowledgment is not available
        assert actual_data["poStatus"] == PoStatuses.IS_SENT_PENDING_ACCEPTANCE
        assert actual_data["receiveVariance"] == 0
        assert actual_data["quantityReceived"] == 0
        assert actual_data["casesReceived"] == 0
        assert actual_data["dateReceived"] is None


def test_depletion_po_with_bulk_skus():
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    # TODO: this is wrong bulk SKU setup and the test should be rewritten (GD-5956)
    bulk_skus = TestBulkSkus.generate_bulk_skus(packaged_sku=sku, bulk_sku=sku, brands=[site_config.brand])
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
        .with_bulk_skus(bulk_skus)
    ):
        response = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["supplier"] == po.supplier_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["sku"] == bulk_skus.packaged_sku_code
        assert actual_data["skuName"] == po.first_line().sku.sku_name
        assert actual_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["receiveVariance"] == actual_data["quantityOrdered"] - actual_data["quantityReceived"]
        assert actual_data["appointmentTime"] == po_shipment.appointment_time.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["orderSize"] == po.first_line().order_size
        assert actual_data["casePrice"] == float(po.first_line().case_price)
        assert actual_data["caseSize"] == po.first_line().case_size
        assert actual_data["caseSizeReceived"] == round(
            actual_data["quantityReceived"] / actual_data["casesReceived"], 3
        )
        assert actual_data["quantityOrdered"] == po.first_line().qty
        assert actual_data["quantityReceived"] == hj_receipt.quantity_received
        assert actual_data["casesReceived"] == hj_receipt.cases_received
        assert actual_data["dateReceived"] == hj_receipt.receipt_time_est.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["totalPrice"] == float(po.first_line().total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            hj_receipt.quantity_received * (po.first_line().case_price / po.first_line().case_size),
            2,
        )
        assert actual_data["emergencyReason"] == po.emergency_reason
        assert actual_data["shipMethod"] == po.shipping_method


def test_scheduled_delivery_date_as_a_fallback_of_date_received():
    """
    Test case checks scheduled delivery date is used as a fallback of data received on Ingredient Depletion
    Test steps:
    1. Generate required test data with site_config, sku, mealkit, ingredient, mealkit ingredient, receipt override, 2
    pos [one with canceled hj_receipt status, the other with receipt_override record]
    2. Make an API request to Ingredient depletion po dropdown -> 200
    3. Check Date Received column values for both records are equal to po's delivery time (scheduled delivery date)
    Expected calculation:
    If there is receipt_override record ->
    Date Received column value is taken from receipt_override receiving_date field
     (which is considered the receiving date for overridden POs (for all received and unreceived/cancelled POs))
    If not receipt_override receiving_date field ->
    Date Received column value is taken from po's delivery time
     (which is considered as scheduled delivery date)
    If not receipt_override record ->
    Date Received column value is taken from hj receipt receipt_time_est
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_1, po_2, po_cancelled = TestPurchaseOrder.generate_pos(quantity=3, site=site_config, sku=sku)
    pos = [po_1, po_2, po_cancelled]
    hj_receipt = TestHJReceipts.generate_hj_receipt(
        po=po_cancelled, site_config=site_config, status=CANCELLED, receipt_time_est=TOMORROW_DATETIME
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_1)
    receipt_override_with_no_receiving_date = TestReceiptOverride.generate_receipt_override(
        site_config=site_config, po=po_2, receiving_date=""
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_receipt_override(receipt_override, receipt_override_with_no_receiving_date)
        .with_hj_receipts(hj_receipt)
    ):
        response = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=3
        )
        actual_pos_data = {po["poNumber"]: po for po in actual_data}
        assert actual_pos_data[po_1.po_number]["dateReceived"] == receipt_override.receiving_date.strftime(
            DATE_TIME_FORMAT_4
        )
        assert actual_pos_data[po_2.po_number]["dateReceived"] == po_2.delivery_time_start.date().strftime(
            DATE_TIME_FORMAT_4
        )
        assert actual_pos_data[po_cancelled.po_number]["dateReceived"] == hj_receipt.receipt_time_est.strftime(
            DATE_TIME_FORMAT_1
        )


def test_ingredient_depletion_transfer_order_dropdown():
    site_config, original_site = SiteConfigs.random_non_repetitive_values(2)
    sku = TestSku.generate_sku()

    original_po_1 = TestPurchaseOrder.generate_po_with_sku(site=original_site, sku=sku)
    original_po_2 = TestPurchaseOrder.generate_po_with_sku(site=original_site, sku=sku)
    original_orders = [original_po_1, original_po_2]

    transfer_order = TestPurchaseOrder.generate_transfer_order(original_pos=original_orders, site=site_config)

    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)

    with (
        TestData(None)
        .with_brands(site_config.brand, original_site.brand)
        .with_sites(site_config.site, original_site.site)
        .with_site_configs(site_config, original_site)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*original_orders, transfer_order)
    ):
        res = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=res, additional_keys=[site_config.site.code], expected_length=2
        )
        actual_items = {it["supplier"]: it for it in actual_data}
        expected_to_items = transfer_order.get_transfer_line_items_by_supplier_code()
        assert len(actual_items) == 2
        for po in original_orders:
            expected = expected_to_items[po.supplier.code]
            actual = actual_items[po.supplier.name]
            assert expected.qty == actual["quantityOrdered"]
            assert expected.order_size == actual["orderSize"]
            comparisons.assert_numbers_equal(expected.case_size, actual["caseSize"])
            comparisons.assert_numbers_equal(expected.case_price, actual["casePrice"])


def test_po_dropdown_for_consolidated_sites_on_ingredient_depletion():
    """
    Test case checks PO dropdown on IMT -> Ingredient Depletion page.
    Test steps:
    1. Generate required data (create site configs with 1 site with two different brands and consolidated_site property,
       and all necessary data for 2 site configs)
    2. Make an API call to receive data from the PO dropdown
    3. Check that column values on the PO dropdown meet with the API response data
    """
    site_config_1, site_config_2 = SiteConfigs.random_non_repetitive_values(qty=2)
    site_config_1.consolidated_site = AZ_BOB_CODE
    site_config_2.consolidated_site = AZ_BOB_CODE
    site_configs = [site_config_1, site_config_2]
    sku = TestSku.generate_sku()
    po_hf = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config_1,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_ep = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config_2,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = [po_ep, po_hf]
    with (
        TestData(None)
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(*pos)
    ):
        response = ImtPoStatusClient().get_po_dropdown(
            sku=sku,
            site_config=site_config_1,
            week=CURRENT_WEEK,
        )
        actual_data = {
            data["poNumber"]: data
            for data in base_steps.process_response_and_extract_data(
                response=response, additional_keys=[site_config_1.site.code], expected_length=2
            )
        }
        assert len(actual_data) == len(pos)
        for po in pos:
            actual_po = actual_data[po.po_number]
            assert actual_po["supplier"] == po.supplier_name
            assert actual_po["poNumber"] == po.po_number
            assert actual_po["sku"] == sku.sku_code
            assert actual_po["skuName"] == po.first_line().sku.sku_name
            assert actual_po["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
            assert actual_po["orderSize"] == po.first_line().order_size
            assert actual_po["casePrice"] == float(po.first_line().case_price)
            assert actual_po["caseSize"] == po.first_line().case_size
            assert actual_po["quantityOrdered"] == po.first_line().qty
            assert actual_po["totalPrice"] == float(po.first_line().total_price)
            assert actual_po["emergencyReason"] == po.emergency_reason
            assert actual_po["shipMethod"] == po.shipping_method
