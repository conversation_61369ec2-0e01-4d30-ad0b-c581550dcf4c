import math
import random
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    OZ_COEFFICIENT,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsFactor,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_1,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    REMAKE_TOOL_DAYS,
    REMAKE_TOOL_DAYS_FACTOR,
)
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJDiscard, TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient, TestRemakeTool
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import ingredient_depletion_steps, po_status_steps
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.services.api.imt.remake_tool_client import RemakeToolClient
from automated_tests.utils import comparisons
from procurement.constants.hellofresh_constant import ReceiveInputType, UnitOfMeasure
from procurement.constants.ordering import IN_PROGRESS_HJ
from procurement.data.models.highjump.highjump import DiscardType


def test_ingredient_requirements_section():
    """
    Test columns in Ingredient Requirements section

    Test steps:
    1. Generate required test data
    2. Send POST call to the remake_tool endpoint
    3. Send GET call to the remake_tool endpoint
    4. Verify that data added through post request are equal to the data from the get request
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    picks_2p, picks_4p, picks_6p = [data_generator.random_int() for _ in range(3)]
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
    ):
        remake_items = {"picks_2p": picks_2p, "picks_4p": picks_4p, "picks_6p": picks_6p}
        post_response = RemakeToolClient().add_or_change_remake_tool(
            site_config=site_config,
            week=CURRENT_WEEK,
            day=remake_day,
            remake_tool_items=remake_items,
            mealkit_slot=mealkit.slot,
        )
        base_steps.process_response_and_extract_data(
            response=post_response, additional_keys=[site_config.site.code], expected_length=2
        )

        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        remake_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["remake"][mealkit.slot]
        assert remake_data["picks_2p"] == picks_2p
        assert remake_data["picks_4p"] == picks_4p
        assert remake_data["picks_6p"] == picks_6p


def test_remake_tool_summary_units_needed_and_status_sections():
    """
    Test Ingredient Summary, Units Needed and Status sections.

    Test steps:
    1. Generate required test data (needed data for set all columns)
    2. Send GET call to the remake_tool endpoint
    3. Check if the data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    category = PurchasingCategories.random_value()
    commodity_group = data_generator.generate_string()
    buyer_sku = TestBuyerSku.generate_buyer_sku(sku=sku, site_config=site_config)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_oscar_forecast(oscar)
        .with_purchasing_category(category, sku)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
        .with_buyer_sku(buyer_sku)
        .with_hybrid_needs(hybrid_needs)
    ):
        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]

        ing_depl_response = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        ing_depl_data = base_steps.process_response_and_extract_data(
            response=ing_depl_response, additional_keys=[site_config.site.code], element_index=0
        )
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == category
        assert actual_data["commodityGroup"] == commodity_group
        assert actual_data["impactedRecipes"] == mealkit.slot
        assert actual_data["buyers"] == buyer_sku.user.full_name
        assert actual_data["plan"] == hybrid_needs.quantity
        assert actual_data["forecastOscar"] == oscar.value
        assert actual_data["delta"] == float(round((1 - math.ceil(oscar.value) / hybrid_needs.quantity), 4))
        assert actual_data["supplementNeed"] == ingredient_depletion_steps.get_expected_supplement_need(ing_depl_data)


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_weekly_section(receiving_type):
    """
    Test Weekly section. This test is parameterized as some columns calculates depending on ReceiveInputType

    Test steps:
    1. Generate required test data (needed data for set all columns)
    2. Send GET call to the remake_tool endpoint
    3. Check if the data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receiving_type
    sku = TestSku.generate_sku()
    po_awaiting = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_in_progress_hj = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME + timedelta(days=10)
    )
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME - timedelta(days=10)
    )
    po_autobagger = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, supplier_name="Autobagger - " + site_config.site.code
    )
    po_received = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    pos = [po_awaiting, po_in_progress_hj, po_not_delivered, po_autobagger, po_received]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    remake_tool = TestRemakeTool.generate_remake_tool(site_config=site_config, mealkit=mealkit)
    next_week_oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK)
    inventory_pull_put = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku)
    hj_discard = TestHJDiscard.generate_discard(sku=sku, site_config=site_config, tran_type=DiscardType.DISCARD)
    discard = TestDiscard.generate_discard(site_config=site_config, sku=sku)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_received)
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        sku=sku,
        site_config=site_config,
    )
    hj_receipt_in_progress_hj = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        status=IN_PROGRESS_HJ,
        receipt_time_est=CURRENT_DATETIME - timedelta(days=1),
        po=po_in_progress_hj,
        quantity_received=0,
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_received)
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_oscar_forecast(oscar, next_week_oscar)
        .with_hybrid_needs(hybrid_needs)
        .with_remake_tool(remake_tool)
        .with_hj_discard(hj_discard)
        .with_discard(discard)
        .with_inventory_pull_put(inventory_pull_put)
        .with_receipt_override(receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_hj_receipts(hj_receipt_in_progress_hj, hj_receipt)
    ):
        res = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        weekly = base_steps.process_response_and_extract_data(
            response=res, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]["weekly"]
        if receiving_type == ReceiveInputType.HIGH_JUMP:
            assert weekly["unitsReceived"] == receipt_override.quantity
            assert weekly["inProgressHj"] == po_in_progress_hj.first_line().qty
            assert (
                weekly["totalOnHand"]
                == weekly["unitsReceived"] + weekly["inventory"] - weekly["discards"] - weekly["pulls"]
            )
            assert weekly["awaitingDelivery"] == sum(po.first_line().qty for po in [po_awaiting, po_autobagger])
        else:
            assert weekly["unitsReceived"] == 0
            assert weekly["inProgressHj"] == 0
            assert (
                weekly["totalOnHand"]
                == weekly["unitsReceived"] + weekly["inventory"] - weekly["discards"] - weekly["pulls"]
            )
            assert weekly["awaitingDelivery"] == sum(
                po.first_line().qty for po in [po_awaiting, po_autobagger, po_in_progress_hj, po_received]
            )
        expected_remake_units = (
            (mealkit_ingredient.picks_2p * remake_tool.picks_2p)
            + (mealkit_ingredient.picks_4p * remake_tool.picks_4p)
            + (mealkit_ingredient.picks_6p * remake_tool.picks_6p)
        )
        assert weekly["remakeUnits"] == expected_remake_units
        assert weekly["unitsNeeded"] == oscar.value
        assert weekly["unitsNeededPlusRemake"] == weekly["remakeUnits"] + weekly["unitsNeeded"]
        assert weekly["unitsOrdered"] == sum(po.first_line().qty for po in pos)
        assert weekly["rowNeed"] == hybrid_needs.quantity + weekly["remakeUnits"]
        assert weekly["unitsInHouseMinusRowNeed"] == weekly["unitsInHouseHJ"] - weekly["rowNeed"]
        assert weekly["nextWeekForecast"] == next_week_oscar.value
        assert weekly["unitsToProduceByAutobagger"] == po_autobagger.first_line().qty
        assert weekly["hjMinusRowNeedMinusForecast"] == weekly["unitsInHouseMinusRowNeed"] - weekly["nextWeekForecast"]
        assert weekly["discards"] == discard.quantity
        assert weekly["inventory"] == inventory_pull_put.quantity
        assert weekly["unitsInHouseHJ"] == hj_pallet_snapshot.pallet_quantity
        assert (
            weekly["onHandMinProductionNeeds"] == weekly["totalOnHand"] - weekly["unitsNeeded"] + weekly["remakeUnits"]
        )
        assert weekly["notDelivered"] == po_not_delivered.first_line().qty
        assert (
            weekly["bufferQuantity"]
            == weekly["totalOnHand"]
            - weekly["unitsNeeded"]
            + weekly["inProgressHj"]
            + weekly["awaitingDelivery"]
            + weekly["notDelivered"]
            - weekly["remakeUnits"]
        )
        assert weekly["bufferPercent"] == round(float(weekly["bufferQuantity"]) / weekly["unitsNeededPlusRemake"], 4)


def test_pulls_and_final_eod_inventory_values():
    """
    Test Pulls and Final End Of Week Inventory columns.
    Final EOD Inventory daily_fields_ing_depl["total_on_hand"][-1] -daily_fields_ing_depl["total_production_need"][-1]

    Test steps:
    1. Generate required test data (needed data for set all columns)
    2. Send GET call to the remake_tool endpoint
    3. Send GET call to the ingredient_depletion endpoint
    4. Check if the data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    inventory_pull_put = TestInventoryPullPut.generate_inventory_pull_put(
        site_config=site_config, sku=sku, quantity=-data_generator.random_int()
    )
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_inventory_pull_put(inventory_pull_put)
    ):
        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]
        weekly = actual_data["weekly"]
        assert weekly["pulls"] == abs(inventory_pull_put.quantity), "Pull is record with - sign"

        ing_depl_response = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        ing_depl_data = base_steps.process_response_and_extract_data(
            response=ing_depl_response, additional_keys=[site_config.site.code], element_index=0
        )["daily"]
        last_day = list(ing_depl_data.values())[-1]
        assert actual_data["finalEndOfWeekBuffer"] == last_day["totalOnHand"] - last_day["totalProductionNeed"]


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_po_dropdown_remake_tool(receiving_type):
    """
    Test PO dropdown. This test is parameterized as some columns calculates depending on ReceiveInputType

    Test steps:
    1. Generate required test data (needed data for set all columns)
    2. Send GET call to the remake_tool_po_status endpoint
    3. Check if the data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receiving_type
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    receiving = TestReceiving.generate_receiving(po=po, site_config=site_config)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_po_shipment(po_shipment)
        .with_hj_receipts(hj_receipt)
        .with_receiving(receiving)
        .with_oscar_forecast(oscar)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        first_line = po.first_line()
        assert actual_data["supplier"] == po.supplier_name
        assert actual_data["poNumber"] == po.po_number
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["scheduledDeliveryDate"] == po.delivery_time_start.strftime(DATE_FORMAT_1)
        assert actual_data["receiveVariance"] == abs(actual_data["quantityOrdered"] - actual_data["quantityReceived"])
        assert actual_data["appointmentTime"] == po_shipment.appointment_time.strftime(DATE_TIME_FORMAT_1)
        assert actual_data["orderSize"] == first_line.order_size
        assert actual_data["casePrice"] == float(first_line.case_price)
        assert actual_data["caseSize"] == first_line.case_size
        assert actual_data["quantityReceived"] == po_status_steps.get_quantity_received(
            receiving_type, hj_receipt, receiving
        )
        assert actual_data["casesReceived"] == po_status_steps.get_cases_received(receiving_type, hj_receipt, receiving)
        assert Decimal(str(actual_data["caseSizeReceived"])) == round(
            Decimal(str(actual_data["quantityReceived"])) / actual_data["casesReceived"], 3
        )
        assert actual_data["dateReceived"] == po_status_steps.get_date_received(receiving_type, hj_receipt, receiving)
        assert actual_data["totalPrice"] == float(first_line.total_price)
        assert round(Decimal(str(actual_data["totalPriceReceived"])), 2) == round(
            Decimal(str(actual_data["quantityReceived"] * (actual_data["casePrice"] / actual_data["caseSize"]))),
            2,
        )
        assert actual_data["emergencyReason"] == po.emergency_reason
        assert actual_data["shipMethod"] == po.shipping_method
        comparisons.assert_numbers_equal(
            actual_data["forecastDeliveryPercent"],
            max(actual_data["quantityOrdered"], actual_data["quantityReceived"]) / int(oscar.value),
            delta=0.001,
        )


@pytest.mark.parametrize("units", [UnitOfMeasure.OUNCE.short_name, UnitOfMeasure.UNIT.short_name])
def test_remake_units_column_in_weekly_section_factor(units):
    """
    Test Remake Units column in weekly section for Factor.
    If unit is oz then remake units =  0.0625 * picks_2p * weight_amount, else 1 * picks_2p * weight_amount

    Test steps:
    1. Generate required test data (site config, sku, mealkit, ingredient, oscar, remake tool)
    2. Make API call to remake_tool endpoint
    3. Check that Remake Units column calculates as expected
    """
    site_config = SiteConfigsFactor.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand, week=CURRENT_WEEK_FACTOR)
    ingredient = TestIngredient.generate_ingredients(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit, ingredient, weight_unit=units
    )
    oscar = TestOscar.generate_oscar(week=CURRENT_WEEK_FACTOR, site_config=site_config, sku=sku)
    remake_day = random.choice(REMAKE_TOOL_DAYS_FACTOR)
    remake_tool = TestRemakeTool.generate_remake_tool(
        site_config=site_config, mealkit=mealkit, week=CURRENT_WEEK_FACTOR
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_oscar_forecast(oscar)
        .with_remake_tool(remake_tool)
    ):
        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK_FACTOR, day=remake_day)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )
        weekly_ingredient_depl_data = actual_data["ingredientDepletion"][0]["weekly"]
        if mealkit_ingredient.weight_unit == UnitOfMeasure.OUNCE.short_name:
            assert weekly_ingredient_depl_data["remakeUnits"] == round(
                OZ_COEFFICIENT * remake_tool.picks_2p * mealkit_ingredient.weight_amount
            )
        else:
            assert (
                weekly_ingredient_depl_data["remakeUnits"]
                == 1 * remake_tool.picks_2p * mealkit_ingredient.weight_amount
            )
