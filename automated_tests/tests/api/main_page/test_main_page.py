import math

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import NO_IMPACT
from automated_tests.data.constants.base_constants import PurchasingCategories, SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    NEXT_WEEK,
    PREVIOUS_WEEK,
)
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import ingredient_depletion_steps
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from automated_tests.services.api.main_page.main_page_client import CriticalBuyerItems
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.constants.ordering import IN_PROGRESS_HJ


def test_critical_buyer_items_main_page():
    """
    Test Critical Buyer Items Dashboard on main page. In critical items buyer section should be records with
    status != no impact

    Test steps:
    1. Generate required data (all data needed for setting ingredient depl)
    2. Make an API call to critical_items endpoint -> 200
    3. Check that values from response are equal to expected
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku, sku_no_impact = TestSku.generate_skus(sku_quantity=2)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredients = [TestIngredient(sku=sku_value, brand=site_config.brand) for sku_value in [sku, sku_no_impact]]
    mealkit_ingredients = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for ingredient in ingredients
    ]
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    oscar_cur_week = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    oscar_next_week = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=NEXT_WEEK)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po)
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, status=IN_PROGRESS_HJ, po=po)
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config)
    commodity_group = data_generator.generate_string()
    category = PurchasingCategories.random_value()
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku, sku_no_impact)
        .with_purchase_orders(po)
        .with_ingredients(*ingredients)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(*mealkit_ingredients)
        .with_hybrid_needs(*hybrid_needs)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
        .with_purchasing_category(category, sku)
        .with_receipt_override(receipt_override)
        .with_hj_receipts(hj_receipt)
        .with_oscar_forecast(oscar_cur_week, oscar_next_week)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_buyer_sku(buyer_sku)
    ):
        response = CriticalBuyerItems().get_critical_buyer_items()
        response_data = base_steps.process_response_and_extract_data(response=response)[CURRENT_WEEK_STR]
        assert len(response_data) == 1, "Sku which has status NO Impact should not be shown on Critical Buyer dashboard"
        actual_data = response_data[0]
        weekly_data = actual_data["weekly"]

        ing_depl_response = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        ing_depl_data = base_steps.process_response_and_extract_data(
            response=ing_depl_response, additional_keys=[site_config.site.code], expected_length=2
        )
        expected_sn = next(
            item
            for item in (ingredient_depletion_steps.get_expected_supplement_need(data) for data in ing_depl_data)
            if item != NO_IMPACT
        )
        hybrid_need_quantities = sum(hybrid_need.quantity for hybrid_need in hybrid_needs)
        exp_delta = float(round((1 - math.ceil(oscar_cur_week.value) / hybrid_need_quantities), 4))
        assert actual_data["brand"] == site_config.brand.code
        assert actual_data["site"] == site_config.site.code
        assert actual_data["sku"] == sku.sku_code
        assert actual_data["skuName"] == sku.sku_name
        assert actual_data["category"] == category
        assert actual_data["commodityGroup"] == commodity_group
        assert actual_data["impactedRecipes"] == mealkit.slot
        assert actual_data["plan"] == hybrid_need_quantities
        assert actual_data["forecastOscar"] == oscar_cur_week.value
        assert actual_data["delta"] == exp_delta
        assert actual_data["sn"] == expected_sn
        assert weekly_data["unitsNeeded"] == oscar_cur_week.value
        assert weekly_data["unitsOrdered"] == po.first_line().qty
        assert weekly_data["unitsReceived"] == receipt_override.quantity
        assert weekly_data["unitsInHouseHj"] == hj_pallet_snapshot.pallet_quantity
        assert weekly_data["rowNeed"] == sum(hn.quantity for hn in hybrid_needs if hn.date >= CURRENT_DATE)
        assert weekly_data["unitsInHouseMinusRowNeed"] == weekly_data["unitsInHouseHj"] - weekly_data["rowNeed"]
        assert weekly_data["nextWeekForecast"] == oscar_next_week.value
