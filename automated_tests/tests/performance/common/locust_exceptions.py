from automated_tests.tests.performance.constants.base_constants import RESPONSE_TIME_THRESHOLD


class ExceedResponseTimeLimit(SystemExit):
    "Raised when the request time exceed threshold"

    def __init__(self, request_name, request_time):
        self.request_name = request_name
        self.response_time = round(request_time / 1000, 4)
        response_threshold_sec = round(RESPONSE_TIME_THRESHOLD / 1000, 4)
        message = (
            f"Request {self.request_name} was processed in {self.response_time}."
            f" when threshold is {response_threshold_sec}."
        )
        super().__init__(message)
