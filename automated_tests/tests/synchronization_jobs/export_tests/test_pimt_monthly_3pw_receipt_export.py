import random
from datetime import datetime

from automated_tests.data.constants.admin_constants import Synchronization<PERSON>ob<PERSON>, SyncJob<PERSON>tatus
from automated_tests.data.constants.base_constants import (
    EmergencyReason,
    PoStatuses,
    PurchasingCategories,
    ShippingMethods,
    Users,
    Warehouses,
)
from automated_tests.data.constants.column_headers.gsheet_headers.pimt_monthly_3pw_headers import (
    PIMT_MONTHLY_3PW_EXPORT_EXPECTED_HEADERS,
)
from automated_tests.data.constants.date_time_constants import (
    DATE_FORMAT_4,
    DATE_TIME_FORMAT_2,
    FIRST_DAY_OF_CURRENT_MONTH,
    FIRST_DAY_OF_MONTH_BEFORE_LAST,
    FIRST_DAY_OF_PREVIOUS_MONTH,
)
from automated_tests.data.constants.file_export_constants import PIMT_MONTHLY_3PW_EXPORT_FILE_NAME
from automated_tests.data.data_generator import generate_random_datetime_in_range, random_int
from automated_tests.data.models.buyer_sku import Test<PERSON>uy<PERSON><PERSON>ku
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_void import TestPoVoid
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.jobs_client import JobsClient
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.services.files import csv_file_parser
from automated_tests.tests.synchronization_jobs.data.gsheet_models.pimt_monthly_3pw_export_model import (
    PimtMonthly3pwExport,
)
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import WhReceivingType
from procurement.core.dates import ScmWeek


def test_monthly_3pw_export_values():
    """
    Test PIMT monthly 3pw export values.

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Start the job and wait for it to complete
    4.Read and parse the file that was exported by job
    5.Check if the following columns are present: 3PW, Supplier, PO #, SKU, SKU Name, Category, Scheduled Delivery Date,
    PO Status, Receiving Variance (units), Order Size, Case Price, Case Size, Quantity Received, Cases Received,
    Date Received, Total Price, Emergency Reason, Buyer, Ship Method
    6.Assert the expected values
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    date = generate_random_datetime_in_range(
        min_datetime=FIRST_DAY_OF_PREVIOUS_MONTH, max_datetime=FIRST_DAY_OF_CURRENT_MONTH
    )
    week = ScmWeek.from_date(date)
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
        emergency_reason=EmergencyReason.random_value(),
        shipping_method=ShippingMethods.random_value(),
        delivery_time_start=date,
        week=week,
    )
    purchasing_category = PurchasingCategories.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    grn = TestGrn.generate_grn(
        po=inbound_po,
        warehouse=warehouse,
        receipt_time_est=date,
        units_received=inbound_po.first_line().qty - random_int(),
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(inbound_po)
        .with_grn(grn)
        .with_buyer_sku(buyer_sku)
        .with_warehouses(warehouse)
        .with_purchasing_category(purchasing_category, sku)
        .with_skus(sku)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        status, warning_msg = JobsClient().get_job_status_by_key(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        assert status == SyncJobStatus.SUCCESS, f"The job is not successful: {warning_msg}"
        headers, content = csv_file_parser.parse_csv_file_from_export_location(
            file_name=PIMT_MONTHLY_3PW_EXPORT_FILE_NAME.format(
                date_from=FIRST_DAY_OF_PREVIOUS_MONTH.strftime(DATE_FORMAT_4),
                date_to=FIRST_DAY_OF_CURRENT_MONTH.strftime(DATE_FORMAT_4),
            ),
            data_model=PimtMonthly3pwExport,
        )

        assert headers == PIMT_MONTHLY_3PW_EXPORT_EXPECTED_HEADERS

        first_row_content = content[0]
        first_line = inbound_po.first_line()
        assert first_row_content.buyer == buyer_sku.user.full_name
        assert first_row_content.case_price == first_line.case_price
        assert first_row_content.case_size == first_line.case_size
        expected_cases_received = round(grn.units_received / first_line.case_size)
        assert first_row_content.cases_received == expected_cases_received
        assert first_row_content.category == purchasing_category
        assert first_row_content.date_received == str(grn.receipt_time_est)
        assert first_row_content.emergency_reason == inbound_po.emergency_reason
        assert first_row_content.order_size == first_line.order_size
        assert first_row_content.warehouse_3pw == warehouse.code
        assert first_row_content.po_number == inbound_po.po_number
        assert first_row_content.po_status == PoStatuses.RECEIVED_UNDER
        assert first_row_content.quantity_received == grn.units_received
        expected_receiving_variance = first_line.qty - grn.units_received
        assert first_row_content.receiving_variance_in_units == expected_receiving_variance
        assert first_row_content.scheduled_delivery_date == inbound_po.delivery_time_start.strftime(DATE_TIME_FORMAT_2)
        assert first_row_content.ship_method == inbound_po.shipping_method
        assert first_row_content.sku == sku.sku_code
        assert first_row_content.sku_name == sku.sku_name
        assert first_row_content.supplier == inbound_po.supplier_name
        expected_total_price = expected_cases_received * first_line.case_price
        assert first_row_content.total_price == expected_total_price


def test_po_include_exclude_monthly_3pw_export():
    """
    Test that PIMT monthly 3pw export includes records where the delivery dates are in the range of the first-day
    previous month and the first day of the current month.

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Start the job and wait for it to complete
    4.Read and parse the file that was exported by job
    5.Assert that file includes records only with a delivery date in a range of the first-day previous month
    and the first day of the current month
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    inbound_po_include = [
        TestPurchaseOrder.generate_inbound_po_with_sku(
            sku=sku,
            warehouse=warehouse,
            delivery_time_start=generate_random_datetime_in_range(
                min_datetime=FIRST_DAY_OF_PREVIOUS_MONTH, max_datetime=FIRST_DAY_OF_CURRENT_MONTH
            ),
        )
        for _ in range(random.randint(1, 20))
    ]
    inbound_po_exclude = [
        TestPurchaseOrder.generate_inbound_po_with_sku(
            sku=sku,
            warehouse=warehouse,
            delivery_time_start=generate_random_datetime_in_range(
                min_datetime=FIRST_DAY_OF_MONTH_BEFORE_LAST, max_datetime=FIRST_DAY_OF_PREVIOUS_MONTH
            ),
        )
        for _ in range(random.randint(1, 20))
    ]
    with (
        TestData.root_data()
        .with_purchase_orders(*inbound_po_include, *inbound_po_exclude)
        .with_warehouses(warehouse)
        .with_skus(sku)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        status, warning_msg = JobsClient().get_job_status_by_key(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        assert status == SyncJobStatus.SUCCESS, f"The job is not successful: {warning_msg}"
        headers, content = csv_file_parser.parse_csv_file_from_export_location(
            file_name=PIMT_MONTHLY_3PW_EXPORT_FILE_NAME.format(
                date_from=FIRST_DAY_OF_PREVIOUS_MONTH.strftime(DATE_FORMAT_4),
                date_to=FIRST_DAY_OF_CURRENT_MONTH.strftime(DATE_FORMAT_4),
            ),
            data_model=PimtMonthly3pwExport,
        )
        assert len(content) == len(inbound_po_include)
        assert all(
            FIRST_DAY_OF_PREVIOUS_MONTH
            <= datetime.strptime(row.scheduled_delivery_date, DATE_TIME_FORMAT_2)
            <= FIRST_DAY_OF_CURRENT_MONTH
            for row in content
        )


def test_monthly_3pw_export_not_include_po_void():
    """
    Test that PIMT monthly 3pw export not include records where po status = "Po Void".

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Start the job and wait for it to complete
    4.Read and parse the file that was exported by job
    5.Assert that the record with po void not created
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    date = generate_random_datetime_in_range(
        min_datetime=FIRST_DAY_OF_PREVIOUS_MONTH, max_datetime=FIRST_DAY_OF_CURRENT_MONTH
    )
    week = ScmWeek.from_date(date)
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku, warehouse=warehouse, delivery_time_start=date, week=week
    )
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    po_void = TestPoVoid.generate_po_void(po=inbound_po, site_config=site_config, week=week)
    with (
        TestData.root_data()
        .with_purchase_orders(inbound_po)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_po_void(po_void)
    ):
        response = ImtPoStatusClient().get_po_status(week, warehouse=warehouse)
        assert response.status_code == 200, response
        response_data = response.json()["data"][warehouse.code][0]
        assert response_data["poStatus"] == PoStatuses.PO_VOID

        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        status, warning_msg = JobsClient().get_job_status_by_key(key=SynchronizationJobs.PIMT_MONTHLY_3PW_REPORT)
        assert status == SyncJobStatus.SUCCESS, f"The job is not successful: {warning_msg}"

        headers, content = csv_file_parser.parse_csv_file_from_export_location(
            file_name=PIMT_MONTHLY_3PW_EXPORT_FILE_NAME.format(
                date_from=FIRST_DAY_OF_PREVIOUS_MONTH.strftime(DATE_FORMAT_4),
                date_to=FIRST_DAY_OF_CURRENT_MONTH.strftime(DATE_FORMAT_4),
            ),
            data_model=PimtMonthly3pwExport,
        )
        assert len(content) == 0, "The length of content should be 0"
