import dataclasses
import random

import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import SynchronizationJobs, SyncJobStatus
from automated_tests.data.constants.base_constants import SiteConfigs, SiteConfigsFactor, SiteConfigsGC, Users
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, NEXT_WEEK, NEXT_WEEK_FACTOR
from automated_tests.data.constants.sync_constants import DocCodes, Projects, SheetNames, SpreadsheetNames
from automated_tests.data.models.gsheet.gsheets import TestGsheet, TestGsheetTab
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.jobs_client import JobsClient
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.production_kit_guide import ProductionKitGuideClient
from procurement.client.googlesheets import DataRange
from procurement.constants.hellofresh_constant import UnitOfMeasure


@pytest.mark.parametrize(
    "is_3pl, week",
    [
        pytest.param(True, NEXT_WEEK, id="is_3pl=True, Next Week"),
        pytest.param(False, NEXT_WEEK, id="is_3pl=False, Next Week"),
        pytest.param(True, CURRENT_WEEK + 5, id="is_3pl=True, Current Week+5"),
        pytest.param(False, CURRENT_WEEK + 5, id="is_3pl=False, Current Week+5"),
    ],
)
def test_future_sku_and_recipes_import(is_3pl, week):
    """
    Test Future Sku and Recipes import for Future PKG Sync. If site is 3pl the data imports from "All 3PLs Offering",
    else from" ALL DCs Offering". As the Future PKG Sync exist from week + 1 till week + 5 in the test parametrized
    boundary values.

    Test steps:
    1.Generate required test data
    2.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
    for import, so they just mark as "")
    3.Start the job and wait for it to complete (expecting success for steps that are testing)
    4.Make API call
    5.Check that table fields data meets appropriate API response values
    """
    site_config = dataclasses.replace(SiteConfigs.NJ_HF.value, is_3pl=is_3pl)
    sku = TestSku.generate_sku()
    dc, brand = site_config.site.code, site_config.brand

    mealkit_name = data_generator.generate_string(5)
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    mealkit_code, mealkit_slot, picks_2p, picks_4p, weight_amount, unit_price = [
        data_generator.random_int() for _ in range(6)
    ]
    dcs_offering_base_rows = [
        mealkit_code,
        str(mealkit_slot),
        "",
        mealkit_name,
        *([""] * 2),
        picks_2p,
        picks_4p,
        *([""] * 6),
    ]
    all_dcs_offering_sheet = TestGsheetTab(
        sheet_name=SheetNames.ALL_DCS_3PLS_OFFERING if is_3pl is True else SheetNames.All_DCS_OFFERING,
        # the first row has all valid values, the further rows have empty values in order: sku_code, site
        data=[
            [dc, *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
            [dc, *dcs_offering_base_rows, *([""] * 4), str(weight_amount), weight_unit, unit_price],
            ["", *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
        ],
        data_range=DataRange(first_row=8, first_column="B"),
    )
    pkg_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.PRODUCTION_KIT_GUIDE.format(brand="", week=week),
        doc_code=DocCodes.PKG,
        week=week,
        tabs=[all_dcs_offering_sheet],
        project=Projects.IMT,
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_test_gsheet(pkg_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.FUTURE_PKG_SYNC, arguments={"week": str(week)})
        status, warnings = JobsClient().get_job_status_by_key(key=SynchronizationJobs.FUTURE_PKG_SYNC)
        assert status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"

        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=week)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )
        assert actual_data["mealNumber"] == str(mealkit_slot)
        assert actual_data["code"] == str(mealkit_code)
        assert actual_data["mealName"] == mealkit_name
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount
        assert actual_data["picks2p"] == picks_2p
        assert actual_data["picks4p"] == picks_4p


def test_future_sku_and_recipes_import_for_ep():
    """
    Test picks_6p column that imports from Future PKG Sync for Every Plate brand.
    The data imports from "All DCs Offering" - Production Kit Guide doc

    Test steps:
    1.Generate required test data (site_config, sku, and generate random mealkit_name, mealkit_code, mealkit_slot,
    picks_2p, picks_4p, picks_6p - the data that needed for loading in sheet)
    2.Create and load gsheet
    3.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
    for import, so they just mark as "")
    4.Start the job and wait for it to complete (expecting success for steps that are testing)
    5.Make API call to PRODUCTION_KIT_GUIDE endpoint -> 200
    6.Check that picks_6p column meets appropriate API response value
    """
    site_config = SiteConfigs.GA_EP.value
    sku = TestSku.generate_sku()
    dc, brand = site_config.site.code, site_config.brand

    mealkit_name = data_generator.generate_string(5)
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    mealkit_code, mealkit_slot, picks_2p, picks_4p, picks_6p, weight_amount, unit_price = [
        data_generator.random_int() for _ in range(7)
    ]
    dcs_offering_base_rows = [
        mealkit_code,
        str(mealkit_slot),
        "",
        mealkit_name,
        *([""] * 2),
        picks_2p,
        picks_4p,
        picks_6p,
        *([""] * 6),
    ]
    all_dcs_offering_sheet = TestGsheetTab(
        sheet_name=SheetNames.All_DCS_OFFERING,
        # the first row has all valid values, the next row has empty sku_code
        data=[
            [dc, *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
            [dc, *dcs_offering_base_rows, *([""] * 4), str(weight_amount), weight_unit, unit_price],
            ["", *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
        ],
        data_range=DataRange(first_row=8, first_column="B"),
    )
    pkg_v2_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.PRODUCTION_KIT_GUIDE.format(brand=brand.name, week=NEXT_WEEK),
        week=NEXT_WEEK,
        doc_code=DocCodes.PKG_V2,
        project=Projects.IMT,
        tabs=[all_dcs_offering_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_test_gsheet(pkg_v2_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(
            key=SynchronizationJobs.FUTURE_PKG_SYNC, arguments={"week": str(NEXT_WEEK)}
        )
        status, warnings = JobsClient().get_job_status_by_key(key=SynchronizationJobs.FUTURE_PKG_SYNC)
        assert status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"

        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=NEXT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )
        assert actual_data["mealNumber"] == str(mealkit_slot)
        assert actual_data["code"] == str(mealkit_code)
        assert actual_data["mealName"] == mealkit_name
        assert actual_data["picks2p"] == picks_2p
        assert actual_data["picks4p"] == picks_4p
        assert actual_data["picks6p"] == picks_6p
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount


@pytest.mark.parametrize("week", [NEXT_WEEK, CURRENT_WEEK + 5])
def test_future_sku_and_recipes_gc_import(week):
    """
    Test Sku and Recipes import IMT Sync for GC. If brand = GC then data imports from "All DCs Offering IMT View".
    As the Future PKG Sync exist from week + 1 till week + 5 in the test parametrized boundary values.

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Create and load gsheet
    4.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
    for import, so they just mark as "")
    5.Start the job and wait for it to complete (expecting success for steps that are testing)
    6.Make API call
    7.Check that table fields data meets appropriate API response values
    """
    site_config = SiteConfigsGC.random_value()
    sku = TestSku.generate_sku()
    brand, dc = site_config.brand.code, site_config.site.code

    mealkit_name = data_generator.generate_string(5)
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    mealkit_code, mealkit_slot, picks_2p, picks_4p, weight_amount, unit_price = [
        data_generator.random_int() for _ in range(6)
    ]
    dcs_offering_base_rows = [
        mealkit_code,
        str(mealkit_slot),
        "",
        mealkit_name,
        *([""] * 2),
        picks_2p,
        picks_4p,
        *([""] * 6),
    ]
    al_dcs_offering_imt_view = TestGsheetTab(
        sheet_name=SheetNames.ALL_DCS_OFFERING_IMT_VIEW,
        # the first row has all valid values, the further rows have empty values in order: sku_code, site
        data=[
            [dc, *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
            [dc, *dcs_offering_base_rows, *([""] * 4), str(weight_amount), weight_unit, unit_price],
            ["", *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
        ],
        data_range=DataRange(first_row=8, first_column="B"),
    )
    pkg_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.PRODUCTION_KIT_GUIDE.format(brand=brand, week=week),
        week=week,
        doc_code=DocCodes.PKG,
        project=Projects.IMT,
        tabs=[al_dcs_offering_imt_view],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_test_gsheet(pkg_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.FUTURE_PKG_SYNC, arguments={"week": str(week)})
        status, warnings = JobsClient().get_job_status_by_key(key=SynchronizationJobs.FUTURE_PKG_SYNC)
        assert status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"

        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=week)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )
        assert actual_data["mealNumber"] == str(mealkit_slot)
        assert actual_data["code"] == str(mealkit_code)
        assert actual_data["mealName"] == mealkit_name
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount
        assert actual_data["picks2p"] == picks_2p
        assert actual_data["picks4p"] == picks_4p


@pytest.mark.skip(reason="The test is skipped due to kafka_factor_pkg_consumption_enabled killswitch logic")
def test_future_pkg_for_factor():
    """
    Test case checks future PKG sync for Factor. If brand = FJ then data imports from "All DCs Offering".
    Test steps:
    1.Generate required test data(site_config, sku, mealkit name, mealkit code, allergens, subrecipe)
    2.Create and load gsheet
    3.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
      for import, so they just mark as "")
    4.Start the job and wait for it to complete (expecting success for steps that are testing)
    5.Make an API call -> 200
    6.Check that table fields data meet appropriate API response values
    """
    site_config = SiteConfigsFactor.random_value()
    sku = TestSku.generate_sku()
    brand, dc = site_config.brand, site_config.site.code
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    mealkit_name, allergens, recipe_name = [data_generator.generate_string() for _ in range(3)]
    meal_number, weight_amount, recipe_code = [data_generator.random_int() for _ in range(3)]
    dcs_offering_base_rows = [
        *([""] * 10),
        str(weight_amount),
        weight_unit,
        allergens,
        *([""] * 11),
        recipe_name,
        recipe_code,
    ]
    all_dcs_offering_sheet = TestGsheetTab(
        sheet_name=SheetNames.All_DCS_OFFERING,
        # the first row has all valid values, the further rows have empty values in order: sku_code, site
        data=[
            [dc, meal_number, *([""] * 2), mealkit_name, "", sku.sku_code, *dcs_offering_base_rows],
            [dc, meal_number, *([""] * 2), mealkit_name, *([""] * 2), *dcs_offering_base_rows],
            ["", meal_number, *([""] * 2), mealkit_name, "", sku.sku_code, *dcs_offering_base_rows],
        ],
        data_range=DataRange(first_row=2, first_column="B"),
    )
    pkg_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.FACTOR_IMT_REMAKE_TOOL.format(week=NEXT_WEEK_FACTOR),
        week=NEXT_WEEK_FACTOR,
        doc_code=DocCodes.PKG,
        project=Projects.IMT,
        tabs=[all_dcs_offering_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_test_gsheet(pkg_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(
            key=SynchronizationJobs.FUTURE_PKG_SYNC, arguments={"week": str(NEXT_WEEK_FACTOR)}
        )
        status, warnings = JobsClient().get_job_status_by_key(key=SynchronizationJobs.FUTURE_PKG_SYNC)
        assert status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"

        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=NEXT_WEEK_FACTOR)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )
        assert actual_data["code"] == str(recipe_code)
        assert actual_data["mealNumber"] == str(meal_number)
        assert actual_data["mealName"] == str(mealkit_name)
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount
        assert actual_data["allergens"] == allergens
        assert actual_data["subRecipe"] == recipe_name


@pytest.mark.parametrize(
    "is_3pl, week",
    [
        pytest.param(True, NEXT_WEEK, id="is_3pl=True, Next Week"),
        pytest.param(False, NEXT_WEEK, id="is_3pl=False, Next Week"),
        pytest.param(True, CURRENT_WEEK + 5, id="is_3pl=True, Current Week+5"),
        pytest.param(False, CURRENT_WEEK + 5, id="is_3pl=False, Current Week+5"),
    ],
)
def test_new_pkg_import_for_hf(is_3pl, week):
    """
    Test Future Sku and Recipes import for Future PKG Sync. If site is 3pl the data imports from "All 3PLs Offering",
    else from" ALL DCs Offering". As the Future PKG Sync exist from week + 1 till week + 5 in the test parametrized
    boundary values.

    Test steps:
    1.Generate required test data
    2.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
    for import, so they just mark as "")
    3.Start the job and wait for it to complete (expecting success for steps that are testing)
    4.Make an API call
    5.Check that table fields data meets appropriate API response values
    """
    site_config = dataclasses.replace(SiteConfigs.NJ_HF.value, is_3pl=is_3pl)
    sku = TestSku.generate_sku()
    dc = site_config.site.code
    mealkit_name = data_generator.generate_string(5)
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    mealkit_code, mealkit_slot, picks_2p, picks_3p, picks_4p, weight_amount, unit_price = [
        data_generator.random_int() for _ in range(7)
    ]
    dcs_offering_base_rows = [
        mealkit_code,
        str(mealkit_slot),
        "",
        mealkit_name,
        *([""] * 2),
        picks_2p,
        picks_3p,
        picks_4p,
        *([""] * 7),
    ]
    all_dcs_offering_sheet = TestGsheetTab(
        sheet_name=SheetNames.ALL_DCS_3PLS_OFFERING if is_3pl else SheetNames.All_DCS_OFFERING,
        # the first row has all valid values, the next rows has empty values in order sku_code, dc
        data=[
            [dc, *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
            [dc, *dcs_offering_base_rows, *([""] * 4), str(weight_amount), weight_unit, unit_price],
            ["", *dcs_offering_base_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit, unit_price],
        ],
        data_range=DataRange(first_row=8, first_column="B"),
    )
    pkg_v3_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.PRODUCTION_KIT_GUIDE.format(brand="", week=week),
        week=week,
        doc_code=DocCodes.PKG_V3,
        project=Projects.IMT,
        tabs=[all_dcs_offering_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_test_gsheet(pkg_v3_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.FUTURE_PKG_SYNC, arguments={"week": str(week)})
        status, warnings = JobsClient().get_job_status_by_key(key=SynchronizationJobs.FUTURE_PKG_SYNC)
        assert status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"

        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=week)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )

        assert actual_data["mealNumber"] == str(mealkit_slot)
        assert actual_data["code"] == str(mealkit_code)
        assert actual_data["mealName"] == mealkit_name
        assert actual_data["picks2p"] == picks_2p
        assert actual_data["picks3p"] == picks_3p
        assert actual_data["picks4p"] == picks_4p
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount
