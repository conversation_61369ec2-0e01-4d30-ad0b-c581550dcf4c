import dataclasses
from datetime import timedelta

import pytest

from automated_tests.data.constants.admin_constants import SynchronizationJobs
from automated_tests.data.constants.base_constants import SiteConfigsCA, UsersCA
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, DATE_FORMAT_1, PREVIOUS_WEEK
from automated_tests.data.models.canada_forecast import TestCanadaForecast
from automated_tests.data.models.inventory_snapshot import TestInventorySnapshot
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.raw_highjump.hj_pallet_snapshot import TestHjPalletSnapshot
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.jobs_client import JobsClient
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from procurement.constants.hellofresh_constant import MARKET_CA, InventoryInputType, ReceiveInputType


@pytest.mark.parametrize("inventory_type", [InventoryInputType.WMSL, InventoryInputType.HJ])
def test_start_of_day_inventory_hj_wmsl_for_ca_market(inventory_type):
    """
    Test case checks Start of Day Inventory column for CA market

    Test steps:
    1.Generate required test data for Ingredient Depletion dashboard for CA market, parametrize inventory_type to have
    HJ and WMSL values.
    3.Start the job and wait for it to complete (expecting success for steps that are testing)
    4.Make an API call
    5.Check Start of Day Inventory column has its first value on Thursday and if inventory_type is WMSL -> it takes its
    value from inventory_snapshot, else -> from hj_pallet_snapshot
    """
    site_config = SiteConfigsCA.random_value()
    site_config_prev_week = dataclasses.replace(site_config, week=PREVIOUS_WEEK)
    site_config.inventory_type = inventory_type
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku(market=MARKET_CA)
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    canada_forecast = TestCanadaForecast.generate_canada_forecast(sku=sku, site_config=site_config)
    inventory_snapshot = TestInventorySnapshot.generate_inventory_snapshot(wh_code=site_config.bob_code, sku=sku)
    hj_pallet_snapshot = TestHjPalletSnapshot.generate_hj_pallet_snapshot_raw(
        sku=sku, po_number=po.po_number, site_config=site_config
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config, site_config_prev_week)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_canada_forecast(canada_forecast)
        .with_inventory_snapshot(inventory_snapshot)
        .with_hj_pallet_snapshot_raw(hj_pallet_snapshot)
    ):
        JobsClient().run_job_and_wait_until_done(
            key=SynchronizationJobs.SNAPSHOT_START_OF_DAY_INVENTORY,
            arguments={"brands": site_config.brand.code},
            market=MARKET_CA,
        )
        JobsClient().wait_until_job_is_done(
            key=SynchronizationJobs.SNAPSHOT_START_OF_DAY_INVENTORY_RUN,
            arguments={"timezone": site_config.site.timezone, "brands": site_config.brand.code},
            market=MARKET_CA,
        )

        response = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        daily_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["daily"]
        thursday_date = (CURRENT_WEEK.get_first_day() + timedelta(days=1)).strftime(DATE_FORMAT_1)

        if inventory_type == InventoryInputType.WMSL:
            assert daily_data[thursday_date]["inventory"] == inventory_snapshot.unit_quantity
        else:
            assert daily_data[thursday_date]["inventory"] == hj_pallet_snapshot.pallet_quantity
