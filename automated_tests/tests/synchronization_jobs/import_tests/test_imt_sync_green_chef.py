import random
from datetime import timedelta

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import SynchronizationJobs, SyncJobStatus
from automated_tests.data.constants.base_constants import PurchasingCategories, SiteConfigsGC, SkuNames, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_FORMAT_2,
)
from automated_tests.data.constants.sync_constants import (
    IMPORTING_ORGANIC_SKU_MAPPING,
    IMPORTING_PACKAGING_SKU_MAPPING,
    IMPORTING_SKU_AND_RECIPES,
    DocCodes,
    Projects,
    SheetNames,
    SpreadsheetNames,
)
from automated_tests.data.models.bulk_sku import TestBulkSkus
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.gsheet.gsheets import TestGsheet, TestGsheetTab
from automated_tests.data.models.highjump import TestHJPackagingPalletSnapshot, TestHJReceipts
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.packaging_demand import TestPackagingDemand
from automated_tests.data.models.packaging_sku_mapping import TestPackagingSku
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.raw_highjump.hj_pallet_snapshot import TestHjPalletSnapshot
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.jobs_client import JobsClient
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient as IngDeplClient
from automated_tests.services.api.imt.packaging_depletion import PackagingDepletionClient
from automated_tests.services.api.imt.production_kit_guide import ProductionKitGuideClient
from procurement.client.googlesheets import DataRange
from procurement.constants.hellofresh_constant import InventoryInputType, ReceiveInputType, UnitOfMeasure


def test_organic_sku_mapping_import_imt_sync():
    """
    Test organic sku mapping imt sync import.
    Interchangable-Origin mapped SKU value - adds value with the same calculation logic bit for corresponding
    Interchangable or Origin mapped SKU if such exists. E.g. SKU_1 has Origin equivalent of SKU_2 then SKU_1’s Units
    Ordered = SKU_2’s Units Ordered = SKU_1’s Units Ordered + SKU_2’s Units Ordered

    Test steps:
    1.Generate required test data
    2.Append the data to the sheet using id, sheet name and necessary data
    3.Start the job and wait for it to complete (expecting success for steps that are testing)
    4.Make API call
    5.Check that table fields data meets appropriate API response values
    """
    site_config = SiteConfigsGC.random_value()
    sku_name = SkuNames.random_value()
    sku_organic = TestSku.generate_sku(sku_name=f"{sku_name} ORG")
    sku_cv = TestSku.generate_sku(sku_name=f"{sku_name} CV")
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient_cv = TestIngredient(sku=sku_cv, brand=site_config.brand)
    mealkit_ingredient_cv = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient_cv)
    po_organic = TestPurchaseOrder.generate_po_with_sku(sku=sku_organic, site=site_config)
    po_cv = TestPurchaseOrder.generate_po_with_sku(sku=sku_cv, site=site_config)
    hj_pallet_snapshot_organic = TestHjPalletSnapshot.generate_hj_pallet_snapshot_raw(
        sku=sku_organic,
        site_config=site_config,
        po_number=po_organic.po_number,
        expiration_date=CURRENT_DATE + timedelta(days=11),
    )
    hj_pallet_snapshot_cv = TestHjPalletSnapshot.generate_hj_pallet_snapshot_raw(
        sku=sku_cv,
        site_config=site_config,
        po_number=po_cv.po_number,
        expiration_date=CURRENT_DATE + timedelta(days=11),
    )
    hj_receipt_cv = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_cv)
    hj_receipt_organic = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_organic)
    # TODO: this is wrong bulk SKU setup and the test should be rewritten (GD-5956)
    bulk_sku_cv = TestBulkSkus.generate_bulk_skus(packaged_sku=sku_cv, bulk_sku=sku_cv, brands=[site_config.brand])
    oscar_cv = TestOscar.generate_oscar(sku=sku_cv, site_config=site_config, week=CURRENT_WEEK)
    inventory_put_cv = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku_cv)
    inventory_put_organic = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku_organic)
    receipt_override_cv = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_cv)
    receipt_override_organic = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_organic)
    mapping_sheet = TestGsheetTab(
        sheet_name=SheetNames.MAPPING,
        # the first row has all valid values, further rows have empty values in order: original_sku_code,
        # interchangable_sku_code
        data=[
            [sku_organic.sku_code, sku_organic.sku_name, sku_cv.sku_code, sku_cv.sku_name],
            ["", sku_organic.sku_name, sku_cv.sku_code, sku_cv.sku_name],
            [sku_organic.sku_code, sku_organic.sku_name, "", sku_cv.sku_name],
        ],
    )
    gc_produce_org_cv_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.GC_PRODUCE_ORG_CV,
        doc_code=DocCodes.ALTERNATIVE_SKU,
        week=CURRENT_WEEK,
        project=Projects.IMT,
        tabs=[mapping_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po_organic, po_cv)
        .with_ingredients(ingredient_cv)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient_cv)
        .with_hj_pallet_snapshot_raw(hj_pallet_snapshot_cv, hj_pallet_snapshot_organic)
        .with_bulk_skus(bulk_sku_cv)
        .with_skus(sku_organic, sku_cv)
        .with_oscar_forecast(oscar_cv)
        .with_inventory_pull_put(inventory_put_cv, inventory_put_organic)
        .with_receipt_override(receipt_override_cv, receipt_override_organic)
        .with_hj_receipts(hj_receipt_cv, hj_receipt_organic)
        .with_test_gsheet(gc_produce_org_cv_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.IMT_SYNC, arguments={"week": CURRENT_WEEK_STR})
        step_status, warnings = JobsClient().get_step_status_for_imt_sync(
            step_name=IMPORTING_ORGANIC_SKU_MAPPING.format(brand=site_config.brand.code),
            params={"week": CURRENT_WEEK_STR},
        )
        assert step_status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"
        res = IngDeplClient().get_ingredient_depletion(site_config=site_config, week=CURRENT_WEEK)
        processed_response = base_steps.process_response_and_extract_data(
            response=res, additional_keys=[site_config.site.code], element_index=0
        )
        weekly_data = processed_response["weekly"]
        bulk_values_data = processed_response["bulkValues"]
        sum_pallet_quantity = hj_pallet_snapshot_organic.pallet_quantity + hj_pallet_snapshot_cv.pallet_quantity
        assert bulk_values_data["bulkUnitsInHj"] == sum_pallet_quantity
        assert weekly_data["unitsInHouseHj"] == sum_pallet_quantity
        assert weekly_data["inventory"] == inventory_put_cv.quantity + inventory_put_organic.quantity
        assert weekly_data["unitsOrdered"] == po_cv.first_line().qty + po_organic.first_line().qty
        assert weekly_data["unitsReceived"] == receipt_override_organic.quantity + receipt_override_cv.quantity


def test_packaging_sku_mapping():
    """
    Test packaging sku mapping import for GC.
    Interchangable-Origin mapped SKU value - adds value with the same calculation logic bit for corresponding
    Interchangable or Origin mapped SKU if such exists. E.g. SKU_1 has Origin equivalent of SKU_2 then SKU_1’s Units
    Ordered = SKU_2’s Units Ordered = SKU_1’s Units Ordered + SKU_2’s Units Ordered

    Test steps:
    1.Generate required test data
    2.Create and load gsheet, append the data to the sheet using id, sheet name and necessary data
    3.Start the job and wait for it to complete (expecting success for steps that are testing)
    4.Make API call to packaging_depletion endpoint -> 200
    5.Check that table fields data meets appropriate API response values
    """
    site_config = SiteConfigsGC.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config.inventory_type = InventoryInputType.HJ
    packaging_sku = TestPackagingSku.generate_packaging_sku_mapping()
    skus = [packaging_sku.original_sku, packaging_sku.interchangable_sku]
    pos = [
        TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku, delivery_time_start=CURRENT_DATE)
        for sku in skus
    ]
    packaging_demand_origin = TestPackagingDemand.generate_packaging_demand(
        sku=packaging_sku.original_sku, site_config=site_config
    )
    packaging_snapshots = [
        TestHJPackagingPalletSnapshot.generate_packaging_snapshot(site_config=site_config, sku=sku) for sku in skus
    ]

    packaging_mapping_sheet = TestGsheetTab(
        sheet_name=SheetNames.PACKAGING_MAPPING,
        # the first row has all valid values, further rows have empty values in order: original_sku_code,
        # interchangable_sku_code
        data=[
            [
                packaging_sku.original_sku.sku_code,
                packaging_sku.original_sku.sku_name,
                packaging_sku.interchangable_sku.sku_code,
                packaging_sku.interchangable_sku.sku_name,
            ],
            [
                "",
                packaging_sku.original_sku.sku_name,
                packaging_sku.interchangable_sku.sku_code,
                packaging_sku.interchangable_sku.sku_name,
            ],
            [
                packaging_sku.original_sku.sku_code,
                packaging_sku.original_sku.sku_name,
                "",
                packaging_sku.interchangable_sku.sku_name,
            ],
        ],
    )
    alternative_sku_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.GC_PRODUCE_ORG_CV,
        doc_code=DocCodes.ALTERNATIVE_SKU,
        week=CURRENT_WEEK,
        project=Projects.IMT,
        tabs=[packaging_mapping_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(packaging_demand_origin)
        .with_hj_packaging_palette_snapshot(*packaging_snapshots)
        .with_test_gsheet(alternative_sku_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.IMT_SYNC, arguments={"week": CURRENT_WEEK_STR})
        step_status, warnings = JobsClient().get_step_status_for_imt_sync(
            step_name=IMPORTING_PACKAGING_SKU_MAPPING.format(brand=site_config.brand.code),
            params={"week": CURRENT_WEEK_STR},
        )
        assert step_status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"
        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        daily_need_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["dailyNeeds"]

        current_day_data = {day["date"]: day for day in daily_need_data}[CURRENT_DATETIME.strftime(DATE_FORMAT_2)]
        assert current_day_data["incoming"] == sum(value.first_line().qty for value in pos)
        assert current_day_data["onHand"] == sum(value.pallet_quantity for value in packaging_snapshots)


def test_sku_and_recipes_gc_import():
    """
    Test Sku and Recipes import IMT Sync for GC.

    Test steps:
    1.Generate required test data
    2.Prepare the test environment
    3.Create and load gsheet
    4.Append the data to the sheet using id, sheet name and necessary data (not all fields take part and are necessary
    for import, so they just mark as "")
    5.Start the job and wait for it to complete (expecting success for steps that are testing)
    6.Make API call
    7.Check that table fields data meets appropriate API response values
    """
    site_config = SiteConfigsGC.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    brand = site_config.brand.code

    dc = site_config.site.code
    mealkit_name = data_generator.generate_string(5)
    weight_unit = str(random.choice(list(UnitOfMeasure)))
    unit_price, mealkit_code, mealkit_slot, picks_2p, picks_4p, weight_amount = [
        data_generator.random_int() for _ in range(6)
    ]
    dcs_offering_base_sheet_rows = [
        mealkit_code,
        str(mealkit_slot),
        "",
        mealkit_name,
        *([""] * 2),
        picks_2p,
        picks_4p,
        *([""] * 6),
    ]
    all_dcs_offering_sheet = TestGsheetTab(
        sheet_name=SheetNames.ALL_DCS_OFFERING_IMT_VIEW,
        # the first row has all valid values, further rows have empty values in order: sku_code, site
        data=[
            [dc, *dcs_offering_base_sheet_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit],
            [dc, *dcs_offering_base_sheet_rows, *([""] * 4), str(weight_amount), weight_unit],
            ["", *dcs_offering_base_sheet_rows, sku.sku_code, *([""] * 3), str(weight_amount), weight_unit],
        ],
        data_range=DataRange(first_row=8, first_column="B"),
    )
    pkg_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.PRODUCTION_KIT_GUIDE.format(brand=brand, week=CURRENT_WEEK),
        doc_code=DocCodes.PKG,
        week=CURRENT_WEEK,
        project=Projects.IMT,
        tabs=[all_dcs_offering_sheet],
        brand=site_config.brand.code,
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po)
        .with_purchasing_category(PurchasingCategories.random_value(), sku)
        .with_skus(sku)
        .with_test_gsheet(pkg_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.IMT_SYNC, arguments={"week": CURRENT_WEEK_STR})
        step_status, warnings = JobsClient().get_step_status_for_imt_sync(
            step_name=IMPORTING_SKU_AND_RECIPES.format(brand=brand),
            params={"week": CURRENT_WEEK_STR},
        )
        assert step_status == SyncJobStatus.SUCCESS, f"The Job failed with: {warnings}"
        response = ProductionKitGuideClient().get_production_kit_guide(site_config=site_config, week=CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[dc], element_index=0
        )
        assert actual_data["mealNumber"] == str(mealkit_slot)
        assert actual_data["code"] == str(mealkit_code)
        assert actual_data["mealName"] == mealkit_name
        assert actual_data["picks2p"] == picks_2p
        assert actual_data["picks4p"] == picks_4p
        assert actual_data["weightUnit"] == weight_unit
        assert actual_data["weightAmount"] == weight_amount
