import pytest

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import SynchronizationJobs, SyncJobStatus
from automated_tests.data.constants.base_constants import UsersCA, WarehousesCA
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, DATE_FORMAT_1, DATE_FORMAT_2, TOMORROW_DATE
from automated_tests.data.constants.sync_constants import DocCodes, Projects, SheetNames, SpreadsheetNames
from automated_tests.data.models.gsheet.gsheets import TestGsheet, TestGsheetTab
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.services.api.admin.synchronization_jobs.jobs_client import JobsClient
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.expiring_inventory_client import ExpiringInventoryClient
from automated_tests.services.api.pimt.pimt_ops_main_client import PimtOpsMainClient
from procurement.client.googlesheets import DataRange
from procurement.constants.hellofresh_constant import MARKET_CA


@pytest.mark.parametrize("units", ["", data_generator.random_int()])
def test_pimt_sync_inventory_canada(units):
    """
    Test import from "CAN PIMT - ALL 3PWs - Grid Dynamics Data Feed" doc "partner_name_FeedData" tab to inventory table
    If units in sheet is None, then unit_quantity in unified_inventory = cases * case size, else units

    Test steps:
    1.Generate required test data (sku, warehouse, po, inventory data)
    2.Start the sync job and wait for it to complete
    3.Make an API call to the expiring inventory report endpoint and inventory dropdown endpoint -> 200
    4.Check that table fields data meet appropriate API response values
    """
    sku = TestSku.generate_sku(market=MARKET_CA)
    warehouse = WarehousesCA.random_value()
    po = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)

    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po,
        expiration_date=TOMORROW_DATE,
        warehouse=warehouse,
        case_size=data_generator.random_int(),
    )
    feed_data_sheet = TestGsheetTab(
        sheet_name=SheetNames.FEED_DATA.format(warehouse_code=warehouse.code),
        # the first row has all valid values, further rows have empty values in order: sku_code, lot
        data=[
            [
                inventory.sku.sku_code,
                inventory.po.supplier_name,
                inventory.case_quantity,
                inventory.case_size,
                units,
                inventory.lot,
                inventory.expiration_date.strftime(DATE_FORMAT_1),
            ],
            [
                "",
                inventory.po.supplier_name,
                inventory.case_quantity,
                inventory.case_size,
                units,
                inventory.lot,
                inventory.expiration_date.strftime(DATE_FORMAT_1),
            ],
            [
                inventory.sku.sku_code,
                inventory.po.supplier_name,
                inventory.case_quantity,
                inventory.case_size,
                units,
                "",
                inventory.expiration_date.strftime(DATE_FORMAT_1),
            ],
        ],
    )
    last_update_sheet = TestGsheetTab(
        sheet_name=SheetNames.LAST_UPDATE,
        data=[["Last Update", inventory.snapshot_timestamp.strftime(DATE_FORMAT_1)]],
        data_range=DataRange(first_row=1),
    )
    pimt_all_3pw_spreadsheet = TestGsheet(
        doc_title=SpreadsheetNames.CA_PIMT_ALL_3PWS,
        doc_code=DocCodes.PIMT,
        project=Projects.PIMT,
        tabs=[feed_data_sheet, last_update_sheet],
        brand=None,
        market=MARKET_CA,
    )
    with (
        TestData(None)
        .with_users(UsersCA.test_user.value)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_test_gsheet(pimt_all_3pw_spreadsheet)
    ):
        JobsClient().run_job_and_wait_until_done(key=SynchronizationJobs.PIMT_SYNC, market=MARKET_CA)
        status, warning_msg = JobsClient().get_job_status_by_key(key=SynchronizationJobs.PIMT_SYNC, market=MARKET_CA)
        assert status == SyncJobStatus.SUCCESS, f"The job is not successful: {warning_msg}"

        response = ExpiringInventoryClient().get_expiring_inventory_report(market=MARKET_CA)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        assert actual_data["supplierName"] == inventory.po.supplier_name
        assert actual_data["unitsPerCaseCount"] == inventory.case_size
        assert actual_data["casesCount"] == inventory.case_quantity
        assert actual_data["total"] == units if units else inventory.case_quantity * inventory.case_size
        assert actual_data["lot"] == inventory.lot
        assert actual_data["expirationDate"] == inventory.expiration_date.strftime(DATE_FORMAT_2)

        response_pimt_inventory = PimtOpsMainClient().get_pimt_ops_main_buying_tool(
            sku_code=sku.sku_code, market=MARKET_CA
        )
        actual_pimt_inventory_data = base_steps.process_response_and_extract_data(
            response=response_pimt_inventory, element_index=0
        )
        assert actual_pimt_inventory_data["site"] == warehouse.code
        assert actual_pimt_inventory_data["supplierName"] == inventory.po.supplier_name
        assert actual_pimt_inventory_data["poNumber"] is None, "PO number should be blank"
        assert actual_pimt_inventory_data["lot"] == inventory.lot
        assert actual_pimt_inventory_data["totalCasesCount"] == inventory.case_quantity
        assert actual_pimt_inventory_data["inventoryStatus"] == inventory.inventory_status
        assert (
            actual_pimt_inventory_data["totalUnitCount"] == units
            if units
            else inventory.case_quantity * inventory.case_size
        )
        assert actual_pimt_inventory_data["casePrice"] == 0, "Case Price should be blank"
        assert actual_pimt_inventory_data["unitsPerCaseCount"] == inventory.case_size
        assert actual_pimt_inventory_data["expirationDate"] == inventory.expiration_date.strftime(DATE_FORMAT_2)
        assert actual_pimt_inventory_data["daysUntilCount"] == (inventory.expiration_date - CURRENT_DATE).days
