import dataclasses
from decimal import Decimal

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class PimtMonthly3pwExport(BaseTableRow):
    warehouse_3pw: str
    supplier: str
    po_number: str
    sku: str
    sku_name: str
    category: str
    scheduled_delivery_date: str
    po_status: str
    receiving_variance_in_units: int
    order_size: int
    case_price: Decimal
    case_size: Decimal
    quantity_received: int
    cases_received: int
    date_received: str
    total_price: Decimal
    emergency_reason: str
    buyer: str
    ship_method: str
