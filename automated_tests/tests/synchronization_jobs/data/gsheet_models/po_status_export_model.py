import dataclasses
from decimal import Decimal

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class FinancialPoExportModel(BaseTableRow):
    brand: str
    site: str
    supplier: str
    po_number: str
    sku_code: str
    sku_name: str
    category: str
    purchasing_unit: str
    scheduled_delivery_date: str
    po_status: str
    receive_variance: int
    order_size: int
    order_unit: str
    case_price: Decimal
    case_size: int
    quantity_ordered: int
    quantity_received: int
    cases_received: int
    date_received: str
    total_price: Decimal
    total_price_received: Decimal
    emergency_reason: str
    buyers: str
    po_buyer: str
    ship_method: str


@dataclasses.dataclass
class FinancialPoExportModelWarehouses(BaseTableRow):
    warehouse_3pw: str
    supplier: str
    po_number: str
    sku_code: str
    sku_name: str
    category: str
    purchasing_unit: str
    scheduled_delivery_date: str
    po_status: str
    receive_variance: int
    order_size: int
    case_price: Decimal
    case_size: int
    case_size_received: str
    quantity_ordered: int
    quantity_received: int
    cases_received: int
    date_received: str
    total_price: Decimal
    total_price_received: Decimal
    emergency_reason: str
    buyer: str
    po_buyer: str
    ship_method: str
