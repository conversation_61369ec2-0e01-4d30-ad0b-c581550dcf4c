import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class PimtExportOpsMainDashboard(BaseTableRow):
    site_3pw: str
    sku: str
    sku_name: str
    category: str
    commodity_group: str
    inhouse: str
    inbound: str
    outbound: str


@dataclasses.dataclass
class PimtExportStrategyMainDashboard(BaseTableRow):
    site: str
    sku_name: str
    sku_code: str
    category: str
    supplier: str
    supplier_code: str
    received_date: str
    lot_code: str
    po_number: str
    cases: str
    total_units: str
    units_per_case: str
    case_price: str
    unit_price: str
    expiration_date: str
    days_until_expiration: str
    source_of_expiration_date: str


@dataclasses.dataclass
class PimtExportCSACData(BaseTableRow):
    sku_code: str
    sku_name: str
    site: str
    average_cost: str
    cold_storage_facility: str


@dataclasses.dataclass
class PimtDailyReplenishmentExportData(BaseTableRow):
    region: str
    sku: str
    sku_name: str
    replenishment_type: str
    buyer: str
    replenishment_buyer: str
    category: str
    sub_category: str
    brand: str
    max_po_lead_time: int
    row_needs: int
    forecast_current_week_plus_0: int
    forecast_current_week_plus_1: int
    forecast_current_week_plus_2: int
    forecast_current_week_plus_3: int
    forecast_current_week_plus_4: int
    forecast_current_week_plus_5: int
    forecast_current_week_plus_6: int
    forecast_current_week_plus_7: int
    forecast_current_week_plus_8: int
    forecast_current_week_plus_9: int
    total_forecasts: int
    average_weekly_forecasts: int
    total_dc_inventory: int
    total_3pl_inventory: int
    total_3pw_inventory: int
    total_inventory_wos: float
    expire_soon_inventory: int
    expired_inventory: int
    inbound_current_week_plus_0: int
    inbound_current_week_plus_1: int
    inbound_current_week_plus_2: int
    inbound_current_week_plus_3: int
    inbound_current_week_plus_4: int
    inbound_current_week_plus_5: int
    inbound_current_week_plus_6: int
    inbound_current_week_plus_7: int
    inbound_current_week_plus_8: int
    inbound_current_week_plus_9: int
    total_inbound: int
    total_undelivered: int
    end_of_weeks_current_week_plus_0: int
    end_of_weeks_current_week_plus_1: int
    end_of_weeks_current_week_plus_2: int
    end_of_weeks_current_week_plus_3: int
    end_of_weeks_current_week_plus_4: int
    end_of_weeks_current_week_plus_5: int
    end_of_weeks_current_week_plus_6: int
    end_of_weeks_current_week_plus_7: int
    end_of_weeks_current_week_plus_8: int
    end_of_weeks_current_week_plus_9: int
    end_of_weeks_wos_current_week_plus_0: float
    end_of_weeks_wos_current_week_plus_1: float
    end_of_weeks_wos_current_week_plus_2: float
    end_of_weeks_wos_current_week_plus_3: float
    end_of_weeks_wos_current_week_plus_4: float
    end_of_weeks_wos_current_week_plus_5: float
    end_of_weeks_wos_current_week_plus_6: float
    end_of_weeks_wos_current_week_plus_7: float
    end_of_weeks_wos_current_week_plus_8: float
    end_of_weeks_wos_current_week_plus_9: float
    total_inventory_cost: float
    dc_inventory_cost: float
    tpl_inventory_cost: float
    tpw_inventory_cost: float
    vendor_managed_inventory: float
