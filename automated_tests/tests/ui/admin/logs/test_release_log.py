from playwright.sync_api import Page, expect

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    ActivationState,
    FeatureTypes,
    ReleaseLogTypes,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    DATE_FORMAT_2,
    FIRST_DAY_OF_MONTH_BEFORE_LAST,
)
from automated_tests.data.constants.error_messages_constants import RELEASE_LOG_WITH_THE_DATE_ALREADY_EXISTS
from automated_tests.data.models.release_log import TestReleaseLog
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.admin.logs.release_log_page import ReleaseLogPage


def test_add_and_delete_release_log(page: Page):
    """
    Test adding new Release log.
    Test steps:
    1. Generate required test data with site_config, feature_type, description
    2. Open Admin -> Release log page
    3. Create a new Release log with release date FIRST_DAY_OF_MONTH_BEFORE_LAST for checking that date picker works as
    expected and extract table data
    4. Check all columns meet their appropriate value.
    5. Delete record and check no rows are shown
    """
    site_config = SiteConfigs.random_value()
    feature_type = FeatureTypes.random_value()
    description_data = data_generator.generate_string()
    release_date = FIRST_DAY_OF_MONTH_BEFORE_LAST
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
    ):
        release_log_page = ReleaseLogPage(page=page)
        release_log_page.open()
        release_log_page.add_release_log(feature_type=feature_type, input_data=description_data, date=release_date)
        table_data = release_log_page.get_release_log_table_data()[0]
        release_log_page.check_release_log_column_headers()
        assert table_data.description == description_data
        assert table_data.is_visible_for_dc_user == ActivationState.ON
        assert table_data.type == feature_type
        release_log_page.save_records()

        release_log_actual_items = release_log_page.get_release_log_items()
        assert release_log_actual_items["title"] == feature_type
        assert release_log_actual_items["description"] == description_data
        assert release_log_actual_items["date"] == release_date.strftime(DATE_FORMAT_2)

        release_log_page.delete_records()
        release_log_page.is_no_rows_to_show_message_present()


def test_edit_release_log(page: Page):
    """
    Test editing Release log record.
    Test steps:
    1. Generate required test data with site_config, release_logs, descriptions
    2. Open Admin -> Release log page
    3. Edit one of the existing records and change feature order
    4. Extract table data
    5. Get the first record that previously was the second one and check that this record was updated
    """
    site_configs = SiteConfigs.random_value()
    release_log_fix = TestReleaseLog.generate_release_log(feature_key=1, feature_type=ReleaseLogTypes.FIX)
    release_log_new = TestReleaseLog.generate_release_log(feature_key=2, feature_type=ReleaseLogTypes.NEW)
    description_to_edit = data_generator.generate_string()
    feature_type_to_edit = FeatureTypes.ONGOING
    dc_user_to_edit = False
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_sites(site_configs.site)
        .with_brands(site_configs.brand)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_fix, release_log_new)
    ):
        release_log_page = ReleaseLogPage(page=page)
        release_log_page.open()
        release_log_page.edit_release_log_record(
            feature_type=feature_type_to_edit, input_data=description_to_edit, set_check=dc_user_to_edit
        )
        release_log_page.check_date_field_is_enabled(expected_enabling=False)
        release_log_page.change_feature_order(seq_num_to_drag=1, seq_num_to_drop=2)
        table_data = release_log_page.get_release_log_table_data()[0]
        assert table_data.type == feature_type_to_edit
        assert table_data.description == description_to_edit
        assert table_data.is_visible_for_dc_user == ActivationState.OFF


def test_warning_message_when_creating_log_for_the_same_date(page: Page):
    """
    Test case checks warning message when creating more than 1 release log record for the same date.
    Test steps:
    1. Generate required test data with site_config,release_log, feature_type, descriptions
    2. Open Admin -> Release log page
    3. Create a Release log record twice setting the same date
    4. Check warning message appears when trying to save the second record.
    """
    site_config = SiteConfigs.random_value()
    feature_type = FeatureTypes.random_value()
    release_log_fix = TestReleaseLog.generate_release_log(feature_key=1, feature_type=ReleaseLogTypes.FIX)
    description_data = data_generator.generate_string()
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_release_log(release_log_fix)
    ):
        release_log_page = ReleaseLogPage(page=page)
        release_log_page.open()
        release_log_page.add_release_log(feature_type=feature_type, input_data=description_data)
        release_log_page.save_records()
        expect(
            page.get_by_text(RELEASE_LOG_WITH_THE_DATE_ALREADY_EXISTS.format(date=CURRENT_DATE.strftime(DATE_FORMAT_2)))
        ).to_be_visible()


def test_release_log_for_dc_user(page: Page):
    """
    Test release log for DC User. If flag "dcUsersAvailable" is False then this record should be unavailable for DC User
    Test steps:
    1.Generate required data (site configs, release log with dcUsersAvailable true, and another one with false dc user)
    2.Open Changelog
    3.Check that for DC User response returns only 1
    """
    site_configs = SiteConfigs.random_value()
    release_log_available_dc_user = TestReleaseLog.generate_release_log(feature_key=1, dc_user_available=False)
    release_log_non_available_dc_user = TestReleaseLog.generate_release_log(feature_key=2, dc_user_available=True)
    with (
        TestData(None)
        .with_site_configs(site_configs)
        .with_sites(site_configs.site)
        .with_brands(site_configs.brand)
        .with_users(Users.dc_user.value)
        .with_release_log(release_log_available_dc_user, release_log_non_available_dc_user)
    ):
        release_log_page = ReleaseLogPage(page=page)
        release_log_page.open()
        release_log_page.open_changelog()
        release_log_page.check_changelog_items(expected_len=1), "The length of release log items should be 1"
