import random

from playwright.sync_api import Page, expect

from automated_tests.data.constants.admin_constants import (
    DOCS_INVALID_STATUS,
    DOCS_VALID_STATUS,
    DOES_NOT_EXIST,
    EP_ERROR_MESSAGE,
    EP_LABELS,
    EP_OSCAR,
    GSHEET_CODE_ID_HF_IMT,
    HF_GSHEET_LABELS,
    REVALIDATE_STATUS,
)
from automated_tests.data.constants.base_constants import EP, Brands, Colors, FactorBrand, Sites, Users
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, NEXT_WEEK, PREVIOUS_WEEK, SCM_WEEK_27
from automated_tests.data.models.imt_config import TestGsheetConfig
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.admin.imt.imt_gsheets_page import ImtAdminGsheetPage
from procurement.constants.hellofresh_constant import Domain


def test_add_links_and_filter(page: Page):
    """
    Test adding links and filter them by week, and check brands dropdown

    Test steps:
    1.Generate required data (gsheet configs for HF, brands)
    2.Open Gsheet page in IMT section
    3.Open Brands dropdown
    4.Assert that brands in the dropdown matches added to the db
    5.Choose 27 week from week dropdown
    6.Check that status is: "All docs are valid" and check labels for HF
    7.Choose current week from week dropdown and check links
    8.Check that status is: Revalidate all
    9.Choose EP brand from brand dropdown, validate labels and check that links are not repeated for EP brand
    """
    brands = *Brands.get_all_values(), FactorBrand.FJ.value
    gsheet_configs_imt_hf = TestGsheetConfig.generate_gsheet_configs(
        gsheet_code_id_data=GSHEET_CODE_ID_HF_IMT,
        brand=Brands.HF.value,
        project=Domain.IMT,
        week=SCM_WEEK_27,
    )
    with TestData(None).with_brands(*brands).with_sites(Sites.random_value()).with_users(
        Users.test_user.value
    ).with_gsheets(*gsheet_configs_imt_hf):
        imt_admin_page = ImtAdminGsheetPage(page=page)
        imt_admin_page.open()
        actual_brands = imt_admin_page.get_brands_names_from_dropdown()
        assert sorted(actual_brands) == sorted(brand.name for brand in brands)
        imt_admin_page.choose_week_from_dropdown(week=SCM_WEEK_27)
        imt_admin_page.validate_imt_admin_breadcrumbs_title(week=SCM_WEEK_27)
        imt_admin_page.check_button_with_status_present(expected_status=DOCS_VALID_STATUS)
        imt_admin_page.validate_gsheet_labels(expected_labels=HF_GSHEET_LABELS)
        imt_admin_page.choose_week_from_dropdown(week=CURRENT_WEEK)
        imt_admin_page.check_general_and_weekly_links()
        imt_admin_page.check_button_with_status_present(expected_status=REVALIDATE_STATUS)
        imt_admin_page.choose_item_from_brand_dropdown(item_name=EP)
        imt_admin_page.validate_gsheet_labels(expected_labels=EP_LABELS)
        imt_admin_page.check_links_are_not_repeated_for_ep()


def test_add_link_manually(page: Page):
    """
    Test adding link manually

    Test steps:
    1.Generate required data
    2.Open Imt Gsheet page
    3.Insert link and check that the link is valid
    """
    with TestData.root_data():
        imt_admin_page = ImtAdminGsheetPage(page=page)
        imt_admin_page.open()
        imt_admin_page.insert_link(
            doc_name="Total Procurement Purchasing", link="1RfRuf0h_OZTbZnlPsY80n4YOuEuEVkVS7QZ8l17W74Q"
        )


def test_invalid_link_for_hf_and_ep_brands(page: Page):
    """
    Test adding invalid link for HF and EP brands

    Test steps:
    1.Generate required data (invalid_gsheet_link)
    2.Open Imt Gsheet page
    3.Insert incorrect link for HF and check that 'Document with ID {} not found or does not exist' message is present
    4.Choose EP brand from brand dropdown, insert EP_OSCAR link for Hybrid Needs doc and check that
    "The title does not match the expected value: 'actual title - "EP OSCAR"; expected title - "EP Hybrid Needs - Week
    {0}"' error message is present
    """
    invalid_gsheet_link = "invalid_link"
    with TestData.root_data():
        imt_admin_page = ImtAdminGsheetPage(page=page)
        imt_admin_page.open()
        imt_admin_page.insert_link(doc_name=random.choice(HF_GSHEET_LABELS), link=invalid_gsheet_link)
        expect(page.get_by_text(DOES_NOT_EXIST.format(invalid_gsheet_link))).to_be_visible()
        imt_admin_page.check_button_with_status_present(DOCS_INVALID_STATUS)

        imt_admin_page.choose_item_from_brand_dropdown(EP)
        imt_admin_page.insert_link(doc_name="Hybrid Needs", link=EP_OSCAR)
        expect(page.get_by_text(EP_ERROR_MESSAGE.format(CURRENT_WEEK.week))).to_be_visible()
        imt_admin_page.check_button_with_status_present(DOCS_INVALID_STATUS)


def test_ongoing_week_is_highlighted(page: Page):
    """
    Test ongoing week is highlighted.
    Current week is highlighted in cyan color if it's not picked. In case if any week is picked it should be highlighted
    in azure color.

    Test steps:
    1. Generate the test data
    2. Open AdminIMTPage
    3. Open week dropdown and choose next week
    4. Click on the week dropdown menu and check that current week is highlighted in cyan color
    5. Check that previous week is highlighted in dark gray color and next week in azure color (as next week is picked)
    """
    with TestData.root_data():
        imt_admin_page = ImtAdminGsheetPage(page=page)
        imt_admin_page.open()
        imt_admin_page.choose_week_from_dropdown(week=NEXT_WEEK)
        imt_admin_page.check_week_item_color_dropdown(week=CURRENT_WEEK, expected_color=Colors.CYAN)
        imt_admin_page.check_week_item_color_dropdown(week=PREVIOUS_WEEK, expected_color=Colors.DARK_GREY)
        imt_admin_page.check_week_item_color_dropdown(week=NEXT_WEEK, expected_color=Colors.AZURE)
