import dataclasses
import random

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    <PERSON><PERSON>,
    FactorBrand,
    Markets,
    MultiMarketUsers,
    SiteConfigs,
    SiteConfigsCA,
    Sites,
    Users,
)
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.constants.sync_constants import Projects
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.comment import TestComment
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.packaging_demand import TestPackagingDemand
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_void import TestPoVoid
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.supplier import TestSupplier
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.admin.imt.sites_page import SitesPage
from automated_tests.pages_new.calculations.expected_daily_need_page import ExpectedDailyNeedPage
from automated_tests.pages_new.calculations.perpetual_gross_needs_page import PerpetualGrossNeedsPage
from automated_tests.pages_new.imt.comments_log_page import CommentsLogPage
from automated_tests.pages_new.imt.forms.discard_form_page import DiscardPage
from automated_tests.pages_new.imt.forms.inventory_pull_put_form_page import (
    InventoryPullPutLocators,
    InventoryPullPutPage,
)
from automated_tests.pages_new.imt.forms.po_void_form_page import PoVoidPage
from automated_tests.pages_new.imt.forms.receipt_override_form_page import ReceiptOverridePage
from automated_tests.pages_new.imt.forms.weekend_coverage_checklist_form_page import WeekendCoverageChecklistPage
from automated_tests.pages_new.imt.ingredients_depletion_page import IngredientsDepletionPage
from automated_tests.pages_new.imt.packaging_depletion_page import PackagingDepletionPage
from automated_tests.pages_new.imt.po_status_view_page import PoStatusViewPage
from automated_tests.pages_new.imt.production_kit_guide_page import ProductionKitGuidePage
from automated_tests.pages_new.imt.production_need_ingredients_page import ProductionNeedIngredientsPage
from automated_tests.pages_new.imt.remake_tool_page import RemakeToolPage
from automated_tests.services.api.admin.imt.sites_client import SiteClient
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.weekend_coverage_checklist_form import WeekendCoverageChecklistClient
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.managers.admin.dc_admin import DcProperty


def test_add_new_site_config(page: Page):
    """
    Test adding new site config and removing it

    Test steps:
    1.Generate required test data (TestSiteConfig to add it)
    2.Prepare the test environment
    3.Open IMT Sites Page
    4.Click ADD NEW CONFIG button -> ADD NEW CONFIG modal should be visible
    5.Fill all required fields on ADD NEW CONFIG modal and click SAVE btn ->
                                                 ADD NEW CONFIG modal should be closed and new config should be saved
    6.Assert corner message should be "Item has been successfully created"
    7.Assert table headers and table data
    8.Request GET API call to Sites endpoint and verify that created config is returned
    """
    site_config = SiteConfigs.random_value()
    site_config_to_add = dataclasses.replace(
        site_config,
        enabled=True,
        is_3pl=True,
        is_hj_autostore=True,
        is_fulfilment_only=True,
        multi_brand_dc=site_config.site.code,
        supplier_names=[data_generator.generate_string()],
        consolidated_site=site_config.site.code,
    )
    site_config_to_add.site.dc_azure_role = site_config.site.code
    supplier = TestSupplier.generate_supplier(name=site_config_to_add.supplier_names[0])
    with TestData(None).with_site_configs(site_config).with_brands(site_config.brand).with_sites(
        site_config.site
    ).with_users(Users.test_user.value).with_supplier(supplier):
        sites_page = SitesPage(page=page)
        sites_page.open()
        try:
            sites_page.add_new_site_config(site_config=site_config_to_add)
            sites_page.check_sites_column_headers()
            table_data = sites_page.get_sites_table_data()[0]
            response = SiteClient().get_site_configs(site_config_to_add.week)
            response_data = base_steps.process_response_and_extract_data(response, element_index=0)
            assert table_data.brand == response_data["brand"] == site_config_to_add.brand.code
            assert table_data.full_site_name == response_data["siteFull"] == site_config_to_add.site.name
            assert table_data.hj_name == response_data["hjName"] == site_config_to_add.hj_name
            assert table_data.ot_name == response_data["orderingToolName"] == site_config_to_add.ot_dc_name
            assert table_data.receiving_type == response_data["receivingType"] == site_config_to_add.receiving_type
            assert table_data.inventory_type == response_data["inventoryType"] == site_config_to_add.inventory_type
            assert table_data.site == response_data["siteShort"] == site_config_to_add.site.code
            assert table_data.timezone == response_data["timezone"] == site_config_to_add.site.timezone
            assert table_data.hn_data_source == response_data["hnDataSource"] == site_config_to_add.hybrid_need_source
            assert table_data.supplier_name == response_data["supplierNames"][0] == site_config_to_add.supplier_names[0]
            assert response_data["dcAzureRole"] == site_config_to_add.site.dc_azure_role
            assert response_data["enabled"] == site_config_to_add.enabled
            assert response_data["is3pl"] == site_config_to_add.is_3pl
            assert response_data["isHjAutostore"] == site_config_to_add.is_hj_autostore
            assert response_data["source"] == site_config_to_add.source
            assert response_data["week"] == str(site_config_to_add.week)
            assert response_data["optional"]["multi_brand_dc"] == site_config_to_add.multi_brand_dc
            assert response_data["optional"]["consolidated_site_code"] == site_config_to_add.consolidated_site
        finally:
            site_config_to_add.unload()


def test_edit_and_delete_site_config(page: Page):
    """
    Test editing IMT site in ImtSitesPage

    Test steps:
    1.Generate required test data (TestSiteConfig) and add it to the DB and another TestSiteConfig to get fields of it
    2.Prepare the test environment
    3.Open IMT Sites Page
    4.Click pencil button regarding config to change -> EDIT CONFIG modal should be visible
    5.Fill all required fields on EDIT CONFIG modal and click SAVE btn ->
                                             EDIT CONFIG modal should be closed and new config should be saved
    6.Assert corner message should be "Item has been  successfully edited"
    7.Request GET API call to Sites endpoint and verify that edited config is returned and all edited fields are updated
    8.Click on delete btn to delete config
    9.Request GET API call to Sites endpoint -> No site configs should be returned and check that "Site config has been
    successfully deleted" message is present
    """
    site_config_1, site_config_2 = SiteConfigs.random_non_repetitive_values(qty=2)
    site_config_1.properties = {DcProperty.LOCKED_PRICING.name: generate_string()}
    site_config_1.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config_2.receiving_type = ReceiveInputType.MANUAL
    site_config_2.site = site_config_1.site
    site_config_2.brand = site_config_1.brand
    site_config_2.multi_brand_dc = site_config_2.site.code
    site_config_2.consolidated_site = site_config_2.site.code
    site_config_2.site.dc_azure_role = generate_string()
    site_config_2.enabled = not site_config_1.enabled
    site_config_2.is_3pl = not site_config_1.is_3pl
    site_config_2.is_hj_autostore = not site_config_1.is_hj_autostore
    site_config_2.supplier_names = [data_generator.generate_string()]
    supplier = TestSupplier.generate_supplier(name=site_config_2.supplier_names[0])
    with (
        TestData(None)
        .with_site_configs(site_config_1)
        .with_brands(site_config_1.brand)
        .with_sites(site_config_1.site)
        .with_users(Users.test_user.value)
        .with_supplier(supplier)
    ):
        sites_page = SitesPage(page=page)
        sites_page.open()
        sites_page.edit_site_config(site_config=site_config_2)
        response = SiteClient().get_site_configs(site_config_2.week)
        response_data = base_steps.process_response_and_extract_data(response, element_index=0)
        assert response_data["brand"] == site_config_2.brand.code
        assert response_data["dcAzureRole"] == site_config_2.site.dc_azure_role
        assert response_data["enabled"] == site_config_2.enabled
        assert response_data["hjName"] == site_config_2.hj_name
        assert response_data["is3pl"] == site_config_2.is_3pl
        assert response_data["isHjAutostore"] == site_config_2.is_hj_autostore
        assert response_data["orderingToolName"] == site_config_2.ot_dc_name
        assert response_data["receivingType"] == site_config_2.receiving_type
        assert response_data["siteFull"] == site_config_2.site.name
        assert response_data["siteShort"] == site_config_2.site.code
        assert response_data["source"] == site_config_2.source
        assert response_data["week"] == str(site_config_2.week)
        assert response_data["optional"]["multi_brand_dc"] == site_config_2.multi_brand_dc
        assert response_data["optional"]["consolidated_site_code"] == site_config_2.consolidated_site
        assert response_data["supplierNames"][0] == site_config_2.supplier_names[0]

        sites_page.delete_site_configs()
        response = SiteClient().get_site_configs(site_config_2.week)
        base_steps.process_response_and_extract_data(response, expected_length=0)


def test_change_sequence(page: Page):
    """
    Test changing order of IMT sites on ImtSitesPage
    Test steps:
    1. Prepare the test environment with brands, sites, site_configs
    2. Open Admin -> IMT Sites Page
    3. Get all sites from the table and shuffle them
    4. Click the SET SITES SEQUENCE button -> the SITES SEQUENCE modal should appear
    5. Set the sits sequence according to the shuffled sites and click the SAVE button ->
        The SITES SEQUENCE modal should be closed and the new sequence should be saved and wait for API response
        after changing the order
    6. Sort shuffled items by brand
    7. Get all sites from the table
    8. Assert that the table site's sequence equals the shuffled sites
    """
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*[site_config.site for site_config in SiteConfigs.get_all_values()])
        .with_site_configs(*SiteConfigs.get_all_values())
        .with_users(Users.test_user.value)
    ):
        sites_page = SitesPage(page=page)
        sites_page.open()
        sites_table_data = sites_page.get_sites_table_data()
        site_configs = [(site_config.brand, site_config.site) for site_config in sites_table_data]
        random.shuffle(site_configs)
        response = SiteClient().get_site_configs(CURRENT_WEEK)
        sites_page.set_site_sequence(site_configs)
        sites_page.wait_for_api_response(response_url=response.url)
        site_configs_ordered_brands = sites_page.order_admin_site_configs_by_brand(site_configs)
        sites_table_data = sites_page.get_sites_table_data()
        site_configs_new_order = [(site_config.brand, site_config.site) for site_config in sites_table_data]
        assert site_configs_new_order == site_configs_ordered_brands


def test_brands_in_site_config_form(page: Page):
    """
    Test "Brand" field for Site Config form on Sites Page.

    Test steps:
    1.Load the app
    2.Go to Admin → Sites
    3.Click "Add new config"
    4.Click on the Brands field
    5.Looping through brands and assert the that brand was selected
    """
    all_brands = [*Brands.get_all_values(), *FactorBrand.get_all_values()]
    with (
        TestData(None)
        .with_brands(*all_brands)
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*SiteConfigs.get_all_values())
        .with_users(*Users.get_all_values())
    ):
        sites_page = SitesPage(page=page)
        sites_page.open()
        sites_page.click_add_new_config_button()
        for brand in all_brands:
            sites_page.choose_brand_from_brand_dropdown(brand=brand)
            actual_brand_text = sites_page.get_text_from_brand_field()
            assert actual_brand_text == brand.name


def test_fulfillment_sites_are_displayed(page: Page):
    """
    Test that fulfillment sites are displayed on Packaging Depletion View, PO Status View,
    PO Void Form, Discards Form, Receipt Override Form, Comments Log and WCC form pages

    Test steps:
    1. Generate all required data (site configs with is_fulfilment property)
    2. Go through the pages (Packaging Depletion View, PO Status View, PO Void Form, Discards Form,
     Receipt Override Form, Comments Log pages) and check that the record exist on each page.
    3. Open WCC form, click on the 'Add record', and check that in 'PO Number' column po number for fulfillment site
    is not present
    """
    site_config = dataclasses.replace(SiteConfigs.random_value(), is_fulfilment_only=True)
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        site_config=site_config, po=po, cases_received=data_generator.random_int()
    )
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po)
    po_void = TestPoVoid.generate_po_void(site_config=site_config, po=po)
    discard = TestDiscard.generate_discard(sku=sku, site_config=site_config)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    comment_data = TestComment.generate_sku_comment(
        site=site_config.site.code, brand=site_config.brand.code, domain=Projects.IMT, sku=sku
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_receipt_override(receipt_override)
        .with_grn(grn)
        .with_po_void(po_void)
        .with_discard(discard)
        .with_packaging_demand(packaging_demand)
        .with_comments(comment_data)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()
        packaging_depletion_page.is_no_data_message_present(expected_presence=False)

        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()
        po_status_view_page.is_no_data_message_present(expected_presence=False)

        po_void_form_page = PoVoidPage(page=page)
        po_void_form_page.open()
        po_void_form_page.is_no_data_message_present(expected_presence=False)

        receipt_override_form_page = ReceiptOverridePage(page=page)
        receipt_override_form_page.open()
        receipt_override_form_page.is_no_data_message_present(expected_presence=False)

        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.is_no_data_message_present(expected_presence=False)

        comments_log_page = CommentsLogPage(page=page)
        comments_log_page.open()
        comments_log_page.is_no_data_message_present(expected_presence=False)

        wcc_form_page = WeekendCoverageChecklistPage(page=page)
        wcc_form_page.open()
        wcc_form_page.click_add_records()
        response = WeekendCoverageChecklistClient().get_weekend_checklist_pos_data(week=CURRENT_WEEK)
        list_of_pos = wcc_form_page.get_all_values_text_from_list_of_pos(response_url=response.url)
        assert list_of_pos == [po.po_number]


def test_fulfillment_sites_are_not_displayed(page: Page):
    """
    Test that fulfillment site aren`t displayed in the other pages except (Packaging Depletion View, PO Status View,
    PO Void Form, Discards Form, Receipt Override Form, Comments Log)

    Test steps:
    1. Generate all required data (site configs one of them with is_fulfilment property, another one without)
    2. Open Ing Depletion, Remake Tool, Production Needs, PKG pages and check that fulfillment site is not present
    3. Open Inventory pull put form, click on the 'Add record', and check that in 'Site' column fulfillment site is
    not present
    """
    site_config_is_fulfilment = dataclasses.replace(SiteConfigs.NJ_HF.value, is_fulfilment_only=True)
    site_config_not_fulfilment = SiteConfigs.CA_HF.value
    sku = TestSku.generate_sku()
    po_nj_hf = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_is_fulfilment)
    po_ca_hf = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_not_fulfilment)
    with (
        TestData(None)
        .with_brands(site_config_is_fulfilment.brand)
        .with_sites(site_config_is_fulfilment.site, site_config_not_fulfilment.site)
        .with_site_configs(site_config_is_fulfilment, site_config_not_fulfilment)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_nj_hf, po_ca_hf)
    ):
        perpetual_gross_need_page = PerpetualGrossNeedsPage(page=page)
        perpetual_gross_need_page.open()
        perpetual_gross_need_page.is_no_data_message_present()

        expected_daily_need_page = ExpectedDailyNeedPage(page=page)
        expected_daily_need_page.open()
        expected_daily_need_page.is_no_data_message_present()

        ing_depletion_page = IngredientsDepletionPage(page=page)
        ing_depletion_page.open()
        ing_depletion_page.is_no_data_message_present()

        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        remake_tool_page.is_no_data_message_present()

        production_needs_page = ProductionNeedIngredientsPage(page=page)
        production_needs_page.open()
        production_needs_page.is_no_data_message_present()

        pkg_page = ProductionKitGuidePage(page=page)
        pkg_page.open()
        pkg_page.is_no_data_message_present()

        pull_put_form_page = InventoryPullPutPage(page=page)
        pull_put_form_page.open()
        pull_put_form_page.click_add_records()
        form_locators_dict = {InventoryPullPutLocators.BRAND_FIELD: site_config_not_fulfilment.brand.code}
        pull_put_form_page.add_records(form_locators_dict=form_locators_dict)
        list_of_sites = pull_put_form_page.get_all_values_text_from_list_of_sites()
        assert list_of_sites == ["(empty)", site_config_not_fulfilment.site.code]


def test_show_archived_button(page: Page):
    """
    Test "Show archived" button. If site is disabled and week < Current week - 5, then site is considered archived.

    Test steps:
    1.Generate required data (site_config, site_config archived (disabled, week = current week -6))
    2.Open Sites page ("Show archived" button is disable by default)
    3.Get table data and check that here present only 1 record with non-archived site
    4.Enable "Show archived" button
    5.Check that the page presents 2 records with the archived site and non-archived site
    """
    site_config = SiteConfigs.NJ_HF.value
    site_config_archived = dataclasses.replace(SiteConfigs.GA_EP.value, enabled=False, week=CURRENT_WEEK - 6)
    with TestData(None).with_site_configs(site_config, site_config_archived).with_brands(
        site_config_archived.brand, site_config.brand
    ).with_sites(site_config.site, site_config_archived.site).with_users(Users.test_user.value):
        sites_page = SitesPage(page=page)
        sites_page.open()
        table_data = sites_page.get_sites_table_data()
        assert len(table_data) == 1
        assert table_data[0].site == site_config.site.code
        sites_page.enable_disable_archived_toggle(archived=True)
        table_data = sites_page.get_sites_table_data()
        assert len(table_data) == 2


def test_ca_sites_are_not_shown_in_us_site_sequence(page: Page):
    """
    Test case checks sites for each market.
    Test steps:
    1. Generate required test data(multi_market user, site_configs, sites, brands for US and CA markets)
    2. Open Admin-> IMT Sites page
    3. Check all sites for CA are shown and not present in US market
    """
    site_configs = [*SiteConfigs.get_all_values(), *SiteConfigsCA.get_all_values()]
    market_us, market_ca = Markets.US.value, Markets.CA.value
    with TestData(None).with_site_configs(*site_configs).with_users(MultiMarketUsers.test_user.value):
        sites_page = SitesPage(page=page)
        sites_page.open()

        sites_page.change_market_in_dropdown(market_us.name)
        expected_sites_us = [site_config.site.code for site_config in SiteConfigs.get_all_values()]
        actual_sites_us = sites_page.open_set_site_sequence_and_extract_site_names()
        sites_page.click_on_cancel_button()
        assert sorted(actual_sites_us) == sorted(expected_sites_us)

        sites_page.change_market_in_dropdown(market_ca.name)
        expected_sites_ca = [site_config.site.code for site_config in SiteConfigsCA.get_all_values()]
        actual_sites_ca = sites_page.open_set_site_sequence_and_extract_site_names()
        assert sorted(actual_sites_ca) == sorted(expected_sites_ca)
