import pytest
from playwright.sync_api import Page

from automated_tests.data.constants.admin_constants import (
    DC_USER_MENU_HEADERS,
    INVENTORY_PULL_PUT_VIEW_ONLY,
    PROCUREMENT_LEADERSHIP_ADMIN_MENU_HEADERS,
    PROCUREMENT_MANAGER_JOBS,
    PROCUREMENT_USER_JOBS,
    SITE_VIEW_ONLY,
    AvailableJobsAdminSyncJob,
)
from automated_tests.data.constants.base_constants import Markets, SiteConfigs, Users
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.admin.imt.sites_page import SitesPage
from automated_tests.pages_new.admin.sync_jobs.sync_jobs_page import SynchronizationJobsPage
from automated_tests.pages_new.imt.forms.inventory_pull_put_form_page import InventoryPullPutPage


def test_admin_role_job(page: Page):
    """
    Test case checks that users with Admin roles have permission to see all jobs.

    Test steps:
    1.Generate required test data
    2.Open Admin Synchronization_Jobs page with setting role = 'admin'
    3.Assert page has the same columns on UI as expected
    4.Assert 'admin' role can run all jobs, check changelog is visible and clickable
    """
    with TestData(None).with_markets(Markets.US.value).with_users(Users.test_user.value):
        admin_sync_jobs = SynchronizationJobsPage(page=page)
        admin_sync_jobs.open()
        admin_sync_jobs.check_sync_jobs_column_headers()
        sync_jobs_table_data = admin_sync_jobs.get_sync_jobs_table_data()
        row_data = [row.job_name for row in sync_jobs_table_data]
        assert row_data == list(AvailableJobsAdminSyncJob)
        admin_sync_jobs.check_changelog_button_is_clickable_and_visible()


@pytest.mark.parametrize(
    "user", [Users.procurement_manager.value, Users.procurement_user.value, Users.helloconnect_user.value]
)
def test_procurement_manager_user_helloconnect_roles_job(page: Page, user):
    """
    Test case checks that users with procurement_manager, procurement_user, helloconnect roles have permission to see
    limited jobs.

    Test steps:
    1.Generate required test data
    2.Open Admin Synchronization_Jobs page with setting role = 'procurement_manager', 'procurement_user', 'helloconnect'
    3.Assert roles can run the expected jobs,  check changelog is visible and clickable
    4.Open Sites page
    5.Check page title for users
    """
    with TestData(None).with_markets(Markets.US.value).with_users(user):
        admin_sync_jobs = SynchronizationJobsPage(page=page)
        admin_sync_jobs.open()
        sync_jobs_table_data = admin_sync_jobs.get_sync_jobs_table_data()
        row_data = [row.job_name for row in sync_jobs_table_data]
        if user == Users.procurement_manager.value:
            assert row_data == PROCUREMENT_MANAGER_JOBS
        else:
            assert row_data == PROCUREMENT_USER_JOBS
        admin_sync_jobs.check_changelog_button_is_clickable_and_visible()

        sites_admin_page = SitesPage(page=page)
        sites_admin_page.open()
        sites_admin_page.check_breadcrumbs_title(expected_title=SITE_VIEW_ONLY)


def test_procurement_leadership_role_job(page: Page):
    """
    Test case checks that procurement_leadership role doesn't have access to Synchronization_jobs.

    Test steps:
    1.Generate required test data
    2.Open Admin Synchronization_Jobs page with setting role = 'procurement_leadership', check changelog is visible and
    clickable
    3. Assert users with 'procurement_leadership' role can run limited jobs inside ADMIN menu section.
    4. Open Sites page
    5. Check page title for procurement leadership user
    """
    with TestData(None).with_markets(Markets.US.value).with_users(Users.procurement_leadership.value):
        procurement_leadership_sync_jobs = SynchronizationJobsPage(page=page)
        procurement_leadership_sync_jobs.open()
        procurement_leadership_sync_jobs.check_changelog_button_is_clickable_and_visible()
        menu_headers = procurement_leadership_sync_jobs.get_admin_drop_down_rows()
        assert menu_headers == PROCUREMENT_LEADERSHIP_ADMIN_MENU_HEADERS

        sites_admin_page = SitesPage(page=page)
        sites_admin_page.open()
        sites_admin_page.check_breadcrumbs_title(expected_title=SITE_VIEW_ONLY)


@pytest.mark.parametrize("user", [Users.dc_user.value, Users.dc_universal_user.value, Users.dc_management_user.value])
def test_dc_all_user_role_job(page: Page, user):
    """
    Test case checks that dc all users(dc_user, dc_management_user, dc_universal_user) don't have access to ADMIN menu
    section. There are active only Buyer and IMT menu sections.
    Test steps:
    1.Generate required test data
    2.Open Admin Synchronization_Jobs page with setting role = 'dc_user', 'dc_universal_user', 'dc_management_user',
     check changelog is visible and clickable
    3.Assert page has the same limited menu headers on UI as expected.
    """
    with TestData(None).with_markets(Markets.US.value).with_users(user):
        dc_user_sync_jobs = SynchronizationJobsPage(page=page)
        dc_user_sync_jobs.open()
        dc_user_sync_jobs.check_changelog_button_is_clickable_and_visible()

        menu_section_headers = dc_user_sync_jobs.get_menu_headers()
        assert menu_section_headers == DC_USER_MENU_HEADERS


def test_dc_user_on_inventory_pull_put(page: Page):
    """
    Test case checks DC user access on IMT-> Inventory Pull/Put
    Test steps:
    1. Generate required data with random_brand and dc_user
    2. Open IMT -> Inventory Pull/Put page
    3. Check page title for dc user (Dc user should have View only access)
    """
    site_config = SiteConfigs.random_value()
    with TestData(None).with_users(Users.dc_user.value).with_site_configs(site_config):
        pull_put_form_page = InventoryPullPutPage(page=page)
        pull_put_form_page.open()
        pull_put_form_page.check_breadcrumbs_title(expected_title=INVENTORY_PULL_PUT_VIEW_ONLY)
