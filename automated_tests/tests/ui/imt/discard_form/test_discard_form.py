import dataclasses
import random
from datetime import timed<PERSON><PERSON>

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    Colors,
    DayOfWeek,
    Factor<PERSON>rand,
    PurchasingCategories,
    SiteConfigs,
    SiteConfigsFactor,
    SkuNames,
    SkuStatus,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_4,
    DAY_FORMAT,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    TIME_FORMAT_1,
)
from automated_tests.data.constants.error_messages_constants import (
    DISCARD_RATE_GREATER_THAN_WARNING,
    INVALID_BATCH_ERROR_MESSAGE,
    RETIRED_SKU_WARNING,
    RETIRED_SKUS_WARNING,
)
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.highjump import TestHJDiscard
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.forms.discard_form_page import DiscardFormLocators, DiscardPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.discard_form import ImtDiscardFormClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.data.models.highjump.highjump import DiscardType
from procurement.data.models.inventory import DataSource


def test_insert_and_delete_discard_data(page: Page):
    """
    Test insert and delete discard record.

    Test steps:
    1.Generate required data (site config, sku, hybrid need, data for adding to the form)
    2.Open Discard Form page
    3.Click Add records
    4.Fill out all fields, click Validate records button, then click on submit button
    5.Check that the record was created, check column headers, extract data from the table
    6.Check that the data are equal to the data from API response
    7.Click on the delete button to remove the record and check that the record was removed
    8.Check that ongoing week is highlighted
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    discard = TestDiscard.generate_discard(sku=sku, site_config=site_config)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    user = Users.test_user.value
    brand_code = site_config.brand.code
    site_code = site_config.site.code
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_purchasing_categories(purchasing_category)
        .with_hybrid_needs(hybrid_needs)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()
        form_locators_dict = {
            DiscardFormLocators.BRAND_FIELD: brand_code,
            DiscardFormLocators.SITE_FIELD: site_code,
            DiscardFormLocators.DATE_FIELD: discard.discarded_datetime.strftime(DATE_FORMAT_1),
            DiscardFormLocators.TIME_FIELD: discard.discarded_datetime.strftime(TIME_FORMAT_1),
            DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
            DiscardFormLocators.NUM_UNITS_FIELD: str(discard.quantity),
            DiscardFormLocators.COMMENT_FIELD: discard.comment,
        }
        discard_form_page.add_records(form_locators_dict=form_locators_dict)
        discard_form_page.validate_records()
        discard_form_page.submit_records()
        discard_form_page.check_discard_column_headers()
        api_response = ImtDiscardFormClient().get_discard_data(week=CURRENT_WEEK)
        api_response_data = base_steps.process_response_and_extract_data(
            response=api_response, additional_keys=["data"], element_index=0
        )
        table_data = discard_form_page.get_discard_table_data()[0]
        assert table_data.brand == api_response_data["brand"]
        assert table_data.site == api_response_data["site"]
        assert table_data.date == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["discardedTime"],
            input_date_format=DATE_TIME_FORMAT_4,
            output_date_format=DATE_FORMAT_1,
        )

        assert table_data.time == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["discardedTime"],
            input_date_format=DATE_TIME_FORMAT_4,
            output_date_format=TIME_FORMAT_1,
        )
        assert table_data.category == api_response_data["category"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.num_units == api_response_data["quantity"]
        assert table_data.unit_of_measure == api_response_data["unitOfMeasure"]
        assert table_data.discard_rate == ui_utils.to_percent_string(api_response_data["discardRate"])
        assert table_data.username == api_response_data["user"]
        assert table_data.comment == api_response_data["comment"]
        discard_form_page.delete_records()
        discard_form_page.is_no_data_message_present()
        discard_form_page.choose_week_from_dropdown(week=NEXT_WEEK)
        discard_form_page.check_week_item_color_dropdown(week=CURRENT_WEEK, expected_color=Colors.CYAN)


def test_editing_added_records(page: Page):
    """
    Test editing added record. Editable fields are date, time, quantity and comment

    Test steps:
    1.Generate required data (site config, sku, hybrid need, discard, data for editing)
    2.Open Discard Form page
    3.Edit date, time, quantity and comment fields
    4.Check that the values were edited
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    discard = TestDiscard.generate_discard(sku=sku, site_config=site_config)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    date_discarded = (CURRENT_DATETIME - timedelta(days=data_generator.random_int())).strftime(DATE_FORMAT_1)
    time_discarded = (CURRENT_DATETIME - timedelta(hours=data_generator.random_int(1))).strftime(TIME_FORMAT_1)
    discard_quantity = data_generator.random_int(3)
    discard_comment = data_generator.generate_string()
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_hybrid_needs(hybrid_needs)
        .with_discard(discard)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        edit_locators_dict = {
            DiscardFormLocators.DATE_FIELD: date_discarded,
            DiscardFormLocators.TIME_FIELD: time_discarded,
            DiscardFormLocators.DISCARD_QUANTITY_FIELD: str(discard_quantity),
            DiscardFormLocators.COMMENT_FIELD: discard_comment,
        }
        api_response = ImtDiscardFormClient().get_discard_data(week=CURRENT_WEEK)
        discard_form_page.editing_records(edit_locators_dict=edit_locators_dict, response_url=api_response.url)
        table_data = discard_form_page.get_discard_table_data()[0]
        assert table_data.date == date_discarded
        assert table_data.time == time_discarded
        assert table_data.num_units == discard_quantity
        assert table_data.comment == discard_comment


def test_invalid_added_records(page: Page):
    """
    Test invalid added record. If necessary field are empty the message "Invalid batch error" should be present
    Necessary fields are brand, site, ku name, quantity

    Test steps:
    1.Generate required data (site config, sku, hybrid need, discard)
    2.Open Discard Form page
    3.Click Add records button
    4.Add the records with leaving necessary field empty and check that the error message is present
    5.Close the form and repeat step #3 for all fields
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    discard = TestDiscard.generate_discard(sku=sku, site_config=site_config)
    brand_code = site_config.brand.code
    site_code = site_config.site.code
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_hybrid_needs(hybrid_needs)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        form_locators_dict = {
            DiscardFormLocators.BRAND_FIELD: brand_code,
            DiscardFormLocators.SITE_FIELD: site_code,
            DiscardFormLocators.DATE_FIELD: discard.discarded_datetime.strftime(DATE_FORMAT_1),
            DiscardFormLocators.TIME_FIELD: discard.discarded_datetime.strftime(TIME_FORMAT_1),
            DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
            DiscardFormLocators.NUM_UNITS_FIELD: str(discard.quantity),
            DiscardFormLocators.COMMENT_FIELD: discard.comment,
        }

        empty_fields_locators = [
            DiscardFormLocators.BRAND_FIELD,
            DiscardFormLocators.SITE_FIELD,
            DiscardFormLocators.SKU_NAME_FIELD,
            DiscardFormLocators.NUM_UNITS_FIELD,
        ]

        for empty_fields_locator in empty_fields_locators:
            form_locators_dict_copy = form_locators_dict.copy()
            # if brand or site fields are empty - skuName can`t be filled,
            # and if brand is empty then site can't be filled
            if empty_fields_locator in [DiscardFormLocators.BRAND_FIELD]:
                del form_locators_dict_copy[DiscardFormLocators.SKU_NAME_FIELD]
                del form_locators_dict_copy[DiscardFormLocators.SITE_FIELD]
            del form_locators_dict_copy[empty_fields_locator]
            discard_form_page.click_add_records()
            discard_form_page.add_records(form_locators_dict=form_locators_dict_copy)
            discard_form_page.validate_records()
            actual_submit_info_title = discard_form_page.get_submit_info_title()
            assert actual_submit_info_title == INVALID_BATCH_ERROR_MESSAGE
            discard_form_page.cancel_submit_records()
            discard_form_page.click_cancel_button_on_form()


def test_validate_warning_message_discard_rate_more_than_4(page: Page):
    """
    Test that warning message discard rate more than 4. Message should appear for any SKU with a discard rate greater
    than 4% -The running total discard rate for [SKU NAME HERE] is greater than 4% AND SKU_NAME should be highlighted
    in orange

    Test steps:
    1.Generate required data (site config, sku, hybrid need, discard)
    2.Open Discard Form page
    3.Click Add records button
    4.Add the record, click on submit button and check that warning message is present
    5.Click on cancel button and check that the sku name is highlighted in orange
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config, qty=random.randint(100, 200))
    discard = TestDiscard.generate_discard(
        sku=sku, site_config=site_config, quantity=round(hybrid_needs.quantity / random.uniform(0.01, 0.04))
    )
    brand_code = site_config.brand.code
    site_code = site_config.site.code
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_hybrid_needs(hybrid_needs)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()
        form_locators_dict = {
            DiscardFormLocators.BRAND_FIELD: brand_code,
            DiscardFormLocators.SITE_FIELD: site_code,
            DiscardFormLocators.DATE_FIELD: discard.discarded_datetime.strftime(DATE_FORMAT_1),
            DiscardFormLocators.TIME_FIELD: discard.discarded_datetime.strftime(TIME_FORMAT_1),
            DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
            DiscardFormLocators.NUM_UNITS_FIELD: str(discard.quantity),
            DiscardFormLocators.COMMENT_FIELD: discard.comment,
        }
        discard_form_page.add_records(form_locators_dict=form_locators_dict)
        discard_form_page.validate_records()
        actual_warning_message = discard_form_page.get_warning_messages()
        assert len(actual_warning_message) == 1
        assert next(iter(actual_warning_message)) == DISCARD_RATE_GREATER_THAN_WARNING.format(percentage=4)
        discard_form_page.cancel_submit_records()
        discard_form_page.check_sku_name_background_color(expected_color=Colors.ORANGE)


def test_validate_warning_message_discard_rate_less_than_4(page: Page):
    """
    Test that warning message discard rate less than 4. IF the running total discard rate for [SKU NAME HERE] is less
    than 4% THEN discard warning message should be absent

    Test steps:
    1.Generate required data (site config, sku, hybrid need, discard)
    2.Open Discard Form page
    3.Click Add records button
    4.Add the record, click on submit button and check that warning message is absent
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config, qty=random.randint(100, 200))
    discard = TestDiscard.generate_discard(
        sku=sku, site_config=site_config, quantity=round(hybrid_needs.quantity * random.uniform(0.01, 0.035))
    )
    brand_code = site_config.brand.code
    site_code = site_config.site.code
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_hybrid_needs(hybrid_needs)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()
        form_locators_dict = {
            DiscardFormLocators.BRAND_FIELD: brand_code,
            DiscardFormLocators.SITE_FIELD: site_code,
            DiscardFormLocators.DATE_FIELD: discard.discarded_datetime.strftime(DATE_FORMAT_1),
            DiscardFormLocators.TIME_FIELD: discard.discarded_datetime.strftime(TIME_FORMAT_1),
            DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
            DiscardFormLocators.NUM_UNITS_FIELD: str(discard.quantity),
            DiscardFormLocators.COMMENT_FIELD: discard.comment,
        }
        discard_form_page.add_records(form_locators_dict=form_locators_dict)
        discard_form_page.validate_records()
        discard_form_page.check_warning_message_is_not_displayed()


def test_retired_sku_alert(page: Page):
    """
    Test Retired SKU alert. If sku status = Archived or Inactive then sku is considered Retired.

    Test steps:
    1.Generate required data (3 skus (1 archived, 2 inactive, 3 non-retired) with different sku names, site config)
    2.Open Discard form page
    3.Click on "Add records" button, add 3 records and click on the "Validate records" button
    4.Assert that 'Retired Skus' warning message appears and the qty of records = 2 (for Archived and Inactive skus)
    5.Click on "Cancel" button and check that Retired Sku names (first 2 rows) are highlighted in orange color
    """
    site_config = SiteConfigs.random_value()
    sku_statuses = [
        SkuStatus.ARCHIVED,
        SkuStatus.INACTIVE,
        SkuStatus.random_value(exclude=[SkuStatus.ARCHIVED, SkuStatus.INACTIVE]),
    ]
    sku_names = SkuNames.random_non_repetitive_values(qty=3)
    skus = [
        TestSku.generate_sku(status=sku_status, sku_name=sku_name)
        for sku_status, sku_name in zip(sku_statuses, sku_names)
    ]
    with (
        TestData(None)
        .with_skus(*skus)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()
        for sku, row in zip(skus, range(len(skus))):
            form_locators_dict = {
                DiscardFormLocators.BRAND_FIELD: site_config.brand.code,
                DiscardFormLocators.SITE_FIELD: site_config.site.code,
                DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
                DiscardFormLocators.NUM_UNITS_FIELD: str(data_generator.random_int()),
            }
            discard_form_page.add_records(form_locators_dict=form_locators_dict, row_id=row)
        discard_form_page.validate_records()
        warning_massage = discard_form_page.get_warning_messages()
        assert len(warning_massage) == 1
        assert next(iter(warning_massage)) == RETIRED_SKUS_WARNING.format(qty_of_records=2)
        discard_form_page.cancel_submit_records()
        for row_id in range(2):
            discard_form_page.check_sku_name_background_color(row_id=row_id, expected_color=Colors.ORANGE)


def test_retried_sku_and_discard_rate_more_than_4_alerts(page: Page):
    """
    Test case when warning messages for Retired SKU and Discard Rate more than 4% are both present in the submit info.

    Test steps:
    1.Generate required data (site config, retired sku, hybrid_needs, discard_quantity)
    2.Open Discard form page
    3.Click on "Add records" button, add record and click on the "Validate records" button
    4.Assert that in messages present 2 warnings. One should be for retired sku and another for discard rate.
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku(status=SkuStatus.INACTIVE)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config, qty=random.randint(100, 200))
    discard_quantity = round(hybrid_needs.quantity / random.uniform(0.01, 0.04))
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_hybrid_needs(hybrid_needs)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()
        form_locators_dict = {
            DiscardFormLocators.BRAND_FIELD: site_config.brand.code,
            DiscardFormLocators.SITE_FIELD: site_config.site.code,
            DiscardFormLocators.SKU_NAME_FIELD: sku.sku_name,
            DiscardFormLocators.NUM_UNITS_FIELD: str(discard_quantity),
        }
        discard_form_page.add_records(form_locators_dict=form_locators_dict)
        discard_form_page.validate_records()
        warning_massages = discard_form_page.get_warning_messages()
        assert len(warning_massages) == 2
        assert warning_massages == [DISCARD_RATE_GREATER_THAN_WARNING.format(percentage=4), RETIRED_SKU_WARNING]


def test_validate_warning_messages_for_factor(page: Page):
    """
    Test validate warning messages for Factor brand.
    Warning messages should appear for skus with a discard rate greater than 1% for PROTEIN category and
    greater than 8% for PRODUCE/GROCERY/DAIRY.

    Test steps:
    1.Generate required data (site config, skus, hybrid needs, discards, category, the data is generated in such way for
    creating always conditions when discard rate is more than 1% for protein and more than 8% for produce/grocery/dairy)
    2.Open IMT -> Discards Form page
    3.Check validate message "1 record with discard rate greater than ({1}% for PROTEIN,{8}% for PRODUCE/GROCERY/DAIRY)"
    """
    site_config = SiteConfigsFactor.random_value()
    skus = sku_warning_8_percent, sku_warning_1_percent, sku_without_warning = [
        TestSku.generate_sku(brands=[FactorBrand.FJ.value.name]) for _ in range(3)
    ]
    sku_warning_8_percent.sku_name, sku_warning_1_percent.sku_name, sku_without_warning.sku_name = (
        SkuNames.random_non_repetitive_values(qty=3)
    )
    hybrid_needs_1, hybrid_needs_2, hybrid_needs_3 = [
        TestHybridNeeds.generate_hybrid_needs(
            sku=sku,
            site_config=site_config,
            week=CURRENT_WEEK_FACTOR,
            qty=data_generator.random_int(4),
            day=CURRENT_DATETIME,
        )
        for sku in [sku_warning_8_percent, sku_warning_1_percent, sku_without_warning]
    ]
    discard_warning_8_percent = TestDiscard.generate_discard(
        sku=sku_warning_8_percent,
        site_config=site_config,
        week=CURRENT_WEEK_FACTOR,
        quantity=round(hybrid_needs_1.quantity * random.uniform(0.08, 1)),
    )
    discard_warning_1_percent = TestDiscard.generate_discard(
        sku=sku_warning_1_percent,
        site_config=site_config,
        week=CURRENT_WEEK_FACTOR,
        quantity=round(hybrid_needs_2.quantity * random.uniform(0.01, 1)),
    )
    discard_without_warning = TestDiscard.generate_discard(
        sku=sku_without_warning,
        site_config=site_config,
        week=CURRENT_WEEK_FACTOR,
        quantity=round(hybrid_needs_3.quantity * random.uniform(0.01, 0.075)),
    )
    discards = [discard_warning_8_percent, discard_warning_1_percent, discard_without_warning]
    purchasing_category_non_protein = TestSkuCategory(
        sku=sku_warning_8_percent,
        ingredient_category=PurchasingCategories.random_value(
            exclude=[PurchasingCategories.PACKAGING, PurchasingCategories.PROTEIN]
        ),
    )
    purchasing_category_protein = TestSkuCategory(
        sku=sku_warning_1_percent, ingredient_category=PurchasingCategories.PROTEIN
    )
    with (
        TestData(None)
        .with_skus(*skus)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchasing_categories(purchasing_category_protein, purchasing_category_non_protein)
        .with_hybrid_needs(hybrid_needs_1, hybrid_needs_2, hybrid_needs_3)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.choose_week_from_dropdown(week=CURRENT_WEEK_FACTOR)
        discard_form_page.click_add_records()
        for discard, row in zip(discards, range(len(discards))):
            form_locators_dict = {
                DiscardFormLocators.BRAND_FIELD: discard.brand.code,
                DiscardFormLocators.SITE_FIELD: discard.site.code,
                DiscardFormLocators.DATE_FIELD: discard.discarded_datetime.strftime(DATE_FORMAT_1),
                DiscardFormLocators.TIME_FIELD: discard.discarded_datetime.strftime(TIME_FORMAT_1),
                DiscardFormLocators.SKU_NAME_FIELD: discard.sku.sku_name,
                DiscardFormLocators.NUM_UNITS_FIELD: str(discard.quantity),
                DiscardFormLocators.COMMENT_FIELD: discard.comment,
            }
            discard_form_page.add_records(form_locators_dict=form_locators_dict, row_id=row)
        discard_form_page.validate_records()
        actual_warning_messages = discard_form_page.get_warning_messages()
        assert len(actual_warning_messages) == 2, "No warning message should be for third sku item"
        assert actual_warning_messages == [
            DISCARD_RATE_GREATER_THAN_WARNING.format(percentage=8),
            DISCARD_RATE_GREATER_THAN_WARNING.format(percentage=1),
        ]


def test_hj_discard_type_sites_are_not_available_on_discard_form(page: Page):
    """
    Test that sites with HJ Discard type are filtered out from autocomplete dropdown and Brands with no non HJ Discard
    sites are also filtered out from Brand autocomplete dropdown

    1.Generate required data (3 site configs - 1 for EP brand with hj type, 1 for EP brand with non hj type,
    1 for HF with hj type)
    2.Open Discard form page and click on "Add record" button
    3.Open sites dropdown and get sites -> the sites should be absent as brand is not picked and (select brand first)
    row should be present
    4.Open brands dropdown and get brands -> HF brand should be filtered out as it has 1 site with hj discard type, and
    EP brand should be present as it has at least 1 site with non hj discard type
    4.Choose brand and open site dropdown again -> check that site with non hj discard site is present and site with
    hj discard type was filtered out
    """
    site_config_ep_hj = dataclasses.replace(SiteConfigs.GA_EP.value, source=DataSource.HIGHJUMP)
    site_config_ep_non_hj = dataclasses.replace(SiteConfigs.CT_EP.value, source=DataSource.APP)
    site_config_hf_hj = dataclasses.replace(SiteConfigs.CA_HF.value, source=DataSource.HIGHJUMP)
    with (
        TestData(None)
        .with_brands(site_config_ep_hj.brand, site_config_hf_hj.brand)
        .with_sites(site_config_ep_hj.site, site_config_ep_non_hj.site, site_config_hf_hj.site)
        .with_site_configs(site_config_ep_hj, site_config_ep_non_hj, site_config_hf_hj)
        .with_users(Users.test_user.value)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        discard_form_page.click_add_records()

        list_of_sites = discard_form_page.get_all_values_text_from_list_of_sites()
        assert list_of_sites == ["(select brand first)"]
        list_of_brands = discard_form_page.get_all_values_text_from_list_of_brands()
        assert list_of_brands == ["(empty)", site_config_ep_non_hj.brand.code]

        form_locators_dict = {DiscardFormLocators.BRAND_FIELD: site_config_ep_non_hj.brand.code}
        discard_form_page.add_records(form_locators_dict=form_locators_dict)
        list_of_sites = discard_form_page.get_all_values_text_from_list_of_sites()
        assert list_of_sites == ["(empty)", site_config_ep_non_hj.site.code]


def test_hj_wip_discards_on_discard_form(page: Page):
    """
    Test HJ discards on Discard Form
    On discard form should display hj wip discards where tran_type = WIP DISCARD, BULK DISCARD or AUTOBAGGER DISCARD

    Test steps:
    1. Generate required data (site_config, skus, 5 hj discards, hybrid needs)
    2. Open Discard form Page
    3. Extract table data and check that on page 3 record is displayed
    4. Edit the comment field and check that "HJ Discards can't be edited" message is present
    5. Check that data in table are equal to expected
    6. Click on delete button to delete the record and check that "HJ Discards can't be edited" message is present
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config.source = DataSource.HIGHJUMP
    is_wednesday = CURRENT_DATE.strftime(DAY_FORMAT) == DayOfWeek.WEDNESDAY
    sku_names = SkuNames.random_non_repetitive_values(qty=5)
    skus = sku_wip, sku_bulk, sku_autobagger, sku_discard, sku_donation = TestSku.generate_skus_with_specific_sku_names(
        sku_names=sku_names
    )
    hj_wip_discard = TestHJDiscard.generate_discard(
        sku=sku_wip, site_config=site_config, tran_type=DiscardType.WIP_DISCARD
    )
    hj_bulk_discard = TestHJDiscard.generate_discard(
        sku=sku_bulk, site_config=site_config, tran_type=DiscardType.BULK_DISCARD
    )
    hj_autobagger_discard = TestHJDiscard.generate_discard(
        sku=sku_autobagger, site_config=site_config, tran_type=DiscardType.AUTOBUGGER_DISCARD
    )
    hj_discard = TestHJDiscard.generate_discard(sku=sku_discard, site_config=site_config, tran_type=DiscardType.DISCARD)
    hj_donation_discard = TestHJDiscard.generate_discard(
        sku=sku_donation, site_config=site_config, tran_type=DiscardType.DONATION
    )
    hn_wip, hn_bulk, hn_autobagger, hn_discard, hn_donation = [
        TestHybridNeeds.generate_hybrid_needs(
            sku=sku, site_config=site_config, week=PREVIOUS_WEEK if is_wednesday else CURRENT_WEEK, day=CURRENT_DATE
        )
        for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_hj_discard(hj_wip_discard, hj_bulk_discard, hj_autobagger_discard, hj_discard, hj_donation_discard)
        .with_hybrid_needs(hn_wip, hn_bulk, hn_autobagger, hn_discard, hn_donation)
    ):
        discard_form_page = DiscardPage(page=page)
        discard_form_page.open()
        if is_wednesday:
            discard_form_page.choose_week_from_dropdown(week=PREVIOUS_WEEK)
        discard_table_data = discard_form_page.get_discard_table_data()
        assert len(discard_table_data) == 3

        # edit comment field
        edit_locators_dict = {DiscardFormLocators.COMMENT_FIELD: data_generator.generate_string()}
        discard_form_page.editing_records(edit_locators_dict=edit_locators_dict)
        discard_form_page.check_alert_message_hj_discards_cant_be_edited_is_displayed()

        api_response = ImtDiscardFormClient().get_discard_data(week=PREVIOUS_WEEK if is_wednesday else CURRENT_WEEK)
        actual_data = base_steps.process_response_and_extract_data(
            response=api_response, additional_keys=["data"], expected_length=3
        )
        api_data_by_sku_name = {item["skuName"]: item for item in actual_data}
        for row in range(len(discard_table_data)):
            if discard_table_data[row].sku_name == sku_wip.sku_name:
                api_data = api_data_by_sku_name[sku_wip.sku_name]
            elif discard_table_data[row].sku_name == sku_bulk.sku_name:
                api_data = api_data_by_sku_name[sku_bulk.sku_name]
            else:
                api_data = api_data_by_sku_name[sku_autobagger.sku_name]
            assert discard_table_data[row].brand == api_data["brand"]
            assert discard_table_data[row].site == api_data["site"]
            assert discard_table_data[row].date == datetime_utils.reformat_datetime_str(
                date_string=api_data["discardedTime"],
                input_date_format=DATE_TIME_FORMAT_4,
                output_date_format=DATE_FORMAT_1,
            )
            assert discard_table_data[row].time == datetime_utils.reformat_datetime_str(
                date_string=api_data["discardedTime"],
                input_date_format=DATE_TIME_FORMAT_4,
                output_date_format=TIME_FORMAT_1,
            )
            assert discard_table_data[row].sku_name == api_data["skuName"]
            assert discard_table_data[row].num_units == api_data["quantity"]
            assert discard_table_data[row].unit_of_measure == api_data["unitOfMeasure"]
            assert discard_table_data[row].discard_rate == ui_utils.to_percent_string(api_data["discardRate"])
            assert discard_table_data[row].username == ""
            assert discard_table_data[row].comment == ""

        # delete the record
        discard_form_page.delete_records()
        discard_form_page.check_alert_message_hj_discards_cant_be_edited_is_displayed()
