import dataclasses
from datetime import <PERSON><PERSON><PERSON>

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    ALL_POS_WITH_TRANSFERS,
    PO_STATUS_SHEET_NAME,
    RECEIVED_POS_ONLY,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.column_headers.gsheet_headers.financial_weekly_po_export_headers import (
    FINANCIAL_PO_EXPORT_EXPECTED_HEADERS,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FIRST_DAY_DATETIME,
    CURRENT_WEEK_LAST_PRODUCTION_DAY_DATETIME,
    DATE_FORMAT_2,
    DATE_FORMAT_5,
    FIRST_DAY_OF_CURRENT_MONTH,
    LAST_DAY_OF_THE_MONTH_DATETIME,
    PREVIOUS_WEEK,
    PREVIOUS_WEEK_FIRST_DAY_DATETIME,
)
from automated_tests.data.constants.file_export_constants import (
    CUSTOM_MANUAL_PO_EXPORT_FILE_NAME,
    DAILY_MANUAL_PO_EXPORT_FILE_NAME,
    MONTHLY_MANUAL_PO_EXPORT_FILE_NAME,
    WEEK_RANGE_MANUAL_PO_EXPORT_FILE_NAME,
    WEEKLY_MANUAL_PO_EXPORT_FILE_NAME,
)
from automated_tests.data.constants.ordering_constants import HJReceiptStatuses
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.po_status_view_page import PoStatusViewPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.admin.synchronization_jobs import financial_export_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.services.files import excel_file_parser
from automated_tests.tests.synchronization_jobs.data.gsheet_models.po_status_export_model import FinancialPoExportModel
from automated_tests.utils import files_utils
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.core.dates import ScmWeek


def test_manual_po_export_daily_with_brand_check(page: Page):
    """
    Data is exported if:
    The receipt date (hj_receipt.receipt_time_est for HJ receiving type) should be for the day before export date.

    Test steps:
    1.Generate the test data: 4 PO, 4 HJ Receipts for each PO, assign
                             hj_receipt_hf_day_before.receipt_time_est = date_to_export - timedelta(days=1)
                            hj_receipt_hf_to_export.receipt_time_est = date_to_export
                            hj_receipt_ep_not_to_export.receipt_time_est = date_to_export
                            hj_receipt_hf_day_after.receipt_time_est = date_to_export + timedelta(days=1)
      We added one PO with another brand to be sure that this PO will not be exported
    2.Prepare the test environment
    3.Open IMT PO Status page
    4.Click on the Generate export/report button
    5.Fill all necessary data (All brands, Daily time period and set date_to_export)
        and click Generate button -> the xlsx file should be downloaded
    6.Read the file and check that the there is only data for po_to_export
    """
    site_config_ep = dataclasses.replace(SiteConfigs.CT_EP.value, receiving_type=ReceiveInputType.HIGH_JUMP)
    site_config_hf = dataclasses.replace(SiteConfigs.CA_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP)

    po_hf_day_before, po_hf_to_export, po_hf_day_after = TestPurchaseOrder.generate_pos_with_sku(
        pos_quantity=3,
        one_sku=True,
        site_config=site_config_hf,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )

    sku = po_hf_to_export.first_line().sku
    po_ep_not_to_export = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config_ep,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = [po_hf_day_before, po_hf_to_export, po_ep_not_to_export, po_hf_day_after]
    date_to_export = CURRENT_DATETIME
    receipt_times_est = [date_to_export - timedelta(days=1), date_to_export, date_to_export + timedelta(days=1)]
    hj_receipt_hf_day_before, hj_receipt_hf_to_export, hj_receipt_hf_day_after = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config_hf, receipt_time_est=receipt_time_est)
        for po, receipt_time_est in zip([po_hf_day_before, po_hf_to_export, po_hf_day_after], receipt_times_est)
    ]
    hj_receipt_ep_not_to_export = TestHJReceipts.generate_hj_receipt(
        po=po_ep_not_to_export, site_config=site_config_ep, receipt_time_est=date_to_export
    )
    buyer_sku_1 = TestBuyerSku.generate_buyer_sku(site_config=site_config_hf, sku=sku)
    buyer_sku_2 = TestBuyerSku.generate_buyer_sku(site_config=site_config_ep, sku=sku)
    category = TestSkuCategory(ingredient_category=PurchasingCategories.random_value(), sku=sku)
    with (
        TestData(None)
        .with_brands(site_config_ep.brand, site_config_hf.brand)
        .with_sites(site_config_ep.site, site_config_hf.site)
        .with_site_configs(site_config_ep, site_config_hf)
        .with_users(Users.test_user.value)
        .with_purchase_orders(*pos)
        .with_hj_receipts(
            hj_receipt_hf_day_before, hj_receipt_hf_to_export, hj_receipt_hf_day_after, hj_receipt_ep_not_to_export
        )
        .with_buyer_sku(buyer_sku_1, buyer_sku_2)
        .with_purchasing_categories(category)
        .with_skus(sku)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()

        daily_file_name = DAILY_MANUAL_PO_EXPORT_FILE_NAME.format(
            date_str=(date_to_export + timedelta(days=1)).strftime(DATE_FORMAT_2),
            brand_code_lower=site_config_hf.brand.code.lower(),
        )
        res = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config_hf)
        res_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=res, expected_length=3, additional_keys=[site_config_hf.site.code]
            )
        }
        try:
            # Here date_to_export + timedelta(days=1) because it exports data for previous day
            po_status_view_page.generate_export_report(
                time_period=po_status_view_page.ExportTimePeriods.DAILY,
                brand=site_config_hf.brand,
                daily_date=CURRENT_DATE + timedelta(days=1),
                file_name=daily_file_name,
            )
            files_utils.wait_until_downloading_done(file_name=daily_file_name)
            assert files_utils.is_file_in_folder(file_name=daily_file_name)
            headers, rows = excel_file_parser.get_downloaded_xlsx_file_data(
                FinancialPoExportModel, daily_file_name, sheet_name=PO_STATUS_SHEET_NAME
            )
            assert headers == FINANCIAL_PO_EXPORT_EXPECTED_HEADERS
            assert len(rows) == 1
            assert rows[0].po_number == po_hf_to_export.po_number
            financial_export_steps.assert_financial_po_export_values(rows[0], res_data[rows[0].po_number])
        finally:
            files_utils.delete_file_from_downloads(daily_file_name)


def test_manual_po_export_weekly(page: Page):
    """
    Test weekly financial po export.
    Data is exported if:
    The receipt date (hj_receipt.receipt_time_est for HJ receiving type)
    should be in diapason from week.get_first_day() till (week.last_production_day + 1 day)
    Test steps:
    1.Generate the test data: 5 PO (1 from which is cancelled po), 5 HJ Receipts for each PO, assign
                            hj_receipt_prev_week.receipt_time_est = weekly_start_date - timedelta(days=1)
                            hj_receipt_weekly_start.receipt_time_est = weekly_start_date
                            hj_receipt_weekly_end.receipt_time_est = weekly_end_date
                            hj_receipt_next_week.receipt_time_est = weekly_end_date + timedelta(days=1)
    2.Prepare the test environment
    3.Open IMT PO Status page
    4.Click on the Generate export/report button and set PO_FILTER to be RECEIVED_POS_ONLY to check po_cancelled is
    filtered out from the response
    5.Fill all necessary data (All brands, weekly time period and set week) and click Generate button -> the xlsx file
    should be downloaded
    6.Read the file and check that the there is only data for po_weekly_start and po_weekly_end
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po_previous_week, po_weekly_start, po_weekly_end, po_next_week = TestPurchaseOrder.generate_pos_with_sku(
        pos_quantity=4,
        one_sku=True,
        site_config=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = po_previous_week, po_weekly_start, po_weekly_end, po_next_week
    weekly_start_date, weekly_end_date = CURRENT_WEEK_FIRST_DAY_DATETIME, CURRENT_WEEK_LAST_PRODUCTION_DAY_DATETIME
    receipt_times_est = [
        weekly_start_date - timedelta(days=1),
        weekly_start_date,
        weekly_end_date,
        weekly_end_date + timedelta(days=1),
    ]
    sku = po_weekly_start.first_line().sku
    po_cancelled = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        delivery_time_start=CURRENT_DATETIME - timedelta(days=10),
    )
    hj_receipts_po_cancelled = TestHJReceipts.generate_hj_receipt(
        po=po_cancelled, site_config=site_config, status=HJReceiptStatuses.CANCELLED
    )
    hj_receipts = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, receipt_time_est=receipt_time_est)
        for po, receipt_time_est in zip(pos, receipt_times_est)
    ]

    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    category = TestSkuCategory(ingredient_category=PurchasingCategories.random_value(), sku=sku)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(*pos, po_cancelled)
        .with_hj_receipts(*hj_receipts, hj_receipts_po_cancelled)
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(category)
        .with_skus(sku)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()

        weekly_file_name = WEEKLY_MANUAL_PO_EXPORT_FILE_NAME.format(
            week_str=str(ScmWeek.from_date(weekly_start_date)), brand_code_lower=site_config.brand.code.lower()
        )

        res = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        res_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=res, expected_length=5, additional_keys=[site_config.site.code]
            )
        }
        try:
            po_status_view_page.generate_export_report(
                time_period=po_status_view_page.ExportTimePeriods.WEEKLY,
                po_filter=RECEIVED_POS_ONLY,
                week=ScmWeek.from_date(weekly_start_date),
                file_name=weekly_file_name,
            )
            files_utils.wait_until_downloading_done(file_name=weekly_file_name)
            assert files_utils.is_file_in_folder(file_name=weekly_file_name)
            headers, rows = excel_file_parser.get_downloaded_xlsx_file_data(
                FinancialPoExportModel, weekly_file_name, sheet_name=PO_STATUS_SHEET_NAME
            )
            assert headers == FINANCIAL_PO_EXPORT_EXPECTED_HEADERS
            assert len(rows) == 2
            assert sorted([row.po_number for row in rows]) == sorted(
                [po_weekly_start.po_number, po_weekly_end.po_number]
            )
            for row in rows:
                financial_export_steps.assert_financial_po_export_values(row, res_data[row.po_number])
        finally:
            files_utils.delete_file_from_downloads(weekly_file_name)


def test_manual_po_export_with_week_range(page: Page):
    """
    Test financial po export with week range.

    Test steps:
    1.Generate the test data: 6 PO (1 from which with rejected status),6 HJ Receipts for each PO for prev_week_first_day
    - 1 day, for previous_week, for current_week_last_prod_day and current_week_last_prod_day + 1 day,2 transfer orders
    2.Open IMT PO Status page
    3.Click on the Generate export/report button having checked 'ALL POs + TOs'
    4.Fill all necessary data (All brands, week range period, set up week_from and week_to)
    and click Generate button -> the xlsx file should be downloaded
    5.Read the file and check that the there is only data (included rejected po, po_to) for week range previous
    week_first day till last prod_day of current_week (included) and check both transfer order records are exported
    """
    site_config, original_site = SiteConfigs.random_non_repetitive_values(2)
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    site_config.bob_code = site_config.dc.bob_code = site_config.site.code
    sku = TestSku.generate_sku()
    po_week_before_previous_week, po_week_from, po_week_to, po_next_week = TestPurchaseOrder.generate_pos(
        quantity=4,
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    po_rejected = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        delivery_time_start=CURRENT_DATETIME - timedelta(days=10),
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    week_from, week_to = PREVIOUS_WEEK, CURRENT_WEEK
    receipt_times_est = [
        PREVIOUS_WEEK_FIRST_DAY_DATETIME - timedelta(days=1),
        PREVIOUS_WEEK_FIRST_DAY_DATETIME,
        CURRENT_WEEK_LAST_PRODUCTION_DAY_DATETIME,
        CURRENT_WEEK_LAST_PRODUCTION_DAY_DATETIME + timedelta(days=1),
    ]
    hj_receipts_po_rejected = TestHJReceipts.generate_hj_receipt(
        po=po_rejected, site_config=site_config, status=HJReceiptStatuses.REJECTED
    )
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    category = TestSkuCategory(ingredient_category=PurchasingCategories.random_value(), sku=sku)
    original_pos = TestPurchaseOrder.generate_pos(quantity=2, site=original_site, sku=sku)
    transfer_order = TestPurchaseOrder.generate_transfer_order(
        original_pos=original_pos,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = po_week_before_previous_week, po_week_from, po_week_to, po_next_week
    hj_receipts = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, receipt_time_est=receipt_time_est)
        for po, receipt_time_est in zip(pos, receipt_times_est)
    ]
    transfer_hj_receipts = [
        TestHJReceipts.generate_hj_receipt(
            po=transfer_order,
            site_config=site_config,
            receipt_time_est=PREVIOUS_WEEK_FIRST_DAY_DATETIME,
            transfer_item_index=i,
        )
        for i in range(len(transfer_order.transfer_line_items))
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(*pos, transfer_order, *original_pos, po_rejected)
        .with_hj_receipts(*hj_receipts, *transfer_hj_receipts, hj_receipts_po_rejected)
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(category)
        .with_skus(sku)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()

        weekly_file_name = WEEK_RANGE_MANUAL_PO_EXPORT_FILE_NAME.format(
            week_from=week_from, week_to=week_to, brand_code_lower=site_config.brand.code.lower()
        )

        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        response_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=response, expected_length=6, additional_keys=[site_config.site.code]
            )
        }
        api_to_response = ImtPoStatusClient().get_to_dropdown_data(po=transfer_order)
        api_to_response_data = base_steps.process_response_and_extract_data(response=api_to_response, expected_length=2)
        actual_to_data = {api_data["supplier"]: api_data for api_data in api_to_response_data}
        try:
            po_status_view_page.generate_export_report(
                po_filter=ALL_POS_WITH_TRANSFERS,
                time_period=po_status_view_page.ExportTimePeriods.WEEK_RANGE,
                week_from=week_from,
                week_to=week_to,
                file_name=weekly_file_name,
            )
            files_utils.wait_until_downloading_done(weekly_file_name)
            assert files_utils.is_file_in_folder(weekly_file_name)
            headers, rows = excel_file_parser.get_downloaded_xlsx_file_data(
                FinancialPoExportModel, weekly_file_name, sheet_name=PO_STATUS_SHEET_NAME
            )
            assert headers == FINANCIAL_PO_EXPORT_EXPECTED_HEADERS
            assert len(rows) == 5
            # for 2 transfer order datas there are generated separate po records
            assert sorted([row.po_number for row in rows]) == sorted(
                [
                    po_week_from.po_number,
                    po_week_to.po_number,
                    po_rejected.po_number,
                    transfer_order.po_number,
                    transfer_order.po_number,
                ]
            )
            for row in rows:
                if row.po_number == transfer_order.po_number:
                    expected_data = actual_to_data[row.supplier]
                else:
                    expected_data = response_data[row.po_number]
                financial_export_steps.assert_financial_po_export_values(row, expected_data)
        finally:
            files_utils.delete_file_from_downloads(weekly_file_name)


def test_manual_po_export_monthly(page: Page):
    """
    Data is exported if:
    The receipt date (hj_receipt.receipt_time_est for HJ receiving type) should be in diapason from
    first day of the calendar month till last day of the calendar month.
    Test steps:
    1.Generate the test data: 5 PO (one with transfer order to check it is filtered out when generating report for
    'All Pos' cause po_to is included only when is selected 'All POs + TOs'), 5 HJ Receipts for each PO, assign
                       hj_receipt_last_day_of_prev_month.receipt_time_est = all_days_in_month[0]-timedelta(days=1)
                       hj_receipt_first_day_of_current_month.receipt_time_est = all_days_in_month[0]
                       hj_receipt_last_day_of_current_month.receipt_time_est = all_days_in_month[-1]
                       hj_receipt_first_day_of_next_month.receipt_time_est = all_days_in_month[-1]+timedelta(days=1)
                       hj_receipt_po_to = all_days_in_month[0]
    2.Prepare the test environment
    3.Open IMT PO Status page
    4.Click on the Generate export/report button
    5.Fill all necessary data (All brands, monthly time period and set start month date and end month date)
        and click Generate button -> the xlsx file should be downloaded
    6.Read the file and check that the there is only data for po_monthly_start and po_monthly_end
    """
    site_config, original_site = SiteConfigs.random_non_repetitive_values(2)
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    (
        po_last_day_of_prev_month,
        po_first_day_of_current_month,
        po_last_day_of_current_month,
        po_first_day_of_next_month,
    ) = TestPurchaseOrder.generate_pos_with_sku(
        pos_quantity=4,
        one_sku=True,
        site_config=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    sku = po_first_day_of_current_month.first_line().sku
    original_po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        po_number=data_generator.generate_po_number(bob_code=site_config.bob_code),
        source_bob_code=site_config.bob_code,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    transfer_order = TestPurchaseOrder.generate_transfer_order(original_pos=[original_po], site=site_config)
    pos = (
        po_last_day_of_prev_month,
        po_first_day_of_current_month,
        po_last_day_of_current_month,
        po_first_day_of_next_month,
        transfer_order,
    )
    receipt_times_est = [
        FIRST_DAY_OF_CURRENT_MONTH - timedelta(days=1),
        FIRST_DAY_OF_CURRENT_MONTH,
        LAST_DAY_OF_THE_MONTH_DATETIME,
        LAST_DAY_OF_THE_MONTH_DATETIME + timedelta(days=1),
        FIRST_DAY_OF_CURRENT_MONTH,
    ]
    (
        hj_receipt_last_day_of_prev_month,
        hj_receipt_first_day_of_current_month,
        hj_receipt_last_day_of_current_month,
        hj_receipt_first_day_of_next_month,
        hj_receipt_po_to,
    ) = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, receipt_time_est=receipt_time_est)
        for po, receipt_time_est in zip(pos, receipt_times_est)
    ]

    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    category = TestSkuCategory(ingredient_category=PurchasingCategories.random_value(), sku=sku)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(*pos)
        .with_hj_receipts(
            hj_receipt_last_day_of_prev_month,
            hj_receipt_first_day_of_current_month,
            hj_receipt_last_day_of_current_month,
            hj_receipt_first_day_of_next_month,
        )
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(category)
        .with_skus(sku)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()

        monthly_file_name = MONTHLY_MANUAL_PO_EXPORT_FILE_NAME.format(
            month_year_str=FIRST_DAY_OF_CURRENT_MONTH.strftime(DATE_FORMAT_5),
            brand_code_lower=site_config.brand.code.lower(),
        )
        res = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        res_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=res, expected_length=5, additional_keys=[site_config.site.code]
            )
        }
        try:
            po_status_view_page.generate_export_report(
                time_period=po_status_view_page.ExportTimePeriods.MONTHLY,
                month_date=FIRST_DAY_OF_CURRENT_MONTH,
                file_name=monthly_file_name,
            )
            files_utils.wait_until_downloading_done(file_name=monthly_file_name)
            assert files_utils.is_file_in_folder(file_name=monthly_file_name)
            headers, rows = excel_file_parser.get_downloaded_xlsx_file_data(
                FinancialPoExportModel, monthly_file_name, sheet_name=PO_STATUS_SHEET_NAME
            )
            assert headers == FINANCIAL_PO_EXPORT_EXPECTED_HEADERS
            assert len(rows) == 2
            assert sorted([row.po_number for row in rows]) == sorted(
                [po_first_day_of_current_month.po_number, po_last_day_of_current_month.po_number]
            )
            for row in rows:
                financial_export_steps.assert_financial_po_export_values(row, res_data[row.po_number])
        finally:
            files_utils.delete_file_from_downloads(monthly_file_name)


def test_manual_po_export_custom(page: Page):
    """
    Data is exported if:
    The receipt date (hj_receipt.receipt_time_est for HJ receiving type) should be in diapason from
    first custom date till last custom date.
    Test steps:
    1.Generate the test data: 4 PO, 4 HJ Receipts for each PO, assign
                            custom_start_date = all_days_in_month[0] - timedelta(days=10)
                            custom_end_date = all_days_in_month[-1] + timedelta(days=10)
                            hj_receipt_custom_before.receipt_time_est = custom_start_date - timedelta(days=1)
                            hj_receipt_custom_start.receipt_time_est = custom_start_date
                            hj_receipt_custom_end.receipt_time_est = custom_end_date
                            hj_receipt_custom_after.receipt_time_est = custom_end_date + timedelta(days=1)
    2.Prepare the test environment
    3.Open IMT PO Status page
    4.Click on the Generate export/report button
    5.Fill all necessary data (All brands, monthly time period and set start month date and end month date)
        and click Generate button -> the xlsx file should be downloaded
    6.Read the file and check that the there is only data for po_custom_start and po_custom_end
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po_custom_before, po_custom_start, po_custom_end, po_custom_after = TestPurchaseOrder.generate_pos_with_sku(
        pos_quantity=4,
        one_sku=True,
        site_config=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    pos = po_custom_before, po_custom_start, po_custom_end, po_custom_after
    sku = po_custom_start.first_line().sku
    custom_start_date = FIRST_DAY_OF_CURRENT_MONTH - timedelta(days=10)
    custom_end_date = LAST_DAY_OF_THE_MONTH_DATETIME + timedelta(days=10)
    receipt_times_est = [
        custom_start_date - timedelta(days=1),
        custom_start_date,
        custom_end_date,
        custom_end_date + timedelta(days=1),
    ]
    hj_receipt_custom_before, hj_receipt_custom_start, hj_receipt_custom_end, hj_receipt_custom_after = [
        TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config, receipt_time_est=receipt_time_est)
        for po, receipt_time_est in zip(pos, receipt_times_est)
    ]
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    category = TestSkuCategory(ingredient_category=PurchasingCategories.random_value(), sku=sku)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(po_custom_before, po_custom_start, po_custom_end, po_custom_after)
        .with_hj_receipts(
            hj_receipt_custom_before, hj_receipt_custom_start, hj_receipt_custom_end, hj_receipt_custom_after
        )
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(category)
        .with_skus(sku)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()

        custom_file_name = CUSTOM_MANUAL_PO_EXPORT_FILE_NAME.format(
            custom_date_str_1=custom_start_date.strftime(DATE_FORMAT_2),
            custom_date_str_2=custom_end_date.strftime(DATE_FORMAT_2),
            brand_code_lower=site_config.brand.code.lower(),
        )

        res = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        res_data = {
            po["poNumber"]: po
            for po in base_steps.process_response_and_extract_data(
                response=res, expected_length=4, additional_keys=[site_config.site.code]
            )
        }
        try:
            po_status_view_page.generate_export_report(
                time_period=po_status_view_page.ExportTimePeriods.CUSTOM,
                custom_date_start=custom_start_date,
                custom_date_end=custom_end_date,
                file_name=custom_file_name,
            )
            files_utils.wait_until_downloading_done(file_name=custom_file_name)
            assert files_utils.is_file_in_folder(file_name=custom_file_name)
            headers, rows = excel_file_parser.get_downloaded_xlsx_file_data(
                FinancialPoExportModel, custom_file_name, sheet_name=PO_STATUS_SHEET_NAME
            )
            assert headers == FINANCIAL_PO_EXPORT_EXPECTED_HEADERS
            assert len(rows) == 2
            assert sorted([row.po_number for row in rows]) == sorted(
                [po_custom_start.po_number, po_custom_end.po_number]
            )
            for row in rows:
                financial_export_steps.assert_financial_po_export_values(row, res_data[row.po_number])
        finally:
            files_utils.delete_file_from_downloads(custom_file_name)
