from datetime import timed<PERSON><PERSON>

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    CASE_AND_DELIVERY_TIME_DISCREPANCY,
    CASE_SIZE_DISCREPANCY,
    DIFFERENT_SHIPPED_AND_ORDER_QUANTITY,
    WAREHOUSES_BRAND,
    Colors,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    UnitMeasureASN,
    UnitOfMeasurePoAcknowledgement,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    DATE_TIME_FORMAT_4,
    NEXT_WEEK,
    PREVIOUS_WEEK,
)
from automated_tests.data.models.advanced_shipping_notice import TestAdvanceShippingNotice
from automated_tests.data.models.buyer_sku import Test<PERSON>uy<PERSON>Sku
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_acknowledgement import TestPOAcknowledgementLineItems
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.po_status_view_page import PoStatusViewPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.utils import comparisons, datetime_utils, ui_utils
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import WhReceivingType
from procurement.data.models.ordering.advance_shipping_notice import AdvanceShippingNoticeState


def test_pimt_po_status_main_values(page: Page):
    """
    Test case check main values on PIMT -> PO Status.
    Test steps:
    1. Generate required test data, setup inbound po and po for imt sites -> to be sure that record is filtered out from
    warehouses
    2. Make an API call to PO Status endpoint -> 200, extract response data
    3. Open PIMT -> PO Status page, extract table data
    4. Check column headers, check values from table data meet appropriate api response data
    5. Check enabled Multi-Week toggle for warehouses
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse)
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(
        sku=sku,
        warehouse=warehouse,
        emergency_reason=EmergencyReason.CHARITY,
        shipping_method=ShippingMethods.random_value(),
    )
    purchasing_category = TestSkuCategory(sku, PurchasingCategories.random_value())
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    grn = TestGrn.generate_grn(
        po=inbound_po,
        warehouse=warehouse,
        units_received=inbound_po.first_line().case_size + data_generator.random_int(),
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=inbound_po)
    po_acknowledgement = TestPOAcknowledgementLineItems.generate_po_acknowledgement(
        po=inbound_po, unit_of_measure=UnitOfMeasurePoAcknowledgement.CASE
    )
    advance_shipping_notice = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=inbound_po, unit_of_measure=UnitMeasureASN.unit
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_purchase_orders(inbound_po)
        .with_grn(grn)
        .with_buyer_sku(buyer_sku)
        .with_warehouses(warehouse)
        .with_purchasing_categories(purchasing_category)
        .with_po_acknowledgement_line_items(po_acknowledgement)
        .with_advance_shipping_notice(advance_shipping_notice)
        .with_skus(sku)
        .with_po_shipment(po_shipment)
    ):
        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, warehouse=warehouse)
        api_response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[warehouse.code], element_index=0
        )

        po_status_page = PoStatusViewPage(page=page)
        po_status_page.open()
        po_status_page.choose_item_from_brand_dropdown(item_name="Warehouses")
        table_data = po_status_page.get_po_status_table_data()[0]
        po_status_page.check_po_status_column_headers(is_warehouse=True)

        assert table_data.supplier == api_response_data["supplier"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.sku_code == api_response_data["sku"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.category == api_response_data["category"]
        assert table_data.purchasing_uom == api_response_data["purchasingUnit"]
        assert table_data.po_status == api_response_data["poStatus"]
        assert table_data.scheduled_delivery_date == api_response_data["scheduledDeliveryDate"]
        assert table_data.receiving_variance_in_units == ui_utils.comma_separated_str(
            api_response_data["receiveVariance"]
        )
        assert table_data.order_size == str(api_response_data["orderSize"])
        assert table_data.order_unit == api_response_data["orderUnit"]
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(api_response_data["casePrice"])
        assert table_data.case_size == str(api_response_data["caseSize"])
        assert table_data.case_size_received == ui_utils.comma_separated_str(api_response_data["caseSizeReceived"])
        assert table_data.quantity_ordered == ui_utils.comma_separated_str(api_response_data["quantityOrdered"])
        assert table_data.quantity_received == ui_utils.comma_separated_str(api_response_data["quantityReceived"])
        assert table_data.cases_received == str(api_response_data["casesReceived"])
        assert table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.total_price == ui_utils.comma_separated_str_with_dollar(api_response_data["totalPrice"])
        assert table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response_data["totalPriceReceived"]
        )
        assert table_data.emergency_reason == api_response_data["emergencyReason"]
        assert table_data.po_buyer == api_response_data["poBuyer"]
        assert table_data.assigned_buyer == api_response_data["buyers"]
        assert table_data.ship_method == api_response_data["shipMethod"]
        assert table_data.proposed_quantity_in_cases == str(api_response_data["proposedQuantityCases"])
        assert table_data.proposed_in_unit_case == str(api_response_data["proposedUnitsPerCase"])
        assert table_data.proposed_quantity_in_units == ui_utils.comma_separated_str(
            api_response_data["proposedQuantityUnits"]
        )
        assert table_data.proposed_delivery_date == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["proposedDeliveryDate"],
            input_date_format=DATE_TIME_FORMAT_4,
            output_date_format=DATE_FORMAT_1,
        )
        assert table_data.shipment_date == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["asnShipmentDate"],
            input_date_format=DATE_FORMAT_2,
            output_date_format=DATE_FORMAT_1,
        )
        assert table_data.planned_delivery_time == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["asnPlannedDeliveryTime"],
            input_date_format=DATE_FORMAT_2,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.shipped_quantity_in_cases == str(api_response_data["asnShippedQuantityCases"])
        assert table_data.units_of_measure == api_response_data["asnUnitsOfMeasure"]
        assert table_data.case_count == str(api_response_data["asnCaseCount"])
        assert table_data.shipped_quantity_in_units == str(api_response_data["asnShippedQuantityUnits"])

        assert table_data.load_number == api_response_data["loadNumber"]
        assert table_data.carrier == api_response_data["carrierName"]
        assert table_data.origin_location == api_response_data["originLocation"]
        assert table_data.pallet_count == str(api_response_data["palletCount"])
        assert table_data.appointment_time == datetime_utils.reformat_datetime_str(
            api_response_data["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )

        po_status_page.enable_disable_multi_week_toggle(set_check=True)
        po_status_page.choose_week_range(week_from=PREVIOUS_WEEK, week_to=NEXT_WEEK)
        week_dc_table_data = po_status_page.get_po_status_week_dc_table_data(is_multi_week_enabled=True)[0]
        assert week_dc_table_data.dc == warehouse.code
        assert week_dc_table_data.week == CURRENT_WEEK


def test_pimt_po_status_different_shipped_order_qty_filter(page: Page):
    """
    Test Different shipped and order quantity filter.
    ASN Shipped Quantity(units) ≠ PO Quantity or the ASN Planned Delivery Time > the PO Scheduled Delivery date

    Test steps:
    1.Generate require data
    2.Open PIMT Po Status page
    3.Open general menu filter on Po Status column
    4.Open filters tab and check Different shipped and order quantity
    5.Check that after filtering on page present 2 records
    """
    warehouse_1, warehouse_2, warehouse_3 = Warehouses.random_non_repetitive_values(qty=3)
    warehouse_1.receiving_type = warehouse_2.receiving_type = warehouse_3.receiving_type = WhReceivingType.E2OPEN_GRN
    sku_1, sku_2, sku_3 = TestSku.generate_skus(sku_quantity=3)
    po_warehouse_1 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_1, sku=sku_1)
    po_warehouse_2 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_2, sku=sku_2)
    po_warehouse_3 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_3, sku=sku_3)
    grn = [
        TestGrn.generate_grn(po=po_warehouse_1, warehouse=warehouse_1),
        TestGrn.generate_grn(po=po_warehouse_2, warehouse=warehouse_2),
        TestGrn.generate_grn(po=po_warehouse_3, warehouse=warehouse_3),
    ]
    # need to set size to satisfy condition ASN Shipped Quantity(units) ≠ PO Quantity
    advance_shipping_notice_po_1 = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_warehouse_1, size=po_warehouse_1.first_line().qty + data_generator.random_int()
    )
    # need to set planned_delivery_time to satisfy condition ASN Planned Delivery Time>Scheduled Delivery
    advance_shipping_notice_po_2 = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_warehouse_2, planned_delivery_time=CURRENT_DATE + timedelta(days=2)
    )
    with (
        TestData.root_data()
        .with_purchase_orders(po_warehouse_1, po_warehouse_2, po_warehouse_3)
        .with_grn(*grn)
        .with_warehouses(warehouse_1, warehouse_2, warehouse_3)
        .with_advance_shipping_notice(advance_shipping_notice_po_1, advance_shipping_notice_po_2)
        .with_skus(sku_1, sku_2, sku_3)
    ):
        po_status_page = PoStatusViewPage(page=page)
        po_status_page.open()
        po_status_page.choose_item_from_brand_dropdown(item_name=WAREHOUSES_BRAND)
        po_status_page.open_general_menu_filter_by_column_name(column_name="PO Status")
        po_status_page.open_filter_and_check_filter_by_name(filter_name=DIFFERENT_SHIPPED_AND_ORDER_QUANTITY)
        table_data = po_status_page.get_po_status_table_data()
        assert len(table_data) == 2


def test_pimt_po_status_case_and_delivery_time_discrepancy_filter(page: Page):
    """
    Test Case and Delivery time discrepancy filter.
    At first, it`s needed to make sure that conditions for red filter is not satisfied.
    ASN Case Count ≠ PO Case Size or the ASN Planned Delivery Time < PO Scheduled Delivery
    or the PO Status = Partially Shipped

    Test steps:
    1.Generate require data
    2.Open PIMT -> Po Status page
    3.Open general menu filter on Po Status column
    4.Open filters tab and check Case and delivery time discrepancy
    5.Check that after filtering on page present 3 records
    """
    warehouse_1, warehouse_2, warehouse_3, warehouse_4 = Warehouses.random_non_repetitive_values(qty=4)
    warehouse_1.receiving_type = warehouse_2.receiving_type = warehouse_3.receiving_type = (
        warehouse_4.receiving_type
    ) = WhReceivingType.E2OPEN_GRN
    sku_1, sku_2, sku_3, sku_4 = TestSku.generate_skus(sku_quantity=4)
    po_warehouse_1 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_1, sku=sku_1)
    po_warehouse_2 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_2, sku=sku_2)
    po_warehouse_3 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_3, sku=sku_3)
    po_warehouse_4 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse_4, sku=sku_4)
    grn = [
        TestGrn.generate_grn(po=po_warehouse_1, warehouse=warehouse_1),
        TestGrn.generate_grn(po=po_warehouse_2, warehouse=warehouse_2),
        TestGrn.generate_grn(po=po_warehouse_3, warehouse=warehouse_3),
        TestGrn.generate_grn(po=po_warehouse_4, warehouse=warehouse_4),
    ]
    advance_shipping_notice_po_1 = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_warehouse_1,
        unit_of_measure=UnitMeasureASN.unit,
        size=po_warehouse_1.first_line().qty,
        packing_size=po_warehouse_1.first_line().case_size + data_generator.random_int(),
    )
    advance_shipping_notice_po_2 = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_warehouse_2,
        unit_of_measure=UnitMeasureASN.unit,
        size=po_warehouse_2.first_line().qty,
        planned_delivery_time=CURRENT_DATETIME - timedelta(days=2),
    )
    advance_shipping_notice_po_3 = TestAdvanceShippingNotice.generate_advance_shipping_notice(
        po=po_warehouse_3,
        shipping_state=AdvanceShippingNoticeState.SHIPPING_STATE_PARTIALLY_SHIPPED,
        unit_of_measure=UnitMeasureASN.unit,
        size=po_warehouse_3.first_line().qty,
    )
    with (
        TestData.root_data()
        .with_purchase_orders(po_warehouse_1, po_warehouse_2, po_warehouse_3, po_warehouse_4)
        .with_grn(*grn)
        .with_warehouses(warehouse_1, warehouse_2, warehouse_3, warehouse_4)
        .with_advance_shipping_notice(
            advance_shipping_notice_po_1, advance_shipping_notice_po_2, advance_shipping_notice_po_3
        )
        .with_skus(sku_1, sku_2, sku_3, sku_4)
    ):
        po_status_page = PoStatusViewPage(page=page)
        po_status_page.open()
        po_status_page.choose_item_from_brand_dropdown(item_name=WAREHOUSES_BRAND)
        po_status_page.open_general_menu_filter_by_column_name(column_name="PO Status")
        po_status_page.open_filter_and_check_filter_by_name(filter_name=CASE_AND_DELIVERY_TIME_DISCREPANCY)
        table_data = po_status_page.get_po_status_table_data()
        assert len(table_data) == 3


def test_pimt_po_status_case_discrepancy_filter(page: Page):
    """
    Test Case Size Discrepancy filter. The filter is applicable only for Received Statuses (Received - Accurate,
    Received - Over, Received - Under). The filter is added to filter mismatch between case sizes

    Test steps:
    1.Generate required data (warehouse with e2open_grn type, the data for creating is sent status, received
    accurate, received over and received under)
    2.Open Po status page
    3.Open Case Size Received burger filter, go to filters on burger popup
    4.Click on the Case Size Discrepancy filter
    5.Check there are 3 records(with Received - Accurate,Received - Over, Received - Under) instead of 4 as po with
    Is Sent status should be filtered out
    6.Check that values in Case Size Received color have orange background color which also indicates that the filter
    has been applied
    """
    warehouse = Warehouses.random_value()
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    po_is_sent, po_received_accurate, po_received_under, po_received_over = TestPurchaseOrder.generate_inbound_pos(
        sku=sku, warehouse=warehouse, pos_quantity=4
    )
    grn_received_accurate = TestGrn.generate_grn(
        po=po_received_accurate,
        warehouse=warehouse,
        units_received=po_received_accurate.first_line().qty,
        cases_received=po_received_accurate.first_line().qty - data_generator.random_int(3),
    )
    grn_received_under = TestGrn.generate_grn(
        po=po_received_under,
        warehouse=warehouse,
        units_received=po_received_under.first_line().qty - data_generator.random_int(),
    )
    grn_received_over = TestGrn.generate_grn(
        po=po_received_over,
        warehouse=warehouse,
        units_received=po_received_over.first_line().qty + data_generator.random_int(),
    )
    with (
        TestData.root_data()
        .with_purchase_orders(po_received_accurate, po_received_under, po_received_over, po_is_sent)
        .with_warehouses(warehouse)
        .with_skus(sku)
        .with_grn(grn_received_accurate, grn_received_under, grn_received_over)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()
        po_status_view_page.choose_item_from_brand_dropdown(item_name=WAREHOUSES_BRAND)
        po_status_view_page.open_general_menu_filter_by_column_name(column_name="Case Size Received")
        po_status_view_page.open_filter_and_check_filter_by_name(filter_name=CASE_SIZE_DISCREPANCY)
        table_data = po_status_view_page.get_po_status_table_data()
        assert len(table_data) == 3
        for row in range(len(table_data)):
            po_status_view_page.check_color_case_size_received_flag(row_index=row, expected_color=Colors.DARK_ORANGE)


def test_transfer_order_dropdown_on_pimt_po_status_view(page: Page):
    """
    Test TO dropdown on PIMT PO Status View.
    The dropdown is present when the supplier value is “[Many Suppliers]”.

    Test steps:
    1. Generate required data (warehouse, sku, po, transfer orders and suppliers for transfer order)
    2. Open Po Status View Page
    3. Choose 'Warehouses' from brand dropdown
    3. Expand TO dropdown and extract table data
    4. Check that values and headers in TO dropdown are equal to expected
    """
    warehouse, original_wh = Warehouses.random_non_repetitive_values(2)
    warehouse.receiving_type = WhReceivingType.E2OPEN_GRN
    original_wh.receiving_type = WhReceivingType.E2OPEN_GRN
    sku = TestSku.generate_sku()
    # bob_code in po number should match with bob_code in warehouse
    original_pos = TestPurchaseOrder.generate_pos(quantity=2, warehouse=original_wh, sku=sku)
    transfer_order = TestPurchaseOrder.generate_transfer_order(original_pos=original_pos, warehouse=warehouse)
    grn = TestGrn.generate_grn(po=transfer_order, warehouse=warehouse, cases_received=data_generator.random_int())
    with (
        TestData.root_data()
        .with_purchase_orders(transfer_order, *original_pos)
        .with_skus(sku)
        .with_warehouses(warehouse)
        .with_grn(grn)
    ):
        po_status_view_page = PoStatusViewPage(page=page)
        po_status_view_page.open()
        po_status_view_page.choose_item_from_brand_dropdown(item_name=WAREHOUSES_BRAND)
        po_status_view_page.open_to_dropdown_by_po_number(po=transfer_order)
        po_status_view_page.check_to_dropdown_column_headers()

        api_response = ImtPoStatusClient().get_to_dropdown_data(po=transfer_order)
        api_response_data = base_steps.process_response_and_extract_data(response=api_response, expected_length=2)
        table_data = po_status_view_page.get_to_status_dropdown_table_data()
        assert len(table_data) == 2
        actual_to_table_data = {to_data.supplier: to_data for to_data in table_data}
        actual_to_api_response_data = {api_data["supplier"]: api_data for api_data in api_response_data}
        for po in original_pos:
            actual_data = actual_to_table_data[po.supplier_name]
            expected_data = actual_to_api_response_data[po.supplier_name]
            assert actual_data.supplier == expected_data["supplier"]
            comparisons.assert_numbers_equal(actual_data.order_size, expected_data["orderSize"])
            comparisons.assert_numbers_equal(actual_data.case_size, expected_data["caseSize"])
            comparisons.assert_numbers_equal(actual_data.quantity_ordered, expected_data["quantityOrdered"])
            assert actual_data.order_unit == expected_data["orderUnit"]
            assert actual_data.case_price == ui_utils.comma_separated_str_with_dollar(expected_data["casePrice"])
            assert actual_data.total_price == ui_utils.comma_separated_str_with_dollar(expected_data["totalPrice"])
