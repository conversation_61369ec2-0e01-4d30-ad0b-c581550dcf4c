from datetime import timed<PERSON>ta

import pytest
from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    Colors,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION,
    CURRENT_WEEK_FIRST_DAY,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    NEXT_WEEK,
    WEDNESDAY_PM,
)
from automated_tests.data.models.buyer_sku import Test<PERSON>uyer<PERSON>ku
from automated_tests.data.models.forecasts import Test<PERSON>car
from automated_tests.data.models.highjump import TestHJPackagingPalletSnapshot, TestHJReceipts
from automated_tests.data.models.packaging_demand import TestPackagingDemand
from automated_tests.data.models.packaging_override import TestPackagingOverride
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.packaging_depletion_page import PackagingDepletionPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.api_steps.imt import packaging_depletion_steps
from automated_tests.services.api.imt.packaging_depletion import PackagingDepletionClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.managers.depletion.constants import SUPPLEMENT_NEEDED


def test_main_values(page: Page):
    """
    Test values in Ingredient Summary, Status, Daily Needs and Weekly Overview sections on Packaging Depletion Dashboard

    Test steps:
    1.Generate required test data
    2.Open packaging depletion page
    3.Make API call and extract data from the response
    4.Expand Ingredient Summary section and first day of the week (Wednesday PM)
    5.Extract table data
    6.Check if the table data matches the expected values from the response
    7.Edit On Hand Override and Incoming Po Override values to 0 and check that when the user enters 0, the cell reverts
     back to a blank value.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, sku=sku, site_config=site_config)
    commodity_group = data_generator.generate_string()
    packaging_snapshot_wednesday_pm = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config, sku=sku, snapshot_date=CURRENT_WEEK_FIRST_DAY
    )
    packaging_snapshot_wednesday_am = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config, sku=sku, snapshot_date=CURRENT_WEEK_FIRST_DAY + timedelta(days=7)
    )
    packaging_override = TestPackagingOverride.generate_packaging_override(
        site_config=site_config, sku=sku, day=CURRENT_WEEK_FIRST_DAY
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_purchasing_categories(purchasing_category)
        .with_packaging_demand(packaging_demand)
        .with_buyer_sku(buyer_sku)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
        .with_hj_palette_snapshot(packaging_snapshot_wednesday_am, packaging_snapshot_wednesday_pm)
        .with_packaging_override(packaging_override)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        api_response_weekly_overview = api_response["weeklyOverview"]
        api_response_daily_needs = api_response["dailyNeeds"][0]

        ingredient_summary_table_data = packaging_depletion_page.get_ingredient_summary_table_data()[0]
        center_table_data = packaging_depletion_page.get_packaging_depletion_center_data()[0]

        assert ingredient_summary_table_data.sku == api_response["sku"]
        assert ingredient_summary_table_data.sku_name == api_response["skuName"]
        assert ingredient_summary_table_data.category == api_response["category"]
        assert ingredient_summary_table_data.buyer == api_response["buyer"]
        assert ingredient_summary_table_data.commodity_group == api_response["commodityGroup"]

        assert center_table_data.supplement_need == api_response["supplementNeed"]
        assert center_table_data.units_shorts == ui_utils.comma_separated_str(api_response["unitsShort"])

        assert center_table_data.wednesday_pm_on_hand_hj == ui_utils.comma_separated_str(
            api_response_daily_needs["onHand"]
        )
        assert center_table_data.wednesday_pm_on_hand_override == str(api_response_daily_needs["onHandOverride"])
        assert center_table_data.wednesday_pm_on_hand_hj_min_on_hand_projected == ui_utils.comma_separated_str(
            api_response_daily_needs["onHandMinOnHandProjected"]
        )
        assert center_table_data.wednesday_pm_on_hand_projected == ui_utils.comma_separated_str(
            api_response_daily_needs["onHandProjected"]
        )
        assert center_table_data.wednesday_pm_demand == ui_utils.comma_separated_str(api_response_daily_needs["demand"])
        assert center_table_data.wednesday_pm_incoming_po == ui_utils.comma_separated_str(
            api_response_daily_needs["incoming"]
        )
        assert center_table_data.wednesday_pm_incoming_po_override == str(api_response_daily_needs["incomingOverride"])
        assert center_table_data.wednesday_pm_eod_inventory == ui_utils.comma_separated_str(
            api_response_daily_needs["eodInventory"]
        )
        assert center_table_data.wednesday_pm_status == api_response_daily_needs["status"]
        assert center_table_data.total_weeks_demand == ui_utils.comma_separated_str(
            api_response_weekly_overview["demand"]
        )
        assert center_table_data.start_week_inventory == ui_utils.comma_separated_str(
            api_response_weekly_overview["startWeekInventory"]
        )
        assert center_table_data.units_ordered == ui_utils.comma_separated_str(
            api_response_weekly_overview["unitsOrdered"]
        )
        assert center_table_data.units_received == ui_utils.comma_separated_str(
            api_response_weekly_overview["unitsReceived"]
        )
        assert center_table_data.end_of_week_inventory == ui_utils.comma_separated_str(
            api_response_weekly_overview["eowInventory"]
        )
        assert center_table_data.overconsumption == ui_utils.comma_separated_str(
            api_response_weekly_overview["overconsumption"]
        )

        # we edit the field for Wednesday PM, that's why day_index = 0
        value = str(0)
        packaging_depletion_page.add_edit_on_hand_override_value(day_index=0, value=value)
        packaging_depletion_page.add_edit_incoming_po_override_value(day_index=0, value=value)
        center_table_data = packaging_depletion_page.get_packaging_depletion_center_data()[0]
        assert center_table_data.wednesday_pm_on_hand_override == value
        assert center_table_data.wednesday_pm_incoming_po_override == value


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_column_headers(page: Page, receiving_type):
    """
    Test column headers with expanded columns and in consolidated view on Packaging Depletion Dashboard
    If ReceiveInputType == HIGH_JUMP: THEN the first column for days in daily needs should be On Hand HJ.
    If ReceiveInputType == MANUAL: THEN the first column for days in daily needs should be On Hand Override.
    In consolidated view the first column for days in daily needs should be On Hand Override.

    Test steps:
    1.Generate required test data
    2.Open packaging depletion page
    3.Expand all columns
    4.Extract column headers
    5.Verify if columns headers are equal to expected column headers
    6.Make active the consolidated view
    7.Verify if columns headers are equal to expected column headers
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = receiving_type
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        packaging_depletion_page.check_packaging_depletion_column_headers(site_config=site_config)

        packaging_depletion_page.enable_disable_brand_consolidated_view(consolidated=True)
        packaging_depletion_page.check_packaging_depletion_column_headers(is_consolidated=True)


def test_liner_consolidation_values(page: Page):
    """
    Test values in Ingredient Summary, Status, Daily Needs and Weekly Overview sections on Packaging Depletion Dashboard
    for Liner Consolidation

    Test steps:
    1.Generate required test data
    2.Open packaging depletion page
    3.Make API call and extract data from the response
    4.Expand Ingredient Summary section and first day of the week (Wednesday PM)
    5.Extract table data
    6.Check if the table data matches the expected values from the response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku_name = packaging_depletion_steps.random_sku_name_for_liners()
    sku_with_part_a = TestSku.generate_sku(sku_name=sku_name + " Part A")
    sku_with_part_b = TestSku.generate_sku(sku_name=sku_name + " Part B")
    sku_non_parts = TestSku.generate_sku(sku_name=sku_name)
    skus = [sku_with_part_a, sku_with_part_b, sku_non_parts]
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config) for sku in skus]
    packaging_demands = [
        TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config) for sku in skus
    ]
    packaging_snapshot_wednesday_pm = [
        TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
            site_config=site_config, sku=sku, snapshot_date=CURRENT_WEEK_FIRST_DAY
        )
        for sku in skus
    ]
    user = Users.test_user.value
    buyer_sku = [TestBuyerSku.generate_buyer_sku(user=user, sku=sku, site_config=site_config) for sku in skus]
    purchasing_category = [
        TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value()) for sku in skus
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_buyer_sku(*buyer_sku)
        .with_skus(*skus)
        .with_purchase_orders(*pos)
        .with_packaging_demand(*packaging_demands)
        .with_hj_palette_snapshot(*packaging_snapshot_wednesday_pm)
        .with_purchasing_categories(*purchasing_category)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        api_response_daily_needs = api_response["dailyNeeds"][0]

        ingredient_summary_table_data = packaging_depletion_page.get_ingredient_summary_table_data()[0]
        center_table_data = packaging_depletion_page.get_packaging_depletion_center_data()[0]

        assert ingredient_summary_table_data.sku == ""
        assert ingredient_summary_table_data.sku_name.replace("\n(3)", "") == api_response["skuName"]
        assert ingredient_summary_table_data.category == api_response["category"]
        assert ingredient_summary_table_data.commodity_group == api_response["commodityGroup"]
        assert ingredient_summary_table_data.buyer == api_response["buyer"]

        assert center_table_data.supplement_need == api_response["supplementNeed"]
        assert center_table_data.units_shorts == ""

        assert center_table_data.wednesday_pm_on_hand_hj == ui_utils.comma_separated_str(
            api_response_daily_needs["onHand"]
        )
        assert center_table_data.wednesday_pm_on_hand_override == ""
        assert center_table_data.wednesday_pm_on_hand_projected == ui_utils.comma_separated_str(
            api_response_daily_needs["onHandProjected"]
        )
        assert center_table_data.wednesday_pm_on_hand_hj_min_on_hand_projected == ui_utils.comma_separated_str(
            api_response_daily_needs["onHandMinOnHandProjected"]
        )
        assert center_table_data.wednesday_pm_demand == ui_utils.comma_separated_str(api_response_daily_needs["demand"])
        assert center_table_data.wednesday_pm_incoming_po == ui_utils.comma_separated_str(
            api_response_daily_needs["incoming"]
        )
        assert center_table_data.wednesday_pm_incoming_po_override == ""
        assert center_table_data.wednesday_pm_eod_inventory == ui_utils.comma_separated_str(
            api_response_daily_needs["eodInventory"]
        )
        assert center_table_data.wednesday_pm_status == api_response_daily_needs["status"]

        weekly_overview_table_data = [
            center_table_data.total_weeks_demand,
            center_table_data.start_week_inventory,
            center_table_data.units_ordered,
            center_table_data.units_received,
            center_table_data.end_of_week_inventory,
            center_table_data.overconsumption,
        ]

        assert all(weekly_overview == "" for weekly_overview in weekly_overview_table_data)


def test_packaging_depletion_po_dropdown(page: Page):
    """
    Test PO dropdown on Packaging Depletion Dashboard

    Test steps:
    1.Generate required test data
    2.Open packaging depletion page
    3.Make API call and extract data from the response
    4.Open dropdown clicking on sku arrow button
    5.Extract column headers
    6.Verify if columns headers are equal to expected column headers
    7.Extract table data
    8.Check that table data are equal to expected data from response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config,
        sku=sku,
        delivery_time_start=CURRENT_DATE,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
        .with_po_shipment(po_shipment)
        .with_hj_receipts(hj_receipt)
        .with_oscar_forecast(oscar)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()
        packaging_depletion_page.open_dropdown_by_sku(sku=sku.sku_code)
        packaging_depletion_page.check_po_dropdown_column_headers()

        response = PackagingDepletionClient().get_packaging_depletion_po_status(
            site_config=site_config, week=CURRENT_WEEK, sku=sku
        )
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        dropdown_table_data = packaging_depletion_page.get_po_dropdown_table_data()[0]

        assert dropdown_table_data.supplier == api_response["supplier"]
        assert dropdown_table_data.po_number == api_response["poNumber"]
        assert dropdown_table_data.sku_code == api_response["sku"]
        assert dropdown_table_data.sku_name == api_response["skuName"]
        assert dropdown_table_data.scheduled_delivery_date == api_response["scheduledDeliveryDate"]
        assert dropdown_table_data.po_status == api_response["poStatus"]
        assert dropdown_table_data.receiving_variance_in_units == ui_utils.comma_separated_str(
            api_response["receiveVariance"]
        )
        assert dropdown_table_data.appointment_time == datetime_utils.reformat_datetime_str(
            date_string=api_response["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert dropdown_table_data.order_size == str(api_response["orderSize"])
        assert dropdown_table_data.case_price == ui_utils.comma_separated_str_with_dollar(api_response["casePrice"])
        assert dropdown_table_data.case_size == ui_utils.comma_separated_str(api_response["caseSize"])
        assert dropdown_table_data.quantity_ordered == ui_utils.comma_separated_str(api_response["quantityOrdered"])
        assert dropdown_table_data.quantity_received == ui_utils.comma_separated_str(api_response["quantityReceived"])
        assert dropdown_table_data.cases_received == ui_utils.comma_separated_str(api_response["casesReceived"])
        assert dropdown_table_data.case_size_received == ui_utils.comma_separated_str(api_response["caseSizeReceived"])
        assert dropdown_table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert dropdown_table_data.total_price == ui_utils.comma_separated_str_with_dollar(api_response["totalPrice"])
        assert dropdown_table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response["totalPriceReceived"]
        )
        assert dropdown_table_data.emergency_reason == api_response["emergencyReason"]
        assert dropdown_table_data.ship_method == api_response["shipMethod"]
        assert dropdown_table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            api_response["forecastDeliveryPercent"]
        )


def test_pink_flag_for_on_hand_actual_and_projected(page: Page):
    """
    Test case checks On Hand(Actual) and On Hand(Projected) values are highlighted in pink for Production Day when EOD
    of Production day+2 is negative. Here Production day is set for Wednesday.
    Test steps:
    1. Generate required test data
    2. Set up test data using TestData context manager
    3. Open packaging depletion page
    4. Make an API call and extract data from the response
    5. Check EOD inventory is negative for Production day + 2
    6. Check On Hand(Actual) and On Hand(Projected) value for Production day are highlighted in pink.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    packaging_demand = TestPackagingDemand.generate_packaging_demand(sku=sku, site_config=site_config)
    packaging_snapshot_wednesday = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config,
        sku=sku,
        snapshot_date=CURRENT_WEEK_FIRST_DAY,
        pallet_quantity=packaging_demand.demand_by_day[0],
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_packaging_demand(packaging_demand)
        .with_hj_packaging_palette_snapshot(packaging_snapshot_wednesday)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response_daily_needs_friday = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["dailyNeeds"][2]

        assert api_response_daily_needs_friday["eodInventory"] < 0
        packaging_depletion_page.check_on_hand_actual_and_projected_flag_colors(day_index=0, expected_color=Colors.PINK)


def test_supplement_need_pink_flag(page: Page):
    """
    Test case checks Supplement Needs value is highlighted in pink for Production Day when status
    of Production day+2 is Supplement need and this Production Day haven't passed yet.
    In this test Production day is set for Wednesday.
    Test steps:
    1. Generate required test data -don't add po and snapshots to have on hand and incoming POs == 0.
    Also, we should set the demand for the production day + 2
    2. Set up the test data using TestData context manager
    3. Open packaging depletion page
    4. Make an API call and extract data from the response
    5. Check EOD inventory is negative for Production day + 2
    6. Check Supplement Need value is highlighted in pink and equals to the production day
    """
    # we need to add a site config for current week, otherwise UI will hang
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku, site_config=site_config, demand_by_day=[0, 0, 1, 0, 0, 0, 0, 0], week=NEXT_WEEK
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_packaging_demand(packaging_demand)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()
        packaging_depletion_page.choose_week_from_dropdown(week=NEXT_WEEK)

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=NEXT_WEEK)
        daily_needs_data_friday = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )["dailyNeeds"][2]
        assert daily_needs_data_friday["demand"] > 0
        assert daily_needs_data_friday["status"] == SUPPLEMENT_NEEDED
        packaging_depletion_page.check_supplement_need_flag_color_and_text(
            expected_color=Colors.PINK, expected_text=WEDNESDAY_PM
        )


def test_liner_consolidation_yellow_on_hand_highlight(page: Page):
    """
    Test case checks On hand values are highlighted in yellow for Supplement need day
    when EOD for Supplement need day is > 0
    In this test Supplement need day is set for current day.
    Test steps:
    1. Generate required test data - add liner sku, po, snapshots and demand for the current day.
    2. Set up the test data using TestData context manager
    3. Open packaging depletion page
    4. Make an API call and extract data from the response
    5. Check EOD inventory is positive for Supplement need day
    6. Check On Hand (Actual), On Hand (Projected) and On Hand (Actual) - On Hand (Projected)
    values are highlighted in yellow
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku(sku_name=packaging_depletion_steps.random_sku_name_for_liners())
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME)
    pallet_snapshot = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config, sku=sku, snapshot_date=CURRENT_DATE
    )
    current_day_index = datetime_utils.get_current_date_index()
    demand_by_day = [0 for _ in range(8)]
    demand_by_day[current_day_index] = pallet_snapshot.pallet_quantity + data_generator.random_int()
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku, site_config=site_config, demand_by_day=demand_by_day
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
        .with_hj_packaging_palette_snapshot(pallet_snapshot)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        daily_needs_current_day = api_response["dailyNeeds"][current_day_index]
        assert daily_needs_current_day["eodInventory"] > 0
        computed_on_hand_current_day = packaging_depletion_steps.get_computed_on_hand(
            daily_needs_current_day["onHandOverride"],
            daily_needs_current_day["onHand"],
            daily_needs_current_day["onHandProjected"],
        )
        assert computed_on_hand_current_day < daily_needs_current_day["demand"]

        packaging_depletion_page.expand_collapse_header(CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION[current_day_index])
        packaging_depletion_page.check_all_on_hand_flag_colors(
            day_index=current_day_index, expected_color=Colors.YELLOW
        )


@pytest.mark.parametrize(
    "on_hand_override, incoming_override",
    [
        pytest.param(data_generator.random_int(3), data_generator.random_int(3), id="overrides_more_than_0"),
        pytest.param(0, 0, id="overrides_equal_0"),
    ],
)
def test_orange_on_hand_incoming_highlight(page: Page, on_hand_override, incoming_override):
    """
    Test case checks On hand and Incoming values are highlighted in orange for the day
    when EOD is >0 and Override values are not Null
    Test steps:
    1. Generate required test data - add sku, po, snapshots and demand for the current day.
    2. Set up the test data using TestData context manager
    3. Open packaging depletion page
    4. Make an API call and extract data from the response
    5. Check EOD inventory and overrides are positive for Supplement need day
    6. Check following values are highlighted in yellow-orange: On Hand(Actual), On Hand(Projected),
    On Hand(Override), On Hand(HJ)/On Hand(Projected), Incoming POs, Incoming POs(Override)
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME)
    pallet_snapshot = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config, sku=sku, snapshot_date=CURRENT_DATE
    )
    current_day_index = datetime_utils.get_current_date_index()
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku, site_config=site_config, demand_by_day=[0] * 8
    )
    packaging_override = TestPackagingOverride.generate_packaging_override(
        site_config=site_config, sku=sku, on_hand_override=on_hand_override, incoming_override=incoming_override
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
        .with_hj_packaging_palette_snapshot(pallet_snapshot)
        .with_packaging_override(packaging_override)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        daily_needs_current_day = api_response["dailyNeeds"][current_day_index]
        assert daily_needs_current_day["eodInventory"] >= 0
        assert daily_needs_current_day["onHandOverride"] >= 0
        assert daily_needs_current_day["incomingOverride"] >= 0
        assert api_response["dailyNeeds"][current_day_index + 1]["eodInventory"] >= 0
        if current_day_index + 2 < len(api_response["dailyNeeds"]):
            assert api_response["dailyNeeds"][current_day_index + 2]["eodInventory"] >= 0
        packaging_depletion_page.expand_collapse_header(CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION[current_day_index])
        packaging_depletion_page.check_all_hand_and_incoming_flag_colors(
            day_index=current_day_index, expected_color=Colors.YELLOW_ORANGE
        )


def test_red_on_hand_incoming_highlight(page: Page):
    """
    Test case checks On hand values are highlighted in red for the day
    when EOD is < 0
    Test steps:
    1. Generate required test data - add sku, po, snapshots and demand for the current day.
    2. Set up the test data using TestData context manager
    3. Open packaging depletion page
    4. Make an API call and extract data from the response
    5. Check EOD inventory is negative for Supplement need day
    6. Check following values are highlighted in yellow-orange:  Incoming POs, Incoming POs(Override)
    And current values are highlighted in red: On Hand(Actual), On Hand(Projected),
    On Hand(Override), On Hand(HJ)/On Hand(Projected),
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME)
    pallet_snapshot = TestHJPackagingPalletSnapshot.generate_packaging_snapshot(
        site_config=site_config, sku=sku, snapshot_date=CURRENT_DATE
    )
    current_day_index = datetime_utils.get_current_date_index()
    packaging_demand = TestPackagingDemand.generate_packaging_demand(
        sku=sku,
        site_config=site_config,
    )
    packaging_override = TestPackagingOverride.generate_packaging_override(
        site_config=site_config, sku=sku, on_hand_override=0, incoming_override=0
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_packaging_demand(packaging_demand)
        .with_hj_packaging_palette_snapshot(pallet_snapshot)
        .with_packaging_override(packaging_override)
    ):
        packaging_depletion_page = PackagingDepletionPage(page=page)
        packaging_depletion_page.open()

        response = PackagingDepletionClient().get_packaging_depletion(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )

        for daily_needs_data in api_response["dailyNeeds"]:
            assert daily_needs_data["eodInventory"] < 0

        packaging_depletion_page.expand_collapse_header(CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION[current_day_index])
        packaging_depletion_page.check_all_on_hand_flag_colors(
            day_index=current_day_index, expected_color=Colors.RED_ORANGE
        )
