from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import Colors, SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_5,
    NEXT_WEEK,
    TOMORROW_DATE,
)
from automated_tests.data.constants.error_messages_constants import INVALID_RECEIPT_INPUT_ERROR_MESSAGE
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.imt.forms.receipt_override_form_page import (
    ReceiptOverrideFormLocators,
    ReceiptOverridePage,
)
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.receipt_override_form import ReceiptOverrideClient
from automated_tests.utils import datetime_utils
from procurement.constants.hellofresh_constant import ReceiveInputType


def test_insert_receipt_override_and_delete_data(page: Page):
    """
    Test case checks all columns on IMT -> Receipt Override dashboard

    Test steps:
    1. Generate required test data with site_config, po, sku, grn, adjReceivedVolume, adjReceivedCases
    2. Open IMT -> Receipt Override page and add records
    3. Check column headers to be as expected
    4. Extract table data, make a get request and check that values meet
    5. Click on delete record button and check not data is shown.
    6. Check that ongoing week is highlighted
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        site_config=site_config, po=po, cases_received=data_generator.random_int()
    )
    adjReceivedVolume, adjReceivedCases = [data_generator.random_int() for _ in range(2)]
    received_date = TOMORROW_DATE
    comment = data_generator.generate_string()
    brand, dc = site_config.brand.code, site_config.site.code
    with (
        TestData(None)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_grn(grn)
    ):
        receipt_override_form_page = ReceiptOverridePage(page=page)
        receipt_override_form_page.open()
        receipt_override_form_page.click_add_records()
        form_locators_dict = {
            ReceiptOverrideFormLocators.BRAND_FIELD: brand,
            ReceiptOverrideFormLocators.SITE_FIELD: dc,
            ReceiptOverrideFormLocators.PO_NUMBER_FIELD: po.po_number,
            ReceiptOverrideFormLocators.SKU_CODE_FIELD: sku.sku_code,
            ReceiptOverrideFormLocators.ADJ_RECEIVED_VOLUME_FIELD: str(adjReceivedVolume),
            ReceiptOverrideFormLocators.ADJ_RECEIVED_DATE_FIELD: received_date.strftime(DATE_FORMAT_1),
            ReceiptOverrideFormLocators.ADJ_RECEIVED_CASES_FIELD: str(adjReceivedCases),
            ReceiptOverrideFormLocators.COMMENT_FIELD: comment,
        }
        receipt_override_form_page.add_records(form_locators_dict=form_locators_dict)
        receipt_override_form_page.validate_records()
        receipt_override_form_page.submit_records()
        receipt_override_form_page.check_receipt_override_column_headers()
        table_data = receipt_override_form_page.get_receipt_override_table_data()[0]
        api_response = ReceiptOverrideClient().get_receipt_override_data(week=CURRENT_WEEK)
        api_response_data = base_steps.process_response_and_extract_data(
            response=api_response, additional_keys=["data"], element_index=0
        )
        assert table_data.entry_type == api_response_data["entryType"]
        assert table_data.brand == api_response_data["brand"]
        assert table_data.site == api_response_data["dc"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.supplier_name == api_response_data["supplierName"]
        assert table_data.sku_code == api_response_data["skuCode"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.quantity_received == str(api_response_data["quantityReceived"])
        assert table_data.adj_received_volume == str(api_response_data["adjustedReceivedVolume"])
        assert table_data.difference_receipt_vs_adj == str(
            api_response_data["differenceReceiptVsAdj"]
        ), "Difference: Receipt vs. Adjusted should be equal to difference between actual and received inventory."
        assert table_data.cases_received == str(api_response_data["casesReceived"])
        assert table_data.adj_received_cases == str(api_response_data["adjustedReceivedCases"])
        assert table_data.case_difference_receipt_vs_adj == str(api_response_data["caseDifferenceReceiptVsAdj"])
        assert table_data.submitted_by == api_response_data["submittedBy"]
        assert table_data.timestamp == datetime_utils.reformat_datetime_str(
            api_response_data["timestamp"], DATE_TIME_FORMAT_1, DATE_TIME_FORMAT_5
        )
        assert table_data.receive_date == api_response_data["adjustedReceivingDate"]
        assert table_data.comment == api_response_data["comment"]
        receipt_override_form_page.delete_records()
        receipt_override_form_page.is_no_data_message_present()
        receipt_override_form_page.choose_week_from_dropdown(week=NEXT_WEEK)
        receipt_override_form_page.check_week_item_color_dropdown(week=CURRENT_WEEK, expected_color=Colors.CYAN)


def test_update_recorded_data(page: Page):
    """
    Test case checks editable adjustedReceivedVolume, adjustedReceivedCases and comment columns.

    Test steps:
    1. Generate required test data with site_config, sku, po, grn, receipt override
    2. Open IMT -> Receipt Override page (there is already generated record)
    3. Edit record by setting new values for adjustedReceivedVolume and adjustedReceivedCases columns
    4. Extract table data and check that the mentioned columns have been updated.
    5. Delete comment and check that comment was removed
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        site_config=site_config, po=po, cases_received=data_generator.random_int()
    )
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po)
    adjReceivedVolumePatch, adjReceivedCasesPatch = [data_generator.random_int() for _ in range(2)]
    received_date_patch = TOMORROW_DATE
    comment_patch = data_generator.generate_string()
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_grn(grn)
        .with_purchase_orders(po)
        .with_receipt_override(receipt_override)
    ):
        receipt_override_form_page = ReceiptOverridePage(page=page)
        receipt_override_form_page.open()
        edit_locators_dict = {
            ReceiptOverrideFormLocators.ADJ_RECEIVED_VOLUME_FIELD: str(adjReceivedVolumePatch),
            ReceiptOverrideFormLocators.ADJ_RECEIVED_CASES_FIELD: str(adjReceivedCasesPatch),
            ReceiptOverrideFormLocators.ADJ_RECEIVED_DATE_FIELD: received_date_patch.strftime(DATE_FORMAT_1),
            ReceiptOverrideFormLocators.COMMENT_FIELD: comment_patch,
        }
        api_response = ReceiptOverrideClient().get_receipt_override_data(week=CURRENT_WEEK)
        receipt_override_form_page.editing_records(edit_locators_dict=edit_locators_dict, response_url=api_response.url)
        table_data = receipt_override_form_page.get_receipt_override_table_data()[0]
        assert table_data.adj_received_volume == str(adjReceivedVolumePatch)
        assert table_data.adj_received_cases == str(adjReceivedCasesPatch)
        assert table_data.receive_date == received_date_patch.strftime(DATE_FORMAT_1)
        assert table_data.comment == comment_patch


def test_recording_duplicated_data(page: Page):
    """
    Test case checks error message appears when trying to add duplicate records.
    Test steps:
    1. Generate required test data with site_config, sku, po, grn
    2. Open IMT -> Received Receipt Override page
    3. Add 2 records with the same data - > INVALID_RECEIPT_INPUT_ERROR_MESSAGE should be seen
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        site_config=site_config, po=po, cases_received=data_generator.random_int()
    )
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_grn(grn)
        .with_purchase_orders(po)
    ):
        receipt_override_form_page = ReceiptOverridePage(page=page)
        receipt_override_form_page.open()
        receipt_override_form_page.click_add_records()
        form_locators_dict = {
            ReceiptOverrideFormLocators.BRAND_FIELD: site_config.brand.code,
            ReceiptOverrideFormLocators.SITE_FIELD: site_config.site.code,
            ReceiptOverrideFormLocators.PO_NUMBER_FIELD: po.po_number,
            ReceiptOverrideFormLocators.SKU_CODE_FIELD: sku.sku_code,
        }
        receipt_override_form_page.add_records(form_locators_dict=form_locators_dict, row_id=0)
        receipt_override_form_page.add_records(form_locators_dict=form_locators_dict, row_id=1)
        receipt_override_form_page.validate_records()
        actual_warning_message = receipt_override_form_page.get_submit_info_title()
        assert actual_warning_message == INVALID_RECEIPT_INPUT_ERROR_MESSAGE


def test_empty_record_values(page: Page):
    """
    Test case checks user can't validate record when having empty column.

    Test steps:
    1. Generate required test data with site_config, sku, po, grn
    2. Open IMT -> Received Receipt Override page
    3. Check every time user receives an error message when adding record with empty column.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    grn = TestGrn.generate_grn_by_site_config(
        site_config=site_config, po=po, cases_received=data_generator.random_int()
    )
    adjReceivedVolume = data_generator.random_int()
    with (
        TestData(None)
        .with_skus(sku)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_grn(grn)
    ):
        receipt_override_form_page = ReceiptOverridePage(page=page)
        receipt_override_form_page.open()
        form_locators_dict = {
            ReceiptOverrideFormLocators.ADJ_RECEIVED_VOLUME_FIELD: str(adjReceivedVolume),
            ReceiptOverrideFormLocators.BRAND_FIELD: site_config.brand.code,
            ReceiptOverrideFormLocators.SITE_FIELD: site_config.site.code,
            ReceiptOverrideFormLocators.PO_NUMBER_FIELD: po.po_number,
        }
        receipt_override_form_page.click_add_records()
        for form_locator, form_value in form_locators_dict.items():
            receipt_override_form_page.add_records(form_locators_dict={form_locator: form_value}, row_id=0)
            receipt_override_form_page.validate_records()
            actual_warning_message = receipt_override_form_page.get_submit_info_title()
            assert actual_warning_message == INVALID_RECEIPT_INPUT_ERROR_MESSAGE
            receipt_override_form_page.cancel_submit_records()
