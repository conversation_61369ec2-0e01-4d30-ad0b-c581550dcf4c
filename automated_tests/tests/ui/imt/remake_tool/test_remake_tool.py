import random
from datetime import timed<PERSON><PERSON>

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsFactor,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    REMAKE_TOOL_DAYS,
    REMAKE_TOOL_DAYS_FACTOR,
)
from automated_tests.data.models.buyer_sku import TestBuyer<PERSON>ku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJDiscard, TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient, TestRemakeTool
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.remake_tool_page import RemakeToolPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.services.api.imt.remake_tool_client import RemakeToolClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.constants.ordering import IN_PROGRESS_HJ
from procurement.data.models.highjump.highjump import DiscardType


def test_ingredient_summary_units_needed_status_sections_and_final_eod_inventory(page: Page):
    """
    Test Ingredient Summary, Units Needed and Status sections and Final End Of Week Inventory column.

    Test steps:
    1. Generate required data (needed data for set up all columns)
    2. Open Remake Tool Page, check column headers
    3. Get table data from Ingredient Summary, Units Needed, Status and last sections
    6. Make an API call -> 200
    7. Check values from table data meet appropriate API response data
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    commodity_group = data_generator.generate_string()
    purchasing_category = TestSkuCategory(sku, PurchasingCategories.random_value())
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(
        sku=sku, site_config=site_config, qty=random.randint(-100, 100)
    )
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(hybrid_needs)
        .with_oscar_forecast(oscar)
        .with_buyer_sku(buyer_sku)
        .with_purchasing_categories(purchasing_category)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
    ):
        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        remake_tool_page.check_remake_tool_column_headers()
        ing_summary_table_data = remake_tool_page.get_ingredient_summary_table_data()[0]
        center_table_data = remake_tool_page.get_remake_tool_center_table_data()[0]

        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]

        assert ing_summary_table_data.sku == actual_data["sku"]
        assert ing_summary_table_data.sku_name == actual_data["skuName"]
        assert ing_summary_table_data.category == actual_data["category"]
        assert ing_summary_table_data.commodity_group == actual_data["commodityGroup"]
        assert ing_summary_table_data.buyer == actual_data["buyers"]
        assert ing_summary_table_data.impacted_recipes == actual_data["impactedRecipes"]
        assert center_table_data.plan == str(actual_data["plan"])
        assert center_table_data.forecast_oscar == ui_utils.comma_separated_str(actual_data["forecastOscar"])
        assert center_table_data.delta == ui_utils.to_percent_string(actual_data["delta"])
        assert center_table_data.supplement_need == actual_data["supplementNeed"]
        assert center_table_data.final_eod_inventory == ui_utils.comma_separated_str(
            actual_data["finalEndOfWeekBuffer"]
        )


def test_remake_tool_weekly_section(page: Page):
    """
    Test Weekly section.

    Test steps:
    1. Generate required data (needed data for set up all columns)
    2. Open Remake Tool Page and extract table data from Weekly section
    4. Make an API call -> 200
    5. Check that table data meets with the API response data
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_awaiting = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    po_in_progress_hj = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME + timedelta(days=10)
    )
    po_not_delivered = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, delivery_time_start=CURRENT_DATETIME - timedelta(days=10)
    )
    po_autobagger = TestPurchaseOrder.generate_po_with_sku(
        sku=sku, site=site_config, supplier_name="Autobagger - " + site_config.site.code
    )
    po_received = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    pos = [po_awaiting, po_in_progress_hj, po_not_delivered, po_autobagger, po_received]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    remake_tool = TestRemakeTool.generate_remake_tool(site_config=site_config, mealkit=mealkit)
    next_week_oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK)
    inventory_pull_put = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku)
    hj_discard = TestHJDiscard.generate_discard(sku=sku, site_config=site_config, tran_type=DiscardType.DISCARD)
    discard = TestDiscard.generate_discard(site_config=site_config, sku=sku)
    receipt_override = TestReceiptOverride.generate_receipt_override(site_config=site_config, po=po_received)
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        sku=sku,
        site_config=site_config,
    )
    hj_receipt_in_progress_hj = TestHJReceipts.generate_hj_receipt(
        site_config=site_config,
        status=IN_PROGRESS_HJ,
        receipt_time_est=CURRENT_DATETIME - timedelta(days=1),
        po=po_in_progress_hj,
        quantity_received=0,
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_received)
    remake_day = random.choice(REMAKE_TOOL_DAYS)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_oscar_forecast(oscar, next_week_oscar)
        .with_hybrid_needs(hybrid_needs)
        .with_remake_tool(remake_tool)
        .with_hj_discard(hj_discard)
        .with_discard(discard)
        .with_inventory_pull_put(inventory_pull_put)
        .with_receipt_override(receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_hj_receipts(hj_receipt, hj_receipt_in_progress_hj)
    ):
        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        table_data = remake_tool_page.get_remake_tool_center_table_data()[0]

        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=remake_day)
        api_response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]["weekly"]
        assert table_data.remake_units == ui_utils.comma_separated_str(api_response_data["remakeUnits"])
        assert table_data.units_needed == ui_utils.comma_separated_str(api_response_data["unitsNeeded"])
        assert table_data.units_needed_plus_remakes == ui_utils.comma_separated_str(
            api_response_data["unitsNeededPlusRemake"]
        )
        assert table_data.units_ordered == ui_utils.comma_separated_str(api_response_data["unitsOrdered"])
        assert table_data.units_received == ui_utils.comma_separated_str(api_response_data["unitsReceived"])
        assert table_data.units_in_house_hj == ui_utils.comma_separated_str(api_response_data["unitsInHouseHJ"])
        assert table_data.row_need == ui_utils.comma_separated_str(api_response_data["rowNeed"])
        assert table_data.units_in_house_minus_row_need == ui_utils.comma_separated_str(
            api_response_data["unitsInHouseMinusRowNeed"]
        )
        assert table_data.next_week_forecast == ui_utils.comma_separated_str(api_response_data["nextWeekForecast"])
        assert table_data.hj_min_row_need_min_next_week_forecast == ui_utils.comma_separated_str(
            api_response_data["hjMinusRowNeedMinusForecast"]
        )
        assert table_data.units_scheduled_by_autobagger == ui_utils.comma_separated_str(
            api_response_data["unitsToProduceByAutobagger"]
        )
        assert table_data.inventory == ui_utils.comma_separated_str(api_response_data["inventory"])
        assert table_data.discards == ui_utils.comma_separated_str(api_response_data["discards"])
        assert table_data.pull == ui_utils.comma_separated_str(api_response_data["pulls"])
        assert table_data.total_on_hands == ui_utils.comma_separated_str(api_response_data["totalOnHand"])
        assert table_data.total_on_hand_minus_prod_need == ui_utils.comma_separated_str(
            api_response_data["onHandMinProductionNeeds"]
        )
        assert table_data.in_progress_hj == ui_utils.comma_separated_str(api_response_data["inProgressHj"])
        assert table_data.awaiting_delivery == ui_utils.comma_separated_str(api_response_data["awaitingDelivery"])
        assert table_data.not_delivered == ui_utils.comma_separated_str(api_response_data["notDelivered"])
        assert table_data.buffer_quantity == ui_utils.comma_separated_str(api_response_data["bufferQuantity"])
        assert table_data.buffer_percent == ui_utils.to_percent_string(api_response_data["bufferPercent"])


def test_remake_tool_po_dropdown(page: Page):
    """
    Test PO dropdown on Remake Tool dashboard.

    Test steps:
    1. Generate required data (needed data for set up all columns)
    2. Open Remake Tool Page and PO dropdown by sku
    3. Check all column headers are equals to expected
    4. Get table data from PO dropdown
    5. Make API call
    6. Check that table data meets with the API response
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    receiving = TestReceiving.generate_receiving(po=po, site_config=site_config)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_po_shipment(po_shipment)
        .with_hj_receipts(hj_receipt)
        .with_receiving(receiving)
        .with_oscar_forecast(oscar)
    ):
        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        remake_tool_page.open_dropdown_by_sku_code(sku_code=sku.sku_code)
        remake_tool_page.check_dropdown_column_headers_on_popup()

        response = ImtPoStatusClient().get_po_status(CURRENT_WEEK, site_config=site_config)
        api_response_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], element_index=0
        )
        table_data = remake_tool_page.get_dropdown_table_data()[0]
        assert table_data.supplier == api_response_data["supplier"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.sku_code == api_response_data["sku"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.scheduled_delivery_date == api_response_data["scheduledDeliveryDate"]
        assert table_data.po_status == api_response_data["poStatus"]
        assert table_data.receiving_variance_in_units == ui_utils.comma_separated_str(
            api_response_data["receiveVariance"]
        )
        assert table_data.appointment_time == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.order_size == ui_utils.comma_separated_str(api_response_data["orderSize"])
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(api_response_data["casePrice"])
        assert table_data.case_size == ui_utils.comma_separated_str(api_response_data["caseSize"])
        assert table_data.quantity_ordered == ui_utils.comma_separated_str(api_response_data["quantityOrdered"])
        assert table_data.quantity_received == ui_utils.comma_separated_str(api_response_data["quantityReceived"])
        assert table_data.cases_received == ui_utils.comma_separated_str(api_response_data["casesReceived"])
        assert table_data.case_size_received == ui_utils.comma_separated_str(api_response_data["caseSizeReceived"])
        assert table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.total_price == ui_utils.comma_separated_str_with_dollar(api_response_data["totalPrice"])
        assert table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response_data["totalPriceReceived"]
        )
        assert table_data.emergency_reason == api_response_data["emergencyReason"]
        assert table_data.ship_method == api_response_data["shipMethod"]
        assert table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            api_response_data["forecastDeliveryPercent"]
        )


def test_ingredient_requirements_section(page: Page):
    """
    Test Ingredient Requirements section and editable meals values.

    Test steps:
    1. Generate required data (needed data for set up all columns)
    2. Open Remake Tool Page
    3. Get table data and check that picks_2p, picks_4p, picks_6p to be as expected
    4. Edit picks_2p, picks_4p, picks_6p columns with new values and check that values are updated
    5. Click on 'Clear Remake Data' button and check that values changed to 0
    6. Open 'Days' dropdown by day and check that all values in dropdown are presented and enabled
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    remake_tool = TestRemakeTool.generate_remake_tool(site_config=site_config, mealkit=mealkit)
    row_ids = [0, 1, 2]
    new_picks_2p, new_picks_4p, new_picks_6p = [data_generator.random_int(3) for _ in range(3)]
    day_wednesday = "Wednesday 1"
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_remake_tool(remake_tool)
    ):
        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        left_table_data = remake_tool_page.get_ingredient_requirements_left_table_data()
        center_table_data = remake_tool_page.get_ingredient_requirements_center_table_data()
        table_data = {}
        for meals_name, meals_value in zip(left_table_data, center_table_data):
            table_data[meals_name.meals] = meals_value.meals
        assert table_data["2P"] == str(remake_tool.picks_2p)
        assert table_data["4P"] == str(remake_tool.picks_4p)
        assert table_data["6P"] == str(remake_tool.picks_6p)

        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK, day=day_wednesday)
        for row_id, picks in zip(row_ids, [new_picks_2p, new_picks_4p, new_picks_6p]):
            remake_tool_page.edit_or_add_meals_value(row_id=row_id, value=str(picks))
            remake_tool_page.wait_for_api_response(response_url=response.url)
        left_data = remake_tool_page.get_ingredient_requirements_left_table_data()
        center_data = remake_tool_page.get_ingredient_requirements_center_table_data()
        table_data = {}
        for meals_name, meals_value in zip(left_data, center_data):
            table_data[meals_name.meals] = meals_value.meals
        assert table_data["2P"] == str(new_picks_2p)
        assert table_data["4P"] == str(new_picks_4p)
        assert table_data["6P"] == str(new_picks_6p)

        remake_tool_page.click_on_clear_remake_data_button()
        center_table_data = remake_tool_page.get_ingredient_requirements_center_table_data()
        assert all(value.meals == str(0) for value in center_table_data)

        remake_tool_page.check_clickable_days_in_dropdown(day=day_wednesday)


def test_ingredient_requirements_section_and_remake_units_for_factor(page: Page):
    """
    Test Ingredient Requirements section for Factor, editable remakes values and Remake Units value in weekly section

    Test steps:
    1. Generate required data (needed data for set up all columns)
    2. Open Remake Tool Page
    3. Get table data and check remakes value is equal to set up
    4. Edit remakes with new value and check that value was updated
    5. Get table data from remake tool center table, make API call to remake_tool endpoint and check that Remake Units
    value is met with value from response
    """
    site_config = SiteConfigsFactor.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand, week=CURRENT_WEEK_FACTOR)
    ingredient = TestIngredient.generate_ingredients(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    remake_tool = TestRemakeTool.generate_remake_tool(
        site_config=site_config, mealkit=mealkit, week=CURRENT_WEEK_FACTOR
    )
    oscar = TestOscar.generate_oscar(week=CURRENT_WEEK_FACTOR, site_config=site_config, sku=sku)
    remakes_for_edit = data_generator.random_int()
    remake_day = random.choice(REMAKE_TOOL_DAYS_FACTOR)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_remake_tool(remake_tool)
        .with_oscar_forecast(oscar)
    ):
        remake_tool_page = RemakeToolPage(page=page)
        remake_tool_page.open()
        remake_tool_page.choose_week_from_dropdown(week=CURRENT_WEEK_FACTOR)
        left_table_data = remake_tool_page.get_ingredient_requirements_left_table_data()
        center_table_data = remake_tool_page.get_ingredient_requirements_center_table_data()
        ingredient_requirements_table_data = {
            left.meals: center.meals for left, center in zip(left_table_data, center_table_data)
        }
        assert ingredient_requirements_table_data["Remakes"] == str(remake_tool.picks_2p)

        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK_FACTOR, day=remake_day)
        remake_tool_page.edit_or_add_meals_value(row_id=0, value=str(remakes_for_edit))
        remake_tool_page.wait_for_api_response(response_url=response.url)
        left_table_data = remake_tool_page.get_ingredient_requirements_left_table_data()
        center_table_data = remake_tool_page.get_ingredient_requirements_center_table_data()
        ingredient_requirements_table_data = {
            left.meals: center.meals for left, center in zip(left_table_data, center_table_data)
        }
        assert ingredient_requirements_table_data["Remakes"] == str(remakes_for_edit)
        remake_tool_table_data = remake_tool_page.get_remake_tool_center_table_data()[0]
        response = RemakeToolClient().get_remake_tool(site_config=site_config, week=CURRENT_WEEK_FACTOR, day=remake_day)
        weekly_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )["ingredientDepletion"][0]["weekly"]
        assert remake_tool_table_data.remake_units == ui_utils.comma_separated_str(weekly_data["remakeUnits"])
