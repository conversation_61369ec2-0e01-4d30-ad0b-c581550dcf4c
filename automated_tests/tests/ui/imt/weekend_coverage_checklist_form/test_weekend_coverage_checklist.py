from datetime import datetime

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    ChecklistStatus,
    Colors,
    DayOfWeek,
    EmergencyReason,
    ShippingMethods,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    DATE_TIME_FORMAT_14,
    NEXT_WEEK,
    TOMORROW_DATETIME,
)
from automated_tests.data.constants.error_messages_constants import (
    FOB_PICK_UP_DATE_CANT_BE_SET,
    INVALID_WCC_INPUT_ERROR_MESSAGE,
)
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.ics_tickets import TestIcsTicket
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.weekend_coverage_checklist import TestWcc
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.imt.forms.weekend_coverage_checklist_form_page import (
    WccFormLocators,
    WeekendCoverageChecklistPage,
)
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.ics_tickets_client import IcsTicketClient
from automated_tests.services.api.imt.weekend_coverage_checklist_form import WeekendCoverageChecklistClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType


def test_insert_weekend_checklist_data_and_delete_record(page: Page):
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, shipping_method=ShippingMethods.FREIGHT_ON_BOARD, sku=sku
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    user = Users.test_user.value
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_po_shipment(po_shipment)
        .with_users(user)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        weekend_checklist.click_add_records()
        form_locators_dict = {
            WccFormLocators.PO_NUMBER_FIELD: po.po_number,
            WccFormLocators.SKU_NAME_FIELD: sku.sku_name,
            WccFormLocators.FOB_PICK_UP_DATE: CURRENT_DATETIME.strftime(DATE_FORMAT_1),
            WccFormLocators.PO_LANDING_DAY_FIELD: weekend_checklist_coverage.po_landing_day,
            WccFormLocators.PRODUCTION_DAY_AFFECTED_FIELD: weekend_checklist_coverage.production_day_affected,
            WccFormLocators.TO_CHECK_FIELD: weekend_checklist_coverage.to_check,
            WccFormLocators.CONTACT_NAME_FIELD: weekend_checklist_coverage.contact_name_vendor_carrier,
            WccFormLocators.EMAIL_PHONE_FIELD: weekend_checklist_coverage.email_phone,
            WccFormLocators.BACK_UP_VENDOR_FIELD: weekend_checklist_coverage.back_up_vendor,
        }
        weekend_checklist.add_records(form_locators_dict=form_locators_dict)
        weekend_checklist.validate_records()
        weekend_checklist.submit_records()
        weekend_checklist.check_weekend_checklist_column_headers()
        api_response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        api_response_data = base_steps.process_response_and_extract_data(
            response=api_response, additional_keys=["data"], element_index=0
        )
        table_data = weekend_checklist.get_weekend_checklist_table_data()[0]
        assert table_data.brand == api_response_data["brand"]
        assert table_data.dc == api_response_data["site"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.po_status == api_response_data["poStatus"]
        assert table_data.ship_method == api_response_data["shipMethod"]
        assert table_data.carrier == api_response_data["carrierName"]
        assert table_data.fob_pick_up_date == datetime.strftime(
            datetime.strptime(api_response_data["fobPickUpDate"], DATE_FORMAT_2), DATE_FORMAT_1
        )
        assert table_data.po_landing_day == api_response_data["poLandingDay"]
        assert table_data.production_day_affected == api_response_data["productionDayAffected"]
        assert table_data.to_check == api_response_data["toCheck"]
        assert table_data.contact_name == api_response_data["contactNameVendorCarrier"]
        assert table_data.email_phone == api_response_data["emailPhone"]
        assert table_data.back_up_vendor == api_response_data["backUpVendor"]
        assert table_data.username == api_response_data["updatedBy"]
        weekend_checklist.delete_records()
        weekend_checklist.is_no_data_message_present()
        weekend_checklist.choose_week_from_dropdown(week=NEXT_WEEK)
        weekend_checklist.check_week_item_color_dropdown(week=CURRENT_WEEK, expected_color=Colors.CYAN)


def test_editing_recorded_data(page: Page):
    """
    Test that fields are editable. The fields that can be edited: FOB Pick Up Date (shipping_method=FREIGHT_ON_BOARD),
    PO Landing Day, Production Day Affected, What needs to be checked, Contact Name, Email/Phone, Back Up Vendor,
    Status, Comments

    Test steps:
    1. Load the data
    2. Open the WCC form
    3. Looping over the editable fields click on the field, remove the value, and write a new one.
    4. Check that the fields are filled with new values
    """
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, shipping_method=ShippingMethods.FREIGHT_ON_BOARD)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    user = Users.test_user.value
    po_landing_day = DayOfWeek.random_value(exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY])
    production_day_affected = DayOfWeek.random_value(exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY])
    comment = to_check = back_up_vendor = data_generator.generate_string()
    contact_name_vendor_carrier = user.full_name
    email_phone = user.email
    status = ChecklistStatus.random_value()
    fob_pick_up_date = TOMORROW_DATETIME.strftime(DATE_FORMAT_1)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(user)
        .with_weekend_coverage_checklist(weekend_checklist_coverage)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        edit_locators_dict = {
            WccFormLocators.FOB_PICK_UP_DATE: fob_pick_up_date,
            WccFormLocators.PO_LANDING_DAY_FIELD: po_landing_day,
            WccFormLocators.PRODUCTION_DAY_AFFECTED_FIELD: production_day_affected,
            WccFormLocators.TO_CHECK_FIELD: to_check,
            WccFormLocators.CONTACT_NAME_FIELD: contact_name_vendor_carrier,
            WccFormLocators.EMAIL_PHONE_FIELD: email_phone,
            WccFormLocators.BACK_UP_VENDOR_FIELD: back_up_vendor,
            WccFormLocators.STATUS_FIELD: status,
            WccFormLocators.COMMENT_FIELD: comment,
        }
        response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        weekend_checklist.edit_records(edit_locators_dict=edit_locators_dict, row_id=0, response_url=response.url)
        table_data = weekend_checklist.get_weekend_checklist_table_data()[0]
        assert table_data.fob_pick_up_date == fob_pick_up_date
        assert table_data.po_landing_day == po_landing_day
        assert table_data.production_day_affected == production_day_affected
        assert table_data.to_check == to_check
        assert table_data.contact_name == contact_name_vendor_carrier
        assert table_data.email_phone == email_phone
        assert table_data.back_up_vendor == back_up_vendor
        assert table_data.status == status
        assert table_data.comments == comment


def test_that_fob_pick_up_date_cannot_be_edited(page: Page):
    """
    Test the editable FOB Pick Up Date field when shipping method = "Vendor Delivered".
    The message "'FOB Pick Up Date' can't be set for non 'Freight on Board' or 'Crossdock' shipping order"
     should appear in the corner

    Test steps:
    1. Load the data
    2. Open the WCC form
    3. Double-click on FOB Pick Up Date field and pass the date
    4. Check that error message appear in the corner of the page and the field not filled with date
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_weekend_coverage_checklist(weekend_checklist_coverage)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        edit_locators_dict = {WccFormLocators.FOB_PICK_UP_DATE: TOMORROW_DATETIME.strftime(DATE_FORMAT_1)}
        response = WeekendCoverageChecklistClient().get_weekend_checklist_data(week=CURRENT_WEEK)
        weekend_checklist.edit_records(edit_locators_dict=edit_locators_dict, response_url=response.url)
        weekend_checklist.check_message_is_present(FOB_PICK_UP_DATE_CANT_BE_SET)


def test_fob_pick_up_date_not_pass_validation(page: Page):
    """
    Test the field FOB Pick Up Date validation when shipping method = "Vendor Delivered".
    When shipping method = "Vendor Delivered" then after setting up a date for FOB Pick Up Date, validation
    should not pass and error "Invalid Weekend Coverage Checklist input" appear.

    Test steps:
    1. Load the data
    2. Open the WCC form
    3. Click "Add records" button
    4. Fill in all mandatory fields and FOB Pick Up Date
    5. Validate records
    6. Check that validation is not passed and error appear
    """
    site_config = SiteConfigs.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
        .with_skus(sku)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        weekend_checklist.click_add_records()
        form_locators_dict = {
            WccFormLocators.PO_NUMBER_FIELD: po.po_number,
            WccFormLocators.SKU_NAME_FIELD: sku.sku_name,
            WccFormLocators.FOB_PICK_UP_DATE: CURRENT_DATETIME.strftime(DATE_FORMAT_1),
            WccFormLocators.PO_LANDING_DAY_FIELD: weekend_checklist_coverage.po_landing_day,
            WccFormLocators.PRODUCTION_DAY_AFFECTED_FIELD: weekend_checklist_coverage.production_day_affected,
        }
        weekend_checklist.add_records(form_locators_dict=form_locators_dict)
        weekend_checklist.validate_records()
        actual_warning_message = weekend_checklist.get_submit_info_title()
        assert actual_warning_message == INVALID_WCC_INPUT_ERROR_MESSAGE


def test_error_messages_for_mandatory_fields(page: Page):
    site_config = SiteConfigs.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config)
    weekend_checklist_coverage = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_skus(po.first_line().sku)
        .with_purchase_orders(po)
        .with_users(Users.test_user.value)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        weekend_checklist.click_add_records()
        form_locators_dict = {
            WccFormLocators.BACK_UP_VENDOR_FIELD: weekend_checklist_coverage.back_up_vendor,
            WccFormLocators.PO_NUMBER_FIELD: po.po_number,
            WccFormLocators.SKU_NAME_FIELD: po.first_line().sku.sku_name,
            WccFormLocators.PO_LANDING_DAY_FIELD: weekend_checklist_coverage.po_landing_day,
        }

        for form_locator, form_value in form_locators_dict.items():
            weekend_checklist.add_records(form_locators_dict={form_locator: form_value}, row_id=0)
            weekend_checklist.validate_records()
            actual_warning_message = weekend_checklist.get_submit_info_title()
            assert actual_warning_message == INVALID_WCC_INPUT_ERROR_MESSAGE
            weekend_checklist.cancel_submit_records()


def test_po_popup_and_ics_tickets_on_wcc_board(page: Page):
    """
    Test case checks PO popup and ICS tickets on WCC board.
    Test steps:
    1. Generate required test data with site_config, sku, po, wcc, hj_receipt, po_shipment, ics ticket
    2. Open WCC, click on Po arrow button to have PO popup opened, check column headers, extract table data
    2. Make an API call to WCC PO Status endpoint -> 200, extract data
    3. Check values from table taba are equal to appropriate values from the API response.
    4. Close PO popup, click on ticket by po number and extract table data
    5. Check that column headers and data are equal to expected
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    wcc = TestWcc.generate_weekend_coverage_checklist(po=po, site_config=site_config)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    ics_ticket = TestIcsTicket.generate_ics_ticket(site_config=site_config, po=po)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_weekend_coverage_checklist(wcc)
        .with_hj_receipts(hj_receipt)
        .with_po_shipment(po_shipment)
        .with_oscar_forecast(oscar)
        .with_ics_ticket(ics_ticket)
    ):
        weekend_checklist = WeekendCoverageChecklistPage(page=page)
        weekend_checklist.open()
        weekend_checklist.click_on_po_number_arrow_btn(po=po)
        weekend_checklist.check_po_dropdown_column_headers()
        response = WeekendCoverageChecklistClient().get_po_status_on_wcc(po=po, sku=sku)
        api_response = base_steps.process_response_and_extract_data(response=response, element_index=0)
        dropdown_table_data = weekend_checklist.get_po_dropdown_table_data()[0]
        assert dropdown_table_data.supplier == api_response["supplier"]
        assert dropdown_table_data.po_number == api_response["poNumber"]
        assert dropdown_table_data.sku_code == api_response["sku"]
        assert dropdown_table_data.sku_name == api_response["skuName"]
        assert dropdown_table_data.scheduled_delivery_date == api_response["scheduledDeliveryDate"]
        assert dropdown_table_data.po_status == api_response["poStatus"]
        assert dropdown_table_data.receiving_variance_in_units == ui_utils.comma_separated_str(
            api_response["receiveVariance"]
        )
        assert dropdown_table_data.appointment_time == datetime_utils.reformat_datetime_str(
            date_string=api_response["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert dropdown_table_data.order_size == str(api_response["orderSize"])
        assert dropdown_table_data.case_price == ui_utils.comma_separated_str_with_dollar(api_response["casePrice"])
        assert dropdown_table_data.case_size == ui_utils.comma_separated_str(api_response["caseSize"])
        assert dropdown_table_data.quantity_ordered == ui_utils.comma_separated_str(api_response["quantityOrdered"])
        assert dropdown_table_data.quantity_received == ui_utils.comma_separated_str(api_response["quantityReceived"])
        assert dropdown_table_data.cases_received == ui_utils.comma_separated_str(api_response["casesReceived"])
        assert dropdown_table_data.case_size_received == ui_utils.comma_separated_str(api_response["caseSizeReceived"])
        assert dropdown_table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert dropdown_table_data.total_price == ui_utils.comma_separated_str_with_dollar(api_response["totalPrice"])
        assert dropdown_table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response["totalPriceReceived"]
        )
        assert dropdown_table_data.emergency_reason == api_response["emergencyReason"]
        assert dropdown_table_data.ship_method == api_response["shipMethod"]
        assert dropdown_table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            api_response["forecastDeliveryPercent"]
        )
        weekend_checklist.click_on_close_button()

        response = IcsTicketClient().get_ics_tickets_data(sku=sku, site_configs=[site_config])
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        weekend_checklist.open_ics_ticket_dropdown_by_po_number(po=po)
        weekend_checklist.check_ics_ticket_dropdown_column_headers()
        weekend_checklist.check_ticket_link_is_enabled()
        table_data = weekend_checklist.get_ics_ticket_dropdown_table_data()[0]
        assert table_data.bob_code == api_data["bobCode"]
        assert table_data.po_number == api_data["poNumber"]
        assert table_data.request_type == api_data["requestType"]
        assert table_data.sku_code == api_data["skuCode"]
        assert table_data.status == api_data["status"]
        assert table_data.production_impact == api_data["productionImpact"]
        assert table_data.subject == api_data["subject"]
        assert table_data.ticket_id == str(api_data["ticketId"])
        assert table_data.ticket_link == api_data["ticketLink"]
        assert table_data.updated_at == datetime_utils.reformat_datetime_str(
            api_data["updatedAt"], input_date_format=DATE_TIME_FORMAT_14, output_date_format=DATE_FORMAT_1
        )
        assert table_data.week == api_data["week"]
