import random

from playwright.sync_api import Page

from automated_tests.data.constants.base_constants import SiteConfigsCA, UsersCA
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, PREVIOUS_WEEK
from automated_tests.data.models.canada_forecast import TestCanadaForecast
from automated_tests.data.models.highjump import TestHjWipConsumption
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.weekly_snapshot import TestWeekly<PERSON>napshot
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.imt.ingredients_depletion_page import IngredientsDepletionPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.ingredient_depletion_client import ImtIngredientDepletionClient
from automated_tests.utils import ui_utils
from procurement.constants.hellofresh_constant import MARKET_CA
from procurement.managers.distribution_center.highjump import DestinationLocation, TransactionType


def test_wip_consumption_columns_on_ingr_depl_for_ca_market(page: Page):
    """
    Test case checks Carryover, Initial Inventory Pulls and Putaway to Production columns on Ingredient depletion
     for the CA market.
    Test steps:
    1. Generate required test data with site_config, sku, inventory planner, WIP consumption for Carryover or Crisper
    locations, WIP consumption for direct move type, wip consumption for auto_move tran_type.
    2. Open Ingredient Depletion page and extract weekly table data
    3. Make an API request -> 200, extract data
    4. Check Carryover and Initial Inventory Pulls column values from table data meet appropriate API response data.
    """
    site_config = SiteConfigsCA.random_value()
    site_config.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku(market=MARKET_CA)
    canada_forecast = TestCanadaForecast.generate_canada_forecast(sku=sku, site_config=site_config)
    wip_carryover_crisper_location = TestHjWipConsumption.generate_hj_wip_consumption(
        site_config=site_config,
        sku=sku,
        tran_type=TransactionType.FULL_PALLET_MOVE,
        destination_location=random.choice([DestinationLocation.CARRYOVER, DestinationLocation.CRISPER]),
    )
    wip_direct_move_tran_type = TestHjWipConsumption.generate_hj_wip_consumption(
        site_config=site_config, sku=sku, tran_type=TransactionType.DIRECTED_MOVE
    )
    wip_auto_move_tran_type = TestHjWipConsumption.generate_hj_wip_consumption(
        site_config=site_config,
        sku=sku,
        tran_type=TransactionType.AUTO_MOVE,
    )
    weekly_snapshot = TestWeeklySnapshot.generate_weekly_snapshot(sku=sku, wh_code=site_config.bob_code)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_users(UsersCA.test_user.value)
        .with_skus(sku)
        .with_canada_forecast(canada_forecast)
        .with_hj_wip_consumption(wip_carryover_crisper_location, wip_direct_move_tran_type, wip_auto_move_tran_type)
        .with_weekly_snapshot(weekly_snapshot)
    ):
        ing_depletion_page = IngredientsDepletionPage(page=page)
        ing_depletion_page.open()
        first_row = ing_depletion_page.get_table_row()
        weekly_table_data = ing_depletion_page.get_ing_depletion_weekly_table_data(row=first_row, market=MARKET_CA)
        api_response = ImtIngredientDepletionClient().get_ingredient_depletion(
            site_config=site_config, week=CURRENT_WEEK
        )
        api_response_data = base_steps.process_response_and_extract_data(
            response=api_response,
            additional_keys=[site_config.site.code],
            element_index=0,
        )["weekly"]
        assert weekly_table_data.carryover == ui_utils.comma_separated_str(api_response_data["wipConsumptionCarryover"])
        assert weekly_table_data.initial_pulls == ui_utils.comma_separated_str(
            api_response_data["wipConsumptionInitialPull"]
        )
        assert weekly_table_data.putaway_production == ui_utils.comma_separated_str(
            api_response_data["wipConsumptionPutawayToProduction"]
        )

        assert weekly_table_data.hj_snapshot == str(api_response_data["hjSnapshot"])
