from random import randint

from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    ALL_POS_WITH_PO_COUNT,
    NO_POS_PLACED,
    Colors,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SkuNames,
    Users,
)
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, DATE_TIME_FORMAT_1, DATE_TIME_FORMAT_3
from automated_tests.data.constants.error_messages_constants import (
    NO_PLACED_PO_FOR_SKU,
    SKU_IS_NOT_RUNNING_THIS_WEEK,
    SKU_IS_OVER_BUDGET,
)
from automated_tests.data.models.allocation_price import TestAllocationPrice
from automated_tests.data.models.bulk_sku import TestBulkSkus
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJReceipts
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.imt.top_variances_page import TopVariancesPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.services.api.imt.top_variances_client import TopVariancesClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType


def test_top_variances_main_values(page: Page):
    """
    Test case checks main values on Top Variances dashboard.
    Test steps:
    1. Generate required test data with 2 skus(main and child), pos, allocation values, oscar, categories, hj_receipt,
    buyer, mealkit, ingredient, mealkit_ingredient
    2. Open Top Variances page and extract table data
    3. Check column headers to be as expected
    4. Make an API call with Top Variances endpoint -> 200 and check values from table data meet API response values
    5. Check comment field is editable
    6. Enable DC consolidating, click filter on Site column and check that PO dropdown is closed
    """
    site_config = SiteConfigs.CA_HF.value
    site_config_2 = SiteConfigs.NJ_HF.value
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    packaged_sku, bulk_sku = TestSku.generate_skus(sku_quantity=2)
    bulk_packaged_sku = TestBulkSkus.generate_bulk_skus(
        packaged_sku=packaged_sku, bulk_sku=bulk_sku, brands=[site_config.brand]
    )
    po_packaged_sku = TestPurchaseOrder.generate_po_with_sku(
        sku=packaged_sku, site=site_config, emergency_reason=EmergencyReason.SAFETY_STOCK
    )
    po_bulk = TestPurchaseOrder.generate_po_with_sku(
        sku=bulk_sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    pos = [po_packaged_sku, po_bulk]
    hj_receipt_packaged = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_packaged_sku)
    allocation_packaged_sku = TestAllocationPrice.generate_allocation_price(sku=packaged_sku, site_config=site_config)
    allocation_bulk_sku = TestAllocationPrice.generate_allocation_price(
        sku=bulk_sku,
        site_config=site_config,
        price=(int(round(po_bulk.first_line().case_price / po_bulk.first_line().case_size, 2))) - randint(1, 100),
    )
    buyer = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=bulk_sku)
    buyer_packaged_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=packaged_sku)
    oscar_packaged_sku = TestOscar.generate_oscar(sku=packaged_sku, site_config=site_config, week=CURRENT_WEEK)
    categories = [
        TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
        for sku in [packaged_sku, bulk_sku]
    ]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=packaged_sku, brand=site_config.brand)
    mealkit_ingredients = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit=mealkit, ingredient=ingredient
    )
    comment = data_generator.generate_string()
    comment_to_edit = data_generator.generate_string(200, with_space_interval=10)
    user = Users.test_user.value
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site, site_config_2.site)
        .with_site_configs(site_config, site_config_2)
        .with_users(user)
        .with_skus(packaged_sku, bulk_sku)
        .with_mealkit(mealkit)
        .with_ingredients(ingredient)
        .with_purchase_orders(*pos)
        .with_mealkit_ingredient(mealkit_ingredients)
        .with_bulk_skus(bulk_packaged_sku)
        .with_purchasing_categories(*categories)
        .with_allocation_price(allocation_bulk_sku, allocation_packaged_sku)
        .with_buyer_sku(buyer, buyer_packaged_sku)
        .with_oscar_forecast(oscar_packaged_sku)
        .with_hj_receipts(hj_receipt_packaged)
    ):
        top_variances = TopVariancesPage(page=page)
        top_variances.open()
        top_variances.select_site_or_region_from_site_bar(site_or_region=site_config.site.code)
        top_variances.check_top_variances_column_headers()

        top_variances.click_on_all_pos_dropdown()
        master_summary_data, *child_summary_data_row = top_variances.get_top_variances_summary_table_data()
        master_center_table_data, *child_center_table_data_row = top_variances.get_top_variances_center_table_data()

        response = TopVariancesClient().get_top_variances_data(site_config=site_config)
        expected_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        expected_pos_data = expected_data["pos"]
        name_mapping_summary_data = {
            "sku": "skuCode",
            "sku_name": "skuName",
            "category": "category",
        }
        name_mapping_center_table_data = {
            "allocation_price": "allocationPrice",
            "po_unit_price": "poUnitPrice",
            "budgeted_cost": "budgetedCost",
            "received_value": "receivedValue",
            "incoming_value": "incomingValue",
        }

        # check summary table data for master row
        for field, json_name in name_mapping_summary_data.items():
            assert getattr(master_summary_data, field) == expected_data[json_name], f"Field {field} does not match"
        assert master_summary_data.po_number == ALL_POS_WITH_PO_COUNT.format(po_count=len(expected_pos_data))
        assert master_summary_data.emergency_reason == ""
        assert master_summary_data.supplier == ""

        # check center table data for master row
        for field, json_name in name_mapping_center_table_data.items():
            assert getattr(master_center_table_data, field) == ui_utils.comma_separated_str_with_dollar(
                expected_data[json_name]
            ), f"Field {field} does not match"
        assert master_center_table_data.buyer == expected_data["buyer"]
        assert master_center_table_data.price_variance == ui_utils.comma_separated_str_with_dollar(
            expected_data["priceVariance"]
        )
        assert master_center_table_data.quantity_variance == (
            ui_utils.comma_separated_str_with_dollar(expected_data["quantityVariance"])
        )
        assert master_center_table_data.variance_status == (expected_data["varianceStatus"] or "")

        actual_po_data = {po["poNumber"]: po for po in expected_pos_data}
        # check summary table data for children pos
        for child_po_row, po in zip(child_summary_data_row, pos):
            assert child_po_row.po_number == actual_po_data[po.po_number]["poNumber"]
            for field, json_name in name_mapping_summary_data.items():
                assert (
                    getattr(child_po_row, field) == actual_po_data[po.po_number][json_name]
                ), f"Field {field} does not match"

        # check center table data for children pos
        for child_po_row, po in zip(child_center_table_data_row, pos):
            assert child_po_row.buyer == actual_po_data[po.po_number]["buyer"]
            for field, json_name in name_mapping_center_table_data.items():
                assert getattr(child_po_row, field) == ui_utils.comma_separated_str_with_dollar(
                    actual_po_data[po.po_number][json_name]
                ), f"Field {field} does not match"

        # check po unit price flag (if po price > allocation price -> it should be highlighted in orange)
        for row_id, summary_row in enumerate(child_summary_data_row, start=1):
            if summary_row.sku == bulk_sku.sku_code:
                top_variances.check_po_unit_price_flag(row_id=row_id, expected_color=Colors.RICH_ORANGE)

        # check editable Comments field
        for comment_value in [comment, comment_to_edit]:
            top_variances.add_or_edit_text_in_comments_field(row_id=0, value=comment_value)
            table_data = top_variances.get_top_variances_center_table_data()[0]
            assert table_data.comment == comment_value
            assert table_data.last_edited_by == user.email
        top_variances.check_comment_tooltip_is_displayed(row_id=0, comment_txt=comment_to_edit)

        top_variances.enable_disable_site_consolidated_view(consolidated=True)
        top_variances.open_general_menu_filter_by_column_name(column_name="DC")
        top_variances.open_filter_and_click_on_checkbox(set_check=False, checkbox_name=site_config_2.site.code)
        assert top_variances.get_aria_expanded_attribute_from_all_pos_dropdown() == "false"


def test_variance_status_on_top_variances(page: Page):
    """
    Test case checks Variance status field on Top Variances
    Test steps:
    1.Generate required data (set up 3 records: For the first record set up oscar and PO (to ensure condition Variance
    > 0 and oscar > 0)). For the second record set up Oscar without PO (to ensure condition oscar > 0 but there are no
    POs). For the third record set up PO but don't set up oscar. For the forth one set sku without po and oscar
    2.Make an API call to top_variances endpoint -> 200
    3.Check Variance Status fields have appropriate statuses
    The logic is the following:
    If variance > 0 AND Forecast > 0 then Variance Status = SKU is Over Budget!.
    If forecast > 0 AND there are no POs placed then No PO(s) Placed for this SKU Yet!.
    If forecast is not present and PO has been placed then This SKU isn't running this week!
    Else: status should be None
    4. Check if there is placed po -> PO_Number should be 'All Pos', else 'No Pos Placed'
    5. Check the flag colors of the variance statuses and price variance values. If the status is SKU is Over Budget!
    -> pink, if status is POs placed then No PO(s) Placed for this SKU Yet!. -> blue, if This SKU isn't running this
     week! -> orange. In other cases -> black
    """
    site_config = SiteConfigs.random_value()
    sku, sku_2, sku_3, sku_4 = TestSku.generate_skus_with_specific_sku_names(
        sku_names=SkuNames.random_non_repetitive_values(qty=4)
    )
    skus = [sku, sku_2, sku_3, sku_4]
    oscar_sku_sku_2 = [
        TestOscar.generate_oscar(sku=sku_item, site_config=site_config, week=CURRENT_WEEK) for sku_item in [sku, sku_2]
    ]
    po_sku_sku_3 = [TestPurchaseOrder.generate_po_with_sku(sku=sku_item, site=site_config) for sku_item in [sku, sku_3]]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredients = [TestIngredient(sku=sku_item, brand=site_config.brand) for sku_item in skus]
    mealkit_ingredients = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for ingredient in ingredients
    ]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_mealkit(mealkit)
        .with_ingredients(*ingredients)
        .with_purchase_orders(*po_sku_sku_3)
        .with_mealkit_ingredient(*mealkit_ingredients)
        .with_oscar_forecast(*oscar_sku_sku_2)
    ):
        top_variances = TopVariancesPage(page=page)
        top_variances.open()
        summary_table_data = top_variances.get_top_variances_summary_table_data()
        table_data = top_variances.get_top_variances_center_table_data()
        sku_conditions = {
            sku.sku_code: (SKU_IS_OVER_BUDGET, Colors.FUCHSIA_PINK),
            sku_2.sku_code: (NO_PLACED_PO_FOR_SKU, Colors.BLUE),
            sku_3.sku_code: (SKU_IS_NOT_RUNNING_THIS_WEEK, Colors.DEEP_ORANGE),
        }
        for row_id, summary_row in enumerate(summary_table_data):
            variance_status, color = sku_conditions.get(summary_row.sku, (None, Colors.BLACK))
            top_variances.check_price_status_flag(row_id=row_id, expected_color=color)
            top_variances.check_variance_status_flag(row_id=row_id, expected_color=color)
            assert table_data[row_id].variance_status == (variance_status if variance_status else "")

            # check po_number column values
            if summary_row.sku in (sku.sku_code, sku_3.sku_code):
                assert summary_row.po_number == ALL_POS_WITH_PO_COUNT.format(po_count=1)
            else:
                assert summary_row.po_number == NO_POS_PLACED


def test_po_dropdown_columns_on_top_variances(page: Page):
    """
    Test case checks PO dropdown on Top Variances.
    Test steps:
    1. Generate required test data with site_config, bulk and packaged skus, pos, hj_receipts, categories, shipments,
     mealkit, ingredient, mealkit_ingredient
    2. Open IMT -> Top Variances page, click on sku arrow button, and extract PO dropdown table data
    3. Check column headers to be as expected
    4. Make an API request to po dropdown endpoint -> 200, extract data
    5. Check that PO dropdown column values for each po meet with the appropriate po API response data.
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    skus = packaged_sku, bulk_sku = TestSku.generate_skus(sku_quantity=2)
    bulk_packaged_sku = TestBulkSkus.generate_bulk_skus(
        packaged_sku=packaged_sku, bulk_sku=bulk_sku, brands=[site_config.brand]
    )
    pos = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku,
            site=site_config,
            emergency_reason=EmergencyReason.SAFETY_STOCK,
            shipping_method=ShippingMethods.VENDOR_DELIVERED,
        )
        for sku in skus
    ]
    hj_receipts = [TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po) for po in pos]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=packaged_sku, brand=site_config.brand)
    mealkit_ingredients = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit=mealkit, ingredient=ingredient
    )
    po_shipments = [TestPOShipment.generate_po_shipment(po=po) for po in pos]
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(*skus)
        .with_mealkit(mealkit)
        .with_ingredients(ingredient)
        .with_purchase_orders(*pos)
        .with_mealkit_ingredient(mealkit_ingredients)
        .with_bulk_skus(bulk_packaged_sku)
        .with_hj_receipts(*hj_receipts)
        .with_po_shipment(*po_shipments)
    ):
        top_variances = TopVariancesPage(page=page)
        top_variances.open()
        top_variances.open_dropdown_by_sku(sku=packaged_sku.sku_code)
        top_variances.check_po_dropdown_column_headers()

        dropdown_table_data = top_variances.get_po_dropdown_table_data()
        actual_po_table_data = {po.po_number: po for po in dropdown_table_data}

        response = ImtPoStatusClient().get_top_variances_po_dropdown(
            site_config=site_config, week=CURRENT_WEEK, sku=bulk_sku
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site_config.site.code], expected_length=2
        )
        actual_po_data = {po["poNumber"]: po for po in actual_data}

        name_mapping = {
            "supplier": "supplier",
            "po_number": "poNumber",
            "sku_code": "sku",
            "sku_name": "skuName",
            "scheduled_delivery_date": "scheduledDeliveryDate",
            "po_status": "poStatus",
            "emergency_reason": "emergencyReason",
            "ship_method": "shipMethod",
        }

        for po in pos:
            for field, json_name in name_mapping.items():
                assert (
                    getattr(actual_po_table_data[po.po_number], field) == actual_po_data[po.po_number][json_name]
                ), f"Field {field} does not match"
            assert actual_po_table_data[po.po_number].receiving_variance_in_units == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["receiveVariance"]
            )
            assert actual_po_table_data[po.po_number].appointment_time == datetime_utils.reformat_datetime_str(
                date_string=actual_po_data[po.po_number]["appointmentTime"],
                input_date_format=DATE_TIME_FORMAT_1,
                output_date_format=DATE_TIME_FORMAT_3,
            )
            assert actual_po_table_data[po.po_number].order_size == str(actual_po_data[po.po_number]["orderSize"])
            assert actual_po_table_data[po.po_number].case_price == ui_utils.comma_separated_str_with_dollar(
                actual_po_data[po.po_number]["casePrice"]
            )
            assert actual_po_table_data[po.po_number].case_size == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["caseSize"]
            )
            assert actual_po_table_data[po.po_number].case_size_received == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["caseSizeReceived"]
            )
            assert actual_po_table_data[po.po_number].quantity_ordered == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["quantityOrdered"]
            )
            assert actual_po_table_data[po.po_number].quantity_received == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["quantityReceived"]
            )
            assert actual_po_table_data[po.po_number].cases_received == ui_utils.comma_separated_str(
                actual_po_data[po.po_number]["casesReceived"]
            )
            assert actual_po_table_data[po.po_number].total_price == ui_utils.comma_separated_str_with_dollar(
                actual_po_data[po.po_number]["totalPrice"]
            )
            assert actual_po_table_data[po.po_number].total_price_received == ui_utils.comma_separated_str_with_dollar(
                actual_po_data[po.po_number]["totalPriceReceived"]
            )
            assert actual_po_table_data[po.po_number].date_received == datetime_utils.reformat_datetime_str(
                date_string=actual_po_data[po.po_number]["dateReceived"],
                input_date_format=DATE_TIME_FORMAT_1,
                output_date_format=DATE_TIME_FORMAT_3,
            )
