from datetime import timed<PERSON><PERSON>

from playwright.sync_api import Page

from automated_tests.data.constants.base_constants import SiteConfigs, SiteConfigsFactor, SiteConfigsGC, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_WEEK,
    CURRENT_WEEK_DAYS_TITLES,
    CURRENT_WEEK_FACTOR,
    CURRENT_WEEK_FIRST_DAY,
    NEXT_WEEK,
)
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.hybrid_needs import (
    TestHybridNeeds,
    TestHybridNeedsLiveUsage,
    TestHybridNeedsShiftLevel,
    TestHybridNeedsStatus,
)
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.imt.production_need_ingredients_page import ProductionNeedIngredientsPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.prod_need_ingredients_client import ProdNeedIngredientsClient
from automated_tests.utils import ui_utils
from procurement.constants.hellofresh_constant import ProductionPlanType


def test_production_need_ingredients_current_week(page: Page):
    """
    Test Production Need Ingredients main values in current week section.

    Test steps:
    1. Generate required test data
    2. Open Production Need Ingredients page
    3. Expand Ingredient Summary and current week sections, and extract data
    4. Check that data in Ingredient Summary and current week sections are equal to expected
    """
    site_config = SiteConfigs.random_value()
    brand = site_config.brand
    site = site_config.site
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand)
    ingredient = TestIngredient(sku, brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    hybrid_need_status = TestHybridNeedsStatus.generate_hybrid_needs_status(
        site_config=site_config, status=ProductionPlanType.PROJECTED
    )
    hybrid_need_shift_level = TestHybridNeedsShiftLevel.generate_hybrid_needs_shift_levels_for_several_days(
        sku=sku, site_config=site_config
    )
    hybrid_needs_live_usage = TestHybridNeedsLiveUsage.generate_hybrid_needs_live_usage_for_several_days(
        sku=sku, site_config=site_config
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(oscar)
        .with_buyer_sku(buyer_sku)
        .with_hybrid_needs_shift_level(*hybrid_need_shift_level)
        .with_hybrid_needs_live_usage(*hybrid_needs_live_usage)
        .with_hybrid_needs_status(hybrid_need_status)
    ):
        prod_need_ingredients_page = ProductionNeedIngredientsPage(page=page)
        prod_need_ingredients_page.open()

        response = ProdNeedIngredientsClient().get_production_need(site_config=site_config, week=CURRENT_WEEK)
        api_response = base_steps.process_response_and_extract_data(
            response=response, additional_keys=[site.code], element_index=0
        )

        api_response_daily = api_response["daily"]
        ingredient_summary_table_data = prod_need_ingredients_page.get_ingredient_summary_table_data()[0]
        ingredient_center_table_data = prod_need_ingredients_page.get_prod_need_ingredients_center_data()[0]

        assert ingredient_summary_table_data.sku == api_response["sku"]
        assert ingredient_summary_table_data.sku_name == api_response["skuName"]
        daily_values = [
            ingredient_center_table_data.daily_count_wednesday_1,
            ingredient_center_table_data.daily_count_thursday_1,
            ingredient_center_table_data.daily_count_friday,
            ingredient_center_table_data.daily_count_saturday,
            ingredient_center_table_data.daily_count_sunday,
            ingredient_center_table_data.daily_count_monday,
            ingredient_center_table_data.daily_count_tuesday,
            ingredient_center_table_data.daily_count_wednesday_2,
            ingredient_center_table_data.daily_count_thursday_2,
        ]
        for index, daily_count in enumerate(daily_values):
            assert daily_count == str(api_response_daily[str(CURRENT_WEEK_FIRST_DAY + timedelta(index))]["value"])

        first_weekday_daily = api_response_daily[str(CURRENT_WEEK_FIRST_DAY)]
        assert ingredient_center_table_data.daily_count_day == str(first_weekday_daily["valueDay"])
        assert ingredient_center_table_data.daily_count_night == str(first_weekday_daily["valueNight"])
        assert ingredient_center_table_data.daily_count_3rd_shift == str(first_weekday_daily["valueThird"])
        assert ingredient_center_table_data.total == str(api_response["total"])
        assert ingredient_center_table_data.row_need == str(api_response["rowNeed"])
        assert ingredient_center_table_data.live_consumption == str(api_response["liveConsumption"])
        assert ingredient_center_table_data.live_row_need == str(api_response["liveRowNeed"])
        assert ingredient_center_table_data.delta == ui_utils.to_percent_string(api_response["delta"])
        assert ingredient_center_table_data.buyer == str(api_response["buyers"])


def test_shift_level_toggle(page: Page):
    """
    Test case checks when 'Shift Level' toggle is turned on and another week is picked up, it should continue to
    be turned on.

    Test steps:
    1. Generate required test data
    2. Open Production Need Ingredients page
    3. Click on 'Shift level' toggle to be enabled / 'Day Shift' is one of the columns that will be shown if the toggle
       is on
    4. Pick up Next week and check that the 'Shift Level' toggle is enabled and view is expanded
    5. Pick up Current week and check that the 'Shift Level' toggle is enabled
    """
    site_config = SiteConfigs.random_value()
    brand = site_config.brand
    sku = TestSku.generate_sku()
    user = Users.test_user.value
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    oscar_current_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    oscar_next_week = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=NEXT_WEEK)
    mock_plan_calculation_next_week = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config, week=NEXT_WEEK
    )
    with (
        TestData(None)
        .with_brands(brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(oscar_current_week, oscar_next_week)
        .with_mock_plan_calculation(mock_plan_calculation_next_week)
    ):
        prod_need_ingredients_page = ProductionNeedIngredientsPage(page=page)
        prod_need_ingredients_page.open()
        prod_need_ingredients_page.check_column_headers_on_production_needs()
        prod_need_ingredients_page.enable_disable_shift_level_view(enable=True)
        prod_need_ingredients_page.check_column_headers_on_production_needs(enabled_shift_level=True)
        prod_need_ingredients_page.check_day_shift_level_column_present()

        # We change a week to change a view and ensure that Shift Level toggle will be enabled
        prod_need_ingredients_page.choose_week_from_dropdown(week=NEXT_WEEK)
        prod_need_ingredients_page.check_shift_level_toggle_enabled()
        prod_need_ingredients_page.check_day_shift_level_column_present()
        prod_need_ingredients_page.choose_week_from_dropdown(week=CURRENT_WEEK)
        prod_need_ingredients_page.check_day_shift_level_column_present()


def test_displaying_days_for_factor_when_switch_to_factor_brand(page: Page):
    """
    Test that when switching from another brand to Factor, the columns from the daily section are in the correct order.
    The issue was when switching from another brand to Factor, the last 2 columns displayed after the last section, not
    in daily section.

    Test steps:
    1.Generate required data
    2.Open the Production Needs page
    3.Switch to Factor brand
    4.Check that column headers in the correct order
    """
    site_config, site_config_fj = SiteConfigs.random_value(), SiteConfigsFactor.random_value()
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    hybrid_needs_fj = TestHybridNeeds.generate_hybrid_needs_for_several_days(
        sku=sku, site_config=site_config_fj, week=CURRENT_WEEK_FACTOR, days_qty=12
    )
    with (
        TestData(None)
        .with_brands(site_config.brand, site_config_fj.brand)
        .with_sites(site_config.site, site_config_fj.site)
        .with_site_configs(site_config, site_config_fj)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_hybrid_needs(*hybrid_needs_fj, *hybrid_needs)
    ):
        prod_need_ingredients_page = ProductionNeedIngredientsPage(page=page)
        prod_need_ingredients_page.open()
        prod_need_ingredients_page.choose_item_from_brand_dropdown(item_name=site_config_fj.brand.name)
        prod_need_ingredients_page.choose_week_from_dropdown(week=CURRENT_WEEK_FACTOR)
        prod_need_ingredients_page.check_column_headers_on_production_needs(factor_brand=True)


def test_days_headers_for_ep_gc(page: Page):
    """
    Test case checks names of the days are shown for EP and GC brand after switching 'Brand Consolidated View' toggle on

    Test steps:
    1. Generate required test data with site_configs for EP and GC brands, sku, hybrid needs for both brands
    2. Open Production Need Ingredients Page
    3. Turn on 'Brand consolidated view' toggle and check column headers to be as expected
    4. Choose the other brand and check days headers to be as expected
    """
    site_config_ep, site_config_gc = SiteConfigs.CT_EP.value, SiteConfigsGC.CO_GC.value
    site_configs = [site_config_ep, site_config_gc]
    brands = [site_config_ep.brand, site_config_gc.brand]
    sku = TestSku.generate_sku()
    hybrid_needs = [
        TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=sc) for sc in site_configs
    ]
    with (
        TestData(None)
        .with_brands(*brands)
        .with_sites(*(sc.site for sc in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_hybrid_needs(*[item for sublist in hybrid_needs for item in sublist])
    ):
        prod_need_ingredients_page = ProductionNeedIngredientsPage(page=page)
        prod_need_ingredients_page.open()
        for brand in brands:
            prod_need_ingredients_page.enable_disable_brand_consolidated_view(consolidated=True)
            prod_need_ingredients_page.check_column_headers_on_production_needs(brand_consolidated_view=True)
            expected_day_headers = prod_need_ingredients_page.get_days_headers()
            assert expected_day_headers == CURRENT_WEEK_DAYS_TITLES
            prod_need_ingredients_page.enable_disable_brand_consolidated_view(consolidated=False)
            if brand == site_config_ep.brand:
                prod_need_ingredients_page.choose_item_from_brand_dropdown(item_name=site_config_gc.brand.name)
