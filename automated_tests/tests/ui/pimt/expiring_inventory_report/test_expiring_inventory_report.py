from playwright.sync_api import Page

from automated_tests.data.constants.base_constants import Users, Warehouses
from automated_tests.data.constants.date_time_constants import DATE_FORMAT_1, DATE_FORMAT_2, DATE_TIME_FORMAT_4
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.pimt_replenishment import TestPimtReplenishment
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.pimt.expiring_inventory_report_page import ExpiringInventoryReportPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.pimt.expiring_inventory_client import ExpiringInventoryClient
from automated_tests.utils import datetime_utils, ui_utils
from automated_tests.utils.warehouse_utils import WarehouseUtils


def test_expiring_inventory(page: Page):
    """
    Test Expiring Inventory report.

    Test steps:
    1. Generate required test data (sku, warehouse, po, site_config, forecast, replenishment, inventory)
    2. Open Expiring Inventory report page
    3.Expand Ingredient Summary section, check column headers and extract table data
    4.Make an API call to the expiring_inventory_report endpoint -> 200
    5.Check all column values from table data meet appropriate api response data
    """
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=inbound_po)
    replenishment = TestPimtReplenishment.generate_replenishment_by_po(po=inbound_po)
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    commodity_group = generate_string()
    forecast = TestOscar.generate_oscar(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_pimt_inventory(inventory)
        .with_pimt_replenishment(replenishment)
        .with_buyer_sku(buyer_sku)
        .with_commodity_group(commodity_group, warehouse.regional_dcs[0], sku)
        .with_warehouses(warehouse)
        .with_oscar_forecast(forecast)
    ):
        expiring_inventory_page = ExpiringInventoryReportPage(page=page)
        expiring_inventory_page.open()
        expiring_inventory_page.expand_collapse_ingredient_summary()
        expiring_inventory_page.check_expiring_inventory_report_column_headers()
        ing_summary_table_data = expiring_inventory_page.get_expiring_inventory_ingredient_summary_table_data()[0]
        table_data = expiring_inventory_page.get_expiring_inventory_table_data()[0]

        response = ExpiringInventoryClient().get_expiring_inventory_report()
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

        assert ing_summary_table_data.sku_code == api_data["skuCode"]
        assert ing_summary_table_data.sku_name == api_data["skuName"]
        assert ing_summary_table_data.buyer == api_data["buyer"]
        assert ing_summary_table_data.commodity_group == api_data["commodityGroup"][0]

        assert table_data.supplier == api_data["supplierName"]
        assert table_data.po == api_data["poNumber"]
        assert table_data.receipt_date == datetime_utils.reformat_datetime_str(
            date_string=api_data["receivedDate"],
            input_date_format=DATE_TIME_FORMAT_4,
            output_date_format=DATE_FORMAT_1,
        )
        assert table_data.unit_cost == ui_utils.comma_separated_str_with_dollar(
            api_data["unitPrice"], two_digits_after_decimal_point=True
        )
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(
            api_data["casePrice"], two_digits_after_decimal_point=True
        )
        assert table_data.case_size == api_data["unitsPerCaseCount"]
        assert table_data.total_cases == api_data["casesCount"]
        assert table_data.total_units == api_data["total"]
        assert table_data.pallet_id == api_data["lot"]
        assert table_data.shelf_life == api_data["shelfLifeInDaysCount"]
        assert table_data.expiration_date == datetime_utils.reformat_datetime_str(
            date_string=api_data["expirationDate"], input_date_format=DATE_FORMAT_2, output_date_format=DATE_FORMAT_1
        )
        assert table_data.days_until == api_data["daysUntilCount"]
        assert table_data.next_week_used == api_data["nextWeekUsed"]
        assert table_data.forecast == api_data["forecast"]
        assert table_data.source_of_exp_date == api_data["source"]
        assert table_data.three_pw == api_data["site"]
        assert table_data.inventory_state == api_data["inventoryStatus"]
        assert table_data.distinction == api_data["distinction"]
        assert table_data.cost_if_discarded == ui_utils.comma_separated_str_with_dollar(
            api_data["costIfDiscarded"], two_digits_after_decimal_point=True
        )
