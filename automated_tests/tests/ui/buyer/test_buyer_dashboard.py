import pytest
from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    INGREDIENT_DEPLETION_BOARD,
    PO_CURRENT_WEEK_BOARD,
    PO_SCHEDULED_FOR_DELIVERY_TODAY_BOARD,
    EmergencyReason,
    PurchasingCategories,
    SiteConfigs,
    Users,
)
from automated_tests.data.constants.column_headers.imt_column_headers import (
    INGREDIENT_DEPLETION_BUYER_BOARD_EXPECTED_HEADERS,
    PO_STATUS_BUYER_EXPECTED_HEADERS,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    YESTERDAY_DATETIME,
)
from automated_tests.data.models.buyer_sku import <PERSON><PERSON><PERSON><PERSON>Sku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import Test<PERSON>car
from automated_tests.data.models.highjump import TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.buyer.buyer_dashboard_page import BuyerPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.buyer.buyer_client import BuyerClient
from automated_tests.services.api.imt.po_status_client import ImtPoStatusClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import ReceiveInputType
from procurement.constants.ordering import IN_PROGRESS_HJ


@pytest.mark.parametrize("week", [CURRENT_WEEK, NEXT_WEEK])
def test_ingredient_depletion_board_buyer(page: Page, week):
    """
    Test Ingredient Depletion boards for current_week and next_week on Buyer dashboard.

    Test steps:
    1.Generate required data (needed data for set up ingredient depl board with values, status should be != NO IMPACT)
    2.Open Buyer Dashboard, pick the buyer
    3.Extract table data from Ingredient Depletion board (depends on the week)
    4.Check that columns headers are met with expected
    5.Check that data in columns are met with data from response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_not_delivered, po_in_progress_hj = TestPurchaseOrder.generate_pos(
        quantity=2, sku=sku, site=site_config, week=week
    )
    po_not_delivered.delivery_time_start = YESTERDAY_DATETIME
    po_autobagger = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, supplier_name="Autobagger - " + site_config.site.code, week=week
    )
    po_received = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    (
        po_not_delivered.first_line().qty,
        po_in_progress_hj.first_line().qty,
        po_autobagger.first_line().qty,
        po_received.first_line().qty,
    ) = [data_generator.random_int() for _ in range(4)]
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand, week=week)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit=mealkit, ingredient=ingredient
    )
    hj_receipt_in_progress_hj = TestHJReceipts.generate_hj_receipt(
        site_config=site_config, status=IN_PROGRESS_HJ, po=po_in_progress_hj, week=week, quantity_received=0
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, po=po_received, week=week)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config, week=week)
    commodity_group = data_generator.generate_string()
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    oscars = TestOscar.generate_oscar_for_several_weeks(
        site_config=site_config, sku=sku, value=data_generator.random_int(6), week=week
    )
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config, week=week)
    receipt_override = TestReceiptOverride.generate_receipt_override(
        site_config=site_config,
        po=po_received,
        week=week,
    )
    discard = TestDiscard.generate_discard(site_config=site_config, sku=sku, week=week)
    inventory_pull_put = TestInventoryPullPut.generate_inventory_pull_put(site_config=site_config, sku=sku, week=week)
    mock_plan_calculation = TestMockPlanCalculation.generate_mock_plan(site_config=site_config)
    board_name = INGREDIENT_DEPLETION_BOARD.format(week=ui_utils.get_week_str(week=week))
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po_not_delivered, po_in_progress_hj, po_autobagger, po_received)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_commodity_group(commodity_group, site_config.site.code, sku)
        .with_purchasing_category(PurchasingCategories.PRODUCE, sku)
        .with_oscar_forecast(*oscars)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_receipt_override(receipt_override)
        .with_hj_receipts(hj_receipt, hj_receipt_in_progress_hj)
        .with_discard(discard)
        .with_mock_plan_calculation(mock_plan_calculation)
        .with_inventory_pull_put(inventory_pull_put)
        .with_buyer_sku(buyer_sku)
    ):
        buyer_page = BuyerPage(page=page)
        buyer_page.open()
        buyer_page.select_buyer(user=buyer_sku.user)
        response = BuyerClient().get_buyer(week=week)
        depletion_api_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)[
            str(week)
        ]["depletion"][0]
        weekly_depletion_data = depletion_api_data["weekly"]
        summary_table_data = buyer_page.get_ingredient_summary_table_data(expand=True, board_name=board_name)[0]
        weekly_table_data = buyer_page.get_center_table_data_ing_depl(board_name=board_name)[0]
        consolidated_table_data = buyer_page.get_consolidated_table_data(board_name=board_name)[0]

        column_headers = buyer_page.get_column_headers_on_buyer_by_board_name(board_name=board_name)
        assert column_headers == INGREDIENT_DEPLETION_BUYER_BOARD_EXPECTED_HEADERS

        assert summary_table_data.sku == depletion_api_data["sku"]
        assert summary_table_data.sku_name == depletion_api_data["skuName"]
        assert summary_table_data.category == depletion_api_data["category"]
        assert summary_table_data.commodity_group == depletion_api_data["commodityGroup"]
        assert summary_table_data.impacted_recipes == depletion_api_data["impactedRecipes"]
        assert weekly_table_data.sn == depletion_api_data["sn"]
        assert weekly_table_data.plan == ui_utils.comma_separated_str(depletion_api_data["plan"])
        assert weekly_table_data.forecastOscar == ui_utils.comma_separated_str(depletion_api_data["forecastOscar"])
        assert (
            weekly_table_data.delta == ui_utils.to_percent_string(abs(depletion_api_data["delta"]))
            if int(depletion_api_data["delta"]) == 0
            else ui_utils.to_percent_string(depletion_api_data["delta"])
        )
        assert weekly_table_data.buffer_percent == ui_utils.to_percent_string(weekly_depletion_data["bufferPercent"])

        name_mapping = {
            "units_needed": "unitsNeeded",
            "units_ordered": "unitsOrdered",
            "units_received": "unitsReceived",
            "units_in_house": "unitsInHouseHj",
            "row_need": "rowNeed",
            "units_in_house_min_row_need": "unitsInHouseMinusRowNeed",
            "next_week_forecast": "nextWeekForecast",
            "units_in_house_min_row_need_min_forecast": "unitsInHouseMinRowNeedMinForecast",
            "units_to_produce_by_autobagger": "unitsToProduceByAutobagger",
            "inventory": "inventory",
            "discards": "discards",
            "pulls": "pulls",
            "total_on_hand": "totalOnHand",
            "on_hand_min_production_needs": "onHandMinProductionNeeds",
            "in_progress_hj": "inProgressHj",
            "awaiting_delivery": "awaitingDelivery",
            "not_delivered": "notDelivered",
            "buffer_quantity": "bufferQuantity",
        }
        for field, json_name in name_mapping.items():
            assert getattr(weekly_table_data, field) == ui_utils.comma_separated_str(
                weekly_depletion_data[json_name]
            ), f"Field {field} does not match"

        assert consolidated_table_data.site == depletion_api_data["site"]
        assert consolidated_table_data.brand == depletion_api_data["brand"]


@pytest.mark.parametrize("week", [CURRENT_WEEK, NEXT_WEEK])
def test_contextual_po_ingredient_depletion(page: Page, week):
    """
    Test PO dropdown for Ingredient Depletion boards for current_week and next_week on Buyer dashboard.

    Test steps:
    1.Generate required data (needed data for set up po dropdown and ingred depl, status should be != NO IMPACT)
    2.Open Buyer Dashboard, pick the buyer and open the po dropdown
    3.Extract table data from Po Status dropdown (depends on the week)
    4.Check that columns headers are met with expected
    5.Check that data in columns are met with data from response
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, emergency_reason=EmergencyReason.SAFETY_STOCK, week=week
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand, week=week)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit=mealkit, ingredient=ingredient
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(site_config=site_config, sku=sku, po=po, week=week)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=site_config)
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    oscars = TestOscar.generate_oscar_for_several_weeks(
        site_config=site_config, sku=sku, value=data_generator.random_int(6), week=week
    )
    mock_plan_calculation = TestMockPlanCalculation.generate_mock_plan(site_config=site_config)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_hybrid_needs(*hybrid_needs)
        .with_oscar_forecast(*oscars)
        .with_mock_plan_calculation(mock_plan_calculation)
        .with_buyer_sku(buyer_sku)
        .with_hj_receipts(hj_receipt)
    ):
        buyer_page = BuyerPage(page=page)
        buyer_page.open()
        buyer_page.select_buyer(user=buyer_sku.user)
        buyer_page.open_dropdown_by_sku_code(sku_code=sku.sku_code)
        api_response = ImtPoStatusClient().get_ingredient_depletion_po_dropdown(
            site_config=site_config, week=week, sku=sku
        )
        api_response_data = base_steps.process_response_and_extract_data(
            response=api_response, additional_keys=[site_config.site.code], element_index=0
        )

        buyer_page.check_dropdown_headers()
        table_data = buyer_page.get_dropdown_table_data()[0]

        assert table_data.supplier == api_response_data["supplier"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.sku == api_response_data["sku"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.scheduled_delivery_date == api_response_data["scheduledDeliveryDate"]
        assert table_data.po_status == api_response_data["poStatus"]
        assert table_data.receiving_variance_in_units == api_response_data["receiveVariance"]
        assert table_data.order_size == api_response_data["orderSize"]
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(
            api_response_data["casePrice"], two_digits_after_decimal_point=True
        )
        assert table_data.case_size == api_response_data["caseSize"]
        assert table_data.quantity_ordered == api_response_data["quantityOrdered"]
        assert table_data.quantity_received == api_response_data["quantityReceived"]
        assert table_data.cases_received == api_response_data["casesReceived"]
        assert table_data.case_size_received == api_response_data["caseSizeReceived"]
        assert table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.total_price_po == ui_utils.comma_separated_str_with_dollar(
            api_response_data["totalPrice"], two_digits_after_decimal_point=True
        )
        assert table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response_data["totalPriceReceived"], two_digits_after_decimal_point=True
        )
        assert table_data.emergency_reason == api_response_data["emergencyReason"]
        assert table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            api_response_data["forecastDeliveryPercent"]
        )


def test_pos_dashboards_on_buyer_board(page: Page):
    """
    Test Po Status boards (Purchase Orders - current week and POs Scheduled for Delivery Today) on Buyer dashboard.

    Test steps:
    1.Generate required data (needed data for set up po satus boards, for Purchase Orders - cur_week po should be
     not_delivered_past_due or received_over or received_under, for POs Scheduled for Delivery Today delivery_time_start
     should be today)
    2.Open Buyer Dashboard, pick the buyer
    3.Extract table data from Po Status boards
    4.Check that columns headers are met with expected
    5.Check that data in columns are met with data from response
    """
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po_received_under = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, emergency_reason=EmergencyReason.SAFETY_STOCK, delivery_time_start=YESTERDAY_DATETIME
    )
    po_delivery_today = TestPurchaseOrder.generate_po_with_sku(
        site=site_config,
        sku=sku,
        delivery_time_start=CURRENT_DATETIME,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
    )
    hj_receipt_accurate = TestHJReceipts.generate_hj_receipt(
        po=po_delivery_today,
        site_config=site_config,
        sku=sku,
        quantity_received=int(po_delivery_today.first_line().qty),
    )
    hj_receipt_under = TestHJReceipts.generate_hj_receipt(
        po=po_received_under,
        site_config=site_config,
        quantity_received=int(po_received_under.first_line().qty) - data_generator.random_int(),
    )
    user = Users.test_user.value
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    po_status_board_name = PO_CURRENT_WEEK_BOARD.format(week=ui_utils.get_week_str(week=CURRENT_WEEK))
    po_today_board_name = PO_SCHEDULED_FOR_DELIVERY_TODAY_BOARD
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(user)
        .with_skus(sku)
        .with_purchase_orders(po_delivery_today, po_received_under)
        .with_purchasing_category(PurchasingCategories.random_value(), sku)
        .with_buyer_sku(buyer_sku)
        .with_hj_receipts(hj_receipt_under, hj_receipt_accurate)
        .with_forecast_upload(oscar)
    ):
        response = BuyerClient().get_buyer(week=CURRENT_WEEK)
        api_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)[CURRENT_WEEK_STR]
        purchase_orders_api_data = api_data["poStatus"][0]
        po_delivery_today_api_data = api_data["poStatusToday"][0]
        buyer_page = BuyerPage(page=page)
        buyer_page.open()
        buyer_page.select_buyer(buyer_sku.user)

        for name in [po_status_board_name, po_today_board_name]:
            column_headers = buyer_page.get_column_headers_on_buyer_by_board_name(board_name=name)
            assert column_headers == PO_STATUS_BUYER_EXPECTED_HEADERS

        purchase_orders_table_data = buyer_page.get_po_table_data_by_board_name(board_name=po_status_board_name)[0]
        po_status_consolidated_table_data = buyer_page.get_consolidated_table_data(board_name=po_status_board_name)[0]
        po_delivery_today_table_data = buyer_page.get_po_table_data_by_board_name(board_name=po_today_board_name)[0]
        po_today_consolidated_table_data = buyer_page.get_consolidated_table_data(board_name=po_today_board_name)[0]
        name_mapping = {
            "supplier": "supplier",
            "po_number": "poNumber",
            "sku_id": "sku",
            "sku_name": "skuName",
            "category": "category",
            "scheduled_delivery_date": "scheduledDeliveryDate",
            "po_status": "poStatus",
            "receiving_variance_in_units": "receiveVariance",
            "order_size": "orderSize",
            "case_price": "casePrice",
            "case_size": "caseSize",
            "case_size_received": "caseSizeReceived",
            "quantity_ordered": "quantityOrdered",
            "quantity_received": "quantityReceived",
            "cases_received": "casesReceived",
            "total_price_po": "totalPrice",
            "total_price_received": "totalPriceReceived",
            "emergency_reason": "emergencyReason",
        }
        for field, json_name in name_mapping.items():
            if field in ["case_price", "total_price_po", "total_price_received"]:
                assert getattr(po_delivery_today_table_data, field) == ui_utils.comma_separated_str_with_dollar(
                    po_delivery_today_api_data[json_name]
                ), f"Field {field} does not match"
                assert getattr(purchase_orders_table_data, field) == ui_utils.comma_separated_str_with_dollar(
                    purchase_orders_api_data[json_name]
                ), f"Field {field} does not match"
            else:
                assert (
                    getattr(po_delivery_today_table_data, field) == po_delivery_today_api_data[json_name]
                ), f"Field {field} does not match"
                assert (
                    getattr(purchase_orders_table_data, field) == purchase_orders_api_data[json_name]
                ), f"Field {field} does not match"
        assert purchase_orders_table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            purchase_orders_api_data["forecastDeliveryPercent"]
        )
        assert po_delivery_today_table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            po_delivery_today_api_data["forecastDeliveryPercent"]
        )
        for field in ["site", "brand"]:
            assert (
                getattr(po_today_consolidated_table_data, field) == po_delivery_today_api_data[field]
            ), f"Field {field} does not match"
            assert (
                getattr(po_status_consolidated_table_data, field) == purchase_orders_api_data[field]
            ), f"Field {field} does not match"
