from playwright.sync_api import Page

from automated_tests.data.constants.base_constants import PurchasingCategories, SiteConfigs, Users
from automated_tests.data.constants.date_time_constants import (
    CURRENT_WEEK,
    CURRENT_WEEK_FIRST_DAY,
    CURRENT_WEEK_SECOND_DAY,
    START_WEEK_DATETIME,
)
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.highjump import TestHJInvSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.calculations.expected_daily_need_page import ExpectedDailyNeedPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.calculations.expected_daily_need_client import ExpectedDailyNeedClient
from automated_tests.utils.ui_utils import comma_separated_str
from procurement.constants.hellofresh_constant import ReceiveInputType


def test_expected_daily_need_cells(page: Page):
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(
        sku=sku, site_config=site_config, week=CURRENT_WEEK, day=START_WEEK_DATETIME
    )
    hj_inv_snapshot = TestHJInvSnapshot.generate_hj_inv_snapshot(
        sku=sku,
        site_config=site_config,
        snapshot_date=CURRENT_WEEK_FIRST_DAY,
        expiration_date=CURRENT_WEEK_SECOND_DAY,
        updated_ts=CURRENT_WEEK_FIRST_DAY,
    )
    # we need pos and hj_receipt to check closingStock (to make inbound not 0)
    po_received_on_first_weekday = TestPurchaseOrder.generate_po_with_sku(
        site=site_config, sku=sku, delivery_time_start=START_WEEK_DATETIME
    )
    hj_receipt = TestHJReceipts.generate_hj_receipt(
        po=po_received_on_first_weekday,
        site_config=site_config,
        receipt_time_est=START_WEEK_DATETIME,
    )
    buyer_sku = TestBuyerSku.generate_buyer_sku(site_config=site_config, sku=sku)
    commodity_group = generate_string()
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_hybrid_needs(hybrid_needs)
        .with_hj_inv_snapshot(hj_inv_snapshot)
        .with_purchase_orders(po_received_on_first_weekday)
        .with_hj_receipts(hj_receipt)
        .with_buyer_sku(buyer_sku)
        .with_commodity_group(group=commodity_group, site=site_config.site.code, sku=sku)
        .with_purchasing_category(PurchasingCategories.random_value(), sku=sku)
    ):
        response = ExpectedDailyNeedClient().get_expected_daily_need(site_config=site_config)
        api_response_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        api_response_data_first_weekday = api_response_data["days"][0]

        expected_daily_need_page = ExpectedDailyNeedPage(page=page)
        expected_daily_need_page.open()
        expected_daily_need_page.check_expected_daily_need_column_headers()
        actual_table_data = expected_daily_need_page.get_expected_daily_need_table_data_first_weekday()[0]
        actual_ingredient_summary_data = expected_daily_need_page.get_ingredient_summary_table_data()[0]

        assert actual_ingredient_summary_data.sku_code == api_response_data["skuCode"]
        assert actual_ingredient_summary_data.sku_name == api_response_data["skuName"]
        assert actual_ingredient_summary_data.buyer == api_response_data["buyers"]
        assert actual_ingredient_summary_data.category == api_response_data["purchasingCategory"]
        assert actual_ingredient_summary_data.commodity_group == api_response_data["commodityGroup"]
        assert actual_table_data.carryover_stock == api_response_data_first_weekday["carryover"]
        assert actual_table_data.stock_buffer == api_response_data_first_weekday["stockBuffer"]
        assert (
            actual_table_data.consumption == api_response_data_first_weekday["consumption"]
        ), "Consumption should be equal to the sum of hybrid needs for this day"
        assert (
            actual_table_data.inbound == api_response_data_first_weekday["inbound"]
        ), "Inbound should include qty of orders received"
        assert actual_table_data.incoming_pos == comma_separated_str(
            api_response_data_first_weekday["incoming"]
        ), "Incoming should be equal to the sum(POs with delivery_time_start == 'this day' that were received or not)"
        assert (
            actual_table_data.stock_difference == api_response_data_first_weekday["stockDifference"]
        ), "If there is no data on previous day closing stock, Stock Difference is 0"
        assert actual_table_data.closing_stock == api_response_data_first_weekday["closingStock"]
        assert actual_table_data.daily_needs == api_response_data_first_weekday["dailyNeeds"]
