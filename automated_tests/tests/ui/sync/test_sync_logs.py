from playwright.sync_api import Page

from automated_tests.data.constants.base_constants import SiteConfigs, Users
from automated_tests.data.constants.sync_constants import LOGS_STEP_NAMES
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.imt.ingredients_depletion_page import IngredientsDepletionPage


def test_ingredient_depletion_sync(page: Page):
    """
    Test case checks sync work on IMT.
    Test steps:
    1. Open Ingredient Depletion page with generated base data
    2. Start sync process(expecting success) and check logs
    """
    site_config = SiteConfigs.random_value()
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_sites(site_config.site)
    ):
        ing_depletion_page = IngredientsDepletionPage(page=page)
        ing_depletion_page.open()
        ing_depletion_page.launch_sync()
        ing_depletion_page.show_sync_logs()
        ing_depletion_page.check_sync_in_progress()
        ing_depletion_page.open_logs_tree_if_closed()
        ing_depletion_page.check_step_names(expected_step_names=LOGS_STEP_NAMES)
