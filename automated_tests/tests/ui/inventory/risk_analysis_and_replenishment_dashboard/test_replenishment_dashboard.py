import dataclasses
from datetime import datetime, timedelta
from http import HTTPStatus

import pytest
from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.admin_constants import WH_TOTAL_REGION
from automated_tests.data.constants.base_constants import (
    Colors,
    EmergencyReason,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    CURRENT_WEEK_STR,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    DATE_TIME_FORMAT_4,
    DATE_TIME_FORMAT_5,
    NEXT_WEEK,
    PREVIOUS_WEEK,
    TOMORROW_DATE,
)
from automated_tests.data.models.buyer_sku import <PERSON><PERSON>uyerSku
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.inventory_snapshot import TestInventorySnapshot
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.pimt_replenishment import TestPimtReplenishment
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.replenishment_override import TestReplenishmentOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.strategy_manager import TestStrategyManager
from automated_tests.data.models.vendor_managed_inventory import TestVendorManagedInventory
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.inventory.replenishment_dashboard_page import ReplenishmentDashboardPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.inventory.risk_analysis_and_replenishment_client import ReplenishmentClient
from automated_tests.services.api.inventory.unified_inventory_module_client import UnifiedInventoryModuleClient
from automated_tests.utils import datetime_utils, ui_utils
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import InventoryInputType, ReceiveInputType, ReplenishmentType


def test_overview_forecasts_override_eov_net_total_undelivered_eow_wos_section_values(page: Page):
    """
    Test case checks Overview, Forecasts, On Order, EOV NET Inventory, Total Undelivered, EOW WOS sections on
    Replenishment Dashboard.
    Test steps:
    1. Generate required test data with sku, warehouse,site_config, inbound_po, replenishment, inventories, cycle_count,
    oscar, pallet_snapshot
    2. Open Replenishment Dashboard page, click on region and extract table data
    3. Make an API request and extract response data
    4. Check values from table data meet appropriate api response data.
    5. Check editable Sku comment on Replenishment
    """
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    po_inbound = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse, week=CURRENT_WEEK)
    po_not_delivered = TestPurchaseOrder.generate_inbound_po_with_sku(
        warehouse=warehouse, sku=sku, delivery_time_start=CURRENT_DATE - timedelta(days=10), week=PREVIOUS_WEEK
    )
    pos_received = [
        TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse, week=NEXT_WEEK + i)
        for i in range(9)
    ]
    user = Users.test_user.value
    replenishment = TestPimtReplenishment.generate_replenishment_by_po(
        po=po_not_delivered,
        replenishment_buyer=user.full_name,
        replenishment_type=ReplenishmentType.DYNAMIC,
    )
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_inbound, expiration_date=CURRENT_DATE)
    oscar = TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=site_config)
    purchasing_subcategory = data_generator.generate_string()
    buyer_sku = TestBuyerSku.generate_buyer_sku(user=user, site_config=site_config, sku=sku)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config)
    strategy_manager = TestStrategyManager.generate_strategy_manager(sku=sku)
    comments = [data_generator.generate_string() for _ in range(2)]
    replenishment_override = TestReplenishmentOverride.generate_replenishment_override(sku=sku, warehouse=warehouse)
    with (
        TestData(None)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_brands(site_config.brand)
        .with_users(user)
        .with_skus(sku)
        .with_purchasing_category(PurchasingCategories.random_value(exclude=[PurchasingCategories.PACKAGING]), sku)
        .with_purchase_orders(po_inbound, *pos_received, po_not_delivered)
        .with_pimt_replenishment(replenishment)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_oscar_forecast(*oscar)
        .with_purchasing_subcategory(subcategory=purchasing_subcategory, sku=sku)
        .with_buyer_sku(buyer_sku)
        .with_hybrid_needs(hybrid_needs)
        .with_strategy_manager(strategy_manager)
        .with_replenishment_override(replenishment_override)
    ):
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(warehouse.region)
        replenishment_dashboard.check_replenishment_dashboard_column_headers()

        response = ReplenishmentClient().get_replenishment_dashboard(warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

        overview_section_table_data = replenishment_dashboard.get_overview_table_data()[0]
        assert overview_section_table_data.sku == actual_data["sku"]
        assert overview_section_table_data.sku_name == actual_data["skuName"]

        row_item = replenishment_dashboard.get_table_row()
        unpinned_overview_section_table_data = replenishment_dashboard.get_unpinned_overview_table_data(row_item)
        assert unpinned_overview_section_table_data.replenishment_type == actual_data["replenishmentType"]
        assert unpinned_overview_section_table_data.buyer == actual_data["buyer"]
        assert unpinned_overview_section_table_data.strategy_manager == actual_data["strategyManager"]
        assert unpinned_overview_section_table_data.replenishment_buyer == actual_data["replenishmentBuyer"]
        assert unpinned_overview_section_table_data.category == actual_data["category"]
        assert unpinned_overview_section_table_data.sub_category == actual_data["subCategory"]
        assert unpinned_overview_section_table_data.brand == actual_data["brand"]

        # check Inventory Targets table data
        inventory_targets_table_data = replenishment_dashboard.get_inventory_targets_table_data(row=row_item)
        assert inventory_targets_table_data.leadTime == actual_data["leadTime"]

        # check Forecasts table data
        forecasts_table_data = replenishment_dashboard.get_forecasts_table_data(row=row_item)
        assert forecasts_table_data.total_forecast == actual_data["totalForecast"]
        assert forecasts_table_data.average_weekly_demand == actual_data["averageWeeklyDemand"]
        assert forecasts_table_data.in_week_row_need == actual_data["inWeekRowNeed"]
        for index, value in enumerate(
            forecasts_table_data.get_all_values(exclude=["total_forecast", "average_weekly_demand", "in_week_row_need"])
        ):
            assert value == actual_data["forecasts"][str(CURRENT_WEEK + index)]

        # check Override table data, check editable Total on Hand column
        override_table_data = replenishment_dashboard.get_override_table_data(row=row_item)
        assert override_table_data.totalOnHandOverride == str(actual_data["totalOnHandOverride"])
        replenishment_dashboard.check_total_on_hand_column_background_color(expected_color=Colors.YELLOW_ORANGE)

        # check On Order table data
        on_order_table_data = replenishment_dashboard.get_on_order_table_data(row=row_item)
        assert on_order_table_data.total_inbounds == actual_data["totalInbounds"]
        assert on_order_table_data.total_undelivered == actual_data["totalUndelivered"]
        for index, value in enumerate(
            on_order_table_data.get_all_values(exclude=["total_inbounds", "total_undelivered"])
        ):
            assert value == actual_data["inbounds"][str(CURRENT_WEEK + index)]

        # check EOW NET Inventory table data
        eow_net_table_data = replenishment_dashboard.get_eow_net_inventory_table_data(row=row_item)
        for index, value in enumerate(eow_net_table_data.get_all_values()):
            assert value == actual_data["endOfWeek"][str(CURRENT_WEEK + index)]

        # check EOW WOS table data
        eow_wos_table_data = replenishment_dashboard.get_eow_wos_table_data(row=row_item)
        for index, value in enumerate(eow_wos_table_data.get_all_values()):
            assert value == actual_data["endOfWeekWos"][str(CURRENT_WEEK + index)]

        value_to_edit = str(data_generator.random_int())
        replenishment_dashboard.add_edit_total_on_hand_override_column(value=value_to_edit)
        override_table_data = replenishment_dashboard.get_override_table_data(row=row_item)
        assert override_table_data.totalOnHandOverride == value_to_edit
        # check API response data after updating Total on Hand
        response = ReplenishmentClient().get_replenishment_dashboard(warehouse=warehouse)
        actual_data_after_edit = base_steps.process_response_and_extract_data(response=response, element_index=0)
        # check EOW NET Inventory table data after editing Total on Hand override column
        eow_net_table_data = replenishment_dashboard.get_eow_net_inventory_table_data(row=row_item)
        for index, value in enumerate(eow_net_table_data.get_all_values()):
            assert value == actual_data_after_edit["endOfWeek"][str(CURRENT_WEEK + index)]

        for comment in comments:
            replenishment_dashboard.add_or_edit_comment_and_get_time_on_replenishment(sku=sku, comment=comment)
            actual_sku_comment = replenishment_dashboard.get_sku_name_comment_on_replenishment(sku=sku)
            expected_comment = replenishment_dashboard.create_expected_comment(comment_text=comment)
            assert actual_sku_comment == expected_comment
        replenishment_dashboard.remove_sku_comment_from_replenishment(sku=sku)


def test_replenishment_dashboard_inventory_dropdown_values(page: Page):
    """
    Test case checks sku name dropdown values on Replenishment Dashboard.
    Test steps:
    1. Generate required test data with site_config, warehouse, sku, inbound_po, inventory, oscar, replenishment
    2. Open Replenishment Dashboard page, click on region ,click on sku_name dropdown and extract table data
    3. Make an API request with unified_inventory endpoint (it's the source endpoint) and extract response data
    4. Check dropdown headers, check values from dropdown table data meet appropriate api response data.
    """
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    sku = TestSku.generate_sku()
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    po = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    inventory_expiring = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=inbound_po, expiration_date=TOMORROW_DATE, warehouse=warehouse
    )
    replenishment = TestPimtReplenishment.generate_replenishment_by_po(po=inbound_po, warehouse=warehouse)
    with (
        TestData(None)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchasing_category(PurchasingCategories.random_value(exclude=[PurchasingCategories.PACKAGING]), sku)
        .with_purchase_orders(inbound_po, po)
        .with_pimt_replenishment(replenishment)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory_expiring)
    ):
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.open_dropdown_by_sku(sku=sku.sku_name)
        replenishment_dashboard.check_inventory_dropdown_headers()
        dropdown_table_data = replenishment_dashboard.get_inventory_dropdown_table_data()[0]
        dropdown_response = UnifiedInventoryModuleClient().get_unified_inventory_module()
        actual_dropdown_data = base_steps.process_response_and_extract_data(response=dropdown_response, element_index=0)

        name_mapping = {
            "source": "source",
            "site": "site",
            "site_name": "siteName",
            "sku_code": "skuCode",
            "sku_name": "skuName",
            "brand": "brands",
            "category": "category",
            "status": "status",
            "location_id": "locationId",
            "lot": "lot",
            "po_number": "poNumber",
            "supplier": "supplier",
        }
        for field, json_name in name_mapping.items():
            assert (
                getattr(dropdown_table_data, field) == actual_dropdown_data[json_name]
            ), f"Field {field} doesn't match"
        assert dropdown_table_data.expiration_date == (
            datetime.strptime(actual_dropdown_data["expirationDate"], DATE_FORMAT_2).strftime(DATE_FORMAT_1)
        )
        assert dropdown_table_data.quantity == ui_utils.comma_separated_str(actual_dropdown_data["quantity"])
        assert dropdown_table_data.snapshot_timestamp == (
            datetime_utils.reformat_datetime_str(
                date_string=actual_dropdown_data["snapshotTimestamp"],
                input_date_format=DATE_TIME_FORMAT_4,
                output_date_format=DATE_TIME_FORMAT_5,
            )
        )


@pytest.mark.parametrize("is_3pl", [True, False])
def test_on_hand_and_inventory_costing_sections(page: Page, is_3pl):
    """
    Test case checks On Hand and Inventory Costing sections values on Replenishment Dashboard.
    Test steps:
    1. Generate required test data with sku, warehouse, site_config, inbound_po, replenishment, inventories
    2. Open Replenishment Dashboard page, click on region and extract table data
    3. Make an API request and extract response data
    4. Check values from On Hand table data meet appropriate api response data.
    """
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    site_config = dataclasses.replace(WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse), is_3pl=is_3pl)
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    replenishment = TestPimtReplenishment.generate_replenishment_by_po(po=inbound_po, warehouse=warehouse)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=inbound_po, warehouse=warehouse)
    inventory_site = TestPimtUnifiedInventory.generate_inventory_by_site_config(po=inbound_po, site_config=site_config)
    vendor_managed_inventory = TestVendorManagedInventory.generate_vendor_managed_inventory(
        region="".join(warehouse.packaging_regions), supplier_name=inbound_po.supplier_name, sku=sku
    )
    with (
        TestData(None)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_warehouses(warehouse)
        .with_purchase_orders(inbound_po)
        .with_pimt_replenishment(replenishment)
        .with_pimt_inventory(inventory, inventory_site)
        .with_vendor_managed_inventory(vendor_managed_inventory)
    ):
        response = ReplenishmentClient().get_replenishment_dashboard(warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.expand_on_hand_section(expand=True)
        row_item = replenishment_dashboard.get_table_row()
        on_hand_table_data = replenishment_dashboard.get_on_hand_table_data(row=row_item)
        assert on_hand_table_data.totalOnHand == actual_data["totalOnHand"]
        assert on_hand_table_data.totalInventoryTpl == actual_data["totalInventoryTpl"]
        assert on_hand_table_data.totalInventoryCore == actual_data["totalInventoryCore"]
        assert on_hand_table_data.vendorManagedInventory == actual_data["vendorManagedInventory"]
        assert on_hand_table_data.totalInventoryWarehouse == actual_data["totalInventoryWarehouse"]
        assert on_hand_table_data.expiredInventory == actual_data["expiredInventory"]
        assert on_hand_table_data.totalInventoryWos == actual_data["totalInventoryWos"]
        assert on_hand_table_data.expiringSoonInventory == actual_data["expiringSoonInventory"]

        replenishment_dashboard.expand_inventory_costing_section()
        inventory_costing_table_data = replenishment_dashboard.get_inventory_costing_table_data(row=row_item)
        assert inventory_costing_table_data.totalCost == ui_utils.comma_separated_str_with_dollar(
            actual_data["totalCost"]
        )
        assert inventory_costing_table_data.dcCost == ui_utils.comma_separated_str_with_dollar(actual_data["dcCost"])
        assert inventory_costing_table_data.tplCost == ui_utils.comma_separated_str_with_dollar(actual_data["tplCost"])
        assert inventory_costing_table_data.tpwCost == ui_utils.comma_separated_str_with_dollar(actual_data["tpwCost"])


def test_replenishment_po_dropdown(page: Page):
    """
    Test PO Dropdown on Replenishment dashboard.

    Test steps:
    1. Generate required test data
    2. Open Replenishment Dashboard page, click on region, open PO dropdown by sku_code and extract data
    3. Make an API request and extract response data
    4. Check values from table data meet appropriate api response data.
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.receiving_type = ReceiveInputType.GRN
    warehouse = WarehouseUtils.get_warehouse_by_site_config(site_config=site_config)
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    inventory = TestPimtUnifiedInventory.generate_inventory_by_site_config(
        po=po, expiration_date=TOMORROW_DATE, site_config=site_config
    )
    grn = TestGrn.generate_grn_by_site_config(
        po=po, site_config=site_config, units_received=po.first_line().case_size + data_generator.random_int()
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    oscar = TestOscar.generate_oscar(site_config=site_config, sku=sku, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_grn(grn)
        .with_po_shipment(po_shipment)
        .with_oscar_forecast(oscar)
    ):
        response = ReplenishmentClient().get_replenishment_po_status_dropdown(warehouse=warehouse, sku=sku)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.open_dropdown_by_sku_code(sku_code=sku.sku_code)
        replenishment_dashboard.check_dropdown_column_headers_on_popup()
        table_data = replenishment_dashboard.get_dropdown_table_data()[0]
        assert table_data.supplier == actual_data["supplier"]
        assert table_data.po_number == actual_data["poNumber"]
        assert table_data.sku_code == actual_data["sku"]
        assert table_data.sku_name == actual_data["skuName"]
        assert table_data.scheduled_delivery_date == actual_data["scheduledDeliveryDate"]
        assert table_data.po_status == actual_data["poStatus"]
        assert table_data.receiving_variance_in_units == ui_utils.comma_separated_str(actual_data["receiveVariance"])
        assert table_data.appointment_time == datetime_utils.reformat_datetime_str(
            date_string=actual_data["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.order_size == ui_utils.comma_separated_str(actual_data["orderSize"])
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(actual_data["casePrice"])
        assert table_data.case_size == ui_utils.comma_separated_str(actual_data["caseSize"])
        assert table_data.quantity_ordered == ui_utils.comma_separated_str(actual_data["quantityOrdered"])
        assert table_data.quantity_received == ui_utils.comma_separated_str(actual_data["quantityReceived"])
        assert table_data.cases_received == ui_utils.comma_separated_str(actual_data["casesReceived"])
        assert table_data.case_size_received == ui_utils.comma_separated_str(actual_data["caseSizeReceived"])
        assert table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=actual_data["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.total_price == ui_utils.comma_separated_str_with_dollar(actual_data["totalPrice"])
        assert table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            actual_data["totalPriceReceived"]
        )
        assert table_data.ship_method == actual_data["shipMethod"]
        assert table_data.emergency_reason == actual_data["emergencyReason"]
        assert table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            actual_data["forecastDeliveryPercent"]
        )


def test_added_po_popup_for_undelivered_pos_on_replenishment(page: Page):
    """
    Test case checks added PO dropdown for Undelivered pos.
    Test steps:
    1. Generate required test data with site_config, warehouse, undelivered_po, inventory, po_shipment
    2. Make an API request to undelivered_po_status endpoint -> 200, extract response data
    3. Open Inventory -> Risk Analysis and Replenishment page, click on Total Undelivered arrow button, extract data
    4. Check column headers, check values from table data meet appropriate API response value for warehouse region and
    Total section.
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    warehouse = WarehouseUtils.get_warehouse_by_site_config(site_config=site_config)
    po_not_delivered = TestPurchaseOrder.generate_inbound_po_with_sku(
        warehouse=warehouse,
        sku=sku,
        delivery_time_start=CURRENT_DATE - timedelta(days=10),
        week=PREVIOUS_WEEK,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    inventory = TestPimtUnifiedInventory.generate_inventory_by_site_config(
        po=po_not_delivered, site_config=site_config, expiration_date=TOMORROW_DATE
    )
    po_shipment = TestPOShipment.generate_po_shipment(po=po_not_delivered)
    with (
        TestData(None)
        .with_site_configs(site_config)
        .with_sites(site_config.site)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po_not_delivered)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_po_shipment(po_shipment)
    ):
        response = ReplenishmentClient().get_undelivered_pos_po_status_dropdown(sku=sku, warehouse=warehouse)
        actual_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        for region in [warehouse.region, WH_TOTAL_REGION]:
            replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=region)
            replenishment_dashboard.expand_on_order_section(expand=True)
            replenishment_dashboard.click_on_total_undelivered_arrow_button(
                value=ui_utils.comma_separated_str(po_not_delivered.first_line().qty)
            )
            replenishment_dashboard.check_undelivered_pos_po_dropdown_expected_headers()
            dropdown_table_data = replenishment_dashboard.get_undelivered_pos_po_dropdown_table_data()[0]
            assert dropdown_table_data.supplier == actual_data["supplier"]
            assert dropdown_table_data.po_number == actual_data["poNumber"]
            assert dropdown_table_data.sku_code == actual_data["sku"]
            assert dropdown_table_data.sku_name == actual_data["skuName"]
            assert dropdown_table_data.scheduled_delivery_date == actual_data["scheduledDeliveryDate"]
            assert dropdown_table_data.po_status == actual_data["poStatus"]
            assert dropdown_table_data.order_size == ui_utils.comma_separated_str(actual_data["orderSize"])
            assert dropdown_table_data.case_price == ui_utils.comma_separated_str_with_dollar(actual_data["casePrice"])
            assert dropdown_table_data.case_size == ui_utils.comma_separated_str(actual_data["caseSize"])
            assert dropdown_table_data.quantity_ordered == ui_utils.comma_separated_str(actual_data["quantityOrdered"])
            assert dropdown_table_data.total_price == ui_utils.comma_separated_str_with_dollar(
                actual_data["totalPrice"]
            )
            assert dropdown_table_data.emergency_reason == actual_data["emergencyReason"]
            assert dropdown_table_data.ship_method == actual_data["shipMethod"]
            if region == warehouse.region:
                replenishment_dashboard.click_on_close_button()


def test_forecast_dropdown_on_replenishment(page: Page):
    """
    Test forecast dropdown on 10 Week Forecast column. It shows all 10 weeks of the forecast.

    Test steps:
    1. Generate required test data (sku, site_config, warehouse, forecasts for next 10 weeks)
    2. Open Replenishment Dashboard page
    3. Expand forecast dropdown, check column headers and extract table data
    4. Check that values in forecast dropdown are equal to expected
    """
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    site_config = WarehouseUtils.get_site_config_by_warehouse(warehouse=warehouse)
    forecasts = TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=site_config)
    with (
        TestData(None)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_warehouses(warehouse)
        .with_oscar_forecast(*forecasts)
    ):
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.expand_forecast_dropdown()
        replenishment_dashboard.check_forecast_dropdown_column_headers()

        table_data = replenishment_dashboard.get_forecast_dropdown_table_data()
        expected_dc = [data.dc for data in table_data]
        expected_weeks = [item.week for item in table_data]
        expected_sku_code = [item.sku_code for item in table_data]
        expected_forecasts_values = [item.forecast for item in table_data]

        assert [item.site.code for item in forecasts] == expected_dc
        assert [item.week for item in forecasts] == expected_weeks
        assert [item.sku.sku_code for item in forecasts] == expected_sku_code
        assert [ui_utils.comma_separated_str(item.value) for item in forecasts] == expected_forecasts_values


def test_added_yellow_flag_on_eow_net_inventory(page: Page):
    """
    Test case checks added yellow flag on Eow Net Inventory section
    Test steps:
    1. Generate required test data with site_config with WMSL type, inv_snapshot, replenishment, hybrid_needs
    2. Open Risk Analysis and Replenishment page
    3. Check Eow Net Inventory background color for Current week is highlighted in yellow.
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    site_config.inventory_type = InventoryInputType.WMSL
    warehouse = WarehouseUtils.get_warehouse_by_site_config(site_config=site_config)
    po = TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config)
    inventory_snapshot_current_day = TestInventorySnapshot.generate_inventory_snapshot(
        wh_code=site_config.bob_code, sku=sku, expiration_date=TOMORROW_DATE
    )
    replenishment = TestPimtReplenishment.generate_replenishment_by_po(po=po)
    hybrid_needs = TestHybridNeeds.generate_hybrid_needs(
        sku=sku,
        site_config=site_config,
        qty=0.9 * float(inventory_snapshot_current_day.unit_quantity + po.first_line().qty),
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_inventory_snapshot(inventory_snapshot_current_day)
        .with_pimt_replenishment(replenishment)
        .with_oscar_forecast(oscar)
        .with_hybrid_needs(hybrid_needs)
        .with_warehouses(warehouse)
    ):
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.check_eow_inventory_current_week_background_color(expected_color=Colors.OLIVE)


def test_high_forecast_and_eow_negative_value_flags(page: Page):
    """
    Test case checks orange flag on Forecast and red flag on EOW Net section
    Test steps:
    1. Generate required test data
    2. Open Risk Analysis and Replenishment page
    3. Select warehouse region, expand forecast section and check for current week it is highlighted in orange
    4. Edit Total On Hand override value, make an API request, check override value is crated and check endOfWeekFlags
    (flag should return True for all weeks expect Current Week which has positive value)
    4. Extract table data for Eow Net Inventory section and check if value <= 0 week is highlighted in red
    """
    sku = TestSku.generate_sku()
    site_config = SiteConfigs.random_value()
    warehouse = WarehouseUtils.get_warehouse_by_site_config(site_config=site_config)
    forecast_current_week = TestOscar.generate_oscar(
        sku=sku, site_config=site_config, week=CURRENT_WEEK, value=data_generator.random_int(5)
    )
    forecasts_next_weeks = TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=site_config, week=NEXT_WEEK)
    inbound_po = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    inbound_po.first_line().qty = data_generator.random_int()
    with (
        TestData(None)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_brands(site_config.brand)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(inbound_po)
        .with_oscar_forecast(*forecasts_next_weeks, forecast_current_week)
        .with_warehouses(warehouse)
    ):
        replenishment_dashboard = ReplenishmentDashboardPage(page=page)
        replenishment_dashboard.open()
        replenishment_dashboard.select_site_or_region_from_site_bar(site_or_region=warehouse.region)
        replenishment_dashboard.expand_forecasts_section()
        replenishment_dashboard.check_forecast_section_current_week_background_color(expected_color=Colors.RICH_ORANGE)

        value_to_edit = str(data_generator.random_int(1))
        replenishment_dashboard.add_edit_total_on_hand_override_column(value=value_to_edit)
        override_response = ReplenishmentClient().add_replenishment_override_data(
            warehouse=warehouse, sku=sku, value=int(value_to_edit)
        )
        actual_data = base_steps.process_response_and_extract_data(
            response=override_response, expected_length=2, status_code=HTTPStatus.CREATED
        )
        row_item = replenishment_dashboard.get_table_row()
        eow_net_table_data = replenishment_dashboard.get_eow_net_inventory_table_data(row=row_item)
        for week, value in actual_data["endOfWeek"].items():
            assert value > 0 if week == CURRENT_WEEK_STR else value < 0

        # check flag - current week has a positive value that's why it is not highlighted
        for week, flag in actual_data["endOfWeekFlags"].items():
            assert flag is (False if week == CURRENT_WEEK_STR else True)
        for index, value in enumerate(eow_net_table_data.get_all_values()):
            if index == 0:
                assert value > 0
            else:
                replenishment_dashboard.check_eow_inv_next_weeks_background_color(expected_color=Colors.RED_ORANGE)
