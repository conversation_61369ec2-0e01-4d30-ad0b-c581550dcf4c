from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    <PERSON><PERSON>,
    SiteConfigs,
    SiteConfigsFactor,
    Sites,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, PREVIOUS_WEEK
from automated_tests.data.constants.error_messages_constants import REQUIRED_ERROR_MESSAGE, TWELVE_CHARACTERS_LIMIT
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.network_depletion_preset import TestNetworkDepletionPreset
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData
from automated_tests.pages_new.inventory.network_depletion_page import NetworkDepletionPage
from automated_tests.services.api.inventory.network_depletion_client import NetworkDepletionModuleClient


def test_preset_configuration(page: Page):
    """
    Test creation and reset of preset configuration.

    Test steps:
    1.Generate required data
    2.Open Network Depletion Page
    3.Check if preset is not configured and 'No Data' is displayed
    4.Open preset configuration from one of presets and click on the edit button
    5.Change preset name and pick specific site and warehouse according to set up data
    6.Get table data and assert that data are equal to picked
    7.Click on the edit button one more time, click on the reset button, save the result and check that on the board
    the data are not displayed
    """
    site_config = SiteConfigs.random_value()
    site_configs = SiteConfigs.get_all_values()
    for site in site_configs:
        site.week = PREVIOUS_WEEK
    warehouse = Warehouses.random_value()
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config)
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config.brand)
    ingredient = TestIngredient(sku, site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_warehouses(*Warehouses.get_all_values())
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_pimt_inventory(inventory)
        .with_oscar_forecast(oscar)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.is_no_data_message_present()
        network_depl_page.select_preset("Preset 2")
        network_depl_page.edit_selected_preset(
            preset_name=data_generator.generate_string(str_length=12),
            site_configs=[site_config],
            warehouses=[warehouse],
        )

        table_data = network_depl_page.get_network_depletion_pinned_table_data(expand=True)[0]
        assert table_data.site == ", ".join(sorted([site_config.site.code, warehouse.code]))
        assert table_data.brand == site_config.brand.code

        network_depl_page.open_edit_selected_preset_modal()
        network_depl_page.click_reset_button()
        network_depl_page.click_save_button()
        network_depl_page.is_no_data_message_present()


def test_preset_configuration_availability(page: Page):
    """
    Test preset configuration availability. If HF/EP/GC site is selected, FJ DCs/3PLs should be grayed out and can’t be
    selected by the user. (does not affect warehouses). Similarly, if FJ site is selected, users shouldn’t be able to
    select HF/EP/GC sites. (does not affect warehouses)

    Test steps:
    1.Generate required data
    2.Open Network Depletion Page
    3.Click on the edit button
    4.Pick specific site (for HF or EP brand) and warehouse according to set up data
    5.Check that the sites for Factor can’t be selected, but warehouses are available
    6.Pick specific site (for FJ brand) and warehouse according to set up data
    7.Check that the sites for HF/EP can’t be selected, but warehouses are available
    8.Click on the edit button and clear preset name field
    9.Check that error message that Preset name should be required and save button is disabled
    10.Edit preset name with 13 characters and check that "12 characters is allowed" appears and save button is disabled
    """
    site_config, site_config_factor = SiteConfigs.NJ_HF.value, SiteConfigsFactor.GA_FJ.value
    warehouse = Warehouses.random_value()
    with (
        TestData(None)
        .with_brands(site_config_factor.brand, site_config.brand)
        .with_sites(site_config.site, site_config_factor.site)
        .with_site_configs(*SiteConfigs.get_all_values(), *SiteConfigsFactor.get_all_values())
        .with_users(Users.test_user.value)
        .with_warehouses(*Warehouses.get_all_values())
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.open_edit_selected_preset_modal()

        network_depl_page.choose_sites_and_warehouses(
            site_configs=[site_config_factor],
        )
        assert not network_depl_page.check_site_button_is_enabled(
            brand_or_warehouse_title=site_config.brand.name, site_and_warehouse=site_config.site.code
        )
        assert network_depl_page.check_site_button_is_enabled(
            brand_or_warehouse_title="Warehouses", site_and_warehouse=warehouse.code
        )
        network_depl_page.click_reset_button()

        network_depl_page.choose_sites_and_warehouses(site_configs=[site_config])

        assert not network_depl_page.check_site_button_is_enabled(
            brand_or_warehouse_title=site_config_factor.brand.name, site_and_warehouse=site_config_factor.site.code
        )
        assert network_depl_page.check_site_button_is_enabled(
            brand_or_warehouse_title="Warehouses", site_and_warehouse=warehouse.code
        )

        network_depl_page.clear_preset_name()
        assert network_depl_page.get_preset_name_error_message() == REQUIRED_ERROR_MESSAGE
        network_depl_page.check_save_button_is_enabled(expect_enable=False)

        network_depl_page.set_preset_name_in_modal(preset_name=data_generator.generate_string(str_length=13))
        assert network_depl_page.get_preset_name_error_message() == TWELVE_CHARACTERS_LIMIT
        network_depl_page.check_save_button_is_enabled(expect_enable=False)


def test_preset_configuration_switch(page: Page):
    """
    Test switch between different preset configuration.

    Test steps:
    1.Generate required data
    2.Open Network Depletion Page
    3.Choose Preset 1  and click on the edit button, pick specific site and save the result
    4.Choose Preset 2 and click on the edit button, pick specific site and save the result
    5.Click on the first preset and check that data are equal to set up for first preset
    6.Click on the second preset and check that data are equal to set up for second preset
    """
    site_config_nj, site_config_ca = SiteConfigs.NJ_HF.value, SiteConfigs.CA_HF.value
    site_configs_prev_week = SiteConfigs.get_all_values()
    for site in site_configs_prev_week:
        site.week = PREVIOUS_WEEK
    site_configs = [site_config_nj, site_config_ca]
    sku = TestSku.generate_sku()
    po = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    mealkit = TestMealkit.generate_mealkit_by_brand(site_config_nj.brand)
    ingredient = TestIngredient(sku, site_config_nj.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*site_configs_prev_week)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(*po)
        .with_oscar_forecast(*oscars)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.is_no_data_message_present()
        network_depl_page.select_preset("Preset 1")
        preset_1_response = NetworkDepletionModuleClient().get_network_depletion(preset_id=1)
        network_depl_page.edit_selected_preset(site_configs=[site_config_nj])
        network_depl_page.wait_for_api_response(response_url=preset_1_response.url)

        network_depl_page.select_preset("Preset 2")
        preset_2_response = NetworkDepletionModuleClient().get_network_depletion(preset_id=2)
        network_depl_page.edit_selected_preset(site_configs=[site_config_ca])
        network_depl_page.wait_for_api_response(response_url=preset_2_response.url)

        network_depl_page.select_preset("Preset 1")
        table_data = network_depl_page.get_network_depletion_pinned_table_data(expand=True)[0]
        assert table_data.site == site_config_nj.site.code

        network_depl_page.select_preset("Preset 2")
        table_data = network_depl_page.get_network_depletion_pinned_table_data(expand=True)[0]
        assert table_data.site == site_config_ca.site.code


def test_cancel_preset_configuration(page: Page):
    """
    Test canceling preset configuration.

    Test steps:
    1.Generate required data
    2.Open Network Depletion Page
    3.Choose preset and click on the edit button
    4.Pick specific site, save the result and check that the data are equal to set up
    5.Click on the edit button one more time, pick specific site and click on the cancel button
    6.Check that the data after click cancel button was not saved and on the board only data that was previously set up
    """
    site_config_nj, site_config_ca = SiteConfigs.NJ_HF.value, SiteConfigs.CA_HF.value
    site_configs_prev_week = SiteConfigs.get_all_values()
    for site in site_configs_prev_week:
        site.week = PREVIOUS_WEEK
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_nj)
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config_nj.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config_nj.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    oscars = [
        TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs_prev_week
    ]
    with (
        TestData(None)
        .with_brands(*Brands.get_all_values())
        .with_sites(*Sites.get_all_values())
        .with_site_configs(*site_configs_prev_week)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_purchase_orders(po)
        .with_oscar_forecast(*oscars)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.select_preset("Preset 1")
        network_depl_page.edit_selected_preset(site_configs=[site_config_nj])

        table_data = network_depl_page.get_network_depletion_pinned_table_data(expand=True)[0]
        assert table_data.site == site_config_nj.site.code
        assert table_data.brand == site_config_nj.brand.code

        network_depl_page.open_edit_selected_preset_modal()
        network_depl_page.choose_sites_and_warehouses(site_configs=[site_config_ca])

        network_depl_page.click_cancel_button()
        table_data = network_depl_page.get_network_depletion_pinned_table_data()[0]
        assert table_data.site == site_config_nj.site.code
        assert table_data.brand == site_config_nj.brand.code


def test_reset_preset_configuration(page: Page):
    """
    Test reset preset configuration.

    Test steps:
    1.Generate required data (site_config, warehouse, preset with them selected)
    2.Open Network Depletion Page
    3.Choose preset and click on the edit button
    4.Change preset name
    5.Click RESET button
    6.Verify that the modified Name field, Sites, and Warehouses have been reset
    """
    site_config = SiteConfigs.random_value()
    warehouse = Warehouses.random_value()
    preset_name = "Preset 1"
    preset = TestNetworkDepletionPreset.create_preset(
        name=preset_name, site_configs=[site_config], warehouses=[warehouse]
    )
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_warehouses(warehouse)
        .with_network_depletion_presets(preset)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.select_preset(preset.name)
        network_depl_page.open_edit_selected_preset_modal()
        network_depl_page.set_preset_name_in_modal(data_generator.generate_string())
        network_depl_page.click_reset_button()
        assert network_depl_page.get_preset_name() == preset_name
        assert network_depl_page.get_selected_sites_and_warehouses() == []
