import dataclasses
from datetime import timed<PERSON>ta

import pytest
from playwright.sync_api import Page

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import (
    Brands,
    Colors,
    EmergencyReason,
    IcsStatus,
    PurchasingCategories,
    ShippingMethods,
    SiteConfigs,
    SiteConfigsFactor,
    Sites,
    Users,
    Warehouses,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    DATE_FORMAT_1,
    DATE_FORMAT_2,
    DATE_TIME_FORMAT_1,
    DATE_TIME_FORMAT_3,
    DATE_TIME_FORMAT_4,
    DATE_TIME_FORMAT_5,
    DATE_TIME_FORMAT_14,
    DAY_FORMAT,
    NEXT_WEEK,
    NEXT_WEEK_FACTOR,
    NEXT_WEEK_FIRST_DAY,
    PREVIOUS_WEEK,
    TOMORROW_DATE,
    WEEK_FIRST_MONDAY,
    WEE<PERSON>_FIRST_TUESDAY,
    WEEK_LENGTH,
    YES<PERSON><PERSON><PERSON>Y_DATE,
    YESTERDAY_DATETIME,
)
from automated_tests.data.models.allocation_price import TestAllocationPrice
from automated_tests.data.models.allowed_produce_buffer import TestAllowedProduceBuffer
from automated_tests.data.models.bulk_sku import TestBulkSkus
from automated_tests.data.models.buyer_sku import TestBuyerSku
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.highjump import TestHJDiscard, TestHJPalletSnapshot, TestHJReceipts, TestHJWip
from automated_tests.data.models.hybrid_needs import TestHybridNeeds, TestHybridNeedsStatus
from automated_tests.data.models.ics_tickets import TestIcsTicket
from automated_tests.data.models.ingredient import TestIngredient, TestMealkit, TestMealkitIngredient
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.mock_plan_calculation import TestMockPlanCalculation
from automated_tests.data.models.network_depletion_preset import TestNetworkDepletionPreset
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.po_shipment import TestPOShipment
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.sku import TestSku
from automated_tests.data.test_data import TestData, TestSkuCategory
from automated_tests.pages_new.inventory.network_depletion_page import NetworkDepletionPage
from automated_tests.services.api.api_steps import base_steps
from automated_tests.services.api.imt.ics_tickets_client import IcsTicketClient
from automated_tests.services.api.inventory.network_depletion_client import NetworkDepletionModuleClient
from automated_tests.utils import datetime_utils, ui_utils
from procurement.constants.hellofresh_constant import PRODUCTION_TYPE_ASSEMBLY, DayOfWeek, ReceiveInputType
from procurement.constants.ordering import CLOSED, IN_PROGRESS_HJ
from procurement.data.models.highjump.highjump import DiscardType


def test_ingredient_summary_units_needed_and_status_sections_on_ndm(page: Page):
    """
    Test Ingredient Summary, Units Needed and Status sections.

    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard)
    2. Open Network Depletion Page, check editable sku comment field (for Preset 2 generate the same preset and check
    comment is added after updating Preset 1, for Preset 3 generate preset without one site to check comment is not
    shown there, remove comment on Preset 1 -> check on Preset 2 comment is filtered out accordingly)
    3. Get table data from ingredient summary section and check that table data meets with the API response
    4. Click on the Arrow button on Brand field and get table data -> 3 rows appeared, totally 4 rows
    5. Get table data from units needed and status sections and check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    po = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po, warehouse=warehouse)
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    purchasing_category = TestSkuCategory(sku=sku, ingredient_category=PurchasingCategories.random_value())
    commodity_group = data_generator.generate_string()
    buyer_skus = [TestBuyerSku.generate_buyer_sku(sku=sku, site_config=site_config) for site_config in site_configs]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    hybrid_needs = [
        TestHybridNeeds.generate_hybrid_needs_for_several_days(sku=sku, site_config=config) for config in site_configs
    ]
    hybrid_need_statuses = [
        TestHybridNeedsStatus.generate_hybrid_needs_statuses(site_config=config) for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    comments = [data_generator.generate_string() for _ in range(2)]
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_purchasing_categories(purchasing_category)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, po)
        .with_commodity_group(commodity_group, site_config_hf.site.code, sku)
        .with_oscar_forecast(*oscars)
        .with_hybrid_needs_status(*[item for sublist in hybrid_need_statuses for item in sublist])
        .with_hybrid_needs(*[item for sublist in hybrid_needs for item in sublist])
        .with_buyer_sku(*buyer_skus)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()
        for comment in comments:
            network_depletion_page.add_or_edit_comment_and_get_time_on_network_depletion(sku=sku, comment=comment)
            actual_sku_comment = network_depletion_page.get_sku_name_comment_on_network_depletion(sku=sku)
            expected_comment = network_depletion_page.create_expected_comment(comment_text=comment)
            assert actual_sku_comment == expected_comment

        network_depletion_page.select_preset("Preset 2")
        network_depletion_page.edit_selected_preset(site_configs=site_configs, warehouses=[warehouse])
        actual_preset_2_comment = network_depletion_page.get_sku_name_comment_on_network_depletion(sku=sku)
        assert actual_preset_2_comment == expected_comment

        network_depletion_page.select_preset("Preset 3")
        network_depletion_page.edit_selected_preset(site_configs=[site_config_hf], warehouses=[warehouse])
        network_depletion_page.check_sku_name_comment_popup_is_not_visible(sku=sku)
        network_depletion_page.select_preset("Preset 1")
        network_depletion_page.remove_sku_comment_from_network_depletion(sku=sku)
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        api_network_depl_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        network_depletion_page.wait_for_api_response(response_url=response.url)
        table_data = network_depletion_page.get_network_depletion_pinned_table_data(response_url=response.url)[0]
        name_mapping = [
            ("site", "site"),
            ("brand", "brand"),
            ("sku", "sku"),
            ("buyer", "buyer"),
            ("sku_name", "skuName"),
            ("category", "category"),
            ("commodity_group", "commodityGroup"),
            ("impacted_recipes", "impactedRecipes"),
            ("unit_of_measure", "unitOfMeasure"),
        ]

        for field, ui_name in name_mapping:
            assert getattr(table_data, field) == api_network_depl_data[ui_name], f"Field {field} does not match"

        network_depletion_page.open_brands_dropdown_by_sku_code(sku=sku)
        table_rows = network_depletion_page.get_network_depletion_pinned_table_data()
        assert len(table_rows) == 4

        first_row = network_depletion_page.get_table_row()
        table_data = network_depletion_page.get_network_units_needed_and_status_table_data(row=first_row)
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        name_mapping = [
            ("plan", "plan"),
            ("planned_production", "plannedProduction"),
            ("row_forecast", "rowForecast"),
            ("forecast_oscar", "forecastOscar"),
        ]
        for field, ui_name in name_mapping:
            assert getattr(table_data, field) == ui_utils.comma_separated_str(
                api_data[ui_name]
            ), f"Field {field} does not match"

        assert table_data.delta == ui_utils.to_percent_string(api_data["delta"])
        assert table_data.sn == api_data["sn"]
        assert table_data.supplement_need_hj == api_data["supplementNeedHj"]
        assert table_data.critical_delivery == api_data["criticalDelivery"]
        network_depletion_page.select_preset("Preset 2")
        network_depletion_page.check_sku_name_comment_popup_is_not_visible(sku=sku)


@pytest.mark.parametrize("receiving_type", [ReceiveInputType.HIGH_JUMP, ReceiveInputType.MANUAL])
def test_weekly_overview_section_on_ndm(page: Page, receiving_type):
    """
    Test Weekly Overview section.

    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard)
    2. Open Network Depletion Page
    3. Get table data from units needed and status sections
    4. Make API call
    5. Check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, receiving_type=receiving_type, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    warehouse = Warehouses.random_value()
    po_for_inventory = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf, week=NEXT_WEEK)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_for_inventory, warehouse=warehouse)
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config, week=NEXT_WEEK) for config in site_configs]
    po_not_delivered = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, delivery_time_start=CURRENT_DATE - timedelta(days=10), week=NEXT_WEEK
        )
        for config in site_configs
    ]
    po_autobagger = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, supplier_name="Autobagger - " + config.site.code, week=NEXT_WEEK
        )
        for config in site_configs
    ]
    po_in_progress_hj = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku,
            site=config,
            delivery_time_start=NEXT_WEEK_FIRST_DAY + timedelta(days=10),
            week=NEXT_WEEK,
        )
        for config in site_configs
    ]
    hj_receipt_in_progress_hj = [
        TestHJReceipts.generate_hj_receipt(
            site_config=config,
            status=IN_PROGRESS_HJ,
            receipt_time_est=YESTERDAY_DATE,
            po=po,
            week=NEXT_WEEK,
            quantity_received=0,
        )
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hj_receipt = [
        TestHJReceipts.generate_hj_receipt(site_config=config, po=po, week=NEXT_WEEK)
        for config, po in zip(site_configs, pos)
    ]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand, week=NEXT_WEEK) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [
        TestOscar.generate_oscar_for_several_weeks(sku=sku, site_config=config, week=NEXT_WEEK)
        for config in site_configs
    ]
    mock_plan = TestMockPlanCalculation.generate_mock_plan(
        site_config=site_config_hf, production_type=PRODUCTION_TYPE_ASSEMBLY, week=NEXT_WEEK
    )
    receipt_overrides = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po, week=NEXT_WEEK)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        sku=sku, site_config=site_config_hf, week=NEXT_WEEK
    )
    hj_discard = TestHJDiscard.generate_discard(
        sku=sku,
        site_config=site_config_hf,
        tran_type=DiscardType.DISCARD,
        discard_date=CURRENT_DATE + timedelta(days=WEEK_LENGTH),
    )
    hj_wip_discard = TestHJDiscard.generate_discard(
        sku=sku,
        site_config=site_config_hf,
        tran_type=DiscardType.WIP_DISCARD,
        discard_date=CURRENT_DATE + timedelta(days=WEEK_LENGTH),
    )
    hj_wip = TestHJWip(sku=sku, hj_name=site_config_hf.hj_name, quantity=data_generator.random_int())
    hj_donation_discard = [
        TestHJDiscard.generate_discard(
            site_config=sc,
            sku=sku,
            tran_type=DiscardType.DONATION,
            discard_date=CURRENT_DATE + timedelta(days=WEEK_LENGTH),
        )
        for sc in site_configs
    ]
    hybrid_needs_hf = [
        TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config_hf, week=CURRENT_WEEK, day=day)
        for day in CURRENT_WEEK.week_days()
    ]
    hybrid_needs_ep = [
        TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config_ep, week=CURRENT_WEEK, day=day)
        for day in CURRENT_WEEK.week_days()
    ]
    inventory_pull_put = [
        TestInventoryPullPut.generate_inventory_pull_put(site_config=config, sku=sku, week=NEXT_WEEK)
        for config in site_configs
    ]
    discard = [
        TestDiscard.generate_discard(
            site_config=config, sku=sku, week=NEXT_WEEK, discarded_datetime=CURRENT_DATE + timedelta(days=WEEK_LENGTH)
        )
        for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    price_hf = TestAllocationPrice.generate_allocation_price(
        sku=sku, site_config=site_config_hf, price=data_generator.random_int(1), week=NEXT_WEEK
    )
    price_ep = TestAllocationPrice.generate_allocation_price(
        sku=sku, site_config=site_config_ep, price=data_generator.random_int(2), week=NEXT_WEEK
    )
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_mock_plan_calculation(mock_plan)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, *po_in_progress_hj, *po_autobagger, *po_not_delivered, po_for_inventory)
        .with_oscar_forecast(*[item for sublist in oscars for item in sublist])
        .with_receipt_override(*receipt_overrides)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_hj_discard(hj_discard, hj_wip_discard, *hj_donation_discard)
        .with_hj_wip(hj_wip)
        .with_hybrid_needs(*hybrid_needs_hf, *hybrid_needs_ep)
        .with_inventory_pull_put(*inventory_pull_put)
        .with_hj_receipts(*hj_receipt, *hj_receipt_in_progress_hj)
        .with_discard(*discard)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
        .with_allocation_price(price_ep, price_hf)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()
        network_depletion_page.choose_week_from_dropdown(week=NEXT_WEEK)

        first_row = network_depletion_page.get_table_row()
        table_data = network_depletion_page.get_network_depletion_weekly_table_data(row=first_row)
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id, week=NEXT_WEEK)
        api_aggregated_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["weekly"]
        name_mapping = [
            ("units_needed", "unitsNeeded"),
            ("units_ordered", "unitsOrdered"),
            ("units_received", "unitsReceived"),
            ("units_in_house", "unitsInHouseHj"),
            ("hj_discards", "hjDiscards"),
            ("units_on_production_floor", "unitsOnProductionFloor"),
            ("row_need", "rowNeed"),
            ("units_in_house_minus_row_need", "unitsInHouseMinusRowNeed"),
            ("start_of_the_day_inventory", "startOfTheDayInventory"),
            ("start_of_the_day_inventory_minus_units_in_house_hj", "startOfTheDayInventoryMinusUnitsInHouseHj"),
            ("previous_week_row_need", "previousWeekRowNeed"),
            ("next_week_forecast", "nextWeekForecast"),
            ("units_in_house_min_row_need_min_forecast", "unitsInHouseMinRowNeedMinForecast"),
            ("units_to_produce_by_autobagger", "unitsToProduceByAutobagger"),
            ("inventory", "inventory"),
            ("discards", "discards"),
            ("donations", "donations"),
            ("pulls", "pulls"),
            ("total_on_hand", "totalOnHand"),
            ("on_hand_min_production_needs", "onHandMinProductionNeeds"),
            ("in_progress_hj", "inProgressHj"),
            ("awaiting_delivery", "awaitingDelivery"),
            ("not_delivered", "notDelivered"),
            ("buffer_quantity", "bufferQuantity"),
        ]
        for field, ui_name in name_mapping:
            assert getattr(table_data, field) == ui_utils.comma_separated_str(
                api_aggregated_data[ui_name]
            ), f"Field {field} does not match"

        assert table_data.buffer_percent == ui_utils.to_percent_string(api_aggregated_data["bufferPercent"])
        assert table_data.allocation_price == "${:.2f} - ${:.2f}".format(
            float(api_aggregated_data["allocationPrice"]["min"]), float(api_aggregated_data["allocationPrice"]["max"])
        )


def test_allocation_price_with_specific_price(page: Page):
    """
    Test Allocation Price value with specific price. If allocation prices are the same then it shows as 1 value instead
    of range of prices.

    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard, allocation price)
    2. Open Network Depletion Page
    3. Get table data from units needed and status sections
    4. Make API call
    5. Check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    allocation_price = data_generator.random_int()
    price = [
        TestAllocationPrice.generate_allocation_price(sku=sku, site_config=config, price=allocation_price)
        for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(*oscars)
        .with_allocation_price(*price)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()

        first_row = network_depletion_page.get_table_row()
        table_data = network_depletion_page.get_network_depletion_weekly_table_data(row=first_row)
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id, week=CURRENT_WEEK)
        api_aggregated_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["weekly"]
        assert table_data.allocation_price == "${:.2f}".format(float(api_aggregated_data["allocationPrice"]["max"]))


def test_bulk_sku_values_section_on_ndm(page: Page):
    """
    Test Bulk Values section.
    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard)
    2. Open Network Depletion Page
    3. Get table data from bulk values section
    4. Make API call
    5. Check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP, week=PREVIOUS_WEEK
    )
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku, bulk_sku = TestSku.generate_skus(sku_quantity=2)
    warehouse = Warehouses.random_value()
    po_for_inventory = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    inventory = TestPimtUnifiedInventory.generate_inventory_by_po(po=po_for_inventory, warehouse=warehouse)
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=bulk_sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(
        sku=bulk_sku,
        site_config=site_config_hf,
    )
    bulk_skus = TestBulkSkus.generate_bulk_skus(packaged_sku=sku, bulk_sku=bulk_sku, brands=[site_config_hf.brand])
    bulk_skus.brands = [site_config.brand.code for site_config in site_configs]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs, warehouses=[warehouse])
    hj_discards = [
        TestHJDiscard.generate_discard(sku=bulk_sku, site_config=config, tran_type=DiscardType.DISCARD)
        for config in site_configs
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku, bulk_sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, po_for_inventory)
        .with_receipt_override(*receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_bulk_skus(bulk_skus)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory)
        .with_network_depletion_presets(preset)
        .with_hj_discard(*hj_discards)
        .with_oscar_forecast(*oscars)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()

        first_row = network_depletion_page.get_table_row()
        table_data = network_depletion_page.get_network_depletion_bulk_table_data(row=first_row)
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["bulkValues"]
        name_mapping = [
            ("bulk_units_ordered", "bulkUnitsOrdered"),
            ("bulk_units_received", "bulkUnitsReceived"),
            ("bulk_discards", "bulkDiscards"),
            ("delta", "delta"),
            ("bulk_units_in_hj", "bulkUnitsInHj"),
        ]

        for field, ui_name in name_mapping:
            assert getattr(table_data, field) == ui_utils.comma_separated_str(
                api_data[ui_name]
            ), f"Field {field} does not match"


def test_buffer_analysis_section_on_ndm(page: Page):
    """
    Test Buffer Analysis section.
    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard)
    2. Open Network Depletion Page
    3. Get table data from buffer analysis section
    4. Make API call
    5. Check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value,
        receiving_type=ReceiveInputType.HIGH_JUMP,
    )
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    hj_pallet_snapshot = TestHJPalletSnapshot.generate_hj_pallet_snapshot(sku=sku, site_config=site_config_hf)
    allowed_buffer = [
        TestAllowedProduceBuffer.generate_allowed_buffer(site_config=config, sku=sku) for config in site_configs
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos)
        .with_oscar_forecast(*oscars)
        .with_receipt_override(*receipt_override)
        .with_hj_palette_snapshot(hj_pallet_snapshot)
        .with_purchasing_category(PurchasingCategories.PRODUCE, sku)
        .with_network_depletion_presets(preset)
        .with_allowed_buffer(*allowed_buffer)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()
        first_row = network_depletion_page.get_table_row()
        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        table_data = network_depletion_page.get_network_depletion_buffer_table_data(
            row=first_row, response_url=response.url
        )
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)["bufferAnalysis"]
        assert table_data.allowed_buffer == ui_utils.to_percent_string(api_data["allowedBuffer"])
        assert table_data.po_buffer == ui_utils.to_percent_string(api_data["poBuffer"])
        assert table_data.buffer_quantity_needed == ui_utils.comma_separated_str(api_data["bufferQuantityNeeded"])
        assert table_data.supplier == api_data["supplier"]
        assert table_data.case_yield == ui_utils.comma_separated_str(api_data["caseYield"])
        assert table_data.case_total == ui_utils.comma_separated_str(api_data["caseTotal"])
        assert table_data.case_cost == ui_utils.comma_separated_str_with_dollar(api_data["caseCost"])
        assert table_data.total_cost == ui_utils.comma_separated_str_with_dollar(api_data["totalCost"])


def test_daily_section_on_ndm(page: Page):
    """
    Test Daily section. We check only 2 first weekdays, cause all days are the same,
    but Monday first column is different

    Test steps:
    1. Generate required data (2 site configs, warehouse, data for Network Depletion dashboard)
    2. Open Network Depletion Page
    3. Get table data from dailies sections (Monday and Tuesday only)
    4. Make API call
    5. Check that table data meets with the API response
    """
    site_config_hf = dataclasses.replace(
        SiteConfigs.NJ_HF.value, receiving_type=ReceiveInputType.HIGH_JUMP, week=PREVIOUS_WEEK
    )
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, hj_name="ASDF", site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    pos = [TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config) for config in site_configs]
    po_in_progress_hj = [
        TestPurchaseOrder.generate_po_with_sku(
            sku=sku, site=config, delivery_time_start=CURRENT_DATE + timedelta(days=10)
        )
        for config in site_configs
    ]
    po_received = [
        TestPurchaseOrder.generate_po_with_sku(sku=sku, site=config, delivery_time_start=YESTERDAY_DATE)
        for config in site_configs
    ]
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    inventory_pull_put = [
        TestInventoryPullPut.generate_inventory_pull_put(site_config=config, sku=sku) for config in site_configs
    ]
    discard = [TestDiscard.generate_discard(site_config=config, sku=sku) for config in site_configs]
    hj_receipt_in_progress_hj = [
        TestHJReceipts.generate_hj_receipt(
            site_config=config,
            status=IN_PROGRESS_HJ,
            # receipt_time_est - 5h because of est timezone
            receipt_time_est=YESTERDAY_DATETIME - timedelta(hours=5),
            po=po,
        )
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hj_receipt_closed = [
        TestHJReceipts.generate_hj_receipt(site_config=config, status=CLOSED, po=po)
        for config, po in zip(site_configs, po_in_progress_hj)
    ]
    hybrid_needs = [TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=config) for config in site_configs]
    receipt_override = [
        TestReceiptOverride.generate_receipt_override(site_config=config, po=po)
        for config, po in zip(site_configs, pos)
    ]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(*pos, *po_in_progress_hj, *po_received)
        .with_oscar_forecast(*oscars)
        .with_receipt_override(*receipt_override)
        .with_inventory_pull_put(*inventory_pull_put)
        .with_discard(*discard)
        .with_hj_receipts(*hj_receipt_in_progress_hj, *hj_receipt_closed)
        .with_hybrid_needs(*hybrid_needs)
        .with_network_depletion_presets(preset)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()
        network_depletion_page.click_on_show_hide_daily_columns_button()
        network_depletion_page.check_network_depletion_column_headers()
        first_row = network_depletion_page.get_table_row()
        table_data = network_depletion_page.get_network_depletion_daily_table_data(row=first_row)

        response = NetworkDepletionModuleClient().get_network_depletion(preset_id=preset.id)
        api_data = base_steps.process_response_and_extract_data(response=response, element_index=0)
        api_data_monday = api_data["daily"][WEEK_FIRST_MONDAY.strftime(DATE_FORMAT_1)]
        api_data_tuesday = api_data["daily"][WEEK_FIRST_TUESDAY.strftime(DATE_FORMAT_1)]

        name_mapping_monday = [
            ("confirmed_inventory_monday", "inventory"),
            ("units_delivered_monday", "unitsDelivered"),
            ("discards_monday", "discards"),
            ("on_hand_monday", "onHand"),
            ("production_needs_monday", "productionNeeds"),
            ("eod_inventory_prod_monday", "eodInventoryProduction"),
            ("units_on_order_monday", "unitsOnOrder"),
            ("eod_inventory_order_monday", "eodInventoryOrder"),
            ("total_production_need_monday", "totalProductionNeed"),
            ("total_on_hand_minus_total_production_need_monday", "totalOnHandMinusTotalProductionNeed"),
        ]
        name_mapping_tuesday = [
            ("start_of_day_inventory_tuesday", "inventory"),
            ("discards_tuesday", "discards"),
            ("on_hand_tuesday", "onHand"),
            ("production_needs_tuesday", "productionNeeds"),
            ("eod_inventory_prod_tuesday", "eodInventoryProduction"),
            ("units_on_order_tuesday", "unitsOnOrder"),
            ("eod_inventory_order_tuesday", "eodInventoryOrder"),
            ("total_production_need_tuesday", "totalProductionNeed"),
            ("total_on_hand_minus_total_production_need_tuesday", "totalOnHandMinusTotalProductionNeed"),
        ]
        for field, ui_name in name_mapping_monday:
            assert getattr(table_data, field) == ui_utils.comma_separated_str(
                api_data_monday[ui_name]
            ), f"Field {field} does not match"

        for field, ui_name in name_mapping_tuesday:
            assert getattr(table_data, field) == ui_utils.comma_separated_str(
                api_data_tuesday[ui_name]
            ), f"Field {field} does not match"
        assert table_data.status_in_week_monday == api_data_monday["statusInWeek"]
        assert table_data.status_monday == api_data_monday["status"]
        assert table_data.status_in_week_tuesday == api_data_tuesday["statusInWeek"]
        assert table_data.status_tuesday == api_data_tuesday["status"]


def test_ongoing_week_on_ndm(page: Page):
    """
    Test case checks week dropdown is showing the current week for HF and Factor brands.
    Test steps:
    1. Generate required data - site_configs for HF and Factor brands
    2. Open the Network Depletion Page
    3. Click on Edit preset, set prepared preset, and Factor brand
    4. Choose next week and check that current_week_factor is shown and is highlighted as the current week
    5. Edit the same preset setting for the other brand - HF, and save
    6. Choose next week and check current_week for HF is shown and is highlighted as the current week
    """
    site_config_hf, site_config_factor = SiteConfigs.NJ_HF.value, SiteConfigsFactor.GA_FJ.value
    site_configs = [site_config_factor, site_config_hf]
    with (
        TestData(None)
        .with_brands(*(config.brand for config in site_configs))
        .with_sites(*(config.site for config in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()

        for site_config in site_configs:
            network_depl_page.edit_selected_preset(site_configs=[site_config])
            if site_config == site_config_factor:
                network_depl_page.choose_week_from_dropdown(week=NEXT_WEEK_FACTOR)
                network_depl_page.check_week_item_color_dropdown(week=CURRENT_WEEK_FACTOR, expected_color=Colors.CYAN)
            else:
                network_depl_page.choose_week_from_dropdown(week=NEXT_WEEK)
                network_depl_page.check_week_item_color_dropdown(week=CURRENT_WEEK, expected_color=Colors.CYAN)


def test_ics_tickets_dropdown_on_ndm(page: Page):
    """
    Test ICS tickets dropdown on NDM.

    Test steps:
    1. Generate required data (2 site configs, data for Network Depletion dashboard, 2 ics tickets)
    2. Open Network Depletion Page
    3. Click on ticket button to open ics ticket dropdown
    4. Extract table data, check that here is 2 records (for both site configs) and check column headers
    5. Check that table data meets with the API response
    6. Check that ticket id color is gray for record where the status is closed, and orange for open|in progress status
    """
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=PREVIOUS_WEEK)
    site_config_ep = dataclasses.replace(site_config_hf, brand=Brands.EP.value, site=Sites.NJ_EP.value)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    po_hf = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_hf)
    po_ep = TestPurchaseOrder.generate_po_with_sku(sku=sku, site=site_config_ep)
    mealkits = [TestMealkit.generate_mealkit_by_brand(brand=config.brand) for config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=config.brand) for config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit=mealkit, ingredient=ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    oscars = [TestOscar.generate_oscar(sku=sku, site_config=config, week=CURRENT_WEEK) for config in site_configs]
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    ics_ticket_hf_open = TestIcsTicket.generate_ics_ticket(
        site_config=site_config_hf,
        po=po_hf,
        ticket_id=data_generator.random_int(3),
        status=IcsStatus.random_value(exclude=[IcsStatus.CLOSED]),
    )
    ics_ticket_ep_closed = TestIcsTicket.generate_ics_ticket(
        site_config=site_config_ep, po=po_ep, status=IcsStatus.CLOSED
    )
    with (
        TestData(None)
        .with_brands(*(site_config.brand for site_config in site_configs))
        .with_sites(*(c.site for c in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_purchase_orders(po_hf, po_ep)
        .with_oscar_forecast(*oscars)
        .with_network_depletion_presets(preset)
        .with_ics_ticket(ics_ticket_hf_open, ics_ticket_ep_closed)
    ):
        network_depletion_page = NetworkDepletionPage(page=page)
        network_depletion_page.open()

        response = IcsTicketClient().get_ics_tickets_data(sku=sku, site_configs=site_configs)
        api_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)
        api_data_by_bob_code = {item["bobCode"]: item for item in api_data}
        network_depletion_page.open_ics_ticket_dropdown_on_ndm_by_sku_code(sku_code=sku.sku_code)
        network_depletion_page.check_ics_ticket_dropdown_column_headers()

        ics_ticket_dropdown_data = network_depletion_page.get_ics_ticket_dropdown_table_data()
        assert len(ics_ticket_dropdown_data) == 2
        for row in range(len(ics_ticket_dropdown_data)):
            api_data = api_data_by_bob_code.get(ics_ticket_dropdown_data[row].bob_code)
            assert ics_ticket_dropdown_data[row].po_number == api_data["poNumber"]
            assert ics_ticket_dropdown_data[row].production_impact == api_data["productionImpact"]
            assert ics_ticket_dropdown_data[row].request_type == api_data["requestType"]
            assert ics_ticket_dropdown_data[row].sku_code == api_data["skuCode"]
            assert ics_ticket_dropdown_data[row].status == api_data["status"]
            assert ics_ticket_dropdown_data[row].subject == api_data["subject"]
            assert ics_ticket_dropdown_data[row].ticket_id == str(api_data["ticketId"])
            assert ics_ticket_dropdown_data[row].ticket_link == api_data["ticketLink"]
            assert ics_ticket_dropdown_data[row].updated_at == datetime_utils.reformat_datetime_str(
                api_data["updatedAt"], input_date_format=DATE_TIME_FORMAT_14, output_date_format=DATE_FORMAT_1
            )
            assert ics_ticket_dropdown_data[row].week == api_data["week"]
        network_depletion_page.check_ics_column_background_color(
            column_id="ticketId", column_value=str(ics_ticket_hf_open.ticket_id), expected_color=Colors.DARK_ORANGE
        )
        network_depletion_page.check_ics_column_background_color(
            column_id="ticketId", column_value=str(ics_ticket_ep_closed.ticket_id), expected_color=Colors.LIGHT_GRAY
        )


def test_ndm_po_dropdown(page: Page):
    """
    Test PO dropdown on NDM dashboard
    Test steps:
    1.Generate required test data (site config, data for NDM dashboard)
    2.Make an API call and extract data from the response
    3.Open NDM page
    4.Open po dropdown and check if columns headers are equal to the expected column headers
    5.Extract table data
    6.Check that the values from the table data are equal to the expected data from the api response
    """
    site_config = SiteConfigs.random_value()
    site_config.week = PREVIOUS_WEEK
    site_config.receiving_type = ReceiveInputType.HIGH_JUMP
    sku = TestSku.generate_sku()
    po = TestPurchaseOrder.generate_po_with_sku(
        sku=sku,
        site=site_config,
        emergency_reason=EmergencyReason.SAFETY_STOCK,
        shipping_method=ShippingMethods.VENDOR_DELIVERED,
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    hj_receipt = TestHJReceipts.generate_hj_receipt(po=po, site_config=site_config)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config])
    po_shipment = TestPOShipment.generate_po_shipment(po=po)
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_purchase_orders(po)
        .with_hj_receipts(hj_receipt)
        .with_network_depletion_presets(preset)
        .with_po_shipment(po_shipment)
        .with_mealkit(mealkit)
        .with_ingredients(ingredient)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_oscar_forecast(oscar)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion_po_dropdown(preset_id=preset.id, sku=sku)
        api_response_data = base_steps.process_response_and_extract_data(response=response, element_index=0)

        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.open_dropdown_by_sku(sku=sku.sku_code)
        network_depl_page.check_po_dropdown_column_headers()
        table_data = network_depl_page.get_po_dropdown_table_data()[0]
        assert table_data.supplier == api_response_data["supplier"]
        assert table_data.po_number == api_response_data["poNumber"]
        assert table_data.sku_code == api_response_data["sku"]
        assert table_data.sku_name == api_response_data["skuName"]
        assert table_data.scheduled_delivery_date == api_response_data["scheduledDeliveryDate"]
        assert table_data.po_status == api_response_data["poStatus"]
        assert table_data.receiving_variance_in_units == ui_utils.comma_separated_str(
            api_response_data["receiveVariance"]
        )
        assert table_data.appointment_time == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["appointmentTime"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.order_size == str(api_response_data["orderSize"])
        assert table_data.case_price == ui_utils.comma_separated_str_with_dollar(api_response_data["casePrice"])
        assert table_data.case_size == str(api_response_data["caseSize"])
        assert table_data.case_size_received == ui_utils.comma_separated_str(api_response_data["caseSizeReceived"])

        assert table_data.quantity_ordered == ui_utils.comma_separated_str(api_response_data["quantityOrdered"])
        assert table_data.quantity_received == ui_utils.comma_separated_str(api_response_data["quantityReceived"])
        assert table_data.cases_received == str(api_response_data["casesReceived"])

        assert table_data.date_received == datetime_utils.reformat_datetime_str(
            date_string=api_response_data["dateReceived"],
            input_date_format=DATE_TIME_FORMAT_1,
            output_date_format=DATE_TIME_FORMAT_3,
        )
        assert table_data.total_price == ui_utils.comma_separated_str_with_dollar(api_response_data["totalPrice"])
        assert table_data.total_price_received == ui_utils.comma_separated_str_with_dollar(
            api_response_data["totalPriceReceived"]
        )
        assert table_data.emergency_reason == api_response_data["emergencyReason"]
        assert table_data.ship_method == api_response_data["shipMethod"]
        assert table_data.phf_delivery_percent_of_forecast == ui_utils.to_percent_string(
            api_response_data["forecastDeliveryPercent"]
        )


def test_inventory_dropdown_on_ndm(page: Page):
    """
    Test case checks Inventory dropdown on Network Depletion Page.

    Test steps:
    1. Generate required data (site config, warehouse, data for Network Depletion board, inventories)
    2. Open NDM page
    3. Open Inventory dropdown by clicking the arrow button on "Units In House" column
    4. Check that column headers are equal to expected
    5. Check that column values in the Inventory dropdown meet with the API response data
    """
    site_config = SiteConfigs.random_value()
    warehouse = Warehouses.random_value()
    sku = TestSku.generate_sku()
    po_1 = TestPurchaseOrder.generate_inbound_po_with_sku(warehouse=warehouse, sku=sku)
    po_2 = TestPurchaseOrder.generate_po_with_sku(site=site_config, sku=sku)
    inventory_wh = TestPimtUnifiedInventory.generate_inventory_by_po(
        po=po_1, warehouse=warehouse, expiration_date=CURRENT_DATE
    )
    inventory_site = TestPimtUnifiedInventory.generate_inventory_by_site_config(
        po=po_2, site_config=site_config, expiration_date=TOMORROW_DATE
    )
    mealkit = TestMealkit.generate_mealkit_by_brand(brand=site_config.brand)
    ingredient = TestIngredient(sku=sku, brand=site_config.brand)
    mealkit_ingredient = TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=[site_config], warehouses=[warehouse])
    purchasing_category = TestSkuCategory(sku, PurchasingCategories.random_value())
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config, week=CURRENT_WEEK)
    with (
        TestData(None)
        .with_brands(site_config.brand)
        .with_sites(site_config.site)
        .with_site_configs(site_config)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredient)
        .with_mealkit(mealkit)
        .with_mealkit_ingredient(mealkit_ingredient)
        .with_warehouses(warehouse)
        .with_pimt_inventory(inventory_wh, inventory_site)
        .with_network_depletion_presets(preset)
        .with_purchase_orders(po_1, po_2)
        .with_purchasing_categories(purchasing_category)
        .with_oscar_forecast(oscar)
    ):
        response = NetworkDepletionModuleClient().get_network_depletion_inventory_dropdown(preset_id=preset.id, sku=sku)
        actual_data = base_steps.process_response_and_extract_data(response=response, expected_length=2)

        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.expand_inventory_dropdown()
        network_depl_page.check_inventory_dropdown_headers()
        inventory_api_data_by_site = {site["site"]: site for site in actual_data}
        dropdown_table_data = network_depl_page.get_inventory_dropdown_table_data()
        assert len(dropdown_table_data) == 2
        for row in range(len(dropdown_table_data)):
            if dropdown_table_data[row].site == site_config.bob_code:
                api_data = inventory_api_data_by_site[site_config.bob_code]
            else:
                api_data = inventory_api_data_by_site[warehouse.code]
            assert dropdown_table_data[row].source == api_data["source"]
            assert dropdown_table_data[row].site == api_data["site"]
            assert dropdown_table_data[row].site_name == api_data["siteName"]
            assert dropdown_table_data[row].sku_code == api_data["skuCode"]
            assert dropdown_table_data[row].sku_name == api_data["skuName"]
            assert dropdown_table_data[row].brand == api_data["brands"]
            assert dropdown_table_data[row].category == api_data["category"]
            assert dropdown_table_data[row].expiration_date == (
                datetime_utils.reformat_datetime_str(
                    date_string=api_data["expirationDate"],
                    input_date_format=DATE_FORMAT_2,
                    output_date_format=DATE_FORMAT_1,
                )
            )
            assert dropdown_table_data[row].quantity == ui_utils.comma_separated_str(api_data["quantity"])
            assert dropdown_table_data[row].status == api_data["status"]
            assert dropdown_table_data[row].location_id == api_data["locationId"]
            assert dropdown_table_data[row].lot == api_data["lot"]
            assert dropdown_table_data[row].po_number == api_data["poNumber"]
            assert dropdown_table_data[row].supplier == api_data["supplier"]
            assert dropdown_table_data[row].snapshot_timestamp == (
                datetime_utils.reformat_datetime_str(
                    date_string=api_data["snapshotTimestamp"],
                    input_date_format=DATE_TIME_FORMAT_4,
                    output_date_format=DATE_TIME_FORMAT_5,
                )
            )


def test_donations_dropdown_values_on_network_depletion(page: Page):
    """
    Test case checks Donation dropdown value on Weekly section on Network Depletion.

    Test steps:
    1. Generate required test data (hj_discards with Donations tran_type, preset, oscar, site configs)
    2. Open NDM page and choose week
    3. Expand Donation dropdown on consolidated row, check column headers and extract table data
    4. Check that table data meet data from API response
    """
    is_wednesday = CURRENT_DATE.strftime(DAY_FORMAT) == DayOfWeek.WEDNESDAY
    week = PREVIOUS_WEEK if is_wednesday else CURRENT_WEEK
    site_config_hf = dataclasses.replace(SiteConfigs.NJ_HF.value, week=week)
    site_config_ep = dataclasses.replace(SiteConfigs.CT_EP.value, week=week)
    site_configs = [site_config_hf, site_config_ep]
    sku = TestSku.generate_sku()
    mealkits = [TestMealkit.generate_mealkit_by_brand(site_config.brand, week=week) for site_config in site_configs]
    ingredients = [TestIngredient(sku=sku, brand=site_config.brand) for site_config in site_configs]
    mealkit_ingredient = [
        TestMealkitIngredient.generate_mealkit_ing_by_mealkit_and_ingredient(mealkit, ingredient)
        for mealkit, ingredient in zip(mealkits, ingredients)
    ]
    hj_donation_discards = [
        TestHJDiscard.generate_discard(site_config=site_config, sku=sku, tran_type=DiscardType.DONATION)
        for site_config in site_configs
    ]

    hj_discard = [TestHJDiscard.generate_discard(site_config=sc, sku=sku) for sc in site_configs]
    oscar = TestOscar.generate_oscar(sku=sku, site_config=site_config_hf, week=week)
    preset = TestNetworkDepletionPreset.create_preset(site_configs=site_configs)
    with (
        TestData(None)
        .with_brands(*(sc.brand for sc in site_configs))
        .with_sites(*(sc.site for sc in site_configs))
        .with_site_configs(*site_configs)
        .with_users(Users.test_user.value)
        .with_skus(sku)
        .with_ingredients(ingredients[0])
        .with_mealkit(*mealkits)
        .with_mealkit_ingredient(*mealkit_ingredient)
        .with_hj_discard(*hj_donation_discards, *hj_discard)
        .with_network_depletion_presets(preset)
        .with_oscar_forecast(oscar)
    ):
        network_depl_page = NetworkDepletionPage(page=page)
        network_depl_page.open()
        network_depl_page.choose_week_from_dropdown(week=week)
        network_depl_page.expand_donation_dropdown()
        network_depl_page.check_column_headers_on_discard_donation_dropdown()

        donations_dropdown_response = NetworkDepletionModuleClient().get_network_depletion_donation_dropdown(
            site_configs=site_configs, sku=sku, week=week
        )
        donations_dropdown_actual_data = base_steps.process_response_and_extract_data(
            response=donations_dropdown_response, expected_length=2
        )
        api_data_by_site = {site["site"]: site for site in donations_dropdown_actual_data}
        donation_dropdown_table_data = network_depl_page.get_table_data_from_discard_donation_dropdown()
        assert len(donation_dropdown_table_data) == 2
        for table_data in donation_dropdown_table_data:
            if table_data.site == site_config_hf.site.code:
                api_data = api_data_by_site[site_config_hf.site.code]
            else:
                api_data = api_data_by_site[site_config_ep.site.code]

            assert table_data.discard_date == datetime_utils.reformat_datetime_str(
                date_string=api_data["discardDate"],
                input_date_format=DATE_TIME_FORMAT_1,
                output_date_format=DATE_FORMAT_1,
            )
            assert table_data.quantity == str(api_data["quantity"])
            assert table_data.site == api_data["site"]
            assert table_data.sku_code == api_data["skuCode"]
            assert table_data.transaction_type == api_data["tranType"]
