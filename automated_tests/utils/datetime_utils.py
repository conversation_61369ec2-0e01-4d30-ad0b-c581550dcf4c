from datetime import datetime

import pytz
from google.type import datetime_pb2

from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_WEEK


def get_datetime_for_specific_timezone(timezone: datetime.tzinfo, processing_datetime: datetime = None):
    return (
        processing_datetime.astimezone(timezone).replace(tzinfo=None)
        if processing_datetime
        else datetime.now(timezone).replace(tzinfo=None)
    )


def reformat_datetime_str(date_string: str, input_date_format: str, output_date_format: str):
    datetime_obj = datetime.strptime(date_string, input_date_format)
    formatted_date = datetime_obj.strftime(output_date_format)
    return formatted_date


def get_current_date_index():
    for day_index, day in enumerate(CURRENT_WEEK.week_days()):
        if day == CURRENT_DATE:
            return day_index


def convert_to_datetime_pb2(python_datetime: datetime, tz: str = None):
    """
    Converts a Python datetime object to a datetime_pb2.DateTime
    """
    if tz:
        tz_info = pytz.timezone(zone=tz)
        python_datetime = python_datetime.astimezone(tz=tz_info)
        timezone_proto = datetime_pb2.TimeZone(id=tz)
    else:
        timezone_proto = None
    return datetime_pb2.DateTime(
        year=python_datetime.year,
        month=python_datetime.month,
        day=python_datetime.day,
        hours=python_datetime.hour,
        minutes=python_datetime.minute,
        seconds=python_datetime.second,
        nanos=python_datetime.microsecond,
        time_zone=timezone_proto,
    )
