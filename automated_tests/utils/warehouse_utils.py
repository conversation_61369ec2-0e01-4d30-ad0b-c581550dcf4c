import random

from automated_tests.data.constants.base_constants import SiteConfigs, SiteConfigsCA, Warehouses, WarehousesCA
from automated_tests.data.models.imt_config import TestBrand, TestSiteConfig
from automated_tests.data.models.warehouse import TestWarehouse
from procurement.constants.hellofresh_constant import MARKET_US


class WarehouseUtils:
    @staticmethod
    def get_ot_dc_name_by_warehouse(warehouse: TestWarehouse) -> str:
        otdc_names = []
        for test_site_config in SiteConfigs.get_all_values():
            if test_site_config.site.code == random.choice(warehouse.regional_dcs):
                otdc_names.append(test_site_config.ot_dc_name)
        return random.choice(otdc_names)

    @staticmethod
    def get_regional_dc(warehouse: TestWarehouse) -> TestSiteConfig:
        return random.choice([sc for sc in SiteConfigs.get_all_values() if sc.site.code in warehouse.regional_dcs])

    @staticmethod
    def get_site_config_by_warehouse(warehouse: TestWarehouse, brand: TestBrand = None) -> TestSiteConfig:
        return random.choice(
            [
                test_site_config
                for test_site_config in (
                    SiteConfigs.get_all_values()
                    if warehouse.market.code == MARKET_US
                    else SiteConfigsCA.get_all_values()
                )
                if test_site_config.site.code in warehouse.regional_dcs
                and (not brand or test_site_config.brand == brand)
            ]
        )

    @staticmethod
    def get_warehouse_by_site_config(site_config: TestSiteConfig) -> TestWarehouse:
        return random.choice(WarehouseUtils.get_warehouses_by_regional_dc(site_config))

    @staticmethod
    def get_warehouses_by_regional_dc(site_config: TestSiteConfig) -> list[TestWarehouse]:
        warehouses = {}
        for warehouse in (
            WarehousesCA.get_all_values() if site_config.market.code != MARKET_US else Warehouses.get_all_values()
        ):
            if site_config.site.code in warehouse.regional_dcs:
                warehouses[warehouse.key] = warehouse
        return list(warehouses.values())
