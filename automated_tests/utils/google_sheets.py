import logging
import time

import allure
from google.auth.exceptions import GoogleAuthError
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from procurement.client.googlesheets import DataRange, gsheet_client
from procurement.core.config_utils import config

logger = logging.getLogger(__name__)


class GoogleDriveService:
    api_version = "v3"
    service_name = "drive"
    scopes = ["https://www.googleapis.com/auth/drive.file"]

    def __init__(self):
        self._service_account_config = config["googlesheet_sa"]["config"]

    @allure.step("Loaded spreadsheet with name {name}.")
    def create_and_load_spreadsheet(self, name) -> str:
        @retry(retry=retry_if_exception_type(HttpError), stop=stop_after_attempt(8), wait=wait_exponential(2))
        @retry(retry=retry_if_exception_type(GoogleAuthError), stop=stop_after_attempt(3))
        def file_create():
            return gsheet_client.create_spreadsheet(name)

        id_ = file_create()
        logger.info(f"File ID: {id_}")
        return id_

    @retry(retry=retry_if_exception_type(HttpError), stop=stop_after_attempt(8), wait=wait_exponential(2))
    @retry(retry=retry_if_exception_type(GoogleAuthError), stop=stop_after_attempt(3))
    def delete_spreadsheet(self, id_):
        gsheet_client.delete_spreadsheet(spreadsheet_id=id_)

    def _resource(self):
        credentials = Credentials.from_service_account_info(self._service_account_config, scopes=self.scopes)
        return build(self.service_name, self.api_version, credentials=credentials)


def read_sheet(model, sheet_name: str, gsheet, is_formatted=True):
    max_retries = 10
    retry_delay = 3
    for retry_count in range(max_retries):
        read_data = gsheet_client.read(
            spreadsheet_id=gsheet.doc_id,
            sheet_name=sheet_name,
            data_range=DataRange(first_row=1, last_row=17, first_column="A", last_column="YY"),
            is_formatted=is_formatted,
        )
        headers = read_data.pop(0)
        rows = [model(*row) for row in read_data]
        if len(rows) != 0:
            return headers, rows
        elif retry_count == max_retries - 1:
            raise Exception("Failed to read data after {} retries".format(max_retries))
        else:
            time.sleep(retry_delay)
