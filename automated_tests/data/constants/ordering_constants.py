from enum import StrEnum

from automated_tests.utils.extended_enum import ExtendedEnum


class HJReceiptStatuses(ExtendedEnum, StrEnum):
    UNLOADING = "Unloading"
    CLOSED = "Closed"
    PRINTING_LABELS = "Printing Labels"
    REJECTED = "Rejected"
    CANCELLED = "Cancelled"
    QC_SUPERVISOR_NEEDED = "QC Supervisor Needed"
    IN_QC_CHECK = "In QC Check"
    READY_TO_PRINT_LABELS = "Ready to Print Labels"
    CHECKED_IN = "Checked in"
    BUILDING_PALLETS = "Building Pallets"
    CLOSED_PARTIAL_REJECTION = "Closed - Partial Rejection"
