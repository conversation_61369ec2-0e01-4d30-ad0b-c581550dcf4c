from enum import StrEnum

from automated_tests.utils.extended_enum import ExtendedEnum

ITEM_HAS_BEEN_SUCCESSFULLY_EDITED_ALERT_TEXT = "Item has been successfully edited"
ITEM_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT = "Item has been successfully created"
ITEM_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT = "Item has been successfully deleted"
SITE_CONFIG_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT = "Site config has been successfully created"
SITE_CONFIG_HAS_BEEN_SUCCESSFULLY_UPDATED_ALERT_TEXT = "Site config has been successfully updated"
SITE_CONFIG_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT = "Site config has been successfully deleted"

DOES_NOT_EXIST = "Document with ID {} not found or does not exist"
DOCS_VALID_STATUS = "All docs are valid"
DOCS_INVALID_STATUS = "Invalid documents"
REVALIDATE_STATUS = "Revalidate all"
EP_ERROR_MESSAGE = (
    "The title does not match the expected value: "
    'actual title - "EP OSCAR"; expected title - "EP Hybrid Needs - Week {0}"'
)
ADMIN_SITE_TITLE = "Admin | Sites IMT"
IMT_INVENTORY_PULL_PUT_TITLE = "IMT | Inventory Pull/Put"
SITE_VIEW_ONLY = "Sites (view only)"
INVENTORY_PULL_PUT_VIEW_ONLY = "Pull/Inventory (View Only)"
SALUTATION_MESSAGE = "{user_full_name}, Welcome to Inventory Management {market}"
UPDATES_AVAILABLE = "UPDATES AVAILABLE"
NO_ROWS_TO_SHOW = "No Rows To Show"
STATE_IS_SAVED = "State is saved"

GSHEET_CODE_ID_HF_IMT = {
    "commodity_group": "1RfRuf0h_OZTbZnlPsY80n4YOuEuEVkVS7QZ8l17W74Q",
    "po_status_export": "1b4RJm0eXSFrvbeM9-oPKR6yip6Zz9v6QrUf6Qmiv5xY",
    "pkg_v3": "1ZeaZlenxiWkZ6RIFsFfePaEKvxRcSnQPj7MF78_ghn4",
    "pkg": "1kNfOmXSDSjQCnUKL3-_H8gkuxEDMIzsXhFLRCxBbh_Y",
    "imt_data_dumps_3pl": "16HxWYEQd4UiwlyXyVxLNm894i2u0sdftUDEqWLWGEYg",
    "ambient_safety_stock": "1_rFyBsBTQ_Q5ei_CEOfXfpyfKyBvs_Se1je_6arx2Ss",
    "hybrid_needs_3pl": "1Mxb5KkKZ7l7Yn17B8W20ef90r9mlgzvj5hexkhaRqqM",
    "mock_plan_calculation": "1khLeI2P3rUfmXs9Wnbo7L9SCqqMGoyUrbBUCXEb_awo",
    "packaging_demand": "1ssSTKagSsANaxZoxoaSH2vFL6Br9OTO4ihaLOIEtCWs",
    "imt_inventory": "1FrqWLSuur9oGpsRQ8kuk6ZYxFwfQXe4NU37pl4y158M",
    "pimt_master_replenishment": "1clG-oztDzhYjxLDNWzetsE1MfEZz8ksZ7LyyRaAI8L4",
    "topo_sku": "1j4ewXiQ3D5R7Gc3F80KjKayOsqr6uyFbAihKepQQuec",
}

GSHEET_CODE_ID_HF_IMT_CA = {
    "commodity_group": "1ECVzzp7YC9d1HITaXkDF5k3VGtCFDZy32WsysS7Mv7I",
    "rwa_sku_mapping": "1h7yPUQoJzIzqMSW0RoMkwSFuIlA0p4LKxRL_PKre3H8",
    "consolidated_packaging_demand": "1vkslGPKLba5tT7LxPLYH322Vx-f_kMcRKljvzTOTWyg",
}


HF_GSHEET_LABELS = [
    "Total Procurement Purchasing",
    "PO Status Export",
    "PIMT Master Replenishment",
    "IMT Data Feeds",
    "Master Ambient Safety Stock Data",
    "US TOPO SKUs",
    "Production Kit Guide",
    "3PL IMT - Data Dumps",
    "3PL Hybrid Needs",
    "Mock Plan Calculation",
    "In - Week Packaging Demand",
]

CA_HF_IMT_GSHEET_LABELS = [
    "RWA SKU mapping",
    "Total Procurement Purchasing",
    "Consolidated Packaging Demand",
]

HF_GSHEET_LABELS_GENERAL = [
    "Total Procurement Purchasing",
    "PO Status Export",
    "PIMT Master Replenishment",
    "IMT Data Feeds",
    "Master Ambient Safety Stock Data",
]

HF_GSHEET_LABELS_WEEKLY = [
    "Production Kit Guide",
    "3PL IMT - Data Dumps",
    "3PL Hybrid Needs",
    "Mock Plan Calculation",
    "In - Week Packaging Demand",
]

EP_LABELS = [
    "Total Procurement Purchasing",
    "Hybrid Needs",
    "Production Kit Guide",
]

PIMT_GSHEETS_TITLE = "PIMT Admin GSheet"

PIMT_GSHEETS_LABELS = [
    "PIMT Export",
    "PIMT - ALL 3PWs - Grid Dynamics Data Feed",
]

PIMT_GSHEET_LINKS = {
    "pimt-export": "1wjg2jjkSJcoU_5o50hkbwFpVh1kgaafmLXvILO1INl0",
    "pimt": "1QCdsNxs8gkFOrBrpADO23H13Y0F0XVWNsWEAwKfNQRw",
}

EP_OSCAR = "1rjpDpSCr4kfuzNKG6INfk0aDWkzp6C_kKw_zfvVbr0c"

SUPPLEMENT_NEEDED = "Supplement Needed"
DAY_PASSED = "Day Passed"
NO_IMPACT = "No Impact"

SCHEDULED_DELIVERY_NECESSARY = "Scheduled Delivery Necessary"
PRODUCTION_WILL_STOP = "Production Will Stop"


class SynchronizationJobs(str):
    PO_EXPORT = "PO_EXPORT"
    PIMT_OPS_MAIN_PROTEIN_EXPORT = "OPS_MAIN_PROTEIN_EXPORT"
    PIMT_CSAC_EXPORT = "CSAC_EXPORT"
    PIMT_OPS_MAIN_GDP_EXPORT = "OPS_MAIN_GDP_EXPORT"
    PIMT_STRATEGY_EXPORT = "STRATEGY_EXPORT"
    PIMT_DAILY_REPLENISHMENT_EXPORT = "REPLENISHMENT_EXPORT"
    PACKAGING_PO_STATUS_DAILY_EXPORT = "PACKAGING_PO_STATUS_DAILY_EXPORT"
    WEEKLY_LINER_GUIDANCE_DAILY_EXPORT = "WEEKLY_LINER_GUIDANCE_DAILY_EXPORT"
    PACKAGING_DEPLETION_DAILY_EXPORT = "PACKAGING_DEPLETION_DAILY_EXPORT"
    FACTOR_PO_STATUS_DAILY_EXPORT = "FACTOR_PO_STATUS_DAILY_EXPORT"
    PIMT_SYNC = "PIMT_SYNC"
    IMT_SYNC = "IMT_SYNC"
    FUTURE_PKG_SYNC = "FUTURE_PKG_SYNC"
    FACTOR_NETWORK_DEPLETION_DAILY_EXPORT = "FACTOR_NETWORK_DEPLETION_DAILY_EXPORT"
    FACTOR_WEEKLY_FINANCIAL_EXPORT = "FACTOR_WEEKLY_FINANCIAL_EXPORT"
    FACTOR_MONTHLY_FINANCIAL_EXPORT = "FACTOR_MONTHLY_FINANCIAL_EXPORT"
    MANUAL_FORMS_EXPORT_TO_S3 = "MANUAL_FORMS_EXPORT_TO_S3"
    UNIFIED_INVENTORY_EXPORT_TO_S3 = "UNIFIED_INVENTORY_EXPORT_TO_S3"
    BULK_SKUS_EXPORT = "BULK_SKUS_EXPORT"
    PIMT_DAILY_EXCEPTION_REPORT = "PIMT_DAILY_EXCEPTION_REPORT"
    PIMT_MONTHLY_3PW_REPORT = "PIMT_MONTHLY_3PW_REPORT"
    HJ_PACKAGING_SNAPSHOT = "HJ_PACKAGING_SNAPSHOT"
    HJ_PACKAGING_SNAPSHOT_RUN = "HJ_PACKAGING_SNAPSHOT_RUN"
    CORE_DAILY_FINANCIAL_EXPORT = "CORE_DAILY_FINANCIAL_EXPORT"
    CORE_WEEKLY_FINANCIAL_EXPORT = "CORE_WEEKLY_FINANCIAL_EXPORT"
    CORE_MONTHLY_FINANCIAL_EXPORT = "CORE_MONTHLY_FINANCIAL_EXPORT"
    SNAPSHOT_START_OF_DAY_INVENTORY = "SNAPSHOT_START_OF_DAY"
    SNAPSHOT_START_OF_DAY_INVENTORY_RUN = "SNAPSHOT_START_OF_DAY_RUN"
    KAFKA_FORECAST_UPLOAD = "FORECAST_UPLOAD"
    HJ_WIP_CONSUMPTION_SYNC = "HJ_WIP_CONSUMPTION_SYNC"


class WhRegionApiInput(ExtendedEnum, StrEnum):
    NORTHEAST = "Northeast"
    SOUTH = "South"
    WEST = "West"
    SOUTHEAST = "Southeast"
    SOUTHWEST = "Southwest"


WH_TOTAL_REGION = "Total"


class WhReceivingTypeApiInput(ExtendedEnum, StrEnum):
    NA = "N/A"
    E2OPEN_GRN = "E2OPEN_GRN"
    HJ_GRN = "HJ_GRN"


class WhInventoryTypeApiInput(ExtendedEnum, StrEnum):
    E2OPEN = "E2Open"
    HJ = "HighJump"
    GSHEET = "Gsheet"


class PimtTabName(StrEnum):
    PIMT_DAILY_GRN_EXCEPTIONS_EXPORT = "PIMT Daily GRN Exceptions Report"
    PIMT_DAILY_EXCEPTIONS_EXPORT = "PIMT Daily Exceptions Report"
    PIMT_UNDELIVERED = "PIMT Undelivered"


class SyncJobStatus(StrEnum):
    SUCCESS = "success"
    FAILED = "failed"
    QUEUED = "queued"


PIMT_DAILY_EXCEPTIONS_REPORT = "PIMT_daily_exceptions_report_{date}"


class AvailableJobsAdminSyncJob(StrEnum):
    IMT_SYNC = "IMT Sync"
    PIMT_SYNC = "PIMT Sync"
    PIMT_CSAC_EXPORT = "PIMT CSAC Export"
    PIMT_OPS_MAIN_PROTEIN_EXPORT = "PIMT Ops Main Dashboard Protein Export"
    PIMT_POS_MAIN_GDP_EXPORT = "PIMT Ops Main Dashboard G/D/P Export"
    PIMT_STRATEGY_EXPORT = "PIMT Strategy Main Dashboard Export"
    PIMT_REPLENISHMENT_EXPORT = "PIMT Replenishment Export"
    PIMT_DAILY_EXCEPTIONS_REPORT = "PIMT Daily Exceptions Report"
    PIMT_MONTHLY_3PW_REPORT = "PIMT Monthly 3PW Report"
    CLEAN_UP_PARALLEL_SYNC_LOG = "Clean Up parallel sync log"
    DAILY_HJ_INVENTORY_SCHEDULE = "Daily HJ inventory schedule"
    UPDATE_HJ_PACKAGING_PALLET_SNAPSHOT = "Schedule update HJ Packaging Pallet Snapshot"
    KAFKA_FORECAST_UPLOAD = "Kafka forecast Upload"
    PO_STATUS_EXPORT_TO_GSHEET = "PO status export to Gsheet"
    FACTOR_WEEKLY_FINANCIAL_PO_EXPORT = "Factor Weekly Financial PO Export"
    FACTOR_MONTHLY_FINANCIAL_PO_EXPORT = "Factor Monthly Financial PO Export"
    HF_EP_GC_WEEKLY_FINANCIAL_PO_EXPORT = "HF, EP, GC Weekly Financial PO Export"
    HF_EP_GC_MONTHLY_FINANCIAL_PO_EXPORT = "HF, EP, GC Monthly Financial PO Export"
    DAILY_FINANCIAL_PO_EXPORT = "Daily Financial PO Export"
    IMT_PACKAGING_DEPLETION_DAILY_EXPORT = "IMT Packaging Depletion Daily Export"
    IMT_WEEKLY_LINER_GUIDANCE_DEPLETION_DAILY_EXPORT = "IMT Weekly Liner Guidance Depletion Daily Export"
    IMT_PACKAGING_PO_STATUS_DAILY_EXPORT = "IMT Packaging PO Status Daily Export"
    IMT_FACTOR_PO_STATUS_DAILY_EXPORT = "IMT Factor PO Status Daily Export"
    FACTOR_NETWORK_DEPLETION_DAILY_EXPORT = "Factor Network Depletion Export"
    FUTURE_PKG_SYNC = "Future PKG sync"
    SEND_MANUAL_FORMS_EXPORT_TO_S3 = "Send manual forms export to S3"
    SEND_BULK_SKUS_EXPORT_TO_S3 = "Send Bulk SKUs export to S3"
    IMPORT_ALLOWED_BUFFERS = "Import allowed buffers"


PROCUREMENT_MANAGER_JOBS = [
    AvailableJobsAdminSyncJob.IMT_SYNC,
    AvailableJobsAdminSyncJob.PIMT_SYNC,
    AvailableJobsAdminSyncJob.PIMT_CSAC_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_OPS_MAIN_PROTEIN_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_POS_MAIN_GDP_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_STRATEGY_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_REPLENISHMENT_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_DAILY_EXCEPTIONS_REPORT,
    AvailableJobsAdminSyncJob.PIMT_MONTHLY_3PW_REPORT,
    AvailableJobsAdminSyncJob.KAFKA_FORECAST_UPLOAD,
    AvailableJobsAdminSyncJob.IMT_PACKAGING_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_WEEKLY_LINER_GUIDANCE_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_PACKAGING_PO_STATUS_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_FACTOR_PO_STATUS_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.FACTOR_NETWORK_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.SEND_BULK_SKUS_EXPORT_TO_S3,
    AvailableJobsAdminSyncJob.IMPORT_ALLOWED_BUFFERS,
]

PROCUREMENT_USER_JOBS = [
    AvailableJobsAdminSyncJob.IMT_SYNC,
    AvailableJobsAdminSyncJob.PIMT_SYNC,
    AvailableJobsAdminSyncJob.PIMT_CSAC_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_OPS_MAIN_PROTEIN_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_POS_MAIN_GDP_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_STRATEGY_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_REPLENISHMENT_EXPORT,
    AvailableJobsAdminSyncJob.PIMT_DAILY_EXCEPTIONS_REPORT,
    AvailableJobsAdminSyncJob.PIMT_MONTHLY_3PW_REPORT,
    AvailableJobsAdminSyncJob.KAFKA_FORECAST_UPLOAD,
    AvailableJobsAdminSyncJob.IMT_PACKAGING_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_WEEKLY_LINER_GUIDANCE_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_PACKAGING_PO_STATUS_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMT_FACTOR_PO_STATUS_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.FACTOR_NETWORK_DEPLETION_DAILY_EXPORT,
    AvailableJobsAdminSyncJob.IMPORT_ALLOWED_BUFFERS,
]


class MenuItem(StrEnum):
    CHANGELOG = "Changelog"
    ADMIN = "Admin"
    BUYER = "Buyer"
    PIMT = "PIMT"
    IMT = "IMT"
    INVENTORY = "Inventory"
    CALCULATIONS = "Calculations"


DC_USER_MENU_HEADERS = [MenuItem.CHANGELOG, MenuItem.BUYER, MenuItem.IMT, MenuItem.INVENTORY]


class AdminMenuDropDown(StrEnum):
    GSHEETS = "GSheets"
    BRANDS = "Brands"
    SITES = "Sites"
    WAREHOUSES = "Warehouses"
    METRICS = "Metrics"
    SYNC_JOBS = "Synchronization Jobs"
    BULK_SKUS = "Bulk SKUs"
    RELEASE_LOG = "Release log"


PROCUREMENT_LEADERSHIP_ADMIN_MENU_HEADERS = [
    AdminMenuDropDown.GSHEETS,
    AdminMenuDropDown.BRANDS,
    AdminMenuDropDown.SITES,
    AdminMenuDropDown.GSHEETS,
    AdminMenuDropDown.WAREHOUSES,
    AdminMenuDropDown.BULK_SKUS,
]

ADMIN_MENU_DROP_DOWN_ITEMS = [
    "GSheets",
    "Brands",
    "Sites",
    "GSheets",
    "Warehouses",
    "Synchronization Jobs",
    "OPE Demand Data",
    "Bulk SKUs",
    "Release log",
]

BUYER_DROP_DOWN_MENU_ITEMS = ["Buyer Dashboard"]

PIMT_MENU_DROP_DOWN_ITEMS = [
    "OPs Main Dashboard",
    "Ambient Safety Stock",
    "Expiring Inventory Report",
    "Metrics",
    "Comments Log",
]

IMT_MENU_DROP_DOWN_ITEMS = [
    "Ingredients Depletion",
    "Packaging Depletion",
    "PO Status View",
    "Remake Tool",
    "Production Need - Ingredients",
    "Production Kit Guide",
    "Top Variances",
    "Inventory Pull/Put",
    "PO Void",
    "Receipt Override",
    "Weekend Coverage Checklist",
    "Discard Form",
    "Comments Log",
]

INVENTORY_MENU_DROP_DOWN_ITEMS = [
    "Inventory Module",
    "Network Depletion Module",
    "Unified Inventory Module",
    "Risk Analysis & Replenishment",
]

CALCULATIONS_MENU_DROP_DOWN_ITEMS = [
    "Expected Daily Need",
    "Perpetual Gross Needs",
]

ADMIN_MENU_DROP_DOWN_ITEMS_LINKS = [
    "#/admin/imt/gsheets",
    "#/admin/imt/brands",
    "#/admin/imt/sites",
    "#/admin/pimt/gsheets",
    "#/admin/pimt/warehouses",
    "#/admin/jobs/sync-jobs",
    "#/admin/jobs/sku-demand",
    "#/admin/skus/bulk-skus",
    "#/admin/logs/release-log",
]

BUYER_DROP_DOWN_MENU_ITEMS_LINKS = ["#/buyer/dashboards/buyer-dashboard"]

PIMT_MENU_DROP_DOWN_ITEMS_LINKS = [
    "#/pimt/dashboards/ops-main-dashboard",
    "#/pimt/dashboards/ambient-safety-stock",
    "#/pimt/dashboards/expiring-inventory-report",
    "#/pimt/dashboards/metrics",
    "#/pimt/logs/comments",
]

IMT_MENU_DROP_DOWN_ITEMS_LINKS = [
    "#/imt/dashboards/ingredients-depletion",
    "#/imt/dashboards/packaging-depletion",
    "#/imt/dashboards/po-status-view",
    "#/imt/dashboards/remake-tool",
    "#/imt/dashboards/production-need-ingredients",
    "#/imt/dashboards/production-kit-guide",
    "#/imt/procurement-analytics/top-variances",
    "#/imt/procurement-manual-forms/inventory-pull-put",
    "#/imt/procurement-manual-forms/po-void",
    "#/imt/procurement-manual-forms/receipt-override",
    "#/imt/procurement-manual-forms/weekend-coverage-checklist",
    "#/imt/fulfillment-forms/discard-form",
    "#/imt/logs/comments",
]

INVENTORY_MENU_DROP_DOWN_ITEMS_LINKS = [
    "#/inventory/dashboards/inventory-module",
    "#/inventory/dashboards/network-depletion-module",
    "#/inventory/dashboards/unified-inventory",
    "#/inventory/dashboards/replenishment",
]

CALCULATIONS_MENU_DROP_DOWN_ITEMS_LINKS = [
    "#/calculations/dashboards/expected-daily-need",
    "#/calculations/dashboards/perpetual-gross-needs",
]

SYNC_JOBS_PAGE_HEADERS = ["Job Name", "Status", "Last Updated", "Actions"]
ADMIN_WAREHOUSES_ADD_NEW_CONFIG_POPUP_HEADLINE = "New Warehouse Config"
BRANDS_ORDER = ["HF", "EP", "GC", "FJ"]
