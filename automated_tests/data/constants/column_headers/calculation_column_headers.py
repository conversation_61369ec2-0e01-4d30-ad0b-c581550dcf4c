from automated_tests.data.constants.date_time_constants import PRODUCTION_WEEK_16_DAYS_STR

EXPECTED_DAILY_NEEDS_HEADERS = [
    "SKU",
    "SKU Name",
    "Category",
    "Commodity Group",
    "Buyer",
    *(["Closing Stock", "Daily Needs"] * 44),
    "Carryover Stock",
    "Stock buffer",
    "Incoming POs",
    "Inbound",
    "Consumption",
    "Stock difference",
]

# # when we generate data for current and future weeks, 2 days overlap on perpetual gross needs board,
# that's why there displayed 16, not 18 row headers
EXPECTED_PERPETUAL_GROSS_NEEDS = [
    "SKU",
    "SKU Name",
    "SCM Week",
    "Sum of All Gross Needs",
    "Category",
    "Commodity Group",
    "Buyer",
    *PRODUCTION_WEEK_16_DAYS_STR,
]
