BULK_SKUS_HEADERS = [
    "Packaged SKU Code",
    "Packaged SKU Name",
    "Bulk SKU Code",
    "Bulk SKU Name",
    "Brands",
    "Pick Conversions",
    "Bulk Master SKU",
    "Actions",
]

WAREHOUSES_HEADERS = [
    "GSheet Name",
    "Full Site Name",
    "Bob Code",
    "Ordering Tool Names",
    "Supplier Names",
    "Region",
    "Regional DCs",
    "Packaging Region",
    "Receiving Type",
    "Inventory Type",
    "Actions",
]

EXPECTED_SITE_HEADERS = [
    "Brand",
    "Site",
    "Full Site Name",
    "HJ Name",
    "OT Name",
    "Supplier Names",
    "Receiving Type",
    "Inventory Type",
    "Hybrid Needs Source",
    "DC Bob Code",
    "Timezone",
    "Actions",
]

EXPECTED_SITE_HEADERS_CANADA = [
    "Brand",
    "Site",
    "Full Site Name",
    "HJ Name",
    "OT Name",
    "Supplier Names",
    "Receiving Type",
    "Inventory Type",
    "Hybrid Needs Source",
    "DC Bob Code",
    "Timezone",
    "WIP Consumption",
    "Actions",
]

RELEASE_LOG_HEADERS = ["Description", "Is visible for DC user", "Type", "Actions"]

OPE_DEMAND_DATA_EXPECTED_HEADERS = ["SKU", "Week", "Day", "Site", "Brand", "Quantity"]
