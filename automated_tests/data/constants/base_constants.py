from decimal import Decimal
from enum import IntEnum, StrEnum

from automated_tests.data.constants.admin_constants import WhRegionApiInput
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, FACTOR_WEEK_CONFIG
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.market import TestMarket
from automated_tests.data.models.user import AvailableRoles, TestUser
from automated_tests.data.models.warehouse import TestWarehouse
from automated_tests.utils.extended_enum import ExtendedEnum
from procurement.constants.hellofresh_constant import (
    MARKET_CA,
    MARKET_US,
    InventoryInputType,
    ReceiveInputType,
    WhReceivingType,
)
from procurement.constants.protobuf import GrnUnitMeasure
from procurement.managers.admin.dc_admin import DcProperty


class BobCodes(ExtendedEnum, StrEnum):
    FN = "FN"
    SF = "SF"
    NJ = "NJ"
    LX = "LX"
    GB = "GB"
    NW = "NW"
    EL = "EL"
    AZ = "AZ"
    JO = "JO"
    OV = "OV"
    OI = "OI"


class OtDc(ExtendedEnum, StrEnum):
    UNCOMMON__1 = "Everyplate - NJ Uncommon Carriers"
    UNCOMMON__2 = "HelloFresh United States - Uncommon Carriers"
    FIRST_CHOICE__1 = "First Choice Freezer & Cold Storage - NJ"
    SOUTHWEST__1 = "HelloFresh United States - Southwest"
    SOUTHWEST__2 = "Southwest Cold Storage"
    DREISBACH__1 = "Dreisbach Cold Storage"
    DREISBACH__2 = "Dreisbach Enterprises"
    FWLOGISTICS__1 = "FW Logistics - CA"
    ROMARK__1 = "Romark - CT"
    LINEAGE_ALLENTOWN__1 = "Lineage Allentown - AK"
    CASTELLINI__1 = "Castellini"
    JOLIET__1 = "Joliet"
    VERSACOLD = "Versacold"
    INTERSTATE_WAREHOUSING = "Interstate Warehousing - Castelini"


class OtSuppliers(ExtendedEnum, StrEnum):
    UNCOMMON__1 = "3PL - NJ Uncommon Carriers"
    FIRST_CHOICE__1 = "First Choice Freezer"
    SOUTHWEST__1 = "Southwest Warehouse Service"
    SOUTHWEST__2 = "Southwest Warehouse Services"
    DREISBACH__1 = "CA 3PL- Dreisbach Enterprises"
    FWLOGISTICS__1 = "FW Logistics - CA"
    ROMARK__1 = "Romark - CT"
    LINEAGE_ALLENTOWN__1 = "Lineage Allentown - AK"
    CASTELLINI__1 = "Castellini"
    JOLIET__1 = "Joliet"
    VERSACOLD = "Versacold"
    INTERSTATE_WAREHOUSING = "Interstate Warehousing - Castelini"


class Markets(ExtendedEnum):
    US = TestMarket(code=MARKET_US, name="United States")
    CA = TestMarket(code=MARKET_CA, name="Canada")


class Warehouses(ExtendedEnum):
    UNCOMMON = TestWarehouse(
        code="UnCommon",
        warehouse_name="NJ:UnCommon",
        ot_dcs=[OtDc.UNCOMMON__1, OtDc.UNCOMMON__2],
        ot_suppliers=[OtSuppliers.UNCOMMON__1],
        warehouse_order=2,
        region=WhRegionApiInput.NORTHEAST,
        regional_dcs=["NJ"],
        packaging_regions=["NJH"],
        bob_code=BobCodes.NJ,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    FIRST_CHOICE = TestWarehouse(
        code="FirstChoice",
        warehouse_name="NJ:FirstChoice",
        ot_dcs=[OtDc.FIRST_CHOICE__1],
        ot_suppliers=[OtSuppliers.FIRST_CHOICE__1],
        warehouse_order=1,
        region=WhRegionApiInput.NORTHEAST,
        regional_dcs=["NJ"],
        packaging_regions=["NJH"],
        bob_code=BobCodes.LX,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    SOUTHWEST = TestWarehouse(
        code="Southwest",
        warehouse_name="TX:Southwest",
        ot_dcs=[OtDc.SOUTHWEST__1, OtDc.SOUTHWEST__2],
        ot_suppliers=[OtSuppliers.SOUTHWEST__1, OtSuppliers.SOUTHWEST__2],
        warehouse_order=3,
        region=WhRegionApiInput.SOUTH,
        regional_dcs=["TX"],
        packaging_regions=["TXH"],
        bob_code=BobCodes.EL,
        market=Markets.US.value,
    )
    DREISBACH = TestWarehouse(
        code="Dreisbach",
        warehouse_name="CA:Dreisbach",
        ot_dcs=[OtDc.DREISBACH__1, OtDc.DREISBACH__2],
        ot_suppliers=[OtSuppliers.DREISBACH__1],
        warehouse_order=4,
        region=WhRegionApiInput.WEST,
        regional_dcs=["CA"],
        packaging_regions=["CAH"],
        bob_code=BobCodes.SF,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    FWLOGISTICS = TestWarehouse(
        code="FWLogistics",
        warehouse_name="CA:FWLogistics",
        ot_dcs=[OtDc.FWLOGISTICS__1],
        ot_suppliers=[OtSuppliers.FWLOGISTICS__1],
        warehouse_order=5,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["CA"],
        packaging_regions=["CAH"],
        bob_code=BobCodes.NW,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    ROMARK = TestWarehouse(
        code="Romark",
        warehouse_name="CT:Romark",
        ot_dcs=[OtDc.ROMARK__1],
        ot_suppliers=[OtSuppliers.ROMARK__1],
        warehouse_order=6,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["CT"],
        packaging_regions=["CTE"],
        bob_code=BobCodes.GB,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    LINEAGE_ALLENTOWN = TestWarehouse(
        code="LineageAllentown",
        warehouse_name="AK:LineageAllentown",
        ot_dcs=[OtDc.LINEAGE_ALLENTOWN__1],
        ot_suppliers=[OtSuppliers.LINEAGE_ALLENTOWN__1],
        warehouse_order=7,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["AK"],
        packaging_regions=["AKE"],
        bob_code=BobCodes.FN,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    CASTELLINI = TestWarehouse(
        code="Castellini",
        warehouse_name="GA:Castellini",
        ot_dcs=[OtDc.CASTELLINI__1],
        ot_suppliers=[OtSuppliers.CASTELLINI__1],
        warehouse_order=8,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["GA"],
        packaging_regions=["GAE"],
        bob_code=BobCodes.AZ,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    JOLIET = TestWarehouse(
        code="Joliet",
        warehouse_name="CA:Joliet",
        ot_dcs=[OtDc.JOLIET__1],
        ot_suppliers=[OtSuppliers.JOLIET__1],
        warehouse_order=9,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["CA"],
        packaging_regions=["CAH"],
        bob_code=BobCodes.JO,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )
    INTERSTATE_WAREHOUSING = TestWarehouse(
        code="InterstateWarehousing",
        warehouse_name="KY:Interstate Warehousing",
        ot_dcs=[OtDc.INTERSTATE_WAREHOUSING],
        ot_suppliers=[OtSuppliers.INTERSTATE_WAREHOUSING],
        warehouse_order=10,
        region=WhRegionApiInput.NORTHEAST,
        regional_dcs=["KY"],
        packaging_regions=["KYH"],
        bob_code=BobCodes.OI,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.US.value,
    )


class WarehousesCA(ExtendedEnum):
    OV = TestWarehouse(
        code="OV",
        warehouse_name="ON:OV",
        ot_dcs=[OtDc.VERSACOLD],
        ot_suppliers=[OtSuppliers.VERSACOLD],
        warehouse_order=1,
        region=WhRegionApiInput.SOUTHEAST,
        regional_dcs=["ON"],
        bob_code=BobCodes.OV,
        receiving_type=WhReceivingType.NA,
        inventory_type=InventoryInputType.GSHEET,
        market=Markets.CA.value,
    )


class SkuNames(ExtendedEnum, StrEnum):
    BLACK_BEANS = "Beans, Black - 13.4 Ounce (oz)"
    CHEESE_FETA = "Cheese, Feta - 2 Ounce (oz)"
    EP_RICE_JASMINE = "EP, Rice, Jasmine - 0.75 Cup (c)"
    EP_STOCK_CONCENTRATE = "EP, Stock Concentrate, Veggie - 9.6 Gram (g)"
    CURRY_POWDER = "Curry, Powder - 1 Tablespoon (TBSP)"
    PAPRIKA_SMOKED = "Paprika, Smoked - 1 Teaspoon (tsp)"
    PORK_GROUND = "Pork, Ground - 10 Ounce (oz)"
    PORK_TENDERLOIN = "Pork, Tenderloin - 12 Ounce (oz)"
    SALMON_SKIN_ON = "Salmon, Skin-On - 10 Ounce (oz)"
    SAUSAGE_MIX = "Sausage Mix, Raw Sweet Italian Chicken - 9 Ounce (oz)"
    SHRIMP = "Shrimp - 10 Ounce (oz)"
    TURKEY_GROUND = "Turkey, Ground - 10 Ounce (oz)"
    CELERY_SALT = "Celery Salt - 1 Teaspoon (tsp)"


class Brands(ExtendedEnum):
    HF = TestBrand(code="HF", name="HelloFresh", country_code="US", market=Markets.US.value)
    EP = TestBrand(code="EP", name="EveryPlate", country_code="ER", market=Markets.US.value)
    GC = TestBrand(code="GC", name="Green Chef", country_code="CG", market=Markets.US.value)


class FactorBrand(ExtendedEnum):
    FJ = TestBrand(
        code="FJ", name="Factor US", scm_week_config=FACTOR_WEEK_CONFIG, country_code="FJ", market=Markets.US.value
    )


class BrandsCA(ExtendedEnum):
    HF = TestBrand(code="HF", name="HelloFresh, Chef’s Plate", country_code="CA", market=Markets.CA.value)


class Sites(ExtendedEnum):
    NJ_HF = TestSite(code="NJ", brand=Brands.HF.value, name="New Jersey", sequence_number=1)
    NJ_EP = TestSite(code="NJ", brand=Brands.EP.value, name="New Jersey", sequence_number=1)
    NJ_GC = TestSite(code="NJ", brand=Brands.GC.value, name="New Jersey", sequence_number=1)
    TX_HF = TestSite(code="TX", brand=Brands.HF.value, name="Texas", sequence_number=2)
    TX_EP = TestSite(code="TX", brand=Brands.EP.value, name="Texas", sequence_number=2)
    CA_HF = TestSite(code="CA", brand=Brands.HF.value, name="California", sequence_number=3)
    CA_EP = TestSite(code="CA", brand=Brands.EP.value, name="California", sequence_number=3)
    GA_HF = TestSite(code="GA", brand=Brands.HF.value, name="Georgia", sequence_number=6)
    GA_EP = TestSite(code="GA", brand=Brands.EP.value, name="Georgia", sequence_number=6)
    AZ_EP = TestSite(code="AZ", brand=Brands.EP.value, name="Phoenix", sequence_number=16)
    AZ_HF = TestSite(code="AZ", brand=Brands.HF.value, name="Phoenix", sequence_number=17)
    CT_EP = TestSite(code="CT", brand=Brands.EP.value, name="Connecticut", sequence_number=4)
    AK_EP = TestSite(code="AK", brand=Brands.EP.value, name="Alaska", sequence_number=5)

    CO_GC = TestSite(code="CO", brand=Brands.GC.value, name="Aurora", sequence_number=7)
    SW_GC = TestSite(code="SW", brand=Brands.GC.value, name="Swedesboro", sequence_number=8)

    NJ_FJ = TestSite(code="NJ", brand=FactorBrand.FJ.value, name="New Jersey", sequence_number=1)
    GA_FJ = TestSite(code="GA", brand=FactorBrand.FJ.value, name="Georgia", sequence_number=6)
    AU_FJ = TestSite(code="AU", brand=FactorBrand.FJ.value, name="Factor - Aurora", sequence_number=9)
    BR_FJ = TestSite(code="BR", brand=FactorBrand.FJ.value, name="Factor - Burr Ridge", sequence_number=10)
    AG_FJ = TestSite(code="AG", brand=FactorBrand.FJ.value, name="Factor Goodyear", sequence_number=11)
    AC_FJ = TestSite(code="AC", brand=FactorBrand.FJ.value, name="Artisan Chef Mfg", sequence_number=12)
    UB_FJ = TestSite(code="UB", brand=FactorBrand.FJ.value, name="Fresco Foods", sequence_number=14)
    KY_HF = TestSite(code="KY", brand=Brands.HF.value, name="Castellini - Kentucky", sequence_number=13)
    BO_FJ = TestSite(code="BO", brand=FactorBrand.FJ.value, name="Brett Anthony Foods", sequence_number=15)


class SitesCA(ExtendedEnum):
    ON_HF = TestSite(code="ON", brand=BrandsCA.HF.value, name="Timberlea", sequence_number=1)
    OH_HF = TestSite(code="OH", brand=BrandsCA.HF.value, name="Summerlea", sequence_number=2)
    AE_HF = TestSite(code="AE", brand=BrandsCA.HF.value, name="Edmonton", sequence_number=3)
    BL_HF = TestSite(code="BL", brand=BrandsCA.HF.value, name="Abbotsford", sequence_number=4)


class SiteConfigsCA(ExtendedEnum):
    ON_HF = TestSiteConfig(
        site=SitesCA.ON_HF.value,
        brand=BrandsCA.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="CA - YYZ2",
        hj_name="CA99",
        bob_code="OEH",
        receiving_type=ReceiveInputType.WMSL,
        inventory_type=InventoryInputType.WMSL,
        properties={
            DcProperty.LOCKED_PRICING.name: "L",
        },
    )

    OH_HF = TestSiteConfig(
        site=SitesCA.OH_HF.value,
        brand=BrandsCA.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="Summerlea",
        hj_name="CA98",
        receiving_type=ReceiveInputType.WMSL,
        bob_code="OHH",
        inventory_type=InventoryInputType.WMSL,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )

    AE_HF = TestSiteConfig(
        site=SitesCA.AE_HF.value,
        brand=BrandsCA.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="CA - EDM2",
        hj_name="AE",
        bob_code="AEH",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        inventory_type=InventoryInputType.HJ,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )

    BL_HF = TestSiteConfig(
        site=SitesCA.BL_HF.value,
        brand=BrandsCA.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="CA - Valley",
        hj_name="CA96",
        bob_code="BLH",
        receiving_type=ReceiveInputType.WMSL,
        inventory_type=InventoryInputType.WMSL,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )


class SiteConfigsGC(ExtendedEnum):
    CO_GC = TestSiteConfig(
        site=Sites.CO_GC.value,
        brand=Brands.GC.value,
        week=CURRENT_WEEK,
        ot_dc_name="Green Chef - Aurora",
        hj_name="GC01",
        bob_code="COG",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )

    SW_GC = TestSiteConfig(
        site=Sites.SW_GC.value,
        brand=Brands.GC.value,
        week=CURRENT_WEEK,
        ot_dc_name="Green Chef - Swedesboro",
        hj_name="GC02",
        bob_code="SWG",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )


class SiteConfigsFactor(ExtendedEnum):
    GA_FJ = TestSiteConfig(
        site=Sites.GA_FJ.value,
        brand=FactorBrand.FJ.value,
        week=CURRENT_WEEK,
        ot_dc_name="Factor - Georgia",
        hj_name="FJ01",
        bob_code="GAF",
        receiving_type=ReceiveInputType.MANUAL,
        properties={DcProperty.LOCKED_PRICING.name: "N"},
    )
    NJ_FJ = TestSiteConfig(
        site=Sites.NJ_FJ.value,
        brand=FactorBrand.FJ.value,
        week=CURRENT_WEEK,
        ot_dc_name="FJ United States - NJ",
        hj_name="FJ02",
        bob_code="NJF",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )


class SiteConfigs(ExtendedEnum):
    NJ_HF = TestSiteConfig(
        site=Sites.NJ_HF.value,
        brand=Brands.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="HelloFresh United States - NJ",
        hj_name="HF01",
        bob_code="NJH",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        properties={
            DcProperty.LOCKED_PRICING.name: "L",
        },
    )

    CT_EP = TestSiteConfig(
        site=Sites.CT_EP.value,
        brand=Brands.EP.value,
        week=CURRENT_WEEK,
        ot_dc_name="EveryPlate - Connecticut",
        hj_name="HF02",
        bob_code="CTE",
        receiving_type=ReceiveInputType.HIGH_JUMP,
        properties={DcProperty.LOCKED_PRICING.name: "L"},
    )

    CA_HF = TestSiteConfig(
        site=Sites.CA_HF.value,
        brand=Brands.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="HelloFresh United States - SF",
        hj_name="HF03",
        bob_code="CAH",
        receiving_type=ReceiveInputType.MANUAL,
        properties={
            DcProperty.LOCKED_PRICING.name: "M",
        },
    )

    AK_EP = TestSiteConfig(
        site=Sites.AK_EP.value,
        brand=Brands.EP.value,
        week=CURRENT_WEEK,
        ot_dc_name="Everyplate - Alaska",
        hj_name="HF04",
        bob_code="AKE",
        receiving_type=ReceiveInputType.MANUAL,
        properties={DcProperty.LOCKED_PRICING.name: "M"},
    )

    TX_HF = TestSiteConfig(
        site=Sites.TX_HF.value,
        brand=Brands.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="HelloFresh United States - TX",
        hj_name="HF05",
        bob_code="TXH",
        receiving_type=ReceiveInputType.MANUAL,
        properties={DcProperty.LOCKED_PRICING.name: "N"},
    )

    GA_EP = TestSiteConfig(
        site=Sites.GA_EP.value,
        brand=Brands.EP.value,
        week=CURRENT_WEEK,
        ot_dc_name="EveryPlate - Georgia",
        hj_name="HF06",
        bob_code="GAE",
        receiving_type=ReceiveInputType.MANUAL,
        properties={DcProperty.LOCKED_PRICING.name: "N"},
    )

    KY_HF = TestSiteConfig(
        site=Sites.KY_HF.value,
        brand=Brands.HF.value,
        week=CURRENT_WEEK,
        ot_dc_name="Castellini- KY",
        hj_name="HF07",
        bob_code="KYH",
        receiving_type=ReceiveInputType.MANUAL,
        properties={DcProperty.LOCKED_PRICING.name: "N"},
    )


class MultiMarketUsers(ExtendedEnum):
    test_user = TestUser(
        first_name="tester",
        last_name="testerenko",
        email="<EMAIL>",
        roles={AvailableRoles.ADMIN},
        markets={MARKET_CA, MARKET_US},
    )


class UsersCA(ExtendedEnum):
    test_user = TestUser(
        first_name="tester",
        last_name="testerenko",
        email="<EMAIL>",
        roles={AvailableRoles.ADMIN},
        markets={MARKET_CA},
    )
    dc_user = TestUser(
        first_name="dc",
        last_name="user",
        email="<EMAIL>",
        roles={AvailableRoles.DC_USER},
        markets={MARKET_CA},
    )
    qa_buyer = TestUser(
        first_name="QA",
        last_name="buyer",
        email="<EMAIL>",
        roles={AvailableRoles.ADMIN},
        forced_out=True,
        markets={MARKET_CA},
    )
    helloconnect_user = TestUser(
        first_name="hello",
        last_name="connect",
        email="<EMAIL>",
        roles={AvailableRoles.HELLOCONNECT_USER},
        markets={MARKET_CA},
    )


class Users(ExtendedEnum):
    test_user = TestUser(
        first_name="tester",
        last_name="testerenko",
        email="<EMAIL>",
        roles={AvailableRoles.ADMIN},
    )
    dc_user = TestUser(
        first_name="dc",
        last_name="user",
        email="<EMAIL>",
        roles={AvailableRoles.DC_USER},
    )
    qa_buyer = TestUser(
        first_name="QA",
        last_name="buyer",
        email="<EMAIL>",
        roles={AvailableRoles.ADMIN},
        forced_out=True,
    )
    procurement_manager = TestUser(
        first_name="procurement",
        last_name="manager",
        email="<EMAIL>",
        roles={AvailableRoles.PROCUREMENT_MANAGER},
    )
    procurement_user = TestUser(
        first_name="procurement",
        last_name="user",
        email="<EMAIL>",
        roles={AvailableRoles.PROCUREMENT_USER},
    )
    procurement_leadership = TestUser(
        first_name="procurement",
        last_name="leadership",
        email="<EMAIL>",
        roles={AvailableRoles.PROCUREMENT_LEADERSHIP},
    )
    dc_management_user = TestUser(
        first_name="dc",
        last_name="management_user",
        email="<EMAIL>",
        roles={AvailableRoles.DC_MANAGEMENT_USER},
    )
    dc_universal_user = TestUser(
        first_name="dc",
        last_name="universal_user",
        email="<EMAIL>",
        roles={AvailableRoles.DC_UNIVERSAL_USER},
    )
    helloconnect_user = TestUser(
        first_name="hello",
        last_name="connect",
        email="<EMAIL>",
        roles={AvailableRoles.HELLOCONNECT_USER},
    )


class UnitMeasureASN(ExtendedEnum):
    case = GrnUnitMeasure.CASE
    unit = GrnUnitMeasure.UNIT
    kg = GrnUnitMeasure.KG
    lbs = GrnUnitMeasure.LBS
    oz = GrnUnitMeasure.OZ


class UnitOfMeasurePoAcknowledgement(ExtendedEnum, StrEnum):
    UNIT = "UNIT"
    CASE = "CASE"


class PimtSources(ExtendedEnum, StrEnum):
    TPW_SOURCE = "From 3PW"
    NOT_TPW_SOURCE = "Assumed"


class ExpirationStatusEnum(ExtendedEnum, StrEnum):
    EXPIRED = "Expired"
    LESS_THAN_30 = "1-30 Days"
    LESS_THAN_60 = "30-60 Days"
    MORE_THAN_60 = "+60 Days"


class PoStatuses(ExtendedEnum, StrEnum):
    PARTIALLY_SHIPPED = "Partially Shipped"
    SHIPPED = "Shipped"
    NOT_DELIVERED_PAST_DUE = "Not Delivered - Past Due"
    IN_PROGRESS_HJ_FORMAT = "In Progress HJ - {}"
    DELIVERY_REJECTED = "Delivery Rejected"
    N_A_PO_VOID = "N/A - PO Void"
    RECEIVED_ACCURATE = "Received - Accurate"
    RECEIPT_CANCELLED = "Receipt Cancelled"
    PO_VOID = "PO Void"
    RECEIVED_UNDER = "Received - Under"
    RECEIVED_OVER = "Received - Over"
    IS_SENT = "Is Sent"
    IS_SENT_PENDING_ACCEPTANCE = "Is Sent - Pending Acceptance"
    SUPPLIER_ACCEPTED = "Supplier Accepted"
    SUPPLIER_ACCEPTED_WITH_CHANGES = "Supplier Accepted with Changes"
    SUPPLIER_REJECTED = "Supplier Rejected"
    SUPPLIER_CANCELLED = "Supplier Cancelled"
    INVALID = "Invalid"
    ASN_SHIPPED = "Shipped"
    ASN_PARTIALLY_SHIPPED = "Partially Shipped"
    RECEIVED_PARTIAL_REJECTION = "Received - Partial Rejection"


AWAITING_DELIVERY_STATUSES = frozenset(
    (
        PoStatuses.SUPPLIER_ACCEPTED,
        PoStatuses.SUPPLIER_ACCEPTED_WITH_CHANGES,
        PoStatuses.IS_SENT_PENDING_ACCEPTANCE,
        PoStatuses.ASN_SHIPPED,
        PoStatuses.ASN_PARTIALLY_SHIPPED,
    )
)

RECEIVED_STATUSES = frozenset((PoStatuses.RECEIVED_ACCURATE, PoStatuses.RECEIVED_UNDER, PoStatuses.RECEIVED_OVER))

CARRIER_NAMES_PO_SHIPMENT_LIST = ["ARMSTRONG TRANSPORT GROUP", "SHIPWELL, INC.", "TOTAL QUALITY LOGISTICS"]
ORIGIN_LOCATIONS_PO_SHIPMENT_LIST = [
    "ANAHEIM, CA",
    "PROVIDENCE, RI",
    "EAST HAVEN, CT",
    "VINELAND, NJ",
    "LAUREL, MD",
    "LA VERGNE, TN",
]


class AcceptedIncomingStatusesPo(ExtendedEnum, StrEnum):
    IS_SENT_PENDING_ACCEPTANCE = "Is Sent - Pending Acceptance"
    SUPPLIER_ACCEPTED = "Supplier Accepted"
    SUPPLIER_ACCEPTED_WITH_CHANGES = "Supplier Accepted with Changes"
    PARTIALLY_SHIPPED = "Partially Shipped"
    SHIPPED = "Shipped"
    IN_PROGRESS_HJ = "In Progress HJ"
    RECEIVED_ACCURATE = "Received - Accurate"
    RECEIVED_UNDER = "Received - Under"
    RECEIVED_OVER = "Received - Over"


class Seasons(ExtendedEnum, StrEnum):
    SUMMER = "SUM"
    WINTER = "WIN"
    SPRING = "SPR"
    SUPER_SUMMER = "SUP"


class Sizes(ExtendedEnum, StrEnum):
    XSMALL = "XS"
    SMALL = "SMALL"
    MEDIUM = "MEDIUM"
    LARGE = "LARGE"
    XLARGE = "XLarge"
    RTBS = "RTBS"


class PackagingCategory(ExtendedEnum, StrEnum):
    BOX = "Box"
    KIT_BAG = "Kit Bag"
    SEPARATOR = "Separator"
    SLEEVE = "Sleeve"
    TRAY = "Tray"
    PRIMARY_PACKAGING = "Primary Packaging"
    ICE = "Ice"
    MEDIA = "Media"


class OrderUnits(ExtendedEnum, StrEnum):
    CASE_TYPE = "Case"
    UNIT_TYPE = "Unit"


class PurchasingCategories(ExtendedEnum, StrEnum):
    GROCERY = "Grocery"
    DAIRY = "Dairy"
    PACKAGING = "Packaging"
    PROTEIN = "Protein"
    PRODUCE = "Produce"


class ReplenishmentTypes(ExtendedEnum, StrEnum):
    DETERMINISTIC = "Deterministic"
    REPLENISHMENT = "Replenishment"
    DYNAMIC = "Dynamic"
    REORDER_POINT = "Reorder Point"
    MODIFIED_REPLENISHMENT = "Modified Replenishment"


class DayOfWeek(ExtendedEnum, StrEnum):
    MONDAY = "Monday"
    TUESDAY = "Tuesday"
    WEDNESDAY = "Wednesday"
    THURSDAY = "Thursday"
    FRIDAY = "Friday"
    SATURDAY = "Saturday"
    SUNDAY = "Sunday"


class ShippingMethods(ExtendedEnum, StrEnum):
    VENDOR_DELIVERED = "Vendor Delivered"
    CROSSDOCK = "Crossdock"
    FREIGHT_ON_BOARD = "Freight On Board"


class EmergencyReason(ExtendedEnum, StrEnum):
    SAFETY_STOCK = "Safety Stock"
    FORECAST_INCREASE = "Forecast Increase Order"
    INVENTORY_PULL = "3PW Inventory Pull"
    PACKAGING = "Packaging"
    ORDER_PLANNING = "OrderPlanningService-US"
    CHARITY = "Charity"


class ChecklistStatus(ExtendedEnum, StrEnum):
    COMPLETED = "Completed"
    IN_PROGRESS = "In Progress"
    DELAYED = "Delayed"
    NO_RESPONSE = "No Response"
    OTHER = "Other"


class InventoryPullPutType(ExtendedEnum, StrEnum):
    PULL = "Pull"
    INVENTORY = "Inventory"


class Colors(StrEnum):
    RED_BACKGROUND = "rgba(148, 21, 21, 0.5)"
    BLACK = "rgba(0, 0, 0, 0)"
    RED_ORANGE = "rgb(244, 67, 54)"
    RED_BROWN = "rgb(174, 47, 9)"
    GRAY = "rgb(66, 66, 66)"
    DARK_GREY = "rgb(39, 39, 39)"
    LIGHT_GRAY = "rgb(128, 128, 128)"
    ORANGE = "rgb(162, 99, 5)"
    GREEN = "rgb(0, 191, 165)"
    AZURE = "rgb(24, 102, 192)"
    CYAN = "rgb(8, 157, 137)"
    PINK = "rgb(255, 75, 162)"
    FUCHSIA_PINK = "rgb(234, 51, 247)"
    YELLOW = "rgb(255, 170, 4)"
    YELLOW_ORANGE = "rgb(218, 105, 0)"
    OLIVE = "rgb(165, 165, 13)"
    DARK_ORANGE = "rgb(193, 111, 7)"
    DEEP_ORANGE = "rgb(203, 115, 4)"
    BLUE = "rgb(81, 127, 217)"
    WHITE = "rgb(255, 255, 255)"
    RICH_ORANGE = "rgb(251, 140, 0)"


class HjLocations(ExtendedEnum, StrEnum):
    LOST = "LOST"
    CLOUD = "CLOUD"
    ADJ_LOC = "ADJ LOC"
    ADD_ON_AS = "ADD_ON_AS"
    AS_KIT_STG = "AS_KIT_STG"
    AUTOSTORE_QC = "AUTOSTORE_QC"
    AUTOSTORE_STG = "AUTOSTORE_STG"
    AUTOSTORE = "AUTOSTORE"
    STGRD = "%STGDR%"
    SOMEWHERE = "SOMEWHERE"
    PROD_RTN = "PROD RTN"
    AB_OUT = "AB OUT"
    WIP = "WIP"
    AB_IN = "AB IN"


class SkuStatus(ExtendedEnum, StrEnum):
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    ARCHIVED = "Archived"
    LAUNCH = "Launch"
    LIMITED = "Limited"
    ONBOARDING = "Onboarding"
    DEPLETION_TRACK = "Depletion Track"


class TestGrnDeliveryLineState(ExtendedEnum, IntEnum):
    UNSPECIFIED = 0
    EXPECTED = 1
    OPEN = 2
    RECEIVED = 3
    REJECTED = 4
    CLOSED = 5


class IcsStatus(ExtendedEnum, IntEnum):
    OPEN = 2
    CLOSED = 5
    IN_PROGRESS = 22


class IcsProductionImpact(ExtendedEnum, StrEnum):
    BOXES_MISSING_SERVICE = "BOXES MISSING SERVICE / CPT"
    STOPPED_CURRENTLY_ASSEMBLY_FULFILLMENT = "Stopped Currently - Assembly/Fulfillment"
    STOPPED_CURRENTLY_PREP_PRODUCTION = "Stopped Currently - Prep/Kitting/Cooking & Production"
    POTENTIAL_STOPPAGE_TOMORROW = "Potential Stoppage Tomorrow - Prep/Kitting/Cooking & Production"
    NO_IMPACT = "No Impact Currently"


class ReleaseLogTypes(ExtendedEnum, StrEnum):
    NEW = "NEW"
    FIX = "FIX"
    ONGOING = "ONGOING"


class FeatureTypes(ExtendedEnum, StrEnum):
    WHATS_NEW = "What's New"
    BUG_FIXES = "Bug Fixes"
    ONGOING = "Ongoing"


EP = "EveryPlate"
HF = "HelloFresh"
FJ = "Factor"
OE_BOB_CODE = "OE"
OH_BOB_CODE = "OH"
AZ_BOB_CODE = "AZ"
EZ_BOB_CODE = "EZ"

PACK_DAY_LIST = ["Thu-0", "Fri", "Sat", "Sun", "Mon", "Tue", "Wed", "Thu-1"]
ALL_BRANDS = "All Brands"

OZ_COEFFICIENT = 0.0625
PO_CASE_PRICE_FLAG_PERCENTAGE_THRESHOLD = Decimal(1.15)


class SortingOrder(StrEnum):
    ASCENDING = "ascending"
    DESCENDING = "descending"


class ActivationState(StrEnum):
    ON = "on"
    OFF = "off"


RECEIPT_OVERRIDE_ENTRY_TYPE = "Receipt Override"
PO_SCHEDULED_FOR_DELIVERY_TODAY_BOARD = "POs Scheduled for Delivery Today"
PO_CURRENT_WEEK_BOARD = "Purchase Orders - {week}"
INGREDIENT_DEPLETION_BOARD = "Ingredients Depletion - {week}"
CASE_SIZE_DISCREPANCY = "Case Size Discrepancy"
CASE_AND_DELIVERY_TIME_DISCREPANCY = "Case and delivery time discrepancy"
DIFFERENT_SHIPPED_AND_ORDER_QUANTITY = "Different shipped and order quantity"

ALL_POS = "All POs"
ALL_POS_WITH_TRANSFERS = "All POs + TOs"
ALL_POS_API = "allPos"
RECEIVED_POS_ONLY = "Received POs only"
NO_POS_PLACED = "No POs Placed"
ALL_POS_WITH_PO_COUNT = "All POs\n({po_count})"
WAREHOUSES_BRAND = "Warehouses"

DEFAULT_PICK_CONVERSION = 1
STATUS_BAR_AGGREGATED_ITEMS = ["Average", "Count", "Min", "Max", "Sum"]
CACHE_KEYS_TO_REMOVE = ["imt*", "ordering_tool*", "pimt*"]

PO_STATUS_SHEET_NAME = "PO Status"
PIMT_PO_STATUS_SHEET_NAME = "PIMT PO Status"

NUM_OF_AVAILABLE_WEEKS = 13
