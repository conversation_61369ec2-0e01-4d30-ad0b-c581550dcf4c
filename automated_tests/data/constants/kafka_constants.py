from enum import IntEnum, StrEnum

from automated_tests.utils.extended_enum import ExtendedEnum
from procurement.constants.hellofresh_constant import InventoryInputType

COLUMNS_TO_EXCLUDE_FROM_SELECT = ["_fake_pk"]

WMS_TYPE_MAPPING = {
    "HJ": InventoryInputType.HJ,
    "WMSL": InventoryInputType.WMSL,
    "E2Open": InventoryInputType.E2OPEN,
}


class WmsHeaders(ExtendedEnum, StrEnum):
    HJ = "HJ"
    E2OPEN = "E2Open"
    WMSL = "WMSL"


class PurchaseOrderType(IntEnum):
    PURCHASE_ORDER_TYPE_UNSPECIFIED = 0
    PURCHASE_ORDER_TYPE_STANDARD = 1
    PURCHASE_ORDER_TYPE_EMERGENCY = 2
    PURCHASE_ORDER_TYPE_PROVISIONAL = 3


class AdvanceShippingNoticeShippingMethods(IntEnum):
    SHIPPING_METHOD_UNSPECIFIED = 0
    SHIPPING_METHOD_VENDOR = 1
    SHIPPING_METHOD_CROSSDOCK = 2
    SHIPPING_METHOD_FREIGHT_ON_BOARD = 3
    SHIPPING_METHOD_OTHER = 4


class InventoryInputTypeKafka(IntEnum):
    INVENTORY_INPUT_TYPE_UNSPECIFIED = 0
    INVENTORY_INPUT_TYPE_MANUAL = 1
    INVENTORY_INPUT_TYPE_LOT = 2


class Brands(IntEnum):
    BRAND_UNSPECIFIED = 0
    BRAND_HELLOFRESH = 1
    BRAND_EVERYPLATE = 2
    BRAND_CHEFS_PLATE = 3
    BRAND_GREEN_CHEF = 4
    BRAND_FACTOR75 = 5
    BRAND_GOOD_CHOP = 6
    BRAND_YOUFOODZ = 7
    BRAND_GO_READY_MADE = 8
    BRAND_THEPETSTABLE = 9


class Status(IntEnum):
    STATUS_UNSPECIFIED = 0
    STATUS_ACTIVE = 1
    STATUS_DRAFT = 2
    STATUS_INACTIVE = 3
    STATUS_LIMITED = 4
    STATUS_ARCHIVED = 5


class ManufacturedSkuType(IntEnum):
    MANUFACTURED_SKU_TYPE_UNSPECIFIED = 0
    MANUFACTURED_SKU_TYPE_PACKAGED = 1
    MANUFACTURED_SKU_TYPE_SUB_RECIPE = 2
    MANUFACTURED_SKU_TYPE_RECIPE = 3


class WmsName(IntEnum):
    WMS_NAME_UNSPECIFIED = 0
    WMS_NAME_E2OPEN = 1
    WMS_NAME_FCMS = 2
    WMS_NAME_HJ = 3
    WMS_NAME_WMS2_0 = 4
    WMS_NAME_WMSL = 5


class SupplierStatus(StrEnum):
    ONBOARDING = "Onboarding"
    ACTIVE = "Active"
    OFFBOARDING = "Offboarding"
    INACTIVE = "Inactive"
    ARCHIVED = "Archived"
