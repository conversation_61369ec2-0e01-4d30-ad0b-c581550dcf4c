import dataclasses
import random
import uuid
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Self

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import Users, Warehouses
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, CURRENT_WEEK
from automated_tests.data.data_generator import generate_po_number, random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.supplier import TestSupplier
from automated_tests.data.models.warehouse import TestWarehouse
from automated_tests.utils.warehouse_utils import WarehouseUtils
from procurement.constants.hellofresh_constant import DEPLETION_WEEKS_AHEAD, MARKET_US, NO_BRAND, PoAckUnitOfMeasure
from procurement.core import utils
from procurement.core.dates import ScmWeek
from procurement.core.typing import SKU_CODE
from procurement.data.models.ordering.purchase_order import (
    PurchaseOrderModel,
    PurchaseOrderSkuModel,
    TransferOrderSkuModel,
)
from procurement.services.database import app_db


@dataclasses.dataclass
class TestPurchaseOrderSku:
    sku: TestSku
    case_price: Decimal = Decimal("18.25")
    total_price: Decimal = Decimal("547.5")
    order_size: int = 30
    qty: Decimal = Decimal(4200)
    case_size: Decimal = Decimal(140)
    buffer: Decimal | None = Decimal(10)
    unit_type: str = "CASE_TYPE"
    case_unit: str = "unit"


@dataclasses.dataclass
class TestTransferOrderSku:
    sku: TestSku
    case_price: Decimal = Decimal("18.25")
    total_price: Decimal = Decimal("547.5")
    order_size: int = 30
    qty: Decimal = Decimal(4200)
    case_size: Decimal = Decimal(140)
    unit_type: str = "CASE_TYPE"
    case_unit: str = "unit"
    original_po_number: str = None
    original_supplier_id: str = None
    original_supplier_code: str = None
    original_lot_code: str = None
    line_item_uuid: str = dataclasses.field(default_factory=lambda: str(uuid.uuid4()))


@dataclasses.dataclass
class TestPurchaseOrder(TestRecord):
    po_number: str
    bob_code: str
    source_bob_code: str
    dc: str
    week: ScmWeek
    brand: str = NO_BRAND
    ot_last_updated: datetime = datetime.fromisoformat("2020-07-22 21:32:01")
    delivery_time_start: datetime = datetime.fromisoformat("2020-08-10 10:00:00")
    sent_at: datetime = datetime.fromisoformat("2020-07-23 01:32:00")
    supplier_name: str = "Test supplier"
    sent: bool = True
    deleted: bool = False
    ordered_by: str = Users.test_user.value.email
    status: str = "INITIAL"
    emergency_reason: str = ""
    shipping_method: str = ""
    line_items: dict[SKU_CODE, TestPurchaseOrderSku] = dataclasses.field(default_factory=dict)
    transfer_line_items: list[TestTransferOrderSku] = dataclasses.field(default_factory=list)

    supplier_code: int | None = dataclasses.field(init=False)
    order_number: str = dataclasses.field(init=False)
    supplier: TestSupplier | None = dataclasses.field(init=False)
    market: str = MARKET_US

    __key: tuple = None
    __uuid: str = None

    @property
    def po_uuid(self):
        return self.__uuid

    def __post_init__(self):
        self.order_number = utils.get_order_number_from_po_number(self.po_number)
        self.supplier_code = self.get_default_supplier_code(self.supplier_name)
        self.supplier = TestSupplier(
            code=self.supplier_code,
            name=self.supplier_name,
            legal_name=self.supplier_name,
            market=self.market,
        )
        if self.line_items:
            # making copy of line items for the cases when PO was copied from
            # some base PO with dataclasses.replace to not affect base's items
            self.line_items = dict(self.line_items)
        self.__key = (self.__class__.__name__, self.po_number, self.deleted)
        self.__uuid = str(uuid.uuid4())

    def remove_supplier(self) -> Self:
        self.supplier_name = None
        self.supplier_code = None
        self.supplier = None
        return self

    def get_transfer_line_items_by_supplier_code(self) -> dict[int, TestTransferOrderSku]:
        return {int(it.original_supplier_code): it for it in self.transfer_line_items}

    def with_sku(self, order_sku: TestPurchaseOrderSku) -> Self:
        """
        Adding a PO-SKU line item to the PO.
        Replaces existing SKUs by sku_code, so you can't add two same SKUs to one PO.
        Note: it's making a copy of original PO in order for easier use with "base" POs

        Example:

        po1 = TestPurchaseOrder(...).with_sku(sku1)
        po2 = po1.with_sku(sku2)  # po1 will not be changed here

        # if you need to change po1
        # po1.with_sku(sku2) will not work
        # you need to assign it back after the change:
        po1 = po1.with_sku(sku2)
        :param order_sku: PO's line item
        :return:
        """
        new_po = dataclasses.replace(self)
        new_po.line_items[order_sku.sku.sku_code] = order_sku
        return new_po

    def first_line(self) -> TestPurchaseOrderSku | None:
        return next(iter(self.line_items.values()), None)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(PurchaseOrderModel).values(
                {
                    PurchaseOrderModel.po_uuid: self.__uuid,
                    PurchaseOrderModel.po_number: self.po_number,
                    PurchaseOrderModel.order_number: self.order_number,
                    PurchaseOrderModel.bob_code: self.bob_code,
                    PurchaseOrderModel.source_bob_code: self.source_bob_code,
                    PurchaseOrderModel.brand: self.brand,
                    PurchaseOrderModel.dc: self.dc,
                    PurchaseOrderModel.supplier: self.supplier_name,
                    PurchaseOrderModel.supplier_code: self.supplier_code,
                    PurchaseOrderModel.ot_po_status: self.status,
                    PurchaseOrderModel.is_sent: self.sent,
                    PurchaseOrderModel.week: str(self.week),
                    PurchaseOrderModel.order_date: self.sent_at,
                    PurchaseOrderModel.delivery_time_start: self.delivery_time_start,
                    PurchaseOrderModel.emergency_reason: self.emergency_reason,
                    PurchaseOrderModel.ordered_by: self.ordered_by,
                    PurchaseOrderModel.ot_last_updated: self.ot_last_updated,
                    PurchaseOrderModel.deleted: self.deleted,
                    PurchaseOrderModel.internal_last_updated: self.ot_last_updated,
                    PurchaseOrderModel.shipping_method: self.shipping_method,
                }
            )
        )

        self.__load_order_sku()
        self.__load_transfer_order_sku()

    def __load_order_sku(self):
        app_db.apply_query(
            sqla.insert(PurchaseOrderSkuModel).values(
                [
                    {
                        PurchaseOrderSkuModel.po_uuid: self.__uuid,
                        PurchaseOrderSkuModel.sku_uuid: sku.sku.sku_uuid,
                        PurchaseOrderSkuModel.order_size: sku.order_size,
                        PurchaseOrderSkuModel.order_unit: sku.unit_type,
                        PurchaseOrderSkuModel.case_price: sku.case_price,
                        PurchaseOrderSkuModel.case_size: sku.case_size,
                        PurchaseOrderSkuModel.case_unit: sku.case_unit,
                        PurchaseOrderSkuModel.quantity: sku.qty,
                        PurchaseOrderSkuModel.buffer: sku.buffer,
                        PurchaseOrderSkuModel.total_price: sku.total_price,
                    }
                    for sku in self.line_items.values()
                ]
            )
        )

    def __load_transfer_order_sku(self):
        if not self.transfer_line_items:
            return
        app_db.apply_query(
            sqla.insert(TransferOrderSkuModel).values(
                [
                    {
                        TransferOrderSkuModel.po_uuid: self.po_uuid,
                        TransferOrderSkuModel.line_item_uuid: sku.line_item_uuid,
                        TransferOrderSkuModel.sku_uuid: sku.sku.sku_uuid,
                        TransferOrderSkuModel.order_size: sku.order_size,
                        TransferOrderSkuModel.order_unit: sku.unit_type,
                        TransferOrderSkuModel.case_price: sku.case_price,
                        TransferOrderSkuModel.case_size: sku.case_size,
                        TransferOrderSkuModel.case_unit: sku.case_unit,
                        TransferOrderSkuModel.quantity: sku.qty,
                        TransferOrderSkuModel.total_price: sku.total_price,
                        TransferOrderSkuModel.original_po_number: sku.original_po_number,
                        TransferOrderSkuModel.original_supplier_id: sku.original_supplier_id,
                        TransferOrderSkuModel.original_supplier_code: sku.original_supplier_code,
                        TransferOrderSkuModel.original_lot_code: sku.original_lot_code,
                    }
                    for sku in self.transfer_line_items
                ]
            )
        )

    def unload(self):
        if self.transfer_line_items:
            app_db.apply_query(sqla.delete(PurchaseOrderSkuModel).where(TransferOrderSkuModel.po_uuid == self.__uuid))
        app_db.apply_query(sqla.delete(PurchaseOrderSkuModel).where(PurchaseOrderSkuModel.po_uuid == self.__uuid))
        app_db.apply_query(sqla.delete(PurchaseOrderModel).where(PurchaseOrderModel.po_uuid == self.__uuid))

    @staticmethod
    def get_default_supplier_code(supplier_name: str) -> int:
        return abs(hash(supplier_name) % 100000)

    @staticmethod
    def generate_po(
        site: TestSiteConfig = None,
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        delivery_time_start: datetime | date = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        source_bob_code: str = None,
    ) -> "TestPurchaseOrder":
        if (site is None) == (warehouse is None):
            raise ValueError("Either site or warehouse should be supplied")
        week = week or CURRENT_WEEK
        market = (site or warehouse).market
        bob_code = site.bob_code if site else warehouse.bob_code
        return TestPurchaseOrder(
            po_number=generate_po_number(bob_code) if po_number is None else po_number,
            bob_code=bob_code,
            week=week,
            delivery_time_start=delivery_time_start or datetime.now().replace(microsecond=0),
            supplier_name=supplier_name or f"{data_generator.generate_string(8)} Supplier",
            emergency_reason=emergency_reason or "",
            shipping_method=shipping_method or "",
            brand=site.brand.code if site is not None else NO_BRAND,
            dc=site.site.code if site is not None else None,
            source_bob_code=source_bob_code,
            market=market.code,
        )

    @staticmethod
    def generate_transfer_order(
        original_pos: list["TestPurchaseOrder"],
        site: TestSiteConfig = None,
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        delivery_time_start: datetime | date = None,
        emergency_reason: str = None,
        shipping_method: str = None,
    ) -> "TestPurchaseOrder":
        if (site is None) == (warehouse is None):
            raise ValueError("Either site or warehouse should be supplied")
        first_po = original_pos[0]
        transfer_order = TestPurchaseOrder.generate_po(
            site=site,
            warehouse=warehouse,
            week=week,
            po_number=po_number or generate_po_number((site or warehouse).bob_code, is_transfer_order=True),
            delivery_time_start=delivery_time_start,
            emergency_reason=emergency_reason,
            shipping_method=shipping_method,
            source_bob_code=first_po.bob_code,
        )
        combined_line_item = dataclasses.replace(
            first_po.first_line(),
            total_price=Decimal(),
            order_size=0,
            qty=Decimal(),
        )
        for po in original_pos:
            line_item = po.first_line()
            combined_line_item.total_price += line_item.total_price
            combined_line_item.order_size += line_item.order_size
            combined_line_item.qty += line_item.qty
            to_line = TestTransferOrderSku(
                sku=line_item.sku,
                case_price=line_item.case_price,
                total_price=line_item.total_price,
                order_size=line_item.order_size,
                qty=line_item.qty,
                case_size=line_item.case_size,
                unit_type=line_item.unit_type,
                case_unit=line_item.case_unit,
                original_po_number=po.po_number,
                original_supplier_id=po.supplier.id,
                original_supplier_code=str(po.supplier.code),
                original_lot_code=str(po.supplier.code),
            )
            transfer_order.transfer_line_items.append(to_line)
        transfer_order = transfer_order.with_sku(combined_line_item)
        transfer_order.remove_supplier()
        return transfer_order

    @staticmethod
    def generate_line_item(
        sku: TestSku = None,
        order_size: int = None,
        case_size: Decimal = None,
        case_unit: PoAckUnitOfMeasure = None,
    ) -> TestPurchaseOrderSku:
        sku = sku or TestSku.generate_sku()
        case_price = round(Decimal(random.uniform(1, 1001)), 2)
        case_size = case_size if case_size is not None else Decimal(random_int(3))
        order_size = order_size if order_size is not None else random_int(2)
        return TestPurchaseOrderSku(
            sku=sku,
            order_size=order_size,
            case_size=case_size,
            case_price=Decimal(str(case_price)),
            total_price=Decimal(order_size) * case_price,
            qty=order_size * Decimal(case_size),
            buffer=Decimal(random.randint(5, 20)),
            case_unit=case_unit or "unit",
        )

    @staticmethod
    def generate_po_with_sku(
        site: TestSiteConfig = None,
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        sku: TestSku = None,
        delivery_time_start: datetime | date = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        order_size: int = None,
        case_size: Decimal = None,
        case_unit: PoAckUnitOfMeasure = None,
        source_bob_code: str = None,
    ) -> "TestPurchaseOrder":
        purchase_order = TestPurchaseOrder.generate_po(
            site=site,
            warehouse=warehouse,
            week=week,
            po_number=po_number,
            delivery_time_start=delivery_time_start,
            emergency_reason=emergency_reason,
            shipping_method=shipping_method,
            supplier_name=supplier_name,
            source_bob_code=source_bob_code,
        )
        return purchase_order.with_sku(
            TestPurchaseOrder.generate_line_item(
                sku=sku,
                order_size=order_size,
                case_size=case_size,
                case_unit=case_unit,
            )
        )

    @staticmethod
    def generate_inbound_po_with_sku(
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        sku: TestSku = None,
        delivery_time_start: datetime = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        order_size: int = None,
        case_unit: PoAckUnitOfMeasure = None,
        case_size: Decimal = None,
        source_bob_code: str = None,
    ):
        warehouse = warehouse if warehouse else Warehouses.random_value()
        return TestPurchaseOrder.generate_po_with_sku(
            warehouse=warehouse,
            week=week,
            po_number=po_number,
            sku=sku,
            delivery_time_start=delivery_time_start,
            emergency_reason=emergency_reason,
            shipping_method=shipping_method,
            supplier_name=supplier_name,
            order_size=order_size,
            case_unit=case_unit,
            case_size=case_size,
            source_bob_code=source_bob_code,
        )

    @staticmethod
    def generate_pos(
        quantity: int,
        site: TestSiteConfig = None,
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        sku: TestSku = None,
        delivery_time_start: datetime = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        order_size: int = None,
        case_size: Decimal = None,
        case_unit: PoAckUnitOfMeasure = None,
    ) -> list["TestPurchaseOrder"]:
        return [
            TestPurchaseOrder.generate_po_with_sku(
                site=site,
                warehouse=warehouse,
                week=week,
                po_number=po_number,
                sku=sku,
                delivery_time_start=delivery_time_start,
                emergency_reason=emergency_reason,
                shipping_method=shipping_method,
                supplier_name=supplier_name,
                order_size=order_size,
                case_size=case_size,
                case_unit=case_unit,
            )
            for _ in range(quantity)
        ]

    @staticmethod
    def generate_outbound_purchase_order_with_sku(
        warehouse: TestWarehouse = None,
        week: ScmWeek = None,
        po_number: str = None,
        sku: TestSku = None,
        delivery_time_start: datetime = None,
    ) -> "TestPurchaseOrder":
        week = week or ScmWeek.current_week()
        sku = sku or TestSku.generate_sku()
        warehouse = warehouse or Warehouses.random_value()

        purchase_order = TestPurchaseOrder.generate_po_with_sku(
            site=WarehouseUtils.get_regional_dc(warehouse),
            week=week,
            po_number=po_number,
            sku=sku,
            delivery_time_start=delivery_time_start,
            supplier_name=next(iter(warehouse.ot_suppliers)),
            order_size=random.randint(5, 20),
        )

        return purchase_order

    @staticmethod
    def generate_inbound_pos(pos_quantity: int, sku: TestSku, warehouse: TestWarehouse = None) -> ["TestPurchaseOrder"]:
        return [
            TestPurchaseOrder.generate_inbound_po_with_sku(sku=sku, warehouse=warehouse) for _ in range(pos_quantity)
        ]

    @staticmethod
    def generate_inbound_pos_with_sku(pos_quantity: int):
        return [TestPurchaseOrder.generate_inbound_po_with_sku() for _ in range(pos_quantity)]

    @staticmethod
    def generate_one_warehouse_outbound_pos_for_one_sku(
        pos_quantity: int, sku: TestSku, warehouse: TestWarehouse = None
    ) -> ["TestPurchaseOrder"]:
        warehouse = warehouse if warehouse else Warehouses.random_value()
        return [
            TestPurchaseOrder.generate_outbound_purchase_order_with_sku(sku=sku, warehouse=warehouse)
            for _ in range(pos_quantity)
        ]

    @staticmethod
    def generate_outbound_pos_with_sku(pos_quantity: int) -> ["TestPurchaseOrder"]:
        return [TestPurchaseOrder.generate_outbound_purchase_order_with_sku() for _ in range(pos_quantity)]

    @staticmethod
    def generate_pos_with_sku(
        pos_quantity: int,
        site_config: TestSiteConfig,
        one_sku: bool = False,
        week: ScmWeek = None,
        po_number: str = None,
        delivery_time_start: datetime = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        order_size: int = None,
        case_size: Decimal = None,
        case_unit: PoAckUnitOfMeasure = None,
    ) -> ["TestPurchaseOrder"]:
        if one_sku:
            sku = TestSku.generate_sku()
            return [
                TestPurchaseOrder.generate_po_with_sku(
                    site=site_config,
                    sku=sku,
                    week=week,
                    po_number=po_number,
                    delivery_time_start=delivery_time_start,
                    emergency_reason=emergency_reason,
                    shipping_method=shipping_method,
                    supplier_name=supplier_name,
                    order_size=order_size,
                    case_size=case_size,
                    case_unit=case_unit,
                )
                for _ in range(pos_quantity)
            ]
        else:
            return [TestPurchaseOrder.generate_po_with_sku(site=site_config) for _ in range(pos_quantity)]

    @staticmethod
    def generate_pos_for_several_days(
        days_qty: int,
        site_config: TestSiteConfig,
        start_date: datetime = CURRENT_DATETIME,
        week: ScmWeek = None,
        po_number: str = None,
        sku: TestSku = None,
        emergency_reason: str = None,
        shipping_method: str = None,
        supplier_name: str = None,
        order_size: int = None,
        case_size: Decimal = None,
        case_unit: PoAckUnitOfMeasure = None,
    ):
        date_list = [start_date + timedelta(days=x) for x in range(days_qty)]
        order_size = order_size or random_int(2)
        case_size = case_size or Decimal(random_int(3))
        return [
            TestPurchaseOrder.generate_po_with_sku(
                site=site_config,
                week=week,
                po_number=po_number,
                sku=sku,
                delivery_time_start=delivery_time,
                emergency_reason=emergency_reason,
                shipping_method=shipping_method,
                supplier_name=supplier_name,
                order_size=order_size,
                case_size=case_size,
                case_unit=case_unit,
            )
            for delivery_time in date_list
        ]

    @staticmethod
    def generate_pos_for_several_weeks(
        is_inbound: bool, sku: TestSku, weeks_qty: int = DEPLETION_WEEKS_AHEAD, warehouse: TestWarehouse = None
    ):
        generator = (
            TestPurchaseOrder.generate_inbound_po_with_sku
            if is_inbound
            else TestPurchaseOrder.generate_outbound_purchase_order_with_sku
        )
        return [
            generator(sku=sku, warehouse=warehouse, week=week)
            for week in ScmWeek.range(ScmWeek.current_week(), ScmWeek.current_week() + weeks_qty)
        ]
