import dataclasses
import random
from datetime import datetime
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data.constants.base_constants import Users
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, CURRENT_WEEK
from procurement.core.dates import ScmWeek
from procurement.core.typing import UNITS
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.receive import ReceivingModel
from procurement.services.database import app_db

from .core import TestRecord
from .imt_config import TestBrand, TestSite, TestSiteConfig
from .po import TestPurchaseOrder
from .sku import TestSku
from .user import TestUser


@dataclasses.dataclass
class TestReceiving(TestRecord):
    supplier_name: str
    po: TestPurchaseOrder
    sku: TestSku
    site: TestSite
    week: ScmWeek
    user: TestUser
    brand: TestBrand
    receive_timestamp: datetime
    total_cases_received: int
    case_count_one_total_units: int = None
    case_count_two_total_units: int = None
    case_count_three_total_units: int = None
    case_size_one: UNITS = None
    case_size_two: UNITS = None
    case_size_three: UNITS = None
    source: DataSource = DataSource.APP

    __key: tuple = None
    load_order: ClassVar[int] = (
        max(
            TestSite.load_order,
            TestBrand.load_order,
            TestUser.load_order,
            TestSku.load_order,
            TestPurchaseOrder.load_order,
        )
        + 1
    )

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.po.po_number,
            self.sku.sku_code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(ReceivingModel).values(
                {
                    ReceivingModel.supplier: self.supplier_name,
                    ReceivingModel.supplier_id: "",
                    ReceivingModel.po: self.po.po_number,
                    ReceivingModel.sku_code: self.sku.sku_code,
                    ReceivingModel.sku_ingredient: self.sku.sku_name,
                    ReceivingModel.dc: self.site.code,
                    ReceivingModel.arrival_timestamp: datetime.now(),
                    ReceivingModel.receive_timestamp: self.receive_timestamp,
                    ReceivingModel.delivery_status: "Active",
                    ReceivingModel.total_cases_received: self.total_cases_received,
                    ReceivingModel.case_count_one_total_units: self.case_count_one_total_units,
                    ReceivingModel.case_count_two_total_units: self.case_count_two_total_units,
                    ReceivingModel.case_count_three_total_units: self.case_count_three_total_units,
                    ReceivingModel.case_size_one: self.case_size_one,
                    ReceivingModel.case_size_two: self.case_size_two,
                    ReceivingModel.case_size_three: self.case_size_three,
                    ReceivingModel.week: str(self.week),
                    ReceivingModel.username: self.user.email,
                    ReceivingModel.source: self.source.value,
                    ReceivingModel.brand: self.brand.code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(ReceivingModel).where(
                ReceivingModel.po == self.po.po_number,
                ReceivingModel.sku_code == self.sku.sku_code,
            )
        )

    @staticmethod
    def generate_receiving(
        po: TestPurchaseOrder,
        site_config: TestSiteConfig,
        week: ScmWeek = CURRENT_WEEK,
        user=Users.test_user.value,
        receive_timestamp: datetime = CURRENT_DATETIME,
        total_cases_received: int = random.randint(10, 100),
        case_count_one_total_units: int = random.randint(10, 100),
        case_count_two_total_units: int = 0,
        case_count_three_total_units: int = 0,
        case_size_one: int = random.randint(10, 100),
        case_size_two: int = 0,
        case_size_three: int = 0,
    ) -> "TestReceiving":
        return TestReceiving(
            supplier_name=po.supplier_name,
            po=po,
            sku=po.first_line().sku,
            site=site_config.site,
            week=week,
            user=user,
            brand=site_config.brand,
            receive_timestamp=receive_timestamp,
            total_cases_received=total_cases_received,
            case_count_one_total_units=case_count_one_total_units,
            case_count_two_total_units=case_count_two_total_units,
            case_count_three_total_units=case_count_three_total_units,
            case_size_one=case_size_one,
            case_size_two=case_size_two,
            case_size_three=case_size_three,
        )
