import dataclasses
from decimal import Decimal

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.models.core import TestRecord
from procurement.data.models.pimt.planned_depletion import PlannedDepletionModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestPlannedDepletionModel(TestRecord):
    week: int
    sku_code: str
    quantity: Decimal
    demand_pipeline: str
    region: str
    owner: str
    market: str

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.region,
            self.market,
            self.demand_pipeline,
            self.sku_code,
            self.owner,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(PlannedDepletionModel).values(
                {
                    PlannedDepletionModel.week: self.week,
                    PlannedDepletionModel.sku_code: self.sku_code,
                    PlannedDepletionModel.quantity: self.quantity,
                    PlannedDepletionModel.demand_pipeline: self.demand_pipeline,
                    PlannedDepletionModel.region: self.region,
                    PlannedDepletionModel.owner: self.owner,
                    PlannedDepletionModel.market: self.market,
                },
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(PlannedDepletionModel).where(
                PlannedDepletionModel.week == self.week,
                PlannedDepletionModel.sku_code == self.sku_code,
                PlannedDepletionModel.demand_pipeline == self.demand_pipeline,
                PlannedDepletionModel.region == self.region,
                PlannedDepletionModel.owner == self.owner,
                PlannedDepletionModel.market == self.market,
            )
        )

    @staticmethod
    def generate_planned_depletion(
        week: int = None,
        sku_code: str = None,
        quantity: int | float = None,
        demand_pipeline: str = None,
        region: str = None,
        owner: str = None,
        market: str = None,
    ):
        return TestPlannedDepletionModel(
            week=week,
            sku_code=sku_code or data_generator.generate_sku_code(),
            quantity=Decimal(quantity or data_generator.random_int()),
            demand_pipeline=demand_pipeline or data_generator.generate_string(),
            region=region or data_generator.generate_string(),
            owner=owner or data_generator.generate_string(),
            market=market or data_generator.generate_string(),
        )
