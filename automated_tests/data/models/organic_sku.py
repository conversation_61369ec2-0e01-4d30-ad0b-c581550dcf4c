import dataclasses

import sqlalchemy as sqla

from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.typing import MARKET
from procurement.data.models.inventory.alternative_sku_mapping import OrganicSkuModel
from procurement.services.database import app_db

from .core import TestRecord
from .sku import TestSku


@dataclasses.dataclass
class TestOrganicSku(TestRecord):
    interchangable_sku: TestSku
    original_sku: TestSku
    market: MARKET

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.interchangable_sku.sku_code,
            self.original_sku.sku_code,
            self.market,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(OrganicSkuModel).values(
                {
                    OrganicSkuModel.interchangable_sku_code: self.interchangable_sku.sku_code,
                    OrganicSkuModel.original_sku_code: self.original_sku.sku_code,
                    OrganicSkuModel.market: self.market,
                }
            ),
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(OrganicSkuModel).where(
                OrganicSkuModel.interchangable_sku_code == self.interchangable_sku.sku_code,
                OrganicSkuModel.original_sku_code == self.original_sku.sku_code,
                OrganicSkuModel.market == self.market,
            )
        )

    @staticmethod
    def generate_organic_sku(interchangable_sku: TestSku = None, original_sku: TestSku = None, market: str = MARKET_US):
        return TestOrganicSku(
            interchangable_sku=interchangable_sku or TestSku.generate_sku(),
            original_sku=original_sku or TestSku.generate_sku(),
            market=market,
        )
