import dataclasses
from datetime import datetime
from typing import ClassVar, Optional

import sqlalchemy as sqla

from automated_tests.data.constants.base_constants import Checklist<PERSON>tat<PERSON>, DayOfWeek, Users
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, CURRENT_WEEK, DATE_FORMAT_2
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import Test<PERSON>rand, TestSite, TestSiteConfig
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.user import TestUser
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory.weekend_coverage_checklist import WeekendCoverageChecklistModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestWcc(TestRecord):
    load_order: ClassVar[int] = TestUser.load_order + 1

    week: ScmWeek
    brand: TestBrand
    site: TestSite
    po: TestPurchaseOrder
    sku: TestSku
    updated_by: TestUser
    po_landing_day: DayOfWeek = None
    production_day_affected: DayOfWeek = None
    to_check: str = None
    contact_name_vendor_carrier: str = None
    email_phone: str = None
    back_up_vendor: str = None
    status: ChecklistStatus = None
    comment: str = None
    fob_pick_up_date: datetime = dataclasses.field(default_factory=datetime.now)
    last_updated: datetime = dataclasses.field(default_factory=datetime.now)
    market: str = MARKET_US

    __key: tuple = None
    __id: Optional[int] = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, id(self))

    def key(self) -> tuple:
        return self.__key

    def load(self):
        if self.__id is not None:
            raise ValueError("Record is already loaded, create a new one")
        self.__id = app_db.apply_query(
            sqla.insert(WeekendCoverageChecklistModel)
            .values(
                {
                    WeekendCoverageChecklistModel.week: int(self.week),
                    WeekendCoverageChecklistModel.brand: self.brand.code,
                    WeekendCoverageChecklistModel.site: self.site.code,
                    WeekendCoverageChecklistModel.po_number: self.po.po_number,
                    WeekendCoverageChecklistModel.sku_code: self.sku.sku_code,
                    WeekendCoverageChecklistModel.po_landing_day: self.po_landing_day,
                    WeekendCoverageChecklistModel.production_day_affected: self.production_day_affected,
                    WeekendCoverageChecklistModel.to_check: self.to_check,
                    WeekendCoverageChecklistModel.contact_name_vendor_carrier: self.contact_name_vendor_carrier,
                    WeekendCoverageChecklistModel.email_phone: self.email_phone,
                    WeekendCoverageChecklistModel.back_up_vendor: self.back_up_vendor,
                    WeekendCoverageChecklistModel.status: self.status,
                    WeekendCoverageChecklistModel.comment: self.comment,
                    WeekendCoverageChecklistModel.updated_by_id: self.updated_by.user_id,
                    WeekendCoverageChecklistModel.fob_pick_up_date: self.fob_pick_up_date,
                    WeekendCoverageChecklistModel.last_updated: self.last_updated,
                    WeekendCoverageChecklistModel.market: self.market,
                }
            )
            .returning(WeekendCoverageChecklistModel.id)
        )[0].id

    def unload(self):
        if self.__id is None:
            return
        app_db.apply_query(
            sqla.delete(WeekendCoverageChecklistModel).where(WeekendCoverageChecklistModel.id == self.__id)
        )
        self.__id = None

    @staticmethod
    def generate_weekend_coverage_checklist(
        site_config: TestSiteConfig,
        po: TestPurchaseOrder,
        sku: TestSku = None,
        production_day_affected: DayOfWeek = None,
        po_landing_day: DayOfWeek = None,
        week: ScmWeek = None,
        status: ChecklistStatus = None,
        fob_pick_up_date: datetime = CURRENT_DATETIME.strftime(DATE_FORMAT_2),
    ):
        po_landing_day = po_landing_day or DayOfWeek.random_value(exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY])
        production_day_affected = production_day_affected or DayOfWeek.random_value(
            exclude=[DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY]
        )
        return TestWcc(
            week=week or CURRENT_WEEK,
            brand=site_config.brand,
            site=site_config.site,
            po=po,
            sku=sku or po.first_line().sku,
            updated_by=Users.test_user.value,
            po_landing_day=po_landing_day,
            production_day_affected=production_day_affected,
            to_check=generate_string(),
            contact_name_vendor_carrier=generate_string(),
            email_phone=generate_string(),
            back_up_vendor=generate_string(),
            status=status or ChecklistStatus.random_value(),
            comment=generate_string(),
            fob_pick_up_date=fob_pick_up_date,
        )
