import dataclasses
from datetime import datetime
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import Users
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, CURRENT_WEEK
from automated_tests.data.data_generator import generate_string
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.user import TestUser
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.discard import DiscardModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestDiscard(TestRecord):
    user: TestUser
    site: TestSite
    sku: TestSku
    quantity: int
    week: ScmWeek
    brand: TestBrand
    source: DataSource = DataSource.APP
    discarded_datetime: datetime = None
    quality_instructions: str = None
    reason: str = None
    comment: str = None
    updated_by: TestUser = None
    deleted_by: TestUser = None
    deleted_ts: datetime = None
    market: str = MARKET_US

    __key: tuple = None
    load_order: ClassVar[int] = (
        max(
            TestSku.load_order,
            TestSite.load_order,
            TestBrand.load_order,
            TestUser.load_order,
        )
        + 1
    )

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.brand.code,
            self.site.code,
            self.sku.sku_code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(DiscardModel).values(
                {
                    DiscardModel.user: self.user.user_id,
                    DiscardModel.timestamp: CURRENT_DATETIME,
                    DiscardModel.dc: self.site.code,
                    DiscardModel.sku: self.sku.sku_code,
                    DiscardModel.discarded_datetime: self.discarded_datetime,
                    DiscardModel.quantity: self.quantity,
                    DiscardModel.quality_instructions: self.quality_instructions,
                    DiscardModel.reason: self.reason,
                    DiscardModel.source: self.source.value,
                    DiscardModel.week: str(self.week),
                    DiscardModel.brand: self.brand.code,
                    DiscardModel.comment: self.comment,
                    DiscardModel.updated_by: self.updated_by.email if self.updated_by else None,
                    DiscardModel.deleted_by: self.deleted_by.email if self.deleted_by else None,
                    DiscardModel.deleted_ts: self.deleted_ts,
                    DiscardModel.market: self.market,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(DiscardModel).where(
                DiscardModel.week == str(self.week),
                DiscardModel.brand == self.brand.code,
                DiscardModel.dc == self.site.code,
                DiscardModel.sku == self.sku.sku_code,
                DiscardModel.market == self.market,
            )
        )

    @staticmethod
    def generate_discard(
        sku: TestSku,
        site_config: TestSiteConfig,
        user: TestUser = Users.test_user.value,
        quantity: int = None,
        week: ScmWeek = None,
        discarded_datetime: datetime = None,
        source: DataSource = DataSource.APP,
        quality_instructions: str = None,
        reason: str = None,
        comment: str = generate_string(),
        updated_by: TestUser = None,
        deleted_by: TestUser = None,
        deleted_ts: datetime = None,
    ):
        return TestDiscard(
            user=user,
            site=site_config.site,
            sku=sku,
            quantity=quantity if quantity is not None else data_generator.random_int(),
            week=week or CURRENT_WEEK,
            brand=site_config.brand,
            discarded_datetime=discarded_datetime or CURRENT_DATETIME,
            source=source,
            quality_instructions=quality_instructions,
            reason=reason,
            comment=comment,
            updated_by=updated_by,
            deleted_by=deleted_by,
            deleted_ts=deleted_ts,
        )
