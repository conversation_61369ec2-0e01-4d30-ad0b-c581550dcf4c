import dataclasses
from decimal import Decimal
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory.allocation_price import AllocationPriceModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestAllocationPrice(TestRecord):
    sku: TestSku
    site: TestSite
    brand: TestBrand
    price: Decimal
    week: ScmWeek

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSite.load_order, TestBrand.load_order, TestSku.load_order) + 1

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.brand.code, self.site.code, self.sku.sku_code, self.week)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(AllocationPriceModel).values(
                {
                    AllocationPriceModel.sku_code: self.sku.sku_code,
                    AllocationPriceModel.site: self.site.code,
                    AllocationPriceModel.price: self.price,
                    AllocationPriceModel.scm_week: int(self.week),
                    AllocationPriceModel.brand: self.brand.code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(AllocationPriceModel).where(
                AllocationPriceModel.brand == self.brand.code,
                AllocationPriceModel.site == self.site.code,
                AllocationPriceModel.sku_code == self.sku.sku_code,
                AllocationPriceModel.scm_week == int(self.week),
            )
        )

    @staticmethod
    def generate_allocation_price(
        sku: TestSku, site_config: TestSiteConfig, week: ScmWeek = CURRENT_WEEK, price: int = None
    ) -> "TestAllocationPrice":
        return TestAllocationPrice(
            sku=sku,
            site=site_config.site,
            brand=site_config.brand,
            price=price or data_generator.random_int(),
            week=week,
        )
