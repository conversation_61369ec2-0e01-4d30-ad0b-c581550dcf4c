import abc
import dataclasses
from typing import ClassVar, Type

import sqlalchemy as sqla
from sqlalchemy import ColumnElement

from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.sku import TestSku
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.data.models.base import BaseModel
from procurement.data.models.inventory.ingredient import (
    CommodityGroupModel,
    IngredientSiteCommodityGroupModel,
    PurchasingCategoryModel,
    PurchasingSubcategoryModel,
)
from procurement.data.models.ordering.culinary_sku import CulinarySkuModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestCategoryBase(TestRecord):
    unique: ClassVar[bool] = False
    load_order: ClassVar[int] = TestSku.load_order + 1

    name: str

    __key: tuple = None
    __id: int = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.name)

    @abc.abstractmethod
    def _category_model(self) -> Type[BaseModel]:
        pass

    @abc.abstractmethod
    def _name_column(self) -> ColumnElement:
        pass

    def key(self) -> tuple:
        return self.__key

    def load(self):
        existing_category = app_db.select_one(
            sqla.select(self._category_model()).where(self._name_column() == self.name)
        )
        if not existing_category:
            self.__id = app_db.apply_query(
                sqla.insert(self._category_model())
                .values({self._name_column(): self.name})
                .returning(self._category_model().id)
            )[0].id

    def unload(self):
        if self.__id is not None:
            app_db.apply_query(sqla.delete(self._category_model()).where(self._category_model().id == self.__id))


@dataclasses.dataclass
class TestPurchasingCategory(TestCategoryBase):
    def _category_model(self) -> Type[BaseModel]:
        return PurchasingCategoryModel

    def _name_column(self) -> ColumnElement:
        return PurchasingCategoryModel.name


@dataclasses.dataclass
class TestPurchasingSubcategory(TestCategoryBase):
    def _category_model(self) -> Type[BaseModel]:
        return PurchasingSubcategoryModel

    def _name_column(self) -> ColumnElement:
        return PurchasingSubcategoryModel.name


@dataclasses.dataclass
class TestCommodityGroup(TestCategoryBase):
    def _category_model(self) -> Type[BaseModel]:
        return CommodityGroupModel

    def _name_column(self) -> ColumnElement:
        return CommodityGroupModel.group_name


@dataclasses.dataclass
class TestPurchasingCategoryAssignment(TestRecord):
    load_order: ClassVar[int] = TestCategoryBase.load_order + 1

    category: TestPurchasingCategory
    sku: TestSku

    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.sku.sku_code)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        category_id = app_db.select_scalar(
            sqla.select(PurchasingCategoryModel.id).where(PurchasingCategoryModel.name == self.category.name)
        )
        app_db.apply_query(
            sqla.update(CulinarySkuModel)
            .values({CulinarySkuModel.purchasing_category_id: category_id})
            .where(CulinarySkuModel.sku_code == self.sku.sku_code)
        )

    def unload(self):
        app_db.apply_query(
            sqla.update(CulinarySkuModel)
            .values({CulinarySkuModel.purchasing_category_id: None})
            .where(CulinarySkuModel.sku_code == self.sku.sku_code)
        )


@dataclasses.dataclass
class TestPurchasingSubcategoryAssignment(TestRecord):
    load_order: ClassVar[int] = TestCategoryBase.load_order + 1

    subcategory: str
    sku: TestSku

    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.sku.sku_code)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        subcategory_id = app_db.select_scalar(
            sqla.select(PurchasingSubcategoryModel.id).where(PurchasingSubcategoryModel.name == self.subcategory.name)
        )
        app_db.apply_query(
            sqla.update(CulinarySkuModel)
            .values({CulinarySkuModel.purchasing_subcategory_id: subcategory_id})
            .where(CulinarySkuModel.sku_code == self.sku.sku_code)
        )

    def unload(self):
        app_db.apply_query(
            sqla.update(CulinarySkuModel)
            .values({CulinarySkuModel.purchasing_subcategory_id: None})
            .where(CulinarySkuModel.sku_code == self.sku.sku_code)
        )


@dataclasses.dataclass
class TestCommodityGroupAssignment(TestRecord):
    load_order: ClassVar[int] = TestCategoryBase.load_order + 1

    group: TestCommodityGroup
    sku: TestSku
    site: str
    market: str = MARKET_US

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.site,
            self.sku.sku_code,
            self.market,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        commodity_group_id = app_db.select_scalar(sqla.select(CommodityGroupModel.id))
        app_db.apply_query(
            sqla.insert(IngredientSiteCommodityGroupModel).values(
                {
                    IngredientSiteCommodityGroupModel.commodity_group_id: commodity_group_id,
                    IngredientSiteCommodityGroupModel.sku_code: self.sku.sku_code,
                    IngredientSiteCommodityGroupModel.site: self.site,
                    IngredientSiteCommodityGroupModel.market: self.market,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(IngredientSiteCommodityGroupModel).where(
                IngredientSiteCommodityGroupModel.site == self.site,
                IngredientSiteCommodityGroupModel.sku_code == self.sku.sku_code,
                IngredientSiteCommodityGroupModel.market == self.market,
            )
        )
