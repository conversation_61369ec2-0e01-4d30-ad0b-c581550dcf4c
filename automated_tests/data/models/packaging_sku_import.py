import dataclasses

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.sku import TestSku
from procurement.data.models.inventory.packaging_units_per_truck_load import PackagingSkuImportModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestPackagingSkuImport(TestRecord):
    bob_code: str
    sku: TestSku
    units_per_truck_load: int

    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.bob_code, self.sku.sku_code)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(PackagingSkuImportModel).values(
                {
                    PackagingSkuImportModel.bob_code: self.bob_code,
                    PackagingSkuImportModel.sku_code: self.sku.sku_code,
                    PackagingSkuImportModel.units_per_truck_load: self.units_per_truck_load,
                }
            ),
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(PackagingSkuImportModel).where(
                PackagingSkuImportModel.bob_code == self.bob_code,
                PackagingSkuImportModel.sku_code == self.sku.sku_code,
            )
        )

    @staticmethod
    def generate_packaging_sku_import(
        bob_code: str, sku: TestSku, units_per_truck_load: int = None
    ) -> "TestPackagingSkuImport":
        if units_per_truck_load is None:
            units_per_truck_load = data_generator.random_int()
        return TestPackagingSkuImport(
            bob_code=bob_code,
            sku=sku,
            units_per_truck_load=units_per_truck_load,
        )
