import dataclasses
from datetime import datetime

import sqlalchemy as sqla

from procurement.data.models.pimt.exceptions import ExceptionModel
from procurement.services.database import app_db

from .core import TestRecord


@dataclasses.dataclass
class TestPimtExceptions(TestRecord):
    exception_date: datetime
    stats: dict

    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.exception_date)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(ExceptionModel).values(
                {ExceptionModel.exception_date: self.exception_date, ExceptionModel.stats: self.stats}
            )
        )

    def unload(self):
        app_db.apply_query(sqla.delete(ExceptionModel).where(ExceptionModel.exception_date == self.exception_date))
