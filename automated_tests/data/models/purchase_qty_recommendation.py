import dataclasses
from decimal import Decimal
from typing import ClassVar, Optional

import sqlalchemy as sqla

from automated_tests.data.data_generator import random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.supplier import TestSupplier
from procurement.core.dates import ScmWeek
from procurement.data.models.ordering.purchase_qty_recommendation import PurchaseQtyRecommendationModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestPurchaseQtyRecommendationItem(TestRecord):
    week: ScmWeek
    site: TestSite
    sku: TestSku
    supplier: TestSupplier
    quantity: Decimal
    year: int = None

    __key: tuple = None
    load_order: ClassVar[int] = (
        max(
            TestSku.load_order,
            TestSite.load_order,
            TestSupplier.load_order,
        )
        + 1
    )

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.site,
            self.sku.sku_uuid,
            self.supplier.id,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(PurchaseQtyRecommendationModel)
            .values(
                {
                    PurchaseQtyRecommendationModel.week: self.week.to_number_format(),
                    PurchaseQtyRecommendationModel.site: self.site,
                    PurchaseQtyRecommendationModel.sku_uuid: self.sku.sku_uuid,
                    PurchaseQtyRecommendationModel.supplier_uuid: self.supplier.id,
                    PurchaseQtyRecommendationModel.quantity: self.quantity,
                }
            )
            .returning(1)
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(PurchaseQtyRecommendationModel).where(
                PurchaseQtyRecommendationModel.week == self.week.to_number_format(),
                PurchaseQtyRecommendationModel.site == self.site,
                PurchaseQtyRecommendationModel.sku_uuid == self.sku.sku_uuid,
                PurchaseQtyRecommendationModel.supplier_uuid == self.supplier.id,
            )
        )

    @classmethod
    def generate_purchase_qty_recommendation(
        cls,
        week: ScmWeek,
        site_config: TestSiteConfig,
        supplier: Optional[TestSupplier] = None,
        sku: Optional[TestSku] = None,
        quantity: Optional[int] = None,
    ) -> "TestPurchaseQtyRecommendationItem":
        return cls(
            week=week,
            site=site_config.site,
            sku=sku or TestSku.generate_sku(),
            supplier=supplier or TestSupplier.generate_supplier(),
            quantity=Decimal(quantity if quantity is not None else random_int(1000)),
        )
