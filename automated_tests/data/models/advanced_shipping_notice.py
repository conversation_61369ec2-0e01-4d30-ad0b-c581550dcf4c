import dataclasses
import random
from datetime import datetime
from decimal import Decima<PERSON>
from typing import ClassVar, Optional

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import UnitMeasureASN
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.po import TestPurchaseOrder, TestPurchaseOrderSku
from procurement.data.models.ordering.advance_shipping_notice import (
    AdvanceShippingNoticeModel,
    AdvanceShippingNoticeState,
)
from procurement.services.database import app_db


@dataclasses.dataclass
class TestAdvanceShippingNotice(TestRecord):
    po: TestPurchaseOrder
    shipping_state: AdvanceShippingNoticeState
    number_of_pallets: int
    unit_of_measure: UnitMeasureASN
    size: int
    packing_size: Decimal
    planned_delivery_time: datetime
    shipment_time: datetime

    __key: tuple = None
    load_order: ClassVar[int] = TestPurchaseOrder.load_order + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.po.po_uuid,
            self.po.first_line().sku.sku_code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(AdvanceShippingNoticeModel).values(
                {
                    AdvanceShippingNoticeModel.po_uuid: self.po.po_uuid,
                    AdvanceShippingNoticeModel.order_number: self.po.order_number,
                    AdvanceShippingNoticeModel.sku_code: self.po.first_line().sku.sku_code,
                    AdvanceShippingNoticeModel.shipment_time: self.shipment_time,
                    AdvanceShippingNoticeModel.unit_measure: self.unit_of_measure.value,
                    AdvanceShippingNoticeModel.size: self.size,
                    AdvanceShippingNoticeModel.packing_size: self.packing_size,
                    AdvanceShippingNoticeModel.planned_delivery_time: self.planned_delivery_time,
                    AdvanceShippingNoticeModel.shipping_state: self.shipping_state,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(AdvanceShippingNoticeModel).where(AdvanceShippingNoticeModel.po_uuid == self.po.po_uuid)
        )

    def first_line(self) -> Optional[TestPurchaseOrderSku]:
        return self.po.first_line()

    @staticmethod
    def generate_advance_shipping_notice(
        po: TestPurchaseOrder,
        shipping_state: AdvanceShippingNoticeState = None,
        unit_of_measure: UnitMeasureASN = None,
        planned_delivery_time: datetime = None,
        shipment_time: datetime = None,
        size: int = None,
        packing_size: Decimal = None,
        number_of_pallets: int = None,
    ) -> "TestAdvanceShippingNotice":
        advance_shopping_notice = TestAdvanceShippingNotice(
            po=po,
            shipping_state=(
                shipping_state if shipping_state is not None else random.choice(list(AdvanceShippingNoticeState))
            ),
            number_of_pallets=number_of_pallets or data_generator.random_int(),
            unit_of_measure=unit_of_measure or random.choice(list(UnitMeasureASN)),
            size=size or data_generator.random_int(),
            packing_size=packing_size or Decimal(data_generator.random_int()),
            planned_delivery_time=planned_delivery_time or CURRENT_DATETIME,
            shipment_time=shipment_time or CURRENT_DATETIME,
        )
        return advance_shopping_notice
