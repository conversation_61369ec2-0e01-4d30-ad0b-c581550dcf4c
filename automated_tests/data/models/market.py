import dataclasses
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data.models.core import TestRecord
from procurement.data.models.social.market import MarketModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestMarket(TestRecord):
    load_order: ClassVar[int] = -1

    code: str
    name: str

    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.code, self.name)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(MarketModel).values(
                {
                    MarketModel.name: self.name,
                    MarketModel.code: self.code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(MarketModel).where(
                MarketModel.code == self.code,
                MarketModel.name == self.name,
            )
        )
