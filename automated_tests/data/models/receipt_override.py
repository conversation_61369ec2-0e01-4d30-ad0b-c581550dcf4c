import dataclasses
from datetime import date, datetime, timedelta
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import Users
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_DATETIME, CURRENT_WEEK
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.user import TestUser
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.receipt_override import ReceiptOverrideModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestReceiptOverride(TestRecord):
    user: TestUser
    site: TestSite
    brand: TestBrand
    week: ScmWeek
    po: TestPurchaseOrder
    sku: TestSku
    quantity: int
    cases: int
    receiving_date: date
    upd_tmst: datetime
    comment: str
    source: DataSource = DataSource.APP
    cre_tmst: datetime = dataclasses.field(default_factory=datetime.now)
    market: str = MARKET_US

    __key: tuple = None
    load_order: ClassVar[int] = (
        max(
            TestSku.load_order,
            TestPurchaseOrder.load_order,
            TestUser.load_order,
            TestBrand.load_order,
            TestSite.load_order,
        )
        + 1
    )

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.brand.code,
            self.site.code,
            self.po.po_number,
            self.sku.sku_code,
            self.week,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(ReceiptOverrideModel).values(
                {
                    ReceiptOverrideModel.user: self.user.user_id,
                    ReceiptOverrideModel.dc: self.site.code,
                    ReceiptOverrideModel.brand: self.brand.code,
                    ReceiptOverrideModel.week: str(self.week),
                    ReceiptOverrideModel.source: self.source.value,
                    ReceiptOverrideModel.cases: self.cases,
                    ReceiptOverrideModel.po_number: self.po.po_number,
                    ReceiptOverrideModel.sku_code: self.sku.sku_code,
                    ReceiptOverrideModel.qty: self.quantity,
                    ReceiptOverrideModel.receiving_date: self.receiving_date,
                    ReceiptOverrideModel.upd_tmst: self.upd_tmst,
                    ReceiptOverrideModel.cre_tmst: self.cre_tmst,
                    ReceiptOverrideModel.comment: self.comment,
                    ReceiptOverrideModel.market: self.market,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(ReceiptOverrideModel).where(
                ReceiptOverrideModel.brand == self.brand.code,
                ReceiptOverrideModel.dc == self.site.code,
                ReceiptOverrideModel.po_number == self.po.po_number,
                ReceiptOverrideModel.sku_code == self.sku.sku_code,
                ReceiptOverrideModel.week == str(self.week),
                ReceiptOverrideModel.market == self.market,
            )
        )

    @staticmethod
    def generate_receipt_override(
        site_config: TestSiteConfig,
        po: TestPurchaseOrder,
        user: TestUser = Users.test_user.value,
        week: ScmWeek = None,
        quantity: int = None,
        upd_tmst: datetime = None,
        source: DataSource = DataSource.APP,
        market: str = MARKET_US,
        cases: int = None,
        receiving_date: date | str = None,
        comment: str = None,
    ):
        if receiving_date == "":  # When we want to explicitly set receiving_date to None
            receiving_date = None
        elif receiving_date is None:
            receiving_date = data_generator.random_date(CURRENT_DATE, CURRENT_DATE + timedelta(days=90))
        return TestReceiptOverride(
            user=user,
            site=site_config.site,
            brand=site_config.brand,
            week=week or CURRENT_WEEK,
            po=po,
            cases=cases or data_generator.random_int(),
            comment=comment or data_generator.generate_string(),
            sku=po.first_line().sku,
            quantity=quantity if quantity is not None else data_generator.random_int(),
            receiving_date=receiving_date,
            upd_tmst=upd_tmst or CURRENT_DATETIME,
            source=source,
            market=market,
        )
