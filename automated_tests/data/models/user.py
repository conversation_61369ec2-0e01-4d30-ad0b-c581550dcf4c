import dataclasses
import itertools
from enum import StrEnum
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data.models.core import TestRecord
from procurement.auth.permissions import DC_USER_PERMISSIONS
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.data.models.social.user import (
    PermissionModel,
    PermissionRoleModel,
    RoleModel,
    UserModel,
    UserRoleModel,
)
from procurement.data.models.social.user_view_state import UserViewStateModel
from procurement.services.database import app_db


class AvailableRoles(StrEnum):
    ADMIN = "admin"
    PROCUREMENT_LEADERSHIP = "procurementleadership"
    PROCUREMENT_MANAGER = "procurementmanager"
    PROCUREMENT_USER = "procurementuser"
    SCRUBBER = "scrubber"
    DC_MANAGEMENT_USER = "dcmanagementuser"
    DC_UNIVERSAL_USER = "dcuniversaluser"
    DC_USER = "dcuser"
    HELLOCONNECT_USER = "helloconnect"


DC_ROLE_PERMISSIONS = list(
    itertools.chain.from_iterable(
        (permission.read,) if permission.is_read_only else (permission.write, permission.read)
        for permission in DC_USER_PERMISSIONS
    )
)


@dataclasses.dataclass
class TestDCRole(TestRecord):
    unique: ClassVar[bool] = False

    name: str

    __id: int = None
    __key: tuple = None

    def __post_init__(self):
        self.__key = (self.__class__.__name__, self.name)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        if not app_db.select_scalar(sqla.select(RoleModel).where(RoleModel.short_name == self.name).exists()):
            self.__id = app_db.apply_query(
                sqla.insert(RoleModel)
                .values({RoleModel.short_name: self.name, RoleModel.full_name: self.name})
                .returning(RoleModel.id)
            )[0].id
            self.__load_permissions()

    def __load_permissions(self):
        permissions = app_db.select_all(
            sqla.select(PermissionModel.id).where(PermissionModel.name.in_(DC_ROLE_PERMISSIONS))
        )
        app_db.apply_query(
            sqla.insert(PermissionRoleModel).values(
                [
                    {
                        PermissionRoleModel.permission_id: permission.id,
                        PermissionRoleModel.role_id: self.__id,
                    }
                    for permission in permissions
                ]
            )
        )

    def unload(self):
        if self.__id:
            self.__unload_permissions()
            app_db.apply_query(sqla.delete(RoleModel).where(RoleModel.id == self.__id))

    def __unload_permissions(self):
        app_db.apply_query(sqla.delete(PermissionRoleModel).where(PermissionRoleModel.role_id == self.__id))


@dataclasses.dataclass
class TestUser(TestRecord):
    load_order: ClassVar[int] = TestDCRole.load_order + 1

    first_name: str
    last_name: str
    email: str
    markets: set[str] = None
    forced_out: bool = False
    roles: set[AvailableRoles] = dataclasses.field(default_factory=set)
    dc_roles: set[str] = dataclasses.field(default_factory=set)

    __id: int = None
    __key: tuple = None

    def __post_init__(self):
        self.markets = self.markets or {MARKET_US}
        self.__key = (self.__class__.__name__, self.email)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        self.__id = app_db.apply_query(
            sqla.insert(UserModel)
            .values(
                {
                    UserModel.first_name: self.first_name,
                    UserModel.last_name: self.last_name,
                    UserModel.email: self.email,
                    UserModel.forced_out: self.forced_out,
                }
            )
            .returning(UserModel.id)
        )[0].id
        for market in self.markets:
            self.__load_roles(market)

    def __load_roles(self, market):
        roles = app_db.select_all(
            sqla.select(RoleModel.id).where(RoleModel.short_name.in_(self.dc_roles.union(self.roles)))
        )

        app_db.apply_query(
            sqla.insert(UserRoleModel).values(
                [
                    {
                        UserRoleModel.role_id: role.id,
                        UserRoleModel.user_id: self.__id,
                        UserRoleModel.market: market,
                    }
                    for role in roles
                ]
            )
        )

    def unload(self):
        self.__unload_roles()
        self.__unload_user_view_state()
        app_db.apply_query(sqla.delete(UserModel).where(UserModel.id == self.__id))

    def __unload_roles(self):
        app_db.apply_query(sqla.delete(UserRoleModel).where(UserRoleModel.user_id == self.__id))

    def __unload_user_view_state(self):
        app_db.apply_query(sqla.delete(UserViewStateModel).where(UserViewStateModel.user_id == self.__id))

    @property
    def user_id(self):
        return self.__id

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"
