import dataclasses
from datetime import date, timedelta
from decimal import Decima<PERSON>
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, CURRENT_WEEK_FIRST_DAY
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.core.dates import ScmWeek
from procurement.data.models.forecast.canada_forecast import CanadaForecastModel, CanadaForecastRecipeModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestCanadaForecast(TestRecord):
    sku: TestSku
    site: TestSite
    week: ScmWeek
    day: date
    brand: TestBrand
    value: Decimal

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSku.load_order, TestSite.load_order, TestBrand.load_order) + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.brand.code,
            self.site.code,
            self.day,
            self.sku.sku_code,
            self.week,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(CanadaForecastModel).values(
                {
                    CanadaForecastModel.site: self.site.code,
                    CanadaForecastModel.brand: self.brand.code,
                    CanadaForecastModel.scm_week: int(self.week),
                    CanadaForecastModel.sku_code: self.sku.sku_code,
                    CanadaForecastModel.day: self.day,
                    CanadaForecastModel.value: self.value,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(CanadaForecastModel).where(
                CanadaForecastModel.brand == self.brand.code,
                CanadaForecastModel.site == self.site.code,
                CanadaForecastModel.sku_code == self.sku.sku_code,
                CanadaForecastModel.scm_week == int(self.week),
                CanadaForecastModel.day == self.day,
            )
        )

    @staticmethod
    def generate_canada_forecast(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = None,
        day: date = None,
        value: Decimal = None,
    ) -> "TestCanadaForecast":
        return TestCanadaForecast(
            sku=sku,
            site=site_config.site,
            week=week or CURRENT_WEEK,
            day=day or CURRENT_WEEK_FIRST_DAY,
            brand=site_config.brand,
            value=value or Decimal(data_generator.random_int()),
        )

    @staticmethod
    def generate_canada_forecast_for_several_days(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = CURRENT_WEEK,
        days_qty: int = 9,
    ) -> ["TestCanadaForecast"]:
        date_list = [week.get_first_day() + timedelta(days=x) for x in range(days_qty)]
        forecasts = [
            TestCanadaForecast.generate_canada_forecast(sku=sku, site_config=site_config, day=days, week=week)
            for days in date_list
        ]
        return forecasts


@dataclasses.dataclass
class TestCanadaForecastRecipe(TestRecord):
    sku: TestSku
    site: TestSite
    brand: TestBrand
    week: ScmWeek
    recipe: str
    recipe_name: str
    picks: int
    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.site.code,
            self.brand.code,
            self.week,
            self.sku.sku_code,
            self.recipe,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(CanadaForecastRecipeModel).values(
                {
                    CanadaForecastRecipeModel.sku_code: self.sku.sku_code,
                    CanadaForecastRecipeModel.site: self.site.code,
                    CanadaForecastRecipeModel.brand: self.brand.code,
                    CanadaForecastRecipeModel.week: int(self.week),
                    CanadaForecastRecipeModel.recipe: self.recipe,
                    CanadaForecastRecipeModel.recipe_name: self.recipe_name,
                    # same pick for all meal sizes
                    CanadaForecastRecipeModel.picks: {s: self.picks for s in ("2", "4", "6")},
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(CanadaForecastRecipeModel).where(
                CanadaForecastRecipeModel.site == self.site.code,
                CanadaForecastRecipeModel.brand == self.brand.code,
                CanadaForecastRecipeModel.sku_code == self.sku.sku_code,
                CanadaForecastRecipeModel.week == int(self.week),
            )
        )

    @staticmethod
    def generate_canada_forecast_recipe(
        site_config: TestSiteConfig,
        sku: TestSku,
        week: ScmWeek = None,
        recipe: str = None,
        recipe_name: str = None,
        picks: int = None,
    ) -> "TestCanadaForecastRecipe":
        _recipe = recipe or data_generator.generate_string(str_length=4, with_numbers=True)
        return TestCanadaForecastRecipe(
            site=site_config.site,
            brand=site_config.brand,
            week=week or CURRENT_WEEK,
            sku=sku,
            recipe=_recipe,
            recipe_name=f"{site_config.site.name} recipe {_recipe}" if recipe_name is None else recipe_name,
            picks=data_generator.random_int(1) if picks is None else picks,
        )
