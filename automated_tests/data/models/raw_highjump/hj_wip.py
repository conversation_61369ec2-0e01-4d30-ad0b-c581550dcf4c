import dataclasses

from automated_tests.data.data_generator import random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.services.database import hj


@dataclasses.dataclass
class TestHjWip(TestRecord):
    sku: TestSku
    wh_id: str
    quantity: float

    def __post_init__(self):
        self.__key = (self.__class__.__name__, id(self))

    def key(self) -> tuple:
        return self.__key

    def load(self):
        query = f"""
            INSERT INTO dbo.v_highjump_wip (id, wh_id, SKU, Quantity)
            VALUES ({id(self)}, '{self.wh_id}', '{self.sku.sku_code}', {self.quantity});
        """
        hj.apply_query(query)

    def unload(self):
        hj.apply_query(f"DELETE FROM dbo.v_highjump_wip WHERE id={id(self)};")

    @staticmethod
    def generate_hj_wip_raw(sku: TestSku, site_config: TestSiteConfig, wh_id: str = None, quantity: float = None):
        return TestHjWip(sku=sku, wh_id=wh_id or site_config.hj_name, quantity=quantity or float(random_int()))
