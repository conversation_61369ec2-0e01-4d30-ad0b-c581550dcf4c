import dataclasses
from datetime import date

from automated_tests.data.constants.date_time_constants import TOMORROW_DATE
from automated_tests.data.data_generator import random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.services.database import hj


@dataclasses.dataclass
class TestHjSnapshot(TestRecord):
    sku: TestSku
    quantity: float
    wh_id: str
    expiration_date: date
    status: str

    def __post_init__(self):
        self.__key = (self.__class__.__name__, id(self))

    def key(self) -> tuple:
        return self.__key

    def load(self):
        query = f"""
            INSERT INTO dbo.v_highjump_inv_snapshot_exp (id, wh_id, SKU, Quantity, expiration_date, status)
            VALUES ({id(self)}, '{self.wh_id}', '{self.sku.sku_code}', {self.quantity}, '{self.expiration_date}',
            '{self.status}');
        """
        hj.apply_query(query)

    def unload(self):
        hj.apply_query(f"DELETE FROM dbo.v_highjump_inv_snapshot_exp WHERE id={id(self)};")

    @staticmethod
    def generate_hj_snapshot_raw(
        sku: TestSku,
        site_config: TestSiteConfig,
        wh_id: str = None,
        quantity: float = None,
        expiration_date: date = None,
    ):
        return TestHjSnapshot(
            sku=sku,
            wh_id=wh_id or site_config.hj_name,
            quantity=quantity or float(random_int()),
            expiration_date=expiration_date or TOMORROW_DATE,
            status="A",
        )
