import dataclasses
from datetime import datetime

from automated_tests.data.constants.base_constants import HjLocations
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME
from automated_tests.data.data_generator import random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.services.database import hj


@dataclasses.dataclass
class TestHjDiscard(TestRecord):
    sku: TestSku
    wh_id: str
    tran_type: str
    quantity: int
    discard_date: datetime
    location_id: str

    def __post_init__(self):
        self.__key = (self.__class__.__name__, id(self))

    def key(self) -> tuple:
        return self.__key

    def load(self):
        query = f"""
            INSERT INTO dbo.v_highjump_discards_imt (id, item_number, wh_id, tran_type, tran_qty, end_tran_date,
            location_id
            )
            VALUES (
            {id(self)}, '{self.sku.sku_code}', '{self.wh_id}', '{self.tran_type}', {self.quantity},
            '{self.discard_date}', '{self.location_id}'
            );
        """
        hj.apply_query(query)

    def unload(self):
        hj.apply_query(f"DELETE FROM dbo.v_highjump_discards_imt WHERE id={id(self)};")

    @staticmethod
    def generate_hj_discard_raw(
        sku: TestSku,
        site_config: TestSiteConfig,
        tran_type: str,
        location_id: str = None,
        quantity: int = None,
        wh_id: str = None,
        discard_date: datetime = None,
    ):
        return TestHjDiscard(
            sku=sku,
            wh_id=wh_id or site_config.hj_name,
            quantity=quantity or random_int(),
            discard_date=discard_date or CURRENT_DATETIME,
            tran_type=tran_type,
            location_id=location_id or HjLocations.SOMEWHERE,
        )
