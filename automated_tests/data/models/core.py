import dataclasses
from abc import abstractmethod
from typing import ClassVar


@dataclasses.dataclass
class TestRecord:
    """
    Please do not use these methods directly on test entities,
    this interface is designed for TestData internal usage to load
    and unload corresponding test entities.
    """

    __test__ = False
    load_order: ClassVar[int] = 0
    unique: ClassVar[bool] = True

    @abstractmethod
    def key(self) -> tuple:
        pass

    @abstractmethod
    def load(self):
        pass

    @abstractmethod
    def unload(self):
        pass
