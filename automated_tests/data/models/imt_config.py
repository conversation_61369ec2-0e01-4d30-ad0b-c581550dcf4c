import dataclasses
from datetime import datetime
from typing import Any, ClassVar

import sqlalchemy as sqla

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.dc import TestDc
from automated_tests.data.models.market import TestMarket
from procurement.constants.hellofresh_constant import (
    APP_TIMEZONE,
    Domain,
    HybridNeedsDataSourceType,
    InventoryInputType,
    ReceiveInputType,
)
from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek, ScmWeekConfig
from procurement.data.models.inventory import DataSource
from procurement.data.models.inventory.brand import BrandConfigModel, BrandModel
from procurement.data.models.inventory.site import SiteModel
from procurement.data.models.inventory.weekly_config import WeeklyConfigModel
from procurement.data.models.social.gsheet import GsheetAdminModel, GsheetMetaModel
from procurement.managers.admin.dc_admin import DcProperty
from procurement.services.database import app_db


@dataclasses.dataclass
class TestBrand(TestRecord):
    load_order: ClassVar[int] = -1

    code: str
    name: str
    country_code: str
    market: TestMarket
    consolidated: bool = True
    scm_week_config: ScmWeekConfig = DEFAULT_WEEK_CONFIG

    def key(self) -> tuple:
        return self.__class__.__name__, self.code, self.market.code

    def load(self):
        app_db.apply_query(
            sqla.insert(BrandModel).values(
                {
                    BrandModel.id: self.code,
                    BrandModel.market: self.market.code,
                    BrandModel.name: self.name,
                    BrandModel.consolidated: self.consolidated,
                    BrandModel.week_calendar_mon_shift: self.scm_week_config.calendar_mon_shift,
                    BrandModel.week_length: self.scm_week_config.length,
                    BrandModel.country_code: self.country_code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(BrandModel).where((BrandModel.id == self.code), (BrandModel.market == self.market.code))
        )


@dataclasses.dataclass
class TestBrandConfig(TestRecord):
    brand: TestBrand
    market: TestMarket = dataclasses.field(init=False)
    week: ScmWeek = ScmWeek(2020, 1)
    enabled: bool = True

    def __post_init__(self):
        self.market = self.brand.market

    def key(self) -> tuple:
        return self.__class__.__name__, self.brand.code, self.week, self.market.code

    def load(self):
        app_db.apply_query(
            sqla.insert(BrandConfigModel).values(
                {
                    BrandConfigModel.brand: self.brand.code,
                    BrandConfigModel.week: self.week.to_number_format(),
                    BrandConfigModel.enabled: self.enabled,
                    BrandConfigModel.market: self.market.code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(BrandConfigModel).where(
                BrandConfigModel.brand == self.brand.code,
                BrandConfigModel.market == self.market.code,
                BrandConfigModel.week == self.week.to_number_format(),
            )
        )


@dataclasses.dataclass
class TestSite(TestRecord):
    load_order: ClassVar[int] = -1

    code: str
    brand: TestBrand
    market: TestMarket = dataclasses.field(init=False)
    name: str = None
    sequence_number: int = 0
    timezone: str = str(APP_TIMEZONE)
    dc_azure_role: str = None

    def __post_init__(self):
        if self.name is None:
            self.name = self.code
        self.market = self.brand.market

    def key(self) -> tuple:
        return self.__class__.__name__, self.code, self.brand.code, self.market.code

    def load(self):
        app_db.apply_query(
            sqla.insert(SiteModel).values(
                {
                    SiteModel.id: self.code,
                    SiteModel.name: self.name,
                    SiteModel.brand: self.brand.code,
                    SiteModel.market: self.market.code,
                    SiteModel.sequence_number: self.sequence_number,
                    SiteModel.timezone: self.timezone,
                    SiteModel.dc_azure_role: self.dc_azure_role,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(SiteModel).where(
                SiteModel.id == self.code,
                SiteModel.brand == self.brand.code,
                SiteModel.market == self.market.code,
            )
        )


@dataclasses.dataclass
class TestSiteConfig(TestRecord):
    site: TestSite
    brand: TestBrand
    week: ScmWeek
    ot_dc_name: str
    hj_name: str
    bob_code: str
    market: TestMarket = dataclasses.field(init=False)
    enabled: bool = True
    is_3pl: bool = False
    is_hj_autostore: bool = False
    is_fulfilment_only: bool = False
    multi_brand_dc: str = None
    receiving_type: ReceiveInputType = ReceiveInputType.HIGH_JUMP
    inventory_type: InventoryInputType = InventoryInputType.HJ
    hybrid_need_source: HybridNeedsDataSourceType = HybridNeedsDataSourceType.GSHEET
    is_wip_consumption: bool = False
    supplier_names: list[str] = dataclasses.field(default_factory=list)
    supplier_codes: list[str] = dataclasses.field(default_factory=list)
    source: DataSource = DataSource.APP
    consolidated_site: str | None = None
    ope_bob_code: str | None = None
    properties: dict[str, Any] = dataclasses.field(default_factory=dict)

    dc: TestDc = dataclasses.field(init=False)

    def __post_init__(self):
        self.market = self.brand.market
        self.bob_code = self.site.code + self.brand.code[0]
        self.dc = TestDc(
            bob_code=self.bob_code,
            name=self.ot_dc_name,
            enabled=self.enabled,
            is_third_party=self.is_3pl,
            market=self.market,
        )

    def key(self) -> tuple:
        return (
            self.__class__.__name__,
            self.site.code,
            self.brand.code,
            self.week,
            self.market.code,
        )

    def load(self):
        props = dict(self.properties)
        props.update(
            {
                DcProperty.ORDERING_TOOL_NAME.name: self.ot_dc_name,
                DcProperty.HIGH_JUMP_NAME.name: self.hj_name,
                DcProperty.IS_3PL.name: self.is_3pl,
                DcProperty.IS_HJ_AUTOSTORE.name: self.is_hj_autostore,
                DcProperty.RECEIVING_TYPE.name: self.receiving_type,
                DcProperty.INVENTORY_TYPE.name: self.inventory_type.name,
                DcProperty.SOURCE.name: self.source,
                DcProperty.IS_FULFILMENT_ONLY.name: self.is_fulfilment_only,
                DcProperty.MULTI_BRAND_DC.name: self.multi_brand_dc,
                DcProperty.BOB_CODE.name: self.bob_code,
                DcProperty.HYBRID_NEEDS_DATA_SOURCE.name: self.hybrid_need_source,
                DcProperty.IS_WIP_CONSUMPTION_ENABLED.name: self.is_wip_consumption,
                DcProperty.SUPPLIER_CODES.name: self.supplier_codes,
                DcProperty.CONSOLIDATED_SITE_CODE.name: self.consolidated_site,
                DcProperty.OPE_BOB_CODE.name: self.ope_bob_code,
            }
        )
        app_db.apply_query(
            sqla.insert(WeeklyConfigModel).values(
                {
                    WeeklyConfigModel.site: self.site.code,
                    WeeklyConfigModel.brand: self.brand.code,
                    WeeklyConfigModel.market: self.market.code,
                    WeeklyConfigModel.week: int(self.week),
                    WeeklyConfigModel.enabled: self.enabled,
                    WeeklyConfigModel.properties: props,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(WeeklyConfigModel).where(
                WeeklyConfigModel.site == self.site.code,
                WeeklyConfigModel.brand == self.brand.code,
                WeeklyConfigModel.week == int(self.week),
                WeeklyConfigModel.market == self.market.code,
            )
        )


@dataclasses.dataclass
class TestGsheetConfig(TestRecord):
    doc_code: str
    brand: TestBrand
    project: Domain
    gsheet_id: str
    week: ScmWeek = None
    market: TestMarket = dataclasses.field(init=False)
    created_tmst: datetime = None
    updated_tmst: datetime = None

    __key: tuple = None
    __id: int = None

    def __post_init__(self):
        self.market = self.brand.market

        self.created_tmst = self.created_tmst or datetime.now()
        self.updated_tmst = self.updated_tmst or datetime.now()

        self.__key = (self.__class__.__name__, self.doc_code, self.week)

    def key(self) -> tuple:
        return self.__key

    def load(self):
        doc_meta_id = app_db.select_scalar(
            sqla.select(GsheetMetaModel.id).where(
                GsheetMetaModel.doc_code == self.doc_code,
                GsheetMetaModel.brand == self.brand.code,
                GsheetMetaModel.project == self.project,
                GsheetMetaModel.market == self.market.code,
            )
        )
        if not doc_meta_id:
            raise ValueError(
                f"No doc code '{self.doc_code}' found for brand " f"'{self.brand.code}' and project '{self.project}'"
            )
        self.__id = app_db.apply_query(
            sqla.insert(GsheetAdminModel)
            .values(
                {
                    GsheetAdminModel.meta_id: doc_meta_id,
                    GsheetAdminModel.scm_week: str(self.week),
                    GsheetAdminModel.gsheet_id: self.gsheet_id,
                    GsheetAdminModel.created_tmst: self.created_tmst,
                    GsheetAdminModel.updated_tmst: self.updated_tmst,
                }
            )
            .returning(GsheetAdminModel.id)
        )[0].id

    def unload(self):
        if self.__id is None:
            raise ValueError("Trying to delete test record that is not loaded")
        app_db.apply_query(sqla.delete(GsheetAdminModel).where(GsheetAdminModel.id == self.__id))

    @staticmethod
    def generate_gsheet_configs(
        gsheet_code_id_data: dict,
        brand: TestBrand,
        project: Domain,
        week: ScmWeek = CURRENT_WEEK,
    ):
        gsheet_configs = []
        for doc_code, gsheet_id in gsheet_code_id_data.items():
            gsheet_config = TestGsheetConfig(
                doc_code=doc_code,
                brand=brand,
                project=project,
                gsheet_id=gsheet_id,
                week=week,
            )
            gsheet_configs.append(gsheet_config)

        return gsheet_configs
