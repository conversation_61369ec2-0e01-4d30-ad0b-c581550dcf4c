import dataclasses
import random
from collections.abc import Iterable
from datetime import date, datetime
from decimal import Decimal

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_DATETIME
from automated_tests.data.data_generator import random_int
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.forecasts import TestOscar
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.warehouse import TestWarehouse
from automated_tests.utils.purchase_order_utils import PurchaseOrderUtils
from procurement.constants.hellofresh_constant import InventoryInputType, InventoryState
from procurement.core import utils
from procurement.data.models.inventory.inventory import UnifiedInventoryModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestPimtUnifiedInventory(TestRecord):
    wh_code: str
    po: TestPurchaseOrder
    sku: TestSku
    lot: str
    location_id: str
    # either case quantity or unit quantity should be set
    snapshot_timestamp: datetime
    case_quantity: int = None
    unit_quantity: int = None
    case_size: int = None
    expiration_date: date = None
    inventory_status: InventoryState = InventoryState.AVAILABLE
    inventory_type: InventoryInputType = InventoryInputType.GSHEET
    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.po.po_number,
            self.sku.sku_code,
            self.lot,
            self.inventory_status,
            self.inventory_type,
            self.expiration_date,
            self.snapshot_timestamp,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(UnifiedInventoryModel)
            .values(
                {
                    UnifiedInventoryModel.sku_code: self.sku.sku_code,
                    UnifiedInventoryModel.po_number: self.po.po_number,
                    UnifiedInventoryModel.order_number: self.po.order_number,
                    UnifiedInventoryModel.case_quantity: self.case_quantity,
                    UnifiedInventoryModel.unit_quantity: self.unit_quantity,
                    UnifiedInventoryModel.case_size: self.case_size,
                    UnifiedInventoryModel.lot_code: self.lot,
                    UnifiedInventoryModel.wh_code: self.wh_code,
                    UnifiedInventoryModel.expiration_date: self.expiration_date,
                    UnifiedInventoryModel.inventory_status: self.inventory_status,
                    UnifiedInventoryModel.inventory_type: self.inventory_type,
                    UnifiedInventoryModel.snapshot_timestamp: self.snapshot_timestamp or datetime.now(),
                    UnifiedInventoryModel.location_id: self.location_id,
                }
            )
            .returning(1)
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(UnifiedInventoryModel).where(
                UnifiedInventoryModel.wh_code == self.wh_code,
                UnifiedInventoryModel.order_number == utils.get_order_number_from_po_number(self.po.po_number),
                UnifiedInventoryModel.sku_code == self.sku.sku_code,
                UnifiedInventoryModel.lot_code == self.lot,
                UnifiedInventoryModel.inventory_status == self.inventory_status,
                UnifiedInventoryModel.expiration_date == self.expiration_date,
                UnifiedInventoryModel.inventory_type == self.inventory_type,
            )
        )

    @staticmethod
    def generate_expiring_inventories_by_po(
        pos: Iterable[TestPurchaseOrder], warehouse: TestWarehouse = None
    ) -> list["TestPimtUnifiedInventory"]:
        inventories = []
        for po in pos:
            inventories.append(
                TestPimtUnifiedInventory.generate_inventory_by_po(
                    po=po, warehouse=warehouse, expiration_date=po.week.get_first_day()
                )
            )
        return inventories

    @staticmethod
    def generate_inventories_by_po(
        *pos: TestPurchaseOrder,
        expiration_date: datetime.date = None,
        inventory_type: InventoryInputType = None,
        inventory_status: InventoryState = InventoryState.AVAILABLE,
        warehouse: TestWarehouse = None,
        lot: str = None,
        case_quantity: int = None,
    ) -> list["TestPimtUnifiedInventory"]:
        inventories = []
        for po in pos:
            inventories.append(
                TestPimtUnifiedInventory.generate_inventory_by_po(
                    po=po,
                    lot=lot,
                    case_quantity=case_quantity,
                    warehouse=warehouse,
                    inventory_type=inventory_type,
                    expiration_date=expiration_date,
                    inventory_status=inventory_status,
                )
            )
        return inventories

    @staticmethod
    def generate_inventory_by_po(
        po: TestPurchaseOrder,
        lot: str = None,
        case_quantity: int = None,
        warehouse: TestWarehouse = None,
        expiration_date: date = None,
        inventory_type: InventoryInputType = None,
        inventory_status: InventoryState = InventoryState.AVAILABLE,
        snapshot_timestamp: datetime = CURRENT_DATETIME,
        sku: TestSku = None,
        forecasts_not_to_exceed: Iterable[TestOscar] = None,
        unit_quantity: int = None,
        case_size: int = None,
        location_id: str = None,
    ):
        warehouse = warehouse if warehouse is not None else PurchaseOrderUtils.get_warehouse_by_purchase_order(po)
        # to not exceed total forecast with available inventory,
        # for example to not affect reorder trigger on replenishment
        if forecasts_not_to_exceed:
            if case_quantity:
                raise ValueError("You can't specify both forecast limit and exact quantity")
            total_forecast = sum(f.value for f in forecasts_not_to_exceed)
            case_quantity = round(total_forecast * Decimal(str(random.uniform(0.5, 0.9))) / po.first_line().case_size)
        return TestPimtUnifiedInventory(
            po=po,
            sku=sku if sku else po.first_line().sku,
            lot=lot or str(random_int(6)),
            case_quantity=case_quantity if case_quantity is not None else random.randint(10, 300),
            unit_quantity=unit_quantity,
            case_size=case_size,
            wh_code=warehouse.code,
            expiration_date=expiration_date,
            inventory_status=inventory_status,
            inventory_type=inventory_type or warehouse.inventory_type,
            snapshot_timestamp=snapshot_timestamp,
            location_id=location_id or data_generator.generate_string(),
        )

    @staticmethod
    def generate_inventory_by_site_config(
        po: TestPurchaseOrder,
        site_config: TestSiteConfig,
        lot: str = None,
        case_quantity: int = None,
        expiration_date: date = None,
        inventory_type: InventoryInputType = None,
        inventory_status: InventoryState = InventoryState.AVAILABLE,
        snapshot_timestamp: datetime = CURRENT_DATETIME,
        sku: TestSku = None,
        forecasts_not_to_exceed: Iterable[TestOscar] = None,
        unit_quantity: int = None,
        case_size: int = None,
        location_id: str = None,
    ):
        # to not exceed total forecast with available inventory,
        # for example to not affect reorder trigger on replenishment
        if forecasts_not_to_exceed:
            if case_quantity:
                raise ValueError("You can't specify both forecast limit and exact quantity")
            total_forecast = sum(f.value for f in forecasts_not_to_exceed)
            case_quantity = round(total_forecast * Decimal(str(random.uniform(0.5, 0.9))) / po.first_line().case_size)
        return TestPimtUnifiedInventory(
            po=po,
            sku=sku if sku else po.first_line().sku,
            lot=lot or str(random_int(6)),
            case_quantity=case_quantity if case_quantity is not None else random.randint(10, 300),
            unit_quantity=unit_quantity,
            case_size=case_size,
            wh_code=site_config.bob_code,
            expiration_date=expiration_date,
            inventory_status=inventory_status,
            inventory_type=inventory_type or site_config.inventory_type,
            snapshot_timestamp=snapshot_timestamp,
            location_id=location_id or data_generator.generate_string(),
        )

    @staticmethod
    def generate_inventories_by_po_with_list_of_states(
        po: TestPurchaseOrder,
        sku: TestSku = None,
        lot: int = None,
        case_quantity: int = None,
        warehouse: TestWarehouse = None,
        expiration_date: date = None,
        inventory_statuses: list[InventoryState] = None,
        inventory_type: InventoryInputType = InventoryInputType.E2OPEN,
        snapshot_timestamp: datetime = CURRENT_DATETIME,
        location_id: str = None,
    ):
        if not inventory_statuses:
            inventory_statuses = InventoryState
        inventories = []
        for state in inventory_statuses:
            inventories.append(
                TestPimtUnifiedInventory(
                    po=po,
                    sku=sku or po.first_line().sku,
                    lot=lot or str(random_int(6)),
                    case_quantity=case_quantity or random.randint(10, 300),
                    wh_code=warehouse.code,
                    expiration_date=expiration_date,
                    inventory_status=state,
                    inventory_type=inventory_type,
                    snapshot_timestamp=snapshot_timestamp,
                    location_id=location_id or data_generator.generate_string(),
                )
            )
        return inventories

    @staticmethod
    def generate_hj_inventory(
        warehouse: TestWarehouse,
        po: TestPurchaseOrder,
        sku: TestSku,
        unit_quantity: int = None,
        lot: str = None,
        expiration_date: date = CURRENT_DATE,
        inventory_status: InventoryState = InventoryState.AVAILABLE,
        inventory_type: InventoryInputType = InventoryInputType.HJ,
        snapshot_timestamp: datetime = CURRENT_DATETIME,
        location_id: str = None,
    ) -> "TestPimtUnifiedInventory":
        return TestPimtUnifiedInventory(
            wh_code=warehouse.code,
            sku=sku,
            lot=lot or "LPAB" + str(random_int(5)),
            unit_quantity=unit_quantity or random_int(6),
            po=po,
            expiration_date=expiration_date,
            inventory_status=inventory_status,
            inventory_type=inventory_type,
            snapshot_timestamp=snapshot_timestamp,
            location_id=location_id or data_generator.generate_string(),
        )
