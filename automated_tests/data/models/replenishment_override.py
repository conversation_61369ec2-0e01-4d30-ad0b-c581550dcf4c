import dataclasses
from datetime import datetime
from decimal import Decimal

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.warehouse import TestWarehouse
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.data.models.pimt.replenishment_override import ReplenishmentOverrideModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestReplenishmentOverride(TestRecord):
    region: str
    sku: TestSku
    value: Decimal
    updated_by: str
    last_updated: datetime = dataclasses.field(default_factory=datetime.now)
    market: str = MARKET_US

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.market,
            self.sku.sku_code,
            self.region,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(ReplenishmentOverrideModel).values(
                {
                    ReplenishmentOverrideModel.market: self.market,
                    ReplenishmentOverrideModel.region: self.region,
                    ReplenishmentOverrideModel.sku_code: self.sku.sku_code,
                    ReplenishmentOverrideModel.value: self.value,
                    ReplenishmentOverrideModel.updated_by: self.updated_by,
                    ReplenishmentOverrideModel.last_updated: self.last_updated,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(ReplenishmentOverrideModel).where(
                ReplenishmentOverrideModel.region == self.region,
                ReplenishmentOverrideModel.sku_code == self.sku.sku_code,
                ReplenishmentOverrideModel.market == self.market,
            )
        )

    @staticmethod
    def generate_replenishment_override(
        sku: TestSku,
        warehouse: TestWarehouse,
        last_updated: datetime = None,
        value: int = None,
        updated_by: str = None,
    ):
        return TestReplenishmentOverride(
            market=warehouse.market.code,
            region=warehouse.region,
            sku=sku,
            value=value or data_generator.random_int(),
            updated_by=updated_by or data_generator.generate_string(),
            last_updated=last_updated or CURRENT_DATETIME,
        )
