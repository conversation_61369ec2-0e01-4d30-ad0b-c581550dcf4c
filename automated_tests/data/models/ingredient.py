import dataclasses
import random
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import Sites
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.core.dates import ScmWeek
from procurement.data.models.inventory.ingredient import IngredientModel
from procurement.data.models.inventory.mealkit import MealkitIngredientModel, MealkitModel
from procurement.data.models.inventory.remake_tool import RemakeToolModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestIngredient(TestRecord):
    sku: TestSku
    brand: TestBrand
    pack_size_amount: float = None
    pack_size_unit: str = None
    storage_location: str = None
    allergens: str = None
    is_receipt_bp_drop: bool = False
    is_type_media: bool = False

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSku.load_order, TestBrand.load_order) + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.sku.sku_code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(IngredientModel).values(
                {
                    IngredientModel.sku_code: self.sku.sku_code,
                    IngredientModel.pack_size_amount: self.pack_size_amount,
                    IngredientModel.pack_size_unit: self.pack_size_unit,
                    IngredientModel.storage_location: self.storage_location,
                    IngredientModel.brand: self.brand.code,
                    IngredientModel.allergens: self.allergens,
                    IngredientModel.is_receipt_bp_drop: self.is_receipt_bp_drop,
                    IngredientModel.is_type_media: self.is_type_media,
                }
            )
        )

    def unload(self):
        app_db.apply_query(sqla.delete(IngredientModel).where(IngredientModel.sku_code == self.sku.sku_code))

    @staticmethod
    def generate_ingredients(
        sku: TestSku,
        brand: TestBrand,
        pack_size_amount: float = None,
        pack_size_unit: str = None,
        storage_location: str = None,
        allergens: str = None,
        is_receipt_bp_drop: bool = False,
        is_type_media: bool = False,
    ) -> "TestIngredient":
        return TestIngredient(
            sku=sku,
            brand=brand,
            pack_size_amount=pack_size_amount or float(data_generator.random_int()),
            pack_size_unit=pack_size_unit or str(data_generator.random_int()),
            storage_location=storage_location,
            allergens=allergens,
            is_receipt_bp_drop=is_receipt_bp_drop,
            is_type_media=is_type_media,
        )


@dataclasses.dataclass
class TestMealkit(TestRecord):
    mealkit_id: int
    code: str
    slot: str
    meal_name: str
    week: ScmWeek
    brand: TestBrand
    sub_recipe: str

    __key: tuple = None
    load_order: ClassVar[int] = TestBrand.load_order + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.brand.code,
            self.code,
            self.slot,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        values = {
            MealkitModel.brand: self.brand.code,
            MealkitModel.scm_week: str(self.week),
            MealkitModel.code: self.code,
            MealkitModel.slot: self.slot,
            MealkitModel.meal_name: self.meal_name,
            MealkitModel.sub_recipe: self.sub_recipe,
        }
        if self.mealkit_id:
            values[MealkitModel.id] = self.mealkit_id
        self.mealkit_id = app_db.apply_query(sqla.insert(MealkitModel).values(values).returning(MealkitModel.id))[0].id

    def unload(self):
        app_db.apply_query(
            sqla.delete(MealkitModel).where(
                MealkitModel.brand == self.brand.code,
                MealkitModel.scm_week == str(self.week),
                MealkitModel.code == self.code,
                MealkitModel.slot == self.slot,
            )
        )

    @staticmethod
    def generate_mealkit_by_brand(
        brand: TestBrand, week: ScmWeek = CURRENT_WEEK, slot: int = None, sub_recipe: str = None, mealkit_id: int = None
    ) -> "TestMealkit":
        return TestMealkit(
            code=str(data_generator.random_int(5)),
            slot=str(slot if slot else data_generator.random_int(1)),
            meal_name=data_generator.generate_string(),
            week=week,
            brand=brand,
            sub_recipe=sub_recipe or data_generator.generate_string(),
            mealkit_id=mealkit_id,
        )


@dataclasses.dataclass
class TestMealkitIngredient(TestRecord):
    mealkit: TestMealkit
    ingredient: TestIngredient
    picks_2p: int
    picks_3p: int
    picks_4p: int
    sites: list[TestSite]
    is_in_kitbox: bool = False
    picks_6p: int = 0
    weight_amount: float = None
    weight_unit: str = None

    __key: tuple = None
    load_order: ClassVar[int] = max(TestMealkit.load_order, TestIngredient.load_order, TestSite.load_order) + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.ingredient.sku.sku_code,
            self.mealkit.brand.code,
            self.mealkit.mealkit_id,
            *[site.name for site in self.sites],
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(MealkitIngredientModel).values(
                {
                    MealkitIngredientModel.mealkit_id: self.mealkit.mealkit_id,
                    MealkitIngredientModel.sku_code: self.ingredient.sku.sku_code,
                    MealkitIngredientModel.picks_2p: self.picks_2p,
                    MealkitIngredientModel.picks_3p: self.picks_3p,
                    MealkitIngredientModel.picks_4p: self.picks_4p,
                    MealkitIngredientModel.picks_6p: self.picks_6p,
                    MealkitIngredientModel.is_in_kitbox: self.is_in_kitbox,
                    MealkitIngredientModel.dc_list: self.record_sites,
                    MealkitIngredientModel.weight_amount: self.weight_amount,
                    MealkitIngredientModel.weight_unit: self.weight_unit,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(MealkitIngredientModel).where(
                MealkitIngredientModel.mealkit_id == self.mealkit.mealkit_id,
                MealkitIngredientModel.sku_code == self.ingredient.sku.sku_code,
                MealkitIngredientModel.dc_list == self.record_sites,
            )
        )

    @property
    def record_sites(self):
        return [site.code for site in self.sites]

    @staticmethod
    def generate_mealkit_ing_by_mealkit_and_ingredient(
        mealkit: TestMealkit,
        ingredient: TestIngredient,
        sites: list[TestSite] = None,
        is_in_kitbox: bool = False,
        weight_amount: float = None,
        weight_unit: str = None,
        picks_3p: int = None,
    ) -> "TestMealkitIngredient":
        return TestMealkitIngredient(
            mealkit=mealkit,
            ingredient=ingredient,
            picks_2p=data_generator.random_int(),
            picks_3p=picks_3p if picks_3p else 0,
            picks_4p=data_generator.random_int(),
            picks_6p=data_generator.random_int(),
            sites=[*sites] if sites is not None else [*Sites.get_all_values()],
            is_in_kitbox=is_in_kitbox,
            weight_amount=weight_amount or float(data_generator.random_int()),
            weight_unit=weight_unit or str(random.choice(list(UnitOfMeasure))),
        )


@dataclasses.dataclass
class TestRemakeTool(TestRecord):
    meal: TestMealkit
    week: ScmWeek
    site: TestSite
    picks_2p: int = None
    picks_4p: int = None
    picks_6p: int = None

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSite.load_order, TestMealkit.load_order) + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.site.code,
            self.meal.slot,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(RemakeToolModel).values(
                {
                    RemakeToolModel.meal: self.meal.slot,
                    RemakeToolModel.picks_2p: self.picks_2p,
                    RemakeToolModel.picks_4p: self.picks_4p,
                    RemakeToolModel.picks_6p: self.picks_6p,
                    RemakeToolModel.week: str(self.week),
                    RemakeToolModel.dc: self.site.code,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(RemakeToolModel).where(
                RemakeToolModel.week == str(self.week),
                RemakeToolModel.dc == self.site.code,
                RemakeToolModel.meal == self.meal.slot,
            )
        )

    @staticmethod
    def generate_remake_tool(
        mealkit: TestMealkit, site_config: TestSiteConfig, week: ScmWeek = CURRENT_WEEK
    ) -> "TestRemakeTool":
        return TestRemakeTool(
            meal=mealkit,
            picks_2p=data_generator.random_int(2),
            picks_4p=data_generator.random_int(2),
            picks_6p=data_generator.random_int(2),
            week=week,
            site=site_config.site,
        )
