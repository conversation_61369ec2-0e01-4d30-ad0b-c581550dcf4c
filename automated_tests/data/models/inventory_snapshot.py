import dataclasses
from datetime import date, datetime

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_DATETIME
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.sku import TestSku
from procurement.constants.hellofresh_constant import InventoryInputType, WmslLocationType
from procurement.constants.protobuf import InventoryLocationType, InventoryState
from procurement.data.models.inventory.inventory_snapshot import InventorySnapshotModel
from procurement.services.database import app_db


@dataclasses.dataclass
class TestInventorySnapshot(TestRecord):
    wh_code: str
    sku: TestSku
    snapshot_timestamp: datetime
    expiration_date: date
    location_id: str
    unit_quantity: int
    location_type: InventoryLocationType = InventoryLocationType.UNSPECIFIED
    state: InventoryState = InventoryState.ACTIVE
    inventory_type: InventoryInputType = InventoryInputType.WMSL
    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.wh_code,
            self.sku.sku_code,
            self.state,
            self.inventory_type,
            self.expiration_date,
            self.snapshot_timestamp,
            self.location_id,
            self.location_type,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(InventorySnapshotModel).values(
                {
                    InventorySnapshotModel.sku_code: self.sku.sku_code,
                    InventorySnapshotModel.unit_quantity: self.unit_quantity,
                    InventorySnapshotModel.location_type: self.location_type,
                    InventorySnapshotModel.location_id: self.location_id,
                    InventorySnapshotModel.wh_code: self.wh_code,
                    InventorySnapshotModel.expiration_date: self.expiration_date,
                    InventorySnapshotModel.state: self.state,
                    InventorySnapshotModel.inventory_type: self.inventory_type,
                    InventorySnapshotModel.snapshot_ts: self.snapshot_timestamp,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(InventorySnapshotModel).where(
                InventorySnapshotModel.sku_code == self.sku.sku_code,
                InventorySnapshotModel.wh_code == self.wh_code,
                InventorySnapshotModel.state == str(self.state),
                InventorySnapshotModel.expiration_date == self.expiration_date,
                InventorySnapshotModel.inventory_type == self.inventory_type,
                InventorySnapshotModel.snapshot_ts == self.snapshot_timestamp,
                InventorySnapshotModel.location_id == self.location_id,
                InventorySnapshotModel.location_type == str(self.location_type),
            )
        )

    @staticmethod
    def generate_inventory_snapshot(
        wh_code: str,
        sku: TestSku,
        snapshot_timestamp: datetime = None,
        expiration_date: date = None,
        location_type: InventoryLocationType | WmslLocationType = InventoryLocationType.UNSPECIFIED,
        location_id: str = None,
        state: InventoryState = InventoryState.ACTIVE.name,
        inventory_type: InventoryInputType = InventoryInputType.WMSL,
        unit_quantity: int = None,
    ):
        return TestInventorySnapshot(
            sku=sku,
            wh_code=wh_code,
            snapshot_timestamp=snapshot_timestamp or CURRENT_DATETIME,
            expiration_date=expiration_date or CURRENT_DATE,
            location_type=location_type,
            state=state,
            location_id=location_id or data_generator.generate_string(),
            inventory_type=inventory_type,
            unit_quantity=unit_quantity or data_generator.random_int(),
        )
