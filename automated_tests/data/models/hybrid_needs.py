import dataclasses
from datetime import date, datetime, timedelta
from random import choice
from typing import ClassVar

import sqlalchemy as sqla

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import FactorBrand
from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_DATETIME,
    CURRENT_WEEK,
    CURRENT_WEEK_FACTOR,
    CURRENT_WEEK_STR,
    FACTOR_WEEK_CONFIG,
    FACTOR_WEEK_LENGTH,
    PRODUCTION_WEEK_LENGTH,
)
from automated_tests.data.models.core import TestRecord
from automated_tests.data.models.imt_config import TestBrand, TestSite, TestSiteConfig
from automated_tests.data.models.sku import TestSku
from procurement.constants.hellofresh_constant import ProductionPlanType
from procurement.core.dates import DEFAULT_WEEK_CONFIG, ScmWeek
from procurement.data.models.inventory.hybrid_needs import (
    DeliveryDateNeedsModel,
    HybridNeedsIngredientsModel,
    HybridNeedsIngredientsShiftLevelModel,
    HybridNeedsIngredientsStatusModel,
    HybridNeedsLiveUsageModel,
)
from procurement.services.database import app_db


@dataclasses.dataclass
class TestHybridNeeds(TestRecord):
    sku: TestSku
    site: TestSite
    week: ScmWeek
    date: datetime
    brand: TestBrand
    quantity: int

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSku.load_order, TestSite.load_order, TestBrand.load_order) + 1

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.brand.code,
            self.site.code,
            self.date,
            self.sku.sku_code,
            self.week,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(HybridNeedsIngredientsModel).values(
                {
                    HybridNeedsIngredientsModel.dc: self.site.code,
                    HybridNeedsIngredientsModel.brand: self.brand.code,
                    HybridNeedsIngredientsModel.scm_week: str(self.week),
                    HybridNeedsIngredientsModel.sku_code: self.sku.sku_code,
                    HybridNeedsIngredientsModel.date: self.date,
                    HybridNeedsIngredientsModel.value: self.quantity,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(HybridNeedsIngredientsModel).where(
                HybridNeedsIngredientsModel.brand == self.brand.code,
                HybridNeedsIngredientsModel.dc == self.site.code,
                HybridNeedsIngredientsModel.sku_code == self.sku.sku_code,
                HybridNeedsIngredientsModel.scm_week == str(self.week),
                HybridNeedsIngredientsModel.date == self.date,
            )
        )

    @staticmethod
    def generate_hybrid_needs(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = None,
        day: datetime | date = None,
        qty: int = None,
    ) -> "TestHybridNeeds":
        if not week and day:
            week = ScmWeek.from_date(day)
        elif week == CURRENT_WEEK and not day:
            day = CURRENT_DATETIME
        elif week and not day:
            day = choice(week.week_days())
        elif not week and not day:
            week = CURRENT_WEEK
            day = CURRENT_DATETIME
        return TestHybridNeeds(
            sku=sku,
            site=site_config.site,
            week=week,
            date=day,
            brand=site_config.brand,
            quantity=qty if qty is not None else data_generator.random_int(),
        )

    @staticmethod
    def generate_hybrid_needs_for_several_days(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = CURRENT_WEEK,
        days_qty: int = 9,
    ) -> ["TestHybridNeeds"]:
        week_config = FACTOR_WEEK_CONFIG if site_config.brand == FactorBrand.FJ.value else DEFAULT_WEEK_CONFIG
        date_list = [week.get_first_day(week_config) + timedelta(days=x) for x in range(days_qty)]
        hybrid_needs = [
            TestHybridNeeds.generate_hybrid_needs(sku=sku, site_config=site_config, week=week, day=day)
            for day in date_list
        ]

        return hybrid_needs


@dataclasses.dataclass
class TestHybridNeedsStatus(TestRecord):
    week: ScmWeek
    site: TestSite
    brand: TestBrand
    day: date
    status: str

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.week,
            self.day,
            self.site.code,
            self.brand.code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(HybridNeedsIngredientsStatusModel).values(
                {
                    HybridNeedsIngredientsStatusModel.scm_week: str(self.week),
                    HybridNeedsIngredientsStatusModel.day: self.day,
                    HybridNeedsIngredientsStatusModel.site: self.site.code,
                    HybridNeedsIngredientsStatusModel.brand: self.brand.code,
                    HybridNeedsIngredientsStatusModel.status: self.status,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(HybridNeedsIngredientsStatusModel).where(
                HybridNeedsIngredientsStatusModel.scm_week == str(self.week),
                HybridNeedsIngredientsStatusModel.day == self.day,
                HybridNeedsIngredientsStatusModel.site == self.site.code,
                HybridNeedsIngredientsStatusModel.brand == self.brand.code,
            )
        )

    @staticmethod
    def generate_hybrid_needs_status(
        site_config: TestSiteConfig, week: ScmWeek = None, day: date = None, status=None
    ) -> "TestHybridNeedsStatus":
        return TestHybridNeedsStatus(
            week=week or CURRENT_WEEK,
            day=day or CURRENT_DATE,
            site=site_config.site,
            brand=site_config.brand,
            status=(
                status if status else ProductionPlanType.ACTUAL if day <= CURRENT_DATE else ProductionPlanType.PROJECTED
            ),
        )

    @staticmethod
    def generate_hybrid_needs_statuses(
        site_config: TestSiteConfig,
        week: ScmWeek = None,
        days_qty: int = None,
    ) -> ["TestHybridNeedsStatus"]:
        week_config = site_config.brand.scm_week_config
        if not week:
            week = CURRENT_WEEK if week_config == DEFAULT_WEEK_CONFIG else CURRENT_WEEK_FACTOR
        if not days_qty:
            days_qty = week_config.length
        date_list = [week.get_first_day(week_config) + timedelta(days=x) for x in range(days_qty)]
        hybrid_needs = [
            TestHybridNeedsStatus.generate_hybrid_needs_status(site_config=site_config, week=week, day=date)
            for date in date_list
        ]
        return hybrid_needs


@dataclasses.dataclass
class TestHybridNeedsShiftLevel(TestRecord):
    sku: TestSku
    site: TestSite
    week: ScmWeek
    brand: TestBrand
    day: date
    value_day: int
    value_night: int
    value_third: int

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.day,
            self.week,
            self.site.code,
            self.sku.sku_code,
            self.brand.code,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(HybridNeedsIngredientsShiftLevelModel).values(
                {
                    HybridNeedsIngredientsShiftLevelModel.date: self.day,
                    HybridNeedsIngredientsShiftLevelModel.scm_week: str(self.week),
                    HybridNeedsIngredientsShiftLevelModel.dc: self.site.code,
                    HybridNeedsIngredientsShiftLevelModel.sku_code: self.sku.sku_code,
                    HybridNeedsIngredientsShiftLevelModel.brand: self.brand.code,
                    HybridNeedsIngredientsShiftLevelModel.value_day: self.value_day,
                    HybridNeedsIngredientsShiftLevelModel.value_night: self.value_night,
                    HybridNeedsIngredientsShiftLevelModel.value_third: self.value_third,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(HybridNeedsIngredientsShiftLevelModel).where(
                HybridNeedsIngredientsShiftLevelModel.date == self.day,
                HybridNeedsIngredientsShiftLevelModel.scm_week == str(self.week),
                HybridNeedsIngredientsShiftLevelModel.dc == self.site.code,
                HybridNeedsIngredientsShiftLevelModel.sku_code == self.sku.sku_code,
                HybridNeedsIngredientsShiftLevelModel.brand == self.brand.code,
            )
        )

    @staticmethod
    def generate_hybrid_need_shift_level(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = None,
        day: date = None,
        value_day: int = None,
        value_night: int = None,
        value_third: int = None,
    ) -> "TestHybridNeedsShiftLevel":
        return TestHybridNeedsShiftLevel(
            sku=sku,
            site=site_config.site,
            brand=site_config.brand,
            week=week or CURRENT_WEEK,
            day=day or CURRENT_DATE,
            value_day=value_day or data_generator.random_int(),
            value_night=value_night or data_generator.random_int(),
            value_third=value_third or data_generator.random_int(),
        )

    @staticmethod
    def generate_hybrid_needs_shift_levels_for_several_days(
        sku: TestSku,
        site_config: TestSiteConfig,
        days_qty: int = 9,
        start_date: datetime = CURRENT_WEEK.get_first_day(),
    ) -> list["TestHybridNeedsShiftLevel"]:
        date_list = [start_date + timedelta(days=x) for x in range(days_qty)]
        hybrid_needs = [
            TestHybridNeedsShiftLevel.generate_hybrid_need_shift_level(sku=sku, site_config=site_config, day=day)
            for day in date_list
        ]
        return hybrid_needs


@dataclasses.dataclass
class TestDeliveryDateNeeds(TestRecord):
    sku: TestSku
    site: TestSite
    week: ScmWeek
    day: date | datetime
    brand: TestBrand
    quantity: int

    __key: tuple = None
    load_order: ClassVar[int] = max(TestSku.load_order, TestSite.load_order, TestBrand.load_order) + 1

    def __post_init__(self):
        self.day = self.day.date() if isinstance(self.day, datetime) else self.day
        self.__key = (
            self.__class__.__name__,
            self.brand.code,
            self.site.code,
            self.day,
            self.sku.sku_code,
            self.week,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(DeliveryDateNeedsModel).values(
                {
                    DeliveryDateNeedsModel.dc: self.site.code,
                    DeliveryDateNeedsModel.brand: self.brand.code,
                    DeliveryDateNeedsModel.scm_week: str(self.week),
                    DeliveryDateNeedsModel.sku_code: self.sku.sku_code,
                    DeliveryDateNeedsModel.date: self.day,
                    DeliveryDateNeedsModel.value: self.quantity,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(DeliveryDateNeedsModel).where(
                DeliveryDateNeedsModel.brand == self.brand.code,
                DeliveryDateNeedsModel.dc == self.site.code,
                DeliveryDateNeedsModel.sku_code == self.sku.sku_code,
                DeliveryDateNeedsModel.scm_week == str(self.week),
                DeliveryDateNeedsModel.date == self.day,
            )
        )

    @staticmethod
    def generate_delivery_date_need(
        site_config: TestSiteConfig,
        sku: TestSku,
        quantity: int = None,
        week: ScmWeek = None,
        day: datetime | date = None,
    ) -> "TestDeliveryDateNeeds":
        return TestDeliveryDateNeeds(
            brand=site_config.brand,
            site=site_config.site,
            week=week or CURRENT_WEEK_STR,
            day=day or CURRENT_DATE,
            sku=sku,
            quantity=quantity or data_generator.random_int(),
        )

    @staticmethod
    def generate_delivery_date_needs_for_several_days(
        sku: TestSku,
        site_config: TestSiteConfig,
        week: ScmWeek = CURRENT_WEEK,
    ) -> ["TestDeliveryDateNeeds"]:
        days_qty = FACTOR_WEEK_LENGTH if site_config.brand == FactorBrand.FJ.value else PRODUCTION_WEEK_LENGTH
        week_config = FACTOR_WEEK_CONFIG if site_config.brand == FactorBrand.FJ.value else DEFAULT_WEEK_CONFIG
        date_list = [week.get_first_day(week_config) + timedelta(days=x) for x in range(days_qty)]
        delivery_date = [
            TestDeliveryDateNeeds.generate_delivery_date_need(sku=sku, site_config=site_config, week=week, day=day)
            for day in date_list
        ]
        return delivery_date


@dataclasses.dataclass
class TestHybridNeedsLiveUsage(TestRecord):
    site: TestSite
    sku: TestSku
    week: ScmWeek
    day: date
    brand: TestBrand
    value: int

    __key: tuple = None

    def __post_init__(self):
        self.__key = (
            self.__class__.__name__,
            self.brand.code,
            self.week,
            self.site.code,
            self.sku.sku_code,
            self.day,
        )

    def key(self) -> tuple:
        return self.__key

    def load(self):
        app_db.apply_query(
            sqla.insert(HybridNeedsLiveUsageModel).values(
                {
                    HybridNeedsLiveUsageModel.site: self.site.code,
                    HybridNeedsLiveUsageModel.sku_code: self.sku.sku_code,
                    HybridNeedsLiveUsageModel.scm_week: int(self.week),
                    HybridNeedsLiveUsageModel.day: self.day,
                    HybridNeedsLiveUsageModel.brand: self.brand.code,
                    HybridNeedsLiveUsageModel.value: self.value,
                }
            )
        )

    def unload(self):
        app_db.apply_query(
            sqla.delete(HybridNeedsLiveUsageModel).where(
                HybridNeedsLiveUsageModel.scm_week == int(self.week),
                HybridNeedsLiveUsageModel.sku_code == self.sku.sku_code,
                HybridNeedsLiveUsageModel.day == self.day,
                HybridNeedsLiveUsageModel.site == self.site.code,
                HybridNeedsLiveUsageModel.brand == self.brand.code,
            )
        )

    @staticmethod
    def generate_hybrid_needs_live_usage(
        site_config: TestSiteConfig, sku: TestSku, week: ScmWeek = None, day: date = None, value: int = None
    ) -> "TestHybridNeedsLiveUsage":
        return TestHybridNeedsLiveUsage(
            sku=sku,
            week=week or CURRENT_WEEK,
            day=day or CURRENT_DATE,
            site=site_config.site,
            brand=site_config.brand,
            value=value or data_generator.random_int(),
        )

    @staticmethod
    def generate_hybrid_needs_live_usage_for_several_days(
        site_config: TestSiteConfig,
        sku: TestSku,
        week: ScmWeek = None,
        days_qty: int = None,
    ) -> ["TestHybridNeedsLiveUsage"]:
        week_config = site_config.brand.scm_week_config
        if not week:
            week = CURRENT_WEEK if week_config == DEFAULT_WEEK_CONFIG else CURRENT_WEEK_FACTOR
        if not days_qty:
            days_qty = week_config.length
        date_list = [week.get_first_day(week_config) + timedelta(days=x) for x in range(days_qty)]
        hn_live_usages = [
            TestHybridNeedsLiveUsage.generate_hybrid_needs_live_usage(
                site_config=site_config, sku=sku, week=week, day=date
            )
            for date in date_list
        ]
        return hn_live_usages
