import logging
import os
import tarfile
from datetime import timedelta

import docker
from docker import DockerClient

from procurement.core.cache_utils.caching import no_args_temp_cache

CONTAINER_NAME = "ci-worker-1"
logger = logging.getLogger(__name__)


@no_args_temp_cache(ttl=timedelta(minutes=20))
def _get_docker_client() -> DockerClient:
    return docker.from_env()


def get_file_from_container(tar_file_path, file_path_in_container):
    _copy_from_container(dest=tar_file_path, src=file_path_in_container)
    _extract_file_from_tarfile(tar_file_path=tar_file_path)


def _copy_from_container(dest, src):
    client = _get_docker_client()
    container = client.containers.get(CONTAINER_NAME)
    if not os.path.exists(dest.parent):
        os.makedirs(dest.parent)
    with open(dest, "wb") as f:
        data, _ = container.get_archive(src)
        for chunk in data:
            f.write(chunk)


def _extract_file_from_tarfile(tar_file_path):
    with tarfile.open(tar_file_path) as tar:
        for member in tar.getmembers():
            if member.isfile():
                tar.extract(member, path=tar_file_path.parent)

    os.remove(tar_file_path)
