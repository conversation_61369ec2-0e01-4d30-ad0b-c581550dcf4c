import dataclasses
import json
from datetime import datetime, timedelta

from google.type import decimal_pb2
from hellofresh.proto.stream.distribution_center.inbound.goods_received_note.v1 import record_pb2

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import TestGrnDeliveryLineState
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, TOMORROW_DATETIME
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.services.kafka.kafka_client import BaseDataForProduce, KafkaClient
from automated_tests.utils import datetime_utils
from procurement.constants.protobuf import GrnUnitMeasure


@dataclasses.dataclass
class GrnKafkaDataForProduce(BaseDataForProduce):
    sku_code: str
    quantity: str
    sku_uom: GrnUnitMeasure
    state: TestGrnDeliveryLineState
    order_size: str
    po_delivery_line_id: str
    unloaded_quantity: str
    received_quantity: str
    expected_quantity: str
    palletized_quantity: str
    case_size: str
    expiration_date: datetime
    timezone: str
    rejected_quantity: str
    lot_number: str
    po_delivery_id: str
    delivery_time: datetime
    expected_delivery_start_time: datetime
    expected_delivery_end_time: datetime
    dc_code: str
    reference: str
    delivery_start_time: datetime
    delivery_end_time: datetime
    wms_name: str

    def prepare_kafka_data_value(self, serialization_class: type = None):
        purchase_order_line = serialization_class.PurchaseOrderLine(
            sku_code=self.sku_code,
            quantity=decimal_pb2.Decimal(value=self.quantity),
            sku_uom=self.sku_uom,
            order_size=decimal_pb2.Decimal(value=self.order_size),
        )

        purchase_order_delivery_line = serialization_class.PurchaseOrderDeliveryLine(
            id=self.po_delivery_line_id,
            sku_code=self.sku_code,
            unloaded_quantity=decimal_pb2.Decimal(value=self.unloaded_quantity),
            received_quantity=decimal_pb2.Decimal(value=self.received_quantity),
            expected_quantity=decimal_pb2.Decimal(value=self.expected_quantity),
            palletized_quantity=decimal_pb2.Decimal(value=self.palletized_quantity),
            case_size=decimal_pb2.Decimal(value=self.case_size),
            state=self.state,
            sku_uom=self.sku_uom,
            expiration_date=datetime_utils.convert_to_datetime_pb2(self.expiration_date, tz=self.timezone),
            rejected_quantity=decimal_pb2.Decimal(value=self.rejected_quantity),
            lot_number=self.lot_number,
        )

        purchase_order_delivery = serialization_class.PurchaseOrderDelivery(
            id=self.po_delivery_id,
            delivery_time=datetime_utils.convert_to_datetime_pb2(self.delivery_time, tz=self.timezone),
            expected_delivery_start_time=datetime_utils.convert_to_datetime_pb2(
                self.expected_delivery_start_time, tz=self.timezone
            ),
            expected_delivery_end_time=datetime_utils.convert_to_datetime_pb2(
                self.expected_delivery_end_time, tz=self.timezone
            ),
            state=self.state,
            lines=[purchase_order_delivery_line],
        )

        goods_received_note = serialization_class(
            dc_code=self.dc_code,
            reference=self.reference,
            delivery_start_time=datetime_utils.convert_to_datetime_pb2(self.delivery_start_time, tz=self.timezone),
            delivery_end_time=datetime_utils.convert_to_datetime_pb2(self.delivery_end_time, tz=self.timezone),
            state=self.state,
            lines=[purchase_order_line],
            deliveries=[purchase_order_delivery],
        )

        return goods_received_note.SerializeToString()

    def prepare_kafka_data_key(self, serialization_class: type = None):
        massage_key = {"dcCode": self.dc_code, "reference": self.reference}
        return json.dumps(massage_key).encode("utf-8")

    def prepare_kafka_data_headers(self, serialization_class: type = None):
        return {"wmsName": self.wms_name}

    @staticmethod
    def generate_grn_message(grn: TestGrn, site_config: TestSiteConfig):
        return GrnKafkaDataForProduce(
            sku_code=grn.sku.sku_code,
            quantity=str(data_generator.random_int()),
            sku_uom=GrnUnitMeasure.UNIT,
            state=grn.status,
            order_size=str(data_generator.random_int()),
            po_delivery_line_id=grn.po.order_number,
            unloaded_quantity=str(data_generator.random_int()),
            received_quantity=str(data_generator.random_int()),
            expected_quantity=str(data_generator.random_int()),
            palletized_quantity=str(data_generator.random_int()),
            case_size=str(data_generator.random_int()),
            expiration_date=CURRENT_DATETIME + timedelta(days=data_generator.random_int()),
            timezone=site_config.site.timezone,
            rejected_quantity=str(data_generator.random_int()),
            lot_number=data_generator.generate_string(),
            po_delivery_id=grn.po.order_number,
            delivery_time=grn.receipt_time_est,
            expected_delivery_start_time=CURRENT_DATETIME,
            expected_delivery_end_time=TOMORROW_DATETIME,
            dc_code=site_config.bob_code,
            reference=grn.po.po_number,
            delivery_start_time=CURRENT_DATETIME,
            delivery_end_time=TOMORROW_DATETIME,
            wms_name=grn.source,
        )


class KafkaGrnTopic(KafkaClient):

    topic_name = "public.distribution-center.inbound.goods-received-note.v1"
    protobuf_value_class = record_pb2.GoodsReceivedNoteValue

    def write_to_topic(self, data: GrnKafkaDataForProduce):
        message_value = data.prepare_kafka_data_value(serialization_class=self.protobuf_value_class)
        message_key = data.prepare_kafka_data_key()
        message_headers = data.prepare_kafka_data_headers()
        self.write_raw_to_topic(
            topic_name=self.topic_name, value=message_value, key=message_key, headers=message_headers
        )
