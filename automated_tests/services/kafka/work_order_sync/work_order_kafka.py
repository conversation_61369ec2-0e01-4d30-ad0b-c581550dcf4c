import dataclasses
import uuid
from datetime import datetime

from google.protobuf import timestamp_pb2
from hellofresh.proto.stream.rte.work_order.v2.work_order_pb2 import WorkOrder

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME, CURRENT_WEEK_STR
from automated_tests.data.models.sku import TestSku
from automated_tests.services.kafka.kafka_client import BaseDataForProduce, KafkaClient
from procurement.constants.hellofresh_constant import UnitOfMeasure


@dataclasses.dataclass
class WorkOrderKafkaDataForProduce(BaseDataForProduce):
    id: str
    number: str
    menu_week: str
    factor_distribution_center_name: str
    high_jump_warehouse_id: str
    recipe_sku: TestSku
    sub_recipe_sku: TestSku
    target_amount: int
    run_percentage: float
    run_number: int
    cook_methods: str
    date_needed: str
    shift_needed: int
    staging_date: str
    staging_shift: int
    locked: bool
    locked_timestamp: datetime
    comments: str
    commenter: str
    cooked_portions: int
    wip_portions: int
    mapped_amounts: int
    qa_hold: int
    lost: int
    donations: int
    discards: int
    is_active: bool
    creation_reason: str
    cook_start_date: str
    cook_start_shift: int
    cook_shifts: int
    part_sku: TestSku
    processing_yield_percentage: float
    is_manufactured: bool
    weight_per_each_quantity: float
    weight_per_each_unit_of_measure: str
    unit_of_measure: UnitOfMeasure
    unit_of_measure_part: UnitOfMeasure
    quantity_part: float
    quantity_value: float
    is_default: bool
    quantity_yield: float

    def prepare_kafka_data_value(self, serialization_class: WorkOrder = None):
        recipe_sku = serialization_class.SKU(
            id=self.recipe_sku.sku_uuid, code=self.recipe_sku.sku_code, name=self.recipe_sku.sku_name
        )
        sub_recipe_sku = serialization_class.SKU(
            id=self.sub_recipe_sku.sku_uuid, code=self.sub_recipe_sku.sku_code, name=self.sub_recipe_sku.sku_name
        )
        manufacturing_state = serialization_class.ManufacturingState(
            cooked_portions=self.cooked_portions,
            wip_portions=self.wip_portions,
            mapped_amounts=self.mapped_amounts,
            qa_hold=self.qa_hold,
            lost=self.lost,
            donations=self.donations,
            discards=self.discards,
        )
        part_quantity = serialization_class.Quantity(
            unit_of_measure=self.unit_of_measure_part, quantity=self.quantity_part
        )
        bill_of_materials = serialization_class.BillOfMaterial(
            sku_id=self.part_sku.sku_uuid,
            name=self.part_sku.sku_name,
            code=self.part_sku.sku_code,
            processing_yield_percentage=self.processing_yield_percentage,
            quantities=[part_quantity],
            weight_per_each_quantity=self.weight_per_each_quantity,
            is_manufactured=self.is_manufactured,
            weight_per_each_unit_of_measure=self.weight_per_each_unit_of_measure,
        )
        yields = serialization_class.Yield(
            is_default=self.is_default, unit_of_measure=self.unit_of_measure, quantity=self.quantity_yield
        )
        quantities = serialization_class.Quantity(unit_of_measure=self.unit_of_measure, quantity=self.quantity_value)
        work_order = serialization_class(
            id=self.id,
            number=self.number,
            menu_week=self.menu_week,
            factor_distribution_center_name=self.factor_distribution_center_name,
            high_jump_warehouse_id=self.high_jump_warehouse_id,
            recipe_sku=recipe_sku,
            sub_recipe_sku=sub_recipe_sku,
            target_amount=self.target_amount,
            run_percentage=self.run_percentage,
            run_number=self.run_number,
            cook_methods=[self.cook_methods],
            date_needed=self.date_needed,
            shift_needed=self.shift_needed,
            staging_date=self.staging_date,
            staging_shift=self.staging_shift,
            locked=self.locked,
            locked_timestamp=timestamp_pb2.Timestamp(seconds=int(datetime.timestamp(self.locked_timestamp)), nanos=0),
            comments=self.comments,
            commenter=self.commenter,
            manufacturing_state=manufacturing_state,
            is_active=self.is_active,
            creation_reason=self.creation_reason,
            cook_start_date=self.cook_start_date,
            cook_start_shift=self.cook_start_shift,
            cook_shifts=self.cook_shifts,
            bill_of_materials=[bill_of_materials],
            yields=[yields],
            quantities=[quantities],
            weight_per_each_quantity=self.weight_per_each_quantity,
            weight_per_each_unit_of_measure=self.weight_per_each_unit_of_measure,
            is_manufactured=self.is_manufactured,
            processing_yield_percentage=self.processing_yield_percentage,
        )
        return work_order.SerializeToString()

    def prepare_kafka_data_key(self, serialization_class: type = None):
        pass

    def prepare_kafka_data_headers(self, serialization_class: type = None):
        pass

    @staticmethod
    def generate_work_order_message(
        sub_recipe_sku: TestSku,
        is_locked: bool = True,
        unit_of_measure_part: UnitOfMeasure = None,
        unit_of_measure: UnitOfMeasure = None,
    ):
        recipe_sku, part_sku = TestSku.generate_skus(sku_quantity=2)
        return WorkOrderKafkaDataForProduce(
            id=str(uuid.uuid4()),
            number=data_generator.generate_string(),
            menu_week=CURRENT_WEEK_STR,
            factor_distribution_center_name=data_generator.generate_string(),
            high_jump_warehouse_id=data_generator.generate_string(),
            recipe_sku=recipe_sku,
            sub_recipe_sku=sub_recipe_sku,
            target_amount=data_generator.random_int(),
            run_percentage=float(data_generator.random_int()),
            run_number=data_generator.random_int(),
            cook_methods=data_generator.generate_string(),
            date_needed=data_generator.generate_string(),
            shift_needed=data_generator.random_int(),
            staging_date=data_generator.generate_string(),
            staging_shift=data_generator.random_int(),
            locked=is_locked,
            locked_timestamp=CURRENT_DATETIME,
            comments=data_generator.generate_string(),
            commenter=data_generator.generate_string(),
            cooked_portions=data_generator.random_int(),
            wip_portions=data_generator.random_int(),
            mapped_amounts=data_generator.random_int(),
            qa_hold=data_generator.random_int(),
            lost=data_generator.random_int(),
            donations=data_generator.random_int(),
            discards=data_generator.random_int(),
            is_active=True,
            creation_reason=data_generator.generate_string(),
            cook_start_date=data_generator.generate_string(),
            cook_start_shift=data_generator.random_int(),
            cook_shifts=data_generator.random_int(),
            part_sku=part_sku,
            processing_yield_percentage=float(data_generator.random_int()),
            is_manufactured=False,
            weight_per_each_quantity=float(data_generator.random_int()),
            weight_per_each_unit_of_measure=str(data_generator.random_int()),
            unit_of_measure_part=unit_of_measure_part or UnitOfMeasure.POUND.short_name,
            unit_of_measure=unit_of_measure or UnitOfMeasure.POUND.short_name,
            quantity_part=float(data_generator.random_int()),
            is_default=True,
            quantity_yield=float(data_generator.random_int()),
            quantity_value=float(data_generator.random_int()),
        )


class KafkaWorkOrderTopic(KafkaClient):

    topic_name = "public.rte.work-order.v2"
    protobuf_value_class = WorkOrder

    def write_to_topic(self, data: WorkOrderKafkaDataForProduce):
        message_value = data.prepare_kafka_data_value(serialization_class=self.protobuf_value_class)
        self.write_raw_to_topic(topic_name=self.topic_name, value=message_value, key=None)
