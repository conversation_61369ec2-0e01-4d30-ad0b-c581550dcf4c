{"name": "packaging", "namespace": "com.hellofresh.planning.suppliersku.packaging", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["entity_created", "entity_updated"]}}, {"name": "supplier_sku_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for supplierskus, common across all systems, and unique across all countries"}, {"name": "market", "type": "string", "doc": "market which the supplier sku belongs to", "default": ""}, {"name": "disposal_category", "type": "string", "default": ""}, {"name": "label_type", "type": "string", "default": ""}, {"name": "weight", "type": "double"}, {"name": "weight_tolerance_over", "type": "double"}, {"name": "weight_tolerance_under", "type": "double"}, {"name": "depth", "type": "double", "doc": "Depth or height, mm"}, {"name": "length", "type": "double", "doc": "Length, mm"}, {"name": "width", "type": "double", "doc": "Width or diameter, mm"}, {"name": "volume", "type": "double", "doc": "Volume in liters, calculated according to the shape."}, {"name": "cases_per_pallet", "type": "int", "default": 0}, {"name": "units_per_pallet", "type": "int", "default": 0}, {"name": "pallet_type", "type": "string", "default": ""}, {"name": "supplier_packaging", "type": "string", "default": ""}, {"name": "case_count", "type": "int", "default": 0}, {"name": "extras", "type": "string", "default": ""}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "shape", "type": "string", "default": "", "doc": "Shape, used to calculate volume"}, {"name": "shipping_mode", "type": "string", "default": "", "doc": "Shipping mode (Truck, Ship, etc)"}, {"name": "units_per_secondary_package", "type": "int", "default": 0, "doc": "Unit per secondary package/case"}, {"name": "shipping_strategy", "type": "string", "default": "", "doc": "Shipping strategy - Direct, Hub/Spoke"}, {"name": "cases_per_layer", "type": "int", "default": 0, "doc": "Cases per layer"}, {"name": "layers_per_pallet", "type": "int", "default": 0, "doc": "Layers per pallet"}, {"name": "pallet_height", "type": "double", "default": 0, "doc": "Pallet height"}, {"name": "stackable", "type": "boolean", "default": false, "doc": "Indicates whether this SupplierSKU can be stacked on top of itself during transportation"}]}