{"name": "suppliersku", "namespace": "com.hellofresh.planning.suppliersku", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["entity_created", "entity_updated"]}}, {"name": "id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for supplierskus, common across all systems, and unique across all countries"}, {"name": "supplier_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for suppliers, common across all systems, and unique across all countries"}, {"name": "supplier_code", "type": "int", "doc": "HelloFresh internal code for this supplier"}, {"name": "supplier_name", "type": "string", "default": "", "doc": "the name of the supplier"}, {"name": "culinary_sku_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for culinarysku, common across all systems, and unique across all countries"}, {"name": "culinary_sku_code", "type": "string", "doc": "HelloFresh internal code for this culinary sku"}, {"name": "culinary_sku_name", "type": "string", "default": "", "doc": "the name of the culinary sku"}, {"name": "market", "type": "string", "doc": "market which the suppliersku belongs to"}, {"name": "status", "type": "string", "doc": "status of the suppliersku"}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "extras", "type": "string", "default": ""}, {"name": "approved_by_fsqa", "type": "boolean", "default": false, "doc": "Indicates whether an SSKU has been approved by the FSQA team through a 3rd party Quality Management System (QMS), like Authenticate."}]}