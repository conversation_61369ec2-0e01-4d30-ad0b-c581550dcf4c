{"name": "facility", "namespace": "com.hellofresh.planning.remps.facility", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["supplier_created", "supplier_updated"]}}, {"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "parent_id", "type": {"type": "string", "logicalType": "uuid"}, "default": ""}, {"name": "name", "type": "string"}, {"name": "code", "type": "int"}, {"name": "legal_name", "type": "string"}, {"name": "status", "type": {"name": "statuses", "type": "enum", "symbols": ["Onboarding", "Active", "Offboarding", "Inactive", "Archived"], "default": "Onboarding"}, "default": "Onboarding"}, {"name": "market", "type": "string"}, {"name": "type", "type": "string"}, {"name": "distribution_centers", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "brands", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "brand", "type": "string", "default": ""}, {"name": "language", "type": "string", "default": ""}, {"name": "currency", "type": "string", "default": ""}, {"name": "address", "type": {"name": "supplier_address", "type": "record", "fields": [{"name": "city", "type": "string"}, {"name": "country", "type": "string"}, {"name": "country_name", "type": "string", "default": ""}, {"name": "state", "type": "string", "default": ""}, {"name": "address", "type": "string"}, {"name": "number", "type": "string"}, {"name": "post_code", "type": "string"}]}}, {"name": "contact_persons", "type": {"type": "array", "items": {"name": "contact_person", "type": "record", "fields": [{"name": "name", "type": "string", "default": ""}, {"name": "emails", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "phone", "type": "string", "default": ""}, {"name": "roles", "type": {"type": "array", "items": "string"}, "default": []}]}}}, {"name": "extras", "type": "string", "default": ""}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "created_by", "type": "string", "default": "", "doc": "email of the user who created the facility"}, {"name": "updated_by", "type": "string", "default": "", "doc": "email of the user who last updated the facility"}]}