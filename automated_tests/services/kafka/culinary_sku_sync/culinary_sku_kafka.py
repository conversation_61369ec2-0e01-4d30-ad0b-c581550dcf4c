import dataclasses
import uuid
from datetime import datetime

from automated_tests.data import data_generator
from automated_tests.data.constants.base_constants import PurchasingCategories
from automated_tests.data.constants.date_time_constants import TOMORROW_DATETIME, YESTERDAY_DATETIME
from automated_tests.data.models.sku import TestSku
from automated_tests.services.kafka.kafka_client import AvroKafkaClient, BaseDataForProduce
from procurement.constants.hellofresh_constant import UnitOfMeasure
from procurement.managers.kafka_utils import AVRO_TIME_FORMAT


@dataclasses.dataclass
class CulinarySkuKafkaDataForProduce(BaseDataForProduce):
    event_type: str
    id: uuid
    market: str
    name: str
    category: str
    subcategory: str
    locale: str
    code: str
    status: str
    brands: str
    type: str
    code_check_req: bool
    acceptable_code_life: int
    crush_class: int
    ingredient_id: str
    recipe_card_unit: str
    recipe_card_translation: str
    recipe_card_quantity: float
    quantity_amount: float
    quantity_unit: str
    temperature_min: int
    temperature_max: int
    temperature_unit: str
    packaging_qty: float
    packaging_type: str
    packaging_unit: str
    packaging_size: float
    cooling_type_name: str
    cooling_type_code: int
    third_pw_parent_id: str
    product_types: str
    created_at: datetime
    updated_at: datetime
    created_by: str
    updated_by: str
    co2_emission_factor: float
    wms_uom: str
    purchasing_category: PurchasingCategories

    def prepare_kafka_data_value(self, serialization_class: type = None):
        message = {
            "event_type": self.event_type,
            "id": self.id,
            "market": self.market,
            "name": self.name,
            "category": self.category,
            "subcategory": self.subcategory,
            "locale": self.locale,
            "code": self.code,
            "status": self.status,
            "brands": [self.brands],
            "type": self.type,
            "code_check_req": self.code_check_req,
            "acceptable_code_life": self.acceptable_code_life,
            "crush_class": self.crush_class,
            "ingredient_id": self.ingredient_id,
            "recipe_card_unit": self.recipe_card_unit,
            "recipe_card_translation": self.recipe_card_translation,
            "recipe_card_quantity": self.recipe_card_quantity,
            "quantity": {"amount": self.quantity_amount, "unit": self.quantity_unit},
            "temperature": {"min": self.temperature_min, "max": self.temperature_max, "unit": self.temperature_unit},
            "packaging": {
                "quantity": self.packaging_qty,
                "type": self.packaging_type,
                "unit": self.packaging_unit,
                "size": self.packaging_size,
            },
            "cooling_type": {"name": self.cooling_type_name, "code": self.cooling_type_code},
            "third_pw_parent_id": self.third_pw_parent_id,
            "product_types": [self.product_types],
            "extras": f'{{"extras":"{{\\"wms_uom\\": \\"{self.wms_uom}\\",'
            f'\\"purchasing_category\\": \\"{self.purchasing_category}\\"}}"}}',
            "created_at": str(self.created_at),
            "updated_at": str(self.updated_at),
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "taxonomy": {
                "category": self.category,
                "category_name": "",
                "subcategory": "",
                "family": "",
                "product": "",
                "attribute_1": "",
                "attribute_2": "",
            },
        }
        return message

    def prepare_kafka_data_key(self, serialization_class: type = None):
        pass

    def prepare_kafka_data_headers(self, serialization_class: type = None):
        pass

    @staticmethod
    def generate_culinary_sku_message(sku: TestSku):
        return CulinarySkuKafkaDataForProduce(
            # event type can be entity_created or entity_updated
            event_type="entity_updated",
            id=sku.sku_uuid,
            market=sku.market,
            name=sku.sku_name,
            category="DRY",
            subcategory=data_generator.generate_string(),
            locale=data_generator.generate_string(),
            code=sku.sku_code,
            status=sku.status,
            brands=",".join(sku.brands),
            type=data_generator.generate_string(),
            code_check_req=False,
            acceptable_code_life=data_generator.random_int(),
            crush_class=data_generator.random_int(),
            ingredient_id=data_generator.generate_string(),
            recipe_card_unit=data_generator.generate_string(),
            recipe_card_translation=data_generator.generate_string(),
            recipe_card_quantity=float(data_generator.random_int()),
            quantity_amount=float(data_generator.random_int()),
            quantity_unit=data_generator.generate_string(),
            temperature_min=data_generator.random_int(),
            temperature_max=data_generator.random_int(),
            temperature_unit=data_generator.generate_string(),
            packaging_qty=float(data_generator.random_int()),
            packaging_type=data_generator.generate_string(),
            packaging_unit=data_generator.generate_string(),
            packaging_size=float(data_generator.random_int()),
            cooling_type_name=data_generator.generate_string(),
            cooling_type_code=data_generator.random_int(),
            third_pw_parent_id=data_generator.generate_string(),
            product_types="Manufactured SKU",
            created_at=YESTERDAY_DATETIME.strftime(AVRO_TIME_FORMAT),
            updated_at=TOMORROW_DATETIME.strftime(AVRO_TIME_FORMAT),
            created_by=data_generator.generate_string(),
            updated_by=data_generator.generate_string(),
            co2_emission_factor=float(data_generator.random_int()),
            wms_uom=UnitOfMeasure.UNIT.short_name,
            purchasing_category=PurchasingCategories.random_value(),
        )


class KafkaCulinarySkuTopic:
    topic_name = "public.planning.culinarysku.v1"
    relative_schema_path = "automated_tests/services/kafka/avro_schemas/culinary_sku_schema.avsc"
    avro_schema = AvroKafkaClient.load_avro_schema(relative_path=relative_schema_path)

    def write_to_topic(self, data: CulinarySkuKafkaDataForProduce):
        message_value = data.prepare_kafka_data_value(serialization_class=self.avro_schema)
        kafka_client = AvroKafkaClient(avro_schema=self.avro_schema.__str__())
        kafka_client.write_raw_to_topic(topic_name=self.topic_name, value=message_value, key=None)
