import dataclasses
from decimal import Decimal
from typing import Optional

from google.type import decimal_pb2, money_pb2
from hellofresh.proto.stream.ordering.purchase_quantity_recommendation.v1alpha1 import (
    purchase_quantity_recommendation_pb2,
)

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.purchase_qty_recommendation import TestPurchaseQtyRecommendationItem
from automated_tests.services.kafka.kafka_client import BaseDataForProduce, KafkaClient


@dataclasses.dataclass
class PurchaseQtyRecommendationKafkaDataForProduce(BaseDataForProduce):
    site: str
    sku_uuid: str
    supplier_uuid: str
    week: int
    quantity: Decimal
    year: int

    # Optional fields with defaults
    packaging_type: int = 1  # Default to PACKAGING_TYPE_UNIT
    unit_of_measure: int = 2  # Default to UNIT_OF_MEASURE_UNIT
    case_size: Optional[Decimal] = None
    pallet_size: Optional[int] = None
    buffer_permyriad: int = 0  # Default to 0% buffer
    price_units: int = 0
    price_nanos: int = 0
    currency_code: str = "USD"

    def _decimal_to_proto(self, value: Decimal) -> decimal_pb2.Decimal:
        return decimal_pb2.Decimal(value=str(value))

    def prepare_kafka_data_value(self, serialization_class: type = None):
        raw_qty = self._decimal_to_proto(self.quantity)
        total_qty_value = self.quantity * (1 + (Decimal(str(self.buffer_permyriad)) / 10000))
        total_qty = self._decimal_to_proto(total_qty_value)

        case_size_value = self.case_size if self.case_size is not None else Decimal("1")
        case_size = self._decimal_to_proto(case_size_value)
        pallet_size = self.pallet_size if self.pallet_size is not None else 1

        single_package_price = money_pb2.Money(
            currency_code=self.currency_code, units=self.price_units, nanos=self.price_nanos
        )

        item = purchase_quantity_recommendation_pb2.PurchaseQuantityRecommendation(
            expected_start_time_local="2025-03-30T06:00:00",
            expected_end_time_local="2025-04-02T08:00:00",
            raw_qty=raw_qty,
            total_qty=total_qty,
            packaging_type=self.packaging_type,
            case_size=case_size,
            pallet_size=pallet_size,
            single_package_price=single_package_price,
            unit_of_measure=self.unit_of_measure,
            buffer_permyriad=self.buffer_permyriad,
        )
        value = serialization_class(purchase_quantity_recommendations=[item])
        return value.SerializeToString()

    def prepare_kafka_data_key(self, serialization_class: type = None):
        week = {"year": self.year, "week": self.week}

        sku_id = self.sku_uuid
        supplier_id = self.supplier_uuid

        return serialization_class(
            year_week=week,
            dc_code=self.site,
            sku_id=sku_id,
            supplier_id=supplier_id,
        ).SerializeToString()

    def prepare_kafka_data_headers(self, serialization_class: type = None):
        pass

    @staticmethod
    def generate_message(item: TestPurchaseQtyRecommendationItem, site_config: TestSiteConfig):
        return PurchaseQtyRecommendationKafkaDataForProduce(
            site=site_config.dc.bob_code,
            sku_uuid=item.sku.sku_uuid,
            supplier_uuid=item.supplier.id,
            week=item.week.week,
            quantity=item.quantity,
            year=item.week.year,
        )


class KafkaPurchaseQtyRecommendationTopic(KafkaClient):
    topic_name = "public.ordering.purchase-quantity-recommendation.v1alpha1"
    protobuf_value_class = purchase_quantity_recommendation_pb2.PurchaseQuantityRecommendationVal
    protobuf_key_class = purchase_quantity_recommendation_pb2.PurchaseQuantityRecommendationKey

    def write_to_topic(self, data: PurchaseQtyRecommendationKafkaDataForProduce):
        message_value = data.prepare_kafka_data_value(serialization_class=self.protobuf_value_class)
        message_key = data.prepare_kafka_data_key(serialization_class=self.protobuf_key_class)
        self.write_raw_to_topic(topic_name=self.topic_name, value=message_value, key=message_key)
