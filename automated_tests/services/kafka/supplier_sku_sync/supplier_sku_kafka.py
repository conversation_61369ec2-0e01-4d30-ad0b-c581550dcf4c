import dataclasses
import uuid
from datetime import datetime

from automated_tests.data import data_generator
from automated_tests.data.constants.date_time_constants import TOMORROW_DATETIME, YESTERDAY_DATETIME
from automated_tests.data.constants.kafka_constants import SupplierStatus
from automated_tests.data.models.pimt_packaging_info import TestSupplierSku
from automated_tests.services.kafka.kafka_client import AvroKafkaClient, BaseDataForProduce
from procurement.managers.kafka_utils import AVRO_TIME_FORMAT


@dataclasses.dataclass
class SupplierSkuKafkaDataForProduce(BaseDataForProduce):
    event_type: str
    id: uuid
    supplier_id: str
    supplier_code: int
    supplier_name: str
    culinary_sku_id: str
    culinary_sku_code: str
    culinary_sku_name: str
    market: str
    status: str
    created_at: datetime
    updated_at: datetime
    approved_by_fsqa: bool

    def prepare_kafka_data_value(self, serialization_class: type = None):
        message = {
            "event_type": self.event_type,
            "id": self.id,
            "supplier_id": self.supplier_id,
            "supplier_code": self.supplier_code,
            "supplier_name": self.supplier_name,
            "culinary_sku_id": self.culinary_sku_id,
            "culinary_sku_code": self.culinary_sku_code,
            "culinary_sku_name": self.culinary_sku_name,
            "market": self.market,
            "status": self.status,
            "created_at": str(self.created_at),
            "updated_at": str(self.updated_at),
            "extras": "",
            "approved_by_fsqa": self.approved_by_fsqa,
        }
        return message

    def prepare_kafka_data_key(self, serialization_class: type = None):
        pass

    def prepare_kafka_data_headers(self, serialization_class: type = None):
        pass

    @staticmethod
    def generate_supplier_sku_message(supplier_sku: TestSupplierSku):
        return SupplierSkuKafkaDataForProduce(
            # event type can be entity_created or entity_updated
            event_type="entity_updated",
            id=str(uuid.uuid4()),
            supplier_id=str(uuid.uuid4()),
            supplier_code=data_generator.random_int(),
            supplier_name=data_generator.generate_string(),
            culinary_sku_id=supplier_sku.sku.sku_uuid,
            culinary_sku_code=supplier_sku.sku.sku_code,
            culinary_sku_name=supplier_sku.sku.sku_name,
            market=supplier_sku.sku.market,
            status=SupplierStatus.ONBOARDING,
            created_at=YESTERDAY_DATETIME.strftime(AVRO_TIME_FORMAT),
            updated_at=TOMORROW_DATETIME.strftime(AVRO_TIME_FORMAT),
            approved_by_fsqa=False,
        )


class KafkaSupplierSkuTopic:
    topic_name = "rawevents.suppliersku.us.beta"
    relative_schema_path = "automated_tests/services/kafka/avro_schemas/supplier_sku_schema.avsc"
    avro_schema = AvroKafkaClient.load_avro_schema(relative_path=relative_schema_path)

    def write_to_topic(self, data: SupplierSkuKafkaDataForProduce):
        message_value = data.prepare_kafka_data_value(serialization_class=self.avro_schema)
        kafka_client = AvroKafkaClient(avro_schema=self.avro_schema.__str__())
        kafka_client.write_raw_to_topic(topic_name=self.topic_name, value=message_value, key=None)
