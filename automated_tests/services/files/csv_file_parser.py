import csv
import os
import shutil

from automated_tests.data.constants.file_export_constants import EXPORT_PATH
from automated_tests.services.docker.docker_client import get_file_from_container


def parse_csv_file_from_export_location(file_name, data_model, delimiter: str = ","):
    """
    This method reads the CSV file from the export location and gets from these file headers and all rows.
    Note: if this method running on CI as a first step it will copy the exported file from
    the worker container to the CI container.
    """
    tar_file_path = EXPORT_PATH / f"{file_name}.tar"
    file_search_pattern = f"{file_name}*.csv"
    headers = []
    content = []
    try:
        if os.environ.get("CI"):
            get_file_from_container(tar_file_path=tar_file_path, file_path_in_container=EXPORT_PATH)
            file_search_pattern = "*/" + file_search_pattern
        csv_file_path = next(EXPORT_PATH.glob(file_search_pattern))
        with open(csv_file_path) as csv_file:
            file_reader = csv.reader(csv_file, delimiter=delimiter)
            for row in file_reader:
                if file_reader.line_num == 1:
                    headers = row
                    continue
                content.append(data_model(*row))
        return headers, content
    finally:
        if os.path.exists(EXPORT_PATH):
            shutil.rmtree(EXPORT_PATH)
