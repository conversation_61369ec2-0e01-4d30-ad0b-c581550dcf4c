import os
import time

import openpyxl

from automated_tests.data.constants.file_export_constants import EXPORT_PATH
from automated_tests.services.docker.docker_client import get_file_from_container
from automated_tests.utils import DOWNLOADS_PATH


def parse_excel_tab(
    data_model,
    report_name,
    tab_name: str,
    max_col: int,
    min_row: int = 1,
    max_row: int = 2,
    min_col: int = 1,
):
    report_file_path = EXPORT_PATH / f"{report_name}.xlsx"
    archive_file_path = EXPORT_PATH / f"{report_name}.tar"
    workbook = None
    try:
        if os.environ.get("CI"):
            get_file_from_container(tar_file_path=archive_file_path, file_path_in_container=report_file_path)
        headers = []
        all_rows = []
        workbook = openpyxl.load_workbook(report_file_path)
        sheet = workbook[tab_name]
        for row_number, row in enumerate(
            sheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col)
        ):
            cells = []
            for cell in row:
                if row_number == 0:
                    headers.append(cell.value)
                    continue
                else:
                    cells.append(cell.value)
        all_rows.append(data_model(*cells))
        return headers, all_rows
    finally:
        if workbook is not None:
            workbook.close()
        if os.path.exists(report_file_path):
            os.remove(report_file_path)


def get_downloaded_xlsx_file_data(model, file_name, sheet_name: str, max_retries=30, retry_delay=1):
    downloads_path = DOWNLOADS_PATH
    file_path = os.path.join(downloads_path, file_name)

    for retry_count in range(max_retries):
        try:
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook[sheet_name]

            read_data = [list(row) for row in sheet.iter_rows(values_only=True)]

            if read_data:
                headers = read_data.pop(0)
                rows = [model(*row) for row in read_data]
                if rows:
                    return headers, rows

        except FileNotFoundError:
            if retry_count == max_retries - 1:
                raise Exception(f"File {file_name} not found after {max_retries} retries")
            else:
                time.sleep(retry_delay)

        except Exception as e:
            if retry_count == max_retries - 1:
                raise Exception(f"Failed to read data after {max_retries} retries: {str(e)}")
            else:
                time.sleep(retry_delay)
