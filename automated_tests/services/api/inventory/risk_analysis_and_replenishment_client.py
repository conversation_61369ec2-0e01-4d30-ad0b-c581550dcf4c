from requests import Response

from automated_tests.data.models.sku import TestSku
from automated_tests.data.models.warehouse import TestWarehouse
from automated_tests.services.api.base_client import BaseClient


class ReplenishmentClient(BaseClient):
    replenishment_dashboard_endpoint = "/api/v1/dc-inventory/replenishment/"
    replenishment_forecasts_endpoint = "/api/v1/dc-inventory/replenishment/forecasts"
    po_status_dropdown_endpoint = "/api/v1/dc-inventory/replenishment/po-status/"
    undelivered_po_status_endpoint = "/api/v1/dc-inventory/replenishment/undelivered-po-status/"
    replenishment_sku_comment_endpoint = "/api/v1/dc-inventory/replenishment/comments/"
    replenishment_override_endpoint = "/api/v1/dc-inventory/replenishment/override/"

    def get_replenishment_dashboard(self, warehouse: TestWarehouse) -> Response:
        return self.get(url=self.replenishment_dashboard_endpoint, params={"region": warehouse.region})

    def get_replenishment_forecast_dropdown(self, warehouse: TestWarehouse, sku: TestSku) -> Response:
        return self.get(
            url=self.replenishment_forecasts_endpoint, params={"region": warehouse.region, "skuCode": sku.sku_code}
        )

    def get_replenishment_po_status_dropdown(self, sku: TestSku, warehouse: TestWarehouse):
        return self.get(
            url=self.po_status_dropdown_endpoint, params={"skuCode": sku.sku_code, "region": warehouse.region}
        )

    def get_undelivered_pos_po_status_dropdown(self, sku: TestSku, warehouse: TestWarehouse):
        return self.get(
            url=self.undelivered_po_status_endpoint, params={"region": warehouse.region, "skuCode": sku.sku_code}
        )

    def add_or_edit_sku_comment_on_replenishment(self, sku: TestSku, warehouse: TestWarehouse, comment: str):
        return self.post(
            url=self.replenishment_sku_comment_endpoint,
            payload={"skuCode": sku.sku_code, "region": warehouse.region, "comment": comment},
        )

    def get_sku_comment_on_replenishment(self, sku: TestSku, warehouse: TestWarehouse):
        return self.get(
            url=self.replenishment_sku_comment_endpoint,
            params={
                "region": warehouse.region,
                "skuCode": sku.sku_code,
            },
        )

    def delete_sku_comment_on_replenishment(self, sku: TestSku, warehouse: TestWarehouse):
        return self.delete(
            url=self.replenishment_sku_comment_endpoint,
            params={
                "skuCode": sku.sku_code,
                "region": warehouse.region,
            },
        )

    def add_replenishment_override_data(self, warehouse: TestWarehouse, sku: TestSku, value: int) -> Response:
        payload = {"region": warehouse.region, "skuCode": sku.sku_code, "value": value}
        return self.post(url=self.replenishment_override_endpoint, payload=payload)

    def delete_replenishment_override_data(self, warehouse: TestWarehouse, sku: TestSku):
        params = {"region": warehouse.region, "skuCode": sku.sku_code}
        return self.delete(url=self.replenishment_override_endpoint, params=params)
