from requests import Response

from automated_tests.services.api.base_client import BaseClient
from procurement.constants.hellofresh_constant import MARKET_US


class PimtOpsMainClient(BaseClient):
    pimt_ops_main_endpoint = "/api/v1/pimt/main-ops/"
    pimt_ops_main_buying_tool_endpoint = "/api/v1/pimt/main-ops/{sku_code}/buying-tool/"

    def get_pimt_ops_main(self, market: str = MARKET_US) -> Response:
        self.set_market(market=market)
        return self.get(url=self.pimt_ops_main_endpoint)

    def get_pimt_ops_main_buying_tool(self, sku_code: str, market: str = MARKET_US) -> Response:
        self.set_market(market=market)
        return self.get(url=self.pimt_ops_main_buying_tool_endpoint.format(sku_code=sku_code))
