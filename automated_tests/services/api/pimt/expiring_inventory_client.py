from requests import Response

from automated_tests.services.api.base_client import BaseClient
from procurement.constants.hellofresh_constant import MARKET_US


class ExpiringInventoryClient(BaseClient):
    expiring_inventory_report_endpoint = "/api/v1/pimt/expiring-inventory-report/"

    def get_expiring_inventory_report(self, market: str = MARKET_US) -> Response:
        self.set_market(market=market)
        return self.get(url=self.expiring_inventory_report_endpoint)
