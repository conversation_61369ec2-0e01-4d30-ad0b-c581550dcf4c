from http import HTTPStatus

from requests import Response


def process_response_and_extract_data(
    response: Response,
    expected_length: int = 1,
    additional_keys: list[str] = None,
    status_code: HTTPStatus = HTTPStatus.OK,
    element_index: int = None,
) -> list | dict:
    """
    This method gets data from the response,
    checks that at least one element exists in the response data
    and gets an element from the data by the index"""
    assert (
        response.status_code == status_code
    ), f"Response status code is {response.status_code}, expected status code: {status_code},response: {response.text}"
    response_data = response.json()["data"]
    if additional_keys:
        for key in additional_keys:
            response_data = response_data[key]
    assert (
        len(response_data) == expected_length
    ), f"Response length is {len(response_data)}, expected length is: {expected_length}, response: {response.text}"
    if element_index is not None:
        return response_data[element_index]
    return response_data
