import datetime
from datetime import timedelta

from hamcrest import assert_that, equal_to

from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATE,
    CURRENT_WEEK_DAYS_STR,
    CURRENT_WEEK_FIRST_DAY,
    NEXT_WEEK_DAYS_STR,
    TOMORROW_DATETIME,
)


def verify_values_dependent_on_current_date(actual_daily_data: dict, expected_carryover_stock: dict):
    for date in (CURRENT_DATE, TOMORROW_DATETIME.date()):
        date_str = date.isoformat()
        if date == CURRENT_DATE:
            assert_that(
                actual_daily_data[date_str]["carryover"], equal_to(expected_carryover_stock[date_str])
            ), "Carryover for today and past days should be pulled from HJ snapshot"
            assert_that(
                actual_daily_data[date_str]["stockBuffer"],
                equal_to(round(actual_daily_data[date_str]["carryover"] * -0.05)),
            ), "Stock Buffer should be equal -5% * carryover stock for carryover stock coming from HJ"
            assert_that(
                actual_daily_data[date_str]["closingStock"],
                equal_to(
                    actual_daily_data[date_str]["carryover"]
                    - actual_daily_data[date_str]["consumption"]
                    + actual_daily_data[date_str]["inbound"]
                ),
            ), "Closing stock should be carryover - consumption + ('inbound' IF date <= current date)"
        else:
            assert_that(
                actual_daily_data[date_str]["carryover"],
                equal_to(actual_daily_data[(date - timedelta(1)).isoformat()]["closingStock"]),
            ), "For the future days carryover stock = closing stock of previous days"
            assert_that(
                actual_daily_data[date_str]["stockBuffer"], equal_to(0)
            ), "Stock Buffer should be 0 for carryover that is not pulled from HJ snapshot"
            assert_that(
                actual_daily_data[date_str]["closingStock"],
                equal_to(
                    actual_daily_data[date_str]["carryover"]
                    - actual_daily_data[date_str]["consumption"]
                    + actual_daily_data[date_str]["incoming"]
                ),
            ), "Closing stock should be carryover - consumption + ('incoming POs' IF 'this date' > current date))"


def verify_values_not_dependent_on_current_date(actual_daily_data: dict, expected_consumption: dict):
    for date_str in CURRENT_WEEK_DAYS_STR:
        verify_daily_needs(actual_daily_data, date_str)
        verify_stock_difference(actual_daily_data, date_str)
        assert_that(
            actual_daily_data[date_str]["consumption"], equal_to(expected_consumption[date_str])
        ), "Consumption should be equal to the sum of hybrid needs for this day"


def verify_daily_needs(actual_daily_data: dict, date_str: str):
    if actual_daily_data[date_str]["closingStock"] > 0:
        assert_that(
            actual_daily_data[date_str]["dailyNeeds"], equal_to(0)
        ), "If closing stock > 0, daily needs should be 0"
    else:
        assert_that(
            actual_daily_data[date_str]["dailyNeeds"],
            equal_to(min(actual_daily_data[date_str]["consumption"], -actual_daily_data[date_str]["closingStock"])),
        ), "Daily needs should be min(consumption, '-closing stock')"


def verify_stock_difference(actual_daily_data: dict, date_str: str):
    if date_str == CURRENT_WEEK_FIRST_DAY.isoformat():
        assert_that(
            actual_daily_data[date_str]["stockDifference"], equal_to(0)
        ), "If there is no data on previous day closing stock, Stock Difference is 0"
    elif datetime.date.fromisoformat(date_str) <= CURRENT_DATE:
        assert_that(
            actual_daily_data[date_str]["stockDifference"],
            equal_to(
                abs(
                    actual_daily_data[date_str]["carryover"]
                    - actual_daily_data[(datetime.date.fromisoformat(date_str) - timedelta(1)).isoformat()][
                        "closingStock"
                    ]
                )
            ),
        ), "Stock Difference should be abs(‘today carryover’ - ‘yday closing stock’)"
    else:
        assert_that(
            actual_daily_data[date_str]["stockDifference"], equal_to(0)
        ), "Stock Difference is 0 for future dates"


def verify_consumption(actual_daily_data: dict, expected_consumption_next_week: list, days: list = NEXT_WEEK_DAYS_STR):
    for index, date_str in enumerate(days):
        assert_that(actual_daily_data[date_str]["consumption"], equal_to(expected_consumption_next_week[index])), (
            f"Consumption for day {date_str} should be equal to oscar * mock plan: "
            f"{expected_consumption_next_week[index]}"
        )
