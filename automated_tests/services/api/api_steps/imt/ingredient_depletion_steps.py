import calendar
from collections import defaultdict
from datetime import date, datetime, timedelta
from typing import List

from hamcrest import assert_that, equal_to

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK, DATE_FORMAT_1
from automated_tests.data.models.discard import TestDiscard
from automated_tests.data.models.grn import TestGrn
from automated_tests.data.models.highjump import TestHJPalletSnapshot, TestHJReceipts
from automated_tests.data.models.hybrid_needs import TestHybridNeeds
from automated_tests.data.models.inventory_pull_put import TestInventoryPullPut
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from procurement.constants.hellofresh_constant import ProductionPlanType, ReceiveInputType
from procurement.core.dates.constants import Weekday
from procurement.managers.depletion.constants import (
    DAY_PASSED,
    NO_IMPACT,
    PRODUCTION_WILL_STOP,
    SCHEDULED_DELIVERY_NECESSARY,
    SUPPLEMENT_NEEDED,
)


def check_daily_status(response_daily_data: dict):
    for daily_key, daily_value in response_daily_data.items():
        if datetime.strptime(daily_key, DATE_FORMAT_1).date() < date.today():
            assert_that(daily_value["status"], equal_to(DAY_PASSED))
        elif (
            daily_value["totalProductionNeed"] != 0
            and daily_value["totalOnHand"] + daily_value["eodInventoryProduction"] < daily_value["totalProductionNeed"]
        ):
            assert_that(daily_value["status"], equal_to(SUPPLEMENT_NEEDED))
        else:
            assert_that(daily_value["status"], equal_to(NO_IMPACT))


def check_daily_status_in_week(response_daily_data: dict):
    day_number = 0
    for daily_key, daily_value in response_daily_data.items():
        if daily_value["eodInventoryProduction"] >= 0:
            assert_that(daily_value["statusInWeek"], equal_to(NO_IMPACT))
        elif daily_value["eodInventoryOrder"] >= 0 or day_number < Weekday.WED:
            assert_that(daily_value["statusInWeek"], equal_to(SCHEDULED_DELIVERY_NECESSARY))
        else:
            assert_that(daily_value["statusInWeek"], equal_to(PRODUCTION_WILL_STOP))
        day_number += 1


def check_daily_discards(response_daily_data: dict, discard: TestDiscard):
    today = date.today()
    for daily_key, daily_value in response_daily_data.items():
        if daily_key == today.strftime(DATE_FORMAT_1):
            assert_that(daily_value["discards"], equal_to(discard.quantity))


def check_daily_inventory(response_daily_data: dict, inventory_pull_put: TestInventoryPullPut):
    day_number = 0
    prev_day_end_of_day_inventory_prod = 0
    for daily_key, daily_value in response_daily_data.items():
        daily_key_as_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        if day_number == 0 and calendar.day_name[daily_key_as_date.weekday()] == "Monday":
            assert_that(daily_value["inventory"], equal_to(inventory_pull_put.quantity))
        else:
            assert_that(daily_value["inventory"], equal_to(prev_day_end_of_day_inventory_prod))
        prev_day_end_of_day_inventory_prod = daily_value["eodInventoryProduction"]
        day_number += 1


def check_daily_units_delivered(
    response_daily_data: dict, hj_receipts: List[TestHJReceipts], receiving_type: ReceiveInputType
):
    """Check that units delivered are correctly distributed across days based on receipt times"""
    if receiving_type != ReceiveInputType.HIGH_JUMP:
        return

    expected_daily_quantities = defaultdict(int)

    for hj_receipt in hj_receipts:
        receipt_date = hj_receipt.receipt_time_est.date().strftime(DATE_FORMAT_1)
        expected_daily_quantities[receipt_date] += hj_receipt.quantity_received

    for daily_key, daily_value in response_daily_data.items():
        expected_qty = expected_daily_quantities[daily_key]
        assert_that(
            daily_value["unitsDelivered"],
            equal_to(expected_qty),
            f"Units delivered for {daily_key=} should be {expected_qty=} but was {daily_value['unitsDelivered']}",
        )


def check_daily_total_on_hand(response_daily_data: dict):
    # res is Expected on_hand for the first Monday, will be extended by other days on_hand values
    res = [response_daily_data[next(iter(response_daily_data))]["onHand"]]
    units_on_order = [daily_data["unitsOnOrder"] for daily_data in response_daily_data.values()]
    units_delivered = [daily_data["unitsDelivered"] for daily_data in response_daily_data.values()]
    discard = [daily_data["discards"] for daily_data in response_daily_data.values()]
    for i, unit_on_order in enumerate(units_on_order[:-1], 1):
        current_day = res[i - 1] + units_delivered[i] + unit_on_order - discard[i]
        res.append(current_day)
    assert_that(res, equal_to([daily_data["totalOnHand"] for daily_data in response_daily_data.values()])), (
        "totalOnHand should be equal to "
        "Previous day On Hand + Today's Units Delivered + Today's Units on Order - Today's Discard"
    )


def check_daily_production_needs(response_daily_data: dict, hybrid_needs_list: List[TestHybridNeeds]):
    expected_production_needs = 0
    for daily_key, daily_value in response_daily_data.items():
        daily_key_as_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        for hybrid_needs in hybrid_needs_list:
            if hybrid_needs.week != CURRENT_WEEK:
                expected_production_needs = 0
            elif daily_key_as_date == hybrid_needs.date.date():
                expected_production_needs = hybrid_needs.quantity
                break
            else:
                expected_production_needs = 0
        assert_that(daily_value["productionNeeds"], equal_to(expected_production_needs))


def check_daily_production_needs_autostore_true(response_daily_data: dict, hybrid_needs_list: List[TestHybridNeeds]):
    expected_needs = {hn.date: hn.quantity for hn in hybrid_needs_list}
    expected_needs[hybrid_needs_list[1].date] += expected_needs.pop(hybrid_needs_list[0].date)
    expected_needs = {day - timedelta(days=1): needs for day, needs in expected_needs.items()}
    for daily_key, daily_value in response_daily_data.items():
        daily_key_as_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        expected = expected_needs.get(daily_key_as_date, 0)
        assert_that(daily_value["productionNeeds"], equal_to(expected))


def check_daily_total_production_need(response_daily_data: dict):
    prev_day_prod_need = 0
    for daily_key, daily_value in response_daily_data.items():
        assert_that(
            daily_value["totalProductionNeed"], equal_to(prev_day_prod_need + daily_value["productionNeeds"])
        ), "totalProductionNeed should be This day Production Needs + SUM of all previous day Production needs"
        prev_day_prod_need += daily_value["productionNeeds"]


def check_daily_units_on_order(response_daily_data: dict, pos: List[TestPurchaseOrder]):
    # Purchase orders should be only with statuses "Awaiting Delivery", "In Progress HJ", "Is Sent",
    # "Supplier Accepted", "Supplier Accepted With Changes"
    for daily_key, daily_value in response_daily_data.items():
        expected_units_on_order = 0
        daily_key_as_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        for po in pos:
            if daily_key_as_date == po.delivery_time_start.date():
                expected_units_on_order = po.first_line().qty
        assert_that(daily_value["unitsOnOrder"], equal_to(expected_units_on_order)), (
            "unitsOnOrder should be a Sum of all Quantity ordered of PO "
            "if it's Schedule delivery date = current day and Po Status is one of: "
            "Awaiting Delivery, In Progress HJ, Is Sent, Supplier Accepted, Supplier Accepted With Changes"
        )


def get_supplement_need_value(sn_data: dict[date, str]) -> str:
    for daily_key, daily_value in sn_data.items():
        if daily_value == SUPPLEMENT_NEEDED:
            day_to_return = calendar.day_name[daily_key.weekday()]
            if list(sn_data.keys()).index(daily_key) > 6:
                return f"{day_to_return} 2"
            elif day_to_return == str(Weekday.WED):
                return f"{day_to_return} 1"
            else:
                return day_to_return
    return NO_IMPACT


def get_daily_supplement_need_hj(response_data: dict) -> dict[date, str]:
    daily_dict = {}
    acc_units_on_order = 0
    for daily_key, daily_value in response_data["daily"].items():
        daily_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        if daily_date < date.today():
            daily_dict[daily_date] = DAY_PASSED
        elif (
            daily_value["totalProductionNeed"] != 0
            and (
                daily_value["totalOnHand"]
                - daily_value["totalProductionNeed"]
                + (acc_units_on_order := acc_units_on_order + daily_value["unitsOnOrder"])
            )
            < 0
        ):
            daily_dict[daily_date] = SUPPLEMENT_NEEDED
        else:
            daily_dict[daily_date] = NO_IMPACT
    return daily_dict


def get_daily_supplement_need(response_data: dict) -> dict[date, str]:
    daily_dict = {}
    for daily_key, daily_value in response_data["daily"].items():
        daily_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        if daily_date < date.today():
            daily_dict[daily_date] = DAY_PASSED
        elif (
            daily_value["totalProductionNeed"] != 0
            and daily_value["totalOnHand"] - daily_value["totalProductionNeed"] < daily_value["productionNeeds"] / 100
        ):
            daily_dict[daily_date] = SUPPLEMENT_NEEDED
        else:
            daily_dict[daily_date] = NO_IMPACT
    return daily_dict


def get_expected_supplement_need_hj(response_data: dict, no_impact_expected: bool = False) -> str:
    daily_dict = get_daily_supplement_need_hj(response_data)
    sn_hj_value = get_supplement_need_value(daily_dict)
    if no_impact_expected:
        assert_that(sn_hj_value, equal_to(NO_IMPACT))
    return sn_hj_value


def get_expected_supplement_need(response_data: dict, no_impact_expected: bool = False) -> str:
    daily_dict = get_daily_supplement_need(response_data)
    sn_value = get_supplement_need_value(daily_dict)
    if no_impact_expected:
        assert_that(sn_value, equal_to(NO_IMPACT))
    return sn_value


def get_expected_critical_delivery(response_daily_data: dict):
    for daily_key, daily_value in response_daily_data.items():
        if daily_value["status"] == SUPPLEMENT_NEEDED:
            return daily_value["statusInWeek"]
    return NO_IMPACT


def get_actual_bulk_values(response_data: dict):
    bulk_values = {}
    for site, rows in response_data.items():
        site_bulk_values = {}
        for row in rows:
            site_bulk_values[row["sku"]] = row["bulkValues"]
        bulk_values[site] = site_bulk_values
    return bulk_values


def check_row_forecast_and_planned_production_columns(
    hybrid_needs: list, hybrid_need_statuses: list, response_data: dict
):
    expected_status_calc = defaultdict(int)
    for hybrid_need in hybrid_needs:
        for hybrid_need_status in hybrid_need_statuses:
            if hybrid_need.date == hybrid_need_status.day:
                expected_status_calc[hybrid_need_status.status] += hybrid_need.quantity
    assert_that(
        expected_status_calc[ProductionPlanType.ACTUAL], equal_to(response_data["plannedProduction"])
    ), "Planned Production should be equal to the sum of hybrid needs qty for plan status"
    assert_that(
        expected_status_calc[ProductionPlanType.PROJECTED], equal_to(response_data["rowForecast"])
    ), "Row Forecast should be equal to the sum of hybrid needs qty for forecast status"


def assert_bulk_values(
    bulk_data: dict,
    po: TestPurchaseOrder,
    receipt_override: TestReceiptOverride,
    hj_pallet_snapshot: TestHJPalletSnapshot,
):
    assert_that(bulk_data["bulkUnitsInHj"], equal_to(hj_pallet_snapshot.pallet_quantity))
    assert_that(bulk_data["bulkUnitsOrdered"], equal_to(po.first_line().qty))
    assert_that(bulk_data["bulkUnitsReceived"], equal_to(receipt_override.quantity))
    assert_that(bulk_data["delta"], equal_to(bulk_data["bulkUnitsOrdered"] - bulk_data["bulkUnitsReceived"]))


def check_daily_units_delivered_for_warehouse(response_daily_data: dict, grns: list[TestGrn]):
    for daily_key, daily_value in response_daily_data.items():
        expected_units_delivered = 0
        daily_key_as_date = datetime.strptime(daily_key, DATE_FORMAT_1).date()
        for grn in grns:
            if daily_key_as_date == grn.receipt_time_est.date():
                expected_units_delivered = grn.units_received
        assert_that(daily_value["unitsDelivered"], equal_to(expected_units_delivered))


def check_daily_eod_inventory_prod_for_warehouse(response_daily_data: dict):
    eod_inventory = 0
    for daily_value in response_daily_data.values():
        eod_inventory += daily_value["unitsDelivered"]
        assert_that(daily_value["eodInventoryProduction"], equal_to(eod_inventory))
