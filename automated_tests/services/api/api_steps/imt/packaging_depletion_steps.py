from collections import defaultdict
from datetime import date, datetime, timedelta
from typing import Dict, List, Tuple

from hamcrest import assert_that, equal_to

from automated_tests.data.constants.base_constants import AcceptedIncomingStatusesPo, Seasons, Sizes
from automated_tests.data.constants.date_time_constants import CURRENT_DATE, CURRENT_WEEK, DATE_FORMAT_2, WEDNESDAY_PM
from automated_tests.data.models.cycle_counts import TestCycleCounts
from automated_tests.data.models.highjump import TestHJPackagingPalletSnapshot
from automated_tests.data.models.inventory_snapshot import TestInventorySnapshot
from automated_tests.data.models.pimt_inventory import TestPimtUnifiedInventory
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.receipt_override import TestReceiptOverride
from automated_tests.data.models.receive import TestReceiving
from automated_tests.data.models.sku import TestSku
from procurement.constants.hellofresh_constant import InventoryInputType, ReceiveInputType
from procurement.core.dates import ScmWeek
from procurement.managers.depletion.constants import DAY_PASSED, NO_IMPACT, SUPPLEMENT_NEEDED


def get_days_of_week(week: ScmWeek) -> Tuple[date, ...]:
    """This method returns the list of dates current week and includes the first day of the next week"""
    days = week.week_days()
    return *days, days[-1] + timedelta(days=1)


def get_computed_on_hand(on_hand_override: int, on_hand: int, on_hand_projected: int) -> int:
    """
    "Computed on Hand is equal to:
    IF Current Day's On Hand (Override) != None
     THEN Current Day's On Hand (Override)
     ELSEIF Current Day's On Hand (HJ) != None
     THEN Current Day's On Hand (HJ)
      ELSE Current Day's On Hand (Projected)
    """
    return next(v for v in (on_hand_override, on_hand, on_hand_projected) if v is not None)


def get_calculated_incoming(incoming_override: int, incoming: int) -> int:
    """
    "Computed Incoming POs" is equal to:
     IF Current Day's Incoming POs (Override) != None
    THEN Current Day's Incoming POs (Override)
      ELSE Current Day's Incoming POs
    """
    return next(v for v in (incoming_override, incoming) if v is not None)


def check_supplement_need(response_daily_data: list, common_data: dict):
    """
    This method checks "Supplement Need" column in Packaging Depletion view according to this formula:
    IF any production day for the SKU is flagged as "Supplement Needed",
    THEN populate with the first production day for which there exists a flag.
    IF no production days are flagged as "Supplement Need",
    THEN this column should be populated with "No Impact"
    """
    for day in response_daily_data:
        if day["status"] == SUPPLEMENT_NEEDED:
            assert_that(common_data["supplementNeed"], equal_to(day["weekDay"]))
            return
    assert_that(common_data["supplementNeed"], equal_to(NO_IMPACT))


def check_units_short(daily_needs_data: list):
    """
    This method checks "Units Short" column in Packaging Depletion view according to this formula:
    Total Weeks Demand - Computed On Hand* - Computed Incoming POs*
    """
    for day in daily_needs_data:
        if day["date"] == CURRENT_DATE.strftime(DATE_FORMAT_2):
            current_day = day
            computed_on_hand = get_computed_on_hand(
                current_day["onHand"], current_day["onHandOverride"], current_day["onHandProjected"]
            )
            incoming_on_hand = get_calculated_incoming(current_day["incoming"], current_day["incomingOverride"])
            assert_that(day["eodInventory"], equal_to(computed_on_hand + incoming_on_hand - day["demand"]))


def check_on_hand_calculation(
    daily_needs_data: list,
    inventory_type: InventoryInputType,
    packaging_snapshot: TestHJPackagingPalletSnapshot = None,
    cycle_count: TestCycleCounts = None,
    inventory_snapshot: TestInventorySnapshot = None,
    unified_inventory: TestPimtUnifiedInventory = None,
    po: TestPurchaseOrder = None,
):
    """
    This method checks "On Hand" column in Packaging Depletion view (when ONLY ONE snapshot available)
    IF Snapshot Date = Production Date
        THEN ["onHand"] == quantity from the appropriate source
    ELSE None
    """
    inventory_mapping = {
        InventoryInputType.WMSL: {
            "snapshot_date": str(inventory_snapshot.snapshot_timestamp.date()),
            "expected_on_hand_value": inventory_snapshot.unit_quantity,
        },
        InventoryInputType.E2OPEN: {
            "snapshot_date": str(unified_inventory.snapshot_timestamp),
            "expected_on_hand_value": unified_inventory.case_quantity * po.first_line().case_size,
        },
        InventoryInputType.GSHEET: {
            "snapshot_date": str(unified_inventory.snapshot_timestamp),
            "expected_on_hand_value": unified_inventory.case_quantity * po.first_line().case_size,
        },
        InventoryInputType.CYCLE_COUNT: {
            "snapshot_date": str(cycle_count.date_of_count.date()),
            "expected_on_hand_value": cycle_count.units,
        },
        InventoryInputType.HJ: {
            "snapshot_date": str(packaging_snapshot.snapshot_date),
            "expected_on_hand_value": packaging_snapshot.pallet_quantity,
        },
    }
    for day in daily_needs_data:
        if day["date"] == inventory_mapping[inventory_type]["snapshot_date"]:
            assert_that(day["onHand"], equal_to(inventory_mapping[inventory_type]["expected_on_hand_value"]))
        else:
            assert_that(day["onHand"], equal_to(None))


def sort_key_days_in_daily_needs(day: dict):
    """This method sorts the list by date"""
    return day["date"]


def check_on_hand_projected_calculation(daily_needs_data_current_week: list, daily_needs_data_previous_week: list):
    """
    This method checks "On Hand (Projected)" column in Packaging Depletion view according to this formula:
    IF the Day is the First Day of the week (Wednesday PM)
    THEN ["onHandProjected"] == Previous week's Wednesday AM ["eodInventory"]
    ELSE ["onHandProjected"] == Previous day's ["eodInventory"]
    """
    daily_needs_data_current_week.sort(key=sort_key_days_in_daily_needs)
    daily_needs_data_previous_week.sort(key=sort_key_days_in_daily_needs)
    for iterator in range(len(daily_needs_data_current_week)):
        if daily_needs_data_current_week[iterator]["weekDay"] == WEDNESDAY_PM:
            assert_that(
                daily_needs_data_current_week[iterator]["onHandProjected"],
                equal_to(daily_needs_data_previous_week[-1]["eodInventory"]),
            )
            continue
        assert_that(
            daily_needs_data_current_week[iterator]["onHandProjected"],
            equal_to(daily_needs_data_current_week[iterator - 1]["eodInventory"]),
        )


def check_on_hand_min_on_hand_projected(daily_needs_data: list):
    """
    This method checks "On Hand(HJ) - On Hand(Projected)" column in Packaging Depletion view according to this formula:
    On Hand (HJ) - On Hand (Projected)
    If On Hand (HJ) is None then result also will be None
    """
    for day in daily_needs_data:
        if day["onHand"] is not None:
            assert_that(day["onHandMinOnHandProjected"], equal_to(day["onHand"] - day["onHandProjected"]))
        else:
            assert_that(day["onHandMinOnHandProjected"], equal_to(None))


def check_demand(daily_needs_data_current_week: list, daily_needs_data_previous_week: list, demand_by_day: list):
    """
    This method checks "Demand" column in Packaging Depletion view according to this formula:
    IF the Day is the First Day of the week (Wednesday PM)
    THEN Previous week's Wednesday AM + Current Week's Wednesday PM Demand
    ELSE ["demand"] == demand_by_day from Packaging Demand
    """
    daily_needs_data_current_week.sort(key=sort_key_days_in_daily_needs)
    daily_needs_data_previous_week.sort(key=sort_key_days_in_daily_needs)
    for iterator in range(len(daily_needs_data_current_week)):
        if daily_needs_data_current_week[iterator]["weekDay"] == WEDNESDAY_PM:
            assert_that(
                daily_needs_data_current_week[iterator]["demand"],
                equal_to(daily_needs_data_previous_week[-1]["demand"] + demand_by_day[iterator]),
            )
        else:
            assert_that(daily_needs_data_current_week[iterator]["demand"], equal_to(demand_by_day[iterator]))


def check_incoming_pos(daily_needs_data: list, delivery_time_date: datetime, actual_po_data: list):
    """
    This method checks "Incoming POs" column in Packaging Depletion view according to this formula:
        SUM of all Quantity ordered of PO Status if its Schedule delivery date = Production Date
        and PO Status is one of Awaiting Delivery: In Progress HJ, IS_SENT, SUPPLIER_ACCEPTED,
        SUPPLIER_ACCEPTED_WITH_CHANGES, Shipped, Partially Shipped, Received Accurate/Over/Under statuses
    """
    for day in daily_needs_data:
        if day["date"] == datetime.strftime(delivery_time_date, DATE_FORMAT_2):
            expected_income = sum(
                [
                    po["quantityOrdered"]
                    for po in actual_po_data
                    if po["poStatus"] in AcceptedIncomingStatusesPo.get_all_values()
                    or po["poStatus"].startswith(AcceptedIncomingStatusesPo.IN_PROGRESS_HJ)
                ]
            )
            assert_that(day["incoming"], equal_to(expected_income))
            break


def check_incoming_po_according_to_received_and_delivery_date(
    daily_needs_data: list, delivery_time_date: datetime, actual_po_data: list
):
    for day in daily_needs_data:
        if day["date"] == datetime.strftime(delivery_time_date, DATE_FORMAT_2):
            expected_income = sum(
                [
                    po["quantityReceived"] or po["quantityOrdered"]
                    for po in actual_po_data
                    if po["poStatus"] in AcceptedIncomingStatusesPo.get_all_values()
                    or po["poStatus"].startswith(AcceptedIncomingStatusesPo.IN_PROGRESS_HJ)
                ]
            )
            assert_that(day["incoming"], equal_to(expected_income))
            break


def check_eod_inventory(daily_needs_data: list):
    """
    This method checks "EOD Inventory" column in Packaging Depletion view according to this formula:
    Computed On Hand* + Computed Incoming POs*  - Demand
    """
    for day in daily_needs_data:
        on_hand = get_computed_on_hand(day["onHand"], day["onHandOverride"], day["onHandProjected"])
        incoming = get_calculated_incoming(day["incoming"], day["incomingOverride"])
        assert_that(
            day["eodInventory"], equal_to(on_hand + (0 if day is daily_needs_data[-1] else incoming) - day["demand"])
        )


def check_status(daily_needs_data: list):
    """
    This method checks "Status" column in Packaging Depletion view according to this formula:
    IF In-Week Date < Today
        THEN "Day Passed"
    ELSE IF Computed On Hand* > Demand OR Demand = 0
        THEN "No Impact"
    ELSE "Supplement Needed"
    """
    for day in daily_needs_data:
        if datetime.strptime(day["date"], DATE_FORMAT_2).date() < CURRENT_DATE:
            assert_that(day["status"], equal_to(DAY_PASSED))
        elif (
            get_computed_on_hand(day["onHand"], day["onHandOverride"], day["onHandProjected"]) > day["demand"]
            or day["demand"] == 0
        ):
            assert_that(day["status"], equal_to(NO_IMPACT))
        else:
            assert_that(day["status"], equal_to(SUPPLEMENT_NEEDED))


def check_total_demand(daily_needs_data: list, weekly_overview_data: dict):
    """
    This method checks "Total Week’s demand" column in Packaging Depletion view according to this formula:
    SUM of all ["demand"] in Daily Needs(Wednesday PM – Wednesday AM)
    """
    sum_of_demands = sum([day["demand"] for day in daily_needs_data])
    assert_that(weekly_overview_data["demand"], equal_to(sum_of_demands))


def check_beginning_of_week_inventory(
    packaging_snapshot: TestHJPackagingPalletSnapshot, receiving_type: ReceiveInputType, weekly_override_data: dict
):
    """
    This method checks "Beginning of Week Inventory" column in Packaging Depletion view according to this formula:
    IF receiving type = HighJump
    THEN ["startWeekInventory"] ==  Pallet_Quantity
    ELSE 0
    """
    weekdays = get_days_of_week(CURRENT_WEEK)
    if receiving_type == ReceiveInputType.HIGH_JUMP and packaging_snapshot.snapshot_date == weekdays[0]:
        assert_that(weekly_override_data["startWeekInventory"], equal_to(packaging_snapshot.pallet_quantity))
    else:
        assert_that(
            weekly_override_data["startWeekInventory"] if weekly_override_data["startWeekInventory"] else 0 == 0
        )


def check_units_received(
    weekly_override_data: dict,
    receiving_type: ReceiveInputType,
    receipt_override: TestReceiptOverride,
    receiving: TestReceiving,
):
    """
    This method checks "Units Received" column in Packaging Depletion view according to this formula:
    IF receiving type = HighJump
    THEN ["unitsReceived"] = total quantity received from Receipt Override
    ELSE SUM Cases * Case Counts from Receiving
    """
    if receiving_type == ReceiveInputType.HIGH_JUMP:
        assert_that(weekly_override_data["unitsReceived"], equal_to(receipt_override.quantity))
    else:
        expected_receiving_quantity = (
            (receiving.case_size_one * receiving.case_count_one_total_units)
            + (receiving.case_size_two * receiving.case_count_two_total_units)
            + (receiving.case_size_three * receiving.case_count_three_total_units)
        )
        assert_that(weekly_override_data["unitsReceived"], equal_to(expected_receiving_quantity))


def check_end_of_week_inventory(
    packaging_snapshot: TestHJPackagingPalletSnapshot, receiving_type: ReceiveInputType, weekly_override_data: dict
):
    """
    This method checks "End-of-Week Inventory" column in Packaging Depletion view according to this formula:
    IF receiving type = HighJump
    THEN ["unitsReceived"] == None except when Wednesday AM Snapshot date available.
    SUM Pallet_Quantity WHERE (HJ Snapshot Date = Wednesday AM Date, SKU, Site match)
    ELSE 0
    """
    weekdays = get_days_of_week(CURRENT_WEEK)
    if receiving_type == ReceiveInputType.HIGH_JUMP and packaging_snapshot.snapshot_date == weekdays[-1]:
        assert_that(weekly_override_data["eowInventory"], equal_to(packaging_snapshot.pallet_quantity))
    else:
        assert_that(weekly_override_data["eowInventory"] if weekly_override_data["eowInventory"] else 0 == 0)


def check_overconsumption(weekly_overview_data: dict):
    """
    This method checks "Overconsumption" column in Packaging Depletion view according to this formula:
    Beginning of Week Inventory + Units Received - Total Week's Demand - End of Week Inventory

    If columns "Beginning of Week Inventory" or "End-of-Week Inventory" are None then result also will be None
    """
    if weekly_overview_data["startWeekInventory"] is None or weekly_overview_data["eowInventory"] is None:
        assert_that(weekly_overview_data["overconsumption"], equal_to(None))
    else:
        expected_overconsumption = (
            weekly_overview_data["startWeekInventory"]
            + weekly_overview_data["unitsReceived"]
            - weekly_overview_data["demand"]
            - weekly_overview_data["eowInventory"]
        )
        assert_that(weekly_overview_data["overconsumption"], equal_to(expected_overconsumption))


def random_sku_name_for_liners():
    """
    Generates a random SKU name for a liner.
    :return: A string in the format "Liner, <random size>, <random season>", where <random size> and <random season> are
    randomly generated values from the `Sizes` and `Seasons` classes, respectively.
    """
    return f"Liner, {Sizes.random_value()}, {Seasons.random_value()}"


def generate_skus_for_liners(sizes: list = None, seasons: list = None) -> Dict[str, TestSku]:
    """
    This method generates SKUs for liners based on given sizes and seasons.
    The Liner SKUs are grouped based on size: xs, small, medium, large, extra large, rtbs and three-letter codes - WIN,
    SPR, SUM, SUP. Therefore, there are 24 unique Parent Liner groups.
    :return: A dictionary, where the keys are in the format of "Liner - {size} - {season}",
    and the values are generated SKUs.
    """
    sizes = sizes or Sizes.get_all_values()
    seasons = seasons or Seasons.get_all_values()

    result = {}
    for season in seasons:
        for size in sizes:
            liner_group = f"Liner - {size} - {season}"
            sku = TestSku.generate_sku(sku_name=f"Liner, {size}, {season}")
            result[liner_group] = sku

    return result


def get_min_parts_for_liners_children(parts: list, ignore_nones: bool = False):
    """
    Finds the smallest non-zero part in a list of integers.
    The argument ignore_nones will affect the logic of None values processing.
    Ignore_nones = True -> then None values in part list will be ignored.
    Ignore_nones = False -> then None values will be processed as 0.
    """
    min_part = None
    for part in parts:
        if part is None and ignore_nones:
            continue
        if not part:
            return 0
        if min_part is None or part < min_part:
            min_part = part
    return min_part


def calculate_liner_children_depending_on_parts(liners: list, week_length: int) -> List[list]:
    """
    This method calculate liners children depending on parts (Part A/ Part B)
    SUM parts: Sum of values for child SKUs. If there are 2 similar SKUs (Part A/B), consider the lower value and
    ignore the higher value from the summation. If one of the similar child SKUs has a 0 value ignore both the non-zero
    value for the other SKU as well. Non parts values includes in SUM parts.
    Note: On Hand, Incoming POs (Override) and On Hand (Override) columns are equal to SUM parts,
    but ignoring empty values
    :return: List of calculated liners children according to documentation
    """
    sku_parts_mapping = defaultdict(list)
    liner_children = []
    for liner in liners:
        if "Part A" in liner["skuName"]:
            sku_parts_mapping[liner["skuName"].replace("Part A", "")].append(liner["dailyNeeds"])
        elif "Part B" in liner["skuName"]:
            sku_parts_mapping[liner["skuName"].replace("Part B", "")].append(liner["dailyNeeds"])
        else:
            liner_children.append(liner["dailyNeeds"])
    for parts in sku_parts_mapping.values():
        parts_daily_data_calculated = []
        for day in range(week_length):
            parts_daily_data_calculated.append(
                {
                    "onHand": get_min_parts_for_liners_children([p[day]["onHand"] for p in parts], ignore_nones=True),
                    "onHandOverride": get_min_parts_for_liners_children(
                        ([p[day]["onHandOverride"] for p in parts]), ignore_nones=True
                    ),
                    "demand": get_min_parts_for_liners_children([p[day]["demand"] for p in parts]) or 0,
                    "incoming": get_min_parts_for_liners_children([p[day]["incoming"] for p in parts]) or 0,
                    "incomingOverride": get_min_parts_for_liners_children(
                        ([p[day]["incomingOverride"] for p in parts]), ignore_nones=True
                    ),
                    "onHandProjected": get_min_parts_for_liners_children([p[day]["onHandProjected"] for p in parts])
                    or 0,
                }
            )
        liner_children.append(parts_daily_data_calculated)
    return liner_children
