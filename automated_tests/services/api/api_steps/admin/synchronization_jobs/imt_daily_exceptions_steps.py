from hamcrest import assert_that, equal_to

from automated_tests.data.constants.date_time_constants import (
    CURRENT_DATETIME,
    CURRENT_WEEK_FACTOR_STR,
    DATE_FORMAT_2,
    TOMORROW_DATETIME,
)
from automated_tests.services.api.api_steps.imt.packaging_depletion_steps import get_computed_on_hand
from automated_tests.tests.synchronization_jobs.data.gsheet_models.imt_packaging_daily_export_model import (
    PackagingDepletionExportData,
)


def assert_packaging_depletion_daily_export(sheet_row: PackagingDepletionExportData, api_data):
    api_daily_needs_data = {day["date"]: day for day in api_data["dailyNeeds"]}
    api_today_data = api_daily_needs_data[CURRENT_DATETIME.strftime(DATE_FORMAT_2)]
    api_tomorrow_data = api_daily_needs_data[TOMORROW_DATETIME.strftime(DATE_FORMAT_2)]

    assert_that(
        sheet_row.beginning_on_hand_today,
        equal_to((api_today_data["onHand"] or api_today_data["onHandProjected"] or 0)),
    )
    expected_on_hand_tomorrow = get_computed_on_hand(
        api_tomorrow_data["onHand"],
        api_tomorrow_data["onHandOverride"],
        api_tomorrow_data["onHandProjected"],
    )
    assert_that(sheet_row.beginning_on_hand_tomorrow, equal_to(expected_on_hand_tomorrow))
    assert_that(sheet_row.buyer, equal_to(api_data["buyer"]))
    assert_that(sheet_row.commodity_group, equal_to(api_data["commodityGroup"]))
    expected_critical_deliveries = (
        sheet_row.beginning_on_hand_tomorrow - sheet_row.demand_tomorrow - sheet_row.incoming_pos_today
    )
    assert_that(sheet_row.critical_deliveries, equal_to(expected_critical_deliveries))
    assert_that(sheet_row.demand_today, equal_to(api_today_data["demand"]))
    assert_that(sheet_row.demand_tomorrow, equal_to(api_tomorrow_data["demand"]))
    assert_that(sheet_row.incoming_pos_today, equal_to(api_today_data["incoming"]))
    assert_that(sheet_row.sku_code, equal_to(api_data["sku"]))
    assert_that(sheet_row.sku_name, equal_to(api_data["skuName"]))
    expected_units_shorts = sheet_row.beginning_on_hand_today - sheet_row.demand_today
    assert_that(sheet_row.units_short, equal_to(expected_units_shorts))


def assert_imt_daily_factor_network_module_gsheet(sheet_row, api_data, is_consolidated: bool):
    assert_that(sheet_row.brand, equal_to((api_data["brand"] or "")))
    assert_that(sheet_row.sku_code, equal_to(api_data["sku"]))
    assert_that(sheet_row.sku_name, equal_to(api_data["skuName"]))
    assert_that(sheet_row.category, equal_to(api_data["category"]))
    assert_that(sheet_row.commodity_group, equal_to((api_data["commodityGroup"] or "")))
    assert_that(sheet_row.buyer, equal_to((api_data["buyer"] or "")))
    assert_that(sheet_row.unit_of_measure, equal_to((api_data["unitOfMeasure"] or "")))
    assert_that(sheet_row.impacted_recipes, equal_to((api_data["impactedRecipes"] or "")))
    assert_that(sheet_row.plan, equal_to(api_data["plan"]))
    assert_that(sheet_row.forecast_oscar, equal_to(api_data["forecastOscar"]))
    assert_that(sheet_row.delta, equal_to(api_data["delta"]))
    assert_that(sheet_row.supplement_need, equal_to((api_data["sn"] or "")))
    assert_that(sheet_row.critical_delivery, equal_to((api_data["criticalDelivery"] or "")))
    assert_that(sheet_row.units_needed, equal_to(api_data["weekly"]["unitsNeeded"]))
    assert_that(sheet_row.units_ordered, equal_to(api_data["weekly"]["unitsOrdered"]))
    assert_that(sheet_row.units_received, equal_to(api_data["weekly"]["unitsReceived"]))
    assert_that(sheet_row.units_in_house_hj, equal_to(api_data["weekly"]["unitsInHouseHj"]))
    assert_that(sheet_row.row_need, equal_to(api_data["weekly"]["rowNeed"]))
    assert_that(sheet_row.units_in_house_minus_row_need, equal_to(api_data["weekly"]["unitsInHouseMinusRowNeed"]))
    assert_that(sheet_row.next_week_forecast, equal_to(api_data["weekly"]["nextWeekForecast"]))
    assert_that(
        sheet_row.units_in_house_min_row_need_min_forecast,
        equal_to(api_data["weekly"]["unitsInHouseMinRowNeedMinForecast"]),
    )
    assert_that(
        sheet_row.units_scheduled_to_be_produced_by_autobagger,
        equal_to(api_data["weekly"]["unitsToProduceByAutobagger"]),
    )
    assert_that(sheet_row.inventory, equal_to(api_data["weekly"]["inventory"]))
    assert_that(sheet_row.discards, equal_to(api_data["weekly"]["discards"]))
    assert_that(sheet_row.pulls, equal_to(api_data["weekly"]["pulls"]))
    assert_that(sheet_row.total_on_hand, equal_to(api_data["weekly"]["totalOnHand"]))
    assert_that(
        sheet_row.total_on_hand_minus_production_needs, equal_to(api_data["weekly"]["onHandMinProductionNeeds"])
    )
    assert_that(sheet_row.in_progress_hj, equal_to(api_data["weekly"]["inProgressHj"]))
    assert_that(sheet_row.awaiting_delivery, equal_to(api_data["weekly"]["awaitingDelivery"]))
    assert_that(sheet_row.not_delivered, equal_to(api_data["weekly"]["notDelivered"]))
    assert_that(sheet_row.buffer_quantity, equal_to(api_data["weekly"]["bufferQuantity"]))
    assert_that(sheet_row.buffer_percent, equal_to(api_data["weekly"]["bufferPercent"]))
    assert_that(sheet_row.scm_week, equal_to(CURRENT_WEEK_FACTOR_STR))
    assert_that(sheet_row.bulk_units_ordered, equal_to(api_data["bulkValues"]["bulkUnitsOrdered"]))
    assert_that(sheet_row.bulk_units_received, equal_to(api_data["bulkValues"]["bulkUnitsReceived"]))
    assert_that(sheet_row.bulk_delta, equal_to(api_data["bulkValues"]["delta"]))
    assert_that(sheet_row.bulk_units_in_hj, equal_to(api_data["bulkValues"]["bulkUnitsInHj"]))
    assert_that(sheet_row.consolidated, equal_to(is_consolidated))
