from requests import Response

from automated_tests.services.api.base_client import BaseClient
from procurement.constants.hellofresh_constant import MARKET_US
from procurement.core.dates import ScmWeek


class BuyerClient(BaseClient):
    buyer_endpoint = "/api/v1/imt/buyers/{buyer_id}/dashboard/"

    def get_buyer(self, week: ScmWeek, market: str = MARKET_US, buyer_id: int = 1) -> Response:
        self.set_market(market=market)
        return self.get(
            url=self.buyer_endpoint.format(buyer_id=buyer_id),
            params={"week": str(week)},
        )
