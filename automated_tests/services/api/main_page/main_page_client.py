from requests import Response

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class CriticalBuyerItems(BaseClient):
    critical_buyer_endpoint = "/api/v1/imt/buyers/critical-items/"

    def get_critical_buyer_items(self, week: ScmWeek = CURRENT_WEEK) -> Response:
        return self.get(url=self.critical_buyer_endpoint, params={"week": str(week)})
