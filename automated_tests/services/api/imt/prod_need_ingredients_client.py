from requests import Response

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ProdNeedIngredientsClient(BaseClient):
    prod_need_ingredients_endpoint = "/api/v1/imt/production-need/"
    prod_need_ingredients_header_endpoint = "/api/v1/imt/production-need/daily-header"

    def get_production_need(self, site_config: TestSiteConfig, week: ScmWeek) -> Response:
        return self.get(
            url=self.prod_need_ingredients_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week)},
        )

    def get_production_need_daily_headers(self, site_config: TestSiteConfig, week: ScmWeek) -> Response:
        return self.get(
            url=self.prod_need_ingredients_header_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week)},
        )
