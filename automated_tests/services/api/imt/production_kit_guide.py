from requests import Response

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ProductionKitGuideClient(BaseClient):
    production_kit_guide_endpoint = "/api/v1/imt/production-kit-guide/"

    def get_production_kit_guide(self, site_config: TestSiteConfig, week: ScmWeek = CURRENT_WEEK) -> Response:
        self.set_market(site_config.market.code)
        return self.get(
            url=self.production_kit_guide_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week)},
        )
