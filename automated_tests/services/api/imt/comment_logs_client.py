from requests import Response

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ImtCommentsLogClient(BaseClient):
    comments_logs_po_comment_endpoint = "/api/v2/comment/imt/po/log/"
    comments_log_sku_comment_endpoint = "/api/v2/comment/imt/sku/log/"

    def get_po_comments(self, week: ScmWeek = CURRENT_WEEK) -> Response:
        return self.get(
            url=self.comments_logs_po_comment_endpoint,
            params={"week": str(week)},
        )

    def get_sku_comments(self, week: ScmWeek = CURRENT_WEEK) -> Response:
        return self.get(url=self.comments_log_sku_comment_endpoint, params={"week": str(week)})
