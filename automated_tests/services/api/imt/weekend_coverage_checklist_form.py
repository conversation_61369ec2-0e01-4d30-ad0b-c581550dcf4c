import datetime

from requests import Response

from automated_tests.data.constants.date_time_constants import DATE_FORMAT_2
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.data.models.sku import TestSku
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class WeekendCoverageChecklistClient(BaseClient):
    weekend_checklist_endpoint = "/api/v1/forms/weekend-coverage-checklist/"
    weekend_checklist_validate_endpoint = "/api/v1/forms/weekend-coverage-checklist/validate/"
    weekend_checklist_id_endpoint = "/api/v1/forms/weekend-coverage-checklist/{id}/"
    weekend_checklist_pos_endpoint = "/api/v1/forms/weekend-coverage-checklist/autocomplete/{week}/pos/"
    weekend_checklist_pos_sku_names_endpoint = (
        "/api/v1/forms/weekend-coverage-checklist/autocomplete/{week}/pos/{po_number}/sku-names/"
    )
    weekend_checklist_preview_by_po_sku_endpoint = (
        "/api/v1/forms/weekend-coverage-checklist/preview/{po_number}/{sku_code}/"
    )
    weekend_checklist_preview_endpoint = "/api/v1/forms/weekend-coverage-checklist/preview/"
    weekend_checklist_set_fob_date_endpoint = "/api/v1/forms/weekend-coverage-checklist/{id}"
    weekend_checklist_po_status_endpoint = "/api/v1/forms/weekend-coverage-checklist/po-status/"

    def get_weekend_checklist_data(self, week: ScmWeek) -> Response:
        return self.get(url=self.weekend_checklist_endpoint, params={"week": str(week)})

    def get_weekend_checklist_pos_data(self, week: ScmWeek) -> Response:
        return self.get(url=self.weekend_checklist_pos_endpoint.format(week=week))

    def get_weekend_checklist_sku_names_by_pos(self, week: ScmWeek, po_number: str) -> Response:
        return self.get(url=self.weekend_checklist_pos_sku_names_endpoint.format(week=week, po_number=po_number))

    def get_weekend_checklist_preview_by_po_sku(self, po_number: str, sku_code: str) -> Response:
        return self.get(
            url=self.weekend_checklist_preview_by_po_sku_endpoint.format(po_number=po_number, sku_code=sku_code)
        )

    def get_weekend_coverage_preview(self, week: ScmWeek) -> Response:
        return self.get(url=self.weekend_checklist_preview_endpoint, params={"week": str(week)})

    def add_weekend_checklist_data(self, weekend_checklist_items: list, week: ScmWeek) -> Response:
        return self.post(
            url=self.weekend_checklist_endpoint, params={"week": str(week)}, payload=weekend_checklist_items
        )

    def validate_weekend_checklist_data(self, weekend_checklist_items: list, week: ScmWeek) -> Response:
        return self.post(
            url=self.weekend_checklist_validate_endpoint, payload=weekend_checklist_items, params={"week": str(week)}
        )

    def edit_weekend_checklist_data(
        self, weekend_checklist_items: dict, weekend_checklist_id: int, week: ScmWeek
    ) -> Response:
        return self.patch(
            url=self.weekend_checklist_id_endpoint.format(id=weekend_checklist_id),
            payload=weekend_checklist_items,
            params={"week": str(week)},
        )

    def delete_weekend_checklist_data(self, weekend_checklist_id: int, week: ScmWeek) -> Response:
        return self.delete(
            url=self.weekend_checklist_id_endpoint.format(id=weekend_checklist_id), params={"week": str(week)}
        )

    def set_weekend_checklist_fob_pick_up_date(
        self, id_: int, week: ScmWeek, fob_pick_up_date: datetime.date
    ) -> Response:
        return self.patch(
            url=self.weekend_checklist_set_fob_date_endpoint.format(id=id_),
            payload={"fobPickUpDate": fob_pick_up_date.strftime(DATE_FORMAT_2)},
            params={"week": str(week)},
        )

    def get_po_status_on_wcc(self, po: TestPurchaseOrder, sku: TestSku) -> Response:
        return self.get(
            url=self.weekend_checklist_po_status_endpoint, params={"poNumber": po.po_number, "skuCode": sku.sku_code}
        )
