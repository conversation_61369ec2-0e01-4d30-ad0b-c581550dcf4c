from requests import Response

from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ReceiptOverrideClient(BaseClient):
    receipt_override_form_endpoint = "/api/v1/forms/receipt-override/"
    receipt_override_validate_form_endpoint = "/api/v1/forms/receipt-override/validate/"
    receipt_override_form_pos_endpoint = "/api/v1/forms/receipt-override/pos/"
    receipt_override_form_skus_endpoint = "/api/v1/forms/receipt-override/skus/"

    def get_receipt_override_data(self, week: ScmWeek) -> Response:
        return self.get(url=self.receipt_override_form_endpoint, params={"week": str(week)})

    def get_receipt_override_data_pos(self, week: ScmWeek, brand: str, dc: str):
        params = {"week": str(week), "brand": brand, "dc": dc}
        return self.get(url=self.receipt_override_form_pos_endpoint, params=params)

    def get_receipt_override_data_skus(self, week: ScmWeek, brand: str, dc: str, po_number: str):
        params = {"week": str(week), "brand": brand, "dc": dc, "po_number": po_number}
        return self.get(url=self.receipt_override_form_skus_endpoint, params=params)

    def add_receipt_override_data(self, receipt_override_item: list, week: ScmWeek) -> Response:
        payload = {"week": str(week), "data": receipt_override_item}
        return self.post(url=self.receipt_override_form_endpoint, payload=payload)

    def validate_receipt_override_data(self, receipt_override_item: list, week: ScmWeek) -> Response:
        payload = {"week": str(week), "data": receipt_override_item}
        return self.post(url=self.receipt_override_validate_form_endpoint, payload=payload)

    def edit_receipt_override_data(self, receipt_override_item: dict) -> Response:
        return self.patch(url=self.receipt_override_form_endpoint, payload=receipt_override_item)

    def delete_receipt_override_data(self, receipt_override_item: dict) -> Response:
        return self.delete(url=self.receipt_override_form_endpoint, payload=receipt_override_item)
