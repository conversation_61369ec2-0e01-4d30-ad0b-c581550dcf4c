from requests import Response

from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ImtIngredientDepletionClient(BaseClient):
    ingredient_depletion_endpoint = "/api/v1/imt/ingredient-depletion/"
    ingredient_depletion_sku_comment_endpoint = "/api/v2/comment/imt/sku/"

    def get_ingredient_depletion(self, site_config: TestSiteConfig, week: ScmWeek) -> Response:
        self.set_market(market=site_config.market.code)
        return self.get(
            url=self.ingredient_depletion_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week)},
        )

    def add_or_edit_sku_comment(
        self, site_config: TestSiteConfig, comment: str, sku: TestSku, week: ScmWeek = CURRENT_WEEK
    ):
        return self.post(
            url=self.ingredient_depletion_sku_comment_endpoint,
            payload={
                "brand": site_config.brand.code,
                "comment": comment,
                "resourceId": sku.sku_code,
                "site": site_config.site.code,
                "week": str(week),
            },
        )

    def get_sku_comment(self, site_config: TestSiteConfig, sku: TestSku, week: ScmWeek = CURRENT_WEEK):
        return self.get(
            url=self.ingredient_depletion_sku_comment_endpoint,
            params={
                "brand": site_config.brand.code,
                "resourceId": sku.sku_code,
                "sites": site_config.site.code,
                "week": str(week),
            },
        )

    def delete_sku_comment(self, site_config: TestSiteConfig, sku: TestSku, week: ScmWeek = CURRENT_WEEK):
        return self.delete(
            url=self.ingredient_depletion_sku_comment_endpoint,
            params={
                "brand": site_config.brand.code,
                "resourceId": sku.sku_code,
                "site": site_config.site.code,
                "week": str(week),
            },
        )
