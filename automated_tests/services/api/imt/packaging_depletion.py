from requests import Response

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class PackagingDepletionClient(BaseClient):
    packaging_depletion_endpoint = "/api/v2/imt/packaging-depletion/"
    packaging_depletion_po_status_endpoint = "/api/v1/imt/packaging-depletion-po-status/"

    def get_packaging_depletion(self, site_config: TestSiteConfig, week: ScmWeek) -> Response:
        self.set_market(market=site_config.market.code)
        return self.get(
            url=self.packaging_depletion_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week)},
        )

    def get_packaging_depletion_po_status(self, site_config: TestSiteConfig, week: ScmWeek, sku: TestSku) -> Response:
        return self.get(
            url=self.packaging_depletion_po_status_endpoint,
            params={
                "week": str(week),
                "skuCode": sku.sku_code,
                "dc": site_config.site.code,
                "brand": site_config.brand.code,
            },
        )
