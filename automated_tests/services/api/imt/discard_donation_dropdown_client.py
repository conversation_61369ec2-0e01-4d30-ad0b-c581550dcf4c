from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class DiscardDonationDropdownClient(BaseClient):
    ingredient_depletion_discards_dropdown_endpoint = "/api/v1/imt/discards/"
    donations_dropdown_endpoint = "/api/v1/imt/donations/"

    def get_donations_dropdown_data(self, site_config: TestSiteConfig, sku: TestSku, week: ScmWeek = CURRENT_WEEK):
        return self.get(
            url=self.donations_dropdown_endpoint,
            params={
                "week": str(week),
                "skuCode": sku.sku_code,
                "brand": site_config.brand.code,
                "site": site_config.site.code,
            },
        )

    def get_discards_dropdown_data(self, site_config: TestSiteConfig, sku: TestSku, week: ScmWeek = CURRENT_WEEK):
        return self.get(
            url=self.ingredient_depletion_discards_dropdown_endpoint,
            params={
                "brand": site_config.brand.code,
                "skuCode": sku.sku_code,
                "site": site_config.site.code,
                "week": str(week),
            },
        )
