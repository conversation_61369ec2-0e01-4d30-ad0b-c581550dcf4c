from requests import Response

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class ImtAggregatedDepletionClient(BaseClient):
    aggregated_depletion_endpoint = "/api/v1/imt/ingredient-depletion/aggregated/"
    aggregated_depletion_po_status_endpoint = "/api/v1/imt/aggregated-po-status/"

    def get_aggregated_depletion(self, site_config: TestSiteConfig, week: ScmWeek) -> Response:
        return self.get(
            url=self.aggregated_depletion_endpoint,
            params={"multiBrandDc": site_config.multi_brand_dc, "week": str(week)},
        )

    def get_aggregated_depletion_po_status(self, week: ScmWeek, sku: TestSku, site_config: TestSiteConfig) -> Response:
        return self.get(
            url=self.aggregated_depletion_po_status_endpoint,
            params={"week": str(week), "skuCode": sku.sku_code, "dc": site_config.multi_brand_dc},
        )
