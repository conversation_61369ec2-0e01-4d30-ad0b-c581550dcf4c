from requests import Response

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.services.api.base_client import BaseClient
from procurement.core.dates import ScmWeek


class RemakeToolClient(BaseClient):
    remake_tool_endpoint = "/api/v1/imt/remake-tool/"

    def get_remake_tool(self, week: ScmWeek, site_config: TestSiteConfig, day: str) -> Response:
        return self.get(
            url=self.remake_tool_endpoint,
            params={"week": str(week), "dc": site_config.site.code, "brand": site_config.brand.code, "day": day},
        )

    def add_or_change_remake_tool(
        self, week: ScmWeek, site_config: TestSiteConfig, day: str, remake_tool_items: dict, mealkit_slot: str
    ) -> Response:
        payload = {mealkit_slot: remake_tool_items}
        return self.post(
            url=self.remake_tool_endpoint,
            params={"brand": site_config.brand.code, "dc": site_config.site.code, "week": str(week), "day": day},
            payload=payload,
        )
