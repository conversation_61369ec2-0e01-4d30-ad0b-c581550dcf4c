from requests import Response

from automated_tests.data.models.imt_config import TestBrand
from automated_tests.services.api.base_client import BaseClient


class BulkSkusClient(BaseClient):
    bulk_skus_endpoint = "/api/v1/admin/bulk-skus/"

    def get_bulk_skus(self) -> Response:
        return self.get(url=self.bulk_skus_endpoint)

    def add_bulk_skus(
        self,
        bulk_sku_code: str,
        packaged_sku_code: str,
        is_master_sku: bool = None,
        brands: list[TestBrand] = None,
        pick_conversion: int = None,
    ) -> Response:
        brands_list = ["All Brands"]
        if brands:
            brands_list = [brand.code for brand in brands]
        payload = {
            "packagedSkuCode": packaged_sku_code,
            "bulkSkuCode": bulk_sku_code,
            "brands": brands_list,
            "isMasterSku": is_master_sku or False,
            "pickConversion": pick_conversion or 1,
        }
        return self.post(url=self.bulk_skus_endpoint, payload=payload)

    def edit_bulk_skus(
        self,
        bulk_sku_code: str,
        bulk_sku_name: str,
        packaged_sku_code: str,
        packaged_sku_name: str,
        brand_codes: list[str] = None,
        is_master_sku: bool = None,
        pick_conversion: int = None,
    ) -> Response:
        payload = {
            "packagedSkuCode": packaged_sku_code,
            "packagedSkuName": bulk_sku_name,
            "bulkSkuCode": bulk_sku_code,
            "bulkSkuName": packaged_sku_name,
            "isMasterSku": is_master_sku or False,
            "pickConversion": pick_conversion or 1,
            "brands": brand_codes,
        }
        return self.patch(url=self.bulk_skus_endpoint, payload=payload)

    def delete_bulk_skus(
        self,
        bulk_sku_code: str,
        packaged_sku_code: str,
    ) -> Response:
        return self.delete(
            url=self.bulk_skus_endpoint, params={"packagedSkuCode": packaged_sku_code, "bulkSkuCode": bulk_sku_code}
        )
