from requests import Response

from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.services.api.base_client import BaseClient


class ForecastPushDataClient(BaseClient):
    forecast_push_data_endpoint = "/api/v1/admin/forecast-push-data/"

    def get_forecast_push_data(self, site_config: TestSiteConfig) -> Response:
        self.set_market(market=site_config.market.code)
        return self.get(url=self.forecast_push_data_endpoint)
