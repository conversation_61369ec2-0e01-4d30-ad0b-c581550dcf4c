import logging
import time

from requests import Response
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from automated_tests.data.constants.admin_constants import SyncJobStatus
from automated_tests.services.api.base_client import BaseClient
from automated_tests.utils.custom_waits import CustomWaiters
from procurement.constants.hellofresh_constant import MARKET_US

logger = logging.getLogger()


class JobsClient(BaseClient):
    jobs_endpoint = "/api/v1/jobs/"

    def run_job(self, key: str, arguments: dict = None, market: str = MARKET_US) -> Response:
        self.set_market(market=market)
        payload = {"arguments": arguments if arguments else {}, "key": key}
        return self.post(url=self.jobs_endpoint, payload=payload)

    def get_jobs_by_key(self, key, arguments: dict = None, market: str = MARKET_US) -> Response:
        self.set_market(market=market)
        payload = arguments if arguments else {}
        return self.get(url=self.jobs_endpoint + key, params=payload)

    def get_job_status_by_key(self, key: str, timeout: int = 60, arguments: dict = None, market: str = MARKET_US):
        start_time = time.time()

        job_status = None
        job_last_sync = None

        while time.time() - start_time < timeout:
            time.sleep(2)
            job_response = self.get_jobs_by_key(key, arguments, market=market).json()
            job_last_sync = job_response["data"]["last_sync"]
            is_success = job_last_sync["success"]

            if not job_last_sync["jobs"]:
                continue

            job_status = job_last_sync["jobs"][0]["status"]

            if is_success:
                return job_status, None

            if job_status == SyncJobStatus.FAILED:
                logger.debug(f"WARNING: {job_response}")
                return job_status, job_response

        error_message = None
        if job_status != "failed":
            error_message = "The job status is still None:\n"
            if job_last_sync:
                error_message += f"Errors: {job_last_sync['errors']}\n"
                error_message += f"In Progress: {job_last_sync['in_progress']}\n"
                error_message += f"Success: {job_last_sync['success']}\n"
                error_message += f"Time: {job_last_sync['time']}\n"
                error_message += f"Jobs: {job_last_sync['jobs']}\n\n"

        raise TimeoutError(error_message or f"Timeout {timeout} seconds has passed")

    @retry(retry=retry_if_exception_type(ConnectionError), stop=stop_after_attempt(2), wait=wait_exponential(5))
    def is_job_in_progress_by_key(self, key: str, market: str = MARKET_US, arguments: dict = None):
        """
        :param arguments: expected arguments
        :param key: job's name
        :param market: market_name
        :return: boolean meaning job is still running or no
        """

        def _predicate() -> bool:
            response_get = self.get_jobs_by_key(market=market, key=key, arguments=arguments)
            assert response_get.status_code == 200
            try:
                job_last_sync = response_get.json()["data"]["last_sync"]
                return job_last_sync["in_progress"]
            except IndexError:
                return True

        return _predicate

    def run_job_and_wait_until_done(
        self, key: str, arguments: dict = None, polling: int = 2, timeout: int = 120, market: str = MARKET_US
    ):
        self.set_market(market=market)
        response_post = self.run_job(arguments=arguments, key=key, market=market)
        assert response_post.status_code == 200, response_post
        self.wait_until_job_is_done(key=key, arguments=arguments, polling=polling, timeout=timeout, market=market)
        return response_post

    def wait_until_job_is_done(
        self, key: str, arguments: dict = None, polling: int = 2, timeout: int = 120, market: str = MARKET_US
    ):
        CustomWaiters(timeout=timeout, poll_frequency=polling).until(
            self.is_job_in_progress_by_key(key=key, market=market, arguments=arguments),
            message="The job still in progress",
        )

    def get_all_steps_by_job_name(self, job_name: str, params: dict = None, market: str = MARKET_US):
        self.set_market(market=market)
        job_url_endpoint = f"{self.jobs_endpoint}{job_name}"
        return self.get(url=job_url_endpoint, params=params).json()["data"]["last_sync"]["jobs"]

    def get_step_status_for_imt_sync(
        self, step_name: str, params: dict = None, brand_code: str = None, market: str = MARKET_US
    ):
        self.set_market(market=market)
        all_steps = self.get_all_steps_by_job_name(job_name="IMT_SYNC", params=params, market=market)[0]["dependants"]
        if brand_code:
            brand_steps = next((step["dependants"] for step in all_steps if brand_code in step["job_name"]), [])
        else:
            brand_steps = all_steps[0]["dependants"]
        steps_mapping = {step["job_name"]: step["status"] for step in brand_steps}
        warnings_mapping = {
            message["job_name"]: message["warning"] if message["warning"] else [] for message in brand_steps
        }
        return steps_mapping[step_name], warnings_mapping[step_name]

    def get_step_warnings_for_imt_sync(self, step_name: str, params: dict = None, market: str = MARKET_US):
        self.set_market(market=market)
        all_steps = self.get_all_steps_by_job_name(job_name="IMT_SYNC", params=params, market=market)[0]["dependants"][
            0
        ]["dependants"]
        return self._get_step_warnings(step_name=step_name, steps=all_steps)

    @staticmethod
    def _get_step_warnings(step_name: str, steps: list):
        steps_mapping = {step["job_name"]: step["warning"] for step in steps}
        errors = [item["error"] for item in steps_mapping[step_name]]
        return errors
