import allure
from playwright.sync_api import expect

from automated_tests.pages_new.base_page import BasePage


class AdminGsheetMixinLocators:
    DOCS_STATUS = "//button[contains(@class, 'btn-sync')]//span[text() = '{doc_status}']"
    GSHEET_LINK_LABEL = "//div[@class='row']//label"
    LABEL_FIELD = "//label[text() = '{field_name}']"
    LINK_FIELD = "//label[text() = '{field_name}']/following-sibling::input"
    DOC_DOWNLOAD_STATUS_BUTTON = "//label[text() = '{label}']//ancestor::div[@class = 'v-input__slot']/div/button"


class AdminGsheetMixin(BasePage):
    @allure.step("Verify if the check mark is present")
    def is_check_mark_present(self, label_name: str):
        self.page.wait_for_selector(AdminGsheetMixinLocators.DOC_DOWNLOAD_STATUS_BUTTON.format(label=label_name))
        expect(
            self.page.locator(AdminGsheetMixinLocators.DOC_DOWNLOAD_STATUS_BUTTON.format(label=label_name))
        ).to_be_visible()

    @allure.step("Check docs have status")
    def check_button_with_status_present(self, expected_status: str):
        expect(
            self.page.locator(AdminGsheetMixinLocators.DOCS_STATUS.format(doc_status=expected_status))
        ).to_be_visible()

    @allure.step("Validate GSheet links have labels")
    def validate_gsheet_labels(self, expected_labels: list[str]):
        expect(self.page.locator(AdminGsheetMixinLocators.GSHEET_LINK_LABEL)).to_have_text(expected_labels)

    @allure.step("Get label status")
    def get_label_status(self, doc_name: str):
        return self.page.locator(AdminGsheetMixinLocators.LABEL_FIELD.format(field_name=doc_name)).get_attribute(
            "class"
        )

    @allure.step("Insert link to gsheet field")
    def insert_link(self, doc_name: str, link: str):
        self.page.locator(AdminGsheetMixinLocators.LINK_FIELD.format(field_name=doc_name)).fill(link)
        self.page.keyboard.press("Enter")
        self.wait_loading()
        self.is_check_mark_present(label_name=doc_name)
