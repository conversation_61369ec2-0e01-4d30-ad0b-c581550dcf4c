import allure
from playwright.sync_api import expect

from automated_tests.data.constants.column_headers.inventory_column_headers import (
    UNIFIED_INVENTORY_MODULE_COLUMN_HEADERS,
)
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.inventory.unified_inventory_module import UnifiedInventoryModuleBaseTableRow


class InventoryDropdownMixinLocators:
    INVENTORY_DROPDOWN_BTN = "//div[contains(@col-id, 'unitsInHouse')]//i[contains(@class, 'chevron-right')]"
    INVENTORY_DROPDOWN_MODAL = "//div[contains(@class, 'inventory-data')]"
    INVENTORY_DROPDOWN_HEADERS = "//div[contains(@class,'inventory-data')]//span[@class='ag-header-cell-text']"
    INVENTORY_DROPDOWN_ROWS = (
        "//div[contains(@class,'inventory-data')]//div[@class='ag-center-cols-container']/child::div"
    )


class InventoryDropdownMixin(TableDataMixin, BasePage):
    @allure.step("Expand Inventory Dropdown and wait until dropdown modal is visible")
    def expand_inventory_dropdown(self):
        self.page.locator(InventoryDropdownMixinLocators.INVENTORY_DROPDOWN_BTN).click()
        expect(self.page.locator(InventoryDropdownMixinLocators.INVENTORY_DROPDOWN_MODAL)).to_be_visible()

    @allure.step("Check Inventory dropdown headers")
    def check_inventory_dropdown_headers(self):
        self.check_column_headers(
            expected_headers=UNIFIED_INVENTORY_MODULE_COLUMN_HEADERS,
            locator=InventoryDropdownMixinLocators.INVENTORY_DROPDOWN_HEADERS,
        )

    @allure.step("Get Inventory dropdown table data on additional views")
    def get_inventory_dropdown_table_data(self) -> list[UnifiedInventoryModuleBaseTableRow]:
        expect(self.page.locator(InventoryDropdownMixinLocators.INVENTORY_DROPDOWN_MODAL)).to_be_visible()
        return self.get_center_table_data(
            rows_locator=InventoryDropdownMixinLocators.INVENTORY_DROPDOWN_ROWS,
            data_model=UnifiedInventoryModuleBaseTableRow,
        )
