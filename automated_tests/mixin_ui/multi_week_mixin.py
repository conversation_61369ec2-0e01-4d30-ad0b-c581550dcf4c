import allure
from playwright.sync_api import expect

from automated_tests.data.constants.base_constants import NUM_OF_AVAILABLE_WEEKS
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.utils import ui_utils
from procurement.core.dates import ScmQuarter, ScmWeek


class MultiWeekMixinLocators:
    MULTI_WEEK_TOGGLE = "//label[text()='Multi-Week']"
    WEEK_RANGE_SELECTOR = "//div[@class='week-activator']"
    SELECTED_WEEK = (
        "//div[text()='{quarter}']/parent::div//button[contains(@class,'week--selected')]/span[text()='{week}']"
    )


class MultiWeekMixin(QuarterOrWeekDropdownMixin, BasePage):

    @allure.step("Enable/Disable Multi-Week toggle")
    def enable_disable_multi_week_toggle(self, set_check: bool):
        multi_week_toggle = self.page.locator(MultiWeekMixinLocators.MULTI_WEEK_TOGGLE)
        expect(multi_week_toggle).to_be_attached()
        multi_week_toggle.set_checked(checked=set_check)
        if set_check:
            expect(self.page.locator(MultiWeekMixinLocators.WEEK_RANGE_SELECTOR)).to_be_visible()

    @allure.step("Choose week ranges")
    def choose_week_range(self, week_from: ScmWeek, week_to: ScmWeek):
        self.toggle_week_dropdown()
        week_from_locator = self.get_week_locator(week=week_from)
        week_to_locator = self.get_week_locator(week=week_to)
        # click on week_from if it's not already selected
        if not self.is_week_selected(week=week_from).is_visible():
            week_from_locator.click()
        week_from_locator.drag_to(week_to_locator)
        week_to_locator.click()

    @allure.step("Check if selected week is visible")
    def is_week_selected(self, week: ScmWeek):
        quarter = ScmQuarter.from_scm_week(week)
        week_str = ui_utils.get_week_str(week)
        return self.page.locator(MultiWeekMixinLocators.SELECTED_WEEK.format(quarter=quarter, week=week_str))

    @allure.step("Check range of enable weeks")
    def check_range_of_enabled_weeks(self, week_start: ScmWeek, picked_week: ScmWeek = None):
        self.choose_week_from_dropdown(week=picked_week or week_start)
        if picked_week:
            self.click_on_specific_week(week=week_start)
        week_to = week_start + NUM_OF_AVAILABLE_WEEKS
        for week in ScmWeek.range(week_start, week_to):
            week_item = self.get_week_locator(week=week)
            expect(week_item).to_be_enabled()
        # uncheck week item and close multi-select dropdown
        self.click_on_specific_week(week=week_start)
        self.toggle_week_dropdown()

    @allure.step("Check disabled week")
    def check_disabled_week(self, week: ScmWeek):
        self.choose_week_from_dropdown(week=week)
        week_to_check = week + NUM_OF_AVAILABLE_WEEKS + 1
        expect(self.get_week_locator(week=week_to_check)).to_be_disabled()
        # uncheck week item and close multi-select dropdown
        self.click_on_specific_week(week=week)
        self.toggle_week_dropdown()
