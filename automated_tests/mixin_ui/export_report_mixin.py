import datetime
import os
from enum import StrEnum
from pathlib import Path

import allure
from playwright.sync_api import expect

from automated_tests.data.constants.base_constants import ALL_POS, WAREHOUSES_BRAND
from automated_tests.data.models.imt_config import TestBrand
from automated_tests.mixin_ui.calendar_mixin import CalendarMixin
from automated_tests.mixin_ui.multi_week_mixin import MultiWeekMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.utils import DOWNLOADS_PATH
from procurement.core.dates import ScmWeek


class ExportReportLocators:
    GENERATE_EXPORT_REPORT_BUTTON = "//button[@id='generateExportReport']"
    EXPORT_POPUP = "//div[@id='export-pop-up']"
    TIME_PERIOD_BRAND_WAREHOUSE_CHECKBOX = "//label[text()='{time_period_brand_warehouse}']"
    PO_FILTER = "//label[text()='{po_type}']"
    QUARTER_DROPDOWN_ITEM = "//div[@class='v-list-item__title' and text()='{quarter}']"
    WEEK_DROPDOWN_ITEM = "//div[@class='v-list-item__title' and text()='{week}']"
    QUARTER_DROPDOWN_ARROW = "//label[text()='Quarter']/following-sibling::div/div/i"
    WEEK_DROPDOWN_ARROW = "//label[text()='Week']/following-sibling::div/div/i"
    WEEK_FROM_DROPDOWN = "//label[text()='Week from']/parent::div"
    WEEK_TO_DROPDOWN = "//label[text()='Week to']/parent::div"
    WEEK_FROM_WEEK_TO_DROPDOWN_ITEM = (
        "//div[contains(@class, 'menuable__content__active')]//div[@class='v-list-item__title' and text()='{week}']"
    )
    SELECT_DATE_LABEL = "//label[text()='Select Date' ]/following-sibling::input"
    SELECT_MONTH_LABEL = "//label[text()='Select Month']/following-sibling::input"
    SELECT_DATE_FROM_LABEL = "//label[text()='Date From']/parent::div"
    SELECT_DATE_TO_LABEL = "//label[text()='Date To']/parent::div"
    GENERATE_BUTTON = "//button/span[text()='Generate']"
    LIST_OF_ACTIVE_ITEMS = "//div[contains(@class,'content__active')]//div[@role='listbox']//div[@role='option']"


class _ExportMultiWeekSelector(MultiWeekMixin):
    WEEK_PICKER = "//div[@id='multi-week-selector']//div[@role='button' and @class='week-activator']//span[1]"


class ExportReportMixin(BasePage):
    def __init__(self, page):
        super().__init__(page)
        self.week_selector = _ExportMultiWeekSelector(page)
        self.calendar_selector = CalendarMixin(page)

    class ExportTimePeriods(StrEnum):
        DAILY = "Daily"
        WEEKLY = "Weekly"
        WEEK_RANGE = "Week range"
        MONTHLY = "Monthly"
        CUSTOM = "Custom"

    @allure.step("Generate export report")
    def generate_export_report(
        self,
        time_period: ExportTimePeriods,
        po_filter: str = ALL_POS,
        brand: TestBrand = None,
        is_warehouse: bool = False,
        daily_date=None,
        week: ScmWeek = None,
        week_from: ScmWeek = None,
        week_to: ScmWeek = None,
        month_date=None,
        custom_date_start=None,
        custom_date_end=None,
        file_name=None,
    ):
        generate_export_report_btn = self.page.locator(ExportReportLocators.GENERATE_EXPORT_REPORT_BUTTON)
        generate_export_report_btn.click()
        expect(self.page.locator(ExportReportLocators.EXPORT_POPUP)).to_be_visible()

        if brand:
            time_period_brand_warehouse = brand.code
        elif is_warehouse:
            time_period_brand_warehouse = WAREHOUSES_BRAND
        else:
            time_period_brand_warehouse = "All"

        self.page.locator(
            ExportReportLocators.TIME_PERIOD_BRAND_WAREHOUSE_CHECKBOX.format(
                time_period_brand_warehouse=time_period_brand_warehouse
            )
        ).check()
        if po_filter:
            self.page.locator(ExportReportLocators.PO_FILTER.format(po_type=po_filter)).click()
        self.page.locator(
            ExportReportLocators.TIME_PERIOD_BRAND_WAREHOUSE_CHECKBOX.format(time_period_brand_warehouse=time_period)
        ).click()
        if time_period == self.ExportTimePeriods.DAILY:
            self.page.locator(ExportReportLocators.SELECT_DATE_LABEL).click()
            self.calendar_selector.set_date(daily_date)
        elif time_period == self.ExportTimePeriods.WEEKLY:
            self.week_selector.choose_week_from_dropdown(week)
        elif time_period == self.ExportTimePeriods.WEEK_RANGE:
            self.week_selector.choose_week_range(week_from, week_to)
        elif time_period == self.ExportTimePeriods.MONTHLY:
            self.page.locator(ExportReportLocators.SELECT_MONTH_LABEL).click()
            self.calendar_selector.set_month(month_date)
        elif time_period == self.ExportTimePeriods.CUSTOM:
            self.set_custom_dates(custom_date_start, custom_date_end)
        generate_btn = self.page.locator(ExportReportLocators.GENERATE_BUTTON)
        with self.page.expect_download() as download_info:
            generate_btn.click()
        download = download_info.value
        download.save_as(Path(DOWNLOADS_PATH + os.sep + file_name))

    def set_custom_dates(self, start_date: datetime.date, end_date: datetime.date):
        select_date_label = self.page.locator(ExportReportLocators.SELECT_DATE_FROM_LABEL)
        select_date_label.click()
        self.calendar_selector.set_date(start_date)
        select_date_label = self.page.locator(ExportReportLocators.SELECT_DATE_TO_LABEL)
        select_date_label.click()
        self.calendar_selector.set_date(end_date)

    def choose_week_from_in_dropdown(self, week_from: ScmWeek):
        self.page.locator(ExportReportLocators.WEEK_FROM_DROPDOWN).click()
        week_from_dropdown = self.page.locator(
            ExportReportLocators.WEEK_FROM_WEEK_TO_DROPDOWN_ITEM.format(week=str(week_from))
        )
        week_from_dropdown.click()
        expect(self.page.locator(ExportReportLocators.WEEK_DROPDOWN_ITEM.format(week=str(week_from)))).to_be_hidden()

    def choose_week_to_in_dropdown(self, week_to: ScmWeek):
        self.page.locator(ExportReportLocators.WEEK_TO_DROPDOWN).click()
        week_to_dropdown = self.page.locator(
            ExportReportLocators.WEEK_FROM_WEEK_TO_DROPDOWN_ITEM.format(week=str(week_to))
        )
        week_to_dropdown.click()
        expect(self.page.locator(ExportReportLocators.WEEK_DROPDOWN_ITEM)).to_be_hidden()
