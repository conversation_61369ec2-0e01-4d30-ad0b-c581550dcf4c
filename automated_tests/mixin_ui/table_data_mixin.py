import dataclasses
import logging
from typing import TypeVar

import allure
from playwright.sync_api import Locator
from playwright.sync_api import TimeoutError as PwTimeoutError
from playwright.sync_api import expect

from automated_tests.pages_new.base_page import BasePage

T = TypeVar("T")
logger = logging.getLogger(__name__)


class TableDataMixinLocators:
    COLUMN_HEADERS_LOCATOR = "//span[@class='ag-header-cell-text' and text()]"
    CENTER_ROW_LOCATORS = "//div[contains(@class,'container')]//div[@class='ag-center-cols-container']/child::div"
    LEFT_ROWS_LOCATOR = "//div[@class='ag-pinned-left-cols-container']/child::div"
    RIGHT_ROWS_LOCATOR = "//div[@class='ag-pinned-right-cols-container']/child::div"
    CELL_IN_ROW_LOCATOR = "//div[contains(@class,'ag-cell ') and not(contains(@col-id,'blank'))]"
    NO_TABLE_DATA_MESSAGE = "//div[@class='v-alert__content' and normalize-space()='No data']"
    EXPAND_COLLAPSE_BTN = "//span[@ref='agLabel' and contains(text(),'{expand_collapse_name}')]"
    NO_ROWS_TO_SHOW = "//div[contains(@class,'no-rows')]//span"
    TOP_CENTRAL_ROW_LOCATOR = "//div[@class='ag-center-cols-container']/child::div[@row-index={row_number}]"
    CELL_IN_ROW_BY_ID = "child::div[@col-id='{id}']"
    COLUMN_IN_TABLE = "//div[@class='ag-header-cell-label']/span[@ref='eText' and text()='{column_header_text}']"
    SECTION_LOCATOR = "//div[@ref='agContainer']/span[text()='{section_name}']"


class TableDataMixin(BasePage):
    @allure.step("Get center table data")
    def get_center_table_data(
        self,
        data_model: type[T],
        rows_locator: str = TableDataMixinLocators.CENTER_ROW_LOCATORS,
        cell_locator=TableDataMixinLocators.CELL_IN_ROW_LOCATOR,
    ) -> list[T]:
        return self.get_table_data(
            rows_locator=rows_locator,
            cell_locator=cell_locator,
            data_model=data_model,
        )

    @allure.step("Get right table data")
    def get_right_table_data(
        self, data_model: type[T], rows_locator: str = TableDataMixinLocators.RIGHT_ROWS_LOCATOR
    ) -> list[T]:
        return self.get_table_data(
            data_model=data_model,
            rows_locator=rows_locator,
            cell_locator=TableDataMixinLocators.CELL_IN_ROW_LOCATOR,
        )

    @allure.step("Get left table data")
    def get_left_table_data(
        self, data_model: type[T], rows_locator: str = TableDataMixinLocators.LEFT_ROWS_LOCATOR
    ) -> list[T]:
        return self.get_table_data(
            data_model=data_model,
            rows_locator=rows_locator,
            cell_locator=TableDataMixinLocators.CELL_IN_ROW_LOCATOR,
        )

    def get_table_data(self, rows_locator: str, cell_locator: str, data_model: type[T]) -> list[T]:
        row_objects = []
        self.wait_loading()
        rows = self.page.locator(rows_locator)
        try:
            rows.last.wait_for(state="attached", timeout=3000)
        except PwTimeoutError:
            pass  # ignoring TimeoutError because the table might be just empty
        for i in range(rows.count()):
            cell_items_text = rows.nth(i).locator(cell_locator).all_inner_texts()
            row_objects.append(data_model(*[item.strip() for item in cell_items_text]))
        return row_objects

    @allure.step("Expand/Collapse header by name")
    def expand_collapse_header(self, expand_collapse_name: str = None, expand: bool = True, specific_locator=None):
        if specific_locator:
            expand_locator = self.page.locator(specific_locator)
        else:
            expand_locator = self.page.locator(
                TableDataMixinLocators.EXPAND_COLLAPSE_BTN.format(expand_collapse_name=expand_collapse_name)
            )
        expect(expand_locator).to_be_attached()
        is_expanded = expand_locator.locator("//ancestor::div[@role='columnheader']").get_attribute("aria-expanded")
        if (expand and is_expanded == "false") or (not expand and is_expanded == "true"):
            additional_xpath = (
                "//following-sibling::span[@ref='agClosed']" if expand else "//following-sibling::span[@ref='agOpened']"
            )
            expand_locator.locator(additional_xpath).click()
        self.wait_loading()

    @allure.step("Check 'No data' message is present/absent")
    def is_no_data_message_present(self, expected_presence: bool = True):
        no_data_message = self.page.locator(TableDataMixinLocators.NO_TABLE_DATA_MESSAGE)
        expect(no_data_message).to_be_visible() if expected_presence else expect(no_data_message).to_be_hidden()

    @allure.step("Check 'No Rows' message is present/absent")
    def is_no_rows_to_show_message_present(self, expected_presence: bool = True):
        no_rows_message = self.page.locator(TableDataMixinLocators.NO_ROWS_TO_SHOW)
        expect(no_rows_message).to_be_visible() if expected_presence else expect(no_rows_message).to_be_hidden()

    @allure.step("Get appropriate table row element")
    def get_table_row(self, row_number: int = 0):
        return self.page.locator(TableDataMixinLocators.TOP_CENTRAL_ROW_LOCATOR.format(row_number=row_number))

    @allure.step("Get cell in row and add to a model")
    def find_cells_in_row_and_add_to_model(
        self, row: Locator, data_model: type[T], ids_to_be_replaced: dict = None
    ) -> T:
        cells = []
        for field in dataclasses.fields(data_model):
            cell_id = field.name
            if ids_to_be_replaced and cell_id in ids_to_be_replaced.keys():
                cell_id = ids_to_be_replaced[cell_id]

            cell_locator = TableDataMixinLocators.CELL_IN_ROW_BY_ID.format(id=cell_id)
            logger.info(f"Find cell in the row with locator: {cell_locator}")
            cell_item_text = row.locator(f"xpath={cell_locator}").text_content() or ""
            cells.append(cell_item_text.strip())

        return data_model(*cells)

    @allure.step("Check child blank value in Summary Section")
    def check_blank_values_on_child_rows(self, expected_values: list[str], locator: str):
        """
        Validates that specific cells within table rows have the expected blank values. Despite we have blank values on
        dashboard it still returns text.
        This method iterates through each row of a table located. For each row, it locates cells by BLANK_CELLS_LOCATOR
        and collects their text content. If any text content is found, it adds it to the list of row objects.
        Asserts that the text contents of the collected rows match the expected_values.
        """
        row_cells_text = []
        rows = self.page.locator(TableDataMixinLocators.LEFT_ROWS_LOCATOR)
        for i in range(rows.count()):
            if cell_items_text := rows.nth(i).locator(locator).all_inner_texts():
                row_cells_text.append(cell_items_text)
        for row in row_cells_text:
            assert row == expected_values

    @allure.step("Check column headers")
    def check_column_headers(
        self,
        expected_headers: list,
        locator: str = TableDataMixinLocators.COLUMN_HEADERS_LOCATOR,
        ignore_order: bool = False,
    ):
        self.page.wait_for_selector(locator)
        if ignore_order:
            assert sorted(self.page.locator(locator).all_text_contents()) == sorted(expected_headers)
        else:
            expect(self.page.locator(locator)).to_have_text(expected_headers)

    @allure.step("Check that {column_header_text} column is displayed")
    def is_column_displayed(self, column_header_text: str):
        return self.page.locator(
            TableDataMixinLocators.COLUMN_IN_TABLE.format(column_header_text=column_header_text)
        ).is_visible()

    @allure.step("Check the specific section is present/absent on the dashboard")
    def check_section_is_displayed_by_name(self, section_name: str, expected_presence: bool = True):
        section_locator = self.page.locator(TableDataMixinLocators.SECTION_LOCATOR.format(section_name=section_name))
        expect(section_locator).to_be_visible() if expected_presence else expect(section_locator).to_be_hidden()
