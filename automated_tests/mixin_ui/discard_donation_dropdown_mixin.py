import allure
from playwright.sync_api import expect

from automated_tests.data.constants.column_headers.dropdown_headers import DISCARD_DONATION_DROPDOWN_EXPECTED_HEADERS
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.discard_donation_dropdown.discard_donation_dropdown import (
    DiscardDonationDropdownTableRow,
)


class DiscardDonationDropdownMixinLocators:
    DISCARD_DROPDOWN_BUTTON = "//div[contains(@col-id, 'weekly.discards')]//i[contains(@class, 'chevron-right')]"
    DONATION_DROPDOWN_BUTTON = "//div[contains(@col-id, 'weekly.donations')]//i[contains(@class, 'chevron-right')]"
    DISCARD_DONATION_DROPDOWN_MODAL = "//div[@class='v-card v-sheet theme--dark']"
    DISCARD_DONATION_DROPDOWN_HEADERS = "//div[@class='v-card v-sheet theme--dark']//div[@role='columnheader']"
    DISCARD_DONATION_DROPDOWN_ROWS = (
        "//div[@class='v-card v-sheet theme--dark']//div[contains(@class,'ag-row-position')]"
    )


class DiscardDonationDropdownMixin(TableDataMixin, BasePage):

    @allure.step("Expand discard dropdown")
    def expand_discard_dropdown(self):
        self.page.locator(DiscardDonationDropdownMixinLocators.DISCARD_DROPDOWN_BUTTON).click()
        expect(self.page.locator(DiscardDonationDropdownMixinLocators.DISCARD_DONATION_DROPDOWN_MODAL)).to_be_visible()

    @allure.step("Expand donation dropdown")
    def expand_donation_dropdown(self):
        self.page.locator(DiscardDonationDropdownMixinLocators.DONATION_DROPDOWN_BUTTON).click()
        expect(self.page.locator(DiscardDonationDropdownMixinLocators.DISCARD_DONATION_DROPDOWN_MODAL)).to_be_visible()

    @allure.step("Check column headers on discard or donation dropdown")
    def check_column_headers_on_discard_donation_dropdown(self):
        self.check_column_headers(
            expected_headers=DISCARD_DONATION_DROPDOWN_EXPECTED_HEADERS,
            locator=DiscardDonationDropdownMixinLocators.DISCARD_DONATION_DROPDOWN_HEADERS,
        )

    @allure.step("Get table data from discard or donation dropdown")
    def get_table_data_from_discard_donation_dropdown(self) -> list[DiscardDonationDropdownTableRow]:
        return self.get_center_table_data(
            data_model=DiscardDonationDropdownTableRow,
            rows_locator=DiscardDonationDropdownMixinLocators.DISCARD_DONATION_DROPDOWN_ROWS,
        )
