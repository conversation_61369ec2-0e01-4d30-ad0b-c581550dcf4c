import datetime

import allure
from playwright.sync_api import expect

from automated_tests.data.constants.date_time_constants import CURRENT_DATETIME
from automated_tests.pages_new.base_page import BasePage


class CalendarLocators:
    GENERAL_MONTH_LABEL = (
        "//div[contains(@class, '__active')]/descendant::div[@class='v-date-picker-header__value']//button"
    )
    GENERAL_YEAR_LABEL = (
        "//div[contains(@class, '__active')]/descendant::div[@class='v-date-picker-header__value']"
        "//button[text()={year}]"
    )
    YEAR_LABEL = "//div[contains(@class, '__active')]/descendant::li[text()='{year}']"
    MONTH_LABEL = "//div[contains(@class, '__active')]/descendant::div[@class='v-btn__content' and text()='{month}']"
    DAY_LABEL = "//div[contains(@class, '__active')]/descendant::button[@type='button']/div[text()='{day}']"


class CalendarMixin(BasePage):
    @allure.step("Set date in the Calendar")
    def set_date(self, date_: datetime.date):
        # click month label to open month view in calendar
        self.page.locator(CalendarLocators.GENERAL_MONTH_LABEL).click()
        # click year label to open year view in calendar
        self.page.locator(CalendarLocators.GENERAL_YEAR_LABEL.format(year=CURRENT_DATETIME.year)).click()
        # choose year
        self.page.locator(CalendarLocators.YEAR_LABEL.format(year=date_.year)).click()
        # choose month
        self.page.locator(CalendarLocators.MONTH_LABEL.format(month=date_.strftime("%b"))).click()
        # choose day
        self.page.locator(CalendarLocators.DAY_LABEL.format(day=date_.day)).click()

    @allure.step("Set month in the Calendar")
    def set_month(self, date_: datetime.date):
        self.page.locator(CalendarLocators.GENERAL_MONTH_LABEL).click()
        year_label = self.page.locator(CalendarLocators.YEAR_LABEL.format(year=date_.year))
        year_label.click()
        month_label = self.page.locator(CalendarLocators.MONTH_LABEL.format(month=date_.strftime("%b")))
        month_label.click()
        expect(self.page.locator(CalendarLocators.MONTH_LABEL.format(month=date_.strftime("%b")))).to_be_hidden()
