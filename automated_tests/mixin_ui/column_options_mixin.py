import allure

from automated_tests.pages_new.base_page import BasePage


class ColumnOptionsMixinLocators:
    GENERAL_FILTER_MENU_BUTTON = (
        "//span[text()='{column_name}']/ancestor::div[@class='ag-cell-label-container']//span[@ref='eMenu']"
    )
    COLUMN_FILTER_BUTTON = "columns"
    COLUMN_FILTER_ITEM_CHECKBOX = "//span[text()='{column_name}']/parent::div//input"
    FILTER_BUTTON = "//div[contains(@class,'ag-tabs-header')]//span[@aria-label='filter']"
    FILTER_NAME_LOCATOR = "//label[text()='{filter_name}']"
    CHECKBOX_ITEM = "//div[text()='{checkbox_name}']/parent::div/descendant::input"
    FILTER_VALUE_INPUT = "Filter..."
    FILTER_TYPE_DROPDOWN = "//div[@class='ag-picker-field-icon']"
    FILTER_TYPE_FROM_LIST = "//span[text()='{filter_type}']/parent::div"
    FILTER_ICON_NEXT_TO_COLUMN_NAME = (
        "//span[text()='{column_name}']//parent::div//span[@class='ag-icon ag-icon-filter']"
    )


class ColumnOptionsMixin(BasePage):

    @allure.step("Open General Filter Menu filter by column name")
    def open_general_menu_filter_by_column_name(self, column_name: str):
        self.page.locator(ColumnOptionsMixinLocators.GENERAL_FILTER_MENU_BUTTON.format(column_name=column_name)).click()

    @allure.step("Click on filter by columns and check filter checkbox by name")
    def check_column_filter_by_name(self, column_name: str, set_check: bool):
        self.page.get_by_label(ColumnOptionsMixinLocators.COLUMN_FILTER_BUTTON).click()
        self.page.locator(
            ColumnOptionsMixinLocators.COLUMN_FILTER_ITEM_CHECKBOX.format(column_name=column_name)
        ).set_checked(set_check)

    @allure.step("Open Filter tab and check filter by name")
    def open_filter_and_check_filter_by_name(self, filter_name: str, set_check: bool = True):
        self.open_filter_tab()
        self.page.locator(ColumnOptionsMixinLocators.FILTER_NAME_LOCATOR.format(filter_name=filter_name)).set_checked(
            set_check
        )

    @allure.step("Open filter and click on checkbox")
    def open_filter_and_click_on_checkbox(self, set_check: bool = True, checkbox_name=str):
        self.open_filter_tab()
        self.click_on_checkbox_in_filter_section(set_check=set_check, checkbox_name=checkbox_name)

    @allure.step("Click on checkbox in filter by value section")
    def click_on_checkbox_in_filter_section(self, set_check: bool = True, checkbox_name=str):
        self.page.locator(ColumnOptionsMixinLocators.CHECKBOX_ITEM.format(checkbox_name=checkbox_name)).set_checked(
            checked=set_check
        )

    @allure.step("Open filter tab")
    def open_filter_tab(self):
        self.page.locator(ColumnOptionsMixinLocators.FILTER_BUTTON).click()

    @allure.step("Filter columns that have numeric values")
    def filter_numeric_columns(
        self,
        column_name: str,
        input_value: str = None,
        filter_type: str = None,
        is_first_trial_to_open_popup: bool = True,
    ):
        self.open_general_menu_filter_by_column_name(column_name=column_name)
        if is_first_trial_to_open_popup:
            self.open_filter_tab()
        if filter_type:
            self.open_filter_type_dropdown_choose_option(filter_type=filter_type)
        if input_value:
            self.page.get_by_placeholder(ColumnOptionsMixinLocators.FILTER_VALUE_INPUT).fill(input_value)
        self.page.wait_for_selector(
            ColumnOptionsMixinLocators.FILTER_ICON_NEXT_TO_COLUMN_NAME.format(column_name=column_name)
        )

    @allure.step("Open Filter type dropdown and choose an option from list")
    def open_filter_type_dropdown_choose_option(self, filter_type: str):
        self.page.locator(ColumnOptionsMixinLocators.FILTER_TYPE_DROPDOWN).click()
        self.page.locator(ColumnOptionsMixinLocators.FILTER_TYPE_FROM_LIST.format(filter_type=filter_type)).click()
