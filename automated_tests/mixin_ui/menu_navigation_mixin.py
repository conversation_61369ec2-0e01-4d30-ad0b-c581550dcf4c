import allure
from playwright.sync_api import expect

from automated_tests.pages_new.base_page import BasePage


class MenuNavigationMixinLocators:
    MARKET_DROPDOWN = "//div[@class='country-picker']"
    MARKET_ITEM_DROPDOWN = "//div[@role='listbox']//div[text()='{name}']"
    MARKET_ITEMS_IN_DROPDOWN = "//div[@role='listbox']//div[@class='v-list-item__title']"
    CHANGELOG_BUTTON = "//span[@class='v-btn__content' and text()='Changelog']"
    MENU_SECTION_HEADERS = "//div[@class='app-navigation']//span[@class='v-btn__content' and text()]"
    COUNTRY_DROPDOWN_ARROW_BUTTON = "//div[@class='country-picker']//div[@class='v-input__append-inner']"
    # menu navigation buttons
    ADMIN_BUTTON = "//div[@class ='app-navigation']//span[normalize-space()='Admin']"
    BUYER_BUTTON = "//div[@class = 'app-navigation']//span[normalize-space()='Buyer']"
    PIMT_BUTTON = "//div[@class = 'app-navigation']//span[normalize-space()='PIMT']"
    IMT_BUTTON = "//div[@class = 'app-navigation']//span[normalize-space()='IMT']"
    INVENTORY_BUTTON = "//div[@class = 'app-navigation']//span[normalize-space()='Inventory']"
    CALCULATIONS_BUTTON = "//div[@class = 'app-navigation']//span[normalize-space()='Calculations']"
    # dropdown items links
    ADMIN_DROPDOWN_ITEMS_LINKS = "//a[contains(@id, '/admin/')]"
    BUYER_DROPDOWN_ITEMS_LINKS = "//a[contains(@id, '/buyer/')]"
    PIMT_DROPDOWN_ITEMS_LINKS = "//a[starts-with(@id, '/pimt')]"
    IMT_DROPDOWN_ITEMS_LINKS = "//a[starts-with(@id, '/imt')]"
    INVENTORY_DROPDOWN_LINKS = "//a[contains(@id, '/inventory/')]"
    CALCULATIONS_DROPDOWN_LINKS = "//a[contains(@id, '/calculation')]"
    # dropdown items
    ADMIN_DROPDOWN_ITEMS = "//a[contains(@id, 'admin')]//div[@class='v-list-item__title']"
    BUYER_DROPDOWN_ITEMS = "//a[contains(@id, '/buyer/')]//div[contains(@class, 'v-list')]"
    PIMT_DROPDOWN_ITEMS = "//a[starts-with(@id, '/pimt')]//div[@class='v-list-item__title']"
    IMT_DROPDOWN_ITEMS = "//a[starts-with(@id, '/imt')]//div[contains(@class, 'v-list')]"
    INVENTORY_DROPDOWN_ITEMS = "//a[contains(@id, '/inventory/')]//div[contains(@class, 'v-list')]"
    CALCULATIONS_DROPDOWN_ITEMS = "//a[contains(@id, '/calculation')]//div[contains(@class, 'v-list')]"
    # login locators
    AVATAR_ICON = "//div[@class = 'app-user']"
    LOGOUT_BUTTON = "//*[contains(text(), 'Logout')]"


class MenuNavigationMixin(BasePage):

    @allure.step("Change market in dropdown")
    def change_market_in_dropdown(self, market: str):
        self.page.locator(MenuNavigationMixinLocators.MARKET_DROPDOWN).click()
        self.page.locator(MenuNavigationMixinLocators.MARKET_ITEM_DROPDOWN.format(name=market)).click()
        self.wait_loading()

    @allure.step("Check 'Changelog' button is visible nad clickable")
    def check_changelog_button_is_clickable_and_visible(self):
        change_log_button = self.page.locator(MenuNavigationMixinLocators.CHANGELOG_BUTTON)
        expect(change_log_button).to_be_visible()
        expect(change_log_button).to_be_enabled()

    @allure.step("Check market items in dropdown on HomePage")
    def get_market_items_in_dropdown(self):
        self.page.locator(MenuNavigationMixinLocators.COUNTRY_DROPDOWN_ARROW_BUTTON).click()
        return self.page.locator(MenuNavigationMixinLocators.MARKET_ITEMS_IN_DROPDOWN).all_inner_texts()

    @allure.step("Get elements from Admin dropdown section")
    def get_admin_drop_down_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.ADMIN_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.ADMIN_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check Admin dropdown items are clickable")
    def check_admin_menu_rows_are_clickable(self):
        admin_rows_locator = self.page.locator(MenuNavigationMixinLocators.ADMIN_DROPDOWN_ITEMS).all()
        for locator in admin_rows_locator:
            expect(locator).to_be_enabled()

    @allure.step("Get menu headers")
    def get_menu_headers(self):
        return self.page.locator(MenuNavigationMixinLocators.MENU_SECTION_HEADERS).all_inner_texts()

    @allure.step("Get elements from Buyer dropdown section")
    def get_buyer_dropdown_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.BUYER_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.BUYER_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check Buyer dropdown items are clickable")
    def check_buyer_rows_are_clickable(self):
        admin_rows_locator = self.page.locator(MenuNavigationMixinLocators.BUYER_DROPDOWN_ITEMS).all()
        for locator in admin_rows_locator:
            expect(locator).to_be_enabled()

    @allure.step("Get elements from PIMT dropdown section")
    def get_pimt_dropdown_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.PIMT_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.PIMT_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check PIMT dropdown items are clickable")
    def check_pimt_menu_rows_are_clickable(self):
        admin_rows_locator = self.page.locator(MenuNavigationMixinLocators.PIMT_DROPDOWN_ITEMS).all()
        for locator in admin_rows_locator:
            expect(locator).to_be_enabled()

    @allure.step("Get elements from IMT dropdown section")
    def get_imt_dropdown_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.IMT_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.IMT_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check IMT dropdown items are clickable")
    def check_imt_menu_rows_are_clickable(self):
        admin_rows_locator = self.page.locator(MenuNavigationMixinLocators.IMT_DROPDOWN_ITEMS).all()
        for locator in admin_rows_locator:
            expect(locator).to_be_enabled()

    @allure.step("Get elements from Inventory dropdown section")
    def get_inventory_dropdown_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.INVENTORY_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.INVENTORY_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check Inventory dropdown items are clickable")
    def check_inventory_menu_rows_are_clickable(self):
        admin_rows_locator = self.page.locator(MenuNavigationMixinLocators.INVENTORY_DROPDOWN_ITEMS).all()
        for locator in admin_rows_locator:
            expect(locator).to_be_enabled()

    @allure.step("Get elements from Calculations dropdown section")
    def get_calculations_dropdown_rows(self) -> list[str]:
        self.page.locator(MenuNavigationMixinLocators.CALCULATIONS_BUTTON).hover()
        return self.page.locator(MenuNavigationMixinLocators.CALCULATIONS_DROPDOWN_ITEMS).all_inner_texts()

    @allure.step("Check calculations dropdown items are clickable")
    def check_calculations_menu_rows_are_clickable(self):
        calculations_rows_locator = self.page.locator(MenuNavigationMixinLocators.CALCULATIONS_DROPDOWN_ITEMS).all()
        for element in calculations_rows_locator:
            expect(element).to_be_enabled()

    @allure.step("Hover on avatar icon and check Logout button is enabled")
    def check_logout_button_is_enabled(self):
        self.page.locator(MenuNavigationMixinLocators.AVATAR_ICON).hover()
        expect(self.page.locator(MenuNavigationMixinLocators.LOGOUT_BUTTON)).to_be_enabled()

    @allure.step("Get Admin links from dropdown")
    def get_admin_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.ADMIN_DROPDOWN_ITEMS_LINKS).all()
        ]

    @allure.step("Get Buyer links from dropdown")
    def get_buyer_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.BUYER_DROPDOWN_ITEMS_LINKS).all()
        ]

    @allure.step("Get PIMT links from dropdown")
    def get_pimt_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.PIMT_DROPDOWN_ITEMS_LINKS).all()
        ]

    @allure.step("Get IMT links from dropdown")
    def get_imt_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.IMT_DROPDOWN_ITEMS_LINKS).all()
        ]

    @allure.step("Get Inventory links from dropdown")
    def get_inventory_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.INVENTORY_DROPDOWN_LINKS).all()
        ]

    @allure.step("Get Calculations links from dropdown")
    def get_calculations_dropdown_links(self):
        return [
            value.get_attribute("href")
            for value in self.page.locator(MenuNavigationMixinLocators.CALCULATIONS_DROPDOWN_LINKS).all()
        ]
