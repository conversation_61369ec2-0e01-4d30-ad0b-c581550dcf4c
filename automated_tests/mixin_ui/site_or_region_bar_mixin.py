import allure
from playwright.sync_api import expect

from automated_tests.pages_new.base_page import BasePage


class SiteOrRegionBarMixinLocators:
    SITE_REGION_BUTTON = "//span[normalize-space()='{site_or_region}']/parent::button"
    SITE_OR_REGION_ITEMS_TO_COLLECT = "//div[contains(@class, 'v-item-group')]//span[@class='v-btn__content']"
    SITE_OR_REGION_ITEM = "//span[@class='v-btn__content' and normalize-space()='{site_or_region}']"
    SITE_TOOLTIP = "//div[contains(@class,'content__active')]//span[text()='{full_site_name}']"


class SiteOrRegionBarMixin(BasePage):

    @allure.step("Choose {site_or_region} site/region")
    def select_site_or_region_from_site_bar(self, site_or_region: str):
        self.page.locator(SiteOrRegionBarMixinLocators.SITE_REGION_BUTTON.format(site_or_region=site_or_region)).click()

    @allure.step("Get all available sites/regions")
    def get_all_sites_or_regions_from_site_bar(self) -> list[str]:
        sites_regions = self.page.locator(SiteOrRegionBarMixinLocators.SITE_OR_REGION_ITEMS_TO_COLLECT).all()
        return [site_region.inner_text() for site_region in sites_regions]

    @allure.step("Check that site tooltip is displayed in site bar")
    def check_site_tooltip_is_displayed(self, site_or_region: str, full_site_name: str):
        self.page.locator(
            SiteOrRegionBarMixinLocators.SITE_OR_REGION_ITEM.format(site_or_region=site_or_region)
        ).hover()
        expect(
            self.page.locator(SiteOrRegionBarMixinLocators.SITE_TOOLTIP.format(full_site_name=full_site_name))
        ).to_be_visible()

    @allure.step("Check site is visible")
    def check_site_is_visible(self, site_or_region: str, is_visible: bool = True):
        site_locator = SiteOrRegionBarMixinLocators.SITE_REGION_BUTTON.format(site_or_region=site_or_region)
        if is_visible:
            self.page.wait_for_selector(site_locator)
            expect(self.page.locator(site_locator)).to_be_visible()
        else:
            expect(self.page.locator(site_locator)).not_to_be_visible()
