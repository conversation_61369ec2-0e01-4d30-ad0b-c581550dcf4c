import allure
from playwright.sync_api import expect

from automated_tests.data.constants.sync_constants import BLUE_ICON
from automated_tests.pages_new.base_page import BasePage


class SyncLocators:
    SYNC_BUTTON = "//div[@class='v-toolbar__content']//span[contains(@class,'badge-dot')]"
    REFRESH_DATA_BUTTON = "//div[@class='sync-actions']/button[normalize-space()='Refresh data']"
    SHOW_SYNC_LOGS = "//div[@class='sync-actions']/button[normalize-space()='Show logs']"
    SYNC_IN_PROGRESS_STATUS = "//div[@class='v-card__actions']//span[normalize-space()='Sync is in progress']"
    LOGS_ICON_STATUS = (
        "//div[@class='v-card__actions']//div[contains(@class,'sync-count')]//span[i[contains(@style,'{style}')]]"
    )
    THREE_VIEW_ROOT_BUTTON = "//div[contains(@class, 'v-treeview-node__root')]/button"
    STEP_NAME = "//div[@class='v-treeview-node__label']"


class SyncMixin(BasePage):
    @allure.step("Click on 'Launch Sync' button.")
    def launch_sync(self):
        self.page.locator(SyncLocators.SYNC_BUTTON).hover()
        self.page.wait_for_selector(SyncLocators.REFRESH_DATA_BUTTON)
        self.page.locator(SyncLocators.REFRESH_DATA_BUTTON).click()

    @allure.step("Open sync logs")
    def show_sync_logs(self):
        self.page.locator(SyncLocators.SYNC_BUTTON).hover()
        self.page.locator(SyncLocators.SHOW_SYNC_LOGS).click()

    @allure.step("Check that sync is in progress")
    def check_sync_in_progress(self):
        expect(self.page.locator(SyncLocators.SYNC_IN_PROGRESS_STATUS)).to_be_visible()
        sync_progress = self.page.locator(SyncLocators.LOGS_ICON_STATUS.format(style=BLUE_ICON))
        expect(sync_progress).to_be_visible()

    @allure.step("Open logs tree")
    def open_logs_tree_if_closed(self):
        tree_toggle = self.page.locator(SyncLocators.THREE_VIEW_ROOT_BUTTON).all()
        for item in tree_toggle:
            if "v-treeview-node__toggle--open" not in item.get_attribute("class"):
                item.click()

    @allure.step("Check step names")
    def check_step_names(self, expected_step_names: list):
        step_names = self.page.locator(SyncLocators.STEP_NAME).all_inner_texts()
        for step in set(step_names):
            assert step in expected_step_names, f"Step {step} is not in expected steps: {expected_step_names}"
