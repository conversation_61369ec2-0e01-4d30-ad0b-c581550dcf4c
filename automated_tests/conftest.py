import logging
import os
import pickle

import allure
import pytest
import requests
from allure_commons.types import AttachmentType

import init
from automated_tests.configs import (
    BASE_API_URL,
    HJ_DATABASE_DATABASE,
    HJ_DATABASE_HOST,
    HJ_DATABASE_PASSWORD,
    HJ_DATABASE_PORT,
    HJ_DATABASE_TIMEOUT,
    HJ_DATABASE_USER,
    PUBSUB_CHANNEL,
)
from automated_tests.data.constants.base_constants import CACHE_KEYS_TO_REMOVE
from automated_tests.services.db.base_db import clean_procurement_db
from procurement.core import initialize
from procurement.core.cache_utils.constants import cache
from procurement.core.cache_utils.factory import LOCAL_CACHES
from procurement.core.dates import ScmWeek
from procurement.core.pubsub.events import InvalidateCacheEvent

logger = logging.getLogger(__name__)
current_week = ScmWeek.current_week()


@pytest.fixture(autouse=True, scope="session")
def setup():
    _check_backend()
    _create_hj_db()
    initialize.init_timezone()


@pytest.fixture(autouse=True, scope="function")
def clean_db():
    clean_procurement_db()
    _clean_local_cache()
    _clean_cache()


def _check_backend():
    response: requests.Response = requests.get(BASE_API_URL + "/metrics")
    if response.status_code != 200:
        raise Exception("Backend is down")


def _clean_cache():
    keys_to_remove = CACHE_KEYS_TO_REMOVE
    for key_or_pattern in keys_to_remove:
        if key_or_pattern.endswith("*"):
            keys_by_pattern = cache.keys(pattern=key_or_pattern)
            for key in keys_by_pattern:
                cache.delete(key)
        else:
            cache.delete(key_or_pattern)


def _clean_local_cache():
    local_keys = (LOCAL_CACHES.brands.key,)
    for key in local_keys:
        cache.publish(PUBSUB_CHANNEL, pickle.dumps(InvalidateCacheEvent(key)))


def _create_hj_db():
    cwd = os.getcwd()
    init_path = os.path.dirname(os.path.dirname(os.path.abspath(init.__file__)))
    os.chdir(init_path)
    init.recreate_dbs(
        {
            "user": HJ_DATABASE_USER,
            "password": HJ_DATABASE_PASSWORD,
            "host": HJ_DATABASE_HOST,
            "port": HJ_DATABASE_PORT,
            "database": HJ_DATABASE_DATABASE,
            "timeout": HJ_DATABASE_TIMEOUT,
        }
    )
    os.chdir(cwd)


@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
    }


def pytest_exception_interact(node, call, report):
    try:
        if "page" not in getattr(node, "funcargs", {}):
            logger.info("Driver is unreachable")
            return
        if "page" in getattr(node, "funcargs", {}):
            node_driver = node.funcargs["page"]
            allure.attach(
                node_driver.screenshot(full_page=True),
                name="Test Failure Screenshot",
                attachment_type=AttachmentType.PNG,
            )
    except Exception as e:
        logger.exception("Seems like test failed before driver init. Error: %s", str(e))
