import logging

import allure
from playwright.sync_api import Page
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from procurement.core import config_utils

logger = logging.getLogger(__name__)


class BasePageLocators:
    BREADCRUMBS_TITLE = "//div[@class='v-toolbar__title']/span[@class='toolbar-title']"
    SORT_BUTTON = "//div[@ref='eLabel']/span[text()='{button_name}']"


class BasePage:
    page_url = UI_APP_URL

    def __init__(self, page: Page):
        self.page = page

    @allure.step(f"Open page by {page_url}")
    def open(self):
        self.page.goto(url=self.page_url)
        self.wait_loading()

    @allure.step("Wait loading")
    def wait_loading(self):
        self.page.wait_for_load_state("load")
        self.page.wait_for_load_state("domcontentloaded")

    @allure.step("Wait for API response by response_url")
    def wait_for_api_response(self, response_url: str):
        """
        Waits for an API response at the specified URL. This method is essential for ensuring that
        the application under test has completed its network operations before proceeding with further
        interactions or assertions.
        Args:
            response_url (str): The URL to wait for the API response. If running in a CI environment,
                                'localhost' in the URL will be replaced with 'back'.
        """
        try:
            response_url = response_url.replace("localhost", "back") if config_utils.is_ci else response_url
            with self.page.expect_response(url_or_predicate=response_url, timeout=2000):
                self.wait_loading()
        except PlaywrightTimeoutError:
            logger.info("Timeout exceeded for API response")

    @allure.step("Create breadcrumbs title")
    def check_breadcrumbs_title(self, expected_title: str):
        expect(self.page.locator(BasePageLocators.BREADCRUMBS_TITLE)).to_have_text(expected_title)

    @allure.step("Refresh page")
    def refresh_page(self):
        self.page.reload()
        self.wait_loading()

    @allure.step("Click sort button")
    def click_on_sort_button(self, button_name: str):
        self.page.locator((BasePageLocators.SORT_BUTTON.format(button_name=button_name))).click()

    @allure.step("Check that {expected_message} is present on the page")
    def check_message_is_present(self, expected_message: str):
        expect(self.page.get_by_text(expected_message)).to_be_visible()
