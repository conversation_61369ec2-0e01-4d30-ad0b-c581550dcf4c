import allure

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.calculation_column_headers import EXPECTED_PERPETUAL_GROSS_NEEDS
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.calculations.perpetual_gross_needs import (
    PerpetualGrossNeedsSummaryTableRow,
    PerpetualGrossNeedsTableRow,
)


class PerpetualGrossNeedsLocators:
    BLANK_CELLS_LOCATOR = "//div[contains(@class,'ag-cell-value') and (contains(@class,'blank'))]"


class PerpetualGrossNeedsPage(TableDropdownDataMixin, TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/calculations/dashboards/perpetual-gross-needs"

    @allure.step("Check column headers on Perpetual Gross Needs board")
    def check_perpetual_gross_needs_column_headers(self, expand: bool = True):
        self.expand_collapse_header(expand_collapse_name="Summary", expand=expand)
        self.check_column_headers(expected_headers=EXPECTED_PERPETUAL_GROSS_NEEDS)

    @allure.step("Get table data from Summary section")
    def get_summary_table_data(self) -> list[PerpetualGrossNeedsSummaryTableRow]:
        return self.get_left_table_data(data_model=PerpetualGrossNeedsSummaryTableRow)

    @allure.step("Get center table data")
    def get_perpetual_gross_needs_center_table_data(self):
        return self.get_center_table_data(data_model=PerpetualGrossNeedsTableRow)

    @allure.step("Check child blank value in Summary Section")
    def check_child_blank_values(self, expected_values):
        self.check_blank_values_on_child_rows(
            expected_values=expected_values, locator=PerpetualGrossNeedsLocators.BLANK_CELLS_LOCATOR
        )
