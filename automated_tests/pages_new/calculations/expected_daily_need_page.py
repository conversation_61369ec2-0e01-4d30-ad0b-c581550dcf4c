import allure

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.calculation_column_headers import EXPECTED_DAILY_NEEDS_HEADERS
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK_FIRST_DAY, DATE_FORMAT_1
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.calculations.expected_daily_need import (
    ExDailyNeedIngredientSummaryTableRow,
    ExpectedDailyNeedTableRow,
)


class ExpectedDailyNeedLocators:
    CELL_IN_ROW_FIRST_WEEKDAY = "//div[contains(@col-id, '_0') and not (contains(@class, 'cell-blank'))]"


class ExpectedDailyNeedPage(TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/calculations/dashboards/expected-daily-need"

    @allure.step("Check column headers on Expected Daily Needs")
    def check_expected_daily_need_column_headers(self):
        self.expand_ingredient_summary()
        self.expand_first_weekday()
        self.check_column_headers(expected_headers=EXPECTED_DAILY_NEEDS_HEADERS)

    @allure.step("Expand all columns on Expected Daily Needs page")
    def expand_ingredient_summary(self):
        self.expand_collapse_header(expand_collapse_name="Ingredient Summary", expand=True)

    @allure.step("Expand first weekday on Expected Daily Needs page")
    def expand_first_weekday(self):
        weekday = CURRENT_WEEK_FIRST_DAY.strftime("%A")
        self.expand_collapse_header(
            expand_collapse_name=weekday + " - " + CURRENT_WEEK_FIRST_DAY.strftime(DATE_FORMAT_1), expand=True
        )

    @allure.step("Get table data on Expected Daily Need page for the first weekday")
    def get_expected_daily_need_table_data_first_weekday(self) -> list[ExpectedDailyNeedTableRow]:
        return self.get_center_table_data(
            data_model=ExpectedDailyNeedTableRow, cell_locator=ExpectedDailyNeedLocators.CELL_IN_ROW_FIRST_WEEKDAY
        )

    @allure.step("Get summary table data on Expected Daily Need page")
    def get_ingredient_summary_table_data(self) -> list[ExDailyNeedIngredientSummaryTableRow]:
        return self.get_left_table_data(data_model=ExDailyNeedIngredientSummaryTableRow)
