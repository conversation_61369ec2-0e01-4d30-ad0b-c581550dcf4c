import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import (
    PACKAGING_DEPLETION_CONSOLIDATED_VIEW_EXPECTED_HEADERS,
    PACKAGING_DEPLETION_HJ_EXPECTED_COLUMN_HEADERS,
    PACKAGING_DEPLETION_MANUAL_EXPECTED_COLUMN_HEADERS,
)
from automated_tests.data.constants.date_time_constants import (
    CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION,
    CURRENT_WEEK_FIRST_DAY,
)
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.mixin_ui.comment_mixin import CommentMixin
from automated_tests.mixin_ui.consolidated_view_mixin import ConsolidatedViewMixin
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.mixin_ui.toolbar_mixin import ToolBarMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.packaging_depletion import (
    PackagingDepletionIngredientSummaryTableRow,
    PackagingDepletionTableRow,
)
from procurement.constants.hellofresh_constant import ReceiveInputType


class PackagingDepletionLocators:
    SKU_NAME_CELL = "//div[@col-id='sku']/preceding::div[text()='{sku_name}']"
    SKU_NAME_COMMENT_POPUP = (
        "//div[text()='{sku_name}']/parent::div/following-sibling::div//div[@class='ag-single-comment']"
    )
    ON_HAND_ACTUAL_VALUE = "//div[@col-id='onHand_{day_index}']//span[@class='chip']"
    ON_HAND_PROJECTED_VALUE = "//div[@col-id='onHandProjected_{day_index}']//span[@class='chip']"
    ON_HAND_ACTUAL_PROJECTED_VALUE = "//div[@col-id='onHandMinOnHandProjected_{day_index}']//span[@class='chip']"
    ON_HAND_OVERRIDE_VALUE = "//div[@col-id='onHandOverride_{day_index}']//span[@class='chip']"
    ON_HAND_OVERRIDE_INPUT = "//div[@col-id='onHandOverride_{day_index}']//input[@type='text']"
    INCOMING_POS_VALUE = "//div[@col-id='incoming_{day_index}']//span[@class='chip']"
    INCOMING_POS_OVERRIDE_VALUE = "//div[@col-id='incomingOverride_{day_index}']//span[@class='chip']"
    INCOMING_POS_OVERRIDE_INPUT = "//div[@col-id='incomingOverride_{day_index}']//input[@type='text']"
    SUPPLEMENT_NEED_VALUE = "//div[@col-id='supplementNeed']//span[@class='chip']"


class PackagingDepletionPage(
    QuarterOrWeekDropdownMixin,
    TableDropdownDataMixin,
    ConsolidatedViewMixin,
    CommentMixin,
    TableDataMixin,
    ToolBarMixin,
    BasePage,
):
    page_url = UI_APP_URL + "/#/imt/dashboards/packaging-depletion"

    @allure.step("Get Sku name comment on Packaging Depletion")
    def get_sku_name_comment_on_packaging_depl(self, sku: TestSku):
        return self.get_sku_name_comment(
            sku=sku,
            sku_name_cell_locator=PackagingDepletionLocators.SKU_NAME_CELL,
            sku_name_comment_locator=PackagingDepletionLocators.SKU_NAME_COMMENT_POPUP,
        )

    @allure.step("Expand Ingredient Summary, Status and Status sections")
    def expand_all_headers(self, expand: bool = True):
        self.expand_collapse_header(expand_collapse_name="Ingredient Summary", expand=expand)
        self.expand_collapse_header(expand_collapse_name="Status", expand=expand)
        self.expand_all_days()

    @allure.step("Expand Daily Needs section")
    def expand_all_days(self, expand: bool = True):
        for day_header_name in CURRENT_WEEK_DAY_NAMES_PACKAGING_DEPLETION:
            self.expand_collapse_header(expand_collapse_name=day_header_name, expand=expand)

    @allure.step("Check Packaging Depletion column headers")
    def check_packaging_depletion_column_headers(
        self, site_config: TestSiteConfig = None, is_consolidated: bool = False
    ):
        if is_consolidated:
            expected_headers = PACKAGING_DEPLETION_CONSOLIDATED_VIEW_EXPECTED_HEADERS
        if site_config:
            self.expand_all_headers()
            expected_headers = (
                PACKAGING_DEPLETION_HJ_EXPECTED_COLUMN_HEADERS
                if site_config.receiving_type == ReceiveInputType.HIGH_JUMP
                else PACKAGING_DEPLETION_MANUAL_EXPECTED_COLUMN_HEADERS
            )
        self.check_column_headers(expected_headers=expected_headers)

    @allure.step("Check consolidated Packaging Depletion column headers")
    def check_consolidated_column_headers(self):
        self.check_column_headers(expected_headers=PACKAGING_DEPLETION_CONSOLIDATED_VIEW_EXPECTED_HEADERS)

    @allure.step("Get data from Ingredient Summary section")
    def get_ingredient_summary_table_data(
        self, expand: bool = True
    ) -> list[PackagingDepletionIngredientSummaryTableRow]:
        self.expand_collapse_header("Ingredient Summary", expand=expand)
        return self.get_left_table_data(data_model=PackagingDepletionIngredientSummaryTableRow)

    @allure.step("Get data from Status, Daily Needs and Weekly Overview sections")
    def get_packaging_depletion_center_data(self, expand: bool = True) -> list[PackagingDepletionTableRow]:
        self.expand_collapse_header("Status", expand=expand)
        self.expand_collapse_header(f"Wednesday PM - {str(CURRENT_WEEK_FIRST_DAY)}", expand=expand)
        return self.get_center_table_data(PackagingDepletionTableRow)

    @allure.step("Check all on hand flag colors")
    def check_all_on_hand_flag_colors(self, day_index: int, expected_color: str):
        for locator in [
            PackagingDepletionLocators.ON_HAND_ACTUAL_VALUE,
            PackagingDepletionLocators.ON_HAND_PROJECTED_VALUE,
            PackagingDepletionLocators.ON_HAND_ACTUAL_PROJECTED_VALUE,
        ]:
            expect(self.page.locator(locator.format(day_index=day_index))).to_have_css(
                "background-color", expected_color
            )

    @allure.step("Check all 'hand' and 'incoming' flag colors")
    def check_all_hand_and_incoming_flag_colors(self, day_index: int, expected_color: str):
        for locator in [
            PackagingDepletionLocators.ON_HAND_ACTUAL_VALUE,
            PackagingDepletionLocators.ON_HAND_PROJECTED_VALUE,
            PackagingDepletionLocators.ON_HAND_ACTUAL_PROJECTED_VALUE,
            PackagingDepletionLocators.INCOMING_POS_VALUE,
            PackagingDepletionLocators.INCOMING_POS_OVERRIDE_VALUE,
        ]:
            expect(self.page.locator(locator.format(day_index=day_index))).to_have_css(
                "background-color", expected_color
            )

    @allure.step("Check 'On Hand (Actual)' and 'On Hand (Projected)' flag colors")
    def check_on_hand_actual_and_projected_flag_colors(self, day_index: int, expected_color: str):
        for locator in [
            PackagingDepletionLocators.ON_HAND_ACTUAL_VALUE,
            PackagingDepletionLocators.ON_HAND_PROJECTED_VALUE,
        ]:
            expect(self.page.locator(locator.format(day_index=day_index))).to_have_css(
                "background-color", expected_color
            )

    @allure.step("Check Supplement Needs flag color and text")
    def check_supplement_need_flag_color_and_text(self, expected_color: str, expected_text: str):
        supplement_need_column = self.page.locator(PackagingDepletionLocators.SUPPLEMENT_NEED_VALUE)
        expect(supplement_need_column).to_have_css("background-color", expected_color)
        expect(supplement_need_column).to_have_text(expected_text)

    @allure.step("Add/edit value in On Hand Override input field")
    def add_edit_on_hand_override_value(self, day_index: int, value: str):
        self.page.locator(PackagingDepletionLocators.ON_HAND_OVERRIDE_VALUE.format(day_index=day_index)).dblclick()
        self.page.locator(PackagingDepletionLocators.ON_HAND_OVERRIDE_INPUT.format(day_index=day_index)).fill(
            value=value
        )
        self.page.keyboard.press("Enter")

    @allure.step("Add/edit value in Incoming Po Override input field")
    def add_edit_incoming_po_override_value(self, day_index: int, value: str):
        self.page.locator(PackagingDepletionLocators.INCOMING_POS_OVERRIDE_VALUE.format(day_index=day_index)).dblclick()
        self.page.locator(PackagingDepletionLocators.INCOMING_POS_OVERRIDE_INPUT.format(day_index=day_index)).fill(
            value=value
        )
        self.page.keyboard.press("Enter")
