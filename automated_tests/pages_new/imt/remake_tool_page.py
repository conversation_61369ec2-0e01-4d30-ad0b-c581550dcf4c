import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import REMAKE_TOOL_EXPECTED_HEADERS
from automated_tests.data.constants.date_time_constants import CURRENT_WEEK
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.mixin_ui.comment_mixin import CommentMixin
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.mixin_ui.sku_dropdown_mixin import SkuDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.remake_tool import (
    RemakeToolIngredientRequirementsTableRow,
    RemakeToolIngredientSummaryTableRow,
    RemakeToolTableRow,
)
from automated_tests.utils import ui_utils
from procurement.core.dates import ScmWeek


class RemakeToolLocators:
    REMAKE_TOOL_LEFT_ROWS = "//div[@id='ing-depl-grid']//div[@class='ag-pinned-left-cols-container']/child::div"
    REMAKE_TOOL_CENTER_ROWS = "//div[@id='ing-depl-grid']//div[@class='ag-center-cols-container']/child::div"
    REMAKE_TOOL_COLUMN_HEADERS = "//div[@id='ing-depl-grid']//span[@class = 'ag-header-cell-text' and text()]"
    CLEAR_REMAKE_DATA_BUTTON = "//span[normalize-space()='Clear Remake Data']"

    # Ingredient requirements dashboard
    MEALS_LEFT_TABLE_DATA = "//div[@class='remake-grid']//div[@class='ag-pinned-left-cols-container']/child::div"
    MEALS_CENTER_TABLE_DATA = "//div[@class='remake-grid']//div[@class='ag-center-cols-container']/child::div"
    MEALS_VALUE = "//div[@class='remake-grid']//div[@class='ag-center-cols-container']//div[@row-id={row}]"
    MEALS_VALUE_INPUT = "//div[@class='remake-grid']//div[@class='ag-center-cols-container']//div[@row-id={row}]//input"
    DAYS_DROPDOWN = "//div[@class='v-select__slot']//div[contains(@class, 'v-select') and text()='{day}']"
    LIST_OF_DAYS_IN_DROPDOWN = "//div[@role='listbox']//div[@class='v-list-item__title']"


class RemakeToolPage(
    CommentMixin, SkuDropdownMixin, QuarterOrWeekDropdownMixin, TableDropdownDataMixin, TableDataMixin, BasePage
):
    page_url = UI_APP_URL + "/#/imt/dashboards/remake-tool"

    @allure.step("Get Ingredient Summary table data on Remake Tool page")
    def get_ingredient_summary_table_data(
        self, week: ScmWeek = CURRENT_WEEK, expand: bool = True
    ) -> list[RemakeToolIngredientSummaryTableRow]:
        self.expand_ingredient_summary_section(expand=expand, week=week)
        return self.get_left_table_data(
            rows_locator=RemakeToolLocators.REMAKE_TOOL_LEFT_ROWS, data_model=RemakeToolIngredientSummaryTableRow
        )

    @allure.step("Get center table data on Remake Tool page")
    def get_remake_tool_center_table_data(self) -> list[RemakeToolTableRow]:
        return self.get_center_table_data(
            rows_locator=RemakeToolLocators.REMAKE_TOOL_CENTER_ROWS, data_model=RemakeToolTableRow
        )

    @allure.step("Check column headers on Remake Tool page")
    def check_remake_tool_column_headers(self, week: ScmWeek = CURRENT_WEEK, expand: bool = True):
        self.expand_ingredient_summary_section(expand=expand, week=week)
        self.check_column_headers(
            expected_headers=REMAKE_TOOL_EXPECTED_HEADERS, locator=RemakeToolLocators.REMAKE_TOOL_COLUMN_HEADERS
        )

    @allure.step("Expand Ingredient Summary section")
    def expand_ingredient_summary_section(self, week: ScmWeek = CURRENT_WEEK, expand: bool = True):
        self.expand_collapse_header(
            expand_collapse_name=f"Week {ui_utils.get_week_str(week=week).lstrip('W')} Ingredient Summary",
            expand=expand,
        )

    @allure.step("Get Ingredient Requirement left table data on Remake Tool page")
    def get_ingredient_requirements_left_table_data(self) -> list[RemakeToolIngredientRequirementsTableRow]:
        return self.get_left_table_data(
            rows_locator=RemakeToolLocators.MEALS_LEFT_TABLE_DATA, data_model=RemakeToolIngredientRequirementsTableRow
        )

    @allure.step("Get Ingredient Requirement center table data on Remake Tool page")
    def get_ingredient_requirements_center_table_data(self) -> list[RemakeToolIngredientRequirementsTableRow]:
        return self.get_center_table_data(
            rows_locator=RemakeToolLocators.MEALS_CENTER_TABLE_DATA, data_model=RemakeToolIngredientRequirementsTableRow
        )

    @allure.step("Click on the 'Clear Remake Data' button on Remake Tool")
    def click_on_clear_remake_data_button(self):
        self.page.locator(RemakeToolLocators.CLEAR_REMAKE_DATA_BUTTON).click()

    @allure.step("Check that all days in days dropdown are presented and enabled on Remake Tool")
    def check_clickable_days_in_dropdown(self, day: str):
        self.page.locator(RemakeToolLocators.DAYS_DROPDOWN.format(day=day)).click()
        list_of_days = self.page.locator(RemakeToolLocators.LIST_OF_DAYS_IN_DROPDOWN).all()
        for item in list_of_days:
            expect(item).to_be_enabled()

    @allure.step("Edit or add meals values on Remake Tool")
    def edit_or_add_meals_value(self, row_id: int, value: str):
        self.page.locator(RemakeToolLocators.MEALS_VALUE.format(row=row_id)).dblclick()
        input_field = self.page.locator(RemakeToolLocators.MEALS_VALUE_INPUT.format(row=row_id))
        input_field.fill(value)
        input_field.press("Enter")

    @allure.step("Get PO number comment on Remake Tool page")
    def get_po_number_comment_on_remake_tool(self, po: TestPurchaseOrder):
        self.expand_collapse_sku_dropdown_view(sku=po.first_line().sku, expand=True)
        return self.get_po_number_comment(po=po)
