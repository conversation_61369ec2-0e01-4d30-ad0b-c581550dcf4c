import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.base_constants import FactorBrand
from automated_tests.data.constants.column_headers.imt_column_headers import (
    PRODUCTION_KIT_GUIDE_EXPECTED_HEADERS,
    PRODUCTION_KIT_GUIDE_EXPECTED_HEADERS_FACTOR,
)
from automated_tests.data.models.imt_config import TestSiteConfig
from automated_tests.data.models.sku import TestSku
from automated_tests.mixin_ui.comment_mixin import CommentMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin, TableDataMixinLocators
from automated_tests.mixin_ui.toolbar_mixin import ToolBarMixin
from automated_tests.pages_new.models.imt.production_kit_guide import (
    ProductionKitGuideTableRow,
    ProductionKitGuideTableRowFactor,
)


class ProductionKitGuideLocators:
    SKU_NAME_CELL = "//div[@col-id='sku' and text()='{value}']/preceding::div[@col-id='skuName' and @role='gridcell']"
    SKU_NAME_COMMENT_POPUP = "//*[text() = '{value}']/ancestor::div//div[@class='ag-single-comment']"


class ProductionKitGuidePage(CommentMixin, QuarterOrWeekDropdownMixin, ToolBarMixin, TableDataMixin):
    page_url = UI_APP_URL + "/#/imt/dashboards/production-kit-guide"

    @allure.step("Check column headers on Production Kit Guide Page")
    def check_pkg_column_headers(self, site_config: TestSiteConfig):
        expected_headers = (
            PRODUCTION_KIT_GUIDE_EXPECTED_HEADERS_FACTOR
            if site_config.brand.code == FactorBrand.FJ.value.code
            else PRODUCTION_KIT_GUIDE_EXPECTED_HEADERS
        )
        self.check_column_headers(expected_headers=expected_headers)

    @allure.step("Get table data on Production Kit Guide Page")
    def get_pkg_table_data(self, site_config: TestSiteConfig):
        data_model = (
            ProductionKitGuideTableRowFactor
            if site_config.brand.code == FactorBrand.FJ.value.code
            else ProductionKitGuideTableRow
        )
        return self.get_center_table_data(data_model=data_model)

    @allure.step("Remove column, save state and check that column was removed on Production Kit Guide Page")
    def remove_column_and_save_state(self, column_name: str, set_check: bool, state_name: str = None):
        self.open_general_menu_filter_by_column_name(column_name=column_name)
        self.check_column_filter_by_name(column_name=column_name, set_check=set_check)
        self.save_state(state_name=state_name)
        expect(self.page.locator(TableDataMixinLocators.COLUMN_HEADERS_LOCATOR)).not_to_contain_text([column_name])

    @allure.step("Get SKU Name comment")
    def get_sku_name_comment_on_pkg(self, sku: TestSku):
        return self.get_sku_name_comment(
            sku=sku,
            sku_name_cell_locator=ProductionKitGuideLocators.SKU_NAME_CELL,
            sku_name_comment_locator=ProductionKitGuideLocators.SKU_NAME_COMMENT_POPUP,
        )
