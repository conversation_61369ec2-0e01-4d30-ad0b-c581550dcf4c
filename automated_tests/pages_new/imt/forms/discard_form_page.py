import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import DISCARD_FORM_EXPECTED_COLUMN_HEADERS
from automated_tests.data.constants.error_messages_constants import HJ_DISCARDS_CANT_BE_EDITED_ERROR_MESSAGE
from automated_tests.mixin_ui.manual_form_mixin import ManualFormMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.discard_form import DiscardFormTable


class DiscardFormLocators:
    BRAND_FIELD = "//div[@row-id='{row_id}']/div[@col-id='brand']"
    SITE_FIELD = "//div[@row-id='{row_id}']/div[@col-id='site']"
    DATE_FIELD = "//div[@row-id='{row_id}']/div[@col-id='date']"
    TIME_FIELD = "//div[@row-id='{row_id}']/div[@col-id='time']"
    SKU_NAME_FIELD = "//div[@row-id='{row_id}']/div[@col-id='skuName']"
    SKU_NAME_INPUT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='skuName']//input[@type='text']"
    NUM_UNITS_FIELD = "//div[@row-id='{row_id}']/div[@col-id='numUnits']"
    DISCARD_QUANTITY_FIELD = "//div[@row-id='{row_id}']/div[@col-id='quantity']"
    COMMENT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='comment']"
    LIST_SITES = "//div[contains(@class,'select-row')]//span[text() = '{site}']"
    LIST_BRANDS = "//div[contains(@class,'select-row')]//span[text() ='{brand}']"
    OPENED_LIST_OF_VALUES = "//div[contains(@class,'ag-virtual-list')]//div[contains(@class,'select-row')]"


class DiscardPage(ManualFormMixin, QuarterOrWeekDropdownMixin, TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/imt/fulfillment-forms/discard-form"

    @allure.step("Check column headers on Discard Form")
    def check_discard_column_headers(self):
        self.check_column_headers(expected_headers=DISCARD_FORM_EXPECTED_COLUMN_HEADERS)

    @allure.step("Get table data on Discard Form")
    def get_discard_table_data(self) -> list[DiscardFormTable]:
        return self.get_center_table_data(data_model=DiscardFormTable)

    @allure.step("Add Records to Discard Form")
    def add_records(self, form_locators_dict: dict, row_id: int = 0):
        locator_mapping = {
            DiscardFormLocators.SKU_NAME_FIELD: self.fill_sku_name_field,
            DiscardFormLocators.BRAND_FIELD: self.fill_brand_field,
            DiscardFormLocators.SITE_FIELD: self.fill_site_field,
        }
        for locator, value in form_locators_dict.items():
            if filed_filler := locator_mapping.get(locator):
                filed_filler(locator, value, row_id)
                continue

            self.page.locator(locator.format(row_id=row_id)).dblclick()
            self.send_keys_to_form(locator=locator, value=value, row_id=row_id)

    @allure.step("Edit records on Discard Form")
    def editing_records(self, edit_locators_dict: dict, row_id: int = 0, response_url: str = None):
        for locator, value in edit_locators_dict.items():
            self.page.locator(locator.format(row_id=row_id)).dblclick()
            self.send_keys_to_form(locator=locator, value=value, row_id=row_id)
            if response_url:
                self.wait_for_api_response(response_url=response_url)

    @allure.step("Fill SKU Name field")
    def fill_sku_name_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(DiscardFormLocators.SKU_NAME_INPUT_FIELD.format(row_id=row_id)).fill(value)
        self.page.keyboard.press("Enter")

    @allure.step("Fill Site field")
    def fill_site_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(DiscardFormLocators.LIST_SITES.format(site=value)).click()

    @allure.step("Fill Brand field")
    def fill_brand_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(DiscardFormLocators.LIST_BRANDS.format(brand=value)).click()

    @allure.step("Get sku name background color")
    def check_sku_name_background_color(self, expected_color: str, row_id: int = 0):
        expect(self.page.locator(DiscardFormLocators.SKU_NAME_FIELD.format(row_id=row_id))).to_have_css(
            name="background-color", value=expected_color
        )

    @allure.step("Get all values text from the list of sites")
    def get_all_values_text_from_list_of_sites(self, row_id: int = 0):
        self.page.locator(DiscardFormLocators.SITE_FIELD.format(row_id=row_id)).dblclick()
        return self.page.locator(DiscardFormLocators.OPENED_LIST_OF_VALUES).all_inner_texts()

    @allure.step("Get all values text from the list of brands")
    def get_all_values_text_from_list_of_brands(self, row_id: int = 0):
        self.page.locator(DiscardFormLocators.BRAND_FIELD.format(row_id=row_id)).dblclick()
        return self.page.locator(DiscardFormLocators.OPENED_LIST_OF_VALUES).all_inner_texts()

    @allure.step("Check alert message 'HJ Discards can't be edited' is displayed'")
    def check_alert_message_hj_discards_cant_be_edited_is_displayed(self):
        expect(self.page.get_by_text(HJ_DISCARDS_CANT_BE_EDITED_ERROR_MESSAGE).first).to_be_visible()
