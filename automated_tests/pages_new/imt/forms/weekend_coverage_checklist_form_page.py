import allure

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import (
    WEEKEND_COVERAGE_CHECKLIST_EXPECTED_COLUMN_HEADERS,
)
from automated_tests.data.models.po import TestPurchaseOrder
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.ics_ticket_mixin import IcsTicketMixin
from automated_tests.mixin_ui.manual_form_mixin import ManualFormMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.weekend_coverage_checklist import WeekendCoverageChecklistFormTable

WEEKEND_COVERAGE_CHECKLIST_PAGE_TITLE = "IMT | Weekend Coverage Checklist"


class WccFormLocators:
    PO_NUMBER_FIELD = "//div[@row-id='{row_id}']/div[@col-id='poNumber']"
    PO_NUMBER_INPUT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='poNumber']//input[@type='text']"
    PO_NUMBER_ARROW_BUTTON = "//span[normalize-space()='{po_number}']//i[contains(@class,'chevron-right')]"
    SKU_NAME_FIELD = "//div[@row-id='{row_id}']/div[@col-id='skuName']"
    SKU_NAME_INPUT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='skuName']//input[@type='text']"
    FOB_PICK_UP_DATE = "//div[@row-id='{row_id}']/div[@col-id='fobPickUpDate']"
    PO_LANDING_DAY_FIELD = "//div[@row-id='{row_id}']/div[@col-id='poLandingDay']"
    PRODUCTION_DAY_AFFECTED_FIELD = "//div[@row-id='{row_id}']/div[@col-id='productionDayAffected']"
    TO_CHECK_FIELD = "//div[@row-id='{row_id}']/div[@col-id='toCheck']"
    CONTACT_NAME_FIELD = "//div[@row-id='{row_id}']/div[@col-id='contactNameVendorCarrier']"
    EMAIL_PHONE_FIELD = "//div[@row-id='{row_id}']/div[@col-id='emailPhone']"
    BACK_UP_VENDOR_FIELD = "//div[@row-id='{row_id}']/div[@col-id='backUpVendor']"
    STATUS_FIELD = "//div[@row-id='{row_id}']/div[@col-id='status']"
    COMMENT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='comment']"

    FOB_PICK_UP_DATES_CALENDAR = "//div[contains(@class,'v-date-picker-table')]//div[text()='{date}']"
    PO_LANDING_DAY_LIST = "//div[@role='listbox']//span[text()='{po_landing_day}']"
    PRODUCTION_DAY_AFFECTED_LIST = "//div[@role='listbox']//span[text()='{production_day}']"
    STATUS_LIST = "//div[contains(@class,'list-container')]//span[text()='{status}']"
    OPENED_LIST_OF_VALUES = "//div[contains(@class,'v-list-item v-list-item--link')]"
    ICS_TICKET_BUTTON = "//div[normalize-space()='{value}']//button[contains(@class,'ticket')]//i"
    ICS_TICKET_BUTTON_WITH_SKU_NAME = (
        "//div[text()='{sku_name}']/parent::div//div[normalize-space()='{value}']//button[contains(@class,'ticket')]//i"
    )


class WeekendCoverageChecklistPage(
    ManualFormMixin, QuarterOrWeekDropdownMixin, IcsTicketMixin, TableDropdownDataMixin, BasePage
):
    page_url = UI_APP_URL + "/#/imt/procurement-manual-forms/weekend-coverage-checklist"

    @allure.step("Get all values text from the list of sites")
    def get_all_values_text_from_list_of_pos(self, response_url: str, row_id: int = 0) -> list[str]:
        po_field_locator = self.page.locator(WccFormLocators.PO_NUMBER_FIELD.format(row_id=row_id))
        po_field_locator.dblclick()
        self.wait_for_api_response(response_url=response_url)
        self.page.keyboard.press("ArrowDown")
        return self.page.locator(WccFormLocators.OPENED_LIST_OF_VALUES).all_inner_texts()

    @allure.step("Add records to WCC Form")
    def add_records(self, form_locators_dict: dict, row_id: int = 0):
        locator_mapping = {
            WccFormLocators.PO_NUMBER_FIELD: self.fill_po_number_field,
            WccFormLocators.SKU_NAME_FIELD: self.fill_sku_name_field,
            WccFormLocators.PO_LANDING_DAY_FIELD: self.fill_po_landing_field,
            WccFormLocators.PRODUCTION_DAY_AFFECTED_FIELD: self.fill_production_day_affected_field,
        }
        for locator, value in form_locators_dict.items():
            if filed_filler := locator_mapping.get(locator):
                filed_filler(locator=locator, value=value, row_id=row_id)
                continue

            self.page.locator(locator.format(row_id=row_id)).dblclick()
            self.send_keys_to_form(locator=locator, value=value, row_id=row_id)

    @allure.step("Fill PO number field")
    def fill_po_number_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(WccFormLocators.PO_NUMBER_INPUT_FIELD.format(row_id=row_id)).fill(value=value)
        self.page.keyboard.press("ArrowDown")
        self.page.keyboard.press("Enter")

    @allure.step("Fill SKU name field")
    def fill_sku_name_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(WccFormLocators.SKU_NAME_INPUT_FIELD.format(row_id=row_id)).fill(value=value)
        self.page.keyboard.press("ArrowDown")
        self.page.keyboard.press("Enter")

    @allure.step("Fill Fob pick up field")
    def fill_fob_pick_up_date(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).fill(value=value)
        self.page.locator(WccFormLocators.FOB_PICK_UP_DATES_CALENDAR.format(date=value)).click()

    @allure.step("Fill po landing field")
    def fill_po_landing_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(WccFormLocators.PO_LANDING_DAY_LIST.format(po_landing_day=value)).click()

    @allure.step("Fill production day affected field")
    def fill_production_day_affected_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(WccFormLocators.PRODUCTION_DAY_AFFECTED_LIST.format(production_day=value)).click()

    @allure.step("Fill status field")
    def fill_status_field(self, locator: str, value: str, row_id: int = 0):
        self.page.locator(locator.format(row_id=row_id)).dblclick()
        self.page.locator(WccFormLocators.STATUS_LIST.format(status=value)).click()

    @allure.step("Edit records on WCC Form")
    def edit_records(self, edit_locators_dict: dict, response_url: str, row_id: int = 0):
        locator_mapping = {
            WccFormLocators.PO_LANDING_DAY_FIELD: self.fill_po_landing_field,
            WccFormLocators.PRODUCTION_DAY_AFFECTED_FIELD: self.fill_production_day_affected_field,
            WccFormLocators.STATUS_FIELD: self.fill_status_field,
        }
        for locator, value in edit_locators_dict.items():
            if filed_filler := locator_mapping.get(locator):
                filed_filler(locator=locator, value=value, row_id=row_id)
                self.wait_for_api_response(response_url=response_url)
                continue

            self.page.locator(locator.format(row_id=row_id)).dblclick()
            self.send_keys_to_form(locator=locator, value=value, row_id=row_id)
            self.wait_for_api_response(response_url=response_url)

    @allure.step("Check column headers on WCC Form")
    def check_weekend_checklist_column_headers(self):
        self.check_column_headers(expected_headers=WEEKEND_COVERAGE_CHECKLIST_EXPECTED_COLUMN_HEADERS)

    @allure.step("Get table data on WCC Form")
    def get_weekend_checklist_table_data(self) -> list[WeekendCoverageChecklistFormTable]:
        return self.get_center_table_data(data_model=WeekendCoverageChecklistFormTable)

    @allure.step("Open PO dropdown on WCC")
    def click_on_po_number_arrow_btn(self, po: TestPurchaseOrder):
        self.page.locator(WccFormLocators.PO_NUMBER_ARROW_BUTTON.format(po_number=po.po_number)).click()

    @allure.step("Open ICS ticket dropdown by po")
    def open_ics_ticket_dropdown_by_po_number(self, po: TestPurchaseOrder):
        self.open_ics_ticket_dropdown_by_value(
            sku_code_or_po_number=po.po_number, ics_button_locator=WccFormLocators.ICS_TICKET_BUTTON
        )

    @allure.step("Open ICS ticket popup with sku names")
    def open_ics_ticket_dropdown_with_sku_names(self, po_number: str, sku_name: str):
        ticket_btn = self.page.locator(
            WccFormLocators.ICS_TICKET_BUTTON_WITH_SKU_NAME.format(sku_name=sku_name, value=po_number)
        )
        ticket_btn.click()
