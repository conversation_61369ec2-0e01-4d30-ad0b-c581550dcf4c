import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import (
    PROD_NEED_INGREDIENTS_FACTOR_HEADERS,
    PROD_NEED_INGREDIENTS_HEADERS,
    PROD_NEED_INGREDIENTS_HEADERS_BRAND_CONSOLIDATED_VIEW,
    PROD_NEED_INGREDIENTS_HEADERS_SHIFT_LEVEL_VIEW,
)
from automated_tests.mixin_ui.comment_mixin import CommentMixin
from automated_tests.mixin_ui.consolidated_view_mixin import ConsolidatedViewMixin
from automated_tests.mixin_ui.quarter_and_week_dropdown import QuarterOrWeekDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.mixin_ui.warehouse_and_brand_dropdown import WarehouseOrBrandDropdownMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.prod_need_ingredients import (
    ProdNeedIngredientsCenterTableRow,
    ProdNeedIngredientsSummaryTableRow,
)


class ProductionNeedIngredientsLocators:
    SHIFT_LEVEL_VIEW_RADIO_BTN = "//label[text()='Shift Level']"
    DAY_SHIFT_COLUMN = "//div[@class='ag-header-cell-label']//span[text()='Day Shift']"
    DAYS_HEADERS = "//div[@Class='ag-header-viewport']//span[@class = 'ag-header-group-text']"


class ProductionNeedIngredientsPage(
    WarehouseOrBrandDropdownMixin,
    QuarterOrWeekDropdownMixin,
    ConsolidatedViewMixin,
    CommentMixin,
    TableDataMixin,
    BasePage,
):
    page_url = UI_APP_URL + "/#/imt/dashboards/production-need-ingredients"

    @allure.step("Get ingredient summary table data")
    def get_ingredient_summary_table_data(self) -> list[ProdNeedIngredientsSummaryTableRow]:
        return self.get_left_table_data(data_model=ProdNeedIngredientsSummaryTableRow)

    @allure.step("Get prod need ingredients center data")
    def get_prod_need_ingredients_center_data(self) -> list[ProdNeedIngredientsCenterTableRow]:
        self.expand_collapse_header("Wednesday 1", expand=True)
        return self.get_center_table_data(data_model=ProdNeedIngredientsCenterTableRow)

    @allure.step("Check column headers on production needs")
    def check_column_headers_on_production_needs(
        self, enabled_shift_level: bool = False, factor_brand: bool = False, brand_consolidated_view: bool = False
    ):
        if factor_brand:
            expected_headers = PROD_NEED_INGREDIENTS_FACTOR_HEADERS
        elif brand_consolidated_view:
            expected_headers = PROD_NEED_INGREDIENTS_HEADERS_BRAND_CONSOLIDATED_VIEW
        else:
            expected_headers = (
                PROD_NEED_INGREDIENTS_HEADERS_SHIFT_LEVEL_VIEW if enabled_shift_level else PROD_NEED_INGREDIENTS_HEADERS
            )
        self.check_column_headers(expected_headers=expected_headers)

    @allure.step("Enable/disable shift level view")
    def enable_disable_shift_level_view(self, enable: bool = True):
        self.page.locator(ProductionNeedIngredientsLocators.SHIFT_LEVEL_VIEW_RADIO_BTN).set_checked(checked=enable)

    @allure.step("Check day shift level column present")
    def check_day_shift_level_column_present(self):
        day_shift_locator = self.page.locator(ProductionNeedIngredientsLocators.DAY_SHIFT_COLUMN).all()
        for item in day_shift_locator:
            expect(item).to_be_visible()

    @allure.step("Get aria selected attribute from Shift Level view radio button")
    def check_shift_level_toggle_enabled(self):
        expect(self.page.locator(ProductionNeedIngredientsLocators.SHIFT_LEVEL_VIEW_RADIO_BTN)).to_be_checked()

    @allure.step("Get Days Headers on prod needs")
    def get_days_headers(self):
        return [
            value
            for value in self.page.locator(ProductionNeedIngredientsLocators.DAYS_HEADERS).all_inner_texts()
            if value != ""
        ]
