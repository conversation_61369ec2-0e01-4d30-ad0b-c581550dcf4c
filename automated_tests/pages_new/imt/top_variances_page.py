import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.imt_column_headers import TOP_VARIANCES_EXPECTED_HEADERS
from automated_tests.mixin_ui.column_options_mixin import ColumnOptionsMixin
from automated_tests.mixin_ui.consolidated_view_mixin import ConsolidatedViewMixin
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.site_or_region_bar_mixin import SiteOrRegionBarMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.imt.top_variances import (
    TopVariancesScrolledTableRow,
    TopVariancesSummaryPinnedTableRow,
)


class TopVariancesLocators:
    ALL_POS_DROPDOWN = "//span[text()='All POs']//parent::span//span[@class='ag-group-contracted']"
    COMMENT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='comment']"
    COMMENT_INPUT_FIELD = "//div[@row-id='{row_id}']/div[@col-id='comment']//input"
    LAST_EDITED_BY_FIELD = "//div[@row-id='{row_id}']/div[@col-id='lastEditedBy']"
    PO_UNIT_PRICE = "//div[@row-index='{row_id}']//div[@col-id='poUnitPrice']"
    VARIANCE_STATUS_COLUMN = "//div[@row-index='{row_id}']//div[@col-id='varianceStatus']"
    PRICE_VARIANCE_COLUMN = "//div[@row-index='{row_id}']//div[@col-id='priceVariance']"
    COMMENT_TOOLTIP = "//div[contains(@class, 'comment-log-tooltip')]/div[normalize-space()='{comment}']"
    ALL_POS_DROPDOWN_FIELD = "//span[text()='All POs']/ancestor::div[@col-id='ag-Grid-AutoColumn']"


class TopVariancesPage(
    ColumnOptionsMixin, SiteOrRegionBarMixin, ConsolidatedViewMixin, TableDropdownDataMixin, TableDataMixin, BasePage
):
    page_url = UI_APP_URL + "/#/imt/procurement-analytics/top-variances"

    @allure.step("Get Top Variances summary pinned table data")
    def get_top_variances_summary_table_data(self, expand: bool = True) -> list[TopVariancesSummaryPinnedTableRow]:
        self.expand_top_variances_ingredient_summary(expand=expand)
        return self.get_left_table_data(data_model=TopVariancesSummaryPinnedTableRow)

    @allure.step("Get table data on Top Variances")
    def get_top_variances_center_table_data(self) -> list[TopVariancesScrolledTableRow]:
        return self.get_center_table_data(data_model=TopVariancesScrolledTableRow)

    @allure.step("Add or edit 'Comments' field")
    def add_or_edit_text_in_comments_field(self, row_id: int, value: str):
        self.page.locator(TopVariancesLocators.COMMENT_FIELD.format(row_id=row_id)).dblclick()
        comment_input_field = self.page.locator(TopVariancesLocators.COMMENT_INPUT_FIELD.format(row_id=row_id))
        comment_input_field.fill(value=value)
        comment_input_field.press("Enter")
        self.wait_loading()
        expect(self.page.locator(TopVariancesLocators.LAST_EDITED_BY_FIELD.format(row_id=row_id))).not_to_be_empty()

    @allure.step("Click on 'All Pos' dropdown")
    def click_on_all_pos_dropdown(self):
        self.page.locator(TopVariancesLocators.ALL_POS_DROPDOWN).click()

    @allure.step("Check Po Unit price color flag")
    def check_po_unit_price_flag(self, row_id: int, expected_color: str):
        expect(self.page.locator(TopVariancesLocators.PO_UNIT_PRICE.format(row_id=row_id))).to_have_css(
            "background-color", expected_color
        )

    @allure.step("Check Variance status color flag")
    def check_variance_status_flag(self, row_id: int, expected_color: str):
        expect(self.page.locator(TopVariancesLocators.VARIANCE_STATUS_COLUMN.format(row_id=row_id))).to_have_css(
            "background-color", expected_color
        )

    @allure.step("Check Price Variance color flag")
    def check_price_status_flag(self, row_id: int, expected_color: str):
        expect(self.page.locator(TopVariancesLocators.PRICE_VARIANCE_COLUMN.format(row_id=row_id))).to_have_css(
            "background-color", expected_color
        )

    @allure.step("Check Top Variances column headers")
    def check_top_variances_column_headers(self, expand: bool = True):
        self.expand_top_variances_ingredient_summary(expand=expand)
        self.check_column_headers(expected_headers=TOP_VARIANCES_EXPECTED_HEADERS)

    @allure.step("Expand Ingredient summary section on Top Variances")
    def expand_top_variances_ingredient_summary(self, expand: bool = True):
        self.expand_collapse_header(expand_collapse_name="Ingredient Summary", expand=expand)

    @allure.step("Check comment tooltip is shown in Comments field")
    def check_comment_tooltip_is_displayed(self, row_id: int, comment_txt: str):
        self.page.locator(TopVariancesLocators.COMMENT_FIELD.format(row_id=row_id)).hover()
        expect(self.page.locator(TopVariancesLocators.COMMENT_TOOLTIP.format(comment=comment_txt))).to_be_visible()

    @allure.step("Get aria-expanded attribute from ALL POS dropdown")
    def get_aria_expanded_attribute_from_all_pos_dropdown(self):
        return self.page.locator(TopVariancesLocators.ALL_POS_DROPDOWN_FIELD).get_attribute("aria-expanded")
