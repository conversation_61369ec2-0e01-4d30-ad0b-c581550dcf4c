import allure
from playwright.sync_api import Locator, expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.cell_ids_to_be_replaced import (
    CELL_IDS_INVENTORY_MODULE_CURRENT_WEEK,
    CELL_IDS_INVENTORY_MODULE_NEXT_WEEK,
)
from automated_tests.data.constants.column_headers.inventory_column_headers import INVENTORY_MODULE_COLUMN_HEADERS
from automated_tests.data.models.sku import TestSku
from automated_tests.mixin_ui.dropdown_table_data_mixin import TableDropdownDataMixin
from automated_tests.mixin_ui.inventory_dropdown_mixin import InventoryDropdownMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.inventory.inventory_module import (
    InventoryModule3pwInventoryTableRow,
    InventoryModuleAvailableInventorySummaryTableRow,
    InventoryModuleCommentTableRow,
    InventoryModuleCurrentWeekTableRow,
    InventoryModuleMaterialSummaryTableRow,
    InventoryModuleNextWeekTableRow,
)


class InventoryModulePageLocators:
    SUPPLEMENT_NEED_CELL = (
        "//span[@class='ag-group-value' and text()='{sku_code}']/ancestor::div[@ref='leftContainer'"
        "]/following-sibling::div/descendant::div[@col-id='supplementNeed']/span/span"
    )
    NOTES_FIELD = "//div[@row-id='{row_id}']/div[@col-id='notes']"
    NOTES_FIELD_INPUT = "//div[@row-id='{row_id}']/div[@col-id='notes']//input"
    UNITS_IN_HOUSE_ADJUSTMENT = "//div[@row-id='{row_id}']/div[@col-id='unitsInHouseAdjustment']"
    UNITS_IN_HOUSE_ADJUSTMENT_INPUT = "//div[@row-id='{row_id}']/div[@col-id='unitsInHouseAdjustment']//input"
    LAST_EDITED_BY_FIELD = "//div[@row-id='{row_id}']/div[@col-id='lastEditedBy']"


class InventoryModulePage(
    InventoryDropdownMixin,
    TableDropdownDataMixin,
    TableDataMixin,
    BasePage,
):
    page_url = UI_APP_URL + "/#/inventory/dashboards/inventory-module"

    @allure.step("Expand collapsed 'Material Summary' section")
    def expand_collapse_material_summary(self, expand: bool = True):
        self.expand_collapse_header(expand_collapse_name="Material Summary", expand=expand)

    @allure.step("Check 'Supplement Needs' background color")
    def check_supplement_need_week_background_color(self, sku: TestSku, expected_color: str):
        expect(
            self.page.locator(InventoryModulePageLocators.SUPPLEMENT_NEED_CELL.format(sku_code=sku.sku_code))
        ).to_have_css("background-color", expected_color)

    @allure.step("Get 'Material Summary' table data")
    def get_material_summary_table_data(self) -> list[InventoryModuleMaterialSummaryTableRow]:
        return self.get_left_table_data(data_model=InventoryModuleMaterialSummaryTableRow)

    @allure.step("Get 'Available Inventory Summary' section table data")
    def get_available_inventory_summary_table_data(
        self, row: Locator
    ) -> InventoryModuleAvailableInventorySummaryTableRow:
        return self.find_cells_in_row_and_add_to_model(
            row=row,
            data_model=InventoryModuleAvailableInventorySummaryTableRow,
        )

    @allure.step("Get '3PW Inventory' section table data")
    def get_3pw_inventory_table_data(self, row: Locator) -> InventoryModule3pwInventoryTableRow:
        return self.find_cells_in_row_and_add_to_model(
            row=row,
            data_model=InventoryModule3pwInventoryTableRow,
        )

    @allure.step("Get 'Current Week' section table data")
    def get_current_week_table_data(self, row: Locator) -> InventoryModuleCurrentWeekTableRow:
        return self.find_cells_in_row_and_add_to_model(
            row=row,
            data_model=InventoryModuleCurrentWeekTableRow,
            ids_to_be_replaced=CELL_IDS_INVENTORY_MODULE_CURRENT_WEEK,
        )

    @allure.step("Get next week section table data")
    def get_next_week_table_data(self, row: Locator) -> InventoryModuleNextWeekTableRow:
        return self.find_cells_in_row_and_add_to_model(
            row=row,
            data_model=InventoryModuleNextWeekTableRow,
            ids_to_be_replaced=CELL_IDS_INVENTORY_MODULE_NEXT_WEEK,
        )

    @allure.step("Get Comment section table data")
    def get_comments_table_data(self, row: Locator) -> InventoryModuleCommentTableRow:
        return self.find_cells_in_row_and_add_to_model(
            row=row,
            data_model=InventoryModuleCommentTableRow,
        )

    @allure.step("Add or edit 'Notes' field")
    def add_or_edit_text_in_notes_field(self, row_id: int, value: str):
        self.page.locator(InventoryModulePageLocators.NOTES_FIELD.format(row_id=row_id)).dblclick()
        notes_input_field = self.page.locator(InventoryModulePageLocators.NOTES_FIELD_INPUT.format(row_id=row_id))
        notes_input_field.fill(value=value)
        notes_input_field.press("Enter")
        self.wait_loading()
        expect(
            self.page.locator(InventoryModulePageLocators.LAST_EDITED_BY_FIELD.format(row_id=row_id))
        ).not_to_be_empty()

    @allure.step("Add and edit 'Units in House (Adjustment)' column")
    def add_edit_units_in_house_adjustment_column_value(self, row_id: int, value: str):
        units_in_house_adj = InventoryModulePageLocators.UNITS_IN_HOUSE_ADJUSTMENT.format(row_id=row_id)
        self.page.wait_for_selector(units_in_house_adj)
        self.page.locator(InventoryModulePageLocators.UNITS_IN_HOUSE_ADJUSTMENT.format(row_id=row_id)).dblclick()
        units_in_house_adj_input = self.page.locator(
            InventoryModulePageLocators.UNITS_IN_HOUSE_ADJUSTMENT_INPUT.format(row_id=row_id)
        )
        units_in_house_adj_input.fill(value=value)
        units_in_house_adj_input.press("Enter")
        self.wait_loading()

    @allure.step("Check Inventory module page column headers")
    def check_inventory_module_column_headers(self):
        self.check_column_headers(expected_headers=INVENTORY_MODULE_COLUMN_HEADERS)
