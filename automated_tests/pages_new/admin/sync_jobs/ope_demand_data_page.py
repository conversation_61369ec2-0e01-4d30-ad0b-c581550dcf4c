import allure

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.admin_column_headers import OPE_DEMAND_DATA_EXPECTED_HEADERS
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.admin.jobs.ope_demand_data import OpeDemandDataTableRow


class OpeDemandDataPage(TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/admin/jobs/sku-demand"

    @allure.step("Check column headers on Ope Demand Data page")
    def check_ope_demand_column_headers(self):
        self.check_column_headers(expected_headers=OPE_DEMAND_DATA_EXPECTED_HEADERS)

    @allure.step("Get table data on OPE Demand Data page")
    def get_ope_demand_table_data(self) -> list[OpeDemandDataTableRow]:
        return self.get_center_table_data(data_model=OpeDemandDataTableRow)
