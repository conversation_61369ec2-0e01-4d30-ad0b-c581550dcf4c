import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.admin_constants import (
    ITEM_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT,
    ITEM_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT,
    ITEM_HAS_BEEN_SUCCESSFULLY_EDITED_ALERT_TEXT,
)
from automated_tests.data.constants.column_headers.admin_column_headers import WAREHOUSES_HEADERS
from automated_tests.data.models.warehouse import TestWarehouse
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.admin.pimt.warehouses import AdminPimtWarehousesTableRow
from procurement.constants.hellofresh_constant import InventoryInputType, WhReceivingType


class WarehousesLocators:
    ADD_NEW_CONFIG_BUTTON = "//span[normalize-space()='Add new config']"
    CONFIG_FORM = "//form[@id='new-config']"
    DROPDOWN_ITEM = "//div[contains(@class,'content__active')]//div[@role='listbox']//div[text()='{item}']"
    DROPDOWN_CHECKBOX_ITEM = (
        "//div[contains(@class,'content__active')]//div[text()='{item}']/parent::div/parent::div//i"
    )
    ACTIVE_CHECKBOXES_IN_DROPDOWN = "//div[contains(@class, 'v-list-item--highlighted')]"
    CLOSE_BUTTON = "//label[text() = '{item}']/parent::div//button[@aria-label='Close']"
    SAVE_BTN = "//span[text()='Save']"
    DELETE_WAREHOUSE_BUTTON = "//button[contains(@class,'mdi-delete')]"
    EDIT_WAREHOUSE = "//button[contains(@class, 'mdi-pencil')]"
    # warehouse form items
    GSHEET_NAME = "//label[text()='GSheet Name']/following-sibling::input"
    FULL_NAME = "//label[text()='Full Name']/following-sibling::input"
    BOB_CODE = "//label[text()='Bob Code']/following-sibling::input"
    HJ_NAME = "//label[text()='HJ Name']/following-sibling::input"
    ORDERING_TOOL_NAMES = "//label[text()='Ordering Tool Names']/following-sibling::div[@class='v-select__selections']"
    SUPPLIER_NAMES_FIELD = "//label[text()='Supplier Names']/parent::div"
    REGION_DROPDOWN = "//label[text()='Region']/following-sibling::div[@class='v-select__selections']"
    RECEIVING_TYPE_DROPDOWN = "//label[text()='Receiving Type']/following-sibling::div[@class='v-select__selections']"
    INVENTORY_TYPE_DROPDOWN = "//label[text()='Inventory Type']/following-sibling::div[@class='v-select__selections']"
    REGIONAL_DCS_DROPDOWN = "//label[text()='Regional DCs']/following-sibling::div[@class='v-select__selections']"
    PACKAGING_REGION_DROPDOWN = (
        "//label[text()='Packaging Region']/following-sibling::div[@class='v-select__selections']"
    )
    IS_3RD_PARTY_CHECKBOX = "//label[text()='Is 3rd Party']"
    # sequence modal locators
    SET_PARTNER_SEQUENCE_BUTTON = "//span[normalize-space()='Set Partner Sequence']"
    WAREHOUSES_IN_TABLE = "//div[@class='v-card__text']//div[contains(@class,'ag-row-position-absolute')]"
    WAREHOUSE_IN_SEQUENCE_MODAL = "//span[text()='{wh_code}']/preceding-sibling::div/span"


class WarehousesPage(TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/admin/pimt/warehouses"

    @allure.step("CHeck column headers on Warehouses")
    def check_warehouses_column_headers(self):
        self.check_column_headers(expected_headers=WAREHOUSES_HEADERS)

    @allure.step("Get table data in on Warehouses page")
    def get_warehouses_table_data(self):
        table_data = self.get_center_table_data(data_model=AdminPimtWarehousesTableRow)
        for warehouse in table_data:
            warehouse.ordering_tool_names = warehouse.ordering_tool_names.split(", ")
            warehouse.supplier_names = warehouse.supplier_names.split(", ")
            warehouse.regional_dcs = warehouse.regional_dcs.split(", ")
            warehouse.packaging_regions = warehouse.packaging_regions.split(", ")
        return table_data

    @allure.step("Add new warehouse")
    def add_new_warehouse(self, warehouse: TestWarehouse):
        self.page.locator(WarehousesLocators.ADD_NEW_CONFIG_BUTTON).click()
        expect(self.page.locator(WarehousesLocators.CONFIG_FORM)).to_be_visible()
        self.fill_gsheet_name(gsheet_name=warehouse.code)
        self.fill_full_name(full_name=warehouse.warehouse_name)
        self.fill_bob_code(bob_code=warehouse.bob_code)
        if warehouse.hj_name:
            self.fill_hj_name(hj_name=warehouse.hj_name)
        self.fill_ordering_tool_name(ordering_tool_names=warehouse.ot_dcs)
        self.fill_supplier_names(supplier_names=warehouse.ot_suppliers)
        self.choose_region_from_dropdown(warehouse.region)
        self.choose_regional_dcs_from_dropdown(warehouse.regional_dcs)
        self.choose_packaging_region_from_dropdown(warehouse.packaging_regions)
        self.choose_receiving_type_from_dropdown(warehouse.receiving_type)
        self.choose_inventory_type_from_dropdown(warehouse.inventory_type)
        self.page.locator(WarehousesLocators.IS_3RD_PARTY_CHECKBOX).set_checked(warehouse.is_3rd_party)
        self.click_save_button()
        expect(self.page.locator(WarehousesLocators.CONFIG_FORM)).not_to_be_visible()
        expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT)).to_be_visible()

    @allure.step("Edit warehouse")
    def edit_warehouse(self, warehouse: TestWarehouse):
        self.page.locator(WarehousesLocators.EDIT_WAREHOUSE).click()
        expect(self.page.locator(WarehousesLocators.CONFIG_FORM)).to_be_visible()
        self.fill_bob_code(bob_code=warehouse.bob_code)
        if warehouse.hj_name:
            self.fill_hj_name(hj_name=warehouse.hj_name)
        self.erase_input_field_by_field_name(field_name="Ordering Tool Names")
        self.fill_ordering_tool_name(ordering_tool_names=warehouse.ot_dcs)
        self.erase_input_field_by_field_name(field_name="Supplier Names")
        self.fill_supplier_names(supplier_names=warehouse.ot_suppliers)
        self.choose_region_from_dropdown(region=warehouse.region)
        self.choose_regional_dcs_from_dropdown(regional_dcs=warehouse.regional_dcs)
        self.choose_packaging_region_from_dropdown(packaging_regions=warehouse.packaging_regions)
        self.choose_receiving_type_from_dropdown(receiving_type=warehouse.receiving_type)
        self.choose_inventory_type_from_dropdown(inventory_type=warehouse.inventory_type)
        self.page.locator(WarehousesLocators.IS_3RD_PARTY_CHECKBOX).set_checked(warehouse.is_3rd_party)
        self.click_save_button()
        expect(self.page.locator(WarehousesLocators.CONFIG_FORM)).not_to_be_visible()
        expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_EDITED_ALERT_TEXT)).to_be_visible()

    @allure.step("Click Save button")
    def click_save_button(self):
        self.page.locator(WarehousesLocators.SAVE_BTN).click()

    @allure.step("Fill Gsheet Name field")
    def fill_gsheet_name(self, gsheet_name: str):
        self.page.locator(WarehousesLocators.GSHEET_NAME).fill(gsheet_name)

    @allure.step("Fill Full Name field")
    def fill_full_name(self, full_name: str):
        self.page.locator(WarehousesLocators.FULL_NAME).fill(full_name)

    @allure.step("Fill Bob Code field")
    def fill_bob_code(self, bob_code: str):
        self.page.locator(WarehousesLocators.BOB_CODE).fill(bob_code)

    @allure.step("Fill Hj Name field")
    def fill_hj_name(self, hj_name: str):
        self.page.locator(WarehousesLocators.HJ_NAME).fill(hj_name)

    @allure.step("Erase the input field by field name")
    def erase_input_field_by_field_name(self, field_name: str):
        close_buttons = self.page.query_selector_all(WarehousesLocators.CLOSE_BUTTON.format(item=field_name))
        for button in close_buttons:
            button.click()

    @allure.step("Fill Ordering Tool Name field")
    def fill_ordering_tool_name(self, ordering_tool_names: list[str]):
        self.page.locator(WarehousesLocators.ORDERING_TOOL_NAMES).click()
        for ot_name in ordering_tool_names:
            self.page.get_by_label("Ordering Tool Names").fill(ot_name)
            self.page.get_by_label("Ordering Tool Names").press("Enter")

    @allure.step("Fill Supplier Names field")
    def fill_supplier_names(self, supplier_names: list[str]):
        self.page.locator(WarehousesLocators.SUPPLIER_NAMES_FIELD).click()
        for supplier_name in supplier_names:
            self.page.get_by_label("Supplier Names").press_sequentially(text=supplier_name)
            self.page.get_by_label("Supplier Names").press(key="Enter")

    @allure.step("Choose Region from dropdown")
    def choose_region_from_dropdown(self, region: str):
        self.page.locator(WarehousesLocators.REGION_DROPDOWN).click()
        self.page.locator(WarehousesLocators.DROPDOWN_ITEM.format(item=region)).click()

    @allure.step("Choose Regional DCs from dropdown")
    def choose_regional_dcs_from_dropdown(self, regional_dcs: list[str]):
        self.page.locator(WarehousesLocators.REGIONAL_DCS_DROPDOWN).click()
        checked_checkboxes = self.page.query_selector_all(WarehousesLocators.ACTIVE_CHECKBOXES_IN_DROPDOWN)
        # Click each checked checkbox to uncheck it if they are active
        for checkbox in checked_checkboxes:
            checkbox.click()
        for dcs in regional_dcs:
            self.page.locator(WarehousesLocators.DROPDOWN_CHECKBOX_ITEM.format(item=dcs)).click()

    @allure.step("Choose Packaging Region from dropdown")
    def choose_packaging_region_from_dropdown(self, packaging_regions: list[str]):
        self.page.locator(WarehousesLocators.PACKAGING_REGION_DROPDOWN).click()
        checked_checkboxes = self.page.query_selector_all(WarehousesLocators.ACTIVE_CHECKBOXES_IN_DROPDOWN)
        # Click each checked checkbox to uncheck it if they are active
        for value in checked_checkboxes:
            value.click()
        for packaging_region in packaging_regions:
            self.page.locator(WarehousesLocators.DROPDOWN_CHECKBOX_ITEM.format(item=packaging_region)).click()

    @allure.step("Choose Receiving Type from dropdown")
    def choose_receiving_type_from_dropdown(self, receiving_type: WhReceivingType):
        self.page.locator(WarehousesLocators.RECEIVING_TYPE_DROPDOWN).click()
        self.page.locator(WarehousesLocators.DROPDOWN_ITEM.format(item=receiving_type)).click()

    @allure.step("Choose Inventory Type from dropdown")
    def choose_inventory_type_from_dropdown(self, inventory_type: InventoryInputType):
        self.page.locator(WarehousesLocators.INVENTORY_TYPE_DROPDOWN).click()
        self.page.locator(WarehousesLocators.DROPDOWN_ITEM.format(item=inventory_type)).click()

    @allure.step("Set warehouse sequence")
    def set_warehouse_sequence(self, warehouses_sequence_dict: dict[int, str]):
        self.page.locator(WarehousesLocators.SET_PARTNER_SEQUENCE_BUTTON).click()
        for sequence_number, wh_code in warehouses_sequence_dict.items():
            actual_warehouses = self.page.query_selector_all(WarehousesLocators.WAREHOUSES_IN_TABLE)
            current_states = {int(item.get_attribute("row-index")): item.inner_text() for item in actual_warehouses}
            element_to_drag = self.page.locator(WarehousesLocators.WAREHOUSE_IN_SEQUENCE_MODAL.format(wh_code=wh_code))
            element_to_drop = self.page.locator(
                WarehousesLocators.WAREHOUSE_IN_SEQUENCE_MODAL.format(wh_code=current_states[sequence_number])
            )
            element_to_drag.drag_to(element_to_drop)
        self.click_save_button()
        expect(self.page.locator(WarehousesLocators.CONFIG_FORM)).not_to_be_visible()

    @allure.step("Delete warehouse")
    def delete_warehouse(self):
        self.page.once("dialog", lambda dialog: dialog.accept())
        self.page.locator(WarehousesLocators.DELETE_WAREHOUSE_BUTTON).click()
        expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT)).to_be_visible()
