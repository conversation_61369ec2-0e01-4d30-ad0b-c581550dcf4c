import allure
from playwright.sync_api import expect

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.admin_constants import (
    ITEM_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT,
    ITEM_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT,
    ITEM_HAS_BEEN_SUCCESSFULLY_EDITED_ALERT_TEXT,
)
from automated_tests.data.constants.column_headers.admin_column_headers import BULK_SKUS_HEADERS
from automated_tests.data.models.imt_config import TestBrand
from automated_tests.data.models.sku import TestSku
from automated_tests.mixin_ui.column_options_mixin import ColumnOptionsMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.models.admin.skus.bulk_skus import AdminBulkSkusTableRow


class BulkSkusLocators:
    ADD_NEW_SKU_BUTTON = "//span[normalize-space()='Add new SKU']"
    CONFIG_FORM = "//form[@id='new-config']"
    PACKAGED_SKU_CODE_FIELD = "//label[text()='Packaged SKU Code']/following-sibling::input"
    BULK_SKU_CODE_FIELD = "//label[text()='Bulk SKU Code']/following-sibling::input"
    BRANDS_DROPDOWN = "//label[text()='Brands']/following-sibling::div"
    ALL_BRANDS_DROPDOWN_ITEM = (
        "//div[@role='listbox']/descendant::div[contains(text(), 'All Brands')]/ancestor::div[@aria-selected]"
    )
    DROPDOWN_ITEM = "//div[@role='listbox']/descendant::div[contains(text(),'{item}')]/ancestor::div[@aria-selected]"
    PICK_CONVERSIONS_FIELD = "//label[text()='Pick Conversions']/following-sibling::input"
    BULK_MASTER_SKU_CHECKBOX = "//label[text()='Bulk Master SKU']"
    ACTIVE_CHECKBOXES_IN_DROPDOWN = "//div[contains(@class, 'v-list-item--highlighted')]"
    SAVE_BUTTON = "//span[text()='Save']"
    DELETE_BUTTON = (
        "//div[contains(@class, '{sku_code}-actions-bar')]/descendant::button[contains(@class, 'mdi-delete')]"
    )
    EDIT_BUTTON = "//div[contains(@class, '{sku_code}-actions-bar')]/descendant::button[contains(@class, 'mdi-pencil')]"


class BulkSkusPage(ColumnOptionsMixin, TableDataMixin):
    page_url = UI_APP_URL + "/#/admin/skus/bulk-skus"

    @allure.step("Check bulk SKUs column headers")
    def check_bulk_skus_column_headers(self):
        self.check_column_headers(expected_headers=BULK_SKUS_HEADERS)

    @allure.step("Get table data in on Bulk SKUs page")
    def get_bulk_skus_table_data(self) -> list[AdminBulkSkusTableRow]:
        return self.get_center_table_data(data_model=AdminBulkSkusTableRow)

    @allure.step("Add new Bulk SKUs mapping")
    def add_new_bulk_skus(
        self,
        bulk_sku: TestSku,
        packaged_sku: TestSku,
        brands: list[TestBrand] = None,
        pick_conversion: str = None,
        is_master_sku: bool = False,
        expected_error: bool = False,
    ):
        self.page.locator(BulkSkusLocators.ADD_NEW_SKU_BUTTON).click()
        expect(self.page.locator(BulkSkusLocators.CONFIG_FORM)).to_be_visible()
        self.fill_packaged_sku_code_field(packaged_sku=packaged_sku)
        self.fill_bulk_sku_code_field(bulk_sku=bulk_sku)
        if pick_conversion:
            self.fill_pick_conversions(pick_conversion=pick_conversion)
        self.page.locator(BulkSkusLocators.BULK_MASTER_SKU_CHECKBOX).set_checked(checked=is_master_sku)
        self.choose_brands_from_dropdown(brands=brands)
        self.page.locator(BulkSkusLocators.SAVE_BUTTON).click()
        if not expected_error:
            expect(self.page.locator(BulkSkusLocators.CONFIG_FORM)).not_to_be_visible()
            expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_CREATED_ALERT_TEXT)).to_be_visible()

    @allure.step("Edit Bulk SKUs mapping")
    def edit_bulks_skus(
        self,
        packaged_sku: TestSku,
        pick_conversion: str,
        brands: list[TestBrand] = None,
        is_master_sku: bool = False,
    ):
        self.page.locator(BulkSkusLocators.EDIT_BUTTON.format(sku_code=packaged_sku.sku_code)).click()
        expect(self.page.locator(BulkSkusLocators.CONFIG_FORM)).to_be_visible()
        self.fill_pick_conversions(pick_conversion=pick_conversion)
        self.page.locator(BulkSkusLocators.BULK_MASTER_SKU_CHECKBOX).set_checked(checked=is_master_sku)
        self.choose_brands_from_dropdown(brands=brands)
        self.page.locator(BulkSkusLocators.SAVE_BUTTON).click()
        expect(self.page.locator(BulkSkusLocators.CONFIG_FORM)).not_to_be_visible()
        expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_EDITED_ALERT_TEXT)).to_be_visible()

    @allure.step("Fill packaged sku code field")
    def fill_packaged_sku_code_field(self, packaged_sku: TestSku):
        self.page.locator(BulkSkusLocators.PACKAGED_SKU_CODE_FIELD).fill(packaged_sku.sku_code)

    @allure.step("Fill bulk sku code field")
    def fill_bulk_sku_code_field(self, bulk_sku: TestSku):
        self.page.locator(BulkSkusLocators.BULK_SKU_CODE_FIELD).fill(bulk_sku.sku_code)

    @allure.step("Fill pick conversions field")
    def fill_pick_conversions(self, pick_conversion: str):
        self.page.locator(BulkSkusLocators.PICK_CONVERSIONS_FIELD).fill(pick_conversion)

    @allure.step("Choose brands from dropdown")
    def choose_brands_from_dropdown(self, brands: list[TestBrand] = None):
        self.page.locator(BulkSkusLocators.BRANDS_DROPDOWN).click()
        all_brands_checkbox = self.page.locator(BulkSkusLocators.ALL_BRANDS_DROPDOWN_ITEM)
        if brands:
            all_brands_checkbox.click()
            for brand in brands:
                self.page.locator(BulkSkusLocators.DROPDOWN_ITEM.format(item=brand.code)).click()
        else:
            # Check "All Brands" if it is not selected
            if all_brands_checkbox.get_attribute("aria-selected") == "false":
                all_brands_checkbox.click()
        self.page.keyboard.press("Escape")

    @allure.step("Delete Bulk Skus record by packaged sku code")
    def delete_bulk_skus(self, packaged_sku: TestSku):
        self.page.once("dialog", lambda dialog: dialog.accept())
        self.page.locator(BulkSkusLocators.DELETE_BUTTON.format(sku_code=packaged_sku.sku_code)).click()
        expect(self.page.get_by_text(ITEM_HAS_BEEN_SUCCESSFULLY_DELETED_ALERT_TEXT)).to_be_visible()
