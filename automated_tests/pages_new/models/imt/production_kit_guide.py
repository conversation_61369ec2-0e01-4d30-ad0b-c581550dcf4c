import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class ProductionKitGuideTableRow(BaseTableRow):
    recipe_code: str
    meal_number: str
    recipe_name: str
    hf_5_digit_sku: int
    picks_2_person: int
    picks_3_person: int
    picks_4_person: int
    picks_6_person: int
    sku_name: str
    hf_full_sku: str
    purchasing_category: str
    full_sku_plus_sku_name: str
    weight_amount: float
    weight_unit: str
    storage_location: str
    allergens: str


@dataclasses.dataclass
class ProductionKitGuideTableRowFactor(BaseTableRow):
    recipe_code: str
    recipe_name: str
    hf_5_digit_sku: int
    sku_name: str
    hf_full_sku: str
    purchasing_category: str
    full_sku_plus_sku_name: str
    weight_amount: float
    weight_unit: str
    storage_location: str
    allergens: str
    sub_recipe_id: str
    sub_recipe: str
