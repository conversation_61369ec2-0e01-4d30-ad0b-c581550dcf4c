import dataclasses


@dataclasses.dataclass
class InventoryPullPutTable:
    brand: str
    dc: str
    type: str
    sku_code: str
    sku_name: str
    quantity: str
    last_edited_by: str
    edited_at: str
    unit_of_measure: str
    comments: str


@dataclasses.dataclass
class InventoryPullPutTableExport:
    brand: str
    dc: str
    type: str
    sku_code: str
    sku_name: str
    quantity: str
    last_edited_by: str
    edited_at: str
    comments: str
