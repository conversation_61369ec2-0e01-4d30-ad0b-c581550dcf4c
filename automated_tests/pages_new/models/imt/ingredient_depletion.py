import dataclasses


@dataclasses.dataclass
class IngredientDepletionPinnedTableRow:
    site: str
    brand: str
    sku: str
    sku_name: str
    category: str
    commodity_group: str
    buyer: str
    unit_of_measure: str
    impacted_recipes: str


@dataclasses.dataclass
class IngredientDepletionPinnedTableRowBulkSkuModal:
    sku: str
    sku_name: str
    category: str
    commodity_group: str
    buyer: str
    unit_of_measure: str
    impacted_recipes: str


@dataclasses.dataclass
class UnitsNeededAndStatusTableRow:
    # Units needed section
    plan: str
    plannedProduction: str
    rowForecast: str
    forecastOscar: str
    delta: str

    # Status section
    sn: str
    supplementNeedHj: str
    criticalDelivery: str


@dataclasses.dataclass
class IngredientDepletionDailyTableRow:
    confirmed_inventory_monday: str
    units_delivered_monday: str
    discards_monday: str
    on_hand_monday: str
    production_needs_monday: str
    eod_inventory_prod_monday: str
    units_on_order_monday: str
    eod_inventory_order_monday: str
    status_in_week_monday: str
    total_onhand_monday: str
    total_production_need_monday: str
    total_on_hand_minus_total_production_need_monday: str
    status_monday: str

    start_of_day_inventory_tuesday: str
    units_delivered_tuesday: str
    discards_tuesday: str
    on_hand_tuesday: str
    production_needs_tuesday: str
    eod_inventory_prod_tuesday: str
    units_on_order_tuesday: str
    eod_inventory_order_tuesday: str
    status_in_week_tuesday: str
    total_onhand_tuesday: str
    total_production_need_tuesday: str
    total_on_hand_minus_total_production_need_tuesday: str
    status_tuesday: str


@dataclasses.dataclass
class IngredientDepletionDailyTableRowWednesday:
    confirmed_inventory_wednesday: str
    units_delivered_wednesday: str
    discards_wednesday: str
    on_hand_wednesday: str
    production_needs_wednesday: str
    eod_inventory_prod_wednesday: str
    units_on_order_wednesday: str
    eod_inventory_order_wednesday: str
    status_in_week_wednesday: str
    total_onhand_wednesday: str
    total_production_need_wednesday: str
    total_on_hand_minus_total_production_need_wednesday: str
    status_wednesday: str


@dataclasses.dataclass
class IngredientDepletionWeeklyTableRow:
    units_needed: str
    units_ordered: str
    units_received: str
    units_in_house: str
    hj_discards: str
    units_on_production_floor: str
    live_row_need: str
    row_need: str
    units_in_house_minus_row_need: str
    start_of_the_day_inventory: str
    start_of_the_day_inventory_minus_units_in_house_hj: str
    previous_week_row_need: str
    next_week_forecast: str
    units_in_house_min_row_need_min_forecast: str
    units_to_produce_by_autobagger: str
    inventory: str
    discards: str
    donations: str
    pulls: str
    total_on_hand: str
    on_hand_min_production_needs: str
    in_progress_hj: str
    awaiting_delivery: str
    not_delivered: str
    buffer_quantity: str
    buffer_percent: str
    allocation_price: str


@dataclasses.dataclass
class IngredientDepletionWeeklyTableRowCA:
    units_needed: str
    units_ordered: str
    units_received: str
    units_in_house: str
    hj_discards: str
    units_on_production_floor: str
    row_need: str
    units_in_house_minus_row_need: str
    start_of_the_day_inventory: str
    start_of_the_day_inventory_minus_units_in_house_hj: str
    previous_week_row_need: str
    next_week_forecast: str
    units_in_house_min_row_need_min_forecast: str
    units_to_produce_by_autobagger: str
    hj_snapshot: str
    inventory: str
    discards: str
    pulls: str
    carryover: str
    initial_pulls: str
    putaway_production: str
    total_on_hand: str
    on_hand_min_production_needs: str
    in_progress_hj: str
    awaiting_delivery: str
    not_delivered: str
    buffer_quantity: str
    buffer_percent: str
    allocation_price: str


@dataclasses.dataclass
class IngredientDepletionWorkOrderTableData:
    required_lbs: str
    staged_lbs: str
    buffered_forecast: str
    wo_rown: str
    staged_plus_hj_vs_received: str
    hj_minus_wo_rown: str
    hj_plus_delivered_minus_wo_rown: str


@dataclasses.dataclass
class IngredientDepletionBulkTableRow:
    bulk_units_ordered: str
    bulk_units_received: str
    bulk_discards: str
    delta: str
    bulk_units_in_hj: str


@dataclasses.dataclass
class IngredientDepletionBufferTableRow:
    allowed_buffer: str
    po_buffer: str
    buffer_quantity_needed: str
    supplier: str
    case_yield: str
    case_total: str
    case_cost: str
    total_cost: str


@dataclasses.dataclass
class EowInventoryTableRow:
    eow_inventory: str
