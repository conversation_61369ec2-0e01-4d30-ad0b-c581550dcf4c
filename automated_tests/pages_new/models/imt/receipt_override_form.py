import dataclasses


@dataclasses.dataclass
class ReceiptOverrideFormTable:
    entry_type: str
    brand: str
    site: str
    po_number: str
    supplier_name: str
    sku_code: str
    sku_name: str
    quantity_received: str
    adj_received_volume: str
    difference_receipt_vs_adj: str
    cases_received: str
    adj_received_cases: str
    case_difference_receipt_vs_adj: str
    receive_date: str
    submitted_by: str
    timestamp: str
    comment: str


@dataclasses.dataclass
class ReceiptOverrideFormExportTable:
    entry_type: str
    brand: str
    site: str
    po_number: str
    supplier_name: str
    sku_code: str
    sku_name: str
    quantity_received: str
    adj_received_volume: str
    difference_receipt_vs_adj: str
    adj_receiving_date: str
    submitted_by: str
    timestamp: str
