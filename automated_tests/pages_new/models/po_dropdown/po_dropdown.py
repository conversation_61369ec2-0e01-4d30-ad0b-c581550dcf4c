import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class PoDropdownTableRow(BaseTableRow):
    supplier: str
    po_number: str
    sku_code: str
    sku_name: str
    scheduled_delivery_date: str
    po_status: str
    receiving_variance_in_units: str
    appointment_time: str
    order_size: str
    case_price: str
    case_size: str
    case_size_received: str
    quantity_ordered: str
    quantity_received: str
    cases_received: str
    date_received: str
    total_price: str
    total_price_received: str
    emergency_reason: str
    ship_method: str
    phf_delivery_percent_of_forecast: str
