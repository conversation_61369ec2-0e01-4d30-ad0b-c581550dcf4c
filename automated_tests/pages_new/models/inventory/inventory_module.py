import dataclasses


@dataclasses.dataclass
class InventoryModuleMaterialSummaryTableRow:
    sku_code: str
    sku_name: str
    category: str
    storage_location: str
    buyer: str
    commodity_group: str
    replenishment_sku: str


@dataclasses.dataclass
class InventoryModuleAvailableInventorySummaryTableRow:
    weeksOnHand: str
    palletsOnHand: str
    unitsInHouseHj: str
    supplementNeed: str
    supplementNeedQuantity: str
    dcExpiringInventory: str
    tenDaysFromExpiration: str


@dataclasses.dataclass
class InventoryModule3pwInventoryTableRow:
    unitsInHouse: str
    unitsInHouseNetwork: str
    threePWOutbound: str
    threePWExpiringInventory: str
    threePWWeeksOnHand: str
    threePWTenDaysFromExpiration: str


@dataclasses.dataclass
class InventoryModuleCurrentWeekTableRow:
    hj_snapshot_yesterday: str
    start_of_day_units_in_house_snapshot: str
    inbound_awaiting_delivery: str
    undelivered_past_due_pos: str
    received_today: str
    units_expiring: str
    row_needs_prev_week: str
    prod_needs_rest_of_week_for_current_week: str
    remaining_inventory: str
    units_in_house_adjustment: int


@dataclasses.dataclass
class InventoryModuleNextWeekTableRow:
    start_of_week_inventory_next_week: str
    inbound_awaiting_delivery_next_week: str
    units_expiring_next_week: str
    prod_needs_full_week_next_week: str
    remaining_inventory_next_week: str


@dataclasses.dataclass
class InventoryModuleCommentTableRow:
    notes: str
    lastEditedBy: str
