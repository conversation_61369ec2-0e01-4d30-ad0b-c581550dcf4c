import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class ReplenishmentDashboardSummaryTableRow:
    sku: str
    sku_name: str


@dataclasses.dataclass
class UnpinnedReplenishmentDashboardSummaryTableRow:
    replenishment_type: str
    buyer: str
    strategy_manager: str
    replenishment_buyer: str
    category: str
    sub_category: str
    brand: str


@dataclasses.dataclass
class ReplenishmentDashboardInventoryTargetsTableRow(BaseTableRow):
    leadTime: int


@dataclasses.dataclass
class ReplenishmentDashboardForecastTableRow(BaseTableRow):
    in_week_row_need: int
    forecast_current_week: int
    forecast_next_week: int
    forecast_next_week_plus_1: int
    forecast_next_week_plus_2: int
    forecast_next_week_plus_3: int
    forecast_next_week_plus_4: int
    forecast_next_week_plus_5: int
    forecast_next_week_plus_6: int
    forecast_next_week_plus_7: int
    forecast_next_week_plus_8: int
    total_forecast: int
    average_weekly_demand: int


@dataclasses.dataclass
class ReplenishmentDashboardOnHandTableRow(BaseTableRow):
    totalOnHand: int
    totalInventoryCore: int
    totalInventoryTpl: int
    vendorManagedInventory: float
    totalInventoryWarehouse: int
    expiredInventory: int
    totalInventoryWos: float
    expiringSoonInventory: int


@dataclasses.dataclass
class ReplenishmentInventoryCostingTableRow:
    totalCost: str
    dcCost: str
    tplCost: str
    tpwCost: str


@dataclasses.dataclass
class ReplenishmentOverrideTableRow:
    totalOnHandOverride: str


@dataclasses.dataclass
class ReplenishmentDashboardOnOrderTableRow(BaseTableRow):
    inbound_current_week: int
    inbound_next_week: int
    inbound_next_week_plus_1: int
    inbound_next_week_plus_2: int
    inbound_next_week_plus_3: int
    inbound_next_week_plus_4: int
    inbound_next_week_plus_5: int
    inbound_next_week_plus_6: int
    inbound_next_week_plus_7: int
    inbound_next_week_plus_8: int
    total_inbounds: int
    total_undelivered: int


@dataclasses.dataclass
class ReplenishmentDashboardEowNetInventoryTableRow(BaseTableRow):
    eow_inventory_current_week: int
    eow_inventory_next_week: int
    eow_inventory_next_week_plus_1: int
    eow_inventory_next_week_plus_2: int
    eow_inventory_next_week_plus_3: int
    eow_inventory_next_week_plus_4: int
    eow_inventory_next_week_plus_5: int
    eow_inventory_next_week_plus_6: int
    eow_inventory_next_week_plus_7: int
    eow_inventory_next_week_plus_8: int


@dataclasses.dataclass
class ReplenishmentDashboardEowWosTableRow(BaseTableRow):
    eow_wos_current_week: float
    eow_wos_next_week: float
    eow_wos_next_week_plus_1: float
    eow_wos_next_week_plus_2: float
    eow_wos_next_week_plus_3: float
    eow_wos_next_week_plus_4: float
    eow_wos_next_week_plus_5: float
    eow_wos_next_week_plus_6: float
    eow_wos_next_week_plus_7: float
    eow_wos_next_week_plus_8: float


@dataclasses.dataclass
class UndeliveredPosDropdownTableRow(BaseTableRow):
    supplier: str
    po_number: str
    sku_code: str
    sku_name: str
    scheduled_delivery_date: str
    po_status: str
    appointment_time: str
    order_size: str
    case_price: str
    case_size: str
    quantity_ordered: str
    total_price: str
    emergency_reason: str
    ship_method: str
    phf_delivery_percent_of_forecast: str


@dataclasses.dataclass
class ForecastDropdownTableRow(BaseTableRow):
    dc: str
    sku_code: str
    week: str
    forecast: str
