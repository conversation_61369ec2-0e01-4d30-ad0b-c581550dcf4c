import dataclasses
from decimal import Decimal

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class IngredientDepletionSummaryTableRow:
    sku: str
    sku_name: str
    category: str
    commodity_group: str
    impacted_recipes: str


@dataclasses.dataclass
class IngredientDepletionWeeklyTableRow:
    # Units needed section
    plan: str
    forecastOscar: str
    delta: str
    # Status section
    sn: str
    # Weekly Overview section
    units_needed: str
    units_ordered: str
    units_received: str
    units_in_house: str
    row_need: str
    units_in_house_min_row_need: str
    next_week_forecast: str
    units_in_house_min_row_need_min_forecast: str
    units_to_produce_by_autobagger: str
    inventory: str
    discards: str
    pulls: str
    total_on_hand: str
    on_hand_min_production_needs: str
    in_progress_hj: str
    awaiting_delivery: str
    not_delivered: str
    buffer_quantity: str
    buffer_percent: str


@dataclasses.dataclass
class ConsolidatedTableRows:
    site: str
    brand: str


@dataclasses.dataclass
class PurchaseOrdersTableRows(BaseTableRow):
    supplier: str
    po_number: str
    sku_id: str
    sku_name: str
    category: str
    scheduled_delivery_date: str
    po_status: str
    receiving_variance_in_units: int
    order_size: int
    case_price: str
    case_size: Decimal
    case_size_received: float
    quantity_ordered: int
    quantity_received: int
    cases_received: int
    total_price_po: str
    total_price_received: str
    emergency_reason: str
    phf_delivery_percent_of_forecast: str


@dataclasses.dataclass
class PoDropdownTableRows(BaseTableRow):
    supplier: str
    po_number: str
    sku: str
    sku_name: str
    scheduled_delivery_date: str
    po_status: str
    receiving_variance_in_units: int
    order_size: int
    case_price: str
    case_size: Decimal
    case_size_received: float
    quantity_ordered: int
    quantity_received: int
    cases_received: int
    date_received: str
    total_price_po: str
    total_price_received: str
    emergency_reason: str
    ship_method: str
    phf_delivery_percent_of_forecast: str
