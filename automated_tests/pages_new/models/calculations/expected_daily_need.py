import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class ExpectedDailyNeedTableRow(BaseTableRow):
    closing_stock: int
    daily_needs: int
    carryover_stock: int
    stock_buffer: int
    incoming_pos: str
    inbound: int
    consumption: int
    stock_difference: int


@dataclasses.dataclass
class ExDailyNeedIngredientSummaryTableRow(BaseTableRow):
    sku_code: str
    sku_name: str
    category: str
    commodity_group: str
    buyer: str
