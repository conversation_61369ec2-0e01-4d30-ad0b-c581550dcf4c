import dataclasses

from automated_tests.pages_new.models.base_table_row import BaseTableRow


@dataclasses.dataclass
class PerpetualGrossNeedsSummaryTableRow(BaseTableRow):
    sku_code: str
    sku_name: str
    scm_week: str
    sum_of_gross_needs: str
    category: str
    commodity_group: str
    buyer: str


@dataclasses.dataclass
class PerpetualGrossNeedsTableRow(BaseTableRow):
    day_1: str
    day_2: str
    day_3: str
    day_4: str
    day_5: str
    day_6: str
    day_7: str
    day_8: str
    day_9: str
    day_10: str
    day_11: str
    day_12: str
    day_13: str
    day_14: str
    day_15: str
    day_16: str
