import allure

from automated_tests.configs import UI_APP_URL
from automated_tests.data.constants.column_headers.pimt_column_headers import COMMENTS_LOG_PO_COMMENTS_HEADERS
from automated_tests.mixin_ui.comment_mixin import CommentMixin
from automated_tests.mixin_ui.table_data_mixin import TableDataMixin
from automated_tests.pages_new.base_page import BasePage
from automated_tests.pages_new.models.pimt.pimt_comments_log import PimtPOCommentsLogTableRow


class PimtCommentsLogLocators:
    PO_COMMENTS_BUTTON = "//span[normalize-space()='PO Comments']"


class PimtCommentsLogPage(CommentMixin, TableDataMixin, BasePage):
    page_url = UI_APP_URL + "/#/pimt/logs/comments"

    @allure.step("Get PO Comments log table data")
    def get_po_comments_log_table_data(self) -> list[PimtPOCommentsLogTableRow]:
        return self.get_center_table_data(PimtPOCommentsLogTableRow)

    @allure.step("Select PO comments on Comments log page")
    def select_po_comments(self):
        self.page.locator(PimtCommentsLogLocators.PO_COMMENTS_BUTTON).click()
        self.wait_loading()

    @allure.step("Check PIMT Comments Log Po Comments headers")
    def check_po_comments_headers(self):
        self.check_column_headers(expected_headers=COMMENTS_LOG_PO_COMMENTS_HEADERS)
