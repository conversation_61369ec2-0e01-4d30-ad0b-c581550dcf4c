@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.application-conventions")
    alias(libs.plugins.jooq)
}

group = "$group.distributioncenterservice".replace("-", "")
description = "Consume and persist distribution center topic"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(project(":lib"))
    api(project(":lib:kafka"))
    api(project(":lib:db"))
    api(libs.jackson.kotlin)
    api(libs.jackson.jsr310)

    testImplementation(testFixtures(project(":lib")))
    testImplementation(testFixtures(project(":lib:kafka")))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }

                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "dc_config"
                        inputSchema = "public"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}
