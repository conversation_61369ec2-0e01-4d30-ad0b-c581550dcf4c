package com.hellofresh.sdf.distributioncenter

import InfraPreparation.getMigratedDataSource
import KafkaInfraPreparation.createKafkaProducer
import KafkaInfraPreparation.kafka
import KafkaInfraPreparation.startKafkaAndCreateTopics
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.sdf.distributioncenter.model.DistributionCenterConfiguration
import com.hellofresh.sdf.distributioncenter.repository.DistributionCenterRepository
import com.hellofresh.sdf.distributioncenter.repository.DistributionCenterRepositoryTest.Companion.assertDcConfig
import com.hellofresh.sdf.lib.kafka.processor.DeserializationExceptionStrategyType
import com.hellofresh.sdf.lib.kafka.processor.PollConfig
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.distributioncenterservice.schema.Tables.DC_CONFIG
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.SUNDAY
import java.time.Duration
import java.time.ZoneOffset
import java.util.concurrent.Executors
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.Serdes
import org.apache.kafka.common.serialization.Serializer
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.shaded.org.awaitility.Awaitility

class DistributionCenterAppIntegrationTest {

    @Test
    fun `should consume from distribution center topic and persist in db`() {
        // given

        val dcConfigurationCode = "DC1"
        val dcConfiguration = DistributionCenterConfiguration(
            SUNDAY,
            "M1",
            ZoneOffset.UTC,
            "GL",
            true,
        )

        val record: ProducerRecord<String, DistributionCenterConfiguration> =
            ProducerRecord(DC_TOPIC, dcConfigurationCode, dcConfiguration)

        // when
        topicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = dsl.selectFrom(DC_CONFIG).fetch()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    records = dsl.selectFrom(DC_CONFIG).fetch()
                    records.count() == 1
                }.let {
                    assertDcConfig(dcConfigurationCode, dcConfiguration, records.first())
                }

            app.cancel()
        }
    }

    @Test
    fun `should consume from distribution center topic and persist in db removing trimming quotes from code`() {
        // given

        val dcConfigurationCodeQuotes = "\"DC1\""
        val dcConfigurationCode = dcConfigurationCodeQuotes.trim('\"')
        val dcConfiguration = DistributionCenterConfiguration(
            SUNDAY,
            "M1",
            ZoneOffset.UTC,
            "GL",
            true,
        )

        val record: ProducerRecord<String, DistributionCenterConfiguration> =
            ProducerRecord(DC_TOPIC, dcConfigurationCodeQuotes, dcConfiguration)

        // when
        topicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = dsl.selectFrom(DC_CONFIG).fetch()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    records = dsl.selectFrom(DC_CONFIG).fetch()
                    records.count() == 1
                }.let {
                    assertDcConfig(dcConfigurationCode, dcConfiguration, records.first())
                }

            app.cancel()
        }
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(DC_CONFIG).execute()
    }

    companion object {
        private lateinit var topicProducer: KafkaProducer<String, DistributionCenterConfiguration>
        private lateinit var consumerConfig: Map<String, String>

        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        private lateinit var app: DistributionCenterApp

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            startKafkaAndCreateTopics(
                listOf(DC_TOPIC),
            )

            topicProducer = createKafkaProducer(
                Serdes.String().serializer(),
                Serializer<DistributionCenterConfiguration> { _, dcConfig ->
                    objectMapper.writeValueAsBytes(dcConfig)
                },
            )
            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "sku-demand-forecast.distribution-center-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
                DeserializationExceptionStrategyType.LOG_ERROR_FAIL.name,
            )
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())

            app = DistributionCenterApp(
                SimpleMeterRegistry(),
                consumerConfig = consumerConfig,
                pollConfig = pollConfig,
                DistributionCenterRepository(dsl),
            )
        }
    }
}
