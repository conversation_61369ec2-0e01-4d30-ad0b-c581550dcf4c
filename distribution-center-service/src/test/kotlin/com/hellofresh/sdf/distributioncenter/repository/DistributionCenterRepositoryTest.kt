package com.hellofresh.sdf.distributioncenter.repository

import InfraPreparation.getMigratedDataSource
import com.hellofresh.sdf.distributioncenter.model.DistributionCenterConfiguration
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skudemandforecast.distributioncenterservice.schema.Tables
import com.hellofresh.skudemandforecast.distributioncenterservice.schema.tables.records.DcConfigRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SUNDAY
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class DistributionCenterRepositoryTest {

    @Test
    fun `dc configurations are persisted`() {
        val dcConfigurations = mapOf(
            "DC1" to DistributionCenterConfiguration(
                SUNDAY,
                "M1",
                ZoneOffset.UTC,
                "GL1",
                true,
            ),
            "DC2" to DistributionCenterConfiguration(
                MONDAY,
                "M2",
                ZoneId.of("Asia/Tokyo"),
                "GL2",
                false,
            ),
        )

        runBlocking {
            distributionCenterRepository.upsert(dcConfigurations)
        }

        val results = dsl.selectFrom(Tables.DC_CONFIG).fetch()

        assertEquals(dcConfigurations.size, results.size)

        results.forEach { record ->
            dcConfigurations[record.dcCode]!!.also {
                assertEquals(it.market, record.market)
                assertEquals(it.productionStart.name, record.productionStart)
                assertEquals(it.zoneId.id, record.zoneId)
                assertEquals(it.globalDc, record.globalDc)
                assertEquals(it.enabled, record.enabled)
            }
        }
    }

    @Test
    fun `dc configurations are update if exist`() {
        val dcConfigurationCode = "DC1"
        val dcConfiguration = DistributionCenterConfiguration(
            SUNDAY,
            "M1",
            ZoneOffset.UTC,
            "GL1",
            true,
        )

        runBlocking {
            distributionCenterRepository.upsert(mapOf(dcConfigurationCode to dcConfiguration))
        }

        val records = dsl.selectFrom(Tables.DC_CONFIG).fetch()
        assertEquals(1, records.size)
        assertDcConfig(dcConfigurationCode, dcConfiguration, records.first())

        val dcConfiguration2 = DistributionCenterConfiguration(
            MONDAY,
            "M2",
            ZoneId.of("Asia/Tokyo"),
            "GL2",
            false,
        )
        runBlocking {
            distributionCenterRepository.upsert(mapOf(dcConfigurationCode to dcConfiguration2))
        }
        val records2 = dsl.selectFrom(Tables.DC_CONFIG).fetch()
        assertEquals(1, records2.size)
        assertDcConfig(dcConfigurationCode, dcConfiguration2, records2.first())
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var distributionCenterRepository: DistributionCenterRepository
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            distributionCenterRepository = DistributionCenterRepository(dsl)
        }

        fun assertDcConfig(
            dcCode: String,
            dcConfiguration: DistributionCenterConfiguration,
            dcConfigRecord: DcConfigRecord
        ) {
            assertEquals(dcCode, dcConfigRecord.dcCode)
            assertEquals(dcConfiguration.market, dcConfigRecord.market)
            assertEquals(dcConfiguration.productionStart.name, dcConfigRecord.productionStart)
            assertEquals(dcConfiguration.zoneId.id, dcConfigRecord.zoneId)
            assertEquals(dcConfiguration.globalDc, dcConfigRecord.globalDc)
            assertEquals(dcConfiguration.enabled, dcConfigRecord.enabled)
        }
    }
}
