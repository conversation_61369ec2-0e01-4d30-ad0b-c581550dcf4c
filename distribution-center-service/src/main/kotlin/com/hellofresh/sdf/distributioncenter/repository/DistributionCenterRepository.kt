package com.hellofresh.sdf.distributioncenter.repository

import com.hellofresh.sdf.distributioncenter.model.DistributionCenterConfiguration
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.distributioncenterservice.schema.tables.records.DcConfigRecord

private const val UPSERT_DCS = "upsert-dcs"

class DistributionCenterRepository(private val dslContext: MetricsDSLContext) {

    fun upsert(distributionCenters: Map<String, DistributionCenterConfiguration>) {
        dslContext.withTagName(UPSERT_DCS)
            .batchMerge(
                distributionCenters.map { (dcCode, distributionCenter) ->
                    DcConfigRecord().apply {
                        this.dcCode = dcCode
                        this.market = distributionCenter.market
                        this.productionStart = distributionCenter.productionStart.name
                        this.zoneId = distributionCenter.zoneId.id
                        this.globalDc = distributionCenter.globalDc
                        this.enabled = distributionCenter.enabled
                    }
                },
            ).execute().size
    }
}
