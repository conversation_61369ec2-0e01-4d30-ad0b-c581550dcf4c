package com.hellofresh.sdf.distributioncenter

import com.hellofresh.sdf.distributioncenter.repository.DistributionCenterRepository
import com.hellofresh.sdf.lib.kafka.loadKafkaConfigurations
import com.hellofresh.sdf.lib.kafka.processor.PollConfig
import com.hellofresh.skuDemandForecast.db.DBConfiguration.jooqDslContext
import com.hellofresh.skuDemandForecast.db.DatabaseConfig
import com.hellofresh.skuDemandForecast.lib.StatusServer
import com.hellofresh.skuDemandForecast.lib.runApplication
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val HEALTH_CHECK_PORT = 8081

suspend fun main() {
    runApplication {
        val numThreads = config["parallelism"].toInt()
        val dslContext = jooqDslContext(
            DatabaseConfig(
                "master-write",
                config["db.host"],
                config["db.username"],
                config["db.password"],
            ),
            false,
            numThreads,
            meterRegistry,
        )
        StatusServer.run(
            meterRegistry,
            HEALTH_CHECK_PORT,
        )

        val distributionCenterApp = DistributionCenterApp(
            meterRegistry,
            consumerConfig = config.loadKafkaConfigurations(),
            pollConfig = PollConfig(
                Duration.parse(config["poll.timeout"]),
                config["poll.interval_ms"].toLong(),
                Duration.parse(config["process.timeout"]),
                config["consumer.poison-pill.deserialization.strategy"],
            ),
            DistributionCenterRepository(dslContext),
        )

        withContext(Dispatchers.IO) {
            repeat(numThreads) {
                launch { distributionCenterApp.runApp() }
            }
        }
    }
}
