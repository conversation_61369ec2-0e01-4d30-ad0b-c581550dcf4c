parallelism=1

#Kafka
group.id=sku-demand-forecast.distribution-center-service.v2
auto.offset.reset=earliest
enable.auto.commit=false

# 5 min
max.poll.interval.ms=600000

# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

poll.interval_ms=10000
poll.timeout=PT5S
process.timeout=PT15S

consumer.poison-pill.deserialization.strategy=LOG_ERROR_IGNORE
