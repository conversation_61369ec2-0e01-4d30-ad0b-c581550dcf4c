db.host=SDF DB Host
db.password=Password to authenticate with SDF database
db.username=Password to authenticate with SDF database

aiven.username=Username to use for <PERSON><PERSON> and schema registry authentication (defaults to Gradle root project name).
aiven.password=Password to use for <PERSON>ven <PERSON> and schema registry authentication (required).

poll.timeout=Duration to wait when poll for new records from kafka brokers
poll.interval_ms=Minimum duration to wait between 2 poll requests
process.timeout=Maximum duration to wait for the process to complete
parallelism=Number of consumer concurrently polling and processing demand. This number should be less than the number of input partitions.
consumer.poison-pill.deserialization.strategy=Strategy to follow when a deserialization error happens
