# Create a namespace in Vault

## Request review of your pull request

Ask in [#tribe-reliability-platform](https://hellofresh.slack.com/archives/C3ADZN2MP) for PR
reviews for this repository.

## Vault Namespaces directory structure

All namespaces are defined under `namespaces/` directory per following structure:

```bash
namespaces
│
├── namespace-1
│   └── main.yml   # namespace-1 specific configuration
│
├── namespace-2
│   └── main.yml   # namespace-2 specific configuration
│
├── namespace-3
│   └── main.yml   # namespace-3 specific configuration
│
├── defaults.yml   # default configuration for a namespace
└── terragrunt.hcl # terragrunt configuration
```

For each namespace directory CI will run `terragrunt plan/apply` with generated `include { path = find_in_parent_folders() }` configuration.
Terragrunt configuration will combine `defaults.yml` and `main.yml` from specific namespace directory and use the merged configuration as inputs for `terraform-modules/namespace` terraform module.

!!! note
    Due to limitations on the CI automation, please make sure to use the .yml extension in your files. Using files with the .yaml extension will not be considered by the automation.

## Namespace configuration example

Namespace configuration is defined in `main.yml` file within namespace directory. When `terragrunt apply` runs it will merge `main.yml` with `default.yml` (`main.yml` keys take precedence).

Example YAML configuration

```yaml
ownership:
  tribe: reliability-platform
  squad: cloud-runtime

environments:                        # List of environments that the application runs on.
  tools: {}                          # For each environment will be created default KV secrets engine
  live: {}                           # and kubernetes auth method. It is possible to override common
  staging: {}                        # kuberentes auth methods parameters like allowed namespaces and
  ahoy:                              # service_accounts here
    auth_methods:
      kubernetes:
        namespaces: ["*"]            # This is a special use case for ahoy to use * wildcard for namespace since ahoy spins up dynamic namespaces

vault:                               # Vault configuration section
  root_namespace: services           # existing vault namespace where the new vault namespace
  auth_methods:                      # should be created.
    kubernetes:                      # Auth methods definition for the namespace: (kubernetes/github_actions/azure_oidc)
      namespaces: ["netbox"]
      service_accounts: [netbox]     # PLEASE bind this to s specific service account used by the pods of the service deployed in k8s which needs access to vault
    azure_oidc:                      # This usually should be set to emails of Squad/Tribe Leads
      az_group_owners:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      az_additional_groups:          # ONLY required in case of additional AZ groups for access which are not provided by default
        - sample

rabbitmq:
  enabled: true
  vhosts: ["/"]
  tags: ["admin"]
```

## YAML parameters list

| Name | Description                                                                                                                                                                                                                                                                                                                             | Default |
| ---- |-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------| ------- |
| `ownership.tribe` | Owner tribe                                                                                                                                                                                                                                                                                                                             | `""` |
| `ownership.squad` | Owner squad                                                                                                                                                                                                                                                                                                                             | `""` |
| `vault.root_namespace` | Root namespace in vault, where the new namespace is located. It is set by default to `services` so the namespace for `my-service` repo will be `services/my-service`. WARNING: This field is immutable! If a namespace has to be moved in a different root namespace, it should be deleted and re-created in the right `root_namespace` | `services` |
| `vault.auth_methods.github_actions_jwt.enabled` | Enable auth method for [Github Actions OIDC][1]                                                                                                                                                                                                                                                                                         | `true` |
| `vault.auth_methods.kubernetes.enabled` | Boolean flag that controls if kubernetes auth method should be created for each environment. It can be overriden per environment                                                                                                                                                                                                        | `true` |
| `vault.auth_methods.kubernetes.namespaces` | Required if `vault.auth_methods.kubernetes.enabled` is true. A list of kubernetes namespaces names which are allowed to login to the vault namespace using kubernetes auth method                                                                                                                                                       | `[]` |
| `vault.auth_methods.kubernetes.service_accounts` | Required if `vault.auth_methods.kubernetes.enabled` is true. A list of kubernetes service account names which are allowed to login to the vault namespace using kubernetes auth method                                                                                                                                                  | `[]` |
| `vault.auth_methods.azure_oidc.enabled` | Boolean flag that controls if Azure AD (SSO) auth method should be enabled for the namespace                                                                                                                                                                                                                                            | `true` |
| `vault.auth_methods.azure_oidc.az_group_owners` | A list of email addresses of Azure AD users who shall be assigned to Azure AD groups as owners. This usually should be set to emails of Squad/Tribe Leads so they can add other team members to the groups and grant them access to the vault namespace                                                                                 | `[]` |
| `vault.auth_methods.azure_oidc.az_additional_groups` | A list of AZ groups of squad/tribe who shall be assigned to Azure AD groups as members. This usually should be set to AZ Group of Squad/Tribe to the groups and grant them access to the vault namespace (_only required in case of additional AZ groups for access which are not provided by default `ownership.squad`_ )                | `[]` |
| `vault.auth_methods.aws.enabled` | Enable AWS auth method  | `true` |
| `vault.auth_methods.aws.role` | Optional role name | `default` |
| `vault.auth_methods.aws.lease_ttl` | Optional token TTL | `1h` |
| `vault.auth_methods.aws.max_lease_ttl` | Optional maximum token TTL | `24h` |
| `vault.auth_methods.aws.iam_principal_arns` | List of AWS IAM Principal ARNs that allowed to authenticate with the vault namespace | `[]` |
| `environments.<environment_name>` | A dictionary of enabled environments for the namespace. For each enabled environment there will be auth method for kubernetes, and key-value secrets engine (kv2) created. Possible environment names are `ahoy`, `au-live`, `au-staging`, `dwh`, `dwh-staging`, `dwh-spark-live`, `dwh-spark-staging`, `live`, `staging`, `tools`                                             | `{}` |
| `environments.<environment_name>.auth_methods.kubernetes.enabled` | Overrides `vault.auth_methods.kubernetes.enabled` for `<environment_name>`                                                                                                                                                                                                                                                              | |
| `environments.<environment_name>.auth_methods.kubernetes.namespaces` | Overrides `vault.auth_methods.kubernetes.namespaces` for `<environment_name>`                                                                                                                                                                                                                                                           | |
| `environments.<environment_name>.auth_methods.kubernetes.service_accounts` | Overrides `vault.auth_methods.kubernetes.service_accounts` for `<environment_name>`                                                                                                                                                                                                                                                     | |
| `rabbitmq.enabled` | Boolean flag that controls if rabbitmq secrets engine is enabled in the namespace                                                                                                                                                                                                                                                       | |
| `rabbitmq.vhosts` | Vhosts that will be enabled for the dynamic rabbitmq users                                                                                                                                                                                                                                                                              | |
| `rabbitmq.tags` | Tags that will be added for the dynamic rabbitmq users                                                                                                                                                                                                                                                                                  | |

!!! Note

    `az_additional_groups` only required in case of additional AZ groups
    for access which are not provided by default.

    example: from the file `namespace.yml`
    `ownership.squad: early-retention` this will by default add AZ group `css_iam_squad_early_retention` already as members
    hence all members of AZ group `css_iam_squad_early_retention` will get access to vault namespce via OIDC

## Validation and autocompletion

The main.yml file is validated in CI with a custom [json schema](../.json-schema/vault-namespace.json).
That schema can also be used in your editor to have inline validation,
autocompletion and documentation.

### Neovim

#### Prerequisites

- [yaml-language-server](https://github.com/redhat-developer/yaml-language-server)
- [nvim-lspconfig plugin](https://github.com/neovim/nvim-lspconfig)

#### Setup

Follow the setup guide for lspconfig and adapt the following configuration to
fit your paths:

```lua
lsp_config.yamlls.setup{
  capabilities = capabilities,
  on_attach = on_attach,
  settings = {
    yaml = {
      schemas = {
        ["/home/<USER>/Projects/hellofresh/vault-namespace-automation/.json-schema/vault-namespace.json"] = "**/vault-namespace-automation/namespaces/**/main.yml"
      }
    }
  }
}
```

### Visual Studio Code

#### Prerequisites

- [yaml extension by redhat](https://marketplace.visualstudio.com/items?itemName=redhat.vscode-yaml)

#### Setup

Once installed, visual studio code should pickup the configuration automatically.

#### Usage

Try `Ctrl`+`Space` for autocompletion.

### github.dev web editor

It's broken:
https://github.com/community/community/discussions/33933

but should work out of the box, using visual studio code's settings.


[1]: https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-hashicorp-vault
