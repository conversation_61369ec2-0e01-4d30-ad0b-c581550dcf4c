# Inventory Management US
<img src="./assets/circle.png" width=25% align="right" />

- [Inventory Management](#inventory-management)
- [Maintainers](#maintainers)
- [Production](#production)
  - [Logging](#logging)
- [Infrastructure](#infrastructure)
- [Dependencies](#dependencies)
- [Development](#development)
  - [Setup](#Setup)
  - [Setup moments regarding Apple laptops with M1 Chip](#setup-moments)
  - [Run application](#run-application)
  - [Apply DB Dump](#apply-db-dump)
  - [DB Migration](#db-migration)
  - [Using Redis and Websockets as an event bus](#event-bus)
  - [Running Tests](#running-tests)
  - [Running backend linter](#running-backend-linter)
  - [UI tests](#ui-tests)
    - [UI tests set up](#ui-tests-set-up)
    - [Ui tests Settings](#ui-tests-settings)
    - [UI tests local run and debugging](#ui-tests-local-run-and-debugging)
  - [Possible troubles](#possible-troubles)
  - [IDE settings](#ide-settings)
    - [Run backend](#run-backend)
    - [Run frontend](#run-frontend)
    - [Connect to databases (database navigator plugin)](#connect-to-databases-database-navigator-plugin)
    - [UI: Linting / Code Formatting](#UI:-Linting-/-Code-Formatting)
      - [IDE plugins integrations](#ide-plugins-integrations)
      - [Rules configuration](#Rules-configuration)
- [Useful links](#useful-links)
  - [Ordering tool](#ordering-tool)

# Inventory Management

Our frontend is based on *VueJS* and our backend on *Python*. Please, use Python 3.12 to run this project. We have 
several backend containers:

* An API container exposed to the web
* A worker container that listen to a Redis queue for background jobs
* A scheduler container to launch tasks in background
* A consumer container to consume messages from kafka and perform buffered db updates

# Dependencies

Dependencies are being managed by [Poetry](https://python-poetry.org/docs/)

Install Poetry with:
```shell
curl -sSL https://install.python-poetry.org | python -
```
#### Possible issues:
SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate
#### Solution:
The latest Python versions on MacOS come with their own private copy of OpenSSL. That means the trust certificates 
in the system are no longer used as defaults by the Python ssl module. To fix that, you need to install a certifi package in your system.
```shell
open /Applications/Python\ 3.12/Install\ Certificates.command
```

Then you need to reload your terminal or run:
```shell
export PATH="/root/.local/bin:$PATH"
poetry config virtualenvs.create false
```

#### Running
For running please enable VPN. It's essential to have it enabled since the source of packages is hidden from public

To install all the project dependencies from *poetry.lock* run:
```shell
cd back
poetry install -E tests-dependencies -E dev
```

To update versions in *poetry.lock* use
```shell
poetry update
```
Make sure to commit it, in order to all the contributors use exact versions of dependencies.

#### Possible issues:
Poetry may hang in Resolving dependencies or Pending state

#### Solution:
If it lasts for a long time, check if your VPN works

Edit *pyproject.toml* file to add a new dependency: specify the version and extra (group) of the dependency. Then you will need to run:
```shell
poetry update
poetry install -E tests-dependencies -E dev
```

# Development

## Setup

1 ) Clone the repository

```shell
<NAME_EMAIL>:hellofresh/inventory-management-us.git
```

2 ) To run environment use docker-compose.yml, run the following:

```shell
docker compose up -d database cache hj
```

###### NOTE: database, cache and hj are just mandatory containers, you can run other containers if needed

#### Possible issues:
If you work from MacOS version 12.1 and higher, you may experience issue with port: tcp 0.0.0.0:7000: address already
   in use

#### Solution:
Your AirPlay receiver may start on Port 7000 and your docker may fail due to this port already being used.
You can disable this port in System Preferences > Sharing > AirPlay receiver. Then you can run docker.

There may also be problems with other ports if other applications are running on them, so you need to do the next:

Command to check all ports in use
```shell
sudo lsof -i -P | grep LISTEN | grep :$PORT
```
The next step is to find out the pid of the port that is being used.
```shell
sudo lsof -i :{PORT}
```
Last step is killing of port
```shell
kill -9 <{YOUR_PID}>
```


3 ) Then you need to set up the back-end (before that ensure that you are connected to Hello Fresh vpn):

```shell
cp back/config/config_base.json  back/config/config.json
cd back/
virtualenv venv
source venv/bin/activate
brew install unixodbc
brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
brew update
HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql18 mssql-tools18
brew install p7zip
export PATH="/root/.local/bin:$PATH"
poetry config virtualenvs.create false
```
###### ask to share a document googlesheets.config.json with you and place it here: back/config/googlesheets.config.json

Add zip file password to back/config/config.json (ask team for the password):
```json
{
  "googlesheet_sa": {
    "password": "<password>"
  }
}
```
or use environment variables GOOGLE_SHEETS_CONFIG or GOOGLE_SHEETS_CONFIG_PATH (you will need decrypted googlesheet service account key)

To update google sheet config use:
```shell
7z a ./back/config/googlesheets/<zip> <file> -p<password>
```

4 ) User & Roles setup for local development
It's better to setup local database from dump. Check [Apply DB Dump](#apply-db-dump) section.

5 ) Set up the front-end:

* Install node.js(version **18**) <a name="install-node"></a>
To do that use **NVM (Node.js Version Manager)**. Within this tool, you can 
easily install and switch between different Node.js versions. Here is the [installation tutorial for macOS](https://tecadmin.net/install-nvm-macos-with-homebrew/).

* Install UI dependencies:

```bash
cd front/
npm run install:all:deps
```
npm run install:husky (for UI devs only)

## Setup *moments*

There was encountered some issues with installing backend dependencies on laptops with *Apple M1 Chip*.
`CompileError: command '/usr/bin/clang' failed with exit code 1`
This error we got for several packages. In particular: grpcio, confluent-kafka and pyodbc.
To fix them there were installed few libs via *brew* and specified appropriate paths in *.zshrc* or *.bash_profile*, whatever.
Commands and config values are attached underneath.

```
brew install openssl
brew install pkg-config
brew install librdkafka
poetry config installer.no-binary pyodbc
```

```
//.zshrc
# fix for laptops with Apple M1 Chip
export LIBRARY_PATH=$LIBRARY_PATH:/usr/local/opt/openssl/lib/
export GRPC_PYTHON_BUILD_SYSTEM_OPENSSL=1
export GRPC_PYTHON_BUILD_SYSTEM_ZLIB=1
export CPPFLAGS="-I/opt/homebrew/opt/openssl/include -I/opt/homebrew/Cellar/unixodbc/2.3.11/include"
export PKG_CONFIG_PATH="/opt/homebrew/opt/openssl@1.1/lib/pkgconfig"
export CPATH=/opt/homebrew/Cellar/librdkafka/2.0.2/include
export C_INCLUDE_PATH=/opt/homebrew/Cellar/librdkafka/2.0.2/include
export LIBRARY_PATH=/opt/homebrew/Cellar/librdkafka/2.0.2/lib
export LDFLAGS="-L/opt/homebrew/Cellar/unixodbc/2.3.11/lib -L/opt/homebrew/opt/openssl/lib"
export CFLAGS=" -I/opt/homebrew/opt/openssl/include"
```

## Run application

* Backend dev API server (`back` folder): `python run.py`
* Backend worker (`back` folder): `export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES && export APP_TYPE=worker && rq worker -c procurement.core.worker_config -w worker.ProcurementWorker --with-scheduler`
* Backend consumer (`back` folder): `export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES && export APP_TYPE=consumer && python consumer.py`
* Backend scheduler (`back` folder): `python clock.py`  (most likely you won't need it)
* Frontend dev server (`front` folder): `npm run dev:vue:start`
* Frontend websocket server (`front` folder): `npm run dev:nest:start`
* Frontend build static (`front` folder): `npm run build`

## Apply DB Dump

First store you postgres password in ~/.pgpass to avoid the password promt, or you can skip this
and then enter password manually.
```bash
echo localhost:5432:inventory_management_us:inventory_management_us:your_password >> ~/.pgpass
chmod 600 ~/.pgpass
```

Then go to `back/init` and execute `apply_dump.sh` providing DB connection args and the dump archive as the last argument.
You will need pgdump tool installed on your machine. You can install libq to avoid installing Postrgesql itself.
```bash
# brew install libpq  # if you need pgdump
cd back/init
./apply_dump.sh -h localhost -p 5432 -d inventory_management_us -U inventory_management_us ~/Downloads/dump_202108211922.sql.gz
```

Then you will need to flush Redis cache. Go to cache container:

for example
```bash
docker exec -it inventory-management-us_cache_1 bash
```
or use docker dashboard to enter container's CLI.

Then connect to Redis itself 
```bash
redis-cli -n 1
# and execute
FLUSHALL
# should return OK
```

## DB Migration
[Yoyo](https://ollycope.com/software/yoyo/latest/#migration-files) - service for DB migration.
If you want to change DB schema, you have to change your entity and add corresponding fixes to back/migration.

## Event Bus
Redis and Node.js webserver are communicating via channel `imt:notifications:v{current_channel_version}`.
Message should have the further structure:

```js
{
  event: string;
  data: object;
}
 ```

## Running Websockets

How to test websockets:
* run webserver in dev mode `npm run dev:nest:start`
* run command `docker ps` in order to get redis container ID
* run redis cli `docker exec -it {containerID} redis-cli`
* now we can publish events using command `PUBLISH {channel} {message}`
* check the webserver's console

## Running Unit
Tests are running using [Tox](https://pypi.org/project/tox/) - virtualenv management and test command-line tool

To run tests follow the steps below:
* Go to the ``/back`` folder
* Execute ``poetry install -E tests-dependencies -E dev``
* Run the following command:<br>
``tox`` - run all tests <br>
``tox -e unit`` - run unit tests <br>
``tox -e unit ./unit/test_ordering_tool.py`` - run specific unit test

## Running backend linter

Before commit, run `tox -e pep8` to check correctness of code style.

If black shows errors, run `tox -e auto_format` to fix black issues.


## UI tests
UI test section should be reviewed by QA's

### UI tests set up
The chrome, chromedriver, [allure](https://docs.qameta.io/allure/) should be downloaded. The correct version of
chromedriver will be installed automatically after running any test if it doesn't exist or is out of date.

The allure CLI can be installed via homebrew `brew install allure`.

### UI tests settings
There is a "headless" mode in `ui_test_config.json`.

So, it can be switched to false and there you can see chrome launched, so it will be easier to debug test.

Otherwise, you can get generated report from allure with screenshots via `allure serve` after tests execution.

### Running/debugging tests locally
You can use terminal for running each application or setup IDE configurations (recommended).

When setting up IDE configuration set `/back` as working directory for API, Worker and Test.\
For Python interpreter register the created earlier *venv* in your IDE and choose it as interpreter in run configurations.

###### NOTE: the following steps assume you've already done dependency installation (app, tests and UI)

Run all commands from the project's root:
###### if `$(pwd)` does not work for you just place absolute project's root path there

1) Start containers for test environment (to not alter your applied development db dump and apply it again)
```shell
docker compose -f ci/docker-compose-local.yml up -d database cache hj
 ```

2) Start backend app  
```shell
export CONFIG=$(pwd)/automated_tests/configs/config_local.json  # env variables in IDE setup
cd back  # working directory in IDE setup
python run.py  # script path in IDE setup (without "python")
 ```

3) Start ui app (if you are going to run UI tests)
```shell
cd front
npm run test:ui:start
```

4) Start worker (if you are going to run synchronization tests)
```shell
export CONFIG=$(pwd)/automated_tests/configs/config_local.json
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export APP_TYPE=worker
cd back
rq worker -c procurement.core.worker_config -w worker.ProcurementWorker --with-scheduler default ot-queue
```

5) Run tests
```shell
export CONFIG=$(pwd)/automated_tests/configs/config_local.json
cd back
python -m pytest automated_tests/tests/api/imt/po_status/test_po_status.py --alluredir=/tmp/allure-results --timeout=10000
# --timeout=10000 to not fail tests by timeout during debugging sessions, add as additional arguments in IDE setup
```

If we use --alluredir we can check after run tests logs and screenshots using command `allure serve /tmp/allure-results`.


## Possible issues:
1. On Mac you can get message like:
``objc[52004]: +[__NSCFConstantString initialize] may have been in progress in another thread when fork() was called.``
Datails about this problem: <https://stackoverflow.com/questions/50168647/multiprocessing-causes-python-to-crash-and-gives-an-error-may-have-been-in-progr>

Solution:
```shell
$ nano ~/.bash_profile

# add this line
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
#

$ source ~/.bash_profile
```
Restart IDE to apply this config if you're runnig code from IDE.
 
2. >           raise ConnectionError(err, request=request)
   >           E           requests.exceptions.ConnectionError: ('Connection aborted.', FileNotFoundError(2, 'No such file or directory'))

Solution:

Remake the link to the docker.sock file in home dir
```shell
sudo ln -s ~/.docker/run/docker.sock /var/run/docker.sock
```

### Generate coverage report
This section should be reviewed by QA's

To check current test coverage you need to run the backend using coverage library.
Open a new terminal tab and run the backend:
```shell
cd back
export PYTHONPATH={YOUR_PROJECT_PATH}/inventory-management-us/back:{YOUR_PROJECT_PATH}/inventory-management-us/back/tests
export CONFIG={YOUR_PROJECT_PATH}/automated_tests/configs/config_local.json;
export GOOGLE_SHEETS_CONFIG_PATH={YOUR_PROJECT_PATH}/automated_tests/configs/googlesheets.config.json
coverage run --branch --source=procurement run.py
```
Then you can run tests in another terminal tab or in your IDE. 
Wait until the test run is finished and kill the backend.
(You need to kill the process that was launched with coverage,
otherwise the report won't be generated). 
You can interrupt it with CTRL+C or find a backend process ID using
and kill it softly (don't use a force kill, it will interrupt generation of the report)
```shell
lsof -t -i:8000 |xargs kill -2
```
Or you can softly kill a process by its name:
```shell
pkill -f "run.py"
```
After that you can generate a coverage report
```shell
coverage html
```
Or if you want to exclude some files from it, use --omit flag
```shell
coverage html --omit="*/__init__.py*,*/procurement/core/*,*procurement/repository/*,*procurement/models/*,*procurement/util/*"
```

## IDE settings

### Backend
Start environment with docker compose file in the project's root
```shell
docker compose up -d database cache hj
```
To run backend and worker use [Running/debugging tests guide](#running/debugging-tests-locally)\
without specifying `CONFIG` env variable (or pointing it to `back/config/config.json` created during [setup](#setup))\
and skipping docker compose step since dev and test envs are separate

### Run frontend
<img src="./assets/run_front.png"/>

### UI: Linting / Code Formatting

#### IDE plugins integrations

* Prettier - <https://prettier.io/docs/en/editors.html>
<img src="./assets/prettier_idea.png"/>
* ESlint - <https://eslint.org/docs/4.0.0/user-guide/integrations>

#### Rules configuration

* Formatting rules config location: `front/.prettierrc.json`
* Lint rules config location: `front/.eslintrc.js`

# Useful links
## Ordering tool

|         | dev                                                                                                       | prod                                                                 |
|---------|-----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------|
| OT      | <https://staging-operations.hellofresh.com/en/api>                                                        | <https://operations.hellofresh.com/en/api>                           |
| secrets | <https://www-staging.hellofresh.com/gw/api/auth/oauth2/client/access_token?grant_type=client_credentials> | <https://gw.hellofresh.com/auth/token?grant_type=client_credentials> |
