@file:Suppress("MagicNumber")

package com.hellofresh.skudemandforecast.recipeimport.model

import com.hellofresh.skuDemandForecast.models.RecipeSnapshot
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import kotlin.random.Random

fun RecipeSnapshot.Companion.default() =
    RecipeSnapshot(
        slotNumber = 1,
        week = "2024-W12",
        country = "DE",
        locale = "DE",
        boxType = "classic-box",
        skuCode = "skuCode",
        skuName = "skuName",
        slotItemName = "slotItemName",
        p1 = 1,
        p2 = 1,
        p3 = 1,
        p4 = 1,
        p5 = 1,
        p6 = 1,
        dcs = null,
    )

fun RecipeSnapshot.Companion.random() =
    with(Random(System.nanoTime())) {
        RecipeSnapshot(
            slotNumber = nextInt(0, 1000),
            week = DcWeek(
                LocalDate.now().plusDays(nextLong(0, 60)),
                DayOfWeek.of(nextInt(1, DayOfWeek.entries.count())),
            ).toString(),
            country = UUID.randomUUID().toString(),
            locale = UUID.randomUUID().toString(),
            boxType = UUID.randomUUID().toString(),
            skuCode = UUID.randomUUID().toString(),
            skuName = UUID.randomUUID().toString(),
            slotItemName = UUID.randomUUID().toString(),
            p1 = 1,
            p2 = 1,
            p3 = 1,
            p4 = 1,
            p5 = 1,
            p6 = 1,
            dcs = null
        )
    }
