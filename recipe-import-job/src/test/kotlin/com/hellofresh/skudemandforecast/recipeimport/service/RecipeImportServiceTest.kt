package com.hellofresh.skudemandforecast.recipeimport.service

import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.distributioncenter.default
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import kotlin.test.Test
import kotlin.test.assertEquals
import org.junit.jupiter.api.AfterEach

class RecipeImportServiceTest {

    private val mockDate = LocalDate.parse("2024-01-24")
    private val expectedDcWeeks = listOf(
        "2024-W04", "2024-W05", "2024-W06", "2024-W07", "2024-W08", "2024-W09",
    ) + (10..21).map { "2024-W$it" }

    @AfterEach
    fun afterEach() {
        unmockkAll()
    }

    @Test
    fun `should prepare the valid dc weeks to fetch the recipes`() {
        val dc = DistributionCenter.Companion.default().copy(
            productionStart = DayOfWeek.MONDAY,
            zoneId = ZoneOffset.UTC,
            market = "Market",
        )
        mockkStatic(LocalDate::class)
        every { LocalDate.now(dc.zoneId) } returns mockDate
        val actualDcWeekList = RecipeImportService.prepareDcWeeks(
            listOf(dc),
        )
        assertEquals(expectedDcWeeks.sorted(), actualDcWeekList.sorted())
    }

    @Test
    fun `should prepare the valid dc weeks from MPS Config weekStart`() {
        val dc = DistributionCenter.Companion.default().copy(
            productionStart = DayOfWeek.MONDAY,
            zoneId = ZoneOffset.UTC,
            market = "Market",
        )

        mockkStatic(LocalDate::class)
        every { LocalDate.now(dc.zoneId) } returns mockDate
        val actualDcWeekList = RecipeImportService.prepareDcWeeks(
            listOf(dc),
        )
        assertEquals(expectedDcWeeks.sorted(), actualDcWeekList.sorted())
    }

    @Test
    fun `should get the earliest production start day`() {
        val dcConfigs = listOf(
            DistributionCenter(
                dcCode = "IT",
                market = "IT",
                productionStart = DayOfWeek.MONDAY,
                zoneId = ZoneId.of("Europe/Rome"),
                enabled = true,
                globalDc = null,
            ),
            DistributionCenter(
                dcCode = "VE",
                market = "DACH",
                productionStart = DayOfWeek.THURSDAY,
                zoneId = ZoneId.of("Europe/Berlin"),
                enabled = true,
                globalDc = null,
            ),
            DistributionCenter(
                dcCode = "BV",
                market = "GB",
                productionStart = DayOfWeek.FRIDAY,
                zoneId = ZoneId.of("Europe/London"),
                enabled = true,
                globalDc = null,
            ),
        )
        val earliestProductionStart = RecipeImportService.getEarliestProductionStart(
            dcConfigs,
        )
        assertEquals(DayOfWeek.MONDAY, earliestProductionStart.first)
        assertEquals(ZoneId.of("Europe/Rome"), earliestProductionStart.second)
    }
}
