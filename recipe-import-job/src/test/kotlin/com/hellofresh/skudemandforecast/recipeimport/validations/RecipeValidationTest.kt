package com.hellofresh.skudemandforecast.recipeimport.validations

import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skudemandforecast.recipeimport.model.Recipe
import com.hellofresh.skudemandforecast.recipeimport.model.random
import com.hellofresh.skudemandforecast.recipeimport.validations.Violation.DuplicateSkuInARecipe
import com.hellofresh.skudemandforecast.recipeimport.validations.Violation.InvalidWeekFormat
import com.hellofresh.skudemandforecast.recipeimport.validations.Violation.NegativeNumbers
import java.util.UUID
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test

class RecipeValidationTest {

    @Test fun `returns no error when recipe is valid`() {
        assertTrue {
            Recipe.random()
                .validate()
                .isEmpty()
        }
    }

    @Test fun `returns error when there is invalid quantity in picks`() {
        val errors = Recipe.random().copy(
            skuPicks = listOf(
                SkuPicks(
                    UUID.randomUUID().toString(),
                    (1..6).map {
                        Pick(it, -1) // bad pick
                    },
                ),
            ),
        ).validate()

        assertEquals(1, errors.size)
        assertEquals(NegativeNumbers, errors.first())
    }

    @Test fun `returns error when sku is repeated`() {
        val sku = SkuPicks(
            UUID.randomUUID().toString(),
            (1..6).map {
                Pick(it, 1) // bad pick
            },
        )

        val errors = Recipe.random()
            .copy(skuPicks = listOf(sku, sku, sku))
            .validate()

        assertEquals(1, errors.size)
        assertEquals(DuplicateSkuInARecipe, errors.first())
    }

    @Test fun `return all the errors when multiple errors exist`() {
        val errors = Recipe.random().copy(
            week = "some week",
            recipeIndex = -1,
        )
            .validate()

        assertEquals(2, errors.size)
        assertContentEquals(
            listOf(NegativeNumbers, InvalidWeekFormat),
            errors,
        )
    }
}
