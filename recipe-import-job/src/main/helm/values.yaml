---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  app:
    deploymentStrategy:
      type: Recreate
    replicaCount: 1
    minAvailable: 0
    containerPorts:
      http: 8080
      http-actuator: 8081
      jmx: '@jmxPort@'
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '2000Mi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    env:
        HF_SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
    spotInstance:
      preferred: true
    startupProbe:
       httpGet:
          path: /startup
          port: 8081
       initialDelaySeconds: 30
       timeoutSeconds: 3
       failureThreshold: 10
       periodSeconds: 5
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 60
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
