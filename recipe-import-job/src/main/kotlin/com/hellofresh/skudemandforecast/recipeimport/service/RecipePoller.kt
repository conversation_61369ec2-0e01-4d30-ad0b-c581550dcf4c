package com.hellofresh.skudemandforecast.recipeimport.service

import com.hellofresh.skuDemandForecast.models.RecipeSnapshot
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skuDemandForecast.recipelib.MpsClient
import com.hellofresh.skudemandforecast.recipeimport.model.Recipe
import java.util.Collections.emptyList

private const val PEOPLE_COUNT_ONE = 1
private const val PEOPLE_COUNT_TWO = 2
private const val PEOPLE_COUNT_THREE = 3
private const val PEOPLE_COUNT_FOUR = 4
private const val PEOPLE_COUNT_FIVE = 5
private const val PEOPLE_COUNT_SIX = 6

const val UNKNOWN_BRAND = "UNKNOWN"
const val DEFAULT_DC_CODE = "ALL"

private val marketMapping = mapOf("BENELUXFR" to "BENELUX")

class RecipePoller(
    private val mpsClient: MpsClient
) {
    fun getPicklistByMarketAndWeeksAndBrands(market: String, weeks: Set<String>, brand: String?): List<Recipe> =
        mpsClient.getRecipes(marketMapping.getOrDefault(market, market), weeks, brand)?.body()?.items?.let { items ->
            items.map { item ->
                item.toRecipeKey(brand) to item
            }.groupBy(
                keySelector = { it.first },
                valueTransform = { it.second },
            ).map { (recipeKey, valueParam) ->
                createRecipes(recipeKey, valueParam)
            }
        } ?: emptyList()

    private fun createRecipes(
        recipeKey: RecipeKey,
        valueParam: List<RecipeSnapshot>
    ): Recipe =
        Recipe(
            recipeIndex = recipeKey.recipeIndex,
            week = recipeKey.week,
            productFamily = recipeKey.productFamily,
            country = recipeKey.country,
            locale = recipeKey.locale,
            brand = recipeKey.brand,
            dcCode = recipeKey.dcCode,
            skuPicks = valueParam.map { recipe ->
                SkuPicks(
                    skuCode = recipe.skuCode,
                    picks = (1..6).map { peopleCount ->
                        val pickValue = when (peopleCount) {
                            PEOPLE_COUNT_ONE -> recipe.p1
                            PEOPLE_COUNT_TWO -> recipe.p2
                            PEOPLE_COUNT_THREE -> recipe.p3
                            PEOPLE_COUNT_FOUR -> recipe.p4
                            PEOPLE_COUNT_FIVE -> recipe.p5
                            PEOPLE_COUNT_SIX -> recipe.p6
                            else -> throw IllegalArgumentException("Invalid people count: $peopleCount")
                        }
                        Pick(peopleCount = peopleCount, picks = pickValue)
                    },
                )
            }
        )

    companion object {
        fun RecipeSnapshot.toRecipeKey(brand: String?) =
            RecipeKey(
                recipeIndex = slotNumber,
                week = week,
                productFamily = boxType,
                country = country,
                locale = getLocale(country, locale),
                brand = getBrand(brand),
                dcCode = dcs?.first() ?: DEFAULT_DC_CODE,
            )

        private fun getLocale(country: String, mpsLocale: String?): String? =
            when (country) {
                "AU", "NZ" -> ""
                else -> mpsLocale
            }

        private fun getBrand(brand: String?): String =
            when (brand) {
                HELLO_FRESH -> "HF"
                EVERY_PLATE -> "EP"
                else -> UNKNOWN_BRAND
            }
    }
}
