package com.hellofresh.skudemandforecast.recipeimport.validations

sealed class Violation {
    // A violation message is returned if a violation is found
    abstract val message: String
    override fun toString() = message

    // User had uploaded negative quantities within the demand quantities file
    data object NegativeNumbers : Violation() {
        override val message = "Negative number value"
    }

    data object BlankString : Violation() {
        override val message: String = "Blank string"
    }

    // Week format is HF Week - 2023-W02
    data object InvalidWeekFormat : Violation() {
        override val message: String = "Week format is not valid, expected - 2023-W08"
    }

    data object DuplicateSkuInARecipe : Violation() {
        override val message: String = "Sku is added multiple times in a recipe"
    }
}

sealed class NoViolation : Violation() {
    override val message: String = ""
}
