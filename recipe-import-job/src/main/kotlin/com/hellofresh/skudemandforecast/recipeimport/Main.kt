package com.hellofresh.skudemandforecast.recipeimport

import com.hellofresh.cif.slack.SlackClient
import com.hellofresh.sdf.shutdown.shutdownNeeded
import com.hellofresh.service.Config
import com.hellofresh.skuDemandForecast.authserviceclient.model.ApplicationConfig
import com.hellofresh.skuDemandForecast.db.DBConfiguration
import com.hellofresh.skuDemandForecast.db.DatabaseConfig
import com.hellofresh.skuDemandForecast.lib.Application
import com.hellofresh.skuDemandForecast.lib.StatusServer
import com.hellofresh.skuDemandForecast.lib.runApplication
import com.hellofresh.skuDemandForecast.recipelib.MpsClient
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.lib.job.KrontabScheduler
import com.hellofresh.skudemandforecast.lib.job.MeteredJob
import com.hellofresh.skudemandforecast.recipeimport.repository.ImportsRepositoryImpl
import com.hellofresh.skudemandforecast.recipeimport.repository.RecipeRepository
import com.hellofresh.skudemandforecast.recipeimport.service.RecipeImportService
import com.hellofresh.skudemandforecast.recipeimport.service.RecipePoller
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.TimeUnit.MINUTES

private const val HTTP_PORT = 8081
private const val READ_WRITE_DB_POOL_SIZE = 5
private val singleThreadJobExecutor = ThreadPoolExecutor(
    1,
    1,
    Long.MAX_VALUE,
    HOURS,
    ArrayBlockingQueue(1, true),
    CallerRunsPolicy(),
).also { shutdownNeeded { AutoCloseable { it.shutdownNow() } } }

fun main() {
    runApplication {
        StatusServer.run(meterRegistry, HTTP_PORT)

        val scheduler = shutdownNeeded {
            KrontabScheduler(
                period = this.config["job.time_minutes"].toInt(),
                timeUnit = MINUTES,
                executor = singleThreadJobExecutor,
            )
        }
        val applicationConfig = applicationConfig()
        val readWriteDslContext = readWriteDslContext()
        val dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(readWriteDslContext))

        val mpsClient = MpsClient(applicationConfig, config["menu.planning.service"], meterRegistry)
        val slackClient = slackClient()

        val recipeImportService = RecipeImportService(
            dslContext = readWriteDslContext,
            recipePoller = RecipePoller(mpsClient),
            dcConfigService = dcConfigService,
            recipeRepository = RecipeRepository(),
            importRepository = ImportsRepositoryImpl(),
            notSupportedMarkets = config.notSupportedMarkets,
            slackClient = slackClient,
        )

        scheduler.schedule {
            MeteredJob(meterRegistry, "recipe-import-job", recipeImportService::process).execute()
        }
    }
}

private fun Application.databaseConfig(name: String) =
    DatabaseConfig(
        configName = name,
        hostName = config["db.host"],
        userName = config["db.username"],
        password = config["db.password"],
    )

private fun Application.readWriteDslContext() =
    DBConfiguration.jooqDslContext(
        databaseConfig = this.databaseConfig("readWrite"),
        readOnly = false,
        parallelism = READ_WRITE_DB_POOL_SIZE,
        meterRegistry = meterRegistry,
    )

private fun Application.applicationConfig() =
    ApplicationConfig(
        authServiceUrl = config["user.auth.service"],
        applicationName = config["application.name"],
        clientId = config["user.auth.service.client.id"],
        clientSecret = config["user.auth.service.client.secret"],
        userName = config["user.auth.service.user.name"],
        password = config["user.auth.service.user.password"],
        authClientTimeout = config["auth.client.call-timeout"],
    )

private fun Application.slackClient() =
    shutdownNeeded { SlackClient(config["slack.webhook"], config["send.slack.notifications.to.markets"].toBoolean()) }

private val Config.notSupportedMarkets: Set<String>
    get() =
        get("recipe.client.not.supported.markets")
            .trim()
            .split(',')
            .toSet()
