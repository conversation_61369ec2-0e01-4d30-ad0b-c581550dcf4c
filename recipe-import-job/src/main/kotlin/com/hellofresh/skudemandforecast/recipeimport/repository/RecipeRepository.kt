package com.hellofresh.skudemandforecast.recipeimport.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skudemandforecast.recipeimport.model.Recipe
import com.hellofresh.skudemandforecast.recipeimport.service.RecipeKey
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.Tables.RECIPE_SNAPSHOT
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.tables.records.RecipeSnapshotRecord
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB
import org.jooq.impl.DSL

const val NULL_LOCALE_VALUE = ""

class RecipeRepository {

    fun selectRecipes(txDsl: MetricsDSLContext, countryWeeks: Set<CountryWeek>) =
        txDsl.withTagName("select-recipe-snapshot")
            .selectFrom(RECIPE_SNAPSHOT)
            .where(
                DSL.row(RECIPE_SNAPSHOT.COUNTRY, RECIPE_SNAPSHOT.WEEK)
                    .`in`(countryWeeks.map { DSL.row(it.country, it.week) }.toSet()),
            )
            .fetch()
            .map {
                Recipe(
                    recipeIndex = it.recipeIndex,
                    week = it.week,
                    productFamily = it.productFamily,
                    country = it.country,
                    locale = if (it.locale == NULL_LOCALE_VALUE) null else it.locale,
                    brand = it.brand,
                    skuPicks = objectMapper.readValue<RecipeValue>(it.value.data()).skus,
                    dcCode = it.dcCode,
                )
            }.toSet()

    fun save(txDsl: MetricsDSLContext, sourceId: UUID, recipes: List<Recipe>) {
        txDsl.withTagName("upsert-import-recipes")
            .batchMerge(recipes.map { it.toRecipeRecord(sourceId) })
            .execute()
        logger.info("Updated ${recipes.size} recipes.")
    }

    fun delete(txDsl: MetricsDSLContext, recipeKeys: Set<RecipeKey>) {
        val deleted = txDsl.withTagName("delete-old-import-recipes")
            .batch(
                recipeKeys.map { key ->
                    txDsl.deleteFrom(RECIPE_SNAPSHOT)
                        .where(RECIPE_SNAPSHOT.RECIPE_INDEX.eq(key.recipeIndex))
                        .and(RECIPE_SNAPSHOT.WEEK.eq(key.week))
                        .and(RECIPE_SNAPSHOT.PRODUCT_FAMILY.eq(key.productFamily))
                        .and(RECIPE_SNAPSHOT.COUNTRY.eq(key.country))
                        .and(RECIPE_SNAPSHOT.LOCALE.eq(key.locale))
                },
            )
            .execute()
        logger.info("Deleted ${deleted.sum()} recipes.")
    }

    companion object : Logging {

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        fun Recipe.toRecipeRecord(sourceId: UUID) =
            this.let { recipe ->
                RecipeSnapshotRecord().apply {
                    recipeIndex = recipe.recipeIndex
                    week = recipe.week
                    productFamily = recipe.productFamily
                    country = recipe.country
                    locale = recipe.locale ?: NULL_LOCALE_VALUE
                    brand = recipe.brand
                    this.sourceId = sourceId
                    dcCode = recipe.dcCode
                    value = JSONB.valueOf(objectMapper.writeValueAsString(RecipeValue(recipe.skuPicks)))
                    dirtyBit = true
                }
            }
    }
}

data class CountryWeek(
    val country: String,
    val week: String
)
