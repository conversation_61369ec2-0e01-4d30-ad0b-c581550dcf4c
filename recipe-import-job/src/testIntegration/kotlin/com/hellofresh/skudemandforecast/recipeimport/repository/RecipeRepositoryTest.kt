package com.hellofresh.skudemandforecast.fileconsumer.repository

import com.hellofresh.skudemandforecast.recipeimport.FunctionalTest
import com.hellofresh.skudemandforecast.recipeimport.model.Import
import com.hellofresh.skudemandforecast.recipeimport.model.Recipe
import com.hellofresh.skudemandforecast.recipeimport.model.random
import com.hellofresh.skudemandforecast.recipeimport.repository.CountryWeek
import com.hellofresh.skudemandforecast.recipeimport.repository.ImportsRepositoryImpl
import com.hellofresh.skudemandforecast.recipeimport.repository.RecipeRepository
import com.hellofresh.skudemandforecast.recipeimport.service.RecipeImportService.Companion.toRecipeKey
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.enums.FileStatus.PENDING
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RecipeRepositoryTest : FunctionalTest() {

    private val sourceId = UUID.randomUUID()
    private val import = Import(sourceId, "WEEK", "MARKET")

    @BeforeEach
    fun beforeEach() {
        ImportsRepositoryImpl().save(dsl, import, PENDING)
    }

    @Test
    fun `recipes are inserted when table is empty`() {
        val recipes = listOf(Recipe.random(), Recipe.random())
        runBlocking { RecipeRepository().save(dsl, sourceId, recipes) }

        assertRecipes(recipes)
    }

    @Test
    fun `fetch existing recipes`() {
        val recipes = listOf(Recipe.random(), Recipe.random())
        runBlocking { RecipeRepository().save(dsl, sourceId, recipes) }

        assertRecipes(recipes)

        assertRecipes(
            RecipeRepository().selectRecipes(dsl, recipes.map { CountryWeek(it.country, it.week) }.toSet()).toList(),
        )
    }

    @Test
    fun `recipes are upserted when already exist`() {
        val recipe1 = Recipe.random()
        val recipe2 = Recipe.random()
        val recipes = listOf(recipe1, recipe2)
        runBlocking { RecipeRepository().save(dsl, sourceId, recipes) }

        assertRecipes(recipes)

        val newSourceId = UUID.randomUUID()
        ImportsRepositoryImpl().save(dsl, import.copy(id = newSourceId), PENDING)
        val updateRecipe1 = recipe1.copy(
            skuPicks = Recipe.random().skuPicks,
        )
        val updateRecipe2 = recipe2.copy(
            skuPicks = Recipe.random().skuPicks,
        )
        val updatedRecipes = listOf(updateRecipe1, updateRecipe2)

        runBlocking { RecipeRepository().save(dsl, newSourceId, updatedRecipes) }

        assertRecipes(updatedRecipes) {
            assertEquals(newSourceId, this.sourceId)
        }
    }

    @Test
    fun `recipes are deleted by primary key`() {
        val recipe1 = Recipe.random()
        val recipe2 = Recipe.random()
        val recipe3 = Recipe.random()
        val recipes = listOf(recipe1, recipe2, recipe3)

        runBlocking { RecipeRepository().save(dsl, sourceId, recipes) }

        assertRecipes(recipes) {
            assertEquals(sourceId, this.sourceId)
        }

        runBlocking { RecipeRepository().delete(dsl, listOf(recipe1, recipe2).map { it.toRecipeKey() }.toSet()) }

        assertEquals(1, dsl.fetchCount(Tables.RECIPE_SNAPSHOT))
    }
}
