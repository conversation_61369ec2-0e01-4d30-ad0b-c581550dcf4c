package com.hellofresh.skudemandforecast.recipeimport

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.db.metrics.withMetrics
import com.hellofresh.skuDemandForecast.models.RecipeSnapshots
import com.hellofresh.skuDemandForecast.models.db.Pick
import com.hellofresh.skuDemandForecast.models.db.RecipeValue
import com.hellofresh.skuDemandForecast.models.db.SkuPicks
import com.hellofresh.skudemandforecast.distributionCenter.DcConfigService
import com.hellofresh.skudemandforecast.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.skudemandforecast.recipeimport.model.Recipe
import com.hellofresh.skudemandforecast.recipeimport.repository.ImportsRepositoryImpl
import com.hellofresh.skudemandforecast.recipeimport.repository.RecipeRepository
import com.hellofresh.skudemandforecast.recipeimport.service.RecipePoller.Companion.toRecipeKey
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.Tables
import com.hellofresh.skudemandforecast.recipeimportjob.schema.public_.tables.records.RecipeSnapshotRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    fun assertRecipes(recipes: List<Recipe>, assertBlock: RecipeSnapshotRecord.() -> Unit = {}) {
        val recipeRecords = dsl.selectFrom(Tables.RECIPE_SNAPSHOT).fetch()
        recipes.forEach {
            with(recipeRecords.first { record -> record.recipeIndex == it.recipeIndex }) {
                assertRecipe(it, this)
                this.assertBlock()
            }
        }
    }

    private fun assertRecipe(recipe: Recipe, recipeRecord: RecipeSnapshotRecord) {
        Assertions.assertTrue(recipeRecord.dirtyBit)
        Assertions.assertEquals(recipe.recipeIndex, recipeRecord.recipeIndex)
        Assertions.assertEquals(recipe.week, recipeRecord.week)
        Assertions.assertEquals(recipe.productFamily, recipeRecord.productFamily)
        Assertions.assertEquals(recipe.country, recipeRecord.country)
        Assertions.assertEquals(recipe.locale, recipeRecord.locale)
        Assertions.assertEquals(recipe.brand, recipeRecord.brand)
        assertEquals(
            recipe.skuPicks.associateBy({ it.skuCode }) { it.picks.associateBy({ it.peopleCount }) { it.picks } },
            objectMapper.readValue<RecipeValue>(recipeRecord.value.data()).skus
                .associateBy({ it.skuCode }) { it.picks.associateBy({ it.peopleCount }) { it.picks } },
        )
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(Tables.RECIPE_SNAPSHOT).execute()
        dsl.deleteFrom(Tables.IMPORTS).execute()
    }

    companion object {

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        lateinit var dcConfigService: DcConfigService
        lateinit var recipeRepository: RecipeRepository
        lateinit var importRepository: ImportsRepositoryImpl
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigService = DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
            recipeRepository = RecipeRepository()
            importRepository = ImportsRepositoryImpl()
        }
    }
}

fun RecipeSnapshots.toRecipes() = items.groupBy { it.toRecipeKey(null) }
    .map { (key, group) ->
        Recipe(
            recipeIndex = key.recipeIndex,
            week = key.week,
            productFamily = key.productFamily,
            country = key.country,
            locale = key.locale,
            brand = key.brand,
            dcCode = key.dcCode,
            skuPicks = group.map {
                SkuPicks(
                    it.skuCode,
                    listOf(
                        Pick(1, it.p1),
                        Pick(2, it.p2),
                        Pick(3, it.p3),
                        Pick(4, it.p4),
                        Pick(5, it.p5),
                        Pick(6, it.p6),
                    ),
                )
            },
        )
    }
