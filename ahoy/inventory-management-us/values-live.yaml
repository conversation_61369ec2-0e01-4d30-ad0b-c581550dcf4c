# Environment tag
environment: &environment live
tag: latest
tribe: &tribe
squad: &squad
slack_alert_channel: &slack_alert_channel ext-hellofresh-grid-dynamics-alerts

vaultNamespace: services/inventory-management-us

deployments:
  app:
    replicaCount: 2
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us
    pullPolicy: Always
    resources:
      limits:
        memory: "2Gi"
      requests:
        memory: "1Gi"
    nodeSelector: {}
    tolerations: []
    affinity: {}
    podAnnotations:
      sidecar.istio.io/inject: "true"
    hpa:
      enabled: false
    extraContainers:
      - containerSpec:
          name: backend
          env:
            - name: APP_TYPE
              value: BACK
            - name: CONFIG
              value: 'vault:live/key-value/data/config#CONFIG'
            - name: GOOGLE_SHEETS_CONFIG
              value: 'vault:live/key-value/data/config#GOOGLE_SHEETS_CONFIG'
          ports:
            - name: back-http
              containerPort: 8000
              protocol: TCP

      - image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us-front
        containerSpec:
          name: frontend
          ports:
            - name: front-http
              containerPort: 8080
              protocol: TCP
          env:
            - name: VUE_APP_AG_GRID_KEY
              value: 'vault:common/key-value/data/config#VUE_APP_AG_GRID_KEY'

      - image: repo.tools-k8s.hellofresh.io/minio/minio
        tag: RELEASE.2020-07-14T19-14-30Z
        containerSpec:
          name: minio
          args:
              - "gateway"
              - "s3"
          ports:
            - name: http
              containerPort: 9000
              protocol: TCP
          env:
            - name: MINIO_ACCESS_KEY
              value: 'vault:common/key-value/data/config#MINIO_ACCESS_KEY'
            - name: MINIO_SECRET_KEY
              value: 'vault:common/key-value/data/config#MINIO_SECRET_KEY'

  consumer:
    replicaCount: 3
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us
    pullPolicy: Always
    resources:
      limits:
        cpu: "1"
        memory: "1Gi"
      requests:
        cpu: "1"
        memory: "512Mi"
    nodeSelector: {}
    tolerations: []
    affinity: {}
    hpa:
      enabled: false
    extraContainers:
      - containerSpec:
          name: consumer
          command: ["python"]
          args: ["consumer.py"]
          env:
            - name: APP_TYPE
              value: CONSUMER
            - name: CONFIG
              value: 'vault:live/key-value/data/config#CONFIG'
            - name: GOOGLE_SHEETS_CONFIG
              value: 'vault:live/key-value/data/config#GOOGLE_SHEETS_CONFIG'
          ports:
            - name: http
              containerPort: 80
              protocol: TCP

  worker:
    replicaCount: 5
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us
    pullPolicy: Always
    resources:
      limits:
        cpu: "1"
        memory: "1.5Gi"
      requests:
        cpu: "1"
        memory: "1Gi"
    nodeSelector: {}
    tolerations: []
    affinity: {}
    hpa:
      enabled: false
    extraContainers:
      - containerSpec:
          name: worker
          env:
            - name: APP_TYPE
              value: WORKER
            - name: CONFIG
              value: 'vault:live/key-value/data/config#CONFIG'
            - name: GOOGLE_SHEETS_CONFIG
              value: 'vault:live/key-value/data/config#GOOGLE_SHEETS_CONFIG'
          command: ["rq"]
          args:
            - worker
            - -c
            - procurement.core.worker_config
            - -w
            - worker.ProcurementWorker
            - --with-scheduler
          ports:
            - name: http
              containerPort: 80
              protocol: TCP

  scheduler:
    replicaCount: 1
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us
    pullPolicy: Always
    resources: {}
    nodeSelector: {}
    tolerations: []
    affinity: {}
    hpa:
      enabled: false
    extraContainers:
      - containerSpec:
          name: scheduler
          env:
            - name: APP_TYPE
              value: CLOCK
            - name: CONFIG
              value: 'vault:live/key-value/data/config#CONFIG'
            - name: GOOGLE_SHEETS_CONFIG
              value: 'vault:live/key-value/data/config#GOOGLE_SHEETS_CONFIG'
          command: ["python"]
          args:
            - clock.py


services:
  worker:
    enabled: true
    type: ClusterIP
    ports:
      http: 80
    enablePrometheus: true
    metrics:
      - endpoint: /
        port: http
  front:
    enabled: true
    type: LoadBalancer
    LBports:
      front: 8080
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:eu-west-1:835487002321:certificate/1e4d7282-483d-407a-b53c-27aca3e0aaf7"
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
      service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: '3600'
      external-dns.alpha.kubernetes.io/hostname: inventory-management-us.live-k8s.hellofresh.io
      service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
      service.beta.kubernetes.io/aws-load-balancer-security-groups: "sg-009a745315032020b"

  back:
    enabled: true
    type: ClusterIP
    ports:
      back-http: 80
    enablePrometheus: true
    metrics:
      - endpoint: /metrics
        port: back-http

configMap:
  PORT: "8080"
  VUE_APP_ENV: "live"
  VUE_APP_API_URL: "https://imt-api.hellofresh.com"
  WORKER_METRICS_PORT: "80"

# Provision dashboards to grafana
provisionDashboards:
  enabled: true
  dashboardLabel: grafana_dashboard

rds-prom-exporter:
  environment: *environment
  tribe: *tribe
  squad: *squad
  alerts_config:
    slack: *slack_alert_channel
  config:
    - instance: "inventory-management-us-db000-live"
      alerts:
        RDSConnectionNumberHigh:
          threshold: 50
          for: 10m
          severity: P3
        RDSDiskIsXPercentFull:
          threshold: 80
          severity: P2
        RDSCPUUsageHigh:
          threshold: 60
          for: 5m
          severity: P2
        RDSBurstBalanceLow:
          threshold: 85
          for: 5m
          severity: P3
    - instance: "inventory-management-us-db001-live"
      alerts:
        RDSConnectionNumberHigh:
          threshold: 50
          for: 10m
          severity: P3
        RDSDiskIsXPercentFull:
          threshold: 80
          severity: P2
        RDSCPUUsageHigh:
          threshold: 60
          for: 5m
          severity: P2
        RDSBurstBalanceLow:
          threshold: 85
          for: 5m
          severity: P3

slos:
  - kind: http
    name: inventory-management-us-app-http
    objective: 99
    highBurnRate:
      severity: P4
    lowBurnRate:
      severity: P5
    spec:
      badStatusCodesRegex: "429|5.*"
      latencyMS: "30000"
      serviceFilter: "inventory-management-us-app"

# Istio
istio:
  back:
    enabled: true
    virtualService:
      routes:
        - name: "backend"
          route:
            - destination:
                host: inventory-management-us-back-k8s
      enabled: true
      timeout: 120s                       # Timeout for HTTP requests
    destinationRule:
      enabled: true
      connectionPool:                     # Comment to disable circuit breaker. See https://istio.io/docs/reference/config/networking/v1alpha3/destination-rule/#ConnectionPoolSettings
        http:
          http1MaxPendingRequests: 1024   # Maximum number of pending HTTP requests to a destination.
          maxRequestsPerConnection: 1024  # Maximum number of requests per connection to a backend.
      outlierDetection:                   # See https://istio.io/docs/reference/config/networking/v1alpha3/destination-rule/#OutlierDetection
        consecutive5xxErrors: 5              # Number of errors before a host is ejected from the connection pool
        interval: 10s                     # Time interval between ejection sweep analysis.
        baseEjectionTime: 30s             # Minimum ejection duration.
        maxEjectionPercent: 20            # Maximum % of hosts in the load balancing pool that can be ejected
  front:
    enabled: false

prometheusRules:
  - name: inventory_management_us.alert.rules
    rules:
      - alert: Synchronization Failed
        annotations:
          summary: 'Detected failed synchronization job'
          description: 'Detected failed synchronization job on live environment. {{ $labels.method }} - {{ $value }}'
        expr: sum by (method) (increase(datasync_measurement{result!="success"}[2m])) > 0
        labels:
          slack: *slack_alert_channel
          severity: not-critical

      - alert: Critical component failed
        annotations:
          summary: 'Detected failed with one of the critical system component'
          description: 'Detected failed {{ $labels.service_type }} system. Message: {{ $labels.message }}'
        expr: sum(increase(exceptions_total{app="InventoryManagement", severity="critical"}[2m])) by (service_type, message) > 0
        labels:
          slack: *slack_alert_channel
          severity: critical
