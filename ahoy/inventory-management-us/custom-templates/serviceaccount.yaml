{{- range $key, $value := .Values.deployments }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "inventory-management-us.fullname" $ }}-{{ $key }}
  labels:
    {{- include "inventory-management-us.labels" $ | nindent 4 }}
    app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
  {{- if or ($.Values.serviceAccountAnnotations) ($value.serviceAccountAnnotations) }}
  annotations:
    {{- with $.Values.serviceAccountAnnotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with $value.serviceAccountAnnotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
