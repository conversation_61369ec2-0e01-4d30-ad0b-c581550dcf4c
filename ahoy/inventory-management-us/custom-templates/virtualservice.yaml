{{- $length := len (.Values.services | default list) }}
{{- range $key, $value := .Values.istio }}
{{- $services := $.Values.services }}
{{- $currentService := index $services $key }}
{{- if and ($value.enabled) ($currentService.enabled) -}}
{{- $fullName := include "inventory-management-us.fullname" $ -}}
{{- $gatewayEnabled := false }}
{{- if $value.gateway }}
  {{- $gatewayEnabled = and $value.gateway.enabled $value.gateway.hosts }}
{{- end  }}
{{- $usePublicGateway := false }}
{{- if and (eq $.Values.environment "staging") $value.publicGateway }}
  {{- $usePublicGateway = and $value.publicGateway.enabled $value.publicGateway.hosts }}
{{- end  }}
---
kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: {{ template "inventory-management-us.name" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}
  labels:
    {{- include "inventory-management-us.labels" $ | nindent 4 }}
    app: {{ template "inventory-management-us.name" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}
spec:
  hosts:
  {{- if $gatewayEnabled }}
    {{- toYaml $value.gateway.hosts | nindent 4 }}
  {{- end }}
    - {{ template "inventory-management-us.name" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}-k8s.{{ $.Release.Namespace }}.svc.cluster.local
  {{- if $usePublicGateway }}
    {{- toYaml $value.publicGateway.hosts | nindent 4 }}
  {{- end }}
  gateways:
  {{- if $gatewayEnabled }}
  - {{ template "inventory-management-us.name" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}
  {{- end }}
  {{- if $usePublicGateway }}
  - {{ template "inventory-management-us.name" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}-pub
  {{- end }}
  - mesh
  http:
    {{- if $value.virtualService }}
    {{- with $value.virtualService.routes }}
    {{- toYaml . | nindent 2 }}
    {{- end }}
    {{- end }}
    {{- if $value.virtualService }}
    {{- with $value.virtualService.retries }}
    retries: {{- toYaml . | trimSuffix "\n" | nindent 6 }}
    {{- end }}
    {{- with $value.virtualService.timeout }}
    timeout: {{ . }}
    {{- end }}
    {{- end }}
---
{{- end }}
{{ end }}
