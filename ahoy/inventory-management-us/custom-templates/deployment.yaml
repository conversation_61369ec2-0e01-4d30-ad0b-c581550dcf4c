{{- range $key, $value := .Values.deployments }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "inventory-management-us.fullname" $ }}-{{ $key }}
  labels:
    app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
    chart: {{ template "inventory-management-us.chart" $ }}
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
    tribe: {{ $.Values.tribe }}
    squad: {{ $.Values.squad }}
    version: "{{ if $.Values.tag }}{{ $.Values.tag }}{{ else }}{{ $.Chart.AppVersion }}{{ end }}"
    helm-gen-tpl: default
    helm-gen-version: 0.0.1
    helm-gen-custom: "true"
    {{- if $value.spotInstance }}
    {{- if $value.spotInstance.preferred }}
    spot: "true"
    {{- end }}
    {{- end }}
spec:
  replicas: {{ $value.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
      release: {{ $.Release.Name }}
  template:
    metadata:
      labels:
        app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
        release: {{ $.Release.Name }}
        environment: {{ $.Values.environment }}
        tribe: {{ $.Values.tribe }}
        squad: {{ $.Values.squad }}
        version: "{{ if $.Values.tag }}{{ $.Values.tag }}{{ else }}{{ $.Chart.AppVersion }}{{ end }}"
        {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
        spot: "true"
        {{- end }}
        {{- end }}
      annotations:
        {{- include "inventory-management-us.vaultAnnotations" $ | nindent 8 }}
      {{- if or ($.Values.podAnnotations) ($value.podAnnotations) }}
        {{- with $.Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with $value.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      serviceAccountName: {{ if $value.serviceAccountName }}{{ $value.serviceAccountName }}{{ else }}{{ template "inventory-management-us.fullname" $ }}-{{ $key }}{{ end }}
      dnsConfig:
        options:
          - name: ndots
            value: "1"
      containers:
        {{- range $key, $container := $value.extraContainers }}
        - image: {{ printf "%s:%s" (default $value.repository $container.image) (default $.Values.tag $container.tag) }}
          imagePullPolicy: {{ $container.imagePullPolicy | default $value.pullPolicy }}
        {{- toYaml $container.containerSpec | nindent 10 }}
          {{- if or ($.Values.configMap) ($.Values.secrets) }}
          envFrom:
          {{- if $.Values.configMap }}
          - configMapRef:
              name: {{ template "inventory-management-us.fullname" $ }}
          {{- end }}
          {{- if $.Values.secrets }}
          - secretRef:
              name: {{ template "inventory-management-us.fullname" $ }}
          {{- end }}
          {{- end }}
          resources:
{{ toYaml $value.resources | indent 12 }}
          {{- end }}

    {{- with $value.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if $value.affinity }}
      {{- with $value.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
      {{- end }}
    {{- else }}
      {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: eks.amazonaws.com/capacityType
                operator: In
                values:
                - SPOT
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if $value.tolerations }}
      {{- with $value.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
      {{- end }}
    {{- else }}
      {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
      tolerations:
      - key: "spotInstance"
        operator: "Exists"
        {{- end}}
      {{- end}}
    {{- end }}
{{- end }}
