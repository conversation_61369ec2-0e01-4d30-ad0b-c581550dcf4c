{{- $length := len (.Values.services | default list) }}
{{- range $key, $value := .Values.services }}
{{- if and $value.enabled $value.enablePrometheus }}
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "inventory-management-us.labels" $ | nindent 4 }}
    app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
    prometheus: kube-prometheus
  name: {{ template "inventory-management-us.fullname" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}-k8s
spec:
  endpoints:
  {{- if $value.metrics }}
  {{- range $metric := $value.metrics }}
  - interval: 30s
    path: {{ $metric.endpoint }}
    port: {{ $metric.port }}
  {{- end }}
  {{- end}}
  jobLabel: {{ template "inventory-management-us.name" $ }}-{{ $key }}
  namespaceSelector:
    matchNames:
    - "{{ $.Release.Namespace }}"
  selector:
    matchLabels:
      app: {{ template "inventory-management-us.name" $ }}-{{ $key }}
  sampleLimit: {{ $value.sampleLimit | default 5000}}
{{- end }}
{{- end }}
