{{- $length := len (.Values.services | default list) }}
{{- range $key, $value := .Values.services }}
{{- if $value.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "<CHARTNAME>.fullname" $ }}{{ if gt $length 1 }}-{{ $key }}{{ end }}-k8s
  labels:
    {{- include "<CHARTNAME>.labels" $ | nindent 4 }}
    app: {{ template "<CHARTNAME>.name" $ }}-{{ $key }}
  {{- with $value.annotations }}
  annotations: {{- toYaml . | nindent 4}}
  {{- end }}
spec:
  type: {{ $value.type }}
  ports:
  {{- if $value.LBports }}
    - port: 80
      targetPort: {{ $value.LBports.front }}
      protocol: TCP
      name: http
    - port: 443
      targetPort: {{ $value.LBports.front }}
      protocol: TCP
      name: https
  {{- else }}
  {{- range $label,$port := $value.ports }}
    - port: {{ $port  }}
      targetPort: {{ $label }}
      protocol: TCP
      name: {{ $label }}
  {{- end }}
  {{- end }}
  selector:
    {{- include "<CHARTNAME>.selectorLabels" $ | nindent 4 }}
    {{- if or (eq $key "back") (eq $key "front") }}
    app: {{ template "<CHARTNAME>.name" $ }}-app
    {{- else }}
    app: {{ template "<CHARTNAME>.name" $ }}-{{ $key }}
    {{- end }}
{{- end }}
{{- end }}
