import com.google.cloud.tools.jib.gradle.JibTask

plugins {
    id("com.hellofresh.sdf.common-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

description = "Sku Specification lib"
group = "$group.sku-specification-lib"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(project(":lib"))
    api(project(":lib:db"))
    api(project(":lib:models:sku-specification"))
    api(project(":lib:models:distribution-center"))
    api(libs.coroutines.jdk8)

    testImplementation(testFixtures(project(":lib")))
    testImplementation(testFixtures(project(":lib:models:distribution-center")))
}

jooq {
    configurations {

        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("DB_JOOQ_PORT_SDF")
                val dbUrl = "***************************************"
                logger.info("generating meta for $dbUrl.")
                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = dbUrl
                    user = "sdf"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "sku_specification"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                }
            }
        }
    }
}

tasks.withType<JibTask>().configureEach {
    enabled = false
}
