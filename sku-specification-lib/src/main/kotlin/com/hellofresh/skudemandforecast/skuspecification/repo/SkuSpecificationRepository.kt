package com.hellofresh.skudemandforecast.skuspecification.repo

import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skudemandforecast.model.distributioncenter.DistributionCenter
import com.hellofresh.skudemandforecast.model.skuspecification.SkuCodeLookUp
import com.hellofresh.skudemandforecast.model.skuspecification.SkuSpecification
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.tables.SkuSpecification.SKU_SPECIFICATION
import com.hellofresh.skudemandforecast.sku_specification_lib.schema.public_.tables.records.SkuSpecificationRecord
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL.lower

interface SkuSpecificationRepository {

    suspend fun fetchSkuCodeLookUp(distributionCenters: List<DistributionCenter>): SkuCodeLookUp
    suspend fun fetchSkuSpecification(markets: Set<String>): Map<UUID, SkuSpecification>
}

class SkuSpecificationRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : SkuSpecificationRepository {

    override suspend fun fetchSkuCodeLookUp(distributionCenters: List<DistributionCenter>): SkuCodeLookUp {
        val dcsByMarket = distributionCenters.groupBy { it.market.uppercase() }
        val skusByMarket = fetchSkuSpecification(dcsByMarket.keys)
            .values.groupBy { it.market.uppercase() }
            .mapValues { (_, skus) -> skus.associateBy { it.code } }

        val skuIdByDcAndSkuCode: Map<String, Map<String, SkuSpecification>> =
            dcsByMarket
                .flatMap { (market, dcs) ->
                    val skus = skusByMarket[market]?.values ?: emptyList()
                    dcs.map { dc ->
                        dc.dcCode to skus.associateBy { it.code }
                    }
                }.toMap()

        return SkuCodeLookUp { dcCode, skuCode -> skuIdByDcAndSkuCode[dcCode]?.get(skuCode) }
    }

    override suspend fun fetchSkuSpecification(markets: Set<String>): Map<UUID, SkuSpecification> =
        metricsDSLContext.withTagName("fetch-sku-by-market")
            .selectFrom(SKU_SPECIFICATION)
            .where(lower(SKU_SPECIFICATION.MARKET).`in`(markets.map { it.lowercase() }))
            .fetchAsync()
            .await()
            .associateBy { it.id }
            .mapValues { (_, skuRecord) -> toSkuSpecification(skuRecord) }

    private fun toSkuSpecification(skuRecord: SkuSpecificationRecord) =
        with(skuRecord) {
            SkuSpecification(
                id = id,
                code = code,
                market = market,
                parentId = parentId,
                fumigatedAllowed = fumigationAllowed,
                packaging = packaging,
            )
        }
}
