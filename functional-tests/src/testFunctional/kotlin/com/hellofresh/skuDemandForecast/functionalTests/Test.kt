package com.hellofresh.skuDemandForecast.functionalTests

import com.hellofresh.kafka.streams.processors.PassThroughProcessor
import org.apache.kafka.streams.Topology

class Test : FunctionalTest(
    Topology().apply {
        addSource("src", "inputTopic")
        addProcessor<String, String, String, String>("proc", { PassThroughProcessor<String, String>() }, "src")
        addSink("sink", "outputTopic", "proc")
    },
    templateValues = mapOf("veDcCode" to "ve"),
)
