plugins {
    id("com.hellofresh.sdf.kotlin-conventions")
    hellofresh.`test-fixtures`
    `test-functional`
}

group = "$group.${project.name}"
description = "The module implements a generic way to functionally tests a service based on Kafka streams."

dependencies {
    testImplementation(libs.hellofresh.kafka.streams)
    testFixturesImplementation(libs.junit.params)
    testFixturesImplementation(project(":lib:models"))
    testFunctionalImplementation(project(":lib"))
    testFunctionalImplementation(libs.flyway.core)
    testFunctionalImplementation(libs.postgresql.driver)
    testFunctionalImplementation(libs.testcontainers.postgresql)
    testFunctionalImplementation(libs.testcontainers.junit)
    testFunctionalImplementation(libs.hikaricp)
    testFunctionalRuntimeOnly(project(":sku-demand-forecast-db"))
    testFunctionalRuntimeOnly(libs.flyway.core)

    testFixturesApi(testFixtures(libs.hellofresh.kafka.streams))
    testFixturesImplementation(libs.protobuf.java.format)
}
