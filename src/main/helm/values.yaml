
---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

provisionDashboards:
  enabled: true
  dashboardLabel: 'grafana_dashboard'

rds-prom-exporter:
    environment: '@tier@'
    tribe: '@tribe@'
    squad: '@squad@'

    config:
      - instance: 'sku-demand-forecast-db000-@tier@'
        alerts:
          RDSDiskWillFillInXHours: { }
          RDSConnectionNumberHigh: { }
          RDSDiskIsXPercentFull: { }
          RDSCPUUsageHigh:
            threshold: 80
            for: 5m
          RDSMemoryUsageHigh: { }
          RDSDiskReadLatencyIncrease: { }

prometheus-cloudwatch-exporter:
    serviceAccount:
        annotations:
            eks.amazonaws.com/role-arn: 'arn:aws:iam::************:role/procurement-inventory-live-staging'

    serviceMonitor:
        enabled: true
        interval: 1m
        labels:
            prometheus: sku-demand-forecast-sqs-nordics-demand-@tier@

    config: |-
        region: eu-west-1
        period_seconds: 60
        delay_seconds: 30
        set_timestamp: false
        use_get_metric_data: true
        metrics:
        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateAgeOfOldestMessage
          aws_statistics: [Maximum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: []

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesDelayed
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesNotVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfEmptyReceives
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesDeleted
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesReceived
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesSent
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: SentMessageSize
          aws_statistics: [Average, Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["hf_nordics_ip_recipe_forecast_notifications_queue_@tier@"]

alerts:
  systemRules:
    - name: '@<EMAIL>'
      rules:
        - alert: '@projectName.upperCamelCase@AppNotRunning'
          expr: '(sum(kube_pod_container_status_running{container=~".*@projectKey@.*",namespace="scm"}) by (container)) < 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'App Not Running'
            description: 'No instance found for the past 5 Minutes'

        - alert: '@projectName.upperCamelCase@AppRestarts'
          expr: 'sum(increase(kube_pod_container_status_restarts_total{container=~".*@projectKey@.*",namespace="scm"}[20m])) by (container) > 1'
          for: '20m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'App Restarted Multiple Times'
            description: 'Instance restarted {{ humanize $value }} during the past 20 Minutes'

        - alert: '@projectName.upperCamelCase@CPUThrottle'
          expr: 'sum(increase(container_cpu_cfs_throttled_periods_total{namespace="scm", pod=~".*@projectKey@.*", container!="POD", container!=""}[5m])) by (container, pod, namespace) / sum(increase(container_cpu_cfs_periods_total{namespace="@tribe@", pod=~".*@projectKey@.*", container!="POD", container!=""}[5m])) by (container, pod, namespace) > 20'
          for: '10m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'CPU is throttling'
            description: 'CPU is throttling {{ humanize $value }}% for the past 10 Minutes'

        - alert: '@projectName.upperCamelCase@NoReplicas'
          expr: 'abs(kube_deployment_status_replicas_available{namespace="scm", deployment=~".*@projectKey@.*"}) < 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'No replicas running'
            description: 'No replicas running for the past 1 Minute'

        - alert: '@projectName.upperCamelCase@CPUUsageTooHigh'
          expr: '(sum(increase(container_cpu_usage_seconds_total{namespace="scm", pod=~".*@projectKey@.*", container!="POD", container!=""}[1m])) by (pod) / sum(kube_pod_container_resource_limits_cpu_cores{namespace="@tribe@", pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod)) > 0.8 AND ON(pod) abs(kube_deployment_status_replicas_available{namespace="@tribe@", deployment=~".*@projectKey@.*"}) == 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'CPU Usage is Too High'
            description: 'CPU usage is too high and there is no room to scale up (CPU usage + max number of replicas configured reached)'

        - alert: '@projectName.upperCamelCase@MemoryUsageTooHigh'
          expr: '(sum(container_memory_working_set_bytes{namespace="scm",pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod) / sum(kube_pod_container_resource_limits_memory_bytes{namespace="@tribe@",pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod)) > 0.8'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Memory Usage is Too High'
            description: 'Memory usage is too high and there is no room to scale up (Memory usage + max number of replicas configured reached)'

        - alert: '@projectName.upperCamelCase@DeserializationError'
          expr: 'sum(rate(csku_inventory_forecast_component_failure_total{name="deserialization"}[1m])) by (topic) > 0'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Deserialization Errors'
            description: '{{ humanize $value }} deserialization errors happened'

        - alert: '@projectName.upperCamelCase@ConsumerLag'
          expr: 'max(kafka_consumergroup_group_lag{group=~"sku-demand-forecast.*", consumer_id!~".*StreamThread.*"}) by (group) > 1000'
          for: '15m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Consumer group lag'
            description: 'Consumer group {{ $labels.name }} has lag of {{ humanize $value }}.'

        - alert: '@projectName.upperCamelCase@AggregatorLatency'
          expr: 'max(procurement_sku_demand_forecast_import_duration_seconds_max{application=~".*-job"}) by (pod) > 60' # 1m
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P2'
          annotations:
            summary: 'Job took more than 2 minutes'
            description: '{{ $labels.pod }} took {{ humanize $value }} seconds to run.'

        - alert: '@projectName.upperCamelCase@RecipeImportError400'
          expr: 'sum(increase(procurement_sku_demand_forecast_http_outgoing_seconds_count{application="recipe-import-job", status="400"}[60m])) by (pod) > 0'
          labels:
              slack: '@slackAlertChannel@-@tier@'
              severity: 'P4'
          annotations:
              summary: 'Recipe Import Errors (HTTP 400) in Last Hour'
              description: 'The recipe import job encountered {{ humanize $value }} HTTP 400 errors in the last hour.'

        - alert: '@projectName.upperCamelCase@RecipeImportError500'
          expr: 'sum(increase(procurement_sku_demand_forecast_http_outgoing_seconds_count{application="recipe-import-job", status="500"}[60m])) by (pod) > 0'
          labels:
              slack: '@slackAlertChannel@-@tier@'
              severity: 'P4'
          annotations:
              summary: 'Recipe Import Errors (HTTP 500) in Last Hour'
              description: 'The recipe import job encountered {{ humanize $value }} HTTP 500 errors in the last hour.'

        - alert: '@projectName.upperCamelCase@RecipeAggregatedDemandMismatch'
          expr: '(sum(procurement_sku_demand_forecast_recipe_agg_demand_mismatch) by (dc) + sum(procurement_sku_demand_forecast_recipe_agg_demand_missing) by (dc) ) > 0'
          for: '15m'
          labels:
              slack: '@slackAlertChannel@-@tier@'
              severity: 'P4'
          annotations:
              summary: 'Recipe Aggregated Demand count mismatch (Agg Fraction Demand vs Recipe Aggregated Demand) since last 15 mins'
              description: 'Recipe Aggregated Demand count mismatch for DC {{ $labels.dc }} since last 15 mins'

        -   alert: '@projectName.upperCamelCase@NordicsDemandSQSMessageProcessingLag'
            expr: 'sum by (queue_name) (aws_sqs_approximate_number_of_messages_visible_average{service="sdf-sku-demand-forecast-prometheus-cloudwatch-exporter"}) > 0'
            for: '10m'
            labels:
                slack: '@slackAlertChannel@-@tier@'
                severity: 'P4'
            annotations:
                summary: 'Automatic Nordics demand file SQS message was not processed for last 10 mins'
                description: 'Automatic Nordics demand file SQS message was not processed for last 10 mins'
