{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["sdf-sku-demand-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["sdf-sku-demand-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["sdf-sku-demand-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["sdf-sku-demand-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2983, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 112, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "To view the AWS SQS lag ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 1}, "id": 104, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (queue_name) (aws_sqs_approximate_number_of_messages_visible_average{service=\"sdf-sku-demand-forecast-prometheus-cloudwatch-exporter\"})", "hide": false, "instant": false, "legendFormat": "{{queue_name}}", "range": true, "refId": "B"}], "title": "AWS SQS ", "transparent": true, "type": "timeseries"}], "title": "AWS SQS Lag", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 103, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "aggregated recipe demand mismatches between fraction and agg demands", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 2}, "id": 113, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "procurement_sku_demand_forecast_recipe_agg_demand_mismatch{dc=~\"$distribution_center\"} > 0", "hide": false, "instant": false, "legendFormat": "Mismatch - {{dc}} - {{week}}", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "procurement_sku_demand_forecast_recipe_agg_demand_missing{dc=~\"$distribution_center\"} > 0", "hide": false, "instant": false, "legendFormat": "Missing - {{dc}} - {{week}}", "range": true, "refId": "A"}], "title": "Recipe Aggregated Mismatch Timeline", "transparent": true, "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "aggregated recipe demand matches between fraction and agg demands", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 12}, "id": 107, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "procurement_sku_demand_forecast_recipe_agg_demand_match{dc=~\"$distribution_center\"}", "hide": false, "instant": false, "legendFormat": "{{dc}} - {{week}}", "range": true, "refId": "B"}], "title": "Recipe Aggregated Match", "transparent": true, "type": "timeseries"}], "title": "Recipe Aggregated Demand", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 93, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 22, "x": 0, "y": 136}, "id": 83, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_mps_demand_sku_mismatch_count{dc=~\"$distribution_center\"}) by (dc, date) != 0", "hide": false, "interval": "", "legendFormat": "{{dc}} - {{date}}", "range": true, "refId": "A"}], "title": "MPS Demand Fraction Mismatch Count", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Skus found in MPS fraction demand table that are missing in new fraction table", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 146}, "id": 95, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_mps_demand_missing_sku_fraction{dc=~\"$distribution_center\"}) by (dc) != 0", "hide": false, "interval": "", "legendFormat": "{{dc}}", "range": true, "refId": "A"}], "title": "Skus Missing in New Fraction Table", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Skus found in MPS fraction demand table that are missing in new fraction table", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 146}, "id": 96, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_mps_demand_missing_sku_new_fraction{dc=~\"$distribution_center\"}) by (dc) != 0", "hide": false, "interval": "", "legendFormat": "{{dc}}", "range": true, "refId": "A"}], "title": "Skus/Dates Missing in MPS Fraction Table", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Skus/Dates combination found in new fraction demand table that are missing in MPS fraction table by dc", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 11, "x": 0, "y": 156}, "id": 97, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_mps_demand_missing_sku_fraction{dc=~\"$distribution_center\"}) by (dc, date) != 0", "hide": false, "interval": "", "legendFormat": "{{dc}} - {{date}}", "range": true, "refId": "A"}], "title": "Skus/Dates Missing in New Fraction Table", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Skus found in new fraction demand table that are missing in mps fraction table by dc and date", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 11, "x": 11, "y": 156}, "id": 85, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_mps_demand_missing_sku_new_fraction{dc=~\"$distribution_center\"}) by (dc, date) != 0", "hide": false, "interval": "", "legendFormat": "{{dc}} - {{date}}", "range": true, "refId": "A"}], "title": "Skus Missing in MPS Fraction Table", "transparent": true, "type": "bargauge"}], "title": "MPS <PERSON><PERSON>", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 88, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Number of recipes that don't match but exist in bouth sources", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 395}, "id": 89, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_recipe_mismatch_count{country=~\"$country\"}) by (country, week) != 0", "hide": false, "interval": "", "legendFormat": "{{country}} - {{week}}", "range": true, "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 10, "x": 12, "y": 395}, "id": 90, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_recipe_missing_count{country=~\"$country\"}) by (country, week) != 0", "hide": false, "interval": "", "legendFormat": "{{country}} - {{week}}", "range": true, "refId": "A"}], "title": "Recipe Missing Count", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Total number of sku missmatches in same recipe", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 475}, "id": 91, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_recipe_sku_mismatch_count{country=~\"$country\"}) by (country, week) != 0", "hide": false, "interval": "", "legendFormat": "{{country}} - {{week}}", "range": true, "refId": "A"}], "title": "Recipe <PERSON> Mismatch Count", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Total number of sku missing (exist just in one source)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 10, "x": 12, "y": 475}, "id": 92, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "af332bf6-da26-494d-8eb8-b09e897974ad"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_sku_demand_forecast_recipe_sku_missing_count{country=~\"$country\"}) by (country, week) != 0", "hide": false, "interval": "", "legendFormat": "{{country}} - {{week}}", "range": true, "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "transparent": true, "type": "bargauge"}], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 108, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Total number of missing keys in Recipe Demand", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 85}, "id": 109, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (dc,week)(procurement_sku_demand_forecast_dach_missing_in_demand_table) > 0", "hide": false, "instant": false, "legendFormat": "{{dc}} - {{week}}", "range": true, "refId": "B"}], "title": "Missing Demand", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Total number of missing keys in Recipe Demand Import", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 13, "y": 85}, "id": 110, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (dc,week) (procurement_sku_demand_forecast_dach_missing_in_demand_imported_table) > 0 ", "hide": false, "instant": false, "legendFormat": "{{dc}} - {{week}}", "range": true, "refId": "B"}], "title": "Missing Imported Demand", "transparent": true, "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Total number of mismatches recipe demand", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 16, "x": 0, "y": 96}, "id": 111, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "left", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "text": {}, "valueMode": "color"}, "pluginVersion": "11.4.0-77868", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (dc,week)(procurement_sku_demand_forecast_dach_different_meals_to_deliver_total) > 0", "hide": false, "instant": false, "legendFormat": "{{dc}} - {{week}}", "range": true, "refId": "B"}], "title": "<PERSON><PERSON><PERSON>", "transparent": true, "type": "bargauge"}], "title": "DACH Recipe Demand", "type": "row"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "metrics_live", "value": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "includeAll": false, "label": "Metrics Datasource", "name": "metrics_ds", "options": [], "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "type": "datasource"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(procurement_sku_demand_forecast_demand_sku_mismatch_count,dc)", "includeAll": true, "multi": true, "name": "distribution_center", "options": [], "query": {"qryType": 1, "query": "label_values(procurement_sku_demand_forecast_demand_sku_mismatch_count,dc)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(procurement_sku_demand_forecast_recipe_mismatch_count,country)", "includeAll": true, "multi": true, "name": "country", "options": [], "query": {"qryType": 1, "query": "label_values(procurement_sku_demand_forecast_recipe_mismatch_count,country)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "<PERSON><PERSON> Demand Forecast Checker", "uid": "dBEi5b2bsek3bMnfU10wZe1CzlHCxqG0hdaWi4eq", "version": 6, "weekStart": ""}