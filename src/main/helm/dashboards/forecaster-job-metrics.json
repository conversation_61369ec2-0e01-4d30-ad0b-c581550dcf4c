{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2423, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by() (procurement_sku_demand_forecast_forecaster_job_sku_updates{country=\"$country\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Sku Updates", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum(procurement_sku_demand_forecast_forecaster_job_recipe_updates{country=\"$country\", week=\"$production_week\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Recipe Updates", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "procurement_sku_demand_forecast_recipe_demand_mismatch{country=\"$country\", week=\"$production_week\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "<PERSON><PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "procurement_sku_demand_forecast_recipe_demand_match{country=\"$country\", week=\"$production_week\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Recipe Demand Matches", "type": "timeseries"}], "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "metrics_live", "value": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "hide": 0, "includeAll": false, "label": "Metrics Datasource", "multi": false, "name": "metrics_ds", "options": [], "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": ["2024-W19"], "value": ["2024-W19"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(production_week)", "hide": 0, "includeAll": false, "label": "Production Week", "multi": true, "name": "production_week", "options": [], "query": {"qryType": 1, "query": "label_values(production_week)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "^\\d{4}-W\\d{2}$", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": ["ES"], "value": ["ES"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(hf_country)", "hide": 0, "includeAll": false, "label": "Country", "multi": true, "name": "country", "options": [], "query": {"qryType": 1, "query": "label_values(hf_country)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-2d", "to": "now"}, "timeRangeUpdatedDuringEditOrView": false, "timepicker": {}, "timezone": "browser", "title": "Forecaster Job Metrics", "uid": "cdk6o9ydazx1cf", "version": 19, "weekStart": ""}