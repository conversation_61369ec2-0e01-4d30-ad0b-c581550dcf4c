package simulations.imt


import io.gatling.core.Predef._
import io.gatling.http.Predef._
import simulations.BaseSimulation

import scala.util.Properties


class ImtAllRegions extends BaseSimulation {
  private val apiName: String = Properties.envOrElse("API_NAME", "ingredient-depletion")
  private val randomProductFeeder = csv(s"data/$apiName-query-params.csv").random

  private val testScenario = scenario(s"Get $apiName by brand, DCs, week")
    .feed(randomProductFeeder)
    .exec(http("${caption}")
      .get("${apiUrl}?brand=${brand}&dc=${dc}&week=${week}")
      .check(status.saveAs("status"))
      .check(status.is(200)))
    .exec { session => possiblyFailedPrintout(session, "IngredientDepletion"); session }

  setUp(
    testScenario.inject(trafficPlan())
  ).protocols(httpProtocol)
}
