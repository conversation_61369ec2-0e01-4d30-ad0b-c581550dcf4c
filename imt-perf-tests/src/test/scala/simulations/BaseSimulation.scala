package simulations

import java.io.{File, FileOutputStream, PrintWriter}

import io.gatling.core.Predef._
import io.gatling.core.controller.inject.open.OpenInjectionStep
import io.gatling.http.Predef._
import io.gatling.http.protocol.HttpProtocolBuilder
import scalaj.http._
import spray.json._

import scala.collection.mutable.ListBuffer
import scala.concurrent.duration._
import scala.util.Properties

abstract class BaseSimulation extends Simulation {

  protected val minTPS: Int = Properties.envOrElse("MIN_TPS", "1").toInt
  protected val maxTPS: Int = Properties.envOrElse("MAX_TPS", "5").toInt
  protected val stepTPS: Int = Properties.envOrElse("STEP_TPS", "1").toInt
  protected val durationMinutes: Int = Properties.envOrElse("DURATION", "20").toInt
  protected val resultFolder: String = Properties.envOrElse("RESULT_FOLDER", "")
  protected val baseUrl: String = System.getenv("VUE_APP_API_URL")

  protected val httpProtocol: HttpProtocolBuilder = http.baseUrl(baseUrl)
    .authorizationHeader(s"Bearer $getToken")


  private def getToken: String = {
    val response: HttpResponse[String] = Http(s"$baseUrl/auth/login_test_user").asString

    if (response.code != 200) {
      throw new RuntimeException(s"Error retrieving token: [${response.code}] with body [${response.body}]")
    }
    response.body.parseJson.asJsObject.fields.getOrElse("access_token", "").asInstanceOf[JsString].value
  }

  protected def possiblyFailedPrintout(session: Session, callName: String): Unit = {
    if (session.baseStatus.name.equals("KO")) {
      val msg = "Request " + callName + " failed with status: " + session("status").asOption[String].getOrElse("--")
      val write = new PrintWriter(new FileOutputStream(new File(resultFolder + "error.log"), true))
      write.write(msg + "\n")
      write.close()
    }
  }

  protected def trafficPlan(): List[OpenInjectionStep] = {

    this.validateTrafficVariables()

    var userLoad = new ListBuffer[OpenInjectionStep]()
    userLoad += nothingFor(5 seconds)

    val stepsCount = 1 + ((maxTPS - minTPS) + stepTPS - 1) / stepTPS //round up an integer division
    val stepDuration = (1.0 * durationMinutes) / stepsCount
    for (i <- 0 until stepsCount) {
      val currentTPS = minTPS + (i * stepTPS)
      userLoad += constantUsersPerSec(currentTPS) during (stepDuration minutes)
    }
    userLoad.toList
  }

  private def validateTrafficVariables(): Unit = {
    validatePositiveInt(stepTPS, "STEP_TPS")
    validatePositiveInt(minTPS, "MIN_TPS")
    validatePositiveInt(maxTPS, "MAX_TPS")
    validatePositiveInt(durationMinutes, "DURATION")
    if (maxTPS < minTPS) {
      throw new TestValidationException("MAX_TPS can not be less than MIN_TPS")
    }
  }

  private def validatePositiveInt(value: Int, fieldName: String): Unit = {
    if (value < 1) {
      throw new TestValidationException(fieldName + "should be positive number")
    }
  }
}
