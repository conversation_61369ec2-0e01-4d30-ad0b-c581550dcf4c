recorder {
  core {
    #mode = "Proxy"
    #encoding = "utf-8"               # The encoding used for reading/writing request bodies and the generated simulation
    #simulationsFolder = ""           # The folder where generated simulation will be generated
    #package = ""                     # The package's name of the generated simulation
    #className = "RecordedSimulation" # The name of the generated Simulation class
    #thresholdForPauseCreation = 100  # The minimum time, in milliseconds, that must pass between requests to trigger a pause creation
    #saveConfig = false               # When set to true, the configuration from the Recorder GUI overwrites this configuration
    #headless = false                 # When set to true, run the Recorder in headless mode instead of the GUI
    #harFilePath = ""                 # The path of the HAR file to convert
  }
  filters {
    #filterStrategy = "Disabled" # The selected filter resources filter strategy (currently supported : "Disabled", "BlackList", "WhiteList")
    #whitelist = []              # The list of ressources patterns that are part of the Recorder's whitelist
    #blacklist = []              # The list of ressources patterns that are part of the Recorder's blacklist
  }
  http {
    #automaticReferer = true       # When set to false, write the referer + enable 'disableAutoReferer' in the generated simulation
    #followRedirect = true         # When set to false, write redirect requests + enable 'disableFollowRedirect' in the generated simulation
    #removeCacheHeaders = true     # When set to true, removes from the generated requests headers leading to request caching
    #inferHtmlResources = true     # When set to true, add inferred resources + set 'inferHtmlResources' with the configured blacklist/whitelist in the generated simulation
    #checkResponseBodies = false   # When set to true, save response bodies as files and add raw checks in the generated simulation
    #useSimulationAsPrefix = false # When set to true, use the simulation class name instead of 'request' as a prefix for http(s) requests
  }
  proxy {
    #port = 8000                         # Local port used by Gatling's Proxy for HTTP/HTTPS
    https {
      #mode = "SelfSignedCertificate"    # The selected "HTTPS mode" (currently supported : "SelfSignedCertificate", "ProvidedKeyStore", "CertificateAuthority")
      keyStore {
        #path = ""                       # The path of the custom key store
        #password = ""                   # The password for this key store
        #type = "JKS"                    # The type of the key store (currently supported: "JKS" and "PKCS#12")
      }
      certificateAuthority {
        #certificatePath = ""            # The path of the custom certificate
        #privateKeyPath = ""             # The certificate's private key path
      }
    }
    outgoing {
      #host = ""     # The outgoing proxy's hostname
      #username = "" # The username to use to connect to the outgoing proxy
      #password = "" # The password corresponding to the user to use to connect to the outgoing proxy
      #port = 0      # The HTTP port to use to connect to the outgoing proxy
      #sslPort = 0   # If set, The HTTPS port to use to connect to the outgoing proxy
    }
  }
  netty {
    #maxInitialLineLength = 10000 # Maximum length of the initial line of the response (e.g. "HTTP/1.0 200 OK")
    #maxHeaderSize = 20000        # Maximum size, in bytes, of each request's headers
    #maxChunkSize = 8192          # Maximum length of the content or each chunk
    #maxContentLength = 100000000 # Maximum length of the aggregated content of each response
  }
}
