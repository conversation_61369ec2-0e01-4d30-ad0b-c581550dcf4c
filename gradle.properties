# suppress inspection "UnusedProperty" for whole file
name=sku-demand-forecast
group=com.hellofresh.skudemandforecast
# Gradle Settings
org.gradle.jvmargs=-Xmx2048m -Xms2048m -XX:ThreadStackSize=4096 -XX:CompilerThreadStackSize=4096
org.gradle.welcome=never
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true
# Container settings
# Digest from 2022-02-08 (`docker pull gcr.io/distroless/java17:latest`)
jib.from.image=gcr.io/distroless/java21@sha256:fe0560fbf87031402f6c725917eabbf9217b5e9fc95fdff9a004dba3587a0513
jib.from.credHelper=gcr
jib.to.repository=489198589229.dkr.ecr.eu-west-1.amazonaws.com/sku-demand-forecast

# Change [jmx.port] also in project.properties
jib.container.jvmFlags=\
  -XX:MaxRAMPercentage=75.0 \
  -Xshare:off \
  -Dlogging.layout=HelloFresh \
  -Dlogging.slack.level=Error \
  -Dcom.sun.management.jmxremote \
  -Dcom.sun.management.jmxremote.port=7483 \
  -Dcom.sun.management.jmxremote.rmi.port=7483 \
  -Dcom.sun.management.jmxremote.local.only=false \
  -Dcom.sun.management.jmxremote.authenticate=false \
  -Dcom.sun.management.jmxremote.ssl=false

gradle.javaExec.jvmFlags=\
  -XX:MaxRAMPercentage=75.0 \
  -Dcom.sun.management.jmxremote=false \
  -Xshare:off
