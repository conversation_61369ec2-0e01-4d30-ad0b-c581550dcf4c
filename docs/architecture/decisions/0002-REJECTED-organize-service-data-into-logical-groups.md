# 2. Organize service data into logical groups

Date: 2022-05-05

## Status

**Rejected**

## Context

We want to organize the data to have a clear logical separation between services data.

## Decision

**This ADR was rejected because at this stage SDF would not evolve to have no more than one service. That means the SDF
should have, at least for now, only one public schema managed by flywayDB.**

The proposal is to have a unique database instance with one database [schema](https://www.postgresql.org/docs/current/ddl-schemas.html) per each service.
In postgresql schema is a [namespace](https://en.wikipedia.org/wiki/Namespace) that contains named database objects such as tables, index, views, and etc.

![Single Database](https://user-images.githubusercontent.com/5173823/166807857-0e03c564-7c01-4bb3-a3d6-f7d025d6089b.jpg)

## Consequences

A single database is easier to operate. In addition, the proposed approach is just an extension of the current scenario,
plus well-organizing database objects to match the microservices domain boundaries.

Besides, it reduces the schema management complexity by centralizing the database object's evolution using flyway DB
migration in one place but with clear predefined boundaries.

Regarding services isolation, to avoid spaghetti database entities, each service can have its credentials, with enough
grants to perform read, write, or read/write operations.
It guarantees that a service that does not manage a specific entity lifecycle would never change the entity state but
could still read the table if desired.
