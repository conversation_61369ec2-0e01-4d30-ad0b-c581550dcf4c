<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="foreacster" type="JetRunConfigurationType">
    <envs>
      <env name="HF_KAFKA_BOOTSTRAP_SERVERS" value="localhost:29092" />
        <env name="HF_TIER" value="local" />
        <env name="HF_SCHEMA_REGISTRY_URL" value="" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.hellofresh.skuDemandForecast.forecaster.MainKt" />
    <module name="sku-demand-forecast.forecaster.main" />
    <shortenClasspath name="NONE" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
