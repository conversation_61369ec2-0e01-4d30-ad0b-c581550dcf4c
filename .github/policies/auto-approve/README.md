The auto-approval policy in this directory only checks if the `bound_claims` of
the GitHub OIDC is abnormal (i.e. not simply the repository it belongs to).

These rules are used with `conftest` to validate the `terraform` / `terragrunt` JSON output.

## Development
You can obtain sample `.json` files uploaded as workflow run artifacts of any [recently run workflow][1].

```sh
# Run unit tests
make test
```

[1]: https://github.com/hellofresh/vault-namespace-automation/actions/workflows/onboarding.yml

