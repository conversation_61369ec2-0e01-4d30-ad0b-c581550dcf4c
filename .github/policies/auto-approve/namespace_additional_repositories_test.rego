package main

no_violations {
  count(deny) == 0
}

test_deny_no_violations {
  input := parse_config("json", `
    {
      "variables": {
        "name": {
          "value": "intfood-sinkers"
        }
      },
      "resource_changes": [
        {
          "type": "vault_jwt_auth_backend_role",
          "change": {
            "actions": [
              "create"
            ],
            "before": {},
            "after": {
              "backend": "github-actions",
              "bound_claims": {
                "repository": "hellofresh/intfood-sinkers"
              }
            }
          }
        }
      ]
    }
  `)

  no_violations with input as input
}

test_deny_create_with_extra_bound_claims {
  input := parse_config("json", `
    {
      "variables": {
        "name": {
          "value": "intfood-sinkers"
        }
      },
      "resource_changes": [
        {
          "type": "vault_jwt_auth_backend_role",
          "change": {
            "actions": [
              "create"
            ],
            "before": {},
            "after": {
              "backend": "github-actions",
              "bound_claims": {
                "repository": "hellofresh/intfood,hellofresh/intfood-sinkers"
              }
            }
          }
        }
      ]
    }
  `)

  deny["Adding additional repositories requires validation from the Security Team."] with input as input
}

test_deny_update_with_extra_bound_claims {
  input := parse_config("json", `
    {
      "variables": {
        "name": {
          "value": "intfood-sinkers"
        }
      },
      "resource_changes": [
        {
          "type": "vault_jwt_auth_backend_role",
          "change": {
            "actions": [
              "update"
            ],
            "before": {
              "backend": "github-actions",
              "bound_claims": {
                "repository": "hellofresh/intfood-sinkers"
              }
            },
            "after": {
              "backend": "github-actions",
              "bound_claims": {
                "repository": "hellofresh/intfood,hellofresh/intfood-sinkers"
              }
            }
          }
        }
      ]
    }
  `)

  deny["Modifying additional repositories requires validation from the Security Team."] with input as input
}

test_deny_create_with_mismatching_bound_claims {
  input := parse_config("json", `
    {
      "variables": {
        "name": {
          "value": "intfood-sinkers"
        }
      },
      "resource_changes": [
        {
          "type": "vault_jwt_auth_backend_role",
          "change": {
            "actions": [
              "create"
            ],
            "before": {},
            "after": {
              "backend": "github-actions",
              "bound_claims": {
                "repository": "hellofresh/intfood"
              }
            }
          }
        }
      ]
    }
  `)

  deny["Adding additional repositories requires validation from the Security Team."] with input as input
}
