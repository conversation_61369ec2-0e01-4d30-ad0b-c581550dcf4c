---
version: 2
mergeable:
  - when: pull_request.*, pull_request_review.*
    name: 'Check tribe/squad labels'
    validate:
      - do: label
        and:
          - must_include:
              regex: 'squad: [a-z]+[a-z\-]+'
              regex_flag: 'none'
              message: 'You need to add squad label for your PR'
          - must_include:
              regex: 'tribe: [a-z]+[a-z\-]+'
              regex_flag: 'none'
              message: 'You need to add tribe label for your PR'
