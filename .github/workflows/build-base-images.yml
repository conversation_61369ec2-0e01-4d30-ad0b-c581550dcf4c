name: Build base images

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths: [ ci/docker/** ]


concurrency: build-base-image

jobs:
  build-main:
    name: Build base images
    runs-on: [ self-hosted, default ]
    env:
      IMAGE_PATH: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us
      BUILD_CONTEXT_PATH: ci/docker

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Build and push back image
        uses: docker/build-push-action@v6
        with:
          context: ${{ env.BUILD_CONTEXT_PATH }}/back
          push: true
          tags: ${{ env.IMAGE_PATH }}:prod-image-back

      - name: Build and push front image
        uses: docker/build-push-action@v6
        with:
          context: ${{ env.BUILD_CONTEXT_PATH }}/front
          push: true
          tags: ${{ env.IMAGE_PATH }}:prod-image-front
