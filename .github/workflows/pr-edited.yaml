---
name: title-check
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

on:
  pull_request:
    # we need synchronize because we want the status check to run everytime.
    types: [ opened, reopened, synchronize, edited ]

jobs:
  title-check:
    name: Title<PERSON>heck
    runs-on: [ self-hosted, default ]
    timeout-minutes: 5
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: PR Title Check
        id: pr-title-check
        uses: ./.github/actions/pr-title-check
