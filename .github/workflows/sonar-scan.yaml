name: "Sonar Scan"

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - master
    paths:
      - terraform-modules/**

  push:
    branches:
      - master
    paths:
      - terraform-modules/**

jobs:
  test:
    name: <PERSON>ar-<PERSON><PERSON>
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
            common/data/defaults SONAR_TOKEN ;
            
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          # SonarQube requires commits history for the analysis
          fetch-depth: 0

      - name: Sonar<PERSON>ube Scan
        uses: hellofresh/jetstream-ci-scripts/actions/sonar-scanner@master
        env:
          SONAR_TOKEN: ${{ env.SONAR_TOKEN }}
          SONAR_HOST_URL: "https://sonarqube.tools-k8s.hellofresh.io"
        with:
          args: >
            -Dproject.settings=./.github/assets/sonar-project.properties
            -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }}
