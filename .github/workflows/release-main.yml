name: Release Main - Live

on:
  workflow_dispatch:
    inputs:
      bump_type:
        description: version bump type
        required: true
        type: choice
        options:
          - patch
          - minor
          - major


concurrency: deploy-live

jobs:
  build-main:
    name: Release Main
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: write
    env:
      KUBE_NAMESPACE: scm
      IMAGE_NAME: inventory-management-us
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
          secrets: |
            live/key-value/data/config CONFIG | CONFIG;

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          ref: master
          fetch-depth: 50

      - name: Bump version
        id: version
        uses: hellofresh/jetstream-ci-scripts/actions/bump-version@master
        with:
          ACCESS_TOKEN: ${{ env.GITHUB_TOKEN }}
          REPOSITORY: ${{ github.repository }}
          bump-type: ${{ github.event.inputs.bump_type }}

      - name: Print release log
        run: |
          echo $(git config --get remote.origin.url)
          git fetch -q
          release_log=$(git log --cherry --format=%s origin/release...origin/master)
          echo "::group::Release log"
          echo "$release_log"
          echo "::endgroup::"
          echo "### Release ${{ steps.version.outputs.new_version }} :ship:" >> $GITHUB_STEP_SUMMARY
          echo "$release_log" | while read -r log; do
            echo "* ${log}" >> $GITHUB_STEP_SUMMARY
          done

      - name: Cut release branch
        run: |
          set -e
          
          version="${{ steps.version.outputs.new_version }}"
          bump_type="${{ github.event.inputs.bump_type }}"
          release_branch="${bump_type}/release-${version}"
          git config user.name github-actions
          git config user.email <EMAIL>

          git fetch -q
          git checkout -b "${release_branch}"
          git push -u origin HEAD
          git checkout -t origin/release
          git merge "${release_branch}" -m "Release ${version}" -q
          git push

      - name: Build and push tag
        uses: ./ci/actions/build-release-tag
        with:
          tag: ${{ steps.version.outputs.new_version }}

      - name: Deploy
        uses: ./ci/actions/deploy
        with:
          environment: live
          tag: ${{ steps.version.outputs.new_version }}
