name: Restart - Live

on:
  workflow_dispatch:
    inputs:
      deployment:
        description: Restart exact deployment (empty = all)
        required: false
        type: choice
        options:
          - ""
          - inventory-management-us-app
          - inventory-management-us-worker
          - inventory-management-us-scheduler
          - inventory-management-us-consumer

concurrency: deploy-live

jobs:
  restart:
    name: Restart
    runs-on: [ self-hosted, default ]
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Restart pods
        uses: ./ci/actions/restart-env
        with:
          environment: live
          deployment: ${{ github.event.inputs.deployment }}
