name: Build release

on:
  workflow_dispatch:
    inputs:
      bump_type:
        description: version bump type (default = branch type)
        required: false
        type: choice
        options:
          - ""
          - patch
          - minor
          - major


concurrency: deploy-live

jobs:
  build-main:
    name: Build main images
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          ref: release

      - name: Bump version
        id: version
        uses: hellofresh/jetstream-ci-scripts/actions/bump-version@master
        with:
          ACCESS_TOKEN: ${{ env.GITHUB_TOKEN }}
          REPOSITORY: ${{ github.repository }}
          bump-type: ${{ github.event.inputs.bump_type }}

      - name: Build and push images
        uses: ./ci/actions/build-release-tag
        with:
          tag: ${{ steps.version.outputs.new_version }}
