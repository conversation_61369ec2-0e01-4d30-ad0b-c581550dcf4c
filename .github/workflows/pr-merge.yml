name: PR merge

on:
   push:
     branches: [ master ]

concurrency: deploy-staging

env:
  # 0.0.0 prefix is used just to maintain valid semver format
  CHART_VERSION: 0.0.0-dev-${{ github.sha }}

jobs:
  build-main:
    name: Build dev images
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Build and push images
        uses: ./ci/actions/build-and-push-deployment-images
        with:
          username: ${{ env.ARTIFACTORY_USERNAME }}
          password: ${{ env.ARTIFACTORY_PASSWORD }}
          github_token: ${{ env.GITHUB_TOKEN }}
          version: ${{ env.CHART_VERSION }}
          dev_image: true

  terraform_apply:
    name: Apply Terraform
    runs-on: [ self-hosted, default ]
    needs: [ build-main ]
    permissions:
      id-token: write
      contents: read
    env:
      TERRAFORM_VERSION: "1.5.7"
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Check if terraform changed
        id: tf-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            **/ahoy/*.tf
            **/staging/*.tf

      - name: Terraform Plan
        if: ${{ steps.tf-files.outputs.any_changed == 'true' }}
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          version: ${{ env.TERRAFORM_VERSION }}
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          vault_token: ${{ env.VAULT_TOKEN }}
          github_token: ${{ env.GITHUB_TOKEN }}
          directory: ${{ github.workspace }}/terraform
          skip_directories: |
            *live*

      - name: Terraform Apply
        if: ${{ steps.tf-files.outputs.any_changed == 'true' }}
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          version: ${{ env.TERRAFORM_VERSION }}
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          vault_token: ${{ env.VAULT_TOKEN }}
          github_token: ${{ env.GITHUB_TOKEN }}
          directory: ${{ github.workspace }}/terraform
          action: apply -auto-approve
          skip_directories: |
            *live*

  helm_deploy:
    name: Deploy
    runs-on: [ self-hosted, default ]
    needs: [ terraform_apply ]
    permissions:
      id-token: write
      contents: read
    env:
      KUBE_NAMESPACE: scm
      IMAGE_NAME: inventory-management-us
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
          secrets: |
            staging/key-value/data/config CONFIG | CONFIG;

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Deploy
        uses: ./ci/actions/deploy
        with:
          environment: staging
          tag: ${{ env.CHART_VERSION }}

      - name: Ensure Rollout
        uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
        with:
          deployment-namespace: ${{ env.KUBE_NAMESPACE }}
          deployment-selector: release=${{ env.IMAGE_NAME }}

      - name: Check JS file's changed
        id: js-changed
        uses: tj-actions/changed-files@v45
        with:
          files: |
            front/**/*.js
            front/**/*.vue

      - name: Notify UI changed
        if: ${{ steps.js-changed.outputs.any_changed == 'true' }}
        uses: ./ci/actions/notify-ui-change
        with:
          config: ${{ env.CONFIG }}
          new_version: ${{ env.CHART_VERSION }}
