---
name: terraform-apply
concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Environment to run DB migration against'
        required: true
        default: 'staging'
        options:
          - staging
          - live

jobs:
  terraform:
    name: Terraform Apply for ${{ github.event.inputs.environment }}
    runs-on: [ self-hosted, default ]
    timeout-minutes: 120
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Terraform Apply for ${{ github.event.inputs.environment }}
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          version: "1.5.7"
          action: "apply -auto-approve"
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          vault_token: ${{ env.VAULT_TOKEN }}
          github_token: ${{ env.GITHUB_TOKEN }}
          directory: ${{ github.workspace }}/terraform/${{ github.event.inputs.environment }}
