# This workflow determines if a given pull-request is eligible for auto-approval
# It works on elimination basis; each step basically being a deny rule

---
name: "PR: Auto-Approve"

on:
  workflow_call:

permissions:
  id-token: write
  contents: read

jobs:
  approve:
    name: approve
    runs-on: [ self-hosted ]
    timeout-minutes: 15
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      # Rule: No Ineligible Files
      # Any changes outside these files will disqualify the PR
      - name: Get changed files
        id: changed-non-ecr-files
        uses: tj-actions/changed-files@v45
        with:
          files_ignore: namespaces/*/main.yml

      - if: steps.changed-non-ecr-files.outputs.any_modified == 'true'
        name: Illegal files changed
        id: rule-no-illegal-files
        run: |
          echo "Changes outside namespaces detected!"
          echo "This PR is ineligible for PR auto-approval"

      # Download: Terraform Resource Changes
      - if: steps.rule-no-illegal-files.outcome == 'skipped'
        name: Download tfplan.json
        uses: actions/download-artifact@v4
        with:
          path: jsons
          pattern: "terragrunt-plan-*"

      - if: steps.rule-no-illegal-files.outcome == 'skipped'
        id: conftest
        name: Check terraform changes
        run: |
          conftest test --policy ".github/policies/auto-approve" jsons/*/tgplan.json || true
          conftest test --policy ".github/policies/auto-approve" jsons/*/tgplan.json --output json > results.json
        continue-on-error: true

      # Conftest Failed, Report via Job Summary
      - if: steps.conftest.outcome == 'failure'
        name: Report conftest failure as github actions step summary
        uses: actions/github-script@v7
        env:
          TITLE: "Changes inelegible for auto-approval"
        with:
          script: |
            const data = require("./results.json")

            data.map(report => {
              const repoName = report.filename.match(/\/(.+?)\/tgplan\.json/)[1];
              const failures = report.failures.map(f => f.msg).join("\n");

              core.summary.addHeading(`<code>${repoName}</code>`, 3);
              core.summary.addCodeBlock(failures);
            });
            core.summary.write()

      # Conftest Passed: AutoApprove
      - if: steps.conftest.outcome == 'success'
        name: Pull secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          role: github-pr-approver-role
          namespace: infrastructure/github-pr-approver
          secrets: |
            common/key-value/data/github-pr-approver GITHUB_TOKEN;


      - if: steps.conftest.outcome == 'success'
        name: Auto Approve
        run: >
          gh pr review
          --body "This PR is eligible for auto-approval! <br>You can find the exact rules for auto-approval eligibility [here](https://github.com/hellofresh/vault-namespace-automation/tree/master/.github/policies/auto-approve)."
          --approve "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
