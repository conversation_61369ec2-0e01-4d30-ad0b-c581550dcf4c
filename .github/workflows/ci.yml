name: CI

on: [pull_request, workflow_dispatch]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code-style:
    name: Code Style
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Common Setup
        uses: ./ci/actions/common-setup

      - name: Run Code Style
        run: |
          cd back
          tox -e pep8

  code-quality:
    name: Code Quality
    runs-on: [self-hosted, default]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Common Setup
        uses: ./ci/actions/common-setup

      - name: Run Code Quality Check
        run: |
          cd back
          tox -e code_quality

  unit:
    name: Unit Tests
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            common/key-value/data/cd google_sheets_config | GOOGLE_SHEETS_CONFIG;

      - name: Common Setup
        uses: ./ci/actions/common-setup

      - name: Run Unit
        run: |
          cd back
          tox -e unit
        env:
          GOOGLE_SHEETS_CONFIG: ${{ env.GOOGLE_SHEETS_CONFIG }}

  lint:
    name: Linter
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;

      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Common Setup
        uses: ./ci/actions/common-setup

      - name: "Run Helm Check: All charts"
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-check@master
        env:
          AHOY_DIR: ahoy
          CHART_NAME: inventory-management-us
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}

      - name: "Validate json configs"
        run: |
          cd back
          tox -e validate_configs

  build-back:
    name: Build Backend Docker
    runs-on: [self-hosted, default]

    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Build Image
        uses: ./ci/actions/build-docker
        with:
          path: ./back
          name: inventory-management-us
          build-args: |
            "PYTHON_BASE_IMAGE=489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us:prod-image-back"

  build-front:
    name: Build Frontend Docker
    runs-on: [self-hosted, default]

    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Build Image
        uses: ./ci/actions/build-docker
        with:
          path: ./front
          name: inventory-management-us-front
          build-args: |
            "NODE_BASE_IMAGE=489198589229.dkr.ecr.eu-west-1.amazonaws.com/inventory-management-us:prod-image-front"

  api:
    name: Run API tests
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    needs:
      - build-back
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Execute API tests
        uses: ./ci/actions/backend-tests
        with:
          tox_folder: automated_tests
          tox_profile: api-ci
          list_of_components_to_run: procurement-db cache hj back

  sync_jobs:
    name: Run Synchronization Jobs tests
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    needs:
      - build-back
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Execute Synchronization Jobs tests
        uses: ./ci/actions/backend-tests
        with:
          tox_folder: automated_tests
          tox_profile: sync-jobs-ci
          list_of_components_to_run: procurement-db cache hj back worker

  kafka_jobs:
    name: Run Kafka Synchronization Jobs tests
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    needs:
      - build-back
    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Execute Kafka Synchronization Jobs tests
        uses: ./ci/actions/backend-tests
        with:
          tox_folder: automated_tests
          tox_profile: kafka-jobs-ci
          list_of_components_to_run: procurement-db cache hj back worker consumer zookeeper kafka

  ui:
    name: Run UI Tests
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    needs:
      - build-back
      - build-front

    steps:
      - name: Setup Environment
        uses: actions/checkout@v4

      - name: Execute UI tests
        uses: ./ci/actions/run-ui-tests
        with:
          tox_folder: automated_tests
          tox_profile: ui-ci
          list_of_components_to_run: procurement-db cache back hj