---
name: cd
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

on:
  push:
    branches: [ 'master' ]

jobs:
  root-helm-charts:
    name: Generate and Deploy the root helm charts
    runs-on: [ self-hosted, default ]
    timeout-minutes: 5
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        tier: [ 'staging', 'live']
    steps:
      - name: Checkout source
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Import vault secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: Generate release version
        id: chart-version
        uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

      - name: Retrieve commit id
        id: version
        shell: bash
        run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: eu-west-1
          role-to-assume: "arn:aws:iam::489198589229:role/github-actions-runner"

      - run: gradle :generateHelmChart

      - name: Build and push chart
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
        with:
          template-name: "default"
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
          CHART_NAME: sdf-sku-demand-forecast
          CHART_PATH: 'build/helm/chart/sdf-sku-demand-forecast'
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
          VERSION: ${{ steps.chart-version.outputs.new_version }}

      - name: Staging - Setup k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: "staging"

      - name: Deploy Staging
        if: ${{ matrix.tier == 'staging' }}
        timeout-minutes: 5
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
        with:
          release-name: sdf-sku-demand-forecast
          chart-name: sdf-sku-demand-forecast
          version: ${{ steps.chart-version.outputs.new_version }}
          values-path: 'build/helm/chart/sdf-sku-demand-forecast/values-staging.yaml'
          namespace: 'scm'
          set-string: tag=sdf-sku-demand-forecast-${{ steps.version.outputs.new_version }}

      - name: Live - Setup k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: "live"

      - name: Deploy Live
        if: ${{ matrix.tier == 'live' }}
        timeout-minutes: 5
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
        with:
          release-name: sdf-sku-demand-forecast
          chart-name: sdf-sku-demand-forecast
          version: ${{ steps.chart-version.outputs.new_version }}
          values-path: 'build/helm/chart/sdf-sku-demand-forecast/values-live.yaml'
          namespace: 'scm'
          set-string: tag=sdf-sku-demand-forecast-${{ steps.version.outputs.new_version }}

  migrate-db:
    name: migrate database
    runs-on: [ self-hosted, default ]
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            staging/key-value/data/rds DB_USERNAME | DB_USERNAME_STAGING ;
            staging/key-value/data/rds DB_PASSWORD | DB_PASSWORD_STAGING ;
            live/key-value/data/rds DB_USERNAME | DB_USERNAME_LIVE ;
            live/key-value/data/rds DB_PASSWORD | DB_PASSWORD_LIVE ;
            staging/key-value/data/outbox DB_USERNAME | DB_OUTBOX_USERNAME_STAGING ;
            staging/key-value/data/outbox DB_PASSWORD | DB_OUTBOX_PASSWORD_STAGING ;
            live/key-value/data/outbox DB_USERNAME | DB_OUTBOX_USERNAME_LIVE ;
            live/key-value/data/outbox DB_PASSWORD | DB_OUTBOX_PASSWORD_LIVE ;
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: DB migrate Staging
        shell: bash
        run: gradle :sku-demand-forecast-db:flywayMigrate
        env:
          HF_TIER: staging
          DB_USERNAME: ${{ env.DB_USERNAME_STAGING }}
          DB_PASSWORD: ${{ env.DB_PASSWORD_STAGING }}
          DB_OUTBOX_USERNAME: ${{ env.DB_OUTBOX_USERNAME_STAGING }}
          DB_OUTBOX_PASSWORD: ${{ env.DB_OUTBOX_PASSWORD_STAGING }}

      - name: DB migrate Live
        shell: bash
        run: gradle :sku-demand-forecast-db:flywayMigrate
        env:
          HF_TIER: live
          DB_USERNAME: ${{ env.DB_USERNAME_LIVE }}
          DB_PASSWORD: ${{ env.DB_PASSWORD_LIVE }}
          DB_OUTBOX_USERNAME: ${{ env.DB_OUTBOX_USERNAME_LIVE }}
          DB_OUTBOX_PASSWORD: ${{ env.DB_OUTBOX_PASSWORD_LIVE }}

  containerize-benthos:
    name: containerize benthos
    runs-on: [ self-hosted, default ]
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: containerize kafka-db-sink
        uses: ./.github/actions/containerize-module
        with:
          module: kafka-db-sink

  deploy:
    name: Deploy
    needs: [ containerize-benthos, migrate-db ]
    runs-on: [ self-hosted, default ]
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        chart:
            - 'api-service'
            - 'demand-anz-consumer'
            - 'demand-calculator'
            - 'demand-matcher-job'
            - 'demand-import-job'
            - 'distribution-center-service'
            - 'files-consumer'
            - 'forecaster-job'
            - 'kafka-db-sink'
            - 'matcher-service'
            - 'mps-forecaster-job'
            - 'recipe-anz-consumer'
            - 'recipe-import-job'
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            common/key-value/data/misc SLACK_URL | SLACK_URL ;
            staging/key-value/data/rds DB_USERNAME | DB_USERNAME_STAGING ;
            staging/key-value/data/rds DB_PASSWORD | DB_PASSWORD_STAGING ;
            live/key-value/data/rds DB_USERNAME | DB_USERNAME_LIVE ;
            live/key-value/data/rds DB_PASSWORD | DB_PASSWORD_LIVE ;
            staging/key-value/data/outbox DB_USERNAME | DB_OUTBOX_USERNAME_STAGING ;
            staging/key-value/data/outbox DB_PASSWORD | DB_OUTBOX_PASSWORD_STAGING ;
            live/key-value/data/outbox DB_USERNAME | DB_OUTBOX_USERNAME_LIVE ;
            live/key-value/data/outbox DB_PASSWORD | DB_OUTBOX_PASSWORD_LIVE ;
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: eu-west-1
          role-to-assume: "arn:aws:iam::489198589229:role/github-actions-runner"

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate release version
        id: chart-version
        uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

      - name: Retrieve commit id
        id: version
        shell: bash
        run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"

      - name: Gradle generate Helm Chart
        shell: bash
        run: gradle :${{ matrix.chart }}:generateHelmChart

      - name: Build container image to registry
        run: gradle :${{ matrix.chart }}:jib
        if: ${{ matrix.chart != 'kafka-db-sink' }}
        shell: bash
        env:
          RELEASE_TAG: ${{ matrix.chart }}-${{ steps.version.outputs.new_version }}

      - name: Build and push chart
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
        with:
          template-name: "default"
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
          CHART_NAME: sdf-${{ matrix.chart }}
          CHART_PATH: 'build/helm/chart/sdf-${{ matrix.chart }}'
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
          VERSION: ${{ steps.chart-version.outputs.new_version }}

      - name: Setup Staging k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: 'staging'

      - name: Deploy Staging
        timeout-minutes: 5
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
        with:
          release-name: sdf-${{ matrix.chart }}
          chart-name: sdf-${{ matrix.chart }}
          version: ${{ steps.chart-version.outputs.new_version }}
          values-path: 'build/helm/chart/sdf-${{ matrix.chart }}/values-staging.yaml'
          namespace: 'scm'
          set-string: tag=${{ matrix.chart }}-${{ steps.version.outputs.new_version }}

      - name: Ensure Staging Rollout
        uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
        if: ${{ matrix.chart != 'demand-matcher-job' }}
        with:
          deployment-selector: 'release=sdf-${{ matrix.chart }}'
          deployment-namespace: 'scm'

      - name: Setup Live k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: 'live'

      - name: Deploy Live
        timeout-minutes: 5
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
        with:
          release-name: sdf-${{ matrix.chart }}
          chart-name: sdf-${{ matrix.chart }}
          version: ${{ steps.chart-version.outputs.new_version }}
          values-path: 'build/helm/chart/sdf-${{ matrix.chart }}/values-live.yaml'
          namespace: 'scm'
          set-string: tag=${{ matrix.chart }}-${{ steps.version.outputs.new_version }}

      - name: Ensure Live Rollout
        uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
        if: ${{ matrix.chart != 'demand-matcher-job' }}
        with:
          deployment-selector: 'release=sdf-${{ matrix.chart }}'
          deployment-namespace: 'scm'

      - if: failure()
        name: set failure
        run: echo "STATUS_TEXT='Deployment failed! :x:'" >> "$GITHUB_ENV"

      - if: success()
        name: set success
        run: echo 'STATUS_TEXT="Deployment successful! ✅"' >> "$GITHUB_ENV"

      - name: slack notification
        if: always()
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ':octocat:'
          pretext: ${{ env.STATUS_TEXT }}
          text: 'Job: Deploy'
          channel: '#proj-sku-demand-forecast-releases'
          username: 'Github Actions: CD'

