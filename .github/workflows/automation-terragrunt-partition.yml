---
name: Execute Terragrunt for ftcp partitions
run-name:  Execute terragrunt run-all apply on partitions ${{ github.event.client_payload.partitions }}

on:
  repository_dispatch:
    types: [ftcp-partition]

jobs:
  terragrunt:
    name: Run terragrunt for ftcp partitions
    runs-on: [ self-hosted, default ]

    permissions:
      id-token: write
      contents: read

    env:
      VAULT_ADDR: "https://vault.secrets.hellofresh.io"
      TF_VAR_azure_auth_oidc_client_secret: ${{ secrets.AZURE_APPLICATION_CLIENT_SECRET }}

    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout repository
        uses: actions/checkout@v4

      - shell: bash
        run: |
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "**************:hellofresh/"
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "https://github.com/hellofresh/"

      - name: Setup terragrunt
        uses: ./.github/actions/setup-terragrunt
        with:
          working-directory: namespaces/

      - name: Initialize repository directory
        working-directory: namespaces/
        run: |
          for partition in ${{ github.event.client_payload.partition }}
          do
          yq e "select(.ownership.partition[] == \"${partition}\" or (.ownership.partition[] | contains(\".*\"))) | filename" */main.yml | xargs dirname
          done | sort -u | while read namespace
          do
          echo "Processing namespace: $namespace"
          cat <<EOF >"$namespace/terragrunt.hcl"
          include "root" {
            path = find_in_parent_folders()
          }
          EOF
          done
    
      - name: Authenticate
        uses: ./.github/actions/authenticate
        with:
          auth-platforms: "aws,azure,vault"
          vault-endpoint: ${{ env.VAULT_ADDR }}
          vault-role: vault-namespace-automation
          azure-client-id: ${{ secrets.AZ_WRITE_CLIENT_ID }}
          azure-tenant-id: ${{ secrets.AZ_TENANT_ID }}
          azure-subscription-id: ${{ secrets.AZ_SUBSCRIPTION_ID }}
          aws-assume-role: "arn:aws:iam::418187812125:role/github-actions"
          aws-region: "eu-west-1"

      - name: Run terragrunt
        env:
          TF_VAR_cloudamqp_apikey: ${{ secrets.CLOUDAMQP_TOKEN }}
        working-directory: namespaces/
        shell: bash
        run: |
          terragrunt run-all apply --terragrunt-non-interactive  --terragrunt-provider-cache --terragrunt-parallelism=5
