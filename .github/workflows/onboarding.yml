---
name: Onboarding

concurrency:
  group: service-${{ github.head_ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

on:
  pull_request:
    branches:
      - master
    paths:
      - .github/workflows/onboarding.yml
      - namespaces/**

  push:
    branches:
      - master
    paths:
      - .github/workflows/onboarding.yml
      - namespaces/**

env:
  aws-assume-role: "arn:aws:iam::418187812125:role/github-actions"
  aws-region: "eu-west-1"

jobs:
  generate-matrix:
    name: Generate job matrix
    runs-on: [ self-hosted, default ]

    outputs:
      matrix: ${{ steps.changed-files.outputs.matrix }}

    steps:
      - id: changed-files
        uses: hellofresh/action-changed-files@v3
        with:
          pattern: ^namespaces/(?P<directory>[^/]+)/

  terragrunt:
    name: Run terragrunt
    runs-on: [ self-hosted, default ]
    needs: [ generate-matrix ]
    strategy:
      matrix: ${{ from<PERSON><PERSON>(needs.generate-matrix.outputs.matrix) }}
      fail-fast: false
    if: ${{ from<PERSON><PERSON>(needs.generate-matrix.outputs.matrix).include[0] }}

    permissions:
      id-token: write
      contents: read

    env:
      VAULT_ADDR: "https://vault.secrets.hellofresh.io"
      TF_VAR_azure_auth_oidc_client_secret: ${{ secrets.AZURE_APPLICATION_CLIENT_SECRET }}

    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - id: select-git-reference
        name: Select git SHA reference
        run: |
          if [ ${{ matrix.reason }} = 'removed' ] && [ ${{ github.event_name }} = 'pull_request' ]; then
            echo "ref=${{ github.event.pull_request.base.ref }}" >> $GITHUB_OUTPUT
          elif [ ${{ matrix.reason }} = 'removed' ] && [ ${{ github.event_name }} = 'push' ]; then
            echo "ref=${{ github.event.before }}" >> $GITHUB_OUTPUT
          elif [ ${{ github.event_name }} = 'pull_request' ]; then
            echo "ref=${{ github.ref }}" >> $GITHUB_OUTPUT
          else
            echo "ref=${{ github.event.after }}" >> $GITHUB_OUTPUT
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.select-git-reference.outputs.ref }}

      - name: Validate YAML
        if: matrix.reason != 'removed'
        run: |-
          command -v ajv || npm install -g ajv-cli
          ajv validate -s .json-schema/vault-namespace.json -d namespaces/${{ matrix.directory }}/main.yml

      - shell: bash
        run: |
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "**************:hellofresh/"
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "https://github.com/hellofresh/"

      - name: Setup terragrunt
        uses: ./.github/actions/setup-terragrunt
        with:
          working-directory: namespaces/${{ matrix.directory }}

      - name: Initialize repository directory
        id: init-dir
        working-directory: namespaces/${{ matrix.directory }}
        run: |-
          cat <<EOF >terragrunt.hcl
          include "root" {
            path = find_in_parent_folders()
          }
          EOF

      - name: Authenticate
        uses: ./.github/actions/authenticate
        with:
          auth-platforms: "aws,azure,vault"
          vault-endpoint: ${{ env.VAULT_ADDR }}
          vault-role: vault-namespace-automation
          azure-client-id: ${{ github.event_name == 'pull_request' && secrets.AZ_READ_CLIENT_ID || secrets.AZ_WRITE_CLIENT_ID }}
          azure-tenant-id: ${{ secrets.AZ_TENANT_ID }}
          azure-subscription-id: ${{ secrets.AZ_SUBSCRIPTION_ID }}
          aws-assume-role: ${{ env.aws-assume-role }}
          aws-region: ${{ env.aws-region }}

      - name: Run terragrunt
        uses: ./.github/actions/terragrunt
        env:
          TF_VAR_cloudamqp_apikey: ${{ secrets.CLOUDAMQP_TOKEN }}
        with:
          working-directory: namespaces/${{ matrix.directory }}
          apply: ${{ github.event_name == 'push' }}
          destroy: ${{ matrix.reason == 'removed' }}

      - name: Delete removed state
        if: matrix.reason == 'removed'
        run: |
          aws s3 rm ${{ github.event_name == 'pull_request' && '--dryrun' || '' }} s3://hf-vault-enterprise-config/namespaces/${{ matrix.directory }}/terraform.tfstate

  pr-status:
    name: compile-status
    if: always() && github.event_name == 'pull_request'
    needs: [ terragrunt ]
    runs-on: [ self-hosted, default ]
    steps:
      - uses: hellofresh/action-compile-job-status@v2
        with:
          check-run-name: "Check workflow run"
          ignore-jobs: ".*report.*"
          check-run-title: "Workflow status"

  report-plan:
    name: Report terragrunt plans
    runs-on: [ self-hosted, default ]
    if: success('terragrunt') && github.event_name == 'pull_request'
    needs: [ terragrunt ]
    continue-on-error: true
    steps:
      - name: Download all terragrunt plans
        uses: actions/download-artifact@v4

      - name: Find existing comment
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: 'github-actions[bot]'
          body-includes: Terragrunt changes

      - name: Build comment
        id: build-comment-body
        uses: actions/github-script@v7
        with:
          script: |
            const path = require('path');
            const { promises: fs } = require('fs');

            const globber = await glob.create('**/tgplan.txt')
            let body = [];
            for await (const file of globber.globGenerator()) {
              let moduleName = path.basename(path.dirname(file));
              let content = await fs.readFile(file, 'utf8');
              body.push(
                `#### Terragrunt changes for \`${moduleName}\`:\n\`\`\`terraform\n${content}\n\`\`\`\n`
              );
            }

            if (body.length > 0) {
              core.setOutput('body', body.join('\n'));
            }

      - name: Post or update comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          edit-mode: replace
          body: ${{ steps.build-comment-body.outputs.body }}

  auto-approve:
    if: github.event_name == 'pull_request'
    needs: [terragrunt]
    uses: hellofresh/vault-namespace-automation/.github/workflows/auto-approve.yml@master
