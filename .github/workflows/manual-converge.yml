---
name: Manual Converge
run-name: Running "terragrunt ${{ inputs.action }}" on ${{ inputs.page-size }} namespaces - page ${{ inputs.page }}

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Terragrunt action to run'
        required: true
        default: 'plan'
        type: string
      page:
        description: 'Page of namespace results to fit 256 GitHub matrix limit'
        required: false
        default: '1'
        type: string
      page-size:
        description: 'Page size of namespace results'
        required: false
        default: '50'
        type: string

jobs:
  namespaces:
    name: Build matrix for all namespaces
    runs-on: [ self-hosted, default ]
    outputs:
      matrix: ${{ steps.set-matrix.outputs.namespaces }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Set namespaces matrix
        id: set-matrix
        shell: bash
        working-directory: namespaces
        run: |
          PAGE_SIZE=${{ inputs.page-size }}
          PAGE=${{ inputs.page }}
          HEAD_LINES=$((PAGE_SIZE * PAGE))
          TAIL_LINES=$PAGE_SIZE

          # Handle last page
          TOTAL_RESULTS=$(find * -maxdepth 1 -type d | wc -l)
          if [[ $HEAD_LINES -gt $TOTAL_RESULTS ]]; then
            HEAD_LINES=$TOTAL_RESULTS
            TAIL_LINES=$((TOTAL_RESULTS % PAGE_SIZE))
          fi

          echo "namespaces=$(find * -maxdepth 1 -type d | head -n $HEAD_LINES | tail -n $TAIL_LINES | jq -cnR '{namespace: [inputs]}')" >> $GITHUB_OUTPUT

  terragrunt:
    name: Run terragrunt
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    env:
      VAULT_ADDR: "https://vault.secrets.hellofresh.io"
    needs: namespaces
    strategy:
      fail-fast: false
      max-parallel: 50
      matrix: ${{ fromJson(needs.namespaces.outputs.matrix) }}
    if: ${{ fromJson(needs.namespaces.outputs.matrix).namespace[0] }}
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Validate YAML
        run: |
          command -v ajv || npm install -g ajv-cli
          xargs -n 1 -- ajv validate -s .json-schema/vault-namespace.json -d namespaces/${{ matrix.namespace }}/main.yml

      - shell: bash
        run: |
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "**************:hellofresh/"
          git config --global --add url."https://oauth2:${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "https://github.com/hellofresh/"

      - name: Setup terragrunt
        uses: ./.github/actions/setup-terragrunt
        with:
          working-directory: namespaces/${{ matrix.namespace }}

      - name: Initialize repository directory
        id: init-dir
        working-directory: namespaces/${{ matrix.namespace }}
        run: |
          cat <<EOF >terragrunt.hcl
          include "root" {
            path = find_in_parent_folders()
          }
          EOF

      - name: Authenticate
        uses: ./.github/actions/authenticate
        with:
          auth-platforms: "aws,azure,vault"
          vault-endpoint: ${{ env.VAULT_ADDR }}
          vault-role: vault-namespace-automation
          azure-client-id: ${{ secrets.AZ_WRITE_CLIENT_ID }}
          azure-tenant-id: ${{ secrets.AZ_TENANT_ID }}
          azure-subscription-id: ${{ secrets.AZ_SUBSCRIPTION_ID }}
          aws-assume-role: "arn:aws:iam::418187812125:role/github-actions"
          aws-region: "eu-west-1"

      - name: Run terragrunt
        env:
          TF_VAR_cloudamqp_apikey: ${{ secrets.CLOUDAMQP_TOKEN }}
          TF_VAR_azure_auth_oidc_client_secret: ${{ secrets.AZURE_APPLICATION_CLIENT_SECRET }}
        working-directory: namespaces/${{ matrix.namespace }}
        shell: bash
        run: |
          terragrunt ${{ inputs.action }}
