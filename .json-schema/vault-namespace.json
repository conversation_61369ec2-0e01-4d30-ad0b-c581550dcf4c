{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "title": "Vault Namespace Schema", "type": "object", "default": {}, "required": ["ownership", "vault"], "additionalProperties": false, "definitions": {"kubernetes": {"type": "object", "required": [], "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "Boolean flag that controls if kubernetes auth method should be created for each environment. It can be overriden per environment"}, "lease_ttl": {"type": "string", "description": "Specifies the token's time-to-live. Defaults to 5m. Must be a valid duration string"}, "max_lease_ttl": {"type": "string", "description": "Specifies the token's maximum time-to-live. Defaults to 24h. Must be a valid duration string"}, "namespaces": {"type": "array", "description": "A list of kubernetes namespaces names which are allowed to login to the vault namespace using kubernetes auth method", "items": {"type": "string"}}, "service_accounts": {"type": "array", "description": "A list of kubernetes service account names which are allowed to login to the vault namespace using kubernetes auth method", "items": {"type": "string", "pattern": "^(?!\\*+$)(.*$)"}}}, "if": {"properties": {"enabled": {"const": false}}}, "else": {"required": ["namespaces", "service_accounts"]}}, "kubernetes_override": {"type": "object", "required": [], "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "Overrides vault.auth_methods.kubernetes.enabled for <environment_name>"}, "namespaces": {"type": "array", "description": "Overrides vault.auth_methods.kubernetes.namespaces for <environment_name>", "items": {"type": "string"}}, "service_accounts": {"type": "array", "description": "Overrides vault.auth_methods.kubernetes.service_accounts for <environment_name>", "items": {"type": "string"}}}}, "kubernetes_identities_override": {"title": "Map of kubernetes namespaces to list of service accounts", "type": "object", "additionalProperties": false, "patternProperties": {"^[a-z0-9]([-a-z0-9]*[a-z0-9]){0,62}$": {"title": "Name of a kubernetes namespace", "type": "array", "minItems": 1, "items": {"title": "Name of a kubernetes service account", "type": "string", "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", "minLength": 1, "maxLength": 63}}}}, "jwt_kubernetes": {"title": "The schema for the `jwt_kubernetes` input field", "type": "object", "required": ["kubernetes_identities"], "additionalProperties": false, "properties": {"kubernetes_identities": {"$ref": "#/definitions/kubernetes_identities_override"}, "enabled": {"title": "Whether JWT Kubernetes auth should be enabled", "type": "boolean", "default": true}}}, "azure_oidc": {"title": "The azure_oidc Schema", "type": "object", "default": {}, "required": ["az_group_owners"], "additionalProperties": false, "properties": {"enabled": {"description": "Boolean flag that controls if Azure AD (SSO) auth method should be enabled for the namespace", "type": "boolean", "default": true}, "az_group_owners": {"title": "The az_group_owners", "description": "A list of email addresses of Azure AD users who shall be assigned to Azure AD groups as owners. This usually should be set to emails of Squad/Tribe Leads so they can add other team members to the groups and grant them access to the vault namespace", "type": "array", "default": [], "items": {"title": "User email", "type": "string"}}, "az_additional_groups": {"title": "The az_additional_groups", "description": "A list of AZ groups of squad/tribe who shall be assigned to Azure AD groups as members.. This usually should be set to AZ Group of Squad/Tribe to the groups and grant them access to the vault namespace (only required in case of additional AZ groups for access which are not provided by default ownership.squad )", "type": "array", "default": [], "items": {"title": "Group name", "type": "string"}}}}, "github_actions_jwt": {"title": "The github_actions_jwt Schema", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"enabled": {"title": "The enabled Schema", "description": "Enable auth method for [Github Actions OIDC](https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-hashicorp-vault)", "type": "boolean", "default": true}, "additional_repositories": {"title": "Additional allowed repositories", "description": "Additional repositories allowed to log in through the namespace", "type": "object", "default": {}, "additionalProperties": false, "patternProperties": {"^[a-z0-9]([-a-z0-9]*[a-z0-9])?$": {"type": "object", "additionalProperties": false, "default": {}, "properties": {"allow_write": {"type": "boolean", "default": false}}}}}}}, "aws": {"type": "object", "required": [], "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "Boolean flag that controls if aws auth method should be created"}, "lease_ttl": {"type": "string", "description": "Specifies the token's time-to-live. Defaults to 1h. Must be a valid duration string"}, "max_lease_ttl": {"type": "string", "description": "Specifies the token's maximum time-to-live. Defaults to 24h. Must be a valid duration string"}, "role": {"type": "string", "description": "AWS auth role name", "default": "default"}, "iam_principal_arns": {"type": "array", "description": "A list of AWS IAM Principal ARN that allowed to authenticate", "items": {"type": "string", "pattern": "^arn:aws:iam::.*"}}}, "if": {"properties": {"enabled": {"const": false}}}, "else": {"required": ["iam_principal_arns"]}}, "environment": {"title": "The environment", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"auth_methods": {"title": "The auth_methods Schema", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"kubernetes": {"$ref": "#/definitions/kubernetes_override"}}}}}, "ftcp_environment": {"title": "An FTCP environment", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"kubernetes_identities": {"$ref": "#/definitions/kubernetes_identities_override"}}}}, "properties": {"ownership": {"title": "The ownership Schema", "type": "object", "default": {}, "required": ["tribe", "squad"], "additionalProperties": false, "properties": {"partition": {"title": "List of owning alliances (more than one to support namespace transitions between alliances)", "type": "array", "minItems": 1}, "tribe": {"title": "The tribe Schema", "type": "string", "default": ""}, "squad": {"title": "The squad Schema", "type": "string", "default": "", "pattern": "^[0-9A-Za-z-_]+$"}}}, "environments": {"title": "The environments Schema", "description": "A dictionary of enabled environments for the namespace. For each enabled environment there will be auth method for kubernetes, and key-value secrets engine (kv2) created. Possible environment names are ahoy, au-live, au-staging, dwh, dwh-staging, live, staging, tools", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"live": {"$ref": "#/definitions/environment"}, "staging": {"$ref": "#/definitions/environment"}, "ahoy": {"$ref": "#/definitions/environment"}, "tools": {"$ref": "#/definitions/environment"}, "dwh": {"$ref": "#/definitions/environment"}, "dwh-staging": {"$ref": "#/definitions/environment"}, "dwh-spark-live": {"$ref": "#/definitions/environment"}, "dwh-spark-staging": {"$ref": "#/definitions/environment"}, "au-staging": {"$ref": "#/definitions/environment"}, "au-live": {"$ref": "#/definitions/environment"}, "bi-spark-staging": {"$ref": "#/definitions/environment"}, "bi-spark-live": {"$ref": "#/definitions/environment"}}}, "ftcp_environments": {"title": "The FTCP environments Schema", "description": "A dictionary of enabled FTCP environments for the namespace. To use an FTCP environment the jwt_kubernetes auth method must be enabled, by configuring kubernetes_identities. Possible environment names are stage and prod", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"prod": {"$ref": "#/definitions/ftcp_environment"}, "stage": {"$ref": "#/definitions/ftcp_environment"}, "shared": {"$ref": "#/definitions/ftcp_environment"}}}, "vault": {"title": "The vault Schema", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"root_namespace": {"title": "The root_namespace Schema", "description": "Root namespace in vault, where the new namespace is located. It is set by default to `services` so the namespace for `my-service` repo will be `services/my-service`", "type": "string", "default": "services"}, "auth_methods": {"title": "The auth_methods Schema", "type": "object", "default": {}, "required": ["kubernetes"], "additionalProperties": false, "properties": {"github_actions_jwt": {"$ref": "#/definitions/github_actions_jwt"}, "kubernetes": {"$ref": "#/definitions/kubernetes"}, "jwt_kubernetes": {"$ref": "#/definitions/jwt_kubernetes"}, "azure_oidc": {"$ref": "#/definitions/azure_oidc"}, "aws": {"$ref": "#/definitions/aws"}}}}}, "pki": {"title": "PKI secrets engine configuration", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "Boolean flag that controls if PKI secrets engine is enabled in the namespace"}, "path": {"type": "string", "description": "Path of PKI secrets engine"}}}, "rabbitmq": {"title": "Rabbitmq secrets engine configuration", "type": "object", "default": {}, "required": [], "additionalProperties": false, "properties": {"enabled": {"type": "boolean", "description": "Boolean flag that controls if rabbitmq secrets engine is enabled in the namespace"}, "vhosts": {"type": "array", "description": "Vhosts that will be enabled for the dynamic rabbitmq users"}, "tags": {"type": "array", "description": "tags that will be added for the dynamic rabbitmq users"}}}}}