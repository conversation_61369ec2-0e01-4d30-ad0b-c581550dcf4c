CREATE TABLE IF NOT EXISTS demand_imported
(
    LIKE demand INCLUDING ALL
);

CREATE TRIGGER demand_imported_updated_at_trigger
    BEFORE UPDATE
    ON demand_imported
    FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamp();

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'file_source') THEN
            ALTER TYPE file_source ADD VALUE 'SCO';
            ALTER TYPE file_source ADD VALUE 'LOCAL_D4';
        END IF;
    END
$$;
