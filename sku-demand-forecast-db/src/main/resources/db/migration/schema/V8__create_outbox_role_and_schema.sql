DO
$$
    BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = '${DB_OUTBOX_USERNAME}') THEN
            RAISE NOTICE 'Role "DB_OUTBOX_USERNAME" already exists. Skipping.';
        ELSE
            CREATE ROLE "${DB_OUTBOX_USERNAME}" LOGIN PASSWORD ${DB_OUTBOX_PASSWORD};
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = '${DB_USERNAME}')
        THEN
            GRANT ${DB_OUTBOX_USERNAME} to ${DB_USERNAME};
        END IF;
    END
$$;

CREATE SCHEMA IF NOT EXISTS outbox AUTHORIZATION ${DB_OUTBOX_USERNAME};
