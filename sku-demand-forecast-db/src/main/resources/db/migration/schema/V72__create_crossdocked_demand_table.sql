DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'crossdock_action') THEN
            CREATE TYPE crossdock_action AS ENUM ('REPLACE', 'DUPLICATE');
        END IF;
    END
$$;

create table if not exists public.crossdocked_demand
(
    sku_id          uuid                                NOT NULL,
    version         integer                             NOT NULL default 0,
    dc_from         varchar                             NOT NULL,
    dc_to           varchar                             NOT NULL,
    week_start      varchar                             NOT NULL, -- HFWeek
    action          crossdock_action                    NOT NULL,
    user_id         varchar                             NOT NULL, -- Uses email_id
    created_at      timestamp default CURRENT_TIMESTAMP NOT NULL,
    updated_at      timestamp default CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (sku_id, dc_from, dc_to, week_start, version)
);

CREATE OR REPLACE TRIGGER set_timestamp
    BEFORE UPDATE
        ON public.crossdocked_demand
        FOR EACH ROW
        EXECUTE PROCEDURE public.trigger_set_timestamp();
