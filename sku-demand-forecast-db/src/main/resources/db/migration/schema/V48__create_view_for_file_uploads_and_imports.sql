CREATE VIEW imports_view AS
SELECT id,
       dcs,
       weeks,
       market,
       file_name as name,
       info,
       errors,
       author_name,
       author_email,
       uploaded_file_type as type,
       uploaded_file_status as status,
       uploaded_file_source AS source,
       created_at,
       updated_at
FROM file_uploads
UNION ALL
SELECT
       id,
       dcs,
       weeks,
       market,
       name,
       null as info,
       errors,
       'Automated Import',
       '',
       type,
       status,
       source,
       created_at,
       updated_at
FROM imports;



