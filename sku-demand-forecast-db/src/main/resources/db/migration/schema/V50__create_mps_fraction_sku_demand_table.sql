create table if not exists mps_fraction_sku_demand
(
    sku_id         uuid    not null,
    dc_code        varchar not null,
    demand_date    date    not null,
    country        varchar not null,
    locale         varchar not null,
    product_family varchar not null,
    recipe_index   integer not null,
    qty            bigint  not null,
    created_at     timestamp default now(),
    updated_at     timestamp default now(),
    primary key (sku_id, demand_date, dc_code, country, locale, recipe_index, product_family)
);

create index if not exists mps_fraction_sku_demand_date_country_idx
    on mps_fraction_sku_demand (demand_date, country);


create or replace trigger set_timestamp_mps_demand
    before update
    on public.mps_fraction_sku_demand
    for each row
execute procedure public.trigger_set_timestamp();
