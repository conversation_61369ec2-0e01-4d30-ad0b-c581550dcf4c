ALTER TABLE aggregated_demand ADD COLUMN IF NOT EXISTS created_at timestamp default now();
ALTER TABLE aggregated_demand ADD COLUMN IF NOT EXISTS updated_at timestamp default null;

ALTER TABLE fraction_sku_demand ADD COLUMN IF NOT EXISTS created_at timestamp default now();
ALTER TABLE fraction_sku_demand ADD COLUMN IF NOT EXISTS updated_at timestamp default null;

ALTER TABLE sku_specification ADD COLUMN IF NOT EXISTS created_at timestamp default now();
ALTER TABLE sku_specification ADD COLUMN IF NOT EXISTS updated_at timestamp default null;
