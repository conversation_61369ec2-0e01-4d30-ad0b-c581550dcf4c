DROP TRIGGER IF EXISTS set_timestamp on public.sku_substitution;

ALTER TABLE IF EXISTS public.sku_substitution DROP CONSTRAINT IF EXISTS sku_substitution_pkey;

ALTER TABLE IF EXISTS public.sku_substitution
    ADD COLUMN IF NOT EXISTS disabled boolean NOT NULL DEFAULT false,
    ADD COLUMN IF NOT EXISTS version integer NOT NULL DEFAULT 0,
    DROP COLUMN IF EXISTS updated_at;

ALTER TABLE IF EXISTS public.sku_substitution
    ADD PRIMARY KEY (id,version),
    ALTER COLUMN disabled DROP DEFAULT,
    ALTER COLUMN version DROP DEFAULT;
