DO
$$
begin
    if exists(select * from pg_available_extensions where name = 'pg_cron') then
        CREATE EXTENSION IF NOT EXISTS pg_cron;
end if;
end;
$$ language plpgsql;

create or replace function public.schedule_cron_job(job_name text, cron_expression text, command text) returns bigint
    language plpgsql
as
$$
declare
scheduled bigint := 0;

begin
    if exists(select * from pg_available_extensions where name = 'pg_cron') then
select cron.schedule(job_name, cron_expression, command) into scheduled;
else
        -- it skips the executing when running inside the zonky embedded DB
        -- I couldn't find a way how to load the pg_cron shared library on it.
        raise notice 'the pg_cron extension is not installed. skipping.';
end if;
return scheduled;
end;
$$;
