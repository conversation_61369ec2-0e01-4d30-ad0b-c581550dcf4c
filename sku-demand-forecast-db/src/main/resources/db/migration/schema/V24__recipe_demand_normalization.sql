drop table if exists public.recipe;
drop table if exists public.demand;

create table if not exists public.recipe
(
    recipe_index            integer not null,
    week                    varchar not null,
    product_family          varchar not null,
    country                 varchar not null,
    locale                  varchar not null,
    value                   JSONB not null,
    dirty_bit               boolean not null default true,
    source_id               uuid not null,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp,

    primary key (recipe_index, week, product_family, locale, country),
    foreign key (source_id) references file_uploads(id)
    );

create table if not exists public.demand
(
    recipe_index            integer not null,
    week                    varchar not null,
    product_family          varchar not null,
    country                 varchar not null,
    locale                  varchar not null,
    day                     varchar not null,
    dc_code                 varchar not null,

    people_count            integer not null,
    meals_to_deliver        integer not null,
    dirty_bit               boolean not null default true,
    source_id               uuid not null,

    created_at              timestamp with time zone default now(),
    updated_at              timestamp,

    primary key (recipe_index, week, day, product_family, country, dc_code, people_count, locale),
    foreign key (source_id) references file_uploads(id)
    );
