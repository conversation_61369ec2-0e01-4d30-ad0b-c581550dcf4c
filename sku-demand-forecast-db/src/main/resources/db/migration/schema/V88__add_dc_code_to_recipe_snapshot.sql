ALTER TABLE IF EXISTS public.recipe_snapshot
    ADD COLUMN IF NOT EXISTS dc_code TEXT NOT NULL DEFAULT 'ALL';

ALTER TABLE  public.recipe_snapshot DROP CONSTRAINT recipe_snapshot_pkey;
ALTER TABLE  public.recipe_snapshot ADD CONSTRAINT recipe_snapshot_pkey primary key (recipe_index, week, product_family, locale, country, brand, dc_code);

DROP VIEW IF EXISTS recipe_view;

CREATE VIEW recipe_view AS
SELECT recipe_index,
       week,
       product_family,
       country,
       locale,
       value,
       brand,
       dirty_bit,
       source_id,
       'RECIPE' AS source,
       'ALL' AS dc_code,
       created_at,
       updated_at
FROM recipe
UNION ALL
SELECT
    recipe_index,
    week,
    product_family,
    country,
    locale,
    value,
    brand,
    dirty_bit,
    source_id,
    'MPS' AS source,
    dc_code,
    created_at,
    updated_at
FROM recipe_snapshot;
