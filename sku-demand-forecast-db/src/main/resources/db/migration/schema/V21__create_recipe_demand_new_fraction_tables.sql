alter table if exists public.file_uploads add primary key (id);

create table if not exists public.recipe
(
    index                   integer not null,
    week_value              varchar not null,
    family                  varchar not null,
    locale                  varchar not null,
    iso2code_value          varchar not null,
    name                    varchar not null,
    skus_mapping_sku_value  varchar not null,
    skus_mapping_name       varchar not null,
    skus_mapping_picks_1    integer not null,
    skus_mapping_picks_2    integer not null,
    skus_mapping_picks_3    integer not null,
    skus_mapping_picks_4    integer not null,
    skus_mapping_picks_5    integer not null,
    skus_mapping_picks_6    integer not null,
    dirty_bit               boolean not null default true,
    source_id               uuid not null,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp,

    primary key (index, week_value, family, locale, iso2code_value),
    foreign key (source_id) references file_uploads(id)
);

create table if not exists public.demand
(
    meal_number             integer not null,
    week                    varchar not null,
    product_family          varchar not null,
    locale                  varchar not null,
    country                 varchar not null,
    day                     varchar not null,
    dc_code                 varchar not null,
    box_name                varchar ,
    size                    integer not null,
    meals_to_deliver        integer not null,
    dirty_bit               boolean not null default true,
    source_id               uuid not null,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp,

    primary key (meal_number, week, day, product_family, country, dc_code, size, locale),
    foreign key (source_id) references file_uploads(id)
);

create table if not exists public.new_fraction_sku_demand
(
    sku_id         uuid    not null,
    dc_code        varchar not null,
    demand_date    date    not null,
    country        varchar not null,
    locale         varchar not null,
    product_family varchar not null,
    recipe_index   integer not null,
    qty            bigint  not null,
    created_at     timestamp default now(),
    updated_at     timestamp,
    primary key (sku_id, demand_date, dc_code, country, locale, recipe_index, product_family)
);

create or replace trigger recipe
    before update
                      on public.new_fraction_sku_demand
                      for each row
                      execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
                      on public.demand
                      for each row
                      execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
                      on public.new_fraction_sku_demand
                      for each row
                      execute procedure public.trigger_set_timestamp();

