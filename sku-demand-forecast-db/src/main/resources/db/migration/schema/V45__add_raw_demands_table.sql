drop table if exists public.raw_demand;

create table if not exists public.raw_demands
(
    hash_message            text        primary key,
    batch_uuid              UUID        NOT NULL,
    batch_create_time       timestamp with time zone not null,
    batch_start_week        varchar not null,
    batch_end_week          varchar not null,
    recipe_index            integer not null, -- slot
    week                    varchar not null,
    country                 varchar not null,
    people_count            integer not null, -- size
    brand                   varchar not null,
    dc_code                 varchar not null,
    day                     varchar not null,
    demand_attribute        varchar not null,
    meals_to_deliver        integer not null, -- quantity
    current_rows            integer not null,
    total_rows              integer not null,
    created_at              timestamp with time zone default now()
);

create index if not exists raw_demands_version_current_total_rows
    on public.raw_demands (batch_uuid, current_rows, total_rows);

select schedule_cron_job('clean raw_demands schedule', '30 4 * * *', 'delete from raw_demands where created_at < now()::date - interval ''2 days''');
