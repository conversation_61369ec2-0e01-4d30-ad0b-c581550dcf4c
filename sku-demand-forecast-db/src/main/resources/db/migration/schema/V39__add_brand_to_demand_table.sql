create table if not exists public.raw_demand
(
    recipe_index            integer not null, -- slot
    week                    varchar not null,
    country                 varchar not null,
    people_count            integer not null, -- size
    brand                   varchar not null,
    dc_code                 varchar not null,
    day                     varchar not null,
    meals_to_deliver        integer not null, -- quantity
    current_rows            integer not null,
    total_rows              integer not null,
    created_at              timestamp with time zone default now(),

    primary key (recipe_index, people_count, week, brand, country, day, dc_code)
);

alter table if exists public.demand
    add column if not exists brand TEXT default 'UNKNOWN';
