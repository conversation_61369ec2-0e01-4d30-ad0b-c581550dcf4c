create table if not exists public.dc_config
(
    dc_code          text                             not null primary key,
    market           text                             not null,
    production_start text                             not null,
    zone_id          text                             not null,
    enabled          boolean                             not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null
);

create or replace trigger set_timestamp
    before update
          on public.dc_config
          for each row
          execute procedure public.trigger_set_timestamp();
