drop table if exists public.recipe_sku_substitution;

create table if not exists public.recipe_sku_substitution
(
    recipe_index            integer not null,
    week                    varchar not null,
    country                 varchar not null,
    brand                   TEXT default 'UNKNOWN',
    dc_code                 varchar not null,
    people_count            integer not null,
    value                   JSONB not null,
    from_date               date not null,
    to_date                 date not null,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp,
    primary key (recipe_index, week, country, brand, dc_code, people_count)
);

create or replace trigger recipe_sku_substitution_updated_at_trigger
    before update on public.recipe_sku_substitution
        for each row execute procedure public.trigger_set_timestamp();
