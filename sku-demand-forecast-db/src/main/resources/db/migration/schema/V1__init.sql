CREATE TABLE IF NOT EXISTS sku_specification
(
    id           UUID    PRIMARY KEY,
    parent_id    UUID,
    code         varchar NOT NULL,
    category     varchar NOT NULL,
    name         varchar,
    cooling_type varchar NOT NULL,
    packaging    varchar NOT NULL
);

CREATE INDEX sku_specification_sku_parent_id_index ON sku_specification(parent_id);

CREATE TABLE IF NOT EXISTS fraction_sku_demand
(
    sku_id               UUID    NOT NULL,
    sku_code             varchar,
    dc_code              varchar NOT NULL,
    demand_date          date    NOT NULL,
    day_of_week          varchar,
    production_year_week varchar
);

ALTER TABLE fraction_sku_demand
    ADD CONSTRAINT fraction_sku_demand_pk PRIMARY KEY (sku_id, demand_date, dc_code);
