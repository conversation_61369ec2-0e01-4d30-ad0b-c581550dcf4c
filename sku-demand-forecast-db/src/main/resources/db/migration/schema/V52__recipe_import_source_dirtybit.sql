
ALTER TABLE IF EXISTS public.imports
    ADD CONSTRAINT imports_id_pk primary key (id)
;

ALTER TABLE IF EXISTS public.recipe_snapshot
    ADD COLUMN IF NOT EXISTS source_id uuid DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS dirty_bit boolean NOT NULL DEFAULT false,
    ADD COLUMN IF NOT EXISTS brand TEXT DEFAULT 'UNKNOWN',
    ADD CONSTRAINT fk_recipe_snapshot_sourceid_imports_id foreign key (source_id) references imports(id)
;

ALTER TABLE  public.recipe_snapshot DROP CONSTRAINT recipe_snapshot_pkey;
ALTER TABLE  public.recipe_snapshot ADD CONSTRAINT recipe_snapshot_pkey primary key (recipe_index, week, product_family, locale, country, brand);


ALTER TABLE IF EXISTS public.recipe_snapshot
    ALTER COLUMN dirty_bit SET DEFAULT true;
