@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.sdf.kotlin-conventions")
    alias(libs.plugins.flyway)
}
group = "$group.${project.name}"
description = "Manages the sku demand forecast database schemas"

dependencies {
    runtimeOnly(libs.postgresql.driver)
}

/**
 * Configure the Flyway migration tool.
 *
 * For more information: [flywayDB documentation](https://flywaydb.org/documentation/)
 */
val tier = System.getenv("HF_TIER") ?: "local"
val dbUser = System.getenv("DB_USERNAME") ?: "sdf"
val dbHost = if (tier == "local") "localhost" else "sku-demand-forecast-db000.$tier.hellofresh.io"
val dbPassword = System.getenv("DB_PASSWORD")
val dbUrl = "**********************************"
val outboxDbUser: String = if (tier == "local") "123456" else System.getenv("DB_OUTBOX_USERNAME")
val outboxDbPassword: String = if (tier == "local") "123456" else System.getenv("DB_OUTBOX_PASSWORD")

flyway {
    url = dbUrl
    user = dbUser
    password = dbPassword
    locations = arrayOf("filesystem:./src/main/resources/db/migration/")
    failOnMissingLocations = true
    placeholders = mapOf(
        "DB_USERNAME" to dbUser,
        "DB_OUTBOX_USERNAME" to outboxDbUser,
        "DB_OUTBOX_PASSWORD" to "'$outboxDbPassword'",
    )
}
