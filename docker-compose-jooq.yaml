---

services:
  postgres-jooq-sdf:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:15.5
    container_name: postgres-jooq-sdf
    ports:
      - '${DB_JOOQ_PORT_SDF:-5551}:5432'
    healthcheck:
      test: [ "CMD-SHELL",
          "pg_isready -q -U ${DB_USERNAME} -d ${DB_NAME}
                    && psql -c '\\x' -c 'SELECT (count(*) > 0) as flyway FROM flyway_schema_history' ${DB_USERNAME} -d ${DB_NAME} --csv -q
                    | grep 'flyway,t'
          "
      ]
      interval: 1s
      timeout: 2s
      retries: 30
    environment:
      LC_ALL: C.UTF-8
      POSTGRES_USER: "sdf"
      POSTGRES_PASSWORD: "123456"
      POSTGRES_DB: "sdf"
    volumes:
      - ./docker/dev/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql

  db-migration-jooq-sdf:
    image: flyway/flyway:9.22
    container_name: db-migration-jooq-sdf
    depends_on:
      - postgres-jooq-sdf
    command: -locations=filesystem:/flyway/sql -connectRetries=60 -connectRetriesInterval=1 -placeholders.DB_OUTBOX_USERNAME=outbox -placeholders.DB_OUTBOX_PASSWORD="'123456'" -placeholders.DB_USERNAME=sdf migrate
    volumes:
      - ./sku-demand-forecast-db/src/main/resources/db/migration/schema:/flyway/sql/migration/schema
    environment:
      FLYWAY_URL: '****************************************************************'
      FLYWAY_USER: "sdf"
      FLYWAY_PASSWORD: "123456"
      FLYWAY_DRIVER: org.postgresql.Driver
