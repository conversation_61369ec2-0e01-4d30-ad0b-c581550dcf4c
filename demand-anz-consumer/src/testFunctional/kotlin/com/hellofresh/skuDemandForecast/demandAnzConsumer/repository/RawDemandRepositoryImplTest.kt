package com.hellofresh.skuDemandForecast.demandAnzConsumer.repository

import com.google.protobuf.Timestamp
import com.google.type.Date
import com.hellofresh.proto.stream.demand.recipeDemandForecast.v2.RecipeDemandForecastKey
import com.hellofresh.proto.stream.demand.recipeDemandForecast.v2.RecipeDemandForecastVal
import com.hellofresh.skuDemandForecast.anzDemandConsumer.FunctionalTest
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.RAW_DEMANDS
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.tables.records.RawDemandsRecord
import com.hellofresh.skuDemandForecast.demandAnzConsumer.model.DcConfig
import java.time.DayOfWeek
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.Optional
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME
import org.junit.jupiter.api.Test

private const val DC_WEEK = "2024-W08"
private const val REGULAR = "regular"
class RawDemandRepositoryImplTest : FunctionalTest() {
    private val rawDemandRepositoryImpl = RawDemandRepositoryImpl(
        dsl,
        mapOf(
            "ML" to DcConfig(
                dcCode = "ML",
                zoneId = ZoneId.of("Australia/Sydney"),
                DayOfWeek.MONDAY,
                "AU"
            ),
        ),
    )

    @Test
    fun `insert new raw demand records`() {
        val rawDemandRecords = (0..2).map { i -> generateMockRawDemandData(i) }
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to rawDemandRecords))
        runBlocking {
            rawDemandRepositoryImpl.save(records = consumerRecords)
        }
        val records = dsl.fetch(RAW_DEMANDS)

        assertEquals(3, records.size)
        records.forEachIndexed { i, rawDemand ->
            val expectedRecipeDemandForecast = rawDemandRecords[i].value()!!
            expectedRecipeDemandForecast.apply {
                assertEquals(slot, rawDemand.recipeIndex)
                assertEquals(week, rawDemand.week)
                assertEquals(regionCode, rawDemand.country)
                assertEquals(size, rawDemand.peopleCount)
                assertEquals(brand, rawDemand.brand)
                assertEquals(dc, rawDemand.dcCode)
                assertEquals(attribute, rawDemand.demandAttribute)
                assertEquals(quantity, rawDemand.mealsToDeliver)
                assertEquals(currentRow, rawDemand.currentRows)
                assertEquals(totalRows, rawDemand.totalRows)
                assertEquals(batchUuid, rawDemand.batchUuid.toString())
                assertEquals(batchStartWeek, rawDemand.batchStartWeek)
                assertEquals(batchEndWeek, rawDemand.batchEndWeek)
                assertEquals(
                    "${rawDemandRecords[i].partition()}${rawDemandRecords[i].offset()}",
                    rawDemand.hashMessage
                )
            }
        }
    }

    @Test
    fun `fetch the latest batchId of raw demands`() {
        val batchIdParam = UUID.randomUUID()
        val rawDemandRecord1 = createRawDemand(1, 3, batchIdParam)
        val rawDemandRecord2 = createRawDemand(2, 3, batchIdParam)
        val rawDemandRecord3 = createRawDemand(3, 3, batchIdParam)
        runBlocking {
            dsl.batchInsert(rawDemandRecord1, rawDemandRecord2, rawDemandRecord3).execute()
            val records = rawDemandRepositoryImpl.fetchCompletedRawDemandBatchIds()
            assertEquals(1, records.size)
            assertEquals(rawDemandRecord1.batchUuid, records.first())
        }
    }

    @Test
    fun `fetch the empty list of batch ids from raw demands for non existing records`() {
        val rawDemandRecord1 = createRawDemand(1, 3)
        val rawDemandRecord2 = createRawDemand(2, 3)
        runBlocking {
            dsl.batchInsert(rawDemandRecord1, rawDemandRecord2).execute()
            val records = rawDemandRepositoryImpl.fetchCompletedRawDemandBatchIds()
            assertEquals(0, records.size)
        }
    }

    @Test
    fun `fetch the empty list of batch ids from raw demands for existing records with different batch id`() {
        val rawDemandRecord1 = createRawDemand(1, 3)
        val rawDemandRecord2 = createRawDemand(2, 3)
        val rawDemandRecord3 = createRawDemand(3, 3, batchIdParam = UUID.randomUUID())
        runBlocking {
            dsl.batchInsert(rawDemandRecord1, rawDemandRecord2, rawDemandRecord3).execute()
            val records = rawDemandRepositoryImpl.fetchCompletedRawDemandBatchIds()
            assertEquals(0, records.size)
        }
    }

    @Test
    fun `fetch the empty list of batch ids from raw demands for records which are older than an hour`() {
        val rawDemandRecord1 = createRawDemand(1, 3)
        val rawDemandRecord2 = createRawDemand(2, 3)
        val rawDemandRecord3 = createRawDemand(
            3,
            3,
            createdAtParam = OffsetDateTime.now().minusDays(1)
        )
        runBlocking {
            dsl.batchInsert(rawDemandRecord1, rawDemandRecord2, rawDemandRecord3).execute()
            val records = rawDemandRepositoryImpl.fetchCompletedRawDemandBatchIds()
            assertEquals(0, records.size)
        }
    }

    @Test
    fun `fetch the raw demands for the given batch id`() {
        val batchIdParam = UUID.randomUUID()
        val rawDemandRecord1 = createRawDemand(1, 3, batchIdParam)
        val rawDemandRecord2 = createRawDemand(2, 3, batchIdParam)
        val rawDemandRecord3 = createRawDemand(3, 3, batchIdParam)
        runBlocking {
            dsl.batchInsert(rawDemandRecord1, rawDemandRecord2, rawDemandRecord3).execute()
            val records = rawDemandRepositoryImpl.fetchRawDemandsUsingBatchId(rawDemandRecord1.batchUuid)
            assertEquals(3, records.size)
            records.map {
                assertEquals(1, it.recipeIndex)
                assertEquals(DC_WEEK, it.week)
                assertEquals("ML", it.dcCode)
                assertEquals("HF", it.brand)
                assertEquals(2, it.peopleCount)
                assertEquals(REGULAR, it.attribute)
                assertEquals(DayOfWeek.MONDAY, it.day)
                assertEquals(10, it.mealsToDeliver)
                assertEquals(rawDemandRecord1.batchUuid, it.batchId)
                assertEquals(rawDemandRecord1.batchStartWeek, it.startWeek.value)
                assertEquals(rawDemandRecord1.batchEndWeek, it.endWeek.value)
            }
        }
    }

    @Test
    fun `should return empty raw demands for the given non existing batch id`() {
        val rawDemandRecord1 = createRawDemand(1, 3)
        runBlocking {
            dsl.batchInsert(rawDemandRecord1).execute()
            val records = rawDemandRepositoryImpl.fetchRawDemandsUsingBatchId(UUID.randomUUID())
            assertEquals(0, records.size)
        }
    }
    private fun createRawDemand(
        currentRowsParam: Int = 1,
        totalRowsParam: Int = 1,
        batchIdParam: UUID = UUID.randomUUID(),
        createdAtParam: OffsetDateTime = OffsetDateTime.now()
    ): RawDemandsRecord =
        RawDemandsRecord().apply {
            recipeIndex = 1
            week = DC_WEEK
            country = "AU"
            peopleCount = 2
            brand = "HF"
            dcCode = "ML"
            day = DayOfWeek.MONDAY.name
            demandAttribute = REGULAR
            mealsToDeliver = 10
            batchUuid = batchIdParam
            batchCreateTime = OffsetDateTime.now()
            batchStartWeek = DC_WEEK
            batchEndWeek = DC_WEEK
            currentRows = currentRowsParam
            totalRows = totalRowsParam
            hashMessage = "partition+offset" + UUID.randomUUID()
            createdAt = createdAtParam
        }

    private fun generateMockRawDemandData(
        i: Int,
        recordTimestampIncrement: Long = i.toLong()
    ): ConsumerRecord<RecipeDemandForecastKey, RecipeDemandForecastVal> {
        val key = RecipeDemandForecastKey.newBuilder().apply {
            slot = 1
            week = DC_WEEK
            regionCode = "AU"
            brand = "au-brand"
            attribute = REGULAR
            size = 2
        }.build()
        val value = RecipeDemandForecastVal.newBuilder().apply {
            batchUuid = UUID.randomUUID().toString()
            batchCreateTime = Timestamp.newBuilder().apply {
                seconds = OffsetDateTime.now(ZoneOffset.UTC).toEpochSecond()
                nanos = OffsetDateTime.now(ZoneOffset.UTC).nano
            }.build()
            batchStartWeek = DC_WEEK
            batchEndWeek = DC_WEEK
            slot = 1
            week = DC_WEEK
            regionCode = "AU"
            size = 2
            brand = "au-brand"
            dc = "ML"
            attribute = REGULAR
            productionDay = Date.newBuilder()
                .setYear(2024)
                .setMonth(2)
                .setDay(14)
                .build()
            quantity = 10
            currentRow = 100
            totalRows = 100
            version = 1
        }.build()

        return ConsumerRecord(
            "test", 0, i.toLong(),
            Instant.now().toEpochMilli() + recordTimestampIncrement,
            CREATE_TIME, 1, 1, key,
            value,
            RecordHeaders(), Optional.empty(),
        )
    }
}
