package com.hellofresh.skuDemandForecast.demandAnzConsumer.repository

import com.hellofresh.skuDemandForecast.anzDemandConsumer.FunctionalTest
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.ANZ_PREKITTING_DEMAND
import com.hellofresh.skuDemandForecast.anzPrekitting.model.Prekitting
import default
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class AnzPrekittingDemandRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should save the prekitting demands`() {
        val anzPrekitingDemandRecordOne = anzPrekittingDemand()
        val anzPrekitingDemandRecordTwo = anzPrekittingDemand(weekParam = "2024-W21")
        val anzPrekitingDemandRecordThree = anzPrekittingDemand(weekParam = "2024-W22")
        runBlocking {
            dsl.batchInsert(
                anzPrekitingDemandRecordOne,
                anzPrekitingDemandRecordTwo,
                anzPrekitingDemandRecordThree
            ).execute()
            val prekitting = Prekitting.default().copy(demandQty = 1000L)
            anzPreKittingDemandRepository.save(
                prekittings = listOf(prekitting),
            )
            val prekittingDemands = dsl.selectFrom(
                ANZ_PREKITTING_DEMAND,
            ).toList()

            val existingPrekittingDemandOne = prekittingDemands.first {
                it.week == "2024-W21"
            }
            assertNotNull(existingPrekittingDemandOne)
            val existingPrekittingDemandTwo = prekittingDemands.first {
                it.week == "2024-W22"
            }
            assertNotNull(existingPrekittingDemandTwo)

            val updatingPrekittingDemand = prekittingDemands.first {
                it.week == "2024-W20"
            }
            assertEquals(prekitting.dcCode, updatingPrekittingDemand.dcCode)
            assertEquals(prekitting.skuId, updatingPrekittingDemand.skuId)
            assertEquals(prekitting.demandDate, updatingPrekittingDemand.demandDate)
            assertEquals(1000L, updatingPrekittingDemand.demandQuantity)
        }
    }
}
