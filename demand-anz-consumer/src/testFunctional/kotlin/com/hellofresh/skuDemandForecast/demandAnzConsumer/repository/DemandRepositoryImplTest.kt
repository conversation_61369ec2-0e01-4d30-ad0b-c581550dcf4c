package com.hellofresh.skuDemandForecast.demandAnzConsumer.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.skuDemandForecast.anzDemandConsumer.FunctionalTest
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.DEMAND
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.tables.records.DemandRecord
import com.hellofresh.skuDemandForecast.demandAnzConsumer.model.Demand
import com.hellofresh.skuDemandForecast.demandAnzConsumer.repository.DemandRepositoryImpl.ExistingDemandKey
import com.hellofresh.skuDemandForecast.models.DemandDetails
import default
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

private const val COUNTRY = "AU"
private const val DC_CODE = "SY"
private const val WEEK = "2024-W08"

class DemandRepositoryImplTest : FunctionalTest() {
    private val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()

    @Test
    fun `should fetch demands with the given country list, dc codes and weeks`() {
        val demandRecordOne = demandRecord()
        runBlocking {
            dsl.batchInsert(demandRecordOne).execute()
            dsl.transaction { configuration ->
                val transaction = dsl.withMeteredConfiguration(configuration)
                val result = demandRepository.fetchDemands(
                    transaction,
                    setOf(ExistingDemandKey(COUNTRY, "ML", WEEK, "HF")),
                )
                Assertions.assertEquals(1, result.size)
                Assertions.assertEquals(demandRecordOne.country, result.first().country)
                Assertions.assertEquals(demandRecordOne.week, result.first().week)
                Assertions.assertEquals(demandRecordOne.demandDetails, result.first().demandDetails)
            }
        }
    }

    @Test
    fun `should fetch empty demands with the given non existing country list and weeks`() {
        val demandRecordOne = demandRecord()
        runBlocking {
            dsl.batchInsert(demandRecordOne).execute()
            dsl.transaction { configuration ->
                val transaction = dsl.withMeteredConfiguration(configuration)
                val result = demandRepository.fetchDemands(
                    transaction,
                    setOf(
                        ExistingDemandKey("GB", DC_CODE, WEEK, "HF"),
                    ),
                )
                Assertions.assertEquals(0, result.size)
            }
        }
    }

    @Test
    fun `should persist new demands`() {
        val demandRecordOne = demandRecord(mealNumberParam = 10)
        val demandRecordTwo = demandRecord(mealNumberParam = 20)
        val demandRecordThree = demandRecord(mealNumberParam = 30)
        val demandRecordFour = demandRecord(mealNumberParam = 40)
        runBlocking {
            dsl.batchInsert(
                demandRecordOne,
                demandRecordTwo,
                demandRecordThree,
                demandRecordFour,
            ).execute()
            val newDemandOne = createDemand(demandRecordOne)
            val newDemandThree = createDemand(demandRecordThree)
            demandRepository.persistDemands(listOf(newDemandOne, newDemandThree))
            val result = dsl.selectFrom(DEMAND).fetch()
            Assertions.assertEquals(2, result.size)
            val newDemandResultOne = result.first { it.recipeIndex == demandRecordOne.recipeIndex }
            assertNotNull(newDemandResultOne)
            assertTrue(newDemandResultOne.dirtyBit)
            val newDemandResultThree = result.first { it.recipeIndex == demandRecordThree.recipeIndex }
            assertNotNull(newDemandResultThree)
            assertTrue(newDemandResultThree.dirtyBit)
        }
    }

    @Test
    fun `should persist new demands for different brand`() {
        val demandRecordHF = demandRecord(mealNumberParam = 10)
        runBlocking {
            dsl.batchInsert(
                demandRecordHF,
            ).execute()
            val newDemandOne = createDemand(demandRecordHF).copy(
                brand = "EP",
            )
            demandRepository.persistDemands(listOf(newDemandOne))
            val result = dsl.selectFrom(DEMAND).fetch()
            Assertions.assertEquals(2, result.size)
            val newDemandResultHF = result.first { it.brand == demandRecordHF.brand }
            assertNotNull(newDemandResultHF)
            assertTrue(newDemandResultHF.dirtyBit)
            val newDemandResultEP = result.first { it.brand == newDemandOne.brand }
            assertNotNull(newDemandResultEP)
            assertTrue(newDemandResultEP.dirtyBit)
        }
    }

    @Test
    fun `should create the new demands`() {
        val demandRecordOne = demandRecord(mealNumberParam = 10)
        val demandRecordTwo = demandRecord(mealNumberParam = 20)
        runBlocking {
            val newDemandOne = createDemand(demandRecordOne)
            val newDemandTwo = createDemand(demandRecordTwo)
            demandRepository.persistDemands(listOf(newDemandOne, newDemandTwo))
            val result = dsl.selectFrom(DEMAND).fetch()
            Assertions.assertEquals(2, result.size)
            val newDemandResultOne = result.first { it.recipeIndex == newDemandOne.recipeIndex }
            assertNotNull(newDemandResultOne)
            assertTrue(newDemandResultOne.dirtyBit)
            val newDemandResultThree = result.first { it.recipeIndex == newDemandTwo.recipeIndex }
            assertNotNull(newDemandResultThree)
            assertTrue(newDemandResultThree.dirtyBit)
        }
    }

    @Test
    fun `should delete the previous demands and create the new demands`() {
        val demandRecordOne = demandRecord(mealNumberParam = 10)
        val demandRecordTwo = demandRecord(mealNumberParam = 20)
        val demandWithEPBrand = demandRecord(mealNumberParam = 30, brandParam = "EP")
        runBlocking {
            val newDemandOne = createDemand(demandRecordOne)
            val newDemandTwo = createDemand(demandRecordTwo)
            val newDemandWithEPBrand = createDemand(demandWithEPBrand)
            demandRepository.persistDemands(listOf(newDemandOne, newDemandTwo, newDemandWithEPBrand))
            val result = dsl.selectFrom(DEMAND).fetch()
            Assertions.assertEquals(3, result.size)
            val newDemandResultOne = result.first { it.recipeIndex == newDemandOne.recipeIndex }
            assertNotNull(newDemandResultOne)
            assertTrue(newDemandResultOne.dirtyBit)
            val newDemandResultTwo = result.first { it.recipeIndex == newDemandTwo.recipeIndex }
            assertNotNull(newDemandResultTwo)
            assertTrue(newDemandResultTwo.dirtyBit)

            // next batch with different recipe index
            val demandRecordThree = demandRecord(mealNumberParam = 100)
            val demandRecordFour = demandRecord(mealNumberParam = 200)
            val newDemandThree = createDemand(demandRecordThree)
            val newDemandFour = createDemand(demandRecordFour)
            demandRepository.persistDemands(listOf(newDemandThree, newDemandFour))
            val nextResult = dsl.selectFrom(DEMAND).fetch()
            Assertions.assertEquals(3, nextResult.size)
            val newDemandResultThree = result.first { it.recipeIndex == newDemandOne.recipeIndex }
            assertNotNull(newDemandResultThree)
            assertTrue(newDemandResultThree.dirtyBit)
            val newDemandResultFour = result.first { it.recipeIndex == newDemandTwo.recipeIndex }
            assertNotNull(newDemandResultFour)
            assertTrue(newDemandResultFour.dirtyBit)
            val newDemandResultWithEPbrand = result.first { it.recipeIndex == newDemandWithEPBrand.recipeIndex }
            assertNotNull(newDemandResultWithEPbrand)
            assertTrue(newDemandResultWithEPbrand.dirtyBit)
        }
    }

    @Test
    fun `should return demand records to be deleted`() {
        val newDemand1 = Demand.default(recipeIndexParam = 1)
        val newDemand2 = Demand.default(recipeIndexParam = 3)
        val newDemands = listOf(
            newDemand1,
            newDemand2,
        )
        val existingDemand1 = demandRecord(mealNumberParam = 1)
        val existingDemand2 = demandRecord(mealNumberParam = 2)
        val existingDemand3 = demandRecord(mealNumberParam = 3)
        val demands = listOf(
            existingDemand1,
            existingDemand2,
            existingDemand3,
        )

        val demandsToBeDeleted = demandRepository.getDemandsToBeDeleted(newDemands, demands)
        assertEquals(1, demandsToBeDeleted.size)
        demandsToBeDeleted.first().apply {
            assertEquals(2, recipeIndex)
            assertEquals(WEEK, week)
            assertEquals(COUNTRY, country)
            assertEquals(2, peopleCount)
            assertEquals("HF", brand)
            assertEquals("ML", dcCode)
            assertEquals(MONDAY.name, day)
            assertEquals(1, mealsToDeliver)
        }
    }

    @Test
    fun `should return empty demand records to be deleted if the existing & new demands matches`() {
        val newDemand1 = Demand.default(recipeIndexParam = 1)
        val newDemand2 = Demand.default(recipeIndexParam = 2)
        val newDemand3 = Demand.default(recipeIndexParam = 3)
        val newDemands = listOf(
            newDemand1,
            newDemand2,
            newDemand3,
        )
        val existingDemand1 = demandRecord(mealNumberParam = 1)
        val existingDemand2 = demandRecord(mealNumberParam = 2)
        val existingDemand3 = demandRecord(mealNumberParam = 3)
        val demands = listOf(
            existingDemand1,
            existingDemand2,
            existingDemand3,
        )

        val demandsToBeDeleted = demandRepository.getDemandsToBeDeleted(newDemands, demands)
        assertTrue(demandsToBeDeleted.isEmpty())
    }

    @Test
    fun `should return empty demand records to be deleted if the new demands has additional demands`() {
        val newDemand1 = Demand.default(recipeIndexParam = 1)
        val newDemand2 = Demand.default(recipeIndexParam = 2)
        val newDemand3 = Demand.default(recipeIndexParam = 3)
        val newDemands = listOf(
            newDemand1,
            newDemand2,
            newDemand3,
        )
        val existingDemand1 = demandRecord(mealNumberParam = 1)
        val existingDemand2 = demandRecord(mealNumberParam = 3)
        val demands = listOf(
            existingDemand1,
            existingDemand2,
        )

        val demandsToBeDeleted = demandRepository.getDemandsToBeDeleted(newDemands, demands)
        assertTrue(demandsToBeDeleted.isEmpty())
    }

    private fun createDemand(demandRecord: DemandRecord) = Demand(
        recipeIndex = demandRecord.recipeIndex,
        week = demandRecord.week,
        country = demandRecord.country,
        peopleCount = demandRecord.peopleCount,
        brand = demandRecord.brand,
        dcCode = demandRecord.dcCode,
        day = DayOfWeek.valueOf(demandRecord.day.uppercase()),
        mealsToDeliver = 10,
        family = demandRecord.productFamily,
        locale = if (demandRecord.locale == NULL_LOCALE_VALUE) null else demandRecord.locale,
        sourceId = demandRecord.sourceId,
        demandDetails = demandRecord.demandDetails.let {
            objectMapper.readValue<DemandDetails>(it.data())
        },
    )
}
