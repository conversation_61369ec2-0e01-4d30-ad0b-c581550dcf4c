package com.hellofresh.skuDemandForecast.anzDemandConsumer

import com.google.protobuf.Timestamp
import com.google.type.Date
import com.hellofresh.proto.stream.demand.recipeDemandForecast.v2.RecipeDemandForecastKey
import com.hellofresh.proto.stream.demand.recipeDemandForecast.v2.RecipeDemandForecastVal
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.RAW_DEMANDS
import com.hellofresh.skuDemandForecast.demandAnzConsumer.consumer.RECIPE_DEMAND_TOPIC
import java.time.Duration
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.ProducerRecord
import org.junit.jupiter.api.Test
import org.testcontainers.shaded.org.awaitility.Awaitility

class AnzDemandFunctionalTest : FunctionalTest() {
    @Test
    fun `should consume from recipe demand forecast topic`() {
        // given
        val key = with(RecipeDemandForecastKey.newBuilder()) {
            week = "2024-W08"
            regionCode = "AU"
            brand = "HF"
            dc = "ML"
            size = 2
            slot = 70
            attribute = "Regular"
            productionDay = with(Date.newBuilder()) {
                day = 1
                month = 2
                year = 2024
                build()
            }
            build()
        }

        val value = with(RecipeDemandForecastVal.newBuilder()) {
            batchUuid = UUID.randomUUID().toString()
            batchCreateTime = Timestamp.newBuilder().apply {
                seconds = OffsetDateTime.now(ZoneOffset.UTC).toEpochSecond()
                nanos = OffsetDateTime.now(ZoneOffset.UTC).nano
            }.build()
            batchStartWeek = "2024-W05"
            batchStartWeek = "2024-W10"
            week = "2024-W08"
            regionCode = "AU"
            brand = "HF"
            dc = "ML"
            size = 2
            slot = 70
            quantity = 300
            currentRow = 10
            totalRows = 1000
            attribute = "Regular"
            productionDay = with(Date.newBuilder()) {
                day = 1
                month = 2
                year = 2024
                build()
            }
            build()
        }

        topicProducer.send(ProducerRecord(RECIPE_DEMAND_TOPIC, key, value)).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = dsl.selectFrom(RAW_DEMANDS).fetch()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    records = dsl.selectFrom(RAW_DEMANDS).fetch()
                    records.count() == 1
                }.let {
                    val record = records.first()
                    assertEquals(value.batchUuid, record.batchUuid.toString())
                    assertEquals(value.batchStartWeek, record.batchStartWeek)
                    assertEquals(value.batchEndWeek, record.batchEndWeek)
                    assertEquals(value.week, record.week)
                    assertEquals(value.regionCode, record.country)
                    assertEquals(value.size, record.peopleCount)
                    assertEquals(value.quantity, record.mealsToDeliver)
                    assertEquals(value.dc, record.dcCode)
                    assertEquals(value.brand, record.brand)
                    assertEquals(value.currentRow, record.currentRows)
                    assertEquals(value.totalRows, record.totalRows)
                }

            app.cancel()
        }
    }
}
