package com.hellofresh.skuDemandForecast.anzPrekitting

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.skuDemandForecast.anzPrekitting.model.PrekittingResponse
import com.hellofresh.skuDemandForecast.anzPrekitting.model.PrekittingWithDays
import com.hellofresh.skudemandforecast.model.distributioncenter.DcWeek
import io.github.resilience4j.retry.Retry
import io.github.resilience4j.retry.RetryConfig
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.binder.okhttp3.OkHttpMetricsEventListener
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SATURDAY
import java.time.DayOfWeek.SUNDAY
import java.time.DayOfWeek.THURSDAY
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.Duration
import okhttp3.OkHttpClient
import org.apache.logging.log4j.kotlin.Logging
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory

private const val MAX_RETRIES = 3
private const val WAIT_DURATION_BETWEEN_RETRIES_MS = 1000L
private const val DEFAULT_TIMEOUT_MILLIS: Long = 30000L
private const val PRECONDITION_FAILED_HTTP_CODE = 412

class PrekittingHttpClient(
    applicationName: String,
    serviceUrl: String,
    meterRegistry: MeterRegistry
) {
    private val client = createEndpoints(
        serviceUrl,
        createHttpClient(meterRegistry = meterRegistry, applicationName = applicationName),
        objectMapper(),
    )
    private val retryConfig = RetryConfig.custom<Any>()
        .maxAttempts(MAX_RETRIES)
        .waitDuration(Duration.ofMillis(WAIT_DURATION_BETWEEN_RETRIES_MS))
        .build()
    private val retry = Retry.of("$applicationName.retry", retryConfig)

    fun get(dcCode: String, week: DcWeek, token: String): List<PrekittingWithDays> =
        retry.executeCallable {
            val response = client.getPrekittingsByDcCodeAndWeek(dcCode, week.value, token).execute()
            when {
                response.isSuccessful -> getPrekittingWithDays(response)
                response.code() == PRECONDITION_FAILED_HTTP_CODE -> {
                    logger.warn(
                        "Bad request response while getting the prekitting demand for dc = $dcCode, week = $week, response = $response",
                    )
                    emptyList()
                }

                else -> {
                    logger.error(
                        "Error occurred while getting the prekitting demand for dc = $dcCode, week = $week, response = $response",
                    )
                    emptyList()
                }
            }
        }

    private fun getPrekittingWithDays(response: Response<PrekittingResponse>): List<PrekittingWithDays> {
        val skuPrekittings = response.body()?.prekitting ?: emptyList()
        return skuPrekittings.map { skuPrekitting ->
            val daysToValue = DayOfWeek.entries.associateWith {
                when (it) {
                    MONDAY -> skuPrekitting.monday
                    TUESDAY -> skuPrekitting.tuesday
                    WEDNESDAY -> skuPrekitting.wednesday
                    THURSDAY -> skuPrekitting.thursday
                    FRIDAY -> skuPrekitting.friday
                    SATURDAY -> skuPrekitting.saturday
                    SUNDAY -> skuPrekitting.sunday
                }
            }
            PrekittingWithDays(skuPrekitting.skuCode, daysToValue)
        }
    }
    private fun createHttpClient(
        readTimeout: Duration = Duration.ofMillis(DEFAULT_TIMEOUT_MILLIS),
        connectTimeout: Duration = Duration.ofMillis(DEFAULT_TIMEOUT_MILLIS),
        meterRegistry: MeterRegistry,
        applicationName: String,
    ): OkHttpClient = OkHttpClient.Builder().apply {
        eventListener(
            OkHttpMetricsEventListener.builder(meterRegistry, "http.outgoing")
                .tag(Tag.of("application", applicationName))
                .build(),
        )
    }
        .readTimeout(readTimeout)
        .connectTimeout(connectTimeout)
        .build()

    private fun objectMapper(): ObjectMapper {
        val objectMapper = jacksonObjectMapper()
        objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.ALWAYS)
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        return objectMapper
    }

    private fun createEndpoints(
        baseUrl: String,
        okHttpClient: OkHttpClient,
        objectMapper: ObjectMapper,
    ): PrekittingServiceEndpoints = Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(okHttpClient)
        .addConverterFactory(JacksonConverterFactory.create(objectMapper))
        .build()
        .create(PrekittingServiceEndpoints::class.java)

    companion object : Logging
}
