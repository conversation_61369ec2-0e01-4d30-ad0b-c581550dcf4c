package com.hellofresh.skuDemandForecast.anzPrekitting

import com.hellofresh.skuDemandForecast.anzPrekitting.model.PrekittingResponse
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Path

interface PrekittingServiceEndpoints {
    @GET("/api/v1/production/prekitting/{market}/{week}")
    fun getPrekittingsByDcCodeAndWeek(
        @Path("market") market: String,
        @Path("week") week: String,
        @Header("encoded-token") token: String
    ): Call<PrekittingResponse>
}
