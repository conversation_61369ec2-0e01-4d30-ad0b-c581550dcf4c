package com.hellofresh.skuDemandForecast.anzPrekitting.repository

import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.ANZ_PREKITTING_DEMAND
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.tables.records.AnzPrekittingDemandRecord
import com.hellofresh.skuDemandForecast.anzPrekitting.model.Prekitting
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

interface AnzPreKittingDemandRepository {
    suspend fun save(prekittings: List<Prekitting>)
}
class AnzPreKittingDemandRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : AnzPreKittingDemandRepository {
    override suspend fun save(prekittings: List<Prekitting>) {
        metricsDSLContext.withTagName("save-prekitting-demands").transactionAsync {
            with(metricsDSLContext.withMeteredConfiguration(it)) {
                val deletedPrekittingDemands = prekittings.map { prekitting ->
                    DeletedPrekittingDemand(prekitting.dcCode, prekitting.week)
                }.toSet()
                deletePrekittingDemand(deletedPrekittingDemands)
                insertPrekittingDemands(prekittings)
            }
        }.await()
    }

    private fun MetricsDSLContext.deletePrekittingDemand(deletedPrekittingDemands: Set<DeletedPrekittingDemand>) {
        this.withTagName("delete-prekitting-demand-data")
            .deleteFrom(ANZ_PREKITTING_DEMAND)
            .where(
                DSL.row(ANZ_PREKITTING_DEMAND.DC_CODE, ANZ_PREKITTING_DEMAND.WEEK)
                    .`in`(
                        deletedPrekittingDemands.map { DSL.row(it.dcCode, it.week) },
                    ),
            )
            .execute()
    }

    private fun MetricsDSLContext.insertPrekittingDemands(prekittings: List<Prekitting>) {
        this.withTagName("insert-prekitting-demand-data").batchInsert(
            prekittings.map { prekitting ->
                AnzPrekittingDemandRecord().apply {
                    dcCode = prekitting.dcCode
                    week = prekitting.week
                    skuId = prekitting.skuId
                    demandDate = prekitting.demandDate
                    demandQuantity = prekitting.demandQty
                }
            },
        ).execute()
    }
}

internal data class DeletedPrekittingDemand(
    val dcCode: String,
    val week: String,
)
