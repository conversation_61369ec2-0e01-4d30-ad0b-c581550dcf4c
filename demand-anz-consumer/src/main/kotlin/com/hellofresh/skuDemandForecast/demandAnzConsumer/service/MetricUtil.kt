package com.hellofresh.skuDemandForecast.demandAnzConsumer.service

import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag

class MetricUtil(private val meterRegistry: MeterRegistry) {
    fun addGauge(metricName: String, count: Int) {
        meterRegistry.gauge(
            metricName,
            count,
        )
    }
    fun addGauge(metricName: String, batchId: String, count: Int) {
        meterRegistry.gauge(
            metricName,
            listOf(Tag.of("demandBatchId", batchId)),
            count
        )
    }
}
