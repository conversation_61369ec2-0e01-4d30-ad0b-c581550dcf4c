package com.hellofresh.skuDemandForecast.demandAnzConsumer.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.DEMAND
import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.tables.records.DemandRecord
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.demandAnzConsumer.model.Demand
import com.hellofresh.skuDemandForecast.demandAnzConsumer.service.MetricUtil
import java.time.DayOfWeek
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting
import org.jooq.JSONB
import org.jooq.impl.DSL
import org.jooq.impl.DSL.excluded
import org.jooq.impl.DSL.not
import org.jooq.impl.DSL.row

const val UNKNOWN_PRODUCT_FAMILY = "UNKNOWN"
const val NULL_LOCALE_VALUE = ""

class DemandRepositoryImpl(
    private val dslContext: MetricsDSLContext,
    private val metricUtil: MetricUtil,
) : DemandRepository {

    override suspend fun persistDemands(demands: List<Demand>) {
        dslContext.withTagName("save-demands-from-anz")
            .transactionAsync { configuration ->
                val metricsDSLContext = dslContext.withMeteredConfiguration(configuration)
                val (demandCountriesToFetch, demandDcCodesToFetch, demandWeeksToFetch) = demands.fold(
                    Triple(emptySet<String>(), emptySet<String>(), emptySet<String>()),
                ) { acc, demand ->
                    Triple(
                        acc.first + demand.country,
                        acc.second + demand.dcCode,
                        acc.third + demand.week,
                    )
                }
                logger.info(
                    "Fetching the demand for country = $demandCountriesToFetch " +
                        " dcCodes = $demandDcCodesToFetch, weeks = $demandWeeksToFetch",
                )
                val existingDemandKeys = demands.map {
                    ExistingDemandKey(
                        it.country,
                        it.dcCode,
                        it.week,
                        it.brand
                    )
                }.toSet()

                val currentDemands = fetchDemands(metricsDSLContext, existingDemandKeys)
                val demandsToBeDeleted = getDemandsToBeDeleted(demands, currentDemands)

                deleteExistingDemand(metricsDSLContext, demandsToBeDeleted)
                createNewDemands(metricsDSLContext, demands)
            }.await()
    }

    fun fetchDemands(
        metricsDSLContext: MetricsDSLContext,
        rawDemands: Set<ExistingDemandKey>
    ): List<DemandRecord> =
        metricsDSLContext.withTagName("fetch-existing-demands-for-anz")
            .selectFrom(DEMAND)
            .where(
                row(
                    DEMAND.COUNTRY,
                    DEMAND.DC_CODE,
                    DEMAND.WEEK,
                    DEMAND.BRAND,
                )
                    .`in`(
                        rawDemands.map {
                            row(
                                it.country,
                                it.dcCode,
                                it.week,
                                it.brand,
                            )
                        },
                    ),
            ).fetch()

    private fun createNewDemands(metricsDSLContext: MetricsDSLContext, newDemands: List<Demand>) {
        if (newDemands.isNotEmpty()) {
            logger.info("Number of demand records to be inserted = ${newDemands.size}")
            metricsDSLContext.withTagName("inserting-new-demands-for-anz").batch(
                DSL.insertInto(DEMAND)
                    .columns(
                        DEMAND.RECIPE_INDEX, DEMAND.WEEK, DEMAND.COUNTRY, DEMAND.PEOPLE_COUNT, DEMAND.BRAND,
                        DEMAND.DC_CODE, DEMAND.DAY, DEMAND.MEALS_TO_DELIVER, DEMAND.DIRTY_BIT, DEMAND.PRODUCT_FAMILY,
                        DEMAND.LOCALE, DEMAND.SOURCE_ID, DEMAND.DEMAND_DETAILS,
                    )
                    .values(0, "", "", 0, "", "", "", 0, true, "", null, null, JSONB.jsonbOrNull(null))
                    .onConflictOnConstraint(DEMAND.primaryKey)
                    .doUpdate()
                    .set(DEMAND.RECIPE_INDEX, 0)
                    .set(DEMAND.WEEK, "")
                    .set(DEMAND.COUNTRY, "")
                    .set(DEMAND.PEOPLE_COUNT, 0)
                    .set(DEMAND.BRAND, "")
                    .set(DEMAND.DC_CODE, "")
                    .set(DEMAND.DAY, "")
                    .set(DEMAND.MEALS_TO_DELIVER, 0)
                    .set(DEMAND.DIRTY_BIT, true)
                    .set(DEMAND.DEMAND_DETAILS, JSONB.valueOf("{}"))
                    .where(not(DEMAND.MEALS_TO_DELIVER.eq(excluded(DEMAND.MEALS_TO_DELIVER)))),
            ).apply {
                newDemands.forEach {
                    bind(
                        it.recipeIndex,
                        it.week,
                        it.country,
                        it.peopleCount,
                        it.brand,
                        it.dcCode,
                        it.day,
                        it.mealsToDeliver,
                        true,
                        it.family,
                        it.locale ?: NULL_LOCALE_VALUE,
                        it.sourceId,
                        JSONB.valueOf(objectMapper.writeValueAsString(it.demandDetails)),
                        it.recipeIndex,
                        it.week,
                        it.country,
                        it.peopleCount,
                        it.brand,
                        it.dcCode,
                        it.day,
                        it.mealsToDeliver,
                        true,
                        JSONB.valueOf(objectMapper.writeValueAsString(it.demandDetails)),
                    )
                }
                execute()
            }
            metricUtil.addGauge("anz_processing_new_demands", newDemands.size)
        }
    }

    private fun deleteExistingDemand(
        metricsDSLContext: MetricsDSLContext,
        demandsToBeDeleted: List<DemandRecord>
    ) {
        if (demandsToBeDeleted.isNotEmpty()) {
            logger.info(
                "Number of demand records to be deleted = ${demandsToBeDeleted.size}",
            )
            metricsDSLContext.withTagName("batch-delete-demands-for-anz").batchDelete(demandsToBeDeleted).execute()
            metricUtil.addGauge(
                "anz_demand_processing_deletes",
                demandsToBeDeleted.size,
            )
        }
    }

    @VisibleForTesting
    fun getDemandsToBeDeleted(newDemands: List<Demand>, existingDemands: List<DemandRecord>): List<DemandRecord> {
        val newDemandKeys = newDemands.map { demand ->
            DemandPrimaryKey(
                recipeIndex = demand.recipeIndex,
                week = demand.week,
                country = demand.country,
                peopleCount = demand.peopleCount,
                brand = demand.brand,
                dcCode = demand.dcCode,
                day = demand.day,
            )
        }.toSet()

        val demandsToBeDeleted = existingDemands.filter { demand ->
            val key = DemandPrimaryKey(
                recipeIndex = demand.recipeIndex,
                week = demand.week,
                country = demand.country,
                peopleCount = demand.peopleCount,
                brand = demand.brand,
                dcCode = demand.dcCode,
                day = DayOfWeek.valueOf(demand.day),
            )
            key !in newDemandKeys
        }
        return demandsToBeDeleted
    }

    data class DemandPrimaryKey(
        val recipeIndex: Int,
        val week: String,
        val country: String,
        val peopleCount: Int,
        val brand: String,
        val dcCode: String,
        val day: DayOfWeek,
    )

    data class ExistingDemandKey(
        val country: String,
        val dcCode: String,
        val week: String,
        val brand: String,
    )
    companion object : Logging {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
    }
}
