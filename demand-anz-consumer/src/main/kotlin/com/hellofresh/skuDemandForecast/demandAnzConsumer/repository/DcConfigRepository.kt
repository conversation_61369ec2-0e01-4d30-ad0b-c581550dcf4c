package com.hellofresh.skuDemandForecast.demandAnzConsumer.repository

import com.hellofresh.skuDemandForecast.anzDemandConsumer.schema.Tables.DC_CONFIG
import com.hellofresh.skuDemandForecast.db.metrics.MetricsDSLContext
import com.hellofresh.skuDemandForecast.demandAnzConsumer.model.DcConfig
import java.time.DayOfWeek
import java.time.ZoneId
import kotlinx.coroutines.future.await

fun interface DcConfigRepository {
    suspend fun fetch(): List<DcConfig>
}
class DcConfigRepositoryJooqImpl(private val dsl: MetricsDSLContext) : DcConfigRepository {
    private val tagName = "fetch-anz-dc-config"
    override suspend fun fetch(): List<DcConfig> = dsl.withTagName(tagName)
        .selectFrom(DC_CONFIG)
        .fetchAsync()
        .await()
        .map { dcConfigRecord ->
            DcConfig(
                dcCode = dcConfigRecord.dcCode,
                zoneId = ZoneId.of(dcConfigRecord.zoneId),
                productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart),
                market = dcConfigRecord.market,
            )
        }
}
