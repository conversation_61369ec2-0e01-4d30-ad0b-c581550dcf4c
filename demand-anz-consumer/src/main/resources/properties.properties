db.host=SDF DB Host
db.password=Password to authenticate with SDF database
db.username=Password to authenticate with SDF database
aiven.username=Username to use for <PERSON><PERSON> and schema registry authentication (defaults to Gradle root project name).
aiven.password=Password to use for <PERSON><PERSON> and schema registry authentication (required).
bootstrap.servers=kafka brokers
prekitting.auth.secret.key=Prekitting auth secret key
prekitting.auth.secret.message=Prekitting auth secret message
prekitting.service.url=Prekitting service URL
